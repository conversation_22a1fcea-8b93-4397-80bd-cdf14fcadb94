<?php
/**
 * FastGoods API 调用示例
 * 演示如何通过API接口添加商品（单规格和多规格）
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 配置信息
$config = [
    'api_url' => 'http://store.haochigui.com/api/fastgoods/add', // API接口URL
    'site_id' => 1 // 站点ID
];

// 调试输出
echo "使用的API URL: " . $config['api_url'] . "\n\n";

echo "========== FastGoods API 调用示例 ==========\n\n";

// 1. 添加单规格商品
echo "开始添加单规格商品...\n\n";

// 准备单规格商品数据
$single_sku_data = [
    'site_id' => $config['site_id'],
    'goods_name' => '单规格API商品-' . date('YmdHis'),
    'goods_class' => 1, // 实物商品
    'goods_class_name' => '实物商品',
    'category_id' => '1,2,3',
    'category_json' => json_encode([[1,2,3]]),
    'goods_image' => 'upload/common/default_goods_img.png',
    'goods_content' => '<p>API接口添加的单规格商品</p>',
    'goods_state' => 1,
    'verify_state' => 1,
    'price' => '99.00',
    'market_price' => '120.00',
    'cost_price' => '80.00',
    'stock' => '100',
    'goods_stock' => '100',
    'goods_stock_alarm' => '10',
    'is_virtual' => 0,
    'is_free_shipping' => 1,
    'shipping_template' => 0,
    'introduction' => 'API添加的单规格商品',
    'keywords' => 'API,单规格',
    'unit' => '件',
    'goods_attr_format' => '[]',
    'spec_type_status' => 0, // 单规格商品标识
    'timer_on' => 0,
    'timer_off' => 0,
    'is_consume_discount' => 0
];

// 调用API
$single_result = callApi($config['api_url'], $single_sku_data);

// 输出结果
echo "单规格商品API调用结果：\n";
echo json_encode($single_result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
echo "\n\n";

// 2. 添加多规格商品
echo "开始添加多规格商品...\n\n";

// 准备多规格商品数据
$multi_sku_data = [
    'site_id' => $config['site_id'],
    'goods_name' => '多规格API商品-' . date('YmdHis'),
    'goods_class' => 1,
    'goods_class_name' => '实物商品',
    'category_id' => '1,2,3',
    'category_json' => json_encode([[1,2,3]]),
    'goods_image' => 'upload/common/default_goods_img.png',
    'goods_content' => '<p>API接口添加的多规格商品</p>',
    'goods_state' => 1,
    'verify_state' => 1,
    'is_virtual' => 0,
    'is_free_shipping' => 1,
    'shipping_template' => 0,
    'introduction' => 'API添加的多规格商品',
    'keywords' => 'API,多规格',
    'unit' => '件',
    'goods_attr_format' => '[]',
    'spec_type_status' => 1, // 多规格商品标识
    'timer_on' => 0,
    'timer_off' => 0,
    'is_consume_discount' => 0,
    
    // 商品规格格式
    'goods_spec_format' => json_encode([
        [
            "spec_id" => -1,
            "spec_name" => "颜色",
            "value" => [
                ["spec_id" => -1, "spec_name" => "颜色", "spec_value_id" => -1, "spec_value_name" => "红色", "image" => ""],
                ["spec_id" => -1, "spec_name" => "颜色", "spec_value_id" => -2, "spec_value_name" => "蓝色", "image" => ""]
            ]
        ]
    ]),
    
    // SKU数据
    'goods_sku_data' => json_encode([
        [
            "spec_name" => "红色",
            "sku_no" => "API" . date('YmdHis') . "-R",
            "sku_spec_format" => [
                ["spec_id" => -1, "spec_name" => "颜色", "spec_value_id" => -1, "spec_value_name" => "红色", "image" => ""]
            ],
            "price" => "100.00",
            "market_price" => "120.00",
            "cost_price" => "80.00",
            "stock" => "50",
            "stock_alarm" => "5",
            "sku_image" => "",
            "sku_images" => "",
            "sku_images_arr" => [],
            "is_default" => 1,
            "weight" => "1",
            "volume" => "0"
        ],
        [
            "spec_name" => "蓝色",
            "sku_no" => "API" . date('YmdHis') . "-B",
            "sku_spec_format" => [
                ["spec_id" => -1, "spec_name" => "颜色", "spec_value_id" => -2, "spec_value_name" => "蓝色", "image" => ""]
            ],
            "price" => "110.00",
            "market_price" => "130.00",
            "cost_price" => "85.00",
            "stock" => "50",
            "stock_alarm" => "5",
            "sku_image" => "",
            "sku_images" => "",
            "sku_images_arr" => [],
            "is_default" => 0,
            "weight" => "1",
            "volume" => "0"
        ]
    ])
];

// 调用API
$multi_result = callApi($config['api_url'], $multi_sku_data);

// 输出结果
echo "多规格商品API调用结果：\n";
echo json_encode($multi_result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
echo "\n\n";

// 显示结果总结
echo "========== 测试结果总结 ==========\n";
if (isset($single_result['code']) && $single_result['code'] === 0) {
    echo "单规格商品添加成功！商品ID: " . $single_result['data'] . "\n";
} else {
    echo "单规格商品添加失败: " . ($single_result['message'] ?? '未知错误') . "\n";
}

if (isset($multi_result['code']) && $multi_result['code'] === 0) {
    echo "多规格商品添加成功！商品ID: " . $multi_result['data'] . "\n";
} else {
    echo "多规格商品添加失败: " . ($multi_result['message'] ?? '未知错误') . "\n";
}

/**
 * 调用API接口的辅助函数
 * @param string $url API地址
 * @param array $data 请求数据
 * @return array 接口返回结果
 */
function callApi($url, $data) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        return ['code' => -1, 'message' => 'CURL错误: ' . curl_error($ch)];
    }
    
    curl_close($ch);
    
    // 尝试解析JSON响应
    $result = json_decode($response, true);
    if ($result === null) {
        // 如果响应不是有效的JSON，则返回错误信息
        return [
            'code' => -1, 
            'message' => '返回数据解析失败', 
            'data' => substr($response, 0, 500) . (strlen($response) > 500 ? '...' : '')
        ];
    }
    
    return $result;
}

/**
 * 使用方法:
 * 1. 通过命令行运行: E:\phpEnv\php\php-7.4\php.exe E:\phpEnv\www\store.haochigui.cm\api_example.php
 * 2. 通过Web访问: http://localhost/api_example.php
 * 
 * 注意：确保API_URL配置正确，如果使用localhost可能导致404错误
 */ 