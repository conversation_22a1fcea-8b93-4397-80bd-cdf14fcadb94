<?php
/**
 * FastGoodsAdd 本地调用示例
 * 直接调用模型类添加商品（单规格和多规格）
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 初始化环境
$_SERVER['REQUEST_URI'] = '/api/fastgoods/add';
require __DIR__ . '/index.php';

echo "========== FastGoodsAdd 本地调用示例 ==========\n\n";

// 实例化FastGoodsAdd类
$fast_goods = new \app\model\goods\FastGoodsAdd();

// 1. 添加单规格商品
echo "开始添加单规格商品...\n\n";

// 准备单规格商品数据
$single_sku_data = [
    'site_id' => 1,
    'goods_name' => '单规格本地调用商品-' . date('YmdHis'),
    'goods_class' => 1, // 实物商品
    'goods_class_name' => '实物商品',
    'category_id' => '1,2,3',
    'category_json' => json_encode([[1,2,3]]),
    'goods_image' => 'upload/common/default_goods_img.png',
    'goods_content' => '<p>本地调用添加的单规格商品</p>',
    'goods_state' => 1,
    'verify_state' => 1,
    'price' => '99.00',
    'market_price' => '120.00',
    'cost_price' => '80.00',
    'stock' => '100',
    'goods_stock' => '100',
    'goods_stock_alarm' => '10',
    'is_virtual' => 0,
    'is_free_shipping' => 1,
    'shipping_template' => 0,
    'introduction' => '本地调用添加的单规格商品',
    'keywords' => '本地调用,单规格',
    'unit' => '件',
    'goods_attr_format' => '[]',
    'spec_type_status' => 0, // 单规格商品标识
    'timer_on' => 0,
    'timer_off' => 0,
    'is_consume_discount' => 0
];

// 调用添加商品方法
$single_result = $fast_goods->addGoods($single_sku_data);

// 输出结果
echo "单规格商品添加结果：\n";
echo json_encode($single_result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
echo "\n\n";

// 2. 添加多规格商品
echo "开始添加多规格商品...\n\n";

// 准备多规格商品数据
$multi_sku_data = [
    'site_id' => 1,
    'goods_name' => '多规格本地调用商品-' . date('YmdHis'),
    'goods_class' => 1,
    'goods_class_name' => '实物商品',
    'category_id' => '1,2,3',
    'category_json' => json_encode([[1,2,3]]),
    'goods_image' => 'upload/common/default_goods_img.png',
    'goods_content' => '<p>本地调用添加的多规格商品</p>',
    'goods_state' => 1,
    'verify_state' => 1,
    'is_virtual' => 0,
    'is_free_shipping' => 1,
    'shipping_template' => 0,
    'introduction' => '本地调用添加的多规格商品',
    'keywords' => '本地调用,多规格',
    'unit' => '件',
    'goods_attr_format' => '[]',
    'spec_type_status' => 1, // 多规格商品标识
    'timer_on' => 0,
    'timer_off' => 0,
    'is_consume_discount' => 0,
    
    // 商品规格格式
    'goods_spec_format' => [
        [
            "spec_id" => -1,
            "spec_name" => "颜色",
            "value" => [
                ["spec_id" => -1, "spec_name" => "颜色", "spec_value_id" => -1, "spec_value_name" => "红色", "image" => ""],
                ["spec_id" => -1, "spec_name" => "颜色", "spec_value_id" => -2, "spec_value_name" => "蓝色", "image" => ""]
            ]
        ],
        [
            "spec_id" => -2,
            "spec_name" => "尺寸",
            "value" => [
                ["spec_id" => -2, "spec_name" => "尺寸", "spec_value_id" => -3, "spec_value_name" => "S", "image" => ""],
                ["spec_id" => -2, "spec_name" => "尺寸", "spec_value_id" => -4, "spec_value_name" => "M", "image" => ""]
            ]
        ]
    ],
    
    // SKU数据
    'goods_sku_data' => [
        // 红色S
        [
            "spec_name" => "红色,S",
            "sku_no" => "LOC" . date('YmdHis') . "-RS",
            "sku_spec_format" => [
                ["spec_id" => -1, "spec_name" => "颜色", "spec_value_id" => -1, "spec_value_name" => "红色", "image" => ""],
                ["spec_id" => -2, "spec_name" => "尺寸", "spec_value_id" => -3, "spec_value_name" => "S", "image" => ""]
            ],
            "price" => "100.00",
            "market_price" => "120.00",
            "cost_price" => "80.00",
            "stock" => "25",
            "stock_alarm" => "5",
            "sku_image" => "",
            "sku_images" => "",
            "sku_images_arr" => [],
            "is_default" => 1,
            "weight" => "1",
            "volume" => "0"
        ],
        // 红色M
        [
            "spec_name" => "红色,M",
            "sku_no" => "LOC" . date('YmdHis') . "-RM",
            "sku_spec_format" => [
                ["spec_id" => -1, "spec_name" => "颜色", "spec_value_id" => -1, "spec_value_name" => "红色", "image" => ""],
                ["spec_id" => -2, "spec_name" => "尺寸", "spec_value_id" => -4, "spec_value_name" => "M", "image" => ""]
            ],
            "price" => "110.00",
            "market_price" => "130.00",
            "cost_price" => "85.00",
            "stock" => "25",
            "stock_alarm" => "5",
            "sku_image" => "",
            "sku_images" => "",
            "sku_images_arr" => [],
            "is_default" => 0,
            "weight" => "1.1",
            "volume" => "0"
        ],
        // 蓝色S
        [
            "spec_name" => "蓝色,S",
            "sku_no" => "LOC" . date('YmdHis') . "-BS",
            "sku_spec_format" => [
                ["spec_id" => -1, "spec_name" => "颜色", "spec_value_id" => -2, "spec_value_name" => "蓝色", "image" => ""],
                ["spec_id" => -2, "spec_name" => "尺寸", "spec_value_id" => -3, "spec_value_name" => "S", "image" => ""]
            ],
            "price" => "105.00",
            "market_price" => "125.00",
            "cost_price" => "82.00",
            "stock" => "25",
            "stock_alarm" => "5",
            "sku_image" => "",
            "sku_images" => "",
            "sku_images_arr" => [],
            "is_default" => 0,
            "weight" => "1",
            "volume" => "0"
        ],
        // 蓝色M
        [
            "spec_name" => "蓝色,M",
            "sku_no" => "LOC" . date('YmdHis') . "-BM",
            "sku_spec_format" => [
                ["spec_id" => -1, "spec_name" => "颜色", "spec_value_id" => -2, "spec_value_name" => "蓝色", "image" => ""],
                ["spec_id" => -2, "spec_name" => "尺寸", "spec_value_id" => -4, "spec_value_name" => "M", "image" => ""]
            ],
            "price" => "115.00",
            "market_price" => "135.00",
            "cost_price" => "87.00",
            "stock" => "25",
            "stock_alarm" => "5",
            "sku_image" => "",
            "sku_images" => "",
            "sku_images_arr" => [],
            "is_default" => 0,
            "weight" => "1.1",
            "volume" => "0"
        ]
    ]
];

// 转换数组为JSON字符串
$multi_sku_data['goods_spec_format'] = json_encode($multi_sku_data['goods_spec_format'], JSON_UNESCAPED_UNICODE);
$multi_sku_data['goods_sku_data'] = json_encode($multi_sku_data['goods_sku_data'], JSON_UNESCAPED_UNICODE);

// 调用添加商品方法
$multi_result = $fast_goods->addGoods($multi_sku_data);

// 输出结果
echo "多规格商品添加结果：\n";
echo json_encode($multi_result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
echo "\n\n";

// 显示结果总结
echo "========== 测试结果总结 ==========\n";
if (isset($single_result['code']) && $single_result['code'] === 0) {
    echo "单规格商品添加成功！商品ID: " . $single_result['data'] . "\n";
} else {
    echo "单规格商品添加失败: " . ($single_result['message'] ?? '未知错误') . "\n";
}

if (isset($multi_result['code']) && $multi_result['code'] === 0) {
    echo "多规格商品添加成功！商品ID: " . $multi_result['data'] . "\n";
} else {
    echo "多规格商品添加失败: " . ($multi_result['message'] ?? '未知错误') . "\n";
}

/**
 * 使用方法:
 * 通过命令行运行: E:\phpEnv\php\php-7.4\php.exe E:\phpEnv\www\store.haochigui.cm\api_example_local.php
 */ 