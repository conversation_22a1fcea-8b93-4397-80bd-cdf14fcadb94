<!--<script type="text/javascript" src="{$http_type}://webapi.amap.com/maps?v=1.4.6&amp;key=2df5711d4e2fd9ecd1622b5a53fc6b1d"></script>-->
<!--<script type="text/javascript" src="STATIC_JS/map_address.js"></script>-->
<script type="text/javascript" src="SHOP_JS/address.js"></script>
<script src="https://map.qq.com/api/gljs?v=1.exp&libraries=service&key={$tencent_map_key}"></script>
<script src="https://map.qq.com/api/js?v=2.exp&key={$tencent_map_key}"></script>
<script src="https://mapapi.qq.com/jsapi_v2/2/4/148/main.js"></script>
<script type="text/javascript" src="STATIC_JS/qq_map.js?time=20240601"></script>
<style>
    .update-address-html .order-map{width:876px;height:380px;}
    .eg-text{ font-size: 12px;line-height: 1;color: red}
</style>

<!-- 修改收货地址模态 -->
<div id="update_address_box" class="update-address-box"></div>
<script type="text/html" id="update_address_html">

<div class="layui-form update-address-html" id='update_address'lay-filter="update_address">
    <input type="hidden" name="order_id" value="{{ d.order_id }}"/>
    <!--自提点地址-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">* </span>收货地址</label>
        <div class="layui-input-inline area-select">
            <select name="province_id" lay-filter="province_id" lay-verify="province_id">
                <option value="">请选择省份</option>
            </select>
        </div>
        <div class="layui-input-inline area-select">
            <select name="city_id"  lay-filter="city_id" lay-verify="city_id">
                <option value="">请选择城市</option>
            </select>
        </div>
        <div class="layui-input-inline area-select">
            <select name="district_id"  lay-filter="district_id" lay-verify="district_id">
                <option value="">请选择区/县</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <div class="layui-input-inline" >
            <input type="text" name="address"  placeholder="请填写具体地址。" lay-verify="required" autocomplete="off" class="layui-input address-content len-long" value="{{# if(d.address != undefined){}}{{ d.address}}{{#  } }}">
            {{# if(d.order_type == 3){ }}
            <input type = "hidden" name="longitude" lay-verify="required" class="layui-input" value="{{# if(d.longitude != undefined){}}{{ d.longitude}}{{#  } }}"/>
            <input type = "hidden" name="latitude" lay-verify="required" class="layui-input" value="{{# if(d.latitude != undefined){}}{{ d.latitude}}{{#  } }}"/>
            {{# } }}
        </div>
        {{# if(d.order_type == 3){ }}
        <button class='layui-btn-primary layui-btn' onclick="refreshFrom();">查找地址</button>
        {{# } }}
    </div>
    {{# if(d.order_type == 3){ }}
    <!--地图定位-->
    <div class="layui-form-item">
        <label class="layui-form-label">地图定位</label>
        <div class="layui-input-block special-length">
            <div id="container" class="order-map"></div>
        </div>
    </div>
    {{# } }}
    <!--联系人方式-->
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">收货人</label>
            <div class="layui-input-block">
                <input type="text" name="name" lay-verify="required" placeholder="请填写收货联系人" autocomplete="off" class="layui-input selffetch-input len-mid" value="{{# if(d.name != undefined){}}{{ d.name}}{{#  } }}">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">手机号码</label>
            <div class="layui-input-block">
                <input type="text" name="mobile" lay-verify="mobile" placeholder="请填写手机号码" autocomplete="off" class="layui-input selffetch-input len-mid" value="{{# if(d.mobile != undefined){}}{{d.mobile }}{{#  } }}">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">固定号码</label>
            <div class="layui-input-block">
                <input type="text" name="telephone" placeholder="请填写固定号码" autocomplete="off" class="layui-input selffetch-input len-mid" value="{{# if(d.telephone != undefined){}}{{d.telephone }}{{#  } }}">
            </div>
        </div>
    </div>

    <div class="layui-form-item analysis">
        <div class="layui-inline">
            <label class="layui-form-label">智能识别地址</label>
            <div class="layui-input-inline ">
                <textarea class="layui-textarea len-long" id="address_text" style="width: 400px; height: 100px;" ></textarea>
                <div class="eg-text">*示例:小红152********山西省太原市小店区**路**号</div>
            </div>
            <div class="layui-input-inline ">
                <button class="layui-btn" onclick="analysis()">解析</button>
            </div>
        </div>
    </div>
    <button class="layui-btn"  lay-submit id="submit_address" lay-filter="submit_address" style="display:none;">保存</button>
</div>
</script>
<script>
var map_class, form,latlng;
// 订单地址修改
function orderAddressUpdate(order_id) {






    var order_info = getOrderInfo(order_id);
    var getTpl = $("#update_address_html").html();
    var order_data = order_info;

    laytpl(getTpl).render(order_data, function(html) {
        layer_index = layer.open({
            type: 1,
            shadeClose: true,
            shade: 0.3,
            offset: 'auto',
            scrollbar: true,
            fixed: false,
            title: "编辑收货地址",
            area: ['1200px'],
            btn: ['确定', '取消'],
            yes: function(index, layero){
                $("#submit_address").click();
            },
            btn2: function(index, layero){
                layer.close(index);
            },
            content:  html,
            success: function(layero, index){
                form.render();
                //初始化省级地址
                getAreaList(0, 1);
                var repeat_flag = false;//防重复标识
                form.render();
                var initdata = {province_id : order_data.province_id, city_id : order_data.city_id, district_id : order_data.district_id};
                initAddress(initdata, "update_address");

                if(order_data.order_type == 3) {
                    if ($.isEmptyObject(order_data) == true) {
                        latlng = {lat: '', lng: ''};
                    } else {
                        latlng = {lat: order_data.latitude, lng: order_data.longitude};
                    }

                    if($("#container").length) {
                        setTimeout(function () {
                            map_class = new mapClass("container", latlng);
                        },200);
                    }
                }
                form.render();
                form.verify({
                    mobile:function (value){
                        if(!ns.getRegexp('mobile').test(value)){
                            return '请输入正确的手机号';
                        }
                    }
                })
                form.on('submit(submit_address)', function(data){

                    if(data.field.province_id == ''){
                        layer.msg('请选择省份', {icon: 5, anim: 6});
                        return;
                    }
                    if(data.field.city_id == ''){
                        layer.msg('请选择城市', {icon: 5, anim: 6});
                        return;
                    }
                    if(data.field.district_id == ''){
                        layer.msg('请选择区/县', {icon: 5, anim: 6});
                        return;
                    }
                    if(data.field.address == ''){
                        layer.msg('请输入详细地址', {icon: 5, anim: 6});
                        return;
                    }
                    //外卖订单修改地址必须选坐标
                    if(order_data.order_type == 3){
                        if(data.field.latitude == '' || data.field.longitude == ''  ){
                            layer.msg('外卖订单必须选择地图坐标', {icon: 5, anim: 6});
                            return;
                        }
                    }
                    var province_name = $("option[value='" + data.field.province_id + "']").text();
                    var city_name = $("option[value='" + data.field.city_id + "']").text();
                    var district_name = $("option[value='" + data.field.district_id + "']").text();
                    // var community_name = $("option[value='" + data.field.community_id + "']").text();
                    data.field.province_name = province_name;
                    data.field.city_name = city_name;
                    data.field.district_name = district_name;
                    // data.field.community_name = community_name;
                    data.field.full_address = province_name + '-' + city_name + '-' + district_name;

                    if(repeat_flag)return;
                    repeat_flag = true;
                    $.ajax({
                        url: ns.url("shop/order/editaddress"),
                        type: "POST",
                        dataType: "JSON",
                        async: false,
                        data: data.field,
                        success: function (res) {
                            layer.msg(res.message);
                            if(res.code == 0){
                                layer.close(layer_index);
                                reloadList();
                            }else{
                                repeat_flag = false;
                            }
                        }
                    });
                    return false;
                });

                $.ajax({
                    url: ns.url("shop/address/geMapConfig"),
                    type: "POST",
                    dataType: "JSON",
                    async: false,
                    data: {},
                    success: function (res) {
                        if(res.data.key == ''){
                            let height = $(".layui-layer-content").height()
                            $(".layui-layer-content").height(height-140)
                            $(".analysis").hide()
                        }else{
                            $(".analysis").show()
                        }
                    }
                });
            }
        });
        form.render();
    });

}

/**
 * 重新渲染表单
 */
function refreshFrom(){
    form.render();
    orderAddressChange();//改变地址
    map_class.mapChange();
}

//动态改变订单地址赋值
function orderAddressChange(){
    map_class.address.province = $("select[name=province_id]").val();
    map_class.address.province_name = $("select[name=province_id] option:selected").text();
    map_class.address.city = $("select[name=city_id]").val();
    map_class.address.city_name = $("select[name=city_id] option:selected").text();
    map_class.address.district = $("select[name=district_id]").val();
    map_class.address.district_name = $("select[name=district_id] option:selected").text();
    // map_class.address.township = $("select[name=community_id]").val();
    // map_class.address.township_name = $("select[name=community_id] option:selected").text();
    map_class.address.detail_address = $("input[name=address]").val()
}

/**
 * 地址下拉框（主要用于坐标记录）
 */
function selectCallBack(){
    $("input[name=longitude]").val(map_class.address.longitude);//坐标
    $("input[name=latitude]").val(map_class.address.latitude);//坐标
    $("input[name=address]").val(map_class.address.address);//详细地址
}

//地图点击回调时间
function mapChangeCallBack(){
    $("input[name=address]").val(map_class.address.address);//详细地址
    $("input[name=longitude]").val(map_class.address.longitude);//坐标
    $("input[name=latitude]").val(map_class.address.latitude);//坐标
    $.ajax({
        type : "post",
        url : ns.url("shop/address/getGeographicId"),
        dataType: 'json',
        async : true,
        data : {
            "address" : map_class.address.area
        },
        success : function(data) {
            map_class.address.province = data.province_id;
            map_class.address.city = data.city_id;
            map_class.address.district = data.district_id;
            // map_class.address.township = data.community_id;
            map_class.map_change = false;
            form.val("update_address", {
                "province_id": data.province_id // "name": "value"
            });
            $("select[name=province_id]").change();
            form.val("update_address", {
                "city_id": data.city_id // "name": "value"
            });
            $("select[name=city_id]").change();
            form.val("update_address", {
                "district_id": data.district_id // "name": "value"
            });
            $("select[name=district_id]").change();
            // form.val("update_address", {
            //     "community_id": data.community_id // "name": "value"
            // });
            refreshFrom();//重新渲染form
            map_class.map_change = true;
        }
    })
}


function analysis(){
    let address = $("#address_text").val();//详细地址
    if(!address){
        layer.msg('请输入详细地址', {icon: 5, anim: 6});
        return;
    }
    $.ajax({
        url: ns.url("shop/address/analysesAddress"),
        type: "POST",
        dataType: "JSON",
        async: false,
        data: {'address':address},
        success: function (res) {
            if(res.code == 0){

                let change = false;
                if(res.data.province_id !== ''){
                    form.val("update_address", {
                        "province_id": res.data.province_id // "name": "value"
                    });
                    $("select[name=province_id]").change();
                    change = true;
                }
                if(res.data.city_id !== '' || change){
                    form.val("update_address", {
                        "city_id": res.data.city_id // "name": "value"
                    });
                    $("select[name=city_id]").change();
                }

                if(res.data.district_id !== '' || change){
                    form.val("update_address", {
                        "district_id": res.data.district_id // "name": "value"
                    });
                    $("select[name=district_id]").change();
                }
                if(res.data.detail !== '' || change){
                    form.val("update_address", {
                        "address": res.data.detail // "name": "value"
                    });
                }
                if(res.data.name !== ''){
                    $("input[name='name']").val(res.data.name)
                }
                if(res.data.mobile !== ''){
                    $("input[name='mobile']").val(res.data.mobile)
                }
                if(res.data.lng !== ''){
                    $("input[name=longitude]").val(res.data.lng);//坐标
                }
                if(res.data.lat !== ''){
                    $("input[name=latitude]").val(res.data.lat);//坐标
                }
                refreshFrom();//重新渲染form
            }else{
                layer.msg(res.message);
            }
        }
    });
}
</script>