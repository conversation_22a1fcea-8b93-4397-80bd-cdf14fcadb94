@CHARSET "UTF-8";

.layui-form-label{
	width: 140px;
}
.layui-form-label + .layui-input-block{
	margin-left: 140px;
}
.layui-tab-title li{
	transition:none;
	-webkit-transition:none;
}
.word-aux{
	margin-left: 140px;
}

.layui-block {
	margin-bottom: 15px;
	overflow: hidden;
}
.commodity-type-box{
	display: flex;
}

.commodity-type-item{
	margin-right: 15px;
	padding: 15px 20px;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	border: 1px solid #e5e5e5;
	cursor: pointer;
}

/* 商品分类 */
.js-attr-list,
.js-new-attr-list {
	max-width: 1000px !important;
}

.js-new-attr-list .prompt-block {
	border: none;
    border-top: 1px solid #e6e6e6;
}

.cate-input-default {
	position: relative;
}

.cate-input-default button {
	position: absolute;
	top: 1px;
	right: 1px;
	border: 0;
	height: 32px;
	line-height: 32px;
	border-left: 1px solid #E6E6E6;
}

.goods-category-wrap {
	position: relative;
}

.goods-category-wrap .layui-block {
	overflow: inherit;
}

.goods-category-list {
	/* width: 800px; */
	background-color: #FFFFFF;
	border: 1px solid #E6E6E6;
	position: absolute;
	left: 200px;
	z-index: 999;
	display: none;
}

/*商品分类弹出框*/
.category-list {
	overflow: hidden;
}

.category-list .item {
	width: 260px;
	position: relative;
	float: left;
	background: #fff;
	height: 345px;
	overflow-y: auto;
}

.category-list .item:last-child {
	margin-right: 0;
}

.category-list .item li {
	display: flex;
	padding: 10px;
	cursor: pointer;
	font-size: 12px;
}

.category-list .item li .category-name {
	display: inline-block;
	margin-left: 4px;
	white-space: nowrap;
	width: 210px;
	text-overflow: ellipsis;
	overflow: hidden;
}

.category-list .item li .right-arrow {
	width: 7px;
	height: 7px;
	border: 1px solid;
	border-color: #666 #666 transparent transparent;
	transform: rotate(45deg) translateY(6px);
}

.js-selected-category .right-arrow {
	margin-right: 5px;
	width: 7px;
	height: 7px;
	border: 1px solid;
	border-color: #666 #666 transparent transparent;
	transform: rotate(45deg);
	display: inline-block;
}
.selected-category-wrap {
	padding: 10px;
	border-bottom: 1px solid #cccccc;
}

/*底部按钮*/
.fixed-btn {
	width: calc(100% - 302px);
	text-align: center;
	position: fixed;
	bottom: 0;
	left: 271px;
	background: #F9F9F9;
	line-height: 80px;
	z-index: 1000;
	border-top: 1px solid #e5e5e5;
}
@media screen and (max-width: 1250px) {
	.fixed-btn {
		bottom: 20px;
	}
}

.footer {
	padding-bottom: 160px;
}

.fixed-btn > button {
	vertical-align: middle;
}

/*, .fixed-btn > button:nth-child(2)*/
.fixed-btn > button:first-child {
	display: none;
}

input[disabled] {
	cursor: not-allowed;
	background-color: #F5F5F5;
}

.spec-edit-list {
	margin-bottom: 10px;
	-webkit-user-select: none;
	-ms-user-select: none;
	-moz-user-select: none
}

.spec-edit-list .spec-item {
	border: 1px dotted transparent;
	padding: 10px;
	position: relative;
	background: #fff;
}

.spec-edit-list .spec-item .layui-form-item:last-child {
	margin-bottom: 0;
}

.spec-edit-list .spec-item:hover {
	border-color: #9E9E9E;
	cursor: move;
}

.spec-edit-list .spec-item .spec-value ul {
	margin-bottom: 10px;
}

.spec-edit-list .spec-item .spec-value ul li {
	display: inline-block;
	line-height: 30px;
	height: 30px;
	padding: 0 15px;
	border-radius: 2px;
	border: 1px solid #e9e9e9;
	background: #f7f7f7;
	font-size: 12px;
	/*transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);*/
	vertical-align: middle;
	opacity: 1;
	margin: 4px 8px 4px 0;
	cursor: pointer;
	position: relative;
}

.spec-edit-list .spec-item .spec-value ul li .img-wrap {
	display: inline-block;
	margin-right: 5px;
	vertical-align: middle;
	width: 25px;
	overflow: hidden;
	height: 25px;
	line-height: 25px;
}

.spec-edit-list .spec-item .spec-value ul li .img-wrap img {
	max-width: 100%;
}

.spec-edit-list .spec-item .spec-value ul li span {
	vertical-align: middle;
}
.spec-edit-list .spec-item .spec-value ul li span:focus{
	outline: none;
	background: #fff;
	padding:5px 10px;
	border:1px solid var(--base-color);
	border-radius: 2px;
}

.spec-edit-list .spec-item .spec-value ul li i {
	font-size: 12px;
	position: absolute;
	top: -8px;
	right: -8px;
	width: 16px;
	height: 16px;
	line-height: 16px;
	text-align: center;
	color: #fff;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	background: rgba(0, 0, 0, 0.3);
	display: none;
}

.spec-edit-list .spec-item .spec-value ul li:hover i {
	display: block;
}
.spec-edit-list .spec-item .layui-icon-close {
	font-size: 12px;
	position: absolute;
	top: -8px;
	right: -8px;
	width: 16px;
	height: 16px;
	line-height: 16px;
	text-align: center;
	color: #fff;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	background: rgba(0, 0, 0, 0.3);
	cursor: pointer;
}

.spec-edit-list .spec-item .add-spec-value-popup {
	position: absolute;
	z-index: 10;
	cursor: auto;
	left: -15px;
	top: 35px;
	background-color: #fff;
	border-radius: 2px;
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
	padding: 15px;
	display: none;
}

.spec-edit-list .layui-unselect.layui-form-select {
	display: none;
}

/*批量操作*/
.batch-operation-sku {
	display: none;
}

.batch-operation-sku span {
	margin-right: 10px;
	display: inline-block;
	height: 34px;
	line-height: 34px;
	cursor: pointer;
}

.batch-operation-sku input {
	display: inline-block;
}

.batch-operation-sku input,
.batch-operation-sku button {
	display: none;
}

.sku-table {
	display: none;
	-webkit-user-select: none;
	-ms-user-select: none;
	-moz-user-select: none
}

.sku-table .layui-input-block .img-wrap {
	display: inline-block;
	position: relative;
	margin: 8px;
	border: 1px solid #e5e5e5;
}

.sku-table .layui-input-block .img-wrap a {
	display: block;
	width: 50px;
	height: 50px;
	line-height: 50px;
	text-align: center;
	overflow: hidden;
}

.sku-table .layui-input-block .img-wrap a img {
	width: 100%;
}

.sku-table .layui-input-block .img-wrap .operation {
	position: absolute;
	top: 0;
	z-index: 10;
	width: 50px;
	height: 50px;
	background: rgba(0, 0, 0, 0.3);
	color: #fff;
	cursor: pointer;
	line-height: 50px;
	text-align: center;
	display: none;
}

.sku-table .layui-input-block .img-wrap:hover .operation {
	display: block;
}

.sku-table .layui-input-block .img-wrap .operation i {
	font-size: 20px;
}

.sku-table .layui-input-block .upload-sku-img {
	border: 1px dashed #d9d9d9;
	width: 50px;
	height: 50px;
	border-radius: 2px;
	background-color: #fbfbfb;
	text-align: center;
	cursor: pointer;
	margin: 8px;
	display: inline-block;
	padding: 15px 0;
	box-sizing: border-box;
}

.sku-table .layui-input-block .layui-form-radio {
	margin: 6px 0px 0 0;
	padding: 0;
}

.sku-table .layui-input-block .layui-form-radio>i {
	margin-right: 3px;
}

.js-shipping-template {
	display: none;
}

.js-goods-image {
	margin-bottom: 10px;
	overflow: hidden;
}

.goods-image-wrap .item {
	overflow: hidden;
	margin-bottom: 10px;
	margin-right: 10px;
	display: inline-block;
}

.layui-word-aux {
	font-size: 12px;
}

/* 展示视频 */
.video-thumb {
	display: block;
	float: left;
	width: 250px;
	height: 120px;
	position: relative;
}

.delete-video {
	width: 51px;
	height: 51px;
	cursor: pointer;
	position: absolute;
	right: 150px;
	top: 45px;
	color: #fff;
	z-index: 100;
}

.replace-video {
	width: 75px;
	height: 51px;
	cursor: pointer;
	position: absolute;
	right: 50px;
	top: 45px;
	color: #fff;
	z-index: 100;
}

.replace-video2 {
	right: 100px;
}

.mask {
	position: absolute;
	left: 0;
	width: 250px;
	height: 92px;
	background: #000;
	opacity: 0.6;
	cursor: pointer;
	z-index:10;
}

.del-img {
	width: 14px;
	margin-right: 5px;
	padding-bottom: 2px;
}

.up-img {
	width: 14px;
	height: 14px;
	margin-right: 5px;
	padding-bottom: 3px;
}

.up-video {
	position: absolute;
	left: 0;
	width: 250px;
	height: 92px;
	cursor: pointer;
	z-index:10;
}

.video-thumb .hide {
	display: none !important;
}

.video-thumb > #goods_video {
	width: 100% !important;
	height: 121px;
	background: #ffffff;
}

.file-title {
	font-size: 12px;
}

.file-title > div {
	margin-top: 10px;
}

.file-title ul {
	color: var(--base-color);
}

.js-attr-list {
	display: none;
}

.js-more-spec {
	display: none;
}

.js-goods-shop-category .layui-form-select {
	margin-bottom: 10px;
}

.js-goods-shop-category .layui-form-select:last-child {
	margin-bottom: 0;
}

.js-goods-shop-category .item {
	position: relative;
}

.js-goods-shop-category .item .layui-icon-close {
	font-size: 12px;
	position: absolute;
	top: -8px;
	right: -8px;
	width: 16px;
	height: 16px;
	line-height: 16px;
	text-align: center;
	color: #fff;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	background: rgba(0, 0, 0, 0.3);
	cursor: pointer;
}

.layui-input {
	display: inline-block;
}

.layui-form-mid {
	float: none;
}

.screen .layui-form-item .layui-form-mid {
	float: left;
}

.layui-form-item .layui-form-checkbox[lay-skin=primary] {
	margin-top: 0px;
}

.prompt-block .prompt-box {
	text-align: center;
}
.prompt-block .prompt-box:after {
	border-right-color: #FFFFFF;
	bottom: -23px;
}


/* 商品分类 */
.goods-category-wrap-box {display: inline-block;position: relative;z-index: 10}
.goods-category-wrap-box .layui-block {display: block; position: relative;}
.goods-category-wrap-box .category-wrap {position: absolute; left: 0; top: 44px; display: none; border: 1px solid #EEEEEE; z-index: 999; background: #fff;}
.goodsCategory{width: 220px;height: 350px;overflow-y: auto;box-sizing: border-box}
.goodsCategory::-webkit-scrollbar{width: 3px;}
.goodsCategory::-webkit-scrollbar-track{-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);border-radius: 10px;background-color: #fff;}
.goodsCategory::-webkit-scrollbar-thumb{height: 20px;border-radius: 10px;-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);background-color: #ccc;}
.goodsCategory ul{margin-left: 0;}
.goodsCategory::-webkit-scrollbar {
	display: none; /* Chrome Safari */
}

.goodsCategory {
	scrollbar-width: none; /* firefox */
	-ms-overflow-style: none; /* IE 10+ */
	overflow-x: hidden;
	overflow-y: auto;
}
.goodsCategory ul li{text-align: left;padding:0 10px;line-height: 30px;}
.goodsCategory ul li i{float: right;line-height: 30px;}
.goodsCategory ul li:hover{cursor: pointer;}
.goodsCategory ul li:hover,.goodsCategory ul li.selected{background: var(--base-color);color: #fff;}
.goodsCategory ul li span{width: 110px;display: inline-block;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;vertical-align: middle;font-size:12px;}
.one{left: 0;}
.two{left: 185px;border-left:0;}
.three{left: 370px;width: 185px;border-left:0;}
.selectGoodsCategory{width: 185px;height: 45px;border:1px solid #CCCCCC;position: absolute;z-index: 100;left: 0;margin-top: 296px;box-sizing: border-box;border-collapse: collapse;background: #fff;}
.selectGoodsCategory a{height: 30px;text-align: center;color: #fff;line-height: 30px; margin: 6px;padding: 0 5px;text-decoration:none;}
.goodsCategory ul li i {float: right;line-height: 30px;}
.hide {display: none;}
.goods-category-mask {width: 100%;height: 100%;position: fixed;left: 0;top: 0;z-index: 9;}
.confirm-select {border: 1px solid var(--base-color);}
.el-input__inner {height:34px;line-height:34px;border-radius:2px;}

.delivery-caozuo {
	margin-left: 10px;
	cursor: pointer;
}

.body-content{padding: 0!important;padding-top: 40px!important;}
.layui-form .layui-tab-title{padding: 15px;padding-bottom: 0;position: fixed;left: 200px;top: 100px;background: #fff;z-index: 1000; /*width: calc(100% - 263px);*/border-top: 20px solid #f8f8f8;transition:none;-webkit-transition:none;}
.layui-layout-admin .layui-body .layui-form .layui-tab-title{left: 256px;/*width: calc(100% - 284px);*/top: 55px;border: 15px solid #EFF0F4;border-bottom: 0;border-right: 0}
.layui-layout-admin .body-content{padding-top: 50px!important;}
.layui-layout-admin .card-common{margin-top: 0;}
.goods-category-wrap-box .cate-input-default{width: 250px;}