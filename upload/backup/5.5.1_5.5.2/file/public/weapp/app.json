{"pages": ["pages/index/index", "pages/goods/cart", "pages/goods/category", "pages/goods/detail", "pages/goods/list", "pages/member/index", "pages/order/payment", "pages/order/list", "pages/order/detail", "pages/order/detail_point"], "subPackages": [{"root": "pages_promotion", "pages": ["cardservice/service_goods/reserve_list", "cardservice/service_goods/reserve_detail", "cardservice/service_goods/reserve_apply", "cardservice/service_goods/my_reserve_list", "cardservice/service_goods/my_reserve_detail", "cardservice/service_goods/service_list", "cardservice/card/list", "cardservice/card/my_card", "cardservice/card/my_detail", "cardservice/card/pick_goods", "cardservice/card/pick_payment", "cardservice/card/card_record", "blindbox/list", "blindbox/goods_list", "blindbox/fill_address", "blindbox/index", "blindbox/my_box", "blindbox/my_prize", "divideticket/list", "divideticket/index", "divideticket/poster", "divideticket/my_guafen", "bundling/detail", "bundling/payment", "topics/list", "topics/detail", "topics/goods_detail", "topics/payment", "seckill/list", "seckill/detail", "seckill/payment", "pintuan/list", "pintuan/detail", "pin<PERSON>an/my_spell", "pintuan/share", "pintuan/payment", "pintuan/order", "bargain/list", "bargain/detail", "bargain/my_bargain", "bargain/payment", "groupbuy/list", "groupbuy/detail", "groupbuy/payment", "pinfan/list", "pinfan/detail", "pinfan/payment", "pinfan/my_rebate", "pinfan/share", "pinfan/order", "point/list", "point/goods_list", "point/detail", "point/payment", "point/order_list", "point/result", "presale/list", "presale/detail", "presale/payment", "presale/order_list", "presale/order_detail", "bale/detail", "bale/payment", "jielong/jielong", "giftcard/index", "giftcard/member", "giftcard/detail", "giftcard/order_list", "giftcard/order_detail", "giftcard/list", "giftcard/card_info", "giftcard/give", "giftcard/give_info", "giftcard/member_give_info", "giftcard/exchange", "giftcard/payment", "giftcard/receive_list", "giftcard/give_list", "giftcard/card_use", "giftcard/not_exist", "giftcard/use_select", "game/cards", "game/turntable", "game/smash_eggs", "game/record", "fenxiao/index", "fenxiao/promote", "fenxiao/apply", "fenxiao/order", "fenxiao/relation", "fenxiao/order_detail", "fenxiao/team", "fenxiao/withdraw_apply", "fenxiao/withdraw_list", "fenxiao/withdrawal_detail", "fenxiao/promote_code", "fenxiao/level", "fenxiao/goods_list", "fenxiao/bill", "fenxiao/ranking_list", "fenxiao/child_fenxiao"]}, {"root": "pages_tool", "pages": ["webview/webview", "hongbao/index", "hongbao/list", "hongbao/my_hongbao", "hongbao/poster", "index/diy", "member/modify_face", "member/account", "member/account_edit", "member/apply_withdrawal", "member/balance", "member/balance_detail", "member/collection", "member/coupon", "member/contact", "member/footprint", "member/level", "member/card", "member/card_buy", "member/card_agreement", "member/level_growth_rules", "member/point", "member/point_detail", "member/signin", "member/withdrawal", "member/withdrawal_detail", "member/address", "member/address_edit", "member/pay_password", "member/cancellation", "member/assets", "member/cancelstatus", "member/cancelsuccess", "member/cancelrefuse", "member/info", "member/info_edit", "login/find", "goods/coupon", "goods/coupon_receive", "goods/evaluate", "goods/search", "goods/brand", "goods/not_exist", "help/list", "help/detail", "notice/list", "notice/detail", "article/list", "article/detail", "recharge/list", "recharge/order_list", "live/list", "store_notes/note_list", "store_notes/note_detail", "chat/room", "member/invite_friends", "pay/index", "pay/wx_pay", "pay/result", "pay/cashier", "pay/offlinepay", "storeclose/storeclose", "order/logistics", "order/evaluate", "order/refund", "order/refund_goods_select", "order/refund_type_select", "order/refund_batch", "order/refund_detail", "order/activist", "login/index", "login/aggrement", "login/login", "login/register", "form/form", "store/store_withdraw", "store/list", "store/detail", "store/store_payment", "store/payment_qrcode", "verification/index", "verification/list", "verification/detail", "weapp/order_shipping"]}], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "", "navigationBarBackgroundColor": "#ffffff", "backgroundColor": "#F7f7f7", "backgroundColorTop": "#f7f7f7", "backgroundColorBottom": "#f7f7f7"}, "tabBar": {"color": "#333", "selectedColor": "#FF0036", "backgroundColor": "#fff", "borderStyle": "white", "list": [{"pagePath": "pages/index/index", "text": ""}, {"pagePath": "pages/goods/category", "text": ""}, {"pagePath": "pages/goods/cart", "text": ""}, {"pagePath": "pages/member/index", "text": ""}]}, "preloadRule": {"pages/index/index": {"network": "all", "packages": ["pages_tool"]}}, "permission": {"scope.userLocation": {"desc": "为了更好地为您提供服务"}, "scope.writePhotosAlbum": {"desc": "为了更好地为您提供服务"}}, "requiredPrivateInfos": ["chooseLocation", "getLocation", "<PERSON><PERSON><PERSON><PERSON>"], "__usePrivacyCheck__": true, "usingComponents": {"loading-cover": "/components/loading-cover/loading-cover", "ns-mp-html": "/components/ns-mp-html/ns-mp-html", "ns-empty": "/components/ns-empty/ns-empty", "mescroll-uni": "/components/mescroll/my-list-mescroll", "mescroll-body": "/components/mescroll/mescroll-body", "ns-login": "/components/ns-login/ns-login", "privacy-popup": "/components/wx-privacy-popup/privacy-popup"}}