(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-badge/uni-badge"],{"0385":function(t,n,e){},"1c94":function(t,n,e){"use strict";e.r(n);var u=e("d2a4"),i=e.n(u);for(var a in u)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(a);n["default"]=i.a},c7c3:function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var u=function(){var t=this.$createElement;this._self._c},i=[]},cfd1:function(t,n,e){"use strict";var u=e("0385"),i=e.n(u);i.a},d2a4:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u={name:"UniBadge",props:{type:{type:String,default:"default"},inverted:{type:Boolean,default:!1},text:{type:String,default:""},size:{type:String,default:"normal"}},methods:{onClick:function(){this.$emit("click")}}};n.default=u},d65e:function(t,n,e){"use strict";e.r(n);var u=e("c7c3"),i=e("1c94");for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(a);e("cfd1");var c=e("828b"),r=Object(c["a"])(i["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);n["default"]=r.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-badge/uni-badge-create-component',
    {
        'components/uni-badge/uni-badge-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("d65e"))
        })
    },
    [['components/uni-badge/uni-badge-create-component']]
]);
