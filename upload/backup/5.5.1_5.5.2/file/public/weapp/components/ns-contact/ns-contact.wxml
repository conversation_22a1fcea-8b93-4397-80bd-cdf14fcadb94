<view class="contact-wrap"><slot></slot><button class="contact-button" type="default" hover-class="none" open-type="{{openType}}" send-message-title="{{sendMessageTitle}}" send-message-path="{{sendMessagePath}}" send-message-img="{{sendMessageImg}}" show-message-card="{{true}}" data-event-opts="{{[['tap',[['contactServicer',['$event']]]]]}}" bindtap="__e"></button><uni-popup class="vue-ref" vue-id="8debb67c-1" type="center" data-ref="servicePopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="service-popup-wrap"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="head-wrap" bindtap="__e"><text>联系客服</text><text class="iconfont icon-close"></text></view><view class="body-wrap">{{siteInfo.site_tel?'请联系客服，客服电话是'+siteInfo.site_tel:'抱歉，商家暂无客服，请线下联系'}}</view></view></uni-popup></view>