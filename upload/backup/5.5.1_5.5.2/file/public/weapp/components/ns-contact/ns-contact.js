(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-contact/ns-contact"],{"0820":function(e,t,n){"use strict";var i=n("3fa7"),s=n.n(i);s.a},"3fa7":function(e,t,n){},5036:function(e,t,n){"use strict";n.r(t);var i=n("a39b"),s=n("5323");for(var o in s)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return s[e]}))}(o);n("0820");var a=n("828b"),c=Object(a["a"])(s["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=c.exports},5323:function(e,t,n){"use strict";n.r(t);var i=n("90b7"),s=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=s.a},"90b7":function(e,t,n){"use strict";(function(e,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"ns-contact",props:{niushop:{type:Object,default:function(){return{}}},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""}},data:function(){return{config:null,openType:""}},created:function(){this.servicerConfig&&(this.config=this.servicerConfig.weapp,"weapp"==this.config.type&&(this.openType="contact"))},methods:{contactServicer:function(){if("none"==this.config.type&&this.$refs.servicePopup.open(),"contact"!=this.openType)switch(this.config.type){case"wxwork":e.openCustomerServiceChat({extInfo:{url:this.config.wxwork_url},corpId:this.config.corpid,showMessageCard:!0,sendMessageTitle:this.sendMessageTitle,sendMessagePath:this.sendMessagePath,sendMessageImg:this.sendMessageImg});break;case"third":location.href=this.config.third_url;break;case"niushop":this.$util.redirectTo("/pages_tool/chat/room",this.niushop);break;default:this.makePhoneCall()}},makePhoneCall:function(){this.$api.sendRequest({url:"/api/site/shopcontact",success:function(e){0==e.code&&e.data.mobile&&n.makePhoneCall({phoneNumber:e.data.mobile})}})}}};t.default=i}).call(this,n("3223")["default"],n("df3c")["default"])},a39b:function(e,t,n){"use strict";n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uniPopup:function(){return n.e("components/uni-popup/uni-popup").then(n.bind(null,"d745"))}},s=function(){var e=this,t=e.$createElement;e._self._c;e._isMounted||(e.e0=function(t){return e.$refs.servicePopup.close()})},o=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-contact/ns-contact-create-component',
    {
        'components/ns-contact/ns-contact-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("5036"))
        })
    },
    [['components/ns-contact/ns-contact-create-component']]
]);
