<block wx:if="{{visibleSync}}"><view data-event-opts="{{[['touchmove',[['moveHandle',['$event']]]]]}}" class="{{['uni-drawer',(showDrawer)?'uni-drawer--visible':'',(rightMode)?'uni-drawer--right':'']}}" catchtouchmove="__e"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="uni-drawer__mask" bindtap="__e"></view><view class="{{['uni-drawer__content',(isIphoneX)?'safe-area':'']}}"><slot></slot></view></view></block>