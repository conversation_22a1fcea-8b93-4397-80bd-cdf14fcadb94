(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/payment/payment"],{"1f3b":function(e,t,n){"use strict";n.r(t);var a=n("e444"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},"3dc5":function(e,t,n){"use strict";var a=n("d200"),o=n.n(a);o.a},"82bc":function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var a={uniPopup:function(){return n.e("components/uni-popup/uni-popup").then(n.bind(null,"d745"))},nsSwitch:function(){return n.e("components/ns-switch/ns-switch").then(n.bind(null,"b0ec"))}},o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.payInfo?e._f("moneyFormat")(e.payMoney):null),a=e.payInfo&&e.balanceDeduct>0&&e.balanceUsable&&1==e.balanceConfig?e._f("moneyFormat")(e.balanceDeduct):null,o=e.payInfo&&e.payMoney>0?e.payTypeList.length:null;e._isMounted||(e.e0=function(t,n){var a=arguments[arguments.length-1].currentTarget.dataset,o=a.eventParams||a["event-params"];n=o.index;e.payIndex=n}),e.$mp.data=Object.assign({},{$root:{f0:n,f1:a,g0:o}})},i=[]},b6f2:function(e,t,n){"use strict";n.r(t);var a=n("82bc"),o=n("1f3b");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("3dc5");var s=n("828b"),c=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"dd3b5764",null,!1,a["a"],void 0);t["default"]=c.exports},d200:function(e,t,n){},e444:function(e,t,n){"use strict";(function(e,a){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n("7ca3"));function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var r={name:"payment",components:{uniPopup:function(){n.e("components/uni-popup/uni-popup").then(function(){return resolve(n("d745"))}.bind(null,n)).catch(n.oe)},nsSwitch:function(){n.e("components/ns-switch/ns-switch").then(function(){return resolve(n("b0ec"))}.bind(null,n)).catch(n.oe)}},props:{balanceUsable:{type:Boolean,default:!0}},data:function(){return{payIndex:0,payTypeList:[{name:"微信支付",icon:"icon-weixin1",type:"wechatpay"},{name:"线下支付",icon:"icondiy icon-yuezhifu",type:"offlinepay"}],payInfo:null,balanceConfig:0,sale:!0,isBalance:0,balance:0,resetPayComplete:!0,repeatFlag:!1}},created:function(e){this.getPayType(),this.balanceUsable&&this.getBalanceConfig()},computed:{balanceDeduct:function(){var e=0;return this.payInfo&&this.balance&&(e=this.balance>this.payInfo.pay_money?this.payInfo.pay_money:this.balance),e},payMoney:function(){var e=0;return this.payInfo&&(e=this.payInfo.pay_money,this.balanceDeduct&&this.isBalance&&this.balanceUsable&&(e=this.payInfo.pay_money-this.balanceDeduct)),e},offlineShow:function(){var e=getCurrentPages(),t=e[e.length-1],n=t.route;return!!this.$store.state.offlineWhiteList.length&&this.$store.state.offlineWhiteList.includes(n)}},methods:{pageShow:function(){if(this.payInfo){var t=e.getStorageSync("offlinepay");t&&(e.removeStorageSync("offlinepay"),this.close())}else e.removeStorageSync("offlinepay")},close:function(){this.$emit("close"),this.$refs.choosePaymentPopup.close()},useBalance:function(){this.isBalance=this.isBalance?0:1,this.$emit("useBalance",this.isBalance)},confirm:function(){0==this.payTypeList.length&&this.payMoney>0?this.$util.showToast({title:"请选择支付方式！"}):0!=this.resetPayComplete?(e.showLoading({title:"支付中...",mask:!0}),this.repeatFlag||(this.repeatFlag=!0,this.pay(),e.setStorageSync("pay_flag",1))):this.$util.showToast({title:"支付取消中，请稍后再试！"})},getPayInfo:function(e,t){var n=this;this.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:e},success:function(e){e.code>=0&&e.data?(n.payInfo=e.data,n.balanceConfig&&n.balanceUsable&&n.getMemberBalance(),setTimeout((function(){n.$refs.choosePaymentPopup.open(),"function"==typeof t&&t()}))):n.$util.showToast({title:"未获取到支付信息！"})}})},getBalanceConfig:function(){var e=this;this.$api.sendRequest({url:"/api/pay/getBalanceConfig",data:{},success:function(t){e.balanceConfig=t.data.balance_show}})},getMemberBalance:function(){var e=this;this.$api.sendRequest({url:"/api/memberaccount/usablebalance",success:function(t){0==t.code&&t.data&&(e.balance=parseFloat(t.data.usable_balance))}})},getPayType:function(){var e=this;this.$api.sendRequest({url:"/api/pay/type",success:function(t){0==t.code&&(""==t.data.pay_type?e.payTypeList=[]:e.payTypeList=e.payTypeList.filter((function(e,n){return-1!=t.data.pay_type.indexOf(e.type)})))}})},pay:function(){var t=this,n=this.payTypeList[this.payIndex];this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:this.payInfo.out_trade_no,pay_type:n?n.type:"",scene:e.getStorageSync("is_test")?1175:a.getLaunchOptionsSync().scene,is_balance:this.isBalance},success:function(o){if(e.hideLoading(),o.code>=0){if(o.data.pay_success)return t.paySuccess(),void(t.repeatFlag=!1);if("offlinepay"==n.type)t.$util.redirectTo("/pages_tool/pay/offlinepay",{outTradeNo:t.payInfo.out_trade_no}),t.repeatFlag=!1;else{var i=o.data.data,s=e.getStorageSync("is_test")?1175:a.getLaunchOptionsSync().scene;if(-1!=[1175,1176,1177,1191,1195].indexOf(s))return void e.requestOrderPayment({timeStamp:i.timeStamp,nonceStr:i.nonceStr,package:i.package,signType:i.signType,paySign:i.paySign,success:function(e){t.paySuccess(),t.repeatFlag=!1},fail:function(n){t.flag=!1,"requestOrderPayment:fail cancel"==n.errMsg?(t.$util.showToast({title:"您已取消支付"}),t.resetpay(),t.repeatFlag=!1):(e.showModal({content:"支付失败,失败原因: "+n.errMsg,showCancel:!1}),setTimeout((function(){t.close(),t.repeatFlag=!1}),1500))}});e.requestPayment(c(c({provider:n.provider},i),{},{success:function(e){t.paySuccess(),t.repeatFlag=!1},fail:function(n){t.flag=!1,"requestPayment:fail cancel"==n.errMsg?(t.$util.showToast({title:"您已取消支付"}),t.resetpay(),t.repeatFlag=!1):(e.showModal({content:"支付失败,失败原因: "+n.errMsg,showCancel:!1}),setTimeout((function(){t.close(),t.repeatFlag=!1}),1500))}}))}}else t.$util.showToast({title:o.message}),t.repeatFlag=!1},fail:function(n){e.hideLoading(),t.$util.showToast({title:"request:fail"}),t.repeatFlag=!1}})},paySuccess:function(){"BlindboxGoodsOrderPayNotify"==this.payInfo.event?this.$util.redirectTo("/pages_promotion/blindbox/index",{outTradeNo:this.payInfo.out_trade_no},"redirectTo"):this.payInfo.return_url?-1!=this.payInfo.return_url.indexOf("http://")||-1!=this.payInfo.return_url.indexOf("https://")?location.replace(this.payInfo.return_url):this.$util.redirectTo(this.payInfo.return_url,{},"redirectTo"):this.$util.redirectTo("/pages_tool/pay/result",{code:this.payInfo.out_trade_no},"redirectTo")},resetpay:function(){var e=this;this.resetPayComplete=!1,this.$api.sendRequest({url:"/api/pay/resetpay",data:{out_trade_no:this.payInfo.out_trade_no},success:function(t){0==t.code?e.getPayInfo(t.data,(function(){e.resetPayComplete=!0})):e.resetPayComplete=!0},fail:function(t){e.resetPayComplete=!0}})}}};t.default=r}).call(this,n("df3c")["default"],n("3223")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/payment/payment-create-component',
    {
        'components/payment/payment-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("b6f2"))
        })
    },
    [['components/payment/payment-create-component']]
]);
