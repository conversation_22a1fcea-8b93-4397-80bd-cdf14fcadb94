<view class="data-v-dd3b5764"><block wx:if="{{payInfo}}"><uni-popup vue-id="489a2b34-1" type="center" mask-click="{{false}}" data-ref="choosePaymentPopup" class="data-v-dd3b5764 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="choose-payment-popup popup data-v-dd3b5764" catchtouchmove="__e"><view class="popup-header data-v-dd3b5764"><text class="tit data-v-dd3b5764">支付方式</text><text data-event-opts="{{[['tap',[['close']]]]}}" class="iconfont icon-close data-v-dd3b5764" bindtap="__e"></text></view><scroll-view class="popup-body data-v-dd3b5764" scroll-y="true"><view class="pay-money data-v-dd3b5764"><text class="money data-v-dd3b5764">{{"支付金额"+$root.f0+"元"}}</text></view><block wx:if="{{balanceDeduct>0&&balanceUsable&&balanceConfig==1}}"><view class="payment-item data-v-dd3b5764"><view class="iconfont icon-yue data-v-dd3b5764"></view><view class="info-wrap data-v-dd3b5764"><text class="name data-v-dd3b5764">余额抵扣</text><view class="money data-v-dd3b5764">{{"可用¥"+$root.f1}}</view></view><ns-switch class="balance-switch data-v-dd3b5764" vue-id="{{('489a2b34-2')+','+('489a2b34-1')}}" checked="{{isBalance==1}}" data-event-opts="{{[['^change',[['useBalance']]]]}}" bind:change="__e" bind:__l="__l"></ns-switch></view></block><block wx:if="{{payMoney>0}}"><block class="data-v-dd3b5764"><block wx:if="{{$root.g0}}"><block class="data-v-dd3b5764"><block wx:for="{{payTypeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-dd3b5764"><block wx:if="{{offlineShow||item.type!='offlinepay'}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" class="payment-item data-v-dd3b5764" bindtap="__e"><view class="{{['iconfont','data-v-dd3b5764',item.icon]}}"></view><text class="name data-v-dd3b5764">{{item.name}}</text><text class="{{['iconfont','data-v-dd3b5764',payIndex==index?'icon-yuan_checked color-base-text':'icon-checkboxblank']}}"></text></view></block></block></block></block></block><block wx:else><block class="data-v-dd3b5764"><view class="empty data-v-dd3b5764">平台尚未配置支付方式！</view></block></block></block></block></scroll-view><view class="popup-footer data-v-dd3b5764"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirm-btn color-base-bg data-v-dd3b5764" bindtap="__e">确认支付</view></view></view></uni-popup></block></view>