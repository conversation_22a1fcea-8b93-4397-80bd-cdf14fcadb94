(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-goods-sku/ns-goods-sku-category"],{"03795":function(t,i,s){"use strict";s.r(i);var e=s("2b8b"),o=s.n(e);for(var u in e)["default"].indexOf(u)<0&&function(t){s.d(i,t,(function(){return e[t]}))}(u);i["default"]=o.a},"03b8":function(t,i,s){},"2b8b":function(t,i,s){"use strict";(function(t){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var e={name:"ns-goods-sku-category",components:{uniPopup:function(){s.e("components/uni-popup/uni-popup-sku-new").then(function(){return resolve(s("2c08"))}.bind(null,s)).catch(s.oe)}},props:{disabled:{type:Boolean,default:!1},pointLimit:{type:[Number,String]},popupType:{type:String,default:"center"}},data:function(){return{systemInfo:{},number:0,btnSwitch:!1,goodsId:0,skuId:0,limitNumber:0,goodsDetail:{},goodsSkuList:{},maxBuy:0,minBuy:0,isLoad:!1,timeout:null,goodsForm:null,skuList:[]}},created:function(){this.systemInfo=t.getSystemInfoSync()},watch:{pointLimit:function(t,i){this.limitNumber=Number(t)}},computed:{cartInputLock:function(){return!this.isLoad||this.number>0}},methods:{calcSkuStock:function(){var t=this;this.goodsDetail.goods_spec_format.forEach((function(i){i.value.forEach((function(i){t.skuList.forEach((function(s){i.sku_id==s.sku_id&&t.$set(i,"stock",s.stock)}))}))}))},show:function(t){this.number=0,this.isLoad=!0,this.goodsDetail=t,this.goodsId=this.goodsDetail.goods_id,this.skuId=this.goodsDetail.sku_id,this.maxBuy=this.goodsDetail.max_buy,this.minBuy=this.goodsDetail.min_buy,this.goodsForm=null,this.getGoodsForm(),this.getGoodsSkuList(this.goodsId),this.keyInput(!1),this.getCurrentCart()&&(this.number=this.getCurrentCart().num),this.$refs.skuPopup.open()},getGoodsForm:function(){var t=this;this.$api.sendRequest({url:"/form/api/form/goodsform",data:{goods_id:this.goodsDetail.goods_id},success:function(i){0==i.code&&i.data&&(t.goodsForm=i.data)}})},hide:function(){this.$refs.skuPopup&&this.$refs.skuPopup.close()},change:function(t,i){if(!this.disabled){this.btnSwitch=!1,this.skuId=t;for(var s=0;s<this.goodsDetail.goods_spec_format.length;s++)for(var e=this.goodsDetail.goods_spec_format[s],o=0;o<e.value.length;o++)i==this.goodsDetail.goods_spec_format[s].value[o].spec_id&&(this.goodsDetail.goods_spec_format[s].value[o].selected=!1);this.isLoad=!0,this.goodsSkuList["sku_"+t]&&(this.goodsDetail=Object.assign({},this.goodsDetail,this.goodsSkuList["sku_"+t])),this.getCurrentCart()?this.number=this.getCurrentCart().num:this.number=0,this.calcSkuStock()}},previewMedia:function(){var i=[];i.push(this.$util.img(this.goodsDetail.sku_image,{size:"big"})),t.previewImage({current:1,urls:i})},getGoodsSkuList:function(t){var i=this;this.$api.sendRequest({url:"/api/goodssku/goodsSkuByCategory",data:{goods_id:t},success:function(t){if(t.code>=0){t.data;var s={};t.data.forEach((function(e,o){e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format)),e.goods_spec_format&&(e.goods_spec_format=JSON.parse(e.goods_spec_format)),1==e.promotion_type&&(e.discountTimeMachine=i.$util.countDown(e.end_time-t.timestamp)),1==e.promotion_type&&e.discountTimeMachine?e.member_price>0&&Number(e.member_price)<=Number(e.discount_price)?e.show_price=e.member_price:e.show_price=e.discount_price:e.member_price>0?e.show_price=e.member_price:e.show_price=e.price,s["sku_"+e.sku_id]=e})),i.goodsSkuList=s,i.skuList=t.data,i.calcSkuStock()}}})},changeNum:function(i,s){var e=this;if(0!=this.goodsDetail.stock&&!this.btnSwitch){var o=this.goodsDetail.stock,u=1;if(1==this.goodsDetail.is_limit&&this.maxBuy>0&&this.maxBuy<o&&(o=this.maxBuy),1==this.goodsDetail.is_limit&&2==this.goodsDetail.limit_type&&this.maxBuy>0&&this.goodsDetail.purchased_num>0){var n=this.maxBuy-this.goodsDetail.purchased_num;o=n<this.goodsDetail.stock?n:this.goodsDetail.stock}if(this.minBuy>1&&(u=this.minBuy),"+"==i){if(this.number<o)this.number++;else{if(this.number>=this.goodsDetail.stock)return void this.$util.showToast({title:"库存不足"});if(1==this.goodsDetail.is_limit&&this.maxBuy>0){if(1==this.goodsDetail.limit_type)return void this.$util.showToast({title:"该商品每次最多购买"+this.maxBuy+this.goodsDetail.unit});if(2==this.goodsDetail.limit_type){var r="该商品每人限购"+this.maxBuy+this.goodsDetail.unit;return r+=this.goodsDetail.purchased_num>0?"，您已购买了"+this.goodsDetail.purchased_num+this.goodsDetail.unit:"",void this.$util.showToast({title:r})}}}var a=t.createSelectorQuery().in(this);a.select("#"+s.currentTarget.id+" .click-event").boundingClientRect((function(t){t&&e.$emit("addCart",t.left,t.top)})).exec()}else"-"==i&&(this.number>u?this.number-=1:this.number=0);this.number>this.limitNumber&&this.limitNumber&&(this.number=this.limitNumber),this.number?this.cartNumChange(this.number):this.deleteCart()}},blur:function(){var t=this;if(this.number||(this.number=1),this.number>this.limitNumber&&this.limitNumber&&(this.number=this.limitNumber),this.goodsDetail.is_limit&&this.maxBuy>0){var i=this.maxBuy-this.goodsDetail.purchased_num;this.number>i&&(this.number=i)}this.number<this.minBuy&&this.minBuy>0&&(this.number=this.minBuy),this.number<=0&&(this.number=1);var s=parseInt(this.number);this.isLoad=!0,setTimeout((function(){t.number=s,t.cartNumChange(t.number)}),0)},keyInput:function(t,i,s){var e=this;s&&(this.isLoad=!1),setTimeout((function(){var s=e.goodsDetail.stock;0!=e.goodsDetail.stock?(t&&0==e.number.length&&(e.number=1),t&&(e.number<=0||isNaN(e.number))&&(e.number=1),e.number>s&&(e.number=s),t&&e.minBuy>1&&e.number<e.minBuy&&(e.number=e.minBuy),t&&(e.number=parseInt(e.number)),i&&i()):e.number=0}),0)},confirm:function(i){var s=this;if(this.storeToken)if(0!=this.goodsDetail.goods_state){if(this.$refs.form){if(!this.$refs.form.verify())return;t.setStorageSync("goodFormData",{goods_id:this.goodsDetail.goods_id,form_data:this.$refs.form.formData})}this.number=1,this.keyInput(!0,(function(){if(0!=s.goodsDetail.stock)if(s.number>s.goodsDetail.stock)s.$util.showToast({title:"库存不足"});else if(1==s.goodsDetail.is_limit&&1==s.goodsDetail.limit_type&&s.maxBuy>0&&s.number>s.maxBuy)s.$util.showToast({title:"该商品每次最多购买"+s.maxBuy+s.goodsDetail.unit});else{if(1==s.goodsDetail.is_limit&&2==s.goodsDetail.limit_type&&s.maxBuy>0&&s.number+s.goodsDetail.purchased_num>s.maxBuy){var t="该商品每人限购"+s.maxBuy+s.goodsDetail.unit;return t+=s.goodsDetail.purchased_num>0?"，您已购买了"+s.goodsDetail.purchased_num+s.goodsDetail.unit:"",void s.$util.showToast({title:t})}s.$emit("addCart",i.detail.x,i.detail.y),s.btnSwitch||(s.btnSwitch=!0,s.$api.sendRequest({url:"/api/cart/add",data:{sku_id:s.skuId,num:s.number},success:function(t){var i=t.data;if(i>0){if(s.getCurrentCart())s.cartList["goods_"+s.goodsId]["sku_"+s.skuId].num=s.number;else{s.cartList["goods_"+s.goodsId]||(s.cartList["goods_"+s.goodsId]={});var e=s.goodsDetail.discount_price;s.goodsDetail.member_price>0&&Number(s.goodsDetail.member_price)<=Number(s.goodsDetail.discount_price)&&(e=s.goodsDetail.member_price),s.cartList["goods_"+s.goodsId]["sku_"+s.skuId]={cart_id:i,goods_id:s.goodsId,sku_id:s.skuId,num:s.number,discount_price:e}}s.$store.dispatch("cartCalculate"),s.$emit("refresh"),s.$util.showToast({title:"加入购物车成功"})}s.btnSwitch=!1},fail:function(t){s.$refs.skuPopup&&s.$refs.skuPopup.close(),s.btnSwitch=!1}}))}else s.$util.showToast({title:"商品已售罄"})}))}else this.$util.showToast({title:"商品已下架"});else this.$refs.login.open()},closeSkuPopup:function(){this.$refs.skuPopup&&this.$refs.skuPopup.close()},imageError:function(){this.goodsDetail.sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},valueImageError:function(t,i){this.goodsDetail.goods_spec_format[t].value[i].image=this.$util.getDefaultImage().goods,this.$forceUpdate()},cartNumChange:function(t){var i=this;t<1&&(t=1),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout((function(){i.$api.sendRequest({url:"/api/cart/edit",data:{num:t,cart_id:i.getCurrentCart().cart_id},success:function(s){s.code>=0&&(i.cartList["goods_"+i.goodsId]["sku_"+i.skuId].num=t,i.$store.dispatch("cartCalculate"),i.$emit("refresh"))}})}),800)},deleteCart:function(){var t=this;this.timeout&&clearTimeout(this.timeout),this.$api.sendRequest({url:"/api/cart/delete",data:{cart_id:this.getCurrentCart().cart_id},success:function(i){i.code>=0&&(delete t.cartList["goods_"+t.goodsId]["sku_"+t.skuId],2==Object.keys(t.cartList["goods_"+t.goodsId]).length&&delete t.cartList["goods_"+t.goodsId],t.$store.dispatch("cartCalculate"),t.$emit("refresh"))}})},getCurrentCart:function(){var t=this.cartList["goods_"+this.goodsId],i=null;return t&&t["sku_"+this.skuId]&&(i=t["sku_"+this.skuId]),i}}};i.default=e}).call(this,s("df3c")["default"])},"5f0e":function(t,i,s){"use strict";s.r(i);var e=s("da72"),o=s("03795");for(var u in o)["default"].indexOf(u)<0&&function(t){s.d(i,t,(function(){return o[t]}))}(u);s("ff84");var n=s("828b"),r=Object(n["a"])(o["default"],e["b"],e["c"],!1,null,"1f8b20e2",null,!1,e["a"],void 0);i["default"]=r.exports},da72:function(t,i,s){"use strict";s.d(i,"b",(function(){return o})),s.d(i,"c",(function(){return u})),s.d(i,"a",(function(){return e}));var e={uniPopup:function(){return s.e("components/uni-popup/uni-popup").then(s.bind(null,"d745"))},nsForm:function(){return s.e("components/ns-form/ns-form").then(s.bind(null,"ae30"))},nsLogin:function(){return Promise.all([s.e("common/vendor"),s.e("components/ns-login/ns-login")]).then(s.bind(null,"2910"))}},o=function(){var t=this,i=t.$createElement,s=(t._self._c,t.$util.img(t.goodsDetail.sku_image,{size:"mid"})),e=parseFloat(t.goodsDetail.min_buy),o=t.__map(t.goodsDetail.goods_spec_format,(function(i,s){var e=t.__get_orig(i),o=t.__map(i.value,(function(i,s){var e=t.__get_orig(i),o=i.image?t.$util.img(i.image,{size:"small"}):null;return{$orig:e,g1:o}}));return{$orig:e,l0:o}}));t.$mp.data=Object.assign({},{$root:{g0:s,m0:e,l1:o}})},u=[]},ff84:function(t,i,s){"use strict";var e=s("03b8"),o=s.n(e);o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-goods-sku/ns-goods-sku-category-create-component',
    {
        'components/ns-goods-sku/ns-goods-sku-category-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("5f0e"))
        })
    },
    [['components/ns-goods-sku/ns-goods-sku-category-create-component']]
]);
