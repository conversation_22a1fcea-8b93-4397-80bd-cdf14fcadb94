<view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="goods-sku" catchtouchmove="__e"><uni-popup class="sku-layer vue-ref" vue-id="18423b42-1" type="bottom" data-ref="skuPopup" data-event-opts="{{[['^change',[['popclose']]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="sku-info" style="{{'height:'+(skuHeight)+';'}}"><view class="header"><block wx:if="{{type=='point'&&goodsDetail.type&&goodsDetail.type!=1}}"><block><block wx:if="{{goodsDetail.type==2}}"><view class="img-wrap"><image src="{{goodsDetail.image?$root.g0:$root.g1}}" mode="aspectFit"></image></view></block><block wx:if="{{goodsDetail.type==3}}"><view class="img-wrap"><image src="{{goodsDetail.image?$root.g2:$root.g3}}" mode="aspectFit"></image></view></block></block></block><block wx:else><view data-event-opts="{{[['tap',[['previewMedia']]]]}}" class="img-wrap" bindtap="__e"><image src="{{$root.g4}}" mode="aspectFit" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e"></image></view></block><view class="main"><block wx:if="{{type=='point'}}"><block><block wx:if="{{goodsDetail.type==1}}"><block><view class="price-wrap"><text class="price price-color font-size-toolbar">{{goodsDetail.point+"积分"}}</text><block wx:if="{{goodsDetail.exchange_price!='0.00'&&goodsDetail.pay_type>0}}"><block><text class="unit price-color font-size-tag">+</text><text class="price price-color font-size-toolbar price-font">{{$root.m0+goodsDetail.exchange_price}}</text></block></block></view><block wx:if="{{goodsDetail.stock_show}}"><view class="stock">{{'库存'+goodsDetail.stock+goodsDetail.unit}}</view></block></block></block><block wx:else><block><view class="price-wrap"><view class="price font-size-toolbar">{{goodsDetail.name}}</view></view><view class="stock" style="height:auto;">积分：<text class="price-color">{{goodsDetail.point}}</text></view><view class="stock">库存：<text class="color-base-text">{{goodsDetail.stock+goodsDetail.unit}}</text></view></block></block></block></block><block wx:else><block wx:if="{{type=='presale'}}"><block><view class="price-wrap"><text class="unit price-style small">￥</text><text class="price price-style large">{{$root.g5[0]}}</text><text class="unit price-style small">{{"."+$root.g6[1]}}</text><block wx:if="{{balance>0}}"><view class="balance"><text>尾款</text><text class="unit price-color font-size-tag price-font">￥</text><text class="price-color price-font">{{balance}}</text></view></block></view><view class="stock presale-stock"><block wx:if="{{goodsDetail.stock_show}}"><block>{{"库存"+goodsDetail.stock+goodsDetail.unit+''}}</block></block></view></block></block><block wx:else><block><block wx:if="{{type=='pintuan'}}"><view class="price-wrap"><block wx:if="{{goodsDetail.pintuan_type=='ordinary'}}"><block><block wx:if="{{goodsDetail.group_id&&goodsDetail.group_id>0}}"><block><text class="unit price-style small">￥</text><text class="price price-style large">{{$root.g7[0]}}</text><text class="unit price-style small">{{"."+$root.g8[1]}}</text></block></block><block wx:else><block><text class="unit price-style small">￥</text><text class="price price-style large">{{$root.g9[0]}}</text><text class="unit price-style small">{{"."+$root.g10[1]}}</text></block></block></block></block><block wx:if="{{goodsDetail.pintuan_type=='ladder'}}"><block><text class="unit price-style small">￥</text><text class="price price-style large">{{$root.g11[0]}}</text><text class="unit price-style small">{{"."+$root.g12[1]}}</text></block></block></view></block><block wx:else><block wx:if="{{type=='groupbuy'}}"><view class="price-wrap"><text class="unit price-style small">￥</text><text class="price price-style large">{{$root.g13[0]}}</text><text class="unit price-style small">{{"."+$root.g14[1]}}</text></view></block><block wx:else><block wx:if="{{type=='topic'}}"><view class="price-wrap"><text class="unit price-style small">￥</text><text class="price price-style large">{{$root.g15[0]}}</text><text class="unit price-style small">{{"."+$root.g16[1]}}</text></view></block><block wx:else><block wx:if="{{type=='seckill'}}"><view class="price-wrap"><text class="unit price-style small">￥</text><text class="price price-style large">{{$root.g17[0]}}</text><text class="unit price-style small">{{"."+$root.g18[1]}}</text></view></block><block wx:else><view class="price-wrap"><text class="unit price-style small">￥</text><block wx:if="{{goodsDetail.discount_price>0}}"><block><block wx:if="{{goodsDetail.member_price}}"><block><block wx:if="{{$root.m1<$root.m2}}"><text class="price price-style large">{{$root.g19[0]}}</text></block><block wx:else><text class="price price-style large">{{$root.g20[0]}}</text></block><block wx:if="{{$root.m3<$root.m4}}"><text class="unit price-style small">{{"."+$root.g21[1]}}</text></block><block wx:else><text class="unit price-style small">{{"."+$root.g22[1]}}</text></block></block></block><block wx:else><block><block wx:if="{{$root.m5<$root.m6}}"><text class="price price-style large">{{$root.g23[0]}}</text></block><block wx:else><text class="price price-style large">{{$root.g24[0]}}</text></block><block wx:if="{{$root.m7<$root.m8}}"><text class="unit price-style small">{{"."+$root.g25[1]}}</text></block><block wx:else><text class="unit price-style small">{{"."+$root.g26[1]}}</text></block></block></block></block></block><block wx:else><block><block wx:if="{{goodsDetail.member_price}}"><text class="price price-style large">{{$root.g27[0]}}</text></block><block wx:else><text class="price price-style large">{{$root.g28[0]}}</text></block><block wx:if="{{goodsDetail.member_price}}"><text class="unit price-style small">{{"."+$root.g29[1]}}</text></block><block wx:else><text class="unit price-style small">{{"."+$root.g30[1]}}</text></block></block></block></view></block></block></block></block><block wx:if="{{goodsDetail.stock_show}}"><view class="stock">{{"库存"+goodsDetail.stock+goodsDetail.unit}}</view></block></block></block></block><block wx:if="{{goodsDetail.sku_spec_format}}"><view class="sku-name font-size-tag"><text class="color-tip">已选规格：</text><block wx:for="{{goodsDetail.sku_spec_format}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text class="spec-value">{{item.spec_value_name}}</text></block></view></block></view><view data-event-opts="{{[['tap',[['closeSkuPopup']]]]}}" class="sku-close iconfont icon-close" bindtap="__e"></view></view><view class="body-item"><scroll-view class="wrap" scroll-y="{{true}}"><block wx:if="{{type=='pintuan'&&goodsDetail.pintuan_type=='ladder'&&!goodsDetail.group_id}}"><view class="sku-list-wrap"><text class="title font-size-base"></text><view data-event-opts="{{[['tap',[['pintuanChange',['pintuan_num']]]]]}}" class="{{['items',(pintuan_num_field=='pintuan_num')?'selected':'']}}" bindtap="__e"><text>{{goodsDetail.pintuan_num+"人团"}}</text></view><block wx:if="{{goodsDetail.pintuan_num_2>0}}"><view data-event-opts="{{[['tap',[['pintuanChange',['pintuan_num_2']]]]]}}" class="{{['items',(pintuan_num_field=='pintuan_num_2')?'selected':'']}}" bindtap="__e"><text>{{goodsDetail.pintuan_num_2+"人团"}}</text></view></block><block wx:if="{{goodsDetail.pintuan_num_3>0}}"><view data-event-opts="{{[['tap',[['pintuanChange',['pintuan_num_3']]]]]}}" class="{{['items',(pintuan_num_field=='pintuan_num_3')?'selected':'']}}" bindtap="__e"><text>{{goodsDetail.pintuan_num_3+"人团"}}</text></view></block></view></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sku-list-wrap"><text class="title font-size-base">{{item.$orig.spec_name}}</text><block wx:for="{{item.l0}}" wx:for-item="item_value" wx:for-index="index_value" wx:key="index_value"><view data-event-opts="{{[['tap',[['change',['$0','$1'],[[['goodsDetail.goods_spec_format','',index],['value','',index_value,'sku_id']],[['goodsDetail.goods_spec_format','',index],['value','',index_value,'spec_id']]]]]]]}}" class="{{['items',(item_value.$orig['selected']||skuId==item_value.$orig.sku_id)?'selected':'',(item_value.$orig['disabled']||!item_value.$orig['selected']&&disabled)?'disabled':'']}}" bindtap="__e"><block wx:if="{{item_value.$orig.image}}"><image src="{{item_value.g31}}" data-event-opts="{{[['error',[['valueImageError',[index,index_value]]]]]}}" binderror="__e"></image></block><text>{{item_value.$orig.spec_value_name}}</text><block wx:if="{{item_value.$orig.stock==0}}"><view class="empty-stock">缺货</view></block></view></block></view></block><view class="number-wrap"><view class="number-line"><block wx:if="{{type=='point'}}"><text class="title font-size-base">兑换数量</text></block><block wx:else><text class="title font-size-base">购买数量</text></block><block wx:if="{{limitNumber>0}}"><text class="limit-txt color-base-text">{{"(每人限购"+limitNumber+goodsDetail.unit+")"}}</text></block><block wx:if="{{maxBuy>0&&minBuy>1}}"><text class="limit-txt color-base-text">{{'('+minBuy+goodsDetail.unit+"起售，限购"+maxBuy+goodsDetail.unit+')'}}</text></block><block wx:else><block wx:if="{{maxBuy>0}}"><text class="limit-txt color-base-text">{{"(限购"+maxBuy+goodsDetail.unit+")"}}</text></block><block wx:else><block wx:if="{{minBuy>1}}"><text class="limit-txt color-base-text">{{"("+minBuy+goodsDetail.unit+"起售)"}}</text></block></block></block><view class="number"><button class="{{['decrease','color-line-border',(decreaseDisabled)?'disabled':'']}}" type="default" data-event-opts="{{[['tap',[['changeNum',['-']]]]]}}" bindtap="__e">-</button><input class="uni-input color-line-border font-size-goods-tag" type="number" placeholder="0" data-event-opts="{{[['blur',[['blur',['$event']]]],['input',[['__set_model',['','number','$event',[]]],['keyInput',[false]]]]]}}" value="{{number}}" bindblur="__e" bindinput="__e"/><button class="{{['increase','color-line-border',(increaseDisabled)?'disabled':'']}}" type="default" data-event-opts="{{[['tap',[['changeNum',['+']]]]]}}" bindtap="__e">+</button></view></view></view><block wx:if="{{goodsForm}}"><ns-form class="vue-ref" vue-id="{{('18423b42-2')+','+('18423b42-1')}}" data="{{goodsForm}}" data-ref="form" data-event-opts="{{[['^changeFormVal',[['changeFormVal']]]]}}" bind:changeFormVal="__e" bind:__l="__l"></ns-form></block></scroll-view></view><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="footer" bindtap="__e"><block wx:if="{{goodsDetail.stock&&goodsDetail.stock!=0&&goodsDetail.is_virtual==0&&type=='buy_now'}}"><button style="{{'background:'+(themeStyle.goods_detail.goods_btn_color_shallow)+';'+('color:'+('#fff')+';')}}" type="primary" data-event-opts="{{[['tap',[['confirm',[true]]]]]}}" catchtap="__e">加入购物车</button></block><block wx:if="{{type=='point'}}"><block><block wx:if="{{goodsDetail.type==1}}"><block><block wx:if="{{goodsDetail.point*number>memberPoint}}"><button disabled="true" type="primary">积分不足</button></block><block wx:else><block wx:if="{{goodsDetail.stock&&goodsDetail.stock!=0}}"><button type="primary">兑换</button></block><block wx:else><button type="primary" disabled="true">库存不足</button></block></block></block></block><block wx:else><block wx:if="{{goodsDetail.type==2||goodsDetail.type==3}}"><block><button type="primary">兑换</button></block></block></block></block></block><block wx:else><block wx:if="{{goodsDetail.goods_state==1}}"><block><block wx:if="{{goodsDetail.stock&&goodsDetail.stock!=0&&type=='join_cart'}}"><button type="primary">加入购物车</button></block><block wx:else><block wx:if="{{goodsDetail.stock&&goodsDetail.stock!=0&&type=='buy_now'}}"><button type="primary">立即购买</button></block><block wx:else><block wx:if="{{goodsDetail.stock&&goodsDetail.stock!=0&&type=='confirm'}}"><button type="primary">确认</button></block><block wx:else><block wx:if="{{goodsDetail.stock&&goodsDetail.stock!=0}}"><block wx:if="{{type!='pintuan'&&type!='pinfan'&&goodsDetail.buy_num}}"><block wx:if="{{goodsDetail.buy_num<=goodsDetail.stock}}"><button type="primary">立即抢购</button></block><block wx:else><button type="primary" disabled="true">库存不足</button></block></block><block wx:else><button type="primary">立即抢购</button></block></block><block wx:else><button type="primary" disabled="true">库存不足</button></block></block></block></block></block></block><block wx:else><button type="primary" disabled="true">该商品已下架</button></block></block></view></view></uni-popup><ns-login class="vue-ref" vue-id="18423b42-3" data-ref="login" bind:__l="__l"></ns-login></view>