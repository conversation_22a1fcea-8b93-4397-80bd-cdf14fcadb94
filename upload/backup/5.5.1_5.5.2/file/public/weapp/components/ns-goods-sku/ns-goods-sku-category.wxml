<view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="goods-sku data-v-1f8b20e2" catchtouchmove="__e"><uni-popup vue-id="3b19f8cf-1" type="{{popupType}}" data-ref="skuPopup" class="data-v-1f8b20e2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="sku-layer data-v-1f8b20e2"><view class="sku-content data-v-1f8b20e2"><view class="sku-info data-v-1f8b20e2" style="{{'height:'+(systemInfo.windowHeight*2+'rpx')+';'}}"><view class="header data-v-1f8b20e2"><view data-event-opts="{{[['tap',[['previewMedia']]]]}}" class="img-wrap data-v-1f8b20e2" bindtap="__e"><image src="{{$root.g0}}" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e" class="data-v-1f8b20e2"></image></view><view class="main data-v-1f8b20e2"><view class="goodname data-v-1f8b20e2">{{goodsDetail.goods_name}}</view><view class="other-info data-v-1f8b20e2"><block wx:if="{{goodsDetail.stock_show}}"><view class="stock color-tip data-v-1f8b20e2">{{'库存'+goodsDetail.stock+goodsDetail.unit}}</view></block><block wx:if="{{$root.m0}}"><view class="starting-num data-v-1f8b20e2">{{'起售'+goodsDetail.min_buy+'件'}}</view></block></view></view></view><view class="body-item data-v-1f8b20e2"><scroll-view class="wrap data-v-1f8b20e2" scroll-y="{{true}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sku-list-wrap data-v-1f8b20e2"><text class="title font-size-tag data-v-1f8b20e2">{{item.$orig.spec_name}}</text><view class="sku-list_item data-v-1f8b20e2"><block wx:for="{{item.l0}}" wx:for-item="item_value" wx:for-index="index_value" wx:key="index_value"><view data-event-opts="{{[['tap',[['change',['$0','$1'],[[['goodsDetail.goods_spec_format','',index],['value','',index_value,'sku_id']],[['goodsDetail.goods_spec_format','',index],['value','',index_value,'spec_id']]]]]]]}}" class="{{['items','color-line-border','font-size-tag','data-v-1f8b20e2',(item_value.$orig['selected']||skuId==item_value.$orig.sku_id)?'selected':'',(item_value.$orig['disabled']||!item_value.$orig['selected']&&disabled)?'disabled':'']}}" bindtap="__e"><block wx:if="{{item_value.$orig.image}}"><image src="{{item_value.g1}}" data-event-opts="{{[['error',[['valueImageError',[index,index_value]]]]]}}" binderror="__e" class="data-v-1f8b20e2"></image></block><text class="data-v-1f8b20e2">{{item_value.$orig.spec_value_name}}</text><block wx:if="{{item_value.$orig.stock==0}}"><view class="empty-stock data-v-1f8b20e2">缺货</view></block></view></block></view></view></block><block wx:if="{{goodsForm}}"><ns-form vue-id="{{('3b19f8cf-2')+','+('3b19f8cf-1')}}" data="{{goodsForm}}" data-ref="form" class="data-v-1f8b20e2 vue-ref" bind:__l="__l"></ns-form></block></scroll-view></view><view class="footer data-v-1f8b20e2"><view class="sku-name font-size-goods-tag data-v-1f8b20e2"><block wx:if="{{goodsDetail.sku_spec_format}}">已选择：<block wx:for="{{goodsDetail.sku_spec_format}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text class="color-tip data-v-1f8b20e2">{{item.spec_value_name}}</text></block></block></view><view class="footer-bottom data-v-1f8b20e2"><view class="footer-left data-v-1f8b20e2"><view class="price-wrap data-v-1f8b20e2"><text class="price price-font data-v-1f8b20e2">{{"￥"+goodsDetail.show_price}}</text></view></view><view class="footer-right data-v-1f8b20e2"><block wx:if="{{cartInputLock}}"><view class="change_num data-v-1f8b20e2"><view data-event-opts="{{[['tap',[['changeNum',['-']]]]]}}" class="num-action data-v-1f8b20e2" bindtap="__e"><text class="desc iconfont icon-jianshao color-base-text data-v-1f8b20e2"></text><view class="click-event data-v-1f8b20e2"></view></view><input class="uni-input data-v-1f8b20e2" type="number" placeholder="0" data-event-opts="{{[['blur',[['blur',['$event']]]],['input',[['__set_model',['','number','$event',[]]],['keyInput',[false,null,true]]]]]}}" value="{{number}}" bindblur="__e" bindinput="__e"/><view class="num-action data-v-1f8b20e2" id="{{'select-sku-num-'+goodsDetail.goods_id}}" data-event-opts="{{[['tap',[['changeNum',['+','$event']]]]]}}" bindtap="__e"><text class="add iconfont icon-add-fill color-base-text change_hover data-v-1f8b20e2"></text><view class="click-event data-v-1f8b20e2"></view></view></view></block><block wx:else><block wx:if="{{number==0&&isLoad}}"><view class="data-v-1f8b20e2"><view class="num-action data-v-1f8b20e2"><block wx:if="{{goodsDetail.stock&&goodsDetail.stock!=0}}"><button type="primary" data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" bindtap="__e" class="data-v-1f8b20e2">加入购物车</button></block><block wx:else><button type="primary" disabled="true" class="data-v-1f8b20e2">确定</button></block><view class="click-event data-v-1f8b20e2"></view></view></view></block></block></view></view></view></view></view></view><view data-event-opts="{{[['tap',[['closeSkuPopup']]]]}}" class="sku-close iconfont icon-close-guanbi data-v-1f8b20e2" bindtap="__e"></view></uni-popup><ns-login vue-id="3b19f8cf-3" data-ref="login" class="data-v-1f8b20e2 vue-ref" bind:__l="__l"></ns-login></view>