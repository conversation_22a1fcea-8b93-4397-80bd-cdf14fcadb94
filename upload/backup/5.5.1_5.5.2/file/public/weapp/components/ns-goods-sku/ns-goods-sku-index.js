(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-goods-sku/ns-goods-sku-index"],{"0c16":function(s,i,t){"use strict";t.r(i);var o=t("bcef"),e=t("cd53");for(var n in e)["default"].indexOf(n)<0&&function(s){t.d(i,s,(function(){return e[s]}))}(n);var d=t("828b"),u=Object(d["a"])(e["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);i["default"]=u.exports},"10c1":function(s,i,t){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o={name:"ns-goods-sku-index",components:{nsGoodsSku:function(){t.e("components/ns-goods-sku/ns-goods-sku").then(function(){return resolve(t("132d"))}.bind(null,t)).catch(t.oe)}},data:function(){return{timeout:{},isRepeat:!1,goodsDetail:{}}},created:function(){},methods:{addCart:function(s,i,t){if(this.storeToken)return"detail"==s||i.is_virtual?(this.$util.redirectTo("/pages/goods/detail",{goods_id:i.goods_id}),!1):void(i.goods_spec_format?this.multiSpecificationGoods(i):this.singleSpecificationGoods(i,t));this.$refs.login.open("/pages/index/index")},singleSpecificationGoods:function(s,i){var t=this,o=this.cartList["goods_"+s.goods_id]&&this.cartList["goods_"+s.goods_id]["sku_"+s.sku_id]?this.cartList["goods_"+s.goods_id]["sku_"+s.sku_id]:null,e=o?o.num:0,n=o&&o.cart_id?"/api/cart/edit":"/api/cart/add",d=s.min_buy>0?s.min_buy:1,u=e>=d?e:d,c=u;o&&o.cart_id&&(c+=s.min_buy>0?s.min_buy:1);var a=o?o.cart_id:0;if(c>parseInt(s.stock))this.$util.showToast({title:"商品库存不足"});else if(s.is_limit&&s.max_buy&&c>parseInt(s.max_buy))this.$util.showToast({title:"该商品每人限购".concat(s.max_buy).concat(s.unit||"件")});else{if(o)this.cartList["goods_"+s.goods_id]["sku_"+s.sku_id].num=c;else{this.cartList["goods_"+s.goods_id]||(this.cartList["goods_"+s.goods_id]={});var r=s.discount_price;s.member_price>0&&Number(s.member_price)<=Number(s.discount_price)&&(r=s.member_price),this.cartList["goods_"+s.goods_id]["sku_"+s.sku_id]={cart_id:a,goods_id:s.goods_id,sku_id:s.sku_id,num:c,discount_price:r}}this.isRepeat||(this.isRepeat=!0,this.$emit("addCart",i.currentTarget.id),this.$api.sendRequest({url:n,data:{cart_id:a,sku_id:s.sku_id,num:c},success:function(i){t.isRepeat=!1,0==i.code?(0==a&&(t.cartList["goods_"+s.goods_id]["sku_"+s.sku_id].cart_id=i.data),t.$util.showToast({title:"商品添加购物车成功"}),t.$store.commit("setCartChange"),t.$store.dispatch("cartCalculate"),t.$emit("cartListChange",t.cartList)):t.$util.showToast({title:i.message})}}))}},multiSpecificationGoods:function(s){var i=this;this.$api.sendRequest({url:"/api/goodssku/getInfoForCategory",data:{sku_id:s.sku_id},success:function(s){if(s.code>=0){var t=s.data;t.unit=t.unit||"件",t.sku_images?t.sku_images=t.sku_images.split(","):t.sku_images=[],t.goods_spec_format&&t.goods_image&&(t.goods_image=t.goods_image.split(","),t.sku_images=t.goods_image.concat(t.sku_images)),t.sku_spec_format&&(t.sku_spec_format=JSON.parse(t.sku_spec_format)),t.goods_spec_format&&(t.goods_spec_format=JSON.parse(t.goods_spec_format)),1==t.promotion_type&&(t.discountTimeMachine=i.$util.countDown(t.end_time-s.timestamp)),1==t.promotion_type&&t.discountTimeMachine?t.member_price>0&&Number(t.member_price)<=Number(t.discount_price)?t.show_price=t.member_price:t.show_price=t.discount_price:t.member_price>0?t.show_price=t.member_price:t.show_price=t.price,i.goodsDetail=t,i.$nextTick((function(){i.$refs.goodsSku&&i.$refs.goodsSku.show("join_cart",(function(s){var t=i.cartList["goods_"+s.goods_id],o=null;t&&t["sku_"+s.sku_id]&&(o=t["sku_"+s.sku_id]),o?i.cartList["goods_"+s.goods_id]["sku_"+s.sku_id].num=s.num:(i.cartList["goods_"+s.goods_id]||(i.cartList["goods_"+s.goods_id]={}),i.cartList["goods_"+s.goods_id]["sku_"+s.sku_id]={cart_id:s.cart_id,goods_id:s.goods_id,sku_id:s.sku_id,num:s.num,discount_price:s.discount_price}),i.$store.dispatch("cartCalculate"),i.$emit("cartListChange",i.cartList),setTimeout((function(){i.$store.commit("setCartChange")}),100)}))}))}}})},refreshGoodsSkuDetail:function(s){this.goodsDetail=Object.assign({},this.goodsDetail,s)}}};i.default=o},bcef:function(s,i,t){"use strict";t.d(i,"b",(function(){return e})),t.d(i,"c",(function(){return n})),t.d(i,"a",(function(){return o}));var o={nsLogin:function(){return Promise.all([t.e("common/vendor"),t.e("components/ns-login/ns-login")]).then(t.bind(null,"2910"))},nsGoodsSku:function(){return t.e("components/ns-goods-sku/ns-goods-sku").then(t.bind(null,"132d"))}},e=function(){var s=this.$createElement;this._self._c},n=[]},cd53:function(s,i,t){"use strict";t.r(i);var o=t("10c1"),e=t.n(o);for(var n in o)["default"].indexOf(n)<0&&function(s){t.d(i,s,(function(){return o[s]}))}(n);i["default"]=e.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-goods-sku/ns-goods-sku-index-create-component',
    {
        'components/ns-goods-sku/ns-goods-sku-index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("0c16"))
        })
    },
    [['components/ns-goods-sku/ns-goods-sku-index-create-component']]
]);
