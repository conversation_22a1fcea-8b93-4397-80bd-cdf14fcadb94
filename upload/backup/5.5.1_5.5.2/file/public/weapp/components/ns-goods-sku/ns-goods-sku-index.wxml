<view class="goods-sku"><ns-login class="vue-ref" vue-id="0c7f829d-1" data-ref="login" bind:__l="__l"></ns-login><block wx:if="{{goodsDetail.goods_id}}"><ns-goods-sku class="vue-ref" vue-id="0c7f829d-2" goods-id="{{goodsDetail.goods_id}}" goods-detail="{{goodsDetail}}" max-buy="{{goodsDetail.max_buy}}" min-buy="{{goodsDetail.min_buy}}" data-ref="goodsSku" data-event-opts="{{[['^refresh',[['refreshGoodsSkuDetail']]]]}}" bind:refresh="__e" bind:__l="__l"></ns-goods-sku></block></view>