(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-goods-action-button/ns-goods-action-button"],{"8e82":function(t,n,e){"use strict";e.r(n);var a=e("cae4"),o=e("db93");for(var u in o)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(u);e("9ed8");var d=e("828b"),i=Object(d["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=i.exports},"9daa":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a={name:"ns-goods-action-button",props:{text:{type:String,default:""},textPrice:{type:String,default:""},background:{type:String,default:""},backgroundClass:{type:String,default:""},backgroundColor:{type:String,default:""},disabled:{type:Boolean,default:!1},disabledText:{type:String,default:""},textColor:{type:String,default:""}},computed:{},methods:{clickEvent:function(){this.$emit("click")}}};n.default=a},"9ed8":function(t,n,e){"use strict";var a=e("d5641"),o=e.n(a);o.a},cae4:function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},o=[]},d5641:function(t,n,e){},db93:function(t,n,e){"use strict";e.r(n);var a=e("9daa"),o=e.n(a);for(var u in a)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(u);n["default"]=o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-goods-action-button/ns-goods-action-button-create-component',
    {
        'components/ns-goods-action-button/ns-goods-action-button-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("8e82"))
        })
    },
    [['components/ns-goods-action-button/ns-goods-action-button-create-component']]
]);
