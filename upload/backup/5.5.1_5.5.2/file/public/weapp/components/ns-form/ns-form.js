(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-form/ns-form"],{"3b17":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"ns-form",components:{pickRegions:function(){Promise.all([a.e("common/vendor"),a.e("components/pick-regions/pick-regions")]).then(function(){return resolve(a("04c1"))}.bind(null,a)).catch(a.oe)}},props:{data:{type:Array,default:{}},customAttr:{type:Object,default:function(){return{}}}},data:function(){return{formData:[]}},created:function(){this.setFormData()},watch:{data:function(){this.setFormData()},formData:{handler:function(t,e){this.$emit("changeFormVal",t)},deep:!0}},methods:{setFormData:function(){var t=this,e=JSON.parse(JSON.stringify(this.data));e.forEach((function(e){if(e.val||(e.val=e.value.default?e.value.default:""),e.value.options&&(e.option_lists=[],e.value.options.forEach((function(t,a){var i={};if(i.value=t,i.checked=!1,"Radio"==e.controller&&(!e.val&&0==a||e.val&&e.val==t)&&(i.checked=!0,e.val=t),"Checkbox"==e.controller&&e.val){var l=e.val.split(",");i.checked=-1!=l.indexOf(t)}e.option_lists.push(i)}))),"Img"==e.controller&&(e.img_lists=e.val?e.val.split(","):[]),"Date"!=e.controller||e.val||(e.value.is_show_default?e.value.is_current?e.val=t.getDate():e.val=e.value.default:e.val=""),"Datelimit"==e.controller)if(e.val){var a=e.val.split(" - ");e.start_date=a[0],e.end_date=a[1]}else e.val="",e.value.is_show_default_start?e.value.is_current_start?e.start_date=t.getDate():e.start_date=e.value.default_start:e.start_date="",e.value.is_show_default_end?e.value.is_current_end?e.end_date=t.getDate():e.end_date=e.value.default_end:e.end_date="",e.start_date&&e.end_date&&(e.val=e.start_date+" - "+e.end_date);if("Time"!=e.controller||e.val||(e.value.is_show_default?e.value.is_current?e.val=t.getTime():e.val=e.value.default:e.val=""),"Timelimit"==e.controller)if(e.val){var i=e.val.split(" - ");e.start_time=i[0],e.end_time=i[1]}else e.val="",e.value.is_show_default_start?e.value.is_current_start?e.start_time=t.getTime():e.start_time=e.value.default_start:e.start_time="",e.value.is_show_default_end?e.value.is_current_end?e.end_time=t.getTime():e.end_time=e.value.default_end:e.end_time="",e.start_time&&e.end_time&&(e.val=e.start_time+" - "+e.end_time);"City"==e.controller&&(e.full_address="",e.select_arr=1==e.value.default_type?"2":"3",e.val?e.default_regions=e.val.split("-"):e.default_regions=[])})),this.formData=JSON.parse(JSON.stringify(e))},verify:function(){for(var t=!0,e=0;e<this.formData.length;e++){var a=this.formData[e];if("Text"==a.controller){if(a.value.required&&!a.val){t=!1,this.$util.showToast({title:"请输入"+a.value.title});break}if("ID_CARD"==a.name&&!1===/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(a.val)){if(t=!a.value.required,0==t)return this.$util.showToast({title:"身份证输入不合法"}),!1;if(1==t&&""!=a.val)return this.$util.showToast({title:"身份证输入不合法"}),!1}if("MOBILE"==a.name&&!1===this.$util.verifyMobile(a.val)){if(t=!a.value.required,0==t)return this.$util.showToast({title:"手机号输入不合法"}),!1;if(1==t&&""!=a.val)return this.$util.showToast({title:"手机号输入不合法"}),!1}}if("Textarea"==a.controller&&a.value.required&&!a.val){t=!1,this.$util.showToast({title:"请输入"+a.value.title});break}if("Select"==a.controller&&a.value.required&&!a.val){t=!1,this.$util.showToast({title:"请选择"+a.value.title});break}if("Checkbox"==a.controller&&a.value.required&&!a.val){t=!1,this.$util.showToast({title:"请至少选择一个"+a.value.title});break}if("Img"==a.controller&&a.value.required&&!a.val){t=!1,this.$util.showToast({title:"请至少上传一张"+a.value.title});break}if("Date"==a.controller&&a.value.required&&!a.val){t=!1,this.$util.showToast({title:"请选择"+a.value.title});break}if("Datelimit"==a.controller){if(a.value.required&&!a.val){t=!1,this.$util.showToast({title:"请选择"+a.value.title});break}if(this.$util.timeTurnTimeStamp(a.start_date)>this.$util.timeTurnTimeStamp(a.end_date)){t=!1,this.$util.showToast({title:"结束日期不能小于开始日期"});break}}if("Time"==a.controller&&a.value.required&&!a.val){t=!1,this.$util.showToast({title:"请选择"+a.value.title});break}if("Timelimit"==a.controller){if(a.value.required&&!a.val){t=!1,this.$util.showToast({title:"请选择"+a.value.title});break}if(a.start_time>=a.end_time){t=!1,this.$util.showToast({title:"结束时间必须大于开始时间"});break}}if("City"==a.controller&&a.value.required&&!a.val){t=!1,this.$util.showToast({title:"请选择"+a.value.title});break}}return console.log(t),t?this.formData:t},pickerChange:function(t,e){this.formData[e].val=this.data[e].value.options[t.detail.value],this.$forceUpdate()},checkboxChange:function(t,e){this.formData[e].val=t.detail.value.toString(),this.formData[e].option_lists.forEach((function(e){e.checked=-1!=t.detail.value.indexOf(e.value)})),this.$forceUpdate()},radioChange:function(t,e){this.formData[e].val=t.detail.value,this.$forceUpdate()},uploadImg:function(t){var e=this;this.$util.upload(Number(this.formData[t].value.max_count),{path:"evaluateimg"},(function(a){a.length>0&&a.forEach((function(a){if(e.formData[t].img_lists.length>=Number(e.formData[t].value.max_count))return e.$util.showToast({title:"最多上传"+e.formData[t].value.max_count+"张图片"}),!1;e.formData[t].img_lists.push(a)})),e.formData[t].val=e.formData[t].img_lists.toString(),e.$forceUpdate()}))},addImg:function(t){var e=this;if(this.formData[t].img_lists.length>=Number(this.formData[t].value.max_count))return this.$util.showToast({title:"最多上传"+this.formData[t].value.max_count+"张图片"}),!1;this.$util.upload(Number(this.formData[t].value.max_count),{path:"evaluateimg"},(function(a){a.length>0&&a.forEach((function(a){e.formData[t].img_lists.push(a)})),e.formData[t].val=e.formData[t].img_lists.toString(),e.$forceUpdate()}))},delImg:function(t,e){this.formData[e].img_lists.splice(t,1),this.formData[e].val=this.formData[e].img_lists.toString(),this.$forceUpdate()},getDate:function(){var t=new Date,e=t.getFullYear(),a=t.getMonth()+1,i=t.getDate();return a=a>9?a:"0"+a,i=i>9?i:"0"+i,"".concat(e,"-").concat(a,"-").concat(i)},getTime:function(){var t=new Date,e=t.getHours(),a=t.getMinutes();return e=e>9?e:"0"+e,a=a>9?a:"0"+a,"".concat(e,":").concat(a)},bindDateChange:function(t,e){this.formData[e].val=t.detail.value,this.$forceUpdate()},bindStartDateChange:function(t,e){this.$set(this.formData[e],"start_date",t.detail.value),this.$set(this.formData[e],"val",this.formData[e].start_date+" - "+this.formData[e].end_date),this.$forceUpdate()},bindEndDateChange:function(t,e){this.$set(this.formData[e],"end_date",t.detail.value),this.$set(this.formData[e],"val",this.formData[e].start_date+" - "+this.formData[e].end_date),this.$forceUpdate()},bindTimeChange:function(t,e){this.formData[e].val=t.detail.value,this.$forceUpdate()},bindStartTimeChange:function(t,e){this.formData[e].start_time=t.detail.value,this.$forceUpdate()},bindEndTimeChange:function(t,e){this.formData[e].end_time=t.detail.value,this.formData[e].val=this.formData[e].start_time+" - "+this.formData[e].end_time,this.$forceUpdate()},handleGetRegions:function(t,e){this.formData[e].val="",this.formData[e].val+=void 0!=t[0]?t[0].label:"",this.formData[e].val+=void 0!=t[1]?"-"+t[1].label:"",this.formData[e].val+=void 0!=t[2]?"-"+t[2].label:"",this.$forceUpdate()}}};e.default=i},"66d0":function(t,e,a){"use strict";var i=a("d33b"),l=a.n(i);l.a},ae30:function(t,e,a){"use strict";a.r(e);var i=a("f100"),l=a("f37b");for(var r in l)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return l[t]}))}(r);a("66d0");var o=a("828b"),n=Object(o["a"])(l["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=n.exports},d33b:function(t,e,a){},f100:function(t,e,a){"use strict";a.d(e,"b",(function(){return l})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={pickRegions:function(){return Promise.all([a.e("common/vendor"),a.e("components/pick-regions/pick-regions")]).then(a.bind(null,"04c1"))}},l=function(){var t=this,e=t.$createElement,a=(t._self._c,t.__map(t.formData,(function(e,a){var i=t.__get_orig(e),l="Img"==e.controller?t.__map(e.img_lists,(function(e,a){var i=t.__get_orig(e),l=t.$util.img(e);return{$orig:i,g0:l}})):null;return{$orig:i,l0:l}})));t.$mp.data=Object.assign({},{$root:{l1:a}})},r=[]},f37b:function(t,e,a){"use strict";a.r(e);var i=a("3b17"),l=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=l.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-form/ns-form-create-component',
    {
        'components/ns-form/ns-form-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("ae30"))
        })
    },
    [['components/ns-form/ns-form-create-component']]
]);
