<view class="form-wrap form-component"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index"><block><block wx:if="{{item.$orig.controller=='Text'}}"><view class="order-wrap"><view class="order-cell"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box"><input type="text" placeholder="{{item.$orig.value.placeholder}}" placeholder-class="placeholder color-tip" data-event-opts="{{[['input',[['__set_model',['$0','val','$event',[]],[[['formData','',index]]]]]]]}}" value="{{item.$orig.val}}" bindinput="__e"/></view></view></view></block><block wx:if="{{item.$orig.controller=='Textarea'}}"><view class="order-wrap"><view class="order-cell flex-box textarea"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box"><textarea placeholder="{{item.$orig.value.placeholder}}" placeholder-class="placeholder color-tip" data-event-opts="{{[['input',[['__set_model',['$0','val','$event',[]],[[['formData','',index]]]]]]]}}" value="{{item.$orig.val}}" bindinput="__e"></textarea></view></view></view></block><block wx:if="{{item.$orig.controller=='Select'}}"><picker mode="selector" range="{{item.$orig.value.options}}" data-event-opts="{{[['change',[['pickerChange',['$event',index]]]]]}}" bindchange="__e"><view class="order-wrap"><view class="order-cell"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box"><block wx:if="{{item.$orig.val!=''}}"><text>{{item.$orig.val}}</text></block><block wx:else><text class="color-tip">请选择</text></block></view><text class="iconfont icon-right"></text></view></view></picker></block><block wx:if="{{item.$orig.controller=='Checkbox'}}"><view class="order-wrap"><view class="order-cell"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box check-group-box"><checkbox-group data-event-opts="{{[['change',[['checkboxChange',['$event',index]]]]]}}" bindchange="__e"><block wx:for="{{item.$orig.option_lists}}" wx:for-item="v" wx:for-index="k" wx:key="k"><label><checkbox value="{{v.value}}" checked="{{v.checked}}"></checkbox><view class="checkbox"><text class="{{['iconfont',(!v.checked)?'icon-fuxuankuang2':'',(v.checked)?'icon-fuxuankuang1 color-base-text':'']}}"></text>{{''+v.value+''}}</view></label></block></checkbox-group></view></view></view></block><block wx:if="{{item.$orig.controller=='Radio'}}"><view class="order-wrap"><view class="order-cell"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box radio-group-box"><radio-group data-event-opts="{{[['change',[['radioChange',['$event',index]]]]]}}" bindchange="__e"><block wx:for="{{item.$orig.option_lists}}" wx:for-item="v" wx:for-index="k" wx:key="k"><label><radio value="{{v.value}}" checked="{{item.$orig.val==v.value}}"></radio><view class="radio-box"><text class="{{['iconfont',(item.$orig.val!=v.value)?'icon-yuan_checkbox':'',(item.$orig.val==v.value)?'icon-yuan_checked color-base-text':'']}}"></text>{{''+v.value+''}}</view></label></block></radio-group></view></view></view></block><block wx:if="{{item.$orig.controller=='Img'}}"><view class="order-wrap"><view class="order-cell flex-box"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box img-boxs"><block wx:for="{{item.l0}}" wx:for-item="v" wx:for-index="k" wx:key="k"><view data-event-opts="{{[['tap',[['uploadImg',[index]]]]]}}" class="img-box" bindtap="__e"><image src="{{v.g0}}" mode="aspectFill"></image><text data-event-opts="{{[['tap',[['delImg',[k,index]]]]]}}" class="iconfont icon-guanbi" catchtap="__e"></text></view></block><view data-event-opts="{{[['tap',[['addImg',[index]]]]]}}" class="img-box" bindtap="__e"><text class="iconfont icon-add1"></text></view></view></view></view></block><block wx:if="{{item.$orig.controller=='Date'}}"><view class="order-wrap"><view class="order-cell"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box box-flex"><picker mode="date" value="{{item.$orig.val}}" data-event-opts="{{[['change',[['bindDateChange',['$event',index]]]]]}}" bindchange="__e"><view class="{{['uni-input',(!item.$orig.val)?'color-tip':'']}}">{{item.$orig.val?item.$orig.val:item.$orig.value.placeholder}}</view></picker></view><text class="iconfont icon-right"></text></view></view></block><block wx:if="{{item.$orig.controller=='Datelimit'}}"><view class="order-wrap"><view class="order-cell flex-box"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box date-boxs"><view class="date-box"><picker mode="date" value="{{item.$orig.start_date}}" data-event-opts="{{[['change',[['bindStartDateChange',['$event',index]]]]]}}" bindchange="__e"><view class="picker-box"><view class="{{['uni-input',(!item.$orig.start_date)?'color-tip':'']}}">{{item.$orig.start_date?item.$orig.start_date:item.$orig.value.placeholder_start}}</view></view></picker></view><view class="interval iconfont icon-jian"></view><view class="date-box"><picker mode="date" value="{{item.$orig.end_date}}" data-event-opts="{{[['change',[['bindEndDateChange',['$event',index]]]]]}}" bindchange="__e"><view class="picker-box"><view class="{{['uni-input',(!item.$orig.end_date)?'color-tip':'']}}">{{item.$orig.end_date?item.$orig.end_date:item.$orig.value.placeholder_end}}</view></view></picker></view></view></view></view></block><block wx:if="{{item.$orig.controller=='Time'}}"><view class="order-wrap"><view class="order-cell"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box box-flex"><picker mode="time" value="{{item.$orig.val}}" data-event-opts="{{[['change',[['bindTimeChange',['$event',index]]]]]}}" bindchange="__e"><view class="{{['uni-input',(!item.$orig.val)?'color-tip':'']}}">{{item.$orig.val?item.$orig.val:item.$orig.value.placeholder}}</view></picker></view><text class="iconfont icon-right"></text></view></view></block><block wx:if="{{item.$orig.controller=='Timelimit'}}"><view class="order-wrap"><view class="order-cell flex-box"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box date-boxs"><view class="date-box"><picker mode="time" value="{{item.$orig.start_time}}" data-event-opts="{{[['change',[['bindStartTimeChange',['$event',index]]]]]}}" bindchange="__e"><view class="picker-box"><view class="{{['uni-input',(!item.$orig.start_time)?'color-tip':'']}}">{{item.$orig.start_time?item.$orig.start_time:item.$orig.value.placeholder_start}}</view></view></picker></view><view class="interval iconfont icon-jian"></view><view class="date-box"><picker mode="time" value="{{item.$orig.end_time}}" data-event-opts="{{[['change',[['bindEndTimeChange',['$event',index]]]]]}}" bindchange="__e"><view class="picker-box"><view class="{{['uni-input',(!item.$orig.end_time)?'color-tip':'']}}">{{item.$orig.end_time?item.$orig.end_time:item.$orig.value.placeholder_end}}</view></view></picker></view></view></view></view></block><block wx:if="{{item.$orig.controller=='City'}}"><view class="order-wrap"><view class="order-cell box-flex"><view class="name"><text class="tit">{{item.$orig.value.title}}</text><text class="required">{{item.$orig.value.required?'*':''}}</text></view><view class="box"><pick-regions vue-id="{{'14e46528-1-'+index}}" default-regions="{{item.$orig.default_regions}}" select-arr="{{item.$orig.select_arr}}" data-event-opts="{{[['^getRegions',[['handleGetRegions',['$event',index]]]]]}}" bind:getRegions="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['select-address','',(!item.$orig.val)?'empty':'',(!item.$orig.val)?'color-tip':'']}}">{{''+(item.$orig.val?item.$orig.val:item.$orig.select_arr=='2'?'请选择省市':'请选择省市区/县')+''}}</view></pick-regions></view><text class="iconfont icon-right"></text></view></view></block></block></block></view>