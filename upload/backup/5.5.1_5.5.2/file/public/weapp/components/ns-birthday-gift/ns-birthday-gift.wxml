<view class="data-v-5fe79ee3"><block wx:if="{{birthday}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="reward-popup data-v-5fe79ee3" catchtouchmove="__e"><uni-popup vue-id="ce451cfc-1" type="center" maskClick="{{false}}" data-ref="birthdayGift" class="data-v-5fe79ee3 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="reward-wrap data-v-5fe79ee3"><view class="wrap data-v-5fe79ee3" style="{{'background-image:'+('url('+$root.g0+')')+';'}}"><block wx:if="{{memberInfo}}"><view class="birthday-title-name data-v-5fe79ee3">{{"Dear "+memberInfo.nickname}}</view></block><block wx:if="{{birthday.blessing_content}}"><view class="birthday-title-desc data-v-5fe79ee3">{{''+birthday.blessing_content+''}}</view></block><block wx:else><view class="birthday-title-desc data-v-5fe79ee3">感谢您一直以来的支持，在您生日到来之际，特为您送上最真诚的祝福！</view></block><view class="birthday-title-hint data-v-5fe79ee3"><image class="birthday-img-all data-v-5fe79ee3" src="{{$root.g1}}" mode></image><view class="font-size-toolbar data-v-5fe79ee3">生日贺礼</view><image class="birthday-img-all data-v-5fe79ee3" src="{{$root.g2}}" mode></image></view><scroll-view class="register-box data-v-5fe79ee3" scroll-y="true"><view class="reward-content data-v-5fe79ee3"><block wx:if="{{birthday.point>0}}"><view class="content data-v-5fe79ee3"><view class="info data-v-5fe79ee3"><text class="num data-v-5fe79ee3">{{''+$root.m0+''}}<text class="type data-v-5fe79ee3">积分</text></text><view class="desc data-v-5fe79ee3">用于下单时抵现或兑换商品等</view></view><view data-event-opts="{{[['tap',[['closeRewardPopup',['1']]]]]}}" class="tip data-v-5fe79ee3" bindtap="__e">立即查看</view></view></block><block wx:if="{{birthday.balance_type==0&&birthday.balance>0}}"><view class="content data-v-5fe79ee3"><view class="info data-v-5fe79ee3"><text class="num data-v-5fe79ee3">{{''+$root.m1+''}}<text class="type data-v-5fe79ee3">元红包</text></text><view class="desc data-v-5fe79ee3">不可提现红包</view></view><view data-event-opts="{{[['tap',[['closeRewardPopup',['2']]]]]}}" class="tip data-v-5fe79ee3" bindtap="__e">立即查看</view></view></block><block wx:if="{{birthday.balance_type==1&&birthday.balance_money>0}}"><view class="content data-v-5fe79ee3"><view class="info data-v-5fe79ee3"><text class="num data-v-5fe79ee3">{{''+$root.m2+''}}<text class="type data-v-5fe79ee3">元红包</text></text><view class="desc data-v-5fe79ee3">可提现红包</view></view><view data-event-opts="{{[['tap',[['closeRewardPopup',['2']]]]]}}" class="tip data-v-5fe79ee3" bindtap="__e">立即查看</view></view></block><block wx:if="{{$root.g3>0}}"><block class="data-v-5fe79ee3"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-5fe79ee3"><view class="content data-v-5fe79ee3"><view class="info data-v-5fe79ee3"><block wx:if="{{item.$orig.type=='reward'}}"><text class="num data-v-5fe79ee3">{{''+item.m3+''}}<text class="type data-v-5fe79ee3">元优惠劵</text></text></block><block wx:else><block wx:if="{{item.$orig.type=='discount'}}"><text class="num data-v-5fe79ee3">{{''+item.$orig.discount+''}}<text class="type data-v-5fe79ee3">折</text></text></block></block><view class="desc data-v-5fe79ee3">用于下单时抵现或兑换商品等</view></view><view data-event-opts="{{[['tap',[['closeRewardPopup',['3']]]]]}}" class="tip data-v-5fe79ee3" bindtap="__e">立即查看</view></view></block></block></block></block></view></scroll-view></view><view data-event-opts="{{[['tap',[['cancel']]]]}}" class="close-btn data-v-5fe79ee3" bindtap="__e"><text class="iconfont icon-close btn data-v-5fe79ee3"></text></view></view></uni-popup></view></block></view>