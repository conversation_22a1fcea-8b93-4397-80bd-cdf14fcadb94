(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-copyright/ns-copyright"],{"28a4":function(t,o,n){"use strict";n.d(o,"b",(function(){return e})),n.d(o,"c",(function(){return i})),n.d(o,"a",(function(){}));var e=function(){var t=this,o=t.$createElement,n=(t._self._c,t.copyright&&(t.showLogo||t.showBeian)&&t.showLogo&&t.copyright.logo?t.$util.img(t.copyright.logo):null);t.$mp.data=Object.assign({},{$root:{g0:n}})},i=[]},ae29:function(t,o,n){},bf57:function(t,o,n){"use strict";var e=n("ae29"),i=n.n(e);i.a},d564:function(t,o,n){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var e={data:function(){return{showLogo:!0}},created:function(){},computed:{showBeian:function(){return!(this.copyright&&!this.copyright.icp&&!this.copyright.gov_record)}},methods:{link:function(t){t&&this.$util.redirectTo("/pages_tool/webview/webview",{src:encodeURIComponent(t)})},toHref:function(t){location.href=t},error:function(){this.showLogo=!1}}};o.default=e},e43b:function(t,o,n){"use strict";n.r(o);var e=n("d564"),i=n.n(e);for(var r in e)["default"].indexOf(r)<0&&function(t){n.d(o,t,(function(){return e[t]}))}(r);o["default"]=i.a},f37bd:function(t,o,n){"use strict";n.r(o);var e=n("28a4"),i=n("e43b");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(o,t,(function(){return i[t]}))}(r);n("bf57");var c=n("828b"),u=Object(c["a"])(i["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);o["default"]=u.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-copyright/ns-copyright-create-component',
    {
        'components/ns-copyright/ns-copyright-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("f37bd"))
        })
    },
    [['components/ns-copyright/ns-copyright-create-component']]
]);
