(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-goods-action/ns-goods-action"],{3321:function(n,t,e){"use strict";var o=e("88743"),u=e.n(o);u.a},6823:function(n,t,e){"use strict";e.r(t);var o=e("dbdd"),u=e("fb66");for(var a in u)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(a);e("3321");var r=e("828b"),c=Object(r["a"])(u["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=c.exports},88743:function(n,t,e){},"91b0":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={name:"ns-goods-action",props:{safeArea:{type:Boolean,default:!1}}};t.default=o},dbdd:function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return u})),e.d(t,"a",(function(){}));var o=function(){var n=this.$createElement;this._self._c},u=[]},fb66:function(n,t,e){"use strict";e.r(t);var o=e("91b0"),u=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(a);t["default"]=u.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-goods-action/ns-goods-action-create-component',
    {
        'components/ns-goods-action/ns-goods-action-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("6823"))
        })
    },
    [['components/ns-goods-action/ns-goods-action-create-component']]
]);
