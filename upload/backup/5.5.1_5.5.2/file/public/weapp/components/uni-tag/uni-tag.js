(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-tag/uni-tag"],{"3c89":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"UniTag",props:{type:{type:String,default:"default"},size:{type:String,default:"normal"},text:{type:String,default:""},disabled:{type:[String,Boolean],default:!1},inverted:{type:[String,Boolean],default:!1},circle:{type:[String,Boolean],default:!1},mark:{type:[String,Boolean],default:!1}},methods:{onClick:function(){!0!==this.disabled&&"true"!==this.disabled&&this.$emit("click")}}};e.default=i},"3d58":function(t,e,n){"use strict";n.r(e);var i=n("3c89"),a=n.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(u);e["default"]=a.a},"43db":function(t,e,n){"use strict";n.r(e);var i=n("9ab5"),a=n("3d58");for(var u in a)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(u);n("8095");var r=n("828b"),l=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=l.exports},"761e":function(t,e,n){},8095:function(t,e,n){"use strict";var i=n("761e"),a=n.n(i);a.a},"9ab5":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-tag/uni-tag-create-component',
    {
        'components/uni-tag/uni-tag-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("43db"))
        })
    },
    [['components/uni-tag/uni-tag-create-component']]
]);
