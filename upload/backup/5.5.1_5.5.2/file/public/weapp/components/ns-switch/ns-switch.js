(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-switch/ns-switch"],{"31b6":function(n,t,e){},"7e0a":function(n,t,e){"use strict";e.r(t);var c=e("a579"),a=e.n(c);for(var u in c)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return c[n]}))}(u);t["default"]=a.a},"937e":function(n,t,e){"use strict";var c=e("31b6"),a=e.n(c);a.a},a579:function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var c={name:"nsSwitch",props:{checked:{type:Boolean,default:!1}},methods:{change:function(){this.$emit("change")}}};t.default=c},b0ec:function(n,t,e){"use strict";e.r(t);var c=e("d6ca"),a=e("7e0a");for(var u in a)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(u);e("937e");var i=e("828b"),o=Object(i["a"])(a["default"],c["b"],c["c"],!1,null,null,null,!1,c["a"],void 0);t["default"]=o.exports},d6ca:function(n,t,e){"use strict";e.d(t,"b",(function(){return c})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){}));var c=function(){var n=this.$createElement;this._self._c},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-switch/ns-switch-create-component',
    {
        'components/ns-switch/ns-switch-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("b0ec"))
        })
    },
    [['components/ns-switch/ns-switch-create-component']]
]);
