<view><block wx:if="{{text=='客服'}}"><block><ns-contact vue-id="e47d0f3c-1" niushop="{{chatParam}}" send-message-title="{{sendData.title}}" send-message-path="{{sendData.path}}" send-message-img="{{sendData.img}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="action-icon-wrap"><view class="{{['iconfont','color-title',icon]}}"></view><text>{{text}}</text><block wx:if="{{$root.g0}}"><view class="corner-mark color-base-bg" style="{{'background:'+(cornerMarkBg+'!important')+';'+('color:'+(cornerMarkColor)+';')}}">{{cornerMark}}</view></block></view></ns-contact></block></block><block wx:else><block><view data-event-opts="{{[['tap',[['clickEvent',['$event']]]]]}}" class="action-icon-wrap" bindtap="__e"><view class="{{['iconfont','color-title',icon]}}"></view><text>{{text}}</text><block wx:if="{{$root.g1}}"><view class="{{['corner-mark','color-base-bg',($root.m0>99)?'max':'']}}" style="{{'background:'+(cornerMarkBg+'!important')+';'+('color:'+(cornerMarkColor)+';')}}">{{cornerMark>99?'99+':cornerMark}}</view></block></view></block></block></view>