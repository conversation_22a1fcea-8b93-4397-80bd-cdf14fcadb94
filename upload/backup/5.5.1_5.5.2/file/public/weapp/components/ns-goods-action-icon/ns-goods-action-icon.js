(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-goods-action-icon/ns-goods-action-icon"],{"31ea":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var c={name:"ns-goods-action-icon",props:{icon:{type:String,default:""},text:{type:String,default:""},cornerMark:{type:String,default:""},cornerMarkBg:{type:String,default:""},cornerMarkColor:{type:String,default:"#fff"},openType:{type:String,default:""},sendData:{type:Object,default:function(){return{title:"",path:"",img:""}}},chatParam:{type:Object,default:function(){return{}}}},components:{nsContact:function(){e.e("components/ns-contact/ns-contact").then(function(){return resolve(e("5036"))}.bind(null,e)).catch(e.oe)}},methods:{clickEvent:function(){this.$emit("click")}}};n.default=c},5024:function(t,n,e){"use strict";var c=e("bb0a"),o=e.n(c);o.a},5196:function(t,n,e){"use strict";e.r(n);var c=e("31ea"),o=e.n(c);for(var r in c)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return c[t]}))}(r);n["default"]=o.a},"565f":function(t,n,e){"use strict";e.r(n);var c=e("cc83"),o=e("5196");for(var r in o)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(r);e("5024");var a=e("828b"),i=Object(a["a"])(o["default"],c["b"],c["c"],!1,null,null,null,!1,c["a"],void 0);n["default"]=i.exports},bb0a:function(t,n,e){},cc83:function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return r})),e.d(n,"a",(function(){return c}));var c={nsContact:function(){return e.e("components/ns-contact/ns-contact").then(e.bind(null,"5036"))}},o=function(){var t=this.$createElement,n=(this._self._c,"客服"==this.text?this.cornerMark.length:null),e="客服"!=this.text?this.cornerMark.length:null,c="客服"!=this.text&&e?parseInt(this.cornerMark):null;this.$mp.data=Object.assign({},{$root:{g0:n,g1:e,m0:c}})},r=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-goods-action-icon/ns-goods-action-icon-create-component',
    {
        'components/ns-goods-action-icon/ns-goods-action-icon-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("565f"))
        })
    },
    [['components/ns-goods-action-icon/ns-goods-action-icon-create-component']]
]);
