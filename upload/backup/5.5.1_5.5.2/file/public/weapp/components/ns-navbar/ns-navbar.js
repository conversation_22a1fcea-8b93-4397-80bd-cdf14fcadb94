(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-navbar/ns-navbar"],{"27c0":function(t,n,e){"use strict";e.r(n);var a=e("bc90"),o=e.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(i);n["default"]=o.a},"3a93":function(t,n,e){},"4bc6":function(t,n,e){"use strict";var a=e("3a93"),o=e.n(a);o.a},9941:function(t,n,e){"use strict";e.r(n);var a=e("bf61"),o=e("27c0");for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(i);e("4bc6");var r=e("828b"),c=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"a14780c4",null,!1,a["a"],void 0);n["default"]=c.exports},bc90:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e;t.getSystemInfoSync();e=t.getMenuButtonBoundingClientRect();var a={name:"ns-navbar",props:{data:{type:Object,default:function(){return{}}},titleColor:{type:String,default:"#606266"},customBack:{type:Function,default:null},scrollTop:{type:[String,Number],default:"0"},isBack:{type:Boolean,default:!0}},data:function(){return{menuButtonInfo:e,isBackShow:!1,placeholderHeight:0}},computed:{navbarInnerStyle:function(){var t="";return t+="height:"+2*e.height+"rpx;",t},navbarHeight:function(){var t=e.top;return t},bgColor:function(){var t="";if(this.data.topNavBg){t="transparent";var n=0;4==this.data.navStyle&&(n=this.navbarHeight-25),t=this.scrollTop>n?this.data.topNavColor:"transparent"}else t=this.data.topNavColor;return t},capsuleWidth:function(){var t="calc(100vw - ".concat(this.menuButtonInfo.right,"px + ").concat(this.menuButtonInfo.width,"px + 10px)");return t}},created:function(t){var n=getCurrentPages();n.length>1&&(this.isBackShow=!0),this.navbarPlaceholderHeight()},mounted:function(){this.setModuleLocationFn()},methods:{toLink:function(t){t&&this.$util.redirectTo(t)},goBack:function(){"function"===typeof this.customBack?this.customBack():t.navigateBack()},chooseOtherStore:function(){this.globalStoreConfig&&1==this.globalStoreConfig.is_allow_change?this.$util.redirectTo("/pages_tool/store/list"):this.globalStoreInfo&&this.$util.redirectTo("/pages_tool/store/detail",{store_id:this.globalStoreInfo.store_id})},navbarPlaceholderHeight:function(){var n=this;setTimeout((function(){var e=t.createSelectorQuery().in(n);e.select(".ns-navbar-wrap .u-navbar").boundingClientRect((function(t){n.placeholderHeight=t.height})).exec()}))},setModuleLocationFn:function(){var n=this,e=t.createSelectorQuery().in(this);e.select(".ns-navbar-wrap .u-navbar").boundingClientRect((function(t){var e={originalVal:t.height||0,currVal:0};n.$store.commit("setDiyGroupPositionObj",{nsNavbar:e})})).exec()}}};n.default=a}).call(this,e("df3c")["default"])},bf61:function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){}));var a=function(){var t=this,n=t.$createElement,e=(t._self._c,2==t.data.navStyle?t.$util.img(t.data.topNavImg):null),a=3==t.data.navStyle?t.$util.img(t.data.topNavImg):null;t._isMounted||(t.e0=function(n){return t.$util.redirectTo("/pages_tool/goods/search")}),t.$mp.data=Object.assign({},{$root:{g0:e,g1:a}})},o=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-navbar/ns-navbar-create-component',
    {
        'components/ns-navbar/ns-navbar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("9941"))
        })
    },
    [['components/ns-navbar/ns-navbar-create-component']]
]);
