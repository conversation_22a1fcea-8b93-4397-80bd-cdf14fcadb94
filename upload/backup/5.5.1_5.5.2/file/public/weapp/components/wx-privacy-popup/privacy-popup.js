(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/wx-privacy-popup/privacy-popup"],{"51c8":function(t,e,i){"use strict";(function(t,i){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={data:function(){return{agree:!1,showPop:!1,privacyAuthorization:null,privacyResolves:new Set,closeOtherPagePopUpHooks:new Set,privacyContractName:"用户隐私保护指引"}},mounted:function(){this.init(),this.curPageShow()},created:function(){var e=this;try{t.getPrivacySetting({success:function(t){e.privacyContractName=t.privacyContractName}})}catch(i){}},methods:{init:function(){var e=this;t.onNeedPrivacyAuthorization&&t.onNeedPrivacyAuthorization((function(t){"function"===typeof e.privacyAuthorization&&e.privacyAuthorization(t)}))},proactive:function(){var e=this,i=this;t.getPrivacySetting?t.getPrivacySetting({success:function(t){t.needAuthorization?(i.popUp(),e.closeOtherPagePopUp(e.disPopUp)):e.$emit("agree")}}):this.$emit("agree")},curPageShow:function(){var t=this;this.privacyAuthorization=function(e){t.privacyResolves.add(e),t.popUp(),t.closeOtherPagePopUp(t.disPopUp)},this.closeOtherPagePopUpHooks.add(this.disPopUp)},closeOtherPagePopUp:function(t){this.closeOtherPagePopUpHooks.forEach((function(e){t!==e&&e()}))},openPrivacyContract:function(){t.openPrivacyContract({success:function(t){},fail:function(t){}})},handleDisagree:function(){var t=this;this.privacyResolves.forEach((function(t){t({event:"disagree"})})),this.privacyResolves.clear(),this.disPopUp(),i.showModal({content:"未同意隐私协议，无法使用相关功能",success:function(){t.$emit("disagree")}})},handleAgree:function(){this.privacyResolves.forEach((function(t){t({event:"agree",buttonId:"agree-btn"})})),this.privacyResolves.clear(),this.disPopUp(),this.$emit("agree")},popUp:function(){!1===this.showPop&&(this.showPop=!0)},disPopUp:function(){!0===this.showPop&&(this.showPop=!1)}}};e.default=o}).call(this,i("3223")["default"],i("df3c")["default"])},"633d":function(t,e,i){"use strict";i.r(e);var o=i("b857"),n=i("8a46");for(var c in n)["default"].indexOf(c)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(c);i("8274");var a=i("828b"),r=Object(a["a"])(n["default"],o["b"],o["c"],!1,null,"2e10602c",null,!1,o["a"],void 0);e["default"]=r.exports},8274:function(t,e,i){"use strict";var o=i("9f49"),n=i.n(o);n.a},"8a46":function(t,e,i){"use strict";i.r(e);var o=i("51c8"),n=i.n(o);for(var c in o)["default"].indexOf(c)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(c);e["default"]=n.a},"9f49":function(t,e,i){},b857:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this.$createElement;this._self._c},n=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/wx-privacy-popup/privacy-popup-create-component',
    {
        'components/wx-privacy-popup/privacy-popup-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("633d"))
        })
    },
    [['components/wx-privacy-popup/privacy-popup-create-component']]
]);
