(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-goods-recommend/ns-goods-recommend"],{1415:function(t,e,n){},2481:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return o}));var o={diyIcon:function(){return n.e("components/diy-components/diy-icon").then(n.bind(null,"a68f"))},nsLoading:function(){return n.e("components/ns-loading/ns-loading").then(n.bind(null,"10e0"))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.list.length),o=n?t.__map(t.list,(function(e,n){var o=t.__get_orig(e),i=t.goodsImg(e.goods_image),r=t.goodsTag(e),a=""!=r?t.goodsTag(e):null,s=t.$lang("common.currencySymbol"),u=parseFloat(t.showPrice(e)).toFixed(2).split("."),c=parseFloat(t.showPrice(e)).toFixed(2).split("."),l=e.member_price&&e.member_price==t.showPrice(e),d=l?t.$util.img("public/uniapp/index/VIP.png"):null,g=l||1!=e.promotion_type?null:t.$util.img("public/uniapp/index/discount.png"),p=t.showMarketPrice(e),f=p?t.$lang("common.currencySymbol"):null,m=p?t.showMarketPrice(e):null;return{$orig:o,m0:i,m1:r,m2:a,m3:s,g1:u,g2:c,m4:l,g3:d,g4:g,m5:p,m6:f,m7:m}})):null;t._isMounted||(t.e0=function(e,n){var o=arguments[arguments.length-1].currentTarget.dataset,i=o.eventParams||o["event-params"];n=i.item;return e.stopPropagation(),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,n,e)},t.e1=function(e,n){var o=arguments[arguments.length-1].currentTarget.dataset,i=o.eventParams||o["event-params"];n=i.item;return e.stopPropagation(),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,n,e)},t.e2=function(e,n){var o=arguments[arguments.length-1].currentTarget.dataset,i=o.eventParams||o["event-params"];n=i.item;return e.stopPropagation(),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,n,e)},t.e3=function(e,n){var o=arguments[arguments.length-1].currentTarget.dataset,i=o.eventParams||o["event-params"];n=i.item;return e.stopPropagation(),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,n,e)}),t.$mp.data=Object.assign({},{$root:{g0:n,l0:o}})},r=[]},"42e7":function(t,e,n){"use strict";n.r(e);var o=n("e26c"),i=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);e["default"]=i.a},7254:function(t,e,n){"use strict";n.r(e);var o=n("2481"),i=n("42e7");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("9481");var a=n("828b"),s=Object(a["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=s.exports},9481:function(t,e,n){"use strict";var o=n("1415"),i=n.n(o);i.a},e26c:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"ns-goods-recommend",components:{nsLoading:function(){n.e("components/ns-loading/ns-loading").then(function(){return resolve(n("10e0"))}.bind(null,n)).catch(n.oe)},nsGoodsSkuIndex:function(){n.e("components/ns-goods-sku/ns-goods-sku-index").then(function(){return resolve(n("0c16"))}.bind(null,n)).catch(n.oe)}},data:function(){return{list:[],config:{title:"猜你喜欢",sources:"sort",supportPage:[],goodsIds:[],fontWeight:!1,padding:0,cartEvent:"detail",text:"购买",textColor:"#FFFFFF",theme:"default",aroundRadius:25,control:!0,bgColor:"#FF6A00",style:"button",iconDiy:{iconType:"icon",icon:"",style:{fontSize:"60",iconBgColor:[],iconBgColorDeg:0,iconBgImg:"",bgRadius:0,iconColor:["#000000"],iconColorDeg:0}}},page:1,isAll:!0,isClick:!0,showLoading:!1}},props:{isLike:{type:Boolean,default:!0},size:{type:[Number,String],default:10},auto:{type:Boolean,default:!0},load:{type:Boolean,default:!0},route:{type:String,default:""}},mounted:function(){this.auto&&this.getLikeList()},methods:{init:function(){this.list=[],this.page=1},toDetail:function(t){var e={goods_id:t.goods_id};this.$util.redirectTo("/pages/goods/detail",e)},getLikeList:function(t){var e=this,n=this;if(this.isClick&&this.isAll)return this.isClick=!1,this.page>1&&(this.showLoading=!0),new Promise((function(o,i){n.$api.sendRequest({url:"/api/goodssku/recommend",data:{page:e.page,page_size:e.auto?e.size:t,route:e.route},success:function(t){e.showLoading=!1,e.isClick=!0,0==t.code&&(1==e.page&&(e.list=[]),e.config=t.data.config,e.list=e.list.concat(t.data.list),e.list.length==t.data.count&&(e.isAll=!1),e.page+=1,o(t.data.list))}})}))},goodsImg:function(t){var e=t.split(",");return e[0]?this.$util.img(e[0],{size:"mid"}):this.$util.getDefaultImage().goods},imgError:function(t){this.list[t].goods_image=this.$util.getDefaultImage().goods},showPrice:function(t){var e=t.discount_price;return t.member_price&&parseFloat(t.member_price)<parseFloat(e)&&(e=t.member_price),e},showMarketPrice:function(t){if(t.market_price_show){var e=this.showPrice(t);if(t.market_price>0)return t.market_price;if(parseFloat(t.price)>parseFloat(e))return t.price}return""},goodsTag:function(t){return t.label_name||""},cartListChange:function(t){"cart"==this.route&&this.storeToken&&this.$root.getCartData()},addCart:function(t){}}};e.default=o}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-goods-recommend/ns-goods-recommend-create-component',
    {
        'components/ns-goods-recommend/ns-goods-recommend-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7254"))
        })
    },
    [['components/ns-goods-recommend/ns-goods-recommend-create-component']]
]);
