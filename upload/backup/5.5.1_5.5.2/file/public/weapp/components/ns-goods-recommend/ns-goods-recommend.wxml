<block wx:if="{{$root.g0}}"><view class="goods-recommend"><view class="goods-recommend-title"><text class="title">{{config.title}}</text></view><view class="goods-list double-column"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="goods-item margin-bottom" bindtap="__e"><view class="goods-img"><image src="{{item.m0}}" mode="widthFix" lazy-load="{{true}}" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image><block wx:if="{{item.m1!=''}}"><view class="color-base-bg goods-tag">{{item.m2}}</view></block><block wx:if="{{item.$orig.goods_stock<=0}}"><view class="sell-out"><text class="iconfont icon-shuqing"></text></view></block></view><view class="info-wrap"><view class="{{['goods-name',[(config.nameLineMode=='single')?'using-hidden':''],[(config.nameLineMode=='multiple')?'multi-hidden':'']]}}">{{''+item.$orig.goods_name+''}}</view><view class="lineheight-clear"><view class="discount-price"><text class="unit price-style small">{{item.m3}}</text><text class="price price-style large">{{item.g1[0]}}</text><text class="unit price-style small">{{"."+item.g2[1]}}</text></view><block wx:if="{{item.m4}}"><view class="member-price-tag"><image src="{{item.g3}}" mode="widthFix"></image></view></block><block wx:else><block wx:if="{{item.$orig.promotion_type==1}}"><view class="member-price-tag"><image src="{{item.g4}}" mode="widthFix"></image></view></block></block><block wx:if="{{item.m5}}"><view class="delete-price font-size-activity-tag color-tip price-font"><text class="unit">{{item.m6}}</text><text>{{item.m7}}</text></view></block></view><view class="pro-info"><view class="block-wrap"><block wx:if="{{item.$orig.sale_show}}"><view class="sale font-size-activity-tag color-tip">{{"已售"+item.$orig.sale_num+(item.$orig.unit?item.$orig.unit:'件')}}</view></block></view><block wx:if="{{config.control&&item.$orig.is_virtual==0}}"><view class="cart-action-wrap"><block wx:if="{{config.style=='icon-cart'}}"><view class="cart shopping-cart-btn iconfont icon-gouwuche click-wrap" style="{{'color:'+(config.theme=='diy'?config.textColor:'')+';'+('border-color:'+(config.theme=='diy'?config.textColor:'')+';')}}" id="{{'goods-'+item.$orig.id}}" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" catchtap="__e"><view class="click-event"></view></view></block><block wx:else><block wx:if="{{config.style=='icon-add'}}"><view class="cart plus-sign-btn iconfont icon-add1 click-wrap" style="{{'color:'+(config.theme=='diy'?config.textColor:'')+';'+('border-color:'+(config.theme=='diy'?config.textColor:'')+';')}}" id="{{'goods-'+item.$orig.id}}" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" catchtap="__e"><view class="click-event"></view></view></block><block wx:else><block wx:if="{{config.style=='button'}}"><view class="cart buy-btn click-wrap" style="{{'background-color:'+(config.theme=='diy'?config.bgColor:'')+';'+('color:'+(config.theme=='diy'?config.textColor:'')+';')+('font-weight:'+(config.theme=='diy'?config.fontWeight?'bold':'normal':'')+';')+('padding:'+(config.theme=='diy'?'12rpx '+config.padding*2+'rpx':'')+';')}}" id="{{'goods-'+item.$orig.id}}" data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" catchtap="__e">{{''+config.text+''}}<view class="click-event"></view></view></block><block wx:else><block wx:if="{{config.style=='icon-diy'}}"><view class="icon-diy click-wrap" style="{{'color:'+(config.theme=='diy'?config.textColor:'')+';'}}" id="{{'goods-'+item.$orig.id}}" data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" catchtap="__e"><view class="click-event"></view><diy-icon vue-id="{{'5867cde2-1-'+index}}" icon="{{config.iconDiy.icon}}" value="{{config.iconDiy.style?config.iconDiy.style:null}}" bind:__l="__l"></diy-icon></view></block></block></block></block></view></block></view></view></view></block></view><block wx:if="{{showLoading&&load}}"><view class="circle-box"><ns-loading vue-id="5867cde2-2" bind:__l="__l"></ns-loading></view></block><ns-goods-sku-index class="vue-ref" bind:cartListChange="__e" bind:addCart="__e" vue-id="5867cde2-3" data-ref="goodsSkuIndex" data-event-opts="{{[['^cartListChange',[['cartListChange']]],['^addCart',[['addCart']]]]}}" bind:__l="__l"></ns-goods-sku-index></view></block>