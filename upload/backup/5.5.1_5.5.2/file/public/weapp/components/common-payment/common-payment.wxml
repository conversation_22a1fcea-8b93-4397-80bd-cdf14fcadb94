<view class="{{['order-container',(isIphoneX)?'safe-area':'']}}"><view class="payment-navbar" style="{{'padding-top:'+(menuButtonBounding.top+'px')+';'+('height:'+(menuButtonBounding.height+'px')+';')}}"><view class="nav-wrap"><text data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="iconfont icon-back_light" bindtap="__e"></text><view class="navbar-title">确认订单</view></view></view><view class="payment-navbar-block" style="{{'height:'+(menuButtonBounding.bottom+'px')+';'}}"></view><scroll-view class="order-scroll-container" scroll-y="true"><view class="payment-navbar-block"></view><block wx:if="{{paymentData}}"><block wx:if="{{paymentData.is_virtual}}"><view class="mobile-wrap"><view class="tips color-base-text"><text class="iconfont icongantanhao"></text>购买虚拟类商品需填写手机号，方便商家与您联系</view><view class="form-group"><text class="icon"><image src="{{$root.g0}}" mode="widthFix"></image></text><text class="text">手机号码</text><input class="input" type="number" maxlength="11" placeholder="请输入您的手机号码" placeholder-class="color-tip placeholder" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['orderCreateData.member_address']]]]]}}" value="{{orderCreateData.member_address.mobile}}" bindinput="__e"/></view></view></block><block wx:else><block wx:if="{{$root.g1>1}}"><view class="delivery-mode"><view class="action"><block wx:for="{{goodsData.delivery.express_type}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectDeliveryType',['$0'],[[['goodsData.delivery.express_type','',index]]]]]]]}}" class="{{[(item.name==orderCreateData.delivery.delivery_type)?'active':'']}}" bindtap="__e">{{''+item.title+''}}<view class="out-radio"></view></view></block></view></view></block><block wx:if="{{orderCreateData.delivery.delivery_type=='express'}}"><view class="{{['address-box',($root.g2<=1)?'not-delivery-type':'']}}"><block wx:if="{{memberAddress}}"><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="info-wrap" bindtap="__e"><view class="content"><text class="name">{{memberAddress.name?memberAddress.name:''}}</text><text class="mobile">{{memberAddress.mobile?memberAddress.mobile:''}}</text><view class="desc-wrap">{{''+(memberAddress.full_address?memberAddress.full_address:'')+"\n\t\t\t\t\t\t\t\t"+(memberAddress.address?memberAddress.address:'')+''}}</view></view><text class="cell-more iconfont icon-right"></text></view></block><block wx:else><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="empty-wrap" bindtap="__e"><view class="info">请设置收货地址</view><view class="cell-more"><view class="iconfont icon-right"></view></view></view></block><image class="address-line" src="{{$root.g3}}"></image></view></block><block wx:if="{{orderCreateData.delivery.delivery_type=='local'}}"><view class="{{['address-box',($root.g4<=1)?'not-delivery-type':'']}}"><block wx:if="{{localMemberAddress}}"><view><block wx:if="{{$root.g5}}"><block><view class="local-delivery-store"><block wx:if="{{storeInfo}}"><view class="info">由<text class="store-name">{{storeInfo.store_name}}</text>提供配送</view></block><block wx:else><view class="info"><text class="store-name">超出配送范围，请选择其他门店</text></view></block><block wx:if="{{$root.g6>1}}"><view data-event-opts="{{[['tap',[['openPopup',['deliveryPopup']]]]]}}" class="cell-more" bindtap="__e"><text>点击切换</text><text class="iconfont icon-right"></text></view></block></view></block></block><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="info-wrap local" bindtap="__e"><view class="content"><text class="name">{{localMemberAddress.name?localMemberAddress.name:''}}</text><text class="mobile">{{localMemberAddress.mobile?localMemberAddress.mobile:''}}</text><view class="desc-wrap">{{''+(localMemberAddress.full_address?localMemberAddress.full_address:'')+"\n\t\t\t\t\t\t\t\t\t"+(localMemberAddress.address?localMemberAddress.address:'')+''}}</view></view><text class="cell-more iconfont icon-right"></text></view><block wx:if="{{calculateGoodsData.config.local&&calculateGoodsData.delivery.local.info.time_is_open==1}}"><view class="local-box"><view data-event-opts="{{[['tap',[['localtime',['']]]]]}}" class="pick-block" bindtap="__e"><view class="title font-size-base">送达时间</view><view class="time-picker"><text class="{{[(!deliveryTime)?'color-tip':'']}}">{{deliveryTime?deliveryTime:'请选择送达时间'}}</text><text class="iconfont icon-right cell-more"></text></view></view></view></block></view></block><block wx:else><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="empty-wrap" bindtap="__e"><view class="info">请设置收货地址</view><view class="cell-more"><view class="iconfont icon-right"></view></view></view></block><image class="address-line" src="{{$root.g7}}"></image></view></block><block wx:if="{{orderCreateData.delivery.delivery_type=='store'}}"><view class="{{['store-box',($root.g8<=1)?'not-delivery-type':'']}}"><block wx:if="{{storeInfo}}"><block><view data-event-opts="{{[['tap',[['openPopup',['deliveryPopup']]]]]}}" class="store-info" bindtap="__e"><view class="store-address-info"><view class="info-wrap"><view class="title"><text>{{storeInfo.store_name}}</text></view><view class="store-detail"><block wx:if="{{storeInfo.status==0&&storeInfo.close_desc}}"><view class="close-desc">{{storeInfo.close_desc}}</view></block><block wx:if="{{storeInfo.open_date}}"><view>{{"营业时间："+storeInfo.open_date}}</view></block><view class="address">{{storeInfo.full_address+" "+storeInfo.address}}</view></view></view><block wx:if="{{$root.g9}}"><view class="cell-more iconfont icon-right"></view></block></view></view><block wx:if="{{orderCreateData.member_address}}"><view class="mobile-wrap store-mobile"><view class="form-group"><text class="text">姓名</text><input class="input" type="text" placeholder-class="color-tip placeholder" disabled="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['orderCreateData.member_address']]]]]}}" value="{{orderCreateData.member_address.name}}" bindinput="__e"/></view></view></block><block wx:if="{{orderCreateData.member_address}}"><view class="mobile-wrap store-mobile"><view class="form-group"><text class="text">预留手机</text><input class="input" type="number" maxlength="11" placeholder="请输入您的手机号码" placeholder-class="color-tip placeholder" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['orderCreateData.member_address']]]]]}}" value="{{orderCreateData.member_address.mobile}}" bindinput="__e"/></view></view></block><view data-event-opts="{{[['tap',[['storetime',['']]]]]}}" class="store-time" bindtap="__e"><view class="left">提货时间</view><view class="right">{{''+deliveryTime+''}}<text class="iconfont icon-right"></text></view></view></block></block><block wx:else><view class="empty">当前无自提门店，请选择其它配送方式</view></block><image class="address-line" src="{{$root.g10}}"></image></view></block></block><block wx:if="{{calculateGoodsData}}"><view class="site-wrap order-goods"><view class="site-body"><block wx:for="{{$root.l0}}" wx:for-item="goodsItem" wx:for-index="goodsIndex" wx:key="goodsIndex"><view class="goods-item"><view class="goods-wrap"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({goodsItem:goodsItem.$orig})}}" class="goods-img" bindtap="__e"><image src="{{goodsItem.g11}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[goodsIndex]]]]]}}" binderror="__e"></image></view><view class="goods-info"><view class="top-wrap"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({goodsItem:goodsItem.$orig})}}" class="goods-name" bindtap="__e">{{goodsItem.$orig.sku_name}}</view><block wx:if="{{goodsItem.$orig.sku_spec_format}}"><view class="sku"><view class="goods-spec"><block wx:for="{{goodsItem.$orig.sku_spec_format}}" wx:for-item="x" wx:for-index="i" wx:key="i"><block><view>{{x.spec_value_name}}</view></block></block></view></view></block><block wx:if="{{goodsItem.$orig.is_virtual==0}}"><block><block wx:if="{{goodsItem.g12}}"><view class="error-tips"><text class="iconfont icon-gantanhao"></text><text>{{"该商品不支持"+orderCreateData.delivery.delivery_type_name}}</text></view></block></block></block><block wx:if="{{goodsItem.$orig.error&&goodsItem.$orig.error.message}}"><view class="error-tips"><text class="iconfont icon-gantanhao"></text><text>{{goodsItem.$orig.error.message}}</text></view></block></view><view class="goods-sub-section"><view class="color-base-text"><text class="unit price-style small">{{goodsItem.m0}}</text><text class="goods-price price-style large">{{goodsItem.g13[0]}}</text><text class="unit price-style small">{{"."+goodsItem.g14[1]}}</text></view><view><text class="font-size-tag">x</text><text class="font-size-base">{{goodsItem.$orig.num}}</text></view></view></view></view><block wx:if="{{calculateGoodsData.goods_list[goodsIndex].member_card_list}}"><view data-event-opts="{{[['tap',[['selectMemberGoodsCard',[goodsIndex]]]]]}}" class="member-goods-card order-cell" bindtap="__e"><text class="tit">次卡抵扣</text><view class="box text-overflow"><block wx:if="{{calculateGoodsData.goods_list[goodsIndex].card_promotion_money}}"><block><text class="text">{{"次卡抵扣"+calculateGoodsData.goods_list[goodsIndex].card_use_num+"张/"+calculateGoodsData.goods_list[goodsIndex].card_use_num+"次"}}</text><text class="price-font">{{"-￥"+goodsItem.f0}}</text></block></block><block wx:else><text class="color-tip">请选择次卡</text></block></view><text class="iconfont icon-right"></text></view></block><block wx:if="{{goodsData.goods_list[goodsIndex].goods_form}}"><view data-event-opts="{{[['tap',[['editForm',[goodsIndex]]]]]}}" class="goods-form" bindtap="__e"><ns-form class="vue-ref-in-for" vue-id="{{'1ca40ec2-1-'+goodsIndex}}" data="{{goodsData.goods_list[goodsIndex].goods_form.json_data}}" custom-attr="{{goodsItem.a0}}" data-ref="goodsForm" bind:__l="__l"></ns-form><text class="cell-more iconfont icon-right"></text><view class="shade"></view></view></block></view></block></view></view></block><view class="site-wrap buyer-message"><view class="order-cell"><text class="tit">买家留言</text><view data-event-opts="{{[['tap',[['openPopup',['buyerMessagePopup']]]]]}}" class="box text-overflow" bindtap="__e"><block wx:if="{{orderCreateData.buyer_message}}"><text>{{orderCreateData.buyer_message}}</text></block><block wx:else><text class="color-sub">无留言</text></block></view><text class="iconfont icon-right"></text></view></view><block wx:if="{{paymentData.system_form}}"><view class="system-form-wrap"><view class="order-cell"><text class="tit">{{paymentData.system_form.form_name}}</text></view><ns-form class="vue-ref" vue-id="1ca40ec2-2" data="{{paymentData.system_form.json_data}}" data-ref="form" bind:__l="__l"></ns-form></view></block><block wx:if="{{calculateGoodsData||promotionInfo||calculateGoodsData&&calculateGoodsData.max_usable_point>0||goodsData.invoice}}"><view class="site-wrap"><view class="site-footer"><block wx:if="{{$root.g15!=-1}}"><view class="order-cell coupon"><text class="tit">优惠券</text><view data-event-opts="{{[['tap',[['openPopup',['couponPopup']]]]]}}" class="box text-overflow" bindtap="__e"><block wx:if="{{orderCreateData.coupon&&orderCreateData.coupon.coupon_id}}"><text>已使用优惠券，优惠</text><text class="unit price-font">{{$root.m1}}</text><text class="money price-font">{{$root.f1}}</text></block><block wx:else><text>不使用优惠券</text></block></view><text class="iconfont icon-right"></text></view></block><block wx:if="{{promotionInfo}}"><view class="order-cell"><text class="tit">活动优惠</text><view data-event-opts="{{[['tap',[['openPopup',['promotionPopup']]]]]}}" class="box text-overflow" bindtap="__e"><text>{{promotionInfo.title}}</text></view><text class="iconfont icon-right"></text></view></block><block wx:if="{{calculateGoodsData&&calculateGoodsData.max_usable_point>0}}"><view class="order-cell point"><text class="tit"><text>{{"使用"+$root.m2+"积分可抵扣"}}</text><text class="unit price-font">{{$root.m3}}</text><text class="money price-font">{{$root.f2}}</text></text><view class="box"></view><ns-switch class="balance-switch" vue-id="1ca40ec2-3" checked="{{orderCreateData.is_point==1}}" data-event-opts="{{[['^change',[['usePoint']]]]}}" bind:change="__e" bind:__l="__l"></ns-switch></view></block><block wx:if="{{goodsData.invoice.invoice_status==1}}"><view class="order-cell order-invoice-cell"><text class="tit">发票</text><view data-event-opts="{{[['tap',[['openPopup',['invoicePopup']]]]]}}" class="box text-overflow" bindtap="__e"><block wx:if="{{orderCreateData.is_invoice==1}}"><text>{{(orderCreateData.invoice_type==1?'纸质':'电子')+"发票("+orderCreateData.invoice_content+")"}}</text></block><block wx:else><text>无需发票</text></block></view><text class="iconfont icon-right"></text></view></block></view></view></block><block wx:if="{{$root.g16}}"><view class="site-wrap box member-card-wrap"><view data-event-opts="{{[['tap',[['selectMemberCard',['$event']]]]]}}" class="head" bindtap="__e"><text class="iconfont icon-huiyuan"></text><view class="info">{{'开通'+paymentData.recommend_member_card.level_name+''}}<text>本单预计可省</text><text class="price-color">{{$root.f3}}</text><text>元</text></view><text class="{{['iconfont',orderCreateData.is_open_card==1?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}"></text></view><block wx:if="{{orderCreateData.is_open_card}}"><view class="body"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectMemberCardUnit',['$0'],[[['cardChargeType','',index,'key']]]]]]]}}" class="{{['item',(item.$orig.key==orderCreateData.member_card_unit)?'active color-base-border':'']}}" bindtap="__e"><view class="title">{{item.$orig.title}}</view><view class="price price-font">{{item.m4+item.m5+"/"+item.$orig.unit}}</view><block wx:if="{{item.$orig.key==orderCreateData.member_card_unit}}"><text class="iconfont icon-icon color-base-text price-font identify"></text></block></view></block></view></block></view></block><block wx:if="{{calculateData}}"><view class="order-money"><view class="order-cell"><text class="tit">商品金额</text><view class="box"><text class="unit color-title price-font">{{$root.m6}}</text><text class="money color-title price-font">{{$root.f4}}</text></view></view><block wx:if="{{calculateData.is_virtual==0&&calculateData.delivery_money>0}}"><view class="order-cell"><text class="tit">运费</text><view class="box color-base-text"><text class="operator">+</text><text class="unit price-font">{{$root.m7}}</text><text class="money price-font">{{$root.f5}}</text></view></view></block><block wx:if="{{orderCreateData.is_invoice&&calculateData.invoice_money>0}}"><view class="order-cell"><text class="tit"><text>税费</text><text class="color-base-text font-bold price-font">{{"("+goodsData.invoice.invoice_rate+"%)"}}</text></text><view class="box color-base-text"><text class="operator">+</text><text class="unit price-font">{{$root.m8}}</text><text class="money price-font">{{$root.f6}}</text></view></view></block><block wx:if="{{orderCreateData.is_invoice&&calculateData.invoice_delivery_money>0}}"><view class="order-cell"><text class="tit">发票邮寄费</text><view class="box color-base-text"><text class="operator">+</text><text class="unit price-font">{{$root.m9}}</text><text class="money price-font">{{$root.f7}}</text></view></view></block><block wx:if="{{calculateData.promotion_money>0}}"><view class="order-cell"><text class="tit">优惠</text><view class="box color-base-text"><text class="operator">-</text><text class="unit price-font">{{$root.m10}}</text><text class="money price-font">{{$root.f8}}</text></view></view></block><block wx:if="{{calculateData.coupon_money}}"><view class="order-cell"><text class="tit">优惠券</text><view class="box color-base-text"><text class="operator">-</text><text class="unit price-font">{{$root.m11}}</text><text class="money price-font">{{$root.f9}}</text></view></view></block><block wx:if="{{calculateData.point_money>0}}"><view class="order-cell"><text class="tit">积分抵扣</text><view class="box color-base-text"><text class="operator">-</text><text class="unit price-font">{{$root.m12}}</text><text class="money price-font">{{$root.f10}}</text></view></view></block><block wx:if="{{calculateData.member_card_money>0}}"><view class="order-cell"><text class="tit">会员卡</text><view class="box color-base-text"><text class="operator">+</text><text class="unit price-font">{{$root.m13}}</text><text class="money price-font">{{$root.f11+''}}</text></view></view></block></view><block wx:if="{{transactionAgreement.title&&transactionAgreement.content}}"><view class="agreement">购买前请先阅读<text data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" bindtap="__e">{{"《"+transactionAgreement.title+"》"}}</text>，下单即代表同意该协议</view></block><view class="order-submit bottom-safe-area"><view class="order-settlement-info"><text class="font-size-base color-tip margin-right">{{"共"+calculateData.goods_num+"件"}}</text><text class="font-size-base">合计：</text><text class="unit price-font">{{$root.m14}}</text><text class="money price-font">{{$root.g17[0]}}</text><text class="unit price-font">{{"."+$root.g18[1]}}</text></view><view class="submit-btn"><block wx:if="{{!$root.m15}}"><button class="mini" type="primary" size="mini" data-event-opts="{{[['tap',[['create']]]]}}" bindtap="__e">提交订单</button></block><block wx:else><button class="no-submit mini" size="mini">{{"差"+$root.f12+"起送"}}</button></block></view></view><view class="order-submit-block"></view><block wx:if="{{calculateData}}"><payment class="vue-ref" bind:close="__e" vue-id="1ca40ec2-4" data-ref="choosePaymentPopup" data-event-opts="{{[['^close',[['payClose']]]]}}" bind:__l="__l"></payment></block></block><uni-popup class="vue-ref" vue-id="1ca40ec2-5" type="bottom" mask-click="{{false}}" data-ref="invoicePopup" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="invoice-popup popup" style="{{(orderCreateData.is_invoice==1?'height: 83vh;':'height: 48vh;')}}" catchtouchmove="__e"><view class="popup-header"><text class="tit">发票</text><text data-event-opts="{{[['tap',[['closePopup',['invoicePopup']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><scroll-view class="{{['popup-body',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view><block wx:if="{{goodsData.invoice}}"><view class="invoice-cell"><text class="tit">需要发票</text><view class="option-grpup"><view data-event-opts="{{[['tap',[['changeIsInvoice',['$event']]]]]}}" class="{{['option-item',(orderCreateData.is_invoice==0)?'color-base-bg active':'']}}" bindtap="__e">不需要</view><view data-event-opts="{{[['tap',[['changeIsInvoice',['$event']]]]]}}" class="{{['option-item',(orderCreateData.is_invoice==1)?'color-base-bg active':'']}}" bindtap="__e">需要</view></view></view></block><block wx:if="{{orderCreateData.is_invoice==1}}"><block><view class="invoice-cell"><text class="tit">发票类型</text><view class="option-grpup"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" data-event-params="{{({item})}}" class="{{['option-item',(orderCreateData.invoice_type==item)?'color-base-bg active':'']}}" bindtap="__e">{{''+(item==1?'纸质':'电子')+''}}</view></block></view></view><view class="invoice-cell"><text class="tit">抬头类型</text><view class="option-grpup"><view data-event-opts="{{[['tap',[['changeInvoiceTitleType',[1]]]]]}}" class="{{['option-item',(orderCreateData.invoice_title_type==1)?'color-base-bg active':'']}}" bindtap="__e">个人</view><view data-event-opts="{{[['tap',[['changeInvoiceTitleType',[2]]]]]}}" class="{{['option-item',(orderCreateData.invoice_title_type==2)?'color-base-bg active':'']}}" bindtap="__e">企业</view></view></view><view class="invoice-cell"><text class="tit">发票信息</text><view class="invoice-form-group"><input type="text" placeholder="请填写抬头名称" data-event-opts="{{[['input',[['__set_model',['$0','invoice_title','$event',['trim']],['orderCreateData']]]],['blur',[['$forceUpdate']]]]}}" value="{{orderCreateData.invoice_title}}" bindinput="__e" bindblur="__e"/><block wx:if="{{orderCreateData.invoice_title_type==2}}"><input type="text" placeholder="请填写纳税人识别号" data-event-opts="{{[['input',[['__set_model',['$0','taxpayer_number','$event',['trim']],['orderCreateData']]]],['blur',[['$forceUpdate']]]]}}" value="{{orderCreateData.taxpayer_number}}" bindinput="__e" bindblur="__e"/></block><block wx:if="{{orderCreateData.invoice_type==1}}"><input type="text" placeholder="请填写邮寄地址" data-event-opts="{{[['input',[['__set_model',['$0','invoice_full_address','$event',['trim']],['orderCreateData']]]],['blur',[['$forceUpdate']]]]}}" value="{{orderCreateData.invoice_full_address}}" bindinput="__e" bindblur="__e"/></block><block wx:if="{{orderCreateData.invoice_type==2}}"><input type="text" placeholder="请填写邮箱" data-event-opts="{{[['input',[['__set_model',['$0','invoice_email','$event',['trim']],['orderCreateData']]]],['blur',[['$forceUpdate']]]]}}" value="{{orderCreateData.invoice_email}}" bindinput="__e" bindblur="__e"/></block></view></view><view class="invoice-cell"><text class="tit">发票内容</text><view class="option-grpup"><block wx:for="{{goodsData.invoice.invoice_content_array}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeInvoiceContent',['$0'],[[['goodsData.invoice.invoice_content_array','',index]]]]]]]}}" class="{{['option-item','content',(item==orderCreateData.invoice_content)?'color-base-bg active':'']}}" bindtap="__e">{{''+item+''}}</view></block></view></view></block></block><view class="invoice-tops">发票内容将以根据税法调整，具体请以展示为准，发票内容显示详细商品名 称及价格信息</view></view></scroll-view><view data-event-opts="{{[['tap',[['saveInvoice',['$event']]]]]}}" class="{{['popup-footer',(isIphoneX)?'bottom-safe-area':'']}}" bindtap="__e"><view class="confirm-btn color-base-bg">确定</view></view></view></uni-popup><block wx:if="{{promotionInfo}}"><uni-popup class="vue-ref" vue-id="1ca40ec2-6" type="bottom" data-ref="promotionPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="promotion-popup popup"><view class="popup-header"><text class="tit">活动优惠</text><text data-event-opts="{{[['tap',[['closePopup',['promotionPopup']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><scroll-view class="{{['popup-body',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view class="order-cell" style="align-items:baseline;"><view class="tit"><text class="promotion-mark ns-gradient-promotionpages-payment">{{promotionInfo.title}}</text></view><view class="promotion-content"><view class="tit tit-content" style="white-space:pre-line;"><rich-text nodes="{{promotionInfo.content}}"></rich-text></view></view></view></scroll-view><view class="{{['popup-footer',(isIphoneX)?'bottom-safe-area':'']}}"><view data-event-opts="{{[['tap',[['closePopup',['promotionPopup']]]]]}}" class="confirm-btn color-base-bg" bindtap="__e">确定</view></view></view></uni-popup></block><uni-popup class="vue-ref" vue-id="1ca40ec2-7" type="bottom" data-ref="deliveryPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="delivery-popup popup"><view class="popup-header"><text class="tit">已为您甄选出附近所有相关门店</text><text data-event-opts="{{[['tap',[['closePopup',['deliveryPopup']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><view class="{{['popup-body','store-popup',(isIphoneX)?'safe-area':'']}}"><mescroll-uni class="vue-ref" vue-id="{{('1ca40ec2-8')+','+('1ca40ec2-7')}}" top="50px" data-ref="mescroll" data-event-opts="{{[['^getData',[['getStore']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><view class="delivery-content"><block wx:if="{{storeData}}"><block><block wx:for="{{storeData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectPickupPoint',['$0'],[[['storeData','',index]]]]]]]}}" class="item-wrap" bindtap="__e"><view class="detail"><view class="{{['name',item.store_id==orderCreateData.delivery.store_id?'color-base-text':'']}}"><text>{{item.store_name}}</text><block wx:if="{{item.distance}}"><text>{{"("+item.distance+"km)"}}</text></block></view><view class="info"><block wx:if="{{item.status==0&&item.close_desc}}"><view class="close-desc">{{item.close_desc}}</view></block><view class="{{['font-size-goods-tag',item.store_id==orderCreateData.delivery.store_id?'color-base-text':'']}}">{{"营业时间："+item.open_date}}</view><view class="{{['font-size-goods-tag',item.store_id==orderCreateData.delivery.store_id?'color-base-text':'']}}">{{"地址："+item.full_address+item.address}}</view></view></view><block wx:if="{{item.store_id==orderCreateData.delivery.store_id}}"><view class="icon"><text class="iconfont icon-yuan_checked color-base-text"></text></view></block></view></block></block></block><block wx:else><view class="empty-wrap"><ns-empty vue-id="{{('1ca40ec2-9')+','+('1ca40ec2-8')}}" text="所选择收货地址附近没有可以自提的门店" isIndex="{{false}}" bind:__l="__l"></ns-empty></view></block></view></view></mescroll-uni></view></view></uni-popup><uni-popup class="vue-ref" vue-id="1ca40ec2-10" type="bottom" data-ref="buyerMessagePopup" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="buyermessag-popup popup" style="height:auto;" catchtouchmove="__e"><view class="popup-header"><text class="tit">买家留言</text><text data-event-opts="{{[['tap',[['closePopup',['buyerMessagePopup']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><scroll-view class="{{['popup-body',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view><view class="buyermessag-cell"><view class="buyermessag-form-group"><textarea type="text" maxlength="100" placeholder="留言前建议先与商家协调一致" placeholder-class="color-tip" data-event-opts="{{[['input',[['__set_model',['$0','buyer_message','$event',[]],['orderCreateData']]]]]}}" value="{{orderCreateData.buyer_message}}" bindinput="__e"></textarea></view></view></view></scroll-view><view data-event-opts="{{[['tap',[['saveBuyerMessage',['$event']]]]]}}" class="{{['popup-footer',(isIphoneX)?'bottom-safe-area':'']}}" bindtap="__e"><view class="confirm-btn color-base-bg">确定</view></view></view></uni-popup><block wx:if="{{calculateGoodsData}}"><uni-popup class="vue-ref" vue-id="1ca40ec2-11" type="bottom" mask-click="{{false}}" data-ref="couponPopup" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="coupon-popup popup" catchtouchmove="__e"><view class="popup-header"><text class="tit">优惠券</text><text data-event-opts="{{[['tap',[['closePopup',['couponPopup']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><scroll-view class="{{['popup-body',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><block wx:if="{{$root.g19>0}}"><view><block wx:for="{{$root.l3}}" wx:for-item="couponItem" wx:for-index="couponIndex" wx:key="couponIndex"><view data-event-opts="{{[['tap',[['selectCoupon',['$0'],[[['coupon_list','',couponIndex]]]]]]]}}" class="coupon-item" bindtap="__e"><view class="coupon-info" style="{{'background-color:'+('var(--main-color-shallow)')+';'}}"><view class="info-wrap"><image class="coupon-line" mode="heightFix" src="{{couponItem.g20}}"></image><view class="coupon-money"><block wx:if="{{couponItem.$orig.type=='divideticket'}}"><text class="unit">{{couponItem.m16}}</text><text class="money">{{couponItem.m17}}</text></block><block wx:else><block wx:if="{{couponItem.$orig.type=='reward'}}"><text class="unit">{{couponItem.m18}}</text><text class="money">{{couponItem.m19}}</text></block><block wx:else><block wx:if="{{couponItem.$orig.type=='discount'}}"><text class="money">{{couponItem.m20}}</text><text class="unit">折</text></block></block></block><view class="at-least"><block wx:if="{{couponItem.$orig.at_least>0}}">{{"满"+couponItem.$orig.at_least+"可用"}}</block><block wx:else>无门槛</block></view></view></view><view class="desc-wrap"><view class="coupon-name">{{couponItem.$orig.coupon_name}}</view><block wx:if="{{couponItem.$orig.type=='discount'&&couponItem.$orig.discount_limit>0}}"><view class="limit">{{"最多可抵￥"+couponItem.$orig.discount_limit}}</view></block><view class="time font-size-goods-tag">{{"有效期："+(couponItem.$orig.end_time?couponItem.g21:'长期有效')}}</view></view><view class="{{['iconfont',orderCreateData.coupon.coupon_id==couponItem.$orig.coupon_id?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}"></view></view></view></block></view></block><block wx:else><view class="coupon-empty">暂无可用的优惠券</view></block></scroll-view><view class="{{['popup-footer',(isIphoneX)?'bottom-safe-area':'']}}"><view data-event-opts="{{[['tap',[['useCoupon',['$event']]]]]}}" class="confirm-btn color-base-bg" bindtap="__e">确定</view></view></view></uni-popup></block><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" bindtouchmove="__e"><uni-popup class="vue-ref" vue-id="1ca40ec2-12" type="center" maskClick="{{false}}" data-ref="agreementPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="agreement-conten-box"><view class="close"><text data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><view class="title">{{transactionAgreement.title}}</view><view class="con"><scroll-view class="con" scroll-y="true"><ns-mp-html vue-id="{{('1ca40ec2-13')+','+('1ca40ec2-12')}}" content="{{transactionAgreement.content}}" bind:__l="__l"></ns-mp-html></scroll-view></view></view></uni-popup></view><uni-popup class="vue-ref" vue-id="1ca40ec2-14" type="bottom" data-ref="editFormPopup" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="form-popup popup" style="height:auto;" catchtouchmove="__e"><view class="popup-header"><text class="tit">买家信息</text><text data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><scroll-view class="{{['popup-body',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><block wx:if="{{tempFormData}}"><ns-form class="vue-ref" vue-id="{{('1ca40ec2-15')+','+('1ca40ec2-14')}}" data="{{tempFormData.json_data}}" data-ref="tempForm" bind:__l="__l"></ns-form></block></scroll-view><view data-event-opts="{{[['tap',[['saveForm',['$event']]]]]}}" class="{{['popup-footer',(isIphoneX)?'bottom-safe-area':'']}}" bindtap="__e"><view class="confirm-btn color-base-bg">确定</view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="1ca40ec2-16" type="bottom" data-ref="memberGoodsCardPopup" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="member-card-popup popup" catchtouchmove="__e"><view class="popup-header"><text class="tit">选择次卡</text><text data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><scroll-view class="{{['popup-body',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index"><view data-event-opts="{{[['tap',[['e7',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="card-item" bindtap="__e"><view class="content"><view class="title">{{item.$orig.goods_name}}</view><view class="info"><block wx:if="{{item.$orig.card_type=='timecard'}}"><text>不限次数</text></block><block wx:if="{{item.$orig.card_type=='oncecard'}}"><text>{{"剩余"+(item.$orig.num-item.$orig.use_num)+"次"}}</text></block><block wx:if="{{item.$orig.card_type=='commoncard'}}"><text>{{"剩余"+(item.$orig.total_num-item.$orig.total_use_num)+"次"}}</text></block><text>|</text><text>{{item.$orig.end_time?item.g22:'长期有效'}}</text></view></view><view class="{{['iconfont',selectGoodsCard.itemId==item.$orig.item_id?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}"></view></view></block></scroll-view><view data-event-opts="{{[['tap',[['saveMemberGoodsCard',['$event']]]]]}}" class="{{['popup-footer',(isIphoneX)?'bottom-safe-area':'']}}" bindtap="__e"><view class="confirm-btn color-base-bg">确定</view></view></view></uni-popup></block></scroll-view><ns-select-time class="vue-ref" bind:selectTime="__e" vue-id="1ca40ec2-17" data-ref="timePopup" data-event-opts="{{[['^selectTime',[['selectPickupTime']]]]}}" bind:__l="__l"></ns-select-time><ns-login class="vue-ref" vue-id="1ca40ec2-18" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="1ca40ec2-19" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>