<view class="data-v-a6e9ebaa"><block wx:if="{{reward}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="reward-popup data-v-a6e9ebaa" catchtouchmove="__e"><uni-popup vue-id="1861fa64-1" type="center" maskClick="{{false}}" data-ref="registerReward" class="data-v-a6e9ebaa vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="reward-wrap data-v-a6e9ebaa"><image class="bg-img-head data-v-a6e9ebaa" src="{{$root.g0}}" mode="widthFix"></image><image class="bg-img-money data-v-a6e9ebaa" src="{{$root.g1}}" mode="widthFix"></image><image class="bg-img data-v-a6e9ebaa" src="{{$root.g2}}" mode="widthFix"></image><view class="wrap data-v-a6e9ebaa"><view class="data-v-a6e9ebaa"><scroll-view class="register-box data-v-a6e9ebaa" scroll-y="true"><view class="reward-content data-v-a6e9ebaa"><block wx:if="{{reward.point>0}}"><view class="reward-item data-v-a6e9ebaa"><view class="head data-v-a6e9ebaa">积分奖励</view><view class="content data-v-a6e9ebaa"><view class="info data-v-a6e9ebaa"><view class="data-v-a6e9ebaa"><text class="num data-v-a6e9ebaa">{{reward.point}}</text><text class="type data-v-a6e9ebaa">积分</text></view><view class="desc data-v-a6e9ebaa">用于下单时抵现或兑换商品等</view></view><view data-event-opts="{{[['tap',[['closeRewardPopup',['point']]]]]}}" class="tip data-v-a6e9ebaa" bindtap="__e">立即查看</view></view></view></block><block wx:if="{{reward.growth>0}}"><view class="reward-item data-v-a6e9ebaa"><view class="head data-v-a6e9ebaa">成长值</view><view class="content data-v-a6e9ebaa"><view class="info data-v-a6e9ebaa"><view class="data-v-a6e9ebaa"><text class="num data-v-a6e9ebaa">{{reward.growth}}</text><text class="type data-v-a6e9ebaa">成长值</text></view><view class="desc data-v-a6e9ebaa">用于提升会员等级</view></view><view data-event-opts="{{[['tap',[['closeRewardPopup',['growth']]]]]}}" class="tip data-v-a6e9ebaa" bindtap="__e">立即查看</view></view></view></block><block wx:if="{{reward.balance>0}}"><view class="reward-item data-v-a6e9ebaa"><view class="head data-v-a6e9ebaa">红包奖励</view><view class="content data-v-a6e9ebaa"><view class="info data-v-a6e9ebaa"><view class="data-v-a6e9ebaa"><text class="num data-v-a6e9ebaa">{{reward.balance}}</text><text class="type data-v-a6e9ebaa">元</text></view><view class="desc data-v-a6e9ebaa">不可提现下单时可用</view></view><view data-event-opts="{{[['tap',[['closeRewardPopup',['balance']]]]]}}" class="tip data-v-a6e9ebaa" bindtap="__e">立即查看</view></view></view></block><block wx:if="{{$root.g3>0}}"><view class="reward-item data-v-a6e9ebaa"><view class="head data-v-a6e9ebaa">优惠券奖励</view><block wx:for="{{reward.coupon_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content data-v-a6e9ebaa"><view class="info data-v-a6e9ebaa"><view class="data-v-a6e9ebaa"><text class="num coupon-name data-v-a6e9ebaa">{{item.coupon_name}}</text></view><block wx:if="{{item.at_least>0}}"><view class="desc data-v-a6e9ebaa">{{'满'+item.at_least+(item.type=='discount'?'打'+item.discount+'折':'减'+item.money)+''}}</view></block><block wx:else><view class="desc data-v-a6e9ebaa">{{'无门槛，'+(item.type=='discount'?'打'+item.discount+'折':'减'+item.money)+''}}</view></block></view><view data-event-opts="{{[['tap',[['closeRewardPopup',['coupon']]]]]}}" class="tip data-v-a6e9ebaa" bindtap="__e">立即查看</view></view></block></view></block></view></scroll-view></view></view><view data-event-opts="{{[['tap',[['closeRewardPopup']]]]}}" class="close-btn data-v-a6e9ebaa" bindtap="__e"><text class="iconfont icon-close data-v-a6e9ebaa"></text></view></view></uni-popup></view></block></view>