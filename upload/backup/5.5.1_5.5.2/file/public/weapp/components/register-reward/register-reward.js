(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/register-reward/register-reward"],{"2d16":function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n={name:"register-reward",components:{uniPopup:function(){t.e("components/uni-popup/uni-popup").then(function(){return resolve(t("d745"))}.bind(null,t)).catch(t.oe)}},data:function(){return{reward:null,back:""}},created:function(){},methods:{open:function(e){e&&(this.back=e),this.addonIsExist.memberregister?this.getRegisterReward():this.closeRewardPopup()},cancel:function(){this.$refs.registerReward.close()},getRegisterReward:function(){var e=this;this.$api.sendRequest({url:"/memberregister/api/Config/Config",success:function(r){if(r.code>=0){var t=r.data;1==t.is_use&&(t.value.point>0||t.value.balance>0||t.value.growth>0||t.value.coupon_list.length>0)?(e.reward=t.value,setTimeout((function(){e.$refs.registerReward.open()}))):e.closeRewardPopup()}else e.closeRewardPopup()}})},closeRewardPopup:function(e){switch(this.$refs.registerReward&&this.$refs.registerReward.close(),e){case"point":this.$util.redirectTo("/pages_tool/member/point_detail",{});break;case"balance":this.$util.redirectTo("/pages_tool/member/balance_detail",{});break;case"growth":this.$util.redirectTo("/pages_tool/member/level",{});break;case"coupon":this.$util.redirectTo("/pages_tool/member/coupon",{});break;default:this.$util.loginComplete("/pages/index/index","redirectTo");break}}}};r.default=n},"349c":function(e,r,t){"use strict";t.r(r);var n=t("e841"),i=t("5f97");for(var a in i)["default"].indexOf(a)<0&&function(e){t.d(r,e,(function(){return i[e]}))}(a);t("65a4"),t("4ce3");var u=t("828b"),o=Object(u["a"])(i["default"],n["b"],n["c"],!1,null,"a6e9ebaa",null,!1,n["a"],void 0);r["default"]=o.exports},"4ce3":function(e,r,t){"use strict";var n=t("c4f8"),i=t.n(n);i.a},"5f97":function(e,r,t){"use strict";t.r(r);var n=t("2d16"),i=t.n(n);for(var a in n)["default"].indexOf(a)<0&&function(e){t.d(r,e,(function(){return n[e]}))}(a);r["default"]=i.a},"65a4":function(e,r,t){"use strict";var n=t("8edb"),i=t.n(n);i.a},"8edb":function(e,r,t){},c4f8:function(e,r,t){},e841:function(e,r,t){"use strict";t.d(r,"b",(function(){return i})),t.d(r,"c",(function(){return a})),t.d(r,"a",(function(){return n}));var n={uniPopup:function(){return t.e("components/uni-popup/uni-popup").then(t.bind(null,"d745"))}},i=function(){var e=this,r=e.$createElement,t=(e._self._c,e.reward?e.$util.img("public/uniapp/register_reward/register_reward_img.png"):null),n=e.reward?e.$util.img("public/uniapp/register_reward/register_reward_money.png"):null,i=e.reward?e.$util.img("public/uniapp/register_reward/register_reward_head.png"):null,a=e.reward?e.reward.coupon_list.length:null;e.$mp.data=Object.assign({},{$root:{g0:t,g1:n,g2:i,g3:a}})},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/register-reward/register-reward-create-component',
    {
        'components/register-reward/register-reward-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("349c"))
        })
    },
    [['components/register-reward/register-reward-create-component']]
]);
