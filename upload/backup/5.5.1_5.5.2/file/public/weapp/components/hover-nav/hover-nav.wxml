<block wx:if="{{pageCount==1||need}}"><view class="fixed-box" style="{{'height:'+(fixBtnShow?'330rpx':'120rpx')+';'}}"><block wx:if="{{fixBtnShow}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="btn-item" bindtap="__e"><text class="iconfont icon-shouye1"></text><view>首页</view></view></block><block wx:if="{{fixBtnShow}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="btn-item" bindtap="__e"><text class="iconfont icon-yonghu"></text><view>我的</view></view></block><block wx:if="{{fixBtnShow}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="btn-item icon-xiala" bindtap="__e"><text class="iconfont icon-unfold"></text></view></block><block wx:else><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="{{['btn-item','switch',(fixBtnShow)?'show':'']}}" bindtap="__e"><view>快捷</view><view>导航</view></view></block></view></block>