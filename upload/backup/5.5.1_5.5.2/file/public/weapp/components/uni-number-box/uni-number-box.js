(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-number-box/uni-number-box"],{"0a42":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return u})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},u=[]},"1b30":function(t,e,i){},"1caf":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"UniNumberBox",props:{value:{type:[Number,String],default:1},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},disabled:{type:Boolean,default:!1},inputDisabled:{type:Boolean,default:!1},size:{type:String,default:"default"}},data:function(){return{inputValue:0,initialValue:0,load:!0}},watch:{value:function(t){this.inputValue=+t}},created:function(){this.initialValue=+this.value,this.inputValue=+this.value},methods:{_calcValue:function(t){if(!this.disabled){var e=this._getDecimalScale(),i=this.inputValue*e,n=this.step*e;"minus"===t?i-=n:"plus"===t&&(i+=n),i<this.min&&"minus"===t||i>this.max&&"plus"===t?this.$emit("limit",{value:this.inputValue,type:t}):(this.inputValue=i/e,this.$emit("change",this.inputValue))}},_getDecimalScale:function(){var t=1;return~~this.step!==this.step&&(t=Math.pow(10,(this.step+"").split(".")[1].length)),t},_onInput:function(t){var e=this;setTimeout((function(){var i=t.detail.value;i=+i,i>e.max?(i=e.max,e.$util.showToast({title:"商品库存不足"})):i<e.min&&(e.$util.showToast({title:"商品最少购买"+e.min+"件"}),i=e.min),i||(i=1),e.inputValue=i,e.$forceUpdate(),e.$emit("change",i)}),0)}}};e.default=n},"499c":function(t,e,i){"use strict";i.r(e);var n=i("0a42"),u=i("9b45");for(var a in u)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return u[t]}))}(a);i("9cd2");var l=i("828b"),s=Object(l["a"])(u["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"9b45":function(t,e,i){"use strict";i.r(e);var n=i("1caf"),u=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=u.a},"9cd2":function(t,e,i){"use strict";var n=i("1b30"),u=i.n(n);u.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-number-box/uni-number-box-create-component',
    {
        'components/uni-number-box/uni-number-box-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("499c"))
        })
    },
    [['components/uni-number-box/uni-number-box-create-component']]
]);
