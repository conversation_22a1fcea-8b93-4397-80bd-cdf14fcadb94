.uni-numbox{display:inline-flex;flex-direction:row;justify-content:flex-start;align-items:center;height:70rpx;position:relative}.uni-numbox.small{height:44rpx}.uni-numbox:after{content:"";position:absolute;-webkit-transform-origin:center;transform-origin:center;box-sizing:border-box;pointer-events:none;top:-50%;left:-50%;right:-50%;bottom:-50%;border-radius:12rpx;-webkit-transform:scale(.5);transform:scale(.5)}.uni-numbox__minus,
.uni-numbox__plus{width:40rpx;height:40rpx;border-radius:50%;background-size:100% 100%;background-position:50%}.uni-numbox__value{position:relative;background-color:#f8f8f8;width:80rpx;height:40rpx;text-align:center;border:1px solid #eee;display:inline-block;line-height:36rpx;font-weight:700;margin:0;padding:0;vertical-align:top;min-height:0;border-left:none;border-right:none}.uni-numbox__value.small{width:60rpx;font-size:24rpx}.uni-numbox__value:after{content:"";position:absolute;-webkit-transform-origin:center;transform-origin:center;box-sizing:border-box;pointer-events:none;top:-50%;left:-50%;right:-50%;bottom:-50%;border-top-width:0;border-bottom-width:0;-webkit-transform:scale(.5);transform:scale(.5)}.uni-numbox--disabled{color:silver}.uni-numbox button{width:40rpx;height:40rpx;display:inline-block;box-sizing:initial;border:1px solid #eee;padding:0;margin:0;border-radius:0;background-color:#fff;font-weight:700}.uni-numbox button.disabled{color:#eee;background-color:#f8f8f8!important}.uni-numbox button.decrease{font-size:44rpx;line-height:32rpx}.uni-numbox button.increase{font-size:32rpx;line-height:36rpx}