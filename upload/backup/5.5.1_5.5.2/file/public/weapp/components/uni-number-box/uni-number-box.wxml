<view class="{{['uni-numbox',(size=='small')?'small':'']}}"><button class="{{['decrease',(inputValue<=min||disabled)?'disabled':'',(size=='small')?'small':'']}}" type="default" data-event-opts="{{[['tap',[['_calcValue',['minus']]]]]}}" bindtap="__e">-</button><input class="{{['uni-input','uni-numbox__value',(size=='small')?'small':'']}}" disabled="{{disabled||inputDisabled}}" type="number" data-event-opts="{{[['blur',[['_onInput',['$event']]]],['input',[['__set_model',['','inputValue','$event',[]]]]]]}}" value="{{inputValue}}" bindblur="__e" bindinput="__e"/><button class="{{['increase',(inputValue>=max||disabled)?'disabled':'',(size=='small')?'small':'']}}" type="default" data-event-opts="{{[['tap',[['_calcValue',['plus']]]]]}}" bindtap="__e">+</button></view>