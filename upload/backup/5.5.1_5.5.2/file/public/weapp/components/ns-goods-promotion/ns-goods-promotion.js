(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-goods-promotion/ns-goods-promotion"],{"4d6f":function(t,n,o){"use strict";o.r(n);var e=o("5591"),r=o.n(e);for(var i in e)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(i);n["default"]=r.a},5591:function(t,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={name:"ns-goods-promotion",props:{promotion:{type:String,default:""}},data:function(){return{goodsPromotion:{type:Array}}},created:function(){},methods:{refresh:function(t){this.goodsPromotion=t},redirectTo:function(t,n){this.$util.redirectTo(t,n)}}};n.default=e},"97d8":function(t,n,o){"use strict";o.d(n,"b",(function(){return e})),o.d(n,"c",(function(){return r})),o.d(n,"a",(function(){}));var e=function(){var t=this.$createElement,n=(this._self._c,this.goodsPromotion.length);this.$mp.data=Object.assign({},{$root:{g0:n}})},r=[]},aaed:function(t,n,o){},f073:function(t,n,o){"use strict";var e=o("aaed"),r=o.n(e);r.a},ffa1:function(t,n,o){"use strict";o.r(n);var e=o("97d8"),r=o("4d6f");for(var i in r)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return r[t]}))}(i);o("f073");var u=o("828b"),a=Object(u["a"])(r["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=a.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-goods-promotion/ns-goods-promotion-create-component',
    {
        'components/ns-goods-promotion/ns-goods-promotion-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("ffa1"))
        })
    },
    [['components/ns-goods-promotion/ns-goods-promotion-create-component']]
]);
