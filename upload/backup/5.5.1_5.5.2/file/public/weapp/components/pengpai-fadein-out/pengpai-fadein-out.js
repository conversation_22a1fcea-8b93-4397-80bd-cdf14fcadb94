(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/pengpai-fadein-out/pengpai-fadein-out"],{"26b8":function(t,i,n){"use strict";var e=n("4499"),a=n.n(e);a.a},4499:function(t,i,n){},4974:function(t,i,n){"use strict";n.r(i);var e=n("5211"),a=n.n(e);for(var o in e)["default"].indexOf(o)<0&&function(t){n.d(i,t,(function(){return e[t]}))}(o);i["default"]=a.a},"49e1":function(t,i,n){"use strict";n.r(i);var e=n("8fd7"),a=n("4974");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(i,t,(function(){return a[t]}))}(o);n("26b8");var u=n("828b"),r=Object(u["a"])(a["default"],e["b"],e["c"],!1,null,"273b4204",null,!1,e["a"],void 0);i["default"]=r.exports},5211:function(t,i,n){"use strict";(function(t){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n={name:"pengpai-fadein-out",props:{duration:{type:Number,default:3e3},wait:{type:Number,default:3500},top:{type:Number,default:350},left:{type:Number,default:10},radius:{type:Number,default:30},info:{type:[Array,Object],default:function(){return[]}}},data:function(){return{animationData:{},animationNumber:{},show:!0,index:0,penpaiData:{},timeIndex:0}},computed:{isShow:function(){return this.penpaiData&&Object.keys(this.penpaiData).length}},mounted:function(){this.initData()},methods:{initData:function(){var t=this;this.penpaiData=this.info[this.index],this.donghua(),clearInterval(this.timeIndex),this.timeIndex=setInterval((function(){t.index==t.info.length-1?t.index=0:t.index++,t.penpaiData=t.info[t.index],t.donghua()}),this.duration+this.wait)},donghua:function(){var i=this;this.animationData=t.createAnimation({duration:this.duration/2,timingFunction:"ease"}).top(this.top-this.radius).opacity(.9).step().export(),setTimeout((function(){i.animationData=t.createAnimation({duration:i.duration/2,timingFunction:"ease"}).top(i.top-2*i.radius).opacity(0).step().export()}),this.wait),setTimeout((function(){i.animationData=t.createAnimation({duration:i.duration/2,timingFunction:"ease"}).top(i.top).opacity(0).step().export()}),2800)},closeTimer:function(){clearInterval(this.timeIndex)}}};i.default=n}).call(this,n("df3c")["default"])},"8fd7":function(t,i,n){"use strict";n.d(i,"b",(function(){return e})),n.d(i,"c",(function(){return a})),n.d(i,"a",(function(){}));var e=function(){var t=this.$createElement,i=(this._self._c,this.isShow&&this.show?this.$util.img(this.penpaiData.img):null);this.$mp.data=Object.assign({},{$root:{g0:i}})},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/pengpai-fadein-out/pengpai-fadein-out-create-component',
    {
        'components/pengpai-fadein-out/pengpai-fadein-out-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("49e1"))
        })
    },
    [['components/pengpai-fadein-out/pengpai-fadein-out-create-component']]
]);
