<block wx:if="{{isShow}}"><view class="data-v-273b4204"><block wx:if="{{show}}"><view class="message data-v-273b4204 vue-ref" style="{{'top:'+(top+'px')+';'+('left:'+(left+'px')+';')}}" animation="{{animationData}}" data-ref="ani"><view class="round bg-gradual-orange flex justify-start shadow data-v-273b4204" style="padding:3px;"><view class="cu-avatar cu-a-sm round data-v-273b4204" style="{{'background-image:'+('url('+$root.g0+')')+';'}}"></view><view class="padding-lr-sm flex align-center data-v-273b4204"><text class="text-sm data-v-273b4204">{{penpaiData.title}}</text></view></view></view></block></view></block>