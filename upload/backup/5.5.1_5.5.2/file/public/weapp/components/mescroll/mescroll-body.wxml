<view data-event-opts="{{[['touchstart',[['touchstartEvent',['$event']]]],['touchmove',[['touchmoveEvent',['$event']]]],['touchend',[['touchendEvent',['$event']]]],['touchcancel',[['touchendEvent',['$event']]]]]}}" class="mescroll-body" style="{{'min-height:'+(minHeight)+';'+('padding-top:'+(padTop)+';')+('padding-bottom:'+(padBottom)+';')+('padding-bottom:'+(padBottomConstant)+';')+('padding-bottom:'+(padBottomEnv)+';')}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e" bindtouchcancel="__e"><view class="mescroll-body-content mescroll-touch" style="{{'transform:'+(translateY)+';'+('transition:'+(transition)+';')}}"><block wx:if="{{mescroll.optDown.use}}"><view class="mescroll-downwarp"><view class="downwarp-content"><view class="{{['downwarp-progress',(isDownLoading)?'mescroll-rotate':'']}}" style="{{'transform:'+(downRotate)+';'}}"></view><view class="downwarp-tip">{{downText}}</view></view></view></block><slot></slot><block wx:if="{{mescroll.optUp.use&&!isDownLoading}}"><view class="mescroll-upwarp"><view hidden="{{!(upLoadType===1)}}"><view class="upwarp-progress mescroll-rotate"></view><view class="upwarp-tip">{{mescroll.optUp.textLoading}}</view></view><block wx:if="{{upLoadType===2}}"><view class="upwarp-nodata">{{mescroll.optUp.textNoMore}}</view></block></view></block></view><block wx:if="{{showTop}}"><mescroll-top vue-id="f814a0ea-1" option="{{mescroll.optUp.toTop}}" value="{{isShowToTop}}" data-event-opts="{{[['^click',[['toTopClick']]],['^input',[['__set_model',['','isShowToTop','$event',[]]]]]]}}" bind:click="__e" bind:input="__e" bind:__l="__l"></mescroll-top></block></view>