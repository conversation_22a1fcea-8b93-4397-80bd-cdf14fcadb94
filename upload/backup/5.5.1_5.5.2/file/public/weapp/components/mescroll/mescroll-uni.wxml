<view class="mescroll-uni-warp"><scroll-view class="{{['mescroll-uni',(isFixed)?'mescroll-uni-fixed':'']}}" style="{{'height:'+(scrollHeight)+';'+('padding-top:'+(padTop)+';')+('padding-bottom:'+(padBottom)+';')+('padding-bottom:'+(padBottomConstant)+';')+('padding-bottom:'+(padBottomEnv)+';')+('top:'+(fixedTop)+';')+('bottom:'+(fixedBottom)+';')+('bottom:'+(fixedBottomConstant)+';')+('bottom:'+(fixedBottomEnv)+';')+('background:'+(background)+';')+('padding-left:'+(paddingBoth)+';')+('padding-right:'+(paddingBoth)+';')}}" id="{{viewId}}" scroll-top="{{scrollTop}}" scroll-with-animation="{{scrollAnim}}" scroll-y="{{isDownReset}}" enable-back-to-top="{{true}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]],['touchstart',[['touchstartEvent',['$event']]]],['touchmove',[['touchmoveEvent',['$event']]]],['touchend',[['touchendEvent',['$event']]]],['touchcancel',[['touchendEvent',['$event']]]]]}}" bindscroll="__e" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e" bindtouchcancel="__e"><view class="mescroll-uni-content" style="{{'transform:'+(translateY)+';'+('transition:'+(transition)+';')}}"><block wx:if="{{mescroll.optDown.use}}"><view class="mescroll-downwarp"><view class="downwarp-content"><view class="downwarp-tip">{{downText}}</view></view></view></block><slot></slot><block wx:if="{{mescroll.optUp.use&&!isDownLoading}}"><view class="mescroll-upwarp"><view hidden="{{!(upLoadType===1)}}"><ns-loading vue-id="371dd7c2-1" bind:__l="__l"></ns-loading></view><block wx:if="{{upLoadType===2}}"><view class="upwarp-nodata">{{mescroll.optUp.textNoMore}}</view></block></view></block></view></scroll-view><block wx:if="{{showTop}}"><mescroll-top vue-id="371dd7c2-2" option="{{mescroll.optUp.toTop}}" value="{{isShowToTop}}" data-event-opts="{{[['^click',[['toTopClick']]],['^input',[['__set_model',['','isShowToTop','$event',[]]]]]]}}" bind:click="__e" bind:input="__e" bind:__l="__l"></mescroll-top></block></view>