<block wx:if="{{isInit}}"><mescroll vue-id="06190fb4-1" top="{{top}}" down="{{downOption}}" fixed="{{fixed}}" topbar="{{topbar}}" background="{{background}}" paddingBoth="{{paddingBoth}}" up="{{upOption}}" data-event-opts="{{[['^down',[['downCallback']]],['^up',[['upCallback']]],['^emptyclick',[['emptyClick']]],['^init',[['mescrollInit']]]]}}" bind:down="__e" bind:up="__e" bind:emptyclick="__e" bind:init="__e" bind:__l="__l" vue-slots="{{['default']}}"><slot name="list"></slot></mescroll></block>