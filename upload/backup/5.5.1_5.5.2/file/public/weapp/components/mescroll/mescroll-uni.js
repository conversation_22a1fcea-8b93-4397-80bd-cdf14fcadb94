(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/mescroll/mescroll-uni","components/ns-loading/ns-loading"],{"0cea":function(t,n,o){"use strict";var e=o("320b"),i=o.n(e);i.a},"10e0":function(t,n,o){"use strict";o.r(n);var e=o("1d1d"),i=o("4d29");for(var s in i)["default"].indexOf(s)<0&&function(t){o.d(n,t,(function(){return i[t]}))}(s);o("9b5d");var r=o("828b"),c=Object(r["a"])(i["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=c.exports},"13dc":function(t,n,o){"use strict";o.r(n);var e=o("f9a1"),i=o.n(e);for(var s in e)["default"].indexOf(s)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(s);n["default"]=i.a},"1d1d":function(t,n,o){"use strict";o.d(n,"b",(function(){return e})),o.d(n,"c",(function(){return i})),o.d(n,"a",(function(){}));var e=function(){var t=this.$createElement;this._self._c},i=[]},"1f9d":function(t,n,o){"use strict";o.r(n);var e=o("c6d1"),i=o("13dc");for(var s in i)["default"].indexOf(s)<0&&function(t){o.d(n,t,(function(){return i[t]}))}(s);o("0cea");var r=o("828b"),c=Object(r["a"])(i["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=c.exports},"320b":function(t,n,o){},4514:function(t,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={name:"ns-loading",props:{downText:{type:String,default:"加载中"},isRotate:{type:Boolean,default:!1}},data:function(){return{isShow:!0}},methods:{show:function(){this.isShow=!0},hide:function(){this.isShow=!1}}};n.default=e},"4d29":function(t,n,o){"use strict";o.r(n);var e=o("4514"),i=o.n(e);for(var s in e)["default"].indexOf(s)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(s);n["default"]=i.a},"9b5d":function(t,n,o){"use strict";var e=o("f7dc"),i=o.n(e);i.a},c6d1:function(t,n,o){"use strict";o.d(n,"b",(function(){return i})),o.d(n,"c",(function(){return s})),o.d(n,"a",(function(){return e}));var e={nsLoading:function(){return Promise.resolve().then(o.bind(null,"10e0"))}},i=function(){var t=this.$createElement;this._self._c},s=[]},f7dc:function(t,n,o){},f9a1:function(t,n,o){"use strict";(function(t){var e=o("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=e(o("0cd5")),s=e(o("33ea")),r=(e(o("10e0")),{name:"mescroll-uni",components:{MescrollEmpty:function(){o.e("components/mescroll/components/mescroll-empty").then(function(){return resolve(o("3915"))}.bind(null,o)).catch(o.oe)},MescrollTop:function(){o.e("components/mescroll/components/mescroll-top").then(function(){return resolve(o("7e9f"))}.bind(null,o)).catch(o.oe)}},props:{down:Object,up:Object,top:[String,Number],topbar:Boolean,bottom:[String,Number],safearea:Boolean,fixed:{type:Boolean,default:function(){return!0}},height:[String,Number],showTop:{type:Boolean,default:function(){return!0}},background:String,paddingBoth:{type:[String,Number],default:function(){return 0}}},data:function(){return{mescroll:{optDown:{},optUp:{}},viewId:"id_"+Math.random().toString(36).substr(2),downHight:0,downRate:0,downLoadType:4,upLoadType:0,isShowEmpty:!1,isShowToTop:!1,scrollTop:0,scrollAnim:!1,windowTop:0,windowBottom:0,windowHeight:0,statusBarHeight:0}},computed:{isFixed:function(){return!this.height&&this.fixed},scrollHeight:function(){return this.isFixed?"auto":this.height?this.toPx(this.height)+"px":"100%"},numTop:function(){return this.toPx(this.top)+(this.topbar?44+this.statusBarHeight:0)},fixedTop:function(){return this.isFixed?this.numTop+this.windowTop+"px":0},padTop:function(){return this.isFixed?0:this.numTop+"px"},numBottom:function(){return this.toPx(this.bottom)},fixedBottom:function(){return this.isFixed?this.numBottom+this.windowBottom+"px":0},fixedBottomConstant:function(){return this.safearea?"calc("+this.fixedBottom+" + constant(safe-area-inset-bottom))":this.fixedBottom},fixedBottomEnv:function(){return this.safearea?"calc("+this.fixedBottom+" + env(safe-area-inset-bottom))":this.fixedBottom},padBottom:function(){return this.isFixed?0:this.numBottom+"px"},padBottomConstant:function(){return this.safearea?"calc("+this.padBottom+" + constant(safe-area-inset-bottom))":this.padBottom},padBottomEnv:function(){return this.safearea?"calc("+this.padBottom+" + env(safe-area-inset-bottom))":this.padBottom},isDownReset:function(){return 3===this.downLoadType||4===this.downLoadType},transition:function(){return this.isDownReset?"transform 300ms":""},translateY:function(){return this.downHight>0?"translateY("+this.downHight+"px)":""},isDownLoading:function(){return 3===this.downLoadType},downRotate:function(){return"rotate("+360*this.downRate+"deg)"},downText:function(){switch(this.downLoadType){case 1:return this.mescroll.optDown.textInOffset;case 2:return this.mescroll.optDown.textOutOffset;case 3:return this.mescroll.optDown.textLoading;case 4:return this.mescroll.optDown.textLoading;default:return this.mescroll.optDown.textInOffset}}},methods:{toPx:function(n){if("string"===typeof n)if(-1!==n.indexOf("px"))if(-1!==n.indexOf("rpx"))n=n.replace("rpx","");else{if(-1===n.indexOf("upx"))return Number(n.replace("px",""));n=n.replace("upx","")}else if(-1!==n.indexOf("%")){var o=Number(n.replace("%",""))/100;return this.windowHeight*o}return n?t.upx2px(Number(n)):0},scroll:function(t){var n=this;this.mescroll.scroll(t.detail,(function(){n.$emit("scroll",n.mescroll)}))},touchstartEvent:function(t){this.mescroll.touchstartEvent(t)},touchmoveEvent:function(t){this.mescroll.touchmoveEvent(t)},touchendEvent:function(t){this.mescroll.touchendEvent(t)},emptyClick:function(){this.$emit("emptyclick",this.mescroll)},toTopClick:function(){this.mescroll.scrollTo(0,this.mescroll.optUp.toTop.duration),this.$emit("topclick",this.mescroll)},setClientHeight:function(){var n=this;0!==this.mescroll.getClientHeight(!0)||this.isExec||(this.isExec=!0,this.$nextTick((function(){try{var o=t.createSelectorQuery().in(n).select("#"+n.viewId);o.boundingClientRect((function(t){n.isExec=!1,t?n.mescroll.setClientHeight(t.height):3!=n.clientNum&&(n.clientNum=null==n.clientNum?1:n.clientNum+1,setTimeout((function(){n.setClientHeight()}),100*n.clientNum))})).exec()}catch(e){}})))}},created:function(){var n=this,o={down:{inOffset:function(t){n.downLoadType=1},outOffset:function(t){n.downLoadType=2},onMoving:function(t,o,e){n.downHight=e,n.downRate=o},showLoading:function(t,o){n.downLoadType=3,n.downHight=o},endDownScroll:function(t){n.downLoadType=4,n.downHight=0},callback:function(t){n.$emit("down",t)}},up:{showLoading:function(){n.upLoadType=1},showNoMore:function(){n.upLoadType=2},hideUpScroll:function(){n.upLoadType=0},empty:{onShow:function(t){n.isShowEmpty=t}},toTop:{onShow:function(t){n.isShowToTop=t}},callback:function(t){n.$emit("up",t),n.setClientHeight()}}};i.default.extend(o,s.default);var e=JSON.parse(JSON.stringify({down:n.down,up:n.up}));i.default.extend(e,o),n.mescroll=new i.default(e),n.mescroll.viewId=n.viewId,n.$emit("init",n.mescroll);var r=t.getSystemInfoSync();r.windowTop&&(n.windowTop=r.windowTop),r.windowBottom&&(n.windowBottom=r.windowBottom),r.windowHeight&&(n.windowHeight=r.windowHeight),r.statusBarHeight&&(n.statusBarHeight=r.statusBarHeight),n.mescroll.setBodyHeight(r.windowHeight),n.mescroll.resetScrollTo((function(t,o){var e=n.mescroll.getScrollTop();n.scrollAnim=0!==o,0===o||300===o?(n.scrollTop=e,n.$nextTick((function(){n.scrollTop=t}))):n.mescroll.getStep(e,t,(function(t){n.scrollTop=t}),o)})),n.up&&n.up.toTop&&null!=n.up.toTop.safearea||(n.mescroll.optUp.toTop.safearea=n.safearea)},mounted:function(){this.setClientHeight()}});n.default=r}).call(this,o("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/mescroll/mescroll-uni-create-component',
    {
        'components/mescroll/mescroll-uni-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("1f9d"))
        })
    },
    [['components/mescroll/mescroll-uni-create-component']]
]);
