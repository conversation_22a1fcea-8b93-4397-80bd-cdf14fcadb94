(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-payment/ns-payment"],{"2a87":function(e,t,n){"use strict";n.r(t);var o=n("e290"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=a.a},"7aec":function(e,t,n){"use strict";n.r(t);var o=n("c7f4"),a=n("2a87");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("c5f8");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=s.exports},c5f8:function(e,t,n){"use strict";var o=n("d5cc"),a=n.n(o);a.a},c7f4:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o}));var o={uniPopup:function(){return n.e("components/uni-popup/uni-popup").then(n.bind(null,"d745"))},nsSwitch:function(){return n.e("components/ns-switch/ns-switch").then(n.bind(null,"b0ec"))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e._f("moneyFormat")(e.payMoney)),o=e.payMoney>0?e.payTypeList.length:null;e._isMounted||(e.e0=function(t,n){var o=arguments[arguments.length-1].currentTarget.dataset,a=o.eventParams||o["event-params"];n=a.index;e.payIndex=n}),e.$mp.data=Object.assign({},{$root:{f0:n,g0:o}})},i=[]},d5cc:function(e,t,n){},e290:function(e,t,n){"use strict";(function(e,o){var a=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("7ca3"));function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var c={name:"ns-payment",components:{uniPopup:function(){n.e("components/uni-popup/uni-popup").then(function(){return resolve(n("d745"))}.bind(null,n)).catch(n.oe)},nsSwitch:function(){n.e("components/ns-switch/ns-switch").then(function(){return resolve(n("b0ec"))}.bind(null,n)).catch(n.oe)}},props:{balanceDeduct:{type:[Number,String],default:""},isBalance:{type:Number,default:0},payMoney:{type:[Number,String],default:0}},data:function(){return{isIphoneX:!1,payIndex:0,payTypeList:[{name:"微信支付",provider:"wxpay",icon:"icon-weixin1",type:"wechatpay"}],isMatched:0,payInfo:{},balanceConfig:0,sale:!0}},created:function(t){var n=getCurrentPages()[getCurrentPages().length-1].route,o=n.match("presale/order_list/order_list"),a=n.match("presale/order_detail/order_detail");(o||a)&&(this.sale=!1),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getPayType(),this.getBalanceConfig(),""==e.getStorageSync("paySource")&&this.payIsMatched()},methods:{payIsMatched:function(){var t=this;if(e.getStorageSync("is_test"))this.isMatched=1;else{var n=o.getLaunchOptionsSync();this.$api.sendRequest({url:"/shopcomponent/api/weapp/scenecheck",data:{scene:n.scene},success:function(e){0==e.code&&(t.isMatched=e.data)}})}},open:function(){this.$refs.choosePaymentPopup.open()},close:function(){this.$refs.choosePaymentPopup.close()},useBalance:function(){this.$emit("useBalance")},confirm:function(){0==this.payTypeList.length&&this.payMoney>0?this.$util.showToast({title:"请选择支付方式！"}):(e.showLoading({title:"支付中...",mask:!0}),this.$refs.choosePaymentPopup.close(),this.$emit("confirm"),e.setStorageSync("pay_flag",1))},getPayInfo:function(e){var t=this;this.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:e},success:function(e){e.code>=0&&e.data?(t.payInfo=e.data,2==t.payInfo["pay_status"]?t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo"):t.pay()):(t.$util.showToast({title:"未获取到支付信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))}})},getBalanceConfig:function(){var e=this;this.$api.sendRequest({url:"/api/pay/getBalanceConfig",data:{},success:function(t){e.balanceConfig=t.data.balance_show}})},getPayType:function(){var e=this;this.$api.sendRequest({url:"/api/pay/type",success:function(t){0==t.code&&(""==t.data.pay_type?e.payTypeList=[]:e.payTypeList.forEach((function(n,o){-1==t.data.pay_type.indexOf(n.type)&&e.payTypeList.splice(o,1)})))}})},pay:function(){var t=this,n=this.payTypeList[this.payIndex];n&&this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:this.payInfo.out_trade_no,pay_type:n.type,is_matched:this.isMatched,scene:e.getStorageSync("is_test")?1175:o.getLaunchOptionsSync().scene},success:function(a){if(e.hideLoading(),a.code>=0){var i=a.data.data,r=e.getStorageSync("is_test")?1175:o.getLaunchOptionsSync().scene;if(-1!=[1175,1176,1177,1191,1195].indexOf(r))return void e.requestOrderPayment({timeStamp:i.timeStamp,nonceStr:i.nonceStr,package:i.package,signType:i.signType,paySign:i.paySign,success:function(n){e.removeStorage({key:"is_test"}),"BlindboxGoodsOrderPayNotify"==t.payInfo.event?t.$util.redirectTo("/pages_promotion/blindbox/index",{outTradeNo:t.payInfo.out_trade_no},"","redirectTo"):t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo")},fail:function(n){t.flag=!1,"requestOrderPayment:fail cancel"==n.errMsg?t.$util.showToast({title:"您已取消支付"}):e.showModal({content:"支付失败,失败原因: "+n.errMsg,showCancel:!1}),setTimeout((function(){e.removeStorage({key:"is_test"}),"BlindboxGoodsOrderPayNotify"==t.payInfo.event?t.$util.redirectTo("/pages_promotion/blindbox/index",{outTradeNo:t.payInfo.out_trade_no},"","redirectTo"):t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo")}),2e3)}});e.requestPayment(s(s({provider:n.provider},i),{},{success:function(e){"BlindboxGoodsOrderPayNotify"==t.payInfo.event?t.$util.redirectTo("/pages_promotion/blindbox/index",{outTradeNo:t.payInfo.out_trade_no},"","redirectTo"):t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo")},fail:function(n){t.flag=!1,"requestPayment:fail cancel"==n.errMsg?t.$util.showToast({title:"您已取消支付"}):e.showModal({content:"支付失败,失败原因: "+n.errMsg,showCancel:!1}),setTimeout((function(){"BlindboxGoodsOrderPayNotify"==t.payInfo.event?t.$util.redirectTo("/pages_promotion/blindbox/index",{outTradeNo:t.payInfo.out_trade_no},"","redirectTo"):t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo")}),2e3)}}))}else t.$util.showToast({title:a.message}),setTimeout((function(){t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"redirectTo")}),2e3)},fail:function(n){e.hideLoading(),t.$util.showToast({title:"request:fail"})}})}}};t.default=c}).call(this,n("df3c")["default"],n("3223")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-payment/ns-payment-create-component',
    {
        'components/ns-payment/ns-payment-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7aec"))
        })
    },
    [['components/ns-payment/ns-payment-create-component']]
]);
