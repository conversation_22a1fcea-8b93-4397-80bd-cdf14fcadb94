<view><uni-popup class="vue-ref" vue-id="d2ef4efc-1" type="center" mask-click="{{false}}" data-ref="choosePaymentPopup" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="choose-payment-popup popup" catchtouchmove="__e"><view class="popup-header"><text class="tit">支付方式</text><text data-event-opts="{{[['tap',[['close']]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><scroll-view class="{{['popup-body',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view class="pay-money"><text class="money">{{"支付金额"+$root.f0+"元"}}</text></view><block wx:if="{{balanceDeduct>0&&balanceConfig==1&&sale}}"><view class="payment-item"><view class="iconfont icon-yue"></view><view class="info-wrap"><text class="name">余额抵扣</text><view class="money">{{"可用¥"+balanceDeduct}}</view></view><ns-switch class="balance-switch" vue-id="{{('d2ef4efc-2')+','+('d2ef4efc-1')}}" checked="{{isBalance==1}}" data-event-opts="{{[['^change',[['useBalance']]]]}}" bind:change="__e" bind:__l="__l"></ns-switch></view></block><block wx:if="{{payMoney>0}}"><block><block wx:if="{{$root.g0}}"><block><block wx:for="{{payTypeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" class="payment-item" bindtap="__e"><view class="{{['iconfont',item.icon]}}"></view><text class="name">{{item.name}}</text><text class="{{['iconfont',payIndex==index?'icon-yuan_checked color-base-text':'icon-checkboxblank']}}"></text></view></block></block></block><block wx:else><block><view class="empty">平台尚未配置支付方式！</view></block></block></block></block></scroll-view><view class="{{['popup-footer',(isIphoneX)?'bottom-safe-area':'']}}"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="confirm-btn color-base-bg" bindtap="__e">确认支付</view></view></view></uni-popup></view>