<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}"><x-skeleton vue-id="1949b456-1" type="{{skeletonType}}" loading="{{loading}}" configs="{{skeletonConfig}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{$root.g1}}"><view class="{{['diy-seckill',value.template,value.style]}}" style="{{(wrapStyle)}}"><block wx:if="{{$root.g2}}"><view class="{{['title-wrap',value.titleStyle.style]}}" style="{{'background-image:'+('url('+$root.g3+'), linear-gradient(to right,'+value.titleStyle.bgColorStart+','+value.titleStyle.bgColorEnd+')')+';'}}"><block wx:if="{{value.titleStyle.leftStyle=='text'}}"><view style="{{'font-size:'+(value.titleStyle.fontSize*2+'rpx')+';'+('color:'+(value.titleStyle.textColor)+';')+('font-weight:'+(value.titleStyle.fontWeight?'bold':'')+';')}}">{{''+value.titleStyle.leftText+''}}</view></block><block wx:else><image class="title-img" src="{{$root.g4}}" mode="heightFix"></image></block><block wx:if="{{value.titleStyle.style=='style-2'&&isFuture}}"><text class="seckill-style-name" style="{{'color:'+(value.titleStyle.textColor)+';'}}">距离开始</text></block><view class="time" style="{{'background:'+(value.titleStyle.timeBgColor)+';'}}"><text class="seckill-title-name text" style="{{'color:'+(value.titleStyle.textColor)+';'}}">{{isFuture?'距离开始':'距离结束'}}</text><block wx:if="{{value.titleStyle.timeImageUrl}}"><text class="{{['seckill-title-name icon',value.titleStyle.timeImageUrl]}}"></text></block><text class="hour number" style="{{(titleTimeStyle)}}">{{seckillH?seckillH:'00'}}</text><text class="symbol" style="{{'color:'+(value.titleStyle.colonColor)+';'}}">:</text><text class="minute number" style="{{(titleTimeStyle)}}">{{seckillI?seckillI:'00'}}</text><text class="symbol" style="{{'color:'+(value.titleStyle.colonColor)+';'}}">:</text><text class="second number" style="{{(titleTimeStyle)}}">{{seckillS?seckillS:'00'}}</text></view><block wx:if="{{value.titleStyle.moreSupport}}"><view data-event-opts="{{[['tap',[['toMore',['$event']]]]]}}" class="marketimg-box-title-right" style="{{'font-size:'+(value.titleStyle.moreFontSize*2+'rpx')+';'+('color:'+(value.titleStyle.moreColor)+';')}}" bindtap="__e"><text>{{value.titleStyle.more}}</text><text class="iconfont icon-right" style="{{'font-size:'+(value.titleStyle.moreFontSize*2+'rpx')+';'}}"></text></view></block></view></block><view class="content-wrap"><block wx:if="{{value.template=='row1-of1'}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index,'id']]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g5}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view><view class="content"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.progressStyle.control}}"><view class="progress"><view class="bg" style="{{'background-color:'+(value.theme=='diy'?value.progressStyle.bgColor:'')+';'}}"><view class="curr" style="{{'background-color:'+(value.theme=='diy'?value.progressStyle.currColor:'')+';'+('width:'+((item.$orig.goods_stock+item.$orig.sale_num?item.g6:0)+'rpx')+';')}}"><block wx:if="{{value.style=='style-2'}}"><image class="progress-bar" src="{{item.g7}}" mode="widthFix"></image></block></view></view><view class="num" style="{{'color:'+(value.theme=='diy'?value.saleStyle.color:'')+';'+('border-color:'+(value.theme=='diy'?value.saleStyle.color:'')+';')}}">{{'已抢'+(item.$orig.goods_stock+item.$orig.sale_num?item.g8:'0.00')+'%'}}</view></view></block><view class="bottom-wrap"><view class="price-wrap"><view class="discount-price"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">{{''+item.g9[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">{{''+('.'+item.g10[1])+''}}</text></view><block wx:if="{{value.priceStyle.lineControl&&value.style!='style-2'}}"><view class="original-price price-font" style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">{{'¥'+item.$orig.price+''}}</view></block><block wx:else><view class="price-font"><text style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">原价：</text><text class="original-price" style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">{{"¥"+item.$orig.price}}</text></view></block></view><block wx:if="{{value.btnStyle.control&&isLoad}}"><button style="{{'background:'+(value.btnStyle.theme=='diy'?'linear-gradient(to right,'+value.btnStyle.bgColorStart+','+value.btnStyle.bgColorEnd+')':'')+';'+('color:'+(value.btnStyle.theme=='diy'?value.btnStyle.textColor:'')+';')+('border-radius:'+(value.btnStyle.aroundRadius*2+'rpx')+';')}}">{{''+(isFuture?'即将开始':value.btnStyle.text)+''}}</button></block></view></view></view></block></block><block wx:if="{{value.template=='row1-of2'}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index,'id']]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g11}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl}}"><view class="content"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.priceStyle.mainControl}}"><view class="discount-price"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">{{''+item.g12[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">{{''+('.'+item.g13[1])+''}}</text></view></block><view class="bottom-wrap"><block wx:if="{{value.priceStyle.lineControl}}"><view class="original-price price-font" style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">{{'¥'+item.$orig.price+''}}</view></block><block wx:if="{{value.btnStyle.control&&isLoad}}"><button style="{{'background:'+(value.btnStyle.theme=='diy'?'linear-gradient(to right,'+value.btnStyle.bgColorStart+','+value.btnStyle.bgColorEnd+')':'')+';'+('color:'+(value.btnStyle.theme=='diy'?value.btnStyle.textColor:'')+';')+('border-radius:'+(value.btnStyle.aroundRadius*2+'rpx')+';')}}">{{''+(isFuture?'即将开始':value.btnStyle.text)+''}}</button></block></view></view></block></view></block></block><block wx:if="{{value.template=='horizontal-slide'}}"><block wx:if="{{value.slideMode=='scroll'}}"><scroll-view class="scroll" scroll-x="{{true}}" show-scrollbar="{{false}}"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index,'id']]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g14}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl}}"><view class="content"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.priceStyle.mainControl}}"><view class="discount-price"><block wx:if="{{value.style=='style-2'}}"><image class="tag" src="{{item.g15}}" mode="widthFix"></image></block><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">{{''+item.g16[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">{{''+('.'+item.g17[1])+''}}</text></view></block><block wx:if="{{value.priceStyle.lineControl}}"><view class="original-price price-font" style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">{{'¥'+item.$orig.price+''}}</view></block></view></block></view></block></scroll-view></block><block wx:if="{{value.slideMode=='slide'}}"><swiper class="swiper" style="{{'height:'+(swiperHeight)+';'}}" autoplay="{{false}}"><block wx:for="{{$root.l4}}" wx:for-item="pageItem" wx:for-index="pageIndex" wx:key="pageIndex"><swiper-item class="{{['swiper-item',pageItem.g18]}}"><block wx:for="{{pageItem.l3}}" wx:for-item="item" wx:for-index="dataIndex" wx:key="dataIndex"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList.'+pageIndex+'','',dataIndex,'id']]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g19}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[dataIndex,pageIndex]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl}}"><view class="content"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.priceStyle.mainControl}}"><view class="discount-price"><block wx:if="{{value.style=='style-2'}}"><image class="tag" src="{{pageItem.g20}}" mode="widthFix"></image></block><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">{{''+item.g21[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">{{''+('.'+item.g22[1])+''}}</text></view></block><block wx:if="{{value.priceStyle.lineControl}}"><view class="original-price price-font" style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">{{'¥'+item.$orig.price+''}}</view></block></view></block></view></block></swiper-item></block></swiper></block></block></view></view></block></x-skeleton></view></block>