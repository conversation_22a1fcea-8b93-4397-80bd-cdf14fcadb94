(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-category"],{"20a9":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return o}));var o={uniPopup:function(){return i.e("components/uni-popup/uni-popup").then(i.bind(null,"d745"))},diyCategoryItem:function(){return i.e("components/diy-components/diy-category-item").then(i.bind(null,"81bb"))}},n=function(){var e=this,t=e.$createElement,i=(e._self._c,4==e.value.template?e.__map(e.templateFourData,(function(t,i){var o=e.__get_orig(t),n=e.$util.img(t.image);return{$orig:o,g0:n}})):null),o=4==e.value.template?e.$util.img("/public/uniapp/category/unfold.png"):null,n=4==e.value.template?e.__map(e.templateFourData,(function(t,i){var o=e.__get_orig(t),n=e.$util.img(t.image);return{$orig:o,g2:n}})):null,r=e.categoryTree?e.categoryTree.length:null,a=e.categoryTree&&r&&(2==e.value.template||3==e.value.template||4==e.value.template)&&"part"==e.loadType?e.__map(e.categoryTree,(function(t,i){var o=e.__get_orig(t),n=e.categoryTree.length;return{$orig:o,g4:n}})):null,s=e.categoryTree&&!r?e.$util.img("public/uniapp/category/empty.png"):null,c=e.categoryTree?null:e.$util.img("public/uniapp/category/empty.png"),u=(2==e.value.template||4==e.value.template)&&e.value.quickBuy&&e.storeToken&&e.categoryTree&&e.categoryTree.length,l=(2==e.value.template||4==e.value.template)&&e.value.quickBuy&&e.storeToken&&e.categoryTree&&e.categoryTree.length;e._isMounted||(e.e0=function(t){return e.$util.redirectTo("/pages_tool/goods/search")},e.e1=function(t){return e.$util.redirectTo("/pages_tool/goods/search")},e.e2=function(t){return e.$refs.templateFourPopup.open()},e.e3=function(t){return e.$refs.templateFourPopup.close()},e.e4=function(t){return e.$util.redirectTo("/pages/goods/cart")}),e.$mp.data=Object.assign({},{$root:{l0:i,g1:o,l1:n,g3:r,l2:a,g5:s,g6:c,g7:u,g8:l}})},r=[]},"42a9":function(e,t,i){"use strict";i.r(t);var o=i("f7c7"),n=i.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);t["default"]=n.a},4425:function(e,t,i){"use strict";var o=i("9fde"),n=i.n(o);n.a},"4fbd":function(e,t,i){"use strict";i.r(t);var o=i("20a9"),n=i("42a9");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);i("4425");var a=i("828b"),s=Object(a["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=s.exports},"9fde":function(e,t,i){},f7c7:function(e,t,i){"use strict";(function(e){var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,r=o(i("3b2d"));e.getSystemInfoSync();n=e.getMenuButtonBoundingClientRect();var a,s,c,u={components:{nsGoodsSkuCategory:function(){i.e("components/ns-goods-sku/ns-goods-sku-category").then(function(){return resolve(i("5f0e"))}.bind(null,i)).catch(i.oe)}},name:"diy-category",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{oneCategorySelect:0,select:0,categoryId:"category-0",categoryTree:null,scrollLock:!1,triggered:!0,heightArea:[],isSub:!1,carIconList:{},endTips:0,cartAnimation:{},loadType:"",templateFourData:[],isIphoneX:!1}},created:function(){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getCategoryTree(),this.loadType=1==this.value.goodsLevel&&"all"==this.value.loadType?"all":"part"},mounted:function(){var t=this;s=e.createSelectorQuery().in(this),s.select(".content-wrap").boundingClientRect((function(e){e&&(a=e.height)})).exec(),setTimeout((function(){s.select(".end-tips").boundingClientRect((function(e){e&&e.top>a&&(t.endTips=1)})).exec(),s.select(".cart-icon").boundingClientRect((function(e){e&&(c=e)})).exec(),1==t.value.template&&t.getHeightArea(-1)}),500)},watch:{componentRefresh:function(e){}},computed:{cartTotalMoney:function(){var e=parseFloat(this.cartMoney).toFixed(2);return e.split(".")},navbarHeight:function(){var e=n.top;return e},navbarInnerStyle:function(){var e="";if(4==this.value.template&&this.value.search){e+="height:"+n.height+"px;";var t=n.width?2*n.width+"rpx":"70rpx";e+="padding-right:calc("+t+" + 30rpx);",e+="padding-top:"+this.navbarHeight+"px;"}return(4!=this.value.template||4==this.value.template&&!this.value.search)&&(e+="height:"+2*n.height+"rpx;",e+="padding-top:"+this.navbarHeight+"px;",e+="text-align: center;",e+="line-height:"+2*n.height+"rpx;",e+="font-size: 16px;",e+="padding-bottom: 10rpx;"),e},wxSearchHeight:function(){return"height: 64rpx;","height: 64rpx;"},uniPopupTop:function(){var e;return e=this.navbarHeight+n.height+"px",e}},methods:{pageShow:function(){this.$store.dispatch("getCartNumber"),this.heightArea.length||this.getHeightArea(-1)},getHeightArea:function(e){var t=[];s.selectAll(".content-wrap .child-category").boundingClientRect((function(e){e&&e.length&&e.forEach((function(e,i){0==i?t.push([0,e.height]):t.push([t[i-1][1],t[i-1][1]+e.height])}))})).exec(),this.heightArea=t,-1!=e&&e<this.categoryTree.length-1&&this.$refs.categoryItem[e+1].getGoodsList(),this.refreshData()},getCategoryTree:function(){var e=this;this.$api.sendRequest({url:"/api/goodscategory/tree",data:{level:3},success:function(t){0==t.code&&(e.categoryTree=t.data,4==e.value.template&&(e.templateFourData=JSON.parse(JSON.stringify(e.categoryTree)),e.categoryTree=e.templateFourData[0].child_list))}})},switchOneCategory:function(e){e>=this.categoryTree.length||(this.select=e,this.categoryId="category-"+e,this.scrollLock=!0)},touchStart:function(){this.scrollLock=!1},listenScroll:function(e){if(!this.scrollLock){var t=e.detail.scrollTop;if(this.heightArea.length){for(var i=0;i<this.heightArea.length;i++)if(t>=this.heightArea[i][0]&&t<=this.heightArea[i][1]){this.select=i;break}1!=this.value.template&&"all"==this.value.loadType&&this.heightArea[this.select][1]-t-a<300&&this.$refs.categoryItem[this.select].getGoodsList()}}},onRefresh:function(){this.triggered=!1},onRestore:function(){this.triggered="restore"},toLogin:function(){this.$emit("tologin")},selectSku:function(e,t){var i=this;this.$api.sendRequest({url:"/api/goodssku/getInfoForCategory",data:{sku_id:e.sku_id},success:function(e){if(e.code>=0){var t=e.data;t.unit=t.unit||"件",t.sku_images?t.sku_images=t.sku_images.split(","):t.sku_images=[],t.goods_spec_format&&t.goods_image&&(t.goods_image=t.goods_image.split(","),t.sku_images=t.goods_image.concat(t.sku_images)),t.sku_spec_format&&(t.sku_spec_format=JSON.parse(t.sku_spec_format)),t.goods_spec_format&&(t.goods_spec_format=JSON.parse(t.goods_spec_format)),1==t.promotion_type&&(t.discountTimeMachine=i.$util.countDown(t.end_time-e.timestamp)),1==t.promotion_type&&t.discountTimeMachine?t.member_price>0&&Number(t.member_price)<=Number(t.discount_price)?t.show_price=t.member_price:t.show_price=t.discount_price:t.member_price>0?t.show_price=t.member_price:t.show_price=t.price,i.$refs.skuSelect.show(t)}}})},settlement:function(){var t=this,i=!1;for(var o in this.cartList){var n=this.cartList[o];for(var a in n){if(n.max_buy&&n.num>n.max_buy){i=!0,this.$util.showToast({title:"商品"+n.goods_name+"商品最多可购买"+n.max_buy+"件"});break}if("object"==(0,r.default)(n[a])){if(n[a].num>n[a].stock){i=!0,this.$util.showToast({title:"商品"+n.goods_name+"库存不足"});break}if(n[a].min_buy&&n[a].num<n[a].min_buy){i=!0,this.$util.showToast({title:"商品"+n.goods_name+"商品最少要购买"+n[a].min_buy+"件"});break}}}}i||this.cartIds.length&&!this.isSub&&(this.isSub=!0,e.removeStorageSync("delivery"),e.setStorage({key:"orderCreateData",data:{cart_ids:this.cartIds.toString()},success:function(){t.$util.redirectTo("/pages/order/payment"),t.isSub=!1}}))},addCartPoint:function(e,t){if(2==this.value.template||this.value.quickBuy){var i=(new Date).getTime();this.$set(this.carIconList,i,{left:e,top:t,index:0,bezierPos:this.$util.bezier([{x:e,y:t},{x:e-200,y:e-120},{x:c.left+10,y:c.top}],6).bezier_points,timer:null}),this.startAnimation(i)}},startAnimation:function(t){var i=this,o=this.carIconList[t].bezierPos,n=this.carIconList[t].index;this.carIconList[t].timer=setInterval((function(){if(n<6)i.carIconList[t].left=o[n].x,i.carIconList[t].top=o[n].y,n++;else{clearInterval(i.carIconList[t].timer),delete i.carIconList[t],i.$forceUpdate(),setTimeout((function(){i.$store.commit("setCartChange")}),100);var r=e.createAnimation({duration:200,timingFunction:"ease"});r.scale(1.2).step(),i.cartAnimation=r.export(),setTimeout((function(){r.scale(1).step(),i.cartAnimation=r.export()}),300)}}),50)},templateFourOneFn:function(e){this.categoryTree=this.templateFourData[e].child_list||[],this.oneCategorySelect=e,this.select=0},refreshData:function(){this.$refs.categoryItem[this.select].loadGoodsCartNum(!0)}}};t.default=u}).call(this,i("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-category-create-component',
    {
        'components/diy-components/diy-category-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("4fbd"))
        })
    },
    [['components/diy-components/diy-category-create-component']]
]);
