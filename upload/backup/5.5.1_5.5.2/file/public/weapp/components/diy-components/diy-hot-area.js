(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-hot-area"],{1824:function(t,e,n){"use strict";n.r(e);var r=n("b2b7"),o=n.n(r);for(var u in r)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(u);e["default"]=o.a},2401:function(t,e,n){},"81cc":function(t,e,n){"use strict";var r=n("2401"),o=n.n(r);o.a},b2b7:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r={name:"diy-hot-area",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{}},created:function(){},watch:{componentRefresh:function(t){}},computed:{hotAreaWarp:function(){var t="background-color:"+this.value.componentBgColor+";";return"round"==this.value.componentAngle&&(t+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),t}},methods:{}};e.default=r},c00d:function(t,e,n){"use strict";n.r(e);var r=n("cf7f"),o=n("1824");for(var u in o)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(u);n("81cc");var a=n("828b"),i=Object(a["a"])(o["default"],r["b"],r["c"],!1,null,"868438c6",null,!1,r["a"],void 0);e["default"]=i.exports},cf7f:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var r=function(){var t=this,e=t.$createElement,n=(t._self._c,t.$util.img(t.value.imageUrl));t._isMounted||(t.e0=function(e,n){var r=arguments[arguments.length-1].currentTarget.dataset,o=r.eventParams||r["event-params"];n=o.mapItem;return e.stopPropagation(),t.$util.diyRedirectTo(n.link)}),t.$mp.data=Object.assign({},{$root:{g0:n}})},o=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-hot-area-create-component',
    {
        'components/diy-components/diy-hot-area-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("c00d"))
        })
    },
    [['components/diy-components/diy-hot-area-create-component']]
]);
