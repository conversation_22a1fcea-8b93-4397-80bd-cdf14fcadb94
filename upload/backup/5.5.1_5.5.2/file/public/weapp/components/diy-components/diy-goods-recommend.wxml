<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}" class="data-v-095e6f8a"><x-skeleton vue-id="372895bf-1" type="waterfall" loading="{{loading}}" configs="{{skeletonConfig}}" class="data-v-095e6f8a" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{$root.g1}}"><view class="{{['data-v-095e6f8a','goods-list',goodsValue.style]}}" style="{{(goodsListWarpCss)}}"><block wx:if="{{goodsValue.topStyle.support}}"><view class="top-wrap data-v-095e6f8a"><text class="{{['data-v-095e6f8a','js-icon',goodsValue.topStyle.icon.value]}}" style="{{'background-color:'+(goodsValue.topStyle.icon.bgColor)+';'+('color:'+(goodsValue.topStyle.icon.color)+';')}}"></text><text class="title data-v-095e6f8a" style="{{'color:'+(goodsValue.topStyle.color)+';'}}">{{goodsValue.topStyle.title}}</text><text class="line data-v-095e6f8a" style="{{'color:'+(goodsValue.topStyle.subColor)+';'}}"></text><text class="sub data-v-095e6f8a" style="{{'color:'+(goodsValue.topStyle.subColor)+';'}}">{{goodsValue.topStyle.subTitle}}</text></view></block><swiper class="swiper data-v-095e6f8a" style="{{'height:'+(swiperHeight)+';'}}" autoplay="{{false}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="{{['data-v-095e6f8a','swiper-item',item.g2]}}"><block wx:for="{{item.l0}}" wx:for-item="dataItem" wx:for-index="dataIndex" wx:key="dataIndex"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list.'+index+'','',dataIndex]]]]]]]}}" class="{{['goods-item','data-v-095e6f8a',goodsValue.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="goods-img-wrap _div data-v-095e6f8a"><image class="goods-img data-v-095e6f8a" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{dataItem.g3}}" mode="widthFix" lazy-load="{{true}}" data-event-opts="{{[['error',[['imgError',[index,dataIndex]]]]]}}" binderror="__e"></image><block wx:if="{{dataItem.$orig.goods_stock<=0}}"><view class="sell-out data-v-095e6f8a"><text class="iconfont icon-shuqing data-v-095e6f8a"></text></view></block></view><block wx:if="{{goodsValue.goodsNameStyle.control||goodsValue.priceStyle.mainControl||goodsValue.priceStyle.lineControl||goodsValue.labelStyle.support}}"><view class="{{['data-v-095e6f8a','info-wrap',[(value.nameLineMode=='multiple')?'multi-content':'']]}}"><block wx:if="{{goodsValue.goodsNameStyle.control}}"><view class="{{['goods-name','data-v-095e6f8a',[(goodsValue.nameLineMode=='single')?'using-hidden':''],[(goodsValue.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(goodsValue.theme=='diy'?goodsValue.goodsNameStyle.color:'')+';'+('font-weight:'+(goodsValue.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+dataItem.$orig.goods_name+''}}</view></block><view class="pro-info data-v-095e6f8a"><block wx:if="{{goodsValue.labelStyle.support}}"><view class="label-wrap data-v-095e6f8a" style="{{'background:'+(goodsValue.labelStyle.bgColor)+';'+('color:'+(goodsValue.labelStyle.color)+';')}}"><image src="{{item.g4}}" mode="widthFix" class="data-v-095e6f8a"></image><text class="data-v-095e6f8a">{{goodsValue.labelStyle.title}}</text></view></block><view class="discount-price data-v-095e6f8a"><block wx:if="{{goodsValue.priceStyle.mainControl}}"><view class="price-wrap data-v-095e6f8a"><text class="unit price-style small data-v-095e6f8a" style="{{'color:'+(goodsValue.theme=='diy'?goodsValue.priceStyle.mainColor+'!important':'')+';'}}">￥</text><text class="price price-style large data-v-095e6f8a" style="{{'color:'+(goodsValue.theme=='diy'?goodsValue.priceStyle.mainColor+'!important':'')+';'}}">{{dataItem.g5[0]}}</text><text class="unit price-style small data-v-095e6f8a" style="{{'color:'+(goodsValue.theme=='diy'?goodsValue.priceStyle.mainColor+'!important':'')+';'}}">{{'.'+dataItem.g6[1]}}</text></view></block><block wx:if="{{dataItem.m0}}"><view class="delete-price price-font data-v-095e6f8a" style="{{'color:'+(goodsValue.theme=='diy'?goodsValue.priceStyle.lineColor:'')+';'}}">{{"￥"+dataItem.m1}}</view></block><block wx:if="{{goodsValue.saleStyle.control}}"><view class="sale data-v-095e6f8a" style="{{'color:'+(goodsValue.theme=='diy'?goodsValue.saleStyle.color:'')+';'}}">{{'售'+dataItem.$orig.sale_num+(dataItem.$orig.unit?dataItem.$orig.unit:'件')+''}}</view></block></view></view></view></block></view></block></swiper-item></block></swiper></view></block></x-skeleton></view></block>