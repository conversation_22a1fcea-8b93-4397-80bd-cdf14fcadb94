(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-presale"],{"0bfb":function(e,t,o){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={name:"diy-presale",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{loading:!0,skeletonType:"",skeletonConfig:{},list:[],page:1}},created:function(){this.initSkeleton(),this.getData()},watch:{componentRefresh:function(e){this.getData()}},computed:{warpCss:function(){var e="";return e+="background-color:"+this.value.componentBgColor+";","round"==this.value.componentAngle&&(e+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",e+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",e+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",e+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),e},goodsItemCss:function(){var t="";t+="background-color:"+this.value.elementBgColor+";","round"==this.value.elementAngle&&(t+="border-top-left-radius:"+2*this.value.topElementAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topElementAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomElementAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomElementAroundRadius+"rpx;"),"shadow"==this.value.ornament.type&&(t+="box-shadow:0 0 10rpx "+this.value.ornament.color+";"),"stroke"==this.value.ornament.type&&(t+="border:2rpx solid "+this.value.ornament.color+";");var o=e.getSystemInfoSync().windowWidth;if("horizontal-slide"==this.value.template){var i="";i="scroll"==this.value.slideMode&&"diy"==this.value.goodsMarginType?this.rpxUpPx(2*this.value.goodsMarginNum):[o-2*this.rpxUpPx(20)-3*this.rpxUpPx(200)-2*this.rpxUpPx(2*this.value.margin.both)]/6,t+="margin-right:"+i+"px;",t+="margin-left:"+i+"px;"}return t},swiperHeight:function(){return"multiple"==this.value.nameLineMode?"shadow"==this.value.ornament.type?"390rpx":"382rpx":"shadow"==this.value.ornament.type?"364rpx":"348rpx"}},methods:{initSkeleton:function(){"row1-of1"==this.value.template?(this.skeletonType="list",this.skeletonConfig={textRows:2}):"horizontal-slide"==this.value.template&&(this.skeletonType="waterfall",this.skeletonConfig={gridRows:1,gridColumns:3,headHeight:"200rpx",textRows:2,textWidth:["100%","80%"]})},rpxUpPx:function(t){var o=e.getSystemInfoSync().windowWidth,i=o*parseInt(t)/750;return Math.floor(i)},getData:function(){var e=this,t={num:this.value.count};"diy"==this.value.sources&&(t.num=0,t.goods_id_arr=this.value.goodsId.toString()),this.$api.sendRequest({url:"/presale/api/goods/lists",data:t,success:function(t){if(0==t.code&&t.data&&(e.list=t.data,"horizontal-slide"==e.value.template&&"slide"==e.value.slideMode)){var o=[];e.page=Math.ceil(e.list.length/3);for(var i=0;i<e.page;i++){o[i]=[];for(var l=3*i;l<e.list.length;l++){if(3==o[i].length)break;o[i].push(e.list[l])}}e.list=o}e.loading=!1}})},toDetail:function(e){this.$util.redirectTo("/pages_promotion/presale/detail",{id:e.presale_id})},imageError:function(e){this.list[e].goods_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},showPrice:function(e){var t=e.price;return e.member_price&&parseFloat(e.member_price)<parseFloat(t)&&(t=e.member_price),t}}};t.default=o}).call(this,o("df3c")["default"])},1871:function(e,t,o){"use strict";o.r(t);var i=o("a4eb"),l=o("499a");for(var r in l)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return l[e]}))}(r);o("a5f5");var n=o("828b"),a=Object(n["a"])(l["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=a.exports},"499a":function(e,t,o){"use strict";o.r(t);var i=o("0bfb"),l=o.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(r);t["default"]=l.a},a4eb:function(e,t,o){"use strict";o.d(t,"b",(function(){return l})),o.d(t,"c",(function(){return r})),o.d(t,"a",(function(){return i}));var i={xSkeleton:function(){return Promise.all([o.e("common/vendor"),o.e("uni_modules/x-skeleton/components/x-skeleton/x-skeleton")]).then(o.bind(null,"3268"))}},l=function(){var e=this,t=e.$createElement,o=(e._self._c,e.loading||e.list&&e.list.length),i=o?e.list.length:null,l=o&&i&&"row1-of1"==e.value.template?e.__map(e.list,(function(t,o){var i=e.__get_orig(t),l=e.$util.img(t.goods_image,{size:"mid"}),r=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.btnStyle.control)&&e.value.priceStyle.mainControl?e.showPrice(t).split("."):null,n=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.btnStyle.control)&&e.value.priceStyle.mainControl?e.showPrice(t).split("."):null;return{$orig:i,g2:l,g3:r,g4:n}})):null,r=o&&i&&"horizontal-slide"==e.value.template&&"scroll"==e.value.slideMode?e.__map(e.list,(function(t,o){var i=e.__get_orig(t),l=e.$util.img(t.goods_image,{size:"mid"}),r=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl)&&e.value.priceStyle.mainControl?e.showPrice(t).split("."):null,n=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl)&&e.value.priceStyle.mainControl?e.showPrice(t).split("."):null;return{$orig:i,g5:l,g6:r,g7:n}})):null,n=o&&i&&"horizontal-slide"==e.value.template&&"slide"==e.value.slideMode?e.__map(e.page,(function(t,o){var i=e.__get_orig(t),l=e.list.length&&[e.list[o].length/3]>=1&&"flex-between",r=e.__map(e.list[o],(function(t,o){var i=e.__get_orig(t),l=e.$util.img(t.goods_image,{size:"mid"}),r=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl)&&e.value.priceStyle.mainControl?e.showPrice(t).split("."):null,n=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl)&&e.value.priceStyle.mainControl?e.showPrice(t).split("."):null;return{$orig:i,g9:l,g10:r,g11:n}}));return{$orig:i,g8:l,l2:r}})):null;e.$mp.data=Object.assign({},{$root:{g0:o,g1:i,l0:l,l1:r,l3:n}})},r=[]},a5f5:function(e,t,o){"use strict";var i=o("c1f2"),l=o.n(i);l.a},c1f2:function(e,t,o){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-presale-create-component',
    {
        'components/diy-components/diy-presale-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("1871"))
        })
    },
    [['components/diy-components/diy-presale-create-component']]
]);
