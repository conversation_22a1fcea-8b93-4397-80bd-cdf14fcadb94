(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-video"],{2227:function(t,e,n){},"26ae":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"diy-video",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{}},created:function(){},watch:{componentRefresh:function(t){}},computed:{videoWarpCss:function(){var t="";return"round"==this.value.componentAngle&&(t+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),t}},methods:{}};e.default=o},"852e":function(t,e,n){"use strict";var o=n("2227"),u=n.n(o);u.a},"968c":function(t,e,n){"use strict";n.r(e);var o=n("be55"),u=n("d8f4");for(var r in u)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(r);n("852e");var i=n("828b"),a=Object(i["a"])(u["default"],o["b"],o["c"],!1,null,"18befe80",null,!1,o["a"],void 0);e["default"]=a.exports},be55:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return u})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=(this._self._c,this.$util.img(this.value.videoUrl)),n=this.$util.img(this.value.imageUrl);this.$mp.data=Object.assign({},{$root:{g0:e,g1:n}})},u=[]},d8f4:function(t,e,n){"use strict";n.r(e);var o=n("26ae"),u=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);e["default"]=u.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-video-create-component',
    {
        'components/diy-components/diy-video-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("968c"))
        })
    },
    [['components/diy-components/diy-video-create-component']]
]);
