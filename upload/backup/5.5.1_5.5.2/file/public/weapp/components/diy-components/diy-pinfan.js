(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-pinfan"],{"398c":function(t,e,o){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"diy-pinfan",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{loading:!0,skeletonType:"",skeletonConfig:{},list:[],page:1}},created:function(){this.initSkeleton(),this.getData()},watch:{componentRefresh:function(t){this.getData()}},computed:{warpCss:function(){var t="";return t+="background-color:"+this.value.componentBgColor+";","round"==this.value.componentAngle&&(t+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),t},goodsItemCss:function(){var e="";e+="background-color:"+this.value.elementBgColor+";","round"==this.value.elementAngle&&(e+="border-top-left-radius:"+2*this.value.topElementAroundRadius+"rpx;",e+="border-top-right-radius:"+2*this.value.topElementAroundRadius+"rpx;",e+="border-bottom-left-radius:"+2*this.value.bottomElementAroundRadius+"rpx;",e+="border-bottom-right-radius:"+2*this.value.bottomElementAroundRadius+"rpx;"),"shadow"==this.value.ornament.type&&(e+="box-shadow:0 0 10rpx "+this.value.ornament.color+";"),"stroke"==this.value.ornament.type&&(e+="border:2rpx solid "+this.value.ornament.color+";");var o=t.getSystemInfoSync().windowWidth;if("horizontal-slide"==this.value.template){var i="";i="scroll"==this.value.slideMode&&"diy"==this.value.goodsMarginType?this.rpxUpPx(2*this.value.goodsMarginNum):[o-2*this.rpxUpPx(20)-3*this.rpxUpPx(200)-2*this.rpxUpPx(2*this.value.margin.both)]/6,e+="margin-left:"+i+"px;",e+="margin-right:"+i+"px;"}return e},swiperHeight:function(){return"multiple"==this.value.nameLineMode?"shadow"==this.value.ornament.type?"444rpx":"432rpx":"shadow"==this.value.ornament.type?"404rpx":"396rpx"}},methods:{initSkeleton:function(){"row1-of1"==this.value.template?(this.skeletonType="list",this.skeletonConfig={textRows:3}):"horizontal-slide"==this.value.template&&(this.skeletonType="waterfall",this.skeletonConfig={gridRows:1,gridColumns:3,headHeight:"200rpx",textRows:2,textWidth:["100%","80%"]})},rpxUpPx:function(e){var o=t.getSystemInfoSync().windowWidth,i=o*parseInt(e)/750;return Math.floor(i)},getData:function(){var t=this,e={num:this.value.count};"diy"==this.value.sources&&(e.num=0,e.goods_id_arr=this.value.goodsId.toString()),this.$api.sendRequest({url:"/pinfan/api/goods/lists",data:e,success:function(e){if(0==e.code&&e.data&&(t.list=e.data,"horizontal-slide"==t.value.template&&"slide"==t.value.slideMode)){var o=[];t.page=Math.ceil(t.list.length/3);for(var i=0;i<t.page;i++){o[i]=[];for(var n=3*i;n<t.list.length;n++){if(3==o[i].length)break;o[i].push(t.list[n])}}t.list=o}t.loading=!1}})},toDetail:function(t){this.$util.redirectTo("/pages_promotion/pinfan/detail",{pinfan_id:t.pintuan_id})},imageError:function(t){this.list[t].goods_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}}};e.default=o}).call(this,o("df3c")["default"])},7605:function(t,e,o){"use strict";o.r(e);var i=o("fe49"),n=o("cb05");for(var l in n)["default"].indexOf(l)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(l);o("ee52");var r=o("828b"),a=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=a.exports},cb05:function(t,e,o){"use strict";o.r(e);var i=o("398c"),n=o.n(i);for(var l in i)["default"].indexOf(l)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(l);e["default"]=n.a},e837:function(t,e,o){},ee52:function(t,e,o){"use strict";var i=o("e837"),n=o.n(i);n.a},fe49:function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return l})),o.d(e,"a",(function(){return i}));var i={xSkeleton:function(){return Promise.all([o.e("common/vendor"),o.e("uni_modules/x-skeleton/components/x-skeleton/x-skeleton")]).then(o.bind(null,"3268"))}},n=function(){var t=this,e=t.$createElement,o=(t._self._c,t.loading||t.list&&t.list.length),i=o&&"row1-of1"==t.value.template?t.__map(t.list,(function(e,o){var i=t.__get_orig(e),n=t.$util.img(e.goods_image,{size:"mid"}),l=(t.value.goodsNameStyle.control||t.value.priceStyle.mainControl||t.value.priceStyle.lineControl||t.value.btnStyle.control)&&t.value.priceStyle.mainControl?e.pintuan_price.split("."):null,r=(t.value.goodsNameStyle.control||t.value.priceStyle.mainControl||t.value.priceStyle.lineControl||t.value.btnStyle.control)&&t.value.priceStyle.mainControl?e.pintuan_price.split("."):null;return{$orig:i,g1:n,g2:l,g3:r}})):null,n=o&&"horizontal-slide"==t.value.template&&"scroll"==t.value.slideMode?t.__map(t.list,(function(e,o){var i=t.__get_orig(e),n=t.$util.img(e.goods_image,{size:"mid"}),l=(t.value.goodsNameStyle.control||t.value.priceStyle.mainControl)&&t.value.priceStyle.mainControl?e.pintuan_price.split("."):null,r=(t.value.goodsNameStyle.control||t.value.priceStyle.mainControl)&&t.value.priceStyle.mainControl?e.pintuan_price.split("."):null;return{$orig:i,g4:n,g5:l,g6:r}})):null,l=o&&"horizontal-slide"==t.value.template&&"slide"==t.value.slideMode?t.__map(t.page,(function(e,o){var i=t.__get_orig(e),n=t.list.length&&[t.list[o].length/3]>=1&&"flex-between",l=t.__map(t.list[o],(function(e,o){var i=t.__get_orig(e),n=t.$util.img(e.goods_image,{size:"mid"}),l=(t.value.goodsNameStyle.control||t.value.priceStyle.mainControl)&&t.value.priceStyle.mainControl?e.pintuan_price.split("."):null,r=(t.value.goodsNameStyle.control||t.value.priceStyle.mainControl)&&t.value.priceStyle.mainControl?e.pintuan_price.split("."):null;return{$orig:i,g8:n,g9:l,g10:r}}));return{$orig:i,g7:n,l2:l}})):null;t.$mp.data=Object.assign({},{$root:{g0:o,l0:i,l1:n,l3:l}})},l=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-pinfan-create-component',
    {
        'components/diy-components/diy-pinfan-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7605"))
        })
    },
    [['components/diy-components/diy-pinfan-create-component']]
]);
