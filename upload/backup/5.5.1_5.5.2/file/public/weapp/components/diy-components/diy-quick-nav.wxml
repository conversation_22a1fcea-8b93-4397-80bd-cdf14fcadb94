<view style="{{(value.pageStyle)}}"><view style="{{(componentStyle)}}"><scroll-view class="quick-nav" scroll-x="true"><view class="uni-scroll-view-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['redirectTo',['$0'],[[['value.list','',index,'link']]]]]]]}}" class="quick-nav-item" style="{{'background:'+('linear-gradient(to right,'+item.$orig.bgColorStart?item.$orig.bgColorStart:''+','+item.$orig.bgColorEnd?item.$orig.bgColorEnd:''+')')+';'}}" bindtap="__e"><block wx:if="{{item.$orig.imageUrl||item.$orig.icon}}"><view class="quick-img"><block wx:if="{{item.$orig.iconType=='img'}}"><image src="{{item.g0}}" mode="heightFix" show-menu-by-longpress="{{true}}"></image></block><block wx:if="{{item.$orig.iconType=='icon'}}"><diy-icon style="{{'font-size:'+('60rpx')+';'}}" vue-id="{{'178a027e-1-'+index}}" icon="{{item.$orig.icon}}" value="{{item.$orig.style?item.$orig.style:null}}" bind:__l="__l"></diy-icon></block></view></block><text class="quick-text" style="{{'color:'+(item.$orig.textColor)+';'}}">{{item.$orig.title}}</text></view></block></view></scroll-view><ns-login class="vue-ref" vue-id="178a027e-2" data-ref="login" bind:__l="__l"></ns-login></view></view>