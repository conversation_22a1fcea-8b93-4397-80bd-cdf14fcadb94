(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-groupbuy"],{"275d":function(e,t,o){"use strict";o.r(t);var l=o("9083"),i=o.n(l);for(var n in l)["default"].indexOf(n)<0&&function(e){o.d(t,e,(function(){return l[e]}))}(n);t["default"]=i.a},7265:function(e,t,o){},9083:function(e,t,o){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={name:"diy-groupbuy",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{loading:!0,skeletonType:"",skeletonConfig:{},list:[],page:1}},created:function(){this.initSkeleton(),this.getData()},watch:{componentRefresh:function(e){this.getData()}},computed:{warpCss:function(){var e="";return e+="background-color:"+this.value.componentBgColor+";","round"==this.value.componentAngle&&(e+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",e+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",e+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",e+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),e},goodsItemCss:function(){var t="";t+="background-color:"+this.value.elementBgColor+";","round"==this.value.elementAngle&&(t+="border-top-left-radius:"+2*this.value.topElementAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topElementAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomElementAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomElementAroundRadius+"rpx;"),"shadow"==this.value.ornament.type&&(t+="box-shadow:0 0 10rpx "+this.value.ornament.color+";"),"stroke"==this.value.ornament.type&&(t+="border:2rpx solid "+this.value.ornament.color+";");var o=e.getSystemInfoSync().windowWidth;if("horizontal-slide"==this.value.template){var l="";l="scroll"==this.value.slideMode&&"diy"==this.value.goodsMarginType?this.rpxUpPx(2*this.value.goodsMarginNum):[o-2*this.rpxUpPx(20)-3*this.rpxUpPx(200)-2*this.rpxUpPx(2*this.value.margin.both)]/6,t+="margin-left:"+l+"px;",t+="margin-right:"+l+"px;"}return t},swiperHeight:function(){return"multiple"==this.value.nameLineMode?"shadow"==this.value.ornament.type?"404rpx":"392rpx":"shadow"==this.value.ornament.type?"376rpx":"368rpx"}},methods:{initSkeleton:function(){"row1-of1"==this.value.template?(this.skeletonType="list",this.skeletonConfig={textRows:2}):"horizontal-slide"==this.value.template&&(this.skeletonType="waterfall",this.skeletonConfig={gridRows:1,gridColumns:3,headHeight:"200rpx",textRows:2,textWidth:["100%","80%"]})},rpxUpPx:function(t){var o=e.getSystemInfoSync().windowWidth,l=o*parseInt(t)/750;return Math.floor(l)},getData:function(){var e=this,t={num:this.value.count};"diy"==this.value.sources&&(t.num=0,t.goods_id_arr=this.value.goodsId.toString()),this.$api.sendRequest({url:"/groupbuy/api/goods/lists",data:t,success:function(t){if(0==t.code){if(e.list=t.data,"horizontal-slide"==e.value.template&&"slide"==e.value.slideMode){var o=[];e.page=Math.ceil(e.list.length/3);for(var l=0;l<e.page;l++){o[l]=[];for(var i=3*l;i<e.list.length;i++){if(3==o[l].length)break;o[l].push(e.list[i])}}e.list=o}e.loading=!1}}})},toDetail:function(e){this.$util.redirectTo("/pages_promotion/groupbuy/detail",{groupbuy_id:e.groupbuy_id})},imageError:function(e){this.list[e].goods_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}}};t.default=o}).call(this,o("df3c")["default"])},bc89:function(e,t,o){"use strict";var l=o("7265"),i=o.n(l);i.a},dacd:function(e,t,o){"use strict";o.d(t,"b",(function(){return i})),o.d(t,"c",(function(){return n})),o.d(t,"a",(function(){return l}));var l={xSkeleton:function(){return Promise.all([o.e("common/vendor"),o.e("uni_modules/x-skeleton/components/x-skeleton/x-skeleton")]).then(o.bind(null,"3268"))}},i=function(){var e=this,t=e.$createElement,o=(e._self._c,e.loading||e.list&&e.list.length),l=o&&"row1-of1"==e.value.template?e.__map(e.list,(function(t,o){var l=e.__get_orig(t),i=e.$util.img(t.goods_image,{size:"mid"}),n=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.priceStyle.lineControl||e.value.btnStyle.control)&&e.value.priceStyle.mainControl?t.groupbuy_price.split("."):null,r=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.priceStyle.lineControl||e.value.btnStyle.control)&&e.value.priceStyle.mainControl?t.groupbuy_price.split("."):null;return{$orig:l,g1:i,g2:n,g3:r}})):null,i=o&&"horizontal-slide"==e.value.template&&"scroll"==e.value.slideMode?e.__map(e.list,(function(t,o){var l=e.__get_orig(t),i=e.$util.img(t.goods_image,{size:"mid"}),n=e.value.saleStyle.control?e.$util.img("public/uniapp/groupbuy/bg.png"):null,r=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.priceStyle.lineControl)&&e.value.priceStyle.mainControl?t.groupbuy_price.split("."):null,a=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.priceStyle.lineControl)&&e.value.priceStyle.mainControl?t.groupbuy_price.split("."):null;return{$orig:l,g4:i,g5:n,g6:r,g7:a}})):null,n=o&&"horizontal-slide"==e.value.template&&"slide"==e.value.slideMode?e.__map(e.page,(function(t,o){var l=e.__get_orig(t),i=e.list.length&&[e.list[o].length/3]>=1&&"flex-between",n=e.value.saleStyle.control?e.$util.img("public/uniapp/groupbuy/bg.png"):null,r=e.__map(e.list[o],(function(t,o){var l=e.__get_orig(t),i=e.$util.img(t.goods_image,{size:"mid"}),n=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.priceStyle.lineControl)&&e.value.priceStyle.mainControl?t.groupbuy_price.split("."):null,r=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.priceStyle.lineControl)&&e.value.priceStyle.mainControl?t.groupbuy_price.split("."):null;return{$orig:l,g9:i,g11:n,g12:r}}));return{$orig:l,g8:i,g10:n,l2:r}})):null;e.$mp.data=Object.assign({},{$root:{g0:o,l0:l,l1:i,l3:n}})},n=[]},fbd9:function(e,t,o){"use strict";o.r(t);var l=o("dacd"),i=o("275d");for(var n in i)["default"].indexOf(n)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(n);o("bc89");var r=o("828b"),a=Object(r["a"])(i["default"],l["b"],l["c"],!1,null,null,null,!1,l["a"],void 0);t["default"]=a.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-groupbuy-create-component',
    {
        'components/diy-components/diy-groupbuy-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("fbd9"))
        })
    },
    [['components/diy-components/diy-groupbuy-create-component']]
]);
