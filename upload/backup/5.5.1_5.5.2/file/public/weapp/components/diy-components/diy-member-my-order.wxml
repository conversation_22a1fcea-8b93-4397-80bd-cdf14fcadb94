<view style="{{(value.pageStyle)}}"><view class="common-wrap" style="{{(warpCss)}}"><view class="order-wrap"><view class="status-wrap"><view data-event-opts="{{[['tap',[['redirect',['/pages/order/list?status=waitpay']]]]]}}" class="item-wrap" bindtap="__e"><view class="icon-block"><block wx:if="{{value.style==3}}"><image src="{{$root.g0}}" mode="widthFix"></image><view class="icon-shade" style="{{('-webkit-mask-image: url('+$root.g1+');-webkit-mask-box-image: url('+$root.g2+');')}}"></view></block><block wx:else><block wx:if="{{value.icon.waitPay}}"><diy-icon vue-id="611f2838-1" icon="{{value.icon.waitPay.icon}}" value="{{value.icon.waitPay.style?value.icon.waitPay.style:null}}" bind:__l="__l"></diy-icon></block></block><block wx:if="{{orderNum.waitpay>0}}"><text class="order-num color-base-bg price-font">{{orderNum.waitpay>99?'99+':orderNum.waitpay}}</text></block></view><view class="title">{{value.icon.waitPay.title}}</view></view><view data-event-opts="{{[['tap',[['redirect',['/pages/order/list?status=waitsend']]]]]}}" class="item-wrap" bindtap="__e"><view class="icon-block"><block wx:if="{{value.style==3}}"><image src="{{$root.g3}}" mode="widthFix"></image><view class="icon-shade" style="{{('-webkit-mask-image: url('+$root.g4+');-webkit-mask-box-image: url('+$root.g5+');')}}"></view></block><block wx:else><block wx:if="{{value.icon.waitSend}}"><diy-icon vue-id="611f2838-2" icon="{{value.icon.waitSend.icon}}" value="{{value.icon.waitSend.style?value.icon.waitSend.style:null}}" bind:__l="__l"></diy-icon></block></block><block wx:if="{{orderNum.waitsend>0}}"><text class="order-num color-base-bg price-font">{{orderNum.waitsend>99?'99+':orderNum.waitsend}}</text></block></view><view class="title">{{value.icon.waitSend.title}}</view></view><view data-event-opts="{{[['tap',[['redirect',['/pages/order/list?status=waitconfirm']]]]]}}" class="item-wrap" bindtap="__e"><view class="icon-block"><block wx:if="{{value.style==3}}"><image src="{{$root.g6}}" mode="widthFix"></image><view class="icon-shade" style="{{('-webkit-mask-image: url('+$root.g7+');-webkit-mask-box-image: url('+$root.g8+');')}}"></view></block><block wx:else><block wx:if="{{value.icon.waitConfirm}}"><diy-icon vue-id="611f2838-3" icon="{{value.icon.waitConfirm.icon}}" value="{{value.icon.waitConfirm.style?value.icon.waitConfirm.style:null}}" bind:__l="__l"></diy-icon></block></block><block wx:if="{{orderNum.waitconfirm>0}}"><text class="order-num color-base-bg price-font">{{orderNum.waitconfirm>99?'99+':orderNum.waitconfirm}}</text></block></view><view class="title">{{value.icon.waitConfirm.title}}</view></view><view data-event-opts="{{[['tap',[['redirect',['/pages/order/list?status=wait_use']]]]]}}" class="item-wrap" bindtap="__e"><view class="icon-block"><block wx:if="{{value.style==3}}"><image src="{{$root.g9}}" mode="widthFix"></image><view class="icon-shade" style="{{('-webkit-mask-image: url('+$root.g10+');-webkit-mask-box-image: url('+$root.g11+');')}}"></view></block><block wx:else><block wx:if="{{value.icon.waitUse}}"><diy-icon vue-id="611f2838-4" icon="{{value.icon.waitUse.icon}}" value="{{value.icon.waitUse.style?value.icon.waitUse.style:null}}" bind:__l="__l"></diy-icon></block></block><block wx:if="{{orderNum.wait_use>0}}"><text class="order-num color-base-bg price-font">{{orderNum.wait_use>99?'99+':orderNum.wait_use}}</text></block></view><view class="title">{{value.icon.waitUse.title}}</view></view><view data-event-opts="{{[['tap',[['redirect',['/pages_tool/order/activist']]]]]}}" class="item-wrap" bindtap="__e"><view class="icon-block"><block wx:if="{{value.style==3}}"><image src="{{$root.g12}}" mode="widthFix"></image><view class="icon-shade" style="{{('-webkit-mask-image: url('+$root.g13+');-webkit-mask-box-image: url('+$root.g14+');')}}"></view></block><block wx:else><block wx:if="{{value.icon.refunding}}"><diy-icon vue-id="611f2838-5" icon="{{value.icon.refunding.icon}}" value="{{value.icon.refunding.style?value.icon.refunding.style:null}}" bind:__l="__l"></diy-icon></block></block><block wx:if="{{orderNum.refunding>0}}"><text class="order-num color-base-bg price-font">{{orderNum.refunding>99?'99+':orderNum.refunding}}</text></block></view><view class="title">{{value.icon.refunding.title}}</view></view></view></view><ns-login class="vue-ref" vue-id="611f2838-6" data-ref="login" bind:__l="__l"></ns-login></view></view>