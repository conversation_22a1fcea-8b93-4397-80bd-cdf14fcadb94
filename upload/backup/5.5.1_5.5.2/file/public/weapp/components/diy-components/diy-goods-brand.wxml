<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}"><x-skeleton vue-id="52983054-1" type="waterfall" loading="{{loading}}" configs="{{skeletonConfig}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['brand-wrap',value.ornament.type]}}" style="{{(warpCss)}}"><view class="{{[value.style]}}"><view hidden="{{!(value.title)}}" class="title-wrap" style="{{'color:'+(value.textColor)+';'+('font-weight:'+(value.fontWeight?'bold':'')+';')}}">{{value.title}}</view><view class="ul-wrap"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="li-item"><image class="brand-pic" style="{{(itemCss)}}" src="{{item.g1}}" mode="aspectFit" data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]],['error',[['imgError',[index]]]]]}}" bindtap="__e" binderror="__e"></image></view></block></view></view></view></x-skeleton></view></block>