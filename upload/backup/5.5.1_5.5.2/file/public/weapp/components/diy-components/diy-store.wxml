<view style="{{(value.pageStyle)}}"><view class="store-wrap"><block wx:if="{{value.style==1}}"><block><view class="store-box store-one"><view class="store-info"><view data-event-opts="{{[['tap',[['toStoreList']]]]}}" class="info-box" style="{{'color:'+(value.textColor)+';'}}" bindtap="__e"><block wx:if="{{globalStoreInfo&&globalStoreInfo.store_id}}"><block><text class="title">{{globalStoreInfo.store_name}}</text><text><text class="change margin-left">切换</text><text class="iconfont icon-right"></text></text></block></block><block wx:else><text class="title">定位中...</text></block></view><view class="address-wrap" style="{{'color:'+(value.textColor)+';'}}"><text class="iconfont icon-dizhi"></text><block wx:if="{{globalStoreInfo&&globalStoreInfo.store_id}}"><text data-event-opts="{{[['tap',[['mapRoute',['$event']]]]]}}" class="address" bindtap="__e">{{globalStoreInfo.show_address}}</text></block><block wx:else><text>获取当前位置...</text></block></view></view><view data-event-opts="{{[['tap',[['selectStore']]]]}}" class="store-image" bindtap="__e"><block wx:if="{{globalStoreInfo&&globalStoreInfo.store_image}}"><image src="{{$root.g0}}" mode="aspectFill"></image></block><block wx:else><image src="{{$root.g1.store}}" mode="aspectFill"></image></block></view></view></block></block><block wx:if="{{value.style==2}}"><block><view data-event-opts="{{[['tap',[['toStoreList']]]]}}" class="store-box store-three" bindtap="__e"><view class="store-info"><view data-event-opts="{{[['tap',[['selectStore']]]]}}" class="store-image" bindtap="__e"><block wx:if="{{globalStoreInfo&&globalStoreInfo.store_image}}"><image src="{{$root.g2}}" mode="aspectFill"></image></block><block wx:else><image src="{{$root.g3.store}}" mode="aspectFill"></image></block></view><view class="info-box" style="{{'color:'+(value.textColor)+';'}}"><block wx:if="{{globalStoreInfo&&globalStoreInfo.store_id}}"><block><text class="title">{{globalStoreInfo.store_name}}</text><text><text class="change margin-left">切换</text><text class="iconfont icon-right"></text></text></block></block><block wx:else><text class="title">定位中...</text></block></view></view><view data-event-opts="{{[['tap',[['search']]]]}}" class="store-icon" catchtap="__e"><text class="iconfont icon-sousuo3" style="{{'color:'+(value.textColor)+';'}}"></text></view></view></block></block><block wx:if="{{value.style==3}}"><block><view data-event-opts="{{[['tap',[['toStoreList']]]]}}" class="store-box store-four" bindtap="__e"><view class="store-left-wrap"><block wx:if="{{globalStoreInfo&&globalStoreInfo.store_id}}"><block><text class="iconfont icon-weizhi" style="{{'color:'+(value.textColor)+';'}}"></text><text class="title" style="{{'color:'+(value.textColor)+';'}}">{{globalStoreInfo.store_name}}</text><text class="iconfont icon-unfold" style="{{'color:'+(value.textColor)+';'}}"></text></block></block><block wx:else><text class="title">定位中...</text></block></view><view class="store-right-search"><input class="uni-input font-size-tag" type="text" disabled="{{true}}" placeholder="商品搜索" data-event-opts="{{[['tap',[['search']]]]}}" catchtap="__e"/><text data-event-opts="{{[['tap',[['search']]]]}}" class="iconfont icon-sousuo3" catchtap="__e"></text></view></view></block></block></view></view>