<view style="{{(value.pageStyle)}}" class="data-v-29ecd1d6"><view class="single-graph data-v-29ecd1d6"><view class="swiper-box data-v-29ecd1d6" style="{{(imgAdsMarginWarp)}}"><block wx:if="{{$root.g0==1}}"><block class="data-v-29ecd1d6"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="simple-graph-wrap data-v-29ecd1d6" style="{{(imgAdsSwiper)}}" bindtap="__e"><image style="{{'height:'+(imgAdsValue.list[0].imgHeight)+';'}}" src="{{$root.g1}}" mode="widthFix" show-menu-by-longpress="{{true}}" class="data-v-29ecd1d6"></image></view></block></block><block wx:else><swiper class="{{['swiper','data-v-29ecd1d6',(imgAdsValue.indicatorLocation=='left')?'swiper-left':'',(imgAdsValue.indicatorLocation=='right')?'swiper-right':'',(imgAdsValue.carouselStyle=='line')?'ns-indicator-dots':'']}}" style="{{'height:'+(swiperHeight)+';'}}" autoplay="{{true}}" interval="{{imgAdsValue.interval}}" circular="true" indicator-dots="{{isDots}}" indicator-color="rgba(130, 130, 130, .5)" indicator-active-color="{{imgAdsValue.indicatorColor}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.$orig.imageUrl}}"><swiper-item data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="swiper-item data-v-29ecd1d6" style="{{(imgAdsSwiper)}}" bindtap="__e"><view class="item data-v-29ecd1d6" style="{{(imgAdsSwiper+'height: '+item.$orig.imgHeight)}}"><image src="{{item.g2}}" mode="{{item.$orig.imageMode||'scaleToFill'}}" show-menu-by-longpress="{{true}}" class="data-v-29ecd1d6"></image></view></swiper-item></block></block></swiper></block><block wx:if="{{$root.g3}}"><view class="{{['data-v-29ecd1d6','swiper-dot-box',[(imgAdsValue.carouselStyle=='line')?'straightLine':''],[(imgAdsValue.indicatorLocation=='left')?'swiper-left':''],[(imgAdsValue.indicatorLocation=='right')?'swiper-right':'']]}}"><block wx:for="{{$root.l1}}" wx:for-item="numItem" wx:for-index="numIndex" wx:key="numIndex"><view class="{{['data-v-29ecd1d6','swiper-dot',[(numIndex==swiperIndex)?'active':'']]}}" style="{{numItem.s0}}"></view></block></view></block></view></view></view>