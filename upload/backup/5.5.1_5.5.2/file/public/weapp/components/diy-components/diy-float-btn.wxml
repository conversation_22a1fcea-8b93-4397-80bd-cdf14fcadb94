<view style="{{(value.pageStyle)}}"><view class="{{['float-btn',(value.bottomPosition==1)?'left_top':'',(value.bottomPosition==2)?'right_top':'',(value.bottomPosition==3)?'left_bottom':'',(value.bottomPosition==4)?'right_bottom':'']}}" style="{{(style)}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="button-box" style="{{'width:'+(value.imageSize+'px')+';'+('height:'+(value.imageSize+'px')+';')+('font-size:'+(value.imageSize+'px')+';')}}" bindtap="__e"><block wx:if="{{!item.$orig.iconType||item.$orig.iconType=='img'}}"><image src="{{item.g0}}" mode="aspectFit" show-menu-by-longpress="{{true}}"></image></block><block wx:else><block wx:if="{{item.$orig.iconType&&item.$orig.iconType=='icon'}}"><diy-icon vue-id="{{'78ef1599-1-'+index}}" icon="{{item.$orig.icon}}" value="{{item.$orig.style?item.$orig.style:null}}" bind:__l="__l"></diy-icon></block></block></view></block></block></view></view>