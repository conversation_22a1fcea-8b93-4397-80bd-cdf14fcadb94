(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-member-info"],{"013c":function(e,t,n){"use strict";var o=n("f279"),i=n.n(o);i.a},"4c7f":function(e,t,n){},6303:function(e,t,n){"use strict";n.r(t);var o=n("ee1e"),i=n("7097");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n("aa36"),n("013c");var r=n("828b"),m=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"3d26f424",null,!1,o["a"],void 0);t["default"]=m.exports},7097:function(e,t,n){"use strict";n.r(t);var o=n("ec02"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},aa36:function(e,t,n){"use strict";var o=n("4c7f"),i=n.n(o);i.a},ec02:function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o;o=e.getMenuButtonBoundingClientRect();var i={name:"diy-member-info",props:{value:{type:Object,default:function(){return{}}},global:{type:Object,default:function(){return{}}}},components:{nsContact:function(){n.e("components/ns-contact/ns-contact").then(function(){return resolve(n("5036"))}.bind(null,n)).catch(n.oe)}},data:function(){return{info:null,superMember:null,memberCode:null,avatarUrl:"",headImg:"",nickName:"",completeInfoCallback:null,menuButtonInfo:o}},options:{styleIsolation:"shared"},created:function(){this.init(!1)},watch:{storeToken:function(e,t){this.init()},componentRefresh:function(e){this.init()}},computed:{memberInfoStyle:function(){var e={},t="",n="contain";return 0==this.global.navBarSwitch&&(e["padding-top"]=this.menuButtonInfo.height+this.menuButtonInfo.top-this.value.margin.top+"px"),4==this.value.style?(t=this.$util.img("app/component/view/member_info/img/style_4_bg.png"),n="cover"):3!=this.value.style&&(t=this.$util.img("public/static/img/diy_view/member_info_bg.png")),"default"==this.value.theme?e.background="url('".concat(t,"') no-repeat bottom / ").concat(n,", var(--base-color)"):e.background="url('".concat(t,"') no-repeat bottom / ").concat(n,",linear-gradient(").concat(this.value.gradientAngle,"deg, ").concat(this.value.bgColorStart," 0%, ").concat(this.value.bgColorEnd," 100%)"),this.$util.objToStyle(e)},infoStyle:function(){var e={};return 4==this.value.style&&(this.superMember?e["padding-bottom"]="276rpx":e["padding-bottom"]="166rpx"),this.$util.objToStyle(e)},superMemberStyle:function(){var e={"margin-left":2*parseInt(this.value.infoMargin)+"rpx ","margin-right":2*parseInt(this.value.infoMargin)+"rpx "};return 3==this.value.style?e.background="#292f45 url("+this.$util.img("public/uniapp/member/supervip_bg.png")+") no-repeat bottom / 100% 100%":4==this.value.style?(e={},e.background="url("+this.$util.img("app/component/view/member_info/img/super_vip_bg_4.png")+") no-repeat bottom / contain"):e.background="url('"+this.$util.img("public/static/img/diy_view/super_member_bg.png")+"') no-repeat bottom / 100% 100%, linear-gradient(107deg, "+this.themeStyle.super_member.super_member_start_bg+" 0%, "+this.themeStyle.super_member.super_member_end_bg+" 100%)",this.$util.objToStyle(e)},warpCss:function(){var e="";return e+="background-color:"+this.value.componentBgColor+";","round"==this.value.componentAngle&&(e+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",e+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",e+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",e+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),e},isDisabled:function(){return!(this.nickName.length>0)}},methods:{init:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e?this.storeToken?this.getMemberInfo():this.$store.commit("setMemberInfo",""):this.memberInfo&&(this.headImg=this.memberInfo.headimg,this.nickName=this.memberInfo.nickname,this.avatarUrl=this.headImg?this.$util.img(this.headImg):this.$util.getDefaultImage().head,"微信用户"==this.nickName&&this.openCompleteInfoPop(),this.getCouponNum()),this.getMemberCardInfo()},getMemberInfo:function(){var e=this;this.$api.sendRequest({url:"/api/member/info",data:{},success:function(t){if(0==t.code){if(e.info=t.data,null==e.info)return e.$store.commit("setToken",""),e.$store.commit("setMemberInfo",""),void e.$store.dispatch("emptyCart");e.headImg=e.info.headimg,e.nickName=e.info.nickname,e.avatarUrl=e.headImg?e.$util.img(e.headImg):e.$util.getDefaultImage().head,"微信用户"==e.nickName&&e.openCompleteInfoPop(),e.getCouponNum()}}})},getCouponNum:function(){var e=this;this.$api.sendRequest({url:"/coupon/api/coupon/num",success:function(t){0==t.code&&(e.info?(e.info.coupon_num=t.data,e.$store.commit("setMemberInfo",e.info)):(e.memberInfo.coupon_num=t.data,e.$forceUpdate(),e.$store.commit("setMemberInfo",e.memberInfo)))}})},getMemberCardInfo:function(){var e=this;this.$api.sendRequest({url:"/supermember/api/membercard/firstcard",success:function(t){0==t.code&&t.data&&(e.superMember=t.data)}})},redirect:function(e){this.storeToken?this.$util.redirectTo(e):e?this.$util.redirectTo("/pages_tool/login/index",{back:encodeURIComponent(e)}):this.$util.redirectTo("/pages_tool/login/index")},showMemberQrcode:function(){var t=this;this.memberInfo.mobile||this.memberInfo.member_code?(this.memberCode&&this.$refs.erWeiPopup.open(),this.$api.sendRequest({url:"/api/member/membereqrcode",data:{page:""},success:function(e){if(0==e.code){var n=(new Date).getTime();t.memberCode={barcode:e.bar_code+"?rand="+n,qrcode:e.data.path+"?rand="+n,member_code:e.member_code},t.$refs.erWeiPopup.open()}}})):e.showModal({title:"提示",content:"使用会员码需先绑定手机号，是否绑定手机号？",success:function(e){e.confirm&&t.$util.redirectTo("/pages_tool/member/info_edit",{action:"bind_mobile"})}})},closeMemberQrcode:function(){this.$refs.erWeiPopup.close()},redirectBeforeAuth:function(e){var t=this;this.storeToken?-1!=this.memberInfo.nickname.indexOf("u_")&&this.memberInfo.nickname==this.memberInfo.username||this.memberInfo.nickname==this.memberInfo.mobile?this.getWxAuth((function(){t.$util.redirectTo(e)})):this.$util.redirectTo(e):this.$refs.login.open("/pages/member/index")},getWxAuth:function(e){this.openCompleteInfoPop((function(){"function"==typeof e&&e()}))},modifyNickname:function(e){var t=this;this.$api.sendRequest({url:"/api/member/modifynickname",data:{nickname:e},success:function(n){0==n.code&&(t.memberInfo.nickname=e,t.$store.commit("setMemberInfo",t.memberInfo))}})},modifyHeadimg:function(e){var t=this;this.$api.sendRequest({url:"/api/member/modifyheadimg",data:{headimg:e},success:function(n){0==n.code&&(t.memberInfo.headimg=e,t.$store.commit("setMemberInfo",t.memberInfo))}})},openCompleteInfoPop:function(e){var t=this;this.$refs.completeInfoPopup.open((function(){t.$store.commit("setBottomNavHidden",!1)})),this.$store.commit("setBottomNavHidden",!0),this.completeInfoCallback=e},saveCompleteInfo:function(){0!=this.nickName.length?(this.modifyNickname(this.nickName),this.modifyHeadimg(this.headImg),this.$refs.completeInfoPopup.close(),this.$store.commit("setBottomNavHidden",!1),"function"==typeof this.completeInfoCallback&&this.completeInfoCallback()):this.$util.showToast({title:"请输入昵称"})},cancelCompleteInfo:function(){this.$refs.completeInfoPopup.close(),this.$store.commit("setBottomNavHidden",!1)},blurNickName:function(e){e.detail.value&&(this.nickName=e.detail.value)},onChooseAvatar:function(t){var n=this;this.avatarUrl=t.detail.avatarUrl,e.getFileSystemManager().readFile({filePath:this.avatarUrl,encoding:"base64",success:function(e){var t="data:image/jpeg;base64,"+e.data;n.$api.uploadBase64({base64:t,success:function(e){0==e.code?n.headImg=e.data.pic_path:n.$util.showToast({title:e.message})},fail:function(){n.$util.showToast({title:"上传失败"})}})}})},splitFn:function(e){return e.replace(/(?=(\d{4})+$)/g," ")}}};t.default=i}).call(this,n("df3c")["default"])},ee1e:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={nsContact:function(){return n.e("components/ns-contact/ns-contact").then(n.bind(null,"5036"))},uniPopup:function(){return n.e("components/uni-popup/uni-popup").then(n.bind(null,"d745"))},nsLogin:function(){return Promise.all([n.e("common/vendor"),n.e("components/ns-login/ns-login")]).then(n.bind(null,"2910"))}},i=function(){var e=this,t=e.$createElement,n=(e._self._c,e.memberInfo&&e.memberInfo.headimg?e.$util.img(e.memberInfo.headimg):null),o=e.memberInfo&&!e.memberInfo.headimg?e.$util.getDefaultImage():null,i=e.memberInfo?-1!=e.memberInfo.nickname.indexOf("u_")&&e.memberInfo.nickname==e.memberInfo.username||e.memberInfo.nickname==e.memberInfo.mobile:null,a=e.memberInfo&&3==e.value.style&&e.memberInfo.member_level?e.$util.img("public/uniapp/member/supervip_icon.png"):null,r=e.memberInfo&&4==e.value.style&&e.memberInfo.member_level?e.$util.img("app/component/view/member_info/img/style_4_vip_tag.png"):null,m=e.memberInfo?null:e.$util.getDefaultImage(),u=parseInt(e.value.infoMargin),s=parseInt(e.value.infoMargin),l=e.memberInfo?(parseFloat(e.memberInfo.balance)+parseFloat(e.memberInfo.balance_money)).toFixed(2):null,c=e.memberInfo?parseFloat(e.memberInfo.point):null,f=!e.superMember||1!=e.value.style&&2!=e.value.style&&3!=e.value.style||3!=e.value.style?null:e.$util.img("public/uniapp/member/open_member.png"),p=e.superMember?e.$util.img("app/component/view/member_info/img/style_4_vip_huangguan.png"):null,d=parseInt(e.value.infoMargin),b=parseInt(e.value.infoMargin),h=e.memberInfo?(parseFloat(e.memberInfo.balance)+parseFloat(e.memberInfo.balance_money)).toFixed(2):null,g=e.memberInfo?parseFloat(e.memberInfo.point):null,I=e.$util.img("app/component/view/member_info/img/style_4_code.png"),v=parseInt(e.value.infoMargin),$=parseInt(e.value.infoMargin),_=e.memberInfo?(parseFloat(e.memberInfo.balance)+parseFloat(e.memberInfo.balance_money)).toFixed(2):null,y=e.memberInfo?parseFloat(e.memberInfo.point):null,k=e.memberCode?e.$util.img(e.memberCode.barcode):null,C=e.memberCode?e.splitFn(e.memberCode.member_code):null,M=e.memberCode?e.$util.img(e.memberCode.qrcode):null,x=e.avatarUrl?null:e.$util.getDefaultImage();e._isMounted||(e.e0=function(t){e.memberInfo.headimg=e.$util.getDefaultImage().head},e.e1=function(t){return e.$util.redirectTo("/pages_tool/member/info")},e.e2=function(t){e.avatarUrl=e.$util.getDefaultImage().head}),e.$mp.data=Object.assign({},{$root:{g0:n,g1:o,g2:i,g3:a,g4:r,g5:m,m0:u,m1:s,g6:l,m2:c,g7:f,g8:p,m3:d,m4:b,g9:h,m5:g,g10:I,m6:v,m7:$,g11:_,m8:y,g12:k,m9:C,g13:M,g14:x}})},a=[]},f279:function(e,t,n){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-member-info-create-component',
    {
        'components/diy-components/diy-member-info-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("6303"))
        })
    },
    [['components/diy-components/diy-member-info-create-component']]
]);
