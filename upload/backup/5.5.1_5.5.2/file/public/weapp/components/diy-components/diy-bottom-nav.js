(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-bottom-nav"],{2532:function(t,e,n){"use strict";n.r(e);var i=n("61c2"),a=n("e926");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("ec23");var o=n("828b"),c=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=c.exports},"3cd6":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"diy-bottom-nav",props:{value:{type:Object},name:{type:String,default:""}},data:function(){return{currentRoute:"",jumpFlag:!0,cartAnimation:{}}},mounted:function(){var e=this,n=getCurrentPages()[getCurrentPages().length-1];n&&n.route&&(this.currentRoute=n.route),this.$nextTick((function(){if(!e.$store.state.cartPosition){var n=t.createSelectorQuery().in(e);n.select("#tabbarCart").boundingClientRect((function(t){t&&e.$store.commit("setCartPosition",t)})).exec(),n.select(".tab-bar").boundingClientRect((function(t){t&&e.$store.commit("setTabBarHeight",t.height+"px")})).exec()}}))},computed:{cartChange:function(){return this.$store.state.cartChange}},watch:{cartChange:function(e,n){var i=this;if(e>n){var a=t.createAnimation({duration:200,timingFunction:"ease"});a.scale(1.2).step(),this.cartAnimation=a.export(),setTimeout((function(){a.scale(1).step(),i.cartAnimation=a.export()}),300)}}},methods:{redirectTo:function(t){this.$emit("callback"),this.$util.diyRedirectTo(t)},verify:function(t){if(null==t||""==t||!t.wap_url)return!1;if(this.name)var e=this.currentRoute+"?name="+this.name;else e=this.currentRoute;return"/pages/index/index"==t.wap_url&&"DIY_VIEW_INDEX"==this.name||!(!e||-1==t.wap_url.indexOf(e))}}};e.default=n}).call(this,n("df3c")["default"])},"61c2":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={diyIcon:function(){return n.e("components/diy-components/diy-icon").then(n.bind(null,"a68f"))}},a=function(){var t=this,e=t.$createElement,n=(t._self._c,t.tabBarList&&t.tabBarList.list?t.__map(t.tabBarList.list,(function(e,n){var i=t.__get_orig(e),a="/pages/goods/cart"!=e.link.wap_url||1!=t.tabBarList.type&&2!=t.tabBarList.type?null:t.verify(e.link),r="/pages/goods/cart"!=e.link.wap_url||1!=t.tabBarList.type&&2!=t.tabBarList.type||!a||"img"!=e.selected_icon_type?null:t.$util.img(e.selectedIconPath),o="/pages/goods/cart"!=e.link.wap_url||1!=t.tabBarList.type&&2!=t.tabBarList.type||a||"img"!=e.icon_type?null:t.$util.img(e.iconPath),c="/pages/goods/cart"==e.link.wap_url||1!=t.tabBarList.type&&2!=t.tabBarList.type?null:t.verify(e.link),u="/pages/goods/cart"==e.link.wap_url||1!=t.tabBarList.type&&2!=t.tabBarList.type||!c||"img"!=e.selected_icon_type?null:t.$util.img(e.selectedIconPath),s="/pages/goods/cart"==e.link.wap_url||1!=t.tabBarList.type&&2!=t.tabBarList.type||c||"img"!=e.icon_type?null:t.$util.img(e.iconPath),l=1!=t.tabBarList.type&&3!=t.tabBarList.type||"diy"!=t.tabBarList.theme?null:t.verify(e.link),p=1!=t.tabBarList.type&&3!=t.tabBarList.type||"default"!=t.tabBarList.theme?null:t.verify(e.link);return{$orig:i,m0:a,g0:r,g1:o,m1:c,g2:u,g3:s,m2:l,m3:p}})):null);t.$mp.data=Object.assign({},{$root:{l0:n}})},r=[]},7382:function(t,e,n){},e926:function(t,e,n){"use strict";n.r(e);var i=n("3cd6"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},ec23:function(t,e,n){"use strict";var i=n("7382"),a=n.n(i);a.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-bottom-nav-create-component',
    {
        'components/diy-components/diy-bottom-nav-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("2532"))
        })
    },
    [['components/diy-components/diy-bottom-nav-create-component']]
]);
