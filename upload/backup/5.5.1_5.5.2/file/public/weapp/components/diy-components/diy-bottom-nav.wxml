<block wx:if="{{tabBarList&&tabBarList.list}}"><view><view class="tab-bar" style="{{'background-color:'+(tabBarList.backgroundColor)+';'}}"><view class="tabbar-border"></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['redirectTo',['$0'],[[['tabBarList.list','id',item.$orig.id,'link']]]]]]]}}" class="item" bindtap="__e"><view class="bd"><block wx:if="{{item.$orig.link.wap_url=='/pages/goods/cart'}}"><block><block wx:if="{{tabBarList.type==1||tabBarList.type==2}}"><view class="icon" animation="{{cartAnimation}}" id="tabbarCart"><block wx:if="{{item.m0}}"><block><block wx:if="{{item.$orig.selected_icon_type=='img'}}"><image src="{{item.g0}}"></image></block><block wx:if="{{item.$orig.selected_icon_type=='icon'}}"><diy-icon vue-id="{{'251134d2-1-'+index}}" icon="{{item.$orig.selectedIconPath}}" value="{{item.$orig.selected_style?item.$orig.selected_style:null}}" bind:__l="__l"></diy-icon></block></block></block><block wx:else><block><block wx:if="{{item.$orig.icon_type=='img'}}"><image src="{{item.g1}}"></image></block><block wx:if="{{item.$orig.icon_type=='icon'}}"><diy-icon vue-id="{{'251134d2-2-'+index}}" icon="{{item.$orig.iconPath}}" value="{{item.$orig.style?item.$orig.style:null}}" bind:__l="__l"></diy-icon></block></block></block><block wx:if="{{cartNumber>0}}"><view class="{{['cart-count-mark','font-size-activity-tag',(item.$orig.link.wap_url=='/pages/goods/cart'&&cartNumber>99)?'max':'']}}" style="{{'background:'+('var(--price-color)')+';'}}">{{''+(cartNumber>99?'99+':cartNumber)+''}}</view></block></view></block></block></block><block wx:else><block><block wx:if="{{tabBarList.type==1||tabBarList.type==2}}"><view class="icon"><block wx:if="{{item.m1}}"><block><block wx:if="{{item.$orig.selected_icon_type=='img'}}"><image src="{{item.g2}}"></image></block><block wx:if="{{item.$orig.selected_icon_type=='icon'}}"><diy-icon vue-id="{{'251134d2-3-'+index}}" icon="{{item.$orig.selectedIconPath}}" value="{{item.$orig.selected_style?item.$orig.selected_style:null}}" bind:__l="__l"></diy-icon></block></block></block><block wx:else><block><block wx:if="{{item.$orig.icon_type=='img'}}"><image src="{{item.g3}}"></image></block><block wx:if="{{item.$orig.icon_type=='icon'}}"><diy-icon vue-id="{{'251134d2-4-'+index}}" icon="{{item.$orig.iconPath}}" value="{{item.$orig.style?item.$orig.style:null}}" bind:__l="__l"></diy-icon></block></block></block></view></block></block></block><block wx:if="{{(tabBarList.type==1||tabBarList.type==3)&&tabBarList.theme=='diy'}}"><view class="label" style="{{'color:'+(item.m2?tabBarList.textHoverColor:tabBarList.textColor)+';'}}">{{''+item.$orig.text+''}}</view></block><block wx:if="{{(tabBarList.type==1||tabBarList.type==3)&&tabBarList.theme=='default'}}"><view class="label" style="{{'color:'+(item.m3?'var(--base-color)':'#333333')+';'}}">{{''+item.$orig.text+''}}</view></block></view></view></block></view><view class="tab-bar-placeholder"></view></view></block>