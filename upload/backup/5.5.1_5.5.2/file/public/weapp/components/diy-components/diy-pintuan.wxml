<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}"><x-skeleton vue-id="fd07f7fe-1" type="{{skeletonType}}" loading="{{loading}}" configs="{{skeletonConfig}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['diy-pintuan',value.template,value.style]}}" style="{{(warpCss)}}"><block wx:if="{{$root.g1}}"><view class="{{[value.titleStyle.style,'pintuan-head']}}" style="{{'background-image:'+('url('+$root.g2+'), linear-gradient(to right,'+value.titleStyle.bgColorStart+','+value.titleStyle.bgColorEnd+')')+';'}}"><block wx:if="{{value.titleStyle.leftStyle=='text'}}"><view class="left-text" style="{{'font-size:'+(value.titleStyle.fontSize*2+'rpx')+';'+('color:'+(value.titleStyle.textColor)+';')+('font-weight:'+(value.titleStyle.fontWeight?'bold':'')+';')}}">{{''+value.titleStyle.leftText+''}}</view></block><block wx:else><image class="left-img" src="{{$root.g3}}" mode="heightFix"></image></block><view class="head-content"><view class="img-warp"><block wx:if="{{$root.g4}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image src="{{item.g5}}" mode="aspectFill" data-event-opts="{{[['error',[['headImageError',[index]]]]]}}" binderror="__e"></image></block></block></block><block wx:else><block><image src="{{$root.g6}}" mode="aspectFill"></image><image src="{{$root.g7}}" mode="aspectFill"></image><image src="{{$root.g8}}" mode="aspectFill"></image></block></block></view><view style="{{'color:'+(value.titleStyle.textColor)+';'}}"><text>{{value.titleStyle.virtualNum||headData.pintuan_count}}</text><text>{{value.titleStyle.style=='style-2'?'人参与':'人拼团成功'}}</text></view></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="head-right" style="{{'font-size:'+(value.titleStyle.moreFontSize*2+'rpx')+';'+('color:'+(value.titleStyle.moreColor)+';')}}" bindtap="__e"><text>{{value.titleStyle.more}}</text><text class="iconfont icon-right"></text></view></view></block><block wx:if="{{value.template=='row1-of1'}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g9}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl||value.priceStyle.lineControl||value.btnStyle.control}}"><view class="content"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.groupStyle.control||value.saleStyle.control}}"><view class="tag-wrap"><block wx:if="{{value.groupStyle.control}}"><view style="{{'color:'+(value.theme=='diy'?value.groupStyle.bgColorStart:'')+';'+('border-color:'+(value.theme=='diy'?value.groupStyle.bgColorStart:'')+';')}}"><text class="iconfont icon-yonghu3" style="{{'background-color:'+(value.theme=='diy'?value.groupStyle.bgColorStart:'')+';'}}"></text><text>{{item.$orig.pintuan_num+"人团"}}</text></view></block><block wx:if="{{value.saleStyle.control}}"><view style="{{'color:'+(value.theme=='diy'?value.saleStyle.color:'')+';'+('border-color:'+(value.theme=='diy'?value.saleStyle.color:'')+';')}}"><text>{{"已拼"+item.$orig.order_num+"件"}}</text></view></block></view></block><view class="bottom-wrap"><view class="price-wrap"><block wx:if="{{value.priceStyle.mainControl}}"><view class="discount-price"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+item.g10[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+('.'+item.g11[1])+''}}</text></view></block><block wx:if="{{value.priceStyle.lineControl}}"><view class="original-price price-font" style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">{{'¥'+item.$orig.price+''}}</view></block></view><block wx:if="{{value.btnStyle.control}}"><button style="{{'background:'+(value.btnStyle.theme=='diy'?'linear-gradient(to right,'+value.btnStyle.bgColorStart+','+value.btnStyle.bgColorEnd+')':'')+';'+('color:'+(value.btnStyle.theme=='diy'?value.btnStyle.textColor:'')+';')+('border-radius:'+(value.btnStyle.aroundRadius*2+'rpx')+';')}}">{{''+value.btnStyle.text+''}}</button></block></view></view></block></view></block></block><block wx:if="{{value.template=='horizontal-slide'}}"><block wx:if="{{value.slideMode=='scroll'}}"><scroll-view class="scroll" scroll-x="{{true}}" show-scrollbar="{{false}}"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g12}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image><block wx:if="{{value.groupStyle.control&&value.style=='style-1'}}"><view class="num" style="{{'color:'+(value.theme=='diy'?value.groupStyle.color:'')+';'+('background:'+(value.theme=='diy'?'linear-gradient(to right,'+value.groupStyle.bgColorStart+','+value.groupStyle.bgColorEnd+')':'')+';')}}">{{''+item.$orig.pintuan_num+'人团'}}</view></block></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl}}"><view class="{{['content',[(value.nameLineMode=='multiple')?'multi-content':'']]}}"><block wx:if="{{value.goodsNameStyle.control&&(value.style=='style-1'||value.style=='style-3')}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.priceStyle.mainControl&&value.style!='style-3'}}"><view class="price-wrap"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+item.g13[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+('.'+item.g14[1])+''}}</text></view></block><block wx:if="{{value.style=='style-3'}}"><view class="other-info-wrap"><block wx:if="{{value.priceStyle.mainControl}}"><view class="price-wrap"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+item.g15[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+('.'+item.g16[1])+''}}</text></view></block><view class="sale-action">去参团</view></view></block><block wx:if="{{value.style=='style-2'}}"><view class="sale-num">{{"已拼"+item.$orig.sale_num+"件"}}</view></block></view></block></view></block></scroll-view></block><block wx:if="{{value.slideMode=='slide'}}"><swiper class="swiper" style="{{'height:'+(swiperHeight)+';'}}" autoplay="{{false}}"><block wx:for="{{$root.l4}}" wx:for-item="pageItem" wx:for-index="pageIndex" wx:key="pageIndex"><swiper-item class="{{['swiper-item',pageItem.g17]}}"><block wx:for="{{pageItem.l3}}" wx:for-item="item" wx:for-index="dataIndex" wx:key="dataIndex"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list.'+pageIndex+'','',dataIndex]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g18}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[dataIndex]]]]]}}" binderror="__e"></image><block wx:if="{{value.groupStyle.control&&value.style!='style-2'}}"><view class="num" style="{{'color:'+(value.theme=='diy'?value.groupStyle.color:'')+';'+('background:'+(value.theme=='diy'?'linear-gradient(to right,'+value.groupStyle.bgColorStart+','+value.groupStyle.bgColorEnd+')':'')+';')}}">{{''+item.$orig.pintuan_num+'人团'}}</view></block></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl}}"><view class="{{['content',[(value.nameLineMode=='multiple')?'multi-content':'']]}}"><block wx:if="{{value.goodsNameStyle.control&&value.style!='style-2'}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.priceStyle.mainControl}}"><view class="price-wrap"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+item.g19[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+('.'+item.g20[1])+''}}</text></view></block><block wx:if="{{value.style=='style-2'}}"><view class="sale-num">{{"已拼"+item.$orig.sale_num+"件"}}</view></block></view></block></view></block></swiper-item></block></swiper></block></block></view></x-skeleton></view></block>