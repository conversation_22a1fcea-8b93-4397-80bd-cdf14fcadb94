(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-goods-recommend"],{1463:function(o,e,t){"use strict";var r=t("677a"),l=t.n(r);l.a},"2f2e":function(o,e,t){"use strict";t.r(e);var r=t("4146"),l=t("58b1");for(var i in l)["default"].indexOf(i)<0&&function(o){t.d(e,o,(function(){return l[o]}))}(i);t("1463");var s=t("828b"),n=Object(s["a"])(l["default"],r["b"],r["c"],!1,null,"095e6f8a",null,!1,r["a"],void 0);e["default"]=n.exports},4146:function(o,e,t){"use strict";t.d(e,"b",(function(){return l})),t.d(e,"c",(function(){return i})),t.d(e,"a",(function(){return r}));var r={xSkeleton:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/x-skeleton/components/x-skeleton/x-skeleton")]).then(t.bind(null,"3268"))}},l=function(){var o=this,e=o.$createElement,t=(o._self._c,o.loading||o.list&&o.list.length),r=t?o.list.length:null,l=t&&r?o.__map(o.page,(function(e,t){var r=o.__get_orig(e),l=[o.list[t].length/3]>=1&&"flex-between",i=(o.goodsValue.goodsNameStyle.control||o.goodsValue.priceStyle.mainControl||o.goodsValue.priceStyle.lineControl||o.goodsValue.labelStyle.support)&&o.goodsValue.labelStyle.support?o.$util.img("app/component/view/goods_recommend/img/label.png"):null,s=o.__map(o.list[t],(function(e,t){var r=o.__get_orig(e),l=o.$util.img(e.goods_image,{size:"mid"}),i=(o.goodsValue.goodsNameStyle.control||o.goodsValue.priceStyle.mainControl||o.goodsValue.priceStyle.lineControl||o.goodsValue.labelStyle.support)&&o.goodsValue.priceStyle.mainControl?o.showPrice(e).split("."):null,s=(o.goodsValue.goodsNameStyle.control||o.goodsValue.priceStyle.mainControl||o.goodsValue.priceStyle.lineControl||o.goodsValue.labelStyle.support)&&o.goodsValue.priceStyle.mainControl?o.showPrice(e).split("."):null,n=o.goodsValue.goodsNameStyle.control||o.goodsValue.priceStyle.mainControl||o.goodsValue.priceStyle.lineControl||o.goodsValue.labelStyle.support?o.goodsValue.priceStyle.lineControl&&o.showMarketPrice(e):null,a=(o.goodsValue.goodsNameStyle.control||o.goodsValue.priceStyle.mainControl||o.goodsValue.priceStyle.lineControl||o.goodsValue.labelStyle.support)&&n?o.showMarketPrice(e):null;return{$orig:r,g3:l,g5:i,g6:s,m0:n,m1:a}}));return{$orig:r,g2:l,g4:i,l0:s}})):null;o.$mp.data=Object.assign({},{$root:{g0:t,g1:r,l1:l}})},i=[]},4486:function(o,e,t){"use strict";(function(o){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t={name:"diy-goods-recommend",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{loading:!0,skeletonConfig:{gridRows:1,gridColumns:3,headWidth:"200rpx",headHeight:"200rpx",textRows:2,textWidth:["100%","60%"]},list:[],goodsValue:{},page:1}},created:function(){this.goodsValue=this.value,this.getGoodsList()},watch:{"globalStoreInfo.store_id":{handler:function(o,e){o!=e&&this.getGoodsList()},deep:!0},componentRefresh:function(o){this.getGoodsList()}},computed:{goodsListWarpCss:function(){var o="";return o+="background-color:"+this.goodsValue.componentBgColor+";","round"==this.goodsValue.componentAngle&&(o+="border-top-left-radius:"+2*this.goodsValue.topAroundRadius+"rpx;",o+="border-top-right-radius:"+2*this.goodsValue.topAroundRadius+"rpx;",o+="border-bottom-left-radius:"+2*this.goodsValue.bottomAroundRadius+"rpx;",o+="border-bottom-right-radius:"+2*this.goodsValue.bottomAroundRadius+"rpx;"),this.goodsValue.bgUrl&&(o+="background-image: url('".concat(this.$util.img(this.goodsValue.bgUrl),"');")),o},goodsItemCss:function(){var e="";e+="background-color:"+this.value.elementBgColor+";","round"==this.goodsValue.elementAngle&&(e+="border-top-left-radius:"+2*this.goodsValue.topElementAroundRadius+"rpx;",e+="border-top-right-radius:"+2*this.goodsValue.topElementAroundRadius+"rpx;",e+="border-bottom-left-radius:"+2*this.goodsValue.bottomElementAroundRadius+"rpx;",e+="border-bottom-right-radius:"+2*this.goodsValue.bottomElementAroundRadius+"rpx;"),"shadow"==this.goodsValue.ornament.type&&(e+="box-shadow:0 0 10rpx "+this.goodsValue.ornament.color+";"),"stroke"==this.goodsValue.ornament.type&&(e+="border:2rpx solid "+this.goodsValue.ornament.color+";");var t=o.getSystemInfoSync().windowWidth,r="";return r="style-2"!=this.goodsValue.style?[t-2*this.rpxUpPx(20)-3*this.rpxUpPx(200)-2*this.rpxUpPx(2*this.value.margin.both)]/6:[t-2*this.rpxUpPx(20)-2*this.rpxUpPx(20)-3*this.rpxUpPx(200)-2*this.rpxUpPx(2*this.value.margin.both)]/6,e+="margin-left:"+r+"px;",e+="margin-right:"+r+"px;",e},swiperHeight:function(){return"style-3"==this.goodsValue.style?"330rpx":"style-2"!=this.goodsValue.style?"multiple"==this.value.nameLineMode?"348rpx":"312rpx":"multiple"==this.value.nameLineMode?"360rpx":"320rpx"}},methods:{rpxUpPx:function(e){var t=o.getSystemInfoSync().windowWidth,r=t*parseInt(e)/750;return Math.floor(r)},getGoodsList:function(){var o=this,e={num:this.goodsValue.count};"category"==this.goodsValue.sources?(e.category_id=this.goodsValue.categoryId,e.category_level=1):"diy"==this.goodsValue.sources&&(e.num=0,e.goods_id_arr=this.goodsValue.goodsId.toString()),e.order=this.goodsValue.sortWay,this.$api.sendRequest({url:"/api/goodssku/components",data:e,success:function(e){if(0==e.code&&e.data){var t=e.data;o.list=t;var r=[];o.page=Math.ceil(o.list.length/3);for(var l=0;l<o.page;l++){r[l]=[];for(var i=3*l;i<o.list.length;i++){if(3==r[l].length)break;r[l].push(o.list[i])}}o.list=r}o.loading=!1}})},toDetail:function(o){this.$util.redirectTo("/pages/goods/detail",{goods_id:o.goods_id})},imgError:function(o,e){this.list[o][e].goods_image=this.$util.getDefaultImage().goods},showPrice:function(o){var e=o.discount_price;return o.member_price&&parseFloat(o.member_price)<parseFloat(e)&&(e=o.member_price),e},showMarketPrice:function(o){var e=this.showPrice(o);return o.market_price>0?o.market_price:o.price>e?o.price:""}}};e.default=t}).call(this,t("df3c")["default"])},"58b1":function(o,e,t){"use strict";t.r(e);var r=t("4486"),l=t.n(r);for(var i in r)["default"].indexOf(i)<0&&function(o){t.d(e,o,(function(){return r[o]}))}(i);e["default"]=l.a},"677a":function(o,e,t){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-goods-recommend-create-component',
    {
        'components/diy-components/diy-goods-recommend-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("2f2e"))
        })
    },
    [['components/diy-components/diy-goods-recommend-create-component']]
]);
