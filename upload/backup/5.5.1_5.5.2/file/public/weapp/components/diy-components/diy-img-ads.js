(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-img-ads"],{"0b8e":function(i,t,e){"use strict";e.r(t);var n=e("0f0a"),r=e("58f6");for(var a in r)["default"].indexOf(a)<0&&function(i){e.d(t,i,(function(){return r[i]}))}(a);e("762a");var o=e("828b"),u=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"29ecd1d6",null,!1,n["a"],void 0);t["default"]=u.exports},"0f0a":function(i,t,e){"use strict";e.d(t,"b",(function(){return n})),e.d(t,"c",(function(){return r})),e.d(t,"a",(function(){}));var n=function(){var i=this,t=i.$createElement,e=(i._self._c,i.imgAdsValue.list.length),n=1==e?i.$util.img(i.imgAdsValue.list[0].imageUrl):null,r=1!=e?i.__map(i.imgAdsValue.list,(function(t,e){var n=i.__get_orig(t),r=t.imageUrl?i.$util.img(t.imageUrl):null;return{$orig:n,g2:r}})):null,a=i.imgAdsValue.list.length>1&&i.value.indicatorIsShow,o=a?i.__map(i.imgAdsValue.list.length,(function(t,e){var n=i.__get_orig(t),r=i.__get_style([e==i.swiperIndex&&{backgroundColor:i.imgAdsValue.indicatorColor}]);return{$orig:n,s0:r}})):null;i._isMounted||(i.e0=function(t){return i.$util.diyRedirectTo(i.imgAdsValue.list[0].link)},i.e1=function(t,e){var n=arguments[arguments.length-1].currentTarget.dataset,r=n.eventParams||n["event-params"];e=r.item;return i.$util.diyRedirectTo(e.link)}),i.$mp.data=Object.assign({},{$root:{g0:e,g1:n,l0:r,g3:a,l1:o}})},r=[]},"58f6":function(i,t,e){"use strict";e.r(t);var n=e("ea65"),r=e.n(n);for(var a in n)["default"].indexOf(a)<0&&function(i){e.d(t,i,(function(){return n[i]}))}(a);t["default"]=r.a},"66af":function(i,t,e){},"762a":function(i,t,e){"use strict";var n=e("66af"),r=e.n(n);r.a},ea65:function(i,t,e){"use strict";(function(i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e={name:"diy-img-ads",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{isDots:!0,swiperHeight:0,imgAdsValue:null,swiperIndex:0}},created:function(){this.calcSingleRow()},watch:{componentRefresh:function(i){this.calcSingleRow()}},computed:{imgAdsMarginWarp:function(){var i;return i="background-color:"+this.value.componentBgColor+";",i},imgAdsSwiper:function(){var i="";return"round"==this.value.componentAngle&&(i+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",i+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",i+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",i+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),i},singleGraphBg:function(){for(var i=[],t=0;t<this.imgAdsValue.list.length;t++){var e=this.imgAdsValue.list[t];i[t]=parseFloat(e.imgHeight)}i.sort((function(i,t){return t-i}));var n="";return n+="background-color:"+this.imgAdsValue.backgroundColor+";",n+="height:"+i[0]*(this.imgAdsValue.backgroundHeight/100)*2+"rpx;",n}},methods:{swiperChange:function(i){this.swiperIndex=i.detail.current},calcSingleRow:function(){var t=this,e=0,n=i.getSystemInfoSync();this.imgAdsValue=JSON.parse(JSON.stringify(this.value)),this.imgAdsValue.list.forEach((function(i,r){var a=i.imgHeight/i.imgWidth;i.imgWidth=n.windowWidth,i.imgWidth-=2*t.value.margin.both,i.imgHeight=i.imgWidth*a,(0==e||e>i.imgHeight)&&(e=i.imgHeight)})),this.imgAdsValue.list.forEach((function(i,n){i.imgHeight=e+"px",t.swiperHeight=e+"px"})),this.imgAdsValue.indicatorColor=this.imgAdsValue.indicatorColor||"#fff",void 0===this.value.indicatorIsShow&&(this.value.indicatorIsShow=!0),this.imgAdsValue.list.length<=1&&(this.isDots=!1),this.isDots=!1}}};t.default=e}).call(this,e("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-img-ads-create-component',
    {
        'components/diy-components/diy-img-ads-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("0b8e"))
        })
    },
    [['components/diy-components/diy-img-ads-create-component']]
]);
