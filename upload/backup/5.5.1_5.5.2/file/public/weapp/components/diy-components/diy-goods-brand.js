(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-goods-brand"],{"0eab":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={xSkeleton:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/x-skeleton/components/x-skeleton/x-skeleton")]).then(n.bind(null,"3268"))}},r=function(){var t=this,e=t.$createElement,n=(t._self._c,t.loading||t.list&&t.list.length),o=n?t.__map(t.list,(function(e,n){var o=t.__get_orig(e),r=t.$util.img(e.image_url);return{$orig:o,g1:r}})):null;t.$mp.data=Object.assign({},{$root:{g0:n,l0:o}})},i=[]},"20a3":function(t,e,n){"use strict";n.r(e);var o=n("c4ca"),r=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=r.a},"350a":function(t,e,n){"use strict";n.r(e);var o=n("0eab"),r=n("20a3");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("f16a");var a=n("828b"),u=Object(a["a"])(r["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=u.exports},a78d:function(t,e,n){},c4ca:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"diy-goods-brand",props:{value:{type:Object,default:function(){return{}}}},components:{uniGrid:function(){n.e("components/uni-grid/uni-grid").then(function(){return resolve(n("cf69"))}.bind(null,n)).catch(n.oe)},uniGridItem:function(){n.e("components/uni-grid-item/uni-grid-item").then(function(){return resolve(n("0466"))}.bind(null,n)).catch(n.oe)}},data:function(){return{list:[],loading:!0,skeletonConfig:{gridRows:2,gridColumns:4,gridRowsGap:"20rpx",headWidth:"120rpx",headHeight:"120rpx",textShow:!1}}},created:function(){this.getBrandList()},watch:{componentRefresh:function(t){this.getBrandList()}},computed:{warpCss:function(){var t="";return t+="background-color:"+this.value.componentBgColor+";","round"==this.value.componentAngle&&(t+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),"shadow"==this.value.ornament.type&&(t+="box-shadow:0 0 10rpx "+this.value.ornament.color),"stroke"==this.value.ornament.type&&(t+="border:2rpx solid "+this.value.ornament.color),t},itemCss:function(){var t="";return"round"==this.value.elementAngle&&(t+="border-top-left-radius:"+2*this.value.topElementAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topElementAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomElementAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomElementAroundRadius+"rpx;"),t}},methods:{getBrandList:function(){var t=this,e={page:1,page_size:this.value.count};"diy"==this.value.sources&&(e.page_size=0,e.brand_id_arr=this.value.brandIds.toString()),this.$api.sendRequest({url:"/api/goodsbrand/page",data:e,success:function(e){if(0==e.code&&e.data){var n=e.data;t.list=n.list}t.loading=!1}})},toDetail:function(t){this.$util.redirectTo("/pages/goods/list",{brand_id:t.brand_id})},imgError:function(t){this.list[t]&&(this.list[t].image_url=this.$util.getDefaultImage().goods)}}};e.default=o},f16a:function(t,e,n){"use strict";var o=n("a78d"),r=n.n(o);r.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-goods-brand-create-component',
    {
        'components/diy-components/diy-goods-brand-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("350a"))
        })
    },
    [['components/diy-components/diy-goods-brand-create-component']]
]);
