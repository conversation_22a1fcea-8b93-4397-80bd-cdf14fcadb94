<view style="{{(value.pageStyle)}}"><view class="diy-notice"><view class="{{['notice',value.contentStyle]}}" style="{{(noticeWrapCss)}}"><block wx:if="{{value.iconType=='img'}}"><image class="notice-img" src="{{$root.g0}}" mode="heightFix"></image></block><block wx:if="{{value.iconType=='icon'}}"><diy-icon style="{{'max-width:'+(30*2+'rpx')+';'+('max-height:'+(30*2+'rpx')+';')+('width:'+('100%')+';')+('height:'+('100%')+';')}}" vue-id="d013a6c0-1" icon="{{value.icon}}" value="{{value.style?value.style:'null'}}" bind:__l="__l"></diy-icon></block><view class="notice-xian"></view><view class="main-wrap"><block wx:if="{{value.scrollWay=='horizontal'}}"><view class="horizontal-wrap"><view class="marquee-wrap"><view class="marquee" style="{{(marqueeStyle)}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['toLink',['$0'],[[['list','',index]]]]]]]}}" style="{{'color:'+(value.textColor)+';'+('font-size:'+(value.fontSize*2+'rpx')+';')+('font-weight:'+(value.fontWeight)+';')}}" bindtap="__e">{{item.title}}</text></block></view><view class="marquee" style="{{(marqueeAgainStyle)}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['toLink',['$0'],[[['list','',index]]]]]]]}}" style="{{'color:'+(value.textColor)+';'+('font-size:'+(value.fontSize*2+'rpx')+';')+('font-weight:'+(value.fontWeight)+';')}}" bindtap="__e">{{item.title}}</text></block></view></view></view></block><block wx:if="{{value.scrollWay=='upDown'}}"><swiper vertical="{{true}}" duration="{{500}}" autoplay="true" circular="true"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><text data-event-opts="{{[['tap',[['toLink',['$0'],[[['list','',index]]]]]]]}}" class="beyond-hiding using-hidden" style="{{'color:'+(value.textColor)+';'+('font-size:'+(value.fontSize*2+'rpx')+';')+('font-weight:'+(value.fontWeight)+';')}}" bindtap="__e">{{''+item.title+''}}</text></swiper-item></block></swiper></block></view></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><uni-popup class="vue-ref" vue-id="d013a6c0-2" type="center" data-ref="noticePopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="notice-popup"><view data-event-opts="{{[['tap',[['closeNoticePopup',['$event']]]]]}}" class="head-wrap" bindtap="__e"><text>公告</text><text class="iconfont icon-close"></text></view><view class="content-wrap">{{notice}}</view><button type="primary" data-event-opts="{{[['tap',[['closeNoticePopup',['$event']]]]]}}" bindtap="__e">我知道了</button></view></uni-popup></view></view></view>