<view style="{{(value.pageStyle)}}"><x-skeleton vue-id="dc243ac8-1" type="banner" loading="{{loading}}" configs="{{skeletonConfig}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="diy-store-label"><block wx:if="{{businessConfig.store_business=='store'}}"><block><scroll-view class="{{[value.contentStyle,[($root.g0==3)?'between':'']]}}" style="{{(storeLabelWrapCss)}}" scroll-x="true" enable-flex="{{true}}"><block wx:for="{{storeLabel}}" wx:for-item="item" wx:for-index="index"><view class="{{['item']}}"><block wx:if="{{value.icon}}"><diy-icon class="icon-box" vue-id="{{('dc243ac8-2-'+index)+','+('dc243ac8-1')}}" icon="{{value.icon}}" value="{{value.style?value.style:'null'}}" bind:__l="__l"></diy-icon></block><text class="label-name" style="{{'color:'+(value.textColor)+';'+('font-size:'+(value.fontSize*2+'rpx')+';')+('font-weight:'+(value.fontWeight)+';')}}">{{item}}</text></view></block></scroll-view></block></block><block wx:else><block><scroll-view class="{{[value.contentStyle,[($root.g1==3)?'between':'']]}}" style="{{(storeLabelWrapCss)}}" scroll-x="true" enable-flex="{{true}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index"><view class="{{['item']}}"><block wx:if="{{value.icon}}"><diy-icon class="icon-box" vue-id="{{('dc243ac8-3-'+index)+','+('dc243ac8-1')}}" icon="{{value.icon}}" value="{{value.style?value.style:'null'}}" bind:__l="__l"></diy-icon></block><text class="label-name" style="{{'color:'+(value.textColor)+';'+('font-size:'+(value.fontSize*2+'rpx')+';')+('font-weight:'+(value.fontWeight)+';')}}">{{item.label_name}}</text></view></block></scroll-view></block></block></view></x-skeleton></view>