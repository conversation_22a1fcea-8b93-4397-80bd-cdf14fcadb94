<view class="{{['category-page-wrap','category-template-'+value.template]}}" style="{{'height:'+('calc(100vh - '+tabBarHeight+')')+';'}}"><block wx:if="{{value.template==4}}"><block><block wx:if="{{value.search}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="search-box" style="{{(navbarInnerStyle)}}" bindtap="__e"><view class="search-content"><input class="uni-input font-size-tag" type="text" maxlength="50" placeholder="商品搜索" confirm-type="search" readonly="true"/><text class="iconfont icon-sousuo3"></text></view></view></block><block wx:if="{{!value.search}}"><view style="{{(navbarInnerStyle)}}">商品分类</view></block></block></block><block wx:if="{{value.template!=4}}"><block><view style="{{(navbarInnerStyle)}}">商品分类</view><block wx:if="{{value.search}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="search-box" style="{{(wxSearchHeight)}}" bindtap="__e"><view class="search-content"><input class="uni-input" type="text" maxlength="50" placeholder="商品搜索" confirm-type="search" readonly="true"/><text class="iconfont icon-sousuo3"></text></view></view></block></block></block><block wx:if="{{value.template==4}}"><view class="template-four wx"><scroll-view class="template-four-wrap" scroll-x="true" scroll-with-animation="{{true}}" scroll-into-view="{{'category-one-'+oneCategorySelect}}" enable-flex="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['category-item',(oneCategorySelect==index)?'select':'']}}" id="{{'category-one-'+index}}" data-event-opts="{{[['tap',[['templateFourOneFn',[index]]]]]}}" bindtap="__e"><view class="{{['image-warp',[(oneCategorySelect==index)?'color-base-border':'']]}}"><image src="{{item.g0}}" mode="aspectFill"></image></view><view class="{{['text',[(oneCategorySelect==index)?'color-base-bg':'']]}}">{{item.$orig.category_name}}</view></view></block></scroll-view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="category-item-all" bindtap="__e"><view class="category-item-all-wrap"><text class="text">展开</text><image class="img" src="{{$root.g1}}" mode="aspectFill"></image></view></view><uni-popup class="vue-ref" vue-id="630053ba-1" type="top" top="{{uniPopupTop}}" data-ref="templateFourPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="template-four-popup"><scroll-view class="template-four-scroll" scroll-y="true" enable-flex="true"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['templateFourOneFn',[index]]]]]}}" class="{{['item',(oneCategorySelect==index)?'selected':'']}}" bindtap="__e"><view class="{{['image-warp',[(oneCategorySelect==index)?'color-base-border':'']]}}"><image src="{{item.g2}}" mode="aspectFill"></image></view><view class="{{['text',[(oneCategorySelect==index)?'color-base-bg':'']]}}">{{item.$orig.category_name}}</view></view></block></scroll-view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="pack-up" bindtap="__e"><text>点击收起</text><text class="iconfont icon-iconangledown-copy"></text></view></view></uni-popup></view></block><block wx:if="{{categoryTree}}"><view class="content-box"><block wx:if="{{$root.g3}}"><block><scroll-view class="tree-wrap" scroll-y="true"><view class="category-item-wrap"><block wx:for="{{categoryTree}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchOneCategory',[index]]]]]}}" class="{{['category-item',[(select==index)?'select':''],[(value.template==4&&select+1===index)?'border-bottom':''],[(value.template==4&&select-1===index)?'border-top':'']]}}" bindtap="__e"><view>{{item.category_name}}</view></view></block></view></scroll-view><view class="right-flex-wrap"><block wx:if="{{value.template==1||loadType=='all'}}"><scroll-view class="content-wrap vue-ref" scroll-y="true" scroll-into-view="{{categoryId}}" scroll-with-animation="{{true}}" refresher-enabled="{{true}}" refresher-default-style="none" refresher-triggered="{{triggered}}" data-ref="contentWrap" data-event-opts="{{[['scroll',[['listenScroll',['$event']]]],['touchstart',[['touchStart',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]],['refresherrestore',[['onRestore',['$event']]]]]}}" bindscroll="__e" bindtouchstart="__e" bindrefresherrefresh="__e" bindrefresherrestore="__e"><block wx:for="{{categoryTree}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="child-category" id="{{'category-'+index}}"><diy-category-item class="vue-ref-in-for" vue-id="{{'630053ba-2-'+index}}" category="{{item}}" value="{{value}}" index="{{index}}" select="{{select}}" oneCategorySelect="{{oneCategorySelect}}" data-ref="categoryItem" data-event-opts="{{[['^tologin',[['toLogin']]],['^selectsku',[['selectSku',['$event',index]]]],['^addCart',[['addCartPoint']]],['^loadfinish',[['getHeightArea']]]]}}" bind:tologin="__e" bind:selectsku="__e" bind:addCart="__e" bind:loadfinish="__e" bind:__l="__l"></diy-category-item></view></block><view data-ref="endTips" class="end-tips vue-ref" style="{{'opacity:'+(endTips)+';'}}">已经到底了~</view></scroll-view></block><block wx:if="{{(value.template==2||value.template==3||value.template==4)&&loadType=='part'}}"><view class="content-wrap"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="child-category-wrap" style="{{'display:'+(select==index?'block':'none')+';'}}" id="{{'category-'+index}}"><diy-category-item class="vue-ref-in-for" vue-id="{{'630053ba-3-'+index}}" category="{{item.$orig}}" value="{{value}}" index="{{index}}" last="{{index==item.g4-1?true:false}}" select="{{select}}" oneCategorySelect="{{oneCategorySelect}}" data-ref="categoryItem" data-event-opts="{{[['^tologin',[['toLogin']]],['^selectsku',[['selectSku',['$event',index]]]],['^addCart',[['addCartPoint']]],['^switch',[['switchOneCategory']]]]}}" bind:tologin="__e" bind:selectsku="__e" bind:addCart="__e" bind:switch="__e" bind:__l="__l"></diy-category-item></view></block></view></block></view></block></block><block wx:else><view class="category-empty"><image src="{{$root.g5}}" mode="widthFix"></image><view class="tips">暂时没有分类哦！</view></view></block></view></block><block wx:else><view class="category-empty"><image src="{{$root.g6}}" mode="widthFix"></image><view class="tips">暂时没有分类哦！</view></view></block><block wx:if="{{$root.g7}}"><view class="cart-bottom-block"></view></block><block wx:if="{{$root.g8}}"><view class="{{['cart-box',(isIphoneX)?'active':'']}}" style="{{'bottom:'+(tabBarHeight)+';'}}"><view class="left-wrap"><view class="cart-icon vue-ref" animation="{{cartAnimation}}" data-ref="cartIcon" data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" bindtap="__e"><text class="iconfont icon-ziyuan1"></text><block wx:if="{{cartNumber}}"><view class="num">{{cartNumber<99?cartNumber:'99+'}}</view></block></view><view class="price"><text class="title">总计：</text><text class="unit font-size-tag price-font">￥</text><text class="money font-size-toolbar price-font">{{cartTotalMoney[0]}}</text><text class="unit font-size-tag price-font">{{"."+(cartTotalMoney[1]?cartTotalMoney[1]:'00')}}</text></view></view><view class="right-wrap"><button class="settlement-btn" type="primary" data-event-opts="{{[['tap',[['settlement',['$event']]]]]}}" bindtap="__e">去结算</button></view></view></block><block wx:for="{{carIconList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cart-point" style="{{'left:'+(item.left+'px')+';'+('top:'+(item.top+'px')+';')}}"></view></block><ns-goods-sku-category class="vue-ref" bind:refresh="__e" bind:addCart="__e" vue-id="630053ba-4" data-ref="skuSelect" data-event-opts="{{[['^refresh',[['refreshData']]],['^addCart',[['addCartPoint']]]]}}" bind:__l="__l"></ns-goods-sku-category></view>