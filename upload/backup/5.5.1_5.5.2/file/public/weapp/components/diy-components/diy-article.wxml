<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}"><x-skeleton vue-id="e4cd7724-1" type="list" loading="{{loading}}" configs="{{skeletonConfig}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="article-wrap" style="{{(warpCss)}}"><view class="{{['list-wrap',value.style]}}" style="{{(warpCss)}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(itemCss)}}" bindtap="__e"><view class="article-img"><image class="cover-img" src="{{item.g1}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image></view><view class="info-wrap"><text class="title">{{item.$orig.article_title}}</text><view class="read-wrap"><block wx:if="{{item.$orig.category_name}}"><block><text class="category-icon"></text><text>{{item.$orig.category_name}}</text></block></block><text class="date">{{item.g2}}</text></view></view></view></block></view></view></x-skeleton></view></block>