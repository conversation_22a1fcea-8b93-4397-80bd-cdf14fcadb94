(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-goods-list"],{"235c":function(e,o,t){"use strict";var l=t("9d91"),a=t.n(l);a.a},"88be":function(e,o,t){"use strict";t.r(o);var l=t("ef37"),a=t("d0dd");for(var i in a)["default"].indexOf(i)<0&&function(e){t.d(o,e,(function(){return a[e]}))}(i);t("235c");var n=t("828b"),s=Object(n["a"])(a["default"],l["b"],l["c"],!1,null,"48ad7b23",null,!1,l["a"],void 0);o["default"]=s.exports},"9d91":function(e,o,t){},d0dd:function(e,o,t){"use strict";t.r(o);var l=t("f6b5"),a=t.n(l);for(var i in l)["default"].indexOf(i)<0&&function(e){t.d(o,e,(function(){return l[e]}))}(i);o["default"]=a.a},ef37:function(e,o,t){"use strict";t.d(o,"b",(function(){return a})),t.d(o,"c",(function(){return i})),t.d(o,"a",(function(){return l}));var l={xSkeleton:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/x-skeleton/components/x-skeleton/x-skeleton")]).then(t.bind(null,"3268"))},diyIcon:function(){return t.e("components/diy-components/diy-icon").then(t.bind(null,"a68f"))}},a=function(){var e=this,o=e.$createElement,t=(e._self._c,e.loading||e.list&&e.list.length),l=t&&"horizontal-slide"!=e.goodsValue.template?e.__map(e.list,(function(o,t){var l=e.__get_orig(o),a=e.$util.img(o.goods_image,{size:"large-mode"==e.goodsValue.template?"big":"mid"}),i=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl||e.goodsValue.btnStyle.control)&&e.goodsValue.priceStyle.mainControl?e.showPrice(o).split("."):null,n=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl||e.goodsValue.btnStyle.control)&&e.goodsValue.priceStyle.mainControl?e.showPrice(o).split("."):null,s=e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl||e.goodsValue.btnStyle.control?o.member_price&&o.member_price==e.showPrice(o)||1==o.promotion_type:null,r=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl||e.goodsValue.btnStyle.control)&&s?o.member_price&&o.member_price==e.showPrice(o):null,d=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl||e.goodsValue.btnStyle.control)&&s&&r?e.$util.img("public/uniapp/index/VIP.png"):null,u=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl||e.goodsValue.btnStyle.control)&&s&&!r&&1==o.promotion_type?e.$util.img("public/uniapp/index/discount.png"):null,g=e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl||e.goodsValue.btnStyle.control?e.goodsValue.priceStyle.lineControl&&e.showMarketPrice(o):null,c=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl||e.goodsValue.btnStyle.control)&&g?e.showMarketPrice(o):null;return{$orig:l,g1:a,g2:i,g3:n,m0:s,m1:r,g4:d,g5:u,m2:g,m3:c}})):null,a=t&&"horizontal-slide"==e.goodsValue.template&&"scroll"==e.goodsValue.slideMode?e.__map(e.list,(function(o,t){var l=e.__get_orig(o),a=e.$util.img(o.goods_image,{size:"mid"}),i=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&e.goodsValue.priceStyle.mainControl?e.showPrice(o).split("."):null,n=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&e.goodsValue.priceStyle.mainControl?e.showPrice(o).split("."):null,s=e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl?o.member_price&&o.member_price==e.showPrice(o)||1==o.promotion_type:null,r=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&s?o.member_price&&o.member_price==e.showPrice(o):null,d=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&s&&r?e.$util.img("public/uniapp/index/VIP.png"):null,u=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&s&&!r&&1==o.promotion_type?e.$util.img("public/uniapp/index/discount.png"):null,g=e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl?e.goodsValue.priceStyle.lineControl&&e.showMarketPrice(o):null,c=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&g?e.showMarketPrice(o):null;return{$orig:l,g6:a,g7:i,g8:n,m4:s,m5:r,g9:d,g10:u,m6:g,m7:c}})):null,i=t&&"horizontal-slide"==e.goodsValue.template&&"slide"==e.goodsValue.slideMode?e.__map(e.list,(function(o,t){var l=e.__get_orig(o),a=e.list.length&&[e.list[t].length/3]>=1&&"flex-between",i=e.__map(e.list[t],(function(o,t){var l=e.__get_orig(o),a=e.$util.img(o.goods_image,{size:"mid"}),i=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&e.goodsValue.priceStyle.mainControl?e.showPrice(o).split("."):null,n=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&e.goodsValue.priceStyle.mainControl?e.showPrice(o).split("."):null,s=e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl?o.member_price&&o.member_price==e.showPrice(o)||1==o.promotion_type:null,r=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&s?o.member_price&&o.member_price==e.showPrice(o):null,d=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&s&&r?e.$util.img("public/uniapp/index/VIP.png"):null,u=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&s&&!r&&1==o.promotion_type?e.$util.img("public/uniapp/index/discount.png"):null,g=e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl?e.goodsValue.priceStyle.lineControl&&e.showMarketPrice(o):null,c=(e.goodsValue.goodsNameStyle.control||e.goodsValue.tag&&"hidden"!=e.goodsValue.tag.value||e.goodsValue.priceStyle.mainControl||e.goodsValue.priceStyle.lineControl)&&g?e.showMarketPrice(o):null;return{$orig:l,g12:a,g13:i,g14:n,m8:s,m9:r,g15:d,g16:u,m10:g,m11:c}}));return{$orig:l,g11:a,l2:i}})):null;e._isMounted||(e.e0=function(o,t){var l=arguments[arguments.length-1].currentTarget.dataset,a=l.eventParams||l["event-params"];t=a.item;return o.stopPropagation(),e.$refs.goodsSkuIndex.addCart(e.goodsValue.btnStyle.cartEvent,t,o)},e.e1=function(o,t){var l=arguments[arguments.length-1].currentTarget.dataset,a=l.eventParams||l["event-params"];t=a.item;return o.stopPropagation(),e.$refs.goodsSkuIndex.addCart(e.goodsValue.btnStyle.cartEvent,t,o)},e.e2=function(o,t){var l=arguments[arguments.length-1].currentTarget.dataset,a=l.eventParams||l["event-params"];t=a.item;return o.stopPropagation(),e.$refs.goodsSkuIndex.addCart(e.goodsValue.btnStyle.cartEvent,t,o)},e.e3=function(o,t){var l=arguments[arguments.length-1].currentTarget.dataset,a=l.eventParams||l["event-params"];t=a.item;return o.stopPropagation(),e.$refs.goodsSkuIndex.addCart(e.goodsValue.btnStyle.cartEvent,t,o)}),e.$mp.data=Object.assign({},{$root:{g0:t,l0:l,l1:a,l3:i}})},i=[]},f6b5:function(e,o,t){"use strict";(function(e){Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l={name:"diy-goods-list",components:{nsGoodsSkuIndex:function(){t.e("components/ns-goods-sku/ns-goods-sku-index").then(function(){return resolve(t("0c16"))}.bind(null,t)).catch(t.oe)}},props:{value:{type:Object,default:function(){return{}}},index:{type:Number,default:0},scrollTop:{type:Number,default:0},refresh:{type:Boolean,default:!1}},data:function(){return{loading:!0,skeletonType:"",skeletonConfig:{},list:[],goodsValue:{},page:1,params:{page:1,page_size:12,num:0,repeat_flag:!1,end:!1},id:0,carIconList:{},cartAnimation:{},isRefresh:!1}},created:function(){this.goodsValue=this.value,this.id=this.$util.generateUUID(),this.initSkeleton(),this.isRefresh=this.refresh},watch:{isRefresh:function(e,o){this.init()},"globalStoreInfo.store_id":{handler:function(e,o){e!=o&&this.init()},deep:!0},scrollTop:function(o){var t=this;if(!this.params.repeat_flag&&!this.params.end){var l=e.getSystemInfoSync(),a=l.windowHeight;if(Object.keys(this.goodsValue).length&&"horizontal-slide"!=this.goodsValue.template){var i=e.createSelectorQuery().in(this);i.select("#goods-list-"+this.id).boundingClientRect((function(e){if(e&&e.top<=a){if(t.params.repeat_flag||t.params.end)return;t.params.page+=1,t.getGoodsList()}})).exec()}}},componentRefresh:function(e){this.goodsValue=this.value,this.initSkeleton(),this.init()}},computed:{goodsListWarpCss:function(){var e="";return e+="background-color:"+this.goodsValue.componentBgColor+";","round"==this.goodsValue.componentAngle&&(e+="border-top-left-radius:"+2*this.goodsValue.topAroundRadius+"rpx;",e+="border-top-right-radius:"+2*this.goodsValue.topAroundRadius+"rpx;",e+="border-bottom-left-radius:"+2*this.goodsValue.bottomAroundRadius+"rpx;",e+="border-bottom-right-radius:"+2*this.goodsValue.bottomAroundRadius+"rpx;"),e},goodsItemCss:function(){var o="";o+="background-color:"+this.goodsValue.elementBgColor+";","round"==this.goodsValue.elementAngle&&(o+="border-top-left-radius:"+2*this.goodsValue.topElementAroundRadius+"rpx;",o+="border-top-right-radius:"+2*this.goodsValue.topElementAroundRadius+"rpx;",o+="border-bottom-left-radius:"+2*this.goodsValue.bottomElementAroundRadius+"rpx;",o+="border-bottom-right-radius:"+2*this.goodsValue.bottomElementAroundRadius+"rpx;"),"shadow"==this.goodsValue.ornament.type&&(o+="box-shadow:0 0 10rpx "+this.goodsValue.ornament.color+";"),"stroke"==this.goodsValue.ornament.type&&(o+="border:2rpx solid "+this.goodsValue.ornament.color+";");var t=e.getSystemInfoSync().windowWidth;if("horizontal-slide"==this.value.template){var l="";l="scroll"==this.value.slideMode&&"diy"==this.value.goodsMarginType?this.rpxUpPx(2*this.value.goodsMarginNum):[t-2*this.rpxUpPx(20)-3*this.rpxUpPx(200)-2*this.rpxUpPx(2*this.value.margin.both)]/6,o+="margin-left:"+l+"px;",o+="margin-right:"+l+"px;"}return o},swiperHeight:function(){return"multiple"==this.value.nameLineMode?"shadow"==this.value.ornament.type?"414rpx":"402rpx":"shadow"==this.value.ornament.type?"370rpx":"358rpx"},cartPosition:function(){return this.$store.state.cartPosition}},methods:{scrollX:function(o){var t=this,l=o.detail.scrollLeft;if(!this.params.repeat_flag&&!this.params.end){var a=e.createSelectorQuery().in(this);a.select("#scrollX-"+this.id).boundingClientRect((function(e){var o=e.width*(t.list.length/3-1)-t.rpxToPx(298);if(l>=o){if(t.params.repeat_flag||t.params.end)return;t.params.page+=1,t.getGoodsList()}})).exec()}},swiperChange:function(e){var o=this,t=e.detail.current;this.params.repeat_flag||this.params.end||t==this.list.length-2&&this.$nextTick((function(){o.params.page+=1,o.getGoodsList()}))},rpxToPx:function(o){var t=e.getSystemInfoSync().screenWidth;return t*Number.parseInt(o)/750},initSkeleton:function(){"row1-of1"==this.goodsValue.template?(this.skeletonType="list",this.skeletonConfig={}):"row1-of2"==this.goodsValue.template?(this.skeletonType="waterfall",this.skeletonConfig={headHeight:"320rpx",textRows:2,textWidth:["100%","80%"]}):"row1-of3"==this.goodsValue.template?(this.skeletonType="waterfall",this.skeletonConfig={gridColumns:3,headHeight:"200rpx",textRows:2,textWidth:["100%","80%"]}):"horizontal-slide"==this.goodsValue.template?(this.skeletonType="waterfall",this.skeletonConfig={gridRows:1,gridColumns:3,headHeight:"200rpx",textRows:2,textWidth:["100%","80%"]}):"large-mode"==this.goodsValue.template&&(this.skeletonType="list",this.skeletonConfig={itemDirection:"column",headWidth:"100%",headHeight:"320rpx",textRows:2,textWidth:["100%","80%"]})},rpxUpPx:function(o){var t=e.getSystemInfoSync().windowWidth,l=t*parseInt(o)/750;return Math.floor(l)},init:function(){this.params={page:1,page_size:12,num:0,repeat_flag:!1,end:!1},this.getGoodsList()},getGoodsList:function(){var o=this;this.params.repeat_flag=!0,this.params.num=this.goodsValue.count,"category"==this.goodsValue.sources?(this.params.category_id=this.goodsValue.categoryId,this.params.category_level=1):"diy"==this.goodsValue.sources&&(this.params.num=0,this.params.goods_id_arr=this.goodsValue.goodsId.toString()),this.params.order=this.goodsValue.sortWay,this.$api.sendRequest({url:"/api/goodssku/pageComponents",data:this.params,success:function(t){if(0==t.code&&t.data){var l=t.data.list,a=l.map((function(e){return e.id=o.genNonDuplicate(),e}));if(1==o.params.page&&(o.list=[]),"horizontal-slide"==o.goodsValue.template&&"slide"==o.goodsValue.slideMode){if(o.params.num){var i=o.params.num-3*o.list.length;a=i>0?a.splice(0,i):[]}var n=[];o.page=Math.ceil(a.length/3);for(var s=0;s<o.page;s++){n[s]=[];for(var r=3*s;r<a.length;r++){if(3==n[s].length)break;n[s].push(a[r])}}o.list=o.list.concat(n)}else{if(o.params.num){var d=o.params.num-o.list.length;a=d>0?a.splice(0,d):[]}o.list=o.list.concat(a)}a.length<o.params.page_size&&(o.params.end=!0)}o.params.repeat_flag=!1,o.loading=!1,"horizontal-slide"!=o.goodsValue.template&&1==o.params.page&&o.$nextTick((function(){var t=e.getSystemInfoSync(),l=t.windowHeight,a=e.createSelectorQuery().in(o);a.select("#goods-list-"+o.id).boundingClientRect((function(e){e&&e.top<=l&&(o.params.page+=1,o.getGoodsList())})).exec()}))}})},toDetail:function(e){this.$util.redirectTo("/pages/goods/detail",{goods_id:e.goods_id})},imgError:function(e){this.list[e]&&(this.list[e].goods_image=this.$util.getDefaultImage().goods)},showPrice:function(e){var o=e.discount_price;return e.member_price&&parseFloat(e.member_price)<parseFloat(o)&&(o=e.member_price),o},showMarketPrice:function(e){var o=this.showPrice(e);return e.market_price>0?e.market_price:parseFloat(e.price)>parseFloat(o)?e.price:""},addCartPoint:function(o){var t=this;if(this.cartPosition){var l=e.createSelectorQuery().in(this);l.select("#"+o+" .click-event").boundingClientRect((function(e){if(e){var o=e.left,l=e.top;if(o<t.cartPosition.left)var a=[{x:o,y:l},{x:o+50,y:l-150},{x:t.cartPosition.left,y:t.cartPosition.top}];else a=[{x:o,y:l},{x:o-50,y:l-150},{x:t.cartPosition.left,y:t.cartPosition.top}];var i=(new Date).getTime();t.$set(t.carIconList,i,{left:o,top:l,index:0,bezierPos:t.$util.bezier(a,6).bezier_points,timer:null}),t.startAnimation(i)}})).exec()}},startAnimation:function(o){var t=this,l=this.carIconList[o].bezierPos,a=this.carIconList[o].index;this.carIconList[o].timer=setInterval((function(){if(a<6)t.carIconList[o].left=l[a].x,t.carIconList[o].top=l[a].y,a++;else{clearInterval(t.carIconList[o].timer),delete t.carIconList[o],t.$forceUpdate();var i=e.createAnimation({duration:200,timingFunction:"ease"});i.scale(1.2).step(),t.cartAnimation=i.export(),setTimeout((function(){i.scale(1).step(),t.cartAnimation=i.export()}),300)}}),50)},genNonDuplicate:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;return Number(Math.random().toString().substr(3,e)+Date.now()).toString(36)}}};o.default=l}).call(this,t("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-goods-list-create-component',
    {
        'components/diy-components/diy-goods-list-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("88be"))
        })
    },
    [['components/diy-components/diy-goods-list-create-component']]
]);
