(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-notes"],{"6bb5":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"diy-notes",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{loading:!0,skeletonConfig:{itemDirection:"column",headWidth:"100%",headHeight:"320rpx",textRows:2,textWidth:["100%","80%"]},dataList:[],giveLikeFlag:!1}},created:function(){this.getDataList()},watch:{componentRefresh:function(t){this.getDataList()}},computed:{notesItemStyle:function(){var t="";return t+="background-color:"+this.value.contentBgColor+";","round"==this.value.elementAngle&&(t+="border-top-left-radius:"+2*this.value.topElementAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topElementAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomElementAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomElementAroundRadius+"rpx;"),t}},methods:{refresh:function(){this.getDataList()},getDataList:function(){var t=this,e={num:this.value.count};"diy"==this.value.sources&&(e.num=0,e.note_id_arr=this.value.noteId.toString()),this.$api.sendRequest({url:"/notes/api/notes/lists",data:e,success:function(e){var i=e.data;if(t.dataList=[],i)for(var n=0;n<i.length;n++){var a={};a=i[n],1==i[n].cover_type?a.img=i[n].cover_img.split(","):a.img=i[n].cover_img,i[n].upload_time?a.update_time_day=t.$util.timeStampTurnTime(i[n].upload_time,"Y-m-d"):a.update_time_day=t.$util.timeStampTurnTime(i[n].create_time,"Y-m-d"),a.label=i[n].goods_highlights.split(","),t.dataList.push(a)}t.loading=!1}})},toMore:function(){this.$util.redirectTo("/pages_tool/store_notes/note_list")},toDetail:function(t){this.$util.redirectTo("/pages_tool/store_notes/note_detail",{note_id:t})},giveLike:function(t,e){var i=this;if(this.storeToken){if(this.giveLikeFlag)return!1;this.giveLikeFlag=!0;var n=1==this.dataList[e].is_dianzan?"/notes/api/record/delete":"/notes/api/record/add";this.$api.sendRequest({url:n,data:{note_id:t},success:function(t){i.giveLikeFlag=!1,0==t.code&&t.data>0?(i.noteType,i.dataList[e].dianzan_num=1==i.dataList[e].is_dianzan?i.dataList[e].dianzan_num-1:i.dataList[e].dianzan_num+1,i.dataList[e].is_dianzan=1==i.dataList[e].is_dianzan?0:1):i.$util.showToast({title:t.message})}})}else this.$refs.login.open("/pages/index/index")},imageError:function(t){this.dataList[t].img=this.$util.getDefaultImage().goods,this.$forceUpdate()}}};e.default=n},"715a":function(t,e,i){"use strict";i.r(e);var n=i("6bb5"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},b7a9:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={xSkeleton:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/x-skeleton/components/x-skeleton/x-skeleton")]).then(i.bind(null,"3268"))},nsLogin:function(){return Promise.all([i.e("common/vendor"),i.e("components/ns-login/ns-login")]).then(i.bind(null,"2910"))}},a=function(){var t=this,e=t.$createElement,i=(t._self._c,t.loading||t.dataList&&t.dataList.length),n=i?t.__map(t.dataList,(function(e,i){var n=t.__get_orig(e),a=1==e.status&&0==e.cover_type?t.$util.img(e.img):null,o=1==e.status&&0!=e.cover_type?t.__map(e.img,(function(e,i){var n=t.__get_orig(e),a=t.$util.img(e);return{$orig:n,g2:a}})):null;return{$orig:n,g1:a,l0:o}})):null;t.$mp.data=Object.assign({},{$root:{g0:i,l1:n}})},o=[]},c7ec:function(t,e,i){"use strict";i.r(e);var n=i("b7a9"),a=i("715a");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("fa09");var s=i("828b"),r=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=r.exports},d379:function(t,e,i){},fa09:function(t,e,i){"use strict";var n=i("d379"),a=i.n(n);a.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-notes-create-component',
    {
        'components/diy-components/diy-notes-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("c7ec"))
        })
    },
    [['components/diy-components/diy-notes-create-component']]
]);
