<view class="{{['item-wrap',type]}}"><block wx:if="{{$root.g0}}"><block><block wx:if="{{category.image_adv}}"><view data-event-opts="{{[['tap',[['diyRedirectTo',['$0'],['category.link_url']]]]]}}" class="category-adv" bindtap="__e"><image src="{{$root.g1}}" mode="widthFix"></image></view></block><block wx:if="{{value.level==2}}"><block><view class="category-title">{{category.category_name}}</view><view class="category-list"><block wx:for="{{$root.l0}}" wx:for-item="one" wx:for-index="oneIndex" wx:key="oneIndex"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({one:one.$orig})}}" class="category-item" bindtap="__e"><view class="img-box"><image src="{{one.g2}}" mode="widthFix"></image></view><view class="name">{{one.$orig.category_name}}</view></view></block></view></block></block><block wx:if="{{value.level==3}}"><block><block wx:for="{{$root.l2}}" wx:for-item="one" wx:for-index="oneIndex" wx:key="oneIndex"><block><view class="category-title">{{one.$orig.category_name}}</view><view class="category-list"><block wx:for="{{one.l1}}" wx:for-item="two" wx:for-index="twoIndex" wx:key="twoIndex"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({two:two.$orig})}}" class="category-item" bindtap="__e"><view class="img-box"><image src="{{two.g3}}" mode="widthFix" lazy-load="{{true}}"></image></view><view class="name">{{two.$orig.category_name}}</view></view></block></view></block></block></block></block></block></block><block wx:if="{{type=='goods'}}"><block><block wx:if="{{loadType=='part'}}"><view class="categoty-goods-wrap" style="{{('padding-top:'+(value.search?0:'20rpx'))}}"><block wx:if="{{category.child_list&&value.goodsLevel==2}}"><block><view class="screen-category-wrap"><scroll-view class="{{['screen-category',(value.template==4)?'screen-category-4':'']}}" scroll-x="true" scroll-with-animation="{{true}}" scroll-into-view="{{scrollIntoView}}"><view class="{{['item',(categoryId==-1)?'selected':'']}}" id="category-2--1" data-event-opts="{{[['tap',[['selectCategory',[-1]]]]]}}" bindtap="__e">全部</view><block wx:for="{{category.child_list}}" wx:for-item="one" wx:for-index="oneIndex" wx:key="oneIndex"><view class="{{['item',(categoryId==oneIndex)?'selected':'']}}" id="{{'category-2-'+oneIndex}}" data-event-opts="{{[['tap',[['selectCategory',[oneIndex]]]]]}}" bindtap="__e">{{''+one.category_name+''}}</view></block></scroll-view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="iconfont icon-unfold" bindtap="__e"></view></view><uni-popup class="vue-ref" vue-id="0a57d9e2-1" type="top" data-ref="screenCategoryPopup" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="screen-category-popup" bindtap="__e"><scroll-view class="{{['screen-category',(value.template==4)?'screen-category-4':'']}}" scroll-y="true"><view class="title">全部</view><block wx:for="{{category.child_list}}" wx:for-item="one" wx:for-index="oneIndex" wx:key="oneIndex"><view data-event-opts="{{[['tap',[['selectCategory',[oneIndex]]]]]}}" class="{{['item',(categoryId==oneIndex)?'selected':'']}}" bindtap="__e">{{''+one.category_name+''}}</view></block></scroll-view></view></uni-popup></block></block><scroll-view class="scroll-goods-view" scroll-y="true" lower-threshold="300" scroll-top="{{scrollTop}}" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]],['touchstart',[['touchstart',['$event']]]],['touchend',[['touchend',['$event']]]],['scroll',[['listenScroll',['$event']]]]]}}" bindscrolltolower="__e" bindtouchstart="__e" bindtouchend="__e" bindscroll="__e"><view class="goods-list" data-template="{{value.template}}"><block wx:if="{{$root.g4}}"><block><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="goods-img" bindtap="__e"><image src="{{item.m0}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image><block wx:if="{{item.$orig.label_name}}"><view class="color-base-bg goods-tag">{{item.$orig.label_name}}</view></block><block wx:if="{{item.$orig.goods_stock<=0}}"><view class="sell-out"><text class="iconfont icon-shuqing"></text></view></block></view><view class="info-wrap"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="name-wrap" bindtap="__e"><view class="goods-name">{{item.$orig.goods_name}}</view></view><view class="price-wrap"><view class="discount-price"><text class="unit price-style small">￥</text><text class="price price-style large">{{item.g5[0]}}</text><text class="unit price-style small">{{"."+item.g6[1]}}</text></view><block wx:if="{{item.m1}}"><view class="member-price-tag"><image src="{{item.g7}}" mode="widthFix"></image></view></block><block wx:else><block wx:if="{{item.$orig.promotion_type==1}}"><view class="member-price-tag"><image src="{{item.g8}}" mode="widthFix"></image></view></block></block></view><view class="footer-wrap"><view class="pro-info"><block wx:if="{{item.m2}}"><view class="delete-price font-size-activity-tag color-tip price-font"><text class="unit">￥</text><text>{{item.m3}}</text></view></block></view><block wx:if="{{value.template==2||value.template==4}}"><view class="right-wrap"><block wx:if="{{item.$orig.is_virtual}}"><block><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="color-base-bg select-sku" bindtap="__e">立即购买</view></block></block><block wx:else><block><block wx:if="{{item.$orig.goods_spec_format}}"><view data-event-opts="{{[['tap',[['selectSku',['$0'],[[['goodsList','',index]]]]]]]}}" class="color-base-bg select-sku" bindtap="__e"><text>选规格</text><block wx:if="{{item.$orig.num}}"><text class="num-tag">{{item.$orig.num}}</text></block></view></block><block wx:else><block><block wx:if="{{cartList['goods_'+item.$orig.goods_id]&&cartList['goods_'+item.$orig.goods_id]['sku_'+item.$orig.sku_id]}}"><block><view data-event-opts="{{[['tap',[['reduce',['$0'],[[['goodsList','',index]]]]]]]}}" class="num-action reduce" bindtap="__e"><text class="iconfont icon-jian"></text></view><view class="num">{{cartList['goods_'+item.$orig.goods_id]['sku_'+item.$orig.sku_id].num}}</view><view class="num-action" id="{{'cart-num-'+index}}" data-event-opts="{{[['tap',[['increase',['$event','$0'],[[['goodsList','',index]]]]]]]}}" bindtap="__e"><text class="iconfont icon-jia"></text><view class="click-event"></view></view></block></block><block wx:else><view class="num-action" id="{{'cart-num-'+index}}" data-event-opts="{{[['tap',[['increase',['$event','$0',0],[[['goodsList','',index]]]]]]]}}" bindtap="__e"><text class="iconfont icon-jia"></text><view class="click-event"></view></view></block></block></block></block></block></view></block><block wx:if="{{value.template==3}}"><view class="right-wrap"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="color-base-bg select-sku" bindtap="__e">立即购买</view></view></block></view></view></view></block></block></block><block wx:else><view class="category-empty"><image src="{{$root.g9}}" mode="widthFix"></image></view></block><block wx:if="{{$root.g10}}"><view data-ref="endTips" class="end-tips vue-ref">已经到底了~</view></block><block wx:else><view data-ref="endTips" data-event-opts="{{[['tap',[['switchCategory',['next']]]]]}}" class="end-tips vue-ref" bindtap="__e"><text class="iconfont icon-xiangshangzhanhang"></text>上滑查看下一分类</view></block></view><loading-cover class="vue-ref" vue-id="0a57d9e2-2" init-show="{{loading}}" data-ref="loadingCover" bind:__l="__l"></loading-cover></scroll-view></view></block><block wx:if="{{$root.g11}}"><block><view class="goods-list" data-template="{{value.template}}"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="goods-img" bindtap="__e"><image src="{{item.m4}}" mode="widthFix" lazy-load="{{true}}" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image><view class="color-base-bg goods-tag">{{item.$orig.label_name}}</view><block wx:if="{{item.$orig.goods_stock<=0}}"><view class="sell-out"><text class="iconfont icon-shuqing"></text></view></block></view><view class="info-wrap"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="name-wrap" bindtap="__e"><view class="goods-name">{{item.$orig.goods_name}}</view></view><view class="price-wrap"><view class="discount-price"><text class="unit price-style small">￥</text><text class="price price-style large">{{item.g12[0]}}</text><text class="unit price-style small">{{"."+item.g13[1]}}</text></view><block wx:if="{{item.m5}}"><view class="member-price-tag"><image src="{{item.g14}}" mode="widthFix"></image></view></block><block wx:else><block wx:if="{{item.$orig.promotion_type==1}}"><view class="member-price-tag"><image src="{{item.g15}}" mode="widthFix"></image></view></block></block></view><view class="footer-wrap"><view class="pro-info"><block wx:if="{{item.m6}}"><view class="delete-price font-size-activity-tag color-tip price-font"><text class="unit">￥</text><text>{{item.m7}}</text></view></block></view><block wx:if="{{value.template==2}}"><view class="right-wrap"><block wx:if="{{item.$orig.is_virtual}}"><block><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="color-base-bg select-sku" bindtap="__e">立即购买</view></block></block><block wx:else><block><block wx:if="{{item.$orig.goods_spec_format}}"><view data-event-opts="{{[['tap',[['selectSku',['$0'],[[['goodsList','',index]]]]]]]}}" class="color-base-bg select-sku" bindtap="__e"><text>选规格</text><block wx:if="{{item.$orig.num}}"><text class="num-tag">{{item.$orig.num}}</text></block></view></block><block wx:else><block><block wx:if="{{cartList['goods_'+item.$orig.goods_id]&&cartList['goods_'+item.$orig.goods_id]['sku_'+item.$orig.sku_id]}}"><block><view data-event-opts="{{[['tap',[['reduce',['$0'],[[['goodsList','',index]]]]]]]}}" class="num-action reduce" bindtap="__e"><text class="iconfont icon-jian"></text></view><view class="num">{{cartList['goods_'+item.$orig.goods_id]['sku_'+item.$orig.sku_id].num}}</view><view class="num-action" id="{{'cart-num-'+index}}" data-event-opts="{{[['tap',[['increase',['$event','$0'],[[['goodsList','',index]]]]]]]}}" bindtap="__e"><text class="iconfont icon-jia"></text><view class="click-event"></view></view></block></block><block wx:else><view class="num-action" id="{{'cart-num-'+index}}" data-event-opts="{{[['tap',[['increase',['$event','$0',0],[[['goodsList','',index]]]]]]]}}" bindtap="__e"><text class="iconfont icon-jia"></text><view class="click-event"></view></view></block></block></block></block></block></view></block><block wx:if="{{value.template==3}}"><view class="right-wrap"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="color-base-bg select-sku" bindtap="__e">立即购买</view></view></block></view></view></view></block></view></block></block></block></block></view>