(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-live"],{"0f02":function(n,e,i){"use strict";i.r(e);var t=i("b529"),o=i.n(t);for(var l in t)["default"].indexOf(l)<0&&function(n){i.d(e,n,(function(){return t[n]}))}(l);e["default"]=o.a},"1a5a1":function(n,e,i){},"20ba":function(n,e,i){"use strict";i.r(e);var t=i("d9b2"),o=i("0f02");for(var l in o)["default"].indexOf(l)<0&&function(n){i.d(e,n,(function(){return o[n]}))}(l);i("6ff7");var a=i("828b"),u=Object(a["a"])(o["default"],t["b"],t["c"],!1,null,null,null,!1,t["a"],void 0);e["default"]=u.exports},"6ff7":function(n,e,i){"use strict";var t=i("1a5a1"),o=i.n(t);o.a},b529:function(n,e,i){"use strict";(function(n){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={components:{},name:"diy-live",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{loading:!0,skeletonConfig:{headHeight:"200rpx"},liveInfo:{banner:"",anchor_img:""}}},created:function(){this.getLiveInfo()},watch:{componentRefresh:function(n){this.getLiveInfo()}},methods:{getLiveInfo:function(){var n=this;this.$api.sendRequest({url:"/live/api/live/info",success:function(e){0==e.code&&e.data?(n.liveInfo=e.data,n.getLiveStatus()):n.liveInfo=null,n.loading=!1}})},entryRoom:function(e){n.navigateTo({url:"plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=".concat(e)})},getLiveStatus:function(){var n=this,e=requirePlugin("live-player-plugin");e.getLiveStatus({room_id:this.liveInfo.roomid}).then((function(e){var i=e.liveStatus;i&&i!=n.liveInfo.live_status&&n.changeLiveStatus(i)})).catch((function(n){console.log("get live status",n)}));var i=setInterval((function(){e.getLiveStatus({room_id:n.liveInfo.roomid}).then((function(e){var t=e.liveStatus;t&&t!=n.liveInfo.live_status&&n.changeLiveStatus(t),n.$util.inArray(t,[103,104,106,107])&&clearInterval(i)})).catch((function(n){console.log("get live status",n)}))}),6e4)},changeLiveStatus:function(n){var e=this;this.$api.sendRequest({url:"/live/api/live/modifyLiveStatus",data:{room_id:this.liveInfo.roomid,status:n},success:function(n){0==n.code&&e.getLiveInfo()}})}}};e.default=i}).call(this,i("3223")["default"])},d9b2:function(n,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return l})),i.d(e,"a",(function(){return t}));var t={xSkeleton:function(){return Promise.all([i.e("common/vendor"),i.e("uni_modules/x-skeleton/components/x-skeleton/x-skeleton")]).then(i.bind(null,"3268"))}},o=function(){var n=this,e=n.$createElement,i=(n._self._c,n.liveInfo&&""!=n.liveInfo.banner?n.$util.img(n.liveInfo.banner):null),t=n.liveInfo&&""==n.liveInfo.banner?n.$util.img("public/uniapp/live/live_default_banner.png"):null,o=n.liveInfo&&(n.value.isShowAnchorInfo||n.value.isShowLiveGood)&&n.value.isShowAnchorInfo&&""!=n.liveInfo.anchor_img?n.$util.img(n.liveInfo.anchor_img):null,l=n.liveInfo&&(n.value.isShowAnchorInfo||n.value.isShowLiveGood)&&n.value.isShowAnchorInfo&&""==n.liveInfo.anchor_img?n.$util.getDefaultImage():null,a=n.liveInfo&&(n.value.isShowAnchorInfo||n.value.isShowLiveGood)&&n.value.isShowLiveGood?n.liveInfo.goods.length:null;n._isMounted||(n.e0=function(e){n.liveInfo.banner=n.$util.img("public/uniapp/live/live_default_banner.png")},n.e1=function(e){n.liveInfo.anchor_img=n.$util.getDefaultImage().head}),n.$mp.data=Object.assign({},{$root:{g0:i,g1:t,g2:o,g3:l,g4:a}})},l=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-live-create-component',
    {
        'components/diy-components/diy-live-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("20ba"))
        })
    },
    [['components/diy-components/diy-live-create-component']]
]);
