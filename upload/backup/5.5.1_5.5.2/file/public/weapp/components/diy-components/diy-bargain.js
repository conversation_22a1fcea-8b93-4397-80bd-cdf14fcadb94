(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-bargain"],{"2a5c":function(e,t,l){"use strict";(function(e){var a=l("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(l("7eb4")),o=a(l("ee10")),n={name:"diy-bargain",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{list:[],page:1,loading:!0,skeletonType:"",skeletonConfig:{}}},created:function(){var e=this;return(0,o.default)(i.default.mark((function t(){return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.initSkeleton(),"row1-of1"!=e.value.template||"style-2"!=e.value.style){t.next=4;break}return t.next=4,e.getDataing();case 4:e.getData();case 5:case"end":return t.stop()}}),t)})))()},watch:{componentRefresh:function(){var e=(0,o.default)(i.default.mark((function e(t){return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("row1-of1"!=this.value.template||"style-2"!=this.value.style){e.next=3;break}return e.next=3,this.getDataing();case 3:this.getData();case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},computed:{warpCss:function(){var e="";return this.value.componentBgColor&&(e+="background:"+this.value.componentBgColor+";"),"round"==this.value.componentAngle&&(e+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",e+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",e+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",e+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),e},goodsItemCss:function(){var t="";t+="background-color:"+this.value.elementBgColor+";","round"==this.value.elementAngle&&(t+="border-top-left-radius:"+2*this.value.topElementAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topElementAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomElementAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomElementAroundRadius+"rpx;"),"shadow"==this.value.ornament.type&&(t+="box-shadow:0 0 10rpx "+this.value.ornament.color+";"),"stroke"==this.value.ornament.type&&(t+="border:2rpx solid "+this.value.ornament.color+";");var l=e.getSystemInfoSync().windowWidth;if("horizontal-slide"==this.value.template){var a="";a="scroll"==this.value.slideMode&&"diy"==this.value.goodsMarginType?this.rpxUpPx(2*this.value.goodsMarginNum):[l-2*this.rpxUpPx(20)-3*this.rpxUpPx(200)-2*this.rpxUpPx(2*this.value.margin.both)]/6,t+="margin-left:"+a+"px;",t+="margin-right:"+a+"px;"}return t},swiperHeight:function(){return"multiple"==this.value.nameLineMode?"shadow"==this.value.ornament.type?"420rpx":"400rpx":"shadow"==this.value.ornament.type?"386rpx":"378rpx"}},methods:{initSkeleton:function(){"row1-of1"==this.value.template?(this.skeletonType="list",this.skeletonConfig={textRows:2}):"horizontal-slide"==this.value.template&&(this.skeletonType="waterfall",this.skeletonConfig={gridRows:1,gridColumns:3,headHeight:"200rpx",textRows:2,textWidth:["100%","80%"]})},rpxUpPx:function(t){var l=e.getSystemInfoSync().windowWidth,a=l*parseInt(t)/750;return Math.floor(a)},getDataing:function(){var e=this;return(0,o.default)(i.default.mark((function t(){var l;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:"/bargain/api/goods/bargainingList",data:{},async:!1});case 2:l=t.sent,l.data&&l.data.forEach((function(e,t){e.is_bargaining=1})),e.list=l.data||[],e.loading=!1;case 6:case"end":return t.stop()}}),t)})))()},getData:function(){var e=this,t={num:this.value.count,is_exclude_bargaining:1};"diy"==this.value.sources&&(t.num=0,t.id_arr=this.value.goodsId.toString()),this.$api.sendRequest({url:"/bargain/api/goods/lists",data:t,success:function(t){if(0==t.code&&("row1-of1"==e.value.template&&"style-2"==e.value.style?e.list=e.list.concat(t.data).splice(0,e.value.count):e.list=t.data,"horizontal-slide"==e.value.template&&"slide"==e.value.slideMode)){var l=[];e.page=Math.ceil(e.list.length/3);for(var a=0;a<e.page;a++){l[a]=[];for(var i=3*a;i<e.list.length;i++){if(3==l[a].length)break;l[a].push(e.list[i])}}e.list=l}e.loading=!1}})},progress:function(e){var t=((parseFloat(e.price)-parseFloat(e.curr_price))/parseFloat(e.price)*214).toFixed();return"NaN"==t&&(t=0),t},toDetail:function(e){this.$util.redirectTo("/pages_promotion/bargain/detail",{b_id:e.bargain_id})},imageError:function(e){this.list[e].goods_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}}};t.default=n}).call(this,l("df3c")["default"])},"35c4":function(e,t,l){"use strict";l.d(t,"b",(function(){return i})),l.d(t,"c",(function(){return o})),l.d(t,"a",(function(){return a}));var a={xSkeleton:function(){return Promise.all([l.e("common/vendor"),l.e("uni_modules/x-skeleton/components/x-skeleton/x-skeleton")]).then(l.bind(null,"3268"))}},i=function(){var e=this,t=e.$createElement,l=(e._self._c,e.loading||e.list&&e.list.length),a=l?e.value.titleStyle.isShow&&e.list&&e.list.length:null,i=l&&a?e.$util.img(e.value.titleStyle.backgroundImage):null,o=l&&a&&"text"!=e.value.titleStyle.leftStyle?e.$util.img(e.value.titleStyle.leftImg):null,n=l&&"row1-of1"==e.value.template?e.__map(e.list,(function(t,l){var a=e.__get_orig(t),i=e.$util.img(t.goods_image,{size:"mid"}),o=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.btnStyle.control)&&"style-2"==e.value.style?e.progress(t):null,n=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.btnStyle.control)&&"style-2"==e.value.style?e.$util.img("public/uniapp/bargain/progress_bar_01.png"):null,r=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.btnStyle.control)&&"style-2"==e.value.style&&t.is_bargaining?(t.price-t.curr_price).toFixed(2):null,u=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.btnStyle.control)&&e.value.priceStyle.mainControl?t.price.split("."):null,s=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.btnStyle.control)&&e.value.priceStyle.mainControl?t.price.split("."):null;return{$orig:a,g4:i,m0:o,g5:n,g6:r,g7:u,g8:s}})):null,r=l&&"horizontal-slide"==e.value.template&&"scroll"==e.value.slideMode?e.__map(e.list,(function(t,l){var a=e.__get_orig(t),i=e.$util.img(t.goods_image,{size:"mid"}),o=e.value.saleStyle.control&&"horizontal-slide"==e.value.template&&"style-2"!=e.value.style?e.$util.img("public/uniapp/bargain/bg.png"):null,n=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.priceStyle.lineControl)&&e.value.priceStyle.mainControl&&"horizontal-slide"==e.value.template&&"style-2"!=e.value.style?t.floor_price.split("."):null,r=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.priceStyle.lineControl)&&e.value.priceStyle.mainControl&&"horizontal-slide"==e.value.template&&"style-2"!=e.value.style?t.floor_price.split("."):null;return{$orig:a,g9:i,g10:o,g11:n,g12:r}})):null,u=l&&"horizontal-slide"==e.value.template&&"slide"==e.value.slideMode?e.__map(e.page,(function(t,l){var a=e.__get_orig(t),i=e.list.length&&[e.list[l].length/3]>=1&&"flex-between",o=e.value.saleStyle.control&&"horizontal-slide"==e.value.template&&"style-2"!=e.value.style?e.$util.img("public/uniapp/bargain/bg.png"):null,n=e.__map(e.list[l],(function(t,l){var a=e.__get_orig(t),i=e.$util.img(t.goods_image,{size:"mid"}),o=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.priceStyle.lineControl)&&e.value.priceStyle.mainControl&&"horizontal-slide"==e.value.template&&"style-2"!=e.value.style?t.floor_price.split("."):null,n=(e.value.goodsNameStyle.control||e.value.priceStyle.mainControl||e.value.priceStyle.lineControl)&&e.value.priceStyle.mainControl&&"horizontal-slide"==e.value.template&&"style-2"!=e.value.style?t.floor_price.split("."):null;return{$orig:a,g14:i,g16:o,g17:n}}));return{$orig:a,g13:i,g15:o,l2:n}})):null;e._isMounted||(e.e0=function(t){return e.$util.redirectTo("/pages_promotion/bargain/list")}),e.$mp.data=Object.assign({},{$root:{g0:l,g1:a,g2:i,g3:o,l0:n,l1:r,l3:u}})},o=[]},"690a":function(e,t,l){},"783e":function(e,t,l){"use strict";var a=l("690a"),i=l.n(a);i.a},"94ef":function(e,t,l){"use strict";l.r(t);var a=l("35c4"),i=l("a19b");for(var o in i)["default"].indexOf(o)<0&&function(e){l.d(t,e,(function(){return i[e]}))}(o);l("783e");var n=l("828b"),r=Object(n["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=r.exports},a19b:function(e,t,l){"use strict";l.r(t);var a=l("2a5c"),i=l.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){l.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-bargain-create-component',
    {
        'components/diy-components/diy-bargain-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("94ef"))
        })
    },
    [['components/diy-components/diy-bargain-create-component']]
]);
