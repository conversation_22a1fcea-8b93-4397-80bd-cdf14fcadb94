<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}" class="data-v-51fd0738"><view class="many-goods-list data-v-51fd0738"><scroll-view class="many-goods-list-head data-v-51fd0738" style="{{(manyWrapCss)}}" scroll-x="true" scroll-into-view="{{'a'+cateIndex}}"><block wx:for="{{value.list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['scroll-item','data-v-51fd0738',(index==cateIndex)?'active':'']}}" id="{{'a'+index}}" data-event-opts="{{[['tap',[['changeCateIndex',['$0',index],[[['value.list','',index]]]]]]]}}" bindtap="__e"><block wx:if="{{index>0}}"><view class="split-line data-v-51fd0738"></view></block><view class="cate data-v-51fd0738"><view class="name data-v-51fd0738" style="{{'color:'+(value.headStyle.titleColor)+';'}}">{{item.title}}</view><view class="{{['desc','data-v-51fd0738',(index==cateIndex&&item.desc)?'color-base-bg':'']}}">{{item.desc}}</view></view></view></block></scroll-view><block wx:if="{{fixedTop}}"><view class="many-goods-list-fill data-v-51fd0738" style="{{'height:'+(manyInfo.height)+';'}}"></view></block><block wx:if="{{goodsValue}}"><diy-goods-list class="many-goods-list-body data-v-51fd0738 vue-ref" vue-id="104d02a1-1" value="{{goodsValue}}" scrollTop="{{scrollTop}}" inex="{{index}}" data-ref="diyGoodsList" bind:__l="__l"></diy-goods-list></block></view></view></block>