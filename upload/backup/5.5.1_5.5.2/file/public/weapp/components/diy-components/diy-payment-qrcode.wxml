<view style="{{(value.pageStyle)}}"><view class="payment-qrocde-wrap" style="{{(warpCss)}}"><view class="payment-qrocde-box"><view class="qrocde-left"><view class="qrocde-desc"><text>门店消费时使用，支付时点击下方展示付款码</text></view><view class="qrocde-action"><button type="primary" data-event-opts="{{[['tap',[['toLink',['$event']]]]]}}" bindtap="__e"><text class="iconfont icon-fukuanma"></text><text class="action-name">付款码</text></button><button type="primary" data-event-opts="{{[['tap',[['openPaymentPopup',['$event']]]]]}}" bindtap="__e"><text class="iconfont icon-saomafu"></text><text class="action-name">扫码付</text></button></view></view><view class="qrocde-right"><text class="iconfont icon-zhifu"></text><text class="name">门店支付</text></view></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><uni-popup class="vue-ref" vue-id="28314263-1" type="center" data-ref="paymentPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="payment-popup"><view data-event-opts="{{[['tap',[['closePaymentPopup',['$event']]]]]}}" class="head-wrap" bindtap="__e"><text>提示</text><text class="iconfont icon-close"></text></view><view class="content-wrap">扫码付请退出程序后直接使用微信扫一扫或返回上一页使用付款码进行支付</view><button type="primary" data-event-opts="{{[['tap',[['closePaymentPopup',['$event']]]]]}}" bindtap="__e">我知道了</button></view></uni-popup></view></view></view>