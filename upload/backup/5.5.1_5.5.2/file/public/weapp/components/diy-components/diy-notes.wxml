<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}"><x-skeleton vue-id="d7ef81fa-1" type="list" loading="{{loading}}" configs="{{skeletonConfig}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="diy-notes" style="{{'background-color:'+(value.componentBgColor)+';'}}"><view class="diy-notes-top"><view class="notes-title" style="{{'color:'+(value.titleTextColor)+';'}}">{{value.title}}</view><view data-event-opts="{{[['tap',[['toMore']]]]}}" class="notes-more" style="{{'color:'+(value.moreTextColor)+';'}}" bindtap="__e">{{value.more}}</view></view><scroll-view class="diy-notes-box" scroll-x="true" show-scrollbar="true"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="i" wx:key="i"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',i,'note_id']]]]]]]}}" class="notes-box-item" style="{{(notesItemStyle)}}" bindtap="__e"><block wx:if="{{item.$orig.status==1}}"><view class="notes-item"><view class="notes-item-con"><view class="notes-title">{{item.$orig.note_title}}</view><block wx:if="{{value.notesLabel==1&&item.$orig.goods_highlights}}"><view class="notes-highlights-list"><block wx:for="{{item.$orig.label}}" wx:for-item="labelItem" wx:for-index="labelIndex" wx:key="labelIndex"><text class="color-base-bg">{{labelItem}}</text></block></view></block><view class="notes-intro"><text class="notes-label color-base-text">{{"#"+(item.$orig.note_type=='goods_item'?'单品介绍':'掌柜说')+"#"}}</text>{{''+item.$orig.note_abstract+''}}</view></view><view class="{{['notes-img-wrap',(item.$orig.cover_type==1)?'notes-img-wrap-list':'']}}"><block wx:if="{{item.$orig.cover_type==0}}"><image class="notes-item-image" src="{{item.g1}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[i]]]]]}}" binderror="__e"></image></block><block wx:else><block wx:for="{{item.l0}}" wx:for-item="imgItem" wx:for-index="imgIndex" wx:key="imgIndex"><image class="notes-item-image-li" src="{{imgItem.g2}}" mode="aspectFit" data-event-opts="{{[['error',[['imageError',[i]]]]]}}" binderror="__e"></image></block></block></view><view class="notes-item-con"><block wx:if="{{value.readNum==1&&item.$orig.is_show_read_num==1||value.uploadTime==1&&item.$orig.is_show_release_time==1}}"><view class="notes-info"><view class="notes-num"><text hidden="{{!(value.uploadTime==1&&item.$orig.is_show_release_time==1)}}">{{item.$orig.update_time_day}}</text></view><view class="notes-num"><text hidden="{{!(value.readNum==1&&item.$orig.is_show_read_num==1)}}">{{"阅读 "+(item.$orig.initial_read_num+item.$orig.read_num)}}</text></view></view></block></view></view></block></view></block></scroll-view><ns-login class="vue-ref" vue-id="{{('d7ef81fa-2')+','+('d7ef81fa-1')}}" data-ref="login" bind:__l="__l"></ns-login></view></x-skeleton></view></block>