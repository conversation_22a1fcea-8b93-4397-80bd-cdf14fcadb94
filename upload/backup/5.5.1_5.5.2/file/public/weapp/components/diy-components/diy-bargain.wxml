<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}"><x-skeleton vue-id="41e5dcd2-1" type="{{skeletonType}}" loading="{{loading}}" configs="{{skeletonConfig}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['diy-bargain',value.template,value.style]}}" style="{{(warpCss)}}"><block wx:if="{{$root.g1}}"><view class="{{[value.titleStyle.style,'bargain-head']}}" style="{{'background-image:'+('url('+$root.g2+'), linear-gradient(to right,'+value.titleStyle.bgColorStart+','+value.titleStyle.bgColorEnd+')')+';'}}"><block wx:if="{{value.titleStyle.leftStyle=='text'}}"><view class="left-text" style="{{'font-size:'+(value.titleStyle.fontSize*2+'rpx')+';'+('color:'+(value.titleStyle.textColor)+';')+('font-weight:'+(value.titleStyle.fontWeight?'bold':'')+';')}}">{{''+value.titleStyle.leftText+''}}</view></block><block wx:else><image class="left-img" src="{{$root.g3}}" mode="heightFix"></image></block><block wx:if="{{value.titleStyle.style=='style-1'}}"><view class="head-content" style="{{'color:'+(value.titleStyle.textColor)+';'}}">低至0元免费拿</view></block><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="head-right" style="{{'font-size:'+(value.titleStyle.moreFontSize*2+'rpx')+';'+('color:'+(value.titleStyle.moreColor)+';')}}" bindtap="__e"><text>{{value.titleStyle.more}}</text><text class="iconfont icon-right"></text></view></view></block><block wx:if="{{value.template=='row1-of1'}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g4}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl||value.btnStyle.control}}"><view class="content"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.style=='style-2'}}"><view class="progress"><view class="bg"><view class="curr" style="{{'width:'+(item.m0*2+'rpx')+';'}}"><image class="progress-bar" mode="widthFix" src="{{item.g5}}"></image></view></view><block wx:if="{{item.$orig.is_bargaining}}"><view class="num">已砍<text>{{"￥"+item.g6}}</text>，仅差<text>{{"￥"+item.$orig.curr_price}}</text></view></block><block wx:else><view class="num">最低可砍至<text>{{"￥"+item.$orig.floor_price}}</text></view></block></view></block><block wx:if="{{value.style=='style-3'}}"><view class="progress">最低可砍至<text class="num">{{"￥"+item.$orig.floor_price}}</text></view></block><view class="price-wrap"><block wx:if="{{value.priceStyle.mainControl}}"><view class="discount-price"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{item.g7[0]}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{"."+item.g8[1]}}</text></view></block><block wx:if="{{value.btnStyle.control}}"><button style="{{'background:'+(value.btnStyle.theme=='diy'?'linear-gradient(to right,'+value.btnStyle.bgColorStart+','+value.btnStyle.bgColorEnd+')':'')+';'+('color:'+(value.btnStyle.theme=='diy'?value.btnStyle.textColor:'')+';')+('border-radius:'+(value.btnStyle.aroundRadius*2+'rpx')+';')}}">{{''+(item.$orig.is_bargaining?'继续砍价':value.btnStyle.text)+''}}</button></block></view></view></block></view></block></block><block wx:if="{{value.template=='horizontal-slide'}}"><block wx:if="{{value.slideMode=='scroll'}}"><scroll-view class="scroll" scroll-x="{{true}}" show-scrollbar="{{false}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g9}}" mode="widthFix" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image><block wx:if="{{value.saleStyle.control&&value.template=='horizontal-slide'&&value.style!='style-2'}}"><image class="bg" src="{{item.g10}}" mode="widthFix"></image></block><block wx:if="{{value.saleStyle.control&&value.template=='horizontal-slide'&&value.style!='style-2'}}"><view class="num" style="{{'color:'+(value.theme=='diy'?value.saleStyle.color:'')+';'}}">{{'已砍'+item.$orig.sale_num+'件'}}</view></block></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl||value.priceStyle.lineControl}}"><view class="{{['content',[(value.nameLineMode=='multiple')?'multi-content':'']]}}"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.priceStyle.mainControl&&value.template=='horizontal-slide'&&value.style!='style-2'}}"><view class="discount-price"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{item.g11[0]}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{'.'+item.g12[1]}}</text></view></block><block wx:if="{{value.priceStyle.lineControl}}"><view class="original-price price-font" style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">{{'¥'+item.$orig.price+''}}</view></block></view></block></view></block></scroll-view></block><block wx:if="{{value.slideMode=='slide'}}"><swiper class="swiper" style="{{'height:'+(swiperHeight)+';'}}" autoplay="{{false}}"><block wx:for="{{$root.l3}}" wx:for-item="pageItem" wx:for-index="pageIndex" wx:key="pageIndex"><swiper-item class="{{['swiper-item',pageItem.g13]}}"><block wx:for="{{pageItem.l2}}" wx:for-item="item" wx:for-index="dataIndex" wx:key="dataIndex"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list.'+pageIndex+'','',dataIndex]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g14}}" mode="widthFix" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError',[dataIndex]]]]]}}" binderror="__e"></image><block wx:if="{{value.saleStyle.control&&value.template=='horizontal-slide'&&value.style!='style-2'}}"><image class="bg" src="{{pageItem.g15}}" mode="widthFix"></image></block><block wx:if="{{value.saleStyle.control&&value.template=='horizontal-slide'&&value.style!='style-2'}}"><view class="num" style="{{'color:'+(value.theme=='diy'?value.saleStyle.color:'')+';'}}">{{'已砍'+item.$orig.sale_num+'件'}}</view></block></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl||value.priceStyle.lineControl}}"><view class="{{['content',[(value.nameLineMode=='multiple')?'multi-content':'']]}}"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.priceStyle.mainControl&&value.template=='horizontal-slide'&&value.style!='style-2'}}"><view class="discount-price"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{item.g16[0]}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{'.'+item.g17[1]}}</text></view></block><block wx:if="{{value.priceStyle.lineControl}}"><view class="original-price price-font" style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">{{'¥'+item.$orig.price+''}}</view></block></view></block></view></block></swiper-item></block></swiper></block></block></view></x-skeleton></view></block>