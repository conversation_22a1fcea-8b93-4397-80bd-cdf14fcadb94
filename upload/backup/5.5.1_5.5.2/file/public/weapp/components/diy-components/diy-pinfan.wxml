<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}"><x-skeleton vue-id="07501c4c-1" type="{{skeletonType}}" loading="{{loading}}" configs="{{skeletonConfig}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['diy-pinfan',value.template,value.style]}}" style="{{(warpCss)}}"><block wx:if="{{value.template=='row1-of1'}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g1}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl||value.priceStyle.lineControl||value.btnStyle.control}}"><view class="content"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.groupStyle.control||value.saleStyle.control}}"><view class="tag-wrap"><block wx:if="{{value.groupStyle.control}}"><view style="{{'color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';'+('border-color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';')}}"><text class="iconfont icon-yonghu3" style="{{'background-color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';'}}"></text><text>{{item.$orig.pintuan_num+"人团"}}</text></view></block><block wx:if="{{value.saleStyle.control}}"><view style="{{'color:'+(value.theme=='diy'?value.saleStyle.color:'')+';'+('border-color:'+(value.theme=='diy'?value.saleStyle.color:'')+';')}}"><text>{{"已拼"+item.$orig.order_num+"件"}}</text></view></block></view></block><view class="price-wrap"><block wx:if="{{value.priceStyle.mainControl}}"><view class="discount-price"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{item.g2[0]}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{"."+item.g3[1]}}</text></view></block><block wx:if="{{value.priceStyle.lineControl}}"><view class="original-price price-font" style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">{{'¥'+item.$orig.price+''}}</view></block><block wx:if="{{value.btnStyle.control}}"><button style="{{'background:'+(value.btnStyle.theme=='diy'?'linear-gradient(to right,'+value.btnStyle.bgColorStart+','+value.btnStyle.bgColorEnd+')':'')+';'+('color:'+(value.btnStyle.theme=='diy'?value.btnStyle.textColor:'')+';')+('border-radius:'+(value.btnStyle.aroundRadius*2+'rpx')+';')}}"><text class="text">{{value.btnStyle.text}}</text><block wx:if="{{item.$orig.reward_type==4}}"><text class="fan">{{"返"+item.$orig.reward_type_num+"积分"}}</text></block><block wx:if="{{item.$orig.reward_type==1||item.$orig.reward_type==2}}"><text class="fan">{{"返￥"+item.$orig.reward_type_num}}</text></block><block wx:if="{{item.$orig.reward_type==3}}"><text class="fan">返优惠券</text></block></button></block></view></view></block></view></block></block><block wx:if="{{value.template=='horizontal-slide'}}"><block wx:if="{{value.slideMode=='scroll'}}"><scroll-view class="scroll" scroll-x="{{true}}" show-scrollbar="{{false}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g4}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl}}"><view class="{{['content',[(value.nameLineMode=='multiple')?'multi-content':'']]}}"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.groupStyle.control}}"><view class="num"><text class="content-tuan-box" style="{{'color:'+(value.theme=='diy'?value.groupStyle.color:'')+';'+('background-color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';')}}">{{''+item.$orig.pintuan_num+'人团'}}</text><block wx:if="{{item.$orig.reward_type==4}}"><text class="content-tuan-price" style="{{'color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';'}}">{{'返'+item.$orig.reward_type_num+'积分'}}</text></block><block wx:if="{{item.$orig.reward_type==1||item.$orig.reward_type==2}}"><text class="content-tuan-price" style="{{'color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';'}}">{{'返￥'+item.$orig.reward_type_num+''}}</text></block><block wx:if="{{item.$orig.reward_type==3}}"><text class="content-tuan-price" style="{{'color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';'}}">返优惠券</text></block></view></block><block wx:if="{{value.priceStyle.mainControl}}"><view class="price-wrap"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{item.g5[0]}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{"."+item.g6[1]}}</text></view></block></view></block></view></block></scroll-view></block><block wx:if="{{value.slideMode=='slide'}}"><swiper class="swiper" style="{{'height:'+(swiperHeight)+';'}}" autoplay="{{false}}"><block wx:for="{{$root.l3}}" wx:for-item="pageItem" wx:for-index="pageIndex" wx:key="pageIndex"><swiper-item class="{{['swiper-item',pageItem.g7]}}"><block wx:for="{{pageItem.l2}}" wx:for-item="item" wx:for-index="dataIndex" wx:key="dataIndex"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list.'+pageIndex+'','',dataIndex]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g8}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[dataIndex]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl}}"><view class="{{['content',[(value.nameLineMode=='multiple')?'multi-content':'']]}}"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.groupStyle.control}}"><view class="num"><text class="content-tuan-box" style="{{'color:'+(value.theme=='diy'?value.groupStyle.color:'')+';'+('background-color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';')}}">{{''+item.$orig.pintuan_num+'人团'}}</text><block wx:if="{{item.$orig.reward_type==4}}"><text class="content-tuan-price" style="{{'color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';'}}">{{'返'+item.$orig.reward_type_num+'积分'}}</text></block><block wx:if="{{item.$orig.reward_type==1||item.$orig.reward_type==2}}"><text class="content-tuan-price" style="{{'color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';'}}">{{'返￥'+item.$orig.reward_type_num+''}}</text></block><block wx:if="{{item.$orig.reward_type==3}}"><text class="content-tuan-price" style="{{'color:'+(value.theme=='diy'?value.groupStyle.bgColor:'')+';'}}">返优惠券</text></block></view></block><block wx:if="{{value.priceStyle.mainControl}}"><view class="price-wrap"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{item.g9[1]}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{"."+item.g10[1]}}</text></view></block></view></block></view></block></swiper-item></block></swiper></block></block></view></x-skeleton></view></block>