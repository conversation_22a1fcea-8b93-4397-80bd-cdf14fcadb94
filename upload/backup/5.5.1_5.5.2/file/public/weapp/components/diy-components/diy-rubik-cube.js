(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-rubik-cube"],{"62de":function(t,i,e){},"8c09":function(t,i,e){"use strict";e.r(i);var r=e("ee2e"),o=e("e496");for(var u in o)["default"].indexOf(u)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(u);e("dc9c");var a=e("828b"),d=Object(a["a"])(o["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);i["default"]=d.exports},cb02:function(t,i,e){"use strict";(function(t){var r=e("47a9");Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;r(e("0817"));var o={name:"diy-rubik-cube",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{customHtml:""}},created:function(){this.init()},watch:{componentRefresh:function(t){this.init()}},computed:{list:function(){var t=this,i=JSON.parse(JSON.stringify(this.value.list));return i.forEach((function(i,e){i.pageItemStyle=t.countBorderRadius(t.value.mode,e)})),i},rubikCubeWrapCss:function(){var t="";return t+="background-color:"+this.value.componentBgColor+";","round"==this.value.componentAngle&&(t+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),t}},methods:{calcWidth:function(t){return-1!=t.indexOf("px")?t.substring(0,t.indexOf("px")):t},init:function(){if("custom-rubik-cube"==this.value.mode)this.value.diyHtml=this.value.diyHtml.replace(/&quot;/g,'"'),this.customHtml=this.value.diyHtml;else{var i={"row1-of2":{ratio:2,width:"calc((100% - "+t.upx2px(2*this.value.imageGap)+"px) / 2)"},"row1-of3":{ratio:3,width:"calc((100% - "+t.upx2px(4*this.value.imageGap)+"px) / 3)"},"row1-of4":{ratio:4,width:"calc((100% - "+t.upx2px(6*this.value.imageGap)+"px) / 4)"}};i[this.value.mode]?this.calcSingleRow(i[this.value.mode]):"row2-lt-of2-rt"==this.value.mode?this.calcFourSquare():"row1-lt-of2-rt"==this.value.mode?this.calcRowOneLeftOfTwoRight():"row1-tp-of2-bm"==this.value.mode?this.calcRowOneTopOfTwoBottom():"row1-lt-of1-tp-of2-bm"==this.value.mode&&this.calcRowOneLeftOfOneTopOfTwoBottom()}this.$forceUpdate()},calcSingleRow:function(i){var e=this;t.getSystemInfo({success:function(r){var o=0;e.list.forEach((function(u,a){var d=u.imgHeight/e.calcWidth(u.imgWidth),l=r.windowWidth-t.upx2px(2*e.value.margin.both);e.value.imageGap>0&&(l-=t.upx2px(i.ratio*e.value.imageGap*2)),u.imgWidth=l/i.ratio,u.imgHeight=u.imgWidth*d,(0==o||o<u.imgHeight)&&(o=u.imgHeight)})),e.list.forEach((function(t,e){t.widthStyle=i.width,t.imgHeight=o}))}})},calcFourSquare:function(){var i=this;t.getSystemInfo({success:function(e){var r=0,o=0;i.list.forEach((function(u,a){var d=-1!=u.imgWidth.indexOf("calc")?(e.windowWidth-24)*(parseInt("100%")-parseInt(t.upx2px(2*i.value.imageGap)+"px"))/2/100:u.imgWidth,l=u.imgHeight/d;u.imgWidth=e.windowWidth,u.imgWidth-=t.upx2px(4*i.value.margin.both),i.value.imageGap>0&&(u.imgWidth-=t.upx2px(2*i.value.imageGap)),u.imgWidth=u.imgWidth/2,u.imgHeight=u.imgWidth*l,a<=1?(0==r||r<u.imgHeight)&&(r=u.imgHeight):a>1&&(0==o||o<u.imgHeight)&&(o=u.imgHeight)})),i.list.forEach((function(e,u){e.imgWidth="calc((100% - "+t.upx2px(2*i.value.imageGap)+"px) / 2)",e.widthStyle=e.imgWidth,u<=1?e.imgHeight=r:u>1&&(e.imgHeight=o)}))}})},calcRowOneLeftOfTwoRight:function(){var i=this,e=0;this.list[1].imgWidth,this.list[2].imgWidth,t.getSystemInfo({success:function(r){i.list.forEach((function(o,u){if(0==u){var a=o.imgHeight/i.calcWidth(o.imgWidth);o.imgWidth=r.windowWidth-t.upx2px(4*i.value.margin.both)-t.upx2px(2*i.value.imageGap),o.imgWidth=o.imgWidth/2,o.imgHeight=o.imgWidth*a,e=(o.imgHeight-t.upx2px(2*i.value.imageGap))/2,o.imgWidth+="px"}else o.imgWidth=i.list[0].imgWidth,o.imgHeight=e}))}})},calcRowOneTopOfTwoBottom:function(){var i=this,e=0;t.getSystemInfo({success:function(r){i.list.forEach((function(o,u){var a=o.imgHeight/i.calcWidth(o.imgWidth);0==u?o.imgWidth=r.windowWidth-t.upx2px(4*i.value.margin.both):u>0&&(o.imgWidth=r.windowWidth-t.upx2px(4*i.value.margin.both)-t.upx2px(2*i.value.imageGap),o.imgWidth=o.imgWidth/2),o.imgHeight=o.imgWidth*a,u>0&&(0==e||e<o.imgHeight)&&(e=o.imgHeight)})),i.list.forEach((function(t,i){t.imgWidth+="px",t.widthStyle=t.imgWidth,i>0&&(t.imgHeight=e)}))}})},calcRowOneLeftOfOneTopOfTwoBottom:function(){var i=this;t.getSystemInfo({success:function(e){i.list.forEach((function(r,o){if(0==o){var u=r.imgHeight/i.calcWidth(r.imgWidth);r.imgWidth=e.windowWidth-t.upx2px(4*i.value.margin.both)-t.upx2px(2*i.value.imageGap),r.imgWidth=r.imgWidth/2,r.imgHeight=r.imgWidth*u}else 1==o?(r.imgWidth=i.list[0].imgWidth,r.imgHeight=(i.list[0].imgHeight-t.upx2px(2*i.value.imageGap))/2):o>1&&(r.imgWidth=(i.list[0].imgWidth-t.upx2px(2*i.value.imageGap))/2,r.imgHeight=i.list[1].imgHeight)})),i.list.forEach((function(t,i){t.imgWidth+="px",t.imgHeight}))}})},countBorderRadius:function(t,i){var e=this,r="";if("right"==this.value.elementAngle)return r;return{"row1-lt-of2-rt":[["border-top-right-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row1-lt-of1-tp-of2-bm":[["border-top-right-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-bottom-right-radius"],["border-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row1-tp-of2-bm":[["border-bottom-left-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-right-radius","border-top-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row2-lt-of2-rt":[["border-top-right-radius","border-bottom-left-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-right-radius","border-bottom-left-radius"],["border-top-left-radius","border-bottom-right-radius","border-top-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row1-of4":[["border-top-right-radius","border-bottom-right-radius"],["border-radius"],["border-radius"],["border-top-left-radius","border-bottom-left-radius"]],"row1-of3":[["border-top-right-radius","border-bottom-right-radius"],["border-radius"],["border-top-left-radius","border-bottom-left-radius"]],"row1-of2":[["border-top-right-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius"]]}[t][i].forEach((function(t,i){r+="border-top-left-radius:"+2*e.value.topElementAroundRadius+"rpx;",r+="border-top-right-radius:"+2*e.value.topElementAroundRadius+"rpx;",r+="border-bottom-left-radius:"+2*e.value.bottomElementAroundRadius+"rpx;",r+="border-bottom-right-radius:"+2*e.value.bottomElementAroundRadius+"rpx;"})),r}}};i.default=o}).call(this,e("df3c")["default"])},dc9c:function(t,i,e){"use strict";var r=e("62de"),o=e.n(r);o.a},e496:function(t,i,e){"use strict";e.r(i);var r=e("cb02"),o=e.n(r);for(var u in r)["default"].indexOf(u)<0&&function(t){e.d(i,t,(function(){return r[t]}))}(u);i["default"]=o.a},ee2e:function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return u})),e.d(i,"a",(function(){return r}));var r={nsMpHtml:function(){return e.e("components/ns-mp-html/ns-mp-html").then(e.bind(null,"d108"))}},o=function(){var t=this,i=t.$createElement,e=(t._self._c,"custom-rubik-cube"!=t.value.mode&&"row1-lt-of2-rt"==t.value.mode?t.$util.img(t.value.list[0].imageUrl):null),r="custom-rubik-cube"!=t.value.mode&&"row1-lt-of2-rt"==t.value.mode?t.__map(t.list,(function(i,e){var r=t.__get_orig(i),o=e>0?t.$util.img(i.imageUrl):null;return{$orig:r,g1:o}})):null,o="custom-rubik-cube"!=t.value.mode&&"row1-lt-of2-rt"!=t.value.mode&&"row1-lt-of1-tp-of2-bm"==t.value.mode?t.$util.img(t.value.list[0].imageUrl):null,u="custom-rubik-cube"!=t.value.mode&&"row1-lt-of2-rt"!=t.value.mode&&"row1-lt-of1-tp-of2-bm"==t.value.mode?t.$util.img(t.value.list[1].imageUrl):null,a="custom-rubik-cube"!=t.value.mode&&"row1-lt-of2-rt"!=t.value.mode&&"row1-lt-of1-tp-of2-bm"==t.value.mode?t.__map(t.list,(function(i,e){var r=t.__get_orig(i),o=e>1?t.$util.img(i.imageUrl):null;return{$orig:r,g4:o}})):null,d="custom-rubik-cube"!=t.value.mode&&"row1-lt-of2-rt"!=t.value.mode&&"row1-lt-of1-tp-of2-bm"!=t.value.mode?t.__map(t.list,(function(i,e){var r=t.__get_orig(i),o=t.$util.img(i.imageUrl);return{$orig:r,g5:o}})):null;t._isMounted||(t.e0=function(i){return t.$util.diyRedirectTo(t.value.list[0].link)},t.e1=function(i,e){var r=arguments[arguments.length-1].currentTarget.dataset,o=r.eventParams||r["event-params"];e=o.item;return t.$util.diyRedirectTo(e.link)},t.e2=function(i){return t.$util.diyRedirectTo(t.value.list[0].link)},t.e3=function(i){return t.$util.diyRedirectTo(t.value.list[1].link)},t.e4=function(i,e){var r=arguments[arguments.length-1].currentTarget.dataset,o=r.eventParams||r["event-params"];e=o.item;return t.$util.diyRedirectTo(e.link)},t.e5=function(i,e){var r=arguments[arguments.length-1].currentTarget.dataset,o=r.eventParams||r["event-params"];e=o.item;return t.$util.diyRedirectTo(e.link)}),t.$mp.data=Object.assign({},{$root:{g0:e,l0:r,g2:o,g3:u,l1:a,l2:d}})},u=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-rubik-cube-create-component',
    {
        'components/diy-components/diy-rubik-cube-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("8c09"))
        })
    },
    [['components/diy-components/diy-rubik-cube-create-component']]
]);
