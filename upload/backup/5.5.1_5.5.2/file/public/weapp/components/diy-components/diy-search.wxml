<view style="{{(value.pageStyle)}}"><view class="diy-search"><view class="{{['diy-search-wrap',value.positionWay]}}" style="{{(fixedCss)}}"><view data-event-opts="{{[['tap',[['search']]]]}}" class="{{['search-box','search-box-'+value.searchStyle]}}" style="{{(searchWrapCss)}}" bindtap="__e"><block wx:if="{{$root.g0}}"><block><block wx:if="{{value.searchStyle==2&&value.iconType=='img'}}"><view class="img"><image src="{{$root.g1}}" mode="heightFix"></image></view></block><block wx:if="{{value.searchStyle==2&&value.iconType=='icon'}}"><diy-icon class="icon" style="{{'max-width:'+(30*2+'rpx')+';'+('max-height:'+(30*2+'rpx')+';')}}" vue-id="bef7c060-1" icon="{{value.icon}}" value="{{value.style?value.style:'null'}}" bind:__l="__l"></diy-icon></block><view class="search-content" style="{{(inputStyle)}}"><input class="uni-input ns-font-size-base" type="text" maxlength="50" placeholder="{{value.title}}" readonly="{{true}}" placeholderStyle="{{placeholderStyle}}" data-event-opts="{{[['confirm',[['search']]],['input',[['__set_model',['','searchText','$event',[]]]]]]}}" value="{{searchText}}" bindconfirm="__e" bindinput="__e"/><text data-event-opts="{{[['tap',[['search']]]]}}" class="iconfont icon-sousuo3" style="{{'color:'+(value.textColor?value.textColor:'rgba(0,0,0,0)')+';'}}" catchtap="__e"></text></view></block></block><block wx:if="{{value.searchStyle==3}}"><block><view data-event-opts="{{[['tap',[['search']]]]}}" class="search-content" style="{{(inputStyle)}}" catchtap="__e"><text class="iconfont icon-sousuo3" style="{{'color:'+(value.textColor?value.textColor:'rgba(0,0,0,0)')+';'}}"></text><input class="uni-input ns-font-size-base" type="text" maxlength="50" placeholder="{{value.title}}" readonly="{{true}}" placeholderStyle="{{placeholderStyle}}" data-event-opts="{{[['confirm',[['search']]],['tap',[['search']]],['input',[['__set_model',['','searchText','$event',[]]]]]]}}" value="{{searchText}}" bindconfirm="__e" catchtap="__e" bindinput="__e"/><text data-event-opts="{{[['tap',[['search']]]]}}" class="search-content-btn" style="{{'background-color:'+(value.pageBgColor?value.pageBgColor:'rgba(0,0,0,0)')+';'}}" catchtap="__e">搜索</text></view><block wx:if="{{value.iconType=='img'}}"><view data-event-opts="{{[['tap',[['redirectTo',['$0'],['value.searchLink']]]]]}}" class="img" catchtap="__e"><image src="{{$root.g2}}" mode="heightFix"></image></view></block><block wx:if="{{value.iconType=='icon'}}"><diy-icon class="icon" style="{{'max-width:'+(30*2+'rpx')+';'+('max-height:'+(30*2+'rpx')+';')}}" vue-id="bef7c060-2" icon="{{value.icon}}" value="{{value.style?value.style:'null'}}" data-event-opts="{{[['^click',[['redirectTo',['$0'],['value.searchLink']]]]]}}" catch:click="__e" bind:__l="__l"></diy-icon></block></block></block></view></view><block wx:if="{{value.positionWay=='fixed'}}"><view class="u-navbar-placeholder" style="{{'width:'+('100%')+';'+('padding-top:'+(moduleHeight)+';')}}"></view></block><ns-login class="vue-ref" vue-id="bef7c060-3" data-ref="login" bind:__l="__l"></ns-login></view></view>