<view style="{{(value.pageStyle)}}"><x-skeleton vue-id="bce90ce8-1" type="banner" loading="{{loading}}" configs="{{skeletonConfig}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{liveInfo}}"><view data-event-opts="{{[['tap',[['entryRoom',['$0'],['liveInfo.roomid']]]]]}}" class="live-wrap" bindtap="__e"><view class="banner-wrap"><image src="{{liveInfo.banner!=''?$root.g0:$root.g1}}" mode="widthFix" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e"></image><view class="shade"></view><view class="wrap"><view class="room-name"><text class="{{['status-name','font-size-base',(liveInfo.live_status=='101')?'color-base-bg':'']}}"><block wx:if="{{liveInfo.live_status=='101'}}"><text class="iconfont icon-zhibozhong font-size-sub"></text></block><block wx:else><text class="iconfont icon-zhibojieshu font-size-sub"></text></block>{{''+liveInfo.status_name+''}}</text>{{''+liveInfo.name+''}}</view></view></view><block wx:if="{{value.isShowAnchorInfo||value.isShowLiveGood}}"><view class="room-info"><block wx:if="{{value.isShowAnchorInfo}}"><block><image class="anchor-img" src="{{liveInfo.anchor_img!=''?$root.g2:$root.g3.head}}" data-event-opts="{{[['error',[['e1',['$event']]]]]}}" binderror="__e"></image><text class="anchor-name">{{"主播："+liveInfo.anchor_name}}</text></block></block><block wx:if="{{value.isShowAnchorInfo&&value.isShowLiveGood}}"><text class="separate">|</text></block><block wx:if="{{value.isShowLiveGood}}"><block><text class="goods-text">{{"直播商品："+$root.g4}}</text></block></block></view></block></view></block></x-skeleton></view>