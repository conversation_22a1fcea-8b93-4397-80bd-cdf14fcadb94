<view style="{{(value.pageStyle)}}"><block wx:if="{{value.mode=='custom-rubik-cube'}}"><view><view style="position:relative;"><ns-mp-html vue-id="e7d6c512-1" content="{{customHtml}}" bind:__l="__l"></ns-mp-html></view></view></block><block wx:else><view class="{{['rubik-cube',value.mode]}}" style="{{(rubikCubeWrapCss)}}"><block wx:if="{{value.mode=='row1-lt-of2-rt'}}"><view class="template-left"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['item',value.mode]}}" style="{{'margin-right:'+(value.imageGap*2+'rpx')+';'+('width:'+(list[0].imgWidth)+';')+('height:'+(list[0].imgHeight+'px')+';')}}" bindtap="__e"><image style="{{(list[0].pageItemStyle)}}" src="{{$root.g0}}" mode="{{list[0].imageMode||'scaleToFill'}}" show-menu-by-longpress="{{true}}"></image></view></view><view class="template-right"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index>0}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="{{['item',value.mode]}}" style="{{'margin-bottom:'+(value.imageGap*2+'rpx')+';'+('width:'+(item.$orig.imgWidth)+';')+('height:'+(item.$orig.imgHeight+'px')+';')}}" bindtap="__e"><image style="{{(item.$orig.pageItemStyle)}}" src="{{item.g1}}" mode="{{item.$orig.imageMode||'scaleToFill'}}" show-menu-by-longpress="{{true}}"></image></view></block></block></view></block><block wx:else><block wx:if="{{value.mode=='row1-lt-of1-tp-of2-bm'}}"><view class="template-left"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="{{['item',value.mode]}}" style="{{'margin-right:'+(value.imageGap*2+'rpx')+';'+('width:'+(list[0].imgWidth)+';')+('height:'+(list[0].imgHeight+'px')+';')}}" bindtap="__e"><image style="{{(list[0].pageItemStyle)}}" src="{{$root.g2}}" mode="{{list[0].imageMode||'scaleToFill'}}" show-menu-by-longpress="{{true}}"></image></view></view><view class="template-right"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="{{['item',value.mode]}}" style="{{'margin-bottom:'+(value.imageGap*2+'rpx')+';'+('width:'+(list[1].imgWidth)+';')+('height:'+(list[1].imgHeight+'px')+';')}}" bindtap="__e"><image style="{{(list[1].pageItemStyle)}}" src="{{$root.g3}}" mode="{{list[1].imageMode||'scaleToFill'}}" show-menu-by-longpress="{{true}}"></image></view><view class="template-bottom"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index>1}}"><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="{{['item',value.mode]}}" style="{{'margin-right:'+(value.imageGap*2+'rpx')+';'+('width:'+(item.$orig.imgWidth)+';')+('height:'+(item.$orig.imgHeight+'px')+';')}}" bindtap="__e"><image style="{{(item.$orig.pageItemStyle)}}" src="{{item.g4}}" mode="{{item.$orig.imageMode||'scaleToFill'}}" show-menu-by-longpress="{{true}}"></image></view></block></block></view></view></block><block wx:else><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="{{['item',value.mode]}}" style="{{'margin-right:'+(value.imageGap*2+'rpx')+';'+('margin-bottom:'+(value.imageGap*2+'rpx')+';')+('width:'+(item.$orig.widthStyle)+';')+('height:'+(item.$orig.imgHeight+'px')+';')}}" bindtap="__e"><image style="{{(item.$orig.pageItemStyle)}}" src="{{item.g5}}" mode="{{item.$orig.imageMode||'scaleToFill'}}" show-menu-by-longpress="{{true}}"></image></view></block></block></block></view></block></view>