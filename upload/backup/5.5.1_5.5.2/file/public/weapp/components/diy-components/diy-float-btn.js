(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-float-btn"],{"13ef":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=t.getSystemInfoSync(),a={name:"diy-float-btn",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{navHeight:0,statusBarHeight:e.statusBarHeight}},created:function(){},watch:{componentRefresh:function(t){}},methods:{},computed:{style:function(){var t={};switch("ios"==e.platform?54:58,parseInt(this.value.bottomPosition)){case 1:t.top=2*(this.navHeight+this.statusBarHeight+parseInt(this.value.btnBottom))+"rpx";break;case 2:t.top=2*(this.navHeight+this.statusBarHeight+parseInt(this.value.btnBottom))+"rpx";break;case 3:t.bottom=2*(100+parseInt(this.value.btnBottom))+"rpx";break;case 4:t.bottom=2*(100+parseInt(this.value.btnBottom))+"rpx";break}return this.$util.objToStyle(t)}}};n.default=a}).call(this,e("df3c")["default"])},2029:function(t,n,e){"use strict";e.r(n);var a=e("5965"),i=e("47bb");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);e("3219");var r=e("828b"),u=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=u.exports},3219:function(t,n,e){"use strict";var a=e("a6dd"),i=e.n(a);i.a},"47bb":function(t,n,e){"use strict";e.r(n);var a=e("13ef"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=i.a},5965:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return a}));var a={diyIcon:function(){return e.e("components/diy-components/diy-icon").then(e.bind(null,"a68f"))}},i=function(){var t=this,n=t.$createElement,e=(t._self._c,t.__map(t.value.list,(function(n,e){var a=t.__get_orig(n),i=n.iconType&&"img"!=n.iconType?null:t.$util.img(n.imageUrl);return{$orig:a,g0:i}})));t._isMounted||(t.e0=function(n,e){var a=arguments[arguments.length-1].currentTarget.dataset,i=a.eventParams||a["event-params"];e=i.item;return t.$util.diyRedirectTo(e.link)}),t.$mp.data=Object.assign({},{$root:{l0:e}})},o=[]},a6dd:function(t,n,e){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-float-btn-create-component',
    {
        'components/diy-components/diy-float-btn-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("2029"))
        })
    },
    [['components/diy-components/diy-float-btn-create-component']]
]);
