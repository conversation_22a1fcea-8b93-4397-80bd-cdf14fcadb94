(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-notice"],{"0cb9":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={diyIcon:function(){return i.e("components/diy-components/diy-icon").then(i.bind(null,"a68f"))},uniPopup:function(){return i.e("components/uni-popup/uni-popup").then(i.bind(null,"d745"))}},o=function(){var t=this.$createElement,e=(this._self._c,"img"==this.value.iconType?this.$util.img(this.value.imageUrl):null);this.$mp.data=Object.assign({},{$root:{g0:e}})},a=[]},"1e9b":function(t,e,i){},"349e":function(t,e,i){"use strict";var n=i("1e9b"),o=i.n(n);o.a},"89b6":function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"diy-notice",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{list:[],notice:"",marqueeWrapWidth:0,marqueeWidth:0,marqueeStyle:"",marqueeAgainStyle:"",time:0,delayTime:1e3}},watch:{componentRefresh:function(t){"initial"==this.value.sources&&this.getData()}},created:function(){},mounted:function(){"initial"==this.value.sources?this.getData():(this.list=this.value.list,this.bindCrossSlipEvent())},computed:{noticeWrapCss:function(){var t="";return t+="background-color:"+this.value.componentBgColor+";","round"==this.value.componentAngle&&(t+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),t}},methods:{getData:function(){var t=this,e={page_size:0};"initial"==this.value.sources&&(e.page_size=this.value.count),this.value.noticeIds.length&&(e.id_arr=this.value.noticeIds.toString(),e.page_size=0),this.$api.sendRequest({url:"/api/notice/page",data:e,success:function(e){0==e.code&&e.data&&(t.list=e.data.list,t.bindCrossSlipEvent())}})},toLink:function(t){"initial"==this.value.sources?this.$util.redirectTo("/pages_tool/notice/detail",{notice_id:t.id}):t?Object.keys(t.link).length>1?this.$util.diyRedirectTo(t.link):(this.notice=t.title,this.$refs.noticePopup.open()):this.$util.redirectTo("/pages_tool/notice/list")},closeNoticePopup:function(){this.$refs.noticePopup.close()},bindCrossSlipEvent:function(){var e=this;"horizontal"==this.value.scrollWay&&setTimeout((function(){e.$nextTick((function(){t.createSelectorQuery().in(e).select(".marquee-wrap").boundingClientRect((function(i){e.marqueeWrapWidth=i.width;var n=t.createSelectorQuery().in(e);n.select(".marquee").boundingClientRect((function(t){e.marqueeWidth=t.width+30,e.time=Math.ceil(10*e.marqueeWidth),e.marqueeWrapWidth>e.marqueeWidth?(e.marqueeStyle="animation: none;",e.marqueeAgainStyle="display:none;"):(e.marqueeStyle="\n\t\t\t\t\t\t\t\t\t\twidth: ".concat(e.marqueeWidth,"px;\n\t\t\t\t\t\t\t\t\t\tanimation-duration: ").concat(e.time,"ms;\n\t\t\t\t\t\t\t\t\t\tanimation-delay: ").concat(e.delayTime,"ms;"),e.marqueeAgainStyle="\n\t\t\t\t\t\t\t\t\t\twidth: ".concat(e.marqueeWidth,"px;\n\t\t\t\t\t\t\t\t\t\tleft: ").concat(e.marqueeWidth,"px;\n\t\t\t\t\t\t\t\t\t\tanimation-duration: ").concat(e.time,"ms;\n\t\t\t\t\t\t\t\t\t\tanimation-delay: ").concat(e.delayTime,"ms;"))})).exec()})).exec()}))}))}}};e.default=i}).call(this,i("df3c")["default"])},af22:function(t,e,i){"use strict";i.r(e);var n=i("89b6"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},bbba:function(t,e,i){"use strict";i.r(e);var n=i("0cb9"),o=i("af22");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("349e");var u=i("828b"),r=Object(u["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=r.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-notice-create-component',
    {
        'components/diy-components/diy-notice-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("bbba"))
        })
    },
    [['components/diy-components/diy-notice-create-component']]
]);
