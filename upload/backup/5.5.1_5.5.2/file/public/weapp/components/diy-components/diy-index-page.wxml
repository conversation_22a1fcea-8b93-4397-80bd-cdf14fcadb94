<view><view class="bg" style="{{(warpCss)}}"><view class="index-page-content"><view class="nav-top-category" style="{{(categoryCss)}}"><block wx:if="{{value}}"><scroll-view class="diyIndex" style="{{'background:'+(value.backgroundColor?value.backgroundColor:'')+';'+('width:'+('calc(100% - 48rpx)')+';')}}" scroll-with-animation="{{true}}" scroll-x="true" scroll-into-view="{{'a'+pageIndex}}" data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><block wx:for="{{cateList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item',(value.styleType=='fill')?'fill':'']}}" style="{{'background:'+(index==pageIndex&&value.styleType=='fill'?value.selectColor:'')+';'}}" id="{{'a'+index}}" data-event-opts="{{[['tap',[['changePageIndex',[index]]]]]}}" bindtap="__e"><block wx:if="{{value.styleType=='fill'}}"><view class="{{['text-con',index==pageIndex?'active':'']}}" style="{{'color:'+(index==pageIndex?'':value.noColor)+';'}}">{{''+(item.short_name?item.short_name:item.category_name)+''}}</view></block><block wx:else><view class="{{['text-con',index==pageIndex?'active':'']}}" style="{{'color:'+(index==pageIndex?value.selectColor:value.noColor)+';'}}">{{''+(item.short_name?item.short_name:item.category_name)+''}}</view></block><block wx:if="{{index==pageIndex&&value.styleType!='fill'}}"><view class="color-base-bg line" style="{{'background:'+(value.selectColor?value.selectColor+'!important':'rgba(0,0,0,0)'+'!important')+';'}}"></view></block></view></block></scroll-view></block><text data-event-opts="{{[['tap',[['unfoldMenu',['$event']]]]]}}" class="iconfont icon-unfold unfold-arrows" style="{{'color:'+(value.moreColor)+';'}}" bindtap="__e"></text></view><uni-popup class="vue-ref" vue-id="12e999e4-1" type="top" top="{{uniPopTop}}" data-ref="navTopCategoryPop" bind:__l="__l" vue-slots="{{['default']}}"><view class="nav-topcategory-pop"><block wx:for="{{cateList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['changePageIndex',[index]]]]]}}" class="{{['category-item',[(pageIndex==index)?'color-base-text color-base-border active':'']]}}" bindtap="__e">{{''+(item.short_name?item.short_name:item.category_name)+''}}</text></block></view></uni-popup><view class="nav_top_category-fill" style="{{'height:'+(moduleHeight)+';'}}"></view><block wx:if="{{pageIndex==0}}"><block><slot name="components"></slot><slot></slot></block></block><block wx:else><block><slot name="components"></slot><view class="index-category-box"><view hidden="{{!(!isloading)}}" class="category-goods"><mescroll-uni class="vue-ref" vue-id="12e999e4-2" top="{{uniPopTop}}" background="{{'url('+$root.g0+') 0px -50px / 100% no-repeat'}}" paddingBoth="30rpx" data-ref="mescroll" data-event-opts="{{[['^getData',[['getGoodsList']]],['^touchmove',[['',['$event']]]]]}}" bind:getData="__e" catch:touchmove="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g1}}"><view class="twoCategorylist"><block wx:if="{{$root.g2<=5}}"><view class="twoCategory min"><view class="twoCategory-page"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toCateGoodsList',['$0',2],[[['twoCategorylist','',index,'category_id_2']]]]]]]}}" class="swiper-item" bindtap="__e"><view class="item-box"><block wx:if="{{item.$orig.image}}"><image src="{{item.g3}}" mode="aspectFill"></image></block><block wx:else><image src="{{item.g4.goods}}" mode="aspectFill"></image></block><view>{{item.$orig.category_name}}</view></view></view></block></view></view></block><block wx:if="{{$root.g5}}"><view class="twoCategory base"><view class="twoCategory-page"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toCateGoodsList',['$0',2],[[['twoCategorylist','',index,'category_id_2']]]]]]]}}" class="swiper-item" bindtap="__e"><view class="item-box"><block wx:if="{{item.$orig.image}}"><image src="{{item.g6}}" mode="aspectFill"></image></block><block wx:else><image src="{{item.g7.goods}}" mode="aspectFill"></image></block><view>{{item.$orig.category_name}}</view></view></view></block></view></view></block><block wx:if="{{$root.g8>10}}"><swiper class="twoCategory big" duration="{{500}}" data-event-opts="{{[['change',[['swiperToCategoryChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{maxPage}}" wx:for-item="page" wx:for-index="__i0__" wx:key="*this"><swiper-item class="twoCategory-page"><block wx:for="{{twoCategorylist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index>=(page-1)*10&&index<page*10}}"><view data-event-opts="{{[['tap',[['toCateGoodsList',['$0',2],[[['twoCategorylist','',index,'category_id_2']]]]]]]}}" class="swiper-item" bindtap="__e"><view class="item-box"><image src="{{item.image}}" mode="aspectFill"></image><view>{{item.category_name}}</view></view></view></block></block></swiper-item></block></swiper></block><view class="dot-box"><block wx:for="{{maxPage}}" wx:for-item="page" wx:for-index="__i1__" wx:key="*this"><block wx:if="{{maxPage>1}}"><view class="{{['dot-item',twoCategorylistId==page-1?'active color-base-bg':'']}}"></view></block></block></view></view></block><block wx:if="{{cateList[pageIndex].image_adv}}"><image class="category_adv" src="{{$root.g9}}" mode="widthFix"></image></block><block wx:if="{{$root.g10}}"><view class="goods-list double-column"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList.'+pageIndex+'.list','',index]]]]]]]}}" class="goods-item" bindtap="__e"><view class="goods-img"><image src="{{item.m0}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image><block wx:if="{{item.m1}}"><view class="color-base-bg goods-tag">{{item.m2}}</view></block><block wx:if="{{value.goodsTag=='diy'}}"><view class="goods-tag-img"><image src="{{item.g11}}"></image></view></block></view><view class="info-wrap"><view class="name-wrap"><view class="goods-name">{{item.$orig.goods_name}}</view></view><view class="lineheight-clear"><view class="discount-price"><text class="unit color-base-text font-size-tag">{{item.m3}}</text><text class="price color-base-text font-size-toolbar">{{item.m4}}</text></view><block wx:if="{{item.m5}}"><view class="member-price-tag"><image src="{{item.g12}}" mode="widthFix"></image></view></block><block wx:else><block wx:if="{{item.$orig.promotion_type==1}}"><view class="member-price-tag"><image src="{{item.g13}}" mode="widthFix"></image></view></block></block></view><view class="pro-info"><block wx:if="{{item.m6}}"><view class="delete-price font-size-activity-tag color-tip"><text class="unit">{{item.m7}}</text><text>{{item.m8}}</text></view></block><view class="sale font-size-activity-tag color-tip">{{"已售"+item.$orig.sale_num+(item.$orig.unit?item.$orig.unit:'件')}}</view></view></view></view></block></view></block><block wx:if="{{$root.g14}}"><view><ns-empty vue-id="{{('12e999e4-3')+','+('12e999e4-2')}}" text="该分类下暂无商品" isIndex="{{false}}" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni></view><view hidden="{{!(isloading)}}" class="loading"><ns-loading class="vue-ref" vue-id="12e999e4-4" data-ref="loading" bind:__l="__l"></ns-loading></view></view></block></block></view></view></view>