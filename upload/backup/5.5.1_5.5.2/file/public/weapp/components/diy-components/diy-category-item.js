(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-category-item"],{6081:function(t,e,i){},"81bb":function(t,e,i){"use strict";i.r(e);var o=i("f4de"),s=i("e150");for(var a in s)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(a);i("d3bd");var r=i("828b"),n=Object(r["a"])(s["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=n.exports},"8d2e":function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"diy-category-item",props:{category:{type:Object},value:{type:Object,default:function(){return{}}},index:{type:Number,default:0},select:{type:Number,default:0},oneCategorySelect:{type:Number,default:0},last:{type:Boolean,default:!1}},data:function(){return{type:"goods",level:3,categoryId:-1,loading:!1,goodsList:[],pageIndex:0,pageSize:10,totalPage:1,touchstartPosition:0,scrollType:"top",contentWrapHeight:0,scrollIntoView:"category-2--1",scrollTop:0,loadType:"",timeout:{},cartEditRepeat:!1}},created:function(){this.type=1==this.value.template?"category":"goods",this.index==this.select&&0==this.pageIndex&&this.getGoodsList(),this.loadType=1==this.value.goodsLevel&&"all"==this.value.loadType?"all":"part"},mounted:function(){var e=this;setTimeout((function(){try{var i=t.createSelectorQuery().in(e);i.select(".scroll-goods-view").boundingClientRect((function(t){t&&(e.contentWrapHeight=t.height)})).exec()}catch(o){}}),1e3)},watch:{oneCategorySelect:function(){this.scrollTop=-1,this.goodsList=[],this.selectCategory(-1)},select:function(){var e=this;if(this.index==this.select){if(this.scrollTop=0,0==this.pageIndex?this.getGoodsList():this.$refs.loadingCover&&"part"==this.loadType&&(this.$refs.loadingCover.show(),setTimeout((function(){e.$refs.loadingCover.hide()}),300)),!this.contentWrapHeight){var i=t.createSelectorQuery().in(this);i.select(".scroll-goods-view").boundingClientRect((function(t){t&&(e.contentWrapHeight=t.height)})).exec()}}else"part"==this.loadType&&(this.scrollTop=-1)},"globalStoreInfo.store_id":{handler:function(t,e){t!=e&&(this.index!=this.select&&"all"!=this.loadType||(this.pageIndex=0,this.categoryId=-1,this.totalPage=1,this.getGoodsList()))},deep:!0}},methods:{getGoodsList:function(){var e=this;if(!("goods"!=this.type||this.loading||this.pageIndex>=this.totalPage)){this.loading=!0,this.pageIndex++,this.$refs.loadingCover&&1==this.pageIndex&&"part"==this.loadType&&setTimeout((function(){e.$refs.loadingCover.show()}));var i="",o="";this.value.sortWay&&("default"==this.value.sortWay?(i="",o=""):"sales"==this.value.sortWay?(i="sale_num",o="desc"):"price"==this.value.sortWay?(i="discount_price",o="desc"):"news"==this.value.sortWay&&(i="create_time",o="desc")),this.$api.sendRequest({url:"/api/goodssku/pageByCategory",data:{page:this.pageIndex,page_size:this.pageSize,category_id:-1!=this.categoryId?this.category.child_list[this.categoryId].category_id:this.category.category_id,order:i,sort:o},success:function(i){0==i.code&&i.data&&(1==e.pageIndex&&(e.goodsList=[]),e.goodsList=e.goodsList.concat(i.data.list),e.loadGoodsCartNum(),e.totalPage=i.data.page_count,e.loading=!1,e.$refs.loadingCover&&"part"==e.loadType&&e.$refs.loadingCover.hide(),setTimeout((function(){"all"==e.loadType&&e.$emit("loadfinish",1==e.pageIndex?e.index:-1);var i=t.createSelectorQuery().in(e);i.select(".goods-list").boundingClientRect((function(t){t&&t.height<e.contentWrapHeight&&(e.scrollType="none")})).exec()}),500))}})}},goodsImg:function(t){var e=t.split(",");return e[0]?this.$util.img(e[0],{size:3==this.value?"big":"mid"}):this.$util.getDefaultImage().goods},imgError:function(t){this.goodsList[t].goods_image=this.$util.getDefaultImage().goods},showPrice:function(t){var e=t.discount_price;return t.member_price&&parseFloat(t.member_price)<parseFloat(e)&&(e=t.member_price),e},showMarketPrice:function(t){if(t.market_price_show){var e=this.showPrice(t);if(t.market_price>0)return t.market_price;if(parseFloat(t.price)>parseFloat(e))return t.price}return""},toDetail:function(t){this.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})},increase:function(e,i){var o=this;if(this.storeToken){if(!this.cartEditRepeat){var s=this.cartList["goods_"+i.goods_id]&&this.cartList["goods_"+i.goods_id]["sku_"+i.sku_id]?this.cartList["goods_"+i.goods_id]["sku_"+i.sku_id]:null,a=s?s.num:0,r=s&&s.cart_id?"/api/cart/edit":"/api/cart/add",n=i.min_buy>0?i.min_buy-1:0,c=a>=i.min_buy?a:n,d=c+1,l=s?s.cart_id:0;if(d>parseInt(i.stock))this.$util.showToast({title:"商品库存不足"});else if(i.is_limit&&i.max_buy&&d>parseInt(i.max_buy))this.$util.showToast({title:"该商品每人限购".concat(i.max_buy).concat(i.unit||"件")});else{var u=t.createSelectorQuery().in(this);if(u.select("#"+e.currentTarget.id+" .click-event").boundingClientRect((function(t){t&&o.$emit("addCart",t.left,t.top)})).exec(),this.timeout[i.sku_id]&&clearTimeout(this.timeout[i.sku_id]),s)this.cartList["goods_"+i.goods_id]["sku_"+i.sku_id].num=d;else{this.cartList["goods_"+i.goods_id]||(this.cartList["goods_"+i.goods_id]={});var g=i.discount_price;i.member_price>0&&Number(i.member_price)<=Number(i.discount_price)&&(g=i.member_price),this.cartList["goods_"+i.goods_id]["sku_"+i.sku_id]={cart_id:l,goods_id:i.goods_id,sku_id:i.sku_id,num:d,discount_price:g}}this.$forceUpdate(),this.timeout[i.sku_id]=setTimeout((function(){o.cartEditRepeat=!0,o.$api.sendRequest({url:r,data:{cart_id:l,sku_id:i.sku_id,num:d},success:function(t){o.cartEditRepeat=!1,0==t.code?(0==l&&(o.cartList["goods_"+i.goods_id]["sku_"+i.sku_id].cart_id=t.data),o.$store.dispatch("cartCalculate")):o.$util.showToast({title:t.message})}})}),800)}}}else this.$emit("tologin")},reduce:function(t){var e=this;if(!this.cartEditRepeat){var i=this.cartList["goods_"+t.goods_id]&&this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id]?this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id]:null,o=i.num>t.min_buy?i.num:1,s=o>1?"/api/cart/edit":"/api/cart/delete",a=o-1;this.timeout[t.sku_id]&&clearTimeout(this.timeout[t.sku_id]),a?this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id].num=a:(delete this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id],2==Object.keys(this.cartList["goods_"+t.goods_id]).length&&delete this.cartList["goods_"+t.goods_id]),this.$forceUpdate(),i&&0==i.cart_id||(this.timeout[t.sku_id]=setTimeout((function(){e.cartEditRepeat=!0,e.$api.sendRequest({url:s,data:{cart_id:i.cart_id,sku_id:t.sku_id,num:a},success:function(t){e.cartEditRepeat=!1,0==t.code?e.$store.dispatch("cartCalculate"):e.$util.showToast({title:t.message})}})}),800))}},selectSku:function(t){this.storeToken?this.$emit("selectsku",t):this.$emit("tologin")},selectCategory:function(t){var e=this;this.categoryId=t,this.pageIndex=0,this.totalPage=1,this.getGoodsList(),setTimeout((function(){e.scrollIntoView="category-2-"+t}))},scrolltolower:function(){this.getGoodsList()},listenScroll:function(t){t.detail.scrollTop<=10?this.scrollType="top":parseInt(t.detail.scrollTop+this.contentWrapHeight+30)>=t.detail.scrollHeight?this.scrollType="bottom":this.scrollType=""},touchstart:function(t){this.touchstartPosition=t.changedTouches[0].clientY},touchend:function(t){var e=t.changedTouches[0].clientY;("top"==this.scrollType||"none"==this.scrollType)&&e-this.touchstartPosition>100?this.switchCategory("prev"):("bottom"==this.scrollType||"none"==this.scrollType)&&this.touchstartPosition-e>100&&this.switchCategory("next")},switchCategory:function(t){if("prev"==t)if(-1==this.categoryId){var e=this.index-1;if(-1==e)return;this.$emit("switch",e)}else{var i=this.categoryId-1;this.selectCategory(i)}else if(-1==this.categoryId||this.category.child_list&&this.categoryId==this.category.child_list.length-1){var o=this.index+1;this.$emit("switch",o)}else{var s=this.categoryId+1;this.selectCategory(s)}},diyRedirectTo:function(t){t&&this.$util.diyRedirectTo(JSON.parse(t))},loadGoodsCartNum:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.goodsList.forEach((function(e){e.num=t.cartList["goods_"+e.goods_id]?t.cartList["goods_"+e.goods_id].num:0})),e&&this.$forceUpdate()}}};e.default=i}).call(this,i("df3c")["default"])},d3bd:function(t,e,i){"use strict";var o=i("6081"),s=i.n(o);s.a},e150:function(t,e,i){"use strict";i.r(e);var o=i("8d2e"),s=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=s.a},f4de:function(t,e,i){"use strict";i.d(e,"b",(function(){return s})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={uniPopup:function(){return i.e("components/uni-popup/uni-popup").then(i.bind(null,"d745"))},loadingCover:function(){return i.e("components/loading-cover/loading-cover").then(i.bind(null,"c003"))}},s=function(){var t=this,e=t.$createElement,i=(t._self._c,"category"==t.type&&t.category.child_list&&t.category.child_list.length),o=i&&t.category.image_adv?t.$util.img(t.category.image_adv):null,s=i&&2==t.value.level?t.__map(t.category.child_list,(function(e,i){var o=t.__get_orig(e),s=t.$util.img(e.image);return{$orig:o,g2:s}})):null,a=i&&3==t.value.level?t.__map(t.category.child_list,(function(e,i){var o=t.__get_orig(e),s=t.__map(e.child_list,(function(e,i){var o=t.__get_orig(e),s=t.$util.img(e.image);return{$orig:o,g3:s}}));return{$orig:o,l1:s}})):null,r="goods"==t.type&&"part"==t.loadType?t.goodsList.length:null,n="goods"==t.type&&"part"==t.loadType&&r?t.__map(t.goodsList,(function(e,i){var o=t.__get_orig(e),s=t.goodsImg(e.goods_image),a=parseFloat(t.showPrice(e)).toFixed(2).split("."),r=parseFloat(t.showPrice(e)).toFixed(2).split("."),n=e.member_price&&e.member_price==t.showPrice(e),c=n?t.$util.img("public/uniapp/index/VIP.png"):null,d=n||1!=e.promotion_type?null:t.$util.img("public/uniapp/index/discount.png"),l=t.showMarketPrice(e),u=l?t.showMarketPrice(e):null;return{$orig:o,m0:s,g5:a,g6:r,m1:n,g7:c,g8:d,m2:l,m3:u}})):null,c="goods"!=t.type||"part"!=t.loadType||r?null:t.$util.img("public/uniapp/category/empty.png"),d="goods"==t.type&&"part"==t.loadType?t.last&&(-1==t.categoryId||!t.category.child_list||t.category.child_list&&t.categoryId==t.category.child_list.length-1):null,l="goods"==t.type?"all"==t.loadType&&t.goodsList.length:null,u="goods"==t.type&&l?t.__map(t.goodsList,(function(e,i){var o=t.__get_orig(e),s=t.goodsImg(e.goods_image),a=parseFloat(t.showPrice(e)).toFixed(2).split("."),r=parseFloat(t.showPrice(e)).toFixed(2).split("."),n=e.member_price&&e.member_price==t.showPrice(e),c=n?t.$util.img("public/uniapp/index/VIP.png"):null,d=n||1!=e.promotion_type?null:t.$util.img("public/uniapp/index/discount.png"),l=t.showMarketPrice(e),u=l?t.showMarketPrice(e):null;return{$orig:o,m4:s,g12:a,g13:r,m5:n,g14:c,g15:d,m6:l,m7:u}})):null;t._isMounted||(t.e0=function(e,i){var o=arguments[arguments.length-1].currentTarget.dataset,s=o.eventParams||o["event-params"];i=s.one;return t.$util.redirectTo("/pages/goods/list",{category_id:i.category_id})},t.e1=function(e,i){var o=arguments[arguments.length-1].currentTarget.dataset,s=o.eventParams||o["event-params"];i=s.two;return t.$util.redirectTo("/pages/goods/list",{category_id:i.category_id})},t.e2=function(e){return t.$refs.screenCategoryPopup.open()},t.e3=function(e){return t.$refs.screenCategoryPopup.close()}),t.$mp.data=Object.assign({},{$root:{g0:i,g1:o,l0:s,l2:a,g4:r,l3:n,g9:c,g10:d,g11:l,l4:u}})},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-category-item-create-component',
    {
        'components/diy-components/diy-category-item-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("81bb"))
        })
    },
    [['components/diy-components/diy-category-item-create-component']]
]);
