<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}"><x-skeleton vue-id="a8c35f0c-1" type="{{skeletonType}}" loading="{{loading}}" configs="{{skeletonConfig}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{$root.g1}}"><view class="{{['diy-presale',value.template,value.style]}}" style="{{(warpCss)}}"><block wx:if="{{value.template=='row1-of1'}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g2}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl||value.btnStyle.control}}"><view class="content"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.priceStyle.mainControl}}"><view class="discount-price"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+item.g3[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+('.'+item.g4[1])+''}}</text></view></block><block wx:if="{{value.btnStyle.control}}"><button style="{{'background:'+(value.btnStyle.theme=='diy'?'linear-gradient(to right,'+value.btnStyle.bgColorStart+','+value.btnStyle.bgColorEnd+')':'')+';'+('color:'+(value.btnStyle.theme=='diy'?value.btnStyle.textColor:'')+';')+('border-radius:'+(value.btnStyle.aroundRadius*2+'rpx')+';')}}">{{''+value.btnStyle.text+''}}</button></block></view></block></view></block></block><block wx:if="{{value.template=='horizontal-slide'}}"><block wx:if="{{value.slideMode=='scroll'}}"><scroll-view class="scroll" scroll-x="{{true}}" show-scrollbar="{{false}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g5}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl}}"><view class="{{['content',[(value.nameLineMode=='multiple')?'multi-content':'']]}}"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.priceStyle.mainControl}}"><view class="discount-price"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+item.g6[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+('.'+item.g7[1])+''}}</text></view></block></view></block></view></block></scroll-view></block><block wx:if="{{value.slideMode=='slide'}}"><swiper class="swiper" style="{{'height:'+(swiperHeight)+';'}}" autoplay="{{false}}"><block wx:for="{{$root.l3}}" wx:for-item="pageItem" wx:for-index="pageIndex" wx:key="pageIndex"><swiper-item class="{{['swiper-item',pageItem.g8]}}"><block wx:for="{{pageItem.l2}}" wx:for-item="item" wx:for-index="dataIndex" wx:key="dataIndex"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list.'+pageIndex+'','',dataIndex]]]]]]]}}" class="{{['item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="img-wrap" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g9}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[dataIndex]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl}}"><view class="{{['content',[(value.nameLineMode=='multiple')?'multi-content':'']]}}"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block><block wx:if="{{value.priceStyle.mainControl}}"><view class="discount-price"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">¥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+item.g10[0]+''}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor+'!important':'')+';'}}">{{''+('.'+item.g11[1])+''}}</text></view></block></view></block></view></block></swiper-item></block></swiper></block></block></view></block></x-skeleton></view></block>