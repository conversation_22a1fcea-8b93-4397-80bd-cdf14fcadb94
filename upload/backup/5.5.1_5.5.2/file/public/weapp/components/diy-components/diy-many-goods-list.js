(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-many-goods-list"],{"2ac6":function(t,e,o){},7245:function(t,e,o){"use strict";o.r(e);var i=o("d68b"),n=o("a7d4");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("ab64");var s=o("828b"),l=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"51fd0738",null,!1,i["a"],void 0);e["default"]=l.exports},"95c6":function(t,e,o){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"diy-many-goods-list",props:{value:{type:Object,default:function(){return{}}},index:{type:Number,default:0},scrollTop:{type:[Number,String]},global:{type:Object,default:function(){return{}}}},data:function(){return{cateIndex:0,goodsValue:null,manyInfo:{bodyHeight:0,bodyTop:0,height:0,top:0},initialTopNum:0}},created:function(){this.value.list&&this.value.list.length&&this.changeCateIndex(this.value.list[0],0,!0)},watch:{componentRefresh:function(t){this.value.list&&this.value.list.length&&this.changeCateIndex(this.value.list[0],0,!0)},scrollTop:function(e){var o=this,i=t.createSelectorQuery().in(this);i.select(".many-goods-list").boundingClientRect((function(t){t&&(o.manyInfo.top=t.top)})).exec(),i.select(".many-goods-list .many-goods-list-body").boundingClientRect((function(t){t&&(o.manyInfo.bodyHeight=t.height||0,o.manyInfo.bodyTop=t.top||0)})).exec()}},computed:{fixedTop:function(){var t=JSON.parse(JSON.stringify(this.$store.state.diyGroupPositionObj)),e=0,o=0;if(delete t.diyManyGoodsList,t){var i=Object.values(t);i.forEach((function(t,o){e+=t.originalVal}))}return this.manyInfo.top<e&&this.manyInfo.bodyTop+this.manyInfo.bodyHeight>e+Number.parseFloat(this.manyInfo.height)?(o=e,(!this.initialTopNum||this.initialTopNum>this.scrollTop)&&(this.initialTopNum=this.scrollTop)):this.initialTopNum=0,o},manyWrapCss:function(){var t="";return t+="position: ".concat(this.fixedTop?"fixed":"initial",";"),t+="top: ".concat(this.fixedTop,"px;"),this.global.topNavBg?t+="background-color: ".concat(this.fixedTop?this.global.topNavColor:"transparent",";"):t+="background-color: #fff;",t}},mounted:function(){var e=this,o=t.createSelectorQuery().in(this);o.select(".many-goods-list .many-goods-list-head").boundingClientRect((function(t){if(t){e.manyInfo.height=(t.height||0)+"px";var o={originalVal:t.height||0};e.$store.commit("setDiyGroupPositionObj",{diyManyGoodsList:o})}})).exec()},methods:{rpxToPx:function(e){var o=t.getSystemInfoSync().screenWidth;return o*Number.parseInt(e)/750},changeCateIndex:function(e,o,i){this.cateIndex=o,this.goodsValue={sources:e.sources,categoryId:e.categoryId,categoryName:e.categoryName,goodsId:e.goodsId,componentBgColor:this.value.componentBgColor,componentAngle:this.value.componentAngle,topAroundRadius:this.value.topAroundRadius,bottomAroundRadius:this.value.bottomAroundRadius,elementBgColor:this.value.elementBgColor,elementAngle:this.value.elementAngle,topElementAroundRadius:this.value.topElementAroundRadius,bottomElementAroundRadius:this.value.bottomElementAroundRadius,count:this.value.count,nameLineMode:this.value.nameLineMode,template:this.value.template,style:this.value.style,ornament:this.value.ornament,sortWay:this.value.sortWay,saleStyle:this.value.saleStyle,tag:this.value.tag,btnStyle:this.value.btnStyle,goodsNameStyle:this.value.goodsNameStyle,theme:this.value.theme,priceStyle:this.value.priceStyle,slideMode:this.value.slideMode,imgAroundRadius:this.value.imgAroundRadius,margin:this.value.margin,goodsMarginType:this.value.goodsMarginType,goodsMarginNum:this.value.goodsMarginNum},i||(this.$refs.diyGoodsList.goodsValue=this.goodsValue,this.fixedTop&&t.pageScrollTo({scrollTop:this.initialTopNum,duration:0}),this.$refs.diyGoodsList.init())}}};e.default=o}).call(this,o("df3c")["default"])},a7d4:function(t,e,o){"use strict";o.r(e);var i=o("95c6"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},ab64:function(t,e,o){"use strict";var i=o("2ac6"),n=o.n(i);n.a},d68b:function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return i}));var i={diyGoodsList:function(){return o.e("components/diy-components/diy-goods-list").then(o.bind(null,"88be"))}},n=function(){var t=this.$createElement,e=(this._self._c,this.value.list&&this.value.list.length);this.$mp.data=Object.assign({},{$root:{g0:e}})},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-many-goods-list-create-component',
    {
        'components/diy-components/diy-many-goods-list-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7245"))
        })
    },
    [['components/diy-components/diy-many-goods-list-create-component']]
]);
