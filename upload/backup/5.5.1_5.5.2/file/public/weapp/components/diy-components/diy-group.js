(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-group"],{"57a7":function(n,e,t){"use strict";t.r(e);var o=t("b8dd"),i=t.n(o);for(var d in o)["default"].indexOf(d)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(d);e["default"]=i.a},5996:function(n,e,t){"use strict";t.d(e,"b",(function(){return i})),t.d(e,"c",(function(){return d})),t.d(e,"a",(function(){return o}));var o={diyStore:function(){return Promise.all([t.e("common/vendor"),t.e("components/diy-components/diy-store")]).then(t.bind(null,"d191"))},diyStoreLabel:function(){return t.e("components/diy-components/diy-store-label").then(t.bind(null,"4675"))},diyText:function(){return t.e("components/diy-components/diy-text").then(t.bind(null,"0020"))},diyNotice:function(){return t.e("components/diy-components/diy-notice").then(t.bind(null,"bbba"))},diyGraphicNav:function(){return t.e("components/diy-components/diy-graphic-nav").then(t.bind(null,"37b9"))},diyImgAds:function(){return t.e("components/diy-components/diy-img-ads").then(t.bind(null,"0b8e"))},diySearch:function(){return t.e("components/diy-components/diy-search").then(t.bind(null,"fc1d"))},diyRichText:function(){return Promise.all([t.e("common/vendor"),t.e("components/diy-components/diy-rich-text")]).then(t.bind(null,"0bc6"))},diyHorzLine:function(){return t.e("components/diy-components/diy-horz-line").then(t.bind(null,"0edb"))},diyHorzBlank:function(){return t.e("components/diy-components/diy-horz-blank").then(t.bind(null,"7bf0"))},diyCoupon:function(){return t.e("components/diy-components/diy-coupon").then(t.bind(null,"218e"))},diyGoodsList:function(){return t.e("components/diy-components/diy-goods-list").then(t.bind(null,"88be"))},diyManyGoodsList:function(){return t.e("components/diy-components/diy-many-goods-list").then(t.bind(null,"7245"))},diyRubikCube:function(){return Promise.all([t.e("common/vendor"),t.e("components/diy-components/diy-rubik-cube")]).then(t.bind(null,"8c09"))},diyVideo:function(){return t.e("components/diy-components/diy-video").then(t.bind(null,"968c"))},diySeckill:function(){return t.e("components/diy-components/diy-seckill").then(t.bind(null,"83f4"))},diyPintuan:function(){return t.e("components/diy-components/diy-pintuan").then(t.bind(null,"cc5d"))},diyGroupbuy:function(){return t.e("components/diy-components/diy-groupbuy").then(t.bind(null,"fbd9"))},diyPinfan:function(){return t.e("components/diy-components/diy-pinfan").then(t.bind(null,"7605"))},diyBargain:function(){return t.e("components/diy-components/diy-bargain").then(t.bind(null,"94ef"))},diyPresale:function(){return t.e("components/diy-components/diy-presale").then(t.bind(null,"1871"))},diyNotes:function(){return t.e("components/diy-components/diy-notes").then(t.bind(null,"c7ec"))},diyFloatBtn:function(){return t.e("components/diy-components/diy-float-btn").then(t.bind(null,"2029"))},diyLive:function(){return t.e("components/diy-components/diy-live").then(t.bind(null,"20ba"))},diyFenxiaoGoodsList:function(){return t.e("components/diy-components/diy-fenxiao-goods-list").then(t.bind(null,"0b27"))},diyGoodsRecommend:function(){return t.e("components/diy-components/diy-goods-recommend").then(t.bind(null,"2f2e"))},diyGoodsBrand:function(){return t.e("components/diy-components/diy-goods-brand").then(t.bind(null,"350a"))},diyArticle:function(){return t.e("components/diy-components/diy-article").then(t.bind(null,"7447"))},diyMemberInfo:function(){return t.e("components/diy-components/diy-member-info").then(t.bind(null,"6303"))},diyMemberMyOrder:function(){return t.e("components/diy-components/diy-member-my-order").then(t.bind(null,"ef98"))},diyQuickNav:function(){return t.e("components/diy-components/diy-quick-nav").then(t.bind(null,"8f76"))},diyPaymentQrcode:function(){return t.e("components/diy-components/diy-payment-qrcode").then(t.bind(null,"08c4"))},diyHotArea:function(){return t.e("components/diy-components/diy-hot-area").then(t.bind(null,"c00d"))},diyFollowOfficialAccount:function(){return t.e("components/diy-components/diy-follow-official-account").then(t.bind(null,"937d"))}},i=function(){var n=this.$createElement;this._self._c},d=[]},"82ff":function(n,e,t){},a750:function(n,e,t){"use strict";t.r(e);var o=t("5996"),i=t("57a7");for(var d in i)["default"].indexOf(d)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(d);t("cb2d");var r=t("828b"),c=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=c.exports},b8dd:function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={components:{},props:{diyData:{type:Object},scrollTop:{type:[String,Number],default:"0"},haveTopCategory:{type:Boolean},followOfficialAccount:{type:Object},refresh:{type:Boolean}},data:function(){return{diyGlobalData:null}},watch:{diyData:{handler:function(n,e){this.diyGlobalData=JSON.parse(JSON.stringify(this.diyData))},deep:!0}},created:function(){this.diyGlobalData=JSON.parse(JSON.stringify(this.diyData))},computed:{topNavColor:function(){var n="";return this.diyData.global.topNavBg?(n="transparent",n=this.scrollTop>20?this.diyData.global.topNavColor:"transparent"):n=this.diyData.global.topNavColor,n},setPageStyle:function(){return this.diyGlobalData.value.forEach((function(n,e){if(n.pageStyle="",n.moduleIndex=e+1,"Search"==n.componentName&&"fixed"==n.positionWay)return!1;n.pageStyle+="background-color:"+n.pageBgColor+";",n.margin&&(n.pageStyle+="padding-top:"+2*n.margin.top+"rpx;",n.pageStyle+="padding-bottom:"+2*n.margin.bottom+"rpx;",n.pageStyle+="padding-right:"+2*n.margin.both+"rpx;",n.pageStyle+="padding-left:"+2*n.margin.both+"rpx;")})),this.diyGlobalData.value},diyDataArray:function(){var n=[],e=this.$store.state.diyGroupShowModule?JSON.parse(this.$store.state.diyGroupShowModule):"";if(e.length){if(e.includes("null"))return[];var t=this.setPageStyle;t.forEach((function(t,o){e.includes(t.componentName)&&n.push(t)}))}else n=this.setPageStyle;return n}},methods:{}};e.default=o},cb2d:function(n,e,t){"use strict";var o=t("82ff"),i=t.n(o);i.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-group-create-component',
    {
        'components/diy-components/diy-group-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("a750"))
        })
    },
    [['components/diy-components/diy-group-create-component']]
]);
