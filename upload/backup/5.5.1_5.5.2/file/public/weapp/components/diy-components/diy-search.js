(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-search"],{"09d0":function(e,t,n){},6559:function(e,t,n){"use strict";var o=n("09d0"),i=n.n(o);i.a},"6594a":function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n;e.getSystemInfoSync();n=e.getMenuButtonBoundingClientRect();var o={name:"diy-search",props:{value:{type:Object,default:function(){return{}}},topNavColor:String,global:{type:Object,default:function(){return{}}},haveTopCategory:{type:Boolean},followOfficialAccount:{type:Object}},data:function(){return{searchText:"",menuButtonInfo:n,height:0,placeholderHeight:0,moduleHeight:0}},computed:{fixedCss:function(){var e="";if("fixed"==this.value.positionWay){var t=this.fixedTop;this.global.topNavBg?e+="background-color:"+("transparent"==this.topNavColor?this.value.pageBgColor:this.topNavColor)+";":e+="background-color:"+this.value.pageBgColor+";",e+="top:"+t+";",e+="padding-top:"+2*this.value.margin.top+"rpx;",e+="padding-left:"+2*this.value.margin.both+"rpx;",e+="padding-right:"+2*this.value.margin.both+"rpx;",e+="padding-bottom:"+2*this.value.margin.bottom+"rpx;"}return e},searchWrapCss:function(){var e="";return e+="background-color:"+this.value.componentBgColor+";",e+="text-align:"+this.value.textAlign+";",e},inputStyle:function(){var e="";return e+="background-color:"+this.value.elementBgColor+";",2==this.value.borderType&&(e+="border-radius:40rpx;"),e},placeholderStyle:function(){var e="";return this.value.textColor?e+="color:"+this.value.textColor:e+="color: rgba(0,0,0,0)",e},fixedTop:function(){var e=this.$store.state.diyGroupPositionObj,t=0;return e.diySearch&&e.diyIndexPage&&e.nsNavbar?t=e.diySearch.moduleIndex>e.diyIndexPage.moduleIndex?e.nsNavbar.originalVal+e.diyIndexPage.originalVal:e.nsNavbar.originalVal:e.diySearch&&e.nsNavbar&&(t=e.nsNavbar.originalVal),t+="px",t}},watch:{componentRefresh:function(e){}},created:function(){var t=this;setTimeout((function(){var n=e.createSelectorQuery();n.select(".page-header >>> .u-navbar").boundingClientRect((function(e){t.global.navBarSwitch?t.height=e?e.height:45:t.height=e?e.height:0,t.haveTopCategory&&(t.height+=49)})).exec()})),"fixed"==this.value.positionWay&&this.navbarPlaceholderHeight()},mounted:function(){"fixed"==this.value.positionWay&&this.setModuleLocationFn()},methods:{search:function(){this.$util.redirectTo("/pages_tool/goods/search")},redirectTo:function(e){!e.wap_url||"pages/member/index"!=this.$util.getCurrRoute()||this.storeToken?this.$util.diyRedirectTo(e):this.$refs.login.open(e.wap_url)},navbarPlaceholderHeight:function(){var t=this;setTimeout((function(){var n=e.createSelectorQuery().in(t);n.select(".diy-search-wrap").boundingClientRect((function(e){t.placeholderHeight=e.height,t.placeholderHeight&&(t.placeholderHeight-=t.value.margin.bottom)})).exec()}))},setModuleLocationFn:function(){var t=this;this.$nextTick((function(){var n=e.createSelectorQuery().in(t);n.select(".diy-search-wrap").boundingClientRect((function(e){var n={originalVal:e.height||0,currVal:0,moduleIndex:t.value.moduleIndex};t.moduleHeight=(e.height||0)+"px",t.$store.commit("setDiyGroupPositionObj",{diySearch:n})})).exec()}))}}};t.default=o}).call(this,n("df3c")["default"])},"7aa6":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={diyIcon:function(){return n.e("components/diy-components/diy-icon").then(n.bind(null,"a68f"))},nsLogin:function(){return Promise.all([n.e("common/vendor"),n.e("components/ns-login/ns-login")]).then(n.bind(null,"2910"))}},i=function(){var e=this,t=e.$createElement,n=(e._self._c,[1,2].includes(e.value.searchStyle)),o=n&&2==e.value.searchStyle&&"img"==e.value.iconType?e.$util.img(e.value.imageUrl):null,i=3==e.value.searchStyle&&"img"==e.value.iconType?e.$util.img(e.value.imageUrl):null;e.$mp.data=Object.assign({},{$root:{g0:n,g1:o,g2:i}})},a=[]},f613:function(e,t,n){"use strict";n.r(t);var o=n("6594a"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},fc1d:function(e,t,n){"use strict";n.r(t);var o=n("7aa6"),i=n("f613");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n("6559");var r=n("828b"),l=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=l.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-search-create-component',
    {
        'components/diy-components/diy-search-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("fc1d"))
        })
    },
    [['components/diy-components/diy-search-create-component']]
]);
