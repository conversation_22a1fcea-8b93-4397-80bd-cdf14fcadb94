(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-index-page"],{"16eb":function(t,e,i){"use strict";i.r(e);var o=i("7556"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},"701d":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={uniPopup:function(){return i.e("components/uni-popup/uni-popup").then(i.bind(null,"d745"))},nsEmpty:function(){return i.e("components/ns-empty/ns-empty").then(i.bind(null,"52a6"))},nsLoading:function(){return i.e("components/ns-loading/ns-loading").then(i.bind(null,"10e0"))}},n=function(){var t=this,e=t.$createElement,i=(t._self._c,0!=t.pageIndex?t.$util.img(t.bgUrl):null),o=0!=t.pageIndex?"undefined"!=t.twoCategorylist&&t.twoCategorylist&&t.twoCategorylist.length>0:null,n=0!=t.pageIndex&&o?t.twoCategorylist.length:null,a=0!=t.pageIndex&&o&&n<=5?t.__map(t.twoCategorylist,(function(e,i){var o=t.__get_orig(e),n=e.image?t.$util.img(e.image):null,a=e.image?null:t.$util.getDefaultImage();return{$orig:o,g3:n,g4:a}})):null,s=0!=t.pageIndex&&o?t.twoCategorylist.length>5&&t.twoCategorylist.length<=10:null,r=0!=t.pageIndex&&o&&s?t.__map(t.twoCategorylist,(function(e,i){var o=t.__get_orig(e),n=e.image?t.$util.img(e.image):null,a=e.image?null:t.$util.getDefaultImage();return{$orig:o,g6:n,g7:a}})):null,l=0!=t.pageIndex&&o?t.twoCategorylist.length:null,g=0!=t.pageIndex&&t.cateList[t.pageIndex].image_adv?t.$util.img(t.cateList[t.pageIndex].image_adv):null,u=0!=t.pageIndex?t.goodsList[t.pageIndex].list.length:null,d=0!=t.pageIndex&&u?t.__map(t.goodsList[t.pageIndex].list,(function(e,i){var o=t.__get_orig(e),n=t.goodsImg(e.goods_image),a="default"==t.value.goodsTag&&""!=t.goodsTag(e),s=a?t.goodsTag(e):null,r="diy"==t.value.goodsTag?t.$util.img(t.value.tagImg.imageUrl):null,l=t.$lang("common.currencySymbol"),g=t.showPrice(e),u=e.member_price&&e.member_price==t.showPrice(e),d=u?t.$util.img("public/uniapp/index/VIP.png"):null,c=u||1!=e.promotion_type?null:t.$util.img("public/uniapp/index/discount.png"),p=t.showMarketPrice(e),h=p?t.$lang("common.currencySymbol"):null,m=p?t.showMarketPrice(e):null;return{$orig:o,m0:n,m1:a,m2:s,g11:r,m3:l,m4:g,m5:u,g12:d,g13:c,m6:p,m7:h,m8:m}})):null,c=0!=t.pageIndex?!t.isloading&&0==t.goodsList[t.pageIndex].list.length:null;t.$mp.data=Object.assign({},{$root:{g0:i,g1:o,g2:n,l0:a,g5:s,l1:r,g8:l,g9:g,g10:u,l2:d,g14:c}})},a=[]},7261:function(t,e,i){},7556:function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={props:{value:{type:Object},bgUrl:{type:String},scrollTop:{type:[String,Number],default:"0"},diyGlobal:{type:Object}},components:{nsLoading:function(){i.e("components/ns-loading/ns-loading").then(function(){return resolve(i("10e0"))}.bind(null,i)).catch(i.oe)}},data:function(){return{pageIndex:0,cateList:[{category_name:"首页"}],twoCategorylist:[],twoCategorylistId:0,goodsList:{},isloading:!0,top:0,isUnfold:!0,moduleHeight:""}},computed:{warpCss:function(){var t=(this.bgUrl?"background:url("+this.$util.img(this.bgUrl)+") no-repeat 0 0/100%":"")+";";return t},categoryCss:function(){var t="";return t+="top:"+this.fixedTop+";",t+="background-color:"+this.topNavColor+";",t},maxPage:function(){var t=0;return this.twoCategorylist&&this.twoCategorylist.length&&(t=Math.ceil(this.twoCategorylist.length/10)),t},type:function(){return!!this.value},topNavColor:function(){var t=this.value.componentBgColor||this.value.pageBgColor;return this.diyGlobal.topNavBg&&this.scrollTop>20&&(t=this.diyGlobal.topNavColor),t},fixedTop:function(){var t=this.$store.state.diyGroupPositionObj,e=0;return t.diySearch&&t.diyIndexPage&&t.nsNavbar?e=t.diySearch.moduleIndex>t.diyIndexPage.moduleIndex?t.nsNavbar.originalVal+"px":t.nsNavbar.originalVal+t.diySearch.originalVal+"px":t.diyIndexPage&&t.nsNavbar&&(e=t.nsNavbar.originalVal+"px"),e},uniPopTop:function(){var t=this.$store.state.diyGroupPositionObj,e="0";return this.fixedTop&&t.diyIndexPage&&(e=Number.parseFloat(this.fixedTop)+t.diyIndexPage.originalVal+"px"),e}},watch:{type:function(t,e){t&&this.getCategoryList()}},mounted:function(){var e=this;this.getCategoryList(),setTimeout((function(){var i=t.createSelectorQuery();e.top=20,i.select(".page-header >>> .u-navbar").boundingClientRect((function(t){var i;i=e.diyGlobal.navBarSwitch?t?t.height:45:t?t.height:0,e.top+=i})).exec()})),this.setModuleLocationFn()},methods:{initPageIndex:function(){this.pageIndex=0,this.showModuleFn()},getCategoryList:function(){var t=this;this.$api.sendRequest({url:"/api/goodscategory/tree",data:{level:3},success:function(e){if(e.code>=0){var i=[],o={list:[]};o.category_name=t.value.title?t.value.title:"首页",i.push(o),t.cateList=i.concat(e.data),Object.keys(t.cateList).forEach((function(e,i){t.goodsList[e]={page:1,list:[]}})),t.twoCategorylist=t.cateList[t.pageIndex].child_list}}})},changePageIndex:function(t){var e=this;this.isloading=!0,this.pageIndex=t,this.showModuleFn(),this.$emit("changeCategoryNav",t),0!=t&&(this.twoCategorylist=this.cateList[this.pageIndex].child_list,this.cateList[this.pageIndex].child_list?(this.twoCategorylist=this.cateList[this.pageIndex].child_list,this.twoCategorylist.forEach((function(t){t.image?t.image=e.$util.img(t.image):t.image=e.$util.getDefaultImage().goods}))):this.twoCategorylist=!1,this.$refs.mescroll&&(this.$refs.mescroll.refresh(),this.$refs.mescroll.myScrollTo(0)))},swiperToCategoryChange:function(t){this.twoCategorylistId=t.detail.current},toDetail:function(t){this.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})},getGoodsList:function(t){var e=this,i=this.pageIndex,o={page:t.num,page_size:t.size};o.category_id=this.cateList[this.pageIndex].category_id_1,o.category_level=1,this.$api.sendRequest({url:"/api/goodssku/page",data:o,success:function(o){e.isloading=!1;var n=[],a=o.message;0==o.code&&o.data?(e.count=o.data.count,n=o.data.list):e.$util.showToast({title:a}),t.endSuccess(n.length),1==t.num&&(e.goodsList[i].list=[]),e.goodsList[i].list=e.goodsList[i].list.concat(n),e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.$forceUpdate()}})},toCateGoodsList:function(t,e){this.$util.redirectTo("/pages/goods/list",{category_id:t,category_level:e})},goodsImg:function(t){var e=t.split(",");return e[0]?this.$util.img(e[0],{size:"mid"}):this.$util.getDefaultImage().goods},imgError:function(t){this.goodsList[t].goods_image=this.$util.getDefaultImage().goods},showPrice:function(t){var e=t.discount_price;return t.member_price&&parseFloat(t.member_price)<parseFloat(e)&&(e=t.member_price),e},showMarketPrice:function(t){if(t.market_price_show){var e=this.showPrice(t);if(t.market_price>0)return t.market_price;if(parseFloat(t.price)>parseFloat(e))return t.price}return""},goodsTag:function(t){return t.label_name||""},unfoldMenu:function(){this.isUnfold?this.$refs.navTopCategoryPop.open():this.$refs.navTopCategoryPop.close(),this.isUnfold=!this.isUnfold},setModuleLocationFn:function(){var e=this,i=t.createSelectorQuery().in(this);i.select(".nav-top-category").boundingClientRect((function(t){var i={originalVal:t.height||0,moduleIndex:e.value.moduleIndex};e.moduleHeight=(t.height||0)+"px",e.$store.commit("setDiyGroupPositionObj",{diyIndexPage:i})})).exec()},showModuleFn:function(){var t=this.$root.diyData.value.filter((function(t,e){return"Search"==t.componentName}));0==this.pageIndex?this.$store.commit("setDiyGroupShowModule",JSON.stringify([])):"fixed"==t[0].positionWay?this.$store.commit("setDiyGroupShowModule",JSON.stringify(["Search"])):this.$store.commit("setDiyGroupShowModule",JSON.stringify(["null"]))}}};e.default=o}).call(this,i("df3c")["default"])},"7ea5d":function(t,e,i){"use strict";i.r(e);var o=i("701d"),n=i("16eb");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("e05f");var s=i("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=r.exports},e05f:function(t,e,i){"use strict";var o=i("7261"),n=i.n(o);n.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-index-page-create-component',
    {
        'components/diy-components/diy-index-page-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7ea5d"))
        })
    },
    [['components/diy-components/diy-index-page-create-component']]
]);
