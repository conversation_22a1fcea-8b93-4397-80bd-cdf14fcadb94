<block wx:if="{{$root.g0}}"><view style="{{(value.pageStyle)}}"><block wx:if="{{$root.g1}}"><view class="{{['diy-fenxiao','goods-list',value.template,value.style]}}" style="{{(goodsListWarpCss)}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['list','',index]]]]]]]}}" class="{{['goods-item',value.ornament.type]}}" style="{{(goodsItemCss)}}" bindtap="__e"><view class="goods-img" style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}"><image style="{{'border-radius:'+(value.imgAroundRadius*2+'rpx')+';'}}" src="{{item.g2}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image></view><block wx:if="{{value.goodsNameStyle.control||value.priceStyle.mainControl||value.priceStyle.lineControl||value.btnStyle.control}}"><view class="info-wrap"><view class="name-wrap"><block wx:if="{{value.goodsNameStyle.control}}"><view class="{{['goods-name',[(value.nameLineMode=='single')?'using-hidden':''],[(value.nameLineMode=='multiple')?'multi-hidden':'']]}}" style="{{'color:'+(value.theme=='diy'?value.goodsNameStyle.color:'')+';'+('font-weight:'+(value.goodsNameStyle.fontWeight?'bold':'')+';')}}">{{''+item.$orig.goods_name+''}}</view></block></view><view class="pro-info"><view class="discount-price"><block wx:if="{{value.priceStyle.mainControl}}"><view class="price-wrap"><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">赚 ￥</text><text class="price price-style large" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">{{item.g3[0]}}</text><text class="unit price-style small" style="{{'color:'+(value.theme=='diy'?value.priceStyle.mainColor:'')+';'}}">{{'.'+item.g4[1]}}</text></view></block><block wx:if="{{value.btnStyle.control&&item.$orig.is_collect==0}}"><view data-event-opts="{{[['tap',[['followGoods',['$0',index],[[['list','',index]]]]]]]}}" class="sale-btn" style="{{'background:'+(value.btnStyle.theme=='diy'?'linear-gradient(to right,'+value.btnStyle.bgColorStart+','+value.btnStyle.bgColorEnd+')':'')+';'+('color:'+(value.btnStyle.theme=='diy'?value.btnStyle.textColor:'')+';')+('border-radius:'+(value.btnStyle.aroundRadius*2+'rpx')+';')}}" catchtap="__e">关注</view></block><block wx:if="{{value.btnStyle.control&&item.$orig.is_collect==1}}"><view data-event-opts="{{[['tap',[['delFollowTip',['$0',index],[[['list','',index]]]]]]]}}" class="sale-btn" style="{{'background:'+(value.btnStyle.theme=='diy'?'linear-gradient(to right,'+value.btnStyle.bgColorStart+','+value.btnStyle.bgColorEnd+')':'')+';'+('color:'+(value.btnStyle.theme=='diy'?value.btnStyle.textColor:'')+';')+('border-radius:'+(value.btnStyle.aroundRadius*2+'rpx')+';')}}" catchtap="__e">取消关注</view></block></view><block wx:if="{{value.priceStyle.lineControl}}"><view class="delete-price" style="{{'color:'+(value.theme=='diy'?value.priceStyle.lineColor:'')+';'}}">{{'￥'+item.$orig.discount_price+''}}</view></block></view></view></block></view></block></view></block></view></block>