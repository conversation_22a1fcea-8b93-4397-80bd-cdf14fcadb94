<view style="{{(value.pageStyle)}}"><view style="{{(componentStyle)}}"><block wx:if="{{value.showStyle=='pageSlide'}}"><block><swiper class="{{['graphic-nav','pageSlide',value.carousel.type]}}" style="{{(swiperHeight)}}" circular="{{true}}" indicator-dots="{{false}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l1}}" wx:for-item="numItem" wx:for-index="numIndex"><swiper-item class="graphic-nav-wrap"><block wx:for="{{numItem.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index>=[numItem.$orig*(value.pageCount*value.rowCount)]&&index<[(numItem.$orig+1)*(value.pageCount*value.rowCount)]}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="{{['graphic-nav-item',value.mode]}}" style="{{'width:'+(100/value.rowCount+'%')+';'}}" bindtap="__e"><block wx:if="{{value.mode!='text'}}"><view class="graphic-img" style="{{'font-size:'+(value.imageSize*2+'rpx')+';'+('width:'+(value.imageSize*2+'rpx')+';')+('height:'+(value.imageSize*2+'rpx')+';')}}"><block wx:if="{{item.$orig.iconType=='img'}}"><image style="{{'max-width:'+(value.imageSize*2+'rpx')+';'+('max-height:'+(value.imageSize*2+'rpx')+';')+('border-radius:'+(value.aroundRadius*2+'rpx')+';')}}" src="{{item.g0}}" mode="aspectFill" show-menu-by-longpress="{{true}}"></image></block><block wx:if="{{item.$orig.iconType=='icon'}}"><diy-icon style="{{'max-width:'+(value.imageSize*2+'rpx')+';'+('max-height:'+(value.imageSize*2+'rpx')+';')+('width:'+('100%')+';')+('height:'+('100%')+';')}}" vue-id="{{'787286a6-1-'+numIndex+'-'+index}}" icon="{{item.$orig.icon}}" value="{{item.$orig.style?item.$orig.style:null}}" bind:__l="__l"></diy-icon></block><block wx:if="{{item.$orig.label.control}}"><text class="tag" style="{{'color:'+(item.$orig.label.textColor)+';'+('background-image:'+('linear-gradient('+item.$orig.label.bgColorStart+','+item.$orig.label.bgColorEnd+')')+';')}}">{{''+item.$orig.label.text+''}}</text></block></view></block><block wx:if="{{value.mode!='img'}}"><text class="graphic-text" style="{{'font-size:'+(value.font.size*2+'rpx')+';'+('font-weight:'+(value.font.weight)+';')+('color:'+(value.font.color)+';')}}">{{''+item.$orig.title+''}}</text></block></view></block></block></swiper-item></block></swiper><block wx:if="{{isIndicatorDots}}"><view class="{{['swiper-dot-box',value.carousel.type]}}"><block wx:for="{{$root.l2}}" wx:for-item="numItem" wx:for-index="numIndex" wx:key="numIndex"><view><view class="{{['swiper-dot',(numIndex==swiperCurrent)?'active':'']}}"></view></view></block></view></block></block></block><block wx:else><scroll-view class="{{['graphic-nav',value.showStyle=='fixed'?'fixed-layout':value.showStyle]}}" scroll-x="{{value.showStyle=='singleSlide'}}"><view class="uni-scroll-view-content"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['redirectTo',['$0'],[[['value.list','',index,'link']]]]]]]}}" class="{{['graphic-nav-item',value.mode]}}" style="{{'width:'+(100/value.rowCount+'%')+';'}}" bindtap="__e"><block wx:if="{{value.mode!='text'}}"><view class="graphic-img" style="{{'font-size:'+(value.imageSize*2+'rpx')+';'+('width:'+(value.imageSize*2+'rpx')+';')+('height:'+(value.imageSize*2+'rpx')+';')}}"><block wx:if="{{item.$orig.iconType=='img'}}"><image style="{{'max-width:'+(value.imageSize*2+'rpx')+';'+('max-height:'+(value.imageSize*2+'rpx')+';')+('border-radius:'+(value.aroundRadius*2+'rpx')+';')}}" src="{{item.g1}}" mode="aspectFill" show-menu-by-longpress="{{true}}"></image></block><block wx:if="{{item.$orig.iconType=='icon'}}"><diy-icon style="{{'max-width:'+(value.imageSize*2+'rpx')+';'+('max-height:'+(value.imageSize*2+'rpx')+';')+('width:'+('100%')+';')+('height:'+('100%')+';')}}" vue-id="{{'787286a6-2-'+index}}" icon="{{item.$orig.icon}}" value="{{item.$orig.style?item.$orig.style:null}}" bind:__l="__l"></diy-icon></block><block wx:if="{{item.$orig.label.control}}"><text class="{{['tag',[(value.mode=='text')?'alone':'']]}}" style="{{'color:'+(item.$orig.label.textColor)+';'+('background-image:'+('linear-gradient('+item.$orig.label.bgColorStart+','+item.$orig.label.bgColorEnd+')')+';')}}">{{''+item.$orig.label.text+''}}</text></block></view></block><block wx:if="{{value.mode!='img'}}"><text class="graphic-text" style="{{'font-size:'+(value.font.size*2+'rpx')+';'+('font-weight:'+(value.font.weight)+';')+('color:'+(value.font.color)+';')}}">{{''+item.$orig.title+''}}</text></block></view></block></view></scroll-view></block><ns-login class="vue-ref" vue-id="787286a6-3" data-ref="login" bind:__l="__l"></ns-login></view></view>