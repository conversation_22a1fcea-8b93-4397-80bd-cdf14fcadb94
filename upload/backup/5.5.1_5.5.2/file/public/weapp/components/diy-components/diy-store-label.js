(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/diy-components/diy-store-label"],{1548:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"diy-store-label",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{loading:!0,skeletonConfig:{gridRows:1,gridRowsGap:"20rpx",headHeight:"40rpx",headBorderRadius:"0"},list:[],notice:"",storeLabel:[],businessConfig:""}},created:function(){this.getData(),this.getStoreConfig()},watch:{componentRefresh:function(t){this.getData()}},computed:{storeLabelWrapCss:function(){var t="";return t+="background-color:"+this.value.componentBgColor+";","round"==this.value.componentAngle&&(t+="border-top-left-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-top-right-radius:"+2*this.value.topAroundRadius+"rpx;",t+="border-bottom-left-radius:"+2*this.value.bottomAroundRadius+"rpx;",t+="border-bottom-right-radius:"+2*this.value.bottomAroundRadius+"rpx;"),t}},methods:{getData:function(){var t=this,e={page:1,page_size:0};"initial"==this.value.sources?e.page_size=this.value.count:"diy"==this.value.sources&&(e.label_id_arr=this.value.labelIds.toString()),this.$api.sendRequest({url:"/store/api/store/labelPage",data:e,success:function(e){0==e.code&&e.data&&(t.list=e.data.list),t.loading=!1}})},getStoreConfig:function(){var t=this;this.$api.sendRequest({url:"/store/api/config/config",success:function(e){e.code>=0&&(t.businessConfig=e.data.business_config,"store"==e.data.business_config.store_business&&t.getStoreInfo())}})},getStoreInfo:function(){var t=this;this.$api.sendRequest({url:"/api/store/info",success:function(e){if(e.data){var n=e.data.label_name.split(","),o=3;"initial"==t.value.sources&&(o=t.value.count);for(var s=0;s<n.length;s++)t.storeLabel.length<o&&""!=n[s]&&t.storeLabel.push(n[s])}}})}}};e.default=o},"2c59":function(t,e,n){"use strict";n.d(e,"b",(function(){return s})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={xSkeleton:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/x-skeleton/components/x-skeleton/x-skeleton")]).then(n.bind(null,"3268"))},diyIcon:function(){return n.e("components/diy-components/diy-icon").then(n.bind(null,"a68f"))}},s=function(){var t=this.$createElement,e=(this._self._c,"store"==this.businessConfig.store_business?this.list.length:null),n="store"!=this.businessConfig.store_business?this.list.length:null;this.$mp.data=Object.assign({},{$root:{g0:e,g1:n}})},i=[]},"3fa8":function(t,e,n){"use strict";var o=n("66b6"),s=n.n(o);s.a},4675:function(t,e,n){"use strict";n.r(e);var o=n("2c59"),s=n("8252");for(var i in s)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(i);n("3fa8");var a=n("828b"),r=Object(a["a"])(s["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=r.exports},"66b6":function(t,e,n){},8252:function(t,e,n){"use strict";n.r(e);var o=n("1548"),s=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=s.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/diy-components/diy-store-label-create-component',
    {
        'components/diy-components/diy-store-label-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("4675"))
        })
    },
    [['components/diy-components/diy-store-label-create-component']]
]);
