(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/ns-login/ns-login"],{"0864":function(e,t,i){"use strict";var a=i("5e56"),o=i.n(a);o.a},1850:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){return a}));var a={uniPopup:function(){return i.e("components/uni-popup/uni-popup").then(i.bind(null,"d745"))},registerReward:function(){return i.e("components/register-reward/register-reward").then(i.bind(null,"349c"))}},o=function(){var e=this,t=e.$createElement,i=(e._self._c,e.avatarUrl?null:e.$util.getDefaultImage());e._isMounted||(e.e0=function(t){e.avatarUrl=e.$util.getDefaultImage().head}),e.$mp.data=Object.assign({},{$root:{g0:i}})},n=[]},2910:function(e,t,i){"use strict";i.r(t);var a=i("1850"),o=i("5a10");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(n);i("fdfa"),i("0864");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"41b8aa87",null,!1,a["a"],void 0);t["default"]=r.exports},"5a10":function(e,t,i){"use strict";i.r(t);var a=i("d346"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},"5e56":function(e,t,i){},"8e52":function(e,t,i){},d346:function(e,t,i){"use strict";(function(e){var a=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("0713")),n=a(i("fe8d")),s={mixins:[o.default],name:"ns-login",components:{uniPopup:function(){i.e("components/uni-popup/uni-popup").then(function(){return resolve(i("d745"))}.bind(null,i)).catch(i.oe)},registerReward:function(){i.e("components/register-reward/register-reward").then(function(){return resolve(i("349c"))}.bind(null,i)).catch(i.oe)}},data:function(){return{url:"",registerConfig:{},avatarUrl:"",headImg:"",nickName:"",isSub:!1,isOpenCaptcha:0,captcha:{id:"",img:""},formData:{key:"",mobile:"",vercode:"",dynacode:""},dynacodeData:{seconds:120,timer:null,codeText:"获取动态码",isSend:!1},authMobileData:{iv:"",encryptedData:""}}},options:{styleIsolation:"shared"},mounted:function(){},watch:{"dynacodeData.seconds":{handler:function(e,t){0==e&&this.refreshDynacodeData()},immediate:!0,deep:!0}},computed:{isDisabled:function(){return 0==this.nickName.length||!(!this.forceBindingMobileControl||0!=this.formData.mobile.length)},forceBindingMobileControl:function(){return!(!this.registerConfig||1!=this.registerConfig.third_party||1!=this.registerConfig.bind_mobile)},wechatConfigStatus:function(){return this.$store.state.wechatConfigStatus}},methods:{getRegisterConfig:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.$api.sendRequest({url:"/api/register/config",success:function(i){i.code>=0&&(e.registerConfig=i.data.value,t&&t())}})},open:function(t){var i=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t||(t=this.$util.getCurrentRoute().path),this.url=t,this.url&&e.setStorageSync("initiateLogin",this.url),a?this.getCode((function(e){i.authLogin(e,"authOnlyLogin")})):this.toLogin()},toLogin:function(){this.url?this.$util.redirectTo("/pages_tool/login/index",{back:encodeURIComponent(this.url)}):this.$util.redirectTo("/pages_tool/login/index")},cancelCompleteInfo:function(){this.$refs.completeInfoPopup&&this.$refs.completeInfoPopup.close(),this.$store.commit("setBottomNavHidden",!1)},blurNickName:function(e){e.detail.value&&(this.nickName=e.detail.value)},onChooseAvatar:function(t){var i=this;this.avatarUrl=t.detail.avatarUrl,e.getFileSystemManager().readFile({filePath:this.avatarUrl,encoding:"base64",success:function(e){var t="data:image/jpeg;base64,"+e.data;i.$api.uploadBase64({base64:t,success:function(e){0==e.code?i.headImg=e.data.pic_path:i.$util.showToast({title:e.message})},fail:function(){i.$util.showToast({title:"上传失败"})}})}})},openCompleteInfoPop:function(){var e=this;this.getRegisterConfig(),this.$refs.completeInfoPopup.open((function(){e.$store.commit("setBottomNavHidden",!1)})),this.$store.commit("setBottomNavHidden",!0)},getCaptchaConfig:function(){var e=this;this.$api.sendRequest({url:"/api/config/getCaptchaConfig",success:function(t){t.code>=0&&(e.isOpenCaptcha=t.data.shop_reception_login,e.isOpenCaptcha&&e.getCaptcha())}})},getCaptcha:function(){var e=this;this.$api.sendRequest({url:"/api/captcha/captcha",data:{captcha_id:this.captcha.id},success:function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))}})},sendMobileCode:function(){var e=this;if(120==this.dynacodeData.seconds&&!this.dynacodeData.isSend){var t={mobile:this.formData.mobile,captcha_id:this.captcha.id,captcha_code:this.formData.vercode},i=[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}];1==this.isOpenCaptcha&&i.push({name:"captcha_code",checkType:"required",errorMsg:"请输入验证码"});var a=n.default.check(t,i);a?(this.dynacodeData.isSend=!0,120==this.dynacodeData.seconds&&(this.dynacodeData.timer=setInterval((function(){e.dynacodeData.seconds--,e.dynacodeData.codeText=e.dynacodeData.seconds+"s后可重新获取"}),1e3)),this.$api.sendRequest({url:"/api/tripartite/mobileCode",data:t,success:function(t){t.code>=0?e.formData.key=t.data.key:(e.$util.showToast({title:t.message}),e.refreshDynacodeData())},fail:function(){e.$util.showToast({title:"request:fail"}),e.refreshDynacodeData()}})):this.$util.showToast({title:n.default.error})}},refreshDynacodeData:function(){this.getCaptcha(),clearInterval(this.dynacodeData.timer),this.dynacodeData={seconds:120,timer:null,codeText:"获取动态码",isSend:!1}},verify:function(e){var t=[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}];1==this.isOpenCaptcha&&""!=this.captcha.id&&t.push({name:"captcha_code",checkType:"required",errorMsg:"请输入验证码"}),t.push({name:"code",checkType:"required",errorMsg:"请输入动态码"});var i=n.default.check(e,t);return!!i||(this.$util.showToast({title:n.default.error}),!1)},forceBindMobile:function(){var t=this,i=e.getStorageSync("authInfo"),a={mobile:this.formData.mobile,key:this.formData.key,code:this.formData.dynacode};""!=this.captcha.id&&(a.captcha_id=this.captcha.id,a.captcha_code=this.formData.vercode),i&&Object.assign(a,i),i.avatarUrl&&(a.headimg=i.avatarUrl),i.nickName&&(a.nickname=i.nickName),e.getStorageSync("source_member")&&(a.source_member=e.getStorageSync("source_member")),this.isSub||(this.isSub=!0,this.$api.sendRequest({url:"/api/tripartite/mobile",data:a,success:function(e){e.code>=0?(t.$store.commit("setToken",e.data.token),t.getMemberInfo(),t.$store.dispatch("getCartNumber"),t.$refs.completeInfoPopup.close(),t.$store.commit("setBottomNavHidden",!1),e.data.is_register&&t.$refs.registerReward.open(t.url)):(t.isSub=!1,t.getCaptcha(),t.$util.showToast({title:e.message}))},fail:function(e){t.isSub=!1,t.getCaptcha()}}))},getPhoneNumber:function(t){var i=this;if("getPhoneNumber:ok"==t.detail.errMsg){var a=e.getStorageSync("authInfo");a&&Object.assign(this.authMobileData,a,t.detail),e.getStorageSync("source_member")&&(this.authMobileData.source_member=e.getStorageSync("source_member")),this.$api.sendRequest({url:"/api/tripartite/getPhoneNumber",data:this.authMobileData,success:function(e){e.code>=0?i.formData.mobile=e.data.mobile:(i.formData.mobile="",i.$util.showToast({title:e.message}))}})}else this.$util.showToast({title:"为了保证您账户的统一性，取消授权将无法为您提供服务"})},bindMobile:function(){var t=this,i=this.authMobileData,a=e.getStorageSync("authInfo");a&&Object.assign(i,a),a.avatarUrl&&(i.headimg=a.avatarUrl),a.nickName&&(i.nickname=a.nickName),this.$api.sendRequest({url:"/api/tripartite/mobileauth",data:i,success:function(e){e.code>=0?(t.$store.commit("setToken",e.data.token),t.getMemberInfo(),t.$store.dispatch("getCartNumber"),t.cancelCompleteInfo(),e.data.is_register&&t.$refs.registerReward.open(t.url)):t.$util.showToast({title:e.message})},fail:function(e){t.$util.showToast({title:"request:fail"})}})},authLogin:function(t){var i=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"authLogin";e.showLoading({title:"登录中"}),e.setStorageSync("authInfo",t),e.getStorageSync("source_member")&&(t.source_member=e.getStorageSync("source_member")),this.$api.sendRequest({url:"authLogin"==a?"/api/login/auth":"/api/login/authonlylogin",data:t,success:function(t){t.code>=0?(i.$store.commit("setToken",t.data.token),i.getMemberInfo(),i.$store.dispatch("getCartNumber"),t.data.is_register?i.$refs.registerReward.open(i.url):i.url?i.$util.loginComplete(i.url,{},"redirectTo"):i.$util.loginComplete("/pages/member/index/index",{},"redirectTo"),i.cancelCompleteInfo(),setTimeout((function(){e.hideLoading()}),1e3)):"MEMBER_NOT_EXIST"==t.data?i.getRegisterConfig((function(){e.hideLoading(),1==i.registerConfig.third_party&&1==i.registerConfig.bind_mobile?i.openCompleteInfoPop():0==i.registerConfig.third_party?i.toLogin():i.openCompleteInfoPop()})):(e.hideLoading(),i.$util.showToast({title:t.message}))},fail:function(){e.hideLoading(),i.$util.showToast({title:"登录失败"})}})},saveH5:function(){if(this.$util.isWeiXin()&&this.forceBindingMobileControl){var e={mobile:this.formData.mobile,key:this.formData.key,code:this.formData.dynacode};if(""!=this.captcha.id&&(e.captcha_id=this.captcha.id,e.captcha_code=this.formData.vercode),!this.verify(e))return}this.forceBindMobile()},saveMp:function(){if(0!=this.nickName.length){var t=e.getStorageSync("authInfo");t&&Object.assign(t,{nickName:this.nickName,avatarUrl:this.headImg}),e.setStorageSync("authInfo",t),this.forceBindingMobileControl?this.bindMobile():this.authLogin(t)}else this.$util.showToast({title:"请输入昵称"})},getMemberInfo:function(){var e=this;this.$api.sendRequest({url:"/api/member/info",success:function(t){t.code>=0&&e.$store.commit("setMemberInfo",t.data)}})}}};t.default=s}).call(this,i("df3c")["default"])},fdfa:function(e,t,i){"use strict";var a=i("8e52"),o=i.n(a);o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ns-login/ns-login-create-component',
    {
        'components/ns-login/ns-login-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("2910"))
        })
    },
    [['components/ns-login/ns-login-create-component']]
]);
