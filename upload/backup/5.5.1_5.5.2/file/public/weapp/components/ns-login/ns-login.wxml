<view class="data-v-41b8aa87"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="complete-info-popup data-v-41b8aa87" catchtouchmove="__e"><uni-popup vue-id="29022e62-1" type="bottom" maskClick="{{false}}" data-ref="completeInfoPopup" class="data-v-41b8aa87 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="complete-info-wrap data-v-41b8aa87"><view class="head data-v-41b8aa87"><text class="title data-v-41b8aa87">获取您的昵称、头像<block wx:if="{{forceBindingMobileControl}}">、手机号</block></text><text class="color-tip tips data-v-41b8aa87">获取用户头像、昵称<block wx:if="{{forceBindingMobileControl}}">、手机号</block>完善个人资料，主要用于向用户提供具有辨识度的用户中心界面</text><text data-event-opts="{{[['tap',[['cancelCompleteInfo',['$event']]]]]}}" class="iconfont icon-close color-tip data-v-41b8aa87" bindtap="__e"></text></view><view class="item-wrap data-v-41b8aa87"><text class="label data-v-41b8aa87">头像</text><button open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e" class="data-v-41b8aa87"><image src="{{avatarUrl?avatarUrl:$root.g0.head}}" mode="aspectFill" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e" class="data-v-41b8aa87"></image><text class="iconfont icon-right color-tip data-v-41b8aa87"></text></button></view><view class="item-wrap data-v-41b8aa87"><text class="label data-v-41b8aa87">昵称</text><input type="nickname" placeholder="请输入昵称" maxlength="50" data-event-opts="{{[['blur',[['blurNickName',['$event']]]],['input',[['__set_model',['','nickName','$event',[]]]]]]}}" value="{{nickName}}" bindblur="__e" bindinput="__e" class="data-v-41b8aa87"/></view><block wx:if="{{forceBindingMobileControl}}"><view class="item-wrap data-v-41b8aa87"><text class="label data-v-41b8aa87">手机号</text><button class="auth-login border-0 data-v-41b8aa87" open-type="getPhoneNumber" plain="{{true}}" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e"><block wx:if="{{formData.mobile}}"><text class="mobile data-v-41b8aa87">{{formData.mobile}}</text></block><block wx:else><text class="color-base-text data-v-41b8aa87">获取手机号</text></block></button></view></block><button class="save-btn data-v-41b8aa87" type="default" disabled="{{isDisabled}}" data-event-opts="{{[['tap',[['saveMp',['$event']]]]]}}" bindtap="__e">保存</button></view></uni-popup></view><privacy-popup vue-id="29022e62-2" data-ref="privacyPopup" class="data-v-41b8aa87 vue-ref" bind:__l="__l"></privacy-popup><register-reward vue-id="29022e62-3" data-ref="registerReward" class="data-v-41b8aa87 vue-ref" bind:__l="__l"></register-reward></view>