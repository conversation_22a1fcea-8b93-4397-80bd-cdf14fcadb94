<view class="ns-time data-v-5ed57989"><uni-popup vue-id="a1d3b5fc-1" type="bottom" data-ref="selectTime" class="data-v-5ed57989 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-5ed57989"><view class="title data-v-5ed57989"><block wx:if="{{obj.delivery&&obj.delivery.delivery_type=='local'}}"><block class="data-v-5ed57989">选择送达时间</block></block><block wx:if="{{obj.delivery&&obj.delivery.delivery_type=='store'}}"><block class="data-v-5ed57989">选择自提时间</block></block><text data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="iconfont icon-close data-v-5ed57989" bindtap="__e"></text></view><view class="body data-v-5ed57989"><scroll-view class="left data-v-5ed57989" scroll-y="{{true}}"><block wx:for="{{dayData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectTime',['days',index,'yes']]]]]}}" class="{{['item','data-v-5ed57989',index==keyJudge?'itemDay':'']}}" bindtap="__e"><block wx:if="{{item.title}}"><block class="data-v-5ed57989">{{item.title}}</block></block><block wx:else><block class="data-v-5ed57989">{{item.month}}</block></block><text class="itemtext data-v-5ed57989">{{item.Day}}</text></view></block></scroll-view><scroll-view class="right data-v-5ed57989" scroll-y="{{true}}"><block wx:for="{{timeData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectTime',['time',index,'yes']]]]]}}" class="{{['item','data-v-5ed57989',key==keyJudge&&index==keys?'itemTime':'']}}" bindtap="__e">{{''+item+''}}<block wx:if="{{key==keyJudge&&index==keys}}"><text class="iconfont icon-yuan_checked color-base-text data-v-5ed57989"></text></block></view></block></scroll-view></view></view></uni-popup></view>