<block wx:if="{{showPopup}}"><view class="uni-popup"><view data-event-opts="{{[['tap',[['close',[true]]]]]}}" class="{{['uni-popup__mask',ani,animation?'ani':'',!custom?'uni-custom':'']}}" bindtap="__e"></view><block wx:if="{{isIphoneX}}"><view data-event-opts="{{[['tap',[['close',[true]]]]]}}" class="{{['uni-popup__wrapper','safe-area',type,ani,animation?'ani':'',!custom?'uni-custom':'']}}" bindtap="__e"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="uni-popup__wrapper-box" catchtap="__e"><slot></slot></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['close',[true]]]]]}}" class="{{['uni-popup__wrapper',type,ani,animation?'ani':'',!custom?'uni-custom':'']}}" bindtap="__e"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="uni-popup__wrapper-box" catchtap="__e"><slot></slot></view></view></block></view></block>