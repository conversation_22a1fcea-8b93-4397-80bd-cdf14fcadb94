<block wx:if="{{width}}"><view class="uni-grid-item" style="{{'width:'+(width)+';'}}"><view data-event-opts="{{[['tap',[['_onClick',['$event']]]]]}}" class="{{['uni-grid-item__box',(showBorder)?'border':'',(square)?'uni-grid-item__box-square':'',(showBorder&&index<column)?'border-top':'',(highlight)?'uni-highlight':'']}}" style="{{'border-color:'+(borderColor)+';'}}" bindtap="__e"><block wx:if="{{marker==='dot'}}"><view class="uni-grid-item__box-dot" style="{{'left:'+(top*2+'rpx')+';'+('top:'+(left*2+'rpx')+';')}}"></view></block><block wx:if="{{marker==='badge'}}"><view class="uni-grid-item__box-badge" style="{{'left:'+(top*2+'rpx')+';'+('top:'+(left*2+'rpx')+';')}}"><uni-badge vue-id="5a30a7c6-1" text="{{text}}" type="{{type}}" size="{{size}}" inverted="{{inverted}}" bind:__l="__l"></uni-badge></view></block><block wx:if="{{marker==='image'}}"><view class="uni-grid-item__box-image" style="{{'left:'+(top*2+'rpx')+';'+('top:'+(left*2+'rpx')+';')}}"><image class="box-image" style="{{'width:'+(imgWidth*2+'rpx')+';'}}" src="{{src}}" mode="widthFix"></image></view></block><view class="uni-grid-item__box-item"><slot></slot></view></view></view></block>