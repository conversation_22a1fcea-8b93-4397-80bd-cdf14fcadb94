(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/pick-regions/pick-regions"],{"04c1":function(e,t,r){"use strict";r.r(t);var a=r("fbb7"),i=r("6b13");for(var n in i)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(n);r("b514");var l=r("828b"),u=Object(l["a"])(i["default"],a["b"],a["c"],!1,null,"1f20d2d5",null,!1,a["a"],void 0);t["default"]=u.exports},1984:function(e,t,r){"use strict";var a=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(r("7eb4")),n=a(r("34cf")),l=a(r("ee10")),u={props:{defaultRegions:{type:Array},selectArr:{type:String}},data:function(){return{pickerValueArray:[],cityArr:[],districtArr:[],multiIndex:[0,0,0],isInitMultiArray:!1,isLoadDefaultAreas:!1}},watch:{defaultRegions:{handler:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.length==this.selectArr&&e.join("")!==t.join("")&&(this.isInitMultiArray=!1,this.handleDefaultRegions())},immediate:!0}},computed:{multiArray:function(){if(this.isLoadDefaultAreas){var e=this.pickedArr.map((function(e){return e.map((function(e){return e.label}))}));return e}},pickedArr:function(){return this.isInitMultiArray?"2"==this.selectArr?[this.pickerValueArray[0],this.pickerValueArray[1]]:[this.pickerValueArray[0],this.pickerValueArray[1],this.pickerValueArray[2]]:"2"==this.selectArr?[this.pickerValueArray[0],this.cityArr]:[this.pickerValueArray[0],this.cityArr,this.districtArr]}},created:function(){this.getDefaultAreas(0,{level:0})},methods:{pickerViewColumnChange:function(e){var t=this;return(0,l.default)(i.default.mark((function r(){var a;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(a=e.detail.value,a[0]==t.multiIndex[0]){r.next=10;break}return r.next=4,t.handleColumnChange({detail:{column:0,value:a[0]}});case 4:return t.multiIndex[1]=e.detail.value[1]||0,r.next=7,t.handleColumnChange({detail:{column:1,value:a[1]}});case 7:t.multiIndex[2]=e.detail.value[2]||0,r.next=17;break;case 10:if(a[1]==t.multiIndex[1]){r.next=16;break}return r.next=13,t.handleColumnChange({detail:{column:1,value:a[1]}});case 13:t.multiIndex[2]=e.detail.value[2]||0,r.next=17;break;case 16:t.multiIndex=a;case 17:case"end":return r.stop()}}),r)})))()},confirmRegions:function(){this.handleValueChange({detail:{value:this.multiIndex}}),this.$refs.regionsRef.close()},handleColumnChange:function(e){var t=this;return(0,l.default)(i.default.mark((function r(){var a,n;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.isInitMultiArray=!1,a=e.detail.column,n=e.detail.value,t.multiIndex[a]=n,r.t0=a,r.next=0===r.t0?7:1===r.t0?14:2===r.t0?18:27;break;case 7:return r.next=9,t.getAreasAsync(t.pickerValueArray[0][t.multiIndex[a]].value);case 9:return t.cityArr=r.sent,r.next=12,t.getAreasAsync(t.cityArr[0].value);case 12:return t.districtArr=r.sent,r.abrupt("break",27);case 14:return r.next=16,t.getAreasAsync(t.cityArr[t.multiIndex[a]].value);case 16:return t.districtArr=r.sent,r.abrupt("break",27);case 18:if(t.cityArr.length){r.next=22;break}return r.next=21,t.getAreasAsync(t.pickerValueArray[0][0].value);case 21:t.cityArr=r.sent;case 22:if(t.districtArr.length){r.next=26;break}return r.next=25,t.getAreasAsync(t.cityArr[0].value);case 25:t.districtArr=r.sent;case 26:return r.abrupt("break",27);case 27:case"end":return r.stop()}}),r)})))()},handleValueChange:function(e){var t=(0,n.default)(e.detail.value,3),r=t[0],a=t[1],i=t[2],l=(0,n.default)(this.pickedArr,3),u=l[0],s=l[1],c=l[2],d="";d="2"==this.selectArr?[u[r],s[a]]:[u[r],s[a],c[i]],this.$emit("getRegions",d)},handleDefaultRegions:function(){var e=this,t=setInterval((function(){for(var r=0;r<e.defaultRegions.length;r++)if(null!=e.pickerValueArray[r])for(var a=function(t){e.defaultRegions[r]!=e.pickerValueArray[r][t].value&&e.defaultRegions[r]!=e.pickerValueArray[r][t].label||1!=e.pickerValueArray[r][t].level||(e.$set(e.multiIndex,r,t),e.getAreas(e.pickerValueArray[r][t].value,(function(r){e.cityArr=r,e.$set(e.pickerValueArray,1,r);for(var a=function(r){if(e.defaultRegions[1]==e.cityArr[r].value||e.defaultRegions[1]==e.cityArr[r].label)return e.$set(e.multiIndex,1,r),e.getAreas(e.cityArr[r].value,(function(a){e.districtArr=a,e.$set(e.pickerValueArray,2,a);for(var i=0;i<e.districtArr.length;i++)if(e.defaultRegions[2]==e.districtArr[i].value||e.defaultRegions[2]==e.districtArr[i].label){e.$set(e.multiIndex,2,i),e.handleValueChange({detail:{value:[t,r,i]}});break}})),"break"},i=0;i<e.cityArr.length;i++){var n=a(i);if("break"===n)break}})))},i=0;i<e.pickerValueArray[r].length;i++)a(i);e.isLoadDefaultAreas&&clearInterval(t)}),100)},getDefaultAreas:function(e,t){var r=this;this.$api.sendRequest({url:"/api/address/lists",data:{pid:e},success:function(e){if(0==e.code){var a=[],i=void 0;e.data.forEach((function(e,r){void 0!=t&&(0==t.level&&void 0!=t.province_id?i=t.province_id:1==t.level&&void 0!=t.city_id?i=t.city_id:2==t.level&&void 0!=t.district_id&&(i=t.district_id)),void 0==i&&0==r&&(i=e.id),a.push({value:e.id,label:e.name,level:e.level})})),r.pickerValueArray[t.level]=a,t.level+1<3?(t.level++,r.getDefaultAreas(i,t)):(r.isInitMultiArray=!0,r.isLoadDefaultAreas=!0,r.handleDefaultRegions())}}})},getAreasAsync:function(e){var t=this;return(0,l.default)(i.default.mark((function r(){var a,n;return i.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.$api.sendRequest({url:"/api/address/lists",data:{pid:e},async:!1});case 2:if(a=r.sent,0!=a.code){r.next=7;break}return n=[],a.data.forEach((function(e,t){n.push({value:e.id,label:e.name,level:e.level})})),r.abrupt("return",n);case 7:case"end":return r.stop()}}),r)})))()},getAreas:function(e,t){this.$api.sendRequest({url:"/api/address/lists",data:{pid:e},success:function(e){if(0==e.code){var r=[];e.data.forEach((function(e,t){r.push({value:e.id,label:e.name,level:e.level})})),t&&t(r)}}})}}};t.default=u},"6b13":function(e,t,r){"use strict";r.r(t);var a=r("1984"),i=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(n);t["default"]=i.a},"6d55":function(e,t,r){},b514:function(e,t,r){"use strict";var a=r("6d55"),i=r.n(a);i.a},fbb7:function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){}));var a=function(){var e=this.$createElement;this._self._c},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/pick-regions/pick-regions-create-component',
    {
        'components/pick-regions/pick-regions-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("04c1"))
        })
    },
    [['components/pick-regions/pick-regions-create-component']]
]);
