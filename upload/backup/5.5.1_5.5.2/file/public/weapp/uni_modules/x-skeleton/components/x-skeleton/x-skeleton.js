(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/x-skeleton/components/x-skeleton/x-skeleton"],{"10cf":function(t,e,n){"use strict";var i=n("2ecd"),o=n.n(i);o.a},"2ecd":function(t,e,n){},3268:function(t,e,n){"use strict";n.r(e);var i=n("47ab"),o=n("da53");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("10cf");var a=n("828b"),s=Object(a["a"])(o["default"],i["b"],i["c"],!1,null,"67adcfb6",null,!1,i["a"],void 0);e["default"]=s.exports},"47ab":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.skeletonLoading?t.__map(t.gridRowsArr,(function(e,n){var i=t.__get_orig(e),o=t.gridRowsArr.length,r=t.gridColumnsArr.length,a=t.__map(t.gridColumnsArr,(function(e,n){var i=t.__get_orig(e),o=t.skeletonConfigs.textShow?t.__map(t.textRowsArr,(function(e,n){var i=t.__get_orig(e),o=t.textRowsArr.length;return{$orig:i,g2:o}})):null;return{$orig:i,l0:o}}));return{$orig:i,g0:o,g1:r,l1:a}})):null);t.$mp.data=Object.assign({},{$root:{l2:n}})},o=[]},c82c:function(t,e,n){"use strict";var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("8e32")),r={name:"x-skeleton",mixins:[o.default],props:{type:{type:String,default:""},loading:{type:Boolean,default:!0},animate:{type:Boolean,default:!0},animateTime:{type:Number|String,default:1.8},fadeOut:{type:Boolean,default:!0},fadeOutTime:{type:Number|String,default:.5},bgColor:{type:String,default:"#EAEDF5"},highlightBgColor:{type:String,default:"#F9FAFF"},configs:{type:Object,default:function(){return{}}}},computed:{gridRowsArr:function(){var t;return new Array(Number((null===(t=this.skeletonConfigs)||void 0===t?void 0:t.gridRows)||[]))},gridColumnsArr:function(){var t;return new Array(Number((null===(t=this.skeletonConfigs)||void 0===t?void 0:t.gridColumns)||[]))},textRowsArr:function(){var t;if(null===(t=this.skeletonConfigs)||void 0===t||!t.textShow)return[];/%$/.test(this.skeletonConfigs.textHeight)&&console.error("x-skeleton: textHeight参数不支持百分比单位");for(var e=[],n=0;n<this.skeletonConfigs.textRows;n++){var i=this.skeletonConfigs,o=i.gridRows,r=i.textWidth,a=i.textHeight,s={},u=this.isArray(r)?r[n]||(n===o-1?"70%":"100%"):n===o-1?"70%":r,l=this.isArray(a)?a[n]||"30rpx":a;/%$/.test(u)?s.width=u:s.width=this.addUnit(u),s.height=this.addUnit(l),e.push(s)}return e},variableStr:function(){var t=this,e=["animateTime","fadeOutTime","bgColor","highlightBgColor"].map((function(e){return e.indexOf("Time")>-1?"--".concat(e,":").concat(t[e],"s"):"--".concat(e,":").concat(t[e])})).join(";");return e}},watch:{loading:{immediate:!0,handler:function(t){var e=this;t?this.skeletonLoading=!0:this.fadeOut?(this.startFadeOut=!0,setTimeout((function(){e.skeletonLoading=!1,e.startFadeOut=!1}),1e3*this.fadeOutTime)):this.skeletonLoading=!1}},type:{immediate:!0,handler:function(t){this.skeletonConfigs="banner"===t?this.bannerConfigs():"info"===t?this.infoConfigs():"text"===t?this.textConfigs():"menu"===t?this.menuConfigs():"list"===t?this.listConfigs():"waterfall"===t?this.waterfallConfigs():this.configs||{}}}},data:function(){return{skeletonConfigs:this.configs||{},skeletonLoading:this.loading,startFadeOut:!1,width:0}},mounted:function(){},methods:{isArray:function(t){return"function"===typeof Array.isArray?Array.isArray(t):"[object Array]"===Object.prototype.toString.call(t)},addUnit:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"auto",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"px";return t=String(t),/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(t)?"".concat(t).concat(e):t}}};e.default=r},da53:function(t,e,n){"use strict";n.r(e);var i=n("c82c"),o=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/x-skeleton/components/x-skeleton/x-skeleton-create-component',
    {
        'uni_modules/x-skeleton/components/x-skeleton/x-skeleton-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("3268"))
        })
    },
    [['uni_modules/x-skeleton/components/x-skeleton/x-skeleton-create-component']]
]);
