<view data-event-opts="{{[['mouseleave',[['leaveCale',['$event']]]]]}}" class="uni-calendar data-v-4c83cca8" bindmouseleave="__e"><block wx:if="{{!insert&&show}}"><view data-event-opts="{{[['tap',[['clean',['$event']]]]]}}" class="{{['uni-calendar__mask','data-v-4c83cca8',(aniMaskShow)?'uni-calendar--mask-show':'']}}" bindtap="__e"></view></block><block wx:if="{{insert||show}}"><view class="{{['uni-calendar__content','data-v-4c83cca8',(!insert)?'uni-calendar--fixed':'',(aniMaskShow)?'uni-calendar--ani-show':'',(aniMaskShow)?'uni-calendar__content-mobile':'']}}"><view class="{{['uni-calendar__header','data-v-4c83cca8',(!insert)?'uni-calendar__header-mobile':'']}}"><block wx:if="{{left}}"><view data-event-opts="{{[['tap',[['pre',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-4c83cca8" catchtap="__e"><view class="uni-calendar__header-btn uni-calendar--left data-v-4c83cca8"></view></view></block><picker mode="date" value="{{date}}" fields="month" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e" class="data-v-4c83cca8"><text class="uni-calendar__header-text data-v-4c83cca8">{{(nowDate.year||'')+' 年 '+(nowDate.month||'')+' 月'}}</text></picker><block wx:if="{{right}}"><view data-event-opts="{{[['tap',[['next',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-4c83cca8" catchtap="__e"><view class="uni-calendar__header-btn uni-calendar--right data-v-4c83cca8"></view></view></block><block wx:if="{{!insert}}"><view data-event-opts="{{[['tap',[['clean',['$event']]]]]}}" class="dialog-close data-v-4c83cca8" bindtap="__e"><view class="dialog-close-plus data-v-4c83cca8" data-id="close"></view><view class="dialog-close-plus dialog-close-rotate data-v-4c83cca8" data-id="close"></view></view></block></view><view class="uni-calendar__box data-v-4c83cca8"><block wx:if="{{showMonth}}"><view class="uni-calendar__box-bg data-v-4c83cca8"><text class="uni-calendar__box-bg-text data-v-4c83cca8">{{nowDate.month}}</text></view></block><view class="uni-calendar__weeks data-v-4c83cca8" style="padding-bottom:7px;"><view class="uni-calendar__weeks-day data-v-4c83cca8"><text class="uni-calendar__weeks-day-text data-v-4c83cca8">{{SUNText}}</text></view><view class="uni-calendar__weeks-day data-v-4c83cca8"><text class="uni-calendar__weeks-day-text data-v-4c83cca8">{{monText}}</text></view><view class="uni-calendar__weeks-day data-v-4c83cca8"><text class="uni-calendar__weeks-day-text data-v-4c83cca8">{{TUEText}}</text></view><view class="uni-calendar__weeks-day data-v-4c83cca8"><text class="uni-calendar__weeks-day-text data-v-4c83cca8">{{WEDText}}</text></view><view class="uni-calendar__weeks-day data-v-4c83cca8"><text class="uni-calendar__weeks-day-text data-v-4c83cca8">{{THUText}}</text></view><view class="uni-calendar__weeks-day data-v-4c83cca8"><text class="uni-calendar__weeks-day-text data-v-4c83cca8">{{FRIText}}</text></view><view class="uni-calendar__weeks-day data-v-4c83cca8"><text class="uni-calendar__weeks-day-text data-v-4c83cca8">{{SATText}}</text></view></view><block wx:for="{{weeks}}" wx:for-item="item" wx:for-index="weekIndex" wx:key="weekIndex"><view class="uni-calendar__weeks data-v-4c83cca8"><block wx:for="{{item}}" wx:for-item="weeks" wx:for-index="weeksIndex" wx:key="weeksIndex"><view class="uni-calendar__weeks-item data-v-4c83cca8"><calendar-item class="uni-calendar-item--hook data-v-4c83cca8" vue-id="{{'031a06b8-1-'+weekIndex+'-'+weeksIndex}}" weeks="{{weeks}}" calendar="{{calendar}}" selected="{{selected}}" lunar="{{lunar}}" checkHover="{{range}}" data-event-opts="{{[['^change',[['choiceDate']]],['^handleMouse',[['handleMouse']]]]}}" bind:change="__e" bind:handleMouse="__e" bind:__l="__l"></calendar-item></view></block></view></block></view><block wx:if="{{!insert&&!range&&typeHasTime}}"><view class="uni-date-changed uni-calendar--fixed-top data-v-4c83cca8" style="padding:0 80px;"><view class="uni-date-changed--time-date data-v-4c83cca8">{{tempSingleDate?tempSingleDate:selectDateText}}</view><time-picker bind:input="__e" class="time-picker-style data-v-4c83cca8" vue-id="031a06b8-2" type="time" start="{{reactStartTime}}" end="{{reactEndTime}}" disabled="{{!tempSingleDate}}" border="{{false}}" hide-second="{{hideSecond}}" value="{{time}}" data-event-opts="{{[['^input',[['__set_model',['','time','$event',[]]]]]]}}" bind:__l="__l"></time-picker></view></block><block wx:if="{{!insert&&range&&typeHasTime}}"><view class="uni-date-changed uni-calendar--fixed-top data-v-4c83cca8"><view class="uni-date-changed--time-start data-v-4c83cca8"><view class="uni-date-changed--time-date data-v-4c83cca8">{{(tempRange.before?tempRange.before:startDateText)+''}}</view><time-picker bind:input="__e" class="time-picker-style data-v-4c83cca8" vue-id="031a06b8-3" type="time" start="{{reactStartTime}}" border="{{false}}" hide-second="{{hideSecond}}" disabled="{{!tempRange.before}}" value="{{timeRange.startTime}}" data-event-opts="{{[['^input',[['__set_model',['$0','startTime','$event',[]],['timeRange']]]]]}}" bind:__l="__l"></time-picker></view><uni-icons style="line-height:50px;" vue-id="031a06b8-4" type="arrowthinright" color="#999" class="data-v-4c83cca8" bind:__l="__l"></uni-icons><view class="uni-date-changed--time-end data-v-4c83cca8"><view class="uni-date-changed--time-date data-v-4c83cca8">{{tempRange.after?tempRange.after:endDateText}}</view><time-picker bind:input="__e" class="time-picker-style data-v-4c83cca8" vue-id="031a06b8-5" type="time" end="{{reactEndTime}}" border="{{false}}" hide-second="{{hideSecond}}" disabled="{{!tempRange.after}}" value="{{timeRange.endTime}}" data-event-opts="{{[['^input',[['__set_model',['$0','endTime','$event',[]],['timeRange']]]]]}}" bind:__l="__l"></time-picker></view></view></block><block wx:if="{{!insert}}"><view class="uni-date-changed uni-date-btn--ok data-v-4c83cca8"><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="uni-datetime-picker--btn data-v-4c83cca8" bindtap="__e">确认</view></view></block></view></block></view>