<view data-event-opts="{{[['tap',[['choiceDate',['$0'],['weeks']]]],['mouseenter',[['handleMousemove',['$0'],['weeks']]]]]}}" class="{{['uni-calendar-item__weeks-box','data-v-c2a16ef2',(weeks.disable)?'uni-calendar-item--disable':'',(weeks.beforeMultiple)?'uni-calendar-item--before-checked-x':'',(weeks.multiple)?'uni-calendar-item--multiple':'',(weeks.afterMultiple)?'uni-calendar-item--after-checked-x':'']}}" bindtap="__e" bindmouseenter="__e"><view class="{{['uni-calendar-item__weeks-box-item','data-v-c2a16ef2',(calendar.fullDate===weeks.fullDate&&(calendar.userChecked||!checkHover))?'uni-calendar-item--checked':'',(checkHover)?'uni-calendar-item--checked-range-text':'',(weeks.beforeMultiple)?'uni-calendar-item--before-checked':'',(weeks.multiple)?'uni-calendar-item--multiple':'',(weeks.afterMultiple)?'uni-calendar-item--after-checked':'',(weeks.disable)?'uni-calendar-item--disable':'']}}"><block wx:if="{{selected&&weeks.extraInfo}}"><text class="uni-calendar-item__weeks-box-circle data-v-c2a16ef2"></text></block><text class="uni-calendar-item__weeks-box-text uni-calendar-item__weeks-box-text-disable uni-calendar-item--checked-text data-v-c2a16ef2">{{weeks.date}}</text></view><view class="{{['data-v-c2a16ef2',(weeks.isDay)?'uni-calendar-item--isDay':'']}}"></view></view>