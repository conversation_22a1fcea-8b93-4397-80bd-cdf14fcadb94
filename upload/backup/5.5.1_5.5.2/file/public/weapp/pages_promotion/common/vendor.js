(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/common/vendor"],{"33da":function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{info:null,timeMachine:null,page:1,goodsList:[],cart:{},totalPrice:0,totalNum:0,goodsSkuDetail:null,skuList:[],cartShow:!1,isSub:!1}},onLoad:function(t){var i=this;if(t.id&&(this.id=t.id),t.scene){var o=decodeURIComponent(t.scene);o=o.split("&"),o.length&&o.forEach((function(e){-1!=e.indexOf("id")&&(i.id=e.split("-")[1])}))}if(t.source_member&&e.setStorageSync("source_member",t.source_member),t.scene){o=decodeURIComponent(t.scene);o=o.split("&"),o.length&&o.forEach((function(t){-1!=t.indexOf("sku_id")&&(i.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&e.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&e.setStorageSync("is_test",1)}))}this.getBaleInfo()},onShow:function(){this.storeToken&&e.getStorageSync("source_member")&&this.$util.onSourceMember(e.getStorageSync("source_member"))},methods:{getBaleInfo:function(){var e=this;this.$api.sendRequest({url:"/bale/api/bale/detail",data:{bale_id:this.id},success:function(t){0==t.code&&t.data?(e.info=t.data,1==e.info.status?(e.timeMachine=e.$util.countDown(e.info.end_time-t.timestamp),e.goodsList=e.info.sku_list,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):2==e.info.status&&(e.$util.showToast({title:"该活动已关闭"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1500))):(e.$util.showToast({title:"未找到活动"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1500))},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},imageError:function(e){this.goodsList[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},singleSkuPlus:function(e){if(void 0!=this.cart["goods_"+e.goods_id]){var t=this.cart["goods_"+e.goods_id].num;if(t+1>e.stock)return void this.$util.showToast({title:"库存不足"});this.cart["goods_"+e.goods_id].num+=1,this.cart["goods_"+e.goods_id]["sku_"+e.sku_id]?this.cart["goods_"+e.goods_id]["sku_"+e.sku_id].num+=1:(this.cart["goods_"+e.goods_id]["sku_"+e.sku_id]=e,this.cart["goods_"+e.goods_id]["sku_"+e.sku_id].num=1)}else this.cart["goods_"+e.goods_id]={num:1},this.cart["goods_"+e.goods_id]["sku_"+e.sku_id]=e,this.cart["goods_"+e.goods_id]["sku_"+e.sku_id].num=1;this.totalNum+=1,this.cart=Object.assign({},this.cart)},singleSkuReduce:function(e){if(void 0!=this.cart["goods_"+e.goods_id]){var t=this.cart["goods_"+e.goods_id]["sku_"+e.sku_id].num;this.cart["goods_"+e.goods_id].num-=1,this.cart["goods_"+e.goods_id]["sku_"+e.sku_id].num-=1,this.totalNum-=1,t-1==0&&(delete this.cart["goods_"+e.goods_id]["sku_"+e.sku_id],1==Object.keys(this.cart["goods_"+e.goods_id]).length&&delete this.cart["goods_"+e.goods_id]),this.cart=Object.assign({},this.cart)}},manySkuSelect:function(e){var t=this;"string"==typeof e.sku_spec_format&&e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format)),"string"==typeof e.goods_sku_spec_format&&e.goods_sku_spec_format&&(e.goods_spec_format=JSON.parse(e.goods_sku_spec_format)),e.bale_id=this.id,e.activity_sku_ids=this.info.sku_ids.split(","),this.goodsSkuDetail=e,setTimeout((function(){t.$refs.goodsSku.show("bale")}),100)},refreshGoodsSkuDetail:function(e){Object.assign(this.goodsSkuDetail,e)},joinCart:function(e){void 0!=this.cart["goods_"+e.goods_id]?void 0!=this.cart["goods_"+e.goods_id]["sku_"+e.sku_id]?(this.cart["goods_"+e.goods_id].num+=e.num-this.cart["goods_"+e.goods_id]["sku_"+e.sku_id].num,this.totalNum+=e.num-this.cart["goods_"+e.goods_id]["sku_"+e.sku_id].num,this.cart["goods_"+e.goods_id]["sku_"+e.sku_id].num=e.num):(this.cart["goods_"+e.goods_id]["sku_"+e.sku_id]=e.detail,this.cart["goods_"+e.goods_id]["sku_"+e.sku_id].num=e.num,this.cart["goods_"+e.goods_id].num+=e.num,this.totalNum+=e.num):(this.cart["goods_"+e.goods_id]={num:e.num},this.cart["goods_"+e.goods_id]["sku_"+e.sku_id]=e.detail,this.cart["goods_"+e.goods_id]["sku_"+e.sku_id].num=e.num,this.totalNum+=e.num),this.cart=Object.assign({},this.cart),this.$refs.goodsSku.hide()},openCartPopup:function(){this.skuList.length&&(this.cartShow=!this.cartShow)},closeCartPopup:function(){this.cartShow=!1},clearCart:function(){this.cart={},this.totalNum=0,this.closeCartPopup()},submit:function(){var t=this;if(this.storeToken){if(this.isSub)return;this.isSub=!0;var i=[];this.skuList.forEach((function(e){i.push({sku_id:e.sku_id,num:e.num})})),e.setStorage({key:"baleOrderCreateData",data:{bale_id:this.id,sku_list_json:JSON.stringify(i)},success:function(){t.isSub=!1,t.$util.redirectTo("/pages_promotion/bale/payment")}})}else this.$nextTick((function(){t.$refs.login.open("/pages_promotion/bale/detail?id="+t.id)}))}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)},sku:function(e){var t="";return e&&(t=e.sku_name.replace(e.goods_name,"")),t}},watch:{cart:{deep:!0,handler:function(e,t){var i=[],o=1;for(var a in this.cart){var s=this.cart[a];for(var r in s)if(-1==r.indexOf("num")){var n=s[r];n.start=o,n.end=o+n.num,i.push(s[r]),o+=n.num}}this.skuList=i}},totalNum:function(e){if(e>0)if(e%this.info.num==0)this.totalPrice=this.info.price*(e/this.info.num);else{for(var t=Math.floor(e/this.info.num),i=parseFloat(t*this.info.price),o=t*this.info.num,a=0;a<e%this.info.num;a++){o+=1;for(var s=0;s<this.skuList.length;s++){var r=this.skuList[s];if(o>=r.start&&o<r.end){i+=parseFloat(r.price);break}}}this.totalPrice=i}else this.totalPrice=0}},onShareAppMessage:function(){var e=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),t=e.path;return{title:"这些商品"+this.info.price+"元任选"+this.info.num+"件",imageUrl:"",path:t,success:function(e){},fail:function(e){}}},onShareTimeline:function(){var e="这些商品"+this.info.price+"元任选"+this.info.num+"件",t=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),i=t.query;return{title:e,query:i,imageUrl:""}}};t.default=i}).call(this,i("df3c")["default"])},3404:function(e,t,i){"use strict";var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(i("7eb4")),s=o(i("ee10")),r={data:function(){return{goodsRoute:"/pages_promotion/seckill/detail",posterApi:"/seckill/api/seckillgoods/poster",seckill_id:0}},onLoad:function(e){var t=this;if(this.seckill_id=e.seckill_id||0,e.id&&(this.seckill_id=e.id),e.scene){var i=decodeURIComponent(e.scene);i=i.split("&"),i.length&&i.forEach((function(e){-1!=e.indexOf("id")&&(t.seckill_id=e.split("-")[1])}))}},onShow:function(){this.getGoodsSkuDetail()},methods:{getGoodsSkuDetail:function(){var e=this;return(0,s.default)(a.default.mark((function t(){var i,o,s,r,n,d,u,l,c;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:"/seckill/api/seckillgoods/detail",async:!1,data:{seckill_id:e.seckill_id}});case 2:i=t.sent,o=i.data,null!=o.goods_sku_detail?(e.goodsSkuDetail=o.goods_sku_detail,e.shopInfo=o.shop_info,e.skuId=e.goodsSkuDetail.sku_id,s=new Date(1e3*i.timestamp),r=60*s.getHours()*60+60*s.getMinutes()+s.getSeconds(),i.timestamp>e.goodsSkuDetail.start_time&&(n=!1,e.goodsSkuDetail.time_list.forEach((function(t,i){r>t.seckill_start_time&&r<t.seckill_end_time&&(n=!0,e.goodsSkuDetail.discountTimeMachine=e.$util.countDown(t.seckill_end_time-r))})),d=null,n||(i.timestamp>e.goodsSkuDetail.end_time?(e.$util.showToast({title:"限时秒杀活动已结束"}),setTimeout((function(){e.$util.redirectTo("/pages/goods/detail",{goods_id:e.goodsSkuDetail.goods_id},"redirectTo")}),1e3)):(u=s,l=new Date(1e3*e.goodsSkuDetail.end_time),u.setHours(0,0,0,0),l.setHours(0,0,0,0),c=u.getTime()===l.getTime(),e.goodsSkuDetail.time_list.forEach((function(e,t){r<e.seckill_start_time&&null==d&&(d=t)})),c?null!=d?e.goodsSkuDetail.discountTimeStart=e.$util.countDown(e.goodsSkuDetail.time_list[d].seckill_start_time-r):(e.$util.showToast({title:"限时秒杀活动已结束"}),setTimeout((function(){e.$util.redirectTo("/pages/goods/detail",{goods_id:e.goodsSkuDetail.goods_id},"redirectTo")}),1e3)):e.goodsSkuDetail.discountTimeStart=d?e.$util.countDown(e.goodsSkuDetail.time_list[d].seckill_start_time-r):e.$util.countDown(e.goodsSkuDetail.time_list[0].seckill_start_time-r)))),e.shareQuery="seckill_id="+e.seckill_id,e.shareUrl=e.goodsRoute+"?"+e.shareQuery,e.chatRoomParams={sku_id:e.goodsSkuDetail.sku_id,type:"seckill",type_id:e.goodsSkuDetail.seckill_id},e.posterParams={id:e.goodsSkuDetail.goods_id,seckillId:e.goodsSkuDetail.seckill_id},e.getShareImg(),e.handleGoodsSkuData(),e.goodsSkuDetail.show_price=e.goodsSkuDetail.seckill_price,e.goodsSkuDetail.save_price=e.goodsSkuDetail.price-e.goodsSkuDetail.seckill_price>0?(e.goodsSkuDetail.price-e.goodsSkuDetail.seckill_price).toFixed(2):0,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):e.$util.redirectTo("/pages_tool/goods/not_exist",{},"redirectTo");case 5:case"end":return t.stop()}}),t)})))()},seckill:function(){this.storeToken?this.$refs.goodsSku.show("seckill"):this.source_member?this.$refs.login.open(this.shareUrl+"&source_member="+this.source_member):this.$refs.login.open(this.shareUrl)},getShareImg:function(){var e=this,t={id:this.goodsSkuDetail.goods_id};this.$api.sendRequest({url:"/seckill/api/seckillgoods/shareimg",data:{qrcode_param:JSON.stringify(t)},success:function(t){0==t.code&&(e.shareImg=t.data.path)}})}}};t.default=r},3949:function(e,t,i){"use strict";var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(i("7eb4")),s=o(i("ee10")),r={data:function(){return{posterApi:"/pinfan/api/goods/poster",goodsRoute:"/pages_promotion/pinfan/detail",groupId:0,pinfan_id:0,pintuanList:[],currentPintuan:{headimg:"",timeMachine:{}},openPopup:!1,timestamp:"",newList:[],pinfanPopShow:!1,groupDetail:null,kill:0}},onLoad:function(e){var t=this;if(this.pinfan_id=e.pinfan_id||0,e.id&&(this.pinfan_id=e.id),this.groupId=e.group_id||0,e.scene){var i=decodeURIComponent(e.scene);i=i.split("&"),i.length&&i.forEach((function(e){-1!=e.indexOf("id")&&(t.pinfan_id=e.split("-")[1]),-1!=e.indexOf("group_id")&&(t.group_id=e.split("-")[1])}))}},onShow:function(){var e=this;return(0,s.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getGoodsSkuDetail();case 2:e.getPintuanGroupList(),e.groupId>0&&e.getGroupDetail();case 4:case"end":return t.stop()}}),t)})))()},methods:{getGoodsSkuDetail:function(){var e=this;return(0,s.default)(a.default.mark((function t(){var i,o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:"/pinfan/api/goods/detail",async:!1,data:{pintuan_id:e.pinfan_id}});case 2:i=t.sent,o=i.data,null!=o.goods_sku_detail?(e.goodsSkuDetail=o.goods_sku_detail,e.skuId=e.goodsSkuDetail.sku_id,e.goodsSkuDetail.group_id=e.groupId,e.goodsSkuDetail.end_time-i.timestamp>0?e.goodsSkuDetail.timeMachine=e.$util.countDown(e.goodsSkuDetail.end_time-i.timestamp):(e.$util.showToast({title:"活动已结束"}),setTimeout((function(){e.$util.redirectTo("/pages/goods/detail",{goods_id:e.goodsSkuDetail.goods_id},"redirectTo")}),1e3)),e.shareQuery="pinfan_id="+e.pinfan_id+"&group_id="+e.groupId,e.shareUrl=e.goodsRoute+"?"+e.shareQuery,e.chatRoomParams={sku_id:e.goodsSkuDetail.sku_id,type:"pintuan",type_id:e.goodsSkuDetail.pinfan_id},e.posterParams={id:e.goodsSkuDetail.id},e.handleGoodsSkuData(),e.goodsSkuDetail.show_price=0==e.goodsSkuDetail.group_id?e.goodsSkuDetail.promotion_price:e.goodsSkuDetail.pintuan_price,e.goodsSkuDetail.save_price=e.goodsSkuDetail.price-e.goodsSkuDetail.show_price>0?(e.goodsSkuDetail.price-e.goodsSkuDetail.show_price).toFixed(2):0,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):e.$util.redirectTo("/pages_tool/goods/not_exist",{},"redirectTo");case 5:case"end":return t.stop()}}),t)})))()},pintuan:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.storeToken?(t?(this.resetPrice(),this.goodsSkuDetail.group_id=0):this.goodsSkuDetail.group_id=i||this.groupId,this.$refs.goodsSku.show("pinfan",(function(){e.resetPrice()}))):this.source_member?this.$refs.login.open(this.shareUrl+"&source_member="+this.source_member):this.$refs.login.open(this.shareUrl)},buyNow:function(){var e=this;this.storeToken?this.$refs.goodsSku.show("buy_now",(function(){e.$store.dispatch("getCartNumber")})):this.source_member?this.$refs.login.open(this.shareUrl+"&source_member="+this.source_member):this.$refs.login.open(this.shareUrl)},getPintuanGroupList:function(){var e=this;this.$api.sendRequest({url:"/pinfan/api/pinfangroup/lists",data:{goods_id:this.goodsSkuDetail.goods_id},success:function(t){var i=t.data;if(e.timestamp=t.timestamp,null!=i&&i.length){e.pintuanList=i;for(var o=0;o<e.pintuanList.length;o++)e.pintuanList[o]["end_time"]>t.timestamp?(e.pintuanList[o].timeMachine=e.$util.countDown(e.pintuanList[o]["end_time"]-t.timestamp),e.pintuanList[o].currentTime=t.timestamp):e.pintuanList[o].timeMachine=null;e.newList=e.pintuanList.filter((function(e){return e.end_time>t.timestamp})),e.$forceUpdate()}}})},getGroupDetail:function(){var e=this;this.$api.sendRequest({url:"/pinfan/api/pinfangroup/info",data:{group_id:this.groupId},success:function(t){e.kill=t.data.pintuan_num-t.data.pintuan_count,0==t.code&&(t.data.end_time>t.timestamp?t.data.timeMachine=e.$util.countDown(t.data.end_time-t.timestamp):t.data.timeMachine=null,e.groupDetail=t.data,e.pinfanPopShow=!0,e.groupDetail&&2!=e.groupDetail.status&&(e.groupId=0,e.goodsSkuDetail.group_id=0,e.resetPrice())),e.$forceUpdate()}})},openPinTuan:function(e,t,i,o,a){var s=this;this.memberInfo&&this.memberInfo.member_id==a?this.$util.showToast({title:"您不能参与自己的团"}):(this.openPopup=!0,this.currentPintuan={groupId:e,pintuan_num:t,timeMachine:this.$util.countDown(i),headimg:o},this.$refs.pintuanPopup.open((function(){s.goodsSkuDetail.group_id=0,s.openPopup=!1})))},closePinTuanPopup:function(){this.$refs.pintuanPopup.close()},joinPintuan:function(){this.closePinTuanPopup(),this.goodsSkuDetail.group_id=this.currentPintuan.groupId,this.goodsSkuDetail.show_price=this.goodsSkuDetail.pintuan_price,this.goodsSkuDetail.save_price=this.goodsSkuDetail.price-this.goodsSkuDetail.show_price>0?(this.goodsSkuDetail.price-this.goodsSkuDetail.show_price).toFixed(2):0,this.$forceUpdate(),this.pintuan(!1,this.currentPintuan.groupId)},pintuanImageError:function(e){this.pintuanList[e].headimg=this.$util.getDefaultImage().head,this.$forceUpdate()},resetPrice:function(){this.goodsSkuDetail.show_price=0==this.goodsSkuDetail.group_id?this.goodsSkuDetail.promotion_price:this.goodsSkuDetail.pintuan_price,this.goodsSkuDetail.save_price=this.goodsSkuDetail.price-this.goodsSkuDetail.show_price>0?(this.goodsSkuDetail.price-this.goodsSkuDetail.show_price).toFixed(2):0,this.$forceUpdate()},cutStrByte:function(e,t){if(!e||!t)return"";var i,o=e.length;if(o<=t/2)i=e,"0";else for(var a=0,s=0;s<o;s++){if(a+=this.getByteLen(e.charAt(s)),a>t){i=e.substring(0,s);break}if(a==t){i=e.substring(0,s+1);break}}return i||(i=e,"0"),i},getByteLen:function(e){var t=0;if(!e)return t;for(var i=0;i<e.length;i++)e[i]&&(null!=e[i].match(/[^\x00-\xff]/gi)?t+=2:t+=1);return t}}};t.default=r},"49e5":function(e,t,i){"use strict";(function(e){var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(i("7eb4")),s=o(i("ee10")),r={data:function(){return{hour:"00",minute:"00",second:"00",ident:!1,showEmpty:!1,mpShareData:null,mescroll:null,timeList:[],seckillId:null,seckillIndex:null,dataList:[],index:null,timer:null,noStartList:[],show:!1,bgColor:""}},onLoad:function(t){var i=this;if(setTimeout((function(){i.addonIsExist.seckill||(i.$util.showToast({title:"商家未开启秒杀",mask:!0,duration:2e3}),setTimeout((function(){i.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.source_member&&e.setStorageSync("source_member",t.source_member),t.scene){var o=decodeURIComponent(t.scene);o=o.split("&"),o.length&&o.forEach((function(t){-1!=t.indexOf("sku_id")&&(i.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&e.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&e.setStorageSync("is_test",1)}))}},onShow:function(){var t=this;return(0,s.default)(a.default.mark((function i(){return a.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return t.storeToken&&e.getStorageSync("source_member")&&t.$util.onSourceMember(e.getStorageSync("source_member")),t.$util.getMpShare().then((function(e){t.mpShareData=e})),i.next=4,t.getZoneConfig();case 4:t.getTimeList();case 5:case"end":return i.stop()}}),i)})))()},watch:{seckillId:function(e,t){e&&t&&e!=t&&this.mescroll.resetUpScroll(!1)}},methods:{getZoneConfig:function(){var e=this;return(0,s.default)(a.default.mark((function t(){var i,o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:"/api/config/promotionZoneConfig",data:{name:"seckill"},async:!1});case 2:i=t.sent,o=i.data,o&&(e.bgColor=o.bg_color);case 5:case"end":return t.stop()}}),t)})))()},goodsImg:function(e){var t=e.split(",");return t[0]?this.$util.img(t[0],{size:"mid"}):this.$util.getDefaultImage().goods},transformSeckillTime:function(e){e=parseFloat(e);var t=parseInt(e/3600),i=parseInt(e%3600/60),o=parseInt(e%60);return t<10&&(t="0"+t),i<10&&(i="0"+i),o<10&&(o="0"+o),t+":"+i},isEnd:function(e){e&&this.$util.showToast({title:"限时秒杀活动已结束"})},getTimeList:function(){var e=this;this.$api.sendRequest({url:"/seckill/api/seckill/lists",success:function(t){var i=t.data;if(i){var o=new Date(1e3*t.timestamp),a=60*o.getHours()*60+60*o.getMinutes()+o.getSeconds(),s=Object.values(i.list);s.forEach((function(t,i){t.seckill_start_time<=a&&a<t.seckill_end_time?t.isNow=!0:t.isNow=!1,s[i]["startTimeMachine"]=e.$util.countDown(t.seckill_start_time-a),s[i]["endTimeMachine"]=e.$util.countDown(t.seckill_end_time-a),"tomorrow"==t.type&&(s[i]["startTimeMachine"]=e.$util.countDown(86400-a+t.seckill_start_time))})),e.timeList=s,s.length?(e.seckillId=e.timeList[0]["id"],e.seckillIndex=0,e.show=!0):e.$refs.loadingCover&&e.$refs.loadingCover.hide();var r=e;setInterval((function(){r.getExpirationTime()}),1e3)}}})},getData:function(e){var t=this;this.mescroll=e,this.$api.sendRequest({url:"/seckill/api/seckillgoods/page",data:{page_size:e.size,page:e.num,seckill_time_id:this.seckillId,seckill_time_type:this.timeList[this.seckillIndex].type},success:function(i){var o=[],a=i.message;0==i.code&&i.data?o=i.data.list:t.$util.showToast({title:a}),e.endSuccess(o.length),1==e.num&&(t.dataList=[]),t.dataList=t.dataList.concat(o),0==t.dataList.length&&(t.showEmpty=!0),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(){e.endErr(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},getExpirationTime:function(){var e,t,i,o=this.timeList,a=new Date,s=a.toLocaleDateString();for(var r in o)if(o[r].isNow){this.ident=!0,t=Date.parse(a),e=Date.parse(s)+1e3*parseInt(o[r].seckill_end_time);var n=(e-t)/1e3;if(i=this.$util.countDown(n),this.hour=i.h,this.minute=i.i,this.second=i.s,0==n){var d=parseInt(r)+1;d<o.length&&(this.seckillIndex=d),this.timeList[r].isNow=!1}return!1}this.ident=!1},ontabtap:function(e,t){t!=this.seckillIndex&&(this.dataList=[]),this.seckillId=e.id,this.seckillIndex=t},toGoodsDetail:function(e){var t=this;this.$api.sendRequest({url:"/api/config/time",data:{},success:function(i){var o=new Date(1e3*i.timestamp),a=60*o.getHours()*60+60*o.getMinutes()+o.getSeconds();t.timeList[t.seckillIndex].seckill_start_time<=a&&a<t.timeList[t.seckillIndex].seckill_end_time?t.timeList[t.seckillIndex].isNow=!0:t.timeList[t.seckillIndex].isNow=!1,t.$forceUpdate(),t.$util.redirectTo("/pages_promotion/seckill/detail",{seckill_id:e.id})}})},imageError:function(e){this.dataList[e].goods_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},goodsTag:function(e){return e.label_name||""}},onShareAppMessage:function(e){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},onReady:function(){var t=this;setTimeout((function(){var i=e.createSelectorQuery().in(t);i.select(".time-wrap").boundingClientRect((function(e){e&&(t.timeTop=e.top)})).exec()}),500)},onHide:function(){clearInterval(this.timer)}};t.default=r}).call(this,i("df3c")["default"])},"4a17":function(e,t,i){"use strict";var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(i("7eb4")),s=o(i("ee10")),r={data:function(){return{goodsRoute:"/pages_promotion/topics/goods_detail",posterApi:"/topic/api/topicgoods/poster",topics_id:0}},onLoad:function(e){var t=this;if(this.topics_id=e.id||0,e.scene){var i=decodeURIComponent(e.scene);i=i.split("&"),i.length&&i.forEach((function(e){-1!=e.indexOf("id")&&(t.topics_id=e.split("-")[1])}))}},onShow:function(){this.getGoodsSkuDetail()},methods:{getGoodsSkuDetail:function(){var e=this;return(0,s.default)(a.default.mark((function t(){var i,o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:"/topic/api/topicgoods/detail",async:!1,data:{topic_id:e.topics_id}});case 2:i=t.sent,o=i.data,null!=o.goods_sku_detail?(e.goodsSkuDetail=o.goods_sku_detail,e.skuId=e.goodsSkuDetail.sku_id,i.timestamp>e.goodsSkuDetail.end_time?(e.$util.showToast({title:"专题活动已结束"}),setTimeout((function(){e.$util.redirectTo("/pages/goods/detail",{goods_id:e.goodsSkuDetail.goods_id},"redirectTo")}),1e3)):e.goodsSkuDetail.discountTimeMachine=e.$util.countDown(e.goodsSkuDetail.end_time-i.timestamp),e.shareQuery="id="+e.topics_id,e.shareUrl=e.goodsRoute+"?"+e.shareQuery,e.chatRoomParams={sku_id:e.goodsSkuDetail.sku_id,type:"topic",type_id:e.topics_id},e.posterParams={id:e.topics_id},e.handleGoodsSkuData(),e.goodsSkuDetail.show_price=e.goodsSkuDetail.topic_price,e.goodsSkuDetail.save_price=e.goodsSkuDetail.price-e.goodsSkuDetail.show_price>0?(e.goodsSkuDetail.price-e.goodsSkuDetail.show_price).toFixed(2):0,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):e.$util.redirectTo("/pages_tool/goods/not_exist",{},"redirectTo");case 5:case"end":return t.stop()}}),t)})))()},topic:function(){this.storeToken?this.$refs.goodsSku.show("topic"):this.$refs.login.open(this.shareUrl)}}};t.default=r},"68d2":function(e,t,i){"use strict";function o(e){return e<10?"0".concat(e):e}function a(e){var t=new Date(e),i=t.getFullYear(),a=t.getMonth()+1,s=t.getDate(),r=t.getDay(),n=t.getHours(),d=t.getMinutes();return{allDate:"".concat(i,"/").concat(o(a),"/").concat(o(s)),date:"".concat(o(a),"-").concat(o(s)),day:"周".concat(["日","一","二","三","四","五","六"][r]),hour:o(n)+":"+o(d)}}Object.defineProperty(t,"__esModule",{value:!0}),t.currentTime=function(){var e=new Date,t=e.getFullYear(),i=e.getMonth()+1,a=e.getDate(),s=o(i)+"-"+o(a),r=e.getHours(),n=e.getMinutes(),d=e.getSeconds(),u=o(r)+":"+o(n)+":"+o(d);return{year:t,date:s,time:u}},t.initData=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=[],i=e?new Date(e):new Date,o=i.getTime(),s=864e5,r={0:"今天",1:"明天",2:"后天"},n=0;n<7;n++){var d,u={};u.date=a(o+s*n).date,u.timeStamp=o+s*n,u.week=""==e&&null!==(d=r[n])&&void 0!==d?d:a(o+s*n).day,t.push(u)}return t},t.initTime=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"09:00",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"18:30",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,o=[],s=a(Date.now()).allDate,r="".concat(s," ").concat(e),n="".concat(s," ").concat(t),d=new Date(r).getTime(),u=new Date(n).getTime(),l=36e5*i,c=d;c<=u;c+=l){var h={};h.time=a(c).hour,h.disable=!1,o.push(h)}return o},t.strFormat=o,t.timeStamp=a,t.weekDate=function(){var e=new Date,t=e.getDay(),i=e.getDate(),a=e.getMonth(),s=e.getYear(),r=new Date(s,a,i-t+1),n=new Date(s,a,i+(7-t)),d=[];return d[0]=o(r.getMonth()+1)+"-"+o(r.getDate()),d[1]=o(n.getMonth()+1)+"-"+o(n.getDate()),d}},"7c33":function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{currTime:"",isIphoneX:!1,orderCreateData:{is_balance:0,pay_password:"",is_invoice:0,invoice_type:0,invoice_title_type:1,is_tax_invoice:0,invoice_title:"",taxpayer_number:"",invoice_content:"",invoice_full_address:"",invoice_email:"",buyer_message:"",buyer_ask_delivery_title:""},orderPaymentData:{shop_goods_list:{site_name:"",express_type:[],coupon_list:[],invoice:{invoice_content_array:[]}},delivery:{site_name:"",express_type:[],coupon_list:[],invoice:{invoice_content_array:[]}},member_account:{balance:0,is_pay_password:0},local_config:{info:{start_time:0,end_time:0,time_week:[]}},invoice:{invoice_status:0},goods_num:0},isSub:!1,tempData:null,is_deposit_back:0,switch_state:!0,storeInfo:{storeList:[],currStore:{}},member_address:{name:"",mobile:""},timeInfo:{week:0,start_time:0,end_time:0,showTimeBar:!1},isFocus:!1,presale_id:0,pay_password:"",action:{icon:""},deliveryWeek:"",menuButtonBounding:{},memberAddress:null,localMemberAddress:null}},methods:{openPopup:function(e){this.$refs[e].open()},closePopup:function(e){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[e].close()},selectAddress:function(){var e={back:this.$util.getCurrentRoute().path,local:0,type:1};"local"==this.orderCreateData.delivery.delivery_type&&(e.local=1,e.type=2),this.$util.redirectTo("/pages_tool/member/address",e)},getOrderPaymentData:function(){var t=this;Object.assign(this.orderCreateData,e.getStorageSync("presaleOrderCreateData")),this.location&&(this.orderCreateData.latitude=this.location.latitude,this.orderCreateData.longitude=this.location.longitude),this.orderCreateData?this.$api.sendRequest({url:"/presale/api/ordercreate/depositPayment",data:this.orderCreateData,success:function(e){e.code>=0?(t.orderPaymentData=e.data,t.is_deposit_back=e.data.promotion_presale_info.is_deposit_back,t.handlePaymentData(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()):t.$util.showToast({title:"未获取到创建订单所需数据!！",success:function(){}})},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}}):this.$util.showToast({title:"未获取到创建订单所需数据!！",success:function(){setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500)}})},handlePaymentData:function(){var t=this;this.orderCreateData.delivery={},this.orderCreateData.coupon={},this.orderCreateData.buyer_message="",this.orderCreateData.is_balance=0,this.orderCreateData.pay_password="",this.orderCreateData.is_invoice=0,this.orderCreateData.invoice_type=1,this.orderCreateData.invoice_title_type=1,this.orderCreateData.is_tax_invoice=0,this.orderCreateData.invoice_title="",1==this.orderPaymentData.invoice.invoice_status&&(this.orderPaymentData.invoice.invoice_type=this.orderPaymentData.invoice.invoice_type.split(","),this.orderCreateData.invoice_type=this.orderPaymentData.invoice.invoice_type[0]);var i=JSON.parse(JSON.stringify(this.orderPaymentData));if(this.orderCreateData.order_key=i.order_key,void 0!=i.delivery.express_type&&void 0!=i.delivery.express_type[0]){var o=i.delivery.express_type;this.orderCreateData.delivery.store_id=0;var a=e.getStorageSync("delivery");if(a){var s=a.delivery_type,r=a.delivery_type_name;o.forEach((function(e){("store"==s&&e.name==s||"local"==s&&e.name==s)&&t.storeSelected(e)})),"store"==s&&(this.member_address={name:i.member_account.nickname,mobile:""!=i.member_account.mobile?i.member_account.mobile:""})}else{s=o[0].name;"store"==s&&(this.member_address={name:i.member_account.nickname,mobile:""!=i.member_account.mobile?i.member_account.mobile:""});r=o[0].title}this.orderCreateData.delivery.delivery_type=s,this.orderCreateData.delivery.delivery_type_name=r,"store"!=o[0].name&&"local"!=o[0].name||this.storeSelected(o[0])}if(this.orderPaymentData.is_virtual&&(this.orderCreateData.member_address={name:i.member_account.nickname,mobile:""!=i.member_account.mobile?i.member_account.mobile:""}),1==this.orderPaymentData.invoice.invoice_status){var n=this.orderPaymentData.invoice.invoice_content_array;n.length&&(this.orderCreateData.invoice_content=n[0])}this.orderPaymentData.goods_list.forEach((function(e){e.sku_spec_format?e.sku_spec_format=JSON.parse(e.sku_spec_format):e.sku_spec_format=[]})),this.orderCalculate()},getTimeStr:function(e){var t=parseInt(e/3600).toString(),i=parseInt(e%3600/60).toString();return 1==i.length&&(i="0"+i),1==t.length&&(t="0"+t),t+":"+i},orderCalculate:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);t.delivery=JSON.stringify(t.delivery),t.coupon=JSON.stringify(t.coupon),"store"==this.orderCreateData.delivery.delivery_type?t.member_address=JSON.stringify(this.member_address):t.member_address=JSON.stringify(t.member_address),this.$api.sendRequest({url:"/presale/api/ordercreate/depositCalculate",data:t,success:function(t){t.code>=0?(!t.data.is_virtual&&t.data.delivery&&("express"==t.data.delivery.delivery_type&&(e.memberAddress=t.data.delivery.member_address),"local"==t.data.delivery.delivery_type&&(e.localMemberAddress=t.data.delivery.member_address)),e.orderPaymentData.delivery_money=t.data.delivery_money,e.orderPaymentData.coupon_money=t.data.coupon_money,e.orderPaymentData.invoice_money=t.data.invoice_money,e.orderPaymentData.invoice_delivery_money=t.data.invoice_delivery_money,e.orderPaymentData.promotion_money=t.data.promotion_money,e.orderPaymentData.order_money=t.data.order_money,e.orderPaymentData.balance_money=t.data.balance_money,e.orderPaymentData.pay_money=t.data.pay_money,e.orderPaymentData.goods_money=t.data.goods_money,e.orderPaymentData.final_money=t.data.final_money,e.orderPaymentData.goods_num=t.data.goods_num,e.orderPaymentData.delivery.member_address=t.data.delivery.member_address,t.data.config.local&&(e.orderPaymentData.local_config=t.data.config.local),e.createBtn(),e.$forceUpdate()):e.$util.showToast({title:t.message})}})},createBtn:function(){return!(this.orderPaymentData.delivery&&"local"==this.orderPaymentData.delivery.delivery_type&&this.orderPaymentData.delivery&&this.orderPaymentData.delivery.error&&this.orderPaymentData.delivery.start_money>this.orderPaymentData.presale_deposit_money)&&!(this.orderPaymentData.delivery&&"local"==this.orderPaymentData.delivery.delivery_type&&this.orderPaymentData.delivery&&this.orderPaymentData.delivery.error&&""!==this.orderPaymentData.delivery.error)},orderCreate:function(){var t=this;if(this.verify()){if(this.isSub)return;this.isSub=!0;var i=this.$util.deepClone(this.orderCreateData);i.delivery=JSON.stringify(i.delivery),i.coupon=JSON.stringify(i.coupon),"store"==this.orderCreateData.delivery.delivery_type?i.member_address=JSON.stringify(this.member_address):i.member_address=JSON.stringify(i.member_address),this.$api.sendRequest({url:"/presale/api/ordercreate/depositCreate",data:i,success:function(i){if(i.code>=0)if(0==t.orderPaymentData.pay_money){switch(t.orderCreateData.delivery.delivery_type){case"express":break;case"store":t.$util.subscribeMessage("ORDER_VERIFY_OUT_TIME,VERIFY_CODE_EXPIRE,VERIFY");break;case"local":break;default:t.$util.subscribeMessage("ORDER_VERIFY_OUT_TIME,VERIFY_CODE_EXPIRE,VERIFY");break}t.$util.redirectTo("/pages_tool/pay/result",{code:i.data},"redirectTo")}else{var o=e.getStorageSync("presaleOrderCreateData");o.out_trade_no=i.data,e.setStorageSync("presaleOrderCreateData",o),t.$refs.choosePaymentPopup.getPayInfo(i.data),t.isSub=!1}else t.isSub=!1,e.hideLoading(),t.$refs.payPassword&&t.$refs.payPassword.close(),10==i.data.error_code||12==i.data.error_code?e.showModal({title:"订单未创建",content:i.message,confirmText:"去设置",success:function(e){e.confirm&&t.selectAddress()}}):t.$util.showToast({title:i.message})}})}},verify:function(){if(1==this.orderPaymentData.is_virtual){if(!this.orderCreateData.member_address.mobile.length)return this.$util.showToast({title:"请输入您的手机号码"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号码"}),!1}if(0==this.orderPaymentData.is_virtual){if("store"!=this.orderCreateData.delivery.delivery_type&&!this.orderPaymentData.delivery.member_address)return this.$util.showToast({title:"express"==this.orderCreateData.delivery.delivery_type?"请先选择您的收货地址":"当前地址不在该门店配送区域，请重新选择可配送该区域的门店"}),!1;if("{}"==JSON.stringify(this.orderCreateData.delivery))return this.$util.showToast({title:"店铺未设置配送方式"}),!1;if("store"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可提货的门店,请选择其他配送方式"}),!1;if(!this.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.member_address.mobile))return this.$util.showToast({title:"请输入正确的预留手机"}),!1}if("local"==this.orderCreateData.delivery.delivery_type&&!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可配送的门店,请选择其他配送方式"}),!1}return!(1==this.orderCreateData.is_invoice&&!this.invoiceVerify())},openSitePromotion:function(){this.$refs.sitePromotionPopup.open()},openSiteDelivery:function(){this.tempData={delivery:this.$util.deepClone(this.orderPaymentData.delivery)},this.$refs.deliveryPopup.open()},selectDeliveryType:function(t){e.setStorageSync("delivery",{delivery_type:t.name,delivery_type_name:t.title}),this.orderCreateData.delivery.delivery_type=t.name,this.orderCreateData.delivery.delivery_type_name=t.title,"store"==t.name&&(this.storeSelected(t),this.member_address.name=this.orderPaymentData.member_account.nickname,this.member_address.mobile||(this.member_address.mobile=""!=this.orderPaymentData.member_account.mobile?this.orderPaymentData.member_account.mobile:"")),"local"==t.name&&this.storeSelected(t),this.orderCalculate(),this.$forceUpdate()},storeSelected:function(e){this.storeInfo.storeList=e.store_list;var t=e.store_list[0]?e.store_list[0]:null;this.selectPickupPoint(t)},selectPickupPoint:function(t){if(t){this.orderCreateData.delivery.store_id=t.store_id,this.storeInfo.currStore=t;var i=e.getStorageSync("delivery");i&&(i.store_id=t.store_id,e.setStorageSync("delivery",i))}else this.orderCreateData.delivery.store_id=0,this.storeInfo.currStore={};this.orderCalculate(),this.$forceUpdate(),this.$refs["deliveryPopup"].close()},popupConfirm:function(e){this.$refs[e].close(),this.orderCalculate(),this.$forceUpdate(),this.tempData=null},useBalance:function(){this.orderCreateData.is_balance?this.orderCreateData.is_balance=0:this.orderCreateData.is_balance=1,this.orderCalculate(),this.$forceUpdate()},setPayPassword:function(){this.$util.redirectTo("/pages_tool/member/pay_password",{back:"/pages_promotion/presale/payment"})},noSet:function(){this.orderCreateData.is_balance=0,this.$refs.payPassword.close(),this.orderCalculate(),this.$forceUpdate()},input:function(t){var i=this;6==t.length&&(e.showLoading({title:"支付中...",mask:!0}),this.$api.sendRequest({url:"/api/member/checkpaypassword",data:{pay_password:t},success:function(o){o.code>=0?i.finalPay?(i.finalPay.pay_password=t,i.finalPay.member_id=i.memberInfo.member_id,i.finalPay.is_balance=i.finalPay.is_use_balance):(i.orderCreateData.pay_password=t,i.orderCreate()):(e.hideLoading(),i.$util.showToast({title:o.message}))},fail:function(t){e.hideLoading()}}))},imageError:function(e){this.orderPaymentData.goods_list[e].sku_image=this.$util.getDefaultImage().goods_img,this.$forceUpdate()},navigateBack:function(){this.$util.goBack()},changeIsInvoice:function(){0==this.orderCreateData.is_invoice?(this.orderCreateData.is_invoice=1,this.orderCreateData.invoice_type||(this.orderCreateData.invoice_type=this.orderPaymentData.invoice.invoice_type.split(",")[0])):this.orderCreateData.is_invoice=0,this.orderCalculate(),this.$forceUpdate()},changeInvoiceType:function(e){this.orderCreateData.invoice_type=e,this.orderCalculate(),this.$forceUpdate()},changeInvoiceTitleType:function(e){this.orderCreateData.invoice_title_type=e,this.orderCalculate(),this.$forceUpdate()},changeIsTaxInvoice:function(){0==this.orderCreateData.is_tax_invoice?this.orderCreateData.is_tax_invoice=1:this.orderCreateData.is_tax_invoice=0,this.$forceUpdate()},changeInvoiceContent:function(e){this.orderCreateData.invoice_content=e,this.$forceUpdate()},invoiceVerify:function(){if(!this.orderCreateData.invoice_title)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写发票抬头"}),!1;if(!this.orderCreateData.taxpayer_number&&2==this.orderCreateData.invoice_title_type)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写纳税人识别号"}),!1;if(1==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_full_address&&1==this.orderPaymentData.is_virtual)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写发票邮寄地址"}),!1;if(2==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_email)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写邮箱"}),!1;if(2==this.orderCreateData.invoice_type){if(!/^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/.test(this.orderCreateData.invoice_email))return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写正确的邮箱"}),!1}return!!this.orderCreateData.invoice_content||(this.$refs.invoicePopup.open(),this.$util.showToast({title:"请选择发票内容"}),!1)},saveInvoice:function(){1==this.orderCreateData.is_invoice?this.invoiceVerify()&&this.closePopup("invoicePopup"):this.closePopup("invoicePopup")},getTime:function(){var e=(new Date).getDay();this.timeInfo.week=["0","1","2","3","4","5","6"][e]},closeInvoicePopup:function(){this.$refs.invoicePopup.close()},switchChange:function(e){this.switch_state=e.detail.value},navigateTo:function(e){this.$util.redirectTo("/pages/goods/detail",{goods_id:e})},openChoosePayment:function(){if(1==this.is_deposit_back){if(!this.switch_state)return this.$util.showToast({title:"预售商品定金不支持退款，请确定同意定金不退款协议。"}),!1;this.$refs.depositRefund.open()}else if(e.setStorageSync("paySource","presale"),console.log(111),this.verify())switch(console.log(222),this.$refs.choosePaymentPopup.open(),this.orderCreateData.delivery.delivery_type){case"express":this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY,ORDER_DELIVERY");break;case"store":this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY");break;case"local":this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY,ORDER_DELIVERY");break;default:this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY");break}},subscribeMessage:function(){this.$util.subscribeMessage("ORDER_PAY,ORDER_DELIVERY,ORDER_TAKE_DELIVERY")},toPayOrder:function(){e.setStorageSync("paySource","presale"),this.verify()&&(this.$refs.depositRefund.close(),this.$refs.choosePaymentPopup.open())},closeDepositRefund:function(){this.$refs.depositRefund.close()},presaleAgreement:function(){this.$refs.presaleAgreement.open()},closePresaleAgreement:function(){this.$refs.presaleAgreement.close()},saveBuyerMessage:function(){this.$refs.buyerMessagePopup.close()},back:function(){e.navigateBack({delta:1})}},onShow:function(){this.storeToken?this.getOrderPaymentData():this.$util.redirectTo("/pages_tool/login/index");var t=e.getStorageSync("presaleOrderCreateData");t&&t.out_trade_no&&this.$util.redirectTo("/pages_promotion/presale/order_list",{},"redirectTo"),this.getTime(),this.isIphoneX=this.$util.uniappIsIPhoneX()},onLoad:function(t){t.id&&(this.presale_id=t.id),this.location||this.$util.getLocation(),this.menuButtonBounding=e.getMenuButtonBoundingClientRect()},onHide:function(){this.$refs.loadingCover&&this.$refs.loadingCover.show()},computed:{balanceDeduct:function(){var e=parseFloat(this.orderPaymentData.member_account.balance_total)<parseFloat(this.orderPaymentData.presale_deposit_money)?parseFloat(this.orderPaymentData.member_account.balance_total):parseFloat(this.orderPaymentData.presale_deposit_money);return e.toFixed(2)},presaleDiscount:function(){return(parseFloat(this.orderPaymentData.presale_money)-parseFloat(this.orderPaymentData.presale_deposit_money)).toFixed(2)}},watch:{location:function(e){e&&this.getOrderPaymentData()}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)},promotion:function(e){var t="";return e&&Object.keys(e).forEach((function(i){t+=e[i].content+"　"})),t}}};t.default=i}).call(this,i("df3c")["default"])},8151:function(e,t,i){"use strict";var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(i("7eb4")),s=o(i("ee10")),r={data:function(){return{goodsRoute:"/pages_promotion/groupbuy/detail",posterApi:"/groupbuy/api/goods/poster",groupbuy_id:0}},onLoad:function(e){var t=this;if(this.skuId=e.sku_id||0,this.groupbuy_id=e.groupbuy_id||0,e.id&&(this.groupbuy_id=e.id),e.scene){var i=decodeURIComponent(e.scene);i=i.split("&"),i.length&&i.forEach((function(e){-1!=e.indexOf("id")&&(t.groupbuy_id=e.split("-")[1]),-1!=e.indexOf("sku_id")&&(t.skuId=e.split("-")[1])}))}},onShow:function(){var e=this;return(0,s.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getGoodsSkuDetail();case 2:case"end":return t.stop()}}),t)})))()},methods:{getGoodsSkuDetail:function(){var e=this;return(0,s.default)(a.default.mark((function t(){var i,o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:"/groupbuy/api/goods/detail",async:!1,data:{groupbuy_id:e.groupbuy_id}});case 2:i=t.sent,o=i.data,null!=o.goods_sku_detail?(e.goodsSkuDetail=o.goods_sku_detail,0==e.skuId&&(e.skuId=e.goodsSkuDetail.sku_id),e.goodsSkuDetail.end_time-i.timestamp>0?e.goodsSkuDetail.timeMachine=e.$util.countDown(e.goodsSkuDetail.end_time-i.timestamp):(e.$util.showToast({title:"活动已结束"}),setTimeout((function(){e.$util.redirectTo("/pages/goods/detail",{goods_id:e.goodsSkuDetail.goods_id},"redirectTo")}),1e3)),e.shareQuery="groupbuy_id="+e.groupbuy_id+"&sku_id="+e.skuId,e.shareUrl=e.goodsRoute+"?"+e.shareQuery,e.chatRoomParams={sku_id:e.goodsSkuDetail.sku_id,type:"groupbuy",type_id:e.goodsSkuDetail.groupbuy_id},e.posterParams={groupbuy_id:e.groupbuy_id},e.handleGoodsSkuData(),e.goodsSkuDetail.show_price=e.goodsSkuDetail.groupbuy_price,e.goodsSkuDetail.save_price=e.goodsSkuDetail.price-e.goodsSkuDetail.show_price>0?(e.goodsSkuDetail.price-e.goodsSkuDetail.show_price).toFixed(2):0,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):e.$util.redirectTo("/pages_tool/goods/not_exist",{},"redirectTo");case 5:case"end":return t.stop()}}),t)})))()},groupbuy:function(){this.storeToken?this.$refs.goodsSku.show("groupbuy"):this.source_member?this.$refs.login.open(this.shareUrl+"&source_member="+this.source_member):this.$refs.login.open(this.shareUrl)}}};t.default=r},"870a":function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={methods:{orderClose:function(t,i){var o=this;e.showModal({title:"提示",content:"您确定要关闭该订单吗？",success:function(e){e.confirm&&o.$api.sendRequest({url:"/presale/api/order/close",data:{order_id:t},success:function(e){0==e.code?"function"==typeof i&&i():o.$util.showToast({title:e.message,duration:2e3})}})}})},deleteOrder:function(t,i){var o=this;e.showModal({title:"提示",content:"您确定要删除该订单吗？",success:function(e){e.confirm&&o.$api.sendRequest({url:"/presale/api/order/delete",data:{order_id:t},success:function(e){0==e.code?"function"==typeof i&&i():o.$util.showToast({title:e.message,duration:2e3})}})}})},refundDeposit:function(t,i){var o=this;e.showModal({title:"提示",content:"您确定要退定金吗？",success:function(e){e.confirm&&o.$api.sendRequest({url:"/presale/api/refund/applyRefund",data:{order_id:t},success:function(e){0==e.code?"function"==typeof i&&i():o.$util.showToast({title:e.message,duration:2e3})}})}})},orderPayFinal:function(e,t){var i=this,o="/presale/api/order/pay",a={id:e.id};""==e.final_out_trade_no&&(o="/presale/api/ordercreate/finalCreate",a.is_balance=this.isBalance),this.$api.sendRequest({url:o,data:a,success:function(e){i.isSub=!1,e.code>=0?0==i.payMoney?i.$util.redirectTo("/pages_tool/pay/result",{code:e.data},"redirectTo"):i.$refs.choosePaymentPopup.getPayInfo(e.data):i.$util.showToast({title:e.message}),i.isBalance=0},fail:function(e){i.isSub=!1}})},orderPayDeposit:function(e,t){var i=this;this.$api.sendRequest({url:"/presale/api/order/pay",data:{id:e.id},success:function(e){i.isSub=!1,e.code>=0?i.$refs.choosePaymentPopup.getPayInfo(e.data):i.$util.showToast({title:e.message})},fail:function(e){i.isSub=!1}})},openPaymentPopup:function(t,i){this.payType=i,"final_money"==i?""==t.final_out_trade_no&&(this.payMoney=t.order_money-t.presale_deposit_money):"presale_deposit_money"==i&&(this.payMoney=t.pay_deposit_money),e.setStorageSync("paySource","presale"),this.$refs.choosePaymentPopup.open()}}};t.default=i}).call(this,i("df3c")["default"])},"8b3c":function(e,t,i){"use strict";var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(i("7eb4")),s=o(i("ee10")),r={data:function(){return{goodsRoute:"/pages_promotion/presale/detail",posterApi:"/presale/api/goods/poster",id:0,groupId:0}},onLoad:function(e){var t=this;if(this.id=e.id||0,this.skuId=e.sku_id||0,e.scene){var i=decodeURIComponent(e.scene);i=i.split("&"),i.length&&i.forEach((function(e){-1!=e.indexOf("id")&&(t.id=e.split("-")[1])}))}},onShow:function(){var e=this;return(0,s.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getGoodsSkuDetail();case 2:case"end":return t.stop()}}),t)})))()},methods:{getGoodsSkuDetail:function(){var e=this;return(0,s.default)(a.default.mark((function t(){var i,o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:"/presale/api/goods/detail",async:!1,data:{id:e.id,sku_id:e.skuId}});case 2:i=t.sent,o=i.data,null!=o.goods_sku_detail?(e.goodsSkuDetail=o.goods_sku_detail,e.goodsSkuDetail.purchased_num=e.goodsSkuDetail.purchased_num?e.goodsSkuDetail.purchased_num:0,e.skuId=e.goodsSkuDetail.sku_id,e.goodsSkuDetail.group_id=e.groupId,e.goodsSkuDetail.end_time-i.timestamp>0?e.goodsSkuDetail.timeMachine=e.$util.countDown(e.goodsSkuDetail.end_time-i.timestamp):(e.$util.showToast({title:"活动已结束"}),setTimeout((function(){e.$util.redirectTo("/pages/goods/detail",{goods_id:e.goodsSkuDetail.goods_id},"redirectTo")}),1e3)),e.shareQuery="id="+e.id,e.shareUrl=e.goodsRoute+"?"+e.shareQuery,e.chatRoomParams={sku_id:e.goodsSkuDetail.sku_id,type:"presale",type_id:e.id},e.posterParams={id:e.id},e.handleGoodsSkuData(),e.goodsSkuDetail.show_price=e.goodsSkuDetail.goods_price,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):e.$util.redirectTo("/pages_tool/goods/not_exist",{},"redirectTo");case 5:case"end":return t.stop()}}),t)})))()},presale:function(){this.storeToken?this.$refs.goodsSku.show("presale"):this.source_member?this.$refs.login.open(this.shareUrl+"&source_member="+this.source_member):this.$refs.login.open(this.shareUrl)},toOrderDetail:function(){this.$util.redirectTo("/pages_promotion/presale/order_list")}}};t.default=r},9851:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,Function.prototype.asyAfter=function(e){var t=this;return function(){var i=t.apply(this,arguments);return"next"===i?e.apply(this,arguments):i}},Date.prototype.pattern=function(e){var t={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours()%12==0?12:this.getHours()%12,"H+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};for(var i in/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length))),/(E+)/.test(e)&&(e=e.replace(RegExp.$1,(RegExp.$1.length>1?RegExp.$1.length>2?"星期":"周":"")+{0:"日",1:"一",2:"二",3:"三",4:"四",5:"五",6:"六"}[this.getDay()+""])),t)new RegExp("("+i+")").test(e)&&(e=e.replace(RegExp.$1,1==RegExp.$1.length?t[i]:("00"+t[i]).substr((""+t[i]).length)));return e};var o=function(){},a=o.prototype;a.getUnix=function(){return(new Date).getTime()},a.getTodayUnix=function(){var e=new Date,t="".concat(e.getFullYear(),"/").concat(e.getMonth()+1,"/").concat(e.getDate()," 00:00:00");return new Date(t).getTime()},a.getYearUnix=function(){var e=new Date;return e.setMonth(0),e.setDate(1),e.setHours(0),e.setMinutes(0),e.setSeconds(0),e.setMilliseconds(0),e.getTime()},a.getLastDate=function(e){if(e){var t=new Date(e);if(t.pattern)return t.pattern("yyyy-MM-dd");var i=t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1,o=t.getDate()<10?"0"+t.getDate():t.getDate();return t.getFullYear()+"-"+i+"-"+o}};var s=new RegExp("-","g");a.getFormatTime=function(e,t){if(!e)return"";switch(function(e){return/^\[object\s(.*)\]$/.exec(Object.prototype.toString.call(e))[1]}(e)){case"Date":e=e.getTime();break;case"String":e=e.replace(s,"/");break;default:e=new Date(e).getTime();break}var i=this.getUnix(),o=(this.getYearUnix(),(i-e)/1e3);if(e>i&&t)return this.getLastDate(e);return function(e,t){var i=function(e){return e<=0||Math.floor(e/60)<=0?"刚刚":"next"}.asyAfter((function(e){return e<3600?Math.floor(e/60)+"分钟前":"next"})).asyAfter((function(e,t){var i=r.getTodayUnix();return e>=3600&&t-i>=0?Math.floor(e/60/60)+"小时前":"next"})).asyAfter((function(e,t){var i=r.getTodayUnix();return e=(i-t)/1e3,e/86400<=31?Math.ceil(e/86400)+"天前":"next"})).asyAfter((function(e,t){return r.getLastDate(t)}));return i(e,t)}(o,e)};var r=new o,n=r;t.default=n},9978:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{isIphoneX:!1,orderCreateData:{buyer_message:""},orderPaymentData:{},isSub:!1,judge:!0,min:1,editLock:!1}},methods:{payClose:function(){this.$util.redirectTo("/pages_promotion/giftcard/order_list",{},"redirectTo")},openPopup:function(e){this.$refs[e].open()},closePopup:function(e){this.$refs[e].close()},getOrderPaymentData:function(){var t=this;this.orderCreateData=e.getStorageSync("giftcardOrderCreateData");var i=e.getStorageSync("pay_flag");this.orderCreateData?this.$api.sendRequest({url:"/giftcard/api/ordercreate/calculate",data:this.orderCreateData,success:function(e){e.code>=0?(t.orderPaymentData=e.data,t.orderPaymentData.timestamp=e.timestamp,t.handlePaymentData(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:"未获取到创建订单所需数据！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}}):1==i?e.removeStorageSync("pay_flag"):(this.$util.showToast({title:"未获取到创建订单所需数据！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))},cartNumChange:function(e){this.editLock||(this.editLock=!0,this.orderCreateData.num=""===e?0:e,Object.assign(this.orderPaymentData,this.orderCreateData),this.orderCalculate())},handlePaymentData:function(){this.orderCreateData.buyer_message="",Object.assign(this.orderPaymentData,this.orderCreateData),this.orderCalculate()},getTimeStr:function(e){var t=parseInt(e/3600).toString(),i=parseInt(e%3600/60).toString();return 1==i.length&&(i="0"+i),1==t.length&&(t="0"+t),t+":"+i},orderCalculate:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);this.$api.sendRequest({url:"/giftcard/api/ordercreate/calculate",data:t,success:function(t){t.code>=0?(e.orderPaymentData=t.data,e.createBtn(),e.$forceUpdate()):e.$util.showToast({title:t.message}),e.editLock=!1}})},createBtn:function(){return!0},orderCreate:function(){var t=this;if(this.verify()){if(this.isSub)return;this.isSub=!0,e.setStorageSync("paySource","giftcard");var i=this.$util.deepClone(this.orderCreateData);this.$api.sendRequest({url:"/giftcard/api/ordercreate/create",data:i,success:function(i){if(e.hideLoading(),i.code>=0)if(parseFloat(t.orderPaymentData.pay_money)>0){var o=e.getStorageSync("giftcardOrderCreateData");o.out_trade_no=i.data,e.setStorageSync("giftcardOrderCreateData",o),t.$refs.choosePaymentPopup.getPayInfo(i.data),t.isSub=!1}else t.$util.redirectTo("/pages_promotion/giftcard/order_list",{},"redirectTo");else t.isSub=!1,10==i.data.error_code||12==i.data.error_code?e.showModal({title:"订单未创建",content:i.message,confirmText:"去设置",success:function(e){e.confirm&&t.selectAddress()}}):t.$util.showToast({title:i.message})},fail:function(i){e.hideLoading(),t.isSub=!1}})}},verify:function(){return!0},imageError:function(e){this.orderPaymentData.order_goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},navigateTo:function(e){this.$util.redirectTo("/pages_promotion/giftcard/detail",{id:e})},openChoosePayment:function(){this.orderCreate()},saveBuyerMessage:function(){this.orderCalculate(),this.$refs.buyerMessagePopup.close()}},onShow:function(){e.getStorageSync("addressBack")&&e.removeStorageSync("addressBack"),this.storeToken?this.getOrderPaymentData():this.$util.redirectTo("/pages_tool/login/index"),this.judge=!0,this.isIphoneX=this.$util.uniappIsIPhoneX()},onHide:function(){this.$refs.loadingCover&&this.$refs.loadingCover.show()},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)}}};t.default=i}).call(this,i("df3c")["default"])},"9d19":function(e,t,i){"use strict";(function(e){var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;o(i("0817"));var a={data:function(){return{windowHeight:0,launch_id:0,bargain_id:0,info:{headimg:""},bargainRecord:[],page:1,totalPage:1,load:!1,timeMachine:null,bargainMoney:"0.00",poster:"-1",posterMsg:"",posterHeight:0,goodsDetail:null,launchInfo:null,maxBuy:1,launchList:null,showMore:!1,isOwn:0,my_bargain_money:0,shareImg:""}},onLoad:function(t){var i=this;if(setTimeout((function(){i.addonIsExist.bargain||(i.$util.showToast({title:"商家未开启砍价",mask:!0,duration:2e3}),setTimeout((function(){i.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.getHeight(),t.l_id&&(this.launch_id=t.l_id),t.b_id&&(this.bargain_id=t.b_id),t.is_own&&(this.isOwn=t.is_own),t.scene){var o=decodeURIComponent(t.scene);o=o.split("&"),o.length&&o.forEach((function(t){-1!=t.indexOf("m")&&e.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("l_id")&&(i.id=t.split("-")[1]),-1!=t.indexOf("b_id")&&(i.bargain_id=t.split("-")[1]),-1!=t.indexOf("is_own")&&(i.isOwn=t.split("-")[1])}))}this.getShareImg()},onShow:function(){this.getBargainInfo()},computed:{progress:function(){if(this.launchInfo&&this.goodsDetail){var e=this.goodsDetail.price-this.launchInfo.floor_price,t=parseInt((this.goodsDetail.price-this.launchInfo.curr_price)/e*100);return isNaN(t)?0:t}return 0},showNum:function(){return this.launchList&&this.launchList.length<3?this.launchList.length:3}},methods:{timeUp:function(){this.getBargainInfo()},getHeight:function(){var t=this;e.getSystemInfo({success:function(e){t.windowHeight=e.windowHeight-44,t.iphoneX&&(t.windowHeight=t.windowHeight-33)}})},getBargainInfo:function(){var e=this;this.load||(this.load=!0,this.$api.sendRequest({url:"/bargain/api/bargain/detail",data:{launch_id:this.launch_id,bargain_id:this.bargain_id},success:function(t){0==t.code?(e.goodsDetail=t.data.goods_sku_detail,e.goodsDetail.sku_spec_format&&(e.goodsDetail.sku_spec_format=JSON.parse(e.goodsDetail.sku_spec_format)),e.$langConfig.title(e.goodsDetail.sku_name),e.goodsDetail.unit=e.goodsDetail.unit||"件",e.goodsDetail.goods_spec_format&&(e.goodsDetail.goods_spec_format=JSON.parse(e.goodsDetail.goods_spec_format)),e.addonIsExist.form&&e.getGoodsForm(),t.data.launch_info&&Object.keys(t.data.launch_info).length>0&&(e.launchInfo=t.data.launch_info,0==e.launchInfo.status&&(e.timeMachine=e.$util.countDown(e.launchInfo.end_time-t.timestamp)),e.launch_id=e.launchInfo.launch_id),t.data.launch_list&&Object.keys(t.data.launch_list).length>0&&(e.launchList=t.data.launch_list),e.isOwn&&e.goodsDetail.is_own>0&&e.launchInfo&&e.launchInfo.self&&e.launchInfo.my_bargain_money&&(e.my_bargain_money=e.launchInfo.my_bargain_money,e.$refs.uniSelfBargainPopup.open()),!e.launchInfo||e.launchInfo.self||e.launchInfo.cut||1!=e.goodsDetail.bargain_status||e.$refs.uniHelpPopup.open(),e.load=!1,e.getBargainRecord(1),e.setPublicShare(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()):(e.load=!1,e.$util.redirectTo("/pages_promotion/bargain/my_bargain"))},fail:function(t){e.load=!1,e.$refs.loadingCover&&e.$refs.loadingCover.hide()}}))},getBargainRecord:function(e){var t=this;this.load||this.launchInfo&&(this.load=!0,this.$api.sendRequest({url:"/bargain/api/bargain/record",data:{page:e,id:this.launchInfo.launch_id},success:function(i){t.load=!1,t.totalPage=i.data.page_count,t.page=e,0==i.code&&i.data.list.length&&(t.bargainRecord=1==e?i.data.list:t.bargainRecord.concat(i.data.list)),t.showMore=!1,e<t.totalPage&&(t.showMore=!0)}}))},scrolltolower:function(){var e=this.page+1;!this.load&&e<=this.totalPage&&this.getBargainRecord(e)},browse:function(){this.$api.sendRequest({url:"/bargain/api/bargain/browse",data:{bargain_id:this.goodsDetail.bargain_id},success:function(e){}})},share:function(){this.$api.sendRequest({url:"/bargain/api/bargain/share",data:{bargain_id:this.goodsDetail.bargain_id},success:function(e){}})},refreshGoodsSkuDetail:function(e){Object.assign(this.goodsDetail,e)},createBargain:function(){var e=this;this.storeToken?(this.$util.subscribeMessage("BARGAIN_COMPLETE"),this.goodsDetail.sku_spec_format||this.goodsDetail.goods_form?this.$refs.goodsSku.show("bargain"):this.$api.sendRequest({url:"/bargain/api/bargain/launch",data:{id:this.goodsDetail.id},success:function(t){if(0==t.code){var i={l_id:t.data,b_id:e.bargain_id};e.goodsDetail.is_own&&(i.is_own=1),e.$util.redirectTo("/pages_promotion/bargain/detail",i,"redirectTo")}else e.$util.showToast({title:t.message})}})):this.$refs.login.open("/pages_promotion/bargain/detail?l_id="+this.launch_id+"&b_id="+this.bargain_id)},buyNow:function(){var t=this,i=e.getStorageSync("goodFormData"),o=function(){e.setStorage({key:"bargainOrderCreateData",data:{launch_id:t.launchInfo.launch_id},success:function(){t.$util.redirectTo("/pages_promotion/bargain/payment")}})};!i&&this.goodsDetail.goods_form?this.$refs.goodsSku.show("bargain",(function(){o()})):o()},bargain:function(){var e=this;this.storeToken?this.$api.sendRequest({url:"/bargain/api/bargain/bargain",data:{id:this.launchInfo.launch_id},success:function(t){0==t.code?(e.bargainMoney=parseFloat(t.data.bargain_money).toFixed(2),e.$refs.uniHelpPopup.close(),e.$refs.uniPopup.open(),e.getBargainInfo()):e.$util.showToast({title:t.message})}}):this.$refs.login.open("/pages_promotion/bargain/detail?l_id="+this.launch_id+"&b_id="+this.bargain_id)},closePopup:function(){this.$refs.uniPopup.close()},closeSelfPop:function(){this.$refs.uniSelfBargainPopup.close()},openSharePopup:function(){this.$refs.uniSelfBargainPopup.close(),this.$refs.sharePopup.open(),this.share()},closeSharePopup:function(){this.$refs.sharePopup.close()},copyUrl:function(){var e=this,t="嘿！朋友就差你这一刀了，帮一下忙呗~"+this.$config.h5Domain+"/pages_promotion/bargain/detail?l_id="+this.launch_id+"&b_id="+this.bargain_id;this.memberInfo&&this.memberInfo.member_id&&(t+="&source_member="+this.memberInfo.member_id),this.$util.copy(t,(function(){e.closeSharePopup()}))},toBargainRecode:function(){var t=e.createSelectorQuery().select(".bargin_introduction");t.boundingClientRect((function(t){e.pageScrollTo({duration:100,scrollTop:t.top+100})})).exec()},setPublicShare:function(){var e=this.$config.h5Domain+"/pages_promotion/bargain/detail?l_id="+this.launch_id+"&b_id="+this.bargain_id;this.memberInfo&&this.memberInfo.member_id&&(e+="&source_member="+this.memberInfo.member_id),this.$util.setPublicShare({title:this.goodsDetail.sku_name,desc:"嘿！朋友就差你这一刀了，帮一下忙呗~",link:e,imgUrl:this.goodsDetail.sku_image},(function(e){}))},openPosterPopup:function(){this.getGoodsPoster(),this.$refs.sharePopup.close()},closePosterPopup:function(){this.$refs.posterPopup.close()},getGoodsPoster:function(){var t=this;e.showLoading({title:"海报生成中..."});var i={l_id:this.launch_id,b_id:this.bargain_id,bargain_id:this.goodsDetail.bargain_id};this.memberInfo&&this.memberInfo.member_id&&(i.source_member=this.memberInfo.member_id),this.$api.sendRequest({url:"/bargain/api/goods/poster",data:{page:"/pages_promotion/bargain/detail",qrcode_param:JSON.stringify(i)},success:function(i){0==i.code?(t.poster=i.data.path+"?time="+(new Date).getTime(),t.$refs.posterPopup.open()):(t.posterMsg=i.message,t.$util.showToast({title:t.posterMsg})),e.hideLoading()},fail:function(t){e.hideLoading()}})},getNewArray:function(e,t){if(e){var i=0,o=[];while(i<e.length)o.push(e.slice(i,i+=t));return o}},toDetail:function(e){this.$util.redirectTo("/pages/goods/detail",{goods_id:e})},getShareImg:function(){var e=this,t={l_id:this.launch_id,b_id:this.bargain_id,bargain_id:this.bargain_id};this.$api.sendRequest({url:"/bargain/api/goods/shareimg",data:{page:"/pages_promotion/bargain/launch",qrcode_param:JSON.stringify(t)},success:function(t){0==t.code&&(e.shareImg=t.data.path)}})},openRulePopup:function(){this.$refs.rulePopup.open()},closeRulePopup:function(){this.$refs.rulePopup.close()},getGoodsForm:function(){var t=this;this.$api.sendRequest({url:"/form/api/form/goodsform",data:{goods_id:this.goodsDetail.goods_id},success:function(i){if(0==i.code&&i.data){t.$set(t.goodsDetail,"goods_form",i.data);var o=e.getStorageSync("goodFormData");!o&&t.launchInfo&&t.goodsDetail.goods_spec_format&&t.goodsDetail.goods_spec_format.forEach((function(e){e.value.forEach((function(e){for(var i=0;i<t.goodsDetail.sku_spec_format.length;i++){var o=t.goodsDetail.sku_spec_format[i];if(o.spec_id==e.spec_id&&o.spec_value_id!=e.spec_value_id){e.disabled=!0;break}}}))}))}}})}},filters:{cover:function(e){return"string"==typeof e&&e.length>0?e.substr(0,1)+"******"+e.substr(-1):""}},onShareAppMessage:function(){var e="/pages_promotion/bargain/detail?l_id="+this.launch_id+"&b_id="+this.bargain_id;return this.memberInfo&&this.memberInfo.member_id&&(e+="&source_member="+this.memberInfo.member_id),this.share(),{title:"嘿！朋友就差你这一刀了，帮一下忙呗~",imageUrl:this.shareImg?this.$util.img(this.shareImg):this.$util.img(this.goodsDetail.sku_image,{size:"big"}),path:e,success:function(e){},fail:function(e){},complete:function(e){}}}};t.default=a}).call(this,i("df3c")["default"])},"9fbb":function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{fenxiaoWords:{}}},methods:{getFenxiaoWrods:function(){var t=this;this.$api.sendRequest({url:"/fenxiao/api/config/words",success:function(i){i.code>=0&&i.data&&(t.fenxiaoWords=i.data,e.setStorageSync("fenxiaoWords",i.data))}})}},onShow:function(){e.getStorageSync("fenxiaoWords")&&(this.fenxiaoWords=e.getStorageSync("fenxiaoWords")),this.getFenxiaoWrods()}};t.default=i}).call(this,i("df3c")["default"])},a962:function(e,t,i){"use strict";(function(e){var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(i("7eb4")),s=o(i("ee10")),r={data:function(){return{jielong_id:"",jielong_info:{},jielong_list:[{goods_image:"",member_headimg:[]}],jielong_buy_page:{},time:"",timer:"",num:"",money:0,specifications:{},specificationsItem:[{spec_name:"",spec_value_name:"",value:[{sku_id:"",spec_value_name:""}]}],index:0,zIndex:!0,shoplist:[],display:!1,returnShoping:[],shareImage:"",switchImage:!1,seeMores:!0,status:"",cutting:5,horseRace:!0,lantern:{},shareImg:""}},onLoad:function(t){var i=this;if(this.jielong_id=t.jielong_id,t.scene){var o=decodeURIComponent(t.scene);o=o.split("&"),o.length&&o.forEach((function(e){-1!=e.indexOf("jielong_id")&&(i.jielong_id=e.split("-")[1])}))}if(t.source_member&&e.setStorageSync("source_member",t.source_member),t.scene){o=decodeURIComponent(t.scene);o=o.split("&"),o.length&&o.forEach((function(t){-1!=t.indexOf("sku_id")&&(i.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&e.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&e.setStorageSync("is_test",1)}))}this.getJielongDetail(),this.getJielongBuyPage(),this.getShopList(),this.openCartPopup()},onshow:function(){this.storeToken&&e.getStorageSync("source_member")&&this.$util.onSourceMember(e.getStorageSync("source_member"))},onShareAppMessage:function(e){var t=this.jielong_info.jielong_name,i=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),o=i.path;return{title:t,path:o,imageUrl:"",success:function(e){},fail:function(e){}}},onShareTimeline:function(){var e=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),t=e.query;return{title:this.jielong_info.jielong_name,query:t,imageUrl:""}},methods:{toLogin:function(){!this.storeToken&&this.$refs.login&&this.$refs.login.open("/pages_promotion/jielong/jielong?jielong_id="+this.jielong_id)},getJielongDetail:function(){var e=this;this.$api.sendRequest({url:"/jielong/api/Goods/jielongDetail",data:{jielong_id:this.jielong_id},success:function(t){0==t.code&&t.data&&t.data.info?(e.status=t.data.info.status,e.jielong_info=t.data.info,e.jielong_list=t.data.list,e.timer=t.timestamp,e.jielong_info.start_time>e.timer?e.time=e.$util.countDown(e.jielong_info.start_time-e.timer):e.jielong_info.start_time<e.timer&&e.jielong_info.end_time>e.timer&&(e.time=e.$util.countDown(e.jielong_info.end_time-e.timer)),e.$refs.loadingCover&&e.$refs.loadingCover.hide()):(e.$util.showToast({title:"未获取到活动信息"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1e3))}})},getJielongBuyPage:function(){var e=this;this.$api.sendRequest({url:"/jielong/api/Goods/jielongBuyPage",data:{jielong_id:this.jielong_id},success:function(t){0==t.code&&t.data&&(e.jielong_buy_page=t.data,e.jielong_buy_page.list.length>0?(e.horseRace=!0,e.running()):e.horseRace=!1)}})},goodsImg:function(e){var t=e.split(",");return t[0]?this.$util.img(t[0],{size:"mid"}):this.$util.getDefaultImage().goods},getGoodsSkuDetail:function(e){var t=this;return(0,s.default)(a.default.mark((function i(){var o,s;return a.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(t.storeToken){i.next=3;break}return t.$refs.login.open(t.currentRoute),i.abrupt("return");case 3:return i.next=5,t.$api.sendRequest({url:"/api/goodssku/getInfoForCategory",async:!1,data:{sku_id:e}});case 5:o=i.sent,s=o.data,s?(t.goodsSkuDetail=s,0==t.skuId&&(t.skuId=t.goodsSkuDetail.sku_id),t.goodsSkuDetail.unit=t.goodsSkuDetail.unit||"件",t.goodsSkuDetail.show_price=t.goodsSkuDetail.discount_price,t.goodsSkuDetail.sku_spec_format&&(t.goodsSkuDetail.sku_spec_format=JSON.parse(t.goodsSkuDetail.sku_spec_format)),t.goodsSkuDetail.goods_spec_format&&(t.goodsSkuDetail.goods_spec_format=JSON.parse(t.goodsSkuDetail.goods_spec_format))):t.$util.redirectTo("/pages/index/index");case 8:case"end":return i.stop()}}),i)})))()},settlement:function(){var t=this,i=[];if(0==this.shoplist.length)return!1;this.shoplist.forEach((function(e){i.push(e.cart_id)})),i=i.toString(),e.setStorage({key:"orderCreateData",data:{cart_ids:i,jielong_id:this.jielong_id},success:function(){t.$util.redirectTo("/pages/order/payment")}})},CartPopup:function(){this.toLogin()?this.toLogin():(this.$refs.cartPopup.open(),this.openCartPopup())},openCartPopup:function(){var e=this;this.$api.sendRequest({url:"/jielong/api/cart/goodsLists",data:{jielong_id:this.jielong_id},success:function(t){e.num=0,e.money=0;var i=t.data;i.length>0&&i.forEach((function(t){e.num=parseInt(t.num)+e.num,e.money=parseFloat(parseFloat(t.price)*parseInt(t.num)+parseFloat(e.money)).toFixed(2)})),e.returnShoping=i}})},closeCartPopup:function(){this.$refs.cartPopup.close()},singleSkuReduce:function(e){var t=this,i=e.sku_id;this.shoplist.forEach((function(o){var a=e.min_buy>0?e.min_buy:1;if(i==o.sku_id)if(o.num>a){var s=--o.num;t.edit(o.cart_id,s)}else(o.num===a||o.num<a)&&t.deleteItem(o.cart_id)})),this.getJielongDetail()},singleSkuPlus:function(t){if(!this.storeToken)return this.toLogin(),!1;if(0==this.status)return e.showToast({title:"活动未开始",icon:"none"}),!1;var i=t.sku_id,o=!0,a=t.min_buy>0?t.min_buy:1,s=t.goods_stock;if(t.max_buy>0&&t.max_buy<t.goods_stock&&(s=t.max_buy),0==this.shoplist.length){if(t.is_limit&&2===t.limit_type){if(t.purchased_num+a>s)return e.showToast({title:"您选择的商品，已超过最大限购量",icon:"none"}),!1}else if(a>s)return e.showToast({title:"您选择的商品，已超过最大限购量",icon:"none"}),!1;this.addShop(i,a)}else{var r=null,n=null;if(this.shoplist.forEach((function(e){i===e.sku_id&&(r=e.num+1,n=e.cart_id,o=!1)})),o){if(t.is_limit&&2===t.limit_type){if(t.purchased_num+a>s)return e.showToast({title:"您选择的商品，已超过最大限购量",icon:"none"}),!1}else if(a>s)return e.showToast({title:"您选择的商品，已超过最大限购量",icon:"none"}),!1;this.addShop(i,a)}else{if(t.is_limit&&2===t.limit_type){if(t.purchased_num+r>s)return e.showToast({title:"您选择的商品，已超过最大限购量",icon:"none"}),!1}else if(r>s)return e.showToast({title:"您选择的商品，已超过最大限购量",icon:"none"}),!1;this.edit(n,r)}}this.getJielongDetail()},clear:function(){var e=this;if(!this.storeToken)return this.toLogin(),!1;this.$api.sendRequest({url:"/jielong/api/cart/clear",data:{jielong_id:this.jielong_id},success:function(t){e.getShopList(),e.openCartPopup(),e.getJielongDetail()}})},codeView:function(t){var i=this;return this.storeToken?0==this.status?(e.showToast({title:"活动未开始",icon:"none"}),!1):(this.switchs(t),this.$refs.erWeiPopup.open(),this.zIndex=!1,this.display=!1,void(0==this.shoplist.length?this.display=!1:this.shoplist.forEach((function(e){t==e.sku_id&&(i.display=!0)})))):(this.toLogin(),!1)},close:function(){this.$refs.erWeiPopup.close(),this.zIndex=!0},switchs:function(e){var t=this;this.title="",this.getShopList(),this.openCartPopup(),this.$api.sendRequest({url:"/api/goodssku/getInfoForCategory",data:{sku_id:e},success:function(i){t.specifications=i.data,t.specificationsItem=JSON.parse(t.specifications.goods_spec_format),t.specificationsItem.forEach((function(i){i.value.forEach((function(i){i.sku_id==e&&(t.title=t.title+i.spec_value_name+"/")}))})),t.specifications.title=t.title,t.switchImage=t.specifications.sku_image.includes("http")}}),this.display=!1,this.shoplist.forEach((function(i){e==i.sku_id&&(t.display=!0)}))},getShopList:function(){var e=this;this.toLogin(),this.$api.sendRequest({url:"/jielong/api/cart/lists",data:{jielong_id:this.jielong_id},success:function(t){e.shoplist=t.data}})},addShop:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.$api.sendRequest({url:"/jielong/api/cart/add",data:{jielong_id:this.jielong_id,num:i,sku_id:e},success:function(e){t.getShopList(),t.openCartPopup(),t.getJielongDetail(),-1==e.code&&t.$util.showToast({title:e.message})}})},edit:function(e,t){var i=this;this.$api.sendRequest({url:"/jielong/api/cart/edit",data:{jielong_id:this.jielong_id,num:t,cart_id:e},success:function(e){i.getShopList(),i.openCartPopup(),i.getJielongDetail(),-1==e.code&&i.$util.showToast({title:e.message})}})},deleteItem:function(e){var t=this;this.$api.sendRequest({url:"/jielong/api/cart/delete",data:{num:0,cart_id:e},success:function(e){t.getShopList(),t.openCartPopup(),t.getJielongDetail()}}),this.display=!1},shoppingCart:function(e){this.display=!0;var t=e.sku_id;this.addShop(t),this.openCartPopup(),this.getJielongDetail()},share:function(){var t=this;if(!this.storeToken)return this.toLogin(),!1;this.shareImage="",e.showLoading({title:"加载中"}),this.$refs.share.open();var i=JSON.stringify({jielong_id:this.jielong_id});this.$api.sendRequest({url:"/jielong/api/goods/poster",data:{page:"/pages_promotion/jielong/jielong",qrcode_param:i},success:function(i){e.hideLoading(),t.shareImage=i.data.poster_path}})},order:function(){this.$util.redirectTo("/pages/order/list")},bottomImage:function(e){return!!e.includes("http")},seeMore:function(){this.seeMores=!this.seeMores,this.seeMores?this.cutting=5:this.cutting=this.jielong_buy_page.count},running:function(){var e=this,t=JSON.parse(JSON.stringify(this.jielong_buy_page.list)),i=1,o=t[0].nickname;o=o.slice(0,1),t[0].content=o+"**",this.lantern=t[0],setInterval((function(){i<t.length?(o=t[i].nickname,o=o.slice(0,1),t[i].content=o+"**",e.lantern=t[i],i++):i=0}),1e4)}}};t.default=r}).call(this,i("df3c")["default"])},b2ed:function(e,t,i){"use strict";(function(e){var o=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(i("7eb4")),s=o(i("ee10")),r={data:function(){return{posterApi:"/pintuan/api/goods/poster",goodsRoute:"/pages_promotion/pintuan/detail",groupId:0,pintuan_id:0,pintuanList:[],currentPintuan:{headimg:"",timeMachine:{}},openPopup:!1,timestamp:"",newList:[],pintuanPopShow:!1,groupDetail:null,pintuan_num_field:"pintuan_num",kill:0}},onLoad:function(e){var t=this;if(this.pintuan_id=e.pintuan_id||0,e.id&&(this.pintuan_id=e.id),this.groupId=e.group_id||0,e.scene){var i=decodeURIComponent(e.scene);i=i.split("&"),i.length&&i.forEach((function(e){-1!=e.indexOf("id")&&(t.pintuan_id=e.split("-")[1]),-1!=e.indexOf("group_id")&&(t.group_id=e.split("-")[1])}))}this.getShareImg()},onShow:function(){var e=this;return(0,s.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getGoodsSkuDetail();case 2:e.getPintuanGroupList(),e.groupId>0&&e.getGroupDetail();case 4:case"end":return t.stop()}}),t)})))()},methods:{getGoodsSkuDetail:function(){var e=this;return(0,s.default)(a.default.mark((function t(){var i,o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:"/pintuan/api/goods/detail",async:!1,data:{pintuan_id:e.pintuan_id}});case 2:i=t.sent,o=i.data,null!=o.goods_sku_detail?(e.goodsSkuDetail=o.goods_sku_detail,e.skuId=e.goodsSkuDetail.sku_id,e.goodsSkuDetail.group_id=e.groupId,e.goodsSkuDetail.pintuan_num_field=e.pintuan_num_field,e.goodsSkuDetail.end_time-i.timestamp>0?e.goodsSkuDetail.timeMachine=e.$util.countDown(e.goodsSkuDetail.end_time-i.timestamp):(e.$util.showToast({title:"活动已结束"}),setTimeout((function(){e.$util.redirectTo("/pages/goods/detail",{goods_id:e.goodsSkuDetail.goods_id},"redirectTo")}),1e3)),e.shareQuery="pintuan_id="+e.pintuan_id+"&group_id="+e.groupId,e.shareUrl=e.goodsRoute+"?"+e.shareQuery,e.chatRoomParams={sku_id:e.goodsSkuDetail.sku_id,type:"pintuan",type_id:e.goodsSkuDetail.pintuan_id},e.posterParams={id:e.pintuan_id},e.handleGoodsSkuData(),e.goodsSkuDetail.show_price=0==e.goodsSkuDetail.group_id?e.goodsSkuDetail.promotion_price:e.goodsSkuDetail.pintuan_price,e.goodsSkuDetail.save_price=e.goodsSkuDetail.price-e.goodsSkuDetail.show_price>0?(e.goodsSkuDetail.price-e.goodsSkuDetail.show_price).toFixed(2):0,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):e.$util.redirectTo("/pages_tool/goods/not_exist",{},"redirectTo");case 5:case"end":return t.stop()}}),t)})))()},refreshPintuan:function(){"ladder"==this.goodsSkuDetail.pintuan_type&&this.groupDetail&&(this.groupDetail.pintuan_num==this.goodsSkuDetail.pintuan_num?this.goodsSkuDetail.pintuan_num_field="pintuan_num":this.groupDetail.pintuan_num==this.goodsSkuDetail.pintuan_num_2?this.goodsSkuDetail.pintuan_num_field="pintuan_num_2":this.groupDetail.pintuan_num==this.goodsSkuDetail.pintuan_num_3&&(this.goodsSkuDetail.pintuan_num_field="pintuan_num_3"),this.goodsDetailViewInit(),this.$forceUpdate())},pintuanPirce:function(e){return"ordinary"==e.pintuan_type||"pintuan_num"==e.pintuan_num_field?e.pintuan_price:"pintuan_num_2"==e.pintuan_num_field?e.pintuan_price_2:"pintuan_num_3"==e.pintuan_num_field?e.pintuan_price_3:void 0},pintuan:function(){var t=this,i=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],o=arguments.length>1?arguments[1]:void 0,a=i?0:o||this.groupId;if(!this.storeToken)return this.shareQuery="pintuan_id="+this.pintuan_id,i||(this.shareQuery+="&group_id="+a),this.source_member&&(this.shareQuery+="&source_member="+this.source_member),this.shareUrl=this.goodsRoute+"?"+this.shareQuery,void this.$refs.login.open(this.shareUrl);if(this.$util.subscribeMessage("PINTUAN_COMPLETE,PINTUAN_FAIL"),this.groupDetail&&2==this.groupDetail.status&&this.groupDetail.order_id){var s=this;e.showModal({title:"提示",content:"您有一个拼团订单待付款，是否现在跳转支付？",success:function(e){e.confirm?s.$util.redirectTo("/pages/order/detail",{order_id:s.groupDetail.order_id}):e.cancel}})}else i?(this.resetPrice(),this.goodsSkuDetail.group_id=0):this.goodsSkuDetail.group_id=a||this.groupId+"",this.$forceUpdate(),this.$refs.goodsSku.show("pintuan",(function(){t.resetPrice()}))},buyNow:function(){var e=this;this.storeToken?this.$refs.goodsSku.show("buy_now",(function(){e.$store.dispatch("getCartNumber")})):this.source_member?this.$refs.login.open(this.shareUrl+"&source_member="+this.source_member):this.$refs.login.open(this.shareUrl)},getPintuanGroupList:function(){var e=this;this.$api.sendRequest({url:"/pintuan/api/pintuangroup/lists",data:{goods_id:this.goodsSkuDetail.goods_id},success:function(t){var i=t.data;if(e.timestamp=t.timestamp,null!=i&&i.length){e.pintuanList=i;for(var o=0;o<e.pintuanList.length;o++)e.pintuanList[o]["end_time"]>t.timestamp?(e.pintuanList[o].timeMachine=e.$util.countDown(e.pintuanList[o]["end_time"]-t.timestamp),e.pintuanList[o].currentTime=t.timestamp):e.pintuanList[o].timeMachine=null;e.newList=e.pintuanList.filter((function(e){return e.end_time>t.timestamp})),e.$forceUpdate()}}})},getGroupDetail:function(){var e=this;this.$api.sendRequest({url:"/pintuan/api/pintuangroup/info",data:{group_id:this.groupId},success:function(t){e.kill=t.data.pintuan_num-t.data.pintuan_count,0==t.code&&(t.data.end_time>t.timestamp?t.data.timeMachine=e.$util.countDown(t.data.end_time-t.timestamp):t.data.timeMachine=null,e.groupDetail=t.data,e.pintuanPopShow=!0,e.groupDetail&&2!=e.groupDetail.status&&(e.groupId=0,e.goodsSkuDetail.group_id=0,e.resetPrice()),"ladder"==e.goodsSkuDetail.pintuan_type&&(e.groupDetail.pintuan_num==e.goodsSkuDetail.pintuan_num?e.goodsSkuDetail.pintuan_num_field="pintuan_num":e.groupDetail.pintuan_num==e.goodsSkuDetail.pintuan_num_2?e.goodsSkuDetail.pintuan_num_field="pintuan_num_2":e.groupDetail.pintuan_num==e.goodsSkuDetail.pintuan_num_3&&(e.goodsSkuDetail.pintuan_num_field="pintuan_num_3"))),e.$forceUpdate()}})},openPinTuan:function(e,t,i,o,a){var s=this;this.memberInfo&&this.memberInfo.member_id==a?this.$util.showToast({title:"您不能参与自己的团"}):(this.openPopup=!0,this.currentPintuan={groupId:e,pintuan_num:t,timeMachine:this.$util.countDown(i),headimg:o},this.$refs.pintuanPopup.open((function(){s.goodsSkuDetail.group_id=0,s.openPopup=!1})))},closePinTuanPopup:function(){this.$refs.pintuanPopup.close()},joinPintuan:function(){this.closePinTuanPopup(),this.goodsSkuDetail.group_id=this.currentPintuan.groupId,this.goodsSkuDetail.show_price=this.goodsSkuDetail.pintuan_price,this.goodsSkuDetail.save_price=this.goodsSkuDetail.price-this.goodsSkuDetail.show_price>0?(this.goodsSkuDetail.price-this.goodsSkuDetail.show_price).toFixed(2):0,this.$forceUpdate(),this.pintuan(!1,this.currentPintuan.groupId)},pintuanImageError:function(e){this.pintuanList[e].headimg=this.$util.getDefaultImage().head,this.$forceUpdate()},resetPrice:function(){this.goodsSkuDetail.show_price=0==this.goodsSkuDetail.group_id?this.goodsSkuDetail.promotion_price:this.goodsSkuDetail.pintuan_price,this.goodsSkuDetail.save_price=this.goodsSkuDetail.price-this.goodsSkuDetail.show_price>0?(this.goodsSkuDetail.price-this.goodsSkuDetail.show_price).toFixed(2):0,this.$forceUpdate()},cutStrByte:function(e,t){if(!e||!t)return"";var i,o=e.length;if(o<=t/2)i=e,"0";else for(var a=0,s=0;s<o;s++){if(a+=this.getByteLen(e.charAt(s)),a>t){i=e.substring(0,s);break}if(a==t){i=e.substring(0,s+1);break}}return i||(i=e,"0"),i},getByteLen:function(e){var t=0;if(!e)return t;for(var i=0;i<e.length;i++)e[i]&&(null!=e[i].match(/[^\x00-\xff]/gi)?t+=2:t+=1);return t},getShareImg:function(){var e=this,t={id:this.pintuan_id,group_id:this.groupId};this.$api.sendRequest({url:"/pintuan/api/goods/shareimg",data:{page:"/pages_promotion/pintuan/detail",qrcode_param:JSON.stringify(t)},success:function(t){0==t.code&&(e.shareImg=t.data.path)}})}}};t.default=r}).call(this,i("df3c")["default"])},b7e4:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{isIphoneX:!1,orderCreateData:{is_balance:0,is_point:1,buyer_message:"",is_invoice:0,invoice_type:1,invoice_title_type:1,is_tax_invoice:0,invoice_title:"",taxpayer_number:"",invoice_content:"",invoice_full_address:"",invoice_email:"",member_address:{name:"",mobile:""},is_open_card:0,member_card_unit:"",delivery:{delivery_type:""}},orderPaymentData:{site_name:"",express_type:[],coupon_list:[],invoice:{invoice_content_array:[]},member_account:{balance:0,is_pay_password:0},delivery:{delivery_type:"",express_type:[],member_address:{name:"",mobile:""},local:{info:{start_time:0,end_time:0,time_week:[]}},delivery_store_info:{}},member_address:{name:"",mobile:""},local_config:{info:{start_time:0,end_time:0,time_week:[]}},delivery_store_info:{}},isSub:!1,tempData:null,manjian:[],storeInfo:{storeList:[],currStore:{}},member_address:{name:"",mobile:""},timeInfo:{week:0,start_time:0,end_time:0,showTime:!1,showTimeBar:!1},post_free:{},deliveryWeek:"",out_trade_no:null,menuButtonBounding:{},memberAddress:null,localMemberAddress:null}},onLoad:function(){var t=this;this.location||this.$util.getLocation(),this.menuButtonBounding=e.getMenuButtonBoundingClientRect(),this.isIphoneX=this.$util.uniappIsIPhoneX(),e.getStorageSync("addressBack")&&e.removeStorageSync("addressBack"),this.storeToken?this.getOrderPaymentData():this.$nextTick((function(){t.$refs.login.open("/pages_promotion/blindbox/fill_address")}))},methods:{getOrderPaymentData:function(){var t=this;if(!this.out_trade_no){this.orderCreateData=e.getStorageSync("blindOrderCreateData");var i=e.getStorageSync("pay_flag");this.orderCreateData?(this.location&&(this.orderCreateData.latitude=this.location.latitude,this.orderCreateData.longitude=this.location.longitude),this.$api.sendRequest({url:"/blindbox/api/order/payment",data:this.orderCreateData,success:function(e){e.code>=0?(t.orderPaymentData=e.data,t.handlePaymentData(),t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.orderCalculate()):(t.$util.showToast({title:"未获取到创建订单所需数据！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})):1==i?e.removeStorageSync("pay_flag"):(this.$util.showToast({title:"未获取到创建订单所需数据！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))}},handlePaymentData:function(){var t=this;this.orderCreateData.delivery={},this.orderCreateData.coupon={},this.orderCreateData.is_balance=0,this.orderCreateData.is_point=0,this.orderCreateData.is_invoice=0,this.orderCreateData.invoice_type=1,this.orderCreateData.invoice_title_type=1,this.orderCreateData.is_tax_invoice=0,this.orderCreateData.invoice_title="";var i=JSON.parse(JSON.stringify(this.orderPaymentData));if(this.orderCreateData.order_key=i.order_key,void 0!=i.delivery.express_type&&void 0!=i.delivery.express_type[0]){var o=i.delivery.express_type;this.orderCreateData.delivery.store_id=0;var a=e.getStorageSync("delivery");if(a){var s=a.delivery_type,r=a.delivery_type_name;o.forEach((function(e){("store"==s&&e.name==s||"local"==s&&e.name==s)&&t.storeSelected(e)})),"store"==s&&(this.member_address={name:i.member_account.nickname,mobile:""!=i.member_account.mobile?i.member_account.mobile:""})}else{s=o[0].name;"store"==s&&(this.member_address={name:i.member_account.nickname,mobile:""!=i.member_account.mobile?i.member_account.mobile:""});r=o[0].title}this.orderCreateData.delivery.delivery_type=s,this.orderCreateData.delivery.delivery_type_name=r,"store"!=o[0].name&&"local"!=o[0].name||this.storeSelected(o[0])}i.is_virtual&&(this.member_address={name:i.member_account.nickname,mobile:""!=i.member_account.mobile?i.member_account.mobile:""})},openPopup:function(e){this.$refs[e].open()},closePopup:function(e){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[e].close()},selectAddress:function(){var e={back:encodeURIComponent("/pages_promotion/blindbox/fill_address?blindbox_goods_id="+this.blindbox_goods_id+"&out_trade_no="+this.outTradeNo),local:0,type:1};"local"==this.orderPaymentData.delivery.delivery_type&&(e.local=1,e.type=2),this.$util.redirectTo("/pages_tool/member/address",e)},orderCalculate:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);t.delivery=JSON.stringify(t.delivery),this.$api.sendRequest({url:"/blindbox/api/order/calculate",data:t,success:function(t){t.code>=0?(t.data.delivery&&("express"==t.data.delivery.delivery_type&&(e.memberAddress=t.data.delivery.member_address),"local"==t.data.delivery.delivery_type&&(e.localMemberAddress=t.data.delivery.member_address)),t.data.config.local&&t.data.config.local.is_use&&(e.orderPaymentData.local_config=t.data.config.local),t.data.delivery.delivery_store_info&&(e.orderPaymentData.delivery_store_info=t.data.delivery.delivery_store_info),e.$forceUpdate()):e.$util.showToast({title:t.message})}})},orderCreate:function(){var t=this;if(this.verify()){if(this.isSub)return;this.isSub=!0,e.showLoading({title:""});var i=this.$util.deepClone(this.orderCreateData);i.delivery=JSON.stringify(i.delivery),"store"==this.orderCreateData.delivery.delivery_type?i.member_address=JSON.stringify(this.member_address):i.member_address=JSON.stringify(i.member_address),this.$api.sendRequest({url:"/blindbox/api/order/create",data:i,success:function(i){e.hideLoading(),0==i.code?t.$util.redirectTo("/pages/order/list",{},"redirectTo"):(t.$util.showToast({title:i.message}),t.isSub=!1)},fail:function(i){e.hideLoading(),t.isSub=!1}})}},verify:function(){if(0==this.orderPaymentData.is_virtual){if(!this.orderCreateData.delivery||!this.orderCreateData.delivery.delivery_type)return this.$util.showToast({title:"商家未设置配送方式"}),!1;if("store"!=this.orderCreateData.delivery.delivery_type&&!this.member_address)return this.$util.showToast({title:"请先选择您的收货地址"}),!1;if("store"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可提货的门店,请选择其他配送方式"}),!1;if(!this.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.member_address.mobile))return this.$util.showToast({title:"请输入正确的预留手机"}),!1;if(!this.orderCreateData.delivery.buyer_ask_delivery_time.start_date||!this.orderCreateData.delivery.buyer_ask_delivery_time.end_date)return this.$util.showToast({title:"请选择自提时间"}),!1}if("local"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可配送的门店,请选择其他配送方式"}),!1;if(this.orderPaymentData.config.local&&this.orderPaymentData.config.local.is_use&&this.orderPaymentData.delivery.local&&this.orderPaymentData.delivery.local.info&&1==this.orderPaymentData.delivery.local.info.time_is_open&&(!this.orderCreateData.delivery.buyer_ask_delivery_time.start_date||!this.orderCreateData.delivery.buyer_ask_delivery_time.end_date))return this.$util.showToast({title:"请选择配送时间"}),!1}}return!0},selectDeliveryType:function(t){e.setStorageSync("delivery",{delivery_type:t.name,delivery_type_name:t.title}),this.orderCreateData.delivery.delivery_type=t.name,this.orderCreateData.delivery.delivery_type_name=t.title,"store"==t.name&&(this.storeSelected(t),this.member_address.name=this.orderPaymentData.member_account.nickname,this.member_address.mobile||(this.member_address.mobile=""!=this.orderPaymentData.member_account.mobile?this.orderPaymentData.member_account.mobile:"")),"local"==t.name&&this.storeSelected(t),this.orderCalculate(),this.$forceUpdate()},storeSelected:function(e){this.storeInfo.storeList=e.store_list;var t=e.store_list[0]?e.store_list[0]:null;this.selectPickupPoint(t)},selectPickupPoint:function(t){if(t){this.orderCreateData.delivery.store_id=t.store_id,this.storeInfo.currStore=t;var i=e.getStorageSync("delivery");i&&(i.store_id=t.store_id),e.setStorageSync("delivery",i)}else this.orderCreateData.delivery.store_id=0,this.storeInfo.currStore={};this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.orderCreateData.buyer_ask_delivery_title="",this.orderCalculate(),this.$forceUpdate(),this.$refs["deliveryPopup"].close()},popupConfirm:function(e){this.orderCalculate(),this.$forceUpdate(),this.tempData=null,this.$refs[e].close()},imageError:function(e){this.orderPaymentData.goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},navigateTo:function(e){this.$util.redirectTo("/pages/goods/detail",{sku_id:e})},localtime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=this.$util.deepClone(this.orderPaymentData.local_config.info);t.delivery_time&&(t.end_time=t.delivery_time[t.delivery_time.length-1].end_time);var i={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(i,e)},storetime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.orderPaymentData.delivery_store_info){var t=this.$util.deepClone(this.storeInfo.currStore);t.delivery_time?(t.delivery_time=JSON.parse(t.delivery_time),t.end_time=t.delivery_time[t.delivery_time.length-1].end_time):t.delivery_time=[{start_time:t.start_time,end_time:t.end_time}];var i={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(i,e),this.$forceUpdate()}},selectPickupTime:function(e){e.data&&e.data.month&&(this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:e.data.start_date,end_date:e.data.end_date},"今天"==e.data.title||"明天"==e.data.title?this.orderCreateData.buyer_ask_delivery_title=e.data.title+"("+e.data.time+")":this.orderCreateData.buyer_ask_delivery_title=e.data.month+"("+e.data.time+")",this.$forceUpdate())},saveBuyerMessage:function(){this.$refs.buyerMessagePopup.close()},subscribeMessage:function(){var e="ORDER_PAY,ORDER_DELIVERY,ORDER_TAKE_DELIVERY";this.orderCreateData.delivery&&"store"==this.orderCreateData.delivery.delivery_type&&(e="ORDER_PAY,ORDER_TAKE_DELIVERY"),this.$util.subscribeMessage(e)},back:function(){e.navigateBack({delta:1})}},computed:{goodsData:function(){if(this.orderPaymentData.goods_list)return this.orderPaymentData.goods_list.forEach((function(e){e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format))})),this.orderPaymentData}},watch:{location:function(e){e&&this.getOrderPaymentData()},storeToken:function(e,t){e&&this.getOrderPaymentData()}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)}}};t.default=i}).call(this,i("df3c")["default"])},e1ff:function(e,t,i){"use strict";(function(e,o){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;i("edd0");var a={data:function(){return{categoryList:[],goodsList:[],categoryId:0,is_fenxiao:!1,currIndex:0,poster:"-1",posterMsg:"",shareUrl:"/pages_promotion/fenxiao/goods_list",shareType:"goods",templateId:["default"]}},onLoad:function(e){var t=this;setTimeout((function(){t.addonIsExist.fenxiao||(t.$util.showToast({title:"商家未开启分销",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),e.templateId&&(this.templateId=e.templateId.split(",")),this.getGoodsCategoryTree()},onShow:function(){this.storeToken?(this.fenxiaoWords&&this.fenxiaoWords.concept&&this.$langConfig.title(this.fenxiaoWords.concept+"中心"),this.is_fenxiao=Boolean(this.memberInfo.is_fenxiao)):this.$util.redirectTo("/pages_tool/login/index",{back:"/pages_promotion/fenxiao/goods_list"},"redirectTo")},methods:{getGoodsList:function(e){var t=this;this.$api.sendRequest({url:"/fenxiao/api/goods/page",data:{page:e.num,page_size:e.size,category_id:this.categoryId},success:function(i){var o=[],a=i.message;0==i.code&&i.data?o=i.data.list:t.$util.showToast({title:a}),e.endSuccess(o.length),1==e.num&&(t.goodsList=[]),t.goodsList=t.goodsList.concat(o),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(i){e.endErr(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},navToDetailPage:function(e){this.$util.redirectTo("/pages/goods/detail",{goods_id:e.goods_id})},getGoodsCategoryTree:function(){var e=this;this.$api.sendRequest({url:"/api/goodscategory/tree",data:{level:1},success:function(t){0==t.code&&(e.categoryList=t.data)}})},changeCategory:function(e){this.categoryId=e,this.$refs.mescroll&&(this.$refs.mescroll.refresh(),this.$refs.mescroll.myScrollTo(0))},shareFn:function(e,t){this.shareType=e,"fenxiao"==this.shareType?this.openPosterPopup():(this.currIndex=t,this.$refs.sharePopup.open())},openBusinessView:function(){e.openBusinessView&&e.openBusinessView({businessType:"friendGoodsRecommend",extraData:{product:{item_code:this.goodsList[this.currIndex].goods_id,title:this.goodsList[this.currIndex].sku_name,image_list:this.$util.img(this.goodsList[this.currIndex].goods_image)}},success:function(e){console.log("success",e)},fail:function(e){console.log("fail",e)}})},openPosterPopup:function(){this.getGoodsPoster(),this.$refs.sharePopup.close()},closePosterPopup:function(){this.$refs.posterPopup.close()},getGoodsPoster:function(){var e=this;o.showLoading({title:"海报生成中..."});var t="",i={};"goods"==this.shareType?(t="/api/goods/poster",i.page="/pages/goods/detail",i.qrcode_param=JSON.stringify({goods_id:this.goodsList[this.currIndex].goods_id,source_member:this.memberInfo.member_id})):(t="/fenxiao/api/fenxiao/poster",i.page="/pages/index/index",i.qrcode_param=JSON.stringify({}),i.template_id=this.templateId[0]),this.$api.sendRequest({url:t,data:i,success:function(t){0==t.code?(e.poster=t.data.path+"?time="+(new Date).getTime(),e.$refs.posterPopup.open()):e.posterMsg=t.message,o.hideLoading()},fail:function(e){o.hideLoading()}})},saveGoodsPoster:function(){var e=this,t=this.$util.img(this.poster);o.downloadFile({url:t,success:function(t){200===t.statusCode&&o.saveImageToPhotosAlbum({filePath:t.tempFilePath,success:function(){e.$util.showToast({title:"保存成功"})},fail:function(){e.$util.showToast({title:"保存失败，请稍后重试"})}})}})},openSharePopup:function(){this.$refs.sharePopup.open()},closeSharePopup:function(){this.$refs.sharePopup.close()},copyUrl:function(){var e=this,t=this.$config.h5Domain+"/pages/goods/detail?goods_id="+this.goodsList[this.currIndex].goods_id+"&source_member="+this.memberInfo.member_id;this.$util.copy(t,(function(){e.closeSharePopup()}))},imageError:function(e){this.goodsList[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}},onShareAppMessage:function(e){var t=this.shareUrl;return"goods"==this.shareType&&(t="/pages/goods/detail?goods_id=".concat(this.goodsList[this.currIndex].goods_id,"&sku_id=").concat(this.goodsList[this.currIndex].sku_id,"&source_member=").concat(this.memberInfo.member_id)),{title:this.goodsList[this.currIndex].sku_name,imageUrl:this.$util.img(this.goodsList[this.currIndex].goods_image,{size:"big"}),path:t,success:function(e){},fail:function(e){}}}};t.default=a}).call(this,i("3223")["default"],i("df3c")["default"])},f9ec:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{isIphoneX:!1,orderCreateData:{member_address:{name:"",mobile:""}},orderPaymentData:{exchange_info:{type:0},delivery:{delivery_type:"",express_type:[],member_address:{name:"",mobile:""},local:{info:{start_time:0,end_time:0,time_week:[]}}}},isSub:!1,tempData:null,storeInfo:{storeList:[],currStore:{}},member_address:{name:"",mobile:""},timeInfo:{week:0,start_time:0,end_time:0,showTime:!1,showTimeBar:!1},deliveryWeek:"",judge:!0,menuButtonBounding:{}}},methods:{openPopup:function(e){this.$refs[e].open()},closePopup:function(e){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[e].close()},selectAddress:function(){var e={back:"/pages_promotion/point/payment",local:0,type:1};"local"==this.orderPaymentData.delivery.delivery_type&&(e.local=1,e.type=2),this.$util.redirectTo("/pages_tool/member/address",e)},getOrderPaymentData:function(){var t=this;this.orderCreateData=e.getStorageSync("exchangeOrderCreateData");var i=e.getStorageSync("pay_flag");this.orderCreateData?(this.location&&(this.orderCreateData.latitude=this.location.latitude,this.orderCreateData.longitude=this.location.longitude),this.$api.sendRequest({url:"/pointexchange/api/ordercreate/payment",data:this.orderCreateData,success:function(e){e.code>=0?(t.orderPaymentData=e.data,t.orderPaymentData.timestamp=e.timestamp,t.handlePaymentData(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:"未获取到创建订单所需数据！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})):1==i?e.removeStorageSync("pay_flag"):(this.$util.showToast({title:"未获取到创建订单所需数据！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))},handlePaymentData:function(){this.orderCreateData.delivery={},this.orderCreateData.buyer_message="";var t=JSON.parse(JSON.stringify(this.orderPaymentData));if(this.orderCreateData.order_key=t.order_key,this.orderCreateData.delivery.store_id=0,this.orderCreateData.member_address=t.delivery.member_address,void 0!=t.delivery.express_type&&void 0!=t.delivery.express_type[0]){var i=e.getStorageSync("delivery"),o=t.delivery.express_type[0];t.delivery.express_type.forEach((function(e){i&&e.name==i.delivery_type&&(o=e)})),this.selectDeliveryType(o)}this.orderPaymentData.is_virtual&&(this.orderCreateData.member_address={mobile:""!=t.member_account.mobile?t.member_account.mobile:""}),this.orderCalculate()},getTimeStr:function(e){var t=parseInt(e/3600).toString(),i=parseInt(e%3600/60).toString();return 1==i.length&&(i="0"+i),1==t.length&&(t="0"+t),t+":"+i},orderCalculate:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);t.delivery=JSON.stringify(t.delivery),"store"==this.orderCreateData.delivery.delivery_type?t.member_address=JSON.stringify(this.member_address):t.member_address=JSON.stringify(t.member_address),this.$api.sendRequest({url:"/pointexchange/api/ordercreate/calculate",data:t,success:function(t){t.code>=0?(e.orderPaymentData.member_address=t.data.member_address,e.orderPaymentData.delivery_money=t.data.delivery_money,e.orderPaymentData.order_money=t.data.order_money,Object.assign(e.orderPaymentData.delivery,t.data.delivery),t.data.local_config&&(e.orderPaymentData.local_config=t.data.config.local),t.data.delivery.delivery_store_info&&(e.orderPaymentData.delivery_store_info=JSON.parse(t.data.delivery.delivery_store_info),e.judge&&("store"==e.orderPaymentData.delivery.delivery_type?e.storetime("no"):"local"==e.orderPaymentData.delivery.delivery_type&&e.localtime("no"),e.judge=!1)),e.createBtn(),e.$forceUpdate()):e.$util.showToast({title:t.message})}})},createBtn:function(){return!(this.orderPaymentData.delivery&&"local"==this.orderPaymentData.delivery.delivery_type&&this.orderPaymentData.delivery&&this.orderPaymentData.delivery.error&&this.orderPaymentData.delivery.start_money>this.orderPaymentData.price)&&!(this.orderPaymentData.delivery&&"local"==this.orderPaymentData.delivery.delivery_type&&this.orderPaymentData.delivery&&this.orderPaymentData.delivery.error&&""!==this.orderPaymentData.delivery.error)},orderCreate:function(){var t=this;if(this.verify()){if(this.isSub)return;this.isSub=!0,e.setStorageSync("paySource","pointexchange");var i=this.$util.deepClone(this.orderCreateData);i.delivery=JSON.stringify(i.delivery),"store"==this.orderCreateData.delivery.delivery_type?i.member_address=JSON.stringify(this.member_address):i.member_address=JSON.stringify(i.member_address),this.$api.sendRequest({url:"/pointexchange/api/ordercreate/create",data:i,success:function(i){if(e.hideLoading(),i.code>=0)if(1==t.orderPaymentData.exchange_info.type&&"0.00"!=t.orderPaymentData.order_money){var o=e.getStorageSync("exchangeOrderCreateData");o.out_trade_no=i.data,e.setStorageSync("exchangeOrderCreateData",o),t.$refs.choosePaymentPopup.getPayInfo(i.data),t.isSub=!1}else t.$util.redirectTo("/pages_promotion/point/result",{},"redirectTo");else t.isSub=!1,10==i.data.error_code||12==i.data.error_code?e.showModal({title:"订单未创建",content:i.message,confirmText:"去设置",success:function(e){e.confirm&&t.selectAddress()}}):t.$util.showToast({title:i.message})},fail:function(i){e.hideLoading(),t.isSub=!1}})}},verify:function(){if(1==this.orderPaymentData.exchange_info.type){if(1==this.orderPaymentData.is_virtual){if(!this.orderCreateData.member_address.mobile.length)return this.$util.showToast({title:"请输入您的手机号码"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号码"}),!1}if(0==this.orderPaymentData.is_virtual){if(!this.orderCreateData.delivery||!this.orderCreateData.delivery.delivery_type)return this.$util.showToast({title:"商家未设置配送方式"}),!1;if("store"!=this.orderCreateData.delivery.delivery_type&&!this.orderCreateData.member_address)return this.$util.showToast({title:"请先选择您的收货地址"}),!1;if("store"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可提货的门店,请选择其他配送方式"}),!1;if(!this.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.member_address.mobile))return this.$util.showToast({title:"请输入正确的预留手机"}),!1;if(!this.orderCreateData.delivery.buyer_ask_delivery_time.start_date||!this.orderCreateData.delivery.buyer_ask_delivery_time.end_date)return this.$util.showToast({title:"请选择自提时间"}),!1}if("local"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可配送的门店,请选择其他配送方式"}),!1;if(this.orderPaymentData.config.local.is_use&&this.orderPaymentData.delivery.local.info&&1==this.orderPaymentData.delivery.local.info.time_is_open&&(!this.orderCreateData.delivery.buyer_ask_delivery_time.start_date||!this.orderCreateData.delivery.buyer_ask_delivery_time.end_date))return this.$util.showToast({title:"请选择配送时间"}),!1}}}return!0},openSiteDelivery:function(){this.tempData={delivery:this.$util.deepClone(this.orderPaymentData.delivery)},this.$refs.deliveryPopup.open()},selectDeliveryType:function(t){e.setStorageSync("delivery",{delivery_type:t.name,delivery_type_name:t.title}),this.orderCreateData.delivery.delivery_type=t.name,this.orderCreateData.delivery.delivery_type_name=t.title,"store"==t.name&&(this.storeSelected(t),this.member_address.name=this.orderPaymentData.member_account.nickname,this.member_address.mobile||(this.member_address.mobile=""!=this.orderPaymentData.member_account.mobile?this.orderPaymentData.member_account.mobile:"")),"local"==t.name&&this.storeSelected(t),this.judge=!0,this.orderCalculate(),this.$forceUpdate()},storeSelected:function(e){this.storeInfo.storeList=e.store_list;var t=e.store_list[0]?e.store_list[0]:null;this.selectPickupPoint(t)},selectPickupPoint:function(t){if(t){this.orderCreateData.delivery.store_id=t.store_id,this.storeInfo.currStore=t;var i=e.getStorageSync("delivery")||{name:this.orderCreateData.delivery.delivery_type,title:this.orderCreateData.delivery.delivery_type_name};i.store_id=t.store_id,e.setStorageSync("delivery",i)}else this.orderCreateData.delivery.store_id=0,this.storeInfo.currStore={};this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.orderCreateData.buyer_ask_delivery_title="",this.orderCalculate(),this.$forceUpdate(),this.$refs["deliveryPopup"].close()},imageError:function(){var e="";e=1==this.orderPaymentData.exchange_info.type?this.$util.img(this.$util.getDefaultImage().goods):2==this.orderPaymentData.exchange_info.type?this.$util.img("public/uniapp/point/coupon.png"):3==this.orderPaymentData.exchange_info.type?this.$util.img("public/uniapp/point/hongbao.png"):this.$util.getDefaultImage().goods,this.orderPaymentData.exchange_info.image=e,this.$forceUpdate()},getTime:function(){var e=(new Date).getDay();this.timeInfo.week=["0","1","2","3","4","5","6"][e]},navigateTo:function(e){this.$util.redirectTo("/pages/goods/detail",{sku_id:e})},openChoosePayment:function(){this.verify()&&1==this.orderPaymentData.exchange_info.type&&"0.00"!=this.orderPaymentData.order_money?this.$refs.choosePaymentPopup.open():this.orderCreate()},localtime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=this.$util.deepClone(this.orderPaymentData.delivery.local.info);t.delivery_time&&(t.end_time=t.delivery_time[t.delivery_time.length-1].end_time);var i={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.TimePopup.open(i,e)},storetime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.orderPaymentData.delivery.delivery_store_info){var t=this.$util.deepClone(this.storeInfo.currStore);t.delivery_time="string"==typeof t.delivery_time&&t.delivery_time?JSON.parse(t.delivery_time):t.delivery_time,t.delivery_time&&(void 0!=t.delivery_time.length||t.delivery_time.length)||(t.delivery_time=[{start_time:t.start_time,end_time:t.end_time}]);var i={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.TimePopup.open(i,e),this.$forceUpdate()}},selectTime:function(e){e.data&&e.data.month&&(this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:e.data.start_date,end_date:e.data.end_date},"今天"==e.data.title||"明天"==e.data.title?this.orderCreateData.buyer_ask_delivery_title=e.data.title+"("+e.data.time+")":this.orderCreateData.buyer_ask_delivery_title=e.data.month+"("+e.data.time+")",this.orderCalculate(),this.$forceUpdate())},back:function(){e.navigateBack({delta:1})}},onShow:function(){e.getStorageSync("addressBack")&&e.removeStorageSync("addressBack"),this.storeToken?this.getOrderPaymentData():this.$util.redirectTo("/pages_tool/login/index"),this.judge=!0,this.getTime(),this.isIphoneX=this.$util.uniappIsIPhoneX()},onHide:function(){this.$refs.loadingCover&&this.$refs.loadingCover.show()},onLoad:function(){this.location||this.$util.getLocation(),this.menuButtonBounding=e.getMenuButtonBoundingClientRect()},watch:{location:function(e){e&&this.getOrderPaymentData()}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)}}};t.default=i}).call(this,i("df3c")["default"])}}]);