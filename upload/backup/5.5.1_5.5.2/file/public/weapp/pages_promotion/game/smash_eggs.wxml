<page-meta page-style="{{themeColor}}" class="data-v-2a82fadc"></page-meta><scroll-view class="container data-v-2a82fadc" scroll-y="true"><view class="data-v-2a82fadc"><view class="head-wrap data-v-2a82fadc"><image src="{{$root.g0}}" mode="widthFix" class="data-v-2a82fadc"></image><view data-event-opts="{{[['tap',[['openRulePopup',['$event']]]]]}}" class="rule-mark data-v-2a82fadc" bindtap="__e">规则</view></view><view class="status-wrap data-v-2a82fadc"><block wx:if="{{gameInfo.status==1}}"><block class="data-v-2a82fadc"><block wx:if="{{storeToken}}"><text class="mark data-v-2a82fadc">您还有<text class="num data-v-2a82fadc">{{gameInfo.surplus_num}}</text>次机会</text></block><block wx:else><text class="mark data-v-2a82fadc">请先进行登录</text></block></block></block><block wx:else><block wx:if="{{gameInfo.status==0}}"><block class="data-v-2a82fadc"><text class="mark data-v-2a82fadc">活动尚未开始</text></block></block><block wx:else><block class="data-v-2a82fadc"><text class="mark data-v-2a82fadc">活动已经结束</text></block></block></block></view><view class="eggs-wrap data-v-2a82fadc"><view data-event-opts="{{[['tap',[['lottery',['$event']]]]]}}" class="box data-v-2a82fadc" bindtap="__e"><image src="{{$root.g1}}" mode="widthFix" class="data-v-2a82fadc"></image></view><view data-event-opts="{{[['tap',[['lottery',['$event']]]]]}}" class="box data-v-2a82fadc" bindtap="__e"><image src="{{$root.g2}}" mode="widthFix" class="data-v-2a82fadc"></image></view><view data-event-opts="{{[['tap',[['lottery',['$event']]]]]}}" class="box data-v-2a82fadc" bindtap="__e"><image src="{{$root.g3}}" mode="widthFix" class="data-v-2a82fadc"></image></view></view><view class="action-text data-v-2a82fadc"><view class="point data-v-2a82fadc">{{"我的积分："+point}}</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="record data-v-2a82fadc" bindtap="__e">我的中奖记录</view></view><block wx:if="{{$root.g4}}"><view class="record-wrap data-v-2a82fadc"><view class="body-shade data-v-2a82fadc"></view><view class="head data-v-2a82fadc">中奖名单</view><view class="body data-v-2a82fadc"><view class="{{['wrap','data-v-2a82fadc',(animate)?'animate':'']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="data-v-2a82fadc"><view class="tit data-v-2a82fadc">{{item.f0}}</view><view class="txt data-v-2a82fadc"><l-time vue-id="{{'2bfd0d1d-1-'+index}}" text="{{item.$orig.create_time*1000}}" class="data-v-2a82fadc" bind:__l="__l"></l-time>{{'获得 '+item.$orig.award_name+''}}</view></view></block></view></view></view></block><uni-popup vue-id="2bfd0d1d-2" type="center" maskClick="{{false}}" data-ref="transitionPopup" class="data-v-2a82fadc vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="transition-popup data-v-2a82fadc"><view class="wrap data-v-2a82fadc"><block wx:if="{{eggStatus==0}}"><image class="eggs data-v-2a82fadc" src="{{$root.g5}}" mode="widthFix"></image></block><block wx:if="{{eggStatus==1}}"><image class="eggs data-v-2a82fadc" src="{{$root.g6}}" mode="widthFix"></image></block><block wx:if="{{eggStatus==2}}"><image class="eggs data-v-2a82fadc" src="{{$root.g7}}" mode="widthFix"></image></block><image class="hammer data-v-2a82fadc" src="{{$root.g8}}" mode="widthFix"></image></view></view></uni-popup><uni-popup vue-id="2bfd0d1d-3" type="center" maskClick="{{false}}" data-ref="resultPopup" class="data-v-2a82fadc vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="result-wrap data-v-2a82fadc"><block wx:if="{{result.is_winning}}"><block class="data-v-2a82fadc"><view class="content-wrap yes data-v-2a82fadc"><block wx:if="{{result.award_type==1}}"><image class="look data-v-2a82fadc" src="{{$root.g9}}" mode="widthFix"></image></block><block wx:if="{{result.award_type==2}}"><image class="look data-v-2a82fadc" src="{{$root.g10}}" mode="widthFix"></image></block><block wx:if="{{result.award_type==3}}"><image class="look data-v-2a82fadc" src="{{$root.g11}}" mode="widthFix"></image></block><block wx:if="{{result.award_type==4}}"><image class="look data-v-2a82fadc" src="{{$root.g12}}" mode="widthFix"></image></block><block wx:if="{{result.award_type==1}}"><view class="msg data-v-2a82fadc">{{"恭喜您抽中"+result.point+"个积分"}}</view></block><block wx:if="{{result.award_type==2}}"><view class="msg data-v-2a82fadc">{{"恭喜您抽中"+result.balance+"元红包"}}</view></block><block wx:if="{{result.award_type==3}}"><view class="msg data-v-2a82fadc">{{"恭喜您抽中优惠券“"+result.relate_name+"”"}}</view></block><view data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="btn data-v-2a82fadc" bindtap="__e">继续抽奖</view><text data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="iconfont icon-round-close data-v-2a82fadc" bindtap="__e"></text></view><image class="bg-img data-v-2a82fadc" src="{{$root.g13}}" mode="widthFix"></image></block></block><block wx:else><block class="data-v-2a82fadc"><view class="content-wrap data-v-2a82fadc"><image class="look data-v-2a82fadc" src="{{$root.g14}}" mode="widthFix"></image><view class="msg data-v-2a82fadc">{{gameInfo.no_winning_desc}}</view><view data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="btn data-v-2a82fadc" bindtap="__e">继续抽奖</view><text data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="iconfont icon-round-close data-v-2a82fadc" bindtap="__e"></text></view><image class="bg-img data-v-2a82fadc" src="{{$root.g15}}" mode="widthFix"></image></block></block></view></uni-popup><uni-popup vue-id="2bfd0d1d-4" type="center" maskClick="{{false}}" data-ref="rulePopup" class="data-v-2a82fadc vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="rule-wrap data-v-2a82fadc"><view class="content-wrap data-v-2a82fadc"><image class="rule-head data-v-2a82fadc" src="{{$root.g16}}" mode="widthFix"></image><scroll-view class="rule data-v-2a82fadc" scroll-y="true"><view class="data-v-2a82fadc"><view class="tit data-v-2a82fadc">活动时间</view><view class="text data-v-2a82fadc">{{$root.g17+" - "+$root.g18}}</view><view class="tit data-v-2a82fadc">参与规则</view><block wx:if="{{gameInfo.join_type==0}}"><view class="text data-v-2a82fadc">{{"每个用户活动期间共有"+gameInfo.join_frequency+"次抽奖机会。"}}</view></block><block wx:else><view class="text data-v-2a82fadc">{{"每个用户活动期间每天都有"+gameInfo.join_frequency+"次抽奖机会，每天0点更新。"}}</view></block><view class="text data-v-2a82fadc">{{"每次抽奖需消耗 "+gameInfo.points+" 积分"}}</view><block wx:if="{{gameInfo.level_id!=0}}"><view class="text data-v-2a82fadc">{{"该活动只有"+gameInfo.level_name+"等级的会员可参与。"}}</view></block><block wx:if="{{gameInfo.remark!=''}}"><block class="data-v-2a82fadc"><view class="tit data-v-2a82fadc">活动说明</view><view class="text data-v-2a82fadc">{{gameInfo.remark}}</view></block></block></view></scroll-view><text data-event-opts="{{[['tap',[['closeRulePopup',['$event']]]]]}}" class="iconfont icon-round-close data-v-2a82fadc" bindtap="__e"></text></view></view></uni-popup><loading-cover vue-id="2bfd0d1d-5" data-ref="loadingCover" class="data-v-2a82fadc vue-ref" bind:__l="__l"></loading-cover><ns-login vue-id="2bfd0d1d-6" data-ref="login" class="data-v-2a82fadc vue-ref" bind:__l="__l"></ns-login><hover-nav vue-id="2bfd0d1d-7" class="data-v-2a82fadc" bind:__l="__l"></hover-nav><privacy-popup vue-id="2bfd0d1d-8" data-ref="privacyPopup" class="data-v-2a82fadc vue-ref" bind:__l="__l"></privacy-popup></view></scroll-view>