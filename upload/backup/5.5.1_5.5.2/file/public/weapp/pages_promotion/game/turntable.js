require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/game/turntable"],{5262:function(e,t,n){"use strict";var i=n("873c"),r=n.n(i);r.a},8157:function(e,t,n){"use strict";n.r(t);var i=n("8c19"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=r.a},"873c":function(e,t,n){},"8c19":function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={components:{uniPopup:function(){n.e("components/uni-popup/uni-popup").then(function(){return resolve(n("d745"))}.bind(null,n)).catch(n.oe)},LTime:function(){Promise.all([n.e("pages_promotion/common/vendor"),n.e("pages_promotion/components/l-time/l-time")]).then(function(){return resolve(n("7477"))}.bind(null,n)).catch(n.oe)}},data:function(){return{isClick:!1,currentIndex:-1,maxRing:6,currentRing:1,speed:300,timer:null,id:0,gameInfo:{award:[{award_img:"",award_name:""}],surplus_num:""},award:[],resultIndex:0,result:{is_winning:1},point:0,animate:!1,scrollTimer:null,shareImg:""}},onLoad:function(t){var n=this;if(setTimeout((function(){n.addonIsExist.turntable||(n.$util.showToast({title:"商家未开启幸运抽奖",mask:!0,duration:2e3}),setTimeout((function(){n.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.id&&(this.id=t.id),t.source_member&&e.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("id")&&(n.id=t.split("-")[1]),-1!=t.indexOf("sku_id")&&(n.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&e.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&e.setStorageSync("is_test",1)}))}},onShow:function(){this.storeToken&&(e.getStorageSync("source_member")&&this.$util.onSourceMember(e.getStorageSync("source_member")),this.getMemberPointInfo()),this.getGameInfo()},onShareAppMessage:function(e){var t=this.gameInfo.game_name,n=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),i=n.path;return{title:t,path:i,imageUrl:"",success:function(e){},fail:function(e){}}},onShareTimeline:function(){var e=this.gameInfo.game_name,t=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),n=t.query;return{title:e,query:n,imageUrl:""}},watch:{storeToken:function(e,t){e&&(this.getMemberPointInfo(),this.getGameInfo())}},methods:{getMemberPointInfo:function(){var e=this;this.$api.sendRequest({url:"/api/memberaccount/info",data:{account_type:"point"},success:function(t){0==t.code&&t.data&&(e.point=parseInt(t.data.point))}})},lottery:function(){var e=this;if(this.storeToken){if(1==this.gameInfo.status)if(this.gameInfo.surplus_num<=0)this.$util.showToast({title:"您的抽奖次数已用完"});else{var t=","+this.gameInfo.level_id+",";"0"==this.gameInfo.level_id||-1!=t.indexOf(","+this.memberInfo.member_level+",")?this.gameInfo.points>0&&this.point<this.gameInfo.points?this.$util.showToast({title:"积分不足"}):this.isClick||(this.isClick=!0,this.currentIndex=0,this.currentRing=1,this.speed=300,this.timer=setInterval(this.startRoll,this.speed),this.$api.sendRequest({url:"/turntable/api/turntable/lottery",data:{id:this.id},success:function(t){t.code>=0?(e.result=t.data,t.data.is_winning?e.resultIndex=e.$util.inArray(t.data.award_id,e.award):e.resultIndex=e.$util.inArray(-1,e.award),e.point-=e.gameInfo.points,e.gameInfo.surplus_num-=1):e.$util.showToast({title:t.message})},fail:function(t){e.resultIndex=e.$util.inArray(-1,e.award)}})):this.$util.showToast({title:"该活动只有"+this.gameInfo.level_name+"等级的会员可参与该活动"})}}else this.$refs.login.open("/pages_promotion/game/turntable?id="+this.id)},getGameInfo:function(){var e=this;this.$api.sendRequest({url:"/turntable/api/turntable/info",data:{id:this.id},success:function(t){t.code>=0&&t.data?(e.gameInfo=t.data,t.data.award.forEach((function(t){e.award.push(t.award_id)})),e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.gameInfo.is_show_winner&&e.gameInfo.draw_record.length>6&&(e.scrollTimer=setInterval(e.scrollRecord,2e3))):(e.$util.showToast({title:"未获取到活动信息！"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1500))},fail:function(t){e.$util.showToast({title:"未获取到活动信息！"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1500)}})},startRoll:function(){if(this.speed>50&&(this.speed-=50),this.currentRing>=this.maxRing-1&&this.speed<700&&(this.speed+=100),this.currentRing==this.maxRing&&this.currentIndex==this.resultIndex)return clearInterval(this.timer),void this.showResult();7==this.currentIndex?(this.currentRing+=1,this.currentIndex=0):this.currentIndex+=1,clearInterval(this.timer),this.timer=setTimeout(this.startRoll,this.speed)},showResult:function(){this.$refs.resultPopup.open()},closePopup:function(){this.isClick=!1,this.currentIndex=-1,this.$refs.resultPopup.close()},openRulePopup:function(){this.$refs.rulePopup.open()},closeRulePopup:function(){this.$refs.rulePopup.close()},scrollRecord:function(){var e=this;this.animate=!0,setTimeout((function(){e.gameInfo.draw_record.push(e.gameInfo.draw_record[0]),e.gameInfo.draw_record.shift(),e.animate=!1}),1e3)}},filters:{cover:function(e){return"string"==typeof e&&e.length>0?e.substr(0,1)+"******"+e.substr(-1):""}},onHide:function(){clearInterval(this.scrollTimer)}};t.default=i}).call(this,n("df3c")["default"])},"981f":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uniPopup:function(){return n.e("components/uni-popup/uni-popup").then(n.bind(null,"d745"))},loadingCover:function(){return n.e("components/loading-cover/loading-cover").then(n.bind(null,"c003"))},nsLogin:function(){return Promise.all([n.e("common/vendor"),n.e("components/ns-login/ns-login")]).then(n.bind(null,"2910"))},hoverNav:function(){return n.e("components/hover-nav/hover-nav").then(n.bind(null,"c1f1"))}},r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.$util.img("public/uniapp/game/turntable_head.png")),i=e.$util.img("public/uniapp/game/turntable_bg.png"),r=e.__map(e.gameInfo.award,(function(t,n){var i=e.__get_orig(t),r=e.$util.img(t.award_img);return{$orig:i,g2:r}})),o=e.gameInfo.is_show_winner&&e.gameInfo.draw_record.length,s=o?e.__map(e.gameInfo.draw_record,(function(t,n){var i=e.__get_orig(t),r=e._f("cover")(t.member_nick_name);return{$orig:i,f0:r}})):null,a=e.result.is_winning?e.$util.img(e.gameInfo.award[e.resultIndex].award_img):null,u=e.result.is_winning?e.$util.img("public/uniapp/game/result_yes.png"):null,l=e.result.is_winning?null:e.$util.img("public/uniapp/game/result_look.png"),c=e.result.is_winning?null:e.$util.img("public/uniapp/game/result_no.png"),d=e.$util.img("public/uniapp/game/rule_head.png"),m=e.$util.timeStampTurnTime(e.gameInfo.start_time),f=e.$util.timeStampTurnTime(e.gameInfo.end_time);e._isMounted||(e.e0=function(t){return e.$util.redirectTo("/pages_promotion/game/record",{id:e.id})}),e.$mp.data=Object.assign({},{$root:{g0:n,g1:i,l0:r,g3:o,l1:s,g4:a,g5:u,g6:l,g7:c,g8:d,g9:m,g10:f}})},o=[]},a173:function(e,t,n){"use strict";n.r(t);var i=n("981f"),r=n("8157");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("5262"),n("a880");var s=n("828b"),a=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,"e2648c8a",null,!1,i["a"],void 0);t["default"]=a.exports},a880:function(e,t,n){"use strict";var i=n("ee6c"),r=n.n(i);r.a},c421:function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("d381");i(n("3240"));var r=i(n("a173"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},ee6c:function(e,t,n){}},[["c421","common/runtime","common/vendor"]]]);