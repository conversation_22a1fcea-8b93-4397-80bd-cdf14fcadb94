require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/game/record"],{"0239":function(e,t,n){},"3f74":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{list:[],id:0}},onLoad:function(e){e.id&&(this.id=e.id)},onShow:function(){this.list=[],this.$refs.mescroll&&this.$refs.mescroll.refresh()},methods:{getListData:function(e){var t=this;this.$api.sendRequest({url:"/api/game/recordPage",data:{id:this.id,page:e.num,page_size:e.size},success:function(n){var i=[];0==n.code&&n.data?i=n.data.list:t.$util.showToast({title:n.message}),e.endSuccess(i.length),1==e.num&&(t.list=[]),t.list=t.list.concat(i),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(n){e.endErr(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})}}}},"6d77":function(e,t,n){"use strict";n.r(t);var i=n("3f74"),o=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=o.a},"7ee3":function(e,t,n){"use strict";n.r(t);var i=n("f01b"),o=n("6d77");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("cd77");var a=n("828b"),u=Object(a["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=u.exports},"9e8b":function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("d381");i(n("3240"));var o=i(n("7ee3"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},cd77:function(e,t,n){"use strict";var i=n("0239"),o=n.n(i);o.a},f01b:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var i={nsEmpty:function(){return n.e("components/ns-empty/ns-empty").then(n.bind(null,"52a6"))},loadingCover:function(){return n.e("components/loading-cover/loading-cover").then(n.bind(null,"c003"))}},o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.list.length),i=n>0?e.__map(e.list,(function(t,n){var i=e.__get_orig(t),o=1==t.award_type?e.$util.img("public/uniapp/game/point.png"):null,r=2==t.award_type?e.$util.img("public/uniapp/game/balance.png"):null,a=3==t.award_type?e.$util.img("public/uniapp/game/coupon.png"):null,u=e.$util.timeStampTurnTime(t.create_time);return{$orig:i,g1:o,g2:r,g3:a,g4:u}})):null;e._isMounted||(e.e0=function(t){return e.$util.redirectTo("/pages_tool/member/point")},e.e1=function(t){return e.$util.redirectTo("/pages_tool/member/balance_detail")},e.e2=function(t){return e.$util.redirectTo("/pages_tool/member/coupon")}),e.$mp.data=Object.assign({},{$root:{g0:n,l0:i}})},r=[]}},[["9e8b","common/runtime","common/vendor"]]]);