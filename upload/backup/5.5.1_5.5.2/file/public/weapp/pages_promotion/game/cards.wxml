<page-meta page-style="{{themeColor}}" class="data-v-4bb1706f"></page-meta><view class="container data-v-4bb1706f"><view class="data-v-4bb1706f"><view class="head-wrap data-v-4bb1706f"><image src="{{$root.g0}}" mode="widthFix" class="data-v-4bb1706f"></image><view data-event-opts="{{[['tap',[['openRulePopup',['$event']]]]]}}" class="rule-mark data-v-4bb1706f" bindtap="__e">规则</view></view><view class="prize-area data-v-4bb1706f"><view class="content-wrap data-v-4bb1706f"><view class="data-v-4bb1706f"><block wx:if="{{showGuide}}"><view class="guide-wrap data-v-4bb1706f"><block wx:if="{{gameInfo.status==1}}"><block class="data-v-4bb1706f"><view class="text data-v-4bb1706f">刮开试试手气</view><view data-event-opts="{{[['tap',[['lottery',['$event']]]]]}}" class="{{['btn','color-base-bg','data-v-4bb1706f',(gameInfo.surplus_num==0)?'disabled':'']}}" bindtap="__e">{{'点我刮奖'+('('+gameInfo.points+'积分)')+''}}</view></block></block><block wx:else><block wx:if="{{gameInfo.status==0}}"><view class="text data-v-4bb1706f">活动尚未开始</view></block><block wx:else><view class="text data-v-4bb1706f">活动已经结束</view></block></block></view></block><block wx:if="{{showGuide}}"><view class="canvas-shade data-v-4bb1706f"></view></block><canvas hidden="{{!(!showGuide&&!popState)}}" class="canvas data-v-4bb1706f" disable-scroll="{{true}}" canvas-id="cardsCanvas" id="canvas" data-event-opts="{{[['touchmove',[['touchMove',['$event']]]],['touchstart',[['targbtn',['$event']]]],['touchend',[['touchend',['$event']]]]]}}" bindtouchmove="__e" bindtouchstart="__e" bindtouchend="__e"></canvas><view data-event-opts="{{[['touchmove',[['contentMove',['$event']]]]]}}" class="result-wrap data-v-4bb1706f" bindtouchmove="__e"><block wx:if="{{result.is_winning}}"><block class="data-v-4bb1706f"><view class="title data-v-4bb1706f">恭喜您中奖了</view><view class="text color-base-text data-v-4bb1706f">{{result.award_name}}</view><button class="warn data-v-4bb1706f" type="warn" data-event-opts="{{[['tap',[['again',['$event']]]]]}}" bindtap="__e">再刮一次</button><view class="tips data-v-4bb1706f">可到“我的中奖记录”中查看</view></block></block><block wx:else><block class="data-v-4bb1706f"><view class="text color-base-text data-v-4bb1706f">{{gameInfo.no_winning_desc}}</view><button class="warn data-v-4bb1706f" type="warn" id="btn" data-event-opts="{{[['tap',[['again',['$event']]]]]}}" bindtap="__e">再刮一次</button></block></block></view></view></view><image src="{{$root.g1}}" mode="widthFix" class="data-v-4bb1706f"></image></view><view class="action-text data-v-4bb1706f"><view class="point data-v-4bb1706f">{{"我的积分："+point}}</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="record data-v-4bb1706f" bindtap="__e">我的中奖记录</view></view><block wx:if="{{$root.g2}}"><view class="record-wrap data-v-4bb1706f"><view class="body-shade data-v-4bb1706f"></view><view class="head data-v-4bb1706f">中奖名单</view><view class="body data-v-4bb1706f"><view class="{{['wrap','data-v-4bb1706f',(animate)?'animate':'']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="data-v-4bb1706f"><view class="tit data-v-4bb1706f">{{item.f0}}</view><view class="txt data-v-4bb1706f"><l-time vue-id="{{'7021bd4b-1-'+index}}" text="{{item.$orig.create_time*1000}}" class="data-v-4bb1706f" bind:__l="__l"></l-time>{{'获得 '+item.$orig.award_name+''}}</view></view></block></view></view></view></block></view><uni-popup vue-id="7021bd4b-2" type="center" maskClick="{{false}}" data-ref="rulePopup" data-event-opts="{{[['^change',[['popChange']]]]}}" bind:change="__e" class="data-v-4bb1706f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="rule-wrap data-v-4bb1706f"><view class="content-wrap data-v-4bb1706f"><image class="rule-head data-v-4bb1706f" src="{{$root.g3}}" mode="widthFix"></image><scroll-view class="rule data-v-4bb1706f" scroll-y="true"><view class="data-v-4bb1706f"><view class="tit data-v-4bb1706f">活动时间</view><view class="text data-v-4bb1706f">{{$root.g4+" - "+$root.g5}}</view><view class="tit data-v-4bb1706f">参与规则</view><block wx:if="{{gameInfo.join_type==0}}"><view class="text data-v-4bb1706f">{{"每个用户活动期间共有"+gameInfo.join_frequency+"次抽奖机会。"}}</view></block><block wx:else><view class="text data-v-4bb1706f">{{"每个用户活动期间每天都有"+gameInfo.join_frequency+"次抽奖机会，每天0点更新。"}}</view></block><view class="text data-v-4bb1706f">{{"每次抽奖需消耗 "+gameInfo.points+" 积分"}}</view><block wx:if="{{gameInfo.level_id!=0}}"><view class="text data-v-4bb1706f">{{"该活动只有"+gameInfo.level_name+'等级的会员可参与。'}}</view></block><block wx:if="{{gameInfo.remark!=''}}"><block class="data-v-4bb1706f"><view class="tit data-v-4bb1706f">活动说明</view><view class="text data-v-4bb1706f">{{gameInfo.remark}}</view></block></block></view></scroll-view><text data-event-opts="{{[['tap',[['closeRulePopup',['$event']]]]]}}" class="iconfont icon-round-close data-v-4bb1706f" bindtap="__e"></text></view></view></uni-popup><loading-cover vue-id="7021bd4b-3" data-ref="loadingCover" class="data-v-4bb1706f vue-ref" bind:__l="__l"></loading-cover><ns-login vue-id="7021bd4b-4" data-ref="login" class="data-v-4bb1706f vue-ref" bind:__l="__l"></ns-login><hover-nav vue-id="7021bd4b-5" class="data-v-4bb1706f" bind:__l="__l"></hover-nav><privacy-popup vue-id="7021bd4b-6" data-ref="privacyPopup" class="data-v-4bb1706f vue-ref" bind:__l="__l"></privacy-popup></view>