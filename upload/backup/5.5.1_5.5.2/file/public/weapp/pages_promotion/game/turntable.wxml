<page-meta page-style="{{themeColor}}" class="data-v-e2648c8a"></page-meta><scroll-view class="container data-v-e2648c8a" scroll-y="true"><view class="data-v-e2648c8a"><view class="head-wrap data-v-e2648c8a"><image src="{{$root.g0}}" mode="widthFix" class="data-v-e2648c8a"></image><view data-event-opts="{{[['tap',[['openRulePopup',['$event']]]]]}}" class="rule-mark data-v-e2648c8a" bindtap="__e">规则</view></view><view class="turntable-wrap data-v-e2648c8a" style="{{'background-image:'+('url('+$root.g1+')')+';'}}"><view class="wrap data-v-e2648c8a"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="award-wrap data-v-e2648c8a"><view class="{{['box','data-v-e2648c8a',(index==currentIndex)?'on':'']}}"><view class="award-img data-v-e2648c8a"><image src="{{item.g2}}" mode="widthFix" class="data-v-e2648c8a"></image></view><view class="award-text data-v-e2648c8a">{{item.$orig.award_name}}</view></view></view></block><block wx:if="{{gameInfo.status==1}}"><block class="data-v-e2648c8a"><block wx:if="{{gameInfo.surplus_num>0||!storeToken}}"><block class="data-v-e2648c8a"><view data-event-opts="{{[['tap',[['lottery',['$event']]]]]}}" class="star-box data-v-e2648c8a" bindtap="__e"><view class="text data-v-e2648c8a">立即抽奖</view><view class="tips data-v-e2648c8a">{{"剩余"+gameInfo.surplus_num+"次"}}</view></view></block></block><block wx:else><block class="data-v-e2648c8a"><view class="star-box disabled data-v-e2648c8a"><view class="text data-v-e2648c8a">立即抽奖</view><view class="tips data-v-e2648c8a">{{"剩余"+gameInfo.surplus_num+"次"}}</view></view></block></block></block></block><block wx:else><block wx:if="{{gameInfo.status==0}}"><block class="data-v-e2648c8a"><view class="status-box data-v-e2648c8a"><view class="data-v-e2648c8a">活动尚未开始</view></view></block></block><block wx:else><block class="data-v-e2648c8a"><view class="status-box data-v-e2648c8a"><view class="data-v-e2648c8a">活动已经结束</view></view></block></block></block></view></view><view class="action-text data-v-e2648c8a"><view class="point data-v-e2648c8a">{{"我的积分："+point}}</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="record data-v-e2648c8a" bindtap="__e">我的中奖记录</view></view><block wx:if="{{$root.g3}}"><view class="record-wrap data-v-e2648c8a"><view class="body-shade data-v-e2648c8a"></view><view class="head data-v-e2648c8a">中奖名单</view><view class="body data-v-e2648c8a"><view class="{{['wrap','data-v-e2648c8a',(animate)?'animate':'']}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="data-v-e2648c8a"><view class="tit data-v-e2648c8a">{{item.f0}}</view><view class="txt data-v-e2648c8a"><l-time vue-id="{{'7a5ce806-1-'+index}}" text="{{item.$orig.create_time*1000}}" class="data-v-e2648c8a" bind:__l="__l"></l-time>{{'获得 '+item.$orig.award_name+''}}</view></view></block></view></view></view></block></view><uni-popup vue-id="7a5ce806-2" type="center" maskClick="{{false}}" data-ref="resultPopup" class="data-v-e2648c8a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="result-wrap data-v-e2648c8a"><block wx:if="{{result.is_winning}}"><block class="data-v-e2648c8a"><view class="content-wrap yes data-v-e2648c8a"><image class="look data-v-e2648c8a" src="{{$root.g4}}" mode="widthFix"></image><view class="msg data-v-e2648c8a">{{"恭喜您抽中"+gameInfo.award[resultIndex].award_name}}</view><view data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="btn data-v-e2648c8a" bindtap="__e">继续抽奖</view><text data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="iconfont icon-round-close data-v-e2648c8a" bindtap="__e"></text></view><image class="bg-img data-v-e2648c8a" src="{{$root.g5}}" mode="widthFix"></image></block></block><block wx:else><block class="data-v-e2648c8a"><view class="content-wrap data-v-e2648c8a"><image class="look data-v-e2648c8a" src="{{$root.g6}}" mode="widthFix"></image><view class="msg data-v-e2648c8a">{{gameInfo.no_winning_desc}}</view><view data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="btn data-v-e2648c8a" bindtap="__e">继续抽奖</view><text data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="iconfont icon-round-close data-v-e2648c8a" bindtap="__e"></text></view><image class="bg-img data-v-e2648c8a" src="{{$root.g7}}" mode="widthFix"></image></block></block></view></uni-popup><uni-popup vue-id="7a5ce806-3" type="center" maskClick="{{false}}" data-ref="rulePopup" class="data-v-e2648c8a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="rule-wrap data-v-e2648c8a"><view class="content-wrap data-v-e2648c8a"><image class="rule-head data-v-e2648c8a" src="{{$root.g8}}" mode="widthFix"></image><scroll-view class="rule data-v-e2648c8a" scroll-y="true"><view class="data-v-e2648c8a"><view class="tit data-v-e2648c8a">活动时间</view><view class="text data-v-e2648c8a">{{$root.g9+" - "+$root.g10}}</view><view class="tit data-v-e2648c8a">参与规则</view><block wx:if="{{gameInfo.join_type==0}}"><view class="text data-v-e2648c8a">{{"每个用户活动期间共有"+gameInfo.join_frequency+"次抽奖机会。"}}</view></block><block wx:else><view class="text data-v-e2648c8a">{{"每个用户活动期间每天都有"+gameInfo.join_frequency+"次抽奖机会，每天0点更新。"}}</view></block><view class="text data-v-e2648c8a">{{"每次抽奖需消耗 "+gameInfo.points+" 积分"}}</view><block wx:if="{{gameInfo.level_id!=0}}"><view class="text data-v-e2648c8a">{{"该活动只有"+gameInfo.level_name+"等级的会员可参与。"}}</view></block><block wx:if="{{gameInfo.remark!=''}}"><block class="data-v-e2648c8a"><view class="tit data-v-e2648c8a">活动说明</view><view class="text data-v-e2648c8a">{{gameInfo.remark}}</view></block></block></view></scroll-view><text data-event-opts="{{[['tap',[['closeRulePopup',['$event']]]]]}}" class="iconfont icon-round-close data-v-e2648c8a" bindtap="__e"></text></view></view></uni-popup><loading-cover vue-id="7a5ce806-4" data-ref="loadingCover" class="data-v-e2648c8a vue-ref" bind:__l="__l"></loading-cover><ns-login vue-id="7a5ce806-5" data-ref="login" class="data-v-e2648c8a vue-ref" bind:__l="__l"></ns-login><hover-nav vue-id="7a5ce806-6" class="data-v-e2648c8a" bind:__l="__l"></hover-nav><privacy-popup vue-id="7a5ce806-7" data-ref="privacyPopup" class="data-v-e2648c8a vue-ref" bind:__l="__l"></privacy-popup></scroll-view>