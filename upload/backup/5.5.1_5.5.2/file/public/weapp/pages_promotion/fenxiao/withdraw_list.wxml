<page-meta page-style="{{themeColor}}"></page-meta><view><mescroll-uni class="member-point" bind:getData="__e" vue-id="2910ee59-1" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g0}}"><view class="detailed-wrap"><view class="cont"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['withdrawList','',index,'id']]]]]]]}}" class="detailed-item" bindtap="__e"><view class="info"><view class="event">{{item.$orig.transfer_type=='balance'&&'余额'||item.$orig.transfer_type=='alipay'&&'支付宝'||item.$orig.transfer_type=='bank'&&'银行卡'||item.$orig.transfer_type=='wechatpay'&&'微信'}}</view><view><text class="time">{{item.g1}}</text></view></view><view class="right-wrap"><view class="num color-base-text">{{"￥"+item.$orig.money}}</view><block wx:if="{{isWithdrawWechat&&item.$orig.transfer_type=='wechatpay'&&item.$orig.status==2}}"><view data-event-opts="{{[['tap',[['toTransfer',['$0'],[[['withdrawList','',index,'id']]]]]]]}}" class="actions" catchtap="__e"><view class="act-btn">收款</view></view></block><block wx:else><view class="status-name" style="{{(item.$orig.status==-1||item.$orig.status==-2?'color:red;':'')}}">{{item.$orig.status_name}}</view></block></view><block wx:if="{{item.$orig.status==-1}}"><view class="fail-reason">{{'拒绝原因：'+item.$orig.refuse_reason+''}}</view></block><block wx:if="{{item.$orig.status==-2}}"><view class="fail-reason">{{'失败原因：'+item.$orig.fail_reason+''}}</view></block></view></block></view></view></block><block wx:else><ns-empty vue-id="{{('2910ee59-2')+','+('2910ee59-1')}}" isIndex="{{false}}" text="暂无提现记录" bind:__l="__l"></ns-empty></block></view></mescroll-uni><ns-login class="vue-ref" vue-id="2910ee59-3" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="2910ee59-4" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>