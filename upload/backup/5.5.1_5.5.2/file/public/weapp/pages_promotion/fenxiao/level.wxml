<page-meta page-style="{{themeColor}}" class="data-v-692beff6"></page-meta><view class="container data-v-692beff6"><view class="level-top data-v-692beff6"><image src="{{$root.g0}}" class="data-v-692beff6"></image></view><swiper class="level-swiper data-v-692beff6" autoplay="{{false}}" duration="{{500}}" previous-margin="50rpx" next-margin="50rpx" current="{{curr}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{levelList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="data-v-692beff6"><view class="{{['level-item','data-v-692beff6',(index==curr)?'curr':'']}}"><view class="level-wrap data-v-692beff6"><view class="member-info data-v-692beff6"><view class="head-img data-v-692beff6"><image src="{{fenxiaoInfo.headimg?$root.g1:$root.g2.head}}" mode="aspectFill" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e" class="data-v-692beff6"></image></view><view class="nickname data-v-692beff6">{{fenxiaoInfo.nickname}}</view><view class="level-name data-v-692beff6">{{item.level_name}}</view></view><view class="level-rate data-v-692beff6"><block wx:if="{{config.level>0}}"><view class="rate-item data-v-692beff6"><view class="title data-v-692beff6">一级分佣比率</view><view class="rate data-v-692beff6">{{item.one_rate}}<text class="percentage data-v-692beff6">%</text></view></view></block><block wx:if="{{config.level>1}}"><view class="rate-item data-v-692beff6"><view class="title data-v-692beff6">二级分佣比率</view><view class="rate data-v-692beff6">{{item.two_rate}}<text class="percentage data-v-692beff6">%</text></view></view></block><block wx:if="{{config.level>2}}"><view class="rate-item data-v-692beff6"><view class="title data-v-692beff6">三级分佣比率</view><view class="rate data-v-692beff6">{{item.three_rate}}<text class="percentage data-v-692beff6">%</text></view></view></block></view><block wx:if="{{item.level_num>fenxiaoInfo.level_num}}"><view class="not-unlocked data-v-692beff6"><text class="iconfont icon-suoding data-v-692beff6"></text></view></block></view></view></swiper-item></block></swiper><block wx:if="{{levelInfo}}"><view class="level-condition data-v-692beff6"><view class="condition-title data-v-692beff6"><view class="title data-v-692beff6">快速升级技巧</view><view class="rate price-font data-v-692beff6"><text class="complete data-v-692beff6">{{levelInfo.complete>levelInfo.task_num?levelInfo.task_num:levelInfo.complete}}</text><text class="num data-v-692beff6">{{"/"+levelInfo.task_num}}</text></view></view><view class="task data-v-692beff6"><block wx:for="{{levelInfo.task}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="task-item data-v-692beff6"><view class="flex-box data-v-692beff6"><view class="title data-v-692beff6">{{''+item.title+''}}<text data-event-opts="{{[['tap',[['openTips',['$0'],[[['levelInfo.task','',index]]]]]]]}}" class="iconfont icon-wenxiao data-v-692beff6" bindtap="__e"></text></view><view class="{{['status','data-v-692beff6',(item.progress==100)?'complete':'']}}">{{''+(item.progress==100?'已完成':'未完成')}}</view></view><view class="progress data-v-692beff6"><progress percent="{{item.progress}}" activeColor="#E7B667" stroke-width="4" class="data-v-692beff6"></progress></view><view class="flex-box data-v-692beff6"><view class="desc data-v-692beff6">{{item.desc}}</view><view class="rate price-font data-v-692beff6"><text class="complete data-v-692beff6">{{item.value}}</text><text class="num data-v-692beff6">{{"/"+item.condition}}</text></view></view></view></block></view></view></block><uni-popup vue-id="bbcf7fb0-1" type="bottom" data-ref="tips" class="data-v-692beff6 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup data-v-692beff6"><view class="popup-header data-v-692beff6"><text class="tit data-v-692beff6">提示</text><text data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="iconfont icon-close data-v-692beff6" bindtap="__e"></text></view><view class="popup-body data-v-692beff6"><view class="data-v-692beff6">{{tips}}</view><block wx:if="{{levelInfo}}"><view class="data-v-692beff6">{{levelInfo.upgrade_type==1?'满足任意一条件即可升级':'满足全部条件才能进行升级'}}</view></block></view></view></uni-popup><ns-goods-recommend vue-id="bbcf7fb0-2" route="fenxiao_level" class="data-v-692beff6" bind:__l="__l"></ns-goods-recommend><loading-cover vue-id="bbcf7fb0-3" data-ref="loadingCover" class="data-v-692beff6 vue-ref" bind:__l="__l"></loading-cover></view>