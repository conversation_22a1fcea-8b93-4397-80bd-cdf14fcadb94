<page-meta page-style="{{themeColor}}"></page-meta><view class="bill"><mescroll-uni class="member-point vue-ref" vue-id="1a735b6e-1" size="{{8}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['default','list']}}"><view slot="list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><block wx:if="{{$root.g0}}"><view class="balances"><block wx:if="{{item.$orig.type=='order'}}"><image src="{{item.g1}}" mode="widthFix"></image></block><block wx:else><image src="{{item.g2}}" mode="widthFix"></image></block><view class="balances-info"><text>{{item.$orig.type_name}}</text><text>{{"账单编号: "+item.$orig.account_no}}</text><text>{{item.g3}}</text></view><view class="balances-num"><text class="{{[item.$orig.money>0?'color-base-text':'']}}">{{item.$orig.money>0?'+'+item.$orig.money:item.$orig.money}}</text></view></view></block></block><block wx:if="{{$root.g4}}"><ns-empty vue-id="{{('1a735b6e-2')+','+('1a735b6e-1')}}" text="暂无账单信息" isIndex="{{false}}" bind:__l="__l"></ns-empty></block></view><loading-cover class="vue-ref" vue-id="{{('1a735b6e-3')+','+('1a735b6e-1')}}" data-ref="loadingCover" bind:__l="__l"></loading-cover></mescroll-uni></view>