<page-meta page-style="{{themeColor}}"></page-meta><view><block wx:if="{{storeToken&&levelNum>1}}"><view class="team-cate"><block wx:for="{{levelList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-event-opts="{{[['tap',[['selectLevel',['$0'],[[['levelList','',index,'level']]]]]]]}}" class="{{['cate-li',(currentLevel==item.level)?'active color-base-text color-base-border':'']}}" bindtap="__e">{{item.name}}</view></block></block></view></block><block wx:if="{{storeToken}}"><mescroll-uni class="member-point vue-ref" vue-id="7be1b9b3-1" top="{{levelNum>1?90:0}}" size="{{8}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.g0!=0}}"><view data-event-opts="{{[['tap',[['toFenxiaoOrder',['$0'],[[['teamList','',index]]]]]]]}}" class="team-li" bindtap="__e"><view class="{{['li-box',(index+1==item.g1)?'active':'']}}"><block wx:if="{{item.$orig.headimg}}"><image src="{{item.g2}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></block><block wx:else><image src="{{item.g3.head}}"></image></block><view class="member-info"><view class="member-name"><block wx:if="{{item.$orig.is_fenxiao}}"><block><view class="left"><view class="flex-box"><view class="name">{{item.$orig.nickname}}</view><view class="title color-base-border color-base-text">{{fenxiaoWords.fenxiao_name}}</view></view><view class="color-tip font-size-goods-tag">{{"加入时间："+item.g4}}</view></view><view class="consume-info"><view><text>{{item.$orig.one_child_num}}</text>人</view><view><text>{{item.$orig.order_num}}</text>单</view><view><text>{{item.f0}}</text>元</view></view></block></block><block wx:else><block><view class="left"><view class="flex-box"><view class="name font-size-tag"><text>{{item.$orig.nickname}}</text></view></view><view class="color-tip font-size-goods-tag">{{"加入时间："+item.g5}}</view></view><view class="consume-info"><view><text>0</text>人</view><view><text>{{item.$orig.order_num}}</text>单</view><view><text>{{item.f1}}</text>元</view></view></block></block></view></view></view></view></block></block><block wx:if="{{$root.g6}}"><block><ns-empty vue-id="{{('7be1b9b3-2')+','+('7be1b9b3-1')}}" text="暂无数据" isIndex="{{false}}" bind:__l="__l"></ns-empty></block></block></view></mescroll-uni></block><ns-login class="vue-ref" vue-id="7be1b9b3-3" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="7be1b9b3-4" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>