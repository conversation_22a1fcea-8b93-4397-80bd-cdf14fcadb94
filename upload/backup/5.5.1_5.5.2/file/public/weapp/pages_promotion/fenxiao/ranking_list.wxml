<page-meta page-style="{{themeColor}}"></page-meta><view class="container"><mescroll-uni class="vue-ref" vue-id="6af9e549-1" top="0" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g0!=0}}"><block><view class="banner" style="{{'background:'+('url('+$root.g1+') no-repeat top left / 100% 100%')+';'}}"><view class="info"><view class="info-pic"><image src="{{info.headimg?$root.g2:$root.g3.head}}" mode="aspectFill" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e"></image></view><view class="member-info"><view class="rank-info-box"><text class="name">{{info.nickname}}</text></view><block wx:if="{{type=='profit'}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="withdrawal" bindtap="__e">点击提现</view></block><block wx:if="{{type=='invited_num'}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="withdrawal" bindtap="__e">我的团队</view></block></view></view></view><block wx:if="{{type=='profit'}}"><view class="fenxiao-team"><view class="fenxiao-index-other"><view class="all-money-item"><view class="img-wrap"><text class="iconfont icon-fenxiao"></text></view><view class="all-money-tit-wrap"><text class="all-money-tit">分销佣金</text><text class="all-money-num">{{info.today_commission+"元"}}</text></view></view></view><view class="fenxiao-index-other"><view class="all-money-item"><view class="img-wrap"><text class="iconfont icon-baixingbeng"></text></view><view class="all-money-tit-wrap"><text class="all-money-tit">佣金排行</text><text class="all-money-num">{{"您排行第"+ranking+"名"}}</text></view></view></view></view></block><block wx:if="{{type=='invited_num'}}"><view class="fenxiao-team"><view class="fenxiao-index-other"><view class="all-money-item"><view class="img-wrap"><text class="iconfont icon-huodongtuiyan"></text></view><view class="all-money-tit-wrap"><text class="all-money-tit">推广人数</text><text class="all-money-num">{{info.one_child_num+"人"}}</text></view></view></view><view class="fenxiao-index-other"><view class="all-money-item"><view class="img-wrap"><text class="iconfont icon-baixingbeng"></text></view><view class="all-money-tit-wrap"><block wx:if="{{type=='invited_num'}}"><text class="all-money-tit">推广排行</text></block><text class="all-money-num">{{"您排行第"+ranking+"名"}}</text></view></view></view></view></block><block wx:if="{{type=='profit'}}"><view class="title-rakn-text">佣金排行</view></block><block wx:if="{{type=='invited_num'}}"><view class="title-rakn-text">推广排行</view></block><view class="ranking-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="ranking-item"><view class="ranking price-font">{{index+1}}</view><view class="content"><view class="head-img"><image src="{{item.$orig.headimg?item.g4:item.g5.head}}" mode="aspectFill" data-event-opts="{{[['error',[['e3',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" binderror="__e"></image></view><view class="nickname">{{item.$orig.nickname}}</view></view><block wx:if="{{type=='profit'}}"><view class="price-font price-style">{{"￥"+item.f0}}</view></block><block wx:if="{{type=='invited_num'}}"><view class="price-font price-style">{{item.$orig.child_num+"人"}}</view></block></view></block></view></block></block><block wx:if="{{$root.g6}}"><block><ns-empty vue-id="{{('6af9e549-2')+','+('6af9e549-1')}}" text="暂无数据" isIndex="{{false}}" bind:__l="__l"></ns-empty></block></block></view></mescroll-uni><loading-cover class="vue-ref" vue-id="6af9e549-3" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>