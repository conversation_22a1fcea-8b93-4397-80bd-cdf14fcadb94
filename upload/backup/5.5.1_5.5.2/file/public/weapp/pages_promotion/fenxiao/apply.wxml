<page-meta page-style="{{themeColor}}"></page-meta><view class="apply"><block wx:if="{{status===''}}"><view><block wx:if="{{basicsConfig.is_apply==1}}"><block><view class="{{['image',fenXiaoAgreement.agreement.img?'':'color-base-bg']}}"><block wx:if="{{fenXiaoAgreement.agreement.img}}"><image class="apply-adv" style="width:100%;" src="{{$root.g0}}" mode="widthFix"></image></block><block wx:else><view class="bg ns-gradient-otherpages-fenxiao-apply-apply-bg"><view class="bg-title"><text>请填写申请信息</text></view><view class="bg-img"><image src="{{$root.g1}}" mode="scaleToFill"></image></view></view></block></view><view class="apply-wrap"><view class="app-info"><view class="info"><view class="apply-item"><view class="title">邀请人</view><text class="shuru">{{sourceMemberInfo.fenxiao_name}}</text></view><view class="apply-item"><view class="title">{{fenxiaoWords.fenxiao_name+"名称"}}</view><input class="input" type="text" maxlength="30" placeholder="{{'请输入'+fenxiaoWords.fenxiao_name+'名称'}}" placeholder-class="pla-cla" data-event-opts="{{[['input',[['__set_model',['$0','fenXiaoName','$event',[]],['formData']]]]]}}" value="{{formData.fenXiaoName}}" bindinput="__e"/></view><view class="apply-item"><view class="title">手机号</view><input class="input" type="number" placeholder="请输入手机号" maxlength="11" placeholder-class="pla-cla" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['formData']]]]]}}" value="{{formData.mobile}}" bindinput="__e"/></view><block wx:if="{{isAgreement}}"><view data-event-opts="{{[['tap',[['checkedRuler']]]]}}" class="apply-xy" bindtap="__e"><view class="{{['iconfont',isChecked?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}"></view>我已阅读并了解<text data-event-opts="{{[['tap',[['openPopup',['$event']]]]]}}" class="color-base-text" catchtap="__e">{{"【"+fenxiaoWords.fenxiao_name+"申请协议】"}}</text></view></block><view class="apply-btn"><button data-event-opts="{{[['tap',[['applyFenXiao',['$event']]]]]}}" class="color-base-bg" bindtap="__e">{{"申请成为"+fenxiaoWords.fenxiao_name}}</button></view></view></view><block wx:if="{{fenxiaoConfig.fenxiao_condition==2||fenxiaoConfig.fenxiao_condition==3}}"><view class="apply-message-wrap"><view class="apply-message"><view class="apply-message-title color-base-bg-before">分销商申请条件</view><block wx:if="{{fenxiaoConfig.fenxiao_condition==2}}"><text class="apply-message-info font-size-goods-tag">{{'申请成为'+fenxiaoWords.fenxiao_name+",需要您的消费次数需要达到"+fenxiaoConfig.consume_count+'次'}}</text></block><block wx:if="{{fenxiaoConfig.fenxiao_condition==3}}"><text class="apply-message-info font-size-goods-tag">{{'申请成为'+fenxiaoWords.fenxiao_name+",需要您的消费金额需要达到"+fenxiaoConfig.consume_money+'元'}}</text></block></view></view></block><block wx:if="{{fenxiaoConfig.fenxiao_condition==4}}"><view class="app-info-list"><view class="apply-message"><view class="apply-message-title color-base-bg-before">分销商申请条件</view><text class="apply-message-info font-size-goods-tag">{{"申请成为"+fenxiaoWords.fenxiao_name+",需要购买以下指定商品(任选其一)"}}</text></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="goods-list" bindtap="__e"><view class="goods-img"><image src="{{item.m0}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image><block wx:if="{{item.m1!=''}}"><view class="color-base-bg goods-tag">{{item.m2}}</view></block></view><view class="goods-content"><view class="content-name">{{item.$orig.goods_name}}</view><view class="delete-price font-size-activity-tag color-tip content-price"><text class="unit">{{item.m3}}</text>{{''+(item.$orig.market_price>0?item.$orig.market_price:item.$orig.price)+''}}</view></view></view></block><block wx:if="{{isShow}}"><view data-event-opts="{{[['tap',[['onOpen']]]]}}" class="content-lists" bindtap="__e">{{''+(isOpen?'展开更多':'收起')+''}}<block wx:if="{{isOpen}}"><text class="iconfont icon-unfold" style="font-weight:bold;"></text></block><block wx:else><text class="iconfont icon-fold"></text></block></view></block></view></block></view></block></block><block wx:else><block wx:if="{{basicsConfig.is_apply==0}}"><view class="empty" style="margin-top:40rpx;"><block wx:if="{{fenxiaoConfig.fenxiao_condition==2||fenxiaoConfig.fenxiao_condition==3}}"><block><image src="{{$root.g2}}" mode="widthFix"></image><block wx:if="{{fenxiaoConfig.fenxiao_condition==2}}"><text>{{"申请成为"+fenxiaoWords.fenxiao_name+",需要您的消费次数需要达到"+fenxiaoConfig.consume_count+"次"}}</text></block><block wx:if="{{fenxiaoConfig.fenxiao_condition==3}}"><text>{{'申请成为'+fenxiaoWords.fenxiao_name+",需要您的消费金额需要达到"+$root.f0+'元'}}</text></block></block></block><block wx:if="{{fenxiaoConfig.fenxiao_condition==4}}"><view class="apply-wrap"><view class="app-info-list"><view class="apply-message"><view class="apply-message-title color-base-bg-before">成为分销商的条件</view><text class="apply-message-info font-size-goods-tag">{{"成为"+fenxiaoWords.fenxiao_name+",需要购买以下指定商品(任选其一)"}}</text></view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="goods-list" bindtap="__e"><view class="goods-img"><image src="{{item.m4}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image><block wx:if="{{item.m5!=''}}"><view class="color-base-bg goods-tag">{{item.m6}}</view></block></view><view class="goods-content"><view class="content-name">{{item.$orig.goods_name}}</view><view class="delete-price font-size-activity-tag color-tip content-price"><text class="unit">{{item.m7}}</text><text>{{item.$orig.market_price>0?item.$orig.market_price:item.$orig.price}}</text></view></view></view></block><block wx:if="{{isShow}}"><view data-event-opts="{{[['tap',[['onOpen']]]]}}" class="content-lists" bindtap="__e">{{''+(isOpen?'展开更多':'收起')+''}}<block wx:if="{{isOpen}}"><text class="iconfont icon-unfold" style="font-weight:bold;"></text></block><block wx:else><text class="iconfont icon-fold"></text></block></view></block></view></view></block></view></block><block wx:else><view class="empty"><image src="{{$root.g3}}" mode="widthFix"></image><text>您还不是分销商</text></view></block></block></view></block><block wx:else><view class="empty"><image src="{{$root.g4}}" mode="widthFix"></image><block wx:if="{{status==1}}"><text>{{"您已提交"+fenxiaoWords.fenxiao_name+"申请，等待平台审核"}}</text></block><block wx:else><block wx:if="{{status==-1}}"><block><text>{{"您提交的"+fenxiaoWords.fenxiao_name+"申请，已被拒绝，请再接再厉"}}</text><view data-event-opts="{{[['tap',[['againApply',['$event']]]]]}}" class="again-btn color-base-bg" bindtap="__e">重新申请</view></block></block></block></view></block><block wx:if="{{fenXiaoAgreement.document}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" bindtouchmove="__e"><uni-popup class="wap-floating vue-ref" vue-id="c9d68b04-1" type="center" data-ref="applyPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="conten-box"><text data-event-opts="{{[['tap',[['toClose',['$event']]]]]}}" class="iconfont icon-close" bindtap="__e"></text><view class="title">{{fenXiaoAgreement.document.title}}</view><view class="content-con"><scroll-view class="con" scroll-y="true" show-scrollbar="true"><ns-mp-html vue-id="{{('c9d68b04-2')+','+('c9d68b04-1')}}" content="{{fenXiaoAgreement.document.content}}" bind:__l="__l"></ns-mp-html></scroll-view></view></view></uni-popup></view></block><loading-cover class="vue-ref" vue-id="c9d68b04-3" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>