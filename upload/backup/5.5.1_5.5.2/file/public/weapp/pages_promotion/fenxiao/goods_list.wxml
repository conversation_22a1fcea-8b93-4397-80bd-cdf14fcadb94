<page-meta page-style="{{themeColor}}"></page-meta><view class="content"><mescroll-uni class="vue-ref" bind:getData="__e" vue-id="cf35fe2e-1" data-ref="mescroll" data-event-opts="{{[['^getData',[['getGoodsList']]]]}}" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><view class="goods-list" style="{{'background-image:'+('url('+$root.g0+')')+';'}}"><scroll-view class="quick-nav" scroll-x="true"><view class="uni-scroll-view-content"><view data-event-opts="{{[['tap',[['changeCategory',[0]]]]]}}" class="{{['quick-nav-item',(categoryId==0)?'selected':'']}}" bindtap="__e">全部</view><block wx:for="{{categoryList}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['changeCategory',['$0'],[[['categoryList','',__i0__,'category_id']]]]]]]}}" class="{{['quick-nav-item',(categoryId==item.category_id)?'selected':'']}}" bindtap="__e">{{''+item.category_name+''}}</view></block></view></scroll-view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['goodsList','',index]]]]]]]}}" class="goods-item" bindtap="__e"><view class="image-wrap"><image src="{{item.g1}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view><view class="goods-content"><view class="goods-name"><text class="name">{{item.$orig.sku_name}}</text><block wx:if="{{item.$orig.label_name}}"><view class="label-list"><text class="label-item">{{item.$orig.label_name}}</text></view></block></view><view class="goods-bottom"><view class="goods-price color-base-text"><text class="font-size-tag">￥</text>{{''+item.$orig.discount_price+''}}</view><view data-event-opts="{{[['tap',[['shareFn',['goods',index]]]]]}}" class="goods-share" catchtap="__e"><text class="icondiy icon-system-share"></text><block wx:if="{{!is_fenxiao}}"><text class="txt">分享</text></block><block wx:else><text class="txt">{{"赚"+item.$orig.commission_money+"元"}}</text></block></view></view></view></view></block><block wx:if="{{$root.g2==0}}"><view class="empty"><ns-empty vue-id="{{('cf35fe2e-2')+','+('cf35fe2e-1')}}" isIndex="{{false}}" text="暂无分销商品" textColor="#fff" bind:__l="__l"></ns-empty></view></block></view></view></mescroll-uni><block wx:if="{{$root.g3}}"><view class="active-btn"><button class="share-btn" plain="{{true}}" open-type="share">分享好友</button><text class="tag">|</text><text data-event-opts="{{[['tap',[['shareFn',['fenxiao']]]]]}}" class="btn" bindtap="__e">生成海报</text></view></block><loading-cover class="vue-ref" vue-id="cf35fe2e-3" data-ref="loadingCover" bind:__l="__l"></loading-cover><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><uni-popup class="share-popup vue-ref" vue-id="cf35fe2e-4" type="bottom" data-ref="sharePopup" bind:__l="__l" vue-slots="{{['default']}}"><view><view class="share-title">分享</view><view class="share-content"><view class="share-box"><button class="share-btn" plain="{{true}}" open-type="share"><view class="iconfont icon-share-friend"></view><text>分享给好友</text></button></view><block wx:if="{{goodsCircle}}"><view class="share-box"><button class="share-btn" plain="{{true}}" data-event-opts="{{[['tap',[['openBusinessView',['$event']]]]]}}" bindtap="__e"><view class="iconfont icon-haowuquan"></view><text>分享到好物圈</text></button></view></block><view data-event-opts="{{[['tap',[['openPosterPopup',['$event']]]]]}}" class="share-box" bindtap="__e"><button class="share-btn" plain="{{true}}"><view class="iconfont icon-pengyouquan"></view><text>生成分享海报</text></button></view></view><view data-event-opts="{{[['tap',[['closeSharePopup',['$event']]]]]}}" class="share-footer" bindtap="__e"><text>取消分享</text></view></view></uni-popup></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><uni-popup class="poster-layer vue-ref" vue-id="cf35fe2e-5" type="bottom" data-ref="posterPopup" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{poster!='-1'}}"><view><view class="image-wrap"><image src="{{$root.g4}}" show-menu-by-longpress="{{true}}"></image></view><view data-event-opts="{{[['tap',[['saveGoodsPoster']]]]}}" class="save" bindtap="__e">保存图片</view></view><view data-event-opts="{{[['tap',[['closePosterPopup']]]]}}" class="close iconfont icon-close" bindtap="__e"></view></block><block wx:else><view class="msg">{{posterMsg}}</view></block></uni-popup></view><hover-nav vue-id="cf35fe2e-6" bind:__l="__l"></hover-nav><privacy-popup class="vue-ref" vue-id="cf35fe2e-7" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>