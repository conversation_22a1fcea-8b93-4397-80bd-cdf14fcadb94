<page-meta page-style="{{themeColor}}"></page-meta><view><view class="withdraw-cate"><block wx:for="{{category}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-event-opts="{{[['tap',[['selectCate',['$0'],[[['category','',index,'id']]]]]]]}}" class="{{['cate-li',(selectId==item.id)?'active color-base-text color-base-bg-before':'']}}" bindtap="__e">{{item.name}}</view></block></block></view><block wx:if="{{storeToken}}"><mescroll-uni class="member-point vue-ref" vue-id="7a53bac4-1" top="90" size="{{8}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view class="goods_list" slot="list"><view class="order-list"><block wx:for="{{$root.l0}}" wx:for-item="orderItem" wx:for-index="orderIndex" wx:key="orderIndex"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['orderList','',orderIndex,'fenxiao_order_id']]]]]]]}}" class="order-item" bindtap="__e"><view class="order-header"><text class="site-name font-size-base">{{orderItem.$orig.order_no}}</text><block wx:if="{{orderItem.$orig.is_refund==1}}"><text class="status-name color-base-text">已退款</text></block><block wx:else><block wx:if="{{orderItem.$orig.is_settlement==1}}"><text class="status-name color-text-green">已结算</text></block><block wx:else><text class="status-name color-text-orange">待结算</text></block></block></view><view class="order-body"><view class="goods-wrap"><view class="goods-img"><image src="{{orderItem.g0}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError',[orderIndex]]]]]}}" binderror="__e"></image></view><view class="goods-info"><view class="top-wrap"><view class="goods-name font-size-base">{{orderItem.$orig.sku_name}}</view><view><text class="color-tip">{{"返"+fenxiaoWords.account}}</text><text class="price-color price-style small">{{orderItem.m0}}</text><text class="price-color price-style large">{{orderItem.g1[0]}}</text><text class="price-color price-style small">{{"."+orderItem.g2[1]}}</text></view></view><view class="goods-sub-section"><view class="goods-price"><text class="unit price-style small">{{orderItem.m1}}</text><text class="price-color price-style large">{{orderItem.g3[0]}}</text><text class="unit price-style small">{{"."+orderItem.g4[1]}}</text></view><view><text><text class="iconfont icon-close"></text>{{''+orderItem.$orig.num+''}}</text></view></view></view></view></view><view class="order-footer"><view class="order-base-info active"><view class="order-type"><text class="color-tip">{{orderItem.g5}}</text></view><view class="total"><text>合计：</text><text class="price-color">{{orderItem.m2}}</text><text class="price-color font-size-toolbar">{{orderItem.g6[0]}}</text><text class="price-color">{{"."+orderItem.g7[1]}}</text></view></view></view></view></block></view><view class="cart-empty"><block wx:if="{{$root.g8}}"><ns-empty vue-id="{{('7a53bac4-2')+','+('7a53bac4-1')}}" text="暂无订单" isIndex="{{false}}" bind:__l="__l"></ns-empty></block><block wx:if="{{$root.g9}}"><ns-empty vue-id="{{('7a53bac4-3')+','+('7a53bac4-1')}}" text="暂无待结算订单" isIndex="{{false}}" bind:__l="__l"></ns-empty></block><block wx:if="{{$root.g10}}"><ns-empty vue-id="{{('7a53bac4-4')+','+('7a53bac4-1')}}" text="暂无已结算订单" isIndex="{{false}}" bind:__l="__l"></ns-empty></block><block wx:if="{{$root.g11}}"><ns-empty vue-id="{{('7a53bac4-5')+','+('7a53bac4-1')}}" text="暂无已退款订单" isIndex="{{false}}" bind:__l="__l"></ns-empty></block></view></view></mescroll-uni></block><ns-login class="vue-ref" vue-id="7a53bac4-6" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="7a53bac4-7" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>