require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/fenxiao/level"],{1270:function(e,o,n){},7337:function(e,o,n){"use strict";n.r(o);var i=n("96e8"),t=n("f955");for(var a in t)["default"].indexOf(a)<0&&function(e){n.d(o,e,(function(){return t[e]}))}(a);n("b760"),n("cf110");var r=n("828b"),s=Object(r["a"])(t["default"],i["b"],i["c"],!1,null,"692beff6",null,!1,i["a"],void 0);o["default"]=s.exports},"96e8":function(e,o,n){"use strict";n.d(o,"b",(function(){return t})),n.d(o,"c",(function(){return a})),n.d(o,"a",(function(){return i}));var i={uniPopup:function(){return n.e("components/uni-popup/uni-popup").then(n.bind(null,"d745"))},nsGoodsRecommend:function(){return n.e("components/ns-goods-recommend/ns-goods-recommend").then(n.bind(null,"7254"))},loadingCover:function(){return n.e("components/loading-cover/loading-cover").then(n.bind(null,"c003"))}},t=function(){var e=this,o=e.$createElement,n=(e._self._c,e.$util.img("public/uniapp/level/level-top-bg.png")),i=e.fenxiaoInfo.headimg?e.$util.img(e.fenxiaoInfo.headimg):null,t=e.fenxiaoInfo.headimg?null:e.$util.getDefaultImage();e._isMounted||(e.e0=function(o){e.fenxiaoInfo.headimg=e.$util.getDefaultImage().head},e.e1=function(o){return e.$refs.tips.close()}),e.$mp.data=Object.assign({},{$root:{g0:n,g1:i,g2:t}})},a=[]},b760:function(e,o,n){"use strict";var i=n("de7b"),t=n.n(i);t.a},c200:function(e,o,n){"use strict";var i=n("47a9");Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var t=i(n("9fbb")),a={data:function(){return{fenxiaoInfo:{condition:{last_level:null}},config:{},levelList:[],curr:0,tips:""}},mixins:[t.default],computed:{levelInfo:function(){if(this.levelList.length){var e=this.levelList[this.curr];if(e.task=[],e.complete=0,e.one_fenxiao_order_num>0){var o={title:"下级消费",desc:"下级消费单数满"+e.one_fenxiao_order_num+"单",tips:"分销商自己购买和自己推荐的直属会员购买的订单次数达到"+e.one_fenxiao_order_num+"单",condition:e.one_fenxiao_order_num,value:this.fenxiaoInfo.one_fenxiao_order_num,progress:parseFloat(this.fenxiaoInfo.one_fenxiao_order_num)>parseFloat(e.one_fenxiao_order_num)?100:(parseFloat(this.fenxiaoInfo.one_fenxiao_order_num)/parseFloat(e.one_fenxiao_order_num)*100).toFixed(2)};100==o.progress&&(e.complete+=1),e.task.push(o)}if(e.one_fenxiao_total_order>0){var n={title:"下级消费",desc:"下级消费金额满"+this.moneyFormat(e.one_fenxiao_total_order)+"元",tips:"分销商自己购买和推荐的直属会员购买的订单的总额达到"+this.moneyFormat(e.one_fenxiao_total_order)+"元",condition:this.moneyFormat(e.one_fenxiao_total_order),value:this.fenxiaoInfo.one_fenxiao_total_order,progress:parseFloat(this.fenxiaoInfo.one_fenxiao_total_order)>parseFloat(e.one_fenxiao_total_order)?100:(parseFloat(this.fenxiaoInfo.one_fenxiao_total_order)/parseFloat(e.one_fenxiao_total_order)*100).toFixed(2)};100==n.progress&&(e.complete+=1),e.task.push(n)}if(e.one_fenxiao_order_money>0){var i={title:"下级消费",desc:"下级消费产生佣金总额满"+this.moneyFormat(e.one_fenxiao_order_money)+"元",tips:"分销商自己购买和自己推荐的直属会员购买的订单佣金总额达到"+this.moneyFormat(e.one_fenxiao_order_money)+"元",condition:this.moneyFormat(e.one_fenxiao_order_money),value:this.fenxiaoInfo.one_fenxiao_order_money,progress:parseFloat(this.fenxiaoInfo.one_fenxiao_order_money)>parseFloat(e.one_fenxiao_order_money)?100:(parseFloat(this.fenxiaoInfo.one_fenxiao_order_money)/parseFloat(e.one_fenxiao_order_money)*100).toFixed(2)};100==i.progress&&(e.complete+=1),e.task.push(i)}if(e.order_num>0){var t={title:"自身消费",desc:"自身消费单数满"+e.order_num+"单",tips:"分销商自己购买的订单次数达到"+e.order_num+"单",condition:e.order_num,value:this.fenxiaoInfo.order_num,progress:parseFloat(this.fenxiaoInfo.order_num)>parseFloat(e.order_num)?100:(parseFloat(this.fenxiaoInfo.order_num)/parseFloat(e.order_num)*100).toFixed(2)};100==t.progress&&(e.complete+=1),e.task.push(t)}if(e.order_money>0){var a={title:"自身消费",desc:"自身消费金额满"+this.moneyFormat(e.order_money)+"元",tips:"分销商自己购买的订单总额满足"+this.moneyFormat(e.order_money)+"元",condition:this.moneyFormat(e.order_money),value:this.fenxiaoInfo.order_money,progress:parseFloat(this.fenxiaoInfo.order_money)>parseFloat(e.order_money)?100:(parseFloat(this.fenxiaoInfo.order_money)/parseFloat(e.order_money)*100).toFixed(2)};100==a.progress&&(e.complete+=1),e.task.push(a)}if(e.one_child_num>0){var r={title:"邀请好友",desc:"邀请好友人数达到"+e.one_child_num+"人",tips:"分销商的直属下级会员人数达到"+e.one_child_num+"人（包含已经申请成为分销商的）",condition:e.one_child_num,value:this.fenxiaoInfo.one_child_num,progress:parseFloat(this.fenxiaoInfo.one_child_num)>parseFloat(e.one_child_num)?100:(parseFloat(this.fenxiaoInfo.one_child_num)/parseFloat(e.one_child_num)*100).toFixed(2)};100==r.progress&&(e.complete+=1),e.task.push(r)}if(e.one_child_fenxiao_num>0){var s={title:"邀请好友",desc:"邀请好友成为分销商人数达到"+e.one_child_fenxiao_num+"人",tips:"分销商的直属下级分销商人数达到"+e.one_child_fenxiao_num+"人",condition:e.one_child_fenxiao_num,value:this.fenxiaoInfo.one_child_fenxiao_num,progress:parseFloat(this.fenxiaoInfo.one_child_fenxiao_num)>parseFloat(e.one_child_fenxiao_num)?100:(parseFloat(this.fenxiaoInfo.one_child_fenxiao_num)/parseFloat(e.one_child_fenxiao_num)*100).toFixed(2)};100==s.progress&&(e.complete+=1),e.task.push(s)}return e.task_num=1==e.upgrade_type?1:e.task.length,e}}},onLoad:function(){},onShow:function(){var e=this;setTimeout((function(){e.addonIsExist.fenxiao||(e.$util.showToast({title:"商家未开启分销",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.fenxiaoWords&&this.fenxiaoWords.fenxiao_name&&this.$langConfig.title(this.fenxiaoWords.fenxiao_name+"等级"),this.storeToken?(this.getFenxiaoInfo(),this.getBasicsConfig()):this.$util.redirectTo("/pages_tool/login/index",{back:"/pages_promotion/fenxiao/level"})},methods:{getFenxiaoLevel:function(){var e=this;this.$api.sendRequest({url:"/fenxiao/api/Level/lists",success:function(o){0==o.code&&o.data&&(e.levelList=o.data,e.levelList.forEach((function(o,n){o.level_id==e.fenxiaoInfo.level_id&&(e.curr=n)})))}})},getFenxiaoInfo:function(){var e=this;this.$api.sendRequest({url:"/fenxiao/api/fenxiao/detail",success:function(o){e.$refs.loadingCover&&e.$refs.loadingCover.hide(),o.code>=0&&o.data?(e.fenxiaoInfo=o.data,e.curr=e.fenxiaoInfo.level_num,e.getFenxiaoLevel()):e.$util.redirectTo("/pages_promotion/fenxiao/apply")},fail:function(){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},getBasicsConfig:function(){var e=this;this.$api.sendRequest({url:"/fenxiao/api/config/basics",success:function(o){o.code>=0&&(e.config=o.data)}})},swiperChange:function(e){this.curr=e.detail.current},moneyFormat:function(e){return isNaN(parseFloat(e))?e:parseFloat(e).toFixed(2)},openTips:function(e){this.tips=e.tips,this.$refs.tips.open()}}};o.default=a},cf110:function(e,o,n){"use strict";var i=n("1270"),t=n.n(i);t.a},d11a:function(e,o,n){"use strict";(function(e,o){var i=n("47a9");n("d381");i(n("3240"));var t=i(n("7337"));e.__webpack_require_UNI_MP_PLUGIN__=n,o(t.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},de7b:function(e,o,n){},f955:function(e,o,n){"use strict";n.r(o);var i=n("c200"),t=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(o,e,(function(){return i[e]}))}(a);o["default"]=t.a}},[["d11a","common/runtime","common/vendor","pages_promotion/common/vendor"]]]);