require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/fenxiao/order_detail"],{"087c":function(t,e,o){"use strict";var i=o("6eb4"),n=o.n(i);n.a},"5fa86":function(t,e,o){"use strict";o.r(e);var i=o("9cbd"),n=o.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"6eb4":function(t,e,o){},"9cbd":function(t,e,o){"use strict";(function(t){var i=o("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(o("9fbb")),r={data:function(){return{isIphoneX:!1,orderId:0,orderData:{action:[]}}},components:{},onLoad:function(e){e.id?this.orderId=e.id:t.navigateBack({delta:1})},mixins:[n.default],onShow:function(){var t=this;setTimeout((function(){t.addonIsExist.fenxiao||(t.$util.showToast({title:"商家未开启分销",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?this.getOrderData():this.$nextTick((function(){t.$refs.login.open("/pages_promotion/fenxiao/order_detail?id="+t.orderId)}))},methods:{getOrderData:function(){var t=this;this.$api.sendRequest({url:"/fenxiao/api/order/info",data:{fenxiao_order_id:this.orderId},success:function(e){e.code>=0?(t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.orderData=e.data):(t.$util.showToast({title:"未获取到订单信息！"}),setTimeout((function(){t.$util.redirectTo("/pages_promotion/fenxiao/order",{},"redirectTo")}),1500))},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},imageError:function(){this.orderData.sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}},watch:{storeToken:function(t,e){t&&this.getOrderData()}}};e.default=r}).call(this,o("df3c")["default"])},b2d4:function(t,e,o){"use strict";o.r(e);var i=o("c300"),n=o("5fa86");for(var r in n)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(r);o("087c");var a=o("828b"),s=Object(a["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},bf09:function(t,e,o){"use strict";(function(t,e){var i=o("47a9");o("d381");i(o("3240"));var n=i(o("b2d4"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(n.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},c300:function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return r})),o.d(e,"a",(function(){return i}));var i={nsLogin:function(){return Promise.all([o.e("common/vendor"),o.e("components/ns-login/ns-login")]).then(o.bind(null,"2910"))},loadingCover:function(){return o.e("components/loading-cover/loading-cover").then(o.bind(null,"c003"))}},n=function(){var t=this.$createElement,e=(this._self._c,this.$util.img(this.orderData.sku_image,{size:"mid"})),o=parseFloat(this.orderData.price).toFixed(2).split("."),i=parseFloat(this.orderData.price).toFixed(2).split("."),n=parseFloat(this.orderData.commission).toFixed(2).split("."),r=parseFloat(this.orderData.commission).toFixed(2).split(".");this.$mp.data=Object.assign({},{$root:{g0:e,g1:o,g2:i,g3:n,g4:r}})},r=[]}},[["bf09","common/runtime","common/vendor","pages_promotion/common/vendor"]]]);