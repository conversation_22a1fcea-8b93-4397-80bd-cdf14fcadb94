<page-meta page-style="{{themeColor}}"></page-meta><view class="goods-list-box"><view class="box-bg" style="{{'background-image:'+('url('+$root.g0+')')+';'}}"><mescroll-uni class="vue-ref" vue-id="fde64f2a-1" top="30" data-ref="mescroll" data-event-opts="{{[['^getData',[['getGoods']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g1>0}}"><view class="goods-list double-column"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['infoList','',index]]]]]]]}}" class="goods-item margin-bottom" bindtap="__e"><view class="goods-img"><image class="imgs-img" src="{{item.m0}}" mode="aspectFit" data-event-opts="{{[['error',[['infoListImg',[index]]]]]}}" binderror="__e"></image></view><view class="info-wrap"><view class="name-wrap"><view class="goods-name">{{item.$orig.goods_name}}</view></view><view class="discount-price"><text class="unit color-base-text font-size-tag">￥<text class="price color-base-text font-size-toolbar">{{item.$orig.price}}</text></text></view></view></view></block></view></block><block wx:else><view><ns-empty vue-id="{{('fde64f2a-2')+','+('fde64f2a-1')}}" isIndex="{{false}}" text="暂无数据" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni></view><ns-login class="vue-ref" vue-id="fde64f2a-3" data-ref="ns-login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="fde64f2a-4" data-ref="loadingCover" bind:__l="__l"></loading-cover><privacy-popup class="vue-ref" vue-id="fde64f2a-5" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>