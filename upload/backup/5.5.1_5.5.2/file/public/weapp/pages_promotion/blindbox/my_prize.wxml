<page-meta page-style="{{themeColor}}"></page-meta><view class="prize-box"><view class="my_prize" style="{{'background-image:'+('url('+$root.g0+')')+';'}}"><view class="top"><view class="top-img"><block wx:if="{{blindgoods.blindbox_images!=''}}"><image src="{{$root.g1}}" mode="aspectFit" data-event-opts="{{[['error',[['errorImg']]]]}}" binderror="__e"></image></block><block wx:else><image src="{{$root.g2}}"></image></block></view><view class="right"><view class="blind-title font-size-toolbar">{{blindgoods.blindbox_name}}</view><block wx:if="{{blindgoods.blindbox_status==1}}"><view class="statused font-size-tag">进行中</view></block><block wx:if="{{blindgoods.blindbox_status==-1}}"><view class="statused font-size-tag">已关闭</view></block><block wx:if="{{blindgoods.blindbox_status==2}}"><view class="statused font-size-tag">已结束</view></block><view class="font-size-tag ul"><text class="li"></text><text>盲盒次数：</text><text>{{'可拆'+blindgoods.blindbox_count+'次，剩余'}}<text class="chi">{{blindgoods.blindbox_inventory}}</text>次</text></view><view class="font-size-tag ul"><text class="li"></text><text>我的次数：</text><text>已拆<text class="chi">{{blindgoods.buy_num}}</text>次</text></view></view></view><view class="content"><view class="tabs"><view data-event-opts="{{[['tap',[['myPrize',['1']]]]]}}" class="{{[diff==1?'tab tab_left act':'tab tab_left']}}" bindtap="__e">我的奖品</view><view data-event-opts="{{[['tap',[['myPrize',['2']]]]]}}" class="{{[diff==2?'tab tab_right act':'tab tab_right']}}" bindtap="__e">参与人</view></view><mescroll-uni class="vue-ref" vue-id="40c31493-1" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getMyPrize']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><view class="big"><block wx:if="{{typeShow}}"><view class="list-tab"><block wx:if="{{$root.g3>0}}"><view class="goods-list"><block wx:for="{{$root.l0}}" wx:for-item="items" wx:for-index="indexs" wx:key="indexs"><view class="list-body"><block wx:if="{{diff==1}}"><view class="list-body-img"><block wx:if="{{items.$orig.sku_image!=''}}"><image src="{{items.g4}}" mode="aspectFit" data-event-opts="{{[['error',[['errorSkuImg']]]]}}" binderror="__e"></image></block><block wx:else><image src="{{items.g5}}"></image></block></view></block><block wx:if="{{diff==2}}"><view class="list-body-img-right"><block wx:if="{{items.$orig.headimg}}"><image src="{{items.g6}}" mode="aspectFit" data-event-opts="{{[['error',[['errorSkuHeadImg']]]]}}" binderror="__e"></image></block><block wx:else><image src="{{items.g7}}"></image></block></view></block><block wx:if="{{diff==1}}"><view class="shop-content"><view class="shop-title">{{items.$orig.sku_name}}</view><view class="shop-time"><text class="color-tip font-size-tag">{{items.g8}}</text><block wx:if="{{items.$orig.is_dispatch==0}}"><button class="mini" type="primary" size="mini" data-event-opts="{{[['tap',[['deliver',['$0'],[[['blindbox','',indexs]]]]]]]}}" bindtap="__e">发货</button></block><block wx:if="{{items.$orig.is_dispatch==1}}"><button class="mini" type="primary" size="mini" data-event-opts="{{[['tap',[['look',['$0'],[[['blindbox','',indexs]]]]]]]}}" bindtap="__e">查看状态</button></block></view></view></block><block wx:if="{{diff==2}}"><view class="shop-content-right"><view class="shop-name-right"><text>{{items.$orig.nickname}}</text><text class="color-tip font-size-tag">{{items.g9}}</text></view><view class="shop-title-right color-sub font-size-tag">{{"获得"+items.$orig.sku_name}}</view></view></block></view></block></view></block><block wx:else><view class="prize-null"><block wx:if="{{diff==1}}"><view class="prize-image"><image src="{{$root.g10}}" mode="aspectFill"></image></view></block><block wx:if="{{diff==1}}"><view class="prize-null-title">暂无奖品~</view></block><block wx:if="{{diff==2}}"><view class="prize-image"><image src="{{$root.g11}}" mode="aspectFill"></image></view></block><block wx:if="{{diff==2}}"><view class="prize-null-title">暂无参与人~</view></block></view></block></view></block><block wx:else><view><view class="jiazai-box" style="background:#fff;"><view class="jiazai">加载中...</view></view></view></block></view></view></mescroll-uni></view></view><loading-cover class="vue-ref" vue-id="40c31493-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>