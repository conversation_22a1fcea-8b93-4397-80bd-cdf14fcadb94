<page-meta page-style="{{themeColor}}"></page-meta><view class="{{['order-container',(isIphoneX)?'safe-area':'']}}"><view class="payment-navbar" style="{{'padding-top:'+(menuButtonBounding.top+'px')+';'+('height:'+(menuButtonBounding.height+'px')+';')}}"><view class="nav-wrap"><text data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="iconfont icon-back_light" bindtap="__e"></text><view class="navbar-title">填写收货信息</view></view></view><view class="payment-navbar-block" style="{{'height:'+(menuButtonBounding.bottom+'px')+';'}}"></view><scroll-view class="order-scroll-container" scroll-y="true"><view class="payment-navbar-block"></view><block wx:if="{{goodsData}}"><block wx:if="{{orderPaymentData.is_virtual==0}}"><block wx:if="{{$root.g0>1}}"><view class="delivery-mode"><view class="action"><block wx:for="{{goodsData.delivery.express_type}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectDeliveryType',['$0'],[[['goodsData.delivery.express_type','',index]]]]]]]}}" class="{{[(item.name==orderCreateData.delivery.delivery_type)?'active':'']}}" bindtap="__e">{{''+item.title+''}}<view class="out-radio"></view></view></block></view></view></block><block wx:if="{{orderCreateData.delivery.delivery_type=='express'}}"><view class="{{['address-box',($root.g1<=1)?'not-delivery-type':'']}}"><block wx:if="{{memberAddress}}"><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="info-wrap" bindtap="__e"><view class="content"><text class="name">{{memberAddress.name?memberAddress.name:''}}</text><text class="mobile">{{memberAddress.mobile?memberAddress.mobile:''}}</text><view class="desc-wrap">{{''+(memberAddress.full_address?memberAddress.full_address:'')+"\n\t\t\t\t\t\t\t\t"+(memberAddress.address?memberAddress.address:'')+''}}</view></view><text class="cell-more iconfont icon-right"></text></view></block><block wx:else><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="empty-wrap" bindtap="__e"><view class="info">请设置收货地址</view><view class="cell-more"><view class="iconfont icon-right"></view></view></view></block><image class="address-line" src="{{$root.g2}}"></image></view></block><block wx:if="{{orderCreateData.delivery.delivery_type=='local'}}"><view class="{{['address-box',($root.g3<=1)?'not-delivery-type':'']}}"><block wx:if="{{localMemberAddress}}"><view><block wx:if="{{$root.g4}}"><block><block wx:if="{{storeInfo.currStore}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="local-delivery-store" bindtap="__e"><view class="info">由<text class="store-name">{{storeInfo.currStore.store_name}}</text>提供配送</view><view class="cell-more"><text>点击切换</text><text class="iconfont icon-right"></text></view></view></block><block wx:else><view class="local-delivery-store"><view class="info"><text class="store-name">您的附近没有可配送的门店，请选择其他配送方式</text></view></view></block></block></block><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="info-wrap local" bindtap="__e"><view class="content"><text class="name">{{localMemberAddress.name?localMemberAddress.name:''}}</text><text class="mobile">{{localMemberAddress.mobile?localMemberAddress.mobile:''}}</text><view class="desc-wrap">{{''+(localMemberAddress.full_address?localMemberAddress.full_address:'')+"\n\t\t\t\t\t\t\t\t\t"+(localMemberAddress.address?localMemberAddress.address:'')+''}}</view></view><text class="cell-more iconfont icon-right"></text></view><block wx:if="{{orderPaymentData.config.local&&orderPaymentData.config.local.is_use&&orderPaymentData.delivery.local&&orderPaymentData.delivery.local.info.time_is_open==1}}"><view class="local-box"><view data-event-opts="{{[['tap',[['localtime',['']]]]]}}" class="pick-block" bindtap="__e"><view class="title font-size-base">送达时间</view><view class="time-picker"><text class="{{[(!orderCreateData.buyer_ask_delivery_title)?'color-tip':'']}}">{{''+(orderCreateData.buyer_ask_delivery_title?orderCreateData.buyer_ask_delivery_title:'请选择送达时间')+''}}</text><text class="iconfont icon-right cell-more"></text></view></view></view></block></view></block><block wx:else><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="empty-wrap" bindtap="__e"><view class="info">请设置收货地址</view><view class="cell-more"><view class="iconfont icon-right"></view></view></view></block><image class="address-line" src="{{$root.g5}}"></image></view></block><block wx:if="{{orderCreateData.delivery.delivery_type=='store'}}"><view class="{{['store-box',($root.g6<=1)?'not-delivery-type':'']}}"><block wx:if="{{storeInfo.currStore}}"><block><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="store-info" bindtap="__e"><view class="store-address-info"><view class="info-wrap"><view class="title"><text>{{storeInfo.currStore.store_name}}</text></view><view class="store-detail"><block wx:if="{{storeInfo.currStore.open_date}}"><view>{{'营业时间：'+storeInfo.currStore.open_date}}</view></block><view class="address">{{storeInfo.currStore.full_address+"\n\t\t\t\t\t\t\t\t\t\t\t"+storeInfo.currStore.address+''}}</view></view></view><view class="cell-more iconfont icon-right"></view></view></view><view class="mobile-wrap store-mobile"><view class="form-group"><text class="text">姓名</text><input class="input" type="text" placeholder-class="color-tip placeholder" disabled="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['member_address']]]]]}}" value="{{member_address.name}}" bindinput="__e"/></view></view><view class="mobile-wrap store-mobile"><view class="form-group"><text class="text">预留手机</text><input class="input" type="number" maxlength="11" placeholder="请输入您的手机号码" placeholder-class="color-tip placeholder" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['member_address']]]]]}}" value="{{member_address.mobile}}" bindinput="__e"/></view></view><view data-event-opts="{{[['tap',[['storetime',['']]]]]}}" class="store-time" bindtap="__e"><view class="left">提货时间</view><view class="right"><text class="{{[(!orderCreateData.buyer_ask_delivery_title)?'color-tip':'']}}">{{''+(orderCreateData.buyer_ask_delivery_title?orderCreateData.buyer_ask_delivery_title:'请选择提货时间')+''}}</text><text class="iconfont icon-right"></text></view></view></block></block><block wx:else><view class="empty">当前无自提门店，请选择其它配送方式</view></block><image class="address-line" src="{{$root.g7}}"></image></view></block></block><view class="site-wrap order-goods"><view class="site-body"><block wx:for="{{$root.l0}}" wx:for-item="goodsItem" wx:for-index="goodsIndex" wx:key="goodsIndex"><view class="goods-item"><view class="goods-wrap"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({goodsItem:goodsItem.$orig})}}" class="goods-img" bindtap="__e"><image src="{{goodsItem.g8}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[goodsIndex]]]]]}}" binderror="__e"></image></view><view class="goods-info"><view class="top-wrap"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" data-event-params="{{({goodsItem:goodsItem.$orig})}}" class="goods-name" bindtap="__e">{{goodsItem.$orig.sku_name}}</view><block wx:if="{{goodsItem.$orig.sku_spec_format}}"><view class="sku"><view class="goods-spec"><block wx:for="{{goodsItem.$orig.sku_spec_format}}" wx:for-item="x" wx:for-index="i" wx:key="i"><block><view>{{x.spec_value_name}}</view></block></block></view></view></block><block wx:if="{{goodsItem.$orig.is_virtual==0}}"><block><block wx:if="{{goodsItem.g9}}"><view class="error-tips"><text class="iconfont icon-gantanhao"></text><text>{{"该商品不支持"+orderCreateData.delivery.delivery_type_name}}</text></view></block></block></block></view><view class="goods-sub-section"><view class="color-base-text"><text class="unit price-style small">{{goodsItem.m0}}</text><text class="goods-price price-style large">{{goodsItem.g10[0]}}</text><text class="unit price-style small">{{"."+goodsItem.g11[1]}}</text></view><view><text class="font-size-tag">x</text><text class="font-size-base">{{goodsItem.$orig.num}}</text></view></view></view></view></view></block></view></view><view class="site-wrap buyer-message"><view class="order-cell"><text class="tit">买家留言</text><view data-event-opts="{{[['tap',[['openPopup',['buyerMessagePopup']]]]]}}" class="box text-overflow" bindtap="__e"><block wx:if="{{orderCreateData.buyer_message}}"><text>{{orderCreateData.buyer_message}}</text></block><block wx:else><text class="color-sub">无留言</text></block></view><text class="iconfont icon-right"></text></view></view><view class="order-submit bottom-safe-area"><view class="order-settlement-info"></view><view class="submit-btn"><button class="mini" type="primary" size="mini" data-event-opts="{{[['tap',[['orderCreate']]]]}}" bindtap="__e">确定</button></view></view><view class="order-submit-block"></view><ns-select-time class="vue-ref" bind:selectTime="__e" vue-id="65c7e4ba-1" data-ref="timePopup" data-event-opts="{{[['^selectTime',[['selectPickupTime']]]]}}" bind:__l="__l"></ns-select-time></block></scroll-view><uni-popup class="vue-ref" vue-id="65c7e4ba-2" type="bottom" data-ref="buyerMessagePopup" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="buyermessag-popup popup" style="height:auto;" catchtouchmove="__e"><view class="popup-header"><text class="tit">买家留言</text><text data-event-opts="{{[['tap',[['closePopup',['buyerMessagePopup']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><scroll-view class="{{['popup-body',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view><view class="buyermessag-cell"><view class="buyermessag-form-group"><textarea type="text" maxlength="100" placeholder="留言前建议先与商家协调一致" placeholder-class="color-tip" data-event-opts="{{[['input',[['__set_model',['$0','buyer_message','$event',[]],['orderCreateData']]]]]}}" value="{{orderCreateData.buyer_message}}" bindinput="__e"></textarea></view></view></view></scroll-view><view data-event-opts="{{[['tap',[['closePopup',['buyerMessagePopup']]]]]}}" class="{{['popup-footer',(isIphoneX)?'bottom-safe-area':'']}}" bindtap="__e"><view class="confirm-btn color-base-bg">确定</view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="65c7e4ba-3" type="bottom" data-ref="deliveryPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="delivery-popup popup"><view class="popup-header"><text class="tit">已为您甄选出附近所有相关门店</text><text data-event-opts="{{[['tap',[['closePopup',['deliveryPopup']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><view class="{{['popup-body','store-popup',(isIphoneX)?'safe-area':'']}}"><view class="delivery-content"><block wx:for="{{storeInfo.storeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectPickupPoint',['$0'],[[['storeInfo.storeList','',index]]]]]]]}}" class="item-wrap" bindtap="__e"><view class="detail"><view class="{{['name',item.store_id==orderPaymentData.delivery.store_id?'color-base-text':'']}}"><text>{{item.store_name}}</text><block wx:if="{{item.distance}}"><text>{{"("+item.distance+"km)"}}</text></block></view><view class="info"><view class="{{['font-size-goods-tag',item.store_id==orderPaymentData.delivery.store_id?'color-base-text':'']}}">{{'营业时间：'+item.open_date+''}}</view><view class="{{['font-size-goods-tag',item.store_id==orderPaymentData.delivery.store_id?'color-base-text':'']}}">{{'地址：'+item.full_address+item.address+''}}</view></view></view><block wx:if="{{item.store_id==orderPaymentData.delivery.store_id}}"><view class="icon"><text class="iconfont icon-yuan_checked color-base-text"></text></view></block></view></block><block wx:if="{{!storeInfo.storeList}}"><view class="empty">所选择收货地址附近没有可以自提的门店</view></block></view></view></view></uni-popup><loading-cover class="vue-ref" vue-id="65c7e4ba-4" data-ref="loadingCover" bind:__l="__l"></loading-cover><ns-login class="vue-ref" vue-id="65c7e4ba-5" data-ref="login" bind:__l="__l"></ns-login><privacy-popup class="vue-ref" vue-id="65c7e4ba-6" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>