require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/blindbox/index"],{"0533":function(i,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return s})),e.d(t,"a",(function(){return n}));var n={uniPopup:function(){return e.e("components/uni-popup/uni-popup").then(e.bind(null,"d745"))},nsPayment:function(){return e.e("components/ns-payment/ns-payment").then(e.bind(null,"7aec"))},nsLogin:function(){return Promise.all([e.e("common/vendor"),e.e("components/ns-login/ns-login")]).then(e.bind(null,"2910"))},loadingCover:function(){return e.e("components/loading-cover/loading-cover").then(e.bind(null,"c003"))}},o=function(){var i=this,t=i.$createElement,e=(i._self._c,i.$util.img("public/uniapp/blindbox/index_bg.png")),n=""!=i.info.blindbox_images?i.$util.img(i.info.blindbox_images):null,o=""==i.info.blindbox_images?i.$util.img("public/uniapp/blindbox/default.png"):null,s=i.info.new_price>0?parseFloat(i.info.new_price).toFixed(2).split("."):null,a=i.info.new_price>0?parseFloat(i.info.new_price).toFixed(2).split("."):null,l=i.$util.img("public/uniapp/blindbox/index.png"),u=i.$util.img("public/uniapp/blindbox/cabinet_two.png"),r=i.__map(i.boxPage,(function(t,e){var n=i.__get_orig(t),o=i.__map(t,(function(t,e){var n=i.__get_orig(t),o=0==t.status?i.$util.img("public/uniapp/blindbox/box.png"):null,s=1==t.status?i.$util.img("public/uniapp/blindbox/null_box.png"):null,a=i.act==e?i.$util.img("public/uniapp/blindbox/checked.png"):null;return{$orig:n,g7:o,g8:s,g9:a}}));return{$orig:n,l0:o}})),d=i.$util.img("public/uniapp/blindbox/btn_one.png"),c=i.$util.img("public/uniapp/blindbox/btn_two.png"),b=i.$util.img("public/uniapp/blindbox/goods_bg.png"),p=i.__map(i.infoList,(function(t,e){var n=i.__get_orig(t),o=t.goods_image?i.goodsImg(t.goods_image):null;return{$orig:n,m0:o}})),g=i.infoList.length,h=g<3?i.infoList.length:null,f=i.$util.img("public/uniapp/blindbox/rule_bg.png"),m=i.$util.img("public/uniapp/blindbox/tag.png"),_=i.$util.timeStampTurnTime(i.info.start_time),x=i.$util.timeStampTurnTime(i.info.end_time),$=i.info.remark?i.$util.img("public/uniapp/blindbox/tag.png"):null,y=i.$util.img("public/uniapp/blindbox/success_bg.png"),v=i.boxDetail.sku_image?i.$util.img(i.boxDetail.sku_image):null,T=parseFloat(i.boxDetail.price).toFixed(2).split("."),P=parseFloat(i.boxDetail.price).toFixed(2).split("."),w=i.$util.img("public/uniapp/hongbao/close.png");i.$mp.data=Object.assign({},{$root:{g0:e,g1:n,g2:o,g3:s,g4:a,g5:l,g6:u,l1:r,g10:d,g11:c,g12:b,l2:p,g13:g,g14:h,g15:f,g16:m,g17:_,g18:x,g19:$,g20:y,g21:v,g22:T,g23:P,g24:w}})},s=[]},2959:function(i,t,e){"use strict";e.r(t);var n=e("0533"),o=e("68db");for(var s in o)["default"].indexOf(s)<0&&function(i){e.d(t,i,(function(){return o[i]}))}(s);e("b7bb"),e("4181");var a=e("828b"),l=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=l.exports},4181:function(i,t,e){"use strict";var n=e("b274"),o=e.n(n);o.a},"68db":function(i,t,e){"use strict";e.r(t);var n=e("8bd3"),o=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(i){e.d(t,i,(function(){return n[i]}))}(s);t["default"]=o.a},"786c":function(i,t,e){},8759:function(i,t,e){"use strict";(function(i,t){var n=e("47a9");e("d381");n(e("3240"));var o=n(e("2959"));i.__webpack_require_UNI_MP_PLUGIN__=e,t(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"8bd3":function(i,t,e){"use strict";(function(i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={components:{nsSwitch:function(){e.e("components/ns-switch/ns-switch").then(function(){return resolve(e("b0ec"))}.bind(null,e)).catch(e.oe)}},data:function(){return{lampX:0,act:null,switch:!1,blindbox_id:null,info:{},infoList:[],boxPage:[],boxData:{},is_balance:0,payMoney:1,isSub:!1,payPrice:0,outTradeNo:"",payInfo:{},page:1,pages:"",inxs:null,orderId:null,boxDetail:{},skuId:0,shareImg:"",autoplay:!0}},onLoad:function(t){var e=this;if(this.blindbox_id=t.blindbox_id,t.source_member&&i.setStorageSync("source_member",t.source_member),t.scene){var n=decodeURIComponent(t.scene);n=n.split("&"),n.length&&n.forEach((function(i){-1!=i.indexOf("blindbox_id")&&(e.blindbox_id=i.split("-")[1])}))}this.outTradeNo=t.outTradeNo||"",""!=this.outTradeNo?this.getboxGoods():(this.getGoods(),this.getblindbox())},onShow:function(){""!=this.outTradeNo?this.getboxGoods():(this.getGoods(),this.getblindbox()),this.storeToken&&i.getStorageSync("source_member")&&this.$util.onSourceMember(i.getStorageSync("source_member"))},onShareAppMessage:function(i){var t=this.info.blindbox_name,e=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),n=e.path;return{title:t,path:n,imageUrl:this.shareImg,success:function(i){},fail:function(i){}}},onShareTimeline:function(){var i=this.info.blindbox_name,t=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),e=t.query;return{title:i,query:e,imageUrl:this.shareImg}},methods:{goodsImg:function(i){var t=i.split(",");return t[0]?this.$util.img(t[0],{size:"mid"}):this.$util.getDefaultImage().goods},getGoods:function(){var i=this;this.$api.sendRequest({url:"/blindbox/api/goods/info",data:{blindbox_id:this.blindbox_id},success:function(t){if(t.code>=0){if(!t.data)return i.$util.showToast({title:"暂无盲盒活动",mask:!0,duration:2e3}),void setTimeout((function(){i.$util.redirectTo("/pages/index/index")}),2e3);i.info=t.data,i.info.blindbox_images?i.shareImg=i.$util.img(i.info.blindbox_images):i.shareImg=i.$util.img("public/uniapp/blindbox/default.png"),i.autoplay=!0;var e=[];for(var n in t.data.goods_list)e.push(t.data.goods_list[n]);i.infoList=e,i.infoList.length<=3&&(i.autoplay=!1)}else i.$util.showToast({title:t.message});i.$refs.loadingCover&&i.$refs.loadingCover.hide()}})},openBlind:function(){var i=this;this.storeToken?(0==this.info.is_new?this.payPrice=this.info.new_price:this.payPrice=this.info.price,0!=this.payMoney&&(this.payMoney=this.payPrice),null!=this.act?this.$refs.choosePaymentPopup.open():this.$util.showToast({title:"请选择盒子"})):this.$nextTick((function(){i.$refs.login.open("/pages_promotion/blindbox/index?blindbox_id="+i.blindbox_id)}))},toMyprize:function(){var i=this;this.storeToken?this.$util.redirectTo("/pages_promotion/blindbox/my_prize",{blindbox_id:this.info.blindbox_id}):this.$nextTick((function(){i.$refs.login.open("/pages_promotion/blindbox/index?blindbox_id="+i.blindbox_id)}))},getblindbox:function(){var i=this;this.$api.sendRequest({url:"/blindbox/api/goods/boxPage",data:{blindbox_id:this.blindbox_id,page:this.page},success:function(t){t.code>=0?(i.page++,i.boxPage.push(t.data.list),i.pages=t.data.page_count,null==i.inxs?i.inxs=0:setTimeout((function(){i.inxs++}),100)):i.$util.showToast({title:t.message})}})},getboxGoods:function(){var i=this;this.$api.sendRequest({url:"/blindbox/api/order/boxDetail",data:{out_trade_no:this.outTradeNo},success:function(t){t.code>=0?(i.isSub=!1,i.$util.showToast({title:"支付成功"}),i.boxDetail=t.data,i.$refs.againPopup.open(),i.blindbox_id=i.boxDetail.blindbox_id,i.encapsulation(),i.$refs.loadingCover&&i.$refs.loadingCover.hide(),i.getGoods(),i.getblindbox()):(i.isSub=!1,i.encapsulation(),i.act=null,i.$util.showToast({title:t.message})),i.is_balance=0}})},blindOrderCreate:function(){var t=this;this.isSub||(this.isSub=!0,this.$api.sendRequest({url:"/blindbox/api/order/createOrder",data:{blindbox_id:this.blindbox_id,blindbox_goods_id:this.boxData.id,is_balance:this.is_balance,price:this.payPrice},success:function(e){i.hideLoading(),e.code>=0?(t.outTradeNo=e.data,0==t.payMoney?t.getboxGoods():(t.$refs.choosePaymentPopup.getPayInfo(e.data),t.isSub=!1),t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.encapsulation(),t.act=null,t.isSub=!1,10==e.data.error_code||12==e.data.error_code?i.showModal({title:"订单未创建",content:e.message,confirmText:"去设置",success:function(i){i.confirm&&t.selectAddress()}}):t.$util.showToast({title:e.message})),0!=t.payMoney&&(t.payMoney=t.payPrice)}}))},useBalance:function(){this.is_balance?this.is_balance=0:(this.payMoney=0,this.is_balance=1),this.$forceUpdate()},checked:function(i,t){this.act==i?this.act=null:0==t.status?this.act=i:this.$util.showToast({title:"该盲盒已出售"}),this.boxData=t},changeGroup:function(){this.act=null,this.boxPage.length<this.pages?this.getblindbox():(1==this.pages?this.$util.showToast({title:"没有更多盒子可换了哦~"}):this.pages<=0&&this.$util.showToast({title:"盲盒已售空，等待商家补货"}),this.inxs+1==this.pages?this.inxs=0:this.inxs++)},infoListImg:function(i){this.infoList[i].goods_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},boxImg:function(){this.info.blindbox_images=this.$util.getDefaultImage().goods,this.$forceUpdate()},redirect:function(i,t){this.$util.redirectTo(i,t)},rule:function(){this.$refs.rulePopup.open()},closeRulePopup:function(){this.$refs.rulePopup.close()},successClose:function(){this.$refs.againPopup.close(),this.act=null,this.encapsulation(),this.getGoods()},reagain:function(){this.successClose()},receive:function(){var t=this;i.setStorage({key:"blindOrderCreateData",data:{sku_id:this.boxDetail.sku_id,num:1,blindbox_goods_id:this.boxDetail.blindbox_goods_id,out_trade_no:this.outTradeNo},success:function(){t.$util.redirectTo("/pages_promotion/blindbox/fill_address")}})},encapsulation:function(){var i=this;this.$api.sendRequest({url:"/blindbox/api/goods/boxPage",data:{blindbox_id:this.blindbox_id,page:this.inxs+1},success:function(t){t.code>=0?i.boxPage.splice(i.inxs,1,t.data.list):i.$util.showToast({title:t.message})}})},toIndex:function(){this.$util.redirectTo("/pages/index/index")},subscribeMessage:function(){this.$util.subscribeMessage("ORDER_PAY,ORDER_DELIVERY,ORDER_TAKE_DELIVERY")}},watch:{is_balance:function(i,t){0==i?this.payMoney=this.payPrice:this.info.member_info&&(this.info.member_info.balance_total-this.payPrice>=0?this.payMoney=0:this.payMoney=this.payPrice-this.info.member_info.balance_total)}},computed:{balanceDeduct:function(){if(this.info.member_info&&1==this.info.is_balance)return this.info.member_info.balance_total<=parseFloat(this.payPrice).toFixed(2)?parseFloat(this.info.member_info.balance_total).toFixed(2):parseFloat(this.payPrice).toFixed(2)}}};t.default=n}).call(this,e("df3c")["default"])},b274:function(i,t,e){},b7bb:function(i,t,e){"use strict";var n=e("786c"),o=e.n(n);o.a}},[["8759","common/runtime","common/vendor"]]]);