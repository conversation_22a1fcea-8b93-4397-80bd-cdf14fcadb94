<page-meta page-style="{{themeColor}}"></page-meta><view class="{{['my_box',isIphoneX?'iphone-x':'']}}"><block wx:if="{{storeToken}}"><view class="my_box_category"><block wx:for="{{boxStatusList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['categoryChange',['$0'],[[['boxStatusList','',index,'id']]]]]]]}}" class="category-item" bindtap="__e"><view class="{{['item-con',item.id==boxStatus?'active color-base-text color-base-bg-before acts':'']}}">{{item.name}}</view></view></block></view></block><block wx:if="{{storeToken}}"><mescroll-uni class="vue-ref" vue-id="0870083a-1" top="90" data-ref="mescroll" data-event-opts="{{[['^getData',[['getbox']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g0>0}}"><view class="item"><block wx:for="{{$root.l0}}" wx:for-item="items" wx:for-index="indexs" wx:key="indexs"><view class="goods-list"><view class="list-body"><view class="list-body-img"><block wx:if="{{items.$orig.blindbox_images!=''}}"><image src="{{items.g1}}" mode="aspectFit" data-event-opts="{{[['error',[['errorImg']]]]}}" binderror="__e"></image></block><block wx:else><image src="{{items.g2}}"></image></block></view><view class="shop-content"><view class="shop-title">{{items.$orig.blindbox_name}}</view><block wx:if="{{items.$orig.blindbox_status==1}}"><view class="statusing font-size-tag">进行中</view></block><block wx:if="{{items.$orig.blindbox_status==2}}"><view class="statused font-size-tag color-tip">已结束</view></block><view class="font-size-tag ul"><text class="li"></text><text class="color-tip lititle">盲盒次数：<text class="color-title">{{'可拆'+items.$orig.blindbox_count+'次，剩余'}}<text class="color-base-text">{{items.$orig.blindbox_inventory}}</text>次</text></text></view><view class="font-size-tag ul"><text class="li-two"></text><text class="color-tip lititle">我的次数：<text class="color-title">已拆<text class="color-base-text">{{items.$orig.buy_num}}</text>次</text></text></view></view></view><view class="my-prize-box"><text class="color-tip prize-time font-size-tag">{{"结束时间："+items.g3}}</text><text data-event-opts="{{[['tap',[['toMyprize',['$0'],[[['blindbox','',indexs]]]]]]]}}" class="color-base-text my-prize" bindtap="__e">我的奖品<text class="iconfont icon-right font-size-sub toprizeicon"></text></text></view></view></block></view></block><block wx:else><view><ns-empty vue-id="{{('0870083a-2')+','+('0870083a-1')}}" isIndex="{{false}}" text="暂无数据" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni></block><ns-login class="vue-ref" vue-id="0870083a-3" data-ref="ns-login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="0870083a-4" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>