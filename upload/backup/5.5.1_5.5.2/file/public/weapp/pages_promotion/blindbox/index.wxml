<page-meta page-style="{{themeColor}}"></page-meta><view class="blind-box"><block wx:if="{{info.blindbox_inventory<=0}}"><view class="null"><text class="iconfont icon-gantanhao1 gantan"></text><text>商品库存不足，请等待商家补货！</text></view></block><view class="box-bg" style="{{'background-image:'+('url('+$root.g0+')')+';'}}"><view class="top"><view class="top-img"><block wx:if="{{info.blindbox_images!=''}}"><image src="{{$root.g1}}" mode="aspectFit" data-event-opts="{{[['error',[['boxImg']]]]}}" binderror="__e"></image></block><block wx:else><image src="{{$root.g2}}"></image></block></view><view class="right"><view class="blind-title font-size-toolbar">{{info.blindbox_name}}</view><view class="scribe-price font-size-tag price-font">{{"￥"+info.price}}</view><block wx:if="{{info.new_price>0}}"><view class="new-price font-size-tag">新人价：<text class="icon price-font">￥</text><text class="money price-font">{{$root.g3[0]}}</text><text class="icon price-font">{{"."+$root.g4[1]}}</text></view></block></view><view data-event-opts="{{[['tap',[['rule']]]]}}" class="rule font-size-tag" bindtap="__e">活动规则</view><view data-event-opts="{{[['tap',[['toIndex']]]]}}" class="explain-img" bindtap="__e"><image class="index-img" src="{{$root.g5}}" mode></image></view></view><view class="cabinet-box"><view class="cabinet" style="{{'background-image:'+('url('+$root.g6+')')+';'}}"><swiper autoplay="{{false}}" indicator-dots="{{false}}" interval="{{3000}}" duration="{{1000}}" current="{{inxs}}" circular="{{true}}" disable-touch="{{true}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><view class="center-one"><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view data-event-opts="{{[['tap',[['checked',[index2,'$0'],[[['boxPage','',index],['','',index2]]]]]]]}}" class="item" bindtap="__e"><block wx:if="{{item2.$orig.status==0}}"><image class="box" src="{{item2.g7}}" mode></image></block><block wx:if="{{item2.$orig.status==1}}"><image class="box" src="{{item2.g8}}" mode></image></block><block wx:if="{{act==index2}}"><image class="checked" src="{{item2.g9}}"></image></block></view></block></view></swiper-item></block></swiper><view data-event-opts="{{[['tap',[['changeGroup']]]]}}" class="btn-one" style="{{'background-image:'+('url('+$root.g10+')')+';'}}" bindtap="__e">换一组</view><view data-event-opts="{{[['tap',[['openBlind']]]]}}" class="btn-two" style="{{'background-image:'+('url('+$root.g11+')')+';'}}" bindtap="__e">拆盲盒</view></view></view><view class="list-box"><view class="list_bit" style="{{'background-image:'+('url('+$root.g12+')')+';'}}"><swiper class="list imgs-list" circular="{{true}}" indicator-dots="{{false}}" disable-touch="{{!autoplay}}" autoplay="{{autoplay}}" interval="1200" duration="{{autoplay?'7000':'0'}}" display-multiple-items="3"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><block wx:if="{{item.$orig.goods_image}}"><image class="imgs-img" src="{{item.m0}}" mode="aspectFit" data-event-opts="{{[['tap',[['redirect',['/pages/goods/detail',['o',['goods_id',item.$orig.goods_id]]]]]],['error',[['infoListImg',[index]]]]]}}" bindtap="__e" binderror="__e"></image></block></swiper-item></block><block wx:if="{{$root.g13<3}}"><block><block wx:for="{{3-$root.g14}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item></swiper-item></block></block></block></swiper></view><view data-event-opts="{{[['tap',[['redirect',['/pages_promotion/blindbox/goods_list',['o',['blindbox_id',info.blindbox_id]]]]]]]}}" class="all font-size-tag" bindtap="__e">查看全部</view></view><view data-event-opts="{{[['tap',[['toMyprize']]]]}}" class="footer" bindtap="__e">我的奖品</view></view><uni-popup class="vue-ref" vue-id="0e14d2d8-1" type="center" data-ref="rulePopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="rule-wrap"><view class="content-wrap"><image class="rule-head" src="{{$root.g15}}" mode></image><view class="rule-title font-size-toolbar">活动规则</view><scroll-view class="rule" scroll-y="true"><view><view class="rule-tit"><view class="rule-img"><image src="{{$root.g16}}" mode></image></view><view class="tit">1. 活动时间</view></view><view class="text">{{"开始时间："+$root.g17}}</view><view class="text">{{"结束时间："+$root.g18}}</view><block wx:if="{{info.remark}}"><view class="rule-tit"><view class="rule-img"><image src="{{$root.g19}}" mode></image></view><view class="tit">2. 活动说明</view></view></block><block wx:if="{{info.remark}}"><view class="text">{{info.remark}}</view></block></view></scroll-view><text data-event-opts="{{[['tap',[['closeRulePopup',['$event']]]]]}}" class="iconfont icon-round-close" bindtap="__e"></text></view></view></uni-popup><view class="success-popup"><uni-popup class="vue-ref" vue-id="0e14d2d8-2" type="center" data-ref="againPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="success-wrap"><view class="content-wrap"><view class="success" style="{{'background-image:'+('url('+$root.g20+')')+';'}}"><view class="goods"><view class="goods-img"><block wx:if="{{boxDetail.sku_image}}"><image src="{{$root.g21}}" mode="aspectFit"></image></block></view><view class="goods-name">{{boxDetail.sku_name}}</view><view class="goods-price"><text class="font-size-tag">￥</text>{{''+$root.g22[0]+''}}<text class="font-size-tag">{{"."+$root.g23[1]}}</text></view></view><view class="again"><view class="color-sub discharge">已为您存放至盒柜<text data-event-opts="{{[['tap',[['redirect',['/pages_promotion/blindbox/my_prize',['o',['blindbox_id',boxDetail.blindbox_id]]]]]]]}}" class="color-base-text tobox" bindtap="__e">前往我的奖品></text></view><view class="agains-btn"><view data-event-opts="{{[['tap',[['receive']]]]}}" class="fill-btn" bindtap="__e">填写收货信息</view><view data-event-opts="{{[['tap',[['reagain']]]]}}" class="again-btn color-base-bg" bindtap="__e">再来一次</view></view></view></view><view data-event-opts="{{[['tap',[['successClose']]]]}}" class="close-btn" bindtap="__e"><image src="{{$root.g24}}" mode></image></view></view></view></uni-popup></view><block wx:if="{{info.member_info}}"><view><ns-payment class="vue-ref" vue-id="0e14d2d8-3" isBalance="{{is_balance}}" balanceDeduct="{{info.price>0&&info.member_info.balance_total>0?balanceDeduct:'0'}}" payMoney="{{payMoney}}" data-ref="choosePaymentPopup" data-event-opts="{{[['^useBalance',[['useBalance']]],['^confirm',[['blindOrderCreate']]]]}}" bind:useBalance="__e" bind:confirm="__e" bind:__l="__l"></ns-payment></view></block><ns-login class="vue-ref" vue-id="0e14d2d8-4" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="0e14d2d8-5" data-ref="loadingCover" bind:__l="__l"></loading-cover><privacy-popup class="vue-ref" vue-id="0e14d2d8-6" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>