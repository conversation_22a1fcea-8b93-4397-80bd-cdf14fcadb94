<page-meta page-style="{{themeColor}}" class="data-v-0392948d"></page-meta><view class="order-container data-v-0392948d"><block wx:if="{{storeToken}}"><view class="order-nav data-v-0392948d"><block wx:for="{{statusList}}" wx:for-item="statusItem" wx:for-index="statusIndex" wx:key="statusIndex"><view class="uni-tab-item data-v-0392948d" id="{{statusItem.id}}" data-current="{{statusIndex}}" data-event-opts="{{[['tap',[['ontabtap',['$event']]]]]}}" bindtap="__e"><text class="{{['uni-tab-item-title','data-v-0392948d',statusItem.status==orderStatus?'uni-tab-item-title-active color-base-text':'']}}">{{''+statusItem.name+''}}</text></view></block></view></block><mescroll-uni vue-id="13d68a43-1" top="80rpx" data-ref="mescroll" data-event-opts="{{[['^getData',[['getListData']]]]}}" bind:getData="__e" class="data-v-0392948d vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-0392948d"><block wx:if="{{$root.g0}}"><block class="data-v-0392948d"><block wx:for="{{$root.l0}}" wx:for-item="orderItem" wx:for-index="orderIndex" wx:key="orderIndex"><view data-event-opts="{{[['tap',[['detail',['$0'],[[['orderList','',orderIndex]]]]]]]}}" class="order-item data-v-0392948d" bindtap="__e"><view class="order-header data-v-0392948d"><text class="order-no data-v-0392948d">{{"订单号："+orderItem.$orig.order_no}}</text><text class="status-name data-v-0392948d">{{''+(orderItem.$orig.order_status==0?'待支付':orderItem.$orig.order_status==1?'已完成':orderItem.$orig.order_status==-1?'已关闭':'')+''}}</text></view><view class="order-body data-v-0392948d"><view class="goods-wrap data-v-0392948d"><view class="goods-img data-v-0392948d"><block wx:if="{{orderItem.$orig.type==2}}"><block class="data-v-0392948d"><image src="{{orderItem.g1?orderItem.g2:orderItem.g3}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError',[orderIndex]]]]]}}" binderror="__e" class="data-v-0392948d"></image></block></block><block wx:else><block wx:if="{{orderItem.$orig.type==3}}"><block class="data-v-0392948d"><image src="{{orderItem.g4?orderItem.g5:orderItem.g6}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError',[orderIndex]]]]]}}" binderror="__e" class="data-v-0392948d"></image></block></block><block wx:else><block class="data-v-0392948d"><image src="{{orderItem.g7}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError',[orderIndex]]]]]}}" binderror="__e" class="data-v-0392948d"></image></block></block></block></view><view class="goods-info data-v-0392948d"><view class="pro-info data-v-0392948d"><view class="goods-name data-v-0392948d">{{orderItem.$orig.exchange_name}}</view><view class="goods-sub-section data-v-0392948d"><text class="goods-price data-v-0392948d"><text class="price-style large data-v-0392948d">{{orderItem.$orig.point}}</text><text class="unit price-style small data-v-0392948d">积分</text><block wx:if="{{orderItem.$orig.price>0}}"><block class="data-v-0392948d">+<text class="unit price-style small data-v-0392948d">{{orderItem.m0}}</text><text class="price-style large data-v-0392948d">{{orderItem.g8[0]}}</text><text class="unit price-style small data-v-0392948d">{{"."+orderItem.g9[1]}}</text></block></block></text><text class="goods-num data-v-0392948d"><text class="iconfont icon-close data-v-0392948d"></text>{{''+orderItem.$orig.num+''}}</text></view></view></view></view></view><view class="order-footer data-v-0392948d"><block wx:if="{{orderItem.$orig.order_status==0&&orderItem.$orig.type==1}}"><view class="order-action data-v-0392948d"><view data-event-opts="{{[['tap',[['orderClose',['$0',orderIndex],[[['orderList','',orderIndex,'order_id']]]]]]]}}" class="order-box-btn font-size-tag data-v-0392948d" catchtap="__e">关闭</view><view data-event-opts="{{[['tap',[['openChoosePayment',['$0','$1'],[[['orderList','',orderIndex,'out_trade_no']],[['orderList','',orderIndex,'price']]]]]]]}}" class="order-box-btn color-base-bg color-base-border data-v-0392948d" catchtap="__e">支付</view></view></block><block wx:else><view class="order-action data-v-0392948d"><view class="order-box-btn font-size-tag data-v-0392948d">查看详情</view></view></block></view></view></block></block></block><block wx:if="{{$root.g10}}"><block class="data-v-0392948d"><view class="cart-empty data-v-0392948d"><ns-empty vue-id="{{('13d68a43-2')+','+('13d68a43-1')}}" isIndex="{{true}}" emptyBtn="{{({url:'/pages_promotion/point/list',text:'去逛逛'})}}" text="暂无积分兑换订单" class="data-v-0392948d" bind:__l="__l"></ns-empty></view></block></block></view></mescroll-uni><ns-payment vue-id="13d68a43-3" payMoney="{{payMoney}}" data-ref="choosePaymentPopup" data-event-opts="{{[['^confirm',[['orderPay']]]]}}" bind:confirm="__e" class="data-v-0392948d vue-ref" bind:__l="__l"></ns-payment><loading-cover vue-id="13d68a43-4" data-ref="loadingCover" class="data-v-0392948d vue-ref" bind:__l="__l"></loading-cover><ns-login vue-id="13d68a43-5" data-ref="login" class="data-v-0392948d vue-ref" bind:__l="__l"></ns-login></view>