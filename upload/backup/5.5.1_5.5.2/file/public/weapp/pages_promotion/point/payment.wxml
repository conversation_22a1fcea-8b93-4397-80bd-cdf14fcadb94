<page-meta page-style="{{themeColor}}" class="data-v-0a0d050b"></page-meta><view class="{{['order-container','data-v-0a0d050b',(isIphoneX)?'safe-area':'']}}"><view class="payment-navbar data-v-0a0d050b" style="{{'padding-top:'+(menuButtonBounding.top+'px')+';'+('height:'+(menuButtonBounding.height+'px')+';')}}"><view class="nav-wrap data-v-0a0d050b"><text data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="iconfont icon-back_light data-v-0a0d050b" bindtap="__e"></text><view class="navbar-title data-v-0a0d050b">确认订单</view></view></view><view class="payment-navbar-block data-v-0a0d050b" style="{{'height:'+(menuButtonBounding.bottom+'px')+';'}}"></view><scroll-view class="order-scroll-container data-v-0a0d050b" scroll-y="true"><view class="payment-navbar-block data-v-0a0d050b"></view><block wx:if="{{orderPaymentData.exchange_info.type==1&&orderPaymentData.is_virtual==0}}"><block wx:if="{{$root.g0}}"><view class="delivery-mode data-v-0a0d050b"><view class="action data-v-0a0d050b"><block wx:for="{{orderPaymentData.delivery.express_type}}" wx:for-item="deliveryItem" wx:for-index="deliveryIndex" wx:key="deliveryIndex"><view data-event-opts="{{[['tap',[['selectDeliveryType',['$0'],[[['orderPaymentData.delivery.express_type','',deliveryIndex]]]]]]]}}" class="{{['data-v-0a0d050b',(deliveryItem.name==orderCreateData.delivery.delivery_type)?'active':'']}}" bindtap="__e">{{''+deliveryItem.title+''}}<view class="out-radio data-v-0a0d050b"></view></view></block></view></view></block><block wx:if="{{orderPaymentData.delivery.delivery_type!='store'}}"><view class="{{['address-box','data-v-0a0d050b',($root.g1<=1)?'not-delivery-type':'']}}"><block wx:if="{{$root.g2}}"><block class="data-v-0a0d050b"><block wx:if="{{$root.g3}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="local-delivery-store data-v-0a0d050b" bindtap="__e"><view class="info data-v-0a0d050b">由<text class="store-name data-v-0a0d050b">{{storeInfo.currStore.store_name}}</text>提供配送</view><view class="cell-more data-v-0a0d050b"><text class="data-v-0a0d050b">点击切换</text><text class="iconfont icon-right data-v-0a0d050b"></text></view></view></block><block wx:else><view class="local-delivery-store data-v-0a0d050b"><view class="info data-v-0a0d050b"><text class="store-name data-v-0a0d050b">您的附近没有可配送的门店，请选择其他配送方式</text></view></view></block></block></block><block wx:if="{{orderCreateData.member_address}}"><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="{{['info-wrap','data-v-0a0d050b',(orderPaymentData.delivery.delivery_type=='local')?'local':'']}}" bindtap="__e"><view class="content data-v-0a0d050b"><text class="name font-size-base data-v-0a0d050b">{{orderCreateData.member_address.name?orderCreateData.member_address.name:''}}</text><text class="font-size-base mobile data-v-0a0d050b">{{orderCreateData.member_address.mobile?orderCreateData.member_address.mobile:''}}</text><text class="cell-more iconfont icon-right data-v-0a0d050b"></text><view class="desc-wrap data-v-0a0d050b">{{''+(orderCreateData.member_address.full_address?orderCreateData.member_address.full_address:'')+"\n\t\t\t\t\t\t\t"+(orderCreateData.member_address.address?orderCreateData.member_address.address:'')+''}}</view></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="empty-wrap data-v-0a0d050b" bindtap="__e"><view class="info data-v-0a0d050b">请设置收货地址</view><view class="cell-more data-v-0a0d050b"><view class="iconfont icon-right data-v-0a0d050b"></view></view></view></block><block wx:if="{{orderPaymentData.delivery.delivery_type=='local'}}"><block class="data-v-0a0d050b"><block wx:if="{{orderPaymentData.config.local.is_use&&orderPaymentData.delivery.local.info&&orderPaymentData.delivery.local.info.time_is_open==1}}"><view class="local-box data-v-0a0d050b"><view data-event-opts="{{[['tap',[['localtime',['$event']]]]]}}" class="pick-block data-v-0a0d050b" bindtap="__e"><view class="font-size-base data-v-0a0d050b">送达时间</view><view class="time-picker data-v-0a0d050b"><text class="{{['data-v-0a0d050b',(!orderCreateData.buyer_ask_delivery_title)?'color-tip':'']}}">{{orderCreateData.buyer_ask_delivery_title?orderCreateData.buyer_ask_delivery_title:'请选择送达时间'}}</text><text class="iconfont icon-right cell-more data-v-0a0d050b"></text></view></view></view></block></block></block><image class="address-line data-v-0a0d050b" src="{{$root.g4}}"></image></view></block><block wx:if="{{orderPaymentData.delivery.delivery_type=='store'&&storeInfo.currStore}}"><view class="{{['store-box','data-v-0a0d050b',($root.g5<=1)?'not-delivery-type':'']}}"><block wx:if="{{storeInfo.currStore}}"><block class="data-v-0a0d050b"><view data-event-opts="{{[['tap',[['openSiteDelivery',['$event']]]]]}}" class="store-info data-v-0a0d050b" bindtap="__e"><view class="store-address-info data-v-0a0d050b"><view class="info-wrap data-v-0a0d050b"><view class="title data-v-0a0d050b"><text class="data-v-0a0d050b">{{storeInfo.currStore.store_name}}</text><view class="cell-more iconfont icon-right data-v-0a0d050b"></view></view><view class="store-detail data-v-0a0d050b"><block wx:if="{{storeInfo.currStore.open_date}}"><view class="data-v-0a0d050b">{{'营业时间：'+storeInfo.currStore.open_date}}</view></block><view class="data-v-0a0d050b">{{"地址："+storeInfo.currStore.full_address+"\n\t\t\t\t\t\t\t\t\t\t"+storeInfo.currStore.address+''}}</view></view></view></view></view><view class="mobile-wrap store-mobile data-v-0a0d050b"><view class="form-group data-v-0a0d050b"><text class="text data-v-0a0d050b">姓名</text><input class="input data-v-0a0d050b" type="text" placeholder-class="color-tip placeholder" disabled="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['member_address']]]]]}}" value="{{member_address.name}}" bindinput="__e"/></view></view><view class="mobile-wrap store-mobile data-v-0a0d050b"><view class="form-group data-v-0a0d050b"><text class="text data-v-0a0d050b">预留手机</text><input class="input data-v-0a0d050b" type="number" maxlength="11" placeholder="请输入您的手机号码" placeholder-class="color-tip placeholder" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['member_address']]]]]}}" value="{{member_address.mobile}}" bindinput="__e"/></view></view><view class="store-time data-v-0a0d050b"><view class="left data-v-0a0d050b">自提时间</view><view data-event-opts="{{[['tap',[['storetime',['$event']]]]]}}" class="right data-v-0a0d050b" bindtap="__e">{{''+orderCreateData.buyer_ask_delivery_title+''}}<text class="iconfont icon-right data-v-0a0d050b"></text></view></view></block></block><block wx:else><view class="empty data-v-0a0d050b">当前无自提门店，请选择其它配送方式</view></block><image class="address-line data-v-0a0d050b" src="{{$root.g6}}"></image></view></block></block><block wx:if="{{orderPaymentData.is_virtual==1&&orderCreateData.member_address}}"><view class="mobile-wrap data-v-0a0d050b"><view class="tips color-base-text data-v-0a0d050b"><text class="iconfont icon-gantanhao data-v-0a0d050b"></text>购买虚拟类商品需填写手机号，方便商家与您联系</view><view class="form-group data-v-0a0d050b"><text class="iconfont icon-dianhua2 data-v-0a0d050b"></text><text class="text data-v-0a0d050b">手机号码</text><input class="input data-v-0a0d050b" type="number" maxlength="11" placeholder="请输入您的手机号码" placeholder-class="color-tip placeholder" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['orderCreateData.member_address']]]]]}}" value="{{orderCreateData.member_address.mobile}}" bindinput="__e"/></view></view></block><view class="{{['site-wrap','data-v-0a0d050b',orderPaymentData.exchange_info.type==2||orderPaymentData.exchange_info.type==3?'margin-top':'']}}"><view class="site-body data-v-0a0d050b"><view class="goods-wrap data-v-0a0d050b"><block wx:if="{{orderPaymentData.exchange_info.type==2}}"><view class="goods-img data-v-0a0d050b"><image src="{{orderPaymentData.exchange_info.image?$root.g7:$root.g8}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e" class="data-v-0a0d050b"></image></view></block><block wx:else><block wx:if="{{orderPaymentData.exchange_info.type==3}}"><view class="goods-img data-v-0a0d050b"><image src="{{orderPaymentData.exchange_info.image?$root.g9:$root.g10}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e" class="data-v-0a0d050b"></image></view></block><block wx:else><view class="goods-img data-v-0a0d050b"><image src="{{$root.g11}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e" class="data-v-0a0d050b"></image></view></block></block><view class="goods-info data-v-0a0d050b"><view class="goods-name data-v-0a0d050b">{{orderPaymentData.exchange_info.name}}</view><view class="goods-sub-section data-v-0a0d050b"><block wx:if="{{orderPaymentData.exchange_info.pay_type==1}}"><view class="color-base-text data-v-0a0d050b"><text class="goods-price data-v-0a0d050b">{{orderPaymentData.exchange_info.point}}</text><text class="unit data-v-0a0d050b">积分</text><block wx:if="{{orderPaymentData.exchange_info.price!='0.00'}}"><text class="unit data-v-0a0d050b">{{"+"+$root.m0}}</text><text class="goods-price data-v-0a0d050b">{{orderPaymentData.exchange_info.price}}</text></block></view></block><view class="data-v-0a0d050b"><text class="font-size-tag data-v-0a0d050b">x</text><text class="font-size-base data-v-0a0d050b">{{orderPaymentData.goods_num}}</text></view></view></view></view></view><view class="site-footer data-v-0a0d050b"><view class="order-cell data-v-0a0d050b"><text class="tit data-v-0a0d050b">买家留言</text><view class="box data-v-0a0d050b"><input type="text" placeholder="留言前建议先与商家协调一致" placeholder-class="color-tip" data-event-opts="{{[['input',[['__set_model',['$0','buyer_message','$event',[]],['orderCreateData']]]]]}}" value="{{orderCreateData.buyer_message}}" bindinput="__e" class="data-v-0a0d050b"/></view></view></view></view><view class="order-money data-v-0a0d050b"><view class="order-cell data-v-0a0d050b"><text class="tit data-v-0a0d050b">所需积分</text><view class="box data-v-0a0d050b"><text class="money data-v-0a0d050b">{{orderPaymentData.point}}</text><text class="unit data-v-0a0d050b">积分</text></view></view><block wx:if="{{orderPaymentData.exchange_info.type==1&&orderPaymentData.delivery_money>0}}"><view class="order-cell data-v-0a0d050b"><text class="tit data-v-0a0d050b">运费</text><view class="box data-v-0a0d050b"><text class="unit data-v-0a0d050b">{{$root.m1}}</text><text class="money data-v-0a0d050b">{{$root.f0}}</text></view></view></block></view><block wx:if="{{orderPaymentData.delivery&&orderPaymentData.delivery.delivery_type=='local'&&orderPaymentData.delivery&&orderPaymentData.delivery.error&&orderPaymentData.delivery.error!==''}}"><view class="error-message data-v-0a0d050b">{{''+orderPaymentData.delivery.error_msg+''}}</view></block><view class="{{['order-submit','data-v-0a0d050b',(isIphoneX)?'bottom-safe-area':'']}}"><view class="order-settlement-info data-v-0a0d050b"><text class="font-size-base color-tip margin-right data-v-0a0d050b">{{"共"+orderPaymentData.goods_num+"件"}}</text><text class="font-size-base data-v-0a0d050b">合计：</text><text class="color-base-text money data-v-0a0d050b">{{orderPaymentData.point}}</text><text class="color-base-text unit data-v-0a0d050b">积分</text><block wx:if="{{orderPaymentData.exchange_info.type==1&&orderPaymentData.order_money>0}}"><text class="color-base-text unit data-v-0a0d050b">{{"+"+$root.m2}}</text><text class="color-base-text money data-v-0a0d050b">{{$root.f1}}</text></block></view><view class="submit-btn data-v-0a0d050b"><block wx:if="{{$root.m3}}"><button class="mini data-v-0a0d050b" type="primary" size="mini" data-event-opts="{{[['tap',[['openChoosePayment']]]]}}" bindtap="__e">提交订单</button></block><block wx:else><button class="no-submit mini data-v-0a0d050b" size="mini"><block wx:if="{{orderPaymentData.delivery&&orderPaymentData.delivery.delivery_type=='local'&&orderPaymentData.delivery&&orderPaymentData.delivery.error&&orderPaymentData.delivery.start_money>orderPaymentData.price}}"><block class="data-v-0a0d050b">{{'差'+$root.f2+'起送'}}</block></block><block wx:else><block class="data-v-0a0d050b">提交订单</block></block></button></block></view></view><view class="order-submit-block _div data-v-0a0d050b"></view><uni-popup vue-id="560f6178-1" type="bottom" data-ref="deliveryPopup" class="data-v-0a0d050b vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="delivery-popup popup data-v-0a0d050b"><view class="popup-header data-v-0a0d050b"><text class="tit data-v-0a0d050b">已为您甄选出附近所有相关门店</text><text data-event-opts="{{[['tap',[['closePopup',['deliveryPopup']]]]]}}" class="iconfont icon-close data-v-0a0d050b" bindtap="__e"></text></view><view class="{{['popup-body','store-popup','data-v-0a0d050b',(isIphoneX)?'safe-area':'']}}"><view class="delivery-content data-v-0a0d050b"><block wx:for="{{storeInfo.storeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectPickupPoint',['$0'],[[['storeInfo.storeList','',index]]]]]]]}}" class="item-wrap data-v-0a0d050b" bindtap="__e"><view class="detail data-v-0a0d050b"><view class="{{['name','data-v-0a0d050b',item.store_id==orderPaymentData.delivery.store_id?'color-base-text':'']}}"><text class="data-v-0a0d050b">{{item.store_name}}</text><block wx:if="{{item.distance}}"><text class="data-v-0a0d050b">{{"("+item.distance+"km)"}}</text></block></view><view class="info data-v-0a0d050b"><view class="{{['font-size-goods-tag','data-v-0a0d050b',item.store_id==orderPaymentData.delivery.store_id?'color-base-text':'']}}">{{'营业时间：'+item.open_date+''}}</view><view class="{{['font-size-goods-tag','data-v-0a0d050b',item.store_id==orderPaymentData.delivery.store_id?'color-base-text':'']}}">{{'地址：'+item.full_address+item.address+''}}</view></view></view><block wx:if="{{item.store_id==orderPaymentData.delivery.store_id}}"><view class="icon data-v-0a0d050b"><text class="iconfont icon-yuan_checked color-base-text data-v-0a0d050b"></text></view></block></view></block><block wx:if="{{!storeInfo.storeList}}"><view class="empty data-v-0a0d050b">所选择收货地址附近没有可以自提的门店</view></block></view></view></view></uni-popup></scroll-view><ns-payment vue-id="560f6178-2" payMoney="{{orderPaymentData.order_money}}" data-ref="choosePaymentPopup" data-event-opts="{{[['^confirm',[['orderCreate']]]]}}" bind:confirm="__e" class="data-v-0a0d050b vue-ref" bind:__l="__l"></ns-payment><loading-cover vue-id="560f6178-3" data-ref="loadingCover" class="data-v-0a0d050b vue-ref" bind:__l="__l"></loading-cover><ns-select-time bind:selectTime="__e" vue-id="560f6178-4" data-ref="TimePopup" data-event-opts="{{[['^selectTime',[['selectTime']]]]}}" class="data-v-0a0d050b vue-ref" bind:__l="__l"></ns-select-time></view>