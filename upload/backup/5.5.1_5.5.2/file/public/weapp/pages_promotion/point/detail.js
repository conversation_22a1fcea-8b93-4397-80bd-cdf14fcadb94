require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/point/detail"],{"0266":function(n,t,o){"use strict";o.r(t);var i=o("0beb"),e=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(n){o.d(t,n,(function(){return i[n]}))}(a);t["default"]=e.a},"0beb":function(n,t,o){"use strict";(function(n){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;i(o("0817"));var e=i(o("e78f")),a={components:{uniPopup:function(){o.e("components/uni-popup/uni-popup").then(function(){return resolve(o("d745"))}.bind(null,o)).catch(o.oe)},uniNumberBox:function(){o.e("components/uni-number-box/uni-number-box").then(function(){return resolve(o("499c"))}.bind(null,o)).catch(o.oe)},nsGoodsSku:function(){o.e("components/ns-goods-sku/ns-goods-sku").then(function(){return resolve(o("132d"))}.bind(null,o)).catch(o.oe)},toTop:function(){o.e("components/toTop/toTop").then(function(){return resolve(o("8f75"))}.bind(null,o)).catch(o.oe)}},data:function(){return{id:0,pointInfo:{image:"",pointCoupon:0},isIphoneX:!1,isLogin:!1,memberPoint:0,isCommunity:!1,shareImg:""}},mixins:[e.default],onLoad:function(t){var o=this;if(t.source_member&&n.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(o.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&n.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&n.setStorageSync("is_test",1)}))}this.isIphoneX=this.$util.uniappIsIPhoneX(),t.id?(this.id=t.id,this.getPointInfo()):this.$util.redirectTo("/pages_promotion/point/list",{},"redirectTo")},onShow:function(){this.storeToken&&n.getStorageSync("source_member")&&this.$util.onSourceMember(n.getStorageSync("source_member"))},onShareAppMessage:function(n){var t="",o="";switch(this.pointInfo.type){case 1:t=this.pointInfo.sku_name,o=this.pointInfo.sku_image;break;case 2:case 3:t=this.pointInfo.name,o=this.pointInfo.image;break}t="仅需"+this.pointInfo.point+"积分即可兑换"+t,o=this.$util.img(o);var i=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),e=i.path;return{title:t,path:e,imageUrl:o,success:function(n){},fail:function(n){}}},onShareTimeline:function(){var n="",t="";switch(this.pointInfo.type){case 1:n=this.pointInfo.sku_name,t=this.pointInfo.sku_image;break;case 2:case 3:n=this.pointInfo.name,t=this.pointInfo.image;break}n="仅需"+this.pointInfo.point+"积分即可兑换"+n,t=this.$util.img(t);var o=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),i=o.query;return{title:n,query:i,imageUrl:t}},computed:{enough:function(){return parseInt(this.pointInfo.point)>parseInt(this.memberPoint)}},watch:{storeToken:function(n,t){n&&(this.isLogin=!0,this.getPointInfo())}},methods:{getAccountInfo:function(n,t){var o=this;this.storeToken?this.$api.sendRequest({url:"/api/memberaccount/info",data:{account_type:"point"},success:function(i){if(0==i.code&&i.data){o.isLogin=!0,o.memberPoint=i.data.point;var e=Math.floor(parseInt(i.data.point)/t);o.Max=n>=e?e:n}else o.$util.showToast({title:i.message});o.$refs.loadingCover&&o.$refs.loadingCover.hide()}}):(this.isLogin=!1,this.$refs.loadingCover&&this.$refs.loadingCover.hide())},getPointInfo:function(){var n=this;this.$api.sendRequest({url:"/pointexchange/api/goods/detail",data:{id:this.id},success:function(t){if(t.code>=0&&0!==t.data.length){n.pointInfo=t.data,n.pointInfo.image?n.shareImg=n.$util.img(n.pointInfo.image):n.shareImg=n.$util.img("public/uniapp/blindbox/default.png"),n.$langConfig.title(n.pointInfo.name);var o=2==n.pointInfo.type?n.pointInfo.count:n.pointInfo.stock;if(2==n.pointInfo.type&&-1==n.pointInfo.stock&&(n.pointInfo.pointCoupon=1),1==n.pointInfo.type){if(n.pointInfo.image=n.pointInfo["sku_image"],n.pointInfo.sku_spec_format&&(n.pointInfo.sku_spec_format=JSON.parse(n.pointInfo.sku_spec_format)),n.pointInfo.goods_attr_format){var i=JSON.parse(n.pointInfo.goods_attr_format);n.pointInfo.goods_attr_format=n.$util.unique(i,"attr_id");for(var e=0;e<n.pointInfo.goods_attr_format.length;e++)for(var a=0;a<i.length;a++)n.pointInfo.goods_attr_format[e].attr_id==i[a].attr_id&&n.pointInfo.goods_attr_format[e].attr_value_id!=i[a].attr_value_id&&(n.pointInfo.goods_attr_format[e].attr_value_name+="、"+i[a].attr_value_name)}n.pointInfo.goods_spec_format&&(n.pointInfo.goods_spec_format=JSON.parse(n.pointInfo.goods_spec_format))}n.pointInfo.unit=n.pointInfo.unit||"件",n.getAccountInfo(o,n.pointInfo.point)}else n.$util.showToast({title:t.message}),setTimeout((function(){n.$util.redirectTo("/pages_promotion/point/list",{},"redirectTo")}),1e3)}})},exchange:function(){this.storeToken?this.$refs.goodsSku.show("point"):this.$refs.login.open("/pages_promotion/point/detail?id="+this.id)},refreshGoodsSkuDetail:function(n){Object.assign(this.pointInfo,n),this.pointInfo.unit=this.pointInfo.unit||"件"},openMerchantsServicePopup:function(){this.$refs.merchantsServicePopup.open()},closeMerchantsServicePopup:function(){this.$refs.merchantsServicePopup.close()},openAttributePopup:function(){this.$refs.attributePopup.open()},closeAttributePopup:function(){this.$refs.attributePopup.close()},imageError:function(){this.pointInfo.image=this.$util.getDefaultImage().goods,this.$forceUpdate()},onCommunity:function(){this.isCommunity=!0},onCloseCommunity:function(){this.isCommunity=!1}}};t.default=a}).call(this,o("df3c")["default"])},2109:function(n,t,o){"use strict";o.d(t,"b",(function(){return e})),o.d(t,"c",(function(){return a})),o.d(t,"a",(function(){return i}));var i={uniPopup:function(){return o.e("components/uni-popup/uni-popup").then(o.bind(null,"d745"))},nsMpHtml:function(){return o.e("components/ns-mp-html/ns-mp-html").then(o.bind(null,"d108"))},nsGoodsSku:function(){return o.e("components/ns-goods-sku/ns-goods-sku").then(o.bind(null,"132d"))},loadingCover:function(){return o.e("components/loading-cover/loading-cover").then(o.bind(null,"c003"))},nsLogin:function(){return Promise.all([o.e("common/vendor"),o.e("components/ns-login/ns-login")]).then(o.bind(null,"2910"))},hoverNav:function(){return o.e("components/hover-nav/hover-nav").then(o.bind(null,"c1f1"))}},e=function(){var n=this,t=n.$createElement,o=(n._self._c,2==n.pointInfo.type&&n.pointInfo.image?n.$util.img(n.pointInfo.image):null),i=2!=n.pointInfo.type||n.pointInfo.image?null:n.$util.img("public/uniapp/point/coupon.png"),e=2!=n.pointInfo.type&&3==n.pointInfo.type&&n.pointInfo.image?n.$util.img(n.pointInfo.image):null,a=2==n.pointInfo.type||3!=n.pointInfo.type||n.pointInfo.image?null:n.$util.img("public/uniapp/point/hongbao.png"),r=2!=n.pointInfo.type&&3!=n.pointInfo.type?n.$util.img(n.pointInfo.image):null,u="0.00"!=n.pointInfo.exchange_price&&n.pointInfo.pay_type>0?n.$lang("common.currencySymbol"):null,s=n.pointInfo.price?n.$lang("common.currencySymbol"):null,p=n.pointInfo.coupon_type&&1!=n.pointInfo.validity_type?n.$util.timeStampTurnTime(n.pointInfo.end_time):null,f=n.pointInfo.qr_data&&1==n.pointInfo.qr_data.qr_state?n.$util.img("public/uniapp/goods/detail_erweiImage.png"):null,c=n.pointInfo.qr_data&&n.pointInfo.qr_data.qr_img&&""!=n.pointInfo.qr_data.qr_img&&1==n.pointInfo.qr_data.qr_state?n.$util.img(n.pointInfo.qr_data.qr_img):null,l=n.pointInfo.qr_data&&n.pointInfo.qr_data.qr_img&&(""==n.pointInfo.qr_data.qr_img||1!=n.pointInfo.qr_data.qr_state)?n.$util.img("public/uniapp/goods/detail_erweiImage.png"):null,m=1==n.pointInfo.type?n.pointInfo.goods_attr_format&&n.pointInfo.goods_attr_format.length>0:null;n._isMounted||(n.e0=function(t){n.imageError(n.$util.img("public/uniapp/point/coupon.png"))},n.e1=function(t){n.imageError(n.$util.img("public/uniapp/point/hongbao.png"))}),n.$mp.data=Object.assign({},{$root:{g0:o,g1:i,g2:e,g3:a,g4:r,m0:u,m1:s,g5:p,g6:f,g7:c,g8:l,g9:m}})},a=[]},5316:function(n,t,o){},"689e":function(n,t,o){"use strict";o.r(t);var i=o("2109"),e=o("0266");for(var a in e)["default"].indexOf(a)<0&&function(n){o.d(t,n,(function(){return e[n]}))}(a);o("e4b4"),o("eeab");var r=o("828b"),u=Object(r["a"])(e["default"],i["b"],i["c"],!1,null,"1868e849",null,!1,i["a"],void 0);t["default"]=u.exports},aabf:function(n,t,o){},bf45:function(n,t,o){"use strict";(function(n,t){var i=o("47a9");o("d381");i(o("3240"));var e=i(o("689e"));n.__webpack_require_UNI_MP_PLUGIN__=o,t(e.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},e4b4:function(n,t,o){"use strict";var i=o("5316"),e=o.n(i);e.a},eeab:function(n,t,o){"use strict";var i=o("aabf"),e=o.n(i);e.a}},[["bf45","common/runtime","common/vendor"]]]);