require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/point/order_list"],{"449f":function(e,t,n){"use strict";n.r(t);var o=n("95a8"),i=n("8479");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("b3fd");var s=n("828b"),a=Object(s["a"])(i["default"],o["b"],o["c"],!1,null,"0392948d",null,!1,o["a"],void 0);t["default"]=a.exports},8479:function(e,t,n){"use strict";n.r(t);var o=n("8d9c"),i=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=i.a},"8d9c":function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={data:function(){return{orderList:[],showEmpty:!1,outTradeNo:"",payMoney:0,statusList:[{status:"all",id:"all",name:"全部"},{status:0,id:"pay",name:"待支付"},{status:1,id:"complete",name:"已完成"}],orderStatus:"all"}},onLoad:function(){var e=this;setTimeout((function(){e.addonIsExist.pointexchange||(e.$util.showToast({title:"商家未开启积分商城",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index",{},"redirectTo")}),2e3))}),1e3),this.storeToken||this.$nextTick((function(){e.$refs.login.open("/pages_promotion/point/order_list")}))},onShow:function(){this.$refs.mescroll&&this.$refs.mescroll.refresh()},watch:{storeToken:function(e,t){e&&this.$refs.mescroll.refresh()}},methods:{ontabtap:function(e){var t=e.target.dataset.current||e.currentTarget.dataset.current;this.orderStatus=this.statusList[t].status,this.$refs.loadingCover.show(),this.$refs.mescroll.refresh()},getListData:function(e){var t=this;this.showEmpty=!1,this.$api.sendRequest({url:"/pointexchange/api/order/page",data:{page:e.num,page_size:e.size,order_status:this.orderStatus},success:function(n){t.showEmpty=!0;var o=[],i=n.message;0==n.code&&n.data?o=n.data.list:t.$util.showToast({title:i}),e.endSuccess(o.length),1==e.num&&(t.orderList=[]),t.orderList=t.orderList.concat(o),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(n){e.endErr(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},orderClose:function(t,n){var o=this;e.showModal({title:"提示",content:"确定关闭此次兑换？",success:function(e){e.confirm&&o.$api.sendRequest({url:"/pointexchange/api/order/close",data:{order_id:t},success:function(e){e.code>=0&&(o.orderList[n].order_status=-1,o.$util.showToast({title:"关闭成功"}),o.$forceUpdate())}})}})},openChoosePayment:function(e,t){this.outTradeNo=e,this.payMoney=parseFloat(t),this.$refs.choosePaymentPopup.open()},orderPay:function(){this.$refs.choosePaymentPopup.getPayInfo(this.outTradeNo)},detail:function(e){1==e.type&&e.relate_order_id?this.$util.redirectTo("/pages/order/detail",{order_id:e.relate_order_id}):this.$util.redirectTo("/pages/order/detail_point",{order_id:e.order_id})},imageError:function(e){this.orderList[e].exchange_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}}};t.default=n}).call(this,n("df3c")["default"])},"95a8":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o}));var o={nsEmpty:function(){return n.e("components/ns-empty/ns-empty").then(n.bind(null,"52a6"))},nsPayment:function(){return n.e("components/ns-payment/ns-payment").then(n.bind(null,"7aec"))},loadingCover:function(){return n.e("components/loading-cover/loading-cover").then(n.bind(null,"c003"))},nsLogin:function(){return Promise.all([n.e("common/vendor"),n.e("components/ns-login/ns-login")]).then(n.bind(null,"2910"))}},i=function(){var e=this,t=e.$createElement,n=(e._self._c,e.orderList.length),o=n?e.__map(e.orderList,(function(t,n){var o=e.__get_orig(t),i=2==t.type?e.$util.img(t.exchange_image):null,r=2==t.type&&i?e.$util.img(t.exchange_image):null,s=2!=t.type||i?null:e.$util.img("public/uniapp/point/coupon.png"),a=2!=t.type&&3==t.type?e.$util.img(t.exchange_image):null,u=2!=t.type&&3==t.type&&a?e.$util.img(t.exchange_image):null,l=2==t.type||3!=t.type||a?null:e.$util.img("public/uniapp/point/hongbao.png"),c=2!=t.type&&3!=t.type?e.$util.img(t.exchange_image):null,d=t.price>0?e.$lang("common.currencySymbol"):null,p=t.price>0?parseFloat(t.price).toFixed(2).split("."):null,f=t.price>0?parseFloat(t.price).toFixed(2).split("."):null;return{$orig:o,g1:i,g2:r,g3:s,g4:a,g5:u,g6:l,g7:c,m0:d,g8:p,g9:f}})):null,i=e.showEmpty&&!e.orderList.length;e.$mp.data=Object.assign({},{$root:{g0:n,l0:o,g10:i}})},r=[]},b065:function(e,t,n){},b3fd:function(e,t,n){"use strict";var o=n("b065"),i=n.n(o);i.a},fd75:function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("d381");o(n("3240"));var i=o(n("449f"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["fd75","common/runtime","common/vendor"]]]);