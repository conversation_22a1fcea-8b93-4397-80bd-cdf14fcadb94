<page-meta page-style="{{themeColor}}" class="data-v-1868e849"></page-meta><view class="data-v-1868e849"><view class="{{['goods-detail','data-v-1868e849',isIphoneX?'active':'']}}" scroll-y="true"><view class="goods-container data-v-1868e849"><view class="goods-media data-v-1868e849"><view class="goods-img show data-v-1868e849"><swiper class="swiper data-v-1868e849"><swiper-item item-id="{{'goods_id_'+pointInfo.type}}" class="data-v-1868e849"><block wx:if="{{pointInfo.type==2}}"><view class="item data-v-1868e849"><image class="adv-pic data-v-1868e849" src="{{pointInfo.image?$root.g0:$root.g1}}" mode="aspectFit" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e"></image></view></block><block wx:else><block wx:if="{{pointInfo.type==3}}"><view class="item data-v-1868e849"><image class="adv-pic data-v-1868e849" src="{{pointInfo.image?$root.g2:$root.g3}}" mode="aspectFit" data-event-opts="{{[['error',[['e1',['$event']]]]]}}" binderror="__e"></image></view></block><block wx:else><view class="item data-v-1868e849"><image class="adv-pic data-v-1868e849" src="{{$root.g4}}" mode="aspectFit" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e"></image></view></block></block></swiper-item></swiper></view></view><view class="group-wrap padding-top data-v-1868e849"><view class="goods-module-wrap data-v-1868e849"><text class="price-symbol data-v-1868e849">{{pointInfo.point+"积分"}}</text><block wx:if="{{pointInfo.exchange_price!='0.00'&&pointInfo.pay_type>0}}"><text class="price-symbol data-v-1868e849">{{"+"+$root.m0+pointInfo.exchange_price}}</text></block><block wx:if="{{pointInfo.price}}"><view class="market-price-wrap data-v-1868e849"><text class="unit data-v-1868e849">{{$root.m1}}</text><text class="money data-v-1868e849">{{pointInfo.price}}</text></view></block><block wx:if="{{pointInfo.stock_show==1||pointInfo.stock_show==undefined}}"><view class="follow-and-share data-v-1868e849"><block wx:if="{{pointInfo.pointCoupon!=1}}"><text class="color-tip data-v-1868e849">{{"库存:"+pointInfo.stock+(pointInfo.stock>=0?pointInfo.unit:'')}}</text></block><block wx:else><text class="color-tip data-v-1868e849">{{"库存:无限"+(pointInfo.stock>=0?pointInfo.unit:'')}}</text></block></view></block></view><view class="goods-module-wrap info data-v-1868e849"><block wx:if="{{pointInfo.type==1}}"><text class="sku-name-wrap data-v-1868e849">{{pointInfo.goods_name}}</text></block><block wx:else><text class="sku-name-wrap data-v-1868e849">{{pointInfo.name}}</text></block></view><view class="coupon-desc data-v-1868e849"><block wx:if="{{pointInfo.balance&&pointInfo.balance>0}}"><view class="color-tip data-v-1868e849">{{"内含"+pointInfo.balance+"元"}}</view></block><block wx:if="{{pointInfo.coupon_type=='random'}}"><view class="color-tip data-v-1868e849">无门槛优惠券</view></block><block wx:if="{{pointInfo.coupon_type=='reward'}}"><view class="color-tip data-v-1868e849">{{'满'+pointInfo.at_least+'减'+pointInfo.money}}</view></block><block wx:if="{{pointInfo.coupon_type=='discount'}}"><view class="color-tip data-v-1868e849">{{'满'+pointInfo.at_least+'元'}}<text class="data-v-1868e849">{{pointInfo.discount+"折"}}</text></view></block><block wx:if="{{pointInfo.coupon_type=='discount'}}"><view class="color-tip data-v-1868e849">{{"最多优惠"+pointInfo.discount_limit+"元"}}</view></block><block wx:if="{{pointInfo.coupon_type}}"><view class="time color-tip data-v-1868e849">{{''+(pointInfo.validity_type==1?'领取之日起'+pointInfo.fixed_term+'天内有效':$root.g5+'到期')+''}}</view></block></view></view><block wx:if="{{pointInfo.qr_data&&pointInfo.qr_data.qr_state==1}}"><view class="detail-community data-v-1868e849"><view class="community-box data-v-1868e849"><image src="{{$root.g6}}" mode="aspectFill" class="data-v-1868e849"></image><view class="community-content data-v-1868e849"><view class="community-title data-v-1868e849">{{pointInfo.qr_data.qr_name}}</view><view class="community-txt data-v-1868e849">{{pointInfo.qr_data.community_describe}}</view></view></view><view data-event-opts="{{[['tap',[['onCommunity']]]]}}" class="community-btn data-v-1868e849" bindtap="__e">添加</view></view></block><view data-event-opts="{{[['touchmove',[['',['$event']]]],['tap',[['onCloseCommunity']]]]}}" hidden="{{!(isCommunity)}}" class="community-model data-v-1868e849" catchtouchmove="__e" catchtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="community-model-content data-v-1868e849" catchtap="__e"><view class="community-model-content-radius data-v-1868e849"><view class="data-v-1868e849">添加社群</view></view><block wx:if="{{pointInfo.qr_data&&pointInfo.qr_data.qr_img}}"><view class="community-model-content-draw data-v-1868e849"><image src="{{pointInfo.qr_data.qr_img!=''&&pointInfo.qr_data.qr_state==1?$root.g7:$root.g8}}" mode="aspectFill" show-menu-by-longpress="true" class="data-v-1868e849"></image></view></block><view class="community-model-content-text data-v-1868e849">长按识别二维码，添加社群</view></view><view data-event-opts="{{[['tap',[['onCloseCommunity']]]]}}" class="community-model-close data-v-1868e849" catchtap="__e"><text class="iconfont icon-close data-v-1868e849"></text></view></view><block wx:if="{{pointInfo.type==1}}"><block class="data-v-1868e849"><view class="newdetail margin-bottom data-v-1868e849"><block wx:if="{{pointInfo.sku_spec_format}}"><view data-event-opts="{{[['tap',[['exchange',['$event']]]]]}}" class="item selected-sku-spec data-v-1868e849" bindtap="__e"><view class="label data-v-1868e849">选择</view><view class="box data-v-1868e849"><block wx:for="{{pointInfo.sku_spec_format}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text class="data-v-1868e849">{{item.spec_name+"/"+item.spec_value_name}}</text></block></view><text class="iconfont icon-right data-v-1868e849"></text></view></block><block wx:if="{{$root.g9}}"><view data-event-opts="{{[['tap',[['openAttributePopup']]]]}}" class="item goods-attribute data-v-1868e849" bindtap="__e"><view class="label data-v-1868e849">属性</view><view class="box data-v-1868e849"><block wx:for="{{pointInfo.goods_attr_format}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index<2}}"><text class="data-v-1868e849">{{item.attr_name+": "+item.attr_value_name}}</text></block></block></view><text class="iconfont icon-right data-v-1868e849"></text></view></block></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-1868e849"><uni-popup vue-id="7654d7be-1" type="bottom" data-ref="attributePopup" class="data-v-1868e849 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="goods-attribute-popup-layer popup-layer data-v-1868e849"><view data-event-opts="{{[['tap',[['closeAttributePopup']]]]}}" class="head-wrap data-v-1868e849" bindtap="__e"><text class="data-v-1868e849">商品属性</text><text class="iconfont icon-close data-v-1868e849"></text></view><scroll-view class="goods-attribute-body data-v-1868e849" scroll-y="{{true}}"><block wx:for="{{pointInfo.goods_attr_format}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item data-v-1868e849"><text class="attr-name data-v-1868e849">{{item.attr_name}}</text><text class="value-name data-v-1868e849">{{item.attr_value_name}}</text></view></block></scroll-view><view class="button-box data-v-1868e849"><button type="primary" data-event-opts="{{[['tap',[['closeAttributePopup']]]]}}" bindtap="__e" class="data-v-1868e849">确定</button></view></view></uni-popup></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-1868e849"><uni-popup vue-id="7654d7be-2" type="bottom" data-ref="merchantsServicePopup" class="data-v-1868e849 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="goods-merchants-service-popup-layer popup-layer data-v-1868e849"><view data-event-opts="{{[['tap',[['closeMerchantsServicePopup']]]]}}" class="head-wrap data-v-1868e849" bindtap="__e"><text class="data-v-1868e849">商品服务</text><text class="iconfont icon-close data-v-1868e849"></text></view><scroll-view scroll-y="{{true}}" class="data-v-1868e849"><block wx:for="{{pointInfo.goods_service}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item','data-v-1868e849',(!item.desc)?'empty-desc':'']}}"><view class="iconfont icon-dui data-v-1868e849"></view><view class="info-wrap data-v-1868e849"><text class="title data-v-1868e849">{{item.service_name}}</text><block wx:if="{{item.desc}}"><text class="describe data-v-1868e849">{{item.desc}}</text></block></view></view></block></scroll-view><view class="button-box data-v-1868e849"><button type="primary" data-event-opts="{{[['tap',[['closeMerchantsServicePopup']]]]}}" bindtap="__e" class="data-v-1868e849">确定</button></view></view></uni-popup></view></block></block><view class="goods-detail-tab data-v-1868e849"><view class="detail-tab data-v-1868e849"><view class="tab-item data-v-1868e849">兑换详情</view></view><view class="detail-content data-v-1868e849"><view class="detail-content-item data-v-1868e849"><block wx:if="{{pointInfo.content}}"><view class="goods-details data-v-1868e849"><ns-mp-html vue-id="7654d7be-3" content="{{pointInfo.content}}" class="data-v-1868e849" bind:__l="__l"></ns-mp-html></view></block><block wx:else><view class="goods-details active data-v-1868e849">暂无兑换详情！</view></block></view></view></view><block wx:if="{{pointInfo.id}}"><ns-goods-sku vue-id="7654d7be-4" member-point="{{memberPoint}}" goods-detail="{{pointInfo}}" goods-id="{{pointInfo.goods_id}}" max-buy="{{pointInfo.max_buy}}" min-buy="{{pointInfo.min_buy}}" source="point" data-ref="goodsSku" data-event-opts="{{[['^refresh',[['refreshGoodsSkuDetail']]]]}}" bind:refresh="__e" class="data-v-1868e849 vue-ref" bind:__l="__l"></ns-goods-sku></block></view></view><block wx:if="{{!isLogin}}"><view data-event-opts="{{[['tap',[['exchange']]]]}}" class="{{['detail-swap','data-v-1868e849',(isIphoneX)?'position-bottom':'']}}" bindtap="__e"><button type="primary" class="data-v-1868e849">登录之后方可兑换</button></view></block><block wx:else><block class="data-v-1868e849"><block wx:if="{{pointInfo.stock==0}}"><view class="{{['detail-swap','data-v-1868e849',(isIphoneX)?'position-bottom':'']}}"><button disabled="{{true}}" class="data-v-1868e849">库存不足</button></view></block><block wx:else><block wx:if="{{enough}}"><view class="{{['detail-swap','data-v-1868e849',(isIphoneX)?'position-bottom':'']}}"><button disabled="{{true}}" class="data-v-1868e849">积分不足</button></view></block><block wx:else><view data-event-opts="{{[['tap',[['exchange']]]]}}" class="{{['detail-swap','data-v-1868e849',(isIphoneX)?'position-bottom':'']}}" bindtap="__e"><button type="primary" hover-class="none" class="data-v-1868e849">兑换</button></view></block></block></block></block><loading-cover vue-id="7654d7be-5" data-ref="loadingCover" class="data-v-1868e849 vue-ref" bind:__l="__l"></loading-cover><ns-login vue-id="7654d7be-6" data-ref="login" class="data-v-1868e849 vue-ref" bind:__l="__l"></ns-login><block wx:if="{{showTop}}"><to-top bind:toTop="__e" vue-id="7654d7be-7" data-event-opts="{{[['^toTop',[['scrollToTopNative']]]]}}" class="data-v-1868e849" bind:__l="__l"></to-top></block><hover-nav vue-id="7654d7be-8" class="data-v-1868e849" bind:__l="__l"></hover-nav><privacy-popup vue-id="7654d7be-9" data-ref="privacyPopup" class="data-v-1868e849 vue-ref" bind:__l="__l"></privacy-popup></view>