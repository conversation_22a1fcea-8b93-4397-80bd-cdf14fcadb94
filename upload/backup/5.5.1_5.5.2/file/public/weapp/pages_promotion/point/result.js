require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/point/result"],{"00df":function(t,n,e){"use strict";(function(t,n){var i=e("47a9");e("d381");i(e("3240"));var a=i(e("4aca"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"4aca":function(t,n,e){"use strict";e.r(n);var i=e("7e12"),a=e("63af");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("6893");var u=e("828b"),r=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);n["default"]=r.exports},"5d42":function(t,n,e){},"63af":function(t,n,e){"use strict";e.r(n);var i=e("fadb"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},6893:function(t,n,e){"use strict";var i=e("5d42"),a=e.n(i);a.a},"7e12":function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement,n=(this._self._c,this.$util.img("public/uniapp/pay/pay_success.png")),e=this.$lang("exchangeSuccess"),i=this.$lang("see"),a=this.$lang("goHome");this.$mp.data=Object.assign({},{$root:{g0:n,m0:e,m1:i,m2:a}})},a=[]},fadb:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={data:function(){return{}},onShow:function(){},methods:{toOrderList:function(){this.$util.redirectTo("/pages_promotion/point/order_list",{},"redirectTo")},toIndex:function(){this.$util.redirectTo("/pages/index/index")}}}}},[["00df","common/runtime","common/vendor"]]]);