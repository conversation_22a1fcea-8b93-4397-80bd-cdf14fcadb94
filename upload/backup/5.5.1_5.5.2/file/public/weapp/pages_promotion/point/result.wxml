<page-meta page-style="{{themeColor}}"></page-meta><view class="container"><view class="image-wrap"><image class="result-image" src="{{$root.g0}}" mode="widthFix"></image></view><view class="msg">{{$root.m0}}</view><view class="action"><view data-event-opts="{{[['tap',[['toOrderList']]]]}}" class="btn color-base-border color-base-text" bindtap="__e">{{$root.m1}}</view><view data-event-opts="{{[['tap',[['toIndex',['$event']]]]]}}" class="btn go-home color-base-bg" bindtap="__e">{{$root.m2}}</view></view></view>