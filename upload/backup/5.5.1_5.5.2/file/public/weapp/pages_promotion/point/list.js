require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/point/list"],{"0410":function(t,e,i){"use strict";i.r(e);var n=i("be48"),o=i("e01c");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("be3f"),i("ffb7");var a=i("828b"),u=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=u.exports},"390f":function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("d381");n(i("3240"));var o=n(i("0410"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"8e77":function(t,e,i){},ba6a:function(t,e,i){},be3f:function(t,e,i){"use strict";var n=i("8e77"),o=i.n(n);o.a},be48:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return n}));var n={uniPopup:function(){return i.e("components/uni-popup/uni-popup").then(i.bind(null,"d745"))},loadingCover:function(){return i.e("components/loading-cover/loading-cover").then(i.bind(null,"c003"))},nsLogin:function(){return Promise.all([i.e("common/vendor"),i.e("components/ns-login/ns-login")]).then(i.bind(null,"2910"))},hoverNav:function(){return i.e("components/hover-nav/hover-nav").then(i.bind(null,"c1f1"))}},o=function(){var t=this,e=t.$createElement,i=(t._self._c,t.$util.img("public/uniapp/point/navbar-sing-icon.png")),n=t.$util.img("public/uniapp/point/point-icon.png"),o=t.$util.img("/public/uniapp/point/point-rule.png"),s=t.$util.img("/public/uniapp/point/must-see.png"),a=t.$util.img("/public/uniapp/point/recharge.png"),u=t.$util.img("/public/uniapp/point/high.png"),r=t.$util.img("/public/uniapp/point/exchange-record.png"),c=t.$util.img("/public/uniapp/point/luck-draw.png"),l=t.$util.img("/public/uniapp/point/point-detail.png"),p=t.$util.img("/public/uniapp/point/recharge-poster.png"),g=t.$util.img("/public/uniapp/point/luck-draw-poster.png"),d=t.rechargeList.length,h=d?t.__map(t.rechargeList.slice(0,4),(function(e,i){var n=t.__get_orig(e),o=parseFloat(e.buy_price);return{$orig:n,m0:o}})):null,m=t.couponList.length,f=m>0?t.couponList.length:null,_=m>0?t.__map(t.couponList,(function(e,i){var n=t.__get_orig(e),o=t.$util.img("public/uniapp/point/coupon_"+t.themeStyle.name+"_bg1.png"),s=t.$util.img("public/uniapp/point/coupon_theme-blue_bg1.jpg"),a="reward"==e.coupon_type?parseFloat(e.money):null,u="discount"==e.coupon_type?parseFloat(e.discount):null,r=0!=e.at_least?parseFloat(e.at_least).toFixed(0):null;return{$orig:n,g14:o,g15:s,m1:a,m2:u,g16:r}})):null,b=t.hongbaoList.length,$=b>0?t.__map(t.hongbaoList,(function(e,i){var n=t.__get_orig(e),o=t.$util.img("public/uniapp/point/hongbao_bg.png"),s=parseFloat(e.balance).toFixed(0);return{$orig:n,g18:o,g19:s}})):null,v=t.goodsList.length,w=v>0?t.goodsList.length:null,L=v>0&&w?t.__map(t.goodsList,(function(e,i){var n=t.__get_orig(e),o=t.goodsImg(e),s=e.price>0&&e.pay_type>0?parseFloat(e.price).toFixed(2).split("."):null,a=e.price>0&&e.pay_type>0?parseFloat(e.price).toFixed(2).split("."):null,u=(e.stock_show||e.sale_show)&&e.stock_show?isNaN(parseInt(e.stock)):null,r=(e.stock_show||e.sale_show)&&e.stock_show&&!u?parseInt(e.stock):null,c=(e.stock_show||e.sale_show)&&e.sale_show?isNaN(parseInt(e.sale_num)):null,l=(e.stock_show||e.sale_show)&&e.sale_show&&!c?parseInt(e.sale_num):null;return{$orig:n,m3:o,g22:s,g23:a,m4:u,m5:r,m6:c,m7:l}})):null;t._isMounted||(t.e0=function(e){return t.$util.redirectTo("/pages_tool/goods/search")}),t.$mp.data=Object.assign({},{$root:{g0:i,g1:n,g2:o,g3:s,g4:a,g5:u,g6:r,g7:c,g8:l,g9:p,g10:g,g11:d,l0:h,g12:m,g13:f,l1:_,g17:b,l2:$,g20:v,g21:w,l3:L}})},s=[]},d94d:function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={components:{uniPopup:function(){i.e("components/uni-popup/uni-popup").then(function(){return resolve(i("d745"))}.bind(null,i)).catch(i.oe)}},data:function(){return{mescroll:{num:0,total:1,loading:!1},categoryList:[{id:1,name:"积分换好物"},{id:2,name:"积分换券"},{id:3,name:"积分换红包"}],isLogin:!1,goodsList:[],couponList:[],hongbaoList:[],point:0,signState:1,mpShareData:null,menuButtonBounding:{bottom:0},rechargeList:[],newestGame:null}},onLoad:function(e){var i=this;if(setTimeout((function(){i.addonIsExist.pointexchange||(i.$util.showToast({title:"商家未开启积分商城",mask:!0,duration:2e3}),setTimeout((function(){i.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.menuButtonBounding=t.getMenuButtonBoundingClientRect(),e.source_member&&t.setStorageSync("source_member",e.source_member),e.scene){var n=decodeURIComponent(e.scene);n=n.split("&"),n.length&&n.forEach((function(e){-1!=e.indexOf("sku_id")&&(i.skuId=e.split("-")[1]),-1!=e.indexOf("m")&&t.setStorageSync("source_member",e.split("-")[1]),-1!=e.indexOf("is_test")&&t.setStorageSync("is_test",1)}))}this.getData(),this.getRechargeList(),this.getNewestGame()},onShow:function(){var e=this;this.storeToken&&t.getStorageSync("source_member")&&this.$util.onSourceMember(t.getStorageSync("source_member")),this.$util.getMpShare().then((function(t){e.mpShareData=t})),this.storeToken&&this.getAccountInfo(),this.getCouponList(),this.getHongbaoList(),this.getSignState()},methods:{getSignState:function(){var t=this;this.$api.sendRequest({url:"/api/membersignin/getSignStatus",success:function(e){0==e.code&&(t.signState=e.data.is_use)}})},jumpPage:function(t){this.$util.redirectTo(t)},openPointPopup:function(){this.$refs.pointPopup.open()},closePointPopup:function(){this.$refs.pointPopup.close()},getCouponList:function(){var t=this;this.$api.sendRequest({url:"/pointexchange/api/goods/page",data:{page_size:0,type:2},success:function(e){0==e.code&&e.data?t.couponList=e.data.list:t.$util.showToast({title:e.message}),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(){this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},getHongbaoList:function(){var t=this;this.$api.sendRequest({url:"/pointexchange/api/goods/page",data:{page_size:0,type:3},success:function(e){0==e.code&&e.data?t.hongbaoList=e.data.list:t.$util.showToast({title:e.message}),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(){this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},getData:function(){var t=this;this.mescroll.loading||this.mescroll.num>=this.mescroll.total||(this.mescroll.loading=!0,this.$api.sendRequest({url:"/pointexchange/api/goods/page",data:{page:this.mescroll.num+1,page_size:10,type:1},success:function(e){var i=[],n=e.message;0==e.code&&e.data?i=e.data.list:t.$util.showToast({title:n}),t.mescroll.loading=!1,t.mescroll.total=e.data.page_count,t.mescroll.num+=1,1==t.mescroll.num&&(t.goodsList=[]),t.goodsList=t.goodsList.concat(i),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(){this.mescroll.loading=!1,this.$refs.loadingCover&&this.$refs.loadingCover.hide()}}))},toDetail:function(t){this.$util.redirectTo("/pages_promotion/point/detail",{id:t.id})},goGoodsList:function(){this.$util.redirectTo("/pages_promotion/point/goods_list")},getAccountInfo:function(){var t=this;this.$api.sendRequest({url:"/api/memberaccount/info",data:{account_type:"point"},success:function(e){0==e.code&&e.data?isNaN(parseFloat(e.data.point))||(t.point=parseFloat(e.data.point).toFixed(0)):t.$util.showToast({title:e.message})}})},login:function(){this.$refs.login.open("/pages_promotion/point/list")},imgError:function(t){this.goodsList[t].image=this.$util.getDefaultImage().goods,this.$forceUpdate()},goodsImg:function(t){var e="";switch(t.type){case 1:e=this.$util.img(t.image.split(",")[0],{size:"mid"});break;case 2:e=t.image?this.$util.img(t.image):this.$util.img("public/uniapp/point/coupon.png");break;case 3:e=t.image?this.$util.img(t.image):this.$util.img("public/uniapp/point/hongbao.png");break}return e},redirect:function(t){this.storeToken?this.$util.redirectTo(t):this.$refs.login.open(t)},getRechargeList:function(){var t=this;this.$api.sendRequest({url:"/memberrecharge/api/memberrecharge/page",data:{page_size:100,page:1},success:function(e){if(0==e.code&&e.data){var i=[];e.data.list.forEach((function(t){t.point>0&&i.push(t)})),t.rechargeList=i}}})},back:function(){getCurrentPages().length>1?t.navigateBack({delta:1}):this.$util.redirectTo("/pages/index/index")},getNewestGame:function(){var t=this;this.$api.sendRequest({url:"/api/game/newestgame",success:function(e){0==e.code&&e.data&&(t.newestGame=e.data)}})},luckdraw:function(){if(this.newestGame)switch(this.newestGame.game_type){case"cards":this.$util.redirectTo("/pages_promotion/game/cards",{id:this.newestGame.game_id});break;case"egg":this.$util.redirectTo("/pages_promotion/game/smash_eggs",{id:this.newestGame.game_id});break;case"turntable":this.$util.redirectTo("/pages_promotion/game/turntable",{id:this.newestGame.game_id});break}else this.$util.showToast({title:"暂无相关活动"})}},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine}};e.default=n}).call(this,i("df3c")["default"])},e01c:function(t,e,i){"use strict";i.r(e);var n=i("d94d"),o=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=o.a},ffb7:function(t,e,i){"use strict";var n=i("ba6a"),o=i.n(n);o.a}},[["390f","common/runtime","common/vendor"]]]);