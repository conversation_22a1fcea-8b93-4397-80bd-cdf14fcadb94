<page-meta page-style="{{themeColor}}"></page-meta><view class="conteiner"><view class="point-navbar" style="{{'padding-top:'+(menuButtonBounding.top+'px')+';'+('height:'+(menuButtonBounding.height+'px')+';')}}"><view class="nav-wrap" style="{{'width:'+(menuButtonBounding.left+'px')+';'}}"><view data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="back" style="{{'width:'+(menuButtonBounding.height+'px')+';'+('height:'+(menuButtonBounding.height+'px')+';')}}" bindtap="__e"><text class="iconfont icon-back_light"></text></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="search" bindtap="__e"><text class="iconfont icon-sousuo3"></text><text class="tips">搜索商品</text></view><view data-event-opts="{{[['tap',[['redirect',['/pages_tool/member/signin']]]]]}}" class="sign" style="{{'width:'+(menuButtonBounding.height+'px')+';'+('height:'+(menuButtonBounding.height+'px')+';')}}" bindtap="__e"><image src="{{$root.g0}}" mode="widthFix"></image></view></view></view><view class="point-navbar-block" style="{{'height:'+(menuButtonBounding.bottom+'px')+';'}}"></view><scroll-view class="point-scroll-view" scroll-y="true" data-event-opts="{{[['scrolltolower',[['getData',['$event']]]]]}}" bindscrolltolower="__e"><view class="point-wrap" style="{{'background-position-y:'+(-menuButtonBounding.bottom+'px')+';'}}"><view class="head-box"><view class="account-content"><view class="left"><image src="{{$root.g1}}" mode="widthFix"></image><view>我的积分</view></view><view data-event-opts="{{[['tap',[['redirect',['/pages_tool/member/point']]]]]}}" class="right" bindtap="__e"><text class="point price-font">{{point}}</text><text class="text">积分</text><text class="iconfont icon-right"></text></view></view><view class="remark"><view class="label">提醒</view><view class="text">积分兑好礼，每日上新换不停！</view></view></view><view class="menu-wrap"><view class="menu-list"><view data-event-opts="{{[['tap',[['openPointPopup']]]]}}" class="menu-item" bindtap="__e"><image class="menu-img" src="{{$root.g2}}"></image><image class="menu-tag" src="{{$root.g3}}"></image><view class="title">活动规则</view></view><view data-event-opts="{{[['tap',[['redirect',['/pages_tool/recharge/list']]]]]}}" class="menu-item" bindtap="__e"><image class="menu-img" src="{{$root.g4}}"></image><image class="menu-tag" src="{{$root.g5}}"></image><view class="title">储值赚积分</view></view><view data-event-opts="{{[['tap',[['redirect',['/pages_promotion/point/order_list']]]]]}}" class="menu-item" bindtap="__e"><image class="menu-img" src="{{$root.g6}}"></image><view class="title">兑换记录</view></view><view data-event-opts="{{[['tap',[['luckdraw',['$event']]]]]}}" class="menu-item" bindtap="__e"><image class="menu-img" src="{{$root.g7}}"></image><view class="title">积分抽奖</view></view><view data-event-opts="{{[['tap',[['redirect',['/pages_tool/member/point_detail']]]]]}}" class="menu-item" bindtap="__e"><image class="menu-img" src="{{$root.g8}}"></image><view class="title">积分明细</view></view></view></view><view class="poster-wrap"><view data-event-opts="{{[['tap',[['redirect',['/pages_tool/recharge/list']]]]]}}" class="poster-item" bindtap="__e"><image src="{{$root.g9}}" mode="widthFix"></image></view><view data-event-opts="{{[['tap',[['luckdraw',['$event']]]]]}}" class="poster-item" bindtap="__e"><image src="{{$root.g10}}" mode="widthFix"></image></view></view><block wx:if="{{$root.g11}}"><view data-event-opts="{{[['tap',[['redirect',['/pages_tool/recharge/list']]]]]}}" class="recharge-list-wrap" bindtap="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item-wrap"><view class="recharge">{{"储值"+item.m0+"元"}}</view><view class="point">{{"可得"+item.$orig.point+"积分"}}</view><view class="btn">去储值</view></view></block></view></block><view class="{{['body-wrap',(!storeToken)?'no-login':'']}}"><block wx:if="{{$root.g12>0}}"><view class="point-exchange-wrap exchange-coupon"><view class="card-category-title"><text class="before-line"></text><text>积分换券</text><text class="after-line"></text></view><view class="list-wrap"><view class="{{['list-wrap-scroll',($root.g13<3)?'single-row':'']}}"><block wx:for="{{$root.l1}}" wx:for-item="couponItem" wx:for-index="couponIndex" wx:key="couponIndex"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['couponList','',couponIndex]]]]]]]}}" class="list-wrap-item coupon-list-wrap-item" bindtap="__e"><view class="img-box"><image src="{{couponItem.g14}}"></image></view><view class="content"><view class="coupon" style="{{'background-image:'+('url('+couponItem.g15+')')+';'}}"><view class="coupon_left color-line-border"><view class="price price-font"><block wx:if="{{couponItem.$orig.coupon_type=='reward'}}"><block><text>￥</text>{{''+couponItem.m1+''}}</block></block><block wx:if="{{couponItem.$orig.coupon_type=='discount'}}"><block>{{''+couponItem.m2}}<text>折</text></block></block></view><view class="coupon-info"><view class="coupon_condition font-size-activity-tag">{{''+(couponItem.$orig.at_least==0?'无门槛优惠券':'满'+couponItem.g16+'可用')+''}}</view><block wx:if="{{couponItem.$orig.goods_type==1}}"><view class="coupon_type font-size-activity-tag">全场券</view></block><block wx:else><block wx:if="{{couponItem.$orig.goods_type==2||couponItem.$orig.goods_type==3}}"><view class="coupon_type font-size-activity-tag">指定券</view></block></block></view></view><view class="coupon_right"><view class="coupon_num font-size-tag">{{couponItem.$orig.point+"积分"}}</view><view class="coupon_btn">兑换</view></view></view></view></view></block></view></view></view></block><block wx:if="{{$root.g17>0}}"><view class="point-exchange-wrap exchange-hongbao"><view class="card-category-title"><text class="before-line"></text><text>积分换红包</text><text class="after-line"></text></view><view class="list-wrap"><block wx:for="{{$root.l2}}" wx:for-item="hongbaoItem" wx:for-index="hongbaoIndex" wx:key="hongbaoIndex"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['hongbaoList','',hongbaoIndex]]]]]]]}}" class="list-wrap-item hongbao-list-wrap-item" bindtap="__e"><view class="img-box"><image src="{{hongbaoItem.g18}}"></image></view><view class="content"><view class="coupon hongbao"><view class="coupon_left"><view class="price price-font"><text>￥</text>{{''+hongbaoItem.g19+''}}</view></view><view class="coupon_right"><view class="coupon_num font-size-tag">{{hongbaoItem.$orig.point+"积分"}}</view><view class="coupon_btn">兑换</view></view></view></view></view></block></view></view></block><block wx:if="{{$root.g20>0}}"><view class="point-exchange-wrap"><view class="card-category-title"><text class="before-line"></text><text>积分换礼品</text><text class="after-line"></text></view><view class="list-wrap"><block wx:if="{{$root.g21}}"><view class="goods-list double-column"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="goods-img" bindtap="__e"><image src="{{item.m3}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image></view><view class="info-wrap"><view class="name-wrap"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="goods-name" bindtap="__e">{{item.$orig.name}}</view></view><view class="lineheight-clear"><view class="discount-price"><view><text class="unit price-font point">{{item.$orig.point}}</text><text class="unit font-size-tag">积分</text></view><block wx:if="{{item.$orig.price>0&&item.$orig.pay_type>0}}"><block><text class="unit font-size-tag">+</text><view><text class="font-size-tag">{{item.g22[0]}}</text><text class="unit font-size-tag">{{"."+item.g23[1]+"元"}}</text></view></block></block></view><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="btn" bindtap="__e">兑换</view></view><block wx:if="{{item.$orig.stock_show||item.$orig.sale_show}}"><view class="pro-info"><block wx:if="{{item.$orig.stock_show}}"><view class="font-size-activity-tag color-tip">{{'库存:'+(item.m4?0:item.m5)}}</view></block><block wx:if="{{item.$orig.sale_show}}"><view class="font-size-activity-tag color-tip sale">{{'已兑:'+(item.m6?0:item.m7)+''}}</view></block></view></block></view></view></block></view></block></view></view></block></view></view></scroll-view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><uni-popup class="vue-ref" vue-id="57c42618-1" type="bottom" data-ref="pointPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="tips-layer"><view data-event-opts="{{[['tap',[['closePointPopup']]]]}}" class="head" bindtap="__e"><view class="title">积分说明</view><text class="iconfont icon-close"></text></view><view class="body"><view class="detail margin-bottom"><view class="tip">积分的获取</view><view class="font-size-base">1、积分可在注册、签到、分享、消费、充值时获得。</view><view class="font-size-base">2、在购买部分商品时可获得积分。</view><view class="tip">积分的使用</view><view class="font-size-base">1、积分可用于兑换积分中心的商品。</view><view class="font-size-base">2、积分可在参与某些活动时使用。</view><view class="font-size-base">3、积分不得转让，出售，不设有效期。</view><view class="tip">积分的查询</view><view class="font-size-base">1、积分可在会员中心中查询具体数额以及明细。</view></view></view></view></uni-popup></view><loading-cover class="vue-ref" vue-id="57c42618-2" data-ref="loadingCover" bind:__l="__l"></loading-cover><ns-login class="vue-ref" vue-id="57c42618-3" data-ref="login" bind:__l="__l"></ns-login><hover-nav vue-id="57c42618-4" bind:__l="__l"></hover-nav><privacy-popup class="vue-ref" vue-id="57c42618-5" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>