<page-meta page-style="{{themeColor}}" class="data-v-762f59ac"></page-meta><view class="order-container data-v-762f59ac"><block wx:if="{{storeToken}}"><view class="order-nav data-v-762f59ac"><block wx:for="{{statusList}}" wx:for-item="statusItem" wx:for-index="statusIndex" wx:key="statusIndex"><view class="uni-tab-item data-v-762f59ac" id="{{statusItem.id}}" data-current="{{statusIndex}}" data-event-opts="{{[['tap',[['ontabtap',['$event']]]]]}}" bindtap="__e"><text class="{{['uni-tab-item-title','data-v-762f59ac',statusItem.status===orderStatus?'uni-tab-item-title-active color-base-border  color-base-text':'']}}">{{''+statusItem.name+''}}</text></view></block></view></block><block wx:if="{{storeToken}}"><mescroll-uni vue-id="3cf60546-1" top="100rpx" data-ref="mescroll" data-event-opts="{{[['^getData',[['getListData']]]]}}" bind:getData="__e" class="data-v-762f59ac vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-762f59ac"><block wx:if="{{$root.g0>0}}"><view class="order-list data-v-762f59ac"><block wx:for="{{$root.l0}}" wx:for-item="orderItem" wx:for-index="orderIndex" wx:key="orderIndex"><view class="order-item data-v-762f59ac"><view class="order-header data-v-762f59ac"><text class="font-size-base data-v-762f59ac">{{"订单号："+orderItem.$orig.order_no}}</text><text class="status-name color-base-text data-v-762f59ac">{{orderItem.$orig.order_status_name}}</text></view><view data-event-opts="{{[['tap',[['orderDetail',['$0'],[[['orderList','',orderIndex]]]]]]]}}" class="order-body data-v-762f59ac" bindtap="__e"><view class="goods-wrap data-v-762f59ac"><view class="goods-img data-v-762f59ac"><image src="{{orderItem.g1}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError',[orderIndex]]]]]}}" binderror="__e" class="data-v-762f59ac"></image></view><view class="goods-info data-v-762f59ac"><view class="pro-info data-v-762f59ac"><view class="goods-name data-v-762f59ac">{{orderItem.$orig.sku_name}}</view></view><view class="goods-sub-section data-v-762f59ac"><text class="goods-price data-v-762f59ac"><text class="unit price-style small data-v-762f59ac">{{orderItem.m0}}</text><text class="price-style large data-v-762f59ac">{{orderItem.g2[0]}}</text><text class="unit price-style small data-v-762f59ac">{{"."+orderItem.g3[1]}}</text></text><text class="goods-num data-v-762f59ac"><text class="iconfont icon-close data-v-762f59ac"></text>{{''+orderItem.$orig.num+''}}</text></view><view class="goods-action data-v-762f59ac"></view></view></view></view><view class="order-footer data-v-762f59ac"><view class="order-base-info data-v-762f59ac"><view class="total data-v-762f59ac"><text class="font-size-sub data-v-762f59ac">{{"共"+orderItem.$orig.num+"件商品"}}</text><block wx:if="{{orderItem.$orig.order_status===0}}"><text class="align-right font-size-base data-v-762f59ac">待付定金：<text class="ns-font-size-lg ns-text-color data-v-762f59ac">{{orderItem.m1+orderItem.$orig.pay_deposit_money}}</text></text></block><block wx:else><block wx:if="{{orderItem.$orig.order_status===1}}"><text class="align-right font-size-base data-v-762f59ac">待付尾款：<text class="ns-font-size-lg ns-text-color data-v-762f59ac">{{orderItem.m2+orderItem.$orig.final_money}}</text></text></block><block wx:else><block wx:if="{{orderItem.$orig.order_status===2}}"><text class="align-right font-size-base data-v-762f59ac">合计：<text class="ns-font-size-lg ns-text-color data-v-762f59ac">{{orderItem.m3+"0.00"}}</text></text></block><block wx:else><block wx:if="{{orderItem.$orig.order_status===-1}}"><text class="align-right font-size-base data-v-762f59ac">合计：<text class="ns-font-size-lg ns-text-color data-v-762f59ac">{{orderItem.m4+orderItem.$orig.order_money}}</text></text></block></block></block></block></view></view><block wx:if="{{orderItem.g4>0}}"><view class="order-action data-v-762f59ac"><block wx:for="{{orderItem.$orig.action}}" wx:for-item="operationItem" wx:for-index="operationIndex" wx:key="operationIndex"><block class="data-v-762f59ac"><block wx:if="{{operationItem.action=='orderPayFinal'}}"><block class="data-v-762f59ac"><block wx:if="{{(orderItem.$orig.refund_status==0||orderItem.$orig.refund_status==-1)&&orderItem.$orig.order_status!=-1}}"><block class="data-v-762f59ac"><block wx:if="{{orderItem.$orig.pay_start_time<timestamp&&orderItem.$orig.pay_end_time>timestamp}}"><view data-event-opts="{{[['tap',[['operation',['$0','$1'],[[['orderList','',orderIndex],['action','',operationIndex,'action']],[['orderList','',orderIndex]]]]]]]}}" class="order-box-btn color-base-border color-base-text data-v-762f59ac" bindtap="__e">{{operationItem.title}}</view></block><block wx:else><view class="order-box-btn disabled data-v-762f59ac">{{operationItem.title}}</view></block></block></block><block wx:else><view class="order-box-btn disabled data-v-762f59ac">{{operationItem.title}}</view></block></block></block><block wx:else><block wx:if="{{operationItem.action=='refundDeposit'}}"><block class="data-v-762f59ac"><block wx:if="{{orderItem.$orig.refund_status==0&&orderItem.$orig.is_deposit_back==0||orderItem.$orig.refund_status==-1&&orderItem.$orig.is_deposit_back==0}}"><view data-event-opts="{{[['tap',[['operation',['$0','$1'],[[['orderList','',orderIndex],['action','',operationIndex,'action']],[['orderList','',orderIndex]]]]]]]}}" class="order-box-btn data-v-762f59ac" bindtap="__e">{{operationItem.title}}</view></block></block></block><block wx:else><view data-event-opts="{{[['tap',[['operation',['$0','$1'],[[['orderList','',orderIndex],['action','',operationIndex,'action']],[['orderList','',orderIndex]]]]]]]}}" class="order-box-btn data-v-762f59ac" bindtap="__e">{{operationItem.title}}</view></block></block></block></block></view></block><block wx:else><view class="order-action data-v-762f59ac"><view data-event-opts="{{[['tap',[['orderDetail',['$0'],[[['orderList','',orderIndex]]]]]]]}}" class="order-box-btn color-base-border color-base-text data-v-762f59ac" bindtap="__e">查看详情</view></view></block></view></view></block></view></block><block wx:else><view class="data-v-762f59ac"><ns-empty vue-id="{{('3cf60546-2')+','+('3cf60546-1')}}" isIndex="{{true}}" emptyBtn="{{({url:'/pages_promotion/presale/list',text:'去逛逛'})}}" text="暂无相关预售订单" class="data-v-762f59ac" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni></block><ns-payment vue-id="3cf60546-3" payMoney="{{payMoney}}" balanceDeduct="{{balanceDeduct}}" isBalance="{{isBalance}}" data-ref="choosePaymentPopup" data-event-opts="{{[['^confirm',[['pay']]],['^useBalance',[['useBalance']]]]}}" bind:confirm="__e" bind:useBalance="__e" class="data-v-762f59ac vue-ref" bind:__l="__l"></ns-payment><ns-login vue-id="3cf60546-4" data-ref="login" class="data-v-762f59ac vue-ref" bind:__l="__l"></ns-login><loading-cover vue-id="3cf60546-5" data-ref="loadingCover" class="data-v-762f59ac vue-ref" bind:__l="__l"></loading-cover></view>