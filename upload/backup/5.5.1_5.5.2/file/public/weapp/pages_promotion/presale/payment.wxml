<page-meta page-style="{{themeColor}}" class="data-v-faafbe52"></page-meta><view class="{{['order-container','data-v-faafbe52',(isIphoneX)?'safe-area':'']}}"><view class="payment-navbar data-v-faafbe52" style="{{'padding-top:'+(menuButtonBounding.top+'px')+';'+('height:'+(menuButtonBounding.height+'px')+';')}}"><view class="nav-wrap data-v-faafbe52"><text data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="iconfont icon-back_light data-v-faafbe52" bindtap="__e"></text><view class="navbar-title data-v-faafbe52">确认订单</view></view></view><view class="payment-navbar-block data-v-faafbe52" style="{{'height:'+(menuButtonBounding.bottom+'px')+';'}}"></view><scroll-view class="order-scroll-container data-v-faafbe52" scroll-y="true"><view class="payment-navbar-block data-v-faafbe52"></view><block wx:if="{{orderPaymentData.is_virtual==0}}"><block wx:if="{{$root.g0>1}}"><view class="delivery-mode data-v-faafbe52"><view class="action data-v-faafbe52"><block wx:for="{{orderPaymentData.delivery.express_type}}" wx:for-item="deliveryItem" wx:for-index="deliveryIndex" wx:key="deliveryIndex"><view data-event-opts="{{[['tap',[['selectDeliveryType',['$0'],[[['orderPaymentData.delivery.express_type','',deliveryIndex]]]]]]]}}" class="{{['data-v-faafbe52',(deliveryItem.name==orderCreateData.delivery.delivery_type)?'active':'']}}" bindtap="__e">{{''+deliveryItem.title+''}}<view class="out-radio data-v-faafbe52"></view></view></block></view></view></block><block wx:if="{{orderCreateData.delivery.delivery_type!='store'}}"><view class="{{['address-box','data-v-faafbe52',($root.g1<=1)?'not-delivery-type':'']}}"><block wx:if="{{orderCreateData.delivery.delivery_type=='local'}}"><block class="data-v-faafbe52"><block wx:if="{{$root.g2>1}}"><block class="data-v-faafbe52"><block wx:if="{{$root.g3}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="local-delivery-store data-v-faafbe52" bindtap="__e"><view class="info data-v-faafbe52">由<text class="store-name data-v-faafbe52">{{storeInfo.currStore.store_name}}</text>提供配送</view><view class="cell-more data-v-faafbe52"><text class="data-v-faafbe52">点击切换</text><text class="iconfont icon-right data-v-faafbe52"></text></view></view></block><block wx:else><view class="local-delivery-store data-v-faafbe52"><view class="info data-v-faafbe52"><text class="store-name data-v-faafbe52">您的附近没有可配送的门店，请选择其他配送方式</text></view></view></block></block></block><block wx:if="{{localMemberAddress}}"><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="info-wrap local data-v-faafbe52" bindtap="__e"><view class="content data-v-faafbe52"><text class="name font-size-base data-v-faafbe52">{{localMemberAddress.name?localMemberAddress.name:''}}</text><text class="font-size-base mobile data-v-faafbe52">{{localMemberAddress.mobile?localMemberAddress.mobile:''}}</text><text class="cell-more iconfont icon-right data-v-faafbe52"></text><view class="desc-wrap data-v-faafbe52">{{''+(localMemberAddress.full_address?localMemberAddress.full_address:'')+"\n\t\t\t\t\t\t\t\t"+(localMemberAddress.address?localMemberAddress.address:'')+''}}</view></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="empty-wrap data-v-faafbe52" bindtap="__e"><view class="info data-v-faafbe52">请设置收货地址</view><view class="cell-more data-v-faafbe52"><view class="iconfont icon-right data-v-faafbe52"></view></view></view></block></block></block><block wx:if="{{orderCreateData.delivery.delivery_type=='express'}}"><block class="data-v-faafbe52"><block wx:if="{{memberAddress}}"><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="info-wrap data-v-faafbe52" bindtap="__e"><view class="content data-v-faafbe52"><text class="name font-size-base data-v-faafbe52">{{memberAddress.name?memberAddress.name:''}}</text><text class="font-size-base mobile data-v-faafbe52">{{memberAddress.mobile?memberAddress.mobile:''}}</text><text class="cell-more iconfont icon-right data-v-faafbe52"></text><view class="desc-wrap data-v-faafbe52">{{''+(memberAddress.full_address?memberAddress.full_address:'')+"\n\t\t\t\t\t\t\t\t"+(memberAddress.address?memberAddress.address:'')+''}}</view></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="empty-wrap data-v-faafbe52" bindtap="__e"><view class="info data-v-faafbe52">请设置收货地址</view><view class="cell-more data-v-faafbe52"><view class="iconfont icon-right data-v-faafbe52"></view></view></view></block></block></block><image class="address-line data-v-faafbe52" src="{{$root.g4}}"></image></view></block><block wx:if="{{orderCreateData.delivery.delivery_type=='store'}}"><view class="{{['store-box','data-v-faafbe52',($root.g5<=1)?'not-delivery-type':'']}}"><block wx:if="{{storeInfo.currStore}}"><block class="data-v-faafbe52"><view data-event-opts="{{[['tap',[['openSiteDelivery',['$event']]]]]}}" class="store-info data-v-faafbe52" bindtap="__e"><view class="store-address-info data-v-faafbe52"><view class="info-wrap data-v-faafbe52"><view class="title data-v-faafbe52"><text class="data-v-faafbe52">{{storeInfo.currStore.store_name}}</text></view><view class="store-detail data-v-faafbe52"><block wx:if="{{storeInfo.currStore.open_date}}"><view class="data-v-faafbe52">{{'营业时间：'+storeInfo.currStore.open_date}}</view></block><view class="address data-v-faafbe52">{{storeInfo.currStore.full_address+"\n\t\t\t\t\t\t\t\t\t\t"+storeInfo.currStore.address+''}}</view></view></view><view class="cell-more iconfont icon-right data-v-faafbe52"></view></view></view><view class="mobile-wrap store-mobile data-v-faafbe52"><view class="form-group data-v-faafbe52"><text class="text data-v-faafbe52">姓名</text><input class="input data-v-faafbe52" type="text" placeholder-class="color-tip placeholder" disabled="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['member_address']]]]]}}" value="{{member_address.name}}" bindinput="__e"/></view></view><view class="mobile-wrap store-mobile data-v-faafbe52"><view class="form-group data-v-faafbe52"><text class="text data-v-faafbe52">预留手机</text><input class="input data-v-faafbe52" type="number" maxlength="11" placeholder="请输入您的手机号码" placeholder-class="color-tip placeholder" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['member_address']]]]]}}" value="{{member_address.mobile}}" bindinput="__e"/></view></view></block></block><block wx:else><view class="empty data-v-faafbe52">当前无自提门店，请选择其它配送方式</view></block><image class="address-line data-v-faafbe52" src="{{$root.g6}}"></image></view></block></block><block wx:if="{{orderPaymentData.is_virtual==1&&orderCreateData.member_address}}"><view class="mobile-wrap data-v-faafbe52"><view class="tips color-base-text data-v-faafbe52"><text class="iconfont icon-gantanhao data-v-faafbe52"></text>购买虚拟类商品需填写手机号，方便商家与您联系</view><view class="form-group data-v-faafbe52"><text class="iconfont icon-dianhua2 data-v-faafbe52"></text><text class="text data-v-faafbe52">手机号码</text><input class="input data-v-faafbe52" type="number" maxlength="11" placeholder="请输入您的手机号码" placeholder-class="color-tip placeholder" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['orderCreateData.member_address']]]]]}}" value="{{orderCreateData.member_address.mobile}}" bindinput="__e"/></view></view></block><view class="site-wrap order-goods data-v-faafbe52"><view class="site-body data-v-faafbe52"><block wx:for="{{$root.l1}}" wx:for-item="goodsItem" wx:for-index="goodsIndex" wx:key="goodsIndex"><view class="goods-item data-v-faafbe52"><view class="goods-wrap data-v-faafbe52"><view data-event-opts="{{[['tap',[['navigateTo',['$0'],[[['orderPaymentData.goods_list','',goodsIndex,'goods_id']]]]]]]}}" class="goods-img data-v-faafbe52" bindtap="__e"><image src="{{goodsItem.g7}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[goodsIndex]]]]]}}" binderror="__e" class="data-v-faafbe52"></image></view><view class="goods-info data-v-faafbe52"><view class="top-wrap data-v-faafbe52"><view data-event-opts="{{[['tap',[['navigateTo',['$0'],[[['orderPaymentData.goods_list','',goodsIndex,'goods_id']]]]]]]}}" class="goods-name data-v-faafbe52" bindtap="__e">{{goodsItem.$orig.sku_name}}</view><block wx:if="{{goodsItem.$orig.sku_spec_format}}"><view class="sku data-v-faafbe52"><view class="goods-spec data-v-faafbe52"><block wx:for="{{goodsItem.l0}}" wx:for-item="x" wx:for-index="i" wx:key="i"><block class="data-v-faafbe52">{{''+x.$orig.spec_value_name+"\n\t\t\t\t\t\t\t\t\t\t\t"+(i<x.g8-1?'; ':'')+''}}</block></block></view></view></block><block wx:if="{{goodsItem.g9}}"><view class="error-tips data-v-faafbe52"><text class="iconfont icon-gantanhao data-v-faafbe52"></text><text class="data-v-faafbe52">{{"该商品不支持"+orderCreateData.delivery.delivery_type_name}}</text></view></block></view><view class="goods-sub-section data-v-faafbe52"><view class="data-v-faafbe52"><text class="unit data-v-faafbe52">{{$root.m0}}</text><text class="goods-price data-v-faafbe52">{{goodsItem.g10[0]}}</text><text class="unit data-v-faafbe52">{{"."+goodsItem.g11[1]}}</text></view><view class="data-v-faafbe52"><text class="font-size-tag data-v-faafbe52">x</text><text class="font-size-base data-v-faafbe52">{{goodsItem.$orig.num}}</text></view></view></view></view></view></block></view></view><view class="site-wrap buyer-message data-v-faafbe52"><view class="order-cell data-v-faafbe52"><text class="tit data-v-faafbe52">买家留言</text><view data-event-opts="{{[['tap',[['openPopup',['buyerMessagePopup']]]]]}}" class="box text-overflow data-v-faafbe52" bindtap="__e"><block wx:if="{{orderCreateData.buyer_message}}"><text class="data-v-faafbe52">{{orderCreateData.buyer_message}}</text></block><block wx:else><text class="color-sub data-v-faafbe52">无留言</text></block></view><text class="iconfont icon-right data-v-faafbe52"></text></view></view><block wx:if="{{orderPaymentData.invoice.invoice_status==1}}"><view class="site-wrap data-v-faafbe52"><view class="site-footer data-v-faafbe52"><view class="order-cell order-invoice-cell data-v-faafbe52"><text class="tit data-v-faafbe52">发票</text><view data-event-opts="{{[['tap',[['openPopup',['invoicePopup']]]]]}}" class="box text-overflow data-v-faafbe52" bindtap="__e"><block wx:if="{{orderCreateData.is_invoice==1}}"><text class="data-v-faafbe52">{{(orderCreateData.invoice_type==1?'纸质':'电子')+"发票("+orderCreateData.invoice_content+")"}}</text></block><block wx:else><text class="data-v-faafbe52">无需发票</text></block></view><text class="iconfont icon-right data-v-faafbe52"></text></view></view></view></block><view class="order-money data-v-faafbe52"><view class="order-cell data-v-faafbe52"><text class="tit data-v-faafbe52">商品金额</text><view class="box color-title data-v-faafbe52"><text class="unit data-v-faafbe52">{{$root.m1}}</text><text class="money data-v-faafbe52">{{$root.f0}}</text></view></view><block wx:if="{{presaleDiscount>0}}"><view class="order-cell data-v-faafbe52"><text class="tit data-v-faafbe52">定金膨胀</text><view class="box color-base-text data-v-faafbe52"><text class="operator data-v-faafbe52">-</text><text class="unit data-v-faafbe52">{{$root.m2}}</text><text class="money data-v-faafbe52">{{presaleDiscount}}</text></view></view></block><block wx:if="{{orderPaymentData.is_virtual==0&&orderPaymentData.delivery_money>0}}"><view class="order-cell data-v-faafbe52"><text class="tit data-v-faafbe52">运费</text><view class="box color-base-text data-v-faafbe52"><text class="operator data-v-faafbe52">+</text><text class="unit data-v-faafbe52">{{$root.m3}}</text><text class="money data-v-faafbe52">{{$root.f1}}</text></view></view></block><block wx:if="{{orderCreateData.is_invoice&&orderPaymentData.invoice_money>0}}"><view class="order-cell data-v-faafbe52"><text class="tit data-v-faafbe52"><text class="data-v-faafbe52">税费</text><text class="color-base-text font-bold data-v-faafbe52">{{"("+orderPaymentData.invoice.invoice_rate+"%)"}}</text></text><view class="box color-base-text data-v-faafbe52"><text class="operator data-v-faafbe52">+</text><text class="unit data-v-faafbe52">{{$root.m4}}</text><text class="money data-v-faafbe52">{{$root.f2}}</text></view></view></block><block wx:if="{{orderCreateData.is_invoice&&orderPaymentData.invoice_delivery_money>0}}"><view class="order-cell data-v-faafbe52"><text class="tit data-v-faafbe52">发票邮寄费</text><view class="box color-base-text data-v-faafbe52"><text class="operator data-v-faafbe52">+</text><text class="unit data-v-faafbe52">{{$root.m5}}</text><text class="money data-v-faafbe52">{{$root.f3}}</text></view></view></block><block wx:if="{{orderPaymentData.promotion_money>0}}"><view class="order-cell data-v-faafbe52"><text class="tit data-v-faafbe52">优惠</text><view class="box color-base-text data-v-faafbe52"><text class="operator data-v-faafbe52">-</text><text class="unit data-v-faafbe52">{{$root.m6}}</text><text class="money data-v-faafbe52">{{$root.f4}}</text></view></view></block></view><view class="order-money data-v-faafbe52"><block wx:if="{{orderPaymentData.is_virtual==0&&orderPaymentData.presale_info.presale_deposit>0}}"><view class="order-cell data-v-faafbe52"><text class="tit color-base-text data-v-faafbe52">阶段一：付定金</text><view class="box align-right data-v-faafbe52"><text class="ns-text-color-black color-base-text data-v-faafbe52"><text class="font-size-tag data-v-faafbe52">{{$root.m7}}</text><text class="font-size-base data-v-faafbe52">{{$root.f5}}</text></text></view></view></block><view class="order-cell data-v-faafbe52"><text class="tit data-v-faafbe52">阶段二：付尾款</text><view class="box align-right data-v-faafbe52"><text class="data-v-faafbe52"><text class="font-size-tag data-v-faafbe52">{{$root.m8}}</text><text class="font-size-base data-v-faafbe52">{{$root.f6}}</text></text></view></view></view><block wx:if="{{is_deposit_back==1}}"><view class="pre-sale-money data-v-faafbe52"><view class="data-v-faafbe52">我已同意定金不退等预售协议<text data-event-opts="{{[['tap',[['presaleAgreement',['$event']]]]]}}" class="iconfont icon-bangzhu1 data-v-faafbe52" bindtap="__e"></text></view><switch class="balance-switch data-v-faafbe52" checked="{{switch_state}}" data-event-opts="{{[['change',[['switchChange',['$event']]]]]}}" bindchange="__e"></switch></view></block><block wx:if="{{orderPaymentData.delivery&&orderPaymentData.delivery.delivery_type=='local'&&orderPaymentData.delivery&&orderPaymentData.delivery.error&&orderPaymentData.delivery.error!==''}}"><view class="error-message data-v-faafbe52">{{''+orderPaymentData.delivery.error_msg+''}}</view></block><view class="{{['order-submit','data-v-faafbe52',(isIphoneX)?'bottom-safe-area':'']}}"><view class="order-settlement-info data-v-faafbe52"><text class="font-size-base color-tip margin-right data-v-faafbe52">{{"共"+orderPaymentData.goods_num+"件"}}</text><text class="font-size-base data-v-faafbe52">定金：</text><text class="color-base-text unit data-v-faafbe52">{{$root.m9}}</text><text class="color-base-text money data-v-faafbe52">{{$root.f7}}</text></view><view class="submit-btn data-v-faafbe52"><block wx:if="{{$root.m10}}"><button class="mini data-v-faafbe52" type="primary" size="mini" data-event-opts="{{[['tap',[['openChoosePayment']]]]}}" bindtap="__e">提交订单</button></block><block wx:else><button class="no-submit mini data-v-faafbe52" size="mini"><block wx:if="{{orderPaymentData.delivery&&orderPaymentData.delivery.delivery_type=='local'&&orderPaymentData.delivery&&orderPaymentData.delivery.error&&orderPaymentData.delivery.start_money>orderPaymentData.presale_deposit_money}}"><block class="data-v-faafbe52">{{'差'+$root.f8+'起送'}}</block></block><block wx:else><block class="data-v-faafbe52">提交订单</block></block></button></block></view></view><view class="order-submit-block _div data-v-faafbe52"></view></scroll-view><uni-popup vue-id="1f32426a-1" type="center" data-ref="depositRefund" class="data-v-faafbe52 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="depositRefund-wrap data-v-faafbe52"><view class="depositRefund-con data-v-faafbe52">预售商品定金不支持退款,同意后可继续下单。</view><view class="popup-footer data-v-faafbe52"><view data-event-opts="{{[['tap',[['closeDepositRefund',['$event']]]]]}}" class="confirm-btn color-tip data-v-faafbe52" bindtap="__e">我再想想</view><view data-event-opts="{{[['tap',[['toPayOrder',['$event']]]]]}}" class="confirm-btn color-base-bg data-v-faafbe52" bindtap="__e">同意并下单</view></view></view></uni-popup><uni-popup vue-id="1f32426a-2" type="center" data-ref="presaleAgreement" class="data-v-faafbe52 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="agreement-wrap data-v-faafbe52"><view class="agreement-title font-size-toolbar data-v-faafbe52">定金不退协议</view><view class="agreement-con data-v-faafbe52"><block wx:if="{{orderPaymentData.presale_info}}">{{''+orderPaymentData.presale_info.deposit_agreement+''}}</block><block wx:else><text class="color-tip data-v-faafbe52">暂无退定金协议</text></block></view><view class="popup-footer data-v-faafbe52"><view data-event-opts="{{[['tap',[['closePresaleAgreement',['$event']]]]]}}" class="confirm-btn color-base-bg data-v-faafbe52" bindtap="__e">确定</view></view></view></uni-popup><uni-popup vue-id="1f32426a-3" type="bottom" data-ref="buyerMessagePopup" class="data-v-faafbe52 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="buyermessag-popup popup data-v-faafbe52" style="height:auto;" catchtouchmove="__e"><view class="popup-header data-v-faafbe52"><text class="tit data-v-faafbe52">买家留言</text><text data-event-opts="{{[['tap',[['closePopup',['buyerMessagePopup']]]]]}}" class="iconfont icon-close data-v-faafbe52" bindtap="__e"></text></view><scroll-view class="{{['popup-body','data-v-faafbe52',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view class="data-v-faafbe52"><view class="buyermessag-cell data-v-faafbe52"><view class="buyermessag-form-group data-v-faafbe52"><textarea type="text" maxlength="100" placeholder="留言前建议先与商家协调一致" placeholder-class="color-tip" data-event-opts="{{[['input',[['__set_model',['$0','buyer_message','$event',[]],['orderCreateData']]]]]}}" value="{{orderCreateData.buyer_message}}" bindinput="__e" class="data-v-faafbe52"></textarea></view></view></view></scroll-view><view data-event-opts="{{[['tap',[['saveBuyerMessage',['$event']]]]]}}" class="{{['popup-footer','data-v-faafbe52',(isIphoneX)?'bottom-safe-area':'']}}" bindtap="__e"><view class="confirm-btn color-base-bg data-v-faafbe52">确定</view></view></view></uni-popup><uni-popup vue-id="1f32426a-4" type="bottom" data-ref="invoicePopup" class="data-v-faafbe52 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="invoice-popup popup data-v-faafbe52" style="{{(orderCreateData.is_invoice==1?'height: 83vh;':'height: 48vh;')}}" catchtouchmove="__e"><view class="popup-header data-v-faafbe52"><text class="tit data-v-faafbe52">发票</text><text data-event-opts="{{[['tap',[['closeInvoicePopup']]]]}}" class="iconfont icon-close data-v-faafbe52" bindtap="__e"></text></view><scroll-view class="{{['popup-body','data-v-faafbe52',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view class="data-v-faafbe52"><block wx:if="{{orderPaymentData.invoice}}"><view class="invoice-cell data-v-faafbe52"><text class="tit data-v-faafbe52">需要发票</text><view class="option-grpup data-v-faafbe52"><view data-event-opts="{{[['tap',[['changeIsInvoice',['$event']]]]]}}" class="{{['option-item','data-v-faafbe52',(orderCreateData.is_invoice==0)?'color-base-bg active':'']}}" bindtap="__e">不需要</view><view data-event-opts="{{[['tap',[['changeIsInvoice',['$event']]]]]}}" class="{{['option-item','data-v-faafbe52',(orderCreateData.is_invoice==1)?'color-base-bg active':'']}}" bindtap="__e">需要</view></view></view></block><block wx:if="{{orderCreateData.is_invoice==1}}"><block class="data-v-faafbe52"><view class="invoice-cell data-v-faafbe52"><text class="tit data-v-faafbe52">发票类型</text><view class="option-grpup data-v-faafbe52"><block wx:for="{{orderPaymentData.invoice.invoice_type}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeInvoiceType',['$0'],[[['orderPaymentData.invoice.invoice_type','',index]]]]]]]}}" class="{{['option-item','data-v-faafbe52',(orderCreateData.invoice_type==item)?'color-base-bg active':'']}}" bindtap="__e">{{''+(item==1?'纸质':'电子')+''}}</view></block></view></view><view class="invoice-cell data-v-faafbe52"><text class="tit data-v-faafbe52">抬头类型</text><view class="option-grpup data-v-faafbe52"><view data-event-opts="{{[['tap',[['changeInvoiceTitleType',[1]]]]]}}" class="{{['option-item','data-v-faafbe52',(orderCreateData.invoice_title_type==1)?'color-base-bg active':'']}}" bindtap="__e">个人</view><view data-event-opts="{{[['tap',[['changeInvoiceTitleType',[2]]]]]}}" class="{{['option-item','data-v-faafbe52',(orderCreateData.invoice_title_type==2)?'color-base-bg active':'']}}" bindtap="__e">企业</view></view></view><view class="invoice-cell data-v-faafbe52"><text class="tit data-v-faafbe52">发票信息</text><view class="invoice-form-group data-v-faafbe52"><input type="text" placeholder="请填写抬头名称" data-event-opts="{{[['input',[['__set_model',['$0','invoice_title','$event',['trim']],['orderCreateData']]]],['blur',[['$forceUpdate']]]]}}" value="{{orderCreateData.invoice_title}}" bindinput="__e" bindblur="__e" class="data-v-faafbe52"/><block wx:if="{{orderCreateData.invoice_title_type==2}}"><input type="text" placeholder="请填写纳税人识别号" data-event-opts="{{[['input',[['__set_model',['$0','taxpayer_number','$event',['trim']],['orderCreateData']]]],['blur',[['$forceUpdate']]]]}}" value="{{orderCreateData.taxpayer_number}}" bindinput="__e" bindblur="__e" class="data-v-faafbe52"/></block><block wx:if="{{orderCreateData.invoice_type==1}}"><input type="text" placeholder="请填写邮寄地址" data-event-opts="{{[['input',[['__set_model',['$0','invoice_full_address','$event',['trim']],['orderCreateData']]]],['blur',[['$forceUpdate']]]]}}" value="{{orderCreateData.invoice_full_address}}" bindinput="__e" bindblur="__e" class="data-v-faafbe52"/></block><block wx:if="{{orderCreateData.invoice_type==2}}"><input type="text" placeholder="请填写邮箱" data-event-opts="{{[['input',[['__set_model',['$0','invoice_email','$event',['trim']],['orderCreateData']]]],['blur',[['$forceUpdate']]]]}}" value="{{orderCreateData.invoice_email}}" bindinput="__e" bindblur="__e" class="data-v-faafbe52"/></block></view></view><view class="invoice-cell data-v-faafbe52"><text class="tit data-v-faafbe52">发票内容</text><view class="option-grpup data-v-faafbe52"><block wx:for="{{orderPaymentData.invoice.invoice_content_array}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeInvoiceContent',['$0'],[[['orderPaymentData.invoice.invoice_content_array','',index]]]]]]]}}" class="{{['option-item','content','data-v-faafbe52',(item==orderCreateData.invoice_content)?'color-base-bg active':'']}}" bindtap="__e">{{''+item+''}}</view></block></view></view></block></block><view class="invoice-tops data-v-faafbe52">发票内容将以根据税法调整，具体请以展示为准，发票内容显示详细商品名 称及价格信息</view></view></scroll-view><view data-event-opts="{{[['tap',[['saveInvoice',['$event']]]]]}}" class="{{['popup-footer','data-v-faafbe52',(isIphoneX)?'bottom-safe-area':'']}}" bindtap="__e"><view class="confirm-btn color-base-bg data-v-faafbe52">确定</view></view></view></uni-popup><uni-popup vue-id="1f32426a-5" type="bottom" data-ref="deliveryPopup" class="data-v-faafbe52 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="delivery-popup popup data-v-faafbe52"><view class="popup-header data-v-faafbe52"><text class="tit data-v-faafbe52">已为您甄选出附近所有相关门店</text><text data-event-opts="{{[['tap',[['closePopup',['deliveryPopup']]]]]}}" class="iconfont icon-close data-v-faafbe52" bindtap="__e"></text></view><view class="{{['popup-body','store-popup','data-v-faafbe52',(isIphoneX)?'safe-area':'']}}"><view class="delivery-content data-v-faafbe52"><block wx:for="{{storeInfo.storeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectPickupPoint',['$0'],[[['storeInfo.storeList','',index]]]]]]]}}" class="item-wrap data-v-faafbe52" bindtap="__e"><view class="detail data-v-faafbe52"><view class="{{['name','data-v-faafbe52',item.store_id==orderPaymentData.delivery.store_id?'color-base-text':'']}}"><text class="data-v-faafbe52">{{item.store_name}}</text><block wx:if="{{item.distance}}"><text class="data-v-faafbe52">{{"("+item.distance+"km)"}}</text></block></view><view class="info data-v-faafbe52"><view class="{{['font-size-goods-tag','data-v-faafbe52',item.store_id==orderPaymentData.delivery.store_id?'color-base-text':'']}}">{{'营业时间：'+item.open_date+''}}</view><view class="{{['font-size-goods-tag','data-v-faafbe52',item.store_id==orderPaymentData.delivery.store_id?'color-base-text':'']}}">{{'地址：'+item.full_address+item.address+''}}</view></view></view><block wx:if="{{item.store_id==orderPaymentData.delivery.store_id}}"><view class="icon data-v-faafbe52"><text class="iconfont icon-yuan_checked color-base-text data-v-faafbe52"></text></view></block></view></block><block wx:if="{{!storeInfo.storeList}}"><view class="empty data-v-faafbe52">所选择收货地址附近没有可以自提的门店</view></block></view></view></view></uni-popup><ns-payment vue-id="1f32426a-6" isBalance="{{orderCreateData.is_balance}}" isPayPassWord="{{orderPaymentData.member_account.is_pay_password}}" balanceDeduct="{{orderPaymentData.order_money>0&&orderPaymentData.member_account.balance_total>0?balanceDeduct:'0'}}" payMoney="{{orderPaymentData.pay_money}}" data-ref="choosePaymentPopup" data-event-opts="{{[['^useBalance',[['useBalance']]],['^confirm',[['orderCreate']]]]}}" bind:useBalance="__e" bind:confirm="__e" class="data-v-faafbe52 vue-ref" bind:__l="__l"></ns-payment><loading-cover vue-id="1f32426a-7" data-ref="loadingCover" class="data-v-faafbe52 vue-ref" bind:__l="__l"></loading-cover></view>