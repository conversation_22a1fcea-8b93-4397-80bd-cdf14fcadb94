require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/presale/order_list"],{"32b0":function(e,t,a){"use strict";var r=a("f0e2"),n=a.n(r);n.a},"6c65":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return r}));var r={nsEmpty:function(){return a.e("components/ns-empty/ns-empty").then(a.bind(null,"52a6"))},nsPayment:function(){return a.e("components/ns-payment/ns-payment").then(a.bind(null,"7aec"))},nsLogin:function(){return Promise.all([a.e("common/vendor"),a.e("components/ns-login/ns-login")]).then(a.bind(null,"2910"))},loadingCover:function(){return a.e("components/loading-cover/loading-cover").then(a.bind(null,"c003"))}},n=function(){var e=this,t=e.$createElement,a=(e._self._c,e.storeToken?e.orderList.length:null),r=e.storeToken&&a>0?e.__map(e.orderList,(function(t,a){var r=e.__get_orig(t),n=e.$util.img(t.sku_image,{size:"mid"}),s=e.$lang("common.currencySymbol"),o=parseFloat(t.price).toFixed(2).split("."),i=parseFloat(t.price).toFixed(2).split("."),u=0===t.order_status?e.$lang("common.currencySymbol"):null,c=0!==t.order_status&&1===t.order_status?e.$lang("common.currencySymbol"):null,l=0!==t.order_status&&1!==t.order_status&&2===t.order_status?e.$lang("common.currencySymbol"):null,d=0!==t.order_status&&1!==t.order_status&&2!==t.order_status&&-1===t.order_status?e.$lang("common.currencySymbol"):null,m=t.action.length;return{$orig:r,g1:n,m0:s,g2:o,g3:i,m1:u,m2:c,m3:l,m4:d,g4:m}})):null;e.$mp.data=Object.assign({},{$root:{g0:a,l0:r}})},s=[]},"75a5":function(e,t,a){"use strict";var r=a("7879"),n=a.n(r);n.a},7879:function(e,t,a){},"78a7":function(e,t,a){"use strict";a.r(t);var r=a("82d9"),n=a.n(r);for(var s in r)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(s);t["default"]=n.a},"82d9":function(e,t,a){"use strict";var r=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("870a")),s={data:function(){return{scrollInto:"",orderStatus:"",statusList:[],orderList:[],contentText:{},isIphoneX:!1,orderData:null,payMoney:0,payType:"",timestamp:0,memberBalance:0,isBalance:0,isSub:!1}},mixins:[n.default],onLoad:function(e){e.status&&(this.orderStatus=e.status)},computed:{balanceDeduct:function(){if(this.orderData&&1==this.orderData.order_status&&""==this.orderData.final_out_trade_no&&this.memberBalance>0){var e=this.orderData.order_money-this.orderData.presale_deposit_money;return(this.memberBalance>e?e:this.memberBalance).toFixed(2)}return 0}},onShow:function(){var e=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getOrderStatus(),this.storeToken?this.getMemberBalance():this.$nextTick((function(){e.$refs.login.open("/pages_promotion/presale/order_list")}))},methods:{getMemberBalance:function(){var e=this;this.$api.sendRequest({url:"/api/memberaccount/info",data:{account_type:"balance,balance_money"},success:function(t){t.data&&(e.memberBalance=parseFloat(t.data.balance)+parseFloat(t.data.balance_money))}})},ontabtap:function(e){var t=e.target.dataset.current||e.currentTarget.dataset.current;this.orderStatus=this.statusList[t].status,this.$refs.loadingCover.show(),this.$refs.mescroll.refresh()},getListData:function(e){var t=this;this.$api.sendRequest({url:"/presale/api/order/page",data:{page:e.num,page_size:e.size,order_status:this.orderStatus},success:function(a){t.timestamp=a.timestamp;var r=[],n=a.message;0==a.code&&a.data?r=a.data.list:t.$util.showToast({title:n}),e.endSuccess(r.length),1==e.num&&(t.orderList=[]),t.orderList=t.orderList.concat(r),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(a){e.endErr(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},getOrderStatus:function(){this.statusList=[{status:"",name:"全部",id:"status_0"},{status:0,name:"待付款",id:"status_0"},{status:1,name:"待付尾款",id:"status_1"},{status:2,name:"已完成",id:"status_2"},{status:-1,name:"已关闭",id:"status_3"}]},operation:function(e,t){var a=this;switch(e){case"deleteOrder":this.deleteOrder(t.id,(function(){a.$refs.mescroll.refresh()}));break;case"orderClose":this.orderClose(t.id,(function(){a.$refs.mescroll.refresh()}));break;case"orderPayDeposit":this.orderData=t,this.openPaymentPopup(t,"presale_deposit_money");break;case"refundDeposit":this.refundDeposit(t.id,(function(){a.$refs.mescroll.refresh()}));break;case"orderPayFinal":this.orderData=t,this.openPaymentPopup(t,"final_money");break}},orderDetail:function(e){this.$util.redirectTo("/pages_promotion/presale/order_detail",{order_id:e.id})},imageError:function(e){this.orderList[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},pay:function(){var e=this;this.isSub||(this.isSub=!0,"final_money"==this.payType?this.orderPayFinal(this.orderData,(function(){e.$refs.mescroll.refresh()})):"presale_deposit_money"==this.payType&&this.orderPayDeposit(this.orderData,(function(){e.$refs.mescroll.refresh()})))},useBalance:function(){this.isBalance?(this.isBalance=0,this.payMoney+=parseFloat(this.balanceDeduct)):(this.isBalance=1,this.payMoney-=parseFloat(this.balanceDeduct))}},watch:{storeToken:function(e,t){e&&(this.getMemberBalance(),this.$refs.mescroll.refresh())}}};t.default=s},"8afd":function(e,t,a){"use strict";(function(e,t){var r=a("47a9");a("d381");r(a("3240"));var n=r(a("d5c3"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(n.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},d5c3:function(e,t,a){"use strict";a.r(t);var r=a("6c65"),n=a("78a7");for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(s);a("32b0"),a("75a5");var o=a("828b"),i=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"762f59ac",null,!1,r["a"],void 0);t["default"]=i.exports},f0e2:function(e,t,a){}},[["8afd","common/runtime","common/vendor","pages_promotion/common/vendor"]]]);