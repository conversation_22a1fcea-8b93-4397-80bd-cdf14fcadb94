require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/divideticket/poster"],{"18b8":function(t,i,e){"use strict";(function(t,i){var n=e("47a9");e("d381");n(e("3240"));var o=n(e("38e5"));t.__webpack_require_UNI_MP_PLUGIN__=e,i(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"1fe9":function(t,i,e){},2591:function(t,i,e){"use strict";var n=e("1fe9"),o=e.n(n);o.a},"29fa":function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;i.default={data:function(){return{poster:"",posterMsg:"",posterHeight:0,couponId:"",groupId:0,inviterId:""}},onLoad:function(t){this.couponId=t.coupon_id,this.groupId=t.group_id,this.inviterId=t.inviter_id,this.getGoodsPoster()},methods:{getGoodsPoster:function(){var t=this;this.$api.sendRequest({url:"/divideticket/api/divideticket/poster",data:{coupon_id:this.couponId,group_id:""==this.groupId?0:this.groupId,inviter_id:""==this.inviterId?0:this.inviterId},success:function(i){0==i.code?t.poster=i.data.path:t.posterMsg=i.message}})}}}},"38e5":function(t,i,e){"use strict";e.r(i);var n=e("47db"),o=e("67b9");for(var r in o)["default"].indexOf(r)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(r);e("2591");var u=e("828b"),s=Object(u["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);i["default"]=s.exports},"47db":function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return o})),e.d(i,"a",(function(){}));var n=function(){var t=this.$createElement,i=(this._self._c,this.$util.img(this.poster));this.$mp.data=Object.assign({},{$root:{g0:i}})},o=[]},"67b9":function(t,i,e){"use strict";e.r(i);var n=e("29fa"),o=e.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(r);i["default"]=o.a}},[["18b8","common/runtime","common/vendor"]]]);