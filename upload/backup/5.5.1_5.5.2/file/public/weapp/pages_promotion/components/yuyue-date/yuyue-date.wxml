<view class="data-v-055c6740"><view class="content data-v-055c6740"><view class="container data-v-055c6740"><view class="date-list-wrap data-v-055c6740"><scroll-view scroll-x="{{true}}" class="data-v-055c6740"><block wx:for="{{dateArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-055c6740"><view data-event-opts="{{[['tap',[['selectDateEvent',[index,'$0'],[[['dateArr','',index]]]]]]]}}" class="flex-box _div data-v-055c6740" bindtap="__e"><view class="date-box data-v-055c6740" style="{{'color:'+(index==dateActive?selectedTabColor:'#909399')+';'}}"><text class="data-v-055c6740">{{item.week+" "+item.date}}</text></view></view></block></block></scroll-view><view class="appointed-day _div data-v-055c6740"><uni-datetime-picker vue-id="1d970b9b-1" type="date" start="{{pickerStartDay}}" end="{{pickerEndDay}}" data-event-opts="{{[['^change',[['change']]]]}}" bind:change="__e" class="data-v-055c6740" bind:__l="__l" vue-slots="{{['default']}}"><text class="day-box data-v-055c6740">指定日期</text><text class="iconfont iconyoujiantou data-v-055c6740"></text></uni-datetime-picker></view></view><block wx:if="{{!isSection}}"><view class="time-box data-v-055c6740"><block wx:for="{{timeArr}}" wx:for-item="item" wx:for-index="_index" wx:key="_index"><block class="data-v-055c6740"><view class="item data-v-055c6740"><view data-event-opts="{{[['tap',[['selectTimeEvent',[_index,'$0'],[[['timeArr','',_index]]]]]]]}}" class="{{['item-box','diy','data-v-055c6740',(item.disable)?'disable':'',(isMultiple?item.isActive:_index==timeActive)?'active':'']}}" bindtap="__e"><text class="data-v-055c6740">{{item.time}}</text></view></view></block></block></view></block><block wx:else><view class="time-box data-v-055c6740"><block wx:for="{{timeArr}}" wx:for-item="item" wx:for-index="_index" wx:key="_index"><block class="data-v-055c6740"><view class="item data-v-055c6740"><view data-event-opts="{{[['tap',[['handleSelectQuantum',[_index,'$0'],[[['timeArr','',_index]]]]]]]}}" class="{{['item-box','data-v-055c6740',(item.disable||item.isInclude)?'disable':'',(item.time==timeQuanBegin||item.time==timeQuanEnd)?'active':'']}}" bindtap="__e"><text class="data-v-055c6740">{{item.time}}</text><text class="all data-v-055c6740">{{item.disable?disableText:undisableText}}</text></view></view></block></block></view></block></view></view></view>