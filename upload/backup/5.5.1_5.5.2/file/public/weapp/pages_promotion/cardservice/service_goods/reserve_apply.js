require('../../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/cardservice/service_goods/reserve_apply"],{"367a":function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s={components:{yuyueDate:function(){Promise.all([i.e("pages_promotion/common/vendor"),i.e("pages_promotion/components/yuyue-date/yuyue-date")]).then(function(){return resolve(i("e069"))}.bind(null,i)).catch(i.oe)},uniPopup:function(){i.e("components/uni-popup/uni-popup").then(function(){return resolve(i("d745"))}.bind(null,i)).catch(i.oe)}},data:function(){return{disableWeek:[],disableWeekTo:[],goodsId:0,date:"",time:"",remark:"",serviceList:[],serviceDetail:"",member_id:0,storeList:[],storeInfo:"",user:"",timeInfo:{},timeInterval:"1",contactData:{},chatRoomParams:{},whetherCollection:0}},onLoad:function(e){var t=this;e.goods_id?this.goodsId=e.goods_id:(this.$util.showToast({title:"未找到服务信息",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))},onShow:function(){var e=this;this.storeToken||this.$nextTick((function(){e.$refs.login.open("/pages_promotion/cardservice/service_goods/reserve_apply?goods_id="+e.goodsId)})),this.getDetail(),this.getWhetherCollection()},methods:{getDetail:function(){var e=this;this.$api.sendRequest({url:"/cardservice/api/service/detail",data:{goods_id:this.goodsId},success:function(t){t.code>=0?(e.serviceDetail=t.data.goods_sku_detail,e.contactData={title:e.serviceDetail.goods_name,path:"",img:e.$util.img(e.serviceDetail.goods_image,{size:"big"})},e.chatRoomParams={sku_id:e.serviceDetail.sku_id,type:"cardservice",type_id:""},e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.getStoreList()):(e.$util.showToast({title:"未找到服务信息",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},getTime:function(e){this.date=e.date,this.time=e.time},toDetail:function(e){this.$util.redirectTo("/pages/goods/detail",{goods_id:this.goodsId})},openServicePopup:function(){""!=this.storeInfo?this.getServiceList():this.$util.showToast({title:"请先选择门店",mask:!0,duration:1e3})},closeServicePopup:function(){this.$refs.servicePopup.close()},openStorePopup:function(){this.$refs.storePopup.open()},closeStorePopup:function(){this.$refs.storePopup.close()},getServiceList:function(){var e=this;this.$api.sendRequest({url:"/store/api/reserve/servicerList",data:{store_id:this.storeInfo.store_id},success:function(t){t.code>=0&&(e.serviceList=t.data,e.serviceList.length>0?e.$refs.servicePopup.open():e.$util.showToast({title:"该门店暂无服务人员",mask:!0,duration:1e3}))}})},setAdd:function(){var t=[];if(t.push({sku_id:this.serviceDetail.sku_id,uid:this.user.uid}),this.verify()){var i={goods:JSON.stringify(t),date:this.date,time:this.time,store_id:this.storeInfo.store_id,username:this.user.username};e.setStorageSync("reserveParams",i),this.$util.redirectTo("/pages_promotion/cardservice/service_goods/reserve_detail",{goods_id:this.goodsId})}},getStoreList:function(){var e=this,t=this.serviceDetail.sale_store;this.$api.sendRequest({url:"/api/store/page",data:{status:"all",store_ids:t},success:function(t){t.code>=0&&t.data.list.length>0&&(e.storeList=t.data.list)}})},setStore:function(e){this.storeInfo=e,this.getStoreTime(),this.closeStorePopup()},setUser:function(e){this.user=e,this.closeServicePopup()},getStoreTime:function(){var e=this;this.$api.sendRequest({url:"/store/api/reserve/getTimeConfig",data:{store_id:this.storeInfo.store_id},success:function(t){if(0==t.code){e.timeInfo=t.data.value;for(var i=0;i<e.timeInfo.week.length;i++)e.timeInfo.week[i]=parseInt(e.timeInfo.week[i]);e.disableWeek=e.getArrDifference(e.timeInfo.week,[1,2,3,4,5,6,0]),e.timeInterval=e.timeInfo.interval,e.disableWeekTo=e.disableWeek;for(var s=0;s<e.disableWeek.length;s++)1==e.disableWeek[s]&&(e.disableWeek[s]="周一"),2==e.disableWeek[s]&&(e.disableWeek[s]="周二"),3==e.disableWeek[s]&&(e.disableWeek[s]="周三"),4==e.disableWeek[s]&&(e.disableWeek[s]="周四"),5==e.disableWeek[s]&&(e.disableWeek[s]="周五"),6==e.disableWeek[s]&&(e.disableWeek[s]="周六"),0==e.disableWeek[s]&&(e.disableWeek[s]="周日")}}})},getArrDifference:function(e,t){return e.concat(t).filter((function(e,t,i){return i.indexOf(parseInt(e))===i.lastIndexOf(e)}))},goHome:function(){this.$util.redirectTo("/pages/index/index")},verify:function(){if(""==this.storeInfo)return this.$util.showToast({title:"请选择门店"}),!1;if(""==this.user)return this.$util.showToast({title:"请选择服务人员"}),!1;var e=(new Date).getDay();return-1!=this.disableWeekTo.indexOf(e)?(this.$util.showToast({title:"该天不支持预约"}),!1):!!this.time||(this.$util.showToast({title:"请选择时间段"}),!1)},getWhetherCollection:function(){var e=this;this.$api.sendRequest({url:"/api/goodscollect/iscollect",data:{goods_id:this.goodsId},success:function(t){e.whetherCollection=t.data}})},editCollection:function(){var e=this;this.storeToken&&(0==this.whetherCollection?this.$api.sendRequest({url:"/api/goodscollect/add",data:{sku_id:this.serviceDetail.sku_id,goods_id:this.serviceDetail.goods_id,sku_name:this.serviceDetail.sku_name,sku_price:this.serviceDetail.price,sku_image:this.serviceDetail.sku_image},success:function(t){var i=t.data;i>0&&(e.whetherCollection=1,e.$util.showToast({title:"关注成功"}))}}):this.$api.sendRequest({url:"/api/goodscollect/delete",data:{goods_id:this.serviceDetail.goods_id},success:function(t){var i=t.data;i>0&&(e.whetherCollection=0,e.$util.showToast({title:"取消关注"}))}}))}}};t.default=s}).call(this,i("df3c")["default"])},"45c1":function(e,t,i){"use strict";i.r(t);var s=i("fe33"),o=i("b0c5");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(n);i("fcc4"),i("9ccb");var r=i("828b"),a=Object(r["a"])(o["default"],s["b"],s["c"],!1,null,"2f2c090f",null,!1,s["a"],void 0);t["default"]=a.exports},"9ccb":function(e,t,i){"use strict";var s=i("dafa"),o=i.n(s);o.a},b0c5:function(e,t,i){"use strict";i.r(t);var s=i("367a"),o=i.n(s);for(var n in s)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(n);t["default"]=o.a},b8cc:function(e,t,i){},dafa:function(e,t,i){},f9e5:function(e,t,i){"use strict";(function(e,t){var s=i("47a9");i("d381");s(i("3240"));var o=s(i("45c1"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},fcc4:function(e,t,i){"use strict";var s=i("b8cc"),o=i.n(s);o.a},fe33:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){return s}));var s={nsGoodsActionIcon:function(){return i.e("components/ns-goods-action-icon/ns-goods-action-icon").then(i.bind(null,"565f"))},uniPopup:function(){return i.e("components/uni-popup/uni-popup").then(i.bind(null,"d745"))},nsLogin:function(){return Promise.all([i.e("common/vendor"),i.e("components/ns-login/ns-login")]).then(i.bind(null,"2910"))},loadingCover:function(){return i.e("components/loading-cover/loading-cover").then(i.bind(null,"c003"))}},o=function(){var e=this.$createElement,t=(this._self._c,this.$util.img(this.serviceDetail.goods_image));this.$mp.data=Object.assign({},{$root:{g0:t}})},n=[]}},[["f9e5","common/runtime","common/vendor"]]]);