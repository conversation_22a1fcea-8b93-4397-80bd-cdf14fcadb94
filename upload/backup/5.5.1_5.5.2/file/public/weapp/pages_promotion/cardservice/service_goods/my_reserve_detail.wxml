<page-meta page-style="{{themeColor}}" class="data-v-7301a721"></page-meta><view class="store-body data-v-7301a721"><view class="store-info data-v-7301a721"><view class="store data-v-7301a721"><view class="store-title data-v-7301a721">店铺名称</view><view class="store-name data-v-7301a721">{{reserveDetail.store_name}}</view></view><view data-event-opts="{{[['tap',[['mapRoute']]]]}}" class="store-map data-v-7301a721" bindtap="__e"><image src="{{$root.g0}}" class="_img data-v-7301a721"></image></view></view></view><view class="goods-body data-v-7301a721"><view class="goods-list data-v-7301a721"><block wx:for="{{$root.l0}}" wx:for-item="goodsItem" wx:for-index="goodsIndex" wx:key="goodsIndex"><view class="goods-wrap data-v-7301a721"><view class="goods-img data-v-7301a721"><image src="{{goodsItem.g1}}" mode="aspectFill" lazy-load="{{true}}" class="data-v-7301a721"></image></view><view class="goods-info data-v-7301a721"><view class="pro-info data-v-7301a721"><view class="goods-name data-v-7301a721">{{goodsItem.$orig.goods_name}}</view></view><view class="pro-info-to data-v-7301a721"><view class="content data-v-7301a721">{{"预约时间："+$root.g2}}</view></view><view class="pro-info-to data-v-7301a721"><view class="content data-v-7301a721">预约人数： 1</view></view><view class="pro-info-to data-v-7301a721"><view class="content data-v-7301a721">{{"服务人员："+goodsItem.$orig.username}}</view></view></view></view></block></view></view><view class="reserve-wrap data-v-7301a721"><view class="reserve-list data-v-7301a721"><view class="reserve-item data-v-7301a721"><text class="title data-v-7301a721">姓名</text><text class="content data-v-7301a721">{{reserveDetail.nickname}}</text></view><view class="reserve-item remark-item data-v-7301a721"><text class="title data-v-7301a721">备注</text><block wx:if="{{reserveDetail.remark}}"><text class="data-v-7301a721">{{reserveDetail.remark}}</text></block><block wx:else><text class="data-v-7301a721">暂无备注</text></block></view></view><block wx:if="{{reserveDetail.reserve_state=='wait_confirm'||reserveDetail.reserve_state=='wait_to_store'}}"><view class="tab-bar data-v-7301a721"><button class="reserve-btn data-v-7301a721" type="default" data-event-opts="{{[['tap',[['cancel']]]]}}" bindtap="__e">取消预约</button></view></block><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-7301a721"><uni-popup vue-id="8131d59e-1" type="bottom" data-ref="storeMapPopup" class="data-v-7301a721 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="goods-coupon-popup-layer popup-layer store-map-popuo data-v-7301a721"><view data-event-opts="{{[['tap',[['closeStoreMapPopup']]]]}}" class="head-wrap data-v-7301a721" bindtap="__e"><text class="data-v-7301a721">门店位置</text><text class="iconfont icon-close data-v-7301a721"></text></view><view class="store-map data-v-7301a721"><map class="map data-v-7301a721" latitude="{{reserveDetail.latitude}}" longitude="{{reserveDetail.longitude}}" markers="{{covers}}"></map></view><view class="button-box data-v-7301a721"><button type="primary" data-event-opts="{{[['tap',[['closeStoreMapPopup']]]]}}" bindtap="__e" class="data-v-7301a721">确定</button></view></view></uni-popup></view><ns-login vue-id="8131d59e-2" data-ref="login" class="data-v-7301a721 vue-ref" bind:__l="__l"></ns-login><privacy-popup vue-id="8131d59e-3" data-ref="privacyPopup" class="data-v-7301a721 vue-ref" bind:__l="__l"></privacy-popup></view>