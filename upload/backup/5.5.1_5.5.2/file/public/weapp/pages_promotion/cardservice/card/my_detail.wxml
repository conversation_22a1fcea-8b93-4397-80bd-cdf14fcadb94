<page-meta page-style="{{themeColor}}" class="data-v-711f20ac"></page-meta><view class="page data-v-711f20ac"><block wx:if="{{$root.g0}}"><view class="detail-wrap data-v-711f20ac"><view class="goods-item data-v-711f20ac" style="{{'background-image:'+('url('+$root.g1+')')+';'}}"><view class="conten data-v-711f20ac"><view class="name using-hidden data-v-711f20ac">{{cardDetail.goods_name}}</view><view class="desc using-hidden data-v-711f20ac">{{cardDetail.introduction}}</view><view class="{{['time-info','data-v-711f20ac',cardDetail.status==0?'warning':'']}}"><block wx:if="{{cardDetail.status==1}}"><text class="indate-time data-v-711f20ac">{{$root.m0==0?"长期有效":'至 '+$root.g2}}</text></block><block wx:else><text class="data-v-711f20ac">{{cardDetail.invalid_reason||'已失效'}}</text></block></view></view></view><block wx:if="{{$root.g3}}"><view class="card-info data-v-711f20ac"><view class="card-title data-v-711f20ac">- 套餐包含以下的服务及商品 -</view><block wx:if="{{cardDetail.card_type=='commoncard'}}"><view class="card-desc data-v-711f20ac">{{'卡项内项目/商品总的可用次数为'+cardDetail.total_num+"次，剩余"+(cardDetail.total_num-cardDetail.total_use_num)+'次可用'}}</view></block><view class="card-content data-v-711f20ac"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="card-item data-v-711f20ac" bindtap="__e"><image src="{{item.g4}}" mode="aspectFill" class="data-v-711f20ac"></image><view class="content data-v-711f20ac"><view class="name multi-hidden data-v-711f20ac">{{item.$orig.sku_name}}</view><block wx:if="{{cardDetail.card_type=='oncecard'}}"><view class="total-num data-v-711f20ac">{{"总次数："+item.$orig.num}}</view></block><block wx:if="{{item.g5}}"><text class="total-use-num data-v-711f20ac">{{cardDetail.card_type=='timecard'?"使用次数：不限次数":'已使用次数：'+item.$orig.use_num}}</text></block></view><block wx:if="{{cardDetail.status==1&&(cardDetail.card_type=='commoncard'&&cardDetail.total_num||cardDetail.card_type!='commoncard'&&item.$orig.num-item.$orig.use_num>0)}}"><button class="{{['data-v-711f20ac','button',[(item.$orig.goods_class==4)?'charge-off':''],[(item.$orig.goods_class==1)?'pick-goods':'']]}}" type="default" data-event-opts="{{[['tap',[['toUseFn',['$0'],[[['cardDetail.card_item','',index]]]]]]]}}" catchtap="__e">{{item.$orig.goods_class==4&&'去核销'||item.$orig.goods_class==1&&'去提货'}}</button></block></view></block></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="card-off data-v-711f20ac" bindtap="__e"><text class="data-v-711f20ac">使用记录</text><text class="icondiy icon-system-jiantouyou data-v-711f20ac"></text></view></view></block><block wx:if="{{cardDetail.goods_content}}"><view class="introduction data-v-711f20ac"><view class="title data-v-711f20ac">- 商品详情 -</view><view class="content data-v-711f20ac"><ns-mp-html vue-id="3db21f7c-1" content="{{cardDetail.goods_content}}" class="data-v-711f20ac" bind:__l="__l"></ns-mp-html></view></view></block><uni-popup class="charge-off data-v-711f20ac vue-ref" vue-id="3db21f7c-2" type="center" mask-click="{{false}}" data-ref="chargeOffPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="charge-off-wrap data-v-711f20ac" style="{{'background-image:'+('url('+$root.g6+')')+';'}}"><view class="code data-v-711f20ac"><image class="barcode data-v-711f20ac" src="{{virtualData.barcode}}" mode="aspectFit"></image><image class="qrcode data-v-711f20ac" src="{{virtualData.verify_code_path}}" mode="aspectFit"></image></view><view class="qrcode-desc data-v-711f20ac">向收银员展示此核销码</view><view class="charge-list data-v-711f20ac"><view class="charge-item data-v-711f20ac"><text class="data-v-711f20ac">名称</text><text class="data-v-711f20ac">{{virtualData.name}}</text></view><view class="charge-item data-v-711f20ac"><text class="data-v-711f20ac">剩余次数</text><text class="data-v-711f20ac">{{"x "+virtualData.num}}</text></view><view class="charge-item data-v-711f20ac"><text class="data-v-711f20ac">核销码</text><text data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="color-base-text data-v-711f20ac" bindtap="__e">点击复制</text></view><view class="charge-item data-v-711f20ac"><text class="data-v-711f20ac">有效期</text><text class="data-v-711f20ac">{{virtualData.time}}</text></view></view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="close icondiy icon-system-guanbi data-v-711f20ac" bindtap="__e"></view></view></uni-popup></view></block><ns-login vue-id="3db21f7c-3" data-ref="login" class="data-v-711f20ac vue-ref" bind:__l="__l"></ns-login><loading-cover vue-id="3db21f7c-4" data-ref="loadingCover" class="data-v-711f20ac vue-ref" bind:__l="__l"></loading-cover><privacy-popup vue-id="3db21f7c-5" data-ref="privacyPopup" class="data-v-711f20ac vue-ref" bind:__l="__l"></privacy-popup></view>