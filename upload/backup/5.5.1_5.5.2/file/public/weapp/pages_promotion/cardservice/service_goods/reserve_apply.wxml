<page-meta page-style="{{themeColor}}" class="data-v-2f2c090f"></page-meta><view class="reserve-wrap data-v-2f2c090f"><view class="reserve-item data-v-2f2c090f"><image src="{{$root.g0}}" mode="aspectFill" class="data-v-2f2c090f"></image><view class="conten data-v-2f2c090f"><view class="name multi-hidden data-v-2f2c090f">{{serviceDetail.goods_name}}</view><view class="price-wrap data-v-2f2c090f"><view class="price data-v-2f2c090f"><text class="data-v-2f2c090f">￥</text><block wx:if="{{serviceDetail.discount_price>0&&serviceDetail.discount_price<serviceDetail.price}}"><text class="data-v-2f2c090f">{{serviceDetail.discount_price}}</text></block><block wx:else><text class="data-v-2f2c090f">{{serviceDetail.price}}</text></block></view><view class="line-price data-v-2f2c090f">{{"￥"+serviceDetail.price}}</view></view><view class="btn-wrap data-v-2f2c090f"><text class="num data-v-2f2c090f">{{"已预约"+serviceDetail.sale_num+"人次"}}</text><view class="server-detail data-v-2f2c090f"><text class="data-v-2f2c090f">服务详情</text><text data-event-opts="{{[['tap',[['toDetail',['$0'],['goodsId']]]]]}}" class="icondiy icon-system-jiantouyou data-v-2f2c090f" bindtap="__e"></text></view></view></view></view><view class="store-select data-v-2f2c090f"><view data-event-opts="{{[['tap',[['openStorePopup']]]]}}" class="select-server data-v-2f2c090f" bindtap="__e"><text class="iconfont icon-mendian data-v-2f2c090f"></text><text class="txt data-v-2f2c090f">选择门店</text><text class="icondiy icon-system-jiantouyou arrows data-v-2f2c090f"></text></view><block wx:if="{{storeInfo}}"><view class="store-info data-v-2f2c090f"><view class="store-name data-v-2f2c090f">{{storeInfo.store_name}}</view><view class="store-time data-v-2f2c090f">{{"营业时间："+storeInfo.open_date}}</view><view class="store-addres data-v-2f2c090f">{{"地址："+storeInfo.full_address+" "+storeInfo.address}}</view></view></block></view><view data-event-opts="{{[['tap',[['openServicePopup']]]]}}" class="select-server data-v-2f2c090f" bindtap="__e"><text class="icondiy icon-xuanzhaijishi data-v-2f2c090f"></text><text class="txt data-v-2f2c090f">选择服务人员</text><view class="service-user data-v-2f2c090f"><block wx:if="{{user}}"><text class="txt data-v-2f2c090f">{{user.username}}</text></block><text class="icondiy icon-system-jiantouyou arrows data-v-2f2c090f"></text></view></view><view class="reserve-panel data-v-2f2c090f"><view class="panel-title data-v-2f2c090f"><text class="icondiy icon-shijian data-v-2f2c090f"></text><text class="data-v-2f2c090f">预约时间</text></view><yuyue-date vue-id="066e66ad-1" disableWeek="{{disableWeek}}" beginTime="{{timeInfo.start_time}}" endTime="{{timeInfo.end_time}}" timeInterval="{{timeInterval}}" data-ref="timePopup" data-event-opts="{{[['^change',[['getTime']]]]}}" bind:change="__e" class="data-v-2f2c090f vue-ref" bind:__l="__l"></yuyue-date></view><view class="tab-bar-fill data-v-2f2c090f"></view><view class="tab-bar data-v-2f2c090f"><ns-goods-action-icon vue-id="066e66ad-2" text="首页" icon="icondiy icon-shouye" data-event-opts="{{[['^click',[['goHome']]]]}}" bind:click="__e" class="data-v-2f2c090f" bind:__l="__l"></ns-goods-action-icon><ns-goods-action-icon vue-id="066e66ad-3" text="客服" icon="icondiy icon-qiafu" send-data="{{contactData}}" chatParam="{{chatRoomParams}}" class="data-v-2f2c090f" bind:__l="__l"></ns-goods-action-icon><view data-event-opts="{{[['tap',[['editCollection']]]]}}" class="tab-bar-item data-v-2f2c090f" bindtap="__e"><view class="action-icon-wrap data-v-2f2c090f"><block wx:if="{{whetherCollection==0}}"><view class="icondiy icon-shouzang data-v-2f2c090f"></view></block><block wx:else><view class="icondiy icon-shouzang selected-collection data-v-2f2c090f"></view></block><text class="data-v-2f2c090f">收藏</text></view></view><view class="bnutton-body data-v-2f2c090f"><button class="reserve-btn data-v-2f2c090f" type="default" data-event-opts="{{[['tap',[['setAdd']]]]}}" bindtap="__e">确认预约</button></view></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-2f2c090f"><uni-popup vue-id="066e66ad-4" type="bottom" data-ref="servicePopup" class="data-v-2f2c090f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="goods-coupon-popup-layer popup-layer data-v-2f2c090f"><view data-event-opts="{{[['tap',[['closeServicePopup']]]]}}" class="head-wrap data-v-2f2c090f" bindtap="__e"><text class="data-v-2f2c090f">选择服务人员</text><text class="iconfont icon-close data-v-2f2c090f"></text></view><scroll-view class="service-body scroll data-v-2f2c090f" scroll-y="{{true}}"><block wx:for="{{serviceList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['setUser',['$0'],[[['serviceList','',index]]]]]]]}}" bindtap="__e" class="data-v-2f2c090f"><text class="user-name data-v-2f2c090f">{{item.username}}</text></view></block></scroll-view><view class="button-box data-v-2f2c090f"><button type="primary" data-event-opts="{{[['tap',[['closeServicePopup']]]]}}" bindtap="__e" class="data-v-2f2c090f">确定</button></view></view></uni-popup></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-2f2c090f"><uni-popup vue-id="066e66ad-5" type="bottom" data-ref="storePopup" class="data-v-2f2c090f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="goods-coupon-popup-layer popup-layer data-v-2f2c090f"><view data-event-opts="{{[['tap',[['closeStorePopup']]]]}}" class="head-wrap data-v-2f2c090f" bindtap="__e"><text class="data-v-2f2c090f">选择门店列表</text><text class="iconfont icon-close data-v-2f2c090f"></text></view><scroll-view class="store-body scroll data-v-2f2c090f" scroll-y="{{true}}"><block wx:for="{{storeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['setStore',['$0'],[[['storeList','',index]]]]]]]}}" class="store-select data-v-2f2c090f" bindtap="__e"><view class="store-info data-v-2f2c090f"><view class="store-name data-v-2f2c090f">{{item.store_name}}</view><view class="store-time data-v-2f2c090f">{{"营业时间："+item.open_date}}</view><view class="store-addres data-v-2f2c090f">{{"地址："+item.full_address+" "+item.address}}</view></view></view></block></scroll-view><view class="button-box data-v-2f2c090f"><button type="primary" data-event-opts="{{[['tap',[['closeStorePopup']]]]}}" bindtap="__e" class="data-v-2f2c090f">确定</button></view></view></uni-popup></view><ns-login vue-id="066e66ad-6" data-ref="login" class="data-v-2f2c090f vue-ref" bind:__l="__l"></ns-login><loading-cover vue-id="066e66ad-7" data-ref="loadingCover" class="data-v-2f2c090f vue-ref" bind:__l="__l"></loading-cover></view>