<page-meta page-style="{{themeColor}}" class="data-v-1b414bd8"></page-meta><view class="page data-v-1b414bd8"><view class="card-info data-v-1b414bd8"><view class="card-title data-v-1b414bd8">- 选择提货数量 -</view><block wx:if="{{cardDetail.card_type=='commoncard'}}"><view class="card-desc data-v-1b414bd8">{{"卡项内项目/商品总的可用次数为"+(cardDetail.total_num-cardDetail.total_use_num)+"次"}}</view></block><view class="card-content data-v-1b414bd8"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><block wx:if="{{item.$orig.goods_class==1}}"><view class="card-item data-v-1b414bd8"><image src="{{item.g0}}" mode="aspectFill" class="data-v-1b414bd8"></image><view class="content data-v-1b414bd8"><view class="name multi-hidden data-v-1b414bd8">{{item.$orig.sku_name}}</view><block wx:if="{{cardDetail.card_type=='oncecard'}}"><view class="total-num data-v-1b414bd8">{{"总次数："+item.$orig.num}}</view></block><block wx:if="{{item.g1}}"><text class="total-use-num data-v-1b414bd8">{{''+(cardDetail.card_type=='timecard'?'使用次数：不限次数':'已使用次数：'+item.$orig.use_num)+''}}</text></block></view><view class="select-num data-v-1b414bd8"><uni-number-box vue-id="{{'d86bd1c4-1-'+index}}" min="{{buyNum[index].min}}" max="{{buyNum[index].max}}" value="{{buyNum[index].curr}}" input-disabled="{{true}}" size="small" data-event-opts="{{[['^change',[['cartNumChange',['$event',index]]]]]}}" bind:change="__e" class="data-v-1b414bd8" bind:__l="__l"></uni-number-box></view></view></block></block></view><view class="pick-btn data-v-1b414bd8"><button type="default" disabled="{{pickDisabled}}" data-event-opts="{{[['tap',[['pickGoods',['$event']]]]]}}" bindtap="__e" class="data-v-1b414bd8">提货</button></view></view><loading-cover vue-id="d86bd1c4-2" data-ref="loadingCover" class="data-v-1b414bd8 vue-ref" bind:__l="__l"></loading-cover></view>