<page-meta page-style="{{themeColor}}" class="data-v-539678f5"></page-meta><view class="reserve-wrap data-v-539678f5"><scroll-view class="reserve-nav data-v-539678f5" scroll-x="{{true}}" enable-flex="true"><block wx:for="{{navStatus.list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['ontabtap',[index]]]]]}}" class="{{['nav-item','data-v-539678f5',item.id==navStatus.index?'active':'']}}" bindtap="__e">{{''+item.name+''}}</view></block></scroll-view><mescroll-uni vue-id="491d9e03-1" top="104rpx" data-ref="mescroll" data-event-opts="{{[['^getData',[['getListData']]]]}}" bind:getData="__e" class="data-v-539678f5 vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-539678f5"><block wx:if="{{$root.g0>0}}"><view class="reserve-list data-v-539678f5"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="reserve-item data-v-539678f5"><image src="{{item.g1}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e" class="data-v-539678f5"></image><view class="conten data-v-539678f5"><view class="name multi-hidden data-v-539678f5">{{item.$orig.goods_name}}</view><view class="price data-v-539678f5"><text class="data-v-539678f5">￥</text><text class="data-v-539678f5">{{item.$orig.price}}</text></view><view class="btn-wrap data-v-539678f5"><text class="num data-v-539678f5">{{"已预约"+item.$orig.sale_num+"人次"}}</text><button type="default" data-event-opts="{{[['tap',[['toDetail',['$0'],[[['reserveList','',index,'goods_id']]]]]]]}}" bindtap="__e" class="data-v-539678f5">预约</button></view></view></view></block></view></block><block wx:else><view class="data-v-539678f5"><ns-empty vue-id="{{('491d9e03-2')+','+('491d9e03-1')}}" isIndex="{{false}}" text="暂无预约信息" class="data-v-539678f5" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni><loading-cover vue-id="491d9e03-3" data-ref="loadingCover" class="data-v-539678f5 vue-ref" bind:__l="__l"></loading-cover></view>