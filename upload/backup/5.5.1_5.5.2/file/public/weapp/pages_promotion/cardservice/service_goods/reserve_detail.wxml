<page-meta page-style="{{themeColor}}" class="data-v-492e0388"></page-meta><view class="reserve-wrap data-v-492e0388"><view class="reserve-list data-v-492e0388"><view class="reserve-head data-v-492e0388">预约信息</view><view class="reserve-item data-v-492e0388"><text class="title data-v-492e0388">预约项目</text><text class="content data-v-492e0388">{{serviceDetail.goods_name}}</text></view><view class="reserve-item data-v-492e0388"><text class="title data-v-492e0388">预约时间</text><view class="content data-v-492e0388"><text class="data-v-492e0388">{{params.date}}</text><text class="time data-v-492e0388">{{"("+params.time+")"}}</text></view></view><view class="reserve-item data-v-492e0388"><text class="title data-v-492e0388">预约人数</text><text class="content data-v-492e0388">1</text></view><view class="reserve-item data-v-492e0388"><text class="title data-v-492e0388">服务人员</text><text class="content data-v-492e0388">{{params.username}}</text></view><view class="reserve-item remark-item data-v-492e0388"><text class="title data-v-492e0388">备注</text><textarea type="text" maxlength="100" placeholder="备注信息" placeholder-class="color-tip" data-event-opts="{{[['input',[['__set_model',['$0','remark','$event',[]],['formData']]]]]}}" value="{{formData.remark}}" bindinput="__e" class="data-v-492e0388"></textarea></view></view><view class="tab-bar data-v-492e0388"><view class="tab-bar-item data-v-492e0388"></view><button class="reserve-btn data-v-492e0388" type="default" data-event-opts="{{[['tap',[['setAdd']]]]}}" bindtap="__e">确认预约</button></view><ns-login vue-id="0ea58520-1" data-ref="login" class="data-v-492e0388 vue-ref" bind:__l="__l"></ns-login></view>