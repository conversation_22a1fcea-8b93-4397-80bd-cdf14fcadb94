<page-meta page-style="{{themeColor}}" class="data-v-24806b26"></page-meta><view class="page data-v-24806b26"><view class="recodrd-list data-v-24806b26"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toOrderDetail',['$0'],[[['list','',index]]]]]]]}}" class="recodrd-item data-v-24806b26" bindtap="__e"><view class="item-head data-v-24806b26"><text class="data-v-24806b26">{{item.g0}}</text><text class="data-v-24806b26">{{item.$orig.type=="verify"?'核销成功':'提货成功'}}</text></view><view class="item-body data-v-24806b26"><view class="image data-v-24806b26"><image src="{{item.g1}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e" class="data-v-24806b26"></image></view><view class="content-wrap data-v-24806b26"><view class="content-item name data-v-24806b26"><text class="multi-hidden data-v-24806b26">{{item.$orig.sku_name}}</text></view><view class="content-item data-v-24806b26"><text class="data-v-24806b26">{{item.$orig.type=="verify"?'本次核销':'本次提货'}}</text><text class="color-base-text data-v-24806b26">{{"x"+item.$orig.num}}</text></view></view></view></view></block></view><block wx:if="{{!$root.g2}}"><view class="data-v-24806b26"><ns-empty vue-id="4b7364b4-1" text="暂无记录" class="data-v-24806b26" bind:__l="__l"></ns-empty></view></block><loading-cover vue-id="4b7364b4-2" data-ref="loadingCover" class="data-v-24806b26 vue-ref" bind:__l="__l"></loading-cover></view>