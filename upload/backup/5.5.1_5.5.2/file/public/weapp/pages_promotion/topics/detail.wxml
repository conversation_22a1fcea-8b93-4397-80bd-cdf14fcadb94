<page-meta page-style="{{themeColor}}"></page-meta><view class="topic-detail" style="{{'background-color:'+(bgColor)+';'}}"><mescroll-uni class="vue-ref" vue-id="8ddc4d62-1" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{topicAdv}}"><view class="topic-pic"><image src="{{$root.g0}}" mode="widthFix"></image></view></block><block wx:if="{{$root.g1}}"><view class="goods-list double-column"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index]]]]]]]}}" class="goods-img" bindtap="__e"><image src="{{item.m0}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image></view><view class="info-wrap"><view class="name-wrap"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index]]]]]]]}}" class="goods-name" bindtap="__e">{{item.$orig.sku_name}}</view></view><view class="lineheight-clear"><view class="discount-price"><text class="unit price-style small">{{item.m1}}</text><text class="price price-style large">{{item.g2[0]}}</text><text class="unit price-style small">{{"."+item.g3[1]}}</text></view></view><view class="pro-info"><block wx:if="{{item.m2}}"><view class="delete-price font-size-activity-tag color-tip price-font"><text class="unit">{{item.m3}}</text><text>{{item.m4}}</text></view></block><block wx:if="{{item.$orig.sale_show}}"><view class="sale font-size-activity-tag color-tip">{{"已售"+item.$orig.sale_num+(item.$orig.unit?item.$orig.unit:'件')}}</view></block></view></view></view></block></view></block><block wx:if="{{!$root.g4}}"><view><ns-empty vue-id="{{('8ddc4d62-2')+','+('8ddc4d62-1')}}" text="暂无有相应的商品" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni><hover-nav vue-id="8ddc4d62-3" bind:__l="__l"></hover-nav><loading-cover class="vue-ref" vue-id="8ddc4d62-4" data-ref="loadingCover" bind:__l="__l"></loading-cover><privacy-popup class="vue-ref" vue-id="8ddc4d62-5" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>