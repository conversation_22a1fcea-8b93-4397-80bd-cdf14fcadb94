<page-meta page-style="{{themeColor}}"></page-meta><view class="page" style="{{'background:'+(bgColor)+';'}}"><block wx:if="{{show}}"><view><block wx:if="{{seckillId&&addonIsExist.seckill}}"><mescroll-uni class="vue-ref" vue-id="d16ef1da-1" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]],['^scroll',[['scroll']]]]}}" bind:getData="__e" bind:scroll="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><ns-adv class="adv-wrap" vue-id="{{('d16ef1da-2')+','+('d16ef1da-1')}}" keyword="NS_SECKILL" bind:__l="__l"></ns-adv><view class="time-wrap"><scroll-view class="scroll-wrap" scroll-x="{{true}}" scroll-with-animation="{{true}}" scroll-into-view="{{'a'+seckillId}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="key" wx:key="key"><view class="{{['time-item',seckillId==item.$orig.id?'active':'']}}" id="{{'a'+item.$orig.id}}" data-event-opts="{{[['tap',[['ontabtap',['$0',key],[[['timeList','',key]]]]]]]}}" bindtap="__e"><view class="start-time font-size-toolbar">{{item.m0}}</view><block wx:if="{{item.$orig.type=='today'}}"><block><block wx:if="{{!item.$orig.isNow}}"><view class="em font-size-tag">即将开始</view></block><block wx:else><block wx:if="{{item.$orig.isNow}}"><view class="em font-size-tag">抢购中</view></block></block></block></block><block wx:else><block><view class="em font-size-tag">明日预告</view></block></block></view></block></scroll-view></view><view class="time-box"><block wx:if="{{timeList[seckillIndex].isNow&&timeList[seckillIndex].endTimeMachine}}"><block>本场还剩<uni-count-down vue-id="{{('d16ef1da-3')+','+('d16ef1da-1')}}" day="{{timeList[seckillIndex].endTimeMachine.d}}" hour="{{timeList[seckillIndex].endTimeMachine.h}}" minute="{{timeList[seckillIndex].endTimeMachine.i}}" second="{{timeList[seckillIndex].endTimeMachine.s}}" color="#fff" splitorColor="#fff !important" backgroundColor="#000" border-color="transparent" bind:__l="__l"></uni-count-down>结束</block></block><block wx:else><block wx:if="{{!timeList[seckillIndex].isNow&&timeList[seckillIndex].startTimeMachine}}"><block>本场还剩<uni-count-down vue-id="{{('d16ef1da-4')+','+('d16ef1da-1')}}" day="{{timeList[seckillIndex].startTimeMachine.d}}" hour="{{timeList[seckillIndex].startTimeMachine.h}}" minute="{{timeList[seckillIndex].startTimeMachine.i}}" second="{{timeList[seckillIndex].startTimeMachine.s}}" color="#fff" splitorColor="#fff !important" backgroundColor="#000" border-color="transparent" bind:__l="__l"></uni-count-down>开始</block></block></block></view><block wx:if="{{$root.g0}}"><view class="goods-list single-column"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="goodsIndex" wx:key="goodsIndex"><view data-event-opts="{{[['tap',[['toGoodsDetail',['$0'],[[['dataList','',goodsIndex]]]]]]]}}" class="item" bindtap="__e"><view class="img-wrap"><image src="{{item.g1}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[goodsIndex]]]]]}}" binderror="__e"></image></view><view class="content"><view class="goods-name multi-hidden">{{item.$orig.goods_name}}</view><view class="progress"><view class="bg"><view class="curr" style="{{'width:'+(item.g2+'rpx')+';'}}"><image class="progress-bar" src="{{item.g3}}" mode="widthFix"></image></view></view><block wx:if="{{timeList[seckillIndex].isNow}}"><view class="num">{{"仅剩"+item.$orig.goods_stock+'件'}}</view></block></view><view class="bottom-wrap"><view class="price-wrap"><view class="discount-price"><text class="unit price-style small">¥</text><text class="price price-style large">{{item.g4[0]}}</text><text class="unit price-style small">{{'.'+item.g5[1]}}</text></view><view class="price-font"><text>原价：</text><text class="original-price">{{"¥"+item.$orig.price}}</text></view></view><button>{{timeList[seckillIndex].isNow?'马上抢':'即将开始'}}</button></view></view></view></block></view></block><block wx:if="{{$root.g6}}"><view style="z-index:1 !important;"><ns-empty vue-id="{{('d16ef1da-5')+','+('d16ef1da-1')}}" isIndex="{{false}}" text="暂时没有商品哦！去别处看看吧~" textColor="#fff" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni></block></view></block><block wx:else><view class="big-empty"><ns-empty vue-id="d16ef1da-6" isIndex="{{false}}" text="暂时没有商品哦！去别处看看吧~" textColor="#fff" bind:__l="__l"></ns-empty></view></block><hover-nav vue-id="d16ef1da-7" bind:__l="__l"></hover-nav><loading-cover class="vue-ref" vue-id="d16ef1da-8" data-ref="loadingCover" bind:__l="__l"></loading-cover><privacy-popup class="vue-ref" vue-id="d16ef1da-9" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>