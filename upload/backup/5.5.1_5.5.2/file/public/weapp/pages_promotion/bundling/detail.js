require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/bundling/detail"],{"0f4f":function(n,e,t){"use strict";(function(n,e){var i=t("47a9");t("d381");i(t("3240"));var o=i(t("370d"));n.__webpack_require_UNI_MP_PLUGIN__=t,e(o.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},"370d":function(n,e,t){"use strict";t.r(e);var i=t("b721"),o=t("954d");for(var a in o)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(a);t("8516");var r=t("828b"),u=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=u.exports},8516:function(n,e,t){"use strict";var i=t("95a6"),o=t.n(i);o.a},"950a":function(n,e,t){"use strict";(function(n){var i=t("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(t("7eb4")),a=i(t("ee10")),r={data:function(){return{num:1,blId:0,combo:[],packagePrice:[],saveThePrice:0,isDisabled:!1,isIphoneX:!1}},onLoad:function(n){this.blId=n.bl_id||0,this.isIphoneX=this.$util.uniappIsIPhoneX()},onShow:function(){var n=this;setTimeout((function(){n.addonIsExist.bundling||(n.$util.showToast({title:"商家未开启组合套餐",mask:!0,duration:2e3}),setTimeout((function(){n.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.getDetail()},onHide:function(){this.btnSwitch=!0},methods:{getDetail:function(){var n=this;this.$api.sendRequest({url:"/bundling/api/bundling/detail",data:{bl_id:this.blId},success:function(e){e.data?(n.combo=e.data,n.numberChange()):n.$util.showToast({title:e.message}),n.$refs.loadingCover&&n.$refs.loadingCover.hide()},fail:function(e){n.$refs.loadingCover&&n.$refs.loadingCover.hide()}})},toGoodsDetail:function(n){this.$util.redirectTo("/pages/goods/detail",{goods_id:n.goods_id})},numberChange:function(n,e){var t=this;setTimeout((function(){var i=0;n&&0==t.num.length&&(t.num=1,i++),n&&(t.num<=0||isNaN(t.num))&&(t.number=1,i++),n&&(t.num=parseInt(t.num));for(var o=0,a=0;a<t.combo.bundling_goods.length;a++)o+=parseFloat(t.combo.bundling_goods[a].price),t.combo.bundling_goods[a].stock<t.num&&i++;t.isDisabled=!(i>0),t.saveThePrice=((o-t.combo.bl_price)*t.num).toFixed(2),t.packagePrice=(t.combo.bl_price*t.num).toFixed(2),e&&e()}),0)},comboBuy:function(){var e=this;return(0,a.default)(o.default.mark((function t(){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isDisabled){t.next=2;break}return t.abrupt("return");case 2:e.numberChange(!0,(function(){if(e.storeToken){if(0!=e.btnSwitch){e.btnSwitch=!1;var t={bl_id:e.blId,num:e.num};n.setStorage({key:"comboOrderCreateData",data:t,success:function(){e.$util.redirectTo("/pages_promotion/bundling/payment"),e.btnSwitch=!0}})}}else e.$refs.login.open("/pages_promotion/bundling/detail?bl_id="+e.blId)}));case 3:case"end":return t.stop()}}),t)})))()},imageError:function(n){this.combo.bundling_goods[n].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}},onShareAppMessage:function(n){var e="/pages_promotion/combo/detail?bl_id="+this.blId;return{title:"购买套餐，优惠多多哦",path:e,success:function(n){},fail:function(n){}}}};e.default=r}).call(this,t("df3c")["default"])},"954d":function(n,e,t){"use strict";t.r(e);var i=t("950a"),o=t.n(i);for(var a in i)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(a);e["default"]=o.a},"95a6":function(n,e,t){},b721:function(n,e,t){"use strict";t.d(e,"b",(function(){return o})),t.d(e,"c",(function(){return a})),t.d(e,"a",(function(){return i}));var i={hoverNav:function(){return t.e("components/hover-nav/hover-nav").then(t.bind(null,"c1f1"))},loadingCover:function(){return t.e("components/loading-cover/loading-cover").then(t.bind(null,"c003"))},nsLogin:function(){return Promise.all([t.e("common/vendor"),t.e("components/ns-login/ns-login")]).then(t.bind(null,"2910"))}},o=function(){var n=this,e=n.$createElement,t=(n._self._c,n.$lang("common.currencySymbol")),i=n.__map(n.combo.bundling_goods,(function(e,t){var i=n.__get_orig(e),o=n.$util.img(e.sku_image,{size:"mid"}),a=parseFloat(e.price).toFixed(2).split("."),r=parseFloat(e.price).toFixed(2).split(".");return{$orig:i,g0:o,g1:a,g2:r}})),o=n.$lang("common.currencySymbol"),a=parseFloat(n.packagePrice).toFixed(2).split("."),r=parseFloat(n.packagePrice).toFixed(2).split(".");n.$mp.data=Object.assign({},{$root:{m0:t,l0:i,m1:o,g3:a,g4:r}})},a=[]}},[["0f4f","common/runtime","common/vendor"]]]);