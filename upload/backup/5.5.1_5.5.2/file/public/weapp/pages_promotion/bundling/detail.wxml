<page-meta page-style="{{themeColor}}"></page-meta><view><view class="{{['combo-package',isIphoneX?'combo-iphonex':'']}}"><view class="combo-package-content"><view class="combo-package-name color-title">{{combo.bl_name}}</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toGoodsDetail',['$0'],[[['combo.bundling_goods','',index]]]]]]]}}" class="goods-info" bindtap="__e"><view class="goods-img"><view class="img-wrap"><image src="{{item.g0}}" mode="aspectFit" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></view></view><view class="data-info"><view class="goods-name">{{item.$orig.sku_name}}</view><view class="price-wrap"><text class="unit price-style small">{{$root.m0}}</text><text class="price price-style large">{{item.g1[0]}}</text><text class="unit price-style small">{{"."+item.g2[1]}}</text><text class="num">x1</text></view><block wx:if="{{item.$orig.stock<num}}"><view class="stock-tips color-base-text">{{'库存不足，剩余：'+item.$orig.stock+''}}<block wx:if="{{item.$orig.unit}}"><block>{{item.$orig.unit}}</block></block><block wx:else><block>件</block></block></view></block></view></view></block></view><view class="{{['footer',isIphoneX?'padding-bottom':'']}}"><view class="price-wrap"><text class="label">套餐价：</text><text class="unit price-color">{{$root.m1}}</text><text class="price price-color">{{$root.g3[0]}}</text><text class="unit price-color">{{"."+$root.g4[1]}}</text></view><block wx:if="{{isDisabled}}"><button class="footer-btn mini" type="primary" size="mini" data-event-opts="{{[['tap',[['comboBuy']]]]}}" bindtap="__e">立即购买</button></block><block wx:else><button class="footer-btn mini" disabled="{{true}}">立即购买</button></block></view></view><hover-nav vue-id="4a91cdac-1" bind:__l="__l"></hover-nav><loading-cover class="vue-ref" vue-id="4a91cdac-2" data-ref="loadingCover" bind:__l="__l"></loading-cover><ns-login class="vue-ref" vue-id="4a91cdac-3" data-ref="login" bind:__l="__l"></ns-login><privacy-popup class="vue-ref" vue-id="4a91cdac-4" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>