require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/bundling/payment"],{"0d60":function(n,t,e){"use strict";e.d(t,"b",(function(){return a})),e.d(t,"c",(function(){return r})),e.d(t,"a",(function(){return o}));var o={commonPayment:function(){return Promise.all([e.e("common/vendor"),e.e("components/common-payment/common-payment")]).then(e.bind(null,"47f2"))}},a=function(){var n=this.$createElement;this._self._c},r=[]},"115a":function(n,t,e){"use strict";e.r(t);var o=e("0d60"),a=e("292c");for(var r in a)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(r);e("3773");var i=e("828b"),u=Object(i["a"])(a["default"],o["b"],o["c"],!1,null,"313ba39a",null,!1,o["a"],void 0);t["default"]=u.exports},"24ba":function(n,t,e){},"292c":function(n,t,e){"use strict";e.r(t);var o=e("80fe"),a=e.n(o);for(var r in o)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(r);t["default"]=a.a},3773:function(n,t,e){"use strict";var o=e("24ba"),a=e.n(o);a.a},"5de4":function(n,t,e){"use strict";(function(n,t){var o=e("47a9");e("d381");o(e("3240"));var a=o(e("115a"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"80fe":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{api:{payment:"/bundling/api/ordercreate/payment",calculate:"/bundling/api/ordercreate/calculate",create:"/bundling/api/ordercreate/create"}}},provide:function(){return{promotion:this.promotion.bind(this)}},onShow:function(){this.$refs.payment&&this.$refs.payment.pageShow()},methods:{promotion:function(n){if(n.bunding_info)return{title:"组合套餐",content:n.bunding_info.bl_name}}}}}},[["5de4","common/runtime","common/vendor"]]]);