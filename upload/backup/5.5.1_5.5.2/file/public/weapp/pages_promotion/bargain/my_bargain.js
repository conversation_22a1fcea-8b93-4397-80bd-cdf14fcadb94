require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/bargain/my_bargain"],{"2d37":function(t,n,e){"use strict";var i=e("7d09"),a=e.n(i);a.a},"4d46":function(t,n,e){"use strict";e.r(n);var i=e("d836"),a=e("a6f4");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("2d37"),e("7534");var r=e("828b"),u=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"1ae7b9bb",null,!1,i["a"],void 0);n["default"]=u.exports},"5fa8":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i={components:{uniCountDown:function(){e.e("components/uni-count-down/uni-count-down").then(function(){return resolve(e("e12a"))}.bind(null,e)).catch(e.oe)}},data:function(){return{mescroll:null,dataList:[],statusList:[{id:"all",name:"全部"},{id:0,name:"正在砍价"},{id:1,name:"砍价成功"},{id:2,name:"砍价失败"}],status:"all",bargainState:[{color:"#FFA044",text:"正在砍价"},{color:"#11BD64",text:"砍价成功"},{color:"#FF4544",text:"砍价失败"}]}},onShow:function(){var t=this;setTimeout((function(){t.addonIsExist.bargain||(t.$util.showToast({title:"商家未开启砍价",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.storeToken||this.$nextTick((function(){t.$refs.login.open("/pages_promotion/bargain/my_bargain")}))},methods:{getData:function(t){var n=this;this.mescroll=t,this.$api.sendRequest({url:"/bargain/api/bargain/launchPage",data:{page_size:t.size,page:t.num,status:this.status},success:function(e){var i=[],a=e.message;0==e.code&&e.data?i=e.data.list:n.$util.showToast({title:a}),t.endSuccess(i.length),1==t.num&&(n.dataList=[]),i.forEach((function(t){t.end_time>e.timestamp?t.timeMachine=n.$util.countDown(t.end_time-e.timestamp):t.timeMachine=null})),n.dataList=n.dataList.concat(i),n.$refs.loadingCover&&n.$refs.loadingCover.hide()},fail:function(){t.endErr(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},goBargainDetail:function(t){this.$util.redirectTo("/pages_promotion/bargain/detail",{l_id:t.launch_id,b_id:t.bargain_id})},categoryChange:function(t){this.status=t,this.mescroll.resetUpScroll()},imageError:function(t){this.dataList[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},memberImageError:function(t,n){this.dataList[t].bargain_record[n].headimg=this.$util.getDefaultImage().head,this.$forceUpdate()}},onBackPress:function(t){return"navigateBack"!==t.from&&(this.$util.redirectTo("/pages/member/index"),!0)},watch:{storeToken:function(t,n){t&&this.$refs.mescroll.refresh()}}};n.default=i},7534:function(t,n,e){"use strict";var i=e("d5a5"),a=e.n(i);a.a},"7d09":function(t,n,e){},a6f4:function(t,n,e){"use strict";e.r(n);var i=e("5fa8"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},d5a5:function(t,n,e){},d836:function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return i}));var i={uniCountDown:function(){return e.e("components/uni-count-down/uni-count-down").then(e.bind(null,"e12a"))},nsEmpty:function(){return e.e("components/ns-empty/ns-empty").then(e.bind(null,"52a6"))},nsLogin:function(){return Promise.all([e.e("common/vendor"),e.e("components/ns-login/ns-login")]).then(e.bind(null,"2910"))},loadingCover:function(){return e.e("components/loading-cover/loading-cover").then(e.bind(null,"c003"))}},a=function(){var t=this,n=t.$createElement,e=(t._self._c,t.storeToken?t.__map(t.dataList,(function(n,e){var i=t.__get_orig(n),a=t.$util.timeStampTurnTime(n.start_time),o=t.$util.img(n.sku_image,{size:"mid"}),r=n.status?t.$lang("common.currencySymbol"):null,u=t.__map(n.bargain_record,(function(n,e){var i=t.__get_orig(n),a=""==n.headimg?t.$util.img(t.$util.getDefaultImage().head):null,o=""!=n.headimg?t.$util.img(n.headimg):null;return{$orig:i,g2:a,g3:o}}));return{$orig:i,g0:a,g1:o,m0:r,l0:u}})):null),i=t.storeToken?t.dataList.length:null;t._isMounted||(t.e0=function(n){return t.$util.redirectTo("/pages_promotion/bargain/list")}),t.$mp.data=Object.assign({},{$root:{l1:e,g4:i}})},o=[]},fa42:function(t,n,e){"use strict";(function(t,n){var i=e("47a9");e("d381");i(e("3240"));var a=i(e("4d46"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["fa42","common/runtime","common/vendor"]]]);