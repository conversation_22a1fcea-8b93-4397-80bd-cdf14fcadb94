<page-meta page-style="{{themeColor}}" class="data-v-1ae7b9bb"></page-meta><view class="data-v-1ae7b9bb"><block wx:if="{{storeToken}}"><view class="my_spell_category data-v-1ae7b9bb"><block wx:for="{{statusList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['categoryChange',['$0'],[[['statusList','',index,'id']]]]]]]}}" class="category-item data-v-1ae7b9bb" bindtap="__e"><view class="{{['item-con','data-v-1ae7b9bb',item.id==status?'active color-base-text color-base-bg-before':'']}}">{{item.name}}</view></view></block></view></block><block wx:if="{{storeToken}}"><mescroll-uni vue-id="26860f16-1" top="90" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" class="data-v-1ae7b9bb vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-1ae7b9bb"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-list data-v-1ae7b9bb"><view data-event-opts="{{[['tap',[['goBargainDetail',['$0'],[[['dataList','',index]]]]]]]}}" class="goods-item-content data-v-1ae7b9bb" bindtap="__e"><view class="goods-item-state data-v-1ae7b9bb"><text class="state-time data-v-1ae7b9bb">{{"发起砍价 "+item.g0}}</text><text class="state-sign data-v-1ae7b9bb" style="{{'color:'+(bargainState[item.$orig.status].color)+';'}}">{{bargainState[item.$orig.status].text}}</text></view><view class="goods-item-wrap data-v-1ae7b9bb"><view class="image-wrap data-v-1ae7b9bb"><image src="{{item.g1}}" mode="aspectFit" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e" class="data-v-1ae7b9bb"></image></view><view class="content data-v-1ae7b9bb"><view class="title data-v-1ae7b9bb">{{item.$orig.sku_name}}</view><text class="residue-price color-base-text data-v-1ae7b9bb">{{"已砍至"+item.$orig.curr_price+"元"}}</text><view class="price-box data-v-1ae7b9bb"><block wx:if="{{item.$orig.status}}"><text class="original-price data-v-1ae7b9bb">{{item.m0+item.$orig.price}}</text></block><block wx:if="{{item.$orig.timeMachine&&item.$orig.status==0}}"><view class="time data-v-1ae7b9bb"><uni-count-down vue-id="{{('26860f16-2-'+index)+','+('26860f16-1')}}" day="{{item.$orig.timeMachine.d}}" hour="{{item.$orig.timeMachine.h}}" minute="{{item.$orig.timeMachine.i}}" second="{{item.$orig.timeMachine.s}}" color="#fff" splitorColor="#000 !important" backgroundColorClass="color-base-bg" border-color="transparent" class="data-v-1ae7b9bb" bind:__l="__l"></uni-count-down><text class="end-txt data-v-1ae7b9bb">后结束</text></view></block></view></view></view></view><view class="goods-item-action data-v-1ae7b9bb"><view class="invitation-bargain data-v-1ae7b9bb"><block wx:for="{{item.l0}}" wx:for-item="recordItem" wx:for-index="recordIndex"><image src="{{recordItem.$orig.headimg==''?recordItem.g2:recordItem.g3}}" data-event-opts="{{[['error',[['memberImageError',[index,recordIndex]]]]]}}" binderror="__e" class="data-v-1ae7b9bb"></image></block><block wx:if="{{item.$orig.status==0}}"><text class="invitation-bargain-end color-base-text color-base-border data-v-1ae7b9bb">+</text></block><block wx:else><text class="invitation-bargain-end color-base-text color-base-border data-v-1ae7b9bb"><text class="icon-ellipsis iconfont data-v-1ae7b9bb"></text></text></block></view><block wx:if="{{item.$orig.status==0&&item.$orig.bargain_status==1}}"><button class="btn data-v-1ae7b9bb" type="default" data-event-opts="{{[['tap',[['goBargainDetail',['$0'],[[['dataList','',index]]]]]]]}}" bindtap="__e">继续砍价</button></block><block wx:if="{{item.$orig.status==1&&item.$orig.bargain_status==1}}"><block class="data-v-1ae7b9bb"><block wx:if="{{item.$orig.order_id==0}}"><button class="btn data-v-1ae7b9bb" type="default" data-event-opts="{{[['tap',[['goBargainDetail',['$0'],[[['dataList','',index]]]]]]]}}" bindtap="__e">购买商品</button></block><block wx:else><button class="btn data-v-1ae7b9bb" type="default" data-event-opts="{{[['tap',[['goBargainDetail',['$0'],[[['dataList','',index]]]]]]]}}" bindtap="__e">查看详情</button></block></block></block><block wx:if="{{item.$orig.status==2&&item.$orig.bargain_status==1}}"><button class="btn data-v-1ae7b9bb" type="default" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e">重新砍价</button></block></view></view></block><block wx:if="{{$root.g4==0}}"><ns-empty vue-id="{{('26860f16-3')+','+('26860f16-1')}}" isIndex="{{true}}" emptyBtn="{{({url:'/pages_promotion/bargain/list',text:'去逛逛'})}}" text="暂无砍价订单" class="data-v-1ae7b9bb" bind:__l="__l"></ns-empty></block></view></mescroll-uni></block><ns-login vue-id="26860f16-4" data-ref="login" class="data-v-1ae7b9bb vue-ref" bind:__l="__l"></ns-login><loading-cover vue-id="26860f16-5" data-ref="loadingCover" class="data-v-1ae7b9bb vue-ref" bind:__l="__l"></loading-cover></view>