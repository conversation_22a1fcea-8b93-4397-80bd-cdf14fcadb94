<page-meta page-style="{{themeColor}}"></page-meta><view class="page"><view class="pintuan-wrap"><view class="pintuan-list"><view class="pintuan-bg"></view><view data-event-opts="{{[['tap',[['toPintuanDetail',['$0'],['groupDetail.pintuan_goods_id']]]]]}}" class="list-item" bindtap="__e"><view class="item-image"><image src="{{$root.g0}}" mode="widthFix" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e"></image></view><view class="item-desc"><view class="desc-title">{{groupDetail.sku_name}}</view><view class="pintuan-num"><text class="color-base-border">{{groupDetail.pintuan_num+"人团"}}</text><text class="color-base-text color-base-bg-light">{{"已成团"+groupDetail.group_num+"件"}}</text></view><view class="color-tip"><text class="color-base-text font-size-toolbar margin-right price-style large"><text class="font-size-tag price-btn price-style small">￥</text>{{''+groupDetail.order_money+''}}</text><text class="old-price font-size-tag price-font">{{"￥"+groupDetail.discount_price}}</text></view></view></view></view><view class="pintuan-clustering"><block wx:if="{{groupDetail.pintuan_status==2}}"><view class="tips countdown"><view class="tips-title">还差<text class="color-base-text">{{kill}}</text>人成团</view>距结束还剩<block wx:if="{{groupDetail.timeMachine}}"><uni-count-down vue-id="42ce7ef8-1" day="{{groupDetail.timeMachine.d}}" hour="{{groupDetail.timeMachine.h}}" minute="{{groupDetail.timeMachine.i}}" second="{{groupDetail.timeMachine.s}}" color="#fff" splitorColor="#333 !important" backgroundColor="#000" bind:__l="__l"></uni-count-down></block></view></block><block wx:else><block wx:if="{{groupDetail.pintuan_status==3}}"><view class="tips text"><text>恭喜您，拼团成功</text></view></block><block wx:else><block wx:if="{{groupDetail.pintuan_status==1}}"><view class="tips text"><text>很遗憾，拼团失败</text></view></block><block wx:else><block wx:if="{{groupDetail.status!=1}}"><view class="tips text"><text>活动已结束，很遗憾拼团失败</text></view></block></block></block></block><block wx:if="{{groupDetail&&groupDetail.member_list}}"><view class="headimg-group"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index<5}}"><view class="group-member"><block wx:if="{{groupDetail.head_id==item.$orig.member_id}}"><view class="mark ns-gradient-promotionpages-pintuan-share-share">团长</view></block><view class="member-face"><image src="{{item.$orig.member_img?item.g1:item.g2}}" mode="aspectFill"></image></view></view></block></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="__i0__"><block wx:if="{{groupDetail.pintuan_status!=3}}"><view class="group-member"><view class="member-face"><image src="{{item.g3?item.g4:item.g5}}" mode="aspectFill"></image></view></view></block></block></view></block><block wx:if="{{groupDetail.status!=1}}"><view class="pintuan-btn-box"><block wx:if="{{groupDetail.pintuan_status==3}}"><button data-event-opts="{{[['tap',[['orderDetail',['$0'],['groupDetail']]]]]}}" class="one_btn pintuan-btn" bindtap="__e">查看订单</button></block><block wx:else><button class="disabled">活动已结束</button></block></view></block><block wx:else><block wx:if="{{groupDetail.pintuan_status==2}}"><view class="pintuan-btn-box"><button class="pintuan-btn" type="primary" data-event-opts="{{[['tap',[['openSharePopup',['$event']]]]]}}" bindtap="__e">邀请好友参团</button><button data-event-opts="{{[['tap',[['orderDetail',['$0'],['groupDetail']]]]]}}" class="one_btn pintuan-btn" bindtap="__e">查看订单</button></view></block><block wx:else><block wx:if="{{groupDetail.pintuan_status==1||groupDetail.pintuan_status==3}}"><view class="pintuan-btn-box"><button class="pintuan-btn" type="primary" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e">再次发起拼团</button><block wx:if="{{groupDetail.pintuan_status==3}}"><button data-event-opts="{{[['tap',[['orderDetail',['$0'],['groupDetail']]]]]}}" class="one_btn pintuan-btn" bindtap="__e">查看订单</button></block></view></block></block></block></view><view class="pintuan-playing"><view class="pintuan-step-title">拼团玩法</view><view class="pintuan-step-list"><view class="pintuan-step"><view class="step-img"><image src="{{$root.g6}}"></image></view><view class="step-text"><view>支付开团</view><view>或参团</view></view></view><view class="pintuan-step-point"><view></view><view></view><view></view></view><view class="pintuan-step"><view class="step-img"><image src="{{$root.g7}}"></image></view><view class="step-text"><view>邀请好友</view><view>参团</view></view></view><view class="pintuan-step-point"><view></view><view></view><view></view></view><view class="pintuan-step"><view class="step-img"><image src="{{$root.g8}}"></image></view><view class="step-text"><view>达到拼团</view><view>人数</view></view></view><view class="pintuan-step-point"><view></view><view></view><view></view></view><view class="pintuan-step"><view class="step-img"><image src="{{$root.g9}}"></image></view><view class="step-text"><view>组团成功</view><view>等待发货</view></view></view></view></view></view><loading-cover class="vue-ref" vue-id="42ce7ef8-2" data-ref="loadingCover" bind:__l="__l"></loading-cover><hover-nav vue-id="42ce7ef8-3" need="{{true}}" bind:__l="__l"></hover-nav><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><uni-popup class="share-popup vue-ref" vue-id="42ce7ef8-4" type="bottom" data-ref="sharePopup" bind:__l="__l" vue-slots="{{['default']}}"><view><view class="share-title">分享</view><view class="share-content"><view class="share-box"><button class="share-btn" plain="{{true}}" open-type="share"><view class="iconfont icon-share-friend"></view><text>分享给好友</text></button></view><view data-event-opts="{{[['tap',[['openPosterPopup',['$event']]]]]}}" class="share-box" bindtap="__e"><button class="share-btn" plain="{{true}}"><view class="iconfont icon-pengyouquan"></view><text>生成分享海报</text></button></view></view><view data-event-opts="{{[['tap',[['closeSharePopup',['$event']]]]]}}" class="share-footer" bindtap="__e"><text>取消分享</text></view></view></uni-popup><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="poster-layer" catchtouchmove="__e"><uni-popup class="vue-ref" vue-id="42ce7ef8-5" type="center" data-ref="posterPopup" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{poster!='-1'}}"><view class="poster-wrap"><view class="image-wrap"><image src="{{$root.g10}}" show-menu-by-longpress="{{true}}" mode="widthFix"></image><view data-event-opts="{{[['tap',[['closePosterPopup']]]]}}" class="close iconfont icon-close" bindtap="__e"></view></view><view data-event-opts="{{[['tap',[['saveGoodsPoster']]]]}}" class="save-btn" bindtap="__e">保存图片</view></view></block></uni-popup></view></view><privacy-popup class="vue-ref" vue-id="42ce7ef8-6" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>