require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/pintuan/payment"],{2259:function(n,t,e){"use strict";var a=e("fe97"),o=e.n(a);o.a},"4a67":function(n,t,e){"use strict";(function(n,t){var a=e("47a9");e("d381");a(e("3240"));var o=a(e("9f73"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"6d89":function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return r})),e.d(t,"a",(function(){return a}));var a={commonPayment:function(){return Promise.all([e.e("common/vendor"),e.e("components/common-payment/common-payment")]).then(e.bind(null,"47f2"))}},o=function(){var n=this.$createElement;this._self._c},r=[]},"8ea4":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{api:{payment:"/pintuan/api/ordercreate/payment",calculate:"/pintuan/api/ordercreate/calculate",create:"/pintuan/api/ordercreate/create"}}},provide:function(){return{promotion:this.promotion.bind(this)}},onShow:function(){this.$refs.payment&&this.$refs.payment.pageShow()},methods:{promotion:function(n){if(n.pintuan_info)return{title:"拼团",content:n.pintuan_info.pintuan_name}}}}},"9f73":function(n,t,e){"use strict";e.r(t);var a=e("6d89"),o=e("e07d");for(var r in o)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(r);e("2259");var i=e("828b"),u=Object(i["a"])(o["default"],a["b"],a["c"],!1,null,"e2510346",null,!1,a["a"],void 0);t["default"]=u.exports},e07d:function(n,t,e){"use strict";e.r(t);var a=e("8ea4"),o=e.n(a);for(var r in a)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(r);t["default"]=o.a},fe97:function(n,t,e){}},[["4a67","common/runtime","common/vendor"]]]);