<page-meta page-style="{{themeColor}}" class="data-v-4ab08be5"></page-meta><view class="data-v-4ab08be5"><block wx:if="{{storeToken}}"><view class="my_spell_category data-v-4ab08be5"><view data-event-opts="{{[['tap',[['categoryChange',['all']]]]]}}" class="category-item data-v-4ab08be5" bindtap="__e"><view class="{{['item-con','data-v-4ab08be5',pintuanStatus=='all'?'active color-base-text color-base-bg-before':'']}}">全部</view></view><block wx:for="{{pintuanStatusList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['categoryChange',['$0'],[[['pintuanStatusList','',index,'id']]]]]]]}}" class="category-item data-v-4ab08be5" bindtap="__e"><view class="{{['item-con','data-v-4ab08be5',item.id==pintuanStatus?'active color-base-text color-base-bg-before':'']}}">{{item.name}}</view></view></block></view></block><block wx:if="{{storeToken}}"><mescroll-uni vue-id="64e51b10-1" top="90" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" class="data-v-4ab08be5 vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-4ab08be5"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-list data-v-4ab08be5"><view class="list-header data-v-4ab08be5"><text class="state-time data-v-4ab08be5">{{"发起拼单 "+item.g0}}</text><text class="state-sign data-v-4ab08be5" style="{{'color:'+(pintuanState[item.$orig.pintuan_status].color)+';'}}">{{pintuanState[item.$orig.pintuan_status].text}}</text></view><view data-event-opts="{{[['tap',[['toshare',['$0'],[[['dataList','',index,'id']]]]]]]}}" class="list-body data-v-4ab08be5" bindtap="__e"><view class="list-body-img data-v-4ab08be5"><image src="{{item.g1}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e" class="data-v-4ab08be5"></image></view><view class="shop-content data-v-4ab08be5"><view class="shop-title data-v-4ab08be5">{{item.$orig.sku_name}}</view><view class="pintuan-num data-v-4ab08be5">{{item.$orig.pintuan_num+"人拼单"}}</view><view class="pintuan-price price-style large data-v-4ab08be5"><text class="pintuan-price-icon price-style small data-v-4ab08be5">¥</text>{{''+item.g2[0]+''}}<text class="pintuan-price-icon price-style small data-v-4ab08be5">{{"."+item.g3[1]}}</text></view></view></view><block wx:if="{{item.$orig.pintuan_status==2}}"><view class="list-footer data-v-4ab08be5"><block wx:if="{{item.$orig.timeMachine}}"><view class="list-footer-time data-v-4ab08be5"><text class="data-v-4ab08be5">还剩</text><text class="color-base-text data-v-4ab08be5">{{item.$orig.pintuan_num-item.$orig.pintuan_count}}</text><text class="data-v-4ab08be5">人，剩余时间</text><view class="time-wrap data-v-4ab08be5"><uni-count-down class="time data-v-4ab08be5" vue-id="{{('64e51b10-2-'+index)+','+('64e51b10-1')}}" day="{{item.$orig.timeMachine.d}}" hour="{{item.$orig.timeMachine.h}}" minute="{{item.$orig.timeMachine.i}}" second="{{item.$orig.timeMachine.s}}" color="#909399" splitorColor="#909399" background-color="transparent" border-color="transparent" bind:__l="__l"></uni-count-down></view></view><button class="share-btn data-v-4ab08be5" type="primary" size="mini" data-event-opts="{{[['tap',[['toshare',['$0'],[[['dataList','',index,'id']]]]]]]}}" bindtap="__e">邀请好友</button></block><block wx:else><text class="data-v-4ab08be5">拼团失败</text></block></view></block><block wx:else><block wx:if="{{item.$orig.pintuan_status==3}}"><view class="list-footer data-v-4ab08be5"><view class="picture-box data-v-4ab08be5"><block wx:for="{{item.l0}}" wx:for-item="i" wx:for-index="j" wx:key="j"><block wx:if="{{j<4}}"><view class="img-box data-v-4ab08be5"><block wx:if="{{i.$orig.member_img}}"><image src="{{i.g4}}" mode="aspectFill" data-event-opts="{{[['error',[['memberImageError',[index,j]]]]]}}" binderror="__e" class="data-v-4ab08be5"></image></block><block wx:else><image src="{{i.g5}}" mode="aspectFill" class="data-v-4ab08be5"></image></block></view></block></block></view><button class="order-btn mini data-v-4ab08be5" type="primary" size="mini" data-event-opts="{{[['tap',[['toOrderDetail',['$0'],[[['dataList','',index,'order_id']]]]]]]}}" bindtap="__e">查看详情</button></view></block></block></view></block><block wx:if="{{$root.g6==0}}"><view style="padding-top:0;" class="data-v-4ab08be5"><ns-empty vue-id="{{('64e51b10-3')+','+('64e51b10-1')}}" isIndex="{{true}}" emptyBtn="{{({url:'/pages_promotion/pintuan/list',text:'去逛逛'})}}" text="暂无拼团订单" class="data-v-4ab08be5" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni></block><ns-login vue-id="64e51b10-4" data-ref="login" class="data-v-4ab08be5 vue-ref" bind:__l="__l"></ns-login><loading-cover vue-id="64e51b10-5" data-ref="loadingCover" class="data-v-4ab08be5 vue-ref" bind:__l="__l"></loading-cover><privacy-popup vue-id="64e51b10-6" data-ref="privacyPopup" class="data-v-4ab08be5 vue-ref" bind:__l="__l"></privacy-popup></view>