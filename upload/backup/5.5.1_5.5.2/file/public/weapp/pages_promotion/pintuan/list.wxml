<page-meta page-style="{{themeColor}}"></page-meta><view class="page" style="{{'background:'+(bgColor)+';'}}"><block wx:if="{{addonIsExist.pintuan}}"><mescroll-uni class="vue-ref" vue-id="07d0b732-1" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><view class="ns-adv"><ns-adv vue-id="{{('07d0b732-2')+','+('07d0b732-1')}}" keyword="NS_PINTUAN" bind:__l="__l"></ns-adv></view><block wx:if="{{$root.g0}}"><view class="goods-list single-column"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item margin-bottom"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index]]]]]]]}}" class="goods-img" bindtap="__e"><image src="{{item.m0}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image><block wx:if="{{item.m1!=''}}"><view class="color-base-bg goods-tag">{{item.m2}}</view></block></view><view class="info-wrap"><view class="name-wrap"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index]]]]]]]}}" class="goods-name" bindtap="__e"><text class="name-label">{{item.$orig.pintuan_num+"人团"}}</text>{{''+item.$orig.goods_name+''}}</view><view class="pintuan-info"><text class="pintuan-num">{{"已团"+item.$orig.sale_num+"件"}}</text></view></view><view class="lineheight-clear"><view class="discount-price"><text class="unit price-style small">{{item.m3}}</text><text class="price price-style large">{{item.g1[0]}}</text><text class="unit price-style small">{{"."+item.g2[1]}}</text></view></view><view class="pro-info"><view class="delete-price font-size-activity-tag color-tip price-font"><text class="font-size-tag lineheight-clear txt"></text><text class="unit">{{item.m4}}</text>{{''+item.$orig.price+''}}</view><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index]]]]]]]}}" bindtap="__e"><button class="mini" type="primary" size="mini">去拼团</button></view></view></view></view></block></view></block><block wx:if="{{!$root.g3}}"><view><ns-empty vue-id="{{('07d0b732-3')+','+('07d0b732-1')}}" textColor="#fff" isIndex="{{false}}" text="暂无拼团" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni></block><hover-nav vue-id="07d0b732-4" bind:__l="__l"></hover-nav><loading-cover class="vue-ref" vue-id="07d0b732-5" data-ref="loadingCover" bind:__l="__l"></loading-cover><privacy-popup class="vue-ref" vue-id="07d0b732-6" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>