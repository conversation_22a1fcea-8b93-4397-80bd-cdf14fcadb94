<page-meta page-style="{{themeColor}}" class="data-v-e465899e"></page-meta><view class="data-v-e465899e"><block wx:if="{{goodsSkuDetail&&goodsSkuDetail.config&&goodsSkuDetail.config.nav_bar_switch==0}}"><view class="page-header data-v-e465899e"><ns-navbar vue-id="55818a58-1" data="{{navbarData}}" isBack="{{true}}" class="data-v-e465899e" bind:__l="__l"></ns-navbar></view></block><goods-detail-view vue-id="55818a58-2" goodsSkuDetail="{{goodsSkuDetail}}" data-ref="goodsDetailView" class="data-v-e465899e vue-ref" bind:__l="__l" vue-slots="{{['price','entrance','business','articipation','action','fixedbtn']}}"><view slot="price"><block wx:if="{{goodsSkuDetail.timeMachine}}"><view class="goods-promotion data-v-e465899e"><view class="price-info data-v-e465899e"><view class="icon-box data-v-e465899e"><text class="iconfont icon-pintuan2 data-v-e465899e"></text></view><view class="price-box data-v-e465899e"><view class="promotion-text data-v-e465899e">拼团优惠</view><view class="sale-num data-v-e465899e"><view class="pintuan-num data-v-e465899e">{{goodsSkuDetail[goodsSkuDetail.pintuan_num_field]+'人团'}}</view><block wx:if="{{goodsSkuDetail.sale_show}}"><view class="data-v-e465899e">{{'已成团'+goodsSkuDetail.order_num+goodsSkuDetail.unit}}</view></block></view></view></view><view class="countdown data-v-e465899e"><view class="txt data-v-e465899e">距结束仅剩</view><view class="clockrun data-v-e465899e"><uni-count-down vue-id="{{('55818a58-3')+','+('55818a58-2')}}" day="{{goodsSkuDetail.timeMachine.d}}" hour="{{goodsSkuDetail.timeMachine.h}}" minute="{{goodsSkuDetail.timeMachine.i}}" second="{{goodsSkuDetail.timeMachine.s}}" splitorColor="#ffffff" backgroundColor="#ffffff" class="data-v-e465899e" bind:__l="__l"></uni-count-down></view></view></view></block><view class="group-wrap padding-top data-v-e465899e"><view class="goods-module-wrap data-v-e465899e"><text class="promotion-tag data-v-e465899e">拼团价</text><text class="price-symbol price-font data-v-e465899e">{{$root.m0}}</text><text class="price price-font data-v-e465899e">{{$root.g0[0]}}</text><text class="price-symbol price-font data-v-e465899e">{{"."+$root.g1[1]}}</text><block wx:if="{{goodsSkuDetail.price>0}}"><view class="market-price-wrap data-v-e465899e"><text class="unit price-font data-v-e465899e">{{$root.m1}}</text><text class="money price-font data-v-e465899e">{{goodsSkuDetail.price}}</text></view></block><view class="follow-and-share data-v-e465899e"><text data-event-opts="{{[['tap',[['openSharePopup']]]]}}" class="follow iconfont icon-share data-v-e465899e" bindtap="__e"></text><text data-event-opts="{{[['tap',[['editCollection']]]]}}" class="{{['share','iconfont','data-v-e465899e',whetherCollection==1?'icon-likefill color-base-text':'icon-guanzhu']}}" bindtap="__e"></text></view></view><block wx:if="{{goodsSkuDetail.pintuan_type=='ordinary'&&goodsSkuDetail.is_promotion==1}}"><view class="goods-module-wrap promotion-price-wrap data-v-e465899e"><text class="label data-v-e465899e">团长价</text><text class="price-symbol price-font data-v-e465899e">{{$root.m2}}</text><text class="price price-font data-v-e465899e">{{goodsSkuDetail.promotion_price}}</text></view></block><view class="goods-module-wrap info data-v-e465899e"><text class="sku-name-wrap data-v-e465899e">{{goodsSkuDetail.goods_name}}</text><block wx:if="{{goodsSkuDetail.introduction}}"><text class="introduction data-v-e465899e" style="{{'color:'+(goodsSkuDetail.config?goodsSkuDetail.config.introduction_color:'')+';'}}">{{''+goodsSkuDetail.introduction+''}}</text></block><block wx:if="{{goodsSkuDetail.label_name}}"><view class="goods-tag-list data-v-e465899e"><text class="tag-item data-v-e465899e">{{goodsSkuDetail.label_name}}</text></view></block><view class="logistics-wrap data-v-e465899e"><block wx:if="{{goodsSkuDetail.stock_show}}"><text class="data-v-e465899e">{{"库存 "+(goodsSkuDetail.stock+goodsSkuDetail.unit)}}</text></block><block wx:if="{{goodsSkuDetail.sale_show}}"><text class="data-v-e465899e">{{"销量 "+(goodsSkuDetail.sale_num+goodsSkuDetail.unit)}}</text></block></view></view></view><block wx:if="{{$root.g2}}"><view class="group-wrap swiper data-v-e465899e"><swiper class="spelling-block data-v-e465899e" vertical="true" autoplay="true" interval="5000"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="data-v-e465899e"><block wx:if="{{item.$orig.end_time>timestamp}}"><view class="item data-v-e465899e"><view class="user-logo data-v-e465899e"><image src="{{item.$orig.headimg?item.g3:item.g4.head}}" mode="aspectFill" data-event-opts="{{[['error',[['pintuanImageError',[index]]]]]}}" binderror="__e" class="data-v-e465899e"></image></view><block wx:if="{{item.$orig.nickname}}"><text class="user-name data-v-e465899e">{{item.$orig.nickname}}</text></block><view class="info data-v-e465899e"><block wx:if="{{item.$orig.timeMachine}}"><view class="tip data-v-e465899e">还差<text class="color-base-text data-v-e465899e">{{item.$orig.pintuan_num-item.$orig.pintuan_count+"人"}}</text>拼成</view><text class="color-tip font-size-activity-tag data-v-e465899e">剩余</text><uni-count-down vue-id="{{('55818a58-4-'+index)+','+('55818a58-2')}}" day="{{item.$orig.timeMachine.d}}" hour="{{item.$orig.timeMachine.h}}" minute="{{item.$orig.timeMachine.i}}" second="{{item.$orig.timeMachine.s}}" color="#909399" splitorColor="#909399 !important" background-color="transparent" borderColor="transparent" class="data-v-e465899e" bind:__l="__l"></uni-count-down></block></view><block wx:if="{{item.$orig.timeMachine}}"><button class="pin-btn mini data-v-e465899e" type="primary" size="mini" data-event-opts="{{[['tap',[['openPinTuan',['$0',item.$orig.pintuan_num-item.$orig.pintuan_count,item.$orig.end_time-item.$orig.currentTime,'$1','$2'],[[['newList','',index,'group_id']],[['newList','',index,'headimg']],[['newList','',index,'head_id']]]]]]]}}" bindtap="__e">去拼单</button></block><block wx:else><button class="pin-btn disabled mini data-v-e465899e" disabled="{{true}}" size="mini">已结束</button></block></view></block></swiper-item></block></swiper><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-e465899e"><uni-popup class="pintuan-popup-layer data-v-e465899e vue-ref" vue-id="{{('55818a58-5')+','+('55818a58-2')}}" type="center" data-ref="pintuanPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="layer data-v-e465899e"><view class="title data-v-e465899e">参与的拼单</view><block wx:if="{{currentPintuan}}"><view class="info data-v-e465899e">仅剩<text class="color-base-text data-v-e465899e">{{currentPintuan.pintuan_num}}</text>个名额，<block wx:if="{{openPopup}}"><uni-count-down vue-id="{{('55818a58-6')+','+('55818a58-5')}}" day="{{currentPintuan.timeMachine.d}}" hour="{{currentPintuan.timeMachine.h}}" minute="{{currentPintuan.timeMachine.i}}" second="{{currentPintuan.timeMachine.s}}" color="#333" splitorColor="#333" background-color="transparent" borderColor="transparent" class="data-v-e465899e" bind:__l="__l"></uni-count-down></block><text class="data-v-e465899e">后结束</text></view></block><image class="mask-layer-spelling-close data-v-e465899e" src="{{$root.g5}}" data-event-opts="{{[['tap',[['closePinTuanPopup']]]]}}" bindtap="__e"></image><view class="user-list data-v-e465899e"><scroll-view class="imgX data-v-e465899e" scroll-x="{{true}}"><view class="item data-v-e465899e"><text class="boss color-base-bg data-v-e465899e">拼主</text><image src="{{currentPintuan.headimg!=''?$root.g6:$root.g7.head}}" mode="aspectFill" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e" class="data-v-e465899e"></image></view><view class="item data-v-e465899e"><image src="{{$root.g8}}" class="data-v-e465899e"></image></view></scroll-view></view><button type="primary" data-event-opts="{{[['tap',[['joinPintuan']]]]}}" bindtap="__e" class="data-v-e465899e">参与拼单</button></view></uni-popup></view></view></block></view><view data-event-opts="{{[['tap',[['pintuan',['$event']]]]]}}" class="item selected-sku-spec data-v-e465899e" bindtap="__e" slot="entrance" wx:if="{{goodsSkuDetail.sku_spec_format}}"><view class="label data-v-e465899e">选择</view><view class="box data-v-e465899e"><block wx:for="{{goodsSkuDetail.sku_spec_format}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text class="data-v-e465899e">{{item.spec_name+"/"+item.spec_value_name}}</text></block></view><text class="iconfont icon-right data-v-e465899e"></text></view><ns-goods-sku vue-id="{{('55818a58-7')+','+('55818a58-2')}}" goods-id="{{goodsSkuDetail.goods_id}}" goodsDetail="{{goodsSkuDetail}}" data-ref="goodsSku" data-event-opts="{{[['^hideSkuPop',[['refreshPintuan']]],['^refresh',[['refreshGoodsSkuDetail']]]]}}" bind:hideSkuPop="__e" bind:refresh="__e" class="data-v-e465899e vue-ref" slot="business" wx:if="{{goodsSkuDetail.goods_id}}" bind:__l="__l"></ns-goods-sku><view slot="articipation"><view class="diy-process-step data-v-e465899e"><view class="goods-img-content data-v-e465899e">参与流程</view><view class="process-step-box data-v-e465899e"><view class="process-step-item data-v-e465899e"><view class="process-step-icon data-v-e465899e"><text class="iconfont icon-dingdan4 data-v-e465899e"></text></view><view class="process-step-content data-v-e465899e"><view class="data-v-e465899e">1、购买开团</view><view class="data-v-e465899e">点击右下角拼团购买，成功即开团</view></view></view><view class="process-step-line data-v-e465899e"><view class="data-v-e465899e"></view></view><view class="process-step-item data-v-e465899e"><view class="process-step-icon data-v-e465899e"><text class="iconfont icon-yaoqinghaoyou data-v-e465899e"></text></view><view class="process-step-content data-v-e465899e"><view class="data-v-e465899e">2、邀请好友拼团</view><view class="data-v-e465899e">邀请好友一起来参加你的拼团</view></view></view><view class="process-step-line data-v-e465899e"><view class="data-v-e465899e"></view></view><view class="process-step-item data-v-e465899e"><view class="process-step-icon data-v-e465899e"><text class="iconfont icon-chenggong data-v-e465899e"></text></view><view class="process-step-content data-v-e465899e"><view class="data-v-e465899e">3、拼团成功</view><view class="data-v-e465899e">有效期内达到拼团人数即拼团成功</view></view></view></view></view><block wx:if="{{goodsSkuDetail.remark}}"><view class="goods-detail-tab rule-wrap data-v-e465899e"><view class="detail-tab data-v-e465899e"><view class="tab-item data-v-e465899e">活动规则</view></view><view class="content data-v-e465899e">{{goodsSkuDetail.remark}}</view></view></block></view><ns-goods-action vue-id="{{('55818a58-8')+','+('55818a58-2')}}" safeArea="{{isIphoneX}}" class="data-v-e465899e" slot="action" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{goodsSkuDetail.goods_state==1}}"><block wx:if="{{pintuanPopShow}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="mask data-v-e465899e" bindtap="__e"></view></block><block wx:if="{{pintuanPopShow&&groupDetail}}"><view class="pintuan-pop data-v-e465899e"><block wx:if="{{groupDetail.status==2}}"><block class="data-v-e465899e"><view class="pintuan-pop-head data-v-e465899e"><view class="pintuan-headimg data-v-e465899e"><image src="{{$root.g9}}" class="data-v-e465899e"></image></view><view class="pintuan-txt data-v-e465899e"><text class="data-v-e465899e">{{$root.m3}}</text>邀请你加入TA的团</view></view><view class="pintuan-pop-time data-v-e465899e">仅剩<text class="data-v-e465899e">{{$root.m4}}</text>个名额，距结束<uni-count-down vue-id="{{('55818a58-9')+','+('55818a58-8')}}" day="{{groupDetail.timeMachine.d}}" hour="{{groupDetail.timeMachine.h}}" minute="{{groupDetail.timeMachine.i}}" second="{{groupDetail.timeMachine.s}}" color="#FFF" splitorColor="#000000" backgroundColor="#000000" class="data-v-e465899e" bind:__l="__l"></uni-count-down></view><block wx:if="{{groupDetail.pintuan_num<=5}}"><view class="pintuan-pop-member data-v-e465899e"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index"><view class="member-item data-v-e465899e"><image src="{{item.$orig.member_img?item.g10:item.g11}}" class="data-v-e465899e"></image><block wx:if="{{item.$orig.member_id==groupDetail.head_id}}"><view style="{{'background:'+(themeStyle.main_color)+';'}}" class="data-v-e465899e"><image class="pintuan-text data-v-e465899e" mode="widthFix" src="{{item.g12}}"></image></view></block></view></block><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="member-item icon data-v-e465899e" bindtap="__e"><text class="iconfont icon-add1 data-v-e465899e"></text></view></block></view></block><block wx:else><view class="pintuan-pop-member more data-v-e465899e"><view class="member-item-box data-v-e465899e"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index"><block wx:if="{{index<5}}"><view class="member-item data-v-e465899e"><image src="{{item.$orig.member_img?item.g13:item.g14}}" class="data-v-e465899e"></image><block wx:if="{{item.$orig.member_id==groupDetail.head_id}}"><view style="{{'background:'+(themeStyle.main_color)+';'}}" class="data-v-e465899e"><image class="pintuan-text data-v-e465899e" mode="widthFix" src="{{item.g15}}"></image></view></block></view></block></block><view class="member-item icon data-v-e465899e"><text class="iconfont icon-caidan data-v-e465899e"></text></view></view><view data-event-opts="{{[['tap',[['pintuan',[false]]]]]}}" class="member-item icon data-v-e465899e" bindtap="__e"><text class="iconfont icon-add1 data-v-e465899e"></text></view></view></block></block></block><block wx:else><block class="data-v-e465899e"><view class="pintuan-pop-head data-v-e465899e"><view class="pintuan-headimg data-v-e465899e"><image src="{{$root.g16}}" class="data-v-e465899e"></image></view><view class="pintuan-txt data-v-e465899e"><text class="data-v-e465899e">{{$root.m5}}</text>{{'的团 '+(groupDetail.status=='1'?'已关闭':'已完成')+''}}</view></view><view class="pintuan-pop-time data-v-e465899e">非常抱歉，您来晚了一步~</view><view class="pintuan-pop-member txt data-v-e465899e"><view class="pintuan-member-right data-v-e465899e">点击下方开团</view><view class="pintuan-member-left data-v-e465899e"><image src="{{$root.g17}}" mode="widthFix" class="data-v-e465899e"></image></view></view></block></block></view></block><ns-goods-action-icon vue-id="{{('55818a58-10')+','+('55818a58-8')}}" text="首页" icon="icon-shouye1" data-event-opts="{{[['^click',[['goHome']]]]}}" bind:click="__e" class="data-v-e465899e" bind:__l="__l"></ns-goods-action-icon><ns-goods-action-icon vue-id="{{('55818a58-11')+','+('55818a58-8')}}" text="客服" icon="icon-kefu" send-data="{{contactData}}" chatParam="{{chatRoomParams}}" class="data-v-e465899e" bind:__l="__l"></ns-goods-action-icon><ns-goods-action-icon vue-id="{{('55818a58-12')+','+('55818a58-8')}}" text="购物车" cornerMarkBg="{{themeStyle.goods_detail.goods_cart_num_corner}}" icon="icon-gouwuche2" corner-mark="{{cartNumber>0?cartNumber+'':''}}" data-event-opts="{{[['^click',[['goCart']]]]}}" bind:click="__e" class="data-v-e465899e" bind:__l="__l"></ns-goods-action-icon><block wx:if="{{goodsSkuDetail.goods_stock<goodsSkuDetail.pintuan_num&&!goodsSkuDetail.sku_spec_format}}"><ns-goods-action-button class="goods-action-button active4 data-v-e465899e" vue-id="{{('55818a58-13')+','+('55818a58-8')}}" disabled-text="库存不足" disabled="{{true}}" bind:__l="__l"></ns-goods-action-button></block><block wx:else><block wx:if="{{goodsSkuDetail.is_single_buy==1}}"><ns-goods-action-button class="{{['goods-action-button','data-v-e465899e',goodsSkuDetail.is_single_buy==1?'active1':'']}}" vue-id="{{('55818a58-14')+','+('55818a58-8')}}" text-price="{{'¥ '+goodsSkuDetail.price}}" text="单独购买" backgroundColor="{{themeStyle.goods_detail.goods_btn_color_shallow}}" background="linear-gradient(to right, #ffd01e, #ff8917)" data-event-opts="{{[['^click',[['buyNow']]]]}}" bind:click="__e" bind:__l="__l"></ns-goods-action-button></block><block wx:if="{{goodsSkuDetail.pintuan_type=='ordinary'}}"><ns-goods-action-button class="{{['goods-action-button','data-v-e465899e',goodsSkuDetail.is_single_buy==1?'active2':'active4']}}" vue-id="{{('55818a58-15')+','+('55818a58-8')}}" text-price="{{'¥ '+goodsSkuDetail.show_price}}" backgroundColor="{{themeStyle.goods_detail.goods_btn_color}}" textColor="{{themeStyle.btn_text_color}}" text="发起拼团" data-event-opts="{{[['^click',[['pintuan',[true]]]]]}}" bind:click="__e" bind:__l="__l"></ns-goods-action-button></block><block wx:if="{{goodsSkuDetail.pintuan_type=='ladder'}}"><ns-goods-action-button class="{{['goods-action-button','data-v-e465899e',goodsSkuDetail.is_single_buy==1?'active2':'active4']}}" vue-id="{{('55818a58-16')+','+('55818a58-8')}}" text-price="{{'¥ '+$root.m6}}" backgroundColor="{{themeStyle.goods_detail.goods_btn_color}}" textColor="{{themeStyle.btn_text_color}}" text="发起拼团" data-event-opts="{{[['^click',[['pintuan',[true]]]]]}}" bind:click="__e" bind:__l="__l"></ns-goods-action-button></block></block></block><block wx:else><ns-goods-action-button class="goods-action-button active3 data-v-e465899e" vue-id="{{('55818a58-17')+','+('55818a58-8')}}" disabled-text="该商品已下架" disabled="{{true}}" bind:__l="__l"></ns-goods-action-button></block></ns-goods-action><view data-event-opts="{{[['tap',[['pintuan']]]]}}" class="fiexd-icon data-v-e465899e" bindtap="__e" slot="fixedbtn" wx:if="{{groupDetail&&groupDetail.status==2}}"><block wx:if="{{goodsSkuDetail.pintuan_type=='ordinary'}}"><view class="data-v-e465899e"><text class="data-v-e465899e">{{$root.g18+"元"}}</text><text class="data-v-e465899e">去开团</text></view></block><block wx:if="{{goodsSkuDetail.pintuan_type=='ladder'}}"><view class="data-v-e465899e"><text class="data-v-e465899e">{{$root.g19+"元"}}</text><text class="data-v-e465899e">去开团</text></view></block><image mode="widthFix" src="{{$root.g20}}" class="data-v-e465899e"></image></view></goods-detail-view><block wx:if="{{showTop}}"><to-top bind:toTop="__e" vue-id="55818a58-18" data-event-opts="{{[['^toTop',[['scrollToTopNative']]]]}}" class="data-v-e465899e" bind:__l="__l"></to-top></block><ns-login vue-id="55818a58-19" data-ref="login" class="data-v-e465899e vue-ref" bind:__l="__l"></ns-login><loading-cover vue-id="55818a58-20" data-ref="loadingCover" class="data-v-e465899e vue-ref" bind:__l="__l"></loading-cover><privacy-popup vue-id="55818a58-21" data-ref="privacyPopup" class="data-v-e465899e vue-ref" bind:__l="__l"></privacy-popup></view>