<page-meta page-style="{{themeColor}}"></page-meta><view><mescroll-uni class="vue-ref" vue-id="d882abbc-1" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><view class="cf-container color-line-border"><view class="tab"><view data-event-opts="{{[['tap',[['changeState',['all']]]]]}}" bindtap="__e"><text class="{{[status=='all'?'color-base-text active color-base-border-bottom':'']}}">全部</text></view><view data-event-opts="{{[['tap',[['changeState',['to_use']]]]]}}" bindtap="__e"><text class="{{[status=='to_use'?'color-base-text active color-base-border-bottom':'']}}">待使用</text></view><view data-event-opts="{{[['tap',[['changeState',['used']]]]]}}" bindtap="__e"><text class="{{[status=='used'?'color-base-text active color-base-border-bottom':'']}}">已使用</text></view><view data-event-opts="{{[['tap',[['changeState',['expire']]]]]}}" bindtap="__e"><text class="{{[status=='expire'?'color-base-text active color-base-border-bottom':'']}}">已过期</text></view></view></view><block wx:if="{{$root.g0>0}}"><view class="card-box"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view><view class="card-item"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index,'member_card_id']]]]]]]}}" class="card-content" bindtap="__e"><view class="card-img"><image src="{{item.g1}}" mode="aspectFill"></image><block wx:if="{{item.$orig.card_right_type=='balance'}}"><view class="card-label">{{item.$orig.balance+"元储值卡"}}</view></block><block wx:if="{{item.$orig.card_right_type=='goods'}}"><view class="card-label-img"><image src="{{item.g2}}" mode="heightFix"></image></view></block></view></view><block wx:if="{{item.$orig.status=='to_use'}}"><view class="button"><block wx:if="{{item.m0}}"><view class="button-left"><button class="mini" size="mini" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" bindtap="__e">送给朋友</button></view></block><block wx:if="{{!item.$orig.is_transfer}}"><view class="button-right"><button class="mini" size="mini" type="primary" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" bindtap="__e">立即使用</button></view></block></view></block><block wx:else><view class="button"><view class="button-right no-use"><block wx:if="{{item.$orig.status=='used'}}"><button class="mini" size="mini" type="primary" data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" bindtap="__e">已使用</button></block><block wx:if="{{item.$orig.status=='expire'}}"><button class="mini" size="mini" type="primary" data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" bindtap="__e">已过期</button></block></view></view></block></view></view></block></view></block><block wx:else><view class="card-no-data"><view class="card-image"><image mode="widthFix" src="{{$root.g3}}"></image></view><view class="text">暂无卡片记录</view><view class="btn"><button type="primary" size="mini" data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" bindtap="__e">去赠送</button></view></view></block></view></mescroll-uni><loading-cover class="vue-ref" vue-id="d882abbc-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>