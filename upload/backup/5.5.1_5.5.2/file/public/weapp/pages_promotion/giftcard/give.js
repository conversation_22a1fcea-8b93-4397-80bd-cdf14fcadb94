require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/giftcard/give"],{"450a":function(e,t,i){"use strict";i.r(t);var o=i("ae9f"),n=i.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);t["default"]=n.a},"6e58":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return o}));var o={nsLogin:function(){return Promise.all([i.e("common/vendor"),i.e("components/ns-login/ns-login")]).then(i.bind(null,"2910"))},uniPopup:function(){return i.e("components/uni-popup/uni-popup").then(i.bind(null,"d745"))}},n=function(){var e=this,t=e.$createElement,i=(e._self._c,"-1"!=e.poster?e.$util.img(e.poster):null),o=e.shareMask?e.$util.img("public/uniapp/giftcard/wechat_share.png"):null;e._isMounted||(e.e0=function(t){return e.$util.redirectTo("/pages_promotion/giftcard/list")},e.e1=function(t){e.shareMask=!1}),e.$mp.data=Object.assign({},{$root:{g0:i,g1:o}})},r=[]},"6ecab":function(e,t,i){"use strict";i.r(t);var o=i("6e58"),n=i("450a");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);i("98bf");var s=i("828b"),a=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"0b05d16e",null,!1,o["a"],void 0);t["default"]=a.exports},"98bf":function(e,t,i){"use strict";var o=i("dd87"),n=i.n(o);n.a},ae9f:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{memberCardId:0,cardInfo:null,message:"",poster:"-1",posterMsg:"",posterHeight:0,no:"",shareMask:!1}},onLoad:function(e){var t=this;if(e.member_card_id&&(this.memberCardId=e.member_card_id),e.scene){var i=decodeURIComponent(e.scene);i=i.split("&"),i.length&&i.forEach((function(e){-1!=e.indexOf("member_card_id")&&(t.memberCardId=e.split("-")[1])}))}},onShow:function(){this.getData()},methods:{getData:function(){var e=this;this.$api.sendRequest({url:"/giftcard/api/membercard/detail",data:{member_card_id:this.memberCardId},success:function(t){t.code>=0?(e.cardInfo=t.data,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):(e.$util.showToast({title:t.message,mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages_promotion/giftcard/member")}),1500))},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},maskShow:function(){this.shareMask=!0,this.closeSharePopup()},openSharePopup:function(){var e=this;this.$api.sendRequest({url:"/giftcard/api/membercard/blessing",data:{member_card_id:e.memberCardId,blessing:e.message?e.message:"赠你一份好礼，祝你每天都开心快乐！"},success:function(t){t.code>=0?(e.no=t.data,e.$refs.sharePopup.open(),e.setPublicShare()):e.$util.showToast({title:t.message})},fail:function(e){console.log(e)}})},closeSharePopup:function(){this.$refs.sharePopup.close()},copyUrl:function(){var e=this,t="嘿！朋友送你一张礼品卡，快来领取吧~"+this.$config.h5Domain+"/pages_promotion/giftcard/give_info?no="+this.no;this.memberInfo&&this.memberInfo.member_id&&(t+="&source_member="+this.memberInfo.member_id),this.$util.copy(t,(function(){e.closeSharePopup()}))},openPosterPopup:function(){var t=this;this.getGoodsPoster(),this.$refs.sharePopup.close(),this.$refs.posterPopup.open(),"-1"!=this.poster&&setTimeout((function(){var i=e.createSelectorQuery().in(t).select(".poster-layer .image-wrap");i.fields({size:!0},(function(e){var i=e.width,o=parseFloat((740/i).toFixed(2));""!=t.storeToken?t.posterHeight=parseInt(1240/o):t.posterHeight=parseInt(1100/o)})).exec()}),100)},closePosterPopup:function(){this.$refs.posterPopup.close()},getGoodsPoster:function(){var t=this;e.showLoading({title:"海报生成中..."});var i={no:this.no};this.memberInfo&&this.memberInfo.member_id&&(i.source_member=this.memberInfo.member_id),this.$api.sendRequest({url:"/giftcard/api/membercard/poster",data:{page:"/pages_promotion/giftcard/give_info",qrcode_param:JSON.stringify(i)},success:function(i){0==i.code?t.poster=i.data.path+"?time="+(new Date).getTime():t.posterMsg=i.message,e.hideLoading()},fail:function(t){e.hideLoading()}})},setPublicShare:function(){var e=this.$config.h5Domain+"/pages_promotion/giftcard/give_info?no="+this.no;this.memberInfo&&this.memberInfo.member_id&&(e+="&source_member="+this.memberInfo.member_id),this.$util.setPublicShare({title:this.cardInfo.card_name,desc:this.message?this.message:"赠你一份好礼，祝你每天都开心快乐！",link:e,imgUrl:this.$util.img(this.cardInfo.card_cover.split(",")[0])},(function(e){}))}},onShareAppMessage:function(){var e="/pages_promotion/giftcard/give_info?no="+this.no;return this.memberInfo&&this.memberInfo.member_id&&(e+="&source_member="+this.memberInfo.member_id),{title:"朋友送你一张礼品卡，快来领取吧~",imageUrl:this.$util.img(this.cardInfo.card_cover.split(",")[0]),path:e,success:function(e){},fail:function(e){},complete:function(e){}}}};t.default=i}).call(this,i("df3c")["default"])},d72f:function(e,t,i){"use strict";(function(e,t){var o=i("47a9");i("d381");o(i("3240"));var n=o(i("6ecab"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(n.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},dd87:function(e,t,i){}},[["d72f","common/runtime","common/vendor"]]]);