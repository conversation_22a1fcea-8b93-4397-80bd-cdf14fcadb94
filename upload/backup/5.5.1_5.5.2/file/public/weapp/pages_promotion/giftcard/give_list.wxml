<page-meta page-style="{{themeColor}}"></page-meta><view><mescroll-uni class="vue-ref" vue-id="36e63498-1" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g0>0}}"><view class="card-box"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view><view class="card-item"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index,'member_card_id']]]]]]]}}" class="card-content" bindtap="__e"><view class="card-img"><image src="{{item.g1}}" mode="aspectFill"></image><block wx:if="{{item.$orig.card_right_type=='balance'}}"><view class="card-label">{{item.$orig.balance+"元储值卡"}}</view></block><block wx:if="{{item.$orig.card_right_type=='goods'}}"><view class="card-label-img"><image src="{{item.g2}}" mode="heightFix"></image></view></block></view></view></view></view></block></view></block><block wx:else><view class="card-no-data"><view class="card-image"><image mode="widthFix" src="{{$root.g3}}"></image></view><view class="text">暂无卡片记录</view><view class="btn"><button type="primary" size="mini" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e">去赠送</button></view></view></block></view></mescroll-uni><loading-cover class="vue-ref" vue-id="36e63498-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>