<page-meta page-style="{{themeColor}}" class="data-v-0b05d16e"></page-meta><view class="page data-v-0b05d16e"><view class="detail-box data-v-0b05d16e"><view class="title data-v-0b05d16e">文字祝福<text class="data-v-0b05d16e">(自定义)</text></view><view class="textarea data-v-0b05d16e"><textarea maxlength="15" placeholder="赠你一份好礼，祝你每天都开心快乐！" placeholder-style="font-size:28rpx;color:#999999" data-event-opts="{{[['input',[['__set_model',['','message','$event',[]]]]]]}}" value="{{message}}" bindinput="__e" class="data-v-0b05d16e"></textarea></view><view class="bottom-btn data-v-0b05d16e"><button type="primary" data-event-opts="{{[['tap',[['openSharePopup',['$event']]]]]}}" bindtap="__e" class="data-v-0b05d16e">送给朋友</button><button data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="data-v-0b05d16e">放入卡包</button></view></view><ns-login vue-id="0efc402a-1" data-ref="login" class="data-v-0b05d16e vue-ref" bind:__l="__l"></ns-login><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-0b05d16e"><uni-popup class="share-popup data-v-0b05d16e vue-ref" vue-id="0efc402a-2" type="bottom" data-ref="sharePopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="data-v-0b05d16e"><view class="share-title data-v-0b05d16e">分享</view><view class="share-content data-v-0b05d16e"><view class="share-box data-v-0b05d16e"><button class="share-btn data-v-0b05d16e" plain="{{true}}" open-type="share"><view class="iconfont icon-share-friend data-v-0b05d16e"></view><text class="data-v-0b05d16e">分享给好友</text></button></view><view data-event-opts="{{[['tap',[['openPosterPopup',['$event']]]]]}}" class="share-box data-v-0b05d16e" bindtap="__e"><button class="share-btn data-v-0b05d16e" plain="{{true}}"><view class="iconfont icon-pengyouquan data-v-0b05d16e"></view><text class="data-v-0b05d16e">生成分享海报</text></button></view></view><view data-event-opts="{{[['tap',[['closeSharePopup',['$event']]]]]}}" class="share-footer data-v-0b05d16e" bindtap="__e"><text class="data-v-0b05d16e">取消分享</text></view></view></uni-popup></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-0b05d16e"><uni-popup class="poster-layer data-v-0b05d16e vue-ref" vue-id="0efc402a-3" type="bottom" data-ref="posterPopup" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{poster!='-1'}}"><view class="data-v-0b05d16e"><view class="image-wrap data-v-0b05d16e"><image src="{{$root.g0}}" show-menu-by-longpress="{{true}}" class="data-v-0b05d16e"></image></view><view data-event-opts="{{[['tap',[['saveGoodsPoster']]]]}}" class="save data-v-0b05d16e" bindtap="__e">保存图片</view></view><view data-event-opts="{{[['tap',[['closePosterPopup']]]]}}" class="close iconfont icon-close data-v-0b05d16e" bindtap="__e"></view></block><block wx:else><view class="msg data-v-0b05d16e">{{posterMsg}}</view></block></uni-popup><block wx:if="{{shareMask}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="wechat-share data-v-0b05d16e" bindtap="__e"><image src="{{$root.g1}}" mode="widthFix" class="data-v-0b05d16e"></image></view></block></view></view>