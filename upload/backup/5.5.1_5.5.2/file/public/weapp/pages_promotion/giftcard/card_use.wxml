<page-meta page-style="{{themeColor}}" class="data-v-2f11f680"></page-meta><view class="data-v-2f11f680"><view class="{{['order-container','data-v-2f11f680',(isIphoneX)?'safe-area':'']}}"><view class="payment-navbar data-v-2f11f680" style="{{'padding-top:'+(menuButtonBounding.top+'px')+';'+('height:'+(menuButtonBounding.height+'px')+';')}}"><view class="nav-wrap data-v-2f11f680"><text data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="iconfont icon-back_light data-v-2f11f680" bindtap="__e"></text><view class="navbar-title data-v-2f11f680">确认订单</view></view></view><view class="payment-navbar-block data-v-2f11f680" style="{{'height:'+(menuButtonBounding.bottom+'px')+';'}}"></view><scroll-view class="order-scroll-container data-v-2f11f680" scroll-y="true"><view class="payment-navbar-block data-v-2f11f680"></view><block wx:if="{{paymentData}}"><block wx:if="{{paymentData.is_virtual}}"><view class="mobile-wrap data-v-2f11f680"><view class="tips color-base-text data-v-2f11f680"><text class="iconfont icon-gantanhao data-v-2f11f680"></text>购买虚拟类商品需填写手机号，方便商家与您联系</view><view class="form-group data-v-2f11f680"><text class="icon data-v-2f11f680"><image src="{{$root.g0}}" mode="widthFix" class="data-v-2f11f680"></image></text><text class="text data-v-2f11f680">手机号码</text><input class="input data-v-2f11f680" type="number" maxlength="11" placeholder="请输入您的手机号码" placeholder-class="color-tip placeholder" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['orderCreateData.delivery.member_address']]]]]}}" value="{{orderCreateData.delivery.member_address.mobile}}" bindinput="__e"/></view></view></block><block wx:else><block wx:if="{{$root.g1>1}}"><view class="delivery-mode data-v-2f11f680"><view class="action data-v-2f11f680"><block wx:for="{{goodsData.delivery.express_type}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectDeliveryType',['$0'],[[['goodsData.delivery.express_type','',index]]]]]]]}}" class="{{['data-v-2f11f680',(item.name==orderCreateData.delivery.delivery_type)?'active':'']}}" bindtap="__e">{{''+item.title+''}}<view class="out-radio data-v-2f11f680"></view></view></block></view></view></block><block wx:if="{{orderCreateData.delivery.delivery_type=='express'}}"><view class="{{['address-box','data-v-2f11f680',($root.g2<=1)?'not-delivery-type':'']}}"><block wx:if="{{memberAddress}}"><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="info-wrap data-v-2f11f680" bindtap="__e"><view class="content data-v-2f11f680"><text class="name data-v-2f11f680">{{memberAddress.name?memberAddress.name:''}}</text><text class="mobile data-v-2f11f680">{{memberAddress.mobile?memberAddress.mobile:''}}</text><view class="desc-wrap data-v-2f11f680">{{''+(memberAddress.full_address?memberAddress.full_address:'')+"\n\t\t\t\t\t\t\t\t\t"+(memberAddress.address?memberAddress.address:'')+''}}</view></view><text class="cell-more iconfont icon-right data-v-2f11f680"></text></view></block><block wx:else><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="empty-wrap data-v-2f11f680" bindtap="__e"><view class="info data-v-2f11f680">请设置收货地址</view><view class="cell-more data-v-2f11f680"><view class="iconfont icon-right data-v-2f11f680"></view></view></view></block><image class="address-line data-v-2f11f680" src="{{$root.g3}}"></image></view></block><block wx:if="{{orderCreateData.delivery.delivery_type=='local'}}"><view class="{{['address-box','data-v-2f11f680',($root.g4<=1)?'not-delivery-type':'']}}"><block wx:if="{{localMemberAddress}}"><view class="data-v-2f11f680"><block wx:if="{{$root.g5}}"><block class="data-v-2f11f680"><block wx:if="{{storeInfo}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="local-delivery-store data-v-2f11f680" bindtap="__e"><view class="info data-v-2f11f680">由<text class="store-name data-v-2f11f680">{{storeInfo.store_name}}</text>提供配送</view><view class="cell-more data-v-2f11f680"><text class="data-v-2f11f680">点击切换</text><text class="iconfont icon-right data-v-2f11f680"></text></view></view></block><block wx:else><view class="local-delivery-store data-v-2f11f680"><view class="info data-v-2f11f680"><text class="store-name data-v-2f11f680">您的附近没有可配送的门店，请选择其他配送方式</text></view></view></block></block></block><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="info-wrap local data-v-2f11f680" bindtap="__e"><view class="content data-v-2f11f680"><text class="name data-v-2f11f680">{{localMemberAddress.name?localMemberAddress.name:''}}</text><text class="mobile data-v-2f11f680">{{localMemberAddress.mobile?localMemberAddress.mobile:''}}</text><view class="desc-wrap data-v-2f11f680">{{''+(localMemberAddress.full_address?localMemberAddress.full_address:'')+"\n\t\t\t\t\t\t\t\t\t\t"+(localMemberAddress.address?localMemberAddress.address:'')+''}}</view></view><text class="cell-more iconfont icon-right data-v-2f11f680"></text></view><block wx:if="{{calculateGoodsData.config.local.is_use&&calculateGoodsData.delivery.local.info.time_is_open==1}}"><view class="local-box data-v-2f11f680"><view data-event-opts="{{[['tap',[['localtime',['']]]]]}}" class="pick-block data-v-2f11f680" bindtap="__e"><view class="title font-size-base data-v-2f11f680">送达时间</view><view class="time-picker data-v-2f11f680"><text class="{{['data-v-2f11f680',(!deliveryTime)?'color-tip':'']}}">{{deliveryTime?deliveryTime:'请选择送达时间'}}</text><text class="iconfont icon-right cell-more data-v-2f11f680"></text></view></view></view></block></view></block><block wx:else><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="empty-wrap data-v-2f11f680" bindtap="__e"><view class="info data-v-2f11f680">请设置收货地址</view><view class="cell-more data-v-2f11f680"><view class="iconfont icon-right data-v-2f11f680"></view></view></view></block><image class="address-line data-v-2f11f680" src="{{$root.g6}}"></image></view></block><block wx:if="{{orderCreateData.delivery.delivery_type=='store'}}"><view class="{{['store-box','data-v-2f11f680',($root.g7<=1)?'not-delivery-type':'']}}"><block wx:if="{{storeInfo}}"><block class="data-v-2f11f680"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="store-info data-v-2f11f680" bindtap="__e"><view class="store-address-info data-v-2f11f680"><view class="info-wrap data-v-2f11f680"><view class="title data-v-2f11f680"><text class="data-v-2f11f680">{{storeInfo.store_name}}</text></view><view class="store-detail data-v-2f11f680"><block wx:if="{{storeInfo.open_date}}"><view class="data-v-2f11f680">{{"营业时间："+storeInfo.open_date}}</view></block><view class="address data-v-2f11f680">{{storeInfo.full_address+" "+storeInfo.address}}</view></view></view><view class="cell-more iconfont icon-right data-v-2f11f680"></view></view></view><view class="mobile-wrap store-mobile data-v-2f11f680"><view class="form-group data-v-2f11f680"><text class="text data-v-2f11f680">姓名</text><input class="input data-v-2f11f680" type="text" placeholder-class="color-tip placeholder" disabled="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['orderCreateData.member_address']]]]]}}" value="{{orderCreateData.member_address.name}}" bindinput="__e"/></view></view><view class="mobile-wrap store-mobile data-v-2f11f680"><view class="form-group data-v-2f11f680"><text class="text data-v-2f11f680">预留手机</text><input class="input data-v-2f11f680" type="number" maxlength="11" placeholder="请输入您的手机号码" placeholder-class="color-tip placeholder" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['orderCreateData.member_address']]]]]}}" value="{{orderCreateData.member_address.mobile}}" bindinput="__e"/></view></view><view data-event-opts="{{[['tap',[['storetime',['']]]]]}}" class="store-time data-v-2f11f680" bindtap="__e"><view class="left data-v-2f11f680">提货时间</view><view class="right data-v-2f11f680">{{''+deliveryTime+''}}<text class="iconfont icon-right data-v-2f11f680"></text></view></view></block></block><block wx:else><view class="empty data-v-2f11f680">当前无自提门店，请选择其它配送方式</view></block><image class="address-line data-v-2f11f680" src="{{$root.g8}}"></image></view></block></block><view class="site-wrap order-goods data-v-2f11f680"><view class="site-body data-v-2f11f680"><block wx:for="{{$root.l0}}" wx:for-item="goodsItem" wx:for-index="goodsIndex" wx:key="goodsIndex"><view class="goods-item data-v-2f11f680"><view class="goods-wrap data-v-2f11f680"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({goodsItem:goodsItem.$orig})}}" class="goods-img data-v-2f11f680" bindtap="__e"><image src="{{goodsItem.g9}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[goodsIndex]]]]]}}" binderror="__e" class="data-v-2f11f680"></image></view><view class="goods-info data-v-2f11f680"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" data-event-params="{{({goodsItem:goodsItem.$orig})}}" class="goods-name data-v-2f11f680" bindtap="__e">{{goodsItem.$orig.sku_name}}</view><block wx:if="{{goodsItem.$orig.sku_spec_format}}"><view class="sku data-v-2f11f680"><view class="goods-spec data-v-2f11f680"><block wx:for="{{goodsItem.$orig.sku_spec_format}}" wx:for-item="x" wx:for-index="i" wx:key="i"><block class="data-v-2f11f680"><view class="data-v-2f11f680">{{x.spec_value_name}}</view></block></block></view></view></block><block wx:if="{{goodsItem.$orig.is_virtual==0}}"><block class="data-v-2f11f680"><block wx:if="{{goodsItem.g10}}"><view class="error-tips data-v-2f11f680"><text class="iconfont icon-gantanhao data-v-2f11f680"></text><text class="data-v-2f11f680">{{"该商品不支持"+orderCreateData.delivery.delivery_type_name}}</text></view></block></block></block><block wx:if="{{goodsItem.$orig.error&&goodsItem.$orig.error.message}}"><view class="error-tips data-v-2f11f680"><text class="iconfont icon-gantanhao data-v-2f11f680"></text><text class="data-v-2f11f680">{{goodsItem.$orig.error.message}}</text></view></block><view class="goods-sub-section data-v-2f11f680"><view class="color-base-text data-v-2f11f680"><text class="unit price-style small data-v-2f11f680"></text><text class="goods-price price-style large data-v-2f11f680"></text><text class="unit price-style small data-v-2f11f680"></text></view><view class="data-v-2f11f680"><text class="font-size-tag data-v-2f11f680">x</text><text class="font-size-base data-v-2f11f680">{{goodsItem.$orig.num}}</text></view></view></view></view><block wx:if="{{goodsItem.$orig.goods_form}}"><view data-event-opts="{{[['tap',[['editForm',[goodsIndex]]]]]}}" class="goods-form data-v-2f11f680" bindtap="__e"><ns-form vue-id="{{'5bf3a084-1-'+goodsIndex}}" data="{{goodsItem.$orig.goods_form.json_data}}" custom-attr="{{goodsItem.a0}}" data-ref="goodsForm" class="data-v-2f11f680 vue-ref-in-for" bind:__l="__l"></ns-form><text class="cell-more iconfont icon-right data-v-2f11f680"></text><view class="shade data-v-2f11f680"></view></view></block></view></block></view></view><view class="site-wrap buyer-message data-v-2f11f680"><view class="order-cell data-v-2f11f680"><text class="tit data-v-2f11f680">买家留言</text><view data-event-opts="{{[['tap',[['openPopup',['buyerMessagePopup']]]]]}}" class="box text-overflow data-v-2f11f680" bindtap="__e"><block wx:if="{{orderCreateData.buyer_message}}"><text class="data-v-2f11f680">{{orderCreateData.buyer_message}}</text></block><block wx:else><text class="color-sub data-v-2f11f680">无留言</text></block></view><text class="iconfont icon-right data-v-2f11f680"></text></view></view><block wx:if="{{paymentData.system_form}}"><view class="system-form-wrap data-v-2f11f680"><ns-form vue-id="5bf3a084-2" data="{{paymentData.system_form.json_data}}" data-ref="form" class="data-v-2f11f680 vue-ref" bind:__l="__l"></ns-form></view></block><block wx:if="{{calculateData}}"><view class="order-submit bottom-safe-area data-v-2f11f680"><view class="submit-btn data-v-2f11f680"><button class="sava-btn mini data-v-2f11f680" type="primary" size="mini" data-event-opts="{{[['tap',[['create']]]]}}" bindtap="__e">立即兑换</button></view></view><view class="order-submit-block data-v-2f11f680"></view><block wx:if="{{calculateData}}"><payment bind:close="__e" vue-id="5bf3a084-3" data-ref="choosePaymentPopup" data-event-opts="{{[['^close',[['payClose']]]]}}" class="data-v-2f11f680 vue-ref" bind:__l="__l"></payment></block></block><block wx:if="{{storeList}}"><uni-popup vue-id="5bf3a084-4" type="bottom" data-ref="deliveryPopup" class="data-v-2f11f680 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="delivery-popup popup data-v-2f11f680"><view class="popup-header data-v-2f11f680"><text class="tit data-v-2f11f680">已为您甄选出附近所有相关门店</text><text data-event-opts="{{[['tap',[['closePopup',['deliveryPopup']]]]]}}" class="iconfont icon-close data-v-2f11f680" bindtap="__e"></text></view><view class="{{['popup-body','store-popup','data-v-2f11f680',(isIphoneX)?'safe-area':'']}}"><view class="delivery-content data-v-2f11f680"><block wx:for="{{storeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectPickupPoint',['$0'],[[['storeList','',index]]]]]]]}}" class="item-wrap data-v-2f11f680" bindtap="__e"><view class="detail data-v-2f11f680"><view class="{{['name','data-v-2f11f680',item.store_id==orderCreateData.delivery.store_id?'color-base-text':'']}}"><text class="data-v-2f11f680">{{item.store_name}}</text><block wx:if="{{item.distance}}"><text class="data-v-2f11f680">{{"("+item.distance+"km)"}}</text></block></view><view class="info data-v-2f11f680"><view class="{{['font-size-goods-tag','data-v-2f11f680',item.store_id==orderCreateData.delivery.store_id?'color-base-text':'']}}">{{'营业时间：'+item.open_date+''}}</view><view class="{{['font-size-goods-tag','data-v-2f11f680',item.store_id==orderCreateData.delivery.store_id?'color-base-text':'']}}">{{'地址：'+item.full_address+item.address+''}}</view></view></view><block wx:if="{{item.store_id==orderCreateData.delivery.store_id}}"><view class="icon data-v-2f11f680"><text class="iconfont icon-yuan_checked color-base-text data-v-2f11f680"></text></view></block></view></block><block wx:if="{{!storeList}}"><view class="empty data-v-2f11f680">所选择收货地址附近没有可以自提的门店</view></block></view></view></view></uni-popup></block><uni-popup vue-id="5bf3a084-5" type="bottom" data-ref="buyerMessagePopup" class="data-v-2f11f680 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="buyermessag-popup popup data-v-2f11f680" style="height:auto;" catchtouchmove="__e"><view class="popup-header data-v-2f11f680"><text class="tit data-v-2f11f680">买家留言</text><text data-event-opts="{{[['tap',[['closePopup',['buyerMessagePopup']]]]]}}" class="iconfont icon-close data-v-2f11f680" bindtap="__e"></text></view><scroll-view class="{{['popup-body','data-v-2f11f680',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view class="data-v-2f11f680"><view class="buyermessag-cell data-v-2f11f680"><view class="buyermessag-form-group data-v-2f11f680"><textarea type="text" maxlength="100" placeholder="留言前建议先与商家协调一致" placeholder-class="color-tip" data-event-opts="{{[['input',[['__set_model',['$0','buyer_message','$event',[]],['orderCreateData']]]]]}}" value="{{orderCreateData.buyer_message}}" bindinput="__e" class="data-v-2f11f680"></textarea></view></view></view></scroll-view><view data-event-opts="{{[['tap',[['saveBuyerMessage',['$event']]]]]}}" class="{{['popup-footer','data-v-2f11f680',(isIphoneX)?'bottom-safe-area':'']}}" bindtap="__e"><view class="confirm-btn color-base-bg data-v-2f11f680">确定</view></view></view></uni-popup><uni-popup vue-id="5bf3a084-6" type="bottom" data-ref="editFormPopup" class="data-v-2f11f680 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="form-popup popup data-v-2f11f680" style="height:auto;" catchtouchmove="__e"><view class="popup-header data-v-2f11f680"><text class="tit data-v-2f11f680">买家信息</text><text data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="iconfont icon-close data-v-2f11f680" bindtap="__e"></text></view><scroll-view class="{{['popup-body','data-v-2f11f680',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><block wx:if="{{tempFormData}}"><ns-form vue-id="{{('5bf3a084-7')+','+('5bf3a084-6')}}" data="{{tempFormData.json_data}}" data-ref="tempForm" class="data-v-2f11f680 vue-ref" bind:__l="__l"></ns-form></block></scroll-view><view data-event-opts="{{[['tap',[['saveForm',['$event']]]]]}}" class="{{['popup-footer','data-v-2f11f680',(isIphoneX)?'bottom-safe-area':'']}}" bindtap="__e"><view class="confirm-btn color-base-bg data-v-2f11f680">确定</view></view></view></uni-popup></block></scroll-view><ns-select-time bind:selectTime="__e" vue-id="5bf3a084-8" data-ref="timePopup" data-event-opts="{{[['^selectTime',[['selectPickupTime']]]]}}" class="data-v-2f11f680 vue-ref" bind:__l="__l"></ns-select-time><ns-login vue-id="5bf3a084-9" data-ref="login" class="data-v-2f11f680 vue-ref" bind:__l="__l"></ns-login><loading-cover vue-id="5bf3a084-10" data-ref="loadingCover" class="data-v-2f11f680 vue-ref" bind:__l="__l"></loading-cover></view></view>