<page-meta page-style="{{themeColor}}" class="data-v-0aa21b3f"></page-meta><view class="page data-v-0aa21b3f"><view class="detail-box data-v-0aa21b3f"><view class="detail-img data-v-0aa21b3f"><image src="{{$root.g0}}" class="data-v-0aa21b3f"></image><block wx:if="{{giftDetail&&giftDetail.card_right_type=='balance'}}"><view class="card-label data-v-0aa21b3f">{{giftDetail.balance+"元储值卡"}}</view></block><block wx:if="{{giftDetail&&giftDetail.card_right_type=='goods'}}"><view class="card-label-img data-v-0aa21b3f"><image src="{{$root.g1}}" mode="heightFix" class="data-v-0aa21b3f"></image></view></block></view><view class="detail-head data-v-0aa21b3f">选择卡面</view><block wx:if="{{imageList}}"><scroll-view class="img-list data-v-0aa21b3f" scroll-x="{{true}}" show-scrollbar="{{false}}" enable-flex="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="{{['img-item','data-v-0aa21b3f',(image.media_id==item.$orig.media_id)?'selected':'']}}" bindtap="__e"><image src="{{item.g2}}" mode="heightFix" class="data-v-0aa21b3f"></image><block wx:if="{{image.media_id==item.$orig.media_id}}"><text class="iconfont icon-right1 data-v-0aa21b3f"></text></block></view></block></scroll-view></block><block wx:if="{{giftDetail&&giftDetail.card_right_type=='goods'}}"><block class="data-v-0aa21b3f"><block wx:if="{{giftDetail.goods_list}}"><view class="gift-list data-v-0aa21b3f"><block wx:if="{{giftDetail.card_right_goods_type=='all'}}"><view class="detail-head data-v-0aa21b3f">{{"以下商品 在使用时任选"+giftDetail.card_right_goods_count+"件"}}</view></block><block wx:else><view class="detail-head data-v-0aa21b3f">本礼品卡包含以下商品</view></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="gift-item data-v-0aa21b3f"><view class="goods-img data-v-0aa21b3f"><image src="{{item.g3}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e" class="data-v-0aa21b3f"></image></view><view class="goods-info data-v-0aa21b3f"><view class="goods-name data-v-0aa21b3f">{{item.$orig.sku_info.sku_name}}</view><view class="gift-bottom data-v-0aa21b3f"><view class="gift-price price-font data-v-0aa21b3f"></view><block wx:if="{{giftDetail.card_right_goods_type=='item'}}"><view class="gift-num data-v-0aa21b3f"><view class="num data-v-0aa21b3f">{{"x"+item.$orig.goods_num}}</view></view></block></view></view></view></block></view></block><view class="card-box data-v-0aa21b3f"><view class="detail-head data-v-0aa21b3f">使用须知</view><view class="card-info data-v-0aa21b3f"><view class="goods-details data-v-0aa21b3f"><block wx:if="{{giftDetail.instruction}}"><ns-mp-html vue-id="bc3711aa-1" content="{{giftDetail.instruction}}" class="data-v-0aa21b3f" bind:__l="__l"></ns-mp-html></block><block wx:else><ns-mp-html vue-id="bc3711aa-2" content="{{giftDetail.giftcard_desc}}" class="data-v-0aa21b3f" bind:__l="__l"></ns-mp-html></block></view></view></view><block wx:if="{{giftDetail&&giftDetail.desc}}"><view class="card-box data-v-0aa21b3f"><view class="detail-head data-v-0aa21b3f">卡片详情</view><view class="card-info data-v-0aa21b3f"><view class="goods-details data-v-0aa21b3f"><ns-mp-html vue-id="bc3711aa-3" content="{{giftDetail.desc}}" class="data-v-0aa21b3f" bind:__l="__l"></ns-mp-html></view></view></view></block><view class="bottom-btn data-v-0aa21b3f"><view class="bottom-left data-v-0aa21b3f"><view class="price-box data-v-0aa21b3f"><view class="data-v-0aa21b3f">销售价</view><view class="price-style small data-v-0aa21b3f">￥</view><view class="price-style large data-v-0aa21b3f">{{giftDetail.card_price}}</view></view><view class="title-sub data-v-0aa21b3f"></view></view><button type="primary" data-event-opts="{{[['tap',[['buy',['goods']]]]]}}" bindtap="__e" class="data-v-0aa21b3f">制作礼包</button></view></block></block><block wx:if="{{giftDetail&&giftDetail.card_right_type=='balance'}}"><block class="data-v-0aa21b3f"><view class="card-box data-v-0aa21b3f"><view class="detail-head data-v-0aa21b3f">使用须知</view><view class="card-info data-v-0aa21b3f"><view class="goods-details data-v-0aa21b3f"><block wx:if="{{giftDetail.instruction}}"><ns-mp-html vue-id="bc3711aa-4" content="{{giftDetail.instruction}}" class="data-v-0aa21b3f" bind:__l="__l"></ns-mp-html></block><block wx:else><ns-mp-html vue-id="bc3711aa-5" content="{{giftDetail.giftcard_desc}}" class="data-v-0aa21b3f" bind:__l="__l"></ns-mp-html></block></view></view></view><block wx:if="{{giftDetail&&giftDetail.desc}}"><view class="card-box data-v-0aa21b3f"><view class="detail-head data-v-0aa21b3f">卡片详情</view><view class="card-info data-v-0aa21b3f"><view class="goods-details data-v-0aa21b3f"><ns-mp-html vue-id="bc3711aa-6" content="{{giftDetail.desc}}" class="data-v-0aa21b3f" bind:__l="__l"></ns-mp-html></view></view></view></block><view class="bottom-btn data-v-0aa21b3f"><view class="bottom-left data-v-0aa21b3f"><view class="price-box data-v-0aa21b3f"><view class="data-v-0aa21b3f">销售价</view><view class="price-style small data-v-0aa21b3f">￥</view><view class="price-style large data-v-0aa21b3f">{{giftDetail.card_price}}</view></view><view class="title-sub data-v-0aa21b3f"></view></view><button type="primary" data-event-opts="{{[['tap',[['buy',['balance']]]]]}}" bindtap="__e" class="data-v-0aa21b3f">制作礼包</button></view></block></block></view><view class="tab-bar-placeholder data-v-0aa21b3f"></view><loading-cover vue-id="bc3711aa-7" data-ref="loadingCover" class="data-v-0aa21b3f vue-ref" bind:__l="__l"></loading-cover><ns-login vue-id="bc3711aa-8" data-ref="login" class="data-v-0aa21b3f vue-ref" bind:__l="__l"></ns-login><privacy-popup vue-id="bc3711aa-9" data-ref="privacyPopup" class="data-v-0aa21b3f vue-ref" bind:__l="__l"></privacy-popup></view>