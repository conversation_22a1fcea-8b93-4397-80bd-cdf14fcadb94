require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/giftcard/order_detail"],{"03df":function(e,t,o){"use strict";o.r(t);var i=o("6e6c"),n=o.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},"396b":function(e,t,o){"use strict";o.r(t);var i=o("7611"),n=o("03df");for(var r in n)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(r);o("84bc");var a=o("828b"),d=Object(a["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=d.exports},"3c1b":function(e,t,o){},"6e6c":function(e,t,o){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={components:{uniPopup:function(){o.e("components/uni-popup/uni-popup").then(function(){return resolve(o("d745"))}.bind(null,o)).catch(o.oe)}},data:function(){return{isIphoneX:!1,orderId:"",orderDetail:"all",goodsOpen:!0}},onLoad:function(e){var t=this;if(e.order_id&&(this.orderId=e.order_id),e.scene){var o=decodeURIComponent(e.scene);o=o.split("&"),o.length&&o.forEach((function(e){-1!=e.indexOf("order_id")&&(t.orderId=e.split("-")[1])}))}},onShow:function(){var e=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?this.getData():this.$nextTick((function(){e.$refs.login.open("/pages_promotion/giftcard/order_detail?order_id="+e.orderId)}))},methods:{getData:function(){var e=this;this.$api.sendRequest({url:"/giftcard/api/order/detail",data:{order_id:this.orderId},success:function(t){t.code>=0?(e.orderDetail=t.data,setTimeout((function(){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}),300)):(e.$util.showToast({title:t.message}),setTimeout((function(){e.$util.redirectTo("/pages_promotion/giftcard/order_list")}),1500))},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},openChoosePayment:function(){this.storeToken?(e.setStorageSync("paySource","giftcard"),this.$refs.choosePaymentPopup.open()):this.$util.showToast({title:"您尚未登录，请先登录"})},gotoBuy:function(){this.$refs.choosePaymentPopup.getPayInfo(this.orderDetail.out_trade_no)},imageError:function(e){this.orderDetail.order_goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},toCardInfo:function(){this.orderDetail.card_list.length>0?this.$util.redirectTo("/pages_promotion/giftcard/card_info",{member_card_id:this.orderDetail.card_list[0]["member_card_id"]}):this.$util.showToast({title:"礼品卡不存在或已送出"})},closeOrder:function(t){var o=this;e.showModal({title:"提示",content:"您确定要关闭该订单吗？",success:function(e){e.confirm&&o.$api.sendRequest({url:"/giftcard/api/order/close",data:{order_id:t},success:function(e){e.code>=0?o.getData():o.$util.showToast({title:e.message})}})}})}}};t.default=i}).call(this,o("df3c")["default"])},7611:function(e,t,o){"use strict";o.d(t,"b",(function(){return n})),o.d(t,"c",(function(){return r})),o.d(t,"a",(function(){return i}));var i={nsPayment:function(){return o.e("components/ns-payment/ns-payment").then(o.bind(null,"7aec"))},loadingCover:function(){return o.e("components/loading-cover/loading-cover").then(o.bind(null,"c003"))},nsLogin:function(){return Promise.all([o.e("common/vendor"),o.e("components/ns-login/ns-login")]).then(o.bind(null,"2910"))}},n=function(){var e=this,t=e.$createElement,o=(e._self._c,e.$util.img("balance"==e.orderDetail.card_right_type?"public/uniapp/giftcard/order-icon-recharge.png":"public/uniapp/giftcard/order-icon-gift.png")),i=e.orderDetail.card_cover?e.$util.img(e.orderDetail.card_cover):null,n=e.orderDetail.card_cover?null:e.$util.img("public/uniapp/giftcard/default_card.png"),r=e.$util.timeStampTurnTime(e.orderDetail.create_time);e._isMounted||(e.e0=function(t){return e.$util.redirectTo("/pages_promotion/giftcard/list",{order_id:e.orderDetail.order_id})}),e.$mp.data=Object.assign({},{$root:{g0:o,g1:i,g2:n,g3:r}})},r=[]},"84bc":function(e,t,o){"use strict";var i=o("3c1b"),n=o.n(i);n.a},b565:function(e,t,o){"use strict";(function(e,t){var i=o("47a9");o("d381");i(o("3240"));var n=i(o("396b"));e.__webpack_require_UNI_MP_PLUGIN__=o,t(n.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])}},[["b565","common/runtime","common/vendor"]]]);