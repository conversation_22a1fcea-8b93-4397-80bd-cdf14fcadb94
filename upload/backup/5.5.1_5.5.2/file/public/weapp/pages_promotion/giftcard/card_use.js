require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/giftcard/card_use"],{"0704":function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"c",(function(){return o})),r.d(t,"a",(function(){return a}));var a={nsForm:function(){return r.e("components/ns-form/ns-form").then(r.bind(null,"ae30"))},payment:function(){return r.e("components/payment/payment").then(r.bind(null,"b6f2"))},uniPopup:function(){return r.e("components/uni-popup/uni-popup").then(r.bind(null,"d745"))},nsSelectTime:function(){return r.e("components/ns-select-time/ns-select-time").then(r.bind(null,"a523"))},nsLogin:function(){return Promise.all([r.e("common/vendor"),r.e("components/ns-login/ns-login")]).then(r.bind(null,"2910"))},loadingCover:function(){return r.e("components/loading-cover/loading-cover").then(r.bind(null,"c003"))}},i=function(){var e=this,t=e.$createElement,r=(e._self._c,e.paymentData&&e.paymentData.is_virtual?e.$util.img("public/uniapp/order/icon-mobile.png"):null),a=e.paymentData&&!e.paymentData.is_virtual?e.goodsData.delivery.express_type.length:null,i=e.paymentData&&!e.paymentData.is_virtual&&"express"==e.orderCreateData.delivery.delivery_type?e.goodsData.delivery.express_type.length:null,o=e.paymentData&&!e.paymentData.is_virtual&&"express"==e.orderCreateData.delivery.delivery_type?e.$util.img("public/uniapp/order/address-line.png"):null,s=e.paymentData&&!e.paymentData.is_virtual&&"local"==e.orderCreateData.delivery.delivery_type?e.goodsData.delivery.express_type.length:null,n=e.paymentData&&!e.paymentData.is_virtual&&"local"==e.orderCreateData.delivery.delivery_type&&e.localMemberAddress?e.storeList&&Object.keys(e.storeList).length>1:null,d=e.paymentData&&!e.paymentData.is_virtual&&"local"==e.orderCreateData.delivery.delivery_type?e.$util.img("public/uniapp/order/address-line.png"):null,l=e.paymentData&&!e.paymentData.is_virtual&&"store"==e.orderCreateData.delivery.delivery_type?e.goodsData.delivery.express_type.length:null,u=e.paymentData&&!e.paymentData.is_virtual&&"store"==e.orderCreateData.delivery.delivery_type?e.$util.img("public/uniapp/order/address-line.png"):null,c=e.paymentData?e.__map(e.goodsData.goods_list,(function(t,r){var a=e.__get_orig(t),i=e.$util.img(t.sku_image,{size:"mid"}),o=0==t.is_virtual?e.orderCreateData.delivery&&e.orderCreateData.delivery.delivery_type&&t.support_trade_type&&-1==t.support_trade_type.indexOf(e.orderCreateData.delivery.delivery_type):null,s=t.goods_form?{sku_id:t.sku_id,form_id:t.goods_form.id}:null;return{$orig:a,g9:i,g10:o,a0:s}})):null;e._isMounted||(e.e0=function(t){return e.$refs.deliveryPopup.open()},e.e1=function(t){return e.$refs.deliveryPopup.open()},e.e2=function(t,r){var a=arguments[arguments.length-1].currentTarget.dataset,i=a.eventParams||a["event-params"];r=i.goodsItem;return e.$util.redirectTo("/pages/goods/detail",{goods_id:r.goods_id})},e.e3=function(t,r){var a=arguments[arguments.length-1].currentTarget.dataset,i=a.eventParams||a["event-params"];r=i.goodsItem;return e.$util.redirectTo("/pages/goods/detail",{goods_id:r.goods_id})},e.e4=function(t){return e.$refs.editFormPopup.close()}),e.$mp.data=Object.assign({},{$root:{g0:r,g1:a,g2:i,g3:o,g4:s,g5:n,g6:d,g7:l,g8:u,l0:c}})},o=[]},"093c":function(e,t,r){"use strict";r.r(t);var a=r("0704"),i=r("1ba8");for(var o in i)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(o);r("3517"),r("f84e");var s=r("828b"),n=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"2f11f680",null,!1,a["a"],void 0);t["default"]=n.exports},"1ba8":function(e,t,r){"use strict";r.r(t);var a=r("5cb3"),i=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},3517:function(e,t,r){"use strict";var a=r("92d2"),i=r.n(a);i.a},"5cb3":function(e,t,r){"use strict";(function(e,a){var i=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(r("7ca3")),s=i(r("3b2d"));function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var l={options:{styleIsolation:"shared"},data:function(){return{api:{payment:"/giftcard/api/giftcardordercreate/payment",calculate:"/giftcard/api/giftcardordercreate/calculate",create:"/giftcard/api/giftcardordercreate/create"},createDataKey:"giftcarduse",outTradeNo:"",isIphoneX:!1,orderCreateData:{is_balance:0,is_point:1,delivery:{}},paymentData:null,calculateData:null,tempData:null,storeId:0,deliveryTime:"",memberAddress:null,localMemberAddress:null,isRepeat:!1,promotionInfo:null,tempFormData:null,menuButtonBounding:{},storeConfig:null,LocalConfig:null}},created:function(){var t=this;this.menuButtonBounding=e.getMenuButtonBoundingClientRect(),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?(Object.assign(this.orderCreateData,e.getStorageSync(this.createDataKey)),this.location&&(this.orderCreateData.latitude=this.location.latitude,this.orderCreateData.longitude=this.location.longitude),this.payment()):this.$nextTick((function(){t.$refs.loadingCover.hide(),t.$refs.login.open(t.$util.getCurrentRoute().path)}))},computed:{goodsData:function(){if(this.paymentData)return this.paymentData.goods_list.forEach((function(e){e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format))})),this.paymentData},calculateGoodsData:function(){if(this.calculateData)return this.calculateData},balanceDeduct:function(){if(this.calculateData)return this.calculateData.member_account.balance_total<=parseFloat(this.calculateData.order_money).toFixed(2)?parseFloat(this.calculateData.member_account.balance_total).toFixed(2):parseFloat(this.calculateData.order_money).toFixed(2)},storeList:function(){return this.getStoreList()},storeInfo:function(){var e=this.getStoreList();return e&&this.orderCreateData.delivery&&"express"!=this.orderCreateData.delivery.delivery_type&&this.storeId?e[this.orderCreateData.delivery.store_id]:null}},watch:{storeToken:function(e,t){this.payment()},deliveryTime:function(e){e||this.$refs.timePopup.refresh()},location:function(e){e&&(this.orderCreateData.latitude=e.latitude,this.orderCreateData.longitude=e.longitude,this.payment())},calculateGoodsData:function(e){e&&e.config.local&&e.delivery.local.info.time_is_open&&!this.deliveryTime&&this.localtime("no")}},methods:{pageShow:function(){e.getStorageSync("addressBack")&&(e.removeStorageSync("addressBack"),this.payment())},payment:function(){var t=this;this.$api.sendRequest({url:this.api.payment,data:this.orderCreateData,success:function(r){if(0==r.code&&r.data){var i,o=r.data,s=e.getStorageSync("is_test")?1175:a.getLaunchOptionsSync().scene;if(-1!=[1175,1176,1177,1191,1195].indexOf(s)&&o.delivery.express_type&&(o.delivery.express_type=o.delivery.express_type.filter((function(e){return"express"==e.name}))),o&&o.delivery.express_type&&o.delivery.express_type.length){var n=e.getStorageSync("delivery"),d=o.delivery.express_type[0];n&&o.delivery.express_type.forEach((function(e){e.name==n.delivery_type&&(d=e),"local"==e.name&&(t.localConfig=e),"store"==e.name&&(t.storeConfig=e)})),t.selectDeliveryType(d,!1,o.member_account),e.getStorageSync("deliveryTime")&&e.getStorageSync("deliveryTime")["delivery_type"]&&e.getStorageSync("deliveryTime")["delivery_type"]==t.orderCreateData.delivery.delivery_type&&(t.deliveryTime=e.getStorageSync("deliveryTime")["deliveryTime"],t.orderCreateData.delivery.buyer_ask_delivery_time=e.getStorageSync("deliveryTime")["buyer_ask_delivery_time"])}if(o.is_virtual)t.orderCreateData.delivery.member_address={name:o.member_account.nickname,mobile:null!==(i=o.member_account.mobile)&&void 0!==i?i:""};t.orderCreateData.order_key=o.order_key,o=t.handleGoodsFormData(o),t.paymentData=o,t.$forceUpdate(),t.calculate()}}})},handleGoodsFormData:function(t){var r=e.getStorageSync("goodFormData");return t.goods_list.forEach((function(e){if(e.goods_form){var t={};e.form_data?e.form_data.map((function(e){t[e.id]=e})):r&&r.goods_id==e.goods_id&&r.form_data.map((function(e){t[e.id]=e})),Object.keys(t).length&&e.goods_form.json_data.forEach((function(e){t[e.id]&&(e.val=t[e.id].val)}))}})),t},calculate:function(){var e=this;this.$api.sendRequest({url:this.api.calculate,data:this.handleCreateData(),success:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.isShow&&e.$refs.loadingCover.hide(),0==t.code&&t.data?(e.calculateData=t.data,t.data.delivery&&("express"==t.data.delivery.delivery_type&&(e.memberAddress=t.data.delivery.member_address),"local"==t.data.delivery.delivery_type&&(e.localMemberAddress=t.data.delivery.member_address)),e.$forceUpdate()):e.$util.showToast({title:t.message})}})},create:function(){var t=this;this.verify()&&!this.isRepeat&&(this.isRepeat=!0,e.showLoading({title:""}),this.$api.sendRequest({url:this.api.create,data:this.handleCreateData(),success:function(r){e.hideLoading(),0==r.code?(t.outTradeNo=r.data,e.removeStorageSync("deliveryTime"),e.removeStorageSync("goodFormData"),0==t.calculateData.pay_money?((t.paymentData.is_virtual||"store"==t.orderCreateData.delivery.delivery_type)&&t.$util.subscribeMessage("ORDER_VERIFY_OUT_TIME,VERIFY_CODE_EXPIRE,VERIFY"),t.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:t.outTradeNo},success:function(e){e.code>=0&&e.data&&e.data.order_id>0?t.$util.redirectTo("/pages/order/detail",{order_id:e.data.order_id},"redirectTo"):t.$util.redirectTo("/pages/order/list",{},"redirectTo")},fail:function(e){t.$util.redirectTo("/pages/order/list",{},"redirectTo")}})):t.openChoosePayment()):(t.$util.showToast({title:r.message}),t.isRepeat=!1)}}))},handleCreateData:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);return this.$refs.form&&(t.form_data={form_id:this.paymentData.system_form.id,form_data:this.$util.deepClone(this.$refs.form.formData)}),this.$refs.goodsForm&&(t.form_data||(t.form_data={}),t.form_data.goods_form={},this.$refs.goodsForm.forEach((function(r){t.form_data.goods_form[r._props.customAttr.sku_id]={form_id:r._props.customAttr.form_id,form_data:e.$util.deepClone(r.formData)}}))),Object.keys(t).forEach((function(e){var r=t[e];"object"==(0,s.default)(r)&&(t[e]=JSON.stringify(r))})),t.member_address&&this.orderCreateData.delivery&&"store"!=this.orderCreateData.delivery.delivery_type&&delete t.member_address,t},openChoosePayment:function(){if(e.setStorageSync("paySource",""),this.paymentData.is_virtual)this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY");else switch(this.orderCreateData.delivery.delivery_type){case"express":this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY,ORDER_DELIVERY");break;case"store":this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY");break;case"local":this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY,ORDER_DELIVERY");break}this.$refs.choosePaymentPopup.getPayInfo(this.outTradeNo)},verify:function(){if(1==this.paymentData.is_virtual){if(!this.orderCreateData.member_address.mobile.length)return this.$util.showToast({title:"请输入您的手机号码"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号码"}),!1}else{if(!this.orderCreateData.delivery||!this.orderCreateData.delivery.delivery_type)return this.$util.showToast({title:"商家未设置配送方式"}),!1;if("express"==this.orderCreateData.delivery.delivery_type&&!this.memberAddress||"local"==this.orderCreateData.delivery.delivery_type&&!this.localMemberAddress)return this.$util.showToast({title:"请先选择您的收货地址"}),!1;if("store"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可提货的门店,请选择其他配送方式"}),!1;if(!this.orderCreateData.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号"}),!1;if(!this.deliveryTime)return this.$util.showToast({title:"请选择提货时间"}),!1}if("local"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可配送的门店,请选择其他配送方式"}),!1;if(this.calculateGoodsData.config.local.is_use&&1==this.calculateGoodsData.delivery.local.info.time_is_open&&!this.deliveryTime)return this.$util.showToast({title:"请选择送达时间"}),!1}}if(this.$refs.goodsForm){for(var e=!0,t=0;t<this.$refs.goodsForm.length;t++){var r=this.$refs.goodsForm[t];if(e=r.verify(),!e)break}if(!e)return!1}if(this.paymentData.system_form){var a=this.$refs.form.verify();if(!a)return!1}return!0},selectAddress:function(){var e={back:this.$util.getCurrentRoute().path,local:0,type:1};"local"==this.orderCreateData.delivery.delivery_type&&(e.local=1,e.type=2),this.$util.redirectTo("/pages_tool/member/address",e)},selectDeliveryType:function(t){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!this.orderCreateData.delivery||this.orderCreateData.delivery.delivery_type!=t.name){this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""};var i={delivery_type:t.name,delivery_type_name:t.title};"store"!=t.name&&"local"!=t.name||(t.store_list[0]&&(i.store_id=t.store_list[0].store_id),this.storeId=i.store_id?i.store_id:0,this.orderCreateData.member_address||(this.paymentData?this.orderCreateData.member_address={name:this.paymentData.member_account.nickname,mobile:this.paymentData.member_account.mobile}:a&&(this.orderCreateData.member_address={name:a.nickname,mobile:a.mobile}))),this.$set(this.orderCreateData,"delivery",i),this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.deliveryTime="",e.removeStorageSync("deliveryTime"),e.setStorageSync("delivery",i),"express"==this.orderCreateData.delivery.delivery_type||this.location||this.$util.getLocation(),r&&this.calculate(),"store"==t.name&&this.storetime("no"),"local"==t.name&&this.localtime("no")}},imageError:function(e){this.paymentData.goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},selectPickupPoint:function(t){if(t.store_id!=this.storeId){this.storeId=t.store_id,this.orderCreateData.delivery.store_id=t.store_id,this.calculate(),this.resetDeliveryTime();var r=e.getStorageSync("delivery");r.store_id=t.store_id,e.setStorageSync("delivery",r)}this.$refs.deliveryPopup.close()},resetDeliveryTime:function(){this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.deliveryTime="",e.removeStorageSync("deliveryTime")},storetime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.storeInfo){var t=this.$util.deepClone(this.storeInfo);t.delivery_time="string"==typeof t.delivery_time&&t.delivery_time?JSON.parse(t.delivery_time):t.delivery_time,t.delivery_time&&(void 0!=t.delivery_time.length||t.delivery_time.length)||(t.delivery_time=[{start_time:t.start_time,end_time:t.end_time}]);var r={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(r,e),this.$forceUpdate()}},selectPickupTime:function(t){this.deliveryTime=t.data.month+"("+t.data.time+")",this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:t.data.start_date,end_date:t.data.end_date},e.setStorageSync("deliveryTime",{deliveryTime:this.deliveryTime,buyer_ask_delivery_time:this.orderCreateData.delivery.buyer_ask_delivery_time,delivery_type:this.orderCreateData.delivery.delivery_type})},openPopup:function(e){this.tempData=this.$util.deepClone(this.orderCreateData),this.$refs[e].open()},closePopup:function(e){this.orderCreateData=this.$util.deepClone(this.tempData),this.$refs[e].close(),this.tempData=null},saveBuyerMessage:function(){this.calculate(),this.$refs.buyerMessagePopup.close()},payClose:function(){this.$util.redirectTo("/pages/order/detail",{order_id:this.$refs.choosePaymentPopup.payInfo.order_id},"redirectTo")},localtime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.calculateGoodsData&&this.calculateGoodsData.config.local){var t=this.$util.deepClone(this.calculateGoodsData.delivery.local.info);if(Object.keys(t).length){t.delivery_time&&(t.end_time=t.delivery_time[t.delivery_time.length-1].end_time);var r={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(r,e)}}},editForm:function(e){this.tempFormData={index:e,json_data:this.$util.deepClone(this.goodsData.goods_list[e].goods_form.json_data)},this.$refs.editFormPopup.open()},saveForm:function(){this.$refs.tempForm.verify()&&(this.$set(this.paymentData.goods_list[this.tempFormData.index].goods_form,"json_data",this.$refs.tempForm.formData),this.$refs.editFormPopup.close())},back:function(){e.navigateBack({delta:1})},getStoreList:function(){var e=null;return this.orderCreateData.delivery&&("local"==this.orderCreateData.delivery.delivery_type&&this.localConfig&&(e=this.localConfig.store_list,e=e.reduce((function(e,t){return d(d({},e),{},(0,o.default)({},t.store_id,t))}),{})),"store"==this.orderCreateData.delivery.delivery_type&&this.storeConfig&&(e=this.storeConfig.store_list,e=e.reduce((function(e,t){return d(d({},e),{},(0,o.default)({},t.store_id,t))}),{}))),e}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)}}};t.default=l}).call(this,r("df3c")["default"],r("3223")["default"])},"75a7":function(e,t,r){},9285:function(e,t,r){"use strict";(function(e,t){var a=r("47a9");r("d381");a(r("3240"));var i=a(r("093c"));e.__webpack_require_UNI_MP_PLUGIN__=r,t(i.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},"92d2":function(e,t,r){},f84e:function(e,t,r){"use strict";var a=r("75a7"),i=r.n(a);i.a}},[["9285","common/runtime","common/vendor"]]]);