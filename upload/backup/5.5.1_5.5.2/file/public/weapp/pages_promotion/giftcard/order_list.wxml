<page-meta page-style="{{themeColor}}"></page-meta><view><mescroll-uni class="vue-ref" vue-id="3948d84d-1" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><view class="cf-container color-line-border"><view class="tab"><view data-event-opts="{{[['tap',[['changeState',['all']]]]]}}" bindtap="__e"><text class="{{[order_status=='all'?'color-base-text active color-base-border-bottom':'']}}">全部</text></view><view data-event-opts="{{[['tap',[['changeState',['topay']]]]]}}" bindtap="__e"><text class="{{[order_status=='topay'?'color-base-text active color-base-border-bottom':'']}}">待支付</text></view><view data-event-opts="{{[['tap',[['changeState',['complete']]]]]}}" bindtap="__e"><text class="{{[order_status=='complete'?'color-base-text active color-base-border-bottom':'']}}">已完成</text></view><view data-event-opts="{{[['tap',[['changeState',['close']]]]]}}" bindtap="__e"><text class="{{[order_status=='close'?'color-base-text active color-base-border-bottom':'']}}">已关闭</text></view></view></view><block wx:if="{{$root.g0>0}}"><view class="card-box"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view><view class="card-item"><view data-event-opts="{{[['tap',[['orderDetail',['$0'],[[['orderList','',index,'order_id']]]]]]]}}" class="card-head" bindtap="__e"><view class="head-box"><view class="head-icon"><image src="{{item.g1}}" mode="widthFix"></image></view><view class="head-title"><view class="head-type">{{item.$orig.card_right_type=='balance'?'储值卡':'礼品卡'}}</view><view class="head-time">{{item.g2}}</view></view></view><view class="pay-status">{{item.$orig.order_status_name}}</view></view><view data-event-opts="{{[['tap',[['orderDetail',['$0'],[[['orderList','',index,'order_id']]]]]]]}}" class="card-content" bindtap="__e"><view class="card-left"><view class="card-img"><image src="{{item.g3}}" mode="aspectFill"></image></view><view class="card-name">{{item.$orig.order_name}}</view></view><view class="card-price"><view>{{"￥"+item.$orig.card_price}}</view><view>{{"x"+item.$orig.num}}</view></view></view><view class="order-price"><view>{{"￥"+item.$orig.pay_money}}</view></view><block wx:if="{{item.$orig.order_status=='topay'}}"><view class="button"><view class="button-left"><button class="mini" size="mini" data-event-opts="{{[['tap',[['orderDetail',['$0'],[[['orderList','',index,'order_id']]]]]]]}}" bindtap="__e">查看详情</button></view><view class="button-right"><button class="mini" size="mini" data-event-opts="{{[['tap',[['openChoosePayment',['$0','$1','$2'],[[['orderList','',index,'order_id']],[['orderList','',index,'pay_money']],[['orderList','',index,'out_trade_no']]]]]]]}}" bindtap="__e">立即支付</button></view></view></block><block wx:if="{{item.$orig.order_status=='complete'}}"><view class="button"><view class="button-left"><button class="mini" size="mini" data-event-opts="{{[['tap',[['orderDetail',['$0'],[[['orderList','',index,'order_id']]]]]]]}}" bindtap="__e">查看详情</button></view><view class="button-right"><button class="mini" size="mini" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" bindtap="__e">查看卡包</button></view></view></block></view></view></block></view></block><block wx:else><view class="card-no-data"><view class="card-image"><image mode="widthFix" src="{{$root.g4}}"></image></view><view class="text">暂无订单记录</view><view class="btn"><button type="primary" size="mini" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e">去挑礼物</button></view></view></block></view></mescroll-uni><loading-cover class="vue-ref" vue-id="3948d84d-2" data-ref="loadingCover" bind:__l="__l"></loading-cover><ns-payment class="vue-ref" vue-id="3948d84d-3" payMoney="{{price}}" data-ref="choosePaymentPopup" data-event-opts="{{[['^confirm',[['gotoBuy']]]]}}" bind:confirm="__e" bind:__l="__l"></ns-payment></view>