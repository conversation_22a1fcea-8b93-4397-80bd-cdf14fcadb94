<page-meta page-style="{{themeColor}}"></page-meta><view class="{{['order-container',(isIphoneX)?'safe-area':'']}}"><view class="site-wrap"><view class="site-body"><view class="card-head"><image src="{{$root.g0}}" mode="widthFix"></image><text>{{orderDetail.card_right_type=='balance'?'储值卡':'礼品卡'}}</text><view class="order-status">{{orderDetail.order_status_name}}</view></view><view data-event-opts="{{[['tap',[['toCardInfo']]]]}}" class="giftcard-wrap" bindtap="__e"><view class="card-img"><image src="{{orderDetail.card_cover?$root.g1:$root.g2}}" mode="aspectFill"></image></view><view class="goods-info"><view class="goods-name">{{orderDetail.order_name}}</view><view class="goods-price price-font">{{"￥"+orderDetail.goods_money}}</view></view></view><view class="order-content"><view class="content-item"><view class="label">订单号</view><view class="value">{{orderDetail.order_no}}</view></view><view class="content-item"><view class="label">下单时间</view><view class="value">{{$root.g3}}</view></view><view class="content-item"><view class="label">买家留言</view><view class="value">{{orderDetail.buyer_message?orderDetail.buyer_message:'无'}}</view></view><view class="content-item"><view class="label">购买数量</view><view class="value">{{orderDetail.num}}</view></view><view class="content-item"><view class="label">订单金额</view><view class="value">{{"￥"+orderDetail.order_money}}</view></view></view><block wx:if="{{orderDetail.order_status=='topay'}}"><view class="button"><view class="button-left"><button size="mini" data-event-opts="{{[['tap',[['closeOrder',['$0'],['orderDetail.order_id']]]]]}}" bindtap="__e">关闭订单</button></view><view class="button-right"><button size="mini" type="primary" data-event-opts="{{[['tap',[['openChoosePayment']]]]}}" bindtap="__e">立即支付</button></view></view></block><block wx:if="{{orderDetail.order_status=='complete'}}"><view class="button"><view class="button-right"><button size="mini" type="primary" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e">查看卡包</button></view></view></block></view></view><view class="tab-bar-placeholder"></view><ns-payment class="vue-ref" vue-id="ea19e58c-1" payMoney="{{orderDetail.pay_money}}" isBalance="{{orderDetail&&orderDetail.giftcard_info&&orderDetail.giftcard_info.card_right_type!='balance'?1:0}}" data-ref="choosePaymentPopup" data-event-opts="{{[['^confirm',[['gotoBuy']]]]}}" bind:confirm="__e" bind:__l="__l"></ns-payment><loading-cover class="vue-ref" vue-id="ea19e58c-2" data-ref="loadingCover" bind:__l="__l"></loading-cover><ns-login class="vue-ref" vue-id="ea19e58c-3" data-ref="login" bind:__l="__l"></ns-login></view>