<page-meta page-style="{{themeColor}}" class="data-v-2370aeb8"></page-meta><block wx:if="{{cardInfo}}"><view class="page data-v-2370aeb8"><view class="bg-box data-v-2370aeb8" style="{{'background-image:'+('url('+$root.g0+')')+';'}}"><view class="card-img data-v-2370aeb8"><image src="{{$root.g1}}" mode="widthFix" class="data-v-2370aeb8"></image></view><view class="card-box data-v-2370aeb8"><view class="member-info data-v-2370aeb8"><view class="headimg data-v-2370aeb8"><image src="{{$root.g2}}" mode="aspectFill" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e" class="data-v-2370aeb8"></image></view><view class="member data-v-2370aeb8"><text class="member-name data-v-2370aeb8">{{cardInfo.member_nickname}}</text></view></view><view class="desc data-v-2370aeb8"><view class="title data-v-2370aeb8">{{"送您「"+cardInfo.card_name+"」"}}</view><view class="content data-v-2370aeb8">{{cardInfo.blessing}}</view></view></view></view><image class="bg-img data-v-2370aeb8" src="{{$root.g3}}" mode="widthFix"></image><block wx:if="{{!cardInfo.is_self}}"><block class="data-v-2370aeb8"><block wx:if="{{!cardInfo.is_transfer}}"><button class="btn data-v-2370aeb8" type="primary" data-event-opts="{{[['tap',[['transfer']]]]}}" bindtap="__e">拆开看看</button></block><block wx:else><button class="btn is_transfer data-v-2370aeb8" type="primary">该卡已被领取</button></block></block></block><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-2370aeb8"><uni-popup vue-id="76ec3ba4-1" type="center" data-ref="cardPopup" class="data-v-2370aeb8 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="card-popup-layer popup-layer data-v-2370aeb8"><view data-event-opts="{{[['tap',[['closeCardPopup']]]]}}" class="head-wrap data-v-2370aeb8" bindtap="__e"><text class="iconfont icon-close-guanbi data-v-2370aeb8"></text></view><view class="content data-v-2370aeb8"><view class="data-v-2370aeb8">恭喜您</view><view class="data-v-2370aeb8">成功领取了1张礼品卡</view></view><view class="button-box data-v-2370aeb8"><button type="primary" data-event-opts="{{[['tap',[['closeCardPopup']]]]}}" bindtap="__e" class="data-v-2370aeb8">去看看</button></view></view></uni-popup></view><ns-login vue-id="76ec3ba4-2" data-ref="login" class="data-v-2370aeb8 vue-ref" bind:__l="__l"></ns-login><privacy-popup vue-id="76ec3ba4-3" data-ref="privacyPopup" class="data-v-2370aeb8 vue-ref" bind:__l="__l"></privacy-popup></view></block>