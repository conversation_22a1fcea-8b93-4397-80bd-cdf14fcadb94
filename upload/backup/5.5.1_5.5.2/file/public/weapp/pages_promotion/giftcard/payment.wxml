<page-meta page-style="{{themeColor}}" class="data-v-d9e000f0"></page-meta><view class="{{['order-container','data-v-d9e000f0',(isIphoneX)?'safe-area':'']}}"><view class="site-wrap data-v-d9e000f0"><block wx:if="{{orderPaymentData}}"><view class="site-body data-v-d9e000f0"><view class="card-head data-v-d9e000f0"><image src="{{$root.g0}}" mode="widthFix" class="data-v-d9e000f0"></image><text class="data-v-d9e000f0">{{orderPaymentData.card_right_type=='balance'?'储值卡':'礼品卡'}}</text></view><view class="giftcard-wrap data-v-d9e000f0"><view class="card-img data-v-d9e000f0"><image src="{{orderPaymentData.card_cover?$root.g1:$root.g2}}" mode="aspectFill" class="data-v-d9e000f0"></image></view><view class="goods-info data-v-d9e000f0"><view class="goods-name data-v-d9e000f0">{{orderPaymentData.order_name}}</view><view class="goods-price price-font data-v-d9e000f0">{{"￥"+$root.g3}}</view></view></view><view class="buy-num-box data-v-d9e000f0"><view class="order-cell data-v-d9e000f0"><text class="tit data-v-d9e000f0">购买数量</text><view class="box data-v-d9e000f0"><uni-number-box vue-id="cc74650c-1" min="{{min}}" value="{{orderPaymentData.num}}" size="small" data-event-opts="{{[['^change',[['cartNumChange',['$event']]]]]}}" bind:change="__e" class="data-v-d9e000f0" bind:__l="__l"></uni-number-box></view></view></view></view></block></view><view class="order-money data-v-d9e000f0"><view class="order-cell textarea-box data-v-d9e000f0"><text class="tit data-v-d9e000f0">买家留言</text><view data-event-opts="{{[['tap',[['openPopup',['buyerMessagePopup']]]]]}}" class="box text-overflow data-v-d9e000f0" bindtap="__e"><block wx:if="{{orderPaymentData.buyer_message}}"><text class="data-v-d9e000f0">{{orderPaymentData.buyer_message}}</text></block><block wx:else><text class="color-sub data-v-d9e000f0">无留言</text></block></view><text class="iconfont icon-right data-v-d9e000f0"></text></view></view><view class="tab-bar-placeholder data-v-d9e000f0"></view><view class="{{['order-submit','data-v-d9e000f0',(isIphoneX)?'bottom-safe-area':'']}}"><view class="order-settlement-info data-v-d9e000f0"><text class="font-size-base color-tip margin-right data-v-d9e000f0">{{"共"+orderPaymentData.num+"份"}}</text><text class="font-size-base data-v-d9e000f0">合计：</text><text class="price-color unit data-v-d9e000f0">￥</text><text class="price-color money data-v-d9e000f0">{{$root.g4}}</text></view><view class="submit-btn data-v-d9e000f0"><button class="mini data-v-d9e000f0" type="primary" size="mini" data-event-opts="{{[['tap',[['openChoosePayment']]]]}}" bindtap="__e">立即支付</button></view></view><uni-popup vue-id="cc74650c-2" type="bottom" data-ref="buyerMessagePopup" class="data-v-d9e000f0 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="buyermessag-popup popup data-v-d9e000f0" style="height:auto;" catchtouchmove="__e"><view class="popup-header data-v-d9e000f0"><text class="tit data-v-d9e000f0">买家留言</text><text data-event-opts="{{[['tap',[['closePopup',['buyerMessagePopup']]]]]}}" class="iconfont icon-close data-v-d9e000f0" bindtap="__e"></text></view><scroll-view class="{{['popup-body','data-v-d9e000f0',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view class="data-v-d9e000f0"><view class="buyermessag-cell data-v-d9e000f0"><view class="buyermessag-form-group data-v-d9e000f0"><textarea type="text" maxlength="100" placeholder="留言前建议先与商家协调一致" placeholder-class="color-tip" data-event-opts="{{[['input',[['__set_model',['$0','buyer_message','$event',[]],['orderCreateData']]]]]}}" value="{{orderCreateData.buyer_message}}" bindinput="__e" class="data-v-d9e000f0"></textarea></view></view></view></scroll-view><view data-event-opts="{{[['tap',[['saveBuyerMessage',['$event']]]]]}}" class="{{['popup-footer','data-v-d9e000f0',(isIphoneX)?'bottom-safe-area':'']}}" bindtap="__e"><view class="confirm-btn color-base-bg data-v-d9e000f0">确定</view></view></view></uni-popup><payment vue-id="cc74650c-3" balanceUsable="{{orderPaymentData&&orderPaymentData.giftcard_info&&orderPaymentData.giftcard_info.card_right_type=='balance'?false:true}}" data-ref="choosePaymentPopup" data-event-opts="{{[['^close',[['payClose']]]]}}" bind:close="__e" class="data-v-d9e000f0 vue-ref" bind:__l="__l"></payment><loading-cover vue-id="cc74650c-4" data-ref="loadingCover" class="data-v-d9e000f0 vue-ref" bind:__l="__l"></loading-cover></view>