require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/giftcard/exchange"],{"0eb1":function(t,n,e){},"1e4b":function(t,n,e){"use strict";e.r(n);var i=e("98a1"),a=e.n(i);for(var c in i)["default"].indexOf(c)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(c);n["default"]=a.a},"2bcc":function(t,n,e){"use strict";e.r(n);var i=e("6bbc"),a=e("1e4b");for(var c in a)["default"].indexOf(c)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(c);e("5a83");var o=e("828b"),u=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);n["default"]=u.exports},"5a83":function(t,n,e){"use strict";var i=e("0eb1"),a=e.n(i);a.a},"6bbc":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return c})),e.d(n,"a",(function(){return i}));var i={nsLogin:function(){return Promise.all([e.e("common/vendor"),e.e("components/ns-login/ns-login")]).then(e.bind(null,"2910"))}},a=function(){var t=this.$createElement,n=(this._self._c,this.$util.img("public/uniapp/giftcard/exchange-bg.png")),e=this.$util.img("public/uniapp/giftcard/exchange-icon-account.png"),i=this.$util.img("public/uniapp/giftcard/exchange-icon-pwd.png");this.$mp.data=Object.assign({},{$root:{g0:n,g1:e,g2:i}})},c=[]},"98a1":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i={data:function(){return{cardNum:"",card:"",isRepeat:!1}},onLoad:function(){var t=this;this.storeToken||setTimeout((function(){t.$refs.login.open()}),500)},onShow:function(){},methods:{onInput:function(t){var n=t.target.value;this.cardNum=n},onInputTo:function(t){var n=t.target.value;this.card=n},goToExchange:function(){this.$util.redirectTo("/pages_promotion/giftcard/list")},exchange:function(){var t=this;this.isRepeat||(this.isRepeat=!0,this.$api.sendRequest({url:"/giftcard/api/activate/activate",data:{card_no:this.cardNum,card_cdk:this.card},success:function(n){n.code>=0?n.data?(t.$util.showToast({title:"兑换成功"}),setTimeout((function(){t.$util.redirectTo("/pages_promotion/giftcard/card_info",{member_card_id:n.data},"redirectTo")}),1500)):t.$util.showToast({title:"卡号或密码错误，请重新输入"}):t.$util.showToast({title:n.message}),t.isRepeat=!1}}))}}};n.default=i},eaaf:function(t,n,e){"use strict";(function(t,n){var i=e("47a9");e("d381");i(e("3240"));var a=i(e("2bcc"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["eaaf","common/runtime","common/vendor"]]]);