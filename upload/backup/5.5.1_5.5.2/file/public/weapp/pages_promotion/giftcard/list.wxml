<page-meta page-style="{{themeColor}}" class="data-v-2ca381b5"></page-meta><view class="page data-v-2ca381b5"><mescroll-uni vue-id="58be0a7e-1" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" class="data-v-2ca381b5 vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-2ca381b5"><view class="cf-container color-line-border data-v-2ca381b5"><view class="tab data-v-2ca381b5"><view data-event-opts="{{[['tap',[['changeState',['all']]]]]}}" bindtap="__e" class="data-v-2ca381b5"><text class="{{['data-v-2ca381b5',status=='all'?' active ':'']}}">全部</text></view><view data-event-opts="{{[['tap',[['changeState',['to_use']]]]]}}" bindtap="__e" class="data-v-2ca381b5"><text class="{{['data-v-2ca381b5',status=='to_use'?' active ':'']}}">待使用</text></view><view data-event-opts="{{[['tap',[['changeState',['used']]]]]}}" bindtap="__e" class="data-v-2ca381b5"><text class="{{['data-v-2ca381b5',status=='used'?' active ':'']}}">已使用</text></view><view data-event-opts="{{[['tap',[['changeState',['expire']]]]]}}" bindtap="__e" class="data-v-2ca381b5"><text class="{{['data-v-2ca381b5',status=='expire'?' active ':'']}}">已过期</text></view></view></view><block wx:if="{{$root.g0>0}}"><view class="card-box data-v-2ca381b5"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="data-v-2ca381b5"><view class="card-item data-v-2ca381b5"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index,'member_card_id']]]]]]]}}" class="card-content data-v-2ca381b5" bindtap="__e"><view class="card-img data-v-2ca381b5"><image src="{{item.g1}}" mode="aspectFill" class="data-v-2ca381b5"></image><block wx:if="{{item.$orig.card_right_type=='balance'}}"><view class="card-label data-v-2ca381b5">{{''+item.$orig.balance+'元储值卡'}}</view></block><block wx:if="{{item.$orig.card_right_type=='goods'}}"><view class="card-label-img data-v-2ca381b5"><image src="{{item.g2}}" mode="heightFix" class="data-v-2ca381b5"></image></view></block></view><view class="card-no data-v-2ca381b5">{{item.$orig.card_no}}</view></view><block wx:if="{{item.$orig.status=='to_use'}}"><view class="button data-v-2ca381b5"><block wx:if="{{item.m0}}"><view class="button-left data-v-2ca381b5"><button class="mini data-v-2ca381b5" size="mini" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" bindtap="__e">送给朋友</button></view></block><block wx:if="{{!item.$orig.is_transfer}}"><view class="button-right data-v-2ca381b5"><button class="mini data-v-2ca381b5" size="mini" type="primary" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" bindtap="__e">立即使用</button></view></block></view></block><block wx:else><view class="button data-v-2ca381b5"><view class="button-right no-use data-v-2ca381b5"><block wx:if="{{item.$orig.status=='used'}}"><button class="mini data-v-2ca381b5" size="mini" type="primary" data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" bindtap="__e">已使用</button></block><block wx:if="{{item.$orig.status=='expire'}}"><button class="mini data-v-2ca381b5" size="mini" type="primary" data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" bindtap="__e">已过期</button></block></view></view></block></view></view></block></view></block><block wx:else><view class="card-no-data data-v-2ca381b5"><view class="card-image data-v-2ca381b5"><image mode="widthFix" src="{{$root.g3}}" class="data-v-2ca381b5"></image></view><view class="text data-v-2ca381b5">暂无卡片记录</view><view class="btn data-v-2ca381b5"><button type="primary" size="mini" data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" bindtap="__e" class="data-v-2ca381b5">去逛逛</button></view></view></block></view></mescroll-uni><loading-cover vue-id="58be0a7e-2" data-ref="loadingCover" class="data-v-2ca381b5 vue-ref" bind:__l="__l"></loading-cover><view class="tab-bar data-v-2ca381b5"><view class="tabbar-border data-v-2ca381b5"></view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tabRedirectTo',['$0'],[[['tabList','',index,'link']]]]]]]}}" class="item data-v-2ca381b5" bindtap="__e"><view class="bd data-v-2ca381b5"><view class="icon data-v-2ca381b5"><image src="{{item.g4}}" class="data-v-2ca381b5"></image></view><view class="label data-v-2ca381b5" style="{{'color:'+(item.$orig.selected?themeStyle&&themeStyle.giftcard.giftcard_promotion_color:'')+';'}}">{{''+item.$orig.text+''}}</view></view></view></block></view><view class="tab-bar-placeholder data-v-2ca381b5"></view><ns-login vue-id="58be0a7e-3" data-ref="login" class="data-v-2ca381b5 vue-ref" bind:__l="__l"></ns-login><privacy-popup vue-id="58be0a7e-4" data-ref="privacyPopup" class="data-v-2ca381b5 vue-ref" bind:__l="__l"></privacy-popup></view>