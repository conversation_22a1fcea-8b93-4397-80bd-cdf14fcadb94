<page-meta page-style="{{themeColor}}" class="data-v-fba26792"></page-meta><view class="page data-v-fba26792"><view class="card-img data-v-fba26792"><image src="{{$root.g0}}" mode="aspectFill" class="data-v-fba26792"></image><block wx:if="{{cardInfo.card_right_type=='balance'}}"><view class="card-label data-v-fba26792">{{cardInfo.balance+"元储值卡"}}</view></block><block wx:if="{{cardInfo.card_right_type=='goods'}}"><view class="card-label-img data-v-fba26792"><image src="{{$root.g1}}" mode="heightFix" class="data-v-fba26792"></image></view></block></view><block wx:if="{{cardInfo.card_right_type=='goods'}}"><view class="goods-list data-v-fba26792"><block wx:if="{{cardInfo.card_right_goods_type=='item'}}"><view class="goods-title data-v-fba26792">本礼品卡包含以下商品</view></block><block wx:if="{{cardInfo.card_right_goods_type=='all'}}"><view class="goods-title data-v-fba26792">{{"以下商品在使用时任选"+cardInfo.card_right_goods_count+"件"}}</view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item data-v-fba26792"><view class="goods-image data-v-fba26792"><image src="{{item.g2}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e" class="data-v-fba26792"></image></view><view class="goods-info data-v-fba26792"><view class="goods-name data-v-fba26792">{{item.$orig.sku_name}}</view><view class="goods-num data-v-fba26792"><block wx:if="{{cardInfo.status=='to_use'}}"><block class="data-v-fba26792"><block wx:if="{{cardInfo.card_right_goods_type=='item'}}"><text class="data-v-fba26792">{{"x "+item.$orig.total_num}}</text></block></block></block></view></view></view></block><view class="card-box data-v-fba26792"><view class="card-info data-v-fba26792"><view class="card-item data-v-fba26792"><view class="data-v-fba26792">过期时间：</view><view class="data-v-fba26792">{{cardInfo.valid_time>0?$root.g3:'永久有效'}}</view></view><block wx:if="{{cardInfo.from_member_id>0}}"><view class="card-item data-v-fba26792"><view class="data-v-fba26792">赠送人：</view><view class="data-v-fba26792">{{cardInfo.from_member_nickname}}</view></view></block><block wx:if="{{shopInfo&&shopInfo.mobile}}"><view class="card-item data-v-fba26792"><view class="data-v-fba26792">商户电话：</view><view class="color data-v-fba26792">{{shopInfo.mobile}}</view></view></block><view class="card-item data-v-fba26792"><view class="data-v-fba26792">使用须知：</view><view class="details data-v-fba26792"><block wx:if="{{cardInfo.instruction}}"><ns-mp-html vue-id="709cce3a-1" content="{{cardInfo.instruction}}" class="data-v-fba26792" bind:__l="__l"></ns-mp-html></block><block wx:else><ns-mp-html vue-id="709cce3a-2" content="{{cardInfo.giftcard_desc}}" class="data-v-fba26792" bind:__l="__l"></ns-mp-html></block></view></view></view></view></view></block><view class="tab-bar-placeholder data-v-fba26792"></view><block wx:if="{{cardInfo.status=='to_use'}}"><view class="btn data-v-fba26792"><block wx:if="{{cardInfo.is_allow_transfer}}"><button data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="give-btn data-v-fba26792" bindtap="__e">赠送好友</button></block><button class="use-btn data-v-fba26792" type="primary" data-event-opts="{{[['tap',[['toUse']]]]}}" bindtap="__e">立即使用</button></view></block><block wx:if="{{cardInfo.status=='used'}}"><view class="btn data-v-fba26792"><button class="use-btn data-v-fba26792" type="primary" data-event-opts="{{[['tap',[['useInfo']]]]}}" bindtap="__e">已使用</button></view></block><loading-cover vue-id="709cce3a-3" data-ref="loadingCover" class="data-v-fba26792 vue-ref" bind:__l="__l"></loading-cover><ns-login vue-id="709cce3a-4" data-ref="login" class="data-v-fba26792 vue-ref" bind:__l="__l"></ns-login><block wx:if="{{cardInfo.card_right_type=='balance'}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-fba26792"><uni-popup vue-id="709cce3a-5" type="center" data-ref="cardPopup" class="data-v-fba26792 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="card-popup-layer popup-layer data-v-fba26792"><view data-event-opts="{{[['tap',[['closeCardPopup']]]]}}" class="head-wrap data-v-fba26792" bindtap="__e"><text class="iconfont icon-close-guanbi data-v-fba26792"></text></view><view class="content data-v-fba26792"><view class="data-v-fba26792">{{"您的"+cardInfo.balance+"元储值卡"}}</view><view class="data-v-fba26792">已放入您的账户中</view></view><view class="button-box data-v-fba26792"><button type="primary" data-event-opts="{{[['tap',[['closeCardPopup']]]]}}" bindtap="__e" class="data-v-fba26792">去看看</button></view></view></uni-popup></view></block></view>