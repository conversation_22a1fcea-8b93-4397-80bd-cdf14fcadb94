require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/giftcard/detail"],{"3f57":function(t,i,e){"use strict";var n=e("5f65"),a=e.n(n);a.a},"43df":function(t,i,e){"use strict";var n=e("814a"),a=e.n(n);a.a},"5f65":function(t,i,e){},7650:function(t,i,e){"use strict";e.r(i);var n=e("866c"),a=e.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(o);i["default"]=a.a},"814a":function(t,i,e){},"866c":function(t,i,e){"use strict";(function(t){var n=e("47a9");Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;n(e("0817"));var a={data:function(){return{image:"",imageList:null,giftcardId:0,giftDetail:null,goodsList:null,btnSwitch:!1}},onLoad:function(i){var e=this;if(i.source_member&&t.setStorageSync("source_member",i.source_member),i.id&&(this.giftcardId=i.id),i.scene){var n=decodeURIComponent(i.scene);n=n.split("&"),n.length&&n.forEach((function(t){-1!=t.indexOf("id")&&(e.giftcardId=t.split("-")[1])}))}this.getGiftcardDetail()},onShow:function(){},methods:{getGiftcardDetail:function(){var t=this;this.$api.sendRequest({url:"/giftcard/api/giftcard/detail",data:{giftcard_id:this.giftcardId},success:function(i){i.code>=0&&i.data?(t.giftDetail=i.data,t.imageList=i.data.media_list,t.image=t.imageList.length>0?t.imageList[0]:"",t.giftDetail&&t.$langConfig.title(t.giftDetail.card_name),t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:i.message,mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages_promotion/giftcard/not_exist")}),1500))}})},buy:function(i){var e=this,n={media_id:this.image.media_id,media_path:this.image.media_path,num:1,giftcard_id:this.giftcardId};if(this.btnSwitch)return!1;this.btnSwitch=!0,t.setStorage({key:"giftcardOrderCreateData",data:n,success:function(){e.$util.redirectTo("/pages_promotion/giftcard/payment"),e.btnSwitch=!1}})},imgError:function(t){this.giftDetail.goods_list[t].sku_info.sku_image=this.$util.getDefaultImage().goods}}};i.default=a}).call(this,e("df3c")["default"])},"887a":function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return o})),e.d(i,"a",(function(){return n}));var n={nsMpHtml:function(){return e.e("components/ns-mp-html/ns-mp-html").then(e.bind(null,"d108"))},loadingCover:function(){return e.e("components/loading-cover/loading-cover").then(e.bind(null,"c003"))},nsLogin:function(){return Promise.all([e.e("common/vendor"),e.e("components/ns-login/ns-login")]).then(e.bind(null,"2910"))}},a=function(){var t=this,i=t.$createElement,e=(t._self._c,t.$util.img(t.image.media_path)),n=t.giftDetail&&"goods"==t.giftDetail.card_right_type?t.$util.img("public/uniapp/giftcard/giftcard-label.png"):null,a=t.imageList?t.__map(t.imageList,(function(i,e){var n=t.__get_orig(i),a=t.$util.img(i.media_path);return{$orig:n,g2:a}})):null,o=t.giftDetail&&"goods"==t.giftDetail.card_right_type&&t.giftDetail.goods_list?t.__map(t.giftDetail.goods_list,(function(i,e){var n=t.__get_orig(i),a=t.$util.img(i.sku_info.sku_image);return{$orig:n,g3:a}})):null;t._isMounted||(t.e0=function(i,e){var n=arguments[arguments.length-1].currentTarget.dataset,a=n.eventParams||n["event-params"];e=a.item;t.image=e}),t.$mp.data=Object.assign({},{$root:{g0:e,g1:n,l0:a,l1:o}})},o=[]},"8fc5":function(t,i,e){"use strict";(function(t,i){var n=e("47a9");e("d381");n(e("3240"));var a=n(e("a78c"));t.__webpack_require_UNI_MP_PLUGIN__=e,i(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},a78c:function(t,i,e){"use strict";e.r(i);var n=e("887a"),a=e("7650");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(o);e("3f57"),e("43df");var r=e("828b"),c=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"0aa21b3f",null,!1,n["a"],void 0);i["default"]=c.exports}},[["8fc5","common/runtime","common/vendor"]]]);