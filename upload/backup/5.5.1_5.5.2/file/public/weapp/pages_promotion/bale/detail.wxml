<page-meta page-style="{{themeColor}}" class="data-v-6dc446d5"></page-meta><view class="container data-v-6dc446d5"><block wx:if="{{info}}"><block class="data-v-6dc446d5"><view class="activity-head color-base-bg data-v-6dc446d5"><view class="activity-text font-size-toolbar data-v-6dc446d5">{{"以下商品"+info.price+"元任选"+info.num+"件"}}</view><block wx:if="{{info.status==0}}"><view class="no-start data-v-6dc446d5">活动未开始</view></block><block wx:if="{{timeMachine}}"><view class="time data-v-6dc446d5">距离结束还剩<uni-count-down vue-id="13d0793a-1" day="{{timeMachine.d}}" hour="{{timeMachine.h}}" minute="{{timeMachine.i}}" second="{{timeMachine.s}}" color="#fff" splitorColor="#fff !important" backgroundColor="none" border-color="transparent" class="data-v-6dc446d5" bind:__l="__l"></uni-count-down></view></block></view><view class="goods-wrap data-v-6dc446d5"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item data-v-6dc446d5"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="goods-image data-v-6dc446d5" bindtap="__e"><image src="{{item.g0}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e" class="data-v-6dc446d5"></image></view><view class="goods-info data-v-6dc446d5"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="name data-v-6dc446d5" bindtap="__e">{{''+item.$orig.goods_name+''}}</view><view class="spec-name data-v-6dc446d5">{{item.$orig.spec_name}}</view><view class="introduction data-v-6dc446d5">{{item.$orig.introduction}}</view><view class="goods-bottom data-v-6dc446d5"><view class="price price-style large data-v-6dc446d5"><text class="unit price-style small data-v-6dc446d5">{{item.m0}}</text>{{''+item.g1[0]+''}}<text class="unit price-style small data-v-6dc446d5">{{"."+item.g2[1]}}</text></view><view class="num data-v-6dc446d5"><block wx:if="{{cart['goods_'+item.$orig.goods_id]==undefined||!cart['goods_'+item.$orig.goods_id]['sku_'+item.$orig.sku_id]}}"><block class="data-v-6dc446d5"><view class="num-wrap data-v-6dc446d5"><block wx:if="{{item.$orig.stock>0}}"><text data-event-opts="{{[['tap',[['singleSkuPlus',['$0'],[[['goodsList','',index]]]]]]]}}" class="iconfont icon-add-fill color-base-text data-v-6dc446d5" bindtap="__e"></text></block><block wx:else><text class="color-sub data-v-6dc446d5">库存不足</text></block></view></block></block><block wx:else><block wx:if="{{cart['goods_'+item.$orig.goods_id]['sku_'+item.$orig.sku_id]}}"><block class="data-v-6dc446d5"><view class="num-wrap data-v-6dc446d5"><text data-event-opts="{{[['tap',[['singleSkuReduce',['$0'],[[['goodsList','',index]]]]]]]}}" class="iconfont icon-jianshao data-v-6dc446d5" bindtap="__e"></text><text class="goods-num data-v-6dc446d5">{{cart['goods_'+item.$orig.goods_id]['sku_'+item.$orig.sku_id].num}}</text><text data-event-opts="{{[['tap',[['singleSkuPlus',['$0'],[[['goodsList','',index]]]]]]]}}" class="iconfont icon-add-fill color-base-text data-v-6dc446d5" bindtap="__e"></text></view></block></block></block></view></view></view></view></block></view><view class="footer-wrap-fill data-v-6dc446d5"></view><view class="footer-wrap data-v-6dc446d5"><view class="left data-v-6dc446d5"><view data-event-opts="{{[['tap',[['openCartPopup',['$event']]]]]}}" class="cart-wrap data-v-6dc446d5" bindtap="__e"><text class="iconfont icon-cart-on data-v-6dc446d5"></text><block wx:if="{{totalNum}}"><text class="num color-base-bg data-v-6dc446d5">{{totalNum}}</text></block></view><view class="data data-v-6dc446d5"><view class="price price-color data-v-6dc446d5"><text class="unit data-v-6dc446d5">{{$root.m1}}</text>{{''+$root.g3[0]+''}}<text class="unit data-v-6dc446d5">{{"."+$root.g4[1]}}</text></view><view class="desc data-v-6dc446d5">{{info.price+"元任选"+info.num+"件"}}</view></view></view><view class="right data-v-6dc446d5"><block wx:if="{{totalNum>0&&totalNum%info.num==0&&info.status!=0}}"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="sub-btn color-base-bg data-v-6dc446d5" bindtap="__e">立即下单</view></block><block wx:else><view class="sub-btn disabled data-v-6dc446d5">立即下单</view></block></view></view><block wx:if="{{cartShow}}"><view data-event-opts="{{[['tap',[['closeCartPopup',['$event']]]]]}}" class="cart-shade data-v-6dc446d5" style="background-color:rgba(0,0,0,.4);" bindtap="__e"></view></block><block wx:if="{{$root.g5}}"><view class="{{['cart-popup','data-v-6dc446d5',(cartShow)?'show':'']}}"><view class="header data-v-6dc446d5"><view class="left data-v-6dc446d5"><text class="data-v-6dc446d5">购物车</text></view><view data-event-opts="{{[['tap',[['clearCart',['$event']]]]]}}" class="right data-v-6dc446d5" bindtap="__e"><text class="iconfont icon-icon7 data-v-6dc446d5"></text><text class="data-v-6dc446d5">清空购物车</text></view></view><scroll-view class="cart-goods-wrap data-v-6dc446d5" scroll-y="{{true}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item data-v-6dc446d5"><view class="info data-v-6dc446d5"><text class="goods-name data-v-6dc446d5">{{item.$orig.goods_name}}</text><block wx:if="{{item.$orig.goods_name!=item.$orig.sku_name}}"><text class="sku-name data-v-6dc446d5">{{item.f0}}</text></block></view><view class="price price-style large data-v-6dc446d5"><text class="unit price-style small data-v-6dc446d5">{{item.m2}}</text>{{''+item.g6[0]+''}}<text class="unit price-style small data-v-6dc446d5">{{"."+item.g7[1]}}</text></view><view class="num data-v-6dc446d5"><text data-event-opts="{{[['tap',[['singleSkuReduce',['$0'],[[['skuList','',index]]]]]]]}}" class="iconfont icon-jianshao data-v-6dc446d5" bindtap="__e"></text><text class="goods-num data-v-6dc446d5">{{item.$orig.num}}</text><text data-event-opts="{{[['tap',[['singleSkuPlus',['$0'],[[['skuList','',index]]]]]]]}}" class="iconfont icon-add-fill color-base-text data-v-6dc446d5" bindtap="__e"></text></view></view></block></scroll-view></view></block></block></block><block wx:if="{{goodsSkuDetail}}"><ns-goods-sku vue-id="13d0793a-2" goodsId="{{goodsSkuDetail.goods_id}}" goods-detail="{{goodsSkuDetail}}" data-ref="goodsSku" data-event-opts="{{[['^refresh',[['refreshGoodsSkuDetail']]],['^confirm',[['joinCart']]]]}}" bind:refresh="__e" bind:confirm="__e" class="data-v-6dc446d5 vue-ref" bind:__l="__l"></ns-goods-sku></block><ns-login vue-id="13d0793a-3" data-ref="login" class="data-v-6dc446d5 vue-ref" bind:__l="__l"></ns-login><hover-nav vue-id="13d0793a-4" class="data-v-6dc446d5" bind:__l="__l"></hover-nav><loading-cover vue-id="13d0793a-5" data-ref="loadingCover" class="data-v-6dc446d5 vue-ref" bind:__l="__l"></loading-cover><privacy-popup vue-id="13d0793a-6" data-ref="privacyPopup" class="data-v-6dc446d5 vue-ref" bind:__l="__l"></privacy-popup></view>