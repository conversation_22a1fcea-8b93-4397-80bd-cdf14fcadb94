require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/pinfan/share"],{"38e2":function(i,t,e){"use strict";(function(i,t){var n=e("47a9");e("d381");n(e("3240"));var o=n(e("940f"));i.__webpack_require_UNI_MP_PLUGIN__=e,t(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"428e":function(i,t,e){},"6a86":function(i,t,e){"use strict";e.r(t);var n=e("828d"),o=e.n(n);for(var a in n)["default"].indexOf(a)<0&&function(i){e.d(t,i,(function(){return n[i]}))}(a);t["default"]=o.a},"828d":function(i,t,e){"use strict";(function(i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={components:{uniCountDown:function(){e.e("components/uni-count-down/uni-count-down").then(function(){return resolve(e("e12a"))}.bind(null,e)).catch(e.oe)}},data:function(){return{id:0,groupDetail:{sku_image:""},kill:0,poster:"-1",posterMsg:"",posterHeight:0}},onLoad:function(i){var t=this;setTimeout((function(){t.addonIsExist.pinfan||(t.$util.showToast({title:"商家未开启拼团返利",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.id=i.id,this.getGroupDetail()},methods:{getGroupDetail:function(){var i=this;this.$api.sendRequest({url:"/pinfan/api/order/detail",data:{id:this.id},success:function(t){if(0==t.code&&t.data){for(var e=[],n=0;n<t.data.pintuan_num-t.data.member_list.length;n++){e.push("")}i.kill=t.data.pintuan_num-t.data.pintuan_count,t.data.member_list=t.data.member_list.concat(e),t.data.group_end_time>t.timestamp?t.data.timeMachine=i.$util.countDown(t.data.group_end_time-t.timestamp):t.data.timeMachine=null,i.groupDetail=t.data,i.groupDetail.member_list=i.groupDetail.member_list.filter((function(i,t){return i})),i.setPublicShare()}else i.$util.showToast({title:t.message});i.$refs.loadingCover&&i.$refs.loadingCover.hide()},fail:function(t){i.$refs.loadingCover&&i.$refs.loadingCover.hide()}})},toPintuanDetail:function(i){this.$util.redirectTo("/pages_promotion/pinfan/detail",{pinfan_id:i})},toDetail:function(i,t){this.$util.redirectTo("/pages_promotion/pintuan/detail",{pintuan_id:this.groupDetail.pintuan_id,group_id:t})},imageError:function(){this.groupDetail.sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},openSharePopup:function(){this.$refs.sharePopup.open()},closeSharePopup:function(){this.$refs.sharePopup.close()},copyUrl:function(){var i=this,t=this.$config.h5Domain+"/pages_promotion/pinfan/detail?pinfan_id="+this.groupDetail.pintuan_id+"&group_id="+this.groupDetail.group_id;this.memberInfo&&this.memberInfo.member_id&&(t+="&source_member="+this.memberInfo.member_id),this.$util.copy(t,(function(){i.closeSharePopup()}))},orderDetail:function(i){this.$util.redirectTo("/pages/order/detail",{order_id:i.order_id})},setPublicShare:function(){var i=this.$config.h5Domain+"/pages_promotion/pinfan/detail?pinfan_id="+this.groupDetail.pintuan_id+"&group_id="+this.groupDetail.group_id;this.memberInfo&&this.memberInfo.member_id&&(i+="&source_member="+this.memberInfo.member_id),this.$util.setPublicShare({title:this.groupDetail.sku_name,desc:"还差"+(this.groupDetail.pintuan_num-this.groupDetail.pintuan_count)+"人就拼团成功了",link:i,imgUrl:this.$util.img(this.groupDetail.sku_image)})},openPosterPopup:function(){this.getGoodsPoster(),this.$refs.sharePopup.close()},closePosterPopup:function(){this.$refs.posterPopup.close()},getGoodsPoster:function(){var t=this;i.showLoading({title:"海报生成中..."});var e={id:this.groupDetail.pintuan_goods_id,pinfan:this.groupDetail.pintuan_id,group:this.groupDetail.group_id};this.$api.sendRequest({url:"/pinfan/api/goods/poster",data:{page:"/pages_promotion/rebate/order",qrcode_param:JSON.stringify(e)},success:function(e){0==e.code?(t.poster=e.data.path+"?time="+(new Date).getTime(),t.$refs.posterPopup.open()):(t.posterMsg=e.message,t.$util.showToast({title:t.posterMsg})),i.hideLoading()},fail:function(t){i.hideLoading()}})}},onShareAppMessage:function(i){var t="/pages_promotion/pinfan/detail?pinfan_id="+this.groupDetail.pintuan_id+"&group_id="+this.groupDetail.group_id;return this.memberInfo&&this.memberInfo.member_id&&(t+="&source_member="+this.memberInfo.member_id),{title:"还差"+(this.groupDetail.pintuan_num-this.groupDetail.pintuan_count)+"人拼团成功，"+this.groupDetail.sku_name,imageUrl:this.$util.img(this.groupDetail.sku_image,{size:"big"}),path:t,success:function(i){},fail:function(i){}}}};t.default=n}).call(this,e("df3c")["default"])},"940f":function(i,t,e){"use strict";e.r(t);var n=e("b922"),o=e("6a86");for(var a in o)["default"].indexOf(a)<0&&function(i){e.d(t,i,(function(){return o[i]}))}(a);e("ac74");var u=e("828b"),r=Object(u["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=r.exports},ac74:function(i,t,e){"use strict";var n=e("428e"),o=e.n(n);o.a},b922:function(i,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){return n}));var n={uniCountDown:function(){return e.e("components/uni-count-down/uni-count-down").then(e.bind(null,"e12a"))},loadingCover:function(){return e.e("components/loading-cover/loading-cover").then(e.bind(null,"c003"))},uniPopup:function(){return e.e("components/uni-popup/uni-popup").then(e.bind(null,"d745"))}},o=function(){var i=this,t=i.$createElement,e=(i._self._c,i.$util.img("public/uniapp/pintuan/pintuan_bg.png")),n=i.$util.img(i.groupDetail.sku_image,{size:"big"}),o=i.__map(i.groupDetail.member_list,(function(t,e){var n=i.__get_orig(t),o=t.member_img?i.$util.img(t.member_img):null,a=t.member_img?null:i.$util.img("public/uniapp/common/default_headimg.png");return{$orig:n,g2:o,g3:a}})),a=i.$util.img("public/uniapp/common/spelling_who.png"),u=i.$util.img("public/uniapp/pinfan/pinfan_playing.png"),r="-1"!=i.poster?i.$util.img(i.poster):null;i.$mp.data=Object.assign({},{$root:{g0:e,g1:n,l0:o,g4:a,g5:u,g6:r}})},a=[]}},[["38e2","common/runtime","common/vendor"]]]);