<page-meta page-style="{{themeColor}}"></page-meta><view><view class="pintuan-wrap"><view class="pintuan-list"><image class="pintuan-bg" src="{{$root.g0}}" mode="widthFix" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e"></image><view data-event-opts="{{[['tap',[['toPintuanDetail',['$0'],['groupDetail.pintuan_id']]]]]}}" class="list-item" bindtap="__e"><view class="item-image"><image src="{{$root.g1}}" mode="widthFix" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e"></image></view><view class="item-desc"><view class="desc-title">{{groupDetail.sku_name}}</view><view class="pintuan-num"><text class="color-base-text color-base-bg-light">{{"已团"+groupDetail.group_num+"次"}}</text><text class="color-base-border color-base-text">{{groupDetail.pintuan_num+"人团"}}</text></view><view class="color-tip"><text class="color-base-text font-size-toolbar margin-right"><text class="font-size-tag price-btn">￥</text>{{''+groupDetail.pintuan_price+''}}</text><text class="old-price font-size-tag">{{"￥"+groupDetail.discount_price}}</text></view></view></view></view><view class="pintuan-clustering"><block wx:if="{{groupDetail.timeMachine}}"><view class="tips"><block wx:if="{{kill>0}}"><view class="tips-title">还差<text class="color-base-text">{{kill}}</text>人成团</view></block><block wx:else><view class="tips-title">拼团已成功</view></block>距结束还剩<uni-count-down vue-id="7740feba-1" day="{{groupDetail.timeMachine.d}}" hour="{{groupDetail.timeMachine.h}}" minute="{{groupDetail.timeMachine.i}}" second="{{groupDetail.timeMachine.s}}" backgroundColorClass="color-base-bg" splitorColorClass="color-base-text" borderColor="#fff" color="#fff" bind:__l="__l"></uni-count-down></view></block><block wx:else><view class="tips">活动已结束</view></block><view class="headimg-group"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="group-member"><block wx:if="{{groupDetail.head_id==item.$orig.member_id}}"><view class="mark ns-gradient-promotionpages-pintuan-share-share">团长</view></block><view class="member-face"><image src="{{item.$orig.member_img?item.g2:item.g3}}" mode="aspectFill"></image></view></view></block><block wx:for="{{kill}}" wx:for-item="itm" wx:for-index="__i0__"><view class="group-member"><view class="member-face"><image src="{{$root.g4}}" mode="aspectFill"></image></view></view></block></view><block wx:if="{{groupDetail.timeMachine}}"><view class="pintuan-btn-box"><button class="pintuan-btn" type="primary" open-type="share">邀请好友参团</button><button data-event-opts="{{[['tap',[['orderDetail',['$0'],['groupDetail']]]]]}}" class="one_btn pintuan-btn" bindtap="__e">查看订单</button></view></block></view><image class="pintuan-playing" src="{{$root.g5}}" mode="widthFix"></image></view><loading-cover class="vue-ref" vue-id="7740feba-2" data-ref="loadingCover" bind:__l="__l"></loading-cover><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><uni-popup class="share-popup vue-ref" vue-id="7740feba-3" type="bottom" data-ref="sharePopup" bind:__l="__l" vue-slots="{{['default']}}"><view><view class="share-title">分享</view><view class="share-content"><view class="share-box"><button class="share-btn" plain="{{true}}" open-type="share"><view class="iconfont icon-share-friend"></view><text>分享给好友</text></button></view><view data-event-opts="{{[['tap',[['openPosterPopup',['$event']]]]]}}" class="share-box" bindtap="__e"><button class="share-btn" plain="{{true}}"><view class="iconfont icon-pengyouquan"></view><text>生成分享海报</text></button></view></view><view data-event-opts="{{[['tap',[['closeSharePopup',['$event']]]]]}}" class="share-footer" bindtap="__e"><text>取消分享</text></view></view></uni-popup></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="poster-layer" catchtouchmove="__e"><uni-popup class="vue-ref" vue-id="7740feba-4" type="center" data-ref="posterPopup" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{poster!='-1'}}"><view class="poster-wrap"><view class="image-wrap"><image src="{{$root.g6}}" show-menu-by-longpress="{{true}}" mode="widthFix"></image><view data-event-opts="{{[['tap',[['closePosterPopup']]]]}}" class="close iconfont icon-close" bindtap="__e"></view></view><view data-event-opts="{{[['tap',[['saveGoodsPoster']]]]}}" class="save-btn" bindtap="__e">保存图片</view></view></block></uni-popup></view><privacy-popup class="vue-ref" vue-id="7740feba-5" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>