<page-meta page-style="{{themeColor}}" class="data-v-76f2f5ed"></page-meta><view class="body data-v-76f2f5ed"><view data-event-opts="{{[['tap',[['toPintuanDetail',['$0'],['groupDetail.pintuan_id']]]]]}}" class="center-content data-v-76f2f5ed" bindtap="__e"><view class="center-left data-v-76f2f5ed"><image src="{{$root.g0}}" mode class="data-v-76f2f5ed"></image></view><view class="center-right data-v-76f2f5ed"><view class="center-right-top center-right-font data-v-76f2f5ed">{{groupDetail.sku_name}}</view><view class="center-right-center data-v-76f2f5ed">{{groupDetail.pintuan_num+"人团，已团"+goodsSkuDetail.group_num+'次'}}</view><view class="center-right-bottom data-v-76f2f5ed"><view class="center-right-bottom-money center-right-fontnew data-v-76f2f5ed" title="{{groupDetail.pintuan_price}}">{{'￥'+groupDetail.pintuan_price}}</view></view><view class="center-right-bottom-time data-v-76f2f5ed">距结束：<uni-count-down vue-id="5c079694-1" day="{{groupDetail.timeMachine.d}}" hour="{{groupDetail.timeMachine.h}}" minute="{{groupDetail.timeMachine.i}}" second="{{groupDetail.timeMachine.s}}" backgroundColorClass="color-base-bg" splitorColorClass="color-base-text" borderColor="#fff" color="#fff" class="data-v-76f2f5ed" bind:__l="__l"></uni-count-down></view></view></view><view class="die data-v-76f2f5ed"><block wx:if="{{groupDetail.timeMachine.d>0||groupDetail.timeMachine.h>0||groupDetail.timeMachine.i>0||groupDetail.timeMachine.s>0}}"><view class="die-title data-v-76f2f5ed">{{'等待参团，仅剩'+kill+'个名额'}}</view></block><block wx:else><view class="die-title data-v-76f2f5ed">活动已结束</view></block><view class="die-content data-v-76f2f5ed"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="die-content-image data-v-76f2f5ed"><image src="{{item.$orig.member_img?item.g1:item.g2}}" mode class="data-v-76f2f5ed"></image></view></block><block wx:for="{{kill}}" wx:for-item="itm" wx:for-index="__i0__"><view class="die-content-image data-v-76f2f5ed"><image src="{{$root.g3}}" mode="aspectFill" class="data-v-76f2f5ed"></image></view></block></view><block wx:if="{{groupDetail.timeMachine.d>0||groupDetail.timeMachine.h>0||groupDetail.timeMachine.i>0||groupDetail.timeMachine.s>0}}"><view data-event-opts="{{[['tap',[['delegation',['$event']]]]]}}" class="die-bottom-btn data-v-76f2f5ed" bindtap="__e">去参团</view></block></view><uni-popup class="pintuan-popup-layer data-v-76f2f5ed vue-ref" vue-id="5c079694-2" type="center" data-ref="pintuanPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="layer data-v-76f2f5ed"><view class="popup-center data-v-76f2f5ed"><view class="popup-center-top data-v-76f2f5ed">参与的拼单</view><view class="popup-center-text data-v-76f2f5ed">仅剩<text class="data-v-76f2f5ed">{{kill}}</text>个名额,<uni-count-down vue-id="{{('5c079694-3')+','+('5c079694-2')}}" day="{{groupDetail.timeMachine.d}}" hour="{{groupDetail.timeMachine.h}}" minute="{{groupDetail.timeMachine.i}}" second="{{groupDetail.timeMachine.s}}" backgroundColorClass="color-base-bg" splitorColorClass="color-base-text" borderColor="#fff" color="#fff" class="data-v-76f2f5ed" bind:__l="__l"></uni-count-down>后结束</view><view class="popup-center-center data-v-76f2f5ed"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="popup-center-center-image data-v-76f2f5ed"><image src="{{item.$orig.member_img?item.g4:item.g5}}" mode class="data-v-76f2f5ed"></image></view></block><block wx:for="{{kill}}" wx:for-item="itm" wx:for-index="__i1__"><view class="popup-center-center-image data-v-76f2f5ed"><image src="{{$root.g6}}" mode="aspectFill" class="data-v-76f2f5ed"></image></view></block></view><view data-event-opts="{{[['tap',[['joinPintuan']]]]}}" class="popup-center-die data-v-76f2f5ed" bindtap="__e">参与拼单</view><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="popup-center-delect data-v-76f2f5ed" bindtap="__e"><image src="{{$root.g7}}" class="data-v-76f2f5ed"></image></view></view></view></uni-popup><ns-login vue-id="5c079694-4" data-ref="login" class="data-v-76f2f5ed vue-ref" bind:__l="__l"></ns-login><ns-goods-sku vue-id="5c079694-5" goods-detail="{{goodsSkuDetail}}" data-ref="goodsSku" data-event-opts="{{[['^refresh',[['refreshGoodsSkuDetail']]]]}}" bind:refresh="__e" class="data-v-76f2f5ed vue-ref" bind:__l="__l"></ns-goods-sku></view>