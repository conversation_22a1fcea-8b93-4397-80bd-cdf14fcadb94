require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_promotion/pinfan/payment"],{"0287":function(n,t,e){},"0ffa":function(n,t,e){"use strict";e.d(t,"b",(function(){return a})),e.d(t,"c",(function(){return r})),e.d(t,"a",(function(){return o}));var o={commonPayment:function(){return Promise.all([e.e("common/vendor"),e.e("components/common-payment/common-payment")]).then(e.bind(null,"47f2"))}},a=function(){var n=this.$createElement;this._self._c},r=[]},"1c23":function(n,t,e){"use strict";e.r(t);var o=e("20f9"),a=e.n(o);for(var r in o)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(r);t["default"]=a.a},"20f9":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{api:{payment:"/pinfan/api/ordercreate/payment",calculate:"/pinfan/api/ordercreate/calculate",create:"/pinfan/api/ordercreate/create"}}},provide:function(){return{promotion:this.promotion.bind(this)}},onShow:function(){this.$refs.payment&&this.$refs.payment.pageShow()},methods:{promotion:function(n){if(n.pintuan_info)return{title:n.promotion_type_name,content:n.pintuan_info.pintuan_name}}}}},6891:function(n,t,e){"use strict";var o=e("0287"),a=e.n(o);a.a},"6dd1":function(n,t,e){"use strict";(function(n,t){var o=e("47a9");e("d381");o(e("3240"));var a=o(e("fd3b"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},fd3b:function(n,t,e){"use strict";e.r(t);var o=e("0ffa"),a=e("1c23");for(var r in a)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(r);e("6891");var i=e("828b"),c=Object(i["a"])(a["default"],o["b"],o["c"],!1,null,"7161e5e2",null,!1,o["a"],void 0);t["default"]=c.exports}},[["6dd1","common/runtime","common/vendor"]]]);