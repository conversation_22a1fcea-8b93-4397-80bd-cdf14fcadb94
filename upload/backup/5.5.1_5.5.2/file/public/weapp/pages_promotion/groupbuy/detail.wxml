<page-meta page-style="{{themeColor}}" class="data-v-2620f08c"></page-meta><view class="data-v-2620f08c"><block wx:if="{{goodsSkuDetail&&goodsSkuDetail.config&&goodsSkuDetail.config.nav_bar_switch==0}}"><view class="page-header data-v-2620f08c"><ns-navbar vue-id="1b494c12-1" data="{{navbarData}}" isBack="{{true}}" class="data-v-2620f08c" bind:__l="__l"></ns-navbar></view></block><goods-detail-view vue-id="1b494c12-2" goodsSkuDetail="{{goodsSkuDetail}}" data-ref="goodsDetailView" class="data-v-2620f08c vue-ref" bind:__l="__l" vue-slots="{{['price','entrance','articipation','business','action']}}"><view slot="price"><block wx:if="{{goodsSkuDetail.timeMachine}}"><view class="goods-promotion data-v-2620f08c"><view class="price-info data-v-2620f08c"><view class="icon-box data-v-2620f08c"><text class="iconfont icon-tuangou data-v-2620f08c"></text></view><view class="price-box data-v-2620f08c"><view class="promotion-text data-v-2620f08c">团购</view><block wx:if="{{goodsSkuDetail.sale_show}}"><view class="sale-num data-v-2620f08c">{{"已售"+goodsSkuDetail.sale_num+" "+goodsSkuDetail.unit}}</view></block></view></view><view class="countdown data-v-2620f08c"><view class="txt data-v-2620f08c">距结束仅剩</view><view class="clockrun data-v-2620f08c"><uni-count-down vue-id="{{('1b494c12-3')+','+('1b494c12-2')}}" day="{{goodsSkuDetail.timeMachine.d}}" hour="{{goodsSkuDetail.timeMachine.h}}" minute="{{goodsSkuDetail.timeMachine.i}}" second="{{goodsSkuDetail.timeMachine.s}}" splitorColor="#ffffff" backgroundColor="#ffffff" class="data-v-2620f08c" bind:__l="__l"></uni-count-down></view></view></view></block><view class="group-wrap padding-top data-v-2620f08c"><view class="goods-module-wrap data-v-2620f08c"><text class="promotion-tag data-v-2620f08c">团购价</text><text class="price-symbol price-font data-v-2620f08c">{{$root.m0}}</text><text class="price price-font data-v-2620f08c">{{$root.g0[0]}}</text><text class="price-symbol price-font data-v-2620f08c">{{"."+$root.g1[1]}}</text><block wx:if="{{goodsSkuDetail.price>0}}"><view class="market-price-wrap data-v-2620f08c"><text class="unit price-font data-v-2620f08c">{{$root.m1}}</text><text class="money price-font data-v-2620f08c">{{goodsSkuDetail.price}}</text></view></block><view class="follow-and-share data-v-2620f08c"><text data-event-opts="{{[['tap',[['openSharePopup']]]]}}" class="follow iconfont icon-share data-v-2620f08c" bindtap="__e"></text><text data-event-opts="{{[['tap',[['editCollection']]]]}}" class="{{['share','iconfont','data-v-2620f08c',whetherCollection==1?'icon-likefill color-base-text':'icon-guanzhu']}}" bindtap="__e"></text></view></view><view class="goods-module-wrap info data-v-2620f08c"><text class="sku-name-wrap data-v-2620f08c">{{goodsSkuDetail.goods_name}}</text><block wx:if="{{goodsSkuDetail.introduction}}"><text class="introduction data-v-2620f08c" style="{{'color:'+(goodsSkuDetail.config?goodsSkuDetail.config.introduction_color:'')+';'}}">{{''+goodsSkuDetail.introduction+''}}</text></block><block wx:if="{{goodsSkuDetail.label_name}}"><view class="goods-tag-list data-v-2620f08c"><text class="tag-item data-v-2620f08c">{{goodsSkuDetail.label_name}}</text></view></block><view class="logistics-wrap data-v-2620f08c"><block wx:if="{{goodsSkuDetail.stock_show}}"><text class="data-v-2620f08c">{{"库存 "+goodsSkuDetail.stock+" "+goodsSkuDetail.unit}}</text></block><block wx:if="{{goodsSkuDetail.sale_show}}"><text class="data-v-2620f08c">{{"销量 "+goodsSkuDetail.sale_num+" "+goodsSkuDetail.unit}}</text></block></view></view></view></view><view data-event-opts="{{[['tap',[['groupbuy',['$event']]]]]}}" class="item selected-sku-spec data-v-2620f08c" bindtap="__e" slot="entrance" wx:if="{{goodsSkuDetail.sku_spec_format}}"><view class="label data-v-2620f08c">选择</view><view class="box data-v-2620f08c"><block wx:for="{{goodsSkuDetail.sku_spec_format}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text class="data-v-2620f08c">{{item.spec_name+"/"+item.spec_value_name}}</text></block></view><text class="iconfont icon-right data-v-2620f08c"></text></view><view class="goods-detail-tab rule-wrap data-v-2620f08c" slot="articipation" wx:if="{{goodsSkuDetail.rule}}"><view class="detail-tab data-v-2620f08c"><view class="tab-item data-v-2620f08c">活动规则</view></view><view class="content data-v-2620f08c">{{goodsSkuDetail.rule}}</view></view><ns-goods-sku vue-id="{{('1b494c12-4')+','+('1b494c12-2')}}" goods-id="{{goodsSkuDetail.goods_id}}" goods-detail="{{goodsSkuDetail}}" min-buy="{{goodsSkuDetail.buy_num}}" data-ref="goodsSku" data-event-opts="{{[['^refresh',[['refreshGoodsSkuDetail']]]]}}" bind:refresh="__e" class="data-v-2620f08c vue-ref" slot="business" wx:if="{{goodsSkuDetail.goods_id}}" bind:__l="__l"></ns-goods-sku><ns-goods-action vue-id="{{('1b494c12-5')+','+('1b494c12-2')}}" safeArea="{{isIphoneX}}" class="data-v-2620f08c" slot="action" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{goodsSkuDetail.goods_state==1}}"><ns-goods-action-icon vue-id="{{('1b494c12-6')+','+('1b494c12-5')}}" text="首页" icon="icon-shouye1" data-event-opts="{{[['^click',[['goHome']]]]}}" bind:click="__e" class="data-v-2620f08c" bind:__l="__l"></ns-goods-action-icon><ns-goods-action-icon vue-id="{{('1b494c12-7')+','+('1b494c12-5')}}" text="客服" icon="icon-kefu" send-data="{{contactData}}" chatParam="{{chatRoomParams}}" class="data-v-2620f08c" bind:__l="__l"></ns-goods-action-icon><ns-goods-action-icon vue-id="{{('1b494c12-8')+','+('1b494c12-5')}}" text="购物车" cornerMarkBg="{{themeStyle.goods_detail.goods_cart_num_corner}}" icon="icon-gouwuche2" corner-mark="{{cartNumber>0?cartNumber+'':''}}" data-event-opts="{{[['^click',[['goCart']]]]}}" bind:click="__e" class="data-v-2620f08c" bind:__l="__l"></ns-goods-action-icon><block wx:if="{{goodsSkuDetail.goods_stock<goodsSkuDetail.buy_num&&!goodsSkuDetail.sku_spec_format}}"><block class="data-v-2620f08c"><ns-goods-action-button class="goods-action-button active1 data-v-2620f08c" vue-id="{{('1b494c12-9')+','+('1b494c12-5')}}" disabled-text="库存不足" disabled="{{true}}" bind:__l="__l"></ns-goods-action-button></block></block><block wx:else><ns-goods-action-button class="goods-action-button data-v-2620f08c" vue-id="{{('1b494c12-10')+','+('1b494c12-5')}}" backgroundColor="{{themeStyle.goods_detail.goods_btn_color}}" textColor="{{themeStyle.btn_text_color}}" text="立即抢购" data-event-opts="{{[['^click',[['groupbuy']]]]}}" bind:click="__e" bind:__l="__l"></ns-goods-action-button></block></block><block wx:else><ns-goods-action-button class="goods-action-button active2 data-v-2620f08c" vue-id="{{('1b494c12-11')+','+('1b494c12-5')}}" disabled-text="该商品已下架" disabled="{{true}}" bind:__l="__l"></ns-goods-action-button></block></ns-goods-action></goods-detail-view><block wx:if="{{showTop}}"><to-top bind:toTop="__e" vue-id="1b494c12-12" data-event-opts="{{[['^toTop',[['scrollToTopNative']]]]}}" class="data-v-2620f08c" bind:__l="__l"></to-top></block><ns-login vue-id="1b494c12-13" data-ref="login" class="data-v-2620f08c vue-ref" bind:__l="__l"></ns-login><loading-cover vue-id="1b494c12-14" data-ref="loadingCover" class="data-v-2620f08c vue-ref" bind:__l="__l"></loading-cover><privacy-popup vue-id="1b494c12-15" data-ref="privacyPopup" class="data-v-2620f08c vue-ref" bind:__l="__l"></privacy-popup></view>