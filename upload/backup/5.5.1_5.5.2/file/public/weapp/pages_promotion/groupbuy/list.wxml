<page-meta page-style="{{themeColor}}" class="data-v-1446a668"></page-meta><view class="page data-v-1446a668" style="{{'background:'+(bgColor)+';'}}"><block wx:if="{{addonIsExist.groupbuy}}"><mescroll-uni vue-id="2b50ed25-1" size="{{10}}" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:getData="__e" class="data-v-1446a668 vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-1446a668"><view class="ns-adv data-v-1446a668"><ns-adv vue-id="{{('2b50ed25-2')+','+('2b50ed25-1')}}" keyword="NS_GROUPBUY" class="data-v-1446a668" bind:__l="__l"></ns-adv></view><block wx:if="{{$root.g0}}"><view class="goods-list single-column data-v-1446a668"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item margin-bottom data-v-1446a668"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index]]]]]]]}}" class="goods-img data-v-1446a668" bindtap="__e"><image src="{{item.m0}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e" class="data-v-1446a668"></image><block wx:if="{{item.m1!=''}}"><view class="color-base-bg goods-tag data-v-1446a668">{{item.m2}}</view></block></view><view class="info-wrap data-v-1446a668"><view class="name-wrap data-v-1446a668"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index]]]]]]]}}" class="goods-name data-v-1446a668" bindtap="__e">{{item.$orig.goods_name}}</view><view class="introduction font-size-activity-tag color-tip data-v-1446a668">{{"库存"+item.$orig.goods_stock+"件"}}</view></view><view class="lineheight-clear data-v-1446a668">原价：<text class="delete-price font-size-activity-tag color-tip price-font data-v-1446a668"><text class="unit data-v-1446a668">{{item.m3}}</text>{{''+item.$orig.price+''}}</text></view><view class="pro-info data-v-1446a668"><view class="discount-price data-v-1446a668"><text class="unit price-style small data-v-1446a668">{{item.m4}}</text><text class="price price-style large data-v-1446a668">{{item.g1[0]}}</text><text class="unit price-style small data-v-1446a668">{{"."+item.g2[1]}}</text></view><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index]]]]]]]}}" bindtap="__e" class="data-v-1446a668"><button class="mini data-v-1446a668" type="primary" size="mini">去团购</button></view></view></view></view></block></view></block><block wx:if="{{$root.g3==0}}"><view class="data-v-1446a668"><ns-empty vue-id="{{('2b50ed25-3')+','+('2b50ed25-1')}}" text="暂无团购" textColor="#fff" isIndex="{{false}}" class="data-v-1446a668" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni></block><hover-nav vue-id="2b50ed25-4" class="data-v-1446a668" bind:__l="__l"></hover-nav><loading-cover vue-id="2b50ed25-5" data-ref="loadingCover" class="data-v-1446a668 vue-ref" bind:__l="__l"></loading-cover><privacy-popup vue-id="2b50ed25-6" data-ref="privacyPopup" class="data-v-1446a668 vue-ref" bind:__l="__l"></privacy-popup></view>