<page-meta page-style="{{themeColor}}"></page-meta><view class="goods-detail"><block wx:if="{{noteType=='goods_item'}}"><view class="goods-item"><image class="item-img" src="{{$root.g0}}" mode="aspectFit" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e"></image><block wx:if="{{goodsItemInfo.is_show_release_time==1}}"><text class="item-title">{{goodsItemInfo.note_title}}</text></block><block wx:if="{{$root.g1}}"><view class="item-lightspot"><block wx:for="{{goodsItemInfo.goods_highlights}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text class="color-base-bg">{{item}}</text></block></view></block><block wx:if="{{goodsItemInfo.is_show_release_time==1}}"><text class="item-time">{{$root.g2}}</text></block><view class="item-content"><ns-mp-html vue-id="7c1ed042-1" content="{{goodsItemInfo.note_content}}" bind:__l="__l"></ns-mp-html></view><view class="rest-info"><block wx:if="{{goodsItemInfo.is_show_read_num==1}}"><text>阅读<text>{{goodsItemInfo.initial_read_num+goodsItemInfo.read_num}}</text></text></block><block wx:if="{{goodsItemInfo.is_show_dianzan_num==1}}"><text data-event-opts="{{[['tap',[['giveLike',['$event']]]]]}}" bindtap="__e"><block wx:if="{{giveLikeIdent}}"><text class="iconfont icon-likefill color-base-text"></text></block><block wx:if="{{!giveLikeIdent}}"><text class="iconfont icon-gz"></text></block><text>{{goodsItemInfo.initial_dianzan_num+goodsItemInfo.dianzan_num}}</text></text></block></view><view class="item-action"><view class="action-left"><block wx:if="{{!giveLikeIdent}}"><text data-event-opts="{{[['tap',[['giveLike',['$event']]]]]}}" class="iconfont icon-dianzan" bindtap="__e"></text></block><block wx:if="{{giveLikeIdent}}"><text data-event-opts="{{[['tap',[['giveLike',['$event']]]]]}}" class="iconfont icon-dianzan1 active color-base-text" bindtap="__e"></text></block><button class="iconfont icon-share" type="primary" open-type="share"></button></view><block wx:if="{{$root.g3}}"><button data-event-opts="{{[['tap',[['redirectToGoods',['$0'],['goodsItemInfo']]]]]}}" class="color-base-bg action-right" bindtap="__e">购买</button></block></view></view></block><block wx:else><block wx:if="{{noteType=='shop_said'}}"><view class="shop-said"><text class="said-title">{{shopSaidInfo.note_title}}</text><block wx:if="{{shopSaidInfo.is_show_release_time==1}}"><text class="said-time">{{$root.g4}}</text></block><view class="said-content"><ns-mp-html vue-id="7c1ed042-2" content="{{shopSaidInfo.note_content}}" bind:__l="__l"></ns-mp-html></view><view class="said-goods"><block wx:for="{{$root.l0}}" wx:for-item="goodsItme" wx:for-index="goodsIndex" wx:key="goodsIndex"><block wx:if="{{shopSaidInfo.goods_list}}"><view data-event-opts="{{[['tap',[['redirectToGoods',['$0','shop_said'],[[['shopSaidInfo.goods_list','',goodsIndex,'goods_id']]]]]]]}}" class="commodity-item" bindtap="__e"><image class="commodity-img" src="{{goodsItme.g5}}" mode="aspectFit"></image><view class="commodity-content"><text class="commodity-name">{{goodsItme.$orig.goods_name}}</text><text class="commodity-price color-base-text">{{goodsItme.$orig.price}}</text></view></view></block></block></view><view class="rest-info"><block wx:if="{{shopSaidInfo.is_show_read_num==1}}"><text>阅读<text>{{shopSaidInfo.initial_read_num+shopSaidInfo.read_num}}</text></text></block><block wx:if="{{shopSaidInfo.is_show_dianzan_num==1}}"><text data-event-opts="{{[['tap',[['giveLike',['$event']]]]]}}" bindtap="__e"><block wx:if="{{giveLikeIdent}}"><text class="iconfont icon-likefill color-base-text"></text></block><block wx:if="{{!giveLikeIdent}}"><text class="iconfont icon-gz"></text></block><text>{{shopSaidInfo.initial_dianzan_num+shopSaidInfo.dianzan_num}}</text></text></block></view><view class="said-action"><block wx:if="{{!giveLikeIdent}}"><text data-event-opts="{{[['tap',[['giveLike',['$event']]]]]}}" class="iconfont icon-dianzan" bindtap="__e"></text></block><block wx:if="{{giveLikeIdent}}"><text data-event-opts="{{[['tap',[['giveLike',['$event']]]]]}}" class="iconfont icon-dianzan1 color-base-text active" bindtap="__e"></text></block><button class="iconfont icon-share" type="primary" open-type="share"></button></view></view></block></block><loading-cover class="vue-ref" vue-id="7c1ed042-3" data-ref="loadingCover" bind:__l="__l"></loading-cover><hover-nav vue-id="7c1ed042-4" bind:__l="__l"></hover-nav><ns-login class="vue-ref" vue-id="7c1ed042-5" data-ref="login" bind:__l="__l"></ns-login><privacy-popup class="vue-ref" vue-id="7c1ed042-6" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>