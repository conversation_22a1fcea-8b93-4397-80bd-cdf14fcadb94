<page-meta page-style="{{themeColor}}"></page-meta><view><view class="notes-nav"><scroll-view class="notes-list" scroll-x="true"><block wx:for="{{groupInfo.groupList}}" wx:for-item="gruopItem" wx:for-index="gruopIndex" wx:key="gruopIndex"><text data-event-opts="{{[['tap',[['tabCut',['$0',gruopIndex],[[['groupInfo.groupList','',gruopIndex,'group_id']]]]]]]}}" class="{{['notes-item',gruopIndex==groupInfo.currIdent?'color-base-text active color-base-border':'']}}" bindtap="__e">{{''+gruopItem.group_name+''}}</text></block></scroll-view></view><block wx:if="{{groupInfo.currId!=0}}"><mescroll-uni class="vue-ref" vue-id="5b19bd55-1" top="94" data-ref="mescroll" data-event-opts="{{[['^getData',[['getNotesList']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['default','list']}}"><view slot="list"><view class="notes-content"><block wx:for="{{$root.l1}}" wx:for-item="listItem" wx:for-index="listIndex" wx:key="listIndex"><view data-event-opts="{{[['tap',[['noteDetail',['$0'],[[['noteListInfo','',listIndex,'note_id']]]]]]]}}" class="note-item" bindtap="__e"><text class="note-title">{{listItem.$orig.note_title}}</text><block wx:if="{{listItem.$orig.goods_highlights}}"><view class="notes-highlights-list"><block wx:for="{{listItem.$orig.label}}" wx:for-item="labelItem" wx:for-index="labelIndex" wx:key="labelIndex"><text class="color-base-bg">{{labelItem}}</text></block></view></block><view class="note-desc"><text class="color-base-text">{{"#"+(listItem.$orig.note_type=='goods_item'?'单品介绍':'掌柜说')+"#"}}</text>{{''+listItem.$orig.note_abstract+''}}</view><view class="{{['notes-img-wrap',(listItem.$orig.cover_type==1)?'notes-img-wrap-list':'']}}"><block wx:if="{{listItem.$orig.cover_type==0}}"><image class="notes-item-image" src="{{listItem.g0}}" mode="aspectFill"></image></block><block wx:else><block wx:for="{{listItem.l0}}" wx:for-item="imgItem" wx:for-index="imgIndex" wx:key="imgIndex"><image class="notes-item-image-li" src="{{imgItem.g1}}" mode="aspectFit"></image></block></block></view><view class="rest-info"><block wx:if="{{listItem.$orig.is_show_release_time==1}}"><view class="time">{{listItem.g2}}</view></block><view class="read-info"><block wx:if="{{listItem.$orig.is_show_read_num==1}}"><text>阅读<text>{{listItem.$orig.initial_read_num+listItem.$orig.read_num}}</text></text></block><block wx:if="{{listItem.$orig.is_show_dianzan_num==1}}"><text data-event-opts="{{[['tap',[['giveLike',['$0',listIndex],[[['noteListInfo','',listIndex,'note_id']]]]]]]}}" catchtap="__e"><block wx:if="{{listItem.$orig.is_dianzan==1}}"><text class="iconfont icon-likefill color-base-text"></text></block><block wx:else><text class="iconfont icon-gz"></text></block><text>{{listItem.$orig.initial_dianzan_num+listItem.$orig.dianzan_num}}</text></text></block></view></view></view></block></view></view><loading-cover class="vue-ref" vue-id="{{('5b19bd55-2')+','+('5b19bd55-1')}}" data-ref="loadingCover" bind:__l="__l"></loading-cover></mescroll-uni></block><block wx:if="{{$root.g3}}"><view class="empty-box"><ns-empty vue-id="5b19bd55-3" isIndex="{{false}}" text="暂无店铺笔记" bind:__l="__l"></ns-empty></view></block><view class="page-bottom"><diy-bottom-nav vue-id="5b19bd55-4" bind:__l="__l"></diy-bottom-nav></view><ns-login class="vue-ref" vue-id="5b19bd55-5" data-ref="login" bind:__l="__l"></ns-login><privacy-popup class="vue-ref" vue-id="5b19bd55-6" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>