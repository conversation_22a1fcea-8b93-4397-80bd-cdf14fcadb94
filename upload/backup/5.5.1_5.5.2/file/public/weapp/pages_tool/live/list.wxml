<page-meta page-style="{{themeColor}}"></page-meta><view class="live-content"><mescroll-uni class="vue-ref" bind:getData="__e" vue-id="3275bf53-1" data-ref="mescroll" data-event-opts="{{[['^getData',[['getListData']]]]}}" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g0}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['entryRoom',['$0'],[[['liveList','',index,'roomid']]]]]]]}}" class="live-wrap" bindtap="__e"><view class="banner-wrap"><image src="{{item.$orig.banner!=''?item.g1:item.g2}}" mode="widthFix"></image><view class="shade"></view><view class="wrap"><view class="room-name"><text class="{{['status-name','font-size-base',(item.$orig.live_status=='101')?'color-base-bg':'']}}"><block wx:if="{{item.$orig.live_status=='101'}}"><text class="iconfont icon-zhibozhong font-size-sub"></text></block><block wx:else><text class="iconfont icon-zhibojieshu font-size-sub"></text></block>{{''+item.$orig.status_name+''}}</text>{{''+item.$orig.name+''}}</view></view></view><view class="room-info"><image class="anchor-img" src="{{item.$orig.anchor_img!=''?item.g3:item.g4.head}}"></image><text class="anchor-name">{{"主播："+item.$orig.anchor_name}}</text><text class="separate">|</text><text class="goods-text">{{"直播商品："+item.g5}}</text></view><view class="time"><text class="color-tip">{{''+item.g6+''}}<text class="separate">-</text>{{''+item.g7+''}}</text></view></view></block></block></block><block wx:else><block><ns-empty vue-id="{{('3275bf53-2')+','+('3275bf53-1')}}" text="暂无直播记录" isIndex="{{isIndex}}" bind:__l="__l"></ns-empty></block></block></view></mescroll-uni><loading-cover class="vue-ref" vue-id="3275bf53-3" data-ref="loadingCover" bind:__l="__l"></loading-cover><privacy-popup class="vue-ref" vue-id="3275bf53-4" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>