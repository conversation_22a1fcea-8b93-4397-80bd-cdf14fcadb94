require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/login/aggrement"],{4971:function(n,t,e){"use strict";e.r(t);var a=e("aa0c"),r=e("9c2f");for(var o in r)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return r[n]}))}(o);e("b458");var u=e("828b"),i=Object(u["a"])(r["default"],a["b"],a["c"],!1,null,"31398624",null,!1,a["a"],void 0);t["default"]=i.exports},"63a5":function(n,t,e){},"9a15":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{aggrement:null,type:"SERVICE"}},onLoad:function(n){this.type=n.type||"",this.getaggrementInfo()},methods:{getaggrementInfo:function(){var n=this;this.$api.sendRequest({url:"/api/register/aggrement",data:{type:this.type},success:function(t){t.code>=0&&(n.aggrement=t.data),n.$refs.loadingCover&&n.$refs.loadingCover.hide()}})}}}},"9c2f":function(n,t,e){"use strict";e.r(t);var a=e("9a15"),r=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(o);t["default"]=r.a},aa0c:function(n,t,e){"use strict";e.d(t,"b",(function(){return r})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){return a}));var a={nsMpHtml:function(){return e.e("components/ns-mp-html/ns-mp-html").then(e.bind(null,"d108"))},loadingCover:function(){return e.e("components/loading-cover/loading-cover").then(e.bind(null,"c003"))}},r=function(){var n=this.$createElement;this._self._c},o=[]},b458:function(n,t,e){"use strict";var a=e("63a5"),r=e.n(a);r.a},b6d5:function(n,t,e){"use strict";(function(n,t){var a=e("47a9");e("d381");a(e("3240"));var r=a(e("4971"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(r.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["b6d5","common/runtime","common/vendor"]]]);