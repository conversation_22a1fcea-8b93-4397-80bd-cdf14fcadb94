require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/login/find"],{"551e":function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("d381");n(a("3240"));var o=n(a("86c04"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"5e44":function(e,t,a){"use strict";var n=a("c41f"),o=a.n(n);o.a},"5ef7":function(e,t,a){"use strict";(function(e){var n=a("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("7eb4")),r=n(a("ee10")),c=n(a("fe8d")),i={components:{mypOne:function(){a.e("pages_tool/components/myp-one/myp-one").then(function(){return resolve(a("b2c6"))}.bind(null,a)).catch(a.oe)}},data:function(){return{findMode:"mobile",codeText:"重新获取",seconds:120,timer:null,formData:{mobile:"",password:"",rePassword:"",dynacode:"",captcha:""},stepShow:0,isSend:!1,captcha:{id:"",img:""},registerConfig:{}}},onLoad:function(){this.getCaptcha()},onShow:function(){this.getRegisterConfig()},methods:{input:function(e){4==e.length&&(this.formData.dynacode=e,this.stepShow+=1)},navigateBack:function(){this.stepShow>0?this.stepShow-=1:this.$util.redirectTo("/pages_tool/login/index","","redirectTo")},nextStep:function(){var e=this;return(0,r.default)(o.default.mark((function t(){var a,n,r;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=[{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"},{name:"captcha",checkType:"required",errorMsg:e.$lang("captchaPlaceholder")}],n=c.default.check(e.formData,a),!n){t.next=12;break}return e.findMode="mobile",t.next=6,e.$api.sendRequest({url:"/api/member/checkmobile",data:{mobile:e.formData.mobile},async:!1});case 6:if(r=t.sent,0!=r.code){t.next=10;break}return e.$util.showToast({title:"该手机号未注册"}),t.abrupt("return",!1);case 10:t.next=14;break;case 12:return e.$util.showToast({title:c.default.error}),t.abrupt("return",!1);case 14:e.sendDynaCode();case 15:case"end":return t.stop()}}),t)})))()},vertify:function(){var e=this.registerConfig,t=[{name:"password",checkType:"required",errorMsg:"请输入密码"}];if(e.pwd_len>0&&t.push({name:"password",checkType:"lengthMin",checkRule:e.pwd_len,errorMsg:"密码长度不能小于"+e.pwd_len+"位"}),""!=e.pwd_complexity){var a="密码需包含",n="";-1!=e.pwd_complexity.indexOf("number")&&(n+="(?=.*?[0-9])",a+="数字"),-1!=e.pwd_complexity.indexOf("letter")&&(n+="(?=.*?[a-z])",a+="、小写字母"),-1!=e.pwd_complexity.indexOf("upper_case")&&(n+="(?=.*?[A-Z])",a+="、大写字母"),-1!=e.pwd_complexity.indexOf("symbol")&&(n+="(?=.*?[#?!@$%^&*-])",a+="、特殊字符"),t.push({name:"password",checkType:"reg",checkRule:n,errorMsg:a})}var o=c.default.check(this.formData,t);return o?this.formData.password==this.formData.rePassword||(this.$util.showToast({title:"两次密码不一致"}),!1):(this.$util.showToast({title:c.default.error}),!1)},getCaptcha:function(){var e=this;this.$api.sendRequest({url:"/api/captcha/captcha",data:{captcha_id:this.captcha.id},success:function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))}})},sendDynaCode:function(){var t=this;return(0,r.default)(o.default.mark((function a(){var n;return o.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(0!=t.formData.captcha.length){a.next=3;break}return t.$util.showToast({title:t.$lang("captchaPlaceholder")}),a.abrupt("return");case 3:if(!t.isSend){a.next=5;break}return a.abrupt("return");case 5:t.isSend=!0,n={captcha_id:t.captcha.id,captcha_code:t.formData.captcha},n[t.findMode]=t.formData.mobile,"/api/findpassword/mobilecode",t.$api.sendRequest({url:"/api/findpassword/mobilecode",data:n,success:function(a){var n=a.data;n.key?(120==t.seconds&&null==t.timer&&(t.timer=setInterval((function(){t.seconds--,t.codeText="重新获取("+t.seconds+"s)"}),1e3)),e.setStorageSync("forgot_password_token",n.key),t.stepShow+=1):(t.$util.showToast({title:a.message}),t.isSend=!1,t.getCaptcha())},fail:function(e){t.isSend=!1,t.getCaptcha()}});case 10:case"end":return a.stop()}}),a)})))()},save:function(){var t=this;if(this.vertify()){var a={code:this.formData.dynacode,key:e.getStorageSync("forgot_password_token"),password:this.formData.password};a[this.findMode]=this.formData.mobile,"/api/findpassword/mobile",this.$api.sendRequest({url:"/api/findpassword/mobile",data:a,success:function(a){t.$util.showToast({title:a.message}),0==a.code?setTimeout((function(){e.removeStorage({key:"forgot_password_token"}),t.$util.redirectTo("/pages_tool/login/index",{},"redirectTo")}),1e3):t.stepShow-=1}})}},getRegisterConfig:function(){var e=this;this.$api.sendRequest({url:"/api/register/config",success:function(t){t.code>=0&&(e.registerConfig=t.data.value)}})}},watch:{seconds:function(e){0==e&&(this.seconds=120,this.codeText="重新获取",this.isSend=!1,clearInterval(this.timer))}}};t.default=i}).call(this,a("df3c")["default"])},"86c04":function(e,t,a){"use strict";a.r(t);var n=a("ed1c"),o=a("ccca");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("5e44");var c=a("828b"),i=Object(c["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=i.exports},c41f:function(e,t,a){},ccca:function(e,t,a){"use strict";a.r(t);var n=a("5ef7"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},ed1c:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=(e._self._c,0==e.stepShow?e.$lang("accountPlaceholder"):null),n=0==e.stepShow?e.$lang("captchaPlaceholder"):null,o=0==e.stepShow?e.$lang("next"):null,r=2==e.stepShow?e.$lang("passwordPlaceholder"):null,c=2==e.stepShow?e.$lang("rePasswordPlaceholder"):null,i=2==e.stepShow?e.$lang("save"):null;e.$mp.data=Object.assign({},{$root:{m0:a,m1:n,m2:o,m3:r,m4:c,m5:i}})},o=[]}},[["551e","common/runtime","common/vendor"]]]);