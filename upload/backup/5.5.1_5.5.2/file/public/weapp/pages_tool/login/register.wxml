<page-meta page-style="{{themeColor}}" class="data-v-4c6dba61"></page-meta><scroll-view class="container data-v-4c6dba61" scroll-y="true"><view class="header-wrap data-v-4c6dba61"><view class="title data-v-4c6dba61">注册</view><view class="regisiter-agreement data-v-4c6dba61"><text class="color-tip data-v-4c6dba61">已有账号,</text><text data-event-opts="{{[['tap',[['toLogin',['$event']]]]]}}" class="color-base-text data-v-4c6dba61" bindtap="__e">立即登录</text></view></view><view class="body-wrap data-v-4c6dba61"><view class="form-wrap data-v-4c6dba61"><view hidden="{{!(registerMode=='mobile')}}" class="input-wrap data-v-4c6dba61"><view class="content data-v-4c6dba61"><view class="area-code data-v-4c6dba61">+86</view><input class="input data-v-4c6dba61" type="number" placeholder="仅限中国大陆手机号注册" placeholder-class="input-placeholder" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['formData']]]]]}}" value="{{formData.mobile}}" bindinput="__e"/></view></view><view hidden="{{!(registerMode=='account')}}" class="input-wrap data-v-4c6dba61"><view class="content data-v-4c6dba61"><input class="input data-v-4c6dba61" type="text" placeholder="请输入账号" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['__set_model',['$0','account','$event',[]],['formData']]]]]}}" value="{{formData.account}}" bindinput="__e"/></view></view><view hidden="{{!(registerMode=='account')}}" class="input-wrap data-v-4c6dba61"><view class="content data-v-4c6dba61"><input class="input data-v-4c6dba61" type="password" placeholder="请输入密码" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" value="{{formData.password}}" bindinput="__e"/></view></view><view hidden="{{!(registerMode=='account')}}" class="input-wrap data-v-4c6dba61"><view class="content data-v-4c6dba61"><input class="input data-v-4c6dba61" type="password" placeholder="请确认密码" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['__set_model',['$0','rePassword','$event',[]],['formData']]]]]}}" value="{{formData.rePassword}}" bindinput="__e"/></view></view><block wx:if="{{isOpenCaptcha}}"><view class="input-wrap data-v-4c6dba61"><view class="content data-v-4c6dba61"><input class="input data-v-4c6dba61" type="text" placeholder="请输入验证码" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['__set_model',['$0','vercode','$event',[]],['formData']]]]]}}" value="{{formData.vercode}}" bindinput="__e"/><image class="captcha data-v-4c6dba61" src="{{captcha.img}}" data-event-opts="{{[['tap',[['getCaptcha',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view hidden="{{!(registerMode=='mobile')}}" class="input-wrap data-v-4c6dba61"><view class="content data-v-4c6dba61"><input class="input data-v-4c6dba61" type="text" placeholder="请输入动态码" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['__set_model',['$0','dynacode','$event',[]],['formData']]]]]}}" value="{{formData.dynacode}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['sendMobileCode',['$event']]]]]}}" class="{{['dynacode','data-v-4c6dba61',dynacodeData.seconds==120?'color-base-text':'color-tip']}}" bindtap="__e">{{dynacodeData.codeText}}</view></view></view></view><view class="login-mode-box data-v-4c6dba61"><text data-event-opts="{{[['tap',[['switchRegisterMode',['$event']]]]]}}" hidden="{{!($root.g0)}}" bindtap="__e" class="data-v-4c6dba61">使用用户名注册</text><text data-event-opts="{{[['tap',[['switchRegisterMode',['$event']]]]]}}" hidden="{{!($root.g1)}}" bindtap="__e" class="data-v-4c6dba61">使用手机号注册</text></view><view class="btn_view data-v-4c6dba61"><button class="login-btn color-base-border color-base-bg data-v-4c6dba61" type="primary" data-event-opts="{{[['tap',[['register',['$event']]]]]}}" bindtap="__e">注册</button></view><block wx:if="{{registerConfig.agreement_show}}"><view class="regisiter-agreement data-v-4c6dba61"><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['iconfont','is-agree','data-v-4c6dba61',isAgree?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}" bindtap="__e"></text><text class="tips data-v-4c6dba61">请阅读并同意</text><text data-event-opts="{{[['tap',[['toAggrement',['PRIVACY']]]]]}}" class="color-base-text data-v-4c6dba61" bindtap="__e">《隐私协议》</text><text class="tips data-v-4c6dba61">和</text><text data-event-opts="{{[['tap',[['toAggrement',['SERVICE']]]]]}}" class="color-base-text data-v-4c6dba61" bindtap="__e">《用户协议》</text></view></block></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" bindtouchmove="__e" class="data-v-4c6dba61"><uni-popup vue-id="6639d31f-1" type="center" maskClick="{{false}}" data-ref="registerPopup" class="data-v-4c6dba61 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="conten-box data-v-4c6dba61"><view class="close data-v-4c6dba61"><text data-event-opts="{{[['tap',[['toClose',['$event']]]]]}}" class="iconfont icon-close data-v-4c6dba61" bindtap="__e"></text></view><view class="title data-v-4c6dba61">{{agreement.title}}</view><view class="con data-v-4c6dba61"><scroll-view class="con data-v-4c6dba61" scroll-y="true"><ns-mp-html vue-id="{{('6639d31f-2')+','+('6639d31f-1')}}" content="{{agreement.content}}" class="data-v-4c6dba61" bind:__l="__l"></ns-mp-html></scroll-view></view></view></uni-popup></view><loading-cover vue-id="6639d31f-3" data-ref="loadingCover" class="data-v-4c6dba61 vue-ref" bind:__l="__l"></loading-cover><register-reward vue-id="6639d31f-4" data-ref="registerReward" class="data-v-4c6dba61 vue-ref" bind:__l="__l"></register-reward></scroll-view>