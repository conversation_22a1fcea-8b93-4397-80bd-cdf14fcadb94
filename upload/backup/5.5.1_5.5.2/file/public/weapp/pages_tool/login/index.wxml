<page-meta page-style="{{themeColor}}"></page-meta><view class="auth-index" style="{{(warpStyle)}}"><view class="website-logo"><block wx:if="{{siteInfo.logo}}"><image class="logo" src="{{$root.g0}}" mode="aspectFit"></image></block><block wx:else><view class="logo"></view></block></view><view class="login-desc">{{registerConfig.wap_desc}}</view><view class="login-area"><block wx:if="{{$root.m0}}"><view data-event-opts="{{[['tap',[['quickLogin',['$event']]]]]}}" class="btn quick-login" bindtap="__e">快捷登录/注册</view></block><block wx:if="{{$root.g1!=-1}}"><view data-event-opts="{{[['tap',[['toLogin',['mobile']]]]]}}" class="{{['btn',isQuickLogin?'':'quick-login']}}" bindtap="__e">手机号登录</view></block><block wx:if="{{$root.g2}}"><view data-event-opts="{{[['tap',[['toLogin',['account']]]]]}}" class="{{['btn',isQuickLogin?'':'quick-login']}}" bindtap="__e">账号密码登录</view></block><block wx:if="{{registerConfig.agreement_show}}"><view class="agreement"><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['iconfont','agree',isAgree?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}" bindtap="__e"></text><view class="tips-text"><text class="tips">请阅读并同意</text><text data-event-opts="{{[['tap',[['toAggrement',['PRIVACY']]]]]}}" class="agreement-name color-base-text" bindtap="__e">《隐私协议》</text><text class="tips">和</text><text data-event-opts="{{[['tap',[['toAggrement',['SERVICE']]]]]}}" class="agreement-name color-base-text" bindtap="__e">《用户协议》</text></view></view></block><block wx:if="{{$root.g3}}"><view class="footer"><view class="text">其他方式登录</view><view data-event-opts="{{[['tap',[['toLogin',['account']]]]]}}" class="mine icondiy icon-system-wodi2" bindtap="__e"></view><view class="mode-name">账号密码登录</view></view></block></view><ns-login class="vue-ref" vue-id="6722ade8-1" data-ref="login" bind:__l="__l"></ns-login></view>