require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/login/login"],{"34e8":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){return a}));var a={loadingCover:function(){return i.e("components/loading-cover/loading-cover").then(i.bind(null,"c003"))},registerReward:function(){return i.e("components/register-reward/register-reward").then(i.bind(null,"349c"))}},o=function(){var e=this,t=e.$createElement,i=(e._self._c,"mobile"==e.loginMode&&-1!=e.registerConfig.login.indexOf("username")),a="account"==e.loginMode&&-1!=e.registerConfig.login.indexOf("mobile");e._isMounted||(e.e0=function(t){e.isAgree=!e.isAgree}),e.$mp.data=Object.assign({},{$root:{g0:i,g1:a}})},n=[]},5209:function(e,t,i){},"565e":function(e,t,i){},"5dea":function(e,t,i){"use strict";i.r(t);var a=i("34e8"),o=i("b2f8");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(n);i("e0ff"),i("6473");var r=i("828b"),c=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"11d5e82a",null,!1,a["a"],void 0);t["default"]=c.exports},6473:function(e,t,i){"use strict";var a=i("565e"),o=i.n(a);o.a},"750b":function(e,t,i){"use strict";(function(e,t){var a=i("47a9");i("d381");a(i("3240"));var o=a(i("5dea"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},b2f8:function(e,t,i){"use strict";i.r(t);var a=i("eebe"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},e0ff:function(e,t,i){"use strict";var a=i("5209"),o=i.n(a);o.a},eebe:function(e,t,i){"use strict";(function(e){var a=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("fe8d")),n={data:function(){return{isAgree:!1,loginMode:"",formData:{mobile:"",account:"",password:"",vercode:"",dynacode:"",key:""},captcha:{id:"",img:""},isSub:!1,back:"",redirect:"redirectTo",dynacodeData:{seconds:120,timer:null,codeText:"获取动态码",isSend:!1},registerConfig:{register:"",login:""},captchaConfig:1,authInfo:null}},components:{registerReward:function(){i.e("components/register-reward/register-reward").then(function(){return resolve(i("349c"))}.bind(null,i)).catch(i.oe)}},onLoad:function(t){t.loginMode&&(this.loginMode=t.loginMode),t.back&&(this.back=t.back),this.getRegisterConfig(),this.getCaptchaConfig(),this.authInfo=e.getStorageSync("authInfo")},onShow:function(){},onReady:function(){this.$refs.loadingCover&&this.$refs.loadingCover.hide()},methods:{toAggrement:function(e){this.$util.redirectTo("/pages_tool/login/aggrement",{type:e})},getCaptchaConfig:function(){var e=this;this.$api.sendRequest({url:"/api/config/getCaptchaConfig",success:function(t){t.code>=0&&(e.captchaConfig=t.data.shop_reception_login,1==e.captchaConfig&&e.getCaptcha())}})},getRegisterConfig:function(){var e=this;this.$api.sendRequest({url:"/api/register/config",success:function(t){t.code>=0&&(e.registerConfig=t.data.value,e.loginMode||(-1!=e.registerConfig.login.indexOf("mobile")?e.loginMode="mobile":e.loginMode="account"))}})},switchLoginMode:function(){this.loginMode="mobile"==this.loginMode?"account":"mobile"},getCaptcha:function(){var e=this;0!=this.captchaConfig&&this.$api.sendRequest({url:"/api/captcha/captcha",data:{captcha_id:this.captcha.id},success:function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))}})},toRegister:function(){this.back?this.$util.redirectTo("/pages_tool/login/register",{back:encodeURIComponent(this.back)}):this.$util.redirectTo("/pages_tool/login/register")},forgetPassword:function(){this.back?this.$util.redirectTo("/pages_tool/login/find",{back:encodeURIComponent(this.back)}):this.$util.redirectTo("/pages_tool/login/find")},login:function(){var t=this;if("account"==this.loginMode){var i="/api/login/login";a={username:this.formData.account,password:this.formData.password}}else{i="/api/login/mobile";var a={mobile:this.formData.mobile,key:this.formData.key,code:this.formData.dynacode}}if(""!=this.captcha.id&&(a.captcha_id=this.captcha.id,a.captcha_code=this.formData.vercode),this.authInfo&&Object.assign(a,this.authInfo),e.getStorageSync("source_member")&&(a.source_member=e.getStorageSync("source_member")),this.verify(a)){if(this.isSub)return;this.isSub=!0,this.$api.sendRequest({url:i,data:a,success:function(e){if(e.code>=0){var i=e.data.can_receive_registergift;t.$store.commit("setToken",e.data.token),t.$store.dispatch("getCartNumber"),t.getMemberInfo((function(){if(1==i){t.$util.showToast({title:"登录成功"});var e=t.back?t.back:"/pages/member/index";t.$refs.registerReward&&t.$refs.registerReward.open(e)}else t.$util.loginComplete("/pages/member/index/index",{},t.redirect)}))}else t.isSub=!1,t.getCaptcha(),t.$util.showToast({title:e.message})},fail:function(e){t.isSub=!1,t.getCaptcha()}})}},verify:function(e){if(!this.registerConfig.agreement_show||this.isAgree){var t=[];"mobile"==this.loginMode&&(t=[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}],1==this.captchaConfig&&""!=this.captcha.id&&t.push({name:"captcha_code",checkType:"required",errorMsg:this.$lang("captchaPlaceholder")}),t.push({name:"code",checkType:"required",errorMsg:this.$lang("dynacodePlaceholder")})),"account"==this.loginMode&&(t=[{name:"username",checkType:"required",errorMsg:this.$lang("accountPlaceholder")},{name:"password",checkType:"required",errorMsg:this.$lang("passwordPlaceholder")}],1==this.captchaConfig&&""!=this.captcha.id&&t.push({name:"captcha_code",checkType:"required",errorMsg:this.$lang("captchaPlaceholder")}));var i=o.default.check(e,t);return!!i||(this.$util.showToast({title:o.default.error}),!1)}this.$util.showToast({title:"请先阅读并同意协议"})},mobileAuthLogin:function(t){var i=this;if("getPhoneNumber:ok"==t.detail.errMsg){var a={iv:t.detail.iv,encryptedData:t.detail.encryptedData};if(Object.keys(this.authInfo).length&&(Object.assign(a,this.authInfo),this.authInfo.nickName&&(a.nickname=this.authInfo.nickName),this.authInfo.avatarUrl&&(a.headimg=this.authInfo.avatarUrl)),e.getStorageSync("source_member")&&(a.source_member=e.getStorageSync("source_member")),this.isSub)return;this.isSub=!0,this.$api.sendRequest({url:"/api/tripartite/mobileauth",data:a,success:function(e){if(e.code>=0){var t=e.data.can_receive_registergift;i.$store.commit("setToken",e.data.token),i.$store.dispatch("getCartNumber"),i.getMemberInfo((function(){if(1==t){var e=i.back?i.back:"/pages/member/index";i.$refs.registerReward&&i.$refs.registerReward.open(e)}else""!=i.back?i.$util.redirectTo(decodeURIComponent(i.back),{},i.redirect):i.$util.redirectTo("/pages/member/index",{},i.redirect)}))}else i.isSub=!1,i.$util.showToast({title:e.message})},fail:function(e){i.isSub=!1,i.$util.showToast({title:"request:fail"})}})}},sendMobileCode:function(){var e=this;if(120==this.dynacodeData.seconds&&!this.dynacodeData.isSend){var t={mobile:this.formData.mobile,captcha_id:this.captcha.id,captcha_code:this.formData.vercode},i=[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}];1==this.captchaConfig&&i.push({name:"captcha_code",checkType:"required",errorMsg:"请输入验证码"});var a=o.default.check(t,i);a?(this.dynacodeData.isSend=!0,this.dynacodeData.timer=setInterval((function(){e.dynacodeData.seconds--,e.dynacodeData.codeText=e.dynacodeData.seconds+"s后可重新获取"}),1e3),this.$api.sendRequest({url:"/api/login/mobileCode",data:t,success:function(t){t.code>=0?e.formData.key=t.data.key:(e.refreshDynacodeData(),e.$util.showToast({title:t.message}))},fail:function(){e.$util.showToast({title:"request:fail"}),e.refreshDynacodeData()}})):this.$util.showToast({title:o.default.error})}},refreshDynacodeData:function(){this.getCaptcha(),clearInterval(this.dynacodeData.timer),this.dynacodeData={seconds:120,timer:null,codeText:"获取动态码",isSend:!1}},getMemberInfo:function(e){var t=this;this.$api.sendRequest({url:"/api/member/info",success:function(i){i.code>=0&&(t.$store.commit("setMemberInfo",i.data),e&&e())}})}},watch:{"dynacodeData.seconds":{handler:function(e,t){0==e&&this.refreshDynacodeData()},immediate:!0,deep:!0}}};t.default=n}).call(this,i("df3c")["default"])}},[["750b","common/runtime","common/vendor"]]]);