<page-meta page-style="{{themeColor}}"></page-meta><view class="find"><view data-event-opts="{{[['tap',[['navigateBack']]]]}}" class="iconfont icon-close" bindtap="__e"></view><view class="header-wrap"><block wx:if="{{stepShow==0}}"><block><view class="title">请输入手机号</view><view><text class="color-tip">请确认您的账号已绑定此手机号</text></view></block></block><block wx:if="{{stepShow==1}}"><block><view class="title">请输入验证码</view><view><text class="color-tip">{{"已将验证码发送至手机号："+formData.mobile}}</text></view></block></block><block wx:if="{{stepShow==2}}"><block><view class="title">请设置新的密码</view><view><text class="color-tip">建议您的新密码以简单好记为标准</text></view></block></block></view><view class="find-form"><block wx:if="{{stepShow==0}}"><block><view class="form-input"><input class="uni-input" placeholder-class="placeholder-class" type="text" maxlength="17" placeholder="{{$root.m0}}" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['formData']]]]]}}" value="{{formData.mobile}}" bindinput="__e"/></view><view class="form-input align-type"><input class="uni-input info-content" placeholder-class="placeholder-class" type="number" maxlength="4" placeholder="{{$root.m1}}" data-event-opts="{{[['input',[['__set_model',['$0','captcha','$event',[]],['formData']]]]]}}" value="{{formData.captcha}}" bindinput="__e"/><image class="captcha" src="{{captcha.img}}" data-event-opts="{{[['tap',[['getCaptcha',['$event']]]]]}}" bindtap="__e"></image></view><button class="find-btn" type="primary" data-event-opts="{{[['tap',[['nextStep']]]]}}" bindtap="__e">{{$root.m2}}</button></block></block><block wx:if="{{stepShow==1}}"><block><myp-one class="vue-ref" vue-id="4a7863c9-1" maxlength="{{4}}" auto-focus="{{true}}" data-ref="input" data-event-opts="{{[['^input',[['input']]]]}}" bind:input="__e" bind:__l="__l"></myp-one><button class="find-btn" type="primary" disabled="{{isSend}}" data-event-opts="{{[['tap',[['sendDynaCode',['$event']]]]]}}" bindtap="__e">{{codeText}}</button></block></block><block wx:if="{{stepShow==2}}"><block><view class="form-input"><input class="uni-input" placeholder-class="placeholder-class" type="text" maxlength="30" password="true" placeholder="{{$root.m3}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" value="{{formData.password}}" bindinput="__e"/></view><view class="form-input"><input class="uni-input" placeholder-class="placeholder-class" type="text" maxlength="30" password="true" placeholder="{{$root.m4}}" data-event-opts="{{[['input',[['__set_model',['$0','rePassword','$event',[]],['formData']]]]]}}" value="{{formData.rePassword}}" bindinput="__e"/></view><button class="find-btn" type="primary" data-event-opts="{{[['tap',[['save',['$event']]]]]}}" bindtap="__e">{{$root.m5}}</button></block></block></view></view>