<page-meta page-style="{{themeColor}}" class="data-v-11d5e82a"></page-meta><scroll-view class="container data-v-11d5e82a" scroll-y="true"><view class="header-wrap data-v-11d5e82a"><view class="title data-v-11d5e82a">登录</view><block wx:if="{{registerConfig.register!=''}}"><view class="regisiter-agreement data-v-11d5e82a"><text class="color-tip data-v-11d5e82a">还没有账号,</text><text data-event-opts="{{[['tap',[['toRegister',['$event']]]]]}}" class="color-base-text data-v-11d5e82a" bindtap="__e">立即注册</text></view></block></view><view class="body-wrap data-v-11d5e82a"><view class="form-wrap data-v-11d5e82a"><view hidden="{{!(loginMode=='mobile')}}" class="input-wrap data-v-11d5e82a"><view class="content data-v-11d5e82a"><view class="area-code data-v-11d5e82a">+86</view><input class="input data-v-11d5e82a" type="number" placeholder="仅限中国大陆手机号登录" placeholder-class="input-placeholder" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['formData']]]]]}}" value="{{formData.mobile}}" bindinput="__e"/></view></view><view hidden="{{!(loginMode=='account')}}" class="input-wrap data-v-11d5e82a"><view class="content data-v-11d5e82a"><input class="input data-v-11d5e82a" type="text" placeholder="请输入账号" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['__set_model',['$0','account','$event',[]],['formData']]]]]}}" value="{{formData.account}}" bindinput="__e"/></view></view><view hidden="{{!(loginMode=='account')}}" class="input-wrap data-v-11d5e82a"><view class="content data-v-11d5e82a"><input class="input data-v-11d5e82a" type="password" placeholder="请输入密码" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" value="{{formData.password}}" bindinput="__e"/><view hidden="{{!(loginMode=='account')}}" class="align-right data-v-11d5e82a"><text data-event-opts="{{[['tap',[['forgetPassword',['$event']]]]]}}" bindtap="__e" class="data-v-11d5e82a">忘记密码?</text></view></view></view><block wx:if="{{captchaConfig==1}}"><view class="input-wrap data-v-11d5e82a"><view class="content data-v-11d5e82a"><input class="input data-v-11d5e82a" type="text" placeholder="请输入验证码" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['__set_model',['$0','vercode','$event',[]],['formData']]]]]}}" value="{{formData.vercode}}" bindinput="__e"/><image class="captcha data-v-11d5e82a" src="{{captcha.img}}" data-event-opts="{{[['tap',[['getCaptcha',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view hidden="{{!(loginMode=='mobile')}}" class="input-wrap data-v-11d5e82a"><view class="content data-v-11d5e82a"><input class="input data-v-11d5e82a" type="text" placeholder="请输入动态码" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['__set_model',['$0','dynacode','$event',[]],['formData']]]]]}}" value="{{formData.dynacode}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['sendMobileCode',['$event']]]]]}}" class="{{['dynacode','data-v-11d5e82a',dynacodeData.seconds==120?'color-base-text':'color-tip']}}" bindtap="__e">{{dynacodeData.codeText}}</view></view></view></view><view class="login-mode-box data-v-11d5e82a"><text data-event-opts="{{[['tap',[['switchLoginMode',['$event']]]]]}}" hidden="{{!($root.g0)}}" bindtap="__e" class="data-v-11d5e82a">使用账号登录</text><text data-event-opts="{{[['tap',[['switchLoginMode',['$event']]]]]}}" hidden="{{!($root.g1)}}" bindtap="__e" class="data-v-11d5e82a">使用手机号登录</text></view><view class="btn_view data-v-11d5e82a"><button class="login-btn color-base-border color-base-bg data-v-11d5e82a" type="primary" data-event-opts="{{[['tap',[['login',['$event']]]]]}}" bindtap="__e">登录</button></view><block wx:if="{{registerConfig.agreement_show}}"><view class="regisiter-agreement data-v-11d5e82a"><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['iconfont','is-agree','data-v-11d5e82a',isAgree?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}" bindtap="__e"></text><text class="tips data-v-11d5e82a">请阅读并同意</text><text data-event-opts="{{[['tap',[['toAggrement',['PRIVACY']]]]]}}" class="color-base-text data-v-11d5e82a" bindtap="__e">《隐私协议》</text><text class="tips data-v-11d5e82a">和</text><text data-event-opts="{{[['tap',[['toAggrement',['SERVICE']]]]]}}" class="color-base-text data-v-11d5e82a" bindtap="__e">《用户协议》</text></view></block></view><loading-cover vue-id="5b21249e-1" data-ref="loadingCover" class="data-v-11d5e82a vue-ref" bind:__l="__l"></loading-cover><register-reward vue-id="5b21249e-2" data-ref="registerReward" class="data-v-11d5e82a vue-ref" bind:__l="__l"></register-reward></scroll-view>