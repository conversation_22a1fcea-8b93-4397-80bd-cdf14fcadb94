require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/login/register"],{"2d00":function(e,t,i){"use strict";i.r(t);var a=i("9c1a"),n=i("f946");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);i("7103"),i("8c8d");var o=i("828b"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"4c6dba61",null,!1,a["a"],void 0);t["default"]=s.exports},3009:function(e,t,i){"use strict";(function(e,t){var a=i("47a9");i("d381");a(i("3240"));var n=a(i("2d00"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(n.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},7103:function(e,t,i){"use strict";var a=i("b72b"),n=i.n(a);n.a},"8c8d":function(e,t,i){"use strict";var a=i("9c7d"),n=i.n(a);n.a},"9c1a":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={uniPopup:function(){return i.e("components/uni-popup/uni-popup").then(i.bind(null,"d745"))},nsMpHtml:function(){return i.e("components/ns-mp-html/ns-mp-html").then(i.bind(null,"d108"))},loadingCover:function(){return i.e("components/loading-cover/loading-cover").then(i.bind(null,"c003"))},registerReward:function(){return i.e("components/register-reward/register-reward").then(i.bind(null,"349c"))}},n=function(){var e=this,t=e.$createElement,i=(e._self._c,"mobile"==e.registerMode&&-1!=e.registerConfig.register.indexOf("username")),a="account"==e.registerMode&&-1!=e.registerConfig.register.indexOf("mobile");e._isMounted||(e.e0=function(t){e.isAgree=!e.isAgree}),e.$mp.data=Object.assign({},{$root:{g0:i,g1:a}})},r=[]},"9c7d":function(e,t,i){},b18a:function(e,t,i){"use strict";(function(e){var a=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(i("fe8d")),r=(a(i("0817")),{components:{uniPopup:function(){i.e("components/uni-popup/uni-popup").then(function(){return resolve(i("d745"))}.bind(null,i)).catch(i.oe)},registerReward:function(){i.e("components/register-reward/register-reward").then(function(){return resolve(i("349c"))}.bind(null,i)).catch(i.oe)}},data:function(){return{allowRegister:!0,registerMode:"account",formData:{mobile:"",account:"",password:"",rePassword:"",vercode:"",dynacode:"",key:""},agreement:{title:"",content:""},captcha:{id:"",img:""},isSub:!1,back:"",dynacodeData:{seconds:120,timer:null,codeText:"获取动态码",isSend:!1},registerConfig:{register:""},authInfo:null,isAgree:!1,isOpenCaptcha:0}},onLoad:function(t){t.back&&(this.back=t.back),this.getCaptchaConfig(),this.getRegisiterAggrement(),this.getRegisterConfig(),this.authInfo=e.getStorageSync("authInfo")},onShow:function(){},onReady:function(){this.$refs.loadingCover&&this.$refs.loadingCover.hide()},methods:{toAggrement:function(e){this.$util.redirectTo("/pages_tool/login/aggrement",{type:e})},switchRegisterMode:function(){this.registerMode="mobile"==this.registerMode?"account":"mobile"},openPopup:function(){this.$refs.registerPopup.open()},toClose:function(){this.$refs.registerPopup.close()},getRegisiterAggrement:function(){var e=this;this.$api.sendRequest({url:"/api/register/aggrement",success:function(t){t.code>=0&&(e.agreement=t.data)}})},getRegisterConfig:function(){var e=this;this.$api.sendRequest({url:"/api/register/config",success:function(t){t.code>=0&&(e.registerConfig=t.data.value,""==e.registerConfig.register?(e.$util.showToast({title:"平台未启用注册!"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1e3)):-1!=e.registerConfig.register.indexOf("username")?e.registerMode="account":e.registerMode="mobile",e.$refs.loadingCover&&e.$refs.loadingCover.hide())}})},getCaptchaConfig:function(){var e=this;this.$api.sendRequest({url:"/api/config/getCaptchaConfig",success:function(t){t.code>=0&&(e.isOpenCaptcha=t.data.shop_reception_register,1==e.isOpenCaptcha&&e.getCaptcha())}})},getCaptcha:function(){var e=this;0!=this.isOpenCaptcha&&this.$api.sendRequest({url:"/api/captcha/captcha",data:{captcha_id:this.captcha.id},success:function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))}})},register:function(){var t=this;if("account"==this.registerMode){var i="/api/register/username";a={username:this.formData.account,password:this.formData.password}}else{i="/api/register/mobile";var a={mobile:this.formData.mobile,key:this.formData.key,code:this.formData.dynacode}}if(1==this.isOpenCaptcha&&""!=this.captcha.id&&(a.captcha_id=this.captcha.id,a.captcha_code=this.formData.vercode),this.authInfo&&(Object.assign(a,this.authInfo),this.authInfo.nickName&&(a.nickname=this.authInfo.nickName),this.authInfo.avatarUrl&&(a.headimg=this.authInfo.avatarUrl)),e.getStorageSync("source_member")&&(a.source_member=e.getStorageSync("source_member")),this.verify(a)){if(this.isSub)return;this.isSub=!0,this.$api.sendRequest({url:i,data:a,success:function(e){e.code>=0?(t.$store.commit("setToken",e.data.token),t.$store.dispatch("getCartNumber"),t.getMemberInfo((function(){t.$util.showToast({title:"注册成功"});var e=t.back?t.back:"/pages/member/index";t.$refs.registerReward.open(e)}))):(t.isSub=!1,t.getCaptcha(),t.$util.showToast({title:e.message}))},fail:function(e){t.isSub=!1,t.getCaptcha()}})}},verify:function(e){if(this.registerConfig.agreement_show&&!this.isAgree)return this.$util.showToast({title:"请先阅读并同意协议"}),!1;if("mobile"==this.registerMode){var t=[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}];1==this.isOpenCaptcha&&""!=this.captcha.id&&t.push({name:"captcha_code",checkType:"required",errorMsg:this.$lang("captchaPlaceholder")}),t.push({name:"code",checkType:"required",errorMsg:this.$lang("dynacodePlaceholder")})}if("account"==this.registerMode){t=[{name:"username",checkType:"required",errorMsg:"请输入账号"},{name:"password",checkType:"required",errorMsg:"请输入密码"}];var i=this.registerConfig;if(!/^[A-Za-z0-9]+$/.test(e.username))return void this.$util.showToast({title:"用户名只能输入数字跟英文"});if(i.pwd_len>0&&t.push({name:"password",checkType:"lengthMin",checkRule:i.pwd_len,errorMsg:"密码长度不能小于"+i.pwd_len+"位"}),""!=i.pwd_complexity){var a="密码需包含",r="";-1!=i.pwd_complexity.indexOf("number")&&(r+="(?=.*?[0-9])",a+="数字"),-1!=i.pwd_complexity.indexOf("letter")&&(r+="(?=.*?[a-z])",a+="、小写字母"),-1!=i.pwd_complexity.indexOf("upper_case")&&(r+="(?=.*?[A-Z])",a+="、大写字母"),-1!=i.pwd_complexity.indexOf("symbol")&&(r+="(?=.*?[#?!@$%^&*-])",a+="、特殊字符"),t.push({name:"password",checkType:"reg",checkRule:r,errorMsg:a})}if(this.formData.password!=this.formData.rePassword)return this.$util.showToast({title:"两次密码不一致"}),!1;1==this.isOpenCaptcha&&""!=this.captcha.id&&t.push({name:"captcha_code",checkType:"required",errorMsg:this.$lang("captchaPlaceholder")})}var o=n.default.check(e,t);return!!o||(this.$util.showToast({title:n.default.error}),!1)},toLogin:function(){this.back?this.$util.redirectTo("/pages_tool/login/index",{back:encodeURIComponent(this.back)}):this.$util.redirectTo("/pages_tool/login/index")},sendMobileCode:function(){var e=this;if(120==this.dynacodeData.seconds&&!this.dynacodeData.isSend){var t={mobile:this.formData.mobile};1==this.isOpenCaptcha&&""!=this.captcha.id&&(t.captcha_id=this.captcha.id,t.captcha_code=this.formData.vercode);var i=[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}];1==this.isOpenCaptcha&&""!=this.captcha.id&&i.push({name:"captcha_code",checkType:"required",errorMsg:"请输入验证码"});var a=n.default.check(t,i);a?(this.dynacodeData.isSend=!0,this.dynacodeData.timer=setInterval((function(){e.dynacodeData.seconds--,e.dynacodeData.codeText=e.dynacodeData.seconds+"s后可重新获取"}),1e3),this.$api.sendRequest({url:"/api/register/mobileCode",data:t,success:function(t){t.code>=0?e.formData.key=t.data.key:(e.$util.showToast({title:t.message}),e.refreshDynacodeData())},fail:function(){e.$util.showToast({title:"request:fail"}),e.refreshDynacodeData()}})):this.$util.showToast({title:n.default.error})}},refreshDynacodeData:function(){this.getCaptcha(),clearInterval(this.dynacodeData.timer),this.dynacodeData={seconds:120,timer:null,codeText:"获取动态码",isSend:!1}},getMemberInfo:function(e){var t=this;this.$api.sendRequest({url:"/api/member/info",success:function(i){i.code>=0&&(t.$store.commit("setMemberInfo",i.data),e&&e())}})}},watch:{"dynacodeData.seconds":{handler:function(e,t){0==e&&this.refreshDynacodeData()},immediate:!0,deep:!0}}});t.default=r}).call(this,i("df3c")["default"])},b72b:function(e,t,i){},f946:function(e,t,i){"use strict";i.r(t);var a=i("b18a"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a}},[["3009","common/runtime","common/vendor"]]]);