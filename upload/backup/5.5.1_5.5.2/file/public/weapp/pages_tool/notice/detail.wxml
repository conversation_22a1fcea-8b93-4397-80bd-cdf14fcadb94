<page-meta page-style="{{themeColor}}"></page-meta><view class="page"><view class="notice-title">{{detail.title}}</view><view class="notice-meta"><text class="notice-time">{{"发表时间: "+$root.g0}}</text></view><view class="notice-content"><ns-mp-html vue-id="c81545a8-1" content="{{content}}" bind:__l="__l"></ns-mp-html></view><hover-nav vue-id="c81545a8-2" bind:__l="__l"></hover-nav><loading-cover class="vue-ref" vue-id="c81545a8-3" data-ref="loadingCover" bind:__l="__l"></loading-cover><privacy-popup class="vue-ref" vue-id="c81545a8-4" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>