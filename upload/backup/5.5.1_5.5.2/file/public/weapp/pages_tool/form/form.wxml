<page-meta page-style="{{themeColor}}"></page-meta><view><block wx:if="{{detail}}"><view class="page"><view class="form-banner"><image src="{{$root.g0}}" mode="widthFix"></image></view><view class="system-form-wrap"><view class="form-title">请填写表单所需信息</view><ns-form class="vue-ref" vue-id="88ef0e76-1" data="{{detail.json_data}}" data-ref="form" bind:__l="__l"></ns-form><button class="button mini" type="primary" size="mini" data-event-opts="{{[['tap',[['create']]]]}}" bindtap="__e">提交</button></view></view></block><block wx:else><ns-empty vue-id="88ef0e76-2" text="{{complete?'提交成功':'未获取到表单信息'}}" bind:__l="__l"></ns-empty></block><loading-cover class="vue-ref" vue-id="88ef0e76-3" data-ref="loadingCover" bind:__l="__l"></loading-cover><ns-login class="vue-ref" vue-id="88ef0e76-4" data-ref="login" bind:__l="__l"></ns-login></view>