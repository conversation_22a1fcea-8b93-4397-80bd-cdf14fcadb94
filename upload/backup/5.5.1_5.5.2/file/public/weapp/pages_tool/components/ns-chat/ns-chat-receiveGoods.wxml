<view class="goods"><view class="goods-msg"><image src="{{$root.g0}}" mode="aspectFill"></image><view class="goods-item"><view class="title">{{goodsInfo.goods_name}}</view><view class="goods-sku">{{'库存:'+goodsInfo.stock+''}}<text>{{"销量:"+goodsInfo.sale_num}}</text></view><view class="goods-price"><view class="price color-base-text"><text class="price-util">￥</text><text class="price-num">{{goodsInfo.price}}</text></view><view data-event-opts="{{[['tap',[['go_shop']]]]}}" class="see-shop color-base-text" bindtap="__e">查看商品<text class="iconfont icon-right"></text></view></view></view></view></view>