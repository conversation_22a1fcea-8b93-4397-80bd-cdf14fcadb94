require('../../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/components/chat-message/chat-message"],{"10c3":function(e,t,n){"use strict";var o=n("a43d"),i=n.n(o);i.a},4546:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={uniPopup:function(){return n.e("components/uni-popup/uni-popup").then(n.bind(null,"d745"))}},i=function(){var e=this,t=e.$createElement,n=(e._self._c,"string"==e.message.contentType?e.stringToEmjoy(e.message.content):null),o="image"==e.message.contentType?e.$util.img(e.message.image):null,i=e.$util.img(e.currImg);e._isMounted||(e.e0=function(t){e.previewMedia(e.$util.img(e.message.image))}),e.$mp.data=Object.assign({},{$root:{m0:n,g0:o,g1:i}})},a=[]},75345:function(e,t,n){"use strict";n.r(t);var o=n("e32b"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},"8be5":function(e,t,n){"use strict";n.r(t);var o=n("4546"),i=n("75345");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n("10c3");var r=n("828b"),s=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=s.exports},a43d:function(e,t,n){},e32b:function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n("0817")),a=o(n("3291")),r={name:"chat-message",props:{message:{type:Object},send:{type:Boolean}},data:function(){return{avatar:"",defaultAvatar:this.$util.getDefaultImage().store,myHeadImg:"",defaultHead:this.$util.getDefaultImage().head,emjoyList:a.default.emjoyList,currImg:""}},components:{nsChatGoods:function(){n.e("pages_tool/components/ns-chat/ns-chat-goods").then(function(){return resolve(n("e729"))}.bind(null,n)).catch(n.oe)},nsChatOrder:function(){n.e("pages_tool/components/ns-chat/ns-chat-order").then(function(){return resolve(n("8cf6"))}.bind(null,n)).catch(n.oe)},uniPopup:function(){n.e("components/uni-popup/uni-popup").then(function(){return resolve(n("d745"))}.bind(null,n)).catch(n.oe)},nsChatReceiveGoods:function(){n.e("pages_tool/components/ns-chat/ns-chat-receiveGoods").then(function(){return resolve(n("be18"))}.bind(null,n)).catch(n.oe)}},mounted:function(){this.avatar=this.$util.img(this.siteInfo.logo_square),this.myHeadImg=this.$util.img(this.memberInfo.headimg)},methods:{previewMedia:function(t){var n=[t];e.previewImage({current:0,urls:n})},sendGood:function(){this.$emit("sendGood","goods")},sendOrder:function(){this.$emit("sendOrder","order")},myHeadImgError:function(){this.myHeadImg=this.defaultHead},stringToEmjoy:function(e){var t=this;if(e){var n=RegExp(/\[/);if(n.test(e)){var o=e,a=new RegExp("\\[emjoy_(.+?)\\]","g"),r=o.replace(a,(function(e){var n="";for(var o in t.emjoyList)if(e==o){var i=t.$util.img(t.emjoyList[o]);n="<img class='message-img' src='"+i+"'/>";break}return n||e})),s=(0,i.default)(r);return s.forEach((function(e){"img"==e.name&&(e.attrs.style="display: inline-block;width: 32rpx !important;height: 32rpx !important;padding:0 2rpx;")})),s}var u=e;return u}}}};t.default=r}).call(this,n("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages_tool/components/chat-message/chat-message-create-component',
    {
        'pages_tool/components/chat-message/chat-message-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("8be5"))
        })
    },
    [['pages_tool/components/chat-message/chat-message-create-component']]
]);
