<view class="message"><block wx:if="{{goodsInfo&&goodsInfo.goods_name}}"><view class="goods-item"><image src="{{$root.g0}}" mode="aspectFill"></image><view class="goods-info"><view class="goods-name">{{goodsInfo.sku_name?goodsInfo.sku_name:goodsInfo.goods_name}}</view><view class="goods-bottom"><view class="goods-price"><text class="goods-price-sign color-base-text">￥</text><text class="color-base-text">{{goodsInfo.price}}</text></view><view class="goods-option font-size-goods-tag disabled">已发送</view></view></view></view></block><block wx:else><block wx:if="{{goodsDetail}}"><view class="goods-item"><image src="{{$root.g1}}" mode="aspectFill"></image><view class="goods-info"><view class="goods-name">{{goodsDetail.sku_name?goodsDetail.sku_name:goodsDetail.goods_name}}</view><view class="goods-bottom"><view class="goods-price"><text class="goods-price-sign color-base-text">￥</text><text class="color-base-text">{{goodsDetail.price}}</text></view><view data-event-opts="{{[['tap',[['sendMsg',['goods']]]]]}}" class="goods-option font-size-goods-tag color-base-bg" bindtap="__e">发送</view></view></view></view></block></block></view>