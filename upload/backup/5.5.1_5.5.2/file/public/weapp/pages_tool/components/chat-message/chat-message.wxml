<view class="chat-message"><block wx:if="{{message.contentType=='sendGood'}}"><block><ns-chat-goods vue-id="c747addc-1" skuId="{{message.sku_id}}" goodsDetail="{{message.goodsDetail}}" data-event-opts="{{[['^sendMsg',[['sendGood']]]]}}" bind:sendMsg="__e" bind:__l="__l"></ns-chat-goods></block></block><block wx:if="{{message.contentType=='sendOrder'}}"><block><ns-chat-order vue-id="c747addc-2" orderId="{{message.order_id}}" orderdetails="{{message.orderDetail}}" data-event-opts="{{[['^sendMsg',[['sendOrder']]]]}}" bind:sendMsg="__e" bind:__l="__l"></ns-chat-order></block></block><block wx:if="{{message.contentType=='goodssku'}}"><block><ns-chat-receive-goods vue-id="c747addc-3" skuId="{{message.sku_id}}" bind:__l="__l"></ns-chat-receive-goods></block></block><block wx:if="{{message.contentType=='string'}}"><view class="message"><view class="{{['message-item','',message.isItMe?'right':'left']}}"><block wx:if="{{message.isItMe}}"><view class="head_img"><block wx:if="{{myHeadImg}}"><image class="img" src="{{myHeadImg}}" mode="aspectFit" data-event-opts="{{[['error',[['myHeadImgError',['$event']]]]]}}" binderror="__e"></image></block><block wx:else><image class="img" src="{{defaultHead}}" mode="aspectFit"></image></block></view></block><block wx:else><view class="head_img"><block wx:if="{{avatar}}"><image class="img" src="{{avatar}}" mode="aspectFit"></image></block><block wx:else><image class="img" src="{{defaultHead}}" mode="aspectFit"></image></block></view></block><view class="chat_text"><block wx:if="{{message.isItMe&&!message.sendStatus}}"><text class="iconfont icon-warn margin-right color-base-text"></text></block><view class="content"><rich-text nodes="{{$root.m0}}"></rich-text></view></view></view></view></block><block wx:if="{{message.contentType=='image'}}"><view class="message"><view class="{{['message-item','',message.isItMe?'right':'left']}}"><block wx:if="{{message.isItMe}}"><view class="head_img"><block wx:if="{{myHeadImg}}"><image class="img" src="{{myHeadImg}}" mode="aspectFit"></image></block><block wx:else><image class="img" src="{{defaultHead}}" mode="aspectFit"></image></block></view></block><block wx:else><view class="head_img"><block wx:if="{{avatar}}"><image class="img" src="{{avatar}}" mode="aspectFit"></image></block><block wx:else><image class="img" src="{{defaultHead}}" mode="aspectFit"></image></block></view></block><view class="chat_img"><block wx:if="{{message.isItMe&&!message.sendStatus}}"><text class="iconfont icon-warn margin-right color-base-text"></text></block><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="content_img" style="{{'background-image:'+('url('+$root.g0+')')+';'}}" bindtap="__e"></view></view></view></view></block><block wx:else><block wx:if="{{message.contentType=='goods'}}"><view><ns-chat-goods vue-id="c747addc-4" isCanSend="{{false}}" skuId="{{message.sku_id}}" bind:__l="__l"></ns-chat-goods></view></block><block wx:else><block wx:if="{{message.contentType=='order'}}"><view><ns-chat-order vue-id="c747addc-5" isCanSend="{{false}}" orderId="{{message.order_id}}" bind:__l="__l"></ns-chat-order></view></block></block></block><block wx:if="{{message.contentType=='noline'}}"><view class="no-connect-box"><view class="no-connect">客服不在线</view></view></block><block wx:if="{{message.contentType=='online'}}"><view class="no-connect-box"><view class="no-connect">客服在线</view></view></block><uni-popup class="vue-ref" vue-id="c747addc-6" type="center" data-ref="imgPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="imagePop"><image src="{{$root.g1}}" mode="aspectFit"></image></view></uni-popup></view>