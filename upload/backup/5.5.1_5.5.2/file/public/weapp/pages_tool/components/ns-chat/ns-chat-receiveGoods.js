require('../../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/components/ns-chat/ns-chat-receiveGoods"],{2114:function(t,n,e){},"5cfa":function(t,n,e){"use strict";var o=e("2114"),i=e.n(o);i.a},"7fec":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o={name:"ns-chat-receiveGoods",props:{skuId:{type:[Number,String]}},data:function(){return{goodsInfo:{}}},mounted:function(){this.getInfo()},methods:{getInfo:function(){var t=this;this.$api.sendRequest({url:"/api/goodssku/detail",data:{sku_id:this.skuId},success:function(n){n.code>=0&&(t.goodsInfo=n.data.goods_sku_detail,t.$emit("upDOM"))}})},go_shop:function(){this.$util.redirectTo("/pages/goods/detail?goods_id="+this.goodsInfo.goods_id)}}};n.default=o},"8f67":function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var o=function(){var t=this.$createElement,n=(this._self._c,this.$util.img(this.goodsInfo.sku_image));this.$mp.data=Object.assign({},{$root:{g0:n}})},i=[]},be18:function(t,n,e){"use strict";e.r(n);var o=e("8f67"),i=e("fee7");for(var s in i)["default"].indexOf(s)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(s);e("5cfa");var u=e("828b"),a=Object(u["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=a.exports},fee7:function(t,n,e){"use strict";e.r(n);var o=e("7fec"),i=e.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(s);n["default"]=i.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages_tool/components/ns-chat/ns-chat-receiveGoods-create-component',
    {
        'pages_tool/components/ns-chat/ns-chat-receiveGoods-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("be18"))
        })
    },
    [['pages_tool/components/ns-chat/ns-chat-receiveGoods-create-component']]
]);
