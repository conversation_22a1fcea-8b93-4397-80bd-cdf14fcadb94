<view data-event-opts="{{[['touchmove',[['ontouchmove',['$event']]]],['touchend',[['e0',['$event']]]]]}}" class="{{['rate-box','data-v-1a2fe5e2',[(animation)?'animation':''],containerClass]}}" bindtouchmove="__e" bindtouchend="__e"><block wx:for="{{list}}" wx:for-item="val" wx:for-index="i" wx:key="*this"><view class="{{['rate','data-v-1a2fe5e2',[(!disabled&&val<=rateValue&&animation&&touchMoving)?'scale':'',(val<=rateValue)?'color-base-text':'',(val>rateValue)?'defaultColor':''],'rate-'+i,rateClass]}}" style="{{'font-size:'+(fontSize)+';'+('padding-left:'+(i!==0?rateMargin:0)+';')+('padding-right:'+(i<$root.g0-1?rateMargin:0)+';')}}" data-val="{{val}}" data-event-opts="{{[['tap',[['onItemClick',['$event']]]]]}}" bindtap="__e"><text class="iconfont icon-star data-v-1a2fe5e2"></text></view></block></view>