require('../../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/components/uni-status-bar/uni-status-bar"],{"049b":function(t,n,a){"use strict";a.r(n);var u=a("792c"),e=a("a26a");for(var r in e)["default"].indexOf(r)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(r);a("2410");var i=a("828b"),c=Object(i["a"])(e["default"],u["b"],u["c"],!1,null,"d788aa98",null,!1,u["a"],void 0);n["default"]=c.exports},2410:function(t,n,a){"use strict";var u=a("75ab"),e=a.n(u);e.a},"3df0":function(t,n,a){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=2*t.getSystemInfoSync().statusBarHeight+"rpx",u={name:"UniStatusBar",data:function(){return{statusBarHeight:a}}};n.default=u}).call(this,a("df3c")["default"])},"75ab":function(t,n,a){},"792c":function(t,n,a){"use strict";a.d(n,"b",(function(){return u})),a.d(n,"c",(function(){return e})),a.d(n,"a",(function(){}));var u=function(){var t=this.$createElement;this._self._c},e=[]},a26a:function(t,n,a){"use strict";a.r(n);var u=a("3df0"),e=a.n(u);for(var r in u)["default"].indexOf(r)<0&&function(t){a.d(n,t,(function(){return u[t]}))}(r);n["default"]=e.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages_tool/components/uni-status-bar/uni-status-bar-create-component',
    {
        'pages_tool/components/uni-status-bar/uni-status-bar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("049b"))
        })
    },
    [['pages_tool/components/uni-status-bar/uni-status-bar-create-component']]
]);
