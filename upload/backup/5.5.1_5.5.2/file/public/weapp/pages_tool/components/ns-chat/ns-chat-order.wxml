<view class="message"><block wx:if="{{orderdetails}}"><view class="goods-item"><image src="{{$root.g0}}" mode="aspectFill"></image><view class="goods-info"><view class="goods-name">{{orderdetails.order_goods?orderdetails.order_goods[0].sku_name:''}}</view><view class="font-size-goods-tag">{{"订单状态:"+orderdetails.order_status_name}}</view><view class="font-size-goods-tag">{{"配送方式:"+orderdetails.delivery_type_name}}</view><view class="goods-bottom"><view class="goods-price color-base-text"><text class="goods-price-sign">￥</text><text>{{orderdetails.order_goods?orderdetails.order_goods[0].price:''}}</text></view><view data-event-opts="{{[['tap',[['sendMsg',['order']]]]]}}" class="goods-option font-size-goods-tag color-base-bg" bindtap="__e">发送</view></view></view></view></block><block wx:else><block wx:if="{{orderInfo}}"><view class="goods-item"><image src="{{$root.g1}}" mode="aspectFill"></image><view class="goods-info"><view class="goods-name">{{orderInfo.order_goods?orderInfo.order_goods[0].sku_name:''}}</view><view class="font-size-goods-tag">{{"订单状态:"+orderInfo.order_status_name}}</view><view class="font-size-goods-tag">{{"配送方式:"+orderInfo.delivery_type_name}}</view><view class="goods-bottom"><view class="goods-price color-base-text"><text class="goods-price-sign">￥</text><text>{{orderInfo.order_goods?orderInfo.order_goods[0].price:''}}</text></view><view class="goods-option font-size-goods-tag disabled">已发送</view></view></view></view></block></block></view>