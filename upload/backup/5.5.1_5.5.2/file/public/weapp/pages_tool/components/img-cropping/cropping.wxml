<view><block wx:if="{{imgSrc.imgSrc}}"><image class="my-avatar" style="{{$root.s0}}" src="{{imgSrc.imgSrc}}" data-event-opts="{{[['tap',[['fSelect',['$event']]]]]}}" bindtap="__e"></image></block><canvas class="my-canvas" style="{{'top:'+(sT)+';'+('height:'+(csH)+';')}}" canvas-id="avatar-canvas" id="avatar-canvas" disable-scroll="false"></canvas><canvas class="oper-canvas" style="{{'top:'+(sT)+';'+('height:'+(csH)+';')}}" canvas-id="oper-canvas" id="oper-canvas" disable-scroll="false" data-event-opts="{{[['touchstart',[['fStart',['$event']]]],['touchmove',[['fMove',['$event']]]],['touchend',[['fEnd',['$event']]]]]}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"></canvas><canvas class="prv-canvas" style="{{'height:'+(csH)+';'+('top:'+(pT)+';')}}" canvas-id="prv-canvas" id="prv-canvas" disable-scroll="false" data-event-opts="{{[['touchstart',[['fHideImg',['$event']]]]]}}" bindtouchstart="__e"></canvas><view class="oper-wrapper" style="{{'display:'+(sD)+';'+('top:'+(tp)+';')}}"><view class="oper"><block wx:if="{{sO}}"><view class="btn-wrapper"><view data-event-opts="{{[['tap',[['fClose',['$event']]]]]}}" class="cancel" style="{{'width:'+(bW)+';'}}" bindtap="__e"><text>取消</text></view><view data-event-opts="{{[['tap',[['fSelect',['$event']]]]]}}" style="{{'width:'+(bW)+';'}}" bindtap="__e"><text class="iconfont icon-shangchuan"></text></view><view data-event-opts="{{[['tap',[['fRotate',['$event']]]]]}}" style="{{'width:'+(bW)+';'+('display:'+(bD)+';')}}" bindtap="__e"><text class="iconfont icon-xuanzhuan"></text></view><view data-event-opts="{{[['tap',[['fUpload',['$event']]]]]}}" style="{{'width:'+(bW)+';'}}" bindtap="__e"><text class="upload">上传</text></view></view></block><block wx:else><view class="clr-wrapper"><slider class="my-slider" block-size="25" value="0" min="-100" max="100" activeColor="red" backgroundColor="green" block-color="grey" show-value="{{true}}" data-event-opts="{{[['change',[['fColorChange',['$event']]]]]}}" bindchange="__e"></slider><view style="{{'width:'+(bW)+';'}}" hover-class="hover" data-event-opts="{{[['tap',[['fPrvUpload',['$event']]]]]}}" bindtap="__e"><text>上传</text></view></view></block></view></view></view>