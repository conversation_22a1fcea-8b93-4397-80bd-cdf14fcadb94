require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/pay/offlinepay"],{"07da":function(t,n,e){"use strict";e.r(n);var a=e("f81e"),i=e("dba1");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);e("5775");var f=e("828b"),u=Object(f["a"])(i["default"],a["b"],a["c"],!1,null,"b6c8d052",null,!1,a["a"],void 0);n["default"]=u.exports},5775:function(t,n,e){"use strict";var a=e("8049"),i=e.n(a);i.a},8049:function(t,n,e){},"9d80":function(t,n,e){"use strict";(function(t,n){var a=e("47a9");e("d381");a(e("3240"));var i=a(e("07da"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},dba1:function(t,n,e){"use strict";e.r(n);var a=e("f775"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=i.a},f775:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={data:function(){return{config:[],payInfo:{},offlinepayInfo:{out_trade_no:"",imgs:"",imgList:[],desc:""},actionStatus:"add",outTradeNo:"",time:null,activeIndex:0,repeat_flag:!1,routePath:""}},onLoad:function(t){this.outTradeNo=t.outTradeNo,this.offlinepayInfo.out_trade_no=t.outTradeNo,this.getOfflinepayConfig(),this.getOfflinepayPayInfo(this.outTradeNo),this.getPayInfo(this.outTradeNo);var n=getCurrentPages();n.length<1&&this.getroutePath(this.outTradeNo)},methods:{getOfflinepayConfig:function(){var t=this;this.$api.sendRequest({url:"/offlinepay/api/pay/config",success:function(n){if(n.code>=0&&n.data){var e=n.data.value;Object.keys(e).forEach((function(n){"1"==e[n].status&&(e[n].key=n,t.config.push(e[n]))}))}else t.$util.showToast({title:"未获取到支付配置！"})}})},getPayInfo:function(t){var n=this;this.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:t},success:function(t){t.code>=0&&t.data?(n.payInfo=t.data,n.payInfo.timestamp=t.timestamp,n.payInfo.timestamp<n.payInfo.auto_close_time&&(n.payInfo.time=n.$util.countDown(n.payInfo.auto_close_time-n.payInfo.timestamp),n.time=setInterval((function(){n.payInfo.timestamp>=n.payInfo.auto_close_time&&clearInterval(n.time),n.payInfo.timestamp+=1,n.payInfo.time=n.$util.countDown(n.payInfo.auto_close_time-n.payInfo.timestamp),n.$forceUpdate()}),1e3))):n.$util.showToast({title:"未获取到支付信息！"})}})},getOfflinepayPayInfo:function(t){var n=this;this.$api.sendRequest({url:"/offlinepay/api/pay/info",data:{out_trade_no:t},success:function(t){t.code>=0&&t.data&&(n.actionStatus="edit",n.offlinepayInfo=t.data,n.offlinepayInfo.imgList=n.offlinepayInfo.imgs?n.offlinepayInfo.imgs.split(","):[])}})},getroutePath:function(t){var n=this;this.$api.sendRequest({url:"/api/pay/outTradeNoToOrderDetailPath",data:{out_trade_no:t},success:function(t){t.code>=0&&t.data&&(n.routePath=t.data)}})},offlinepayTypeChange:function(t){this.activeIndex=t},copy:function(t){this.$util.copy(t)},addImg:function(){var t=this,n=this.offlinepayInfo.imgList.length;this.$util.upload(5-n,{path:""},(function(n){t.offlinepayInfo.imgList=t.offlinepayInfo.imgList.concat(n),t.offlinepayInfo.imgs=t.offlinepayInfo.imgList.toString()}),"/offlinepay/api/pay/uploadimg")},deleteImg:function(t){this.offlinepayInfo.imgList.splice(t,1),this.offlinepayInfo.imgs=this.offlinepayInfo.imgList.toString()},back:function(){var n=getCurrentPages();n.length>1?t.navigateBack({delta:1}):this.$util.redirectTo(this.routePath,{},"redirectTo")},save:function(){var n=this;this.repeat_flag||(this.offlinepayInfo.imgList.length?(this.repeat_flag=!0,this.$util.subscribeMessage("OFFLINEPAY_AUDIT_REFUSE",(function(){n.saveSubmit()}))):t.showToast({title:"请至少上传一张凭证",icon:"none"}))},saveSubmit:function(){var n=this;this.$api.sendRequest({url:"/offlinepay/api/pay/pay",data:this.offlinepayInfo,success:function(e){n.repeat_flag=!1,e.code>=0?(t.setStorageSync("offlinepay","offlinepay"),n.back()):t.showToast({title:e.message,icon:"none"})}})}}};n.default=e}).call(this,e("df3c")["default"])},f81e:function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var a=function(){var t=this,n=t.$createElement,e=(t._self._c,t.payInfo.pay_money?parseFloat(t.payInfo.pay_money).toFixed(2):null),a="add"==t.actionStatus&&t.payInfo.time?parseInt(t.payInfo.time.h):null,i="add"==t.actionStatus&&t.payInfo.time?parseInt(t.payInfo.time.h):null,o=t.config.length,f=o?t.__map(t.config,(function(n,e){var a=t.__get_orig(n),i=t.activeIndex==e&&0==t.activeIndex?t.$util.img("public/uniapp/offlinepay/head_style_left.png"):null,o=t.activeIndex==e&&1==t.activeIndex?t.$util.img("public/uniapp/offlinepay/head_style_center.png"):null,f=t.activeIndex==e&&2==t.activeIndex?t.$util.img("public/uniapp/offlinepay/head_style_right.png"):null;return{$orig:a,g2:i,g3:o,g4:f}})):null,u=o&&"bank"!=t.config[t.activeIndex].key?t.$util.img(t.config[t.activeIndex].payment_code):null,s=t.config.length,l=s?t.__map(t.offlinepayInfo.imgList,(function(n,e){var a=t.__get_orig(n),i=t.$util.img(n);return{$orig:a,g7:i}})):null,c=s?t.offlinepayInfo.imgList.length:null,p=s&&c<5?t.offlinepayInfo.imgList.length:null,r=s&&c<5&&p?t.offlinepayInfo.imgList.length:null,d=t.config.length;t.$mp.data=Object.assign({},{$root:{g0:e,m0:a,m1:i,g1:o,l0:f,g5:u,g6:s,l1:l,g8:c,g9:p,g10:r,g11:d}})},i=[]}},[["9d80","common/runtime","common/vendor"]]]);