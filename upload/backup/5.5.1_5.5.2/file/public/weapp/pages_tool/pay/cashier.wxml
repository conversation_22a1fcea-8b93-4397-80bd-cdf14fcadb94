<page-meta page-style="{{themeColor}}" class="data-v-17724a08"></page-meta><view class="cashier data-v-17724a08"><block wx:if="{{payInfo}}"><block class="data-v-17724a08"><block wx:if="{{payInfo.pay_status==0}}"><block class="data-v-17724a08"><text class="content data-v-17724a08">{{payInfo.pay_body}}</text><view class="money-wrap data-v-17724a08"><text class="unit price-font data-v-17724a08">￥</text><text class="money price-font data-v-17724a08">{{$root.f0}}</text></view><block wx:if="{{$root.g0>0}}"><block class="data-v-17724a08"><view class="pay-type data-v-17724a08"><block wx:for="{{payTypeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" class="payment-item data-v-17724a08" bindtap="__e"><view class="data-v-17724a08"><text class="{{['iconfont','data-v-17724a08',item.icon]}}"></text><text class="name data-v-17724a08">{{item.name}}</text></view><text class="{{['iconfont','data-v-17724a08',payIndex==index?'icon-yuan_checked color-base-text':'icon-checkboxblank']}}"></text></view></block></view><button type="primary" data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" bindtap="__e" class="data-v-17724a08">确认支付</button></block></block><block wx:else><view class="empty data-v-17724a08">店铺尚未配置支付方式！</view></block></block></block><block wx:else><ns-empty vue-id="ac6f1390-1" text="该支付单据已支付" is-index="{{true}}" class="data-v-17724a08" bind:__l="__l"></ns-empty></block></block></block><block wx:else><ns-empty vue-id="ac6f1390-2" text="未获取到支付信息" is-index="{{true}}" class="data-v-17724a08" bind:__l="__l"></ns-empty></block><ns-login vue-id="ac6f1390-3" data-ref="login" class="data-v-17724a08 vue-ref" bind:__l="__l"></ns-login></view>