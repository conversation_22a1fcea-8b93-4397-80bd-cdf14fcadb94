<page-meta page-style="{{themeColor}}" class="data-v-734686e1"></page-meta><view class="container data-v-734686e1"><block wx:if="{{payInfo.pay_status!=undefined}}"><view class="result-box data-v-734686e1"><block wx:if="{{payInfo.pay_status}}"><image class="result-image data-v-734686e1" src="{{$root.g0}}" mode="widthFix" lazy-load="true"></image><view class="msg success data-v-734686e1">{{$root.m0}}</view><view class="pay-amount data-v-734686e1"><text class="unit price-style small data-v-734686e1">{{$root.m1}}</text><text class="price-style large data-v-734686e1">{{$root.g1[0]}}</text><text class="price-style small data-v-734686e1">{{"."+$root.g2[1]}}</text></view></block><block wx:else><image class="result-image data-v-734686e1" src="{{$root.g3}}" mode="widthFix"></image><view class="msg fail data-v-734686e1">{{$root.m2}}</view></block><block wx:if="{{addonIsExist.memberconsume&&consumeInfo.is_reward==1&&payInfo.pay_status}}"><view class="consume-box data-v-734686e1"><view class="consume-head data-v-734686e1"><view class="consume-head-text data-v-734686e1">恭喜您获得</view></view><view class="consume-list data-v-734686e1"><block wx:if="{{consumeInfo.point_num>0}}"><view data-event-opts="{{[['tap',[['toMemberPoint']]]]}}" class="consume-item data-v-734686e1" bindtap="__e"><image src="{{$root.g4}}" mode="widthFix" class="data-v-734686e1"></image><view class="consume-value color-base-text data-v-734686e1">{{consumeInfo.point_num}}</view><view class="consume-type data-v-734686e1">积分</view></view></block><block wx:if="{{consumeInfo.growth_num>0}}"><view data-event-opts="{{[['tap',[['toMemberLevel']]]]}}" class="consume-item data-v-734686e1" bindtap="__e"><image src="{{$root.g5}}" mode="widthFix" class="data-v-734686e1"></image><view class="consume-value color-base-text data-v-734686e1">{{consumeInfo.growth_num}}</view><view class="consume-type data-v-734686e1">成长值</view></view></block><block wx:if="{{$root.g6>0}}"><view data-event-opts="{{[['tap',[['toMemberCoupon']]]]}}" class="consume-item data-v-734686e1" bindtap="__e"><image src="{{$root.g7}}" mode="widthFix" class="data-v-734686e1"></image><view class="consume-value color-base-text data-v-734686e1">{{$root.g8}}</view><view class="consume-type data-v-734686e1">张优惠券</view></view></block></view></view></block><view class="action data-v-734686e1"><block wx:if="{{storeToken}}"><block wx:if="{{paySource=='recharge'}}"><view data-event-opts="{{[['tap',[['toRecharge']]]]}}" class="btn data-v-734686e1" bindtap="__e">充值记录</view></block><block wx:else><block wx:if="{{paySource=='membercard'}}"><view data-event-opts="{{[['tap',[['toCard']]]]}}" class="btn data-v-734686e1" bindtap="__e">会员卡</view></block><block wx:else><block wx:if="{{paySource=='presale'}}"><view data-event-opts="{{[['tap',[['toPresaleOrder']]]]}}" class="btn data-v-734686e1" bindtap="__e">查看订单</view></block><block wx:else><block wx:if="{{paySource=='giftcard'}}"><view data-event-opts="{{[['tap',[['toOrder']]]]}}" class="btn data-v-734686e1" bindtap="__e">查看订单</view></block><block wx:else><block wx:if="{{paySource=='pointexchange'}}"><view data-event-opts="{{[['tap',[['toExchangeOrder']]]]}}" class="btn data-v-734686e1" bindtap="__e">查看订单</view></block><block wx:else><view data-event-opts="{{[['tap',[['toOrderDetail',['$0'],['payInfo.order_id']]]]]}}" class="btn data-v-734686e1" bindtap="__e">查看订单</view></block></block></block></block></block></block><view data-event-opts="{{[['tap',[['goHome']]]]}}" class="btn go-home data-v-734686e1" bindtap="__e">{{$root.m3}}</view></view></view><ns-goods-recommend vue-id="51557c24-1" route="pay" class="data-v-734686e1" bind:__l="__l"></ns-goods-recommend></block><loading-cover vue-id="51557c24-2" data-ref="loadingCover" class="data-v-734686e1 vue-ref" bind:__l="__l"></loading-cover></view>