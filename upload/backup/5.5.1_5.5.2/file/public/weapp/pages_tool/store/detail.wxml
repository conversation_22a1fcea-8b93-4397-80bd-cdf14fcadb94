<page-meta page-style="{{themeColor}}"></page-meta><block wx:if="{{store}}"><view class="store-detail"><view class="detail-head" style="{{'height:'+(swiperHeight)+';'}}"><swiper class="swiper" autoplay="true" interval="4000" circular="true" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item item-id="{{'store_id_'+index}}"><view data-event-opts="{{[['tap',[['previewMedia',[index]]]]]}}" class="item" bindtap="__e"><image src="{{item.g0}}" mode="aspectFit" data-event-opts="{{[['error',[['swiperImageError',[index]]]]]}}" binderror="__e"></image></view></swiper-item></block></swiper><block wx:if="{{$root.g1}}"><view class="img-indicator-dots"><text>{{swiperCurrent}}</text><text>{{"/"+$root.g2}}</text></view></block></view><view class="detail-content"><view class="content-item"><view class="store-name multi-hidden">{{store.store_name}}</view><view class="{{['store-state',store.is_frozen.is_frozen==1||store.status==0?'warning':'']}}">{{''+(store.is_frozen.is_frozen==1&&'已停业'||store.status==0&&'休息中'||store.status==1&&'营业中'||'--')+''}}</view></view><block wx:if="{{store.open_date||store.is_default||store.is_pickup||store.is_o2o||store.is_express}}"><view class="content-item store-time-wrap"><block wx:if="{{store.status==0&&store.close_desc}}"><view class="close-desc">{{store.close_desc}}</view></block><block wx:if="{{store.open_date}}"><view class="store-time">{{store.open_date}}</view></block><block wx:if="{{store.is_default||store.is_pickup||store.is_o2o||store.is_express}}"><view class="tag-wrap"><block wx:if="{{store.is_default==1}}"><text class="tag-item">总店</text></block><block wx:if="{{store.is_pickup==1}}"><text class="tag-item">门店自提</text></block><block wx:if="{{store.is_o2o==1}}"><text class="tag-item">同城配送</text></block><block wx:if="{{store.is_express==1}}"><text class="tag-item">物流配送</text></block></view></block></view></block><block wx:if="{{store.show_address||store.distance}}"><view class="content-item address-wrap"><view class="address-box"><block wx:if="{{store.show_address}}"><view class="address-name">{{store.show_address}}</view></block><block wx:if="{{store.distance}}"><view class="address-location"><text class="icondiy icon-system-weizhi"></text><text>{{"距您当前位置"+store.distance+"km"}}</text></view></block></view><text data-event-opts="{{[['tap',[['mapRoute']]]]}}" class="icondiy icon-daohang" bindtap="__e"></text></view></block><block wx:if="{{store.telphone}}"><view class="content-item telphone-wrap"><block wx:if="{{store.telphone}}"><text class="telphone">{{store.telphone}}</text></block><text data-event-opts="{{[['tap',[['phoneCall',['$event']]]]]}}" class="iconfont icon-dianhua" bindtap="__e"></text></view></block></view><view class="detail-map"><view class="map-head">门店地图</view><map class="map-body" latitude="{{store.latitude}}" longitude="{{store.longitude}}" markers="{{covers}}"></map></view></view></block>