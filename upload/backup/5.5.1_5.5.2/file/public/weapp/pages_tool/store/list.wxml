<page-meta page-style="{{themeColor}}" class="data-v-529b7ca2"></page-meta><view class="store-list-wrap data-v-529b7ca2"><block wx:if="{{globalStoreConfig&&globalStoreConfig.store_business=='store'}}"><view class="curr-store data-v-529b7ca2"><view class="store-desc data-v-529b7ca2">当前定位</view><view class="store-name-wrap data-v-529b7ca2"><view class="store-name multi-hidden data-v-529b7ca2">{{currentPosition||'定位中...'}}</view><view data-event-opts="{{[['tap',[['reposition']]]]}}" class="store-position data-v-529b7ca2" bindtap="__e"><text class="iconfont icon-dingwei data-v-529b7ca2"></text><text class="data-v-529b7ca2">重新定位</text></view></view></view></block><view class="store-list-box data-v-529b7ca2"><view class="store-list-head data-v-529b7ca2"><view class="head-name data-v-529b7ca2">门店列表</view><view class="head-search data-v-529b7ca2"><text data-event-opts="{{[['tap',[['getData']]]]}}" class="iconfont icon-sousuo data-v-529b7ca2" bindtap="__e"></text><input type="text" placeholder-class="input-placeholder" placeholder="搜索门店" data-event-opts="{{[['confirm',[['getData']]],['input',[['__set_model',['','keyword','$event',[]]]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e" class="data-v-529b7ca2"/></view></view><scroll-view class="store-list-body data-v-529b7ca2" style="{{'height:'+(globalStoreConfig&&globalStoreConfig.store_business=='store'?'calc(100vh - 320rpx)':'')+';'}}" scroll-y="true"><block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['storeTap',['$0'],[[['dataList','',index]]]]]]]}}" class="{{['data-v-529b7ca2','store-item',[(globalStoreInfo&&item.store_id==globalStoreInfo.store_id)?'active':'']]}}" bindtap="__e"><view class="{{['item-state','data-v-529b7ca2',item.is_frozen.is_frozen==1||item.status==0?'warning':'']}}">{{''+(item.is_frozen.is_frozen==1&&'已停业'||item.status==0&&'休息中'||item.status==1&&'营业中'||'--')+''}}</view><view class="item-name multi-hidden data-v-529b7ca2">{{item.store_name}}</view><block wx:if="{{item.status==0&&item.close_desc}}"><view class="item-close-desc data-v-529b7ca2">{{''+item.close_desc+''}}</view></block><view class="item-time data-v-529b7ca2"><view class="item-time-left data-v-529b7ca2"><text class="iconfont icon-shijian1 data-v-529b7ca2"></text><text class="data-v-529b7ca2">{{item.open_date||'--'}}</text></view><block wx:if="{{item.distance}}"><view class="item-time-right data-v-529b7ca2">{{''+(item.distance>1?item.distance+'km':item.distance*1000+'m')+''}}</view></block></view><view class="item-address data-v-529b7ca2"><text class="iconfont icon-location data-v-529b7ca2"></text><text class="data-v-529b7ca2">{{item.show_address}}</text></view><view class="item-other data-v-529b7ca2"><view class="other-tag-wrap data-v-529b7ca2"><block wx:if="{{item.is_default==1}}"><text class="tag-item data-v-529b7ca2">总店</text></block><block wx:if="{{item.is_pickup==1}}"><text class="tag-item data-v-529b7ca2">门店自提</text></block><block wx:if="{{item.is_o2o==1}}"><text class="tag-item data-v-529b7ca2">同城配送</text></block><block wx:if="{{item.is_express==1}}"><text class="tag-item data-v-529b7ca2">物流配送</text></block></view><view data-event-opts="{{[['tap',[['selectStore',['$0'],[[['dataList','',index]]]]]]]}}" class="other-action data-v-529b7ca2" catchtap="__e"><text class="data-v-529b7ca2">详情</text><text class="iconfont icon-right data-v-529b7ca2"></text></view></view></view></block><block wx:if="{{!$root.g0}}"><ns-empty vue-id="4381437c-1" text="您的附近暂无可选门店" isIndex="{{false}}" class="data-v-529b7ca2" bind:__l="__l"></ns-empty></block></scroll-view></view><loading-cover vue-id="4381437c-2" data-ref="loadingCover" class="data-v-529b7ca2 vue-ref" bind:__l="__l"></loading-cover><privacy-popup vue-id="4381437c-3" data-ref="privacyPopup" class="data-v-529b7ca2 vue-ref" bind:__l="__l"></privacy-popup></view>