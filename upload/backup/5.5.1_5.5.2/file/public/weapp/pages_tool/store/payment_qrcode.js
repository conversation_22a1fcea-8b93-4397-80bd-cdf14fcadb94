require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/store/payment_qrcode"],{"1c57":function(e,n,t){"use strict";(function(e,n){var o=t("47a9");t("d381");o(t("3240"));var r=o(t("6abd"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(r.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},"1d75":function(e,n,t){"use strict";var o=t("2360"),r=t.n(o);r.a},2360:function(e,n,t){},"6abd":function(e,n,t){"use strict";t.r(n);var o=t("f19d"),r=t("838c");for(var i in r)["default"].indexOf(i)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(i);t("1d75");var a=t("828b"),s=Object(a["a"])(r["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=s.exports},"81af":function(e,n,t){"use strict";(function(e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var t={data:function(){return{isRepeat:!1,payInfo:null,error:0,timer:null,show:!1,memberrechargeConfig:null,screenBrightness:0}},onShow:function(){var n=this;e.setStorageSync("paySource",""),this.storeToken?(this.getCouponNum(),this.getMemberrechargeConfig(),this.getPayAuthCode(),e.getScreenBrightness({success:function(e){n.screenBrightness=e.value}}),e.setScreenBrightness({value:1,success:function(){}})):this.$nextTick((function(){n.$refs.login.open("/pages_tool/store/payment_qrcode")}))},onLoad:function(){},methods:{getPayAuthCode:function(){var e=this;this.isRepeat||(this.isRepeat=!0,this.timer&&clearInterval(this.timer),this.$api.sendRequest({url:"/api/pay/memberpaycode",success:function(n){e.isRepeat=!1,0==n.code&&n.data?(e.payInfo=n.data,e.error=0,e.show=!1,setTimeout((function(){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}),100)):e.error<5?(e.error++,e.getPayAuthCode()):e.$util.showToast({title:n.message})}}))},refreshPaymentCode:function(){var e=this;this.timer=setInterval((function(){e.getPayAuthCode()}),3e4)},showAuthCode:function(e){this.show=e},getMemberrechargeConfig:function(){var e=this;this.$api.sendRequest({url:"/memberrecharge/api/memberrecharge/config",success:function(n){n.code>=0&&n.data&&(e.memberrechargeConfig=n.data)}})},getCouponNum:function(){var e=this;this.$api.sendRequest({url:"/coupon/api/coupon/num",success:function(n){0==n.code&&(e.memberInfo.coupon_num=n.data,e.$forceUpdate(),e.$store.commit("setMemberInfo",e.memberInfo))}})},splitFn:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4,t=new RegExp("[^\n]{1,"+n+"}","g"),o=e.match(t);return o.join(" ")}},watch:{storeToken:function(e,n){this.getPayAuthCode()}},onHide:function(){this.timer&&clearInterval(this.timer),e.setScreenBrightness({value:this.screenBrightness,success:function(){}})},onUnload:function(){this.timer&&clearInterval(this.timer),e.setScreenBrightness({value:this.screenBrightness,success:function(){}})}};n.default=t}).call(this,t("df3c")["default"])},"838c":function(e,n,t){"use strict";t.r(n);var o=t("81af"),r=t.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(i);n["default"]=r.a},f19d:function(e,n,t){"use strict";t.d(n,"b",(function(){return r})),t.d(n,"c",(function(){return i})),t.d(n,"a",(function(){return o}));var o={loadingCover:function(){return t.e("components/loading-cover/loading-cover").then(t.bind(null,"c003"))},nsLogin:function(){return Promise.all([t.e("common/vendor"),t.e("components/ns-login/ns-login")]).then(t.bind(null,"2910"))}},r=function(){var e=this,n=e.$createElement,t=(e._self._c,e.payInfo&&e.memberInfo&&e.memberInfo.headimg?e.$util.img(e.memberInfo.headimg):null),o=e.payInfo&&e.memberInfo&&!e.memberInfo.headimg?e.$util.getDefaultImage():null,r=e.payInfo&&e.memberInfo&&e.memberInfo.member_level?e.$util.img("app/component/view/member_info/img/style_4_vip_tag.png"):null,i=e.payInfo&&e.memberInfo&&e.show?e.splitFn(e.payInfo.auth_code):null,a=e.payInfo&&e.memberInfo&&!e.show?e.payInfo.auth_code.substring(0,5):null,s=e.payInfo&&e.memberInfo?parseInt(e.memberInfo.point):null,u=e.payInfo&&e.memberInfo?e._f("moneyFormat")(parseFloat(e.memberInfo.balance)+parseFloat(e.memberInfo.balance_money)):null;e._isMounted||(e.e0=function(n){e.memberInfo.headimg=e.$util.getDefaultImage().head},e.e1=function(n){return e.$util.redirectTo(e.memberInfo.member_level_type?"/pages_tool/member/card":"/pages_tool/member/level")},e.e2=function(n){return e.$util.redirectTo("/pages_tool/recharge/list")},e.e3=function(n){return e.$util.redirectTo("/pages_tool/member/point")},e.e4=function(n){return e.$util.redirectTo("/pages_tool/member/balance")},e.e5=function(n){return e.$util.redirectTo("/pages_tool/member/coupon")}),e.$mp.data=Object.assign({},{$root:{g0:t,g1:o,g2:r,m0:i,g3:a,m1:s,f0:u}})},i=[]}},[["1c57","common/runtime","common/vendor"]]]);