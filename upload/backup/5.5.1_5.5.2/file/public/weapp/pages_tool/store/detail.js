require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/store/detail"],{"09e9":function(t,e,i){},1146:function(t,e,i){"use strict";i.r(e);var s=i("de72"),o=i("9db2");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("28a8");var a=i("828b"),n=Object(a["a"])(o["default"],s["b"],s["c"],!1,null,null,null,!1,s["a"],void 0);e["default"]=n.exports},"28a8":function(t,e,i){"use strict";var s=i("09e9"),o=i.n(s);o.a},"79f1":function(t,e,i){"use strict";(function(t,e){var s=i("47a9");i("d381");s(i("3240"));var o=s(i("1146"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"7abd":function(t,e,i){"use strict";(function(t){var s=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=s(i("a29e")),r={data:function(){return{storeId:0,latitude:null,longitude:null,covers:[],store:null,swiperCurrent:1,swiperHeight:""}},onLoad:function(t){this.storeId=t.store_id||0,this.location?(this.latitude=this.location.latitude,this.longitude=this.location.longitude):1==this.mapConfig.wap_is_open&&this.$util.getLocation(),this.getInfo()},watch:{location:function(t){t&&(this.latitude=t.latitude,this.longitude=t.longitude,this.getInfo())}},methods:{phoneCall:function(){t.makePhoneCall({phoneNumber:this.store.telphone})},getInfo:function(){var t=this,e={store_id:this.storeId};this.latitude&&this.longitude&&(e.latitude=this.latitude,e.longitude=this.longitude),this.$api.sendRequest({url:"/api/store/info",data:e,success:function(e){if(e.data){t.store=e.data||{full_address:"",address:"",store_images:[]},t.covers.push({id:1,latitude:t.store.latitude,longitude:t.store.longitude,iconPath:t.$util.img("public/uniapp/store/map_icon.png"),height:25}),t.store.show_address=t.store.full_address.replace(/,/g," ")+" "+t.store.address,t.handleStoreImage()}else t.$util.showToast({title:"门店不存在"}),setTimeout((function(){t.$util.redirectTo("/pages_tool/store/list",{},"redirectTo")}),2e3)}})},handleStoreImage:function(){this.store.store_images||(this.store.store_images=[]),this.store.store_images=this.store.store_images.reduce((function(t,e){return t.images||(t.images=[]),t.images&&t.images.push(e.pic_path),t.spec||(t.spec=[]),t.spec&&t.spec.push(e.pic_spec),t}),{});var e="";this.store.store_images.spec&&this.store.store_images.spec.forEach((function(i,s){"string"==typeof i&&(i=i.split("*")),t.getSystemInfo({success:function(t){var e=i[0]/t.windowWidth;i[0]=i[0]/e,i[1]=i[1]/e}}),(!e||e>i[1])&&(e=i[1])})),this.swiperHeight=Number(e)+"px",Object.keys(this.store.store_images).length||(this.store.store_images={},this.store.store_images.images=[this.$util.img("public/static/img/default_img/square.png")],this.store.store_images.spec=["350*350"],this.swiperHeight="380px")},swiperChange:function(t){this.swiperCurrent=t.detail.current+1},mapRoute:function(){o.default.openMap(Number(this.store.latitude),Number(this.store.longitude),this.store.store_name,"gcj02")},swiperImageError:function(){this.store.store_images.images=this.$util.img("public/static/img/default_img/square.png")}}};e.default=r}).call(this,i("df3c")["default"])},"9db2":function(t,e,i){"use strict";i.r(e);var s=i("7abd"),o=i.n(s);for(var r in s)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(r);e["default"]=o.a},de72:function(t,e,i){"use strict";i.d(e,"b",(function(){return s})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var s=function(){var t=this,e=t.$createElement,i=(t._self._c,t.store?t.__map(t.store.store_images.images,(function(e,i){var s=t.__get_orig(e),o=t.$util.img(e);return{$orig:s,g0:o}})):null),s=t.store?t.store.store_images.images&&t.store.store_images.images.length:null,o=t.store&&s?t.store.store_images.images.length:null;t.$mp.data=Object.assign({},{$root:{l0:i,g1:s,g2:o}})},o=[]}},[["79f1","common/runtime","common/vendor"]]]);