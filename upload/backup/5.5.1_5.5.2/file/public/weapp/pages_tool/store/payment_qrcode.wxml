<page-meta page-style="{{themeColor}}"></page-meta><view class="container"><block wx:if="{{payInfo&&memberInfo}}"><view><view class="paycode-wrap"><view class="member-wrap"><view data-event-opts="{{[['tap',[['getWxAuth',['$event']]]]]}}" class="headimg" bindtap="__e"><image src="{{memberInfo.headimg?$root.g0:$root.g1.head}}" mode="widthFix" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e"></image></view><view class="info-wrap"><view class="nickname">{{memberInfo.nickname}}</view><block wx:if="{{memberInfo.member_level}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="member-level" bindtap="__e"><image class="level-icon" src="{{$root.g2}}" mode="widthFix"></image><view class="level-name">{{memberInfo.member_level_name}}</view></view></block></view><block wx:if="{{addonIsExist.memberrecharge&&memberrechargeConfig&&memberrechargeConfig.is_use}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="recharge" bindtap="__e">去充值</view></block></view><view class="body-wrap"><view class="barcode-wrap"><image class="barcode" src="{{payInfo.barcode}}"></image></view><view class="auth-code"><text class="price-font">{{show?$root.m0:$root.g3+'******'}}</text><block wx:if="{{!show}}"><text data-event-opts="{{[['tap',[['showAuthCode',[true]]]]]}}" class="show" bindtap="__e">查看数字</text></block><block wx:else><text data-event-opts="{{[['tap',[['showAuthCode',[false]]]]]}}" class="show" bindtap="__e">隐藏数字</text></block></view><image class="qrcode" src="{{payInfo.qrcode}}" mode="widthFix"></image><view data-event-opts="{{[['tap',[['getPayAuthCode',['$event']]]]]}}" class="dynamic-code" bindtap="__e"><view class="code">动态码<text>{{payInfo.dynamic_code}}</text><text class="iconfont icon-shuaxin"></text></view></view><view class="tips">付款码仅用于支付时向收银员出示，请勿发送给他人</view></view><view class="footer-wrap"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="account-item" bindtap="__e"><view class="value price-font">{{$root.m1}}</view><view class="title">积分</view></view><view class="split"></view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="account-item" bindtap="__e"><view class="value price-font">{{''+$root.f0+''}}</view><view class="title">余额</view></view><view class="split"></view><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="account-item" bindtap="__e"><view class="value price-font">{{memberInfo.coupon_num?memberInfo.coupon_num:0}}</view><view class="title">优惠券</view></view></view></view></view></block><loading-cover class="vue-ref" vue-id="0469725a-1" data-ref="loadingCover" bind:__l="__l"></loading-cover><ns-login class="vue-ref" vue-id="0469725a-2" data-ref="login" bind:__l="__l"></ns-login></view>