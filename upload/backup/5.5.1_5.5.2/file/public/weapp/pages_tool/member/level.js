require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/level"],{"0528":function(e,n,t){"use strict";var i=t("d7c5"),o=t.n(i);o.a},"10e5":function(e,n,t){"use strict";(function(e){var i=t("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=i(t("e78f")),l={components:{nsProgress:function(){t.e("pages_tool/components/ns-progress/ns-progress").then(function(){return resolve(t("3e58"))}.bind(null,t)).catch(t.oe)},toTop:function(){t.e("components/toTop/toTop").then(function(){return resolve(t("8f75"))}.bind(null,t)).catch(t.oe)},uniPopup:function(){t.e("components/uni-popup/uni-popup").then(function(){return resolve(t("d745"))}.bind(null,t)).catch(t.oe)}},mixins:[o.default],data:function(){return{couponPopList:[],curIndex:0,descIndex:0,isDescAnimating:!1,scaleX:(634/540).toFixed(4),scaleY:(378/330).toFixed(4),swiperConfig:{indicatorDots:!1,indicatorColor:"rgba(255, 255, 255, .4)",indicatorActiveColor:"rgba(255, 255, 255, 1)",interval:3e3,duration:300,circular:!1,previousMargin:"58rpx",nextMargin:"58rpx"},levelList:[{needGrowth:0,growth:0}],levelId:0,growth:0,nowIndex:0,rule:[]}},computed:{listLen:function(){return this.levelList.length},remark:function(){if(this.levelList[this.curIndex])return this.levelList[this.curIndex].remark},nextIndex:function(){return this.curIndex==this.levelList.length-1?this.curIndex:this.curIndex+1}},onLoad:function(){var e=this;this.getLevelRule(),this.storeToken?this.getLevelList():this.$nextTick((function(){e.$refs.login.open("/pages_tool/member/level")}))},onShow:function(){},filters:{rate:function(e,n,t){var i=Number(t),o=Number(n[e].growth);if(e==n.length-1)return i>o?100:0;var l=Number(n[e+1].growth),u=i-o,r=l-o,s=Math.floor(u/r*100);return s>100?100:s}},methods:{swiperChange:function(e){var n=this;this.curIndex=e.detail.current,this.isDescAnimating=!0;var t=setTimeout((function(){n.descIndex=e.detail.current,clearTimeout(t)}),150)},animationfinish:function(e){this.isDescAnimating=!1},getBannerDetail:function(n){e.showLoading({title:"将前往详情页面",duration:2e3,mask:!0})},getLevelList:function(){var e=this;this.$api.sendRequest({url:"/api/memberlevel/lists",success:function(n){if(n.data&&0==n.code){e.levelList=n.data;for(var t=0;t<e.levelList.length;t++)e.levelList[t].send_coupon&&(e.levelList[t].coupon_length=e.levelList[t].send_coupon.split(",").length);e.levelId=e.memberInfo.member_level,e.growth=e.memberInfo.growth;for(var i=0;i<e.levelList.length;i++)if(e.levelList[i].level_id==e.levelId){e.curIndex=i,e.descIndex=i,e.nowIndex=i;break}e.levelList.forEach((function(n,t){parseFloat(n.growth)<parseFloat(e.growth)?(n.needGrowth=0,n.rate=100):(n.needGrowth=(parseFloat(n.growth)-parseFloat(e.growth)).toFixed(2),n.rate=100*(e.growth/n.growth).toFixed(2))})),e.levelList.forEach((function(e){e.consume_discount&&(e.consume_discount=(e.consume_discount/10).toFixed(2))})),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}else e.$util.showToast({title:n.message}),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},getLevelRule:function(){var e=this;this.$api.sendRequest({url:"/api/member/accountrule",success:function(n){0==n.code&&n.data&&n.data.growth&&(e.rule=n.data.growth)}})},growthRules:function(){this.$util.redirectTo("/pages_tool/member/level_growth_rules")},openCoupon:function(e){var n=this;this.couponPopList=[],this.$api.sendRequest({url:"/coupon/api/coupon/couponbyid",data:{id:e},success:function(e){e.code>=0&&(n.couponPopList=e.data)}}),this.$refs.couponPopup.open()},closeCoupon:function(){this.$refs.couponPopup.close()}},onBackPress:function(e){return"navigateBack"!==e.from&&(this.$util.redirectTo("/pages/member/index"),!0)},watch:{storeToken:function(e,n){e&&this.getLevelList()}}};n.default=l}).call(this,t("df3c")["default"])},"2bc5":function(e,n,t){"use strict";t.r(n);var i=t("a2bf"),o=t("b35f");for(var l in o)["default"].indexOf(l)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(l);t("0528");var u=t("828b"),r=Object(u["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);n["default"]=r.exports},a2bf:function(e,n,t){"use strict";t.d(n,"b",(function(){return o})),t.d(n,"c",(function(){return l})),t.d(n,"a",(function(){return i}));var i={uniPopup:function(){return t.e("components/uni-popup/uni-popup").then(t.bind(null,"d745"))},loadingCover:function(){return t.e("components/loading-cover/loading-cover").then(t.bind(null,"c003"))},nsLogin:function(){return Promise.all([t.e("common/vendor"),t.e("components/ns-login/ns-login")]).then(t.bind(null,"2910"))}},o=function(){var e=this,n=e.$createElement,t=(e._self._c,e.$util.img("public/uniapp/level/level-top-bg.png")),i=e.memberInfo&&e.memberInfo.headimg?e.$util.img(e.memberInfo.headimg):null,o=e.memberInfo&&!e.memberInfo.headimg?e.$util.getDefaultImage():null,l=e.levelList.length,u=e.__map(e.levelList,(function(n,t){var i=e.__get_orig(n),o=n["level_picture"]?e.$util.img(n["level_picture"]):null;return{$orig:i,g4:o}})),r=(e.levelList[e.curIndex].is_free_shipping>0||e.levelList[e.curIndex].consume_discount>0||e.levelList[e.curIndex].point_feedback>0)&&e.levelList[e.curIndex].is_free_shipping>0?e.$util.img("public/uniapp/level/exemption_postage.png"):null,s=(e.levelList[e.curIndex].is_free_shipping>0||e.levelList[e.curIndex].consume_discount>0||e.levelList[e.curIndex].point_feedback>0)&&e.levelList[e.curIndex].consume_discount>0?e.$util.img("public/uniapp/level/consumption_discount.png"):null,c=(e.levelList[e.curIndex].is_free_shipping>0||e.levelList[e.curIndex].consume_discount>0||e.levelList[e.curIndex].point_feedback>0)&&e.levelList[e.curIndex].point_feedback>0?e.$util.img("public/uniapp/level/integral_feedback.png"):null,a=(e.levelList[e.curIndex].send_balance>0||e.levelList[e.curIndex].send_balance>0||e.levelList[e.curIndex].send_coupon)&&e.levelList[e.curIndex].send_point>0?e.$util.img("public/uniapp/level/integral.png"):null,d=(e.levelList[e.curIndex].send_balance>0||e.levelList[e.curIndex].send_balance>0||e.levelList[e.curIndex].send_coupon)&&e.levelList[e.curIndex].send_balance>0?e.$util.img("public/uniapp/level/red_packet.png"):null,p=(e.levelList[e.curIndex].send_balance>0||e.levelList[e.curIndex].send_balance>0||e.levelList[e.curIndex].send_coupon)&&e.levelList[e.curIndex].send_coupon?e.$util.img("public/uniapp/level/coupon.png"):null,f=e.__map(e.couponPopList,(function(n,t){var i=e.__get_orig(n),o="discount"==n.type?e.$util.numberFixed(n.discount,1):null;return{$orig:i,g11:o}}));e._isMounted||(e.e0=function(n){e.memberInfo.headimg=e.$util.getDefaultImage().head}),e.$mp.data=Object.assign({},{$root:{g0:t,g1:i,g2:o,g3:l,l0:u,g5:r,g6:s,g7:c,g8:a,g9:d,g10:p,l1:f}})},l=[]},b26d:function(e,n,t){"use strict";(function(e,n){var i=t("47a9");t("d381");i(t("3240"));var o=i(t("2bc5"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(o.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},b35f:function(e,n,t){"use strict";t.r(n);var i=t("10e5"),o=t.n(i);for(var l in i)["default"].indexOf(l)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(l);n["default"]=o.a},d7c5:function(e,n,t){}},[["b26d","common/runtime","common/vendor"]]]);