require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/withdrawal_detail"],{"1a8e":function(t,e,n){"use strict";(function(t){var a=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("7eb4")),r=a(n("ee10")),o={data:function(){return{id:0,detail:{},withdrawInfo:{},requestCount:0}},onLoad:function(t){var e=this;return(0,r.default)(i.default.mark((function n(){return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.id=t.id||0,n.next=3,e.getWithdrawConfig();case 3:t.action&&e.merchantTransfer();case 4:case"end":return n.stop()}}),n)})))()},onShow:function(){this.storeToken?this.getDetail():this.$util.redirectTo("/pages_tool/login/index",{back:"/pages_tool/member/point"},"redirectTo")},onPullDownRefresh:function(){this.getDetail()},methods:{merchantTransfer:function(){var e=this;t.showLoading({});var n;n=this.withdrawInfo.weapp_appid,this.$util.merchantTransfer({transfer_type:"member_withdraw",id:this.id},{mch_id:this.withdrawInfo.mch_id,app_id:n},(function(t){"requestMerchantTransfer:ok"===t.err_msg&&e.updateStatusToInProcess((function(){e.getDetail(!0)})),"requestMerchantTransfer:ok"===t.errMsg&&e.updateStatusToInProcess((function(){e.getDetail(!0)}))}))},getWithdrawConfig:function(){var t=this;return(0,r.default)(i.default.mark((function e(){var n;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$api.sendRequest({url:"/wechatpay/api/transfer/getWithdrawConfig",async:!1});case 2:n=e.sent,0==n.code&&(t.withdrawInfo=n.data);case 4:case"end":return e.stop()}}),e)})))()},updateStatusToInProcess:function(t){var e=this;this.$refs.loadingCover&&this.$refs.loadingCover.show(),this.$api.sendRequest({url:"/wechatpay/api/transfer/inprocess",data:{from_type:"member_withdraw",relate_tag:this.id},success:function(n){n.code>=0&&(e.$refs.loadingCover&&e.$refs.loadingCover.hide(),"function"==typeof t&&t())}})},getDetail:function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$api.sendRequest({url:"/api/memberwithdraw/detail",data:{id:this.id},success:function(a){a.data&&(e.detail=a.data,n&&e.requestCount<10&&3==e.detail.status&&(e.requestCount++,setTimeout((function(){e.getDetail(!0)}),1e3)),t.stopPullDownRefresh()),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})}}};e.default=o}).call(this,n("df3c")["default"])},"26b1":function(t,e,n){"use strict";n.r(e);var a=n("1a8e"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"337f":function(t,e,n){"use strict";(function(t,e){var a=n("47a9");n("d381");a(n("3240"));var i=a(n("d2d2"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},3723:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={loadingCover:function(){return n.e("components/loading-cover/loading-cover").then(n.bind(null,"c003"))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.$util.timeStampTurnTime(t.detail.apply_time)),a=t.detail.status?t.$util.timeStampTurnTime(t.detail.audit_time):null,i=2==t.detail.status?t.$util.timeStampTurnTime(t.detail.payment_time):null;t.$mp.data=Object.assign({},{$root:{g0:n,g1:a,g2:i}})},r=[]},"80b6":function(t,e,n){"use strict";var a=n("f558"),i=n.n(a);i.a},d2d2:function(t,e,n){"use strict";n.r(e);var a=n("3723"),i=n("26b1");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("80b6");var o=n("828b"),u=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=u.exports},f558:function(t,e,n){}},[["337f","common/runtime","common/vendor"]]]);