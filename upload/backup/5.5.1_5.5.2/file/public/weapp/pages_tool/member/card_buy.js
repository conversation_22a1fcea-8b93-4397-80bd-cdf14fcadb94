require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/card_buy"],{"439d":function(e,n,r){"use strict";var t=r("881a"),i=r.n(t);i.a},"643b":function(e,n,r){"use strict";(function(e,n){var t=r("47a9");r("d381");t(r("3240"));var i=t(r("a8bf"));e.__webpack_require_UNI_MP_PLUGIN__=r,n(i.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},7904:function(e,n,r){"use strict";r.r(n);var t=r("9fdf"),i=r.n(t);for(var a in t)["default"].indexOf(a)<0&&function(e){r.d(n,e,(function(){return t[e]}))}(a);n["default"]=i.a},"881a":function(e,n,r){},"9fdf":function(e,n,r){"use strict";(function(e){var t=r("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=t(r("e78f")),a={components:{uniPopup:function(){r.e("components/uni-popup/uni-popup").then(function(){return resolve(r("d745"))}.bind(null,r)).catch(r.oe)}},mixins:[i.default],data:function(){return{isSub:!1,isIphoneX:!1,couponPopList:[],curIndex:0,isDescAnimating:!1,scaleX:(634/540).toFixed(4),scaleY:(378/330).toFixed(4),swiperConfig:{indicatorDots:!1,indicatorColor:"rgba(255, 255, 255, .4)",indicatorActiveColor:"rgba(255, 255, 255, 1)",interval:3e3,duration:300,circular:!1,previousMargin:"58rpx",nextMargin:"58rpx"},levelList:[],levelId:0,cardType:{week:{name:"周卡",unit:"周"},month:{name:"月卡",unit:"月"},quarter:{name:"季卡",unit:"季"},year:{name:"年卡",unit:"年"}},choiceIndex:0,outTradeNo:"",agreement:null}},computed:{listLen:function(){return this.levelList.length},currCard:function(){if(this.levelList[this.curIndex]){var e=this.levelList[this.curIndex],n=e.charge_rule?JSON.parse(e.charge_rule):{};return e.charge_rule_arr=[],Object.keys(n).forEach((function(r){e.charge_rule_arr.push({key:r,value:n[r]})})),e}}},onLoad:function(){var e=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?this.getCardList():this.$nextTick((function(){e.$refs.login.open("/pages_tool/member/card_buy")})),this.getAgreement()},onShow:function(){},watch:{storeToken:function(e,n){e&&this.getCardList()}},methods:{swiperChange:function(e){this.curIndex=e.detail.current,this.choiceIndex=0,this.isDescAnimating=!0},animationfinish:function(e){this.isDescAnimating=!1},getCardList:function(){var e=this;this.$api.sendRequest({url:"/supermember/api/membercard/lists",success:function(n){if(0==n.code&&n.data){e.levelList=n.data,e.levelId=e.memberInfo.member_level;for(var r=0;r<e.levelList.length;r++)if(e.levelList[r].level_id==e.levelId){e.curIndex=r;break}}else e.$util.showToast({title:n.message});e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},choice:function(e){this.choiceIndex=e},create:function(){var n=this;this.memberInfo.member_level_type&&this.memberInfo.member_level!=this.currCard.level_id?e.showModal({title:"提示",content:"您有尚未过期的会员卡，再次购卡会覆盖掉之前的卡，是否继续？",success:function(e){e.confirm&&n.$refs.choosePaymentPopup.open()}}):this.$refs.choosePaymentPopup.open()},toPay:function(){var n=this;this.isSub||(this.isSub=!0,this.$api.sendRequest({url:"/supermember/api/ordercreate/create",data:{level_id:this.currCard.level_id,period_unit:this.currCard.charge_rule_arr[this.choiceIndex].key},success:function(r){r.data&&0==r.code?(n.outTradeNo=r.data.out_trade_no,e.setStorageSync("paySource","membercard"),n.$refs.choosePaymentPopup.getPayInfo(n.outTradeNo)):(n.isSub=!1,n.$util.showToast({title:r.message}))}}))},headimgError:function(){this.memberInfo.headimg=this.$util.getDefaultImage().head},openExplainPopup:function(){this.$refs.explainPopup.open()},closeExplainPopup:function(){this.$refs.explainPopup.close()},getAgreement:function(){var e=this;this.$api.sendRequest({url:"/supermember/api/membercard/agreement",success:function(n){0==n.code&&n.data&&""!=n.data.title&&""!=n.data.content&&(e.agreement=n.data)}})}},onBackPress:function(e){return"navigateBack"!==e.from&&(this.$util.redirectTo("/pages/member/index"),!0)}};n.default=a}).call(this,r("df3c")["default"])},a8bf:function(e,n,r){"use strict";r.r(n);var t=r("eee2"),i=r("7904");for(var a in i)["default"].indexOf(a)<0&&function(e){r.d(n,e,(function(){return i[e]}))}(a);r("439d");var o=r("828b"),u=Object(o["a"])(i["default"],t["b"],t["c"],!1,null,null,null,!1,t["a"],void 0);n["default"]=u.exports},eee2:function(e,n,r){"use strict";r.d(n,"b",(function(){return i})),r.d(n,"c",(function(){return a})),r.d(n,"a",(function(){return t}));var t={uniPopup:function(){return r.e("components/uni-popup/uni-popup").then(r.bind(null,"d745"))},nsPayment:function(){return r.e("components/ns-payment/ns-payment").then(r.bind(null,"7aec"))},nsEmpty:function(){return r.e("components/ns-empty/ns-empty").then(r.bind(null,"52a6"))},nsLogin:function(){return Promise.all([r.e("common/vendor"),r.e("components/ns-login/ns-login")]).then(r.bind(null,"2910"))},loadingCover:function(){return r.e("components/loading-cover/loading-cover").then(r.bind(null,"c003"))}},i=function(){var e=this,n=e.$createElement,r=(e._self._c,e.levelList.length),t=r?e.$util.img("public/uniapp/level/card-top-bg.png"):null,i=r&&e.memberInfo.headimg?e.$util.img(e.memberInfo.headimg):null,a=r&&!e.memberInfo.headimg?e.$util.getDefaultImage():null,o=r&&e.memberInfo.level_expire_time>0?e.$util.timeStampTurnTime(e.memberInfo.level_expire_time):null,u=r?e.__map(e.levelList,(function(n,r){var t=e.__get_orig(n),i=e.levelList.length,a=n&&n["level_picture"]?e.$util.img(n["level_picture"]):null;return{$orig:t,g5:i,g6:a}})):null,c=r?e.__map(e.currCard.charge_rule_arr,(function(n,r){var t=e.__get_orig(n),i=e.currCard.charge_rule_arr.length,a=e.$util.img("public/uniapp/level/card-icon.png"),o=e.$lang("common.currencySymbol");return{$orig:t,g7:i,g8:a,m0:o}})):null,l=r&&(e.currCard.is_free_shipping||e.currCard.consume_discount<100||e.currCard.point_feedback>0)&&e.currCard.point_feedback>0?parseFloat(e.currCard.point_feedback):null,s=r&&(e.currCard.is_free_shipping||e.currCard.consume_discount<100||e.currCard.point_feedback>0)&&(""!=e.currCard.send_coupon||e.currCard.send_point>0||e.currCard.send_balance>0)&&e.currCard.send_balance>0?parseFloat(e.currCard.send_balance):null,d=r&&(e.currCard.is_free_shipping||e.currCard.consume_discount<100||e.currCard.point_feedback>0)&&(""!=e.currCard.send_coupon||e.currCard.send_point>0||e.currCard.send_balance>0)&&""!=e.currCard.send_coupon?e.currCard.send_coupon.split(",").length:null,p=r?e.currCard.charge_rule_arr.length:null,m=r&&p?e.$lang("common.currencySymbol"):null,f=r?e.currCard.charge_rule_arr.length:null;e._isMounted||(e.e0=function(n){return e.$util.redirectTo("/pages_tool/member/card_agreement")}),e.$mp.data=Object.assign({},{$root:{g0:r,g1:t,g2:i,g3:a,g4:o,l0:u,l1:c,m1:l,m2:s,g9:d,g10:p,m3:m,g11:f}})},a=[]}},[["643b","common/runtime","common/vendor"]]]);