require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/cancellation"],{"1d2b":function(e,t,n){"use strict";var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;i(n("0817"));t.default={data:function(){return{agreement:{},isSelect:!1}},onLoad:function(e){this.storeToken?this.getCancelAgreement():this.$util.redirectTo("/pages_tool/login/index")},methods:{getCancelAgreement:function(){var e=this;this.$api.sendRequest({url:"/membercancel/api/membercancel/agreement",success:function(t){t.code>=0&&(e.agreement=t.data)}})},changeSelect:function(){this.isSelect=1!=this.isSelect},next:function(){this.isSelect?this.$util.redirectTo("/pages_tool/member/assets"):this.$util.showToast({title:"请先勾选同意协议"})}}}},"2b6d":function(e,t,n){"use strict";n.r(t);var i=n("6b1b"),a=n("d9f7");for(var c in a)["default"].indexOf(c)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(c);n("e3a8");var r=n("828b"),o=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"7af85f87",null,!1,i["a"],void 0);t["default"]=o.exports},"32b7":function(e,t,n){},"6b1b":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return c})),n.d(t,"a",(function(){return i}));var i={nsMpHtml:function(){return n.e("components/ns-mp-html/ns-mp-html").then(n.bind(null,"d108"))}},a=function(){var e=this.$createElement;this._self._c},c=[]},"6b21":function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("d381");i(n("3240"));var a=i(n("2b6d"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},d9f7:function(e,t,n){"use strict";n.r(t);var i=n("1d2b"),a=n.n(i);for(var c in i)["default"].indexOf(c)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(c);t["default"]=a.a},e3a8:function(e,t,n){"use strict";var i=n("32b7"),a=n.n(i);a.a}},[["6b21","common/runtime","common/vendor"]]]);