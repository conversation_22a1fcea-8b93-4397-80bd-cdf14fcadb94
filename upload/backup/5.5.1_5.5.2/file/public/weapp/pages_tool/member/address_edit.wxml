<page-meta page-style="{{themeColor}}"></page-meta><view class="address-edit-content"><view class="edit-wrap"><view class="tip">地址信息</view><view class="edit-item"><text class="tit">{{''+$root.m0+''}}<text>*</text></text><input class="uni-input" type="text" placeholder-class="placeholder-class" placeholder="{{$root.m1}}" maxlength="30" name="name" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['formData']]]]]}}" value="{{formData.name}}" bindinput="__e"/></view><view class="edit-item"><text class="tit">{{''+$root.m2+''}}<text>*</text></text><input class="uni-input" type="number" placeholder-class="placeholder-class" placeholder="{{$root.m3}}" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['formData']]]]]}}" value="{{formData.mobile}}" bindinput="__e"/></view><view class="edit-item"><text class="tit">{{$root.m4}}</text><input class="uni-input" type="text" placeholder-class="placeholder-class" placeholder="{{$root.m5}}" maxlength="20" data-event-opts="{{[['input',[['__set_model',['$0','telephone','$event',[]],['formData']]]]]}}" value="{{formData.telephone}}" bindinput="__e"/></view><block wx:if="{{localType==2}}"><block><view class="edit-item"><text class="tit">{{''+$root.m6+''}}<text>*</text></text><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="{{['text_inp',(!formData.full_address)?'empty':'',(!formData.full_address)?'color-tip':'']}}" bindtap="__e">{{''+(formData.full_address?formData.full_address:'请选择省市区县')+''}}</view><text data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="padding-left iconfont icon-location" bindtap="__e"></text></view><view class="edit-item"><text class="tit">{{''+$root.m7+''}}<text>*</text></text><text data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="{{['select-address',(!formData.address)?'empty':'',(!formData.address)?'color-tip':'']}}" bindtap="__e">{{''+(formData.address?formData.address:$root.m8)+''}}</text></view><block wx:if="{{isEdit}}"><view class="edit-item"><text class="tit">{{''+$root.m9+''}}<text>*</text></text><input class="uni-input" type="text" placeholder-class="placeholder-class" placeholder="{{$root.m10}}" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','house','$event',[]],['formData']]]]]}}" value="{{formData.house}}" bindinput="__e"/></view></block></block></block><block wx:else><block><view class="edit-item"><text class="tit">{{''+$root.m11+''}}<text>*</text></text><pick-regions vue-id="2913beec-1" default-regions="{{defaultRegions}}" data-event-opts="{{[['^getRegions',[['handleGetRegions']]]]}}" bind:getRegions="__e" bind:__l="__l" vue-slots="{{['default']}}"><text class="{{['select-address','',(!formData.full_address)?'empty':'',(!formData.full_address)?'color-tip':'']}}">{{''+(formData.full_address?formData.full_address:'请选择省市区县')+''}}</text></pick-regions></view><view class="edit-item"><text class="tit">{{''+$root.m12+''}}<text>*</text></text><input class="uni-input" type="text" placeholder-class="placeholder-class" placeholder="{{$root.m13}}" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['formData']]]]]}}" value="{{formData.address}}" bindinput="__e"/></view></block></block></view><block wx:if="{{isOpenIdentify}}"><view class="identify-area"><view class="tip">智能识别地址</view><view class="paste-address"><view class="sample-area">示例：小红152********山西省太原市小店区**路**号</view><view class="intelligent-identify"><textarea class="input-addr" placeholder="粘贴或输入文本，智能识别姓名、电话和地址" name id cols="30" rows="10" data-event-opts="{{[['input',[['__set_model',['','pasteAddress','$event',[]]]]]]}}" value="{{pasteAddress}}" bindinput="__e"></textarea><view class="action-area"><view data-event-opts="{{[['tap',[['identifyAddr']]]]}}" class="identify-btn color-base-bg" bindtap="__e">识别</view></view></view></view></view></block><view class="btn"><button class="add" type="primary" data-event-opts="{{[['tap',[['saveAddress',['$event']]]]]}}" bindtap="__e">{{$root.m14}}</button></view><loading-cover class="vue-ref" vue-id="2913beec-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>