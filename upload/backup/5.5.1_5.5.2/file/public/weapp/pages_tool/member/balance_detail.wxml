<page-meta page-style="{{themeColor}}"></page-meta><view><view class="tab color-bg"><view class="tab-left"><picker range="{{monthData}}" value="{{monthIndex}}" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{''+monthData[monthIndex]+''}}<text class="iconfont icon-iconangledown"></text></view></picker></view><view class="tab-right"><picker class="picker" value="{{balanceIndex}}" range="{{balanceType}}" range-key="label" data-event-opts="{{[['change',[['bindPickerChange',['$event']]]]]}}" bindchange="__e"><text class="desc uni-input">{{balanceType[balanceIndex].label}}</text><text class="iconfont icon-iconangledown"></text></picker></view></view><block wx:if="{{$root.g0}}"><mescroll-uni class="vue-ref" bind:getData="__e" vue-id="138f600b-1" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g1>0}}"><block><view class="detailed-wrap"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="balances"><view data-event-opts="{{[['tap',[['toFromDetail',['$0'],[[['dataList','',index]]]]]]]}}" class="balances-info" bindtap="__e"><text class="title">{{item.$orig.type_name}}</text><text>{{item.$orig.remark}}</text><text>{{item.g2}}</text></view><view class="balances-num"><text class="{{[item.$orig.account_data>0?'color-base-text':'']}}">{{item.$orig.account_data>0?'+'+item.$orig.account_data:item.$orig.account_data}}</text></view></view></block></view></block></block><block wx:else><block><ns-empty vue-id="{{('138f600b-2')+','+('138f600b-1')}}" isIndex="{{false}}" text="暂无余额明细" bind:__l="__l"></ns-empty></block></block></view></mescroll-uni></block><ns-login class="vue-ref" vue-id="138f600b-3" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="138f600b-4" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>