<page-meta page-style="{{themeColor}}" class="data-v-b4bed4d8"></page-meta><view class="data-v-b4bed4d8"><block wx:if="{{storeToken}}"><mescroll-uni bind:getData="__e" vue-id="602bf9e9-1" data-ref="mescroll" data-event-opts="{{[['^getData',[['getListData']]]]}}" class="data-v-b4bed4d8 vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-b4bed4d8"><view class="address-list data-v-b4bed4d8"><block wx:if="{{$root.g0!==0}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="address-item data-v-b4bed4d8"><block wx:if="{{localType==2&&item.$orig.local_data}}"><view class="address-item-top data-v-b4bed4d8"><view class="address-item-left data-v-b4bed4d8"><view class="address-top-info data-v-b4bed4d8"><view class="address-name data-v-b4bed4d8">{{item.$orig.name}}</view><view class="address-tel data-v-b4bed4d8">{{item.$orig.mobile}}</view><block wx:if="{{localType==2&&item.$orig.local_data}}"><view class="address-status data-v-b4bed4d8">{{item.$orig.local_data}}</view></block></view><view class="address-info data-v-b4bed4d8">{{item.$orig.full_address+item.$orig.address}}</view></view><view data-event-opts="{{[['tap',[['addAddress',['edit','$0'],[[['addressList','',index,'id']]]]]]]}}" class="address-item-edit data-v-b4bed4d8" bindtap="__e">{{''+item.m0+''}}</view></view></block><block wx:else><view data-event-opts="{{[['tap',[['setDefault',['$0'],[[['addressList','',index,'id']]]]]]]}}" class="address-item-top data-v-b4bed4d8" bindtap="__e"><view class="address-item-left data-v-b4bed4d8"><view class="address-top-info data-v-b4bed4d8"><view class="address-name data-v-b4bed4d8">{{item.$orig.name}}</view><view class="address-tel data-v-b4bed4d8">{{item.$orig.mobile}}</view></view><view class="address-info data-v-b4bed4d8">{{item.$orig.full_address+item.$orig.address}}</view></view><view data-event-opts="{{[['tap',[['addAddress',['edit','$0'],[[['addressList','',index,'id']]]]]]]}}" class="address-item-edit data-v-b4bed4d8" catchtap="__e">{{''+item.m1+''}}</view></view></block><view class="address-item-bottom data-v-b4bed4d8"><view data-event-opts="{{[['tap',[['setDefault',['$0','$1'],[[['addressList','',index,'id']],[['addressList','',index,'is_default']]]]]]]}}" class="address-default data-v-b4bed4d8" bindtap="__e"><block wx:if="{{localType==2&&item.$orig.local_data}}"><text class="default data-v-b4bed4d8">设为默认地址</text></block><block wx:else><text class="default data-v-b4bed4d8">设为默认地址</text></block><block wx:if="{{item.$orig.is_default==1}}"><switch style="transform:scale(0.7);" checked="{{true}}" disabled="{{true}}" color="{{themeStyle.main_color}}" class="data-v-b4bed4d8"></switch></block><block wx:else><switch style="transform:scale(0.7);" color="{{themeStyle.main_color}}" class="data-v-b4bed4d8"></switch></block></view><view class="address-btn data-v-b4bed4d8"><block wx:if="{{item.$orig.is_default!=1}}"><text data-event-opts="{{[['tap',[['deleteAddress',['$0','$1'],[[['addressList','',index,'id']],[['addressList','',index,'is_default']]]]]]]}}" class="delete data-v-b4bed4d8" bindtap="__e"><text class="iconfont icon-icon7 data-v-b4bed4d8"></text></text></block></view></view></view></block></block><block wx:if="{{$root.g1}}"><view class="empty-box data-v-b4bed4d8"><image src="{{$root.g2}}" mode="widthFix" class="data-v-b4bed4d8"></image><view class="tips data-v-b4bed4d8">暂无收货地址，请添加</view><button class="add-address data-v-b4bed4d8" type="primary" data-event-opts="{{[['tap',[['addAddress',['add']]]]]}}" bindtap="__e">{{$root.m2}}</button><block wx:if="{{local!=1}}"><button class="get-address data-v-b4bed4d8" type="primary" data-event-opts="{{[['tap',[['getChooseAddress']]]]}}" bindtap="__e">{{$root.m3}}</button></block></view></block></view></view></mescroll-uni></block><block wx:if="{{$root.g3!==0}}"><view class="btn-add data-v-b4bed4d8"><block wx:if="{{local!=1}}"><view data-event-opts="{{[['tap',[['getChooseAddress']]]]}}" class="wx-add data-v-b4bed4d8" bindtap="__e"><text class="data-v-b4bed4d8">{{$root.m4}}</text></view></block><button class="add-address data-v-b4bed4d8" type="primary" data-event-opts="{{[['tap',[['addAddress',['add']]]]]}}" bindtap="__e"><text class="iconfont icon-add1 data-v-b4bed4d8"></text>{{''+$root.m5+''}}</button></view></block><ns-login vue-id="602bf9e9-2" data-ref="login" class="data-v-b4bed4d8 vue-ref" bind:__l="__l"></ns-login><loading-cover vue-id="602bf9e9-3" data-ref="loadingCover" class="data-v-b4bed4d8 vue-ref" bind:__l="__l"></loading-cover></view>