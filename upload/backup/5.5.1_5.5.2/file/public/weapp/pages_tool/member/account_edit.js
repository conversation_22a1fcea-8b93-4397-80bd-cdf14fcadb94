require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/account_edit"],{"0e14":function(a,t,e){"use strict";(function(a){var i=e("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(e("fe8d")),n={data:function(){return{formData:{realname:"",mobile:"",withdraw_type:"",bank_account:"",branch_bank_name:""},payList:[],index:0,flag:!1,transferType:[],accountInfo:null,back:"",type:"member"}},onLoad:function(a){a.id&&(this.formData.id=a.id),a.back&&(this.back=a.back),a.type&&(this.type=a.type)},onShow:function(){this.formData.id?this.getAccountDetail():this.getTransferType(),this.formData.id?a.setNavigationBarTitle({title:"编辑账户"}):a.setNavigationBarTitle({title:"新增账户"})},methods:{getAccountDetail:function(){var a=this;this.$api.sendRequest({url:"/api/memberbankaccount/info",data:{id:this.formData.id},success:function(t){0==t.code&&t.data&&(a.accountInfo=t.data,a.formData.realname=t.data.realname,a.formData.mobile=t.data.mobile,a.formData.bank_account=t.data.bank_account,a.formData.branch_bank_name=t.data.branch_bank_name,a.formData.withdraw_type=t.data.withdraw_type),a.getTransferType(),a.$refs.loadingCover&&a.$refs.loadingCover.hide()},fail:function(t){a.getTransferType(),a.$refs.loadingCover&&a.$refs.loadingCover.hide()}})},getTransferType:function(){var a=this;this.payList=[];var t="member"==this.type?"/api/memberwithdraw/transferType":"/fenxiao/api/withdraw/transferType";this.$api.sendRequest({url:t,success:function(t){if(t.code>=0&&t.data){for(var e in delete t.data.balance,a.transferType=t.data,a.transferType)a.payList.push(a.transferType[e]);if(1==a.payList.length&&"银行卡"==a.payList[0]&&(a.formData.withdraw_type="bank"),a.payList.reverse(),!a.formData.id&&a.$refs.loadingCover&&a.$refs.loadingCover.hide(),a.accountInfo&&-1==a.$util.inArray(a.accountInfo.withdraw_type_name,a.payList)&&a.payList.push(a.accountInfo.withdraw_type_name),a.payList.length&&a.accountInfo&&(a.index=a.$util.inArray(a.accountInfo.withdraw_type_name,a.payList)),!a.formData.withdraw_type&&a.payList.length)switch(a.payList[0]){case"银行卡":a.formData.withdraw_type="bank";break;case"支付宝":a.formData.withdraw_type="alipay";break;case"微信零钱":a.formData.withdraw_type="wechatpay";break}}}})},bindPickerChange:function(a){this.index=a.detail.value;var t="";for(var e in this.transferType)this.transferType[e]==this.payList[this.index]&&(t=e);""!=t&&(this.formData.withdraw_type=t)},vertify:function(){var a=[{name:"realname",checkType:"required",errorMsg:"请输入姓名"},{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"},{name:"withdraw_type",checkType:"required",errorMsg:"请选择账户类型"}];"bank"==this.formData.withdraw_type&&a.push({name:"branch_bank_name",checkType:"required",errorMsg:"请输入银行名称"}),"wechatpay"!=this.formData.withdraw_type&&a.push({name:"bank_account",checkType:"required",errorMsg:"请输入提现账号"});var t=r.default.check(this.formData,a);return!!t||(this.$util.showToast({title:r.default.error}),this.flag=!1,!1)},saveAccount:function(){var a=this;if(!this.flag&&(this.flag=!0,this.vertify())){var t=this.formData.id?"edit":"add";this.$api.sendRequest({url:"/api/memberbankaccount/"+t,data:{id:this.formData.id,realname:this.formData.realname,mobile:this.formData.mobile,withdraw_type:this.formData.withdraw_type,bank_account:this.formData.bank_account,branch_bank_name:this.formData.branch_bank_name},success:function(t){0==t.code?(a.formData.id?a.$util.showToast({title:"修改成功"}):a.$util.showToast({title:"添加成功"}),""!=a.back?a.$util.redirectTo(a.back,{},a.redirect):a.$util.redirectTo("/pages_tool/member/account")):(a.flag=!1,a.$util.showToast({title:t.message}))},fail:function(t){a.flag=!1}})}}}};t.default=n}).call(this,e("df3c")["default"])},"271a":function(a,t,e){},"3c05":function(a,t,e){"use strict";e.r(t);var i=e("ddc3"),r=e("6228");for(var n in r)["default"].indexOf(n)<0&&function(a){e.d(t,a,(function(){return r[a]}))}(n);e("e721");var o=e("828b"),c=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=c.exports},6228:function(a,t,e){"use strict";e.r(t);var i=e("0e14"),r=e.n(i);for(var n in i)["default"].indexOf(n)<0&&function(a){e.d(t,a,(function(){return i[a]}))}(n);t["default"]=r.a},"6ba7":function(a,t,e){"use strict";(function(a,t){var i=e("47a9");e("d381");i(e("3240"));var r=i(e("3c05"));a.__webpack_require_UNI_MP_PLUGIN__=e,t(r.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},ddc3:function(a,t,e){"use strict";e.d(t,"b",(function(){return r})),e.d(t,"c",(function(){return n})),e.d(t,"a",(function(){return i}));var i={loadingCover:function(){return e.e("components/loading-cover/loading-cover").then(e.bind(null,"c003"))}},r=function(){var a=this.$createElement,t=(this._self._c,this.$lang("name")),e=this.$lang("mobilePhone"),i=this.$lang("accountType"),r=this.$lang("save");this.$mp.data=Object.assign({},{$root:{m0:t,m1:e,m2:i,m3:r}})},n=[]},e721:function(a,t,e){"use strict";var i=e("271a"),r=e.n(i);r.a}},[["6ba7","common/runtime","common/vendor"]]]);