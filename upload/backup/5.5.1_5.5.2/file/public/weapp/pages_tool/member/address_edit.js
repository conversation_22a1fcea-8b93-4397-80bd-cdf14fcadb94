require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/address_edit"],{"466a":function(a,e,t){"use strict";t.r(e);var i=t("d4c0"),s=t("a921");for(var d in s)["default"].indexOf(d)<0&&function(a){t.d(e,a,(function(){return s[a]}))}(d);t("cb52");var o=t("828b"),r=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=r.exports},"89a6":function(a,e,t){},"8db6":function(a,e,t){"use strict";(function(a){var i=t("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=i(t("fe8d")),d=(i(t("2f8f")),{components:{pickRegions:function(){Promise.all([t.e("common/vendor"),t.e("components/pick-regions/pick-regions")]).then(function(){return resolve(t("04c1"))}.bind(null,t)).catch(t.oe)}},data:function(){return{formData:{id:0,name:"",mobile:"",telephone:"",province_id:"",city_id:"",district_id:"",community_id:"",address:"",full_address:"",house:"",latitude:0,longitude:0,is_default:1},pasteAddress:"",address:"",addressValue:"",back:"",redirect:"redirectTo",flag:!1,defaultRegions:[],localType:1,isEdit:!1,webSign:!1,isOpenIdentify:!1}},onLoad:function(e){if(e.back&&(this.back=e.back),e.redirect&&(this.redirect=e.redirect),e.type&&(this.localType=e.type),e.id&&!e.name)this.formData.id=e.id,this.getAddressDetail();else if(e.name){this.isEdit=!0,this.webSign=!0,a.getStorageSync("addressInfo")&&(this.formData=a.getStorageSync("addressInfo")),this.formData.address=e.name,this.localType=2,this.getAddress(e.latng);var t=this.getQueryVariable("latng").split(",");this.formData.latitude=t[0],this.formData.longitude=t[1],this.formData.house=""}else this.$refs.loadingCover&&this.$refs.loadingCover.hide();this.getMapConfig()},onBackPress:function(){a.setStorageSync("addressInfo","")},onShow:function(){this.formData.id?a.setNavigationBarTitle({title:"编辑收货地址"}):a.setNavigationBarTitle({title:"新增收货地址"})},onReady:function(){this.$refs.loadingCover.hide()},onHide:function(){this.flag=!1},methods:{getMapConfig:function(){var a=this;this.$api.sendRequest({url:"/api/config/geMapConfig",success:function(e){e.data.key?a.isOpenIdentify=!0:a.isOpenIdentify=!1},fail:function(a){}})},identifyAddr:function(){var a=this;this.pasteAddress?this.$api.sendRequest({url:"/api/address/analysesAddress",data:{address:this.pasteAddress},success:function(e){if(e.code>=0){e.data.name&&(a.formData.name=e.data.name),e.data.mobile&&(a.formData.mobile=e.data.mobile),(e.data.province_name||e.data.city_name||e.data.district_name)&&(a.formData.full_address="",a.formData.full_address+=e.data.province_name?e.data.province_name:"",a.formData.full_address+=e.data.city_name?"-"+e.data.city_name:"",a.formData.full_address+=e.data.district_name?"-"+e.data.district_name:"");var t=a.addressValue.split("-");e.data.province_id!=t[0]||e.data.city_id!=t[1]||e.data.district_id!=t[2]?(a.addressValue=e.data.province_id+"-"+e.data.city_id+"-"+e.data.district_id,a.formData.address=e.data.detail):e.data.detail&&(a.formData.address=e.data.detail),a.formData.latitude=e.data.lat||"",a.formData.longitude=e.data.lng||""}else a.$util.showToast({title:e.message})},fail:function(e){a.$util.showToast({title:e.message})}}):this.$util.showToast({title:"请粘贴或输入文本信息"})},getAddressDetail:function(){var a=this;this.$api.sendRequest({url:"/api/memberaddress/info",data:{id:this.formData.id},success:function(e){var t=e.data;null!=t&&(a.formData.name=t.name,a.formData.mobile=t.mobile,a.formData.telephone=t.telephone,a.formData.address=t.address,a.formData.full_address=t.full_address,a.formData.latitude=t.latitude,a.formData.longitude=t.longitude,a.formData.is_default=t.is_default,a.localType=t.type,a.defaultRegions=[t.province_id,t.city_id,t.district_id],a.addressValue+=void 0!=t.province_id?t.province_id:"",a.addressValue+=void 0!=t.city_id?"-"+t.city_id:"",a.addressValue+=void 0!=t.district_id?"-"+t.district_id:""),a.$refs.loadingCover&&a.$refs.loadingCover.hide()},fail:function(e){a.$refs.loadingCover&&a.$refs.loadingCover.hide()}})},getAddress:function(a){var e=this;this.$api.sendRequest({url:"/api/memberaddress/tranAddressInfo",data:{latlng:a},success:function(a){0==a.code?(e.formData.full_address="",e.formData.full_address+=void 0!=a.data.province?a.data.province:"",e.formData.full_address+=void 0!=a.data.city?"-"+a.data.city:"",e.formData.full_address+=void 0!=a.data.district?"-"+a.data.district:"",e.addressValue="",e.addressValue+=void 0!=a.data.province_id?a.data.province_id:"",e.addressValue+=void 0!=a.data.city_id?"-"+a.data.city_id:"",e.addressValue+=void 0!=a.data.district_id?"-"+a.data.district_id:""):e.showToast({title:"数据有误"})}})},handleGetRegions:function(a){this.formData.full_address="",this.formData.full_address+=void 0!=a[0]?a[0].label:"",this.formData.full_address+=void 0!=a[1]?"-"+a[1].label:"",this.formData.full_address+=void 0!=a[2]?"-"+a[2].label:"",this.addressValue="",this.addressValue+=void 0!=a[0]?a[0].value:"",this.addressValue+=void 0!=a[1]?"-"+a[1].value:"",this.addressValue+=void 0!=a[2]?"-"+a[2].value:""},selectAddress:function(){var e=this;a.chooseLocation({success:function(a){e.formData.latitude=a.latitude,e.formData.longitude=a.longitude,e.formData.address=a.name,e.getAddress(a.latitude+","+a.longitude),e.isEdit=!0},fail:function(e){a.getSetting({success:function(e){var t=e.authSetting;t["scope.userLocation"]||a.showModal({title:"是否授权当前位置",content:"需要获取您的地理位置，请确认授权，否则地图功能将无法使用",success:function(t){t.confirm?a.openSetting({success:function(t){!0===t.authSetting["scope.userLocation"]&&(this.$util.showToast({title:"授权成功"}),setTimeout((function(){var t=this;a.chooseLocation({success:function(a){t.formData.latitude=e.latitude,t.formData.longitude=e.longitude,t.formData.address=e.name,t.getAddress(e.latitude+","+e.longitude),t.isEdit=!0}})}),1e3))}}):this.$util.showToast({title:"授权失败"})}})}})}})},getQueryVariable:function(a){for(var e=window.location.search.substring(1),t=e.split("&"),i=0;i<t.length;i++){var s=t[i].split("=");if(s[0]==a)return s[1]}return!1},vertify:function(){this.formData.name=this.formData.name.trim(),this.formData.mobile=this.formData.mobile.trim(),this.formData.address=this.formData.address.trim();var a=[{name:"name",checkType:"required",errorMsg:"请输入姓名"},{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"},{name:"full_address",checkType:"required",errorMsg:"请选择省市区县"},{name:"address",checkType:"required",errorMsg:"详细地址不能为空"}];this.isEdit&&a.push({name:"house",checkType:"required",errorMsg:"门牌不能为空"});var e=s.default.check(this.formData,a);if(e){var t=this.addressValue.split("-");return t[0]?t[1]?!!t[2]||(this.$util.showToast({title:"请选择区"}),this.flag=!1,!1):(this.$util.showToast({title:"请选择市"}),this.flag=!1,!1):(this.$util.showToast({title:"请选择省"}),this.flag=!1,!1)}return this.$util.showToast({title:s.default.error}),this.flag=!1,!1},saveAddress:function(){var e=this;if(!this.flag&&(this.flag=!0,this.vertify())){var t=this.addressValue.split("-"),i={},s="";i={name:this.formData.name,mobile:this.formData.mobile,telephone:this.formData.telephone,province_id:t[0],city_id:t[1],district_id:t[2]?t[2]:"",community_id:0,address:this.isEdit?this.formData.address+this.formData.house:this.formData.address,full_address:this.formData.full_address,latitude:1==this.localType?"":this.formData.latitude,longitude:1==this.localType?"":this.formData.longitude,is_default:this.formData.is_default,type:this.localType},s="add",this.formData.id&&(s="edit",i.id=this.formData.id,""!=this.back&&(i.is_default=1)),this.$api.sendRequest({url:"/api/memberaddress/"+s,data:i,success:function(t){e.flag=!1,0==t.code?(""!=e.back?a.navigateBack({delta:2}):(e.$util.showToast({title:t.message}),a.navigateBack({delta:1})),a.removeStorageSync("addressInfo")):e.$util.showToast({title:t.message})},fail:function(a){e.flag=!1}})}}}});e.default=d}).call(this,t("df3c")["default"])},a921:function(a,e,t){"use strict";t.r(e);var i=t("8db6"),s=t.n(i);for(var d in i)["default"].indexOf(d)<0&&function(a){t.d(e,a,(function(){return i[a]}))}(d);e["default"]=s.a},cb52:function(a,e,t){"use strict";var i=t("89a6"),s=t.n(i);s.a},d4c0:function(a,e,t){"use strict";t.d(e,"b",(function(){return s})),t.d(e,"c",(function(){return d})),t.d(e,"a",(function(){return i}));var i={pickRegions:function(){return Promise.all([t.e("common/vendor"),t.e("components/pick-regions/pick-regions")]).then(t.bind(null,"04c1"))},loadingCover:function(){return t.e("components/loading-cover/loading-cover").then(t.bind(null,"c003"))}},s=function(){var a=this,e=a.$createElement,t=(a._self._c,a.$lang("consignee")),i=a.$lang("consigneePlaceholder"),s=a.$lang("mobile"),d=a.$lang("mobilePlaceholder"),o=a.$lang("telephone"),r=a.$lang("telephonePlaceholder"),n=2==a.localType?a.$lang("receivingCity"):null,l=2==a.localType?a.$lang("address"):null,c=2!=a.localType||a.formData.address?null:a.$lang("addressPlaceholder"),u=2==a.localType&&a.isEdit?a.$lang("house"):null,f=2==a.localType&&a.isEdit?a.$lang("housePlaceholder"):null,m=2!=a.localType?a.$lang("receivingCity"):null,h=2!=a.localType?a.$lang("address"):null,g=2!=a.localType?a.$lang("addressPlaceholder"):null,p=a.$lang("save");a.$mp.data=Object.assign({},{$root:{m0:t,m1:i,m2:s,m3:d,m4:o,m5:r,m6:n,m7:l,m8:c,m9:u,m10:f,m11:m,m12:h,m13:g,m14:p}})},d=[]},e87c:function(a,e,t){"use strict";(function(a,e){var i=t("47a9");t("d381");i(t("3240"));var s=i(t("466a"));a.__webpack_require_UNI_MP_PLUGIN__=t,e(s.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])}},[["e87c","common/runtime","common/vendor"]]]);