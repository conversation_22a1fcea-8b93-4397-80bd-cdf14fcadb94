require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/point"],{1021:function(n,t,o){},4096:function(n,t,o){"use strict";o.r(t);var e=o("68f8"),i=o.n(e);for(var r in e)["default"].indexOf(r)<0&&function(n){o.d(t,n,(function(){return e[n]}))}(r);t["default"]=i.a},"613a":function(n,t,o){"use strict";o.d(t,"b",(function(){return i})),o.d(t,"c",(function(){return r})),o.d(t,"a",(function(){return e}));var e={nsLogin:function(){return Promise.all([o.e("common/vendor"),o.e("components/ns-login/ns-login")]).then(o.bind(null,"2910"))},loadingCover:function(){return o.e("components/loading-cover/loading-cover").then(o.bind(null,"c003"))}},i=function(){var n=this,t=n.$createElement,o=(n._self._c,n.$util.img("public/uniapp/point/point_bg.png")),e=n.$util.img("public/uniapp/point/point_detail_icon.png"),i=n.$util.img("public/uniapp/point/point_shop.png");n._isMounted||(n.e0=function(t){return n.$util.redirectTo("/pages/member/index")},n.e1=function(t){return n.$util.redirectTo("/pages_tool/member/point_detail")},n.e2=function(t){return n.$util.redirectTo("/pages_promotion/point/list")},n.e3=function(t){return n.$util.redirectTo("/pages/index/index")}),n.$mp.data=Object.assign({},{$root:{g0:o,g1:e,g2:i}})},r=[]},"68f8":function(n,t,o){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={data:function(){return{pointInfo:{point:0,totalPoint:0,totalConsumePoint:0,todayPoint:0},menuButtonBounding:{}}},onShow:function(){var n=this;this.storeToken?this.getMemberPoint():this.$nextTick((function(){n.$refs.login.open("/pages_tool/member/point")}))},onLoad:function(){this.menuButtonBounding=n.getMenuButtonBoundingClientRect()},methods:{toSign:function(){this.$util.redirectTo("/pages_tool/member/signin")},getMemberPoint:function(){var n=this;this.$api.sendRequest({url:"/api/memberaccount/point",data:{},success:function(t){0==t.code&&(n.pointInfo.point=parseInt(t.data.point),n.pointInfo.totalPoint=parseInt(t.data.point_all),n.pointInfo.totalConsumePoint=parseInt(t.data.point_use),n.pointInfo.todayPoint=parseInt(t.data.point_today)),n.$refs.loadingCover&&n.$refs.loadingCover.hide()},fail:function(t){n.$refs.loadingCover&&n.$refs.loadingCover.hide()}})}},onBackPress:function(n){return"navigateBack"!==n.from&&(this.$util.redirectTo("/pages/member/index",{},"reLaunch"),!0)},watch:{storeToken:function(n,t){n&&this.getMemberPoint()}}};t.default=o}).call(this,o("df3c")["default"])},"8ca5":function(n,t,o){"use strict";var e=o("1021"),i=o.n(e);i.a},cd42:function(n,t,o){"use strict";(function(n,t){var e=o("47a9");o("d381");e(o("3240"));var i=e(o("d6dd"));n.__webpack_require_UNI_MP_PLUGIN__=o,t(i.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},d6dd:function(n,t,o){"use strict";o.r(t);var e=o("613a"),i=o("4096");for(var r in i)["default"].indexOf(r)<0&&function(n){o.d(t,n,(function(){return i[n]}))}(r);o("8ca5");var u=o("828b"),a=Object(u["a"])(i["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);t["default"]=a.exports}},[["cd42","common/runtime","common/vendor"]]]);