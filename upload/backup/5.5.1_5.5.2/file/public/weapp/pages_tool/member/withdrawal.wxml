<page-meta page-style="{{themeColor}}"></page-meta><view><mescroll-uni class="member-point vue-ref" bind:getData="__e" vue-id="6082f78a-1" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g0}}"><block><view class="detailed-wrap"><view class="cont"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['dataList','',index,'id']]]]]]]}}" class="detailed-item" bindtap="__e"><view class="info"><view class="event">{{item.$orig.transfer_type_name}}</view><view><text class="time">{{item.g1}}</text></view></view><view class="right-wrap"><view class="num color-base-text">{{"￥"+item.$orig.apply_money}}</view><block wx:if="{{isWithdrawWechat&&item.$orig.transfer_type=='wechatpay'&&item.$orig.status==1}}"><view data-event-opts="{{[['tap',[['toTransfer',['$0'],[[['dataList','',index,'id']]]]]]]}}" class="actions" catchtap="__e"><view class="act-btn">收款</view></view></block><block wx:else><view class="status-name" style="{{(item.$orig.status==-1||item.$orig.status==-2?'color:red;':'')}}">{{item.$orig.status_name}}</view></block></view><block wx:if="{{item.$orig.status==-1}}"><view class="fail-reason">{{'拒绝原因：'+item.$orig.refuse_reason+''}}</view></block><block wx:if="{{item.$orig.status==-2}}"><view class="fail-reason">{{'失败原因：'+item.$orig.fail_reason+''}}</view></block></view></block></view></view></block></block><block wx:else><block><ns-empty vue-id="{{('6082f78a-2')+','+('6082f78a-1')}}" isIndex="{{false}}" text="暂无提现记录" bind:__l="__l"></ns-empty></block></block></view></mescroll-uni><loading-cover class="vue-ref" vue-id="6082f78a-3" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>