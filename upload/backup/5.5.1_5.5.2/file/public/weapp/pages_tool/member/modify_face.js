require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/modify_face"],{"1aa7":function(e,t,a){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{memberImg:"",newImg:"",imgurl:""}},components:{imgCropping:function(){Promise.all([a.e("common/vendor"),a.e("pages_tool/components/img-cropping/cropping")]).then(function(){return resolve(a("70be"))}.bind(null,a)).catch(a.oe)}},onShow:function(){this.storeToken?(this.memberImg=this.memberInfo.headimg,this.imgurl=this.memberInfo.headimg):this.$util.redirectTo("/pages_tool/login/index",{back:"/pages_tool/member/modify_face"},"redirectTo")},methods:{chooseImage:function(){this.$refs.imgCropping.fSelect()},myUpload:function(t){var a=this;e.request({url:this.$config.baseUrl+"/api/upload/headimgBase64",method:"POST",data:{app_type:"weapp",app_type_name:"weapp",images:t.base64,token:this.$store.state.token||""},header:{"content-type":"application/x-www-form-urlencoded;application/json"},dataType:"json",responseType:"text",success:function(e){0==e.data.code&&(a.newImg=e.data.data.pic_path,a.imgurl=e.data.data.pic_path)},fail:function(){a.$util.showToast({title:"头像上传失败"})}})},previewImage:function(){e.previewImage({current:0,urls:this.images})},save:function(){var e=this;this.$api.sendRequest({url:"/api/member/modifyheadimg",data:{headimg:this.imgurl},success:function(t){0==t.code?(e.memberInfo.headimg=e.imgurl,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:"头像修改成功"}),setTimeout((function(){e.$util.redirectTo("/pages_tool/member/info",{},"redirectTo")}),2e3)):e.$util.showToast({title:t.message})}})},uploadFace:function(){var t=this;e.chooseImage({count:1,sizeType:["compressed"],success:function(e){var a=e.tempFilePaths;t.$api.upload({url:"/api/upload/headimg",filePath:a[0],fileType:"image",success:function(e){e.code&&(t.newImg=e.data.pic_path,t.imgurl=e.data.pic_path)}})}})}}};t.default=i}).call(this,a("df3c")["default"])},7116:function(e,t,a){},"8b7c":function(e,t,a){"use strict";(function(e,t){var i=a("47a9");a("d381");i(a("3240"));var n=i(a("915e"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(n.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"915e":function(e,t,a){"use strict";a.r(t);var i=a("930c"),n=a("b9ab");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("edc5");var u=a("828b"),c=Object(u["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=c.exports},"930c":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=(e._self._c,""==e.newImg&&e.memberImg?e.$util.img(e.memberImg):null),i=""!=e.newImg||e.memberImg?null:e.$util.getDefaultImage(),n=""!=e.newImg?e.$util.img(e.newImg):null;e._isMounted||(e.e0=function(t){e.memberImg=e.$util.getDefaultImage().head},e.e1=function(t){e.newImg=e.$util.getDefaultImage().head}),e.$mp.data=Object.assign({},{$root:{g0:a,g1:i,g2:n}})},n=[]},b9ab:function(e,t,a){"use strict";a.r(t);var i=a("1aa7"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},edc5:function(e,t,a){"use strict";var i=a("7116"),n=a.n(i);n.a}},[["8b7c","common/runtime","common/vendor"]]]);