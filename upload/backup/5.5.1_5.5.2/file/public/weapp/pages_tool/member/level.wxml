<page-meta page-style="{{themeColor}}"></page-meta><view class="member-level"><view class="level-top"><image src="{{$root.g0}}"></image></view><view class="banner-container"><block wx:if="{{memberInfo}}"><view class="memberInfo"><block wx:if="{{memberInfo.headimg}}"><image src="{{$root.g1}}" mode="aspectFill" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e"></image></block><block wx:else><image src="{{$root.g2.head}}" mode="aspectFill"></image></block><view class="member-desc"><view class="font-size-base">{{memberInfo.nickname}}</view><view class="font-size-activity-tag">{{"当前等级："+memberInfo.member_level_name}}</view></view><view data-event-opts="{{[['tap',[['growthRules',['$event']]]]]}}" class="growth-rules font-size-tag" bindtap="__e"><text class="iconfont icon-wenhao font-size-tag"></text>成长规则</view></view></block><swiper class="margin-bottom" style="{{'width:'+('100vw')+';'+('height:'+('390rpx')+';')}}" indicator-dots="{{swiperConfig.indicatorDots}}" indicator-color="{{swiperConfig.indicatorColor}}" indicator-active-color="{{swiperConfig.indicatorActiveColor}}" autoplay="{{false}}" interval="{{swiperConfig.interval}}" duration="{{swiperConfig.duration}}" circular="{{swiperConfig.circular}}" previous-margin="{{swiperConfig.previousMargin}}" next-margin="{{swiperConfig.nextMargin}}" current="{{curIndex}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]],['animationfinish',[['animationfinish',['$event']]]]]}}" bindchange="__e" bindanimationfinish="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="i" wx:key="i"><swiper-item class="{{[$root.g3==1?'image-container-box':'']}}"><view class="{{['image-container',curIndex===0?i===1?'item-right':i===listLen-1?'item-left':'item-center':curIndex===listLen-1?i===curIndex-1?'item-left':i===curIndex+1?'item-right':i===0?'item-right':i===listLen-2?'item-left':'item-center':i===curIndex-1?'item-left':i===curIndex+1?'item-right':'item-center']}}"><view class="slide-image" style="{{'background-size:100% 100%;background-repeat:no-repeat;'+('transform:'+(curIndex===i?'scale('+scaleX+','+scaleY+')':'scale(1,1)')+';')+('transition-duration:'+('.3s')+';')+('transition-timing-function:'+('ease')+';')}}"><block wx:if="{{item.$orig['level_picture']}}"><image src="{{item.g4}}"></image></block><block wx:else><image style="{{'background-color:'+(item.$orig['bg_color'])+';'}}"></image></block><view class="info"><view class="level-detail" style="{{'color:'+(item.$orig['level_text_color'])+';'}}">{{''+item.$orig.level_name+''}}<block wx:if="{{levelId==item.$orig.level_id}}"><text class="isnow" style="{{'color:'+(item.$orig['level_text_color'])+';'}}">当前等级</text></block></view><view class="growr-name" style="{{'color:'+(item.$orig['level_text_color'])+';'}}">当前成长值</view><view class="growr-value" style="{{'color:'+(item.$orig['level_text_color'])+';'}}">{{growth}}</view><block wx:if="{{levelId==item.$orig.level_id}}"><block><block wx:if="{{levelList[i+1]!=undefined}}"><block><ns-progress vue-id="{{'48b137ce-1-'+i}}" progress="{{levelList[i+1].rate}}" bind:__l="__l"></ns-progress><view class="residue-growr-value" style="{{'color:'+(item.$orig['level_text_color'])+';'}}">{{'再获得'+(levelList[i+1].needGrowth>0?levelList[i+1].needGrowth:0)+'成长值成为下一等级'}}</view></block></block><block wx:else><block><view class="residue-growr-value" style="{{'color:'+(item.$orig['level_text_color'])+';'}}">您现在已经是最高等级</view></block></block></block></block><block wx:else><block><block wx:if="{{item.$orig.needGrowth>0}}"><ns-progress vue-id="{{'48b137ce-2-'+i}}" progress="{{item.$orig.rate}}" bind:__l="__l"></ns-progress></block><block wx:if="{{item.$orig.needGrowth>0}}"><view class="residue-growr-value" style="{{'color:'+(item.$orig['level_text_color'])+';'}}">{{'再获得'+item.$orig.needGrowth+'成长值成为当前等级'}}</view></block></block></block></view></view></view></swiper-item></block></swiper><block wx:if="{{levelList[curIndex].is_free_shipping>0||levelList[curIndex].consume_discount>0||levelList[curIndex].point_feedback>0}}"><view class="member-equity"><view class="equity-title">会员权益</view><block wx:if="{{levelList[curIndex].is_free_shipping>0}}"><view class="equity-itme"><image src="{{$root.g5}}" mode="aspectFit"></image><view class="{{['equity-content',(levelList[curIndex].consume_discount>0)?'active':'']}}"><text>包邮服务</text><text class="equity-desc">提供商品包邮服务</text></view></view></block><block wx:if="{{levelList[curIndex].consume_discount>0}}"><view class="equity-itme"><image src="{{$root.g6}}" mode="aspectFit"></image><view class="{{['equity-content',(levelList[curIndex].point_feedback>0)?'active':'']}}"><text>享受消费折扣服务</text><block wx:if="{{levelList[curIndex].consume_discount==10}}"><text class="equity-desc">不享受任何消费折扣</text></block><block wx:else><text class="equity-desc">{{"提供"+levelList[curIndex].consume_discount+"折消费折扣"}}</text></block></view></view></block><block wx:if="{{levelList[curIndex].point_feedback>0}}"><view class="equity-itme"><image src="{{$root.g7}}" mode="aspectFit"></image><view class="equity-content"><text>享受积分回馈服务</text><text class="equity-desc">{{"提供"+levelList[curIndex].point_feedback+"倍积分回馈倍率"}}</text></view></view></block></view></block><block wx:if="{{levelList[curIndex].send_balance>0||levelList[curIndex].send_balance>0||levelList[curIndex].send_coupon}}"><view class="member-gift"><view class="gift-title">会员礼包</view><block wx:if="{{levelList[curIndex].send_point>0}}"><view class="gift-itme"><image src="{{$root.g8}}" mode="aspectFit"></image><view class="{{['gift-content',(levelList[curIndex].send_balance>0)?'active':'']}}"><text>积分礼包</text><text class="gift-desc">{{"赠送"+levelList[curIndex].send_point+"积分"}}</text></view></view></block><block wx:if="{{levelList[curIndex].send_balance>0}}"><view class="gift-itme"><image src="{{$root.g9}}" mode="aspectFit"></image><view class="{{['gift-content',(levelList[curIndex].send_coupon)?'active':'']}}"><text>红包礼包</text><text class="gift-desc">{{"赠送"+levelList[curIndex].send_balance+"元红包"}}</text></view></view></block><block wx:if="{{levelList[curIndex].send_coupon}}"><view data-event-opts="{{[['tap',[['openCoupon',['$0'],['levelList.'+curIndex+'.send_coupon']]]]]}}" class="gift-itme" bindtap="__e"><image src="{{$root.g10}}" mode="aspectFit"></image><view class="gift-content"><text>优惠券礼包</text><text class="gift-desc">{{"赠送"+levelList[curIndex].coupon_length+"张优惠券"}}</text></view></view></block></view></block></view><uni-popup class="vue-ref" vue-id="48b137ce-3" type="bottom" data-ref="couponPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="coupon-popup-box"><view data-event-opts="{{[['tap',[['closeCoupon',['$event']]]]]}}" class="coupon-popup-title" bindtap="__e">优惠券<text class="iconfont icon-close"></text></view><scroll-view class="coupon-popup-content" scroll-y="{{true}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="coupon-item"><view class="coupon-name"><text class="name">{{item.$orig.coupon_name}}</text><text class="desc"></text></view><block wx:if="{{item.$orig.type=='reward'}}"><view class="coupon-price"><text>{{item.$orig.money}}</text>元</view></block><block wx:if="{{item.$orig.type=='discount'}}"><view class="coupon-price"><text>{{item.g11}}</text>折</view></block></view></block></scroll-view></view></uni-popup><block wx:if="{{showTop}}"><to-top bind:toTop="__e" vue-id="48b137ce-4" data-event-opts="{{[['^toTop',[['scrollToTopNative']]]]}}" bind:__l="__l"></to-top></block><loading-cover class="vue-ref" vue-id="48b137ce-5" data-ref="loadingCover" bind:__l="__l"></loading-cover><ns-login class="vue-ref" vue-id="48b137ce-6" data-ref="login" bind:__l="__l"></ns-login></view>