<page-meta page-style="{{themeColor}}"></page-meta><view class="container"><view data-event-opts="{{[['tap',[['goAccount']]]]}}" class="bank-account-wrap" bindtap="__e"><block wx:if="{{bankAccountInfo.withdraw_type}}"><view class="tx-wrap"><text class="tx-to">提现到</text><block wx:if="{{bankAccountInfo.withdraw_type=='wechatpay'}}"><view class="tx-bank">微信默认钱包</view></block><block wx:else><view class="tx-bank">{{bankAccountInfo.bank_account}}</view></block><block wx:if="{{bankAccountInfo.withdraw_type=='alipay'}}"><view class="tx-img"><image src="{{$root.g0}}" mode="widthFix"></image></view></block><block wx:else><block wx:if="{{bankAccountInfo.withdraw_type=='bank'}}"><view class="tx-img"><image src="{{$root.g1}}" mode="widthFix"></image></view></block><block wx:else><block wx:if="{{bankAccountInfo.withdraw_type=='wechatpay'}}"><view class="tx-img"><image src="{{$root.g2}}" mode="widthFix"></image></view></block></block></block></view></block><block wx:else><text class="tx-to">请添加提现方式</text></block><view class="iconfont icon-right"></view></view><view class="empty-box"></view><view class="withdraw-wrap"><view class="withdraw-wrap-title">提现金额</view><view class="money-wrap"><text class="unit">{{$root.m0}}</text><input class="withdraw-money" type="digit" data-event-opts="{{[['input',[['__set_model',['','withdrawMoney','$event',[]]]]]]}}" value="{{withdrawMoney}}" bindinput="__e"/><block wx:if="{{withdrawMoney}}"><view data-event-opts="{{[['tap',[['remove',['$event']]]]]}}" class="delete" bindtap="__e"><image src="{{$root.g3}}" mode="widthFix"></image></view></block></view><view class="bootom"><view><text class="color-tip">{{"可提现余额："+$root.m1+$root.f0}}</text><text data-event-opts="{{[['tap',[['allTx',['$event']]]]]}}" class="all-tx color-base-text" bindtap="__e">全部提现</text></view></view><view class="desc"><text>{{"最小提现金额为"+$root.m2+$root.f1}}</text><text>{{"，手续费为"+(withdrawInfo.config.rate+'%')}}</text></view></view><view data-event-opts="{{[['tap',[['withdraw',['$event']]]]]}}" class="{{['btn','color-base-border','ns-gradient-otherpages-member-widthdrawal-withdrawal',(withdrawMoney==''||withdrawMoney==0)?'disabled':'']}}" bindtap="__e">提现</view><view data-event-opts="{{[['tap',[['toWithdrawal',['$event']]]]]}}" class="recoend" bindtap="__e"><view class="recoend-con">提现记录</view></view><loading-cover class="vue-ref" vue-id="3ad9280e-1" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>