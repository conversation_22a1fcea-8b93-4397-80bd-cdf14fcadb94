require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/footprint"],{"184f":function(e,t,i){"use strict";i.r(t);var n=i("4a2ee"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"27cb":function(e,t,i){"use strict";(function(e,t){var n=i("47a9");i("d381");n(i("3240"));var o=n(i("563e"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"4a2ee":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=[],o={data:function(){return{goodsList:[],current:-1,manage:!1,idArr:[],mescroll:null,isSub:!1}},onShow:function(){var e=this;this.storeToken?this.$refs.mescroll&&this.$refs.mescroll.refresh():this.$nextTick((function(){e.$refs.login.open("/pages_tool/member/footprint")}))},computed:{selected:function(){return 0==this.idArr.length},isAll:function(){return this.idArr.length==this.goodsList.length}},methods:{getListData:function(e){var t=this;this.mescroll=e,this.$api.sendRequest({url:"/api/goodsbrowse/page",data:{page:e.num,page_size:e.size},success:function(i){var o=[],r=i.message;0==i.code&&i.data?o=i.data.list:t.$util.showToast({title:r}),e.endSuccess(o.length),1==e.num&&(t.goodsList=[]),t.goodsList=t.goodsList.concat(o),n=[],t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(i){e.endErr(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},longpress:function(e){this.current=e},deleteFootprint:function(){var e=this;0!=this.idArr.length?this.isSub||(this.isSub=!0,this.$api.sendRequest({url:"/api/goodsbrowse/delete",data:{id:this.idArr.toString()},success:function(t){e.isSub=!1,t.code>=0?(e.idArr=[],e.mescroll.resetUpScroll()):e.$util.showToast({title:t.message})}})):this.$util.showToast({title:"请选择要删除的数据！"})},manageFootprint:function(){this.manage=!this.manage,n=[]},goodsImg:function(e){var t=e.split(",");return t[0]?this.$util.img(t[0],{size:"mid"}):this.$util.getDefaultImage().goods},imgError:function(e){n=[],this.goodsList[e].goods_image=this.$util.getDefaultImage().goods},showPrice:function(e){var t=e.discount_price;return e.member_price&&parseFloat(e.member_price)<parseFloat(t)&&(t=e.member_price),t},showMarketPrice:function(e){if(e.market_price_show){var t=this.showPrice(e);if(e.market_price>0)return e.market_price;if(parseFloat(e.price)>parseFloat(t))return e.price}return""},goodsTag:function(e){return e.label_name||""},datetime:function(e){var t=new Date;t.setTime(1e3*e.browse_time);var i=t.getFullYear(),o=t.getMonth()+1;o=o<10?"0"+o:o;var r=t.getDate();r=r<10?"0"+r:r;var s=i+"/"+o+"/"+r;if(-1==this.$util.inArray(s,n))return n.push(s),s},singleElection:function(e){-1==this.$util.inArray(e.id,this.idArr)?this.idArr.push(e.id):this.idArr.splice(this.$util.inArray(e.id,this.idArr),1),n=[]},allElection:function(){if(this.idArr.length!=this.goodsList.length){this.idArr=[];var e=[];this.goodsList.forEach((function(t){e.push(t.id)})),this.idArr=e}else this.idArr=[];n=[]},toDetail:function(e){this.$util.redirectTo("/pages/goods/detail",{goods_id:e.goods_id})}},watch:{storeToken:function(e,t){e&&this.$refs.mescroll.refresh()}}};t.default=o},"563e":function(e,t,i){"use strict";i.r(t);var n=i("91e6"),o=i("184f");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("7599");var s=i("828b"),a=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=a.exports},7599:function(e,t,i){"use strict";var n=i("bdd1"),o=i.n(n);o.a},"91e6":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={nsEmpty:function(){return i.e("components/ns-empty/ns-empty").then(i.bind(null,"52a6"))},nsLogin:function(){return Promise.all([i.e("common/vendor"),i.e("components/ns-login/ns-login")]).then(i.bind(null,"2910"))},loadingCover:function(){return i.e("components/loading-cover/loading-cover").then(i.bind(null,"c003"))}},o=function(){var e=this,t=e.$createElement,i=(e._self._c,e.storeToken?e.goodsList.length:null),n=e.storeToken&&i?e.__map(e.goodsList,(function(t,i){var n=e.__get_orig(t),o=e.datetime(t),r=e.manage?e.$util.inArray(t.id,e.idArr):null,s=e.goodsImg(t.goods_image),a=e.goodsTag(t),l=""!=a?e.goodsTag(t):null,u=e.$lang("common.currencySymbol"),c=parseFloat(e.showPrice(t)).toFixed(2).split("."),d=parseFloat(e.showPrice(t)).toFixed(2).split("."),g=t.member_price&&t.member_price==e.showPrice(t),m=g?e.$util.img("public/uniapp/index/VIP.png"):null,f=g||1!=t.promotion_type?null:e.$util.img("public/uniapp/index/discount.png"),h=e.showMarketPrice(t),p=h?e.$lang("common.currencySymbol"):null,b=h?e.showMarketPrice(t):null;return{$orig:n,m0:o,g1:r,m1:s,m2:a,m3:l,m4:u,g2:c,g3:d,m5:g,g4:m,g5:f,m6:h,m7:p,m8:b}})):null,o=e.storeToken?e.goodsList.length&&e.manage:null;e.$mp.data=Object.assign({},{$root:{g0:i,l0:n,g6:o}})},r=[]},bdd1:function(e,t,i){}},[["27cb","common/runtime","common/vendor"]]]);