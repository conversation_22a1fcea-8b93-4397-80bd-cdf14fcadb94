<page-meta page-style="{{themeColor}}"></page-meta><view><view class="tab color-bg"><view class="tab-left"><picker range="{{monthData}}" value="{{monthIndex}}" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{''+monthData[monthIndex]+''}}<text class="iconfont icon-iconangledown"></text></view></picker></view><view class="tab-right"><picker class="picker" value="{{pointIndex}}" range="{{pointType}}" range-key="label" data-event-opts="{{[['change',[['bindPickerChange',['$event']]]]]}}" bindchange="__e"><text class="desc uni-input">{{pointType[pointIndex].label}}</text><text class="iconfont icon-iconangledown"></text></picker></view></view><mescroll-uni class="member-point vue-ref" bind:getData="__e" vue-id="5b5770ff-1" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g0}}"><block><view class="detailed-wrap"><view class="cont"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="detailed-item"><view data-event-opts="{{[['tap',[['toFromDetail',['$0'],[[['dataList','',index]]]]]]]}}" class="info" bindtap="__e"><view class="event">{{item.$orig.type_name}}</view><view class="time-box"><text class="time color-tip">{{item.g1}}</text></view></view><block wx:if="{{item.$orig.account_data>0}}"><view class="num color-base-text">{{"+"+item.m0}}</view></block><block wx:else><view class="num">{{item.m1}}</view></block></view></block></view></view></block></block><block wx:else><block><view class="cart-empty"><ns-empty vue-id="{{('5b5770ff-2')+','+('5b5770ff-1')}}" bind:__l="__l"></ns-empty></view></block></block></view></mescroll-uni><loading-cover class="vue-ref" vue-id="5b5770ff-3" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>