<page-meta page-style="{{themeColor}}"></page-meta><view class="container"><block wx:if="{{storeToken}}"><view class="head-wrap"><text data-event-opts="{{[['tap',[['manageFootprint',['$event']]]]]}}" bindtap="__e">{{manage?'完成':'管理'}}</text></view></block><block wx:if="{{storeToken}}"><mescroll-uni class="vue-ref" vue-id="38f0c9fe-1" top="110rpx" data-ref="mescroll" data-event-opts="{{[['^getData',[['getListData']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:if="{{$root.g0}}"><view class="goods-list single-column"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view><view class="datetime">{{item.m0}}</view><view class="{{['goods-item',(manage)?'manage':'']}}"><block wx:if="{{manage}}"><view data-event-opts="{{[['tap',[['singleElection',['$0'],[[['goodsList','',index]]]]]]]}}" class="checkbox-wrap" bindtap="__e"><text class="{{['iconfont',item.g1!=-1?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}"></text></view></block><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="goods-img" bindtap="__e"><image src="{{item.m1}}" mode="widthFix" data-event-opts="{{[['error',[['imgError',[index]]]]]}}" binderror="__e"></image><block wx:if="{{item.m2!=''}}"><view class="color-base-bg goods-tag">{{item.m3}}</view></block></view><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['goodsList','',index]]]]]]]}}" class="info-wrap" bindtap="__e"><view class="name-wrap"><view class="goods-name">{{item.$orig.goods_name}}</view></view><view class="lineheight-clear"><view class="discount-price"><text class="unit price-style small">{{item.m4}}</text><text class="price price-style large">{{item.g2[0]}}</text><text class="unit price-style small">{{"."+item.g3[1]}}</text></view><block wx:if="{{item.m5}}"><view class="member-price-tag"><image src="{{item.g4}}" mode="widthFix"></image></view></block><block wx:else><block wx:if="{{item.$orig.promotion_type==1}}"><view class="member-price-tag"><image src="{{item.g5}}" mode="widthFix"></image></view></block></block></view><view class="pro-info"><block wx:if="{{item.m6}}"><view class="delete-price font-size-activity-tag color-tip price-font"><text class="unit">{{item.m7}}</text><text>{{item.m8}}</text></view></block><block wx:if="{{item.$orig.sale_show}}"><view class="sale font-size-activity-tag color-tip">{{"已售"+item.$orig.sale_num+(item.$orig.unit?item.$orig.unit:'件')}}</view></block></view></view></view></view></block></view></block><block wx:else><view><ns-empty vue-id="{{('38f0c9fe-2')+','+('38f0c9fe-1')}}" text="暂无浏览过的商品" bind:__l="__l"></ns-empty></view></block><block wx:if="{{$root.g6}}"><view class="bottom-wrap"><view data-event-opts="{{[['tap',[['allElection',['$event']]]]]}}" class="all-election" bindtap="__e"><view class="{{['iconfont',isAll?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}"></view><text>全选</text></view><view class="action-btn"><button class="{{['delete',(selected)?'disabled':'']}}" type="primary" data-event-opts="{{[['tap',[['deleteFootprint']]]]}}" bindtap="__e">删除</button></view></view></block></view></mescroll-uni></block><ns-login class="vue-ref" vue-id="38f0c9fe-3" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="38f0c9fe-4" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>