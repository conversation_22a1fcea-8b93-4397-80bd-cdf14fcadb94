<page-meta page-style="{{themeColor}}"></page-meta><view class="container"><block wx:if="{{step!=0}}"><view class="tips">请输入6位支付密码，建议不要使用重复或连续数字</view></block><block wx:else><view class="tips">{{"验证码已发送至"+$root.f0+"请在下方输入4位数字验证码"}}</view></block><view class="password-wrap"><myp-one class="vue-ref" vue-id="0b660dcd-1" maxlength="{{step==0?4:6}}" is-pwd="{{step!=0}}" auto-focus="{{true}}" data-ref="input" data-event-opts="{{[['^input',[['input']]]]}}" bind:input="__e" bind:__l="__l"></myp-one><view data-event-opts="{{[['tap',[['sendMobileCode',['$event']]]]]}}" hidden="{{!(step==0)}}" class="{{['dynacode',dynacodeData.seconds==120?'color-base-text':'color-tip']}}" bindtap="__e">{{''+dynacodeData.codeText+''}}</view><view hidden="{{!(step==0)}}" class="action-tips">输入短信验证码</view><view hidden="{{!(step==1)}}" class="action-tips">请设置支付密码</view><view hidden="{{!(step==2)}}" class="action-tips">请再次输入</view><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="{{['btn','color-base-bg','color-base-border',(!isClick)?'disabled':'']}}" bindtap="__e">确认</view></view></view>