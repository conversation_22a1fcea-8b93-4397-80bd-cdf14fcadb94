<page-meta page-style="{{themeColor}}"></page-meta><view><block wx:if="{{indent=='all'&&memberInfo}}"><view class="info-wrap"><view class="info-list-cell info-item info-list-con" hover-class="cell-hover" data-event-opts="{{[['tap',[['headImage',['$event']]]]]}}" bindtap="__e"><text class="cell-tit">{{$root.m0}}</text><view class="info-list-head cell-tip"><image src="{{memberInfo.headimg?$root.g0:$root.g1.head}}" mode="aspectFill" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e"></image></view><text class="cell-more"></text></view><block wx:if="{{memberInfo.is_edit_username==1}}"><view class="info-list-cell info-list-con" hover-class="cell-hover" data-event-opts="{{[['tap',[['modifyInfo',['username']]]]]}}" bindtap="__e"><text class="cell-tit">{{$root.m1}}</text><text class="cell-tip">{{memberInfoformData.number}}</text><text class="cell-more"></text></view></block><block wx:else><view class="info-list-cell info-list-con" hover-class="cell-hover"><text class="cell-tit">{{$root.m2}}</text><text class="cell-tip cell-tip1">{{memberInfoformData.number}}</text></view></block><view class="info-list-cell info-list-con" hover-class="cell-hover" data-event-opts="{{[['tap',[['modifyInfo',['name']]]]]}}" bindtap="__e"><text class="cell-tit">{{$root.m3}}</text><text class="cell-tip">{{memberInfoformData.nickName}}</text><text class="cell-more"></text></view><view class="info-list-cell info-list-con" hover-class="cell-hover" data-event-opts="{{[['tap',[['modifyInfo',['realName']]]]]}}" bindtap="__e"><text class="cell-tit">{{$root.m4}}</text><text class="cell-tip">{{memberInfoformData.realName}}</text><text class="cell-more"></text></view><view class="info-list-cell info-list-con" hover-class="cell-hover" data-event-opts="{{[['tap',[['modifyInfo',['sex']]]]]}}" bindtap="__e"><text class="cell-tit">{{$root.m5}}</text><text class="cell-tip">{{memberInfoformData.sex}}</text><text class="cell-more"></text></view><view class="info-list-cell info-list-con" hover-class="cell-hover" data-event-opts="{{[['tap',[['modifyInfo',['birthday']]]]]}}" bindtap="__e"><text class="cell-tit">{{$root.m6}}</text><text class="cell-tip">{{memberInfoformData.birthday}}</text><text class="cell-more"></text></view><view data-event-opts="{{[['tap',[['modifyInfo',['mobile']]]]]}}" class="info-list-cell info-list-con" bindtap="__e"><text class="cell-tit">{{$root.m7}}</text><block wx:if="{{memberInfoformData.user_tel==''}}"><text class="cell-tip">{{$root.m8}}</text></block><block wx:else><text class="cell-tip">{{memberInfoformData.mobile}}</text></block><text class="cell-more"></text></view><view class="info-list-cell info-list-con" hover-class="cell-hover" data-event-opts="{{[['tap',[['modifyInfo',['password']]]]]}}" bindtap="__e"><text class="cell-tit">{{$root.m9}}</text><text class="cell-more"></text></view><view class="info-list-cell info-list-con" hover-class="cell-hover" data-event-opts="{{[['tap',[['modifyInfo',['address']]]]]}}" bindtap="__e"><text class="cell-tit">所在地址</text><block wx:if="{{memberInfo.full_address}}"><text class="cell-tip">{{memberInfo.full_address+"\n\t\t\t\t"+memberInfo.address}}</text></block><block wx:else><text class="cell-tip">去设置</text></block><text class="cell-more"></text></view><block wx:if="{{addonIsExist.membercancel&&memberConfig.is_enable==1}}"><view class="info-list-cell info-list-con" hover-class="cell-hover" data-event-opts="{{[['tap',[['modifyInfo',['cancellation']]]]]}}" bindtap="__e"><text class="cell-tit">{{$root.m10}}</text><text class="cell-more"></text></view></block><view class="info-list-cell info-list-con" hover-class="cell-hover"><text class="cell-tit">版本号</text><text class="cell-tip cell-tip1">{{version}}</text></view><view data-event-opts="{{[['tap',[['logout',['$event']]]]]}}" class="save-item" bindtap="__e"><button type="primary">{{$root.m11}}</button></view></view></block><ns-login class="vue-ref" vue-id="34b87bf1-1" data-ref="login" bind:__l="__l"></ns-login></view>