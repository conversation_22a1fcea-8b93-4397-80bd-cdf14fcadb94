<page-meta page-style="{{themeColor}}"></page-meta><view class="member-level"><block wx:if="{{$root.g0}}"><block><view class="level-top"><image src="{{$root.g1}}"></image></view><view class="banner-container"><view class="memberInfo"><block wx:if="{{memberInfo.headimg}}"><image src="{{$root.g2}}" mode="aspectFill" data-event-opts="{{[['error',[['headimgError',['$event']]]]]}}" binderror="__e"></image></block><block wx:else><image src="{{$root.g3.head}}" mode="aspectFill"></image></block><view class="member-desc"><view class="font-size-toolbar">{{memberInfo.nickname}}</view><block wx:if="{{memberInfo.level_expire_time>0}}"><view class="font-size-tag expire-time">{{"有效期至："+$root.g4}}</view></block></view></view><swiper class="margin-bottom" style="{{'width:'+('100vw')+';'+('height:'+('390rpx')+';')}}" indicator-dots="{{swiperConfig.indicatorDots}}" indicator-color="{{swiperConfig.indicatorColor}}" indicator-active-color="{{swiperConfig.indicatorActiveColor}}" autoplay="{{false}}" interval="{{swiperConfig.interval}}" duration="{{swiperConfig.duration}}" circular="{{swiperConfig.circular}}" previous-margin="{{swiperConfig.previousMargin}}" next-margin="{{swiperConfig.nextMargin}}" current="{{curIndex}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]],['animationfinish',[['animationfinish',['$event']]]]]}}" bindchange="__e" bindanimationfinish="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="i" wx:key="i"><swiper-item class="{{[item.g5==1?'image-container-box':'']}}"><view class="{{['image-container',curIndex===0?i===1?'item-right':i===listLen-1?'item-left':'item-center':curIndex===listLen-1?i===curIndex-1?'item-left':i===curIndex+1?'item-right':i===0?'item-right':i===listLen-2?'item-left':'item-center':i===curIndex-1?'item-left':i===curIndex+1?'item-right':'item-center']}}"><view class="slide-image" style="{{'background-size:100% 100%;background-repeat:no-repeat;'+('transform:'+(curIndex===i?'scale('+scaleX+','+scaleY+')':'scale(1,1)')+';')+('transition-duration:'+('.3s')+';')+('transition-timing-function:'+('ease')+';')}}"><view class="bg-border"></view><block wx:if="{{item.$orig&&item.$orig['level_picture']}}"><image src="{{item.g6}}"></image></block><block wx:else><image style="{{'background-color:'+(item.$orig['bg_color'])+';'}}"></image></block><view class="info"><view class="level-detail" style="{{'color:'+(item.$orig['level_text_color'])+';'}}">{{item.$orig.level_name}}</view><view class="growr-name" style="{{'color:'+(item.$orig['level_text_color'])+';'}}">{{item.$orig.level_name+"可享受消费折扣和"}}</view><view class="growr-value" style="{{'color:'+(item.$orig['level_text_color'])+';'}}">会员大礼包等权益</view><block wx:if="{{item.$orig.remark!=''}}"><view data-event-opts="{{[['tap',[['openExplainPopup',['$event']]]]]}}" class="growth-rules font-size-tag" bindtap="__e"><text class="iconfont icon-wenhao font-size-tag"></text></view></block></view></view></view></swiper-item></block></swiper><view class="card-content"><view class="card-content-head"><view class="line-box"><view class="line right"></view></view><view class="card-content-title">卡种选择</view><view class="line-box"><view class="line"></view></view><view class="clear"></view></view><view class="card-time-list"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['card-item-box',(item.g7==4)?'small':'']}}"><view data-event-opts="{{[['tap',[['choice',[index]]]]]}}" class="{{['card-time-item',(choiceIndex==index)?'active':'']}}" bindtap="__e"><image src="{{item.g8}}" mode="widthFix"></image><view class="time-name">{{cardType[item.$orig.key].name}}</view><view class="time-price">{{''+item.m0+''}}<text class="price">{{item.$orig.value}}</text>{{'/'+cardType[item.$orig.key].unit+''}}</view></view></view></block></view></view><block wx:if="{{currCard.is_free_shipping||currCard.consume_discount<100||currCard.point_feedback>0}}"><view class="card-content"><view class="card-content-head"><view class="line-box"><view class="line right"></view></view><view class="card-content-title">会员权益</view><view class="line-box"><view class="line"></view></view><view class="clear"></view></view><view class="card-privilege-list"><block wx:if="{{currCard.is_free_shipping}}"><view class="card-privilege-item"><view class="card-privilege-icon"><text class="iconfont icon-tedianquanchangbaoyou"></text></view><view class="card-privilege-name">全场包邮</view><view class="card-privilege-text">享受商品包邮服务</view></view></block><block wx:if="{{currCard.consume_discount<100}}"><view class="card-privilege-item"><view class="card-privilege-icon"><text class="iconfont icon-zhekou"></text></view><view class="card-privilege-name">消费折扣</view><view class="card-privilege-text">{{"部分商品下单可享"+currCard.consume_discount/10+"折优惠"}}</view></view></block><block wx:if="{{currCard.point_feedback>0}}"><view class="card-privilege-item"><view class="card-privilege-icon"><text class="iconfont icon-jifen2 f32"></text></view><view class="card-privilege-name">积分回馈</view><view class="card-privilege-text">{{"下单享"+$root.m1+"倍积分回馈"}}</view></view></block></view><block wx:if="{{currCard.send_coupon!=''||currCard.send_point>0||currCard.send_balance>0}}"><view><view class="card-content-head"><view class="line-box"><view class="line right"></view></view><view class="card-content-title">开卡礼包</view><view class="line-box"><view class="line"></view></view><view class="clear"></view></view><view class="card-privilege-list"><block wx:if="{{currCard.send_point>0}}"><view class="card-privilege-item"><view class="card-privilege-icon"><text class="iconfont icon-jifen3"></text></view><view class="card-privilege-name">积分礼包</view><view class="card-privilege-text">{{"赠送"+currCard.send_point+"积分"}}</view></view></block><block wx:if="{{currCard.send_balance>0}}"><view class="card-privilege-item"><view class="card-privilege-icon"><text class="iconfont icon-hongbao"></text></view><view class="card-privilege-name">红包礼包</view><view class="card-privilege-text">{{"赠送"+$root.m2+"元红包"}}</view></view></block><block wx:if="{{currCard.send_coupon!=''}}"><view class="card-privilege-item"><view class="card-privilege-icon"><text class="iconfont icon-youhuiquan1"></text></view><view class="card-privilege-name">优惠券礼包</view><view class="card-privilege-text">{{"赠送"+$root.g9+'张优惠券'}}</view></view></block></view></view></block></view></block><block wx:if="{{$root.g10}}"><block><view class="{{['action-wrap',(isIphoneX)?'bottom-safe-area':'',(agreement)?'have-agreement':'']}}"></view><view class="{{['action',(isIphoneX)?'bottom-safe-area':'',(agreement)?'have-agreement':'']}}"><view data-event-opts="{{[['tap',[['create',['$event']]]]]}}" class="action-btn" bindtap="__e"><block wx:if="{{currCard.level_id==levelId}}"><block><text class="bold title">立即续费</text></block></block><block wx:else><block><block wx:if="{{currCard.charge_type==1}}"><text class="bold title">充值开通</text></block><block wx:else><text class="bold title">立即开通</text></block></block></block><text class="font-size-tag">{{$root.m3}}</text><text class="bold">{{currCard.charge_rule_arr[choiceIndex].value}}</text><text>{{"/"+cardType[currCard.charge_rule_arr[choiceIndex].key].unit}}</text></view><block wx:if="{{agreement}}"><view class="agreement">购买既视为同意<text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e">{{"《"+agreement.title+"》"}}</text></view></block></view></block></block></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><uni-popup class="vue-ref" vue-id="8c135e30-1" type="bottom" data-ref="explainPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="tips-layer"><view data-event-opts="{{[['tap',[['closeExplainPopup']]]]}}" class="head" bindtap="__e"><view class="title">会员卡说明</view><text class="iconfont icon-close"></text></view><view class="body"><view class="detail margin-bottom"><block wx:if="{{currCard.remark!=''}}"><block><view class="tip">会员卡说明</view><view class="font-size-base">{{currCard.remark}}</view></block></block></view></view></view></uni-popup></view><block wx:if="{{$root.g11}}"><ns-payment class="vue-ref" vue-id="8c135e30-2" payMoney="{{currCard.charge_rule_arr[choiceIndex].value}}" data-ref="choosePaymentPopup" data-event-opts="{{[['^confirm',[['toPay']]]]}}" bind:confirm="__e" bind:__l="__l"></ns-payment></block></block></block><block wx:else><block><ns-empty vue-id="8c135e30-3" text="暂无可开会员卡" isIndex="{{false}}" bind:__l="__l"></ns-empty></block></block><ns-login class="vue-ref" vue-id="8c135e30-4" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="8c135e30-5" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>