<page-meta page-style="{{themeColor}}" class="data-v-d17daa1a"></page-meta><view class="signin data-v-d17daa1a"><block wx:if="{{signState}}"><block class="data-v-d17daa1a"><view class="sigin-box data-v-d17daa1a"><view class="sigin-bg data-v-d17daa1a"></view><view class="signin-wrap data-v-d17daa1a"><view class="member-info data-v-d17daa1a"><view class="headimg data-v-d17daa1a"><view class="headimg-img data-v-d17daa1a"><image src="{{headimg?$root.g0:$root.g1.head}}" mode="aspectFill" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e" class="data-v-d17daa1a"></image></view><view class="signin-info data-v-d17daa1a"><view class="data-v-d17daa1a">已连续签到<text class="data-v-d17daa1a">{{signDaysSeries}}</text>天</view><view class="data-v-d17daa1a">{{(hasSign?'明日':'今日')+"签到可获得"+pointTomorrow+"积分"}}</view></view></view><view data-event-opts="{{[['tap',[['sign']]]]}}" class="point-box data-v-d17daa1a" bindtap="__e"><image src="{{$root.g2}}" mode="widthFix" class="data-v-d17daa1a"></image></view></view><view class="signin-days-wrap data-v-d17daa1a"><view class="signin-desc data-v-d17daa1a">连续签到领好礼</view><view class="signin-day-list data-v-d17daa1a"><view class="signin-day-con data-v-d17daa1a"><view class="signin-day-scroll data-v-d17daa1a"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><block class="data-v-d17daa1a"><block wx:if="{{!item.$orig.is_last}}"><view class="{{['signin-day-item','data-v-d17daa1a',(item.$orig.day<signDaysSeries||item.$orig.day==signDaysSeries&&hasSign==0||item.$orig.day==signDaysSeries&&hasSign==1)?'signed':'']}}" id="{{'id_'+item.$orig.day}}"><view class="day data-v-d17daa1a">{{"第"+item.$orig.day+"天"}}</view><image src="{{item.g3}}" class="data-v-d17daa1a"></image><view class="point data-v-d17daa1a">{{item.$orig.point+"积分"}}</view></view></block><block wx:else><view class="{{['signin-day-item','last','data-v-d17daa1a',(item.$orig.day<signDaysSeries||item.$orig.day==signDaysSeries&&hasSign==0||item.$orig.day==signDaysSeries&&hasSign==1)?'signed':'']}}" id="{{'id_'+item.$orig.day}}"><view class="data-v-d17daa1a"><view class="day data-v-d17daa1a">{{"第"+item.$orig.day+"天"}}</view><view class="point data-v-d17daa1a">{{item.$orig.point+"积分"}}</view></view><image src="{{item.g4}}" mode="widthFix" class="data-v-d17daa1a"></image></view></block></block></block></view></view></view></view><view class="my-signin data-v-d17daa1a"><view class="my-signin-title data-v-d17daa1a">我的签到</view><view class="my-signin-con data-v-d17daa1a"><view class="my-signin-item data-v-d17daa1a"><image src="{{$root.g5}}" class="data-v-d17daa1a"></image><view class="my-signin-item-num data-v-d17daa1a">{{"积分："+signPoint}}</view><view class="data-v-d17daa1a">累计获得积分</view></view><view class="my-signin-item data-v-d17daa1a"><image src="{{$root.g6}}" class="data-v-d17daa1a"></image><view class="my-signin-item-num data-v-d17daa1a">{{"成长值："+signGrowth}}</view><view class="data-v-d17daa1a">累计获得成长值</view></view></view></view><block wx:if="{{$root.g7}}"><view class="signin-rule data-v-d17daa1a"><view class="signin-rule-title data-v-d17daa1a">签到规则</view><view class="signin-rule-con data-v-d17daa1a"><block wx:for="{{rule}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="rule-item data-v-d17daa1a"><block wx:if="{{index==0}}"><block class="data-v-d17daa1a">1. 每日签到奖励：</block></block><block wx:else><block class="data-v-d17daa1a">{{index+1+'. 连续签到'+item.day+'天额外奖励：'}}</block></block><block wx:if="{{item.point}}"><text class="data-v-d17daa1a">{{item.point+'积分 '}}</text></block><block wx:if="{{item.growth}}"><text class="data-v-d17daa1a">{{item.growth+'成长值'}}</text></block></view></block><view class="rule-item data-v-d17daa1a">{{''+($root.g8+1)+".连续签到"+cycle+'天为一个周期，连续签到天数签满一个周期或者签到中断，将清空连签天数重新计算签到天数'}}</view><view class="rule-item data-v-d17daa1a">{{''+($root.g9+2)+'. 用户可在签到页每日签到一次，签到后可获得每日签到奖励；连续签到天数达到连签奖励的当天，可额外获得连签奖励'}}</view></view></view></block></view></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-d17daa1a"><uni-popup class="wap-floating data-v-d17daa1a vue-ref" vue-id="3e12513d-1" type="center" maskClick="{{false}}" data-ref="uniPopup" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['close']]]]}}" class="popup-box data-v-d17daa1a" bindtap="__e"><text data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="iconfont icon-close data-v-d17daa1a" bindtap="__e"></text><image class="pic data-v-d17daa1a" src="{{$root.g10}}"></image><view class="popup-content data-v-d17daa1a"><view class="popup-content-wrap data-v-d17daa1a"><block wx:if="{{successTip.point||successTip.growth}}"><view class="title data-v-d17daa1a"><text class="data-v-d17daa1a">恭喜您获得</text><block wx:if="{{successTip.point}}"><text class="data-v-d17daa1a"><text class="num color-base-text data-v-d17daa1a">{{successTip.point}}</text>积分</text></block><block wx:if="{{successTip.growth}}"><text class="data-v-d17daa1a"><text class="num color-base-text data-v-d17daa1a">{{successTip.growth}}</text>成长值</text></block></view></block><view class="desc data-v-d17daa1a">连续签到可获得更多奖励！</view></view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="other-info color-base-bg data-v-d17daa1a" bindtap="__e">知道了</view></view></view></uni-popup></view><hover-nav vue-id="3e12513d-2" need="{{true}}" class="data-v-d17daa1a" bind:__l="__l"></hover-nav></block></block><block wx:else><block class="data-v-d17daa1a"><ns-empty vue-id="3e12513d-3" text="暂未开启签到奖励" subText="请到营销中心开启签到奖励" isIndex="{{false}}" class="data-v-d17daa1a" bind:__l="__l"></ns-empty></block></block><loading-cover vue-id="3e12513d-4" data-ref="loadingCover" class="data-v-d17daa1a vue-ref" bind:__l="__l"></loading-cover><ns-login vue-id="3e12513d-5" data-ref="login" class="data-v-d17daa1a vue-ref" bind:__l="__l"></ns-login><privacy-popup vue-id="3e12513d-6" data-ref="privacyPopup" class="data-v-d17daa1a vue-ref" bind:__l="__l"></privacy-popup></view>