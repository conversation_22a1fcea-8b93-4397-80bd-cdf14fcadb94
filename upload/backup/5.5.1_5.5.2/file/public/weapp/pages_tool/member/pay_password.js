require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/pay_password"],{"292f5":function(e,t,i){"use strict";(function(e,t){var s=i("47a9");i("d381");s(i("3240"));var n=s(i("7357"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(n.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},7357:function(e,t,i){"use strict";i.r(t);var s=i("f3ca"),n=i("8412");for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);i("f19c");var a=i("828b"),c=Object(a["a"])(n["default"],s["b"],s["c"],!1,null,null,null,!1,s["a"],void 0);t["default"]=c.exports},8412:function(e,t,i){"use strict";i.r(t);var s=i("896f"),n=i.n(s);for(var o in s)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(o);t["default"]=n.a},"896f":function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s={components:{mypOne:function(){i.e("pages_tool/components/myp-one/myp-one").then(function(){return resolve(i("b2c6"))}.bind(null,i)).catch(i.oe)}},data:function(){return{isClick:!1,step:1,key:"",code:"",password:"",repassword:"",isSub:!1,back:"",dynacodeData:{seconds:120,timer:null,codeText:"获取验证码",isSend:!1}}},onLoad:function(t){var i=this;t.back&&(this.back=t.back),this.storeToken?""==this.memberInfo.mobile?e.showModal({title:"提示",content:"设置支付密码需要先绑定手机号,是否立即绑定?",success:function(e){e.confirm?i.$util.redirectTo("/pages_tool/member/info",{action:"mobile",back:i.back},"redirectTo"):i.back?i.$util.redirectTo(i.back):i.$util.redirectTo("/pages/member/index")}}):(this.step=0,this.sendMobileCode()):this.$util.redirectTo("/pages_tool/login/index")},methods:{input:function(e){0==this.step?4==e.length?(this.isClick=!0,this.code=e):this.isClick=!1:1==this.step?6==e.length?(this.isClick=!0,this.password=e):this.isClick=!1:6==e.length?(this.isClick=!0,this.repassword=e):this.isClick=!1},confirm:function(){var e=this;if(this.isClick)if(0==this.step)this.$api.sendRequest({url:"/api/member/verifypaypwdcode",data:{code:this.code,key:this.key},success:function(t){0==t.code?(e.$refs.input.clear(),e.isClick=!1,e.step=1):e.$util.showToast({title:t.message})}});else if(1==this.step)this.$refs.input.clear(),this.isClick=!1,this.step=2;else if(this.password==this.repassword){if(this.isSub)return;this.isSub=!0,this.$api.sendRequest({url:"/api/member/modifypaypassword",data:{key:this.key,code:this.code,password:this.password},success:function(t){t.code>=0?(e.$util.showToast({title:"修改成功"}),setTimeout((function(){e.back?e.$util.redirectTo(e.back,{},"redirectTo"):e.$util.redirectTo("/pages/member/index")}),2e3)):(e.initInfo(),e.$util.showToast({title:t.message}))}})}else this.$util.showToast({title:"两次输入的密码不一致",success:function(t){e.initInfo()}})},initInfo:function(){this.isClick=!1,this.step=1,this.password="",this.repassword="",this.isSub=!1,this.$refs.input.clear()},sendMobileCode:function(){var e=this;120==this.dynacodeData.seconds&&(this.dynacodeData.isSend||(this.dynacodeData.isSend=!0,this.$api.sendRequest({url:"/api/member/paypwdcode",success:function(t){e.dynacodeData.isSend=!1,t.code>=0?(e.key=t.data.key,120==e.dynacodeData.seconds&&null==e.dynacodeData.timer&&(e.dynacodeData.timer=setInterval((function(){e.dynacodeData.seconds--,e.dynacodeData.codeText=e.dynacodeData.seconds+"s后可重新获取"}),1e3))):e.$util.showToast({title:t.message})},fail:function(){e.$util.showToast({title:"request:fail"}),e.dynacodeData.isSend=!1}})))}},filters:{mobile:function(e){return e.substring(0,3)+"****"+e.substring(7)}},watch:{"dynacodeData.seconds":{handler:function(e,t){0==e&&(clearInterval(this.dynacodeData.timer),this.dynacodeData={seconds:120,timer:null,codeText:"获取动态码",isSend:!1})},immediate:!0,deep:!0}}};t.default=s}).call(this,i("df3c")["default"])},c356:function(e,t,i){},f19c:function(e,t,i){"use strict";var s=i("c356"),n=i.n(s);n.a},f3ca:function(e,t,i){"use strict";i.d(t,"b",(function(){return s})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){}));var s=function(){var e=this.$createElement,t=(this._self._c,0==this.step?this._f("mobile")(this.memberInfo.mobile):null);this.$mp.data=Object.assign({},{$root:{f0:t}})},n=[]}},[["292f5","common/runtime","common/vendor"]]]);