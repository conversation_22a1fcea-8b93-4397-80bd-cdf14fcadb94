<page-meta page-style="{{themeColor}}"></page-meta><view><view class="money-wrap"><text>{{detail.apply_money}}</text></view><view class="item"><view class="line-wrap"><text class="label">当前状态</text><text class="value">{{detail.status_name}}</text></view><view class="line-wrap"><text class="label">交易号</text><text class="value">{{detail.withdraw_no}}</text></view><view class="line-wrap"><text class="label">手续费</text><text class="value">{{"￥"+detail.service_money}}</text></view><view class="line-wrap"><text class="label">申请时间</text><text class="value">{{$root.g0}}</text></view><block wx:if="{{detail.status}}"><view class="line-wrap"><text class="label">审核时间</text><text class="value">{{$root.g1}}</text></view></block><block wx:if="{{detail.bank_name}}"><view class="line-wrap"><text class="label">银行名称</text><text class="value">{{detail.bank_name}}</text></view></block><view class="line-wrap"><text class="label">收款账号</text><text class="value">{{detail.account_number}}</text></view><block wx:if="{{detail.status==-1&&detail.refuse_reason}}"><view class="line-wrap"><text class="label">拒绝理由</text><text class="value">{{detail.refuse_reason}}</text></view></block><block wx:if="{{detail.status==2}}"><view class="line-wrap"><text class="label">转账方式名称</text><text class="value">{{detail.transfer_type_name}}</text></view></block><block wx:if="{{detail.status==2}}"><view class="line-wrap"><text class="label">转账时间</text><text class="value">{{$root.g2}}</text></view></block></view><block wx:if="{{withdrawInfo.transfer_type&&detail.transfer_type=='wechatpay'&&detail.status==1}}"><view class="operations"><button class="operation" type="primary" data-event-opts="{{[['tap',[['merchantTransfer']]]]}}" bindtap="__e">收款</button></view></block><loading-cover class="vue-ref" vue-id="0ba9773c-1" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>