<page-meta page-style="{{themeColor}}"></page-meta><view class="member-level"><view class="grow-explain"><view class="explain-title"><image src="{{$root.g0}}" mode="aspectFit"></image>成长值说明<image src="{{$root.g1}}" mode="aspectFit"></image></view><view class="explain-table"><view class="explain-tr"><text class="explain-th">等级</text><text class="explain-th">成长值</text></view><block wx:for="{{levelList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="explain-tr"><text class="explain-td">{{item.level_name}}</text><text class="explain-td">{{item.growth}}</text></view></block></view></view><view class="grow-value"><view class="title"><image src="{{$root.g2}}" mode="aspectFit"></image><text>什么是成长值</text></view><view class="content color-tip">成长值是消费者在店铺成为会员后，通过消费计算出来的值。成长值决定会员等级，会员等级越高，所享受的会员权益和会员礼包就越多。</view></view><view class="acquisition-grow"><view class="title"><image src="{{$root.g3}}" mode="aspectFit"></image><text>如何获得成长值</text></view><view class="content color-tip"><text>1、注册会员送x成长值。</text><text>2、会员充值到余额送x成长值。</text><text>3、会员签到送x成长值。</text><text>4、会员消费x元，交易完成即可获得x个成长值。</text></view></view><block wx:if="{{showTop}}"><to-top bind:toTop="__e" vue-id="6a7ad105-1" data-event-opts="{{[['^toTop',[['scrollToTopNative']]]]}}" bind:__l="__l"></to-top></block><loading-cover class="vue-ref" vue-id="6a7ad105-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>