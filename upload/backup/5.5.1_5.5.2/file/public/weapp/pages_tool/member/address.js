require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/member/address"],{"10ca":function(e,t,s){"use strict";var a=s("5f659"),n=s.n(a);n.a},"11cc":function(e,t,s){"use strict";(function(e,t){var a=s("47a9");s("d381");a(s("3240"));var n=a(s("3346"));e.__webpack_require_UNI_MP_PLUGIN__=s,t(n.default)}).call(this,s("3223")["default"],s("df3c")["createPage"])},3346:function(e,t,s){"use strict";s.r(t);var a=s("e89e"),n=s("4b9e");for(var o in n)["default"].indexOf(o)<0&&function(e){s.d(t,e,(function(){return n[e]}))}(o);s("10ca"),s("9977");var r=s("828b"),i=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"b4bed4d8",null,!1,a["a"],void 0);t["default"]=i.exports},"4b9e":function(e,t,s){"use strict";s.r(t);var a=s("4f25"),n=s.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){s.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},"4f25":function(e,t,s){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;s("edd0");var a={data:function(){return{addressList:[],back:"",redirect:"redirectTo",isIndex:!1,showEmpty:!1,local:0,localType:1}},onLoad:function(t){t.back?(this.back=t.back,e.setStorageSync("addressBack",t.back)):e.getStorageSync("addressBack")&&(this.back=e.getStorageSync("addressBack"),e.removeStorageSync("addressBack")),t.redirect&&(this.redirect=t.redirect),t.local&&(this.local=t.local),t.type&&(this.localType=t.type)},onShow:function(){var t=this;e.removeStorageSync("addressInfo"),this.storeToken?this.$refs.mescroll&&this.$refs.mescroll.refresh():this.$nextTick((function(){t.$refs.login.open("/pages_tool/member/address")}))},methods:{getListData:function(t){var s=this,a=0,n=e.getStorageSync("delivery");n&&"local"==n.delivery_type&&(a=n.store_id||0),this.showEmpty=!1,this.$api.sendRequest({url:"/api/memberaddress/page",data:{page:t.num,page_size:t.size,type:this.localType,store_id:a},success:function(e){s.showEmpty=!0;var a=[],n=e.message;0==e.code&&e.data?a=e.data.list:s.$util.showToast({title:n}),t.endSuccess(a.length),1==t.num&&(s.addressList=[]),s.addressList=s.addressList.concat(a),s.$forceUpdate(),s.$refs.loadingCover&&s.$refs.loadingCover.hide()},fail:function(e){t.endErr(),s.$refs.loadingCover&&s.$refs.loadingCover.hide()}})},addAddress:function(e,t){var s={type:this.localType};"edit"==e&&(s.id=t),this.back&&(s.back=this.back),this.$util.redirectTo("/pages_tool/member/address_edit",s)},deleteAddress:function(t,s){var a=this;e.showModal({title:"操作提示",content:"确定要删除该地址吗？",success:function(e){if(e.confirm){if(1==s)return void a.$util.showToast({title:"默认地址，不能删除"});a.$api.sendRequest({url:"/api/memberaddress/delete",data:{id:t},success:function(e){0==e.code?a.$util.showToast({title:"删除成功"}):a.$util.showToast({title:"删除失败"}),a.$refs.mescroll.refresh()},fail:function(e){mescroll.endErr()}})}}})},setDefault:function(t,s){var a=this;1!=s&&this.$api.sendRequest({url:"/api/memberaddress/setdefault",data:{id:t},success:function(t){t.data>0?(a.$refs.mescroll.refresh(),""!=a.back?e.navigateBack({delta:1}):(a.$refs.loadingCover&&a.$refs.loadingCover.show(),a.addressList=[],a.$refs.mescroll.refresh(),a.$util.showToast({title:"修改默认地址成功"}))):a.$util.showToast({title:t.message})}})},getChooseAddress:function(){var t=this;e.chooseAddress({success:function(e){"chooseAddress:ok"==e.errMsg?t.saveAddress({name:e.userName,mobile:e.telNumber,province:e.provinceName,city:e.cityName,district:e.countyName,address:e.detailInfo,full_address:e.provinceName+"-"+e.cityName+"-"+e.countyName,is_default:1}):t.$util.showToast({title:e.errMsg})},fail:function(e){console.log("fail",e)}})},saveAddress:function(t){var s=this;this.$api.sendRequest({url:"/api/memberaddress/addthreeparties",data:t,success:function(t){t.code>=0?""!=s.back?e.navigateBack({delta:1}):s.$refs.mescroll.refresh():s.$util.showToast({title:t.message})}})}},watch:{storeToken:function(e,t){e&&this.$refs.mescroll.refresh()}}};t.default=a}).call(this,s("df3c")["default"])},"5f659":function(e,t,s){},9977:function(e,t,s){"use strict";var a=s("c0e7"),n=s.n(a);n.a},c0e7:function(e,t,s){},e89e:function(e,t,s){"use strict";s.d(t,"b",(function(){return n})),s.d(t,"c",(function(){return o})),s.d(t,"a",(function(){return a}));var a={nsLogin:function(){return Promise.all([s.e("common/vendor"),s.e("components/ns-login/ns-login")]).then(s.bind(null,"2910"))},loadingCover:function(){return s.e("components/loading-cover/loading-cover").then(s.bind(null,"c003"))}},n=function(){var e=this,t=e.$createElement,s=(e._self._c,e.storeToken?e.addressList.length:null),a=e.storeToken&&0!==s?e.__map(e.addressList,(function(t,s){var a=e.__get_orig(t),n=2==e.localType&&t.local_data?e.$lang("modify"):null,o=2==e.localType&&t.local_data?null:e.$lang("modify");return{$orig:a,m0:n,m1:o}})):null,n=e.storeToken?0==e.addressList.length&&e.showEmpty:null,o=e.storeToken&&n?e.$util.img("public/uniapp/member/address/empty.png"):null,r=e.storeToken&&n?e.$lang("newAddAddress"):null,i=e.storeToken&&n&&1!=e.local?e.$lang("getAddress"):null,d=e.addressList.length,l=0!==d&&1!=e.local?e.$lang("getAddress"):null,c=0!==d?e.$lang("newAddAddress"):null;e.$mp.data=Object.assign({},{$root:{g0:s,l0:a,g1:n,g2:o,m2:r,m3:i,g3:d,m4:l,m5:c}})},o=[]}},[["11cc","common/runtime","common/vendor"]]]);