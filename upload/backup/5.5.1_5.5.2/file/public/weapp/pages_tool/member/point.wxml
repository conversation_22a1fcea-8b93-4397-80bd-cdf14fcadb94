<page-meta page-style="{{themeColor}}"></page-meta><view class="point"><view class="custom-navbar" style="{{'padding-top:'+(menuButtonBounding.top+'px')+';'+('height:'+(menuButtonBounding.height+'px')+';')}}"><view class="navbar-wrap"><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="iconfont icon-back_light back" bindtap="__e"></text><view class="navbar-title">我的积分</view></view></view><view class="head-wrap" style="{{'background:'+('url('+$root.g0+') no-repeat right bottom/ auto 340rpx, linear-gradient(314deg, #F16914 0%, #FEAA4C 100%)')+';'}}"><view class="point price-font">{{pointInfo.point}}</view><view class="title">当前积分</view><view class="flex-box"><view class="flex-item"><view class="num price-font">{{pointInfo.totalPoint}}</view><view class="font-size-tag">累计积分</view></view><view class="flex-item"><view class="num price-font">{{pointInfo.totalConsumePoint}}</view><view class="font-size-tag">累计消费</view></view><view class="flex-item"><view class="num price-font">{{pointInfo.todayPoint}}</view><view class="font-size-tag">今日获得</view></view></view></view><view class="menu-wrap"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="menu-item" bindtap="__e"><view class="icon"><image src="{{$root.g1}}" mode="widthFix"></image></view><text class="title">积分明细</text></view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="menu-item" bindtap="__e"><view class="icon"><image src="{{$root.g2}}" mode="widthFix"></image></view><text class="title">积分商城</text></view></view><view class="task-wrap"><view class="title">做任务赚积分</view><view data-event-opts="{{[['tap',[['toSign',['$event']]]]]}}" class="task-item" bindtap="__e"><view class="icon"><text class="iconfont icon-qiandao1"></text></view><view class="wrap"><view class="title">每日签到</view><view class="desc color-tip font-size-tag">连续签到可获得更多积分</view></view><view class="btn">去签到</view></view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="task-item" bindtap="__e"><view class="icon"><text class="iconfont icon-shangpin"></text></view><view class="wrap"><view class="title">购买商品</view><view class="desc color-tip font-size-tag">购买商品可获得积分</view></view><view class="btn">去下单</view></view></view><ns-login class="vue-ref" vue-id="1ac9118d-1" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="1ac9118d-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>