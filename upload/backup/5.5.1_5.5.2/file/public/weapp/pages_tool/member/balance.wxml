<page-meta page-style="{{themeColor}}"></page-meta><view class="balance"><view class="custom-navbar" style="{{'padding-top:'+(menuButtonBounding.top+'px')+';'+('height:'+(menuButtonBounding.height+'px')+';')}}"><view class="navbar-wrap"><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="iconfont icon-back_light back" bindtap="__e"></text><view class="navbar-title">我的余额</view></view></view><view class="head-wrap" style="{{'background:'+('url('+$root.g0+') no-repeat right bottom/ auto 340rpx, linear-gradient(314deg, #FE7849 0%, #FF1959 100%)')+';'}}"><view class="balance price-font">{{$root.g1}}</view><view class="title">账户余额（元）</view><view class="flex-box"><view class="flex-item"><view class="num price-font">{{$root.f0}}</view><view class="font-size-tag">现金余额（元）</view></view><view class="flex-item"><view class="num price-font">{{$root.f1}}</view><view class="font-size-tag">储值余额（元）</view></view></view></view><view class="menu-wrap"><view data-event-opts="{{[['tap',[['toBalanceDetail',['$event']]]]]}}" class="menu-item" bindtap="__e"><view class="icon"><text class="iconfont icon-yuemingxi"></text></view><text class="title">余额明细</text><text class="iconfont icon-right"></text></view><block wx:if="{{addonIsExist.memberrecharge&&memberrechargeConfig&&memberrechargeConfig.is_use}}"><view data-event-opts="{{[['tap',[['toOrderList',['$event']]]]]}}" class="menu-item" bindtap="__e"><view class="icon"><text class="iconfont icon-chongzhijilu"></text></view><text class="title">充值记录</text><text class="iconfont icon-right"></text></view></block></view><view class="action"><block wx:if="{{addonIsExist.memberrecharge&&memberrechargeConfig&&memberrechargeConfig.is_use}}"><view data-event-opts="{{[['tap',[['toList',['$event']]]]]}}" class="recharge-withdraw" bindtap="__e">{{''+$root.m0+''}}</view></block><block wx:if="{{addonIsExist.memberwithdraw&&withdrawConfig&&withdrawConfig.is_use}}"><view data-event-opts="{{[['tap',[['toWithdrawal',['$event']]]]]}}" class="withdraw" bindtap="__e">{{''+$root.m1+''}}</view></block></view><ns-login class="vue-ref" vue-id="98da8ffe-1" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="98da8ffe-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>