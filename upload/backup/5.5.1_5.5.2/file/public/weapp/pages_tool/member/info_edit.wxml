<page-meta page-style="{{themeColor}}"></page-meta><view><block wx:if="{{memberInfo}}"><block wx:if="{{indent=='username'}}"><view class="edit-info"><view class="edit-info-box"><text class="info-name">{{$root.m0}}</text><input class="uni-input info-content input-len" type="text" maxlength="30" placeholder="{{$root.m1}}" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['formData']]]]]}}" value="{{formData.username}}" bindinput="__e"/></view><view class="color-tip font-size-goods-tag set-pass-tips">用户名仅可修改一次，请谨慎设置</view><view data-event-opts="{{[['tap',[['save',['username']]]]]}}" class="save-item" bindtap="__e"><button type="primary">{{$root.m2}}</button></view></view></block><block wx:if="{{indent=='name'}}"><view class="edit-info"><view class="edit-info-box"><text class="info-name">{{$root.m3}}</text><input class="uni-input info-content input-len" type="text" maxlength="30" placeholder="{{$root.m4}}" data-event-opts="{{[['input',[['__set_model',['$0','nickName','$event',[]],['formData']]]]]}}" value="{{formData.nickName}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['save',['name']]]]]}}" class="save-item" bindtap="__e"><button type="primary">{{$root.m5}}</button></view></view></block><block wx:if="{{indent=='realName'}}"><view class="edit-info"><view class="edit-info-box"><text class="info-name">{{$root.m6}}</text><input class="uni-input info-content input-len" type="text" maxlength="30" placeholder="{{$root.m7}}" data-event-opts="{{[['input',[['__set_model',['$0','realName','$event',[]],['formData']]]]]}}" value="{{formData.realName}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['save',['realName']]]]]}}" class="save-item" bindtap="__e"><button type="primary">{{$root.m8}}</button></view></view></block><block wx:if="{{indent=='sex'}}"><view class="edit-info"><view class="edit-info-box"><text class="info-name">{{$root.m9}}</text><radio-group data-event-opts="{{[['change',[['radioChange',['$event']]]]]}}" class="edit-sex-list" bindchange="__e"><block wx:for="{{items}}" wx:for-item="item" wx:for-index="index" wx:key="value"><label class="uni-list-cell uni-list-cell-pd"><view><radio color="{{themeStyle.main_color}}" value="{{item.value}}" checked="{{index===formData.sex}}"></radio></view><view>{{item.name}}</view></label></block></radio-group></view><view data-event-opts="{{[['tap',[['save',['sex']]]]]}}" class="save-item" bindtap="__e"><button type="primary">{{$root.m10}}</button></view></view></block><block wx:if="{{indent=='birthday'}}"><view class="edit-info edit-birthday-list"><view class="edit-info-box"><text class="info-name">{{$root.m11}}</text><picker mode="date" value="{{formData.birthday}}" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{formData.birthday?formData.birthday:'请选择生日'}}</view></picker></view><view data-event-opts="{{[['tap',[['save',['birthday']]]]]}}" class="save-item" bindtap="__e"><button type="primary">{{$root.m12}}</button></view></view></block><block wx:if="{{indent=='password'}}"><view class="edit-info"><block wx:if="{{memberInfo.password==0&&memberInfo.mobile==''}}"><block><view class="empty"><view class="empty_img"><image src="{{$root.g0}}" mode="aspectFit"></image></view><view class="color-tip margin-top margin-bottom">请先绑定手机再执行该操作</view><button class="mini button color-base-bg" type="primary" size="mini" data-event-opts="{{[['tap',[['modifyInfo',['mobile']]]]]}}" bindtap="__e">立即绑定</button></view></block></block><block wx:else><block><block wx:if="{{memberInfo.password}}"><view class="edit-info-box"><text class="info-name">{{$root.m13}}</text><input class="uni-input info-content input-len" type="password" maxlength="30" placeholder="{{$root.m14}}" data-event-opts="{{[['input',[['__set_model',['$0','currentPassword','$event',[]],['formData']]]]]}}" value="{{formData.currentPassword}}" bindinput="__e"/></view></block><block wx:else><block><view class="edit-info-box"><text class="info-name">{{$root.m15}}</text><input class="uni-input info-content" type="number" maxlength="4" placeholder="{{$root.m16}}" data-event-opts="{{[['input',[['__set_model',['$0','mobileVercode','$event',[]],['formData']]]]]}}" value="{{formData.mobileVercode}}" bindinput="__e"/><image class="captcha" src="{{captcha.img}}" data-event-opts="{{[['tap',[['getCaptcha',['$event']]]]]}}" bindtap="__e"></image></view><view class="edit-info-box"><text class="info-name">{{$root.m17}}</text><input class="uni-input info-content" type="number" maxlength="6" placeholder="{{$root.m18}}" data-event-opts="{{[['input',[['__set_model',['$0','mobileDynacode','$event',[]],['formData']]]]]}}" value="{{formData.mobileDynacode}}" bindinput="__e"/><button class="dynacode" type="primary" data-event-opts="{{[['tap',[['passwordMoblieCode']]]]}}" bindtap="__e">{{formData.mobileCodeText}}</button></view><view class="color-tip font-size-goods-tag set-pass-tips">{{'点击“获取动态码”，将会向您已绑定的手机号'+$root.f0+"发送验证码"}}</view></block></block><view class="edit-info-box"><text class="info-name">{{$root.m19}}</text><input class="uni-input info-content input-len" type="password" maxlength="30" placeholder="{{$root.m20}}" data-event-opts="{{[['input',[['__set_model',['$0','newPassword','$event',[]],['formData']]]]]}}" value="{{formData.newPassword}}" bindinput="__e"/></view><view class="edit-info-box"><text class="info-name">{{$root.m21}}</text><input class="uni-input info-content input-len" type="password" maxlength="30" placeholder="{{$root.m22}}" data-event-opts="{{[['input',[['__set_model',['$0','confirmPassword','$event',[]],['formData']]]]]}}" value="{{formData.confirmPassword}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['save',['password']]]]]}}" class="save-item" bindtap="__e"><button type="primary">{{$root.m23}}</button></view></block></block></view></block><block wx:if="{{indent=='mobile'}}"><view class="edit-info"><view class="edit-info-box"><text class="info-name">{{$root.m24}}</text><input class="uni-input info-content" type="number" maxlength="11" placeholder="{{$root.m25}}" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['formData']]]]]}}" value="{{formData.mobile}}" bindinput="__e"/></view><view class="edit-info-box"><text class="info-name">{{$root.m26}}</text><input class="uni-input info-content" type="number" maxlength="4" placeholder="{{$root.m27}}" data-event-opts="{{[['input',[['__set_model',['$0','mobileVercode','$event',[]],['formData']]]]]}}" value="{{formData.mobileVercode}}" bindinput="__e"/><image class="captcha" src="{{captcha.img}}" data-event-opts="{{[['tap',[['getCaptcha',['$event']]]]]}}" bindtap="__e"></image></view><view class="edit-info-box"><text class="info-name">{{$root.m28}}</text><input class="uni-input info-content" type="number" maxlength="6" placeholder="{{$root.m29}}" data-event-opts="{{[['input',[['__set_model',['$0','mobileDynacode','$event',[]],['formData']]]]]}}" value="{{formData.mobileDynacode}}" bindinput="__e"/><button class="dynacode" type="primary" data-event-opts="{{[['tap',[['bindMobileCode']]]]}}" bindtap="__e">{{formData.mobileCodeText}}</button></view><view data-event-opts="{{[['tap',[['save',['mobile']]]]]}}" class="save-item" bindtap="__e"><button type="primary">{{$root.m30}}</button></view></view></block><block wx:if="{{indent=='bind_mobile'}}"><view class="edit-info"><view class="save-item bind-mobile"><button type="primary" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['mobileAuth',['$event']]]]]}}" bindgetphonenumber="__e">一键授权绑定</button></view><view class="save-item bind-mobile"><button type="primary" data-event-opts="{{[['tap',[['manualBinding',['$event']]]]]}}" bindtap="__e">手动绑定</button></view></view></block><block wx:if="{{indent=='address'}}"><view class="edit-info"><view class="edit-info-box"><text class="info-name">所在地区</text><pick-regions vue-id="1bdcb8fc-1" default-regions="{{defaultRegions}}" select-arr="3" data-event-opts="{{[['^getRegions',[['handleGetRegions']]]]}}" bind:getRegions="__e" bind:__l="__l" vue-slots="{{['default']}}"><text class="{{['select-address','',(!formData.fullAddress)?'color-tip':'']}}">{{''+(formData.fullAddress?formData.fullAddress:'请选择省市区县')+''}}</text></pick-regions></view><view class="edit-info-box"><text class="info-name">详细地址</text><input class="uni-input info-content" type="text" placeholder="详细地址" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['formData']]]]]}}" value="{{formData.address}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['save',['address']]]]]}}" class="save-item" bindtap="__e"><button type="primary">{{$root.m31}}</button></view></view></block></block><ns-login class="vue-ref" vue-id="1bdcb8fc-2" data-ref="login" bind:__l="__l"></ns-login></view>