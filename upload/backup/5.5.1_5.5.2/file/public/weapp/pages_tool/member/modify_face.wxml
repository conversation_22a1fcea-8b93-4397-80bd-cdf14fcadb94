<page-meta page-style="{{themeColor}}"></page-meta><view class="nc-modify-content"><view class="modify"><view><block wx:if="{{newImg==''}}"><image src="{{memberImg?$root.g0:$root.g1.head}}" mode="aspectFill" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e"></image></block><block wx:else><image src="{{$root.g2}}" mode="aspectFill" data-event-opts="{{[['error',[['e1',['$event']]]]]}}" binderror="__e"></image></block></view></view><view class="opection-box"><block wx:if="{{newImg==''}}"><block><button type="primary" data-event-opts="{{[['tap',[['chooseImage']]]]}}" bindtap="__e">点击上传</button></block></block><block wx:else><block><view class="opec"><button class="mini" size="mini" type="primary" data-event-opts="{{[['tap',[['save']]]]}}" bindtap="__e">确认保存</button><button class="mini" size="mini" type="primary" data-event-opts="{{[['tap',[['chooseImage']]]]}}" bindtap="__e">重新上传</button></view></block></block></view><img-cropping class="vue-ref" vue-id="ab90c74a-1" selWidth="300" selHeight="300" data-ref="imgCropping" data-event-opts="{{[['^upload',[['myUpload']]]]}}" bind:upload="__e" bind:__l="__l"></img-cropping></view>