<page-meta page-style="{{themeColor}}"></page-meta><view><block wx:if="{{storeToken}}"><mescroll-uni class="vue-ref" bind:getData="__e" vue-id="dd0df160-1" data-ref="mescroll" data-event-opts="{{[['^getData',[['getData']]]]}}" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><view class="nc-info-list-content"><block wx:if="{{type=='fenxiao'&&payList&&payList.balance}}"><view data-event-opts="{{[['tap',[['setBalanceDefault']]]]}}" class="list-item balance-item" bindtap="__e"><view class="item-top"><view class="item-left"><view class="title-text">提现到余额</view></view></view></view></block><block wx:if="{{$root.g0>0}}"><block><block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item"><view class="item-top"><view class="item-left"><view class="title-text">{{item.withdraw_type_name}}</view><view class="info-content"><text class="top-title">{{item.realname}}</text><text class="top-num">{{item.mobile}}</text></view><view class="content-bottom"><block wx:if="{{item.withdraw_type=='alipay'}}"><block>{{'提现账号：'+item.bank_account+''}}</block></block><block wx:if="{{item.withdraw_type=='bank'}}"><block>{{'银行名称 ：'+item.branch_bank_name+''}}</block></block></view></view><view data-event-opts="{{[['tap',[['editAccount',['edit','$0'],[[['dataList','',index,'id']]]]]]]}}" class="item-btn" catchtap="__e">修改</view></view><view class="item-bottom"><view data-event-opts="{{[['tap',[['setDefault',['$0','$1'],[[['dataList','',index,'id']],[['dataList','',index,'is_default']]]]]]]}}" class="account-default" bindtap="__e"><text class="default">设为默认账户</text><block wx:if="{{item.is_default==1}}"><switch style="transform:scale(0.7);" checked="{{true}}" disabled="{{true}}" color="{{themeStyle.main_color}}"></switch></block><block wx:else><switch style="transform:scale(0.7);" color="{{themeStyle.main_color}}"></switch></block></view><view class="account-btn"><block wx:if="{{item.is_default!=1}}"><text data-event-opts="{{[['tap',[['deleteAccount',['$0'],[[['dataList','',index,'id']]]]]]]}}" class="delete" bindtap="__e"><text class="iconfont iconicon7"></text></text></block></view></view></view></block></block></block></view><block wx:if="{{$root.g1}}"><view class="empty-box"><image src="{{$root.g2}}" mode="widthFix"></image><view class="tips">暂无账户信息，请添加</view><button class="add-account" type="primary" data-event-opts="{{[['tap',[['editAccount',['add']]]]]}}" bindtap="__e">{{$root.m0}}</button></view></block></view></mescroll-uni></block><block wx:if="{{$root.g3}}"><view class="btn-add"><button class="add-account" type="primary" data-event-opts="{{[['tap',[['editAccount',['add']]]]]}}" bindtap="__e">{{$root.m1}}</button></view></block><loading-cover class="vue-ref" vue-id="dd0df160-2" data-ref="loadingCover" bind:__l="__l"></loading-cover><ns-login class="vue-ref" vue-id="dd0df160-3" data-ref="login" bind:__l="__l"></ns-login></view>