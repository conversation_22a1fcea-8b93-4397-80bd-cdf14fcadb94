<page-meta page-style="{{themeColor}}" class="data-v-42526520"></page-meta><view class="data-v-42526520"><view class="data-v-42526520"><scroll-view class="refund-container data-v-42526520" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-wrap data-v-42526520"><view class="goods-img data-v-42526520"><image src="{{item.g0}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" binderror="__e" class="data-v-42526520"></image></view><view class="goods-info data-v-42526520"><view class="goods-name data-v-42526520">{{item.$orig.sku_name}}</view></view></view></block><view class="data-v-42526520"><view class="refund-form data-v-42526520"><view data-event-opts="{{[['tap',[['openPopup',['refundReasonPopup']]]]]}}" class="item-wrap data-v-42526520" bindtap="__e"><view class="label data-v-42526520">退款原因：</view><view class="cont reason data-v-42526520"><block wx:if="{{!$root.g1}}"><text class="color-tip data-v-42526520">请选择</text></block><block wx:else><text class="color-tip data-v-42526520">{{refund_reason}}</text></block></view><text class="iconfont icon-right data-v-42526520"></text></view><view class="item-wrap data-v-42526520"><view class="label data-v-42526520">退款方式：</view><block wx:if="{{refund_type==1}}"><view class="cont color-base-text data-v-42526520">退款无需退货</view></block><block wx:else><view class="cont color-base-text data-v-42526520">退货退款</view></block></view><view class="item-wrap data-v-42526520"><view class="label data-v-42526520">退款金额：</view><view class="cont color-base-text data-v-42526520">{{$root.m0+refund_data.refund_money}}</view></view></view><view class="refund-form data-v-42526520"><view class="item-wrap data-v-42526520"><view class="label active data-v-42526520">退款说明</view></view><block wx:if="{{!showText}}"><textarea class="newText data-v-42526520" placeholder="请输入退款说明(选填)" placeholder-class="color-tip font-size-tag" auto-height="{{true}}" data-event-opts="{{[['input',[['__set_model',['','refund_remark','$event',[]]]]]]}}" value="{{refund_remark}}" bindinput="__e"></textarea></block><view class="other-info data-v-42526520"><block wx:for="{{$root.l1}}" wx:for-item="i" wx:for-index="t" wx:key="t"><view class="other-info-box data-v-42526520"><image src="{{i.g2}}" mode="aspectFill" data-event-opts="{{[['tap',[['preview',['$0'],[[['imgList','',t]]]]]]]}}" bindtap="__e" class="data-v-42526520"></image><view data-event-opts="{{[['tap',[['deleteImg',['$0',t],[[['imgList','',t]]]]]]]}}" class="imgDel data-v-42526520" bindtap="__e"><text class="icon iconfont icon-delete data-v-42526520"></text></view></view></block><block wx:if="{{$root.g3}}"><view data-event-opts="{{[['tap',[['addImg']]]]}}" class="other-info-box active data-v-42526520" bindtap="__e"><text class="icon iconfont icon-zhaoxiangji data-v-42526520"></text><text class="data-v-42526520">{{($root.g4?5-$root.g5:0)+"/5"}}</text></view></block></view></view><view class="sub-btn-empty data-v-42526520"></view><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="{{['sub-btn','data-v-42526520',(isIphoneX)?'safe-area':'']}}" bindtap="__e"><button type="primary" class="data-v-42526520">{{$root.m1}}</button></view></view><uni-popup vue-id="6b997ab4-1" type="bottom" data-ref="refundReasonPopup" data-event-opts="{{[['^change',[['change']]]]}}" bind:change="__e" class="data-v-42526520 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="refund-reason-popup popup data-v-42526520"><view class="popup-header data-v-42526520"><view class="data-v-42526520"><text class="tit data-v-42526520">退款原因</text></view><view data-event-opts="{{[['tap',[['closePopup',['refundReasonPopup']]]]]}}" class="align-right data-v-42526520" bindtap="__e"><text class="iconfont icon-close data-v-42526520"></text></view></view><view class="popup-body data-v-42526520"><scroll-view class="{{['scroll-view','data-v-42526520',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view class="reason-list data-v-42526520"><block wx:for="{{refund_data.refund_reason_type}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeReason',['$0'],[[['refund_data.refund_reason_type','',index]]]]]]]}}" class="item data-v-42526520" bindtap="__e"><view class="reason data-v-42526520">{{item}}</view><view class="{{['iconfont','data-v-42526520',refund_reason==item?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}"></view></view></block></view></scroll-view></view><view class="{{['popup-footer','data-v-42526520',(isIphoneX)?'bottom-safe-area':'']}}"><view data-event-opts="{{[['tap',[['closePopup',['refundReasonPopup']]]]]}}" class="confirm-btn color-base-bg data-v-42526520" bindtap="__e">确定</view></view></view></uni-popup></scroll-view><loading-cover vue-id="6b997ab4-2" data-ref="loadingCover" class="data-v-42526520 vue-ref" bind:__l="__l"></loading-cover></view></view>