<page-meta page-style="{{themeColor}}" class="data-v-ccc90300"></page-meta><view class="activist-container data-v-ccc90300"><mescroll-uni bind:getData="__e" vue-id="38824208-1" data-ref="mescroll" data-event-opts="{{[['^getData',[['getListData']]]]}}" class="data-v-ccc90300 vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-ccc90300"><view class="container data-v-ccc90300"><block wx:if="{{$root.g0}}"><block class="data-v-ccc90300"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="order-item data-v-ccc90300"><view class="order-header data-v-ccc90300"><text class="status-num font-size-base data-v-ccc90300">{{item.$orig.order_no}}</text><block wx:if="{{item.$orig.refund_status==3}}"><view class="status-name data-v-ccc90300">退款成功</view></block><block wx:if="{{item.$orig.refund_status==1}}"><view class="status-name color-base-text data-v-ccc90300">退款中</view></block><block wx:if="{{item.$orig.refund_status==-1}}"><view class="status-name color-base-text data-v-ccc90300">退款失败</view></block></view><view class="goods-wrap data-v-ccc90300"><image src="{{item.g1}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e" class="data-v-ccc90300"></image><view class="goods-info data-v-ccc90300"><view data-event-opts="{{[['tap',[['refundDetail',['$0'],[[['refundList','',index,'order_goods_id']]]]]]]}}" class="goods-name data-v-ccc90300" bindtap="__e">{{item.$orig.sku_name}}</view><view class="goods-num data-v-ccc90300"><view class="num-text color-base-text data-v-ccc90300">{{item.$orig.refund_status_name}}</view><view class="num-price data-v-ccc90300"><text class="data-v-ccc90300">{{"￥"+item.$orig.price}}</text><text class="num data-v-ccc90300">{{"×"+item.$orig.num}}</text></view></view></view></view><view class="goods-btn data-v-ccc90300"><view class="btn-text data-v-ccc90300"><text class="data-v-ccc90300">{{"共"+item.$orig.num+"件商品"}}</text><text class="data-v-ccc90300">{{"退款：￥"+item.g2}}</text></view><view class="order-action data-v-ccc90300"><view data-event-opts="{{[['tap',[['refundDetail',['$0'],[[['refundList','',index,'order_goods_id']]]]]]]}}" class="order-box-btn data-v-ccc90300" bindtap="__e">{{item.m0}}</view><block wx:if="{{item.g3}}"><block class="data-v-ccc90300"><block wx:for="{{item.$orig.refund_action}}" wx:for-item="actionItem" wx:for-index="actionIndex" wx:key="actionIndex"><view data-event-opts="{{[['tap',[['refundAction',['$0','$1'],[[['refundList','',index],['refund_action','',actionIndex,'event']],[['refundList','',index]]]]]]]}}" class="order-box-btn data-v-ccc90300" bindtap="__e">{{''+actionItem.title+''}}</view></block></block></block></view></view></view></block></block></block><block wx:else><block class="data-v-ccc90300"><block wx:if="{{showEmpty}}"><view class="cart-empty data-v-ccc90300"><ns-empty vue-id="{{('38824208-2')+','+('38824208-1')}}" isIndex="{{false}}" text="{{$root.m1}}" class="data-v-ccc90300" bind:__l="__l"></ns-empty></view></block></block></block></view></view></mescroll-uni></view>