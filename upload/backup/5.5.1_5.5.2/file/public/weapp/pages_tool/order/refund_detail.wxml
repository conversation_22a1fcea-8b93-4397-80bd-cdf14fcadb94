<page-meta page-style="{{themeColor}}"></page-meta><view><block wx:if="{{detail}}"><scroll-view class="{{['detail-container',(isIphoneX)?'safe-area':'']}}" scroll-y="true"><view hidden="{{!(action=='')}}"><view class="status-wrap"><view class="status-name">{{detail.refund_status_name}}</view><block wx:if="{{detail.refund_status==1}}"><view class="refund-explain"><view class="font-size-goods-tag color-tip">如果商家拒绝，你可重新发起申请</view><view class="font-size-goods-tag color-tip">如果商家同意，将通过申请并退款给你</view></view></block><block wx:if="{{detail.refund_status==5}}"><view class="refund-explain"><view class="font-size-goods-tag color-tip">如果商家确认收货将会退款给你</view><view class="font-size-goods-tag color-tip">如果商家拒绝收货，该次退款将会关闭，你可以重新发起退款</view></view></block></view><view data-event-opts="{{[['tap',[['switchAction',['consultrecord']]]]]}}" class="history-wrap" bindtap="__e"><view>协商记录</view><text class="iconfont icon-right"></text></view><block wx:if="{{detail.refund_status==4}}"><view class="refund-address-wrap"><view class="header">退货地址</view><view><text>{{"收货人："+detail.shop_contacts}}</text></view><view><text>{{"联系方式："+detail.shop_mobile}}</text></view><view><text class="address">{{"退货地址："+detail.shop_address}}</text><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="copy" bindtap="__e">复制</view></view></view></block><view class="refund-info"><view class="header">退款信息</view><view class="body"><view class="goods-wrap"><view data-event-opts="{{[['tap',[['refundDetail',['$0'],['detail']]]]]}}" class="goods-img" bindtap="__e"><image src="{{$root.g0}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError']]]]}}" binderror="__e"></image></view><view class="goods-info"><view data-event-opts="{{[['tap',[['refundDetail',['$0'],['detail']]]]]}}" class="goods-name" bindtap="__e">{{detail.sku_name}}</view></view></view><block wx:if="{{detail.refund_apply_money>0}}"><view class="info"><view class="cell">{{"退款方式："+(detail.refund_type==1?'仅退款':'退款退货')}}</view><view class="cell">{{"申请原因："+detail.refund_reason}}</view><block wx:if="{{detail.refund_remark!=''}}"><view class="cell">{{"申请说明："+detail.refund_remark}}</view></block><view class="cell">{{"申请金额："+$root.m0+detail.refund_apply_money}}</view></view></block><block wx:if="{{detail.refund_apply_money>0}}"><view class="info refund-images"><block wx:if="{{detail.refund_images}}"><view class="cell"><view class="cell-title">退款图片：</view><view class="images"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image src="{{item.g1}}" mode="aspectFill"></image></block></view></view></block></view></block><block wx:if="{{detail.refund_apply_money>0&&detail.refund_status==3}}"><view class="info"><view class="cell">{{"退款金额："+$root.m1+detail.refund_real_money+" ("+detail.refund_money_type_name+")"}}</view><view class="cell">{{"退款说明："+(detail.shop_refund_remark||'--')}}</view><view class="cell">{{"退款编号："+detail.refund_no}}</view><view class="cell">{{"退款时间："+$root.g2}}</view><block wx:if="{{detail.use_point>0}}"><view class="cell">{{"退款积分："+detail.use_point}}</view></block></view></block><block wx:if="{{detail.shop_active_refund==1}}"><view class="info"><view class="cell">{{"主动退款编号："+detail.shop_active_refund_no}}</view><view class="cell">{{"主动退款金额：￥"+detail.shop_active_refund_money+" ("+detail.shop_active_refund_money_type_name+")"}}</view><view class="cell">{{"主动退款说明："+detail.shop_active_refund_remark}}</view></view></block></view></view><block wx:if="{{$root.g3}}"><view class="{{['action',(isIphoneX)?'bottom-safe-area':'']}}"><block wx:for="{{detail.refund_action}}" wx:for-item="actionItem" wx:for-index="actionIndex" wx:key="actionIndex"><view data-event-opts="{{[['tap',[['refundAction',['$0'],[[['detail.refund_action','',actionIndex,'event']]]]]]]}}" class="order-box-btn" bindtap="__e">{{''+actionItem.title+''}}</view></block></view></block></view><view hidden="{{!(action=='returngoods')}}"><view class="return-goods-container"><view class="form-wrap"><view class="item"><view class="label">物流公司</view><view class="cont"><input class="input" type="text" placeholder="请输入物流公司" placeholder-class="input-placeholder color-tip" data-event-opts="{{[['input',[['__set_model',['$0','refund_delivery_name','$event',[]],['formData']]]]]}}" value="{{formData.refund_delivery_name}}" bindinput="__e"/></view></view><view class="item"><view class="label">物流单号</view><view class="cont"><input class="input" type="text" placeholder="请输入物流单号" placeholder-class="input-placeholder color-tip" data-event-opts="{{[['input',[['__set_model',['$0','refund_delivery_no','$event',[]],['formData']]]]]}}" value="{{formData.refund_delivery_no}}" bindinput="__e"/></view></view><view class="item"><view class="label">物流说明</view><view class="cont"><textarea class="textarea" placeholder-class="color-tip font-size-tag" auto-height="{{true}}" placeholder="选填" data-event-opts="{{[['input',[['__set_model',['$0','refund_delivery_remark','$event',[]],['formData']]]]]}}" value="{{formData.refund_delivery_remark}}" bindinput="__e"></textarea></view></view></view><button class="sub-btn" type="primary" data-event-opts="{{[['tap',[['refurnGoods',['$event']]]]]}}" bindtap="__e">提交</button></view></view><view hidden="{{!(action=='consultrecord')}}"><view class="record-wrap"><block wx:for="{{$root.l1}}" wx:for-item="logItem" wx:for-index="logIndex" wx:key="logIndex"><view class="{{['record-item',logItem.$orig.action_way==1?'buyer':'']}}"><view class="cont"><view class="head"><text>{{logItem.$orig.action_way==1?'买家':'卖家'}}</text><text class="time">{{logItem.g4}}</text></view><view class="body"><view class="refund-action">{{logItem.$orig.action}}</view><block wx:if="{{logItem.$orig.desc!=''}}"><view class="desc">{{logItem.$orig.desc}}</view></block></view></view></view></block><view class="empty-box"></view></view><view class="{{['history-bottom',(isIphoneX)?'bottom-safe-area':'']}}"><ns-contact vue-id="651f4106-1" niushop="{{$root.a0}}" bind:__l="__l" vue-slots="{{['default']}}"><view><text class="iconfont icon-ziyuan"></text><text>联系客服</text></view></ns-contact><view data-event-opts="{{[['tap',[['switchAction',['']]]]]}}" bindtap="__e">返回详情</view></view></view></scroll-view></block><loading-cover class="vue-ref" vue-id="651f4106-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>