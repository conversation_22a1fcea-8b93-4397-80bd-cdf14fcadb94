require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/order/logistics"],{"2ba2":function(t,e,i){"use strict";i.r(e);var n=i("8292"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},5513:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={loadingCover:function(){return i.e("components/loading-cover/loading-cover").then(i.bind(null,"c003"))}},o=function(){var t=this,e=t.$createElement,i=(t._self._c,t.__map(t.packageList,(function(e,i){var n=t.__get_orig(e),o=t.__map(e.goods_list,(function(e,i){var n=t.__get_orig(e),o=t.$util.img(e.sku_image,{size:"mid"});return{$orig:n,g0:o}})),r=1==e.delivery_type?t.$util.img(e.express_company_image):null,a=1==e.delivery_type?e.trace.success&&0!=e.trace.list.length:null,s=1!=e.delivery_type||a?null:e.trace.success&&0==e.trace.list.length;return{$orig:n,l0:o,g1:r,g2:a,g3:s}})));t.$mp.data=Object.assign({},{$root:{l1:i}})},r=[]},7643:function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("d381");n(i("3240"));var o=n(i("920a"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},8292:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{orderId:"",packageList:[],isIphoneX:!1,currIndex:0,status:0}},onLoad:function(t){t.order_id&&(this.orderId=t.order_id)},onShow:function(){this.storeToken?this.getPackageInfo():this.$util.redirectTo("/pages_tool/login/index"),this.isIphoneX=this.$util.uniappIsIPhoneX()},methods:{ontabtap:function(t){this.currIndex=t},getPackageInfo:function(){var t=this;this.$api.sendRequest({url:"/api/order/package",data:{order_id:this.orderId},success:function(e){e.code>=0?(t.packageList=e.data,t.packageList.forEach((function(e){e.trace.list&&(e.trace.list=e.trace.list.reverse()),e.status=t.status++})),t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:"未获取到订单信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/order/list")}),1500))},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},toGoodsDetail:function(t){this.$util.redirectTo("/pages/goods/detail",{sku_id:t})},imageError:function(t,e){this.packageList[t].goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},copyDeliveryNo:function(t){this.$util.copy(t)}}};e.default=n},"920a":function(t,e,i){"use strict";i.r(e);var n=i("5513"),o=i("2ba2");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("97d2");var a=i("828b"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"97d2":function(t,e,i){"use strict";var n=i("d215"),o=i.n(n);o.a},d215:function(t,e,i){}},[["7643","common/runtime","common/vendor"]]]);