<page-meta page-style="{{themeColor}}"></page-meta><view><scroll-view class="order-nav" scroll-x="{{true}}" show-scrollbar="{{false}}"><block wx:for="{{packageList}}" wx:for-item="packageItem" wx:for-index="packageIndex" wx:key="packageIndex"><view data-event-opts="{{[['tap',[['ontabtap',[packageIndex]]]]]}}" class="uni-tab-item" bindtap="__e"><text class="{{['uni-tab-item-title',packageIndex==currIndex?'uni-tab-item-title-active color-base-border  color-base-text':'']}}">{{''+packageItem.package_name+''}}</text></view></block></scroll-view><block wx:for="{{$root.l1}}" wx:for-item="packageItem" wx:for-index="packageIndex" wx:key="packageIndex"><view hidden="{{!(packageIndex==currIndex)}}" class="swiper-item"><view class="container"><view class="goods-wrap"><view class="body"><block wx:for="{{packageItem.l0}}" wx:for-item="goodsItem" wx:for-index="goodsIndex" wx:key="goodsIndex"><view class="goods"><view data-event-opts="{{[['tap',[['toGoodsDetail',['$0'],[[['packageList','',packageIndex],['goods_list','',goodsIndex,'sku_id']]]]]]]}}" class="goods-img" bindtap="__e"><image src="{{goodsItem.g0}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[packageIndex,goodsIndex]]]]]}}" binderror="__e"></image></view><view class="goods-info"><view data-event-opts="{{[['tap',[['toGoodsDetail',['$0'],[[['packageList','',packageIndex],['goods_list','',goodsIndex,'sku_id']]]]]]]}}" class="goods-name" bindtap="__e">{{goodsItem.$orig.sku_name}}</view><view class="goods-sub-section"><view><text><text class="iconfont icon-close"></text>{{''+goodsItem.$orig.num+''}}</text></view></view></view></view></block></view></view><block wx:if="{{packageItem.$orig.delivery_type==1}}"><view class="express-company-wrap"><view class="company-logo"><image src="{{packageItem.g1}}"></image></view><view class="info"><view class="company"><text>{{"承运公司： "+packageItem.$orig.express_company_name}}</text></view><view class="no"><text>运单号：<text class="color-tip">{{packageItem.$orig.delivery_no}}</text></text><text data-event-opts="{{[['tap',[['copyDeliveryNo',['$0'],[[['packageList','',packageIndex,'delivery_no']]]]]]]}}" class="iconfont icon-fuzhi" bindtap="__e"></text></view></view></view></block><block wx:if="{{packageItem.$orig.delivery_type==1}}"><view class="track-wrap"><block wx:if="{{packageItem.g2}}"><block><block wx:for="{{packageItem.$orig.trace.list}}" wx:for-item="traceItem" wx:for-index="traceIndex" wx:key="traceIndex"><view class="{{['track-item',traceIndex==0?'active':'']}}"><view class="{{['dot',traceIndex==0?'color-base-bg':'']}}"></view><view class="msg"><view class="{{['text',traceIndex==0?'color-base-text':'']}}">{{traceItem.remark}}</view><view class="{{['time',traceIndex==0?'color-base-text':'']}}">{{traceItem.datetime}}</view></view></view></block></block></block><block wx:else><block wx:if="{{packageItem.g3}}"><block><view class="fail-wrap font-size-base">{{packageItem.$orig.trace.reason}}</view></block></block><block wx:else><block><view class="fail-wrap font-size-base">{{packageItem.$orig.trace.reason}}</view></block></block></block></view></block></view></view></block><loading-cover class="vue-ref" vue-id="747bb8c2-1" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>