<page-meta page-style="{{themeColor}}" class="data-v-285f1912"></page-meta><view class="data-v-285f1912"><view class="data-v-285f1912"><view class="refund-option data-v-285f1912"><view data-event-opts="{{[['tap',[['selectRefundType',[1]]]]]}}" class="option-item data-v-285f1912" bindtap="__e"><view class="data-v-285f1912"><text class="data-v-285f1912">退款无需退货</text><text class="font-size-goods-tag color-tip data-v-285f1912">没收到货，或与卖家协商同意无需退货只退款</text></view><text class="iconfont icon-right data-v-285f1912"></text></view><block wx:if="{{refund_data.delivery_status>0}}"><view data-event-opts="{{[['tap',[['selectRefundType',[2]]]]]}}" class="option-item data-v-285f1912" bindtap="__e"><view class="data-v-285f1912"><text class="data-v-285f1912">退货退款</text><text class="font-size-goods-tag color-tip data-v-285f1912">已收到货，需退还收到的货物</text></view><text class="iconfont icon-right data-v-285f1912"></text></view></block></view><loading-cover vue-id="f21dc6a6-1" data-ref="loadingCover" class="data-v-285f1912 vue-ref" bind:__l="__l"></loading-cover></view></view>