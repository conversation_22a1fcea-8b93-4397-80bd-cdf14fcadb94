<page-meta page-style="{{themeColor}}"></page-meta><view><view><view class="page"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="eval-wrap"><view class="eval-good"><view class="good-box"><image class="good_pic" src="{{item.g0}}" mode="widthFix" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image><view class="good_info font-size-base">{{item.$orig.sku_name}}</view></view></view><block wx:if="{{!isEvaluate}}"><view class="eval-star"><view class="star-box"><view class="star-title color-base-bg-before">描述相符</view><view class="rate-box"><sx-rate vue-id="{{'47dd020e-1-'+index}}" value="{{goodsEvalList[index].scores}}" index="{{index}}" data-event-opts="{{[['^change',[['setStar']]]]}}" bind:change="__e" bind:__l="__l"></sx-rate></view><view class="grade-li"><view class="{{['icon','iconfont',goodsEvalList[index].explain_type=='1'?'icon-haoping1 color-base-text':goodsEvalList[index].explain_type=='2'?'icon-zhongchaping color-base-text':goodsEvalList[index].explain_type=='3'?'icon-zhongchaping':'']}}"></view><block wx:if="{{goodsEvalList[index].explain_type=='1'}}"><view class="font-size-tag color-base-text">好评</view></block><block wx:if="{{goodsEvalList[index].explain_type=='2'}}"><view class="font-size-tag color-base-text">中评</view></block><block wx:if="{{goodsEvalList[index].explain_type=='3'}}"><view class="font-size-tag color-base-text">差评</view></block></view></view></view></block></view><view class="eval-text"><view class="text-box"><block wx:if="{{!isEvaluate}}"><block><textarea placeholder="请在此处输入您的评价" maxlength="200" data-event-opts="{{[['input',[['__set_model',['$0','content','$event',[]],['goodsEvalList.'+index+'']]]]]}}" value="{{goodsEvalList[index].content}}" bindinput="__e"></textarea><text class="maxSize">{{item.g1+"/200"}}</text></block></block><block wx:else><block><textarea placeholder="请在此处输入您的追评" maxlength="200" data-event-opts="{{[['input',[['__set_model',['$0','again_content','$event',[]],['goodsEvalList.'+index+'']]]]]}}" value="{{goodsEvalList[index].again_content}}" bindinput="__e"></textarea><text class="maxSize">{{item.g2+"/200"}}</text></block></block><view class="other-info"><block wx:for="{{item.l0}}" wx:for-item="i" wx:for-index="t" wx:key="t"><view class="other-info-box"><image src="{{i.g3}}" mode="aspectFill" data-event-opts="{{[['tap',[['preview',['$0',index],[[['imgList.'+index+'','',t]]]]]]]}}" bindtap="__e"></image><view data-event-opts="{{[['tap',[['deleteImg',['$0',index,t],[[['imgList.'+index+'','',t]]]]]]]}}" class="imgDel" bindtap="__e"><text class="icon iconfont icon-delete"></text></view></view></block><block wx:if="{{item.g4}}"><view data-event-opts="{{[['tap',[['addImg',[index]]]]]}}" class="other-info-box active" bindtap="__e"><text class="icon iconfont icon-zhaoxiangji"></text><text>{{(item.g5?6-item.g6:0)+"/6"}}</text></view></block></view></view></view></block></block></view><view class="{{['eval-bottom',(isIphoneX)?'safe-area':'']}}"><block wx:if="{{!isEvaluate}}"><view data-event-opts="{{[['tap',[['isAll']]]]}}" class="all-election" bindtap="__e"><view class="{{['iconfont','color-base-text',isAnonymous?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}"></view><text>匿名</text></view></block><view class="action-btn"><button type="primary" data-event-opts="{{[['tap',[['save']]]]}}" bindtap="__e">提交</button></view></view><loading-cover class="vue-ref" vue-id="47dd020e-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view></view>