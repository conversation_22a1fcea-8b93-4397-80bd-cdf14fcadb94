(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/common/vendor"],{1575:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={data:function(){return{collectionList:[],isShowEmpty:!1}},methods:{toDetail:function(e){this.$util.redirectTo("/pages/goods/detail",{goods_id:e.goods_id})},getData:function(e){var t=this;this.isShowEmpty=!1;var i=[];this.$api.sendRequest({url:"/api/goodscollect/page",data:{page_size:e.size,page:e.num},async:!1}).then((function(a){for(var s=a.data.list,o=0;o<s.length;o++)s[o].composite_score=Math.floor((parseFloat(s[o].shop_desccredit)+parseFloat(s[o].shop_servicecredit)+parseFloat(s[o].shop_deliverycredit))/3).toFixed(1);i=i.concat(s),1==e.num&&(t.collectionList=[]),t.collectionList=t.collectionList.concat(s),e.endSuccess(i.length),t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.isShowEmpty=!0}))},listenRefresh:function(e){this.$refs.goodsRecommend.init()},deleteItem:function(e){var t=this;this.$api.sendRequest({url:"/api/goodscollect/delete",data:{goods_id:e},success:function(i){if(0==i.code){t.$util.showToast({title:"删除成功"});var a=t.collectionList,s=a.filter((function(t){return t.goods_id!=e}));t.collectionList=s}else t.$util.showToast({title:i.message})}})},imageError:function(e){this.collectionList[e].logo=this.$util.getDefaultImage().goods,this.$forceUpdate()},goodsImageError:function(e){this.collectionList[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}}};t.default=a},1987:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={methods:{getList:function(){var e=this;this.$api.sendRequest({url:"/memberrecommend/api/memberrecommend/lists",data:{page:this.page,page_size:this.page_size},success:function(t){e.inviteList=e.inviteList.concat(t.data.list),e.total_num=t.data.page_count}})},moreList:function(){this.page++,this.isClick=!1,this.page<this.total_num?(this.getList(),this.isClick=!0):this.page==this.total_num&&this.getList()},getBaseInfo:function(){var e=this;this.$api.sendRequest({url:"/memberrecommend/api/memberrecommend/info",success:function(t){0==t.code&&(e.info=t.data,e.$refs.loadingCover&&e.$refs.loadingCover.hide())}})},openSharePopup:function(){this.$refs.sharePopup.open()},closeSharePopup:function(){this.$refs.sharePopup.close()},openRulePopup:function(){this.$refs.rulePopup.open()},closeRulePopup:function(){this.$refs.rulePopup.close()},copyUrl:function(){var e=this,t=this.$config.h5Domain+"/pages/index/index";this.memberInfo&&this.memberInfo.member_id&&(t+="?source_member="+this.memberInfo.member_id),this.$util.copy(t,(function(){e.closeSharePopup()}))},openPosterPopup:function(){this.getPoster(),this.$refs.sharePopup.close(),this.$refs.posterPopup.open()},closePosterPopup:function(){this.$refs.posterPopup.close()},getPoster:function(){var e=this,t={source_member:this.memberInfo.member_id};this.$api.sendRequest({url:"/memberrecommend/api/memberrecommend/poster",data:{page:"/pages/index/index",qrcode_param:JSON.stringify(t)},success:function(t){0==t.code?e.poster=t.data.path+"?time="+(new Date).getTime():e.posterMsg=t.message}})},savePoster:function(){var t=this,i=this.$util.img(this.poster);e.downloadFile({url:i,success:function(i){200===i.statusCode&&e.saveImageToPhotosAlbum({filePath:i.tempFilePath,success:function(){t.$util.showToast({title:"保存成功"})},fail:function(){t.$util.showToast({title:"保存失败，请稍后重试"})}})}})}}};t.default=i}).call(this,i("df3c")["default"])},3291:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={emjoyList:{"[emjoy_01]":"public/static/img/emjoy/emjoy_01.gif","[emjoy_02]":"public/static/img/emjoy/emjoy_02.gif","[emjoy_03]":"public/static/img/emjoy/emjoy_03.gif","[emjoy_04]":"public/static/img/emjoy/emjoy_04.gif","[emjoy_05]":"public/static/img/emjoy/emjoy_05.gif","[emjoy_06]":"public/static/img/emjoy/emjoy_06.gif","[emjoy_07]":"public/static/img/emjoy/emjoy_07.gif","[emjoy_08]":"public/static/img/emjoy/emjoy_08.gif","[emjoy_09]":"public/static/img/emjoy/emjoy_09.gif","[emjoy_10]":"public/static/img/emjoy/emjoy_10.gif","[emjoy_11]":"public/static/img/emjoy/emjoy_11.gif","[emjoy_12]":"public/static/img/emjoy/emjoy_12.gif","[emjoy_13]":"public/static/img/emjoy/emjoy_13.gif","[emjoy_14]":"public/static/img/emjoy/emjoy_14.gif","[emjoy_15]":"public/static/img/emjoy/emjoy_15.gif","[emjoy_16]":"public/static/img/emjoy/emjoy_16.gif","[emjoy_17]":"public/static/img/emjoy/emjoy_17.gif","[emjoy_18]":"public/static/img/emjoy/emjoy_18.gif","[emjoy_19]":"public/static/img/emjoy/emjoy_19.gif","[emjoy_20]":"public/static/img/emjoy/emjoy_20.gif","[emjoy_21]":"public/static/img/emjoy/emjoy_21.gif","[emjoy_22]":"public/static/img/emjoy/emjoy_22.gif","[emjoy_23]":"public/static/img/emjoy/emjoy_23.gif","[emjoy_24]":"public/static/img/emjoy/emjoy_24.gif","[emjoy_25]":"public/static/img/emjoy/emjoy_25.gif","[emjoy_26]":"public/static/img/emjoy/emjoy_26.gif","[emjoy_27]":"public/static/img/emjoy/emjoy_27.gif","[emjoy_28]":"public/static/img/emjoy/emjoy_28.gif","[emjoy_29]":"public/static/img/emjoy/emjoy_29.gif","[emjoy_30]":"public/static/img/emjoy/emjoy_30.gif","[emjoy_31]":"public/static/img/emjoy/emjoy_31.gif","[emjoy_32]":"public/static/img/emjoy/emjoy_32.gif","[emjoy_33]":"public/static/img/emjoy/emjoy_33.gif","[emjoy_34]":"public/static/img/emjoy/emjoy_34.gif","[emjoy_35]":"public/static/img/emjoy/emjoy_35.gif","[emjoy_36]":"public/static/img/emjoy/emjoy_36.gif","[emjoy_37]":"public/static/img/emjoy/emjoy_37.gif","[emjoy_38]":"public/static/img/emjoy/emjoy_38.gif","[emjoy_39]":"public/static/img/emjoy/emjoy_39.gif","[emjoy_40]":"public/static/img/emjoy/emjoy_40.gif","[emjoy_41]":"public/static/img/emjoy/emjoy_41.gif","[emjoy_42]":"public/static/img/emjoy/emjoy_42.gif","[emjoy_43]":"public/static/img/emjoy/emjoy_43.gif","[emjoy_44]":"public/static/img/emjoy/emjoy_44.gif","[emjoy_45]":"public/static/img/emjoy/emjoy_45.gif","[emjoy_46]":"public/static/img/emjoy/emjoy_46.gif","[emjoy_47]":"public/static/img/emjoy/emjoy_47.gif"}}},4239:function(e,t,i){"use strict";(function(e){var a=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=a(i("7eb4")),o=a(i("ee10")),n=a(i("fe8d")),r={data:function(){return{registerConfig:{},indent:"all",customNavTitle:"",formData:{userHeadImg:"",number:"",nickName:"",sex:"",realName:"",birthday:"",currentPassword:"",newPassword:"",confirmPassword:"",mobile:"",mobileVercode:"",mobileDynacode:"",mobileCodeText:"",username:"",provinceId:0,cityId:0,districtId:0,fullAddress:"",address:""},memberInfoformData:{userHeadImg:"",number:"",nickName:"",sex:"",realName:"",birthday:"",currentPassword:"",newPassword:"",confirmPassword:"",mobile:"",mobileVercode:"",mobileDynacode:"",mobileCodeText:""},langList:[],langIndex:0,seconds:120,timer:null,isSend:!1,captcha:{id:"",img:""},isIphoneX:!1,items:[{value:"0",name:"未知"},{value:"1",name:"男",checked:"true"},{value:"2",name:"女"}],current:0,memberConfig:{is_audit:0,is_enable:0},defaultRegions:[]}},onLoad:function(e){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.formData.mobileCodeText=this.$lang("findanimateCode"),e.back&&(this.back=e.back),this.getCaptcha(),e.action&&(this.indent=e.action,this.setNavbarTitle(),"bind_mobile"==e.action&&this.getCode()),this.getRegisterConfig(),this.initLang(),this.getMemberConfig()},onShow:function(){var e=this;this.storeToken?this.getInfo():this.$nextTick((function(){e.$refs.login.open("/pages_tool/member/info")}))},onHide:function(){this.seconds=120,this.formData.mobileCodeText="获取动态码",this.isSend=!1,clearInterval(this.timer)},watch:{seconds:function(e){0==e&&(this.seconds=120,this.formData.mobileCodeText="获取动态码",this.isSend=!1,clearInterval(this.timer))}},computed:{startDate:function(){return this.getDate("start")},endDate:function(){return this.getDate("end")}},filters:{mobile:function(e){return e.substring(0,3)+"****"+e.substring(7)}},methods:{initLang:function(){if(this.langList=this.$langConfig.list(),e.getStorageSync("lang")){for(var t=0;t<this.langList.length;t++)if(this.langList[t].value==e.getStorageSync("lang")){this.langIndex=t;break}}else this.langIndex=0},setNavbarTitle:function(){var t="个人资料";switch(this.indent){case"name":t=this.$lang("modifyNickname");break;case"realName":t=this.$lang("realName");break;case"sex":t=this.$lang("sex");break;case"birthday":t=this.$lang("birthday");break;case"password":t=this.$lang("password");break;case"mobile":t=this.$lang("mobile");break}e.setNavigationBarTitle({title:t})},getInfo:function(){this.memberInfoformData.userHeadImg=this.memberInfo.headimg,this.memberInfoformData.number=this.memberInfo.username,this.memberInfoformData.nickName=this.memberInfo.nickname,this.memberInfoformData.realName=this.memberInfo.realname?this.memberInfo.realname:"请输入真实姓名",this.memberInfoformData.sex=0==this.memberInfo.sex?"未知":1==this.memberInfo.sex?"男":"女",this.memberInfoformData.birthday=this.memberInfo.birthday?this.$util.timeStampTurnTime(this.memberInfo.birthday,"Y-m-d"):"请选择生日",this.memberInfoformData.mobile=this.memberInfo.mobile,this.formData.username=this.memberInfo.username,this.formData.nickName=this.memberInfo.nickname,this.formData.realName=this.memberInfo.realname,this.formData.sex=this.memberInfo.sex,this.formData.birthday=this.memberInfo.birthday?this.$util.timeStampTurnTime(this.memberInfo.birthday,"Y-m-d"):"",this.formData.provinceId=this.memberInfo.province_id,this.formData.cityId=this.memberInfo.city_id,this.formData.districtId=this.memberInfo.district_id,this.formData.fullAddress=this.memberInfo.full_address,this.formData.address=this.memberInfo.address,this.memberInfo.full_address&&(this.defaultRegions=[this.memberInfo.province_id,this.memberInfo.city_id,this.memberInfo.district_id])},modifyInfo:function(t){switch(t){case"cancellation":this.getCancelStatus();break;case"language":for(var i=[],a=0;a<this.langList.length;a++)i.push(this.langList[a].name);e.showActionSheet({itemList:i,success:function(e){vm.langIndex!=e.tapIndex&&vm.$langConfig.change(vm.langList[e.tapIndex].value)}});break;case"mobile":this.$util.redirectTo("/pages_tool/member/info_edit",{action:"bind_mobile"});break;default:this.$util.redirectTo("/pages_tool/member/info_edit",{action:t})}},getCancelStatus:function(){var e=this;this.$api.sendRequest({url:"/membercancel/api/membercancel/info",success:function(t){t.code>=0&&(t.data?0==t.data.status?e.$util.redirectTo("/pages_tool/member/cancelstatus",{back:"/pages_tool/member/info"}):1==t.data.status?e.$util.redirectTo("/pages_tool/member/cancelsuccess",{back:"/pages_tool/member/info"}):e.$util.redirectTo("/pages_tool/member/cancelrefuse",{back:"/pages_tool/member/info"}):e.$util.redirectTo("/pages_tool/member/cancellation",{back:"/pages_tool/member/info"}))}})},NavReturn:function(){e.navigateBack({delta:1})},getCaptcha:function(){var e=this;this.$api.sendRequest({url:"/api/captcha/captcha",data:{captcha_id:this.captcha.id},success:function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))}})},logout:function(){var t=this;e.showModal({title:"提示",content:"确定要退出登录吗",success:function(e){e.confirm&&(t.$store.commit("setToken",""),t.$store.commit("setMemberInfo",""),t.$store.dispatch("emptyCart"),t.$util.redirectTo("/pages/member/index"))}})},headImage:function(){this.$util.redirectTo("/pages_tool/member/modify_face")},testBinding:function(e){var t=this;return(0,o.default)(s.default.mark((function e(){var i;return s.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.checkMobile();case 2:return i=e.sent,e.abrupt("return",i);case 4:case"end":return e.stop()}}),e)})))()},getMemberConfig:function(){var e=this;this.$api.sendRequest({url:"/membercancel/api/membercancel/config",success:function(t){t.code>=0&&(e.memberConfig=t.data)}})},save:function(e){switch(e){case"username":this.modifyUserName();break;case"name":this.modifyNickName();break;case"realName":this.modifyRealName();break;case"sex":this.modifySex();break;case"birthday":this.modifyBirthday();break;case"password":this.modifyPassword();break;case"mobile":this.modifyMobile();break;case"address":this.modifyAddress();break}},modifyUserName:function(){var e=this;if(this.formData.username!=this.memberInfo.username){var t=[{name:"username",checkType:"required",errorMsg:this.$lang("noEmityUsername")}];if(t.length){var i=n.default.check(this.formData,t);i?this.$api.sendRequest({url:"/api/member/modifyusername",data:{username:this.formData.username},success:function(t){0==t.code?(e.memberInfo.username=e.formData.username,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}}):this.$util.showToast({title:n.default.error})}}else this.$util.showToast({title:this.$lang("alikeusername")})},modifyNickName:function(){var e=this;if(this.formData.nickName!=this.memberInfo.nickname){var t=[{name:"nickName",checkType:"required",errorMsg:this.$lang("noEmityNickname")}];if(t.length){var i=n.default.check(this.formData,t);i?this.$api.sendRequest({url:"/api/member/modifynickname",data:{nickname:this.formData.nickName},success:function(t){0==t.code?(e.memberInfo.nickname=e.formData.nickName,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}}):this.$util.showToast({title:n.default.error})}}else this.$util.showToast({title:this.$lang("alikeNickname")})},modifyRealName:function(){var e=this;if(this.formData.realName==this.memberInfo.realname&&this.memberInfo.realname)this.$util.showToast({title:"与原真实姓名一致，无需修改"});else{var t=[{name:"realName",checkType:"required",errorMsg:"真实姓名不能为空"}];if(t.length){var i=n.default.check(this.formData,t);i?this.$api.sendRequest({url:"/api/member/modifyrealname",data:{realname:this.formData.realName},success:function(t){0==t.code?(e.memberInfo.realname=e.formData.realName,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}}):this.$util.showToast({title:n.default.error})}}},radioChange:function(e){for(var t=0;t<this.items.length;t++)if(this.items[t].value===e.target.value){this.formData.sex=t;break}},modifySex:function(){var e=this;this.$api.sendRequest({url:"/api/member/modifysex",data:{sex:this.formData.sex},success:function(t){0==t.code?(e.memberInfo.sex=e.formData.sex,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}})},bindDateChange:function(e){this.formData.birthday=e.target.value},getDate:function(e){var t=new Date,i=t.getFullYear(),a=t.getMonth()+1,s=t.getDate();return"start"===e?i-=60:"end"===e&&(i+=2),a=a>9?a:"0"+a,s=s>9?s:"0"+s,"".concat(i,"-").concat(a,"-").concat(s)},modifyBirthday:function(){var e=this;0!=this.formData.birthday.length?this.$api.sendRequest({url:"/api/member/modifybirthday",data:{birthday:this.$util.timeTurnTimeStamp(this.formData.birthday)},success:function(t){0==t.code?(e.memberInfo.birthday=e.$util.timeTurnTimeStamp(e.formData.birthday),e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}}):this.$util.showToast({title:"请选择生日"})},getRegisterConfig:function(){var e=this;this.$api.sendRequest({url:"/api/register/config",success:function(t){t.code>=0&&(e.registerConfig=t.data.value)}})},modifyPassword:function(){var t=this;if(this.memberInfo.password)var i=[{name:"currentPassword",checkType:"required",errorMsg:this.$lang("pleaseInputOldPassword")},{name:"newPassword",checkType:"required",errorMsg:this.$lang("pleaseInputNewPassword")}];else i=[{name:"mobileVercode",checkType:"required",errorMsg:this.$lang("confirmCodeInput")},{name:"mobileDynacode",checkType:"required",errorMsg:this.$lang("animateCodeInput")},{name:"newPassword",checkType:"required",errorMsg:this.$lang("pleaseInputNewPassword")}];var a=this.registerConfig;if(a.pwd_len>0&&i.push({name:"newPassword",checkType:"lengthMin",checkRule:a.pwd_len,errorMsg:"新密码长度不能小于"+a.pwd_len+"位"}),a.pwd_complexity){var s="密码需包含",o="";-1!=a.pwd_complexity.indexOf("number")&&(o+="(?=.*?[0-9])",s+="数字"),-1!=a.pwd_complexity.indexOf("letter")&&(o+="(?=.*?[a-z])",s+="、小写字母"),-1!=a.pwd_complexity.indexOf("upper_case")&&(o+="(?=.*?[A-Z])",s+="、大写字母"),-1!=a.pwd_complexity.indexOf("symbol")&&(o+="(?=.*?[#?!@$%^&*-])",s+="、特殊字符"),i.push({name:"newPassword",checkType:"reg",checkRule:o,errorMsg:s})}var r=n.default.check(this.formData,i);if(r){if(this.formData.currentPassword==this.formData.newPassword)return void this.$util.showToast({title:"新密码不能与原密码相同"});if(this.formData.newPassword!=this.formData.confirmPassword)return void this.$util.showToast({title:"两次密码不一致"});this.$api.sendRequest({url:"/api/member/modifypassword",data:{new_password:this.formData.newPassword,old_password:this.formData.currentPassword,code:this.formData.mobileDynacode,key:e.getStorageSync("password_mobile_key")},success:function(i){0==i.code?(t.memberInfo.password=1,t.$store.commit("setMemberInfo",t.memberInfo),t.$util.showToast({title:t.$lang("updateSuccess")}),t.NavReturn(),e.removeStorageSync("password_mobile_key")):(t.$util.showToast({title:i.message}),t.getCaptcha())}})}else this.$util.showToast({title:n.default.error})},verifyMobile:function(){var e=n.default.check(this.formData,[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}]);return!!e||(this.$util.showToast({title:n.default.error}),!1)},checkMobile:function(){var e=this;return(0,o.default)(s.default.mark((function t(){var i;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.verifyMobile()){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,e.$api.sendRequest({url:"/api/member/checkmobile",data:{mobile:e.formData.mobile},async:!1});case 4:if(i=t.sent,0==i.code){t.next=8;break}return e.$util.showToast({title:i.message}),t.abrupt("return",!1);case 8:return t.abrupt("return",!0);case 9:case"end":return t.stop()}}),t)})))()},bindMobileCode:function(){var t=this;return(0,o.default)(s.default.mark((function i(){var a,o;return s.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(120==t.seconds){i.next=2;break}return i.abrupt("return");case 2:a=[{name:"mobile",checkType:"phoneno",errorMsg:t.$lang("surePhoneNumber")},{name:"mobileVercode",checkType:"required",errorMsg:t.$lang("confirmCodeInput")}],o=n.default.check(t.formData,a),o&&!t.isSend?(t.isSend=!0,t.$api.sendRequest({url:"/api/member/bindmobliecode",data:{mobile:t.formData.mobile,captcha_id:t.captcha.id,captcha_code:t.formData.mobileVercode},success:function(i){var a=i.data;a.key?(120==t.seconds&&null==t.timer&&(t.timer=setInterval((function(){t.seconds--,t.formData.mobileCodeText="已发送("+t.seconds+"s)"}),1e3)),e.setStorageSync("mobile_key",a.key)):(t.$util.showToast({title:i.message}),t.isSend=!1)},fail:function(e){t.isSend=!1,t.getCaptcha()}})):t.$util.showToast({title:n.default.error?n.default.error:"请勿重复点击"});case 5:case"end":return i.stop()}}),i)})))()},modifyMobile:function(){var t=this;return(0,o.default)(s.default.mark((function i(){var a,o;return s.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(a=[{name:"mobile",checkType:"phoneno",errorMsg:t.$lang("surePhoneNumber")},{name:"mobileVercode",checkType:"required",errorMsg:t.$lang("confirmCodeInput")},{name:"mobileDynacode",checkType:"required",errorMsg:t.$lang("animateCodeInput")}],o=n.default.check(t.formData,a),!o){i.next=9;break}if(t.formData.mobile!=t.memberInfo.mobile){i.next=6;break}return t.$util.showToast({title:t.$lang("alikePhone")}),i.abrupt("return");case 6:t.$api.sendRequest({url:"/api/member/modifymobile",data:{mobile:t.formData.mobile,captcha_id:t.captcha.id,captcha_code:t.formData.mobileVercode,code:t.formData.mobileDynacode,key:e.getStorageSync("mobile_key")},success:function(e){0==e.code?(t.memberInfo.mobile=t.formData.mobile,t.$store.commit("setMemberInfo",t.memberInfo),t.$util.showToast({title:t.$lang("updateSuccess")}),t.back?t.$util.redirectTo("/pages_tool/member/pay_password",{back:t.back},"redirectTo"):t.NavReturn()):(t.$util.showToast({title:e.message}),t.getCaptcha())},fail:function(e){t.isSend=!1,t.getCaptcha()}}),i.next=10;break;case 9:t.$util.showToast({title:n.default.error});case 10:case"end":return i.stop()}}),i)})))()},passwordMoblieCode:function(){var t=this;120==this.seconds&&(""!=this.formData.mobileVercode?this.isSend?this.$util.showToast({title:"请勿重复点击"}):(this.isSend=!0,this.$api.sendRequest({url:"/api/member/pwdmobliecode",data:{captcha_id:this.captcha.id,captcha_code:this.formData.mobileVercode},success:function(i){var a=i.data;a.key?(120==t.seconds&&null==t.timer&&(t.timer=setInterval((function(){t.seconds--,t.formData.mobileCodeText="已发送("+t.seconds+"s)"}),1e3)),e.setStorageSync("password_mobile_key",a.key)):(t.$util.showToast({title:i.message}),t.isSend=!1)},fail:function(e){t.isSend=!1,t.getCaptcha()}})):this.$util.showToast({title:this.$lang("confirmCodeInput")}))},modifyAddress:function(){var e=this,t=[{name:"fullAddress",checkType:"required",errorMsg:"请选择所在地区"},{name:"address",checkType:"required",errorMsg:"请输入详细地址"}];if(t.length){var i=n.default.check(this.formData,t);i?this.$api.sendRequest({url:"/api/member/modifyaddress",data:{province_id:this.formData.provinceId,city_id:this.formData.cityId,district_id:this.formData.districtId,address:this.formData.address,full_address:this.formData.fullAddress},success:function(t){0==t.code?(e.memberInfo.province_id=e.formData.provinceId,e.memberInfo.city_id=e.formData.cityId,e.memberInfo.district_id=e.formData.districtId,e.memberInfo.address=e.formData.address,e.memberInfo.full_address=e.formData.fullAddress,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}}):this.$util.showToast({title:n.default.error})}},initFormData:function(){this.formData.currentPassword="",this.formData.newPassword="",this.formData.confirmPassword="",this.formData.mobileVercode="",this.formData.mobileDynacode="",this.formData.mobile=""},handleGetRegions:function(e){this.formData.fullAddress="",this.formData.fullAddress+=void 0!=e[0]?e[0].label:"",this.formData.fullAddress+=void 0!=e[1]?"-"+e[1].label:"",this.formData.fullAddress+=void 0!=e[2]?"-"+e[2].label:"",this.formData.provinceId=e[0]?e[0].value:0,this.formData.cityId=e[1]?e[1].value:0,this.formData.districtId=e[2]?e[2].value:0},manualBinding:function(){this.indent="mobile"},mobileAuth:function(e){var t=this;if("getPhoneNumber:ok"==e.detail.errMsg){var i=e.detail;Object.assign(i,this.authInfo),this.$api.sendRequest({url:"/api/member/mobileauth",data:i,success:function(e){0==e.code?(t.$util.showToast({title:t.$lang("updateSuccess")}),t.NavReturn()):t.$util.showToast({title:e.message})}})}}}};t.default=r}).call(this,i("df3c")["default"])},"4d2e":function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{orderId:null,orderNo:"",isAnonymous:0,goodsList:[],goodsEvalList:[],imgList:[],isEvaluate:0,flag:!1,evaluateConfig:{evaluate_audit:1,evaluate_show:0,evaluate_status:1},isIphoneX:!1}},onLoad:function(e){e.order_id?this.orderId=e.order_id:this.$util.redirectTo("/pages/order/list"),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken||this.$util.redirectTo("/pages_tool/login/index",{back:"/pages_tool/order/evaluate?order_id="+this.orderId},"redirectTo"),this.getEvaluateConfig(),this.getOrderInfo()},onShow:function(){this.flag=!1},methods:{getOrderInfo:function(){var e=this,t={order_id:this.orderId};this.$api.sendRequest({url:"/api/order/evluateinfo",data:t,success:function(t){if(0==t.code)if(e.isEvaluate=t.data.evaluate_status,e.goodsList=t.data.list,e.goodsList.length&&(e.orderNo=t.data.list[0].order_no),e.isEvaluate)for(var i=0;i<t.data.list.length;i++){e.imgList.push([]),e.goodsEvalList.push({order_goods_id:t.data.list[i].order_goods_id,goods_id:t.data.list[i].goods_id,sku_id:t.data.list[i].sku_id,again_content:"",again_images:""})}else for(var a=0;a<t.data.list.length;a++){e.imgList.push([]),e.goodsEvalList.push({content:"",images:"",scores:5,explain_type:1,order_goods_id:t.data.list[a].order_goods_id,goods_id:t.data.list[a].goods_id,sku_id:t.data.list[a].sku_id,sku_name:t.data.list[a].sku_name,sku_price:t.data.list[a].price,sku_image:t.data.list[a].sku_image})}else e.$util.showToast({title:"未获取到订单数据"}),setTimeout((function(){e.$util.redirectTo("/pages/order/list",{},"redirectTo")}),1e3);e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(){this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},setStar:function(e){this.goodsEvalList[e.index].scores=e.value,e.value>=4?this.goodsEvalList[e.index].explain_type=1:1<e.value&&e.value<4?this.goodsEvalList[e.index].explain_type=2:this.goodsEvalList[e.index].explain_type=3},isAll:function(){this.isAnonymous?this.isAnonymous=0:this.isAnonymous=1},addImg:function(e){var t=this,i=this.imgList[e].length?this.imgList[e].length:0;this.$util.upload(6-i,{path:"evaluateimg"},(function(i){var a=t.imgList[e];a=a.concat(i),t.imgList[e]=[],t.$set(t.imgList,e,a),t.isEvaluate?t.goodsEvalList[e].again_images=t.imgList[e].toString():t.goodsEvalList[e].images=t.imgList[e].toString()}))},deleteImg:function(e,t,i){this.imgList[t].splice(i,1),this.isEvaluate?this.goodsEvalList[t].again_images=this.imgList[t].toString():this.goodsEvalList[t].images=this.imgList[t].toString()},preview:function(t,i){for(var a=this.imgList[i],s=0;s<a.length;s++)a[s]=this.$util.img(a[s]);e.previewImage({urls:a,current:t})},save:function(){var e=this;if(0!=this.evaluateConfig.evaluate_status){for(var t=0;t<this.goodsEvalList.length;t++)if(this.isEvaluate){if(!this.goodsEvalList[t].again_content.trim().length)return void this.$util.showToast({title:"商品的评价不能为空哦"})}else if(!this.goodsEvalList[t].content.trim().length)return void this.$util.showToast({title:"商品的评价不能为空哦"});var i=JSON.stringify(this.goodsEvalList),a={order_id:this.orderId,goods_evaluate:i};this.isEvaluate||(a.order_no=this.orderNo,a.member_name=this.memberInfo.nickname,a.member_headimg=this.memberInfo.headimg,a.is_anonymous=this.isAnonymous),this.flag||(this.flag=!0,this.$api.sendRequest({url:this.isEvaluate?"/api/goodsevaluate/again":"/api/goodsevaluate/add",data:a,success:function(t){0==t.code?(e.$util.showToast({title:"评价成功"}),setTimeout((function(){e.$util.redirectTo("/pages/order/list",{},"redirectTo")}),1e3)):(e.$util.showToast({title:t.message}),e.flag=!1)},fail:function(t){e.flag=!1}}))}else this.$util.showToast({title:"商家未开启商品评价功能"})},imageError:function(e){this.goodsList[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},getEvaluateConfig:function(){var e=this;this.$api.sendRequest({url:"/api/goodsevaluate/config",success:function(t){if(0==t.code){var i=t.data;e.evaluateConfig=i}}})}}};t.default=i}).call(this,i("df3c")["default"])},"5bee":function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.getClientRect=function(t,i){return new Promise((function(a,s){var o=i?e.createSelectorQuery().in(i):e.createSelectorQuery();return o.select(t).boundingClientRect(a).exec()}))}}).call(this,i("df3c")["default"])},"648c":function(e,t,i){"use strict";(function(e){var a=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=a(i("2f8f")),o={data:function(){return{timeoutObj:null,servicer_id:null,pingInterval:s.default.pingInterval}},onLoad:function(){e.closeSocket(),this.checkOpenSocket()},methods:{checkOpenSocket:function(){console.log("判断是否已连接");var t=this;e.sendSocketMessage({data:"ping",success:function(e){console.log("连接成功,检查")},fail:function(e){console.log("连接失败"),t.openConnection()}})},openConnection:function(){var t=this;console.log("打开连接"),e.connectSocket({url:s.default.webSocket,method:"POST",success:function(e){console.log("连接成功 connectSocket=",e)},fail:function(e){console.log("连接失败 connectSocket=",e)}}),e.onSocketError((function(e){console.error("WebSocket 连接失败:",e.errMsg),t.chatListInit(),t.getChatList()})),this.onSocketMessage()},onSocketMessage:function(){var t=this;console.log("开始监听");var i=this;this.pingInterval=s.default.pingInterval,this.timeoutObj&&clearInterval(this.timeoutObj),this.timeoutObj=null,e.onSocketMessage((function(a){var s=JSON.parse(a.data);if(console.log("监听该服务器消息",a),"close"==s.type)return clearInterval(i.timeoutObj),i.timeoutObj=null,void e.closeSocket();t.reset(),t.getSocketMsg(a.data)}))},getSocketMsg:function(e){var t=this,i=JSON.parse(e),a={isItMe:!1};if(a.contentType=i.type,"init"==i.type)t.$api.sendRequest({url:"/servicer/api/chat/bind",data:{client_id:i.data.client_id,site_id:t.siteId},success:function(e){0==e.code?t.servicer_id=e.data.servicer_id:t.servicer_id=0,t.chatListInit(),t.getChatList()}});else{if("connect"==i.type)return!1;"string"==i.type?a.content=i.data.servicer_say:"image"==i.type?a.image=i.data.servicer_say:"order"==i.type?a.order_id=i.data.order_id:"goodssku"==i.type&&(a.sku_id=i.data.goods_sku_id)}"init"!=i.type&&(t.messageList.push(a),t.$nextTick((function(){t.setPageScrollTo()})))},reset:function(){console.log("检测心跳"),clearInterval(this.timeoutObj),this.start()},start:function(){console.log("启动心跳");var t=this;this.timeoutObj=setInterval((function(){e.sendSocketMessage({data:"ping",success:function(e){console.log("连接中....")},fail:function(e){console.log("连接失败重新连接...."),t.openConnection()}})}),this.pingInterval)}},onUnload:function(){clearInterval(this.timeoutObj),this.timeoutObj=null,this.$api.sendRequest({url:"/servicer/api/chat/bye",data:{servicer_id:this.servicer_id,site_id:this.siteId},success:function(t){e.closeSocket()},fail:function(t){e.closeSocket()}})}};t.default=o}).call(this,i("df3c")["default"])},"7b20":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={lunarInfo:[19416,19168,42352,21717,53856,55632,91476,22176,39632,21970,19168,42422,42192,53840,119381,46400,54944,44450,38320,84343,18800,42160,46261,27216,27968,109396,11104,38256,21234,18800,25958,54432,59984,28309,23248,11104,100067,37600,116951,51536,54432,120998,46416,22176,107956,9680,37584,53938,43344,46423,27808,46416,86869,19872,42416,83315,21168,43432,59728,27296,44710,43856,19296,43748,42352,21088,62051,55632,23383,22176,38608,19925,19152,42192,54484,53840,54616,46400,46752,103846,38320,18864,43380,42160,45690,27216,27968,44870,43872,38256,19189,18800,25776,29859,59984,27480,23232,43872,38613,37600,51552,55636,54432,55888,30034,22176,43959,9680,37584,51893,43344,46240,47780,44368,21977,19360,42416,86390,21168,43312,31060,27296,44368,23378,19296,42726,42208,53856,60005,54576,23200,30371,38608,19195,19152,42192,118966,53840,54560,56645,46496,22224,21938,18864,42359,42160,43600,111189,27936,44448,84835,37744,18936,18800,25776,92326,59984,27424,108228,43744,41696,53987,51552,54615,54432,55888,23893,22176,42704,21972,21200,43448,43344,46240,46758,44368,21920,43940,42416,21168,45683,26928,29495,27296,44368,84821,19296,42352,21732,53600,59752,54560,55968,92838,22224,19168,43476,41680,53584,62034,54560],solarMonth:[31,28,31,30,31,30,31,31,30,31,30,31],Gan:["甲","乙","丙","丁","戊","己","庚","辛","壬","癸"],Zhi:["子","丑","寅","卯","辰","巳","午","未","申","酉","戌","亥"],Animals:["鼠","牛","虎","兔","龙","蛇","马","羊","猴","鸡","狗","猪"],solarTerm:["小寒","大寒","立春","雨水","惊蛰","春分","清明","谷雨","立夏","小满","芒种","夏至","小暑","大暑","立秋","处暑","白露","秋分","寒露","霜降","立冬","小雪","大雪","冬至"],sTermInfo:["9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","9778397bd19801ec9210c965cc920e","97b6b97bd19801ec95f8c965cc920f","97bd09801d98082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd197c36c9210c9274c91aa","97b6b97bd19801ec95f8c965cc920e","97bd09801d98082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec95f8c965cc920e","97bcf97c3598082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd07f595b0b6fc920fb0722","9778397bd097c36b0b6fc9210c8dc2","9778397bd19801ec9210c9274c920e","97b6b97bd19801ec95f8c965cc920f","97bd07f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c920e","97b6b97bd19801ec95f8c965cc920f","97bd07f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec9210c965cc920e","97bd07f1487f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c9274c920e","97bcf7f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c91aa","97b6b97bd197c36c9210c9274c920e","97bcf7f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c920e","97b6b7f0e47f531b0723b0b6fb0722","7f0e37f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36b0b70c9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e37f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc9210c8dc2","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0787b0721","7f0e27f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c91aa","97b6b7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c8dc2","977837f0e37f149b0723b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f5307f595b0b0bc920fb0722","7f0e397bd097c35b0b6fc9210c8dc2","977837f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0721","7f0e37f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc9210c8dc2","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0723b06bd","7f07e7f0e37f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f1487f595b0b0bb0b6fb0722","7f0e37f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e37f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0723b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0723b06bd","7f07e7f0e37f14998083b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14898082b0723b02d5","7f07e7f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e36665b66aa89801e9808297c35","665f67f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e36665b66a449801e9808297c35","665f67f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e36665b66a449801e9808297c35","665f67f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e26665b66a449801e9808297c35","665f67f0e37f1489801eb072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722"],nStr1:["日","一","二","三","四","五","六","七","八","九","十"],nStr2:["初","十","廿","卅"],nStr3:["正","二","三","四","五","六","七","八","九","十","冬","腊"],lYearDays:function(e){var t,i=348;for(t=32768;t>8;t>>=1)i+=this.lunarInfo[e-1900]&t?1:0;return i+this.leapDays(e)},leapMonth:function(e){return 15&this.lunarInfo[e-1900]},leapDays:function(e){return this.leapMonth(e)?65536&this.lunarInfo[e-1900]?30:29:0},monthDays:function(e,t){return t>12||t<1?-1:this.lunarInfo[e-1900]&65536>>t?30:29},solarDays:function(e,t){if(t>12||t<1)return-1;var i=t-1;return 1==i?e%4==0&&e%100!=0||e%400==0?29:28:this.solarMonth[i]},toGanZhiYear:function(e){var t=(e-3)%10,i=(e-3)%12;return 0==t&&(t=10),0==i&&(i=12),this.Gan[t-1]+this.Zhi[i-1]},toAstro:function(e,t){return"魔羯水瓶双鱼白羊金牛双子巨蟹狮子处女天秤天蝎射手魔羯".substr(2*e-(t<[20,19,21,21,21,22,23,23,23,23,22,22][e-1]?2:0),2)+"座"},toGanZhi:function(e){return this.Gan[e%10]+this.Zhi[e%12]},getTerm:function(e,t){if(e<1900||e>2100)return-1;if(t<1||t>24)return-1;var i=this.sTermInfo[e-1900],a=[parseInt("0x"+i.substr(0,5)).toString(),parseInt("0x"+i.substr(5,5)).toString(),parseInt("0x"+i.substr(10,5)).toString(),parseInt("0x"+i.substr(15,5)).toString(),parseInt("0x"+i.substr(20,5)).toString(),parseInt("0x"+i.substr(25,5)).toString()],s=[a[0].substr(0,1),a[0].substr(1,2),a[0].substr(3,1),a[0].substr(4,2),a[1].substr(0,1),a[1].substr(1,2),a[1].substr(3,1),a[1].substr(4,2),a[2].substr(0,1),a[2].substr(1,2),a[2].substr(3,1),a[2].substr(4,2),a[3].substr(0,1),a[3].substr(1,2),a[3].substr(3,1),a[3].substr(4,2),a[4].substr(0,1),a[4].substr(1,2),a[4].substr(3,1),a[4].substr(4,2),a[5].substr(0,1),a[5].substr(1,2),a[5].substr(3,1),a[5].substr(4,2)];return parseInt(s[t-1])},toChinaMonth:function(e){if(e>12||e<1)return-1;var t=this.nStr3[e-1];return t+="月",t},toChinaDay:function(e){var t;switch(e){case 10:t="初十";break;case 20:t="二十";break;case 30:t="三十";break;default:t=this.nStr2[Math.floor(e/10)],t+=this.nStr1[e%10]}return t},getAnimal:function(e){return this.Animals[(e-4)%12]},solar2lunar:function(e,t,i){if(e<1900||e>2100)return-1;if(1900==e&&1==t&&i<31)return-1;if(e)a=new Date(e,parseInt(t)-1,i);else var a=new Date;var s,o=0,n=(e=a.getFullYear(),t=a.getMonth()+1,i=a.getDate(),(Date.UTC(a.getFullYear(),a.getMonth(),a.getDate())-Date.UTC(1900,0,31))/864e5);for(s=1900;s<2101&&n>0;s++)o=this.lYearDays(s),n-=o;n<0&&(n+=o,s--);var r=new Date,c=!1;r.getFullYear()==e&&r.getMonth()+1==t&&r.getDate()==i&&(c=!0);var f=a.getDay(),b=this.nStr1[f];0==f&&(f=7);var u=s,l=this.leapMonth(s),d=!1;for(s=1;s<13&&n>0;s++)l>0&&s==l+1&&0==d?(--s,d=!0,o=this.leapDays(u)):o=this.monthDays(u,s),1==d&&s==l+1&&(d=!1),n-=o;0==n&&l>0&&s==l+1&&(d?d=!1:(d=!0,--s)),n<0&&(n+=o,--s);var m=s,h=n+1,g=t-1,p=this.toGanZhiYear(u),y=this.getTerm(e,2*t-1),v=this.getTerm(e,2*t),_=this.toGanZhi(12*(e-1900)+t+11);i>=y&&(_=this.toGanZhi(12*(e-1900)+t+12));var D=!1,k=null;y==i&&(D=!0,k=this.solarTerm[2*t-2]),v==i&&(D=!0,k=this.solarTerm[2*t-1]);var $=Date.UTC(e,g,1,0,0,0,0)/864e5+25567+10,w=this.toGanZhi($+i-1),I=this.toAstro(t,i);return{lYear:u,lMonth:m,lDay:h,Animal:this.getAnimal(u),IMonthCn:(d?"闰":"")+this.toChinaMonth(m),IDayCn:this.toChinaDay(h),cYear:e,cMonth:t,cDay:i,gzYear:p,gzMonth:_,gzDay:w,isToday:c,isLeap:d,nWeek:f,ncWeek:"星期"+b,isTerm:D,Term:k,astro:I}},lunar2solar:function(e,t,i,a){a=!!a;var s=this.leapMonth(e);this.leapDays(e);if(a&&s!=t)return-1;if(2100==e&&12==t&&i>1||1900==e&&1==t&&i<31)return-1;var o=this.monthDays(e,t),n=o;if(a&&(n=this.leapDays(e,t)),e<1900||e>2100||i>n)return-1;for(var r=0,c=1900;c<e;c++)r+=this.lYearDays(c);var f=0,b=!1;for(c=1;c<t;c++)f=this.leapMonth(e),b||f<=c&&f>0&&(r+=this.leapDays(e),b=!0),r+=this.monthDays(e,c);a&&(r+=o);var u=Date.UTC(1900,1,30,0,0,0),l=new Date(864e5*(r+i-31)+u),d=l.getUTCFullYear(),m=l.getUTCMonth()+1,h=l.getUTCDate();return this.solar2lunar(d,m,h)}},s=a;t.default=s},"88c8":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{diyRoute:"/pages_tool/index/diy"}},onLoad:function(e){},onShow:function(){},methods:{}}},a0ce:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={data:function(){return{showSignDays:[],rule:[{}],hasSign:0,signDaysSeries:0,MonthData:[],signList:[],back:"",redirect:"",successTip:{},startDate:null,endDate:null,isActive:"",signState:1,headimg:"",point:0,growth:0,signPoint:0,signGrowth:0,rewardRuleDay:[],cycle:0,reward:{}}},onLoad:function(e){var t=this;setTimeout((function(){t.addonIsExist.membersignin||(t.$util.showToast({title:"商家未开启会员签到",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),e.back&&(this.back=e.back),e.redirect&&(this.redirect=e.redirect),this.getSignState()},onShow:function(){var e=this;this.storeToken?(this.headimg=this.memberInfo.headimg,this.getMemberInfo(),this.getSignPointData(),this.getSignGrowthData(),this.setPublicShare(),this.getIsSign()):this.$nextTick((function(){e.$refs.login.open("/pages_tool/member/signin")}))},methods:{getMemberInfo:function(){var e=this;this.$api.sendRequest({url:"/api/member/info",success:function(t){t.code>=0&&(e.signDaysSeries=t.data.sign_days_series)}})},getSignPointData:function(){var e=this;this.$api.sendRequest({url:"/api/memberaccount/sum",data:{account_type:"point",from_type:"signin"},success:function(t){0==t.code&&(e.signPoint=t.data)}})},getSignGrowthData:function(){var e=this;this.$api.sendRequest({url:"/api/memberaccount/sum",data:{account_type:"growth",from_type:"signin"},success:function(t){0==t.code&&(e.signGrowth=t.data)}})},getSignState:function(){var e=this;this.$api.sendRequest({url:"/api/membersignin/getSignStatus",success:function(t){0==t.code&&(e.signState=t.data.is_use)}})},navigateBack:function(){""!=this.back?this.$util.redirectTo(this.back,{},this.redirect):this.$util.redirectTo("/pages/member/index")},getRule:function(){var e=this;this.rewardRuleDay=[],this.$api.sendRequest({url:"/api/membersignin/award",success:function(t){if(0==t.code){e.cycle=t.data.cycle||0,e.rule=t.data.reward||[];var i=0;e.rule.length>0&&e.rule.forEach((function(t,a){1==t.day?i=t.point:(e.rewardRuleDay.push(parseInt(t.day)),e.reward[t.day]=t.point)}));var a=[],s=1,o=7,n=t.data.cycle;e.signDaysSeries>5&&(s=e.signDaysSeries-5),n>=e.signDaysSeries+1&&(o=e.signDaysSeries+1),e.signDaysSeries<=5&&(o=8-s),o-s<7&&n>=s+6&&(o=s+6),n==e.signDaysSeries&&(s=e.signDaysSeries-6,o=e.signDaysSeries);for(var r=1;r<=t.data.cycle;r++)r>=s&&r<=o&&a.push({day:r,is_last:0,point:i});for(var c in a&&a.length&&(a[a.length-1]["is_last"]=1),a){var f=a[c];-1!=e.$util.inArray(f.day,e.rewardRuleDay)&&(a[c]["point"]=parseInt(e.reward[f.day])+parseInt(i))}e.showSignDays=a,e.$refs.loadingCover.hide()}}})},getIsSign:function(){var e=this;this.$api.sendRequest({url:"/api/membersignin/issign",success:function(t){0==t.code&&(e.hasSign=t.data,e.getRule(),e.getSignPointData(),e.getSignGrowthData())}})},sign:function(){var e=this;0==this.signState&&this.$util.showToast({title:"签到未开启"}),this.hasSign||1!=this.signState||this.$api.sendRequest({url:"/api/membersignin/signin",success:function(t){0==t.code?(e.successTip=t.data,e.$refs.uniPopup.open(),e.getRule(),e.getSignPointData(),e.getSignGrowthData(),e.hasSign=1,e.signDaysSeries=e.signDaysSeries+1):e.$util.showToast({title:t.message})}})},close:function(){this.$refs.uniPopup.close()},setPublicShare:function(){var e=this.$config.h5Domain+"/pages_tool/member/signin";this.$util.setPublicShare({title:"签到有礼",desc:"天天签到，积分好礼送不停",link:e,imgUrl:""},(function(e){}))}},computed:{pointTomorrow:function(){for(var e=this.signDaysSeries+1,t=this.rule[0].point?parseInt(this.rule[0].point):0,i=1;i<this.rule.length;i++){var a=this.rule[i];a.day==e&&a.point&&(t+=parseInt(a.point))}return t},showDay:function(){return 7*parseInt(this.signDaysSeries/7)+1}},onShareAppMessage:function(){return{title:"签到有礼，天天签到，积分好礼送不停",imageUrl:"",path:"/pages_tool/member/signin",success:function(e){},fail:function(e){},complete:function(e){}}}};t.default=a},fa50:function(e,t,i){"use strict";var a=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=a(i("3b2d")),o=a(i("67ad")),n=a(i("0bdb")),r=a(i("7b20")),c=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=(t.date,t.selected),a=t.startDate,s=t.endDate,n=t.range;(0,o.default)(this,e),this.date=this.getDate(new Date),this.selected=i||[],this.startDate=a,this.endDate=s,this.range=n,this.cleanMultipleStatus(),this.weeks={}}return(0,n.default)(e,[{key:"setDate",value:function(e){this.selectDate=this.getDate(e),this._getWeek(this.selectDate.fullDate)}},{key:"cleanMultipleStatus",value:function(){this.multipleStatus={before:"",after:"",data:[]}}},{key:"resetSatrtDate",value:function(e){this.startDate=e}},{key:"resetEndDate",value:function(e){this.endDate=e}},{key:"getDate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day";e||(e=new Date),"object"!==(0,s.default)(e)&&(e=e.replace(/-/g,"/"));var a=new Date(e);switch(i){case"day":a.setDate(a.getDate()+t);break;case"month":31===a.getDate()?a.setDate(a.getDate()+t):a.setMonth(a.getMonth()+t);break;case"year":a.setFullYear(a.getFullYear()+t);break}var o=a.getFullYear(),n=a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1,r=a.getDate()<10?"0"+a.getDate():a.getDate();return{fullDate:o+"-"+n+"-"+r,year:o,month:n,date:r,day:a.getDay()}}},{key:"_getLastMonthDays",value:function(e,t){for(var i=[],a=e;a>0;a--){var s=new Date(t.year,t.month-1,1-a).getDate();i.push({date:s,month:t.month-1,lunar:this.getlunar(t.year,t.month-1,s),disable:!0})}return i}},{key:"_currentMonthDys",value:function(e,t){for(var i=this,a=[],s=this.date.fullDate,o=function(e){var o=t.year+"-"+(t.month,t.month+"-")+(e<10?"0"+e:e),n=s===o,r=i.selected&&i.selected.find((function(e){if(i.dateEqual(o,e.date))return e})),c=!0,f=!0;if(i.startDate){var b=i.dateCompare(i.startDate,s);c=i.dateCompare(b?i.startDate:s,o)}if(i.endDate){var u=i.dateCompare(s,i.endDate);f=i.dateCompare(o,u?i.endDate:s)}var l=i.multipleStatus.data,d=!1,m=-1;i.range&&(l&&(m=l.findIndex((function(e){return i.dateEqual(e,o)}))),-1!==m&&(d=!0));var h={fullDate:o,year:t.year,date:e,multiple:!!i.range&&d,beforeMultiple:i.dateEqual(i.multipleStatus.before,o),afterMultiple:i.dateEqual(i.multipleStatus.after,o),month:t.month,lunar:i.getlunar(t.year,t.month,e),disable:!c||!f,isDay:n};r&&(h.extraInfo=r),a.push(h)},n=1;n<=e;n++)o(n);return a}},{key:"_getNextMonthDays",value:function(e,t){for(var i=[],a=1;a<e+1;a++)i.push({date:a,month:Number(t.month)+1,lunar:this.getlunar(t.year,Number(t.month)+1,a),disable:!0});return i}},{key:"getInfo",value:function(e){var t=this;e||(e=new Date);var i=this.canlender.find((function(i){return i.fullDate===t.getDate(e).fullDate}));return i}},{key:"dateCompare",value:function(e,t){return e=new Date(e.replace("-","/").replace("-","/")),t=new Date(t.replace("-","/").replace("-","/")),e<=t}},{key:"dateEqual",value:function(e,t){return e=new Date(e.replace("-","/").replace("-","/")),t=new Date(t.replace("-","/").replace("-","/")),e.getTime()-t.getTime()===0}},{key:"geDateAll",value:function(e,t){var i=[],a=e.split("-"),s=t.split("-"),o=new Date;o.setFullYear(a[0],a[1]-1,a[2]);var n=new Date;n.setFullYear(s[0],s[1]-1,s[2]);for(var r=o.getTime()-864e5,c=n.getTime()-864e5,f=r;f<=c;)f+=864e5,i.push(this.getDate(new Date(parseInt(f))).fullDate);return i}},{key:"getlunar",value:function(e,t,i){return r.default.solar2lunar(e,t,i)}},{key:"setSelectInfo",value:function(e,t){this.selected=t,this._getWeek(e)}},{key:"setMultiple",value:function(e){var t=this.multipleStatus,i=t.before,a=t.after;this.range&&(i&&a?(this.multipleStatus.before="",this.multipleStatus.after="",this.multipleStatus.data=[]):i?(this.multipleStatus.after=e,this.dateCompare(this.multipleStatus.before,this.multipleStatus.after)?this.multipleStatus.data=this.geDateAll(this.multipleStatus.before,this.multipleStatus.after):this.multipleStatus.data=this.geDateAll(this.multipleStatus.after,this.multipleStatus.before)):this.multipleStatus.before=e,this._getWeek(e))}},{key:"_getWeek",value:function(e){var t=this.getDate(e),i=(t.fullDate,t.year),a=t.month,s=(t.date,t.day,new Date(i,a-1,1).getDay()),o=new Date(i,a,0).getDate(),n={lastMonthDays:this._getLastMonthDays(s,this.getDate(e)),currentMonthDys:this._currentMonthDys(o,this.getDate(e)),nextMonthDays:[],weeks:[]},r=[],c=42-(n.lastMonthDays.length+n.currentMonthDys.length);n.nextMonthDays=this._getNextMonthDays(c,this.getDate(e)),r=r.concat(n.lastMonthDays,n.currentMonthDys,n.nextMonthDays);for(var f={},b=0;b<r.length;b++)b%7===0&&(f[parseInt(b/7)]=new Array(7)),f[parseInt(b/7)][b%7]=r[b];this.canlender=r,this.weeks=f}}]),e}(),f=c;t.default=f},fccc:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={methods:{cancleRefund:function(t,i){var a=this;e.showModal({content:"撤销之后本次申请将会关闭,如后续仍有问题可再次发起申请。",cancelText:"暂不撤销",cancelColor:"#898989",success:function(e){e.confirm&&a.$api.sendRequest({url:"/api/orderrefund/cancel",data:{order_goods_id:t},success:function(e){"function"==typeof i&&i(e)}})}})}}};t.default=i}).call(this,i("df3c")["default"])}}]);