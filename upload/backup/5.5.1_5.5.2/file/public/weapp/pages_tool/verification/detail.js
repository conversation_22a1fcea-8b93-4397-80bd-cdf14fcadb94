require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/verification/detail"],{"23ce":function(e,i,t){},"7b84":function(e,i,t){"use strict";var n=t("23ce"),r=t.n(n);r.a},"8da4":function(e,i,t){"use strict";(function(e,i){var n=t("47a9");t("d381");n(t("3240"));var r=n(t("a41c"));e.__webpack_require_UNI_MP_PLUGIN__=t,i(r.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},a41c:function(e,i,t){"use strict";t.r(i);var n=t("b1cb"),r=t("cc72");for(var o in r)["default"].indexOf(o)<0&&function(e){t.d(i,e,(function(){return r[e]}))}(o);t("7b84");var a=t("828b"),c=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);i["default"]=c.exports},b1cb:function(e,i,t){"use strict";t.d(i,"b",(function(){return r})),t.d(i,"c",(function(){return o})),t.d(i,"a",(function(){return n}));var n={loadingCover:function(){return t.e("components/loading-cover/loading-cover").then(t.bind(null,"c003"))}},r=function(){var e=this,i=e.$createElement,t=(e._self._c,e.$lang("common.currencySymbol")),n=e.__map(e.verifyInfo.item_array,(function(i,t){var n=e.__get_orig(i),r=e.$util.img(i.img),o=e._f("abs")(i.price),a=e._f("abs")(i.all);return{$orig:n,g0:r,f0:o,f1:a}})),r=e.verifyInfo.is_verify&&e.verifyInfo.verify_time?e.$util.timeStampTurnTime(e.verifyInfo.verify_time):null;e.$mp.data=Object.assign({},{$root:{m0:t,l0:n,g1:r}})},o=[]},cc72:function(e,i,t){"use strict";t.r(i);var n=t("d919"),r=t.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){t.d(i,e,(function(){return n[e]}))}(o);i["default"]=r.a},d919:function(e,i,t){"use strict";(function(e){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var t={data:function(){return{code:"",verifyInfo:{verify_content:{item_array:[],remark_array:[]}},info:[],isSub:!1}},onLoad:function(e){var i=this;if(e.code&&(this.code=e.code),e.scene){var t=decodeURIComponent(e.scene);t=t.split("&"),t.length&&t.forEach((function(e){-1!=e.indexOf("code")&&(i.code=e.split("-")[1])}))}},onShow:function(){this.storeToken?this.checkIsVerifier():this.$util.redirectTo("/pages/member/index"),this.getVerifyInfo()},methods:{checkIsVerifier:function(){var i=this;this.$api.sendRequest({url:"/api/verify/checkisverifier",success:function(t){t.data||(i.$util.showToast({title:"非核销员无此权限"}),setTimeout((function(){e.navigateBack({delta:1})}),1e3))}})},getVerifyInfo:function(){var i=this;this.$api.sendRequest({url:"/api/verify/verifyInfo",data:{verify_code:this.code},success:function(t){t.code>=0?(i.verifyInfo=t.data,i.info=i.verifyInfo.remark_array.splice(0,1),i.verifyInfo.item_array.forEach((function(e){e.all=e.num*e.price})),i.$refs.loadingCover&&i.$refs.loadingCover.hide()):(i.$util.showToast({title:t.message}),setTimeout((function(){e.navigateBack({delta:1})}),1e3))},fail:function(e){i.$refs.loadingCover&&i.$refs.loadingCover.hide()}})},verify:function(){var i=this;this.isSub||(this.isSub=!0,this.$api.sendRequest({url:"/api/verify/verify",data:{verify_code:this.code},success:function(t){if(i.$util.showToast({title:t.message}),t.code>=0){for(var n=!0,r=getCurrentPages().reverse(),o=function(i){if(-1!="pages_tool/verification/index".indexOf(r[i].route))return n=!1,setTimeout((function(){e.navigateBack({delta:i})}),1e3),"break"},a=0;a<r.length;a++){var c=o(a);if("break"===c)break}n&&i.$util.redirectTo("/pages_tool/verification/index",{},"redirectTo")}else i.isSub=!1}}))},imageError:function(e){this.verifyInfo.item_array[e].img=this.$util.getDefaultImage().goods,this.$forceUpdate()},copy:function(e){this.$util.copy(e)}},filters:{abs:function(e){return Math.abs(parseFloat(e)).toFixed(2)}}};i.default=t}).call(this,t("df3c")["default"])}},[["8da4","common/runtime","common/vendor"]]]);