require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/verification/index"],{1031:function(e,t,i){"use strict";i.r(t);var n=i("310d"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"310d":function(e,t,i){"use strict";(function(e){var n=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;i("edd0"),n(i("2f8f"));var o={data:function(){return{operationType:"sweepCode",verify_code:"",isFocus:!1,detail_path:"/pages_tool/verification/detail"}},onLoad:function(){},onShow:function(){var e=this;this.storeToken?this.checkIsVerifier():this.$nextTick((function(){e.$refs.login.open("/pages_tool/verification/index")}))},methods:{focus:function(){this.isFocus=!this.isFocus},scanCode:function(){var t=this;e.scanCode({onlyFromCamera:!0,success:function(e){if("scanCode:ok"==e.errMsg){var i=e.result,n="";switch(e.scanType){case"CODE_128":n=i;break;case"QR_CODE":if(i.indexOf(t.detail_path)>-1){var o=i.match(/\?code=(.+)/);2==o.length&&(n=o[1])}break}if(!n)return void t.$util.showToast({title:"请扫码正确的条码或二维码"});t.$util.redirectTo(t.detail_path+"?code="+n)}else t.$util.showToast({title:e.errorMsg})}})},changeOperationType:function(e){this.operationType=e},checkIsVerifier:function(){var e=this;this.$api.sendRequest({url:"/api/verify/checkisverifier",success:function(t){t.data||(e.$util.showToast({title:"非核销员无此权限"}),setTimeout((function(){e.$util.redirectTo("/pages/member/index")}),1e3)),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},confirm:function(){var e=this;if(!/[\S]+/.test(this.verify_code))return this.$util.showToast({title:"请输入核销码"}),!1;this.$api.sendRequest({url:"/api/verify/verifyInfo",data:{verify_code:this.verify_code},success:function(t){t.code>=0?e.$util.redirectTo("/pages_tool/verification/detail",{code:e.verify_code}):e.$util.showToast({title:t.message})}})}},watch:{storeToken:function(e,t){e&&this.checkIsVerifier()}}};t.default=o}).call(this,i("df3c")["default"])},"3a14":function(e,t,i){"use strict";(function(e,t){var n=i("47a9");i("d381");n(i("3240"));var o=n(i("dc0b"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"60f3":function(e,t,i){"use strict";var n=i("83a8"),o=i.n(n);o.a},"7e17":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={nsLogin:function(){return Promise.all([i.e("common/vendor"),i.e("components/ns-login/ns-login")]).then(i.bind(null,"2910"))},loadingCover:function(){return i.e("components/loading-cover/loading-cover").then(i.bind(null,"c003"))}},o=function(){var e=this,t=e.$createElement;e._self._c;e._isMounted||(e.e0=function(t){return e.$util.redirectTo("/pages_tool/verification/list")})},r=[]},"83a8":function(e,t,i){},dc0b:function(e,t,i){"use strict";i.r(t);var n=i("7e17"),o=i("1031");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("60f3");var c=i("828b"),a=Object(c["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=a.exports}},[["3a14","common/runtime","common/vendor"]]]);