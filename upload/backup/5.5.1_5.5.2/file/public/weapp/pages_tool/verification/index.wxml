<page-meta page-style="{{themeColor}}"></page-meta><view class="container"><view class="action-wrap"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="record-wrap color-base-text" bindtap="__e"><text class="iconfont icon-jilu color-base-text"></text><text>核销记录</text></view><view data-event-opts="{{[['tap',[['scanCode',['$event']]]]]}}" hidden="{{!(operationType=='sweepCode')}}" class="sweep-code ns-gradient-otherpages-member-balance-balance-rechange" bindtap="__e"><text class="iconfont icon-saoma"></text></view><view hidden="{{!(operationType=='manualInput')}}" class="manual-input"><view class="process-wrap"><view class="wrap"><view class="_icon"><text class="iconfont icon-shurutianxiebi color-base-text"></text></view><view class="_text">输入核销码</view></view><view><view><text class="iconfont icon-jiang-copy color-tip"></text></view></view><view class="wrap"><view class="_icon"><text class="iconfont icon-hexiao color-base-text"></text></view><view class="_text">核销</view></view></view><input class="_input vue-ref" type="text" placeholder="请输入核销码" placeholder-class="_placeholder" focus="{{isFocus}}" data-ref="input" data-event-opts="{{[['input',[['__set_model',['','verify_code','$event',[]]]]]]}}" value="{{verify_code}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="_btn" bindtap="__e"><button type="primary">确认</button></view></view></view><view class="arc-edge"></view><view class="action-type-wrap"><view data-event-opts="{{[['tap',[['changeOperationType',['sweepCode']]]]]}}" class="action" bindtap="__e"><view class="_icon"><text class="iconfont icon-saoma"></text></view><view class="_text">扫码核销</view></view><view class="iconfont icon-tiaoxingmasaomiao ns-gradient-otherpages-member-balance-balance-rechange"></view><view data-event-opts="{{[['tap',[['changeOperationType',['manualInput']]]]]}}" class="action" bindtap="__e"><view class="_icon"><text class="iconfont icon-shuru"></text></view><view data-event-opts="{{[['tap',[['focus',['$event']]]]]}}" class="_text" bindtap="__e">手动输入</view></view></view><ns-login class="vue-ref" vue-id="729365ea-1" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="729365ea-2" data-ref="loadingCover" bind:__l="__l"></loading-cover><privacy-popup class="vue-ref" vue-id="729365ea-3" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>