<page-meta page-style="{{themeColor}}"></page-meta><view class="container"><view class="site-wrap"><view class="site-header"><view class="shu color-base-bg"></view><view class="order-detail font-size-base">订单明细</view></view><view class="xian"></view><view class="site-body"><block wx:for="{{$root.l0}}" wx:for-item="goodsItem" wx:for-index="goodsIndex" wx:key="goodsIndex"><block><view class="goods-wrap"><view class="goods-img"><image src="{{goodsItem.g0}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[goodsIndex]]]]]}}" binderror="__e"></image></view><view class="info-wrap"><view class="goods-info"><text class="goods-name font-size-base">{{goodsItem.$orig.name}}</text></view><view class="money-wrap"><view class="align-right">{{$root.m0+goodsItem.f0}}</view><view class="align-right color-tip"><text class="iconfont icon-close"></text>{{''+goodsItem.$orig.num+''}}</view></view></view></view><view class="all"><view class="all-num">{{"共"+goodsItem.$orig.num+"件商品"}}</view><view class="all-money color-base-text"><text>合计:</text>{{'￥'+goodsItem.f1+''}}</view></view></block></block><view class="xian"></view><block wx:for="{{verifyInfo.remark_array}}" wx:for-item="remarkItem" wx:for-index="remarkIndex" wx:key="remarkIndex"><block wx:if="{{remarkItem.value}}"><view class="order-cell"><text class="tit">{{remarkItem.title+"："}}</text><view class="box"><text class="color-tip">{{remarkItem.value}}</text><block wx:if="{{remarkItem.title=='订单编号'}}"><view data-event-opts="{{[['tap',[['copy',['$0'],[[['verifyInfo.remark_array','',remarkIndex,'value']]]]]]]}}" class="copy" bindtap="__e">复制</view></block></view></view></block></block></view></view><view class="order-summary"><view class="site-header"><view class="shu color-base-bg"></view><view class="order-detail">核销明细</view></view><view class="xian"></view><view class="order-cell"><text class="tit">核销类型：</text><view class="box"><text class="color-tip">{{verifyInfo.verify_type_name}}</text></view></view><block wx:if="{{verifyInfo.is_verify}}"><block><view class="order-cell"><text class="tit">核销状态：</text><view class="box"><text class="color-tip">已核销</text></view></view><block wx:if="{{verifyInfo.verify_time}}"><view class="order-cell"><text class="tit">核销人员：</text><view class="box"><text class="color-tip">{{verifyInfo.verifier_name}}</text></view></view></block><block wx:if="{{verifyInfo.verify_time}}"><view class="order-cell"><text class="tit">核销时间：</text><view class="box"><text class="color-tip">{{$root.g1}}</text></view></view></block></block></block></view><block wx:if="{{verifyInfo.is_verify==0}}"><view data-event-opts="{{[['tap',[['verify',['$event']]]]]}}" class="verify-btn" bindtap="__e"><button type="primary">确认使用</button></view></block><loading-cover class="vue-ref" vue-id="51d91b6f-1" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>