<page-meta page-style="{{themeColor}}"></page-meta><view class="verify-container"><view class="type-wrap"><block wx:for="{{typeList}}" wx:for-item="typeItem" wx:for-index="typeIndex" wx:key="typeIndex"><view class="uni-tab-item" id="{{typeItem.pickup}}" data-current="{{typeIndex}}" data-event-opts="{{[['tap',[['ontabtap',['$event']]]]]}}" bindtap="__e"><text class="{{['uni-tab-item-title',type==typeIndex?'uni-tab-item-title-active color-base-text color-base-border':'']}}">{{typeItem.name}}</text></view></block></view><swiper class="swiper-box" style="flex:1;" current="{{type}}" duration="{{200}}" data-event-opts="{{[['change',[['ontabchange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l2}}" wx:for-item="typeItem" wx:for-index="typeIndex" wx:key="typeIndex"><swiper-item class="swiper-item"><scroll-view class="verify-list" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><block wx:if="{{typeItem.g0}}"><block><block wx:for="{{typeItem.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['verifyList.'+typeIndex+'.list','',index,'verify_code']]]]]]]}}" bindtap="__e"><view class="header"><view class="color-tip font-size-goods-tag">{{"核销码："+item.$orig.verify_code}}</view><view class="color-tip align-right font-size-goods-tag">{{"核销员："+item.$orig.verifier_name}}</view></view><view class="xian"></view><view class="body"><block wx:for="{{item.l0}}" wx:for-item="citem" wx:for-index="citemIndex" wx:key="citemIndex"><view class="content-item"><view class="img-wrap"><image src="{{citem.g1}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[typeIndex,index,citemIndex]]]]]}}" binderror="__e"></image></view><view class="info-wrap"><view class="name-wrap"><view class="goods-name font-size-tag">{{citem.$orig.name}}</view><view class="font-size-goods-tag color-tip">{{"核销时间："+item.g2}}</view></view><view class="money-wrap"><view class="align-right color-tip font-size-goods-tag"><text class="iconfont icon-close font-size-goods-tag"></text><text>{{citem.$orig.num}}</text></view></view></view><view class="money-wrap"><view><text class="color-base-text font-size-goods-tag">{{item.m0}}</text><text class="font-size-base color-base-text">{{citem.f0}}</text></view></view></view></block></view></view></view></block></block></block><block wx:else><block><ns-empty vue-id="{{'4b56efc2-1-'+typeIndex}}" isIndex="{{false}}" text="暂无核销记录!" bind:__l="__l"></ns-empty></block></block></scroll-view></swiper-item></block></swiper><loading-cover class="vue-ref" vue-id="4b56efc2-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>