<page-meta page-style="{{themeColor}}"></page-meta><view class="container" id="container"><view data-event-opts="{{[['tap',[['closePopWindow',['$event']]]]]}}" class="{{['room',keyWordsConfig.is_open==1?'active':'']}}" bindtap="__e"><view class="{{['tips',(isLoading)?'show':'']}}">正在获取消息</view><view class="room-content-box"><block wx:for="{{messageList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view id="{{'chat'+index}}"><chat-message vue-id="{{'745158a2-1-'+index}}" message="{{item}}" send="{{send}}" data-event-opts="{{[['^sendGood',[['sendGood',['$event',index]]]],['^sendOrder',[['sendOrder',['$event',index]]]]]}}" bind:sendGood="__e" bind:sendOrder="__e" bind:__l="__l"></chat-message></view></block></view><block wx:if="{{chatBottom}}"><view class="paddingbottom" id="paddingbottom"></view></block></view><view class="{{['input-content',keyWordsConfig.is_open==1?'active':'']}}"><block wx:if="{{keyWordsConfig.is_open==1}}"><view class="keyWords"><block wx:for="{{keyWordsConfig.keyword_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item})}}" bindtap="__e">{{''+item.keyword+''}}</text></block></view></block><view class="input-box"><view data-event-opts="{{[['tap',[['openEmjoy']]]]}}" class="iconfont icon-biaoqing padding-right" bindtap="__e"></view><editor class="message-edit" id="editor" value="{{formData.content}}" data-event-opts="{{[['ready',[['onEditorReady',['$event']]]],['focus',[['inputFocus',['$event']]]],['input',[['__set_model',['$0','content','$event',[]],['formData']],['onEditorinput']]],['blur',[['closePopWindow']]]]}}" bindready="__e" bindfocus="__e" bindinput="__e" bindblur="__e"></editor><view data-event-opts="{{[['tap',[['openChatMore']]]]}}" class="iconfont icon-jiahao01 padding-right" bindtap="__e"></view><view data-event-opts="{{[['tap',[['sendMsg',['message']]]]]}}" class="send_btn color-base-bg" bindtap="__e">发送</view></view><block wx:if="{{inputShow&&inputFirst}}"><view class="inputShow"></view></block><block wx:if="{{emjoyShow}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><view class="emjoy-box"><scroll-view class="emjoy-content" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['addEmjoy',['$0',index],[[['emjoyList','',index]]]]]]]}}" class="emjoy-item" bindtap="__e"><image src="{{item.g0}}"></image></view></block></scroll-view></view></view></block><block wx:if="{{chatMore}}"><view class="more_send"><view data-event-opts="{{[['tap',[['addImg']]]]}}" class="more_send-item" bindtap="__e"><text class="iconfont icon-tupian"></text><view>图片</view></view></view></block></view><loading-cover class="vue-ref" vue-id="745158a2-2" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>