<page-meta page-style="{{themeColor}}" class="data-v-0369c01a"></page-meta><view class="guafen-box data-v-0369c01a"><block wx:if="{{groupInfoList.status!=2}}"><view class="data-v-0369c01a"><view class="guafen_adv data-v-0369c01a" style="{{'background-image:'+('url('+$root.g0+')')+';'}}"><view class="explain data-v-0369c01a"><view data-event-opts="{{[['tap',[['toIndex']]]]}}" class="explain-img data-v-0369c01a" bindtap="__e"><image class="index-img data-v-0369c01a" src="{{$root.g1}}" mode></image></view><view data-event-opts="{{[['tap',[['openRulePopup',['$event']]]]]}}" class="desc ns-rule-bg data-v-0369c01a" bindtap="__e">活动说明</view></view><block wx:if="{{groupInfoList==''&&hongbaoInfo.status==1}}"><view class="guafen_content data-v-0369c01a"><view class="guafen_content_title data-v-0369c01a">点击 [立即领取] 邀请好友瓜分红包</view><view class="guafen_content_num data-v-0369c01a">{{''+$root.f0+''}}<text class="data-v-0369c01a">元</text></view><view data-event-opts="{{[['tap',[['getLaunch']]]]}}" class="btn font-size-base data-v-0369c01a" bindtap="__e">立即领取</view></view></block><block wx:if="{{groupInfoList!=''&&hongbaoInfo.status==1&&groupInfoList.status==0||groupInfoList.status==2}}"><view class="guafen_content data-v-0369c01a"><view class="guafen_photo data-v-0369c01a"><block wx:if="{{$root.g2>0}}"><scroll-view scroll-y="{{true}}" show-scrollbar="{{false}}" class="data-v-0369c01a"><view class="hongbao-lists data-v-0369c01a"><block wx:for="{{$root.l0}}" wx:for-item="headerItem" wx:for-index="headerIndex" wx:key="member_id"><view class="guafen_header_img data-v-0369c01a"><block wx:if="{{headerItem.$orig.headimg!=''}}"><image src="{{headerItem.g3}}" mode data-event-opts="{{[['error',[['headerimageError',[headerIndex]]]]]}}" binderror="__e" class="data-v-0369c01a"></image></block><block wx:else><image src="{{headerItem.g4}}" mode class="data-v-0369c01a"></image></block></view></block><block wx:for="{{groupInfoList.num-$root.g5}}" wx:for-item="itm" wx:for-index="inx"><view data-event-opts="{{[['tap',[['openSharePopup',['$event']]]]]}}" class="guafen_header_img data-v-0369c01a" bindtap="__e"><text class="data-v-0369c01a">+</text></view></block></view></scroll-view></block></view><view class="guafen_content_title data-v-0369c01a">仅差<text class="guafen_content_color data-v-0369c01a">{{groupInfoList.num-$root.g6}}</text>人，即可瓜分<text class="guafen_content_color data-v-0369c01a">{{$root.f1}}</text>元现金红包</view><view class="guafen_content_time data-v-0369c01a"><image src="{{$root.g7}}" class="data-v-0369c01a"></image><text class="seckill-title-name data-v-0369c01a">距结束</text><uni-count-down class="guafen_content_color data-v-0369c01a" vue-id="509e6661-1" hour="{{seckillH}}" minute="{{seckillI}}" second="{{seckillS}}" borderColor="#fff" color="#FF4544" bind:__l="__l"></uni-count-down><image src="{{$root.g8}}" class="data-v-0369c01a"></image></view><view data-event-opts="{{[['tap',[['openSharePopup',['$event']]]]]}}" class="btn data-v-0369c01a" bindtap="__e">邀请好友</view></view></block><block wx:if="{{groupInfoList!=''&&groupInfoList.status==1&&g_member_state==0}}"><view class="guafen_content data-v-0369c01a"><view class="guafen-success data-v-0369c01a">恭喜你，成功瓜分现金红包</view><view class="{{['data-v-0369c01a',$root.g9>4?'guafen_photo':'guafen_photo-two']}}"><block wx:if="{{$root.g10>0}}"><scroll-view scroll-y="{{true}}" show-scrollbar="{{false}}" class="data-v-0369c01a"><view class="{{['data-v-0369c01a',$root.g11>4?'hongbao-lists':'hongbao-lists-two']}}"><block wx:for="{{$root.l1}}" wx:for-item="headerItem" wx:for-index="headerIndex" wx:key="member_id"><view class="guafen_header_img data-v-0369c01a"><block wx:if="{{headerItem.$orig.headimg!=''}}"><image src="{{headerItem.g12}}" mode data-event-opts="{{[['error',[['headerimageError',[headerIndex]]]]]}}" binderror="__e" class="data-v-0369c01a"></image></block><block wx:else><image src="{{headerItem.g13}}" mode class="data-v-0369c01a"></image></block></view></block></view></scroll-view></block></view><view class="guafen-coupon data-v-0369c01a">{{''+money+''}}<text class="data-v-0369c01a">元</text></view><view class="guafen-man color-sub font-size-tag data-v-0369c01a">红包已存入您的账户余额中</view><view data-event-opts="{{[['tap',[['toLook']]]]}}" class="success-btn data-v-0369c01a" bindtap="__e">立即查看</view><view data-event-opts="{{[['tap',[['toGuaFen',['$event']]]]]}}" class="success-guafen-btn data-v-0369c01a" bindtap="__e">瓜分新红包</view></view></block><block wx:if="{{groupInfoList.status!=1&&hongbaoInfo.status==-1||groupInfoList.status!=1&&hongbaoInfo.status==2}}"><block class="data-v-0369c01a"><view class="guafen_over data-v-0369c01a">哎呀，来晚了，活动已结束</view></block></block><block wx:if="{{hongbaoInfo.status==1&&g_member_state==1}}"><block class="data-v-0369c01a"><view class="guafen_over data-v-0369c01a">来晚了，红包已被瓜分完</view></block></block></view><view class="guafen-how data-v-0369c01a"><view class="how-title font-size-toolbar data-v-0369c01a">如何瓜分红包</view><view class="step data-v-0369c01a"><view class="step-img data-v-0369c01a"><image class="step-one data-v-0369c01a" src="{{$root.g14}}"></image><image class="step-jiantou data-v-0369c01a" src="{{$root.g15}}"></image><image class="step-one data-v-0369c01a" src="{{$root.g16}}"></image><image class="step-jiantou data-v-0369c01a" src="{{$root.g17}}"></image><image class="step-three data-v-0369c01a" src="{{$root.g18}}"></image></view><view class="step-txt data-v-0369c01a"><view class="color-sub font-size-tag data-v-0369c01a">参与红包活动</view><view class="color-sub font-size-tag data-v-0369c01a">邀请好友组队</view><view class="color-sub font-size-tag data-v-0369c01a">集齐好友瓜分</view></view></view></view><view class="rule-description data-v-0369c01a"><view class="rule-description-title font-size-toolbar data-v-0369c01a">活动说明</view><view class="color-sub remark data-v-0369c01a">{{hongbaoInfo.remark}}</view></view></view></block><block wx:if="{{groupInfoList.status==2}}"><view class="detail-box data-v-0369c01a"><view class="status-wrap color-base-bg data-v-0369c01a" style="{{'background-image:'+('url('+$root.g19+')')+';'}}"><view class="order-status-left data-v-0369c01a"><view class="status-name data-v-0369c01a"><text class="iconfont icon-wode-tuangou fail data-v-0369c01a"></text><text class="name font-size-toolbar data-v-0369c01a">组队失败</text></view></view></view><view class="guafen_content data-v-0369c01a"><view class="{{['data-v-0369c01a',$root.g20>4?'guafen_photo':'guafen_photo-two']}}"><block wx:if="{{$root.g21>0}}"><scroll-view scroll-y="{{true}}" show-scrollbar="{{false}}" class="data-v-0369c01a"><view class="{{['data-v-0369c01a',$root.g22>4?'hongbao-lists':'hongbao-lists-two']}}"><block wx:for="{{$root.l2}}" wx:for-item="headerItem" wx:for-index="headerIndex" wx:key="member_id"><view class="guafen_header_img data-v-0369c01a"><block wx:if="{{headerItem.$orig.headimg!=''}}"><image src="{{headerItem.g23}}" mode data-event-opts="{{[['error',[['headerimageError',[headerIndex]]]]]}}" binderror="__e" class="data-v-0369c01a"></image></block><block wx:else><image src="{{headerItem.g24}}" mode class="data-v-0369c01a"></image></block></view></block></view></scroll-view></block></view><view class="guafen_content_title data-v-0369c01a">{{'集齐'+groupInfoList.num+'人可瓜分红包，还差'}}<text class="color-base-text data-v-0369c01a">{{groupInfoList.num-$root.g25}}</text>人</view></view><view class="footer-guafen data-v-0369c01a"><view class="footer-list data-v-0369c01a"><text class="data-v-0369c01a">活动开始时间</text><text class="data-v-0369c01a">{{$root.g26}}</text></view><view class="footer-list data-v-0369c01a"><text class="data-v-0369c01a">活动结束时间</text><text class="data-v-0369c01a">{{$root.g27}}</text></view><view class="footer-list data-v-0369c01a"><text class="data-v-0369c01a">组队时间</text><text class="data-v-0369c01a">{{$root.g28}}</text></view></view></view></block><uni-popup vue-id="509e6661-2" type="center" maskClick="{{false}}" data-ref="rulePopup" class="data-v-0369c01a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="rule-wrap data-v-0369c01a"><view class="content-wrap data-v-0369c01a"><image class="rule-head data-v-0369c01a" src="{{$root.g29}}" mode></image><scroll-view class="rule data-v-0369c01a" scroll-y="true"><view class="data-v-0369c01a"><view class="tit data-v-0369c01a">活动时间:</view><view class="text data-v-0369c01a">{{$root.g30+" -\n\t\t\t\t\t\t\t"+$root.g31+''}}</view><view class="tit data-v-0369c01a">活动说明:</view><view class="text data-v-0369c01a">{{hongbaoInfo.remark}}</view></view></scroll-view><text data-event-opts="{{[['tap',[['closeRulePopup',['$event']]]]]}}" class="iconfont icon-round-close data-v-0369c01a" bindtap="__e"></text></view></view></uni-popup><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e" class="data-v-0369c01a"><uni-popup class="share-popup data-v-0369c01a vue-ref" vue-id="509e6661-3" type="bottom" data-ref="sharePopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="data-v-0369c01a"><view class="share-title data-v-0369c01a">分享</view><view class="share-content data-v-0369c01a"><view class="share-box data-v-0369c01a"><button class="share-btn data-v-0369c01a" plain="{{true}}" open-type="share"><view class="iconfont icon-share-friend data-v-0369c01a"></view><text class="data-v-0369c01a">分享给好友</text></button></view><block wx:if="{{goodsCircle}}"><view class="share-box data-v-0369c01a"><button class="share-btn data-v-0369c01a" plain="{{true}}" data-event-opts="{{[['tap',[['openBusinessView',['$event']]]]]}}" bindtap="__e"><view class="iconfont icon-haowuquan data-v-0369c01a"></view><text class="data-v-0369c01a">分享到好物圈</text></button></view></block><view data-event-opts="{{[['tap',[['openPosterPopup',['$event']]]]]}}" class="share-box data-v-0369c01a" bindtap="__e"><button class="share-btn data-v-0369c01a" plain="{{true}}"><view class="iconfont icon-pengyouquan data-v-0369c01a"></view><text class="data-v-0369c01a">生成分享海报</text></button></view></view><view data-event-opts="{{[['tap',[['closeSharePopup',['$event']]]]]}}" class="share-footer data-v-0369c01a" bindtap="__e"><text class="data-v-0369c01a">取消分享</text></view></view></uni-popup></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="reward-popup data-v-0369c01a" catchtouchmove="__e"><uni-popup vue-id="509e6661-4" type="center" maskClick="{{false}}" data-ref="linkPopup" class="data-v-0369c01a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{inviterInfo}}"><view class="wrap-popup data-v-0369c01a"><view class="reward-wrap data-v-0369c01a" style="{{'background-image:'+('url('+$root.g32+')')+';'}}"><image class="img-head data-v-0369c01a" src="{{$root.g33}}" data-event-opts="{{[['error',[['e0',['$event']]]]]}}" binderror="__e"></image><view class="nickname data-v-0369c01a">{{$root.f2}}</view><view class="txt data-v-0369c01a">邀请你加入团队</view><view class="guafen-hongbao data-v-0369c01a">瓜分现金红包</view><view data-event-opts="{{[['tap',[['join']]]]}}" class="qu-btn data-v-0369c01a" style="{{'background-image:'+('url('+$root.g34+')')+';'}}" bindtap="__e">加入队伍</view></view><view data-event-opts="{{[['tap',[['closeLinkPopup']]]]}}" class="close-btn data-v-0369c01a" bindtap="__e"><image src="{{$root.g35}}" mode class="data-v-0369c01a"></image></view></view></block><block wx:else><view class="wrap-popup data-v-0369c01a"><view class="reward-wrap data-v-0369c01a" style="{{'background-image:'+('url('+$root.g36+')')+';'}}"><image class="img-head data-v-0369c01a" src="{{$root.g37}}" data-event-opts="{{[['error',[['e1',['$event']]]]]}}" binderror="__e"></image><view class="nickname data-v-0369c01a">{{$root.f3}}</view><view class="txt data-v-0369c01a">邀请你加入团队</view><view class="guafen-hongbao data-v-0369c01a">瓜分现金红包</view><view data-event-opts="{{[['tap',[['join']]]]}}" class="qu-btn data-v-0369c01a" style="{{'background-image:'+('url('+$root.g38+')')+';'}}" bindtap="__e">加入队伍</view></view><view data-event-opts="{{[['tap',[['closeLinkPopup']]]]}}" class="close-btn data-v-0369c01a" bindtap="__e"><image src="{{$root.g39}}" mode class="data-v-0369c01a"></image></view></view></block></uni-popup></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="reward-popup-three data-v-0369c01a" catchtouchmove="__e"><uni-popup vue-id="509e6661-5" type="center" maskClick="{{false}}" data-ref="friendsPopup" class="data-v-0369c01a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="wrap-popup-three data-v-0369c01a"><view class="reward-wrap-three data-v-0369c01a" style="{{'background-image:'+('url('+$root.g40+')')+';'}}"><view class="join-three data-v-0369c01a">你已加入<text class="nickname-three data-v-0369c01a">{{$root.f4}}</text>的瓜分团</view><block wx:if="{{groupInfoList.member_list}}"><view class="guafen-three data-v-0369c01a">仅差<text class="data-v-0369c01a">{{groupInfoList.num-$root.g41}}</text>人即可瓜分现金红包</view></block><view class="txt-three data-v-0369c01a">{{$root.f5+"元"}}</view><view data-event-opts="{{[['tap',[['goFriend']]]]}}" class="qu-btn-three data-v-0369c01a" style="{{'background-image:'+('url('+$root.g42+')')+';'}}" bindtap="__e">喊好友一起瓜分</view></view><view data-event-opts="{{[['tap',[['closeFriend']]]]}}" class="close-btn-three data-v-0369c01a" bindtap="__e"><image src="{{$root.g43}}" mode class="data-v-0369c01a"></image></view></view></uni-popup></view><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="reward-popup-two data-v-0369c01a" catchtouchmove="__e"><uni-popup vue-id="509e6661-6" type="center" maskClick="{{false}}" data-ref="linkSuccessPopup" class="data-v-0369c01a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="wrap-popup-two data-v-0369c01a"><view class="reward-wrap-two data-v-0369c01a" style="{{'background-image:'+('url('+$root.g44+')')+';'}}"><view class="title-two data-v-0369c01a">瓜分红包</view><view class="together-two data-v-0369c01a">- 好友已集齐 -</view><view class="txt-two data-v-0369c01a">{{$root.f6+"元"}}</view><view data-event-opts="{{[['tap',[['closeGetItNow']]]]}}" class="qu-btn-two data-v-0369c01a" style="{{'background-image:'+('url('+$root.g45+')')+';'}}" bindtap="__e">立即瓜分红包</view></view></view></uni-popup></view><ns-login vue-id="509e6661-7" data-ref="login" class="data-v-0369c01a vue-ref" bind:__l="__l"></ns-login><hover-nav vue-id="509e6661-8" class="data-v-0369c01a" bind:__l="__l"></hover-nav><loading-cover vue-id="509e6661-9" data-ref="loadingCover" class="data-v-0369c01a vue-ref" bind:__l="__l"></loading-cover><privacy-popup vue-id="509e6661-10" data-ref="privacyPopup" class="data-v-0369c01a vue-ref" bind:__l="__l"></privacy-popup></view>