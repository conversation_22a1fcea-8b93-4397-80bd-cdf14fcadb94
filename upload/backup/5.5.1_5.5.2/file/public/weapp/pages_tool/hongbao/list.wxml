<page-meta page-style="{{themeColor}}" class="data-v-36bca317"></page-meta><view class="{{['data-v-36bca317',isIphoneX?'iphone-x':'']}}"><mescroll-uni vue-id="4a3e846b-1" top="20" data-ref="mescroll" data-event-opts="{{[['^getData',[['getMemberCouponList']]]]}}" bind:getData="__e" class="data-v-36bca317 vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-36bca317"><block wx:if="{{$root.g0>0}}"><view class="coupon-listone data-v-36bca317"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toGoodsList',['$0'],[[['list','',index]]]]]]]}}" class="item data-v-36bca317" bindtap="__e"><view class="item-left data-v-36bca317"><view class="item-flex data-v-36bca317"><view class="item-base data-v-36bca317"><block wx:if="{{item.$orig.image!=''}}"><image src="{{item.g1}}" mode="aspectFit" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e" class="data-v-36bca317"></image></block><block wx:else><image src="{{item.g2}}" mode="aspectFit" class="data-v-36bca317"></image></block></view><view class="item-info data-v-36bca317"><view class="use_name data-v-36bca317">{{item.$orig.name}}</view><view class="use_title data-v-36bca317">{{item.$orig.divide_num+"名好友瓜分"+item.f0+"元红包"}}</view><view class="use_time data-v-36bca317">{{"有效期："+item.g3}}</view></view></view></view><view class="item-right data-v-36bca317"><view class="use_price data-v-36bca317"><text class="data-v-36bca317">￥</text>{{''+item.$orig.money+''}}</view><block wx:if="{{item.$orig.g_status==2}}"><view data-event-opts="{{[['tap',[['toGoodsList',['$0'],[[['list','',index]]]]]]]}}" class="tag data-v-36bca317" catchtap="__e">去瓜分</view></block><block wx:if="{{item.$orig.g_status==1}}"><view data-event-opts="{{[['tap',[['toGoodsList',['$0'],[[['list','',index]]]]]]]}}" class="tag data-v-36bca317" catchtap="__e">瓜分成功</view></block><block wx:if="{{item.$orig.g_status==0}}"><view data-event-opts="{{[['tap',[['toGoodsList',['$0'],[[['list','',index]]]]]]]}}" class="tag data-v-36bca317" catchtap="__e">组队中</view></block></view></view></block></view></block><block wx:else><view class="data-v-36bca317"><ns-empty vue-id="{{('4a3e846b-2')+','+('4a3e846b-1')}}" isIndex="{{false}}" text="暂无数据" class="data-v-36bca317" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni><hover-nav vue-id="4a3e846b-3" class="data-v-36bca317" bind:__l="__l"></hover-nav><loading-cover vue-id="4a3e846b-4" data-ref="loadingCover" class="data-v-36bca317 vue-ref" bind:__l="__l"></loading-cover><privacy-popup vue-id="4a3e846b-5" data-ref="privacyPopup" class="data-v-36bca317 vue-ref" bind:__l="__l"></privacy-popup></view>