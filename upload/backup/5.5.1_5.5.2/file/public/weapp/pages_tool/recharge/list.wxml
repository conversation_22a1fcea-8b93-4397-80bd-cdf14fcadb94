<page-meta page-style="{{themeColor}}"></page-meta><view class="page"><view><view class="recharge-title">充值</view><view class="account-box"><view class="label">账户余额</view><view class="value"><text class="price-font">{{balanceInfo.balance}}</text><text>元</text></view></view><view class="recharge-box"><view class="recharge-box-title">选择充值金额<view data-event-opts="{{[['tap',[['toOrderList',['$event']]]]]}}" bindtap="__e"><text class="color-base-text">充值记录</text></view></view><view class="box-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{$root.g0}}"><view data-event-opts="{{[['tap',[['itemClick',[index,'$0','$1'],[[['list','',index,'recharge_id']],[['list','',index,'buy_price']]]]]]]}}" class="{{['content-item',isIndex==index?'active':'']}}" bindtap="__e"><view class="price1"><text class="price-font">{{item.g1}}</text><text>元</text></view><view class="price2">{{"售价 "+item.$orig.buy_price+" 元"}}</view></view></block></block><view data-event-opts="{{[['tap',[['openRecharge']]]]}}" class="content-item" bindtap="__e"><view class="price1"><text class="other">其他金额</text></view></view></view><block wx:if="{{isIndex!==-1}}"><view class="box-text">{{'注：实际到账 '+list[isIndex].face_value+' 元'}}<block wx:if="{{list[isIndex].point>0||list[isIndex].growth>0||list[isIndex].coupon_id}}">，赠送：<block wx:if="{{list[isIndex].point>0}}"><text>{{list[isIndex].point+" 积分，"}}</text></block><block wx:if="{{list[isIndex].growth>0}}"><text>{{list[isIndex].growth+" 成长值"}}</text></block><block wx:if="{{list[isIndex].coupon_id!=''}}"><text>{{"，优惠券X"+$root.g2}}</text></block></block></view></block></view></view><view class="explain"><view class="title">充值说明</view><view class="explain_list"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.$orig.point||item.$orig.growth||item.$orig.coupon_id!=''}}"><view>{{'充值 '+item.$orig.face_value+' 元赠送：'}}<block wx:if="{{item.$orig.point}}"><text>{{item.$orig.point+" 积分，"}}</text></block><block wx:if="{{item.$orig.growth}}"><text>{{item.$orig.growth+" 成长值"}}</text></block><block wx:if="{{item.$orig.coupon_id!=''}}"><text>{{"，优惠券X"+item.g3}}</text></block></view></block></block><view>充值任意金额后，会存到您的账户资金中</view></view></view><uni-popup class="vue-ref" vue-id="03c8dc4e-1" type="bottom" data-ref="rechargePopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="rechargeList"><view class="tip">请输入充值金额</view><block wx:if="{{keywordsInfo.price!==''}}"><view class="input color-tip">{{''+(keywordsInfo.price?keywordsInfo.price:'金额')}}<text class="color-base-text">元</text></view></block><view class="keywords"><view class="keywords-left"><view data-event-opts="{{[['tap',[['keywordsDown',[1]]]]]}}" class="active" bindtap="__e">1</view><view data-event-opts="{{[['tap',[['keywordsDown',[2]]]]]}}" class="active" bindtap="__e">2</view><view data-event-opts="{{[['tap',[['keywordsDown',[3]]]]]}}" class="active" bindtap="__e">3</view><view data-event-opts="{{[['tap',[['keywordsDown',[4]]]]]}}" class="active" bindtap="__e">4</view><view data-event-opts="{{[['tap',[['keywordsDown',[5]]]]]}}" class="active" bindtap="__e">5</view><view data-event-opts="{{[['tap',[['keywordsDown',[6]]]]]}}" class="active" bindtap="__e">6</view><view data-event-opts="{{[['tap',[['keywordsDown',[7]]]]]}}" class="active" bindtap="__e">7</view><view data-event-opts="{{[['tap',[['keywordsDown',[8]]]]]}}" class="active" bindtap="__e">8</view><view data-event-opts="{{[['tap',[['keywordsDown',[9]]]]]}}" class="active" bindtap="__e">9</view><view></view><view data-event-opts="{{[['tap',[['keywordsDown',[0]]]]]}}" class="active" bindtap="__e">0</view><view data-event-opts="{{[['tap',[['keywordsDown',['.']]]]]}}" class="active" bindtap="__e">.</view></view><view class="keywords-right"><view data-event-opts="{{[['tap',[['delPrice',['$event']]]]]}}" bindtap="__e"><text class="iconfont icon-close"></text></view><view data-event-opts="{{[['tap',[['keywordsPayment']]]]}}" class="color-base-bg" bindtap="__e">确认充值</view></view></view></view></uni-popup><ns-payment class="vue-ref" vue-id="03c8dc4e-2" payMoney="{{payMoney}}" data-ref="choosePaymentPopup" data-event-opts="{{[['^confirm',[['toPay']]]]}}" bind:confirm="__e" bind:__l="__l"></ns-payment><button class="add-account" type="primary" disabled="{{recharge_id>0?false:true}}" data-event-opts="{{[['tap',[['openChoosePayment',['$event']]]]]}}" bindtap="__e">充值</button><loading-cover class="vue-ref" vue-id="03c8dc4e-3" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>