<view data-theme="{{themeStyle}}"><mescroll-uni class="vue-ref" vue-id="9cf7f374-1" size="20" data-ref="mescroll" data-event-opts="{{[['^getData',[['getBrandList']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><ns-adv vue-id="{{('9cf7f374-2')+','+('9cf7f374-1')}}" keyword="NS_BRAND" class-name="adv-wrap" bind:__l="__l"></ns-adv><block wx:if="{{$root.g0>0}}"><view class="brand-content"><uni-grid vue-id="{{('9cf7f374-3')+','+('9cf7f374-1')}}" column="{{3}}" showBorder="{{!1}}" data-event-opts="{{[['^change',[['change']]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><uni-grid-item vue-id="{{('9cf7f374-4-'+index)+','+('9cf7f374-3')}}" index="index" bind:__l="__l" vue-slots="{{['default']}}"><image class="brand-pic" src="{{item.g1}}" mode="widthFix"></image><view class="brand_name">{{item.$orig.brand_name}}</view></uni-grid-item></block></uni-grid></view></block><block wx:if="{{$root.g2==0}}"><view><ns-empty vue-id="{{('9cf7f374-5')+','+('9cf7f374-1')}}" text="暂无更多品牌,去首页看看吧" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni><hover-nav vue-id="9cf7f374-6" bind:__l="__l"></hover-nav><loading-cover class="vue-ref" vue-id="9cf7f374-7" data-ref="loadingCover" bind:__l="__l"></loading-cover><privacy-popup class="vue-ref" vue-id="9cf7f374-8" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>