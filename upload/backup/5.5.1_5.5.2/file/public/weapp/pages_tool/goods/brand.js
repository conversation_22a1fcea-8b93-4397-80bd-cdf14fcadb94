require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/goods/brand"],{"2c60":function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return r})),e.d(t,"a",(function(){return i}));var i={nsAdv:function(){return e.e("components/ns-adv/ns-adv").then(e.bind(null,"7e88"))},uniGrid:function(){return e.e("components/uni-grid/uni-grid").then(e.bind(null,"cf69"))},uniGridItem:function(){return e.e("components/uni-grid-item/uni-grid-item").then(e.bind(null,"0466"))},nsEmpty:function(){return e.e("components/ns-empty/ns-empty").then(e.bind(null,"52a6"))},hoverNav:function(){return e.e("components/hover-nav/hover-nav").then(e.bind(null,"c1f1"))},loadingCover:function(){return e.e("components/loading-cover/loading-cover").then(e.bind(null,"c003"))}},o=function(){var n=this,t=n.$createElement,e=(n._self._c,n.brandList.length),i=e>0?n.__map(n.brandList,(function(t,e){var i=n.__get_orig(t),o=n.$util.img(t.image_url);return{$orig:i,g1:o}})):null,o=n.brandList.length;n.$mp.data=Object.assign({},{$root:{g0:e,l0:i,g2:o}})},r=[]},"2d29":function(n,t,e){"use strict";var i=e("3160"),o=e.n(i);o.a},3160:function(n,t,e){},b627:function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={components:{uniGrid:function(){e.e("components/uni-grid/uni-grid").then(function(){return resolve(e("cf69"))}.bind(null,e)).catch(e.oe)},uniGridItem:function(){e.e("components/uni-grid-item/uni-grid-item").then(function(){return resolve(e("0466"))}.bind(null,e)).catch(e.oe)},nsAdv:function(){e.e("components/ns-adv/ns-adv").then(function(){return resolve(e("7e88"))}.bind(null,e)).catch(e.oe)}},data:function(){return{brandList:[],siteId:0}},onLoad:function(n){n.site_id&&(this.siteId=n.site_id)},onShow:function(){},methods:{change:function(n){this.$util.redirectTo("/pages/goods/list",{brand_id:this.brandList[n.detail.index].brand_id})},getBrandList:function(n){var t=this;this.$api.sendRequest({url:"/api/goodsbrand/page",data:{page_size:n.size,page:n.num,site_id:this.siteId},success:function(e){var i=[],o=e.message;0==e.code&&e.data?i=e.data.list:t.$util.showToast({title:o}),n.endSuccess(i.length),1==n.num&&(t.brandList=[]),t.brandList=t.brandList.concat(i),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(){n.endErr(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})}},onShareAppMessage:function(n){return{title:"你想要的大牌都在这里",path:"/pages_tool/goods/brand",success:function(n){},fail:function(n){}}}};t.default=i},e45a:function(n,t,e){"use strict";e.r(t);var i=e("b627"),o=e.n(i);for(var r in i)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(r);t["default"]=o.a},ecf0:function(n,t,e){"use strict";e.r(t);var i=e("2c60"),o=e("e45a");for(var r in o)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(r);e("2d29");var u=e("828b"),a=Object(u["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=a.exports},f1da:function(n,t,e){"use strict";(function(n,t){var i=e("47a9");e("d381");i(e("3240"));var o=i(e("ecf0"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["f1da","common/runtime","common/vendor"]]]);