<page-meta page-style="{{themeColor}}"></page-meta><view><view class="cf-container color-line-border"><view class="tab"><view data-event-opts="{{[['tap',[['changeSort',[1]]]]]}}" bindtap="__e"><text class="{{[sort==1?'color-base-text active color-base-border-bottom':'']}}">全部</text></view><view data-event-opts="{{[['tap',[['changeSort',[2,'reward']]]]]}}" bindtap="__e"><text class="{{[sort==2?'color-base-text active color-base-border-bottom':'']}}">满减券</text></view><view data-event-opts="{{[['tap',[['changeSort',[3,'discount']]]]]}}" bindtap="__e"><text class="{{[sort==3?'color-base-text active color-base-border-bottom':'']}}">折扣券</text></view><view data-event-opts="{{[['tap',[['changeSort',[4,'no_threshold']]]]]}}" bindtap="__e"><text class="{{[sort==4?'color-base-text active color-base-border-bottom':'']}}">无门槛券</text></view></view></view><mescroll-uni class="vue-ref" vue-id="4b35dce9-1" top="100" data-ref="mescroll" data-event-opts="{{[['^getData',[['getMemberCouponList']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><view class="coupon-listone"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['liClick',['$0',index],[[['list','',index]]]]]]]}}" class="item" style="{{'background-color:'+(item.$orig.useState==2?'#F2F2F2':'var(--main-color-shallow)')+';'}}" bindtap="__e"><view class="{{['item-base',(item.$orig.useState==2)?'disabled':'']}}"><image class="coupon-line" mode="heightFix" src="{{$root.g0}}"></image><view><block wx:if="{{item.$orig.type=='reward'}}"><view class="{{['use_price','price-font',(item.$orig.useState==2)?'disabled':'']}}"><text>￥</text>{{''+item.m0+''}}</view></block><block wx:else><block wx:if="{{item.$orig.type=='discount'}}"><view class="{{['use_price','price-font',(item.$orig.useState==2)?'disabled':'']}}">{{''+item.m1+''}}<text>折</text></view></block></block><block wx:if="{{item.$orig.at_least>0}}"><view class="{{['use_condition','font-size-tag',(item.$orig.useState==2)?'disabled':'']}}">{{"满"+item.$orig.at_least+"元可用"}}</view></block><block wx:else><view class="{{['use_condition','font-size-tag',(item.$orig.useState==2)?'disabled':'']}}">无门槛优惠券</view></block></view></view><view class="item-info"><view class="use_title"><view class="title">{{item.$orig.coupon_name}}</view><view class="{{['max_price',(item.$orig.useState==2)?'disabled':'']}}">{{item.$orig.goods_type_name}}</view><block wx:if="{{item.$orig.discount_limit!='0.00'}}"><view class="max_price">{{'(最大优惠'+item.$orig.discount_limit+'元)'}}</view></block><view class="{{['max_price',(item.$orig.useState==2)?'disabled':'']}}">{{item.$orig.use_channel_name}}</view><block wx:if="{{item.$orig.use_channel!='online'}}"><view class="{{['max_price','','truncate',(item.$orig.useState==2)?'disabled':'']}}">{{''+(item.$orig.use_store==='all'?'适用门店：全部门店':'适用门店：'+item.$orig.use_store_name)+''}}</view></block></view><block wx:if="{{item.$orig.validity_type==0}}"><view class="use_time">{{"有效期："+item.g1}}</view></block><block wx:else><block wx:if="{{item.$orig.validity_type==1}}"><view class="use_time">{{"有效期：领取之日起"+item.$orig.fixed_term+"日内有效"}}</view></block><block wx:else><view class="use_time">有效期：长期有效</view></block></block></view><view class="item-btn"><block wx:if="{{item.$orig.useState==0}}"><view data-event-opts="{{[['tap',[['receiveCoupon',['$0',index],[[['list','',index]]]]]]]}}" catchtap="__e">领取</view></block><block wx:if="{{item.$orig.useState==1}}"><view data-event-opts="{{[['tap',[['toGoodsList',['$0',index],[[['list','',index]]]]]]]}}" catchtap="__e">去使用</view></block><block wx:if="{{!item.$orig.received_type&&item.$orig.useState==2}}"><view class="disabled">已抢光</view></block><block wx:if="{{item.$orig.received_type=='out'}}"><view class="disabled">已抢光</view></block><block wx:if="{{item.$orig.received_type=='expire'}}"><view class="disabled">已过期</view></block><block wx:if="{{item.$orig.received_type=='limit'}}"><view class="disabled">已达上限</view></block></view></view></block></view><block wx:if="{{$root.g2==0}}"><view><ns-empty vue-id="{{('4b35dce9-2')+','+('4b35dce9-1')}}" text="暂无可领取的优惠券" isIndex="{{false}}" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni><loading-cover class="vue-ref" vue-id="4b35dce9-3" data-ref="loadingCover" bind:__l="__l"></loading-cover><ns-login class="vue-ref" vue-id="4b35dce9-4" data-ref="login" bind:__l="__l"></ns-login><hover-nav vue-id="4b35dce9-5" bind:__l="__l"></hover-nav><privacy-popup class="vue-ref" vue-id="4b35dce9-6" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>