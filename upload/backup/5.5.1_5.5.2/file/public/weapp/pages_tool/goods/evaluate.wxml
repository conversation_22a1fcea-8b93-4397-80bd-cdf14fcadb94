<page-meta page-style="{{themeColor}}"></page-meta><view class="goods-evaluate"><view class="evaluate-tab"><block wx:for="{{evaluateList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onEvaluateTab',['$0'],[[['evaluateList','',index,'value']]]]]]]}}" class="{{[evaluateTab==item.value?'active-tab':'']}}" bindtap="__e">{{''+item.name+"("+item.count+')'}}</view></block></view><mescroll-uni class="vue-ref" vue-id="7c6a6416-1" top="100" data-ref="mescroll" data-event-opts="{{[['^getData',[['getGoodsEvaluate']]]]}}" bind:getData="__e" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="evaluate-item"><view class="evaluator"><view><view class="evaluator-face"><block wx:if="{{item.$orig.member_headimg}}"><image src="{{item.g0}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[index]]]]]}}" binderror="__e"></image></block><block wx:else><image src="{{item.g1.head}}" mode="aspectFill"></image></block></view><view class="evaluator-info"><view class="evaluator-info-left"><block wx:if="{{item.g2}}"><view class="evaluator-name using-hidden">{{''+item.$orig.member_name[0]+"***"+item.$orig.member_name[item.g3-1]+''}}</view></block><block wx:else><text class="evaluator-name using-hidden">{{item.$orig.member_name}}</text></block><view class="evaluator-time color-tip">{{item.g4}}</view></view><view class="evaluator-xing"><xiao-star-component vue-id="{{('7c6a6416-2-'+index)+','+('7c6a6416-1')}}" starCount="{{item.$orig.scores*2}}" bind:__l="__l"></xiao-star-component></view></view></view></view><view class="cont">{{item.$orig.content}}</view><scroll-view scroll-x="true"><block wx:if="{{item.$orig.images}}"><view class="evaluate-img"><block wx:for="{{item.l0}}" wx:for-item="img" wx:for-index="img_index" wx:key="img_index"><view data-event-opts="{{[['tap',[['previewEvaluate',[index,img_index,'images']]]]]}}" class="img-box" bindtap="__e"><image src="{{img.g5}}" mode="aspectFill"></image></view></block></view></block></scroll-view><block wx:if="{{item.$orig.explain_first!=''}}"><view class="time shop-reply-box"><view class="shop-reply">商家回复：</view><view class="cont">{{item.$orig.explain_first}}</view></view></block><block wx:if="{{item.$orig.again_content!=''&&item.$orig.again_is_audit==1}}"><view class="review-evaluation color-base-text">追加评价</view><view class="cont">{{item.$orig.again_content}}</view><scroll-view scroll-x="true"><block wx:if="{{item.g6>0}}"><view class="evaluate-img"><block wx:for="{{item.l1}}" wx:for-item="again_img" wx:for-index="again_index" wx:key="again_index"><view data-event-opts="{{[['tap',[['previewEvaluate',[index,again_index,'again_images']]]]]}}" class="img-box" bindtap="__e"><image src="{{again_img.g7}}" mode="aspectFill"></image></view></block></view></block></scroll-view><block wx:if="{{item.$orig.again_explain!=''}}"><view class="time shop-reply-box"><block wx:if="{{item.$orig.again_explain!=''}}"><view class="shop-reply">商家回复：</view></block><view class="cont">{{item.$orig.again_explain}}</view></view></block></block></view></block><block wx:if="{{$root.g8==0}}"><view><ns-empty vue-id="{{('7c6a6416-3')+','+('7c6a6416-1')}}" text="暂无商品评价" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni><loading-cover class="vue-ref" vue-id="7c6a6416-4" data-ref="loadingCover" bind:__l="__l"></loading-cover></view>