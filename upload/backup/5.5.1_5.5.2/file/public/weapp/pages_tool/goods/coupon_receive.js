require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages_tool/goods/coupon_receive"],{"07e1":function(e,t,o){"use strict";o.d(t,"b",(function(){return n})),o.d(t,"c",(function(){return s})),o.d(t,"a",(function(){return i}));var i={loadingCover:function(){return o.e("components/loading-cover/loading-cover").then(o.bind(null,"c003"))},nsLogin:function(){return Promise.all([o.e("common/vendor"),o.e("components/ns-login/ns-login")]).then(o.bind(null,"2910"))},hoverNav:function(){return o.e("components/hover-nav/hover-nav").then(o.bind(null,"c1f1"))}},n=function(){var e=this.$createElement,t=(this._self._c,this.$util.img(this.path)),o=0==this.info.validity_type?this.$util.timeStampTurnTime(this.info.end_time):null;this.$mp.data=Object.assign({},{$root:{g0:t,g1:o}})},s=[]},7326:function(e,t,o){"use strict";o.r(t);var i=o("07e1"),n=o("86ec");for(var s in n)["default"].indexOf(s)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(s);o("ed6a");var u=o("828b"),c=Object(u["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=c.exports},"86ec":function(e,t,o){"use strict";o.r(t);var i=o("b603"),n=o.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(s);t["default"]=n.a},"94e9":function(e,t,o){},aad9:function(e,t,o){"use strict";(function(e,t){var i=o("47a9");o("d381");i(o("3240"));var n=i(o("7326"));e.__webpack_require_UNI_MP_PLUGIN__=o,t(n.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},b603:function(e,t,o){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={data:function(){return{info:{},couponTypeId:0,path:"",button:"领取",uid:0,couponBtnSwitch:!1,receivedNum:0,receivedType:""}},onLoad:function(t){var o=this;if(setTimeout((function(){o.addonIsExist.coupon||(o.$util.showToast({title:"商家未开启优惠券",mask:!0,duration:2e3}),setTimeout((function(){o.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.source_member&&e.setStorageSync("source_member",t.source_member),t.coupon_type_id&&(this.couponTypeId=t.coupon_type_id),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(i){-1!=i.indexOf("coupon_type_id")&&(o.couponTypeId=i.split("-")[1]),-1!=i.indexOf("m")&&(o.source_member=i.split("-")[1],e.setStorageSync("source_member",t.source_member))}))}},onShow:function(){this.getCoupon(),this.receivedNum=0,this.storeToken&&(e.getStorageSync("source_member")&&this.$util.onSourceMember(e.getStorageSync("source_member")),this.getReceivedNum())},onHide:function(){this.couponBtnSwitch=!1},methods:{getCoupon:function(){var e=this;this.$api.sendRequest({url:"/coupon/api/coupon/typeinfo",data:{coupon_type_id:this.couponTypeId},success:function(t){var o=t.data;o?(e.info=o,e.path=o.qrcode,e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.buttonRefresh(),e.setPublicShare()):(e.$util.showToast({title:t.message}),setTimeout((function(){e.$util.redirectTo("/pages_tool/goods/coupon",{},"redirectTo")}),1e3))},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},receiveGoodsCoupon:function(e){var t=this;this.storeToken?this.isCanReceive&&(this.couponBtnSwitch||(this.couponBtnSwitch=!0,this.$api.sendRequest({url:"/coupon/api/coupon/receive",data:{coupon_type_id:e,get_type:2},success:function(e){var o=e.data,i=e.message;t.receivedType=o.type,0==e.code&&(i="领取成功",t.receivedNum+=1),t.buttonRefresh(),t.$util.showToast({title:i}),t.couponBtnSwitch=!1},fail:function(e){t.couponBtnSwitch=!1}}))):this.$refs.login.open("/pages_tool/goods/coupon_receive?coupon_type_id="+this.couponTypeId)},getReceivedNum:function(){var e=this;this.$api.sendRequest({url:"/coupon/api/coupon/receivedNum",data:{coupon_type_id:this.couponTypeId},success:function(t){t.code>=0&&(e.receivedNum=t.data,e.buttonRefresh())}})},buttonRefresh:function(){this.button="领取",(this.info.count<=this.info.lead_count||"out"==this.receivedType)&&(this.button="来迟了该优惠券已被领取完了！"),2!=this.info.status&&"expire"!=this.receivedType||(this.button="该优惠券活动已结束！"),3==this.info.status&&(this.button="该优惠券活动已关闭"),"limit"==this.receivedType&&(this.button="该优惠券领取已达到上限！")},setPublicShare:function(){var e=this.$config.h5Domain+"/pages_tool/goods/coupon_receive?coupon_type_id="+this.couponTypeId;this.memberInfo&&this.memberInfo.member_id&&(e+="&source_member="+this.memberInfo.member_id);var t="";if("reward"==this.info.type?t="恭喜您获得一张"+this.info.money+"元的优惠券":"discount"==this.info.type&&(t="恭喜您获得一张"+this.info.discount+"折优惠券"),this.info.at_least>0)var o="满".concat(this.info.at_least,"元可用");else o="无门槛优惠券";this.$util.setPublicShare({title:t,desc:o,link:e,imgUrl:this.siteInfo?this.$util.img(this.siteInfo.logo_square):""})}},computed:{isCanReceive:{get:function(){return!(this.info.count<=this.info.lead_count||["limit","expire","out"].includes(this.receivedType)||2==this.info.status||3==this.info.status)}}},onShareAppMessage:function(e){var t="";"reward"==this.info.type?t="恭喜您获得一张"+this.info.money+"元的优惠券":"discount"==this.info.type&&(t="恭喜您获得一张"+this.info.discount+"折优惠券"),this.info.at_least>0?t+="，满".concat(this.info.at_least,"元可用"):t+="，无门槛优惠券";var o="/pages_tool/goods/coupon_receive?coupon_type_id="+this.couponTypeId;return this.memberInfo&&this.memberInfo.member_id&&(o+="&source_member="+this.memberInfo.member_id),{title:t,path:o,success:function(e){},fail:function(e){}}}};t.default=o}).call(this,o("df3c")["default"])},ed6a:function(e,t,o){"use strict";var i=o("94e9"),n=o.n(i);n.a}},[["aad9","common/runtime","common/vendor"]]]);