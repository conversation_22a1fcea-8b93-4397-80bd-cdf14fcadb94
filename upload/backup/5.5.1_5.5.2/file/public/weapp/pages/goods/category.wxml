<page-meta page-style="{{themeColor}}"></page-meta><view><block wx:if="{{diyData}}"><block><block wx:for="{{diyData.value}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{item.componentName=='GoodsCategory'}}"><view><diy-category class="vue-ref-in-for" vue-id="{{'8fad5394-1-'+index}}" value="{{item}}" data-ref="category" data-event-opts="{{[['^tologin',[['toLogin']]]]}}" bind:tologin="__e" bind:__l="__l"></diy-category></view></block></block></block></block></block><ns-login class="vue-ref" vue-id="8fad5394-2" data-ref="login" bind:__l="__l"></ns-login><loading-cover class="vue-ref" vue-id="8fad5394-3" data-ref="loadingCover" bind:__l="__l"></loading-cover><privacy-popup class="vue-ref" vue-id="8fad5394-4" data-ref="privacyPopup" bind:__l="__l"></privacy-popup><view id="tab-bar"><diy-bottom-nav vue-id="8fad5394-5" bind:__l="__l"></diy-bottom-nav></view></view>