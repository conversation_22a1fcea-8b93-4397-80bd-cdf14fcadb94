<page-meta page-style="{{themeColor}}"></page-meta><view style="height:100vh;width:100vw;overflow:hidden;"><view class="container"><ns-navbar vue-id="e780df58-1" data="{{navbarData}}" isBack="{{false}}" bind:__l="__l"></ns-navbar><scroll-view class="scroll-view" scroll-y="{{true}}" show-scrollbar="{{false}}" refresher-enabled="{{true}}" refresher-triggered="{{refresherTriggered}}" data-event-opts="{{[['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindrefresherrefresh="__e"><block wx:if="{{hasData}}"><block><block wx:if="{{$root.g0}}"><view class="cart-header"><view class="num-wrap">{{"共"+$root.g1+"种商品"}}</view><view data-event-opts="{{[['tap',[['changeAction',['$event']]]]]}}" class="cart-action" bindtap="__e">{{''+(isAction?$root.m0:$root.m1)+''}}</view></view></block><block wx:for="{{$root.l3}}" wx:for-item="siteItem" wx:for-index="siteIndex" wx:key="siteIndex"><view class="cart-wrap"><block wx:if="{{discount.coupon_info}}"><view class="coupon-use-tips"><view><text class="title color-base-text">优惠券</text><text class="desc">{{"领券结算最高可减"+siteItem.f0+"元"}}</text></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="color-base-text" bindtap="__e">{{'点击'+(discount.coupon_info.receive_type=='wait'?'领取':'查看')+''}}<text class="iconfont icon-right"></text></view></view></block><block wx:if="{{globalStoreConfig&&globalStoreConfig.store_business=='store'&&globalStoreInfo}}"><view class="store-wrap"><text class="iconfont icon-dianpu"></text><text class="name">{{globalStoreInfo.store_name}}</text></view></block><block wx:for="{{siteItem.l2}}" wx:for-item="item" wx:for-index="cartIndex" wx:key="cart_id"><block><view data-event-opts="{{[['touchstart',[['touchS',['$event']]]],['touchend',[['touchE',['$event','$0'],[[['cartData','',siteIndex],['cartList','cart_id',item.$orig.cart_id]]]]]]]}}" class="cart-goods" bindtouchstart="__e" bindtouchend="__e"><view class="{{['goods-wrap',(item.$orig.edit)?'edit':'']}}"><view data-event-opts="{{[['tap',[['singleElection',[siteIndex,cartIndex]]]]]}}" class="{{['iconfont',item.$orig.checked?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}" bindtap="__e"></view><view data-event-opts="{{[['tap',[['toGoodsDetail',['$0'],[[['cartData','',siteIndex],['cartList','cart_id',item.$orig.cart_id]]]]]]]}}" class="goods-img" bindtap="__e"><image src="{{item.g2}}" mode="aspectFill" data-event-opts="{{[['error',[['imageError',[siteIndex,cartIndex]]]]]}}" binderror="__e"></image></view><view class="goods-info"><view><view data-event-opts="{{[['tap',[['toGoodsDetail',['$0'],[[['cartData','',siteIndex],['cartList','cart_id',item.$orig.cart_id]]]]]]]}}" class="goods-name" bindtap="__e">{{item.$orig.goods_name}}</view><view class="sku-wrap"><view class="sku"><block wx:if="{{item.g3}}"><view data-event-opts="{{[['tap',[['selectSku',['$0'],[[['cartData','',siteIndex],['cartList','cart_id',item.$orig.cart_id]]]]]]]}}" class="goods-spec" bindtap="__e"><block wx:for="{{item.l0}}" wx:for-item="x" wx:for-index="i" wx:key="i"><block>{{''+x.$orig.spec_name+":"+x.$orig.spec_value_name+"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t"+(i<x.g4-1?';':'')+''}}</block></block></view></block><block wx:if="{{item.g5}}"><text class="iconfont icon-unfold"></text></block></view></view></view><view class="goods-sub-section"><block wx:if="{{item.$orig.promotion_type==1}}"><block><block wx:if="{{item.m2}}"><block><view class="goods-price"><view class="bottom-price price-style large"><text class="unit price-style small">{{item.m3}}</text>{{''+item.g6[0]+''}}<text class="unit price-style small">{{"."+item.g7[1]}}</text><image src="{{item.g8}}"></image></view></view></block></block><block wx:else><block><view class="goods-price"><view class="bottom-price price-style large"><text class="unit price-style small">{{item.m4}}</text>{{''+item.g9[0]+''}}<text class="unit price-style small">{{"."+item.g10[1]}}</text><image src="{{item.g11}}"></image></view></view></block></block></block></block><block wx:else><block><block wx:if="{{item.m5>0}}"><block><view class="goods-price"><view class="bottom-price price-style large"><text class="unit price-style small">{{item.m6}}</text>{{''+item.g12[0]+''}}<text class="unit price-style small">{{"."+item.g13[1]}}</text><image src="{{item.g14}}"></image></view></view></block></block><block wx:else><block><view class="goods-price"><view class="bottom-price price-style large"><text class="unit price-style small">{{item.m7}}</text>{{''+item.g15[0]+''}}<text class="unit price-style small">{{"."+item.g16[1]}}</text></view></view></block></block></block></block><uni-number-box vue-id="{{'e780df58-2-'+siteIndex+'-'+cartIndex}}" min="{{item.$orig.min_buy>1?item.$orig.min_buy:1}}" max="{{item.$orig.max_buy>0&&item.$orig.max_buy<item.$orig.stock?item.$orig.max_buy:item.$orig.stock}}" size="small" value="{{item.m8}}" data-event-opts="{{[['^change',[['cartNumChange',['$event',['o',['siteIndex',siteIndex],['cartIndex',cartIndex]]]]]],['^limit',[['goodsLimit',['$event',['o',['siteIndex',siteIndex],['cartIndex',cartIndex]]]]]]]}}" bind:change="__e" bind:limit="__e" bind:__l="__l"></uni-number-box></view><block wx:if="{{manjian&&manjian['sku_'+item.$orig.sku_id]}}"><view class="discount-wrap"><text class="discount-tag">满减</text><scroll-view class="scroll-view" scroll-x="true"><block wx:for="{{item.l1}}" wx:for-item="mitem" wx:for-index="key" wx:key="key"><block><block wx:if="{{mitem.$orig.discount_money}}"><text>{{mitem.m9+"减"+mitem.$orig.discount_money}}</text></block><block wx:if="{{mitem.$orig.discount_money}}"><text class="interval"></text></block></block></block></scroll-view></view></block></view></view><view data-event-opts="{{[['tap',[['deleteCart',['single',siteIndex,cartIndex]]]]]}}" class="{{['item-del','color-base-bg',(item.$orig.edit)?'show':'']}}" bindtap="__e">{{siteItem.m10}}</view></view></block></block></view></block><block wx:if="{{$root.g17}}"><view class="cart-wrap invalid"><view class="cart-header"><view class="num-wrap"><text class="font-size-base">{{"失效商品"+$root.g18+"件"}}</text></view><view data-event-opts="{{[['tap',[['clearInvalidGoods',['$event']]]]]}}" class="cart-action color-base-text" bindtap="__e">清空</view></view><block wx:for="{{$root.l5}}" wx:for-item="goodsItem" wx:for-index="goodsIndex" wx:key="goodsIndex"><block><view class="cart-goods invalid-goods"><view class="goods-wrap"><view class="iconfont icon-yuan_checked color-tip"></view><view class="goods-img"><image src="{{goodsItem.g19}}" mode="aspectFill"></image></view><view class="goods-info"><view class="goods-name">{{goodsItem.$orig.sku_name}}</view><view><view class="sku"><block wx:if="{{goodsItem.g20}}"><view class="goods-spec"><block wx:for="{{goodsItem.l4}}" wx:for-item="x" wx:for-index="i" wx:key="i"><block>{{''+x.$orig.spec_name+":"+x.$orig.spec_value_name+"\n\t\t\t\t\t\t\t\t\t\t\t\t\t"+(i<x.g21-1?'; ':'')+''}}</block></block></view></block></view></view><view class="goods-sub-section"><view class="goods-price"><text class="bottom-price price-style large"><block wx:if="{{goodsItem.$orig.member_price>0&&goodsItem.$orig.member_price<goodsItem.$orig.discount_price}}"><text class="unit price-style small">{{goodsItem.m11}}</text>{{''+goodsItem.g22[0]+''}}<text class="unit price-style small">{{"."+goodsItem.g23[1]}}</text><image src="{{goodsItem.g24}}"></image></block><block wx:else><text class="unit price-style small">{{goodsItem.m12}}</text>{{''+goodsItem.g25[0]+''}}<text class="unit price-style small">{{"."+goodsItem.g26[1]}}</text></block></text></view><text class="invalid-mark">已失效</text></view></view></view></view></block></block></view></block></block></block><block wx:else><block><view class="cart-empty"><ns-empty vue-id="e780df58-3" text="购物车为空" subText="赶紧去逛逛, 购买心仪的商品吧" isIndex="{{$root.m13}}" bind:__l="__l"></ns-empty><block wx:if="{{!storeToken}}"><button class="button mini" type="primary" size="mini" data-event-opts="{{[['tap',[['toLogin',['$event']]]]]}}" bindtap="__e">去登录</button></block></view></block></block><ns-goods-recommend class="vue-ref" vue-id="e780df58-4" route="cart" data-ref="goodrecommend" bind:__l="__l"></ns-goods-recommend><view class="cart-bottom-block"></view><view class="page-bottom" style="{{'height:'+(tabBarHeight)+';'}}"></view></scroll-view><uni-popup class="vue-ref" vue-id="e780df58-5" custom-class="uni-popup-discount" type="bottom" data-ref="discountPopup" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{$root.g27}}"><view class="discount-popup popup"><view class="popup-header"><text class="tit">优惠明细</text><text data-event-opts="{{[['tap',[['toggleDiscountPopup',['$event']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><view data-event-opts="{{[['tap',[['toggleDiscountPopup',['$event']]]]]}}" class="{{['popup-body',(isIphoneX)?'safe-area':'']}}" bindtap="__e"><view class="detail-item"><view class="title">商品总额</view><view class="money price-font">{{"￥"+$root.f1}}</view></view><block wx:if="{{discount.coupon_money>0}}"><view class="detail-item"><view class="title">优惠券</view><view class="money price-font reduce">{{"-￥"+$root.f2}}</view></view></block><block wx:if="{{discount.promotion_money>0}}"><view class="detail-item"><view class="title">满减</view><view class="money reduce price-font">{{"-￥"+$root.f3+''}}</view></view></block><view class="detail-item total"><view class="title">合计</view><view class="money price-font">{{"￥"+$root.f4}}</view></view></view><view style="{{'height:'+(tabBarHeight)+';'}}"></view></view></block></uni-popup><block wx:if="{{discount.coupon_info}}"><uni-popup class="vue-ref" vue-id="e780df58-6" custom-class="uni-popup-discount" type="bottom" data-ref="couponPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="coupon-popup popup"><view class="popup-header"><text class="tit">优惠券</text><text data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="iconfont icon-close" bindtap="__e"></text></view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="{{['popup-body',(isIphoneX)?'safe-area':'']}}" bindtap="__e"><view class="coupon-item"><view class="coupon-info" style="{{'background-color:'+(discount.coupon_info.receive_type!='wait'?'#F2F2F2':'var(--main-color-shallow)')+';'}}"><view class="info-wrap"><image class="coupon-line" mode="heightFix" src="{{$root.g28}}"></image><view class="coupon-money"><block wx:if="{{discount.coupon_info.type=='reward'}}"><text class="unit">{{$root.m14}}</text><text class="money">{{$root.m15}}</text></block><block wx:else><block wx:if="{{discount.coupon_info.type=='discount'}}"><text class="money">{{$root.m16}}</text><text class="unit">折</text></block></block><view class="at-least"><block wx:if="{{discount.coupon_info.at_least>0}}">{{'满'+discount.coupon_info.at_least+'可用'}}</block><block wx:else>无门槛</block></view></view></view><view class="desc-wrap"><view class="coupon-name">{{discount.coupon_info.coupon_name}}</view><block wx:if="{{discount.coupon_info.type=='discount'&&discount.coupon_info.discount_limit>0}}"><view class="limit">{{'最多可抵￥'+discount.coupon_info.discount_limit+''}}</view></block><block wx:if="{{discount.coupon_info.validity_type==0}}"><view class="time font-size-goods-tag">{{'有效期：'+$root.g29+''}}</view></block><block wx:else><block wx:if="{{discount.coupon_info.validity_type==1}}"><view class="time font-size-goods-tag">{{'有效期：领取之日起'+discount.coupon_info.fixed_term+'天内有效'}}</view></block><block wx:else><view class="time font-size-goods-tag">有效期：长期有效</view></block></block></view><block wx:if="{{discount.coupon_info.receive_type!='wait'}}"><button type="primary" disabled="{{true}}">已领取</button></block><block wx:else><button type="primary" data-event-opts="{{[['tap',[['receiveCoupon',['$0'],['discount.coupon_info.coupon_type_id']]]]]}}" catchtap="__e">领取</button></block></view></view></view><view style="{{'height:'+(tabBarHeight)+';'}}"></view><view class="cart-bottom-block"></view></view></uni-popup></block></view><block wx:if="{{hasData}}"><view class="{{['cart-bottom',(isIphoneX)?'active':'']}}" style="{{'bottom:'+(tabBarHeight)+';'}}"><view data-event-opts="{{[['tap',[['allElection',['$event']]]]]}}" class="all-election" bindtap="__e"><view class="{{['iconfont',checkAll?'icon-yuan_checked color-base-text':'icon-yuan_checkbox']}}"></view><text>{{$root.m17}}</text></view><view class="settlement-info" style="{{'visibility:'+(isAction?'hidden':'visible')+';'}}"><view class="money">{{''+$root.m18+'：'}}<text class="unit price-font">{{$root.m19}}</text><block wx:if="{{$root.g30}}"><block><text class="value price-font">{{$root.g31[0]}}</text><text class="unit price-font">{{"."+$root.g32[1]}}</text></block></block><block wx:else><block><text class="value price-font">{{$root.g33[0]}}</text><text class="unit price-font">{{"."+$root.g34[1]}}</text></block></block></view><block wx:if="{{$root.g35}}"><view data-event-opts="{{[['tap',[['toggleDiscountPopup',['$event']]]]]}}" class="detail" bindtap="__e">优惠明细<text class="{{['iconfont','icon-unfold',(!discountPopupShow)?'open':'']}}"></text></view></block></view><block wx:if="{{isAction}}"><view class="action-btn"><button class="mini" type="primary" size="mini" data-event-opts="{{[['tap',[['deleteCart',['all']]]]]}}" bindtap="__e">{{$root.m20}}</button></view></block><block wx:else><view class="action-btn"><block wx:if="{{totalCount!=0}}"><button class="mini" type="primary" size="mini" data-event-opts="{{[['tap',[['settlement',['$event']]]]]}}" bindtap="__e">{{''+(discount.coupon_info&&discount.coupon_info.receive_type=='wait'?'领券':'立即')+"结算("+totalCount+')'}}</button></block><block wx:else><button class="mini" type="primary" size="mini" disabled="{{true}}" data-event-opts="{{[['tap',[['settlement',['$event']]]]]}}" bindtap="__e">{{$root.m21+"("+totalCount+")"}}</button></block></view></block></view></block><diy-bottom-nav vue-id="e780df58-7" bind:__l="__l"></diy-bottom-nav><block wx:if="{{goodsSkuDetail}}"><ns-goods-sku class="vue-ref" vue-id="e780df58-8" goods-detail="{{goodsSkuDetail}}" goods-id="{{goodsSkuDetail.goods_id}}" max-buy="{{goodsSkuDetail.max_buy}}" min-buy="{{goodsSkuDetail.min_buy}}" data-ref="selectSku" data-event-opts="{{[['^refresh',[['refreshSkuDetail']]]]}}" bind:refresh="__e" bind:__l="__l"></ns-goods-sku></block><loading-cover class="vue-ref" vue-id="e780df58-9" data-ref="loadingCover" bind:__l="__l"></loading-cover><block wx:if="{{showTop}}"><to-top bind:toTop="__e" vue-id="e780df58-10" data-event-opts="{{[['^toTop',[['scrollToTopNative']]]]}}" bind:__l="__l"></to-top></block><ns-login class="vue-ref" vue-id="e780df58-11" data-ref="login" bind:__l="__l"></ns-login><privacy-popup class="vue-ref" vue-id="e780df58-12" data-ref="privacyPopup" bind:__l="__l"></privacy-popup></view>