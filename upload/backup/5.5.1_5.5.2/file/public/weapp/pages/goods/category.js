(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/goods/category"],{3079:function(t,e,a){"use strict";var i=a("7a69"),n=a.n(i);n.a},"690b":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={diyCategory:function(){return a.e("components/diy-components/diy-category").then(a.bind(null,"4fbd"))},nsLogin:function(){return Promise.all([a.e("common/vendor"),a.e("components/ns-login/ns-login")]).then(a.bind(null,"2910"))},loadingCover:function(){return a.e("components/loading-cover/loading-cover").then(a.bind(null,"c003"))},diyBottomNav:function(){return a.e("components/diy-components/diy-bottom-nav").then(a.bind(null,"2532"))}},n=function(){var t=this.$createElement;this._self._c},o=[]},"7a69":function(t,e,a){},e9ff:function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={components:{},data:function(){return{diyData:null,mpShareData:null}},onLoad:function(){t.hideTabBar(),this.getDiyInfo()},onShow:function(){this.$refs.category&&this.$refs.category[0].pageShow()},onUnload:function(){!this.storeToken&&this.$refs.login&&this.$refs.login.cancelCompleteInfo()},methods:{getDiyInfo:function(){var e=this;this.$api.sendRequest({url:"/api/diyview/info",data:{name:"DIY_VIEW_GOODS_CATEGORY"},success:function(a){0==a.code&&a.data&&(e.diyData=a.data,e.diyData.value&&(e.diyData=JSON.parse(e.diyData.value),e.setPublicShare(),e.setMpShare(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()),t.stopPullDownRefresh())}})},setMpShare:function(){var t=this.$util.getCurrentRoute().path;this.$store.state.memberInfo&&this.$store.state.memberInfo.member_id&&(t=this.$util.getCurrentShareRoute(this.$store.state.memberInfo.member_id).path);var e={title:this.diyData.global.weappShareTitle,path:t,imageUrl:this.$util.img(this.diyData.global.weappShareImage),success:function(t){},fail:function(t){}},a={title:this.diyData.global.weappShareTitle,query:t,imageUrl:this.$util.img(this.diyData.global.weappShareImage)};this.mpShareData={appMessage:e,timeLine:a},this.mpShareData.timeLine.query=this.mpShareData.timeLine.query.split("?")[1]||""},setPublicShare:function(){var t=this.$config.h5Domain+"/pages/goods/category",e=this.$store.state.globalStoreInfo;e&&(t+="?store_id="+e.store_id),this.$util.setPublicShare({title:this.diyData.global.wechatShareTitle||this.diyData.global.title,desc:this.diyData.global.wechatShareDesc,link:t,imgUrl:this.diyData.global.weappShareImage?this.$util.img(this.diyData.global.weappShareImage):this.$util.img(this.siteInfo.logo_square)})},toLogin:function(){this.$refs.login.open("/pages/goods/category")}},onPullDownRefresh:function(){t.hideTabBar(),this.getDiyInfo()},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine}};e.default=a}).call(this,a("df3c")["default"])},f049:function(t,e,a){"use strict";a.r(e);var i=a("e9ff"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},f98b:function(t,e,a){"use strict";(function(t,e){var i=a("47a9");a("d381");i(a("3240"));var n=i(a("fd7f"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(n.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},fd7f:function(t,e,a){"use strict";a.r(e);var i=a("690b"),n=a("f049");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("3079");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports}},[["f98b","common/runtime","common/vendor"]]]);