(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/order/detail"],{"016b":function(e,r,t){"use strict";var o=t("634d"),a=t.n(o);a.a},"634d":function(e,r,t){},7496:function(e,r,t){"use strict";(function(e,r){var o=t("47a9");t("d381");o(t("3240"));var a=o(t("ee0a"));e.__webpack_require_UNI_MP_PLUGIN__=t,r(a.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},"880a":function(e,r,t){"use strict";(function(e){var o=t("47a9");Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var a=o(t("2817")),n={data:function(){return{isIphoneX:!1,orderId:0,merchantTradeNo:"",orderData:{action:[],virtual_goods:{is_veirfy:0,verify_record:[]}},action:{icon:""},evaluateConfig:{evaluate_audit:1,evaluate_show:0,evaluate_status:1}}},mixins:[a.default],components:{nsGoodsRecommend:function(){t.e("components/ns-goods-recommend/ns-goods-recommend").then(function(){return resolve(t("7254"))}.bind(null,t)).catch(t.oe)},nsPayment:function(){t.e("components/payment/payment").then(function(){return resolve(t("b6f2"))}.bind(null,t)).catch(t.oe)},nsContact:function(){t.e("components/ns-contact/ns-contact").then(function(){return resolve(t("5036"))}.bind(null,t)).catch(t.oe)}},onLoad:function(e){e.order_id&&(this.orderId=e.order_id),e.merchant_trade_no&&(this.merchantTradeNo=e.merchant_trade_no)},onPullDownRefresh:function(){this.getOrderData(),setTimeout((function(){e.stopPullDownRefresh()}),50)},onShow:function(){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?(this.getEvaluateConfig(),this.getOrderData()):this.$util.redirectTo("/pages_tool/login/index",{back:"/pages/order/detail?order_id="+this.orderId+"&merchant_trade_no="+this.merchantTradeNo}),this.$refs.choosePaymentPopup&&this.$refs.choosePaymentPopup.pageShow()},methods:{goDetail:function(e){this.$util.redirectTo("/pages/goods/detail",{goods_id:e.goods_id})},goRefund:function(e){this.$util.redirectTo("/pages_tool/order/refund",{order_goods_id:e})},goRefundDetail:function(e){this.$util.redirectTo("/pages_tool/order/refund_detail",{order_goods_id:e})},getOrderData:function(){var e=this;this.$api.sendRequest({url:"/api/order/detail",data:{order_id:this.orderId,merchant_trade_no:this.merchantTradeNo},success:function(r){if(r.code>=0){if(0==r.data.order_status){var t=Date.parse(new Date)/1e3;r.data.closeTimeMachine=e.$util.countDown(r.data.create_time+r.data.auto_close-t)}var o=0;e.orderData=r.data,e.orderId=e.orderData.order_id;var a=[];e.orderData.order_goods.forEach((function(e){e.sku_spec_format?e.sku_spec_format=JSON.parse(e.sku_spec_format):e.sku_spec_format=[],0!=e.refund_status&&-1!=e.refund_status||(o+=1,a.push(e.order_goods_id))})),e.action=JSON.parse(r.data.order_status_action),o>1?(e.orderData.refund_batch_status=!0,e.orderData.refund_order_goods_ids=a):e.orderData.refund_batch_status=!1,""!=e.orderData.delivery_store_info&&(e.orderData.delivery_store_info=JSON.parse(e.orderData.delivery_store_info)),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}else e.$util.showToast({title:"未获取到订单信息！"}),setTimeout((function(){e.$util.redirectTo("/pages/order/list")}),1500)},fail:function(r){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},operation:function(r){var t=this;switch(r){case"orderDelete":this.orderDelete(this.orderData.order_id,(function(){setTimeout((function(){1==getCurrentPages().length?t.$util.redirectTo("/pages/member/index"):e.navigateBack()}),500)}));break;case"orderPay":this.orderPay(this.orderData);break;case"orderClose":this.orderClose(this.orderData.order_id,(function(){t.getOrderData()}));break;case"memberTakeDelivery":this.orderDelivery(this.orderData,(function(){t.getOrderData()}));break;case"trace":this.$util.redirectTo("/pages_tool/order/logistics",{order_id:this.orderData.order_id});break;case"memberOrderEvaluation":this.$util.redirectTo("/pages_tool/order/evaluate",{order_id:this.orderData.order_id});break;case"memberBatchRefund":this.$util.redirectTo("/pages_tool/order/refund_type_select",{order_id:this.orderId});break;case"memberVirtualTakeDelivery":this.orderVirtualDelivery(this.orderData,(function(){t.getOrderData()}));break;case"orderOfflinePay":this.$util.redirectTo("/pages_tool/pay/offlinepay",{outTradeNo:this.orderData.out_trade_no});break}},imageError:function(e){this.orderData.order_goods[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},getEvaluateConfig:function(){var e=this;this.$api.sendRequest({url:"/api/goodsevaluate/config",success:function(r){if(0==r.code){var t=r.data;e.evaluateConfig=t}}})},openChoosePayment:function(){this.$refs.choosePaymentPopup.open()},previewMedia:function(r){var t=[];t.push(r),e.previewImage({urls:t})}},filters:{abs:function(e){return Math.abs(parseFloat(e)).toFixed(2)}}};r.default=n}).call(this,t("df3c")["default"])},abc5:function(e,r,t){"use strict";var o=t("e16f"),a=t.n(o);a.a},cb69:function(e,r,t){"use strict";t.r(r);var o=t("880a"),a=t.n(o);for(var n in o)["default"].indexOf(n)<0&&function(e){t.d(r,e,(function(){return o[e]}))}(n);r["default"]=a.a},d4ca:function(e,r,t){"use strict";t.d(r,"b",(function(){return a})),t.d(r,"c",(function(){return n})),t.d(r,"a",(function(){return o}));var o={uniCountDown:function(){return t.e("components/uni-count-down/uni-count-down").then(t.bind(null,"e12a"))},nsContact:function(){return t.e("components/ns-contact/ns-contact").then(t.bind(null,"5036"))},nsGoodsRecommend:function(){return t.e("components/ns-goods-recommend/ns-goods-recommend").then(t.bind(null,"7254"))},nsPayment:function(){return t.e("components/ns-payment/ns-payment").then(t.bind(null,"7aec"))},loadingCover:function(){return t.e("components/loading-cover/loading-cover").then(t.bind(null,"c003"))}},a=function(){var e=this,r=e.$createElement,t=(e._self._c,e.$util.img("public/uniapp/order/status-wrap-bg.png")),o=e.$util.img(e.action.icon),a="presale"==e.orderData.promotion_type&&1==e.orderData.order_status?e.$util.timeStampTurnTime(e.orderData.predict_delivery_time,"Y-m-d"):null,n=2==e.orderData.order_type&&e.orderData.pay_status?e.$util.img(e.orderData.pickup_barcode):null,i=2==e.orderData.order_type&&e.orderData.pay_status?e.$util.img(e.orderData.pickup):null,d=e.$lang("common.currencySymbol"),u=e.__map(e.orderData.order_goods,(function(r,t){var o=e.__get_orig(r),a=e.$util.img(r.sku_image,{size:"mid"}),n=r.sku_spec_format?e.__map(r.sku_spec_format,(function(t,o){var a=e.__get_orig(t),n=r.sku_spec_format.length;return{$orig:a,g6:n}})):null,i=parseFloat(r.price).toFixed(2).split("."),d=parseFloat(r.price).toFixed(2).split("."),u=r.card_item_id?e.$lang("common.currencySymbol"):null,s=r.card_item_id?parseFloat(r.card_promotion_money).toFixed(2).split("."):null,l=r.card_item_id?parseFloat(r.card_promotion_money).toFixed(2).split("."):null,c=r.form?e.__map(r.form,(function(r,t){var o=e.__get_orig(r),a="Img"==r.controller?e.__map(r.img_lists,(function(r,t){var o=e.__get_orig(r),a=e.$util.img(r);return{$orig:o,g11:a}})):null;return{$orig:o,l1:a}})):null;return{$orig:o,g5:a,l0:n,g7:i,g8:d,m1:u,g9:s,g10:l,l2:c}})),s=e.$util.timeStampTurnTime(e.orderData.create_time),l=e.orderData.close_time>0?e.$util.timeStampTurnTime(e.orderData.close_time):null,c=e.orderData.pay_status>0?e.$util.timeStampTurnTime(e.orderData.pay_time):null,_=e.orderData.form?e.__map(e.orderData.form,(function(r,t){var o=e.__get_orig(r),a="Img"==r.controller?e.__map(r.img_lists,(function(r,t){var o=e.__get_orig(r),a=e.$util.img(r);return{$orig:o,g15:a}})):null;return{$orig:o,l4:a}})):null,m={order_id:e.orderData.order_id},g=e.orderData.virtual_goods&&2==e.orderData.goods_class&&0==e.orderData.virtual_goods.is_veirfy?e.$util.img(e.orderData.virtualgoods_barcode):null,f=e.orderData.virtual_goods&&2==e.orderData.goods_class&&0==e.orderData.virtual_goods.is_veirfy?e.$util.img(e.orderData.virtualgoods):null,p=e.orderData.virtual_goods&&2==e.orderData.goods_class&&0==e.orderData.virtual_goods.is_veirfy&&e.orderData.virtual_goods.expire_time>0?e.$util.timeStampTurnTime(e.orderData.virtual_goods.expire_time):null,v=e.orderData.virtual_goods&&2==e.orderData.goods_class&&0==e.orderData.virtual_goods.is_veirfy?e.orderData.virtual_goods.verify_record.length:null,D=e.orderData.virtual_goods&&2==e.orderData.goods_class&&0==e.orderData.virtual_goods.is_veirfy&&v?e.__map(e.orderData.virtual_goods.verify_record,(function(r,t){var o=e.__get_orig(r),a=e.$util.timeStampTurnTime(r.verify_time);return{$orig:o,g20:a}})):null,h=e.orderData.virtual_goods&&4==e.orderData.goods_class&&0==e.orderData.virtual_goods.is_veirfy?e.$util.img(e.orderData.virtualgoods_barcode):null,y=e.orderData.virtual_goods&&4==e.orderData.goods_class&&0==e.orderData.virtual_goods.is_veirfy?e.$util.img(e.orderData.virtualgoods):null,$=e.orderData.virtual_goods&&4==e.orderData.goods_class&&e.orderData.virtual_goods.expire_time>0?e.$util.timeStampTurnTime(e.orderData.virtual_goods.expire_time):null,b=e.orderData.virtual_goods&&4==e.orderData.goods_class?e.orderData.virtual_goods.verify_record.length:null,T=e.orderData.virtual_goods&&4==e.orderData.goods_class&&b?e.__map(e.orderData.virtual_goods.verify_record,(function(r,t){var o=e.__get_orig(r),a=e.$util.timeStampTurnTime(r.verify_time);return{$orig:o,g25:a}})):null,k=e.$lang("common.currencySymbol"),P=4!=e.orderData.order_type?e.$lang("common.currencySymbol"):null,S=e.orderData.member_card_money>0?e.$lang("common.currencySymbol"):null,w=e.orderData.invoice_money>0?e.$lang("common.currencySymbol"):null,C=e.orderData.invoice_delivery_money>0?e.$lang("common.currencySymbol"):null,I=0!=e.orderData.adjust_money?e.$lang("common.currencySymbol"):null,O=0!=e.orderData.adjust_money?e._f("abs")(e.orderData.adjust_money):null,x=e.orderData.promotion_money>0?e.$lang("common.currencySymbol"):null,F=e.orderData.coupon_money>0?e.$lang("common.currencySymbol"):null,M=e.orderData.balance_money>0?e.$lang("common.currencySymbol"):null,N=e.orderData.point_money>0?e.$lang("common.currencySymbol"):null,R=e.$lang("common.currencySymbol"),j=e.orderData.action.length,E=j>0?null:0==e.orderData.action.length&&1==e.orderData.is_evaluate&&1==e.evaluateConfig.evaluate_status,J=e.orderData.action.length>0||1==e.orderData.is_evaluate&&1==e.evaluateConfig.evaluate_status;e._isMounted||(e.e0=function(r){return e.$util.redirectTo("/pages_tool/store/detail",{store_id:e.orderData.delivery_store_id})},e.e1=function(r){return e.$util.copy(e.orderData.delivery_code)},e.e2=function(r){e.previewMedia(e.$util.img(e.orderData.pickup_barcode))},e.e3=function(r){e.previewMedia(e.$util.img(e.orderData.pickup))},e.e4=function(r,t){var o=arguments[arguments.length-1].currentTarget.dataset,a=o.eventParams||o["event-params"];t=a.img;e.previewMedia(e.$util.img(t))},e.e5=function(r,t){var o=arguments[arguments.length-1].currentTarget.dataset,a=o.eventParams||o["event-params"];t=a.item;return e.$util.copy(t.val)},e.e6=function(r){return e.$util.copy(e.orderData.order_no)},e.e7=function(r,t){var o=arguments[arguments.length-1].currentTarget.dataset,a=o.eventParams||o["event-params"];t=a.img;e.previewMedia(e.$util.img(t))},e.e8=function(r,t){var o=arguments[arguments.length-1].currentTarget.dataset,a=o.eventParams||o["event-params"];t=a.item;return e.$util.copy(t.val)},e.e9=function(r){return e.$util.redirectTo("/pages_promotion/cardservice/card/my_card")},e.e10=function(r){return e.$util.copy(e.orderData.virtual_code)},e.e11=function(r,t){var o=arguments[arguments.length-1].currentTarget.dataset,a=o.eventParams||o["event-params"];t=a.item;return e.$util.copy(t.card_info.cardno)},e.e12=function(r,t){var o=arguments[arguments.length-1].currentTarget.dataset,a=o.eventParams||o["event-params"];t=a.item;return e.$util.copy(t.card_info.password)},e.e13=function(r){return e.$util.copy(e.orderData.virtual_code)}),e.$mp.data=Object.assign({},{$root:{g0:t,g1:o,g2:a,g3:n,g4:i,m0:d,l3:u,g12:s,g13:l,g14:c,l5:_,a0:m,g16:g,g17:f,g18:p,g19:v,l6:D,g21:h,g22:y,g23:$,g24:b,l7:T,m2:k,m3:P,m4:S,m5:w,m6:C,m7:I,f0:O,m8:x,m9:F,m10:M,m11:N,m12:R,g26:j,g27:E,g28:J}})},n=[]},e16f:function(e,r,t){},ee0a:function(e,r,t){"use strict";t.r(r);var o=t("d4ca"),a=t("cb69");for(var n in a)["default"].indexOf(n)<0&&function(e){t.d(r,e,(function(){return a[e]}))}(n);t("abc5"),t("016b");var i=t("828b"),d=Object(i["a"])(a["default"],o["b"],o["c"],!1,null,"7074e248",null,!1,o["a"],void 0);r["default"]=d.exports}},[["7496","common/runtime","common/vendor"]]]);