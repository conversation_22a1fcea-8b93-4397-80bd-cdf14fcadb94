(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/order/list"],{2289:function(e,t,r){"use strict";var a=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(r("2817")),s={data:function(){return{scrollInto:"",orderStatus:"all",statusList:[],orderList:[],contentText:{},mergePayOrder:[],isIphoneX:!1,evaluateConfig:{evaluate_audit:1,evaluate_show:0,evaluate_status:1},orderData:{},payMoney:0,payMoneyMerge:0,order_id:0,searchText:"",pageText:"",payConfig:null,isTradeManaged:!1}},components:{payment:function(){r.e("components/payment/payment").then(function(){return resolve(r("b6f2"))}.bind(null,r)).catch(r.oe)}},mixins:[i.default],onLoad:function(e){e.status&&(this.orderStatus=e.status),e.order_id&&(this.order_id=e.order_id)},onShow:function(){var e=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getEvaluateConfig(),this.getOrderStatus(),this.storeToken?this.$refs.mescroll&&this.$refs.mescroll.refresh():this.$nextTick((function(){e.$refs.loadingCover&&e.$refs.loadingCover.hide()})),this.$refs.choosePaymentPopup&&this.$refs.choosePaymentPopup.pageShow()},onUnload:function(){!this.storeToken&&this.$refs.login&&this.$refs.login.cancelCompleteInfo()},methods:{toLogin:function(){this.$refs.login.open()},ontabtap:function(e){var t=e.target.dataset.current||e.currentTarget.dataset.current;this.orderStatus=this.statusList[t].status,""==this.orderStatus&&(this.mergePayOrder=[]),this.$refs.loadingCover.show(),this.$refs.mescroll.refresh()},getListData:function(e){var t=this;this.$api.sendRequest({url:"/api/order/lists",data:{page:e.num,page_size:e.size,order_status:this.orderStatus,order_id:this.order_id,searchText:this.pageText},success:function(r){var a=[],i=r.message,s=0;0==r.code&&r.data?(a=r.data.list,s=r.data.auto_close,t.payConfig=r.data.pay_config,t.isTradeManaged=r.data.is_trade_managed):t.$util.showToast({title:i}),e.endSuccess(a.length),1==e.num&&(t.orderList=[],t.order_id=0),t.orderList=t.orderList.concat(a);var o=Date.parse(new Date)/1e3;t.orderList.forEach((function(e){e.discountTimeMachine=t.$util.countDown(e.create_time+s-o),e.order_goods.forEach((function(e){if(e.sku_spec_format)try{e.sku_spec_format=JSON.parse(e.sku_spec_format)}catch(t){e.sku_spec_format=e.sku_spec_format}else e.sku_spec_format=[]}))})),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(r){e.endErr(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},getOrderStatus:function(){this.statusList=[{status:"all",name:this.$lang("all"),id:"status_0"},{status:"waitpay",name:this.$lang("waitPay"),id:"status_1"},{status:"waitsend",name:this.$lang("readyDelivery"),id:"status_2"},{status:"waitconfirm",name:this.$lang("waitDelivery"),id:"status_3"},{status:"wait_use",name:this.$lang("waitUse"),id:"status_4"}]},operation:function(e,t){var r=this;this.status;switch(e){case"orderDelete":this.orderDelete(t.order_id,(function(){r.$refs.mescroll.refresh()}));break;case"orderPay":this.orderData=t,this.payMoney=parseFloat(t.pay_money),this.orderPay(t);break;case"orderClose":this.orderClose(t.order_id,(function(){r.$refs.mescroll.refresh()}));break;case"memberTakeDelivery":this.orderData=t,this.orderData.pay_config={},this.orderData.pay_config.mch_id=this.payConfig.mch_id,this.orderData.is_trade_managed=this.isTradeManaged,this.orderDelivery(this.orderData,(function(){r.$refs.mescroll.refresh()}));break;case"trace":this.$util.redirectTo("/pages_tool/order/logistics",{order_id:t.order_id});break;case"memberOrderEvaluation":this.$util.redirectTo("/pages_tool/order/evaluate",{order_id:t.order_id});break;case"memberVirtualTakeDelivery":this.orderData=t,this.orderData.pay_config={},this.orderData.pay_config.mch_id=this.payConfig.mch_id,this.orderData.is_trade_managed=this.isTradeManaged,this.orderVirtualDelivery(this.orderData,(function(){r.$refs.mescroll.refresh()}));break;case"orderOfflinePay":this.orderData=t,this.$util.redirectTo("/pages_tool/pay/offlinepay",{outTradeNo:this.orderData.out_trade_no});break}},orderDetail:function(e){this.$util.redirectTo("/pages/order/detail",{order_id:e.order_id})},selectOrder:function(e,t){-1!=this.$util.inArray(e,this.mergePayOrder)?(this.mergePayOrder.splice(this.$util.inArray(e,this.mergePayOrder),1),this.payMoneyMerge-=parseFloat(t)):(this.payMoneyMerge+=parseFloat(t),this.mergePayOrder.push(e))},imageError:function(e,t){this.orderList[e].order_goods[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},getEvaluateConfig:function(){var e=this;this.$api.sendRequest({url:"/api/goodsevaluate/config",success:function(t){if(0==t.code){var r=t.data;e.evaluateConfig=r}}})},search:function(){this.pageText=this.searchText,this.$refs.mescroll.refresh()}},computed:{mpOrderList:function(){if(this.orderList[this.status])return this.orderList[this.status].list||[]}},watch:{storeToken:function(e,t){e&&this.$refs.mescroll.refresh()}}};t.default=s},"48b1":function(e,t,r){"use strict";(function(e,t){var a=r("47a9");r("d381");a(r("3240"));var i=a(r("bcaa"));e.__webpack_require_UNI_MP_PLUGIN__=r,t(i.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},"4a7e":function(e,t,r){},"75fb":function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"c",(function(){return s})),r.d(t,"a",(function(){return a}));var a={uniCountDown:function(){return r.e("components/uni-count-down/uni-count-down").then(r.bind(null,"e12a"))},nsEmpty:function(){return r.e("components/ns-empty/ns-empty").then(r.bind(null,"52a6"))},payment:function(){return r.e("components/payment/payment").then(r.bind(null,"b6f2"))},nsLogin:function(){return Promise.all([r.e("common/vendor"),r.e("components/ns-login/ns-login")]).then(r.bind(null,"2910"))},loadingCover:function(){return r.e("components/loading-cover/loading-cover").then(r.bind(null,"c003"))}},i=function(){var e=this,t=e.$createElement,r=(e._self._c,e.storeToken?e.orderList.length:null),a=e.storeToken&&r>0?e.__map(e.orderList,(function(t,r){var a=e.__get_orig(t),i=t.order_goods.length,s=1==i?e.__map(t.order_goods,(function(t,r){var a=e.__get_orig(t),i=e.$util.img(t.sku_image,{size:"mid"}),s=t.sku_spec_format?e.__map(t.sku_spec_format,(function(r,a){var i=e.__get_orig(r),s=t.sku_spec_format.length;return{$orig:i,g3:s}})):null;return{$orig:a,g2:i,l0:s}})):null,o=1!=i?e.__map(t.order_goods,(function(t,r){var a=e.__get_orig(t),i=e.$util.img(t.sku_image,{size:"mid"});return{$orig:a,g4:i}})):null,n=1!=i?e.$util.img("public/uniapp/order/order-shade.png"):null,u=e.$lang("common.currencySymbol"),d=t.action.length,l=d>0&&0==t.order_status&&"offlinepay"!==t.pay_type?e.$util.img("public/uniapp/order/time.png"):null,c=d>0?null:0==t.action.length&&1==t.is_evaluate&&1==e.evaluateConfig.evaluate_status;return{$orig:a,g1:i,l1:s,l2:o,g5:n,m0:u,g6:d,g7:l,g8:c}})):null,i=!e.storeToken||r>0?null:e.$lang("emptyTips"),s=e.storeToken?null:e.$lang("emptyTips");e.$mp.data=Object.assign({},{$root:{g0:r,l3:a,m1:i,m2:s}})},s=[]},8710:function(e,t,r){"use strict";r.r(t);var a=r("2289"),i=r.n(a);for(var s in a)["default"].indexOf(s)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(s);t["default"]=i.a},ba6e:function(e,t,r){},bcaa:function(e,t,r){"use strict";r.r(t);var a=r("75fb"),i=r("8710");for(var s in i)["default"].indexOf(s)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(s);r("edcd"),r("d962");var o=r("828b"),n=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"b95cefce",null,!1,a["a"],void 0);t["default"]=n.exports},d962:function(e,t,r){"use strict";var a=r("4a7e"),i=r.n(a);i.a},edcd:function(e,t,r){"use strict";var a=r("ba6e"),i=r.n(a);i.a}},[["48b1","common/runtime","common/vendor"]]]);