<page-meta page-style="{{themeColor}}" class="data-v-b95cefce"></page-meta><view class="order-container data-v-b95cefce"><block wx:if="{{storeToken}}"><view class="cate-search data-v-b95cefce"><view class="search-box data-v-b95cefce"><input class="uni-input data-v-b95cefce" maxlength="50" confirm-type="search" placeholder="请输入商品名称/订单编号" data-event-opts="{{[['confirm',[['search']]],['input',[['__set_model',['','searchText','$event',[]]]]]]}}" value="{{searchText}}" bindconfirm="__e" bindinput="__e"/><text data-event-opts="{{[['tap',[['search']]]]}}" class="iconfont icon-sousuo3 data-v-b95cefce" bindtap="__e"></text></view></view></block><block wx:if="{{storeToken}}"><view class="order-nav data-v-b95cefce"><block wx:for="{{statusList}}" wx:for-item="statusItem" wx:for-index="statusIndex" wx:key="statusIndex"><view class="uni-tab-item data-v-b95cefce" id="{{statusItem.id}}" data-current="{{statusIndex}}" data-event-opts="{{[['tap',[['ontabtap',['$event']]]]]}}" bindtap="__e"><text class="{{['uni-tab-item-title','data-v-b95cefce',statusItem.status==orderStatus?'uni-tab-item-title-active color-base-text':'']}}">{{''+statusItem.name+''}}</text></view></block></view></block><block wx:if="{{storeToken}}"><mescroll-uni vue-id="439118e4-1" top="176rpx" data-ref="mescroll" data-event-opts="{{[['^getData',[['getListData']]]]}}" bind:getData="__e" class="data-v-b95cefce vue-ref" bind:__l="__l" vue-slots="{{['list']}}"><view slot="list" class="data-v-b95cefce"><block wx:if="{{$root.g0>0}}"><view class="order-list data-v-b95cefce"><block wx:for="{{$root.l3}}" wx:for-item="orderItem" wx:for-index="orderIndex" wx:key="orderIndex"><view class="order-item data-v-b95cefce"><view class="{{['order-header','data-v-b95cefce',(orderStatus=='waitpay'&&orderItem.$orig.order_status==0)?'waitpay':'']}}"><text class="order-no data-v-b95cefce">{{"订单号："+orderItem.$orig.order_no}}</text><text class="order-type-name data-v-b95cefce">{{orderItem.$orig.order_type_name}}</text><text class="status-name data-v-b95cefce">{{orderItem.$orig.order_status_name}}</text></view><view data-event-opts="{{[['tap',[['orderDetail',['$0'],[[['orderList','',orderIndex]]]]]]]}}" class="order-body data-v-b95cefce" bindtap="__e"><block wx:if="{{orderItem.g1==1}}"><block class="data-v-b95cefce"><block wx:for="{{orderItem.l1}}" wx:for-item="goodsItem" wx:for-index="goodsIndex" wx:key="goodsIndex"><view class="goods-wrap data-v-b95cefce"><view class="goods-img data-v-b95cefce"><image src="{{goodsItem.g2}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError',[orderIndex,goodsIndex]]]]]}}" binderror="__e" class="data-v-b95cefce"></image></view><view class="goods-info data-v-b95cefce"><view class="pro-info data-v-b95cefce"><block wx:if="{{goodsItem.$orig.goods_class==2}}"><view class="goods-name data-v-b95cefce">{{goodsItem.$orig.goods_name}}</view></block><block wx:else><view class="goods-name data-v-b95cefce">{{goodsItem.$orig.sku_name}}</view></block><block wx:if="{{goodsItem.$orig.sku_spec_format}}"><view class="sku data-v-b95cefce"><view class="goods-spec data-v-b95cefce"><block wx:for="{{goodsItem.l0}}" wx:for-item="x" wx:for-index="i" wx:key="i"><block class="data-v-b95cefce">{{''+x.$orig.spec_value_name+"\n\t\t\t\t\t\t\t\t\t\t\t\t\t"+(i<x.g3-1?'; ':'')+''}}</block></block></view></view></block></view><view class="goods-action data-v-b95cefce"></view></view></view></block></block></block><block wx:else><block class="data-v-b95cefce"><view class="multi-order-goods data-v-b95cefce"><scroll-view class="scroll-view data-v-b95cefce" scroll-x="true"><view class="goods-wrap data-v-b95cefce"><block wx:for="{{orderItem.l2}}" wx:for-item="goodsItem" wx:for-index="goodsIndex" wx:key="goodsIndex"><view class="goods-img data-v-b95cefce"><image src="{{goodsItem.g4}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['imageError',[orderIndex,goodsIndex]]]]]}}" binderror="__e" class="data-v-b95cefce"></image></view></block></view></scroll-view><view class="shade data-v-b95cefce"><image src="{{orderItem.g5}}" class="data-v-b95cefce"></image></view></view></block></block></view><view class="order-footer data-v-b95cefce"><view class="order-base-info data-v-b95cefce"><view class="total data-v-b95cefce"><text class="font-size-sub data-v-b95cefce">{{"共"+orderItem.$orig.goods_num+"件商品"}}</text><text class="align-right font-size-base data-v-b95cefce">实付款：<text class="font-size-base price-font data-v-b95cefce">{{orderItem.m0+orderItem.$orig.order_money}}</text></text></view></view><block wx:if="{{orderItem.g6>0}}"><view class="order-action data-v-b95cefce"><block wx:if="{{orderItem.$orig.order_status==0&&orderItem.$orig.pay_type!=='offlinepay'}}"><view class="order-time data-v-b95cefce" id="action-date"><image src="{{orderItem.g7}}" class="data-v-b95cefce"></image>剩余时间：<uni-count-down vue-id="{{('439118e4-2-'+orderIndex)+','+('439118e4-1')}}" day="{{orderItem.$orig.discountTimeMachine.d}}" hour="{{orderItem.$orig.discountTimeMachine.h}}" minute="{{orderItem.$orig.discountTimeMachine.i}}" second="{{orderItem.$orig.discountTimeMachine.s}}" color="#FF4644" splitorColor="#FF4644" class="data-v-b95cefce" bind:__l="__l"></uni-count-down></view></block><block wx:if="{{evaluateConfig.evaluate_status==1&&orderItem.$orig.is_evaluate==1}}"><view data-event-opts="{{[['tap',[['operation',['memberOrderEvaluation','$0'],[[['orderList','',orderIndex]]]]]]]}}" class="order-box-btn data-v-b95cefce" bindtap="__e"><block wx:if="{{orderItem.$orig.evaluate_status==0}}"><text class="data-v-b95cefce">评价</text></block><block wx:else><block wx:if="{{orderItem.$orig.evaluate_status==1}}"><text class="data-v-b95cefce">追评</text></block></block></view></block><block wx:for="{{orderItem.$orig.action}}" wx:for-item="operationItem" wx:for-index="operationIndex" wx:key="operationIndex"><view data-event-opts="{{[['tap',[['operation',['$0','$1'],[[['orderList','',orderIndex],['action','',operationIndex,'action']],[['orderList','',orderIndex]]]]]]]}}" class="{{['order-box-btn','data-v-b95cefce',(operationItem.action=='orderPay')?'color-base-border color-base-bg':'']}}" bindtap="__e">{{''+operationItem.title+''}}</view></block></view></block><block wx:else><block wx:if="{{orderItem.g8}}"><view class="order-action data-v-b95cefce"><block wx:if="{{orderItem.$orig.is_evaluate==1}}"><view data-event-opts="{{[['tap',[['operation',['memberOrderEvaluation','$0'],[[['orderList','',orderIndex]]]]]]]}}" class="order-box-btn data-v-b95cefce" bindtap="__e"><block wx:if="{{orderItem.$orig.evaluate_status==0}}"><text class="data-v-b95cefce">评价</text></block><block wx:else><block wx:if="{{orderItem.$orig.evaluate_status==1}}"><text class="data-v-b95cefce">追评</text></block></block></view></block></view></block><block wx:else><view class="order-action data-v-b95cefce"><view data-event-opts="{{[['tap',[['orderDetail',['$0'],[[['orderList','',orderIndex]]]]]]]}}" class="order-box-btn data-v-b95cefce" bindtap="__e">查看详情</view></view></block></block></view></view></block></view></block><block wx:else><view class="data-v-b95cefce"><ns-empty vue-id="{{('439118e4-3')+','+('439118e4-1')}}" isIndex="{{false}}" text="{{$root.m1}}" class="data-v-b95cefce" bind:__l="__l"></ns-empty></view></block></view></mescroll-uni></block><block wx:if="{{!storeToken}}"><view class="no-login data-v-b95cefce"><view class="data-v-b95cefce"><ns-empty vue-id="439118e4-4" isIndex="{{false}}" text="{{$root.m2}}" class="data-v-b95cefce" bind:__l="__l"></ns-empty></view><button class="button mini data-v-b95cefce" type="primary" size="mini" data-event-opts="{{[['tap',[['toLogin',['$event']]]]]}}" bindtap="__e">去登录</button></view></block><payment vue-id="439118e4-5" data-ref="choosePaymentPopup" class="data-v-b95cefce vue-ref" bind:__l="__l"></payment><ns-login vue-id="439118e4-6" data-ref="login" class="data-v-b95cefce vue-ref" bind:__l="__l"></ns-login><loading-cover vue-id="439118e4-7" data-ref="loadingCover" class="data-v-b95cefce vue-ref" bind:__l="__l"></loading-cover></view>