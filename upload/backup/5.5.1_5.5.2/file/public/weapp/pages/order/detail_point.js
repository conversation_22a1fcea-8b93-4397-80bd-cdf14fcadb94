(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/order/detail_point"],{3042:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o}));var o={nsContact:function(){return n.e("components/ns-contact/ns-contact").then(n.bind(null,"5036"))},nsGoodsRecommend:function(){return n.e("components/ns-goods-recommend/ns-goods-recommend").then(n.bind(null,"7254"))},nsPayment:function(){return n.e("components/ns-payment/ns-payment").then(n.bind(null,"7aec"))},loadingCover:function(){return n.e("components/loading-cover/loading-cover").then(n.bind(null,"c003"))}},r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.$util.img("public/uniapp/order/status-wrap-bg.png")),o=0==e.orderData.order_status?e.$util.img("public/uniapp/order/order-icon.png"):null,r=1==e.orderData.order_status?e.$util.img("public/uniapp/order/order-icon-received.png"):null,i=-1==e.orderData.order_status?e.$util.img("public/uniapp/order/order-icon-close.png"):null,a=e.exchangeImage(e.orderData),c=e.orderData.price>0?e.$lang("common.currencySymbol"):null,d=e.$util.timeStampTurnTime(e.orderData.create_time),u=e.orderData.close_time>0?e.$util.timeStampTurnTime(e.orderData.close_time):null,s={order_id:e.orderData.order_id},l=e.orderData.delivery_price>0?e.$lang("common.currencySymbol"):null,p=e.orderData.order_money>0?e.$lang("common.currencySymbol"):null;e._isMounted||(e.e0=function(t){return e.$util.copy(e.orderData.order_no)}),e.$mp.data=Object.assign({},{$root:{g0:n,g1:o,g2:r,g3:i,m0:a,m1:c,g4:d,g5:u,a0:s,m2:l,m3:p}})},i=[]},4845:function(e,t,n){"use strict";var o=n("e703"),r=n.n(o);r.a},6491:function(e,t,n){},c9dd:function(e,t,n){"use strict";n.r(t);var o=n("e58f"),r=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=r.a},d81f:function(e,t,n){"use strict";n.r(t);var o=n("3042"),r=n("c9dd");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("4845"),n("e609");var a=n("828b"),c=Object(a["a"])(r["default"],o["b"],o["c"],!1,null,"5059fc2c",null,!1,o["a"],void 0);t["default"]=c.exports},e58f:function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={components:{nsGoodsRecommend:function(){n.e("components/ns-goods-recommend/ns-goods-recommend").then(function(){return resolve(n("7254"))}.bind(null,n)).catch(n.oe)},nsContact:function(){n.e("components/ns-contact/ns-contact").then(function(){return resolve(n("5036"))}.bind(null,n)).catch(n.oe)}},data:function(){return{isIphoneX:!1,orderId:0,orderData:{action:[]},action:{icon:""},storeDetail:{}}},onLoad:function(e){e.order_id&&(this.orderId=e.order_id)},onShow:function(){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?this.getOrderData():this.$util.redirectTo("/pages_tool/login/index",{back:"/pages/order/detail_point?order_id="+this.orderId})},methods:{goRefund:function(e){this.$util.redirectTo("/pages_tool/order/refund",{order_goods_id:e})},goRefundDetail:function(e){this.$util.redirectTo("/pages_tool/order/refund_detail",{order_goods_id:e})},goDetail:function(e){this.$util.redirectTo("/pages_promotion/point/detail",{id:e})},navigateBack:function(){this.$util.goBack()},getOrderData:function(){var t=this;this.$api.sendRequest({url:"/pointexchange/api/order/info",data:{order_id:this.orderId},success:function(n){e.stopPullDownRefresh(),n.code>=0?(t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.orderData=n.data,""!=t.orderData.delivery_store_info&&(t.orderData.delivery_store_info=JSON.parse(t.orderData.delivery_store_info))):(t.$util.showToast({title:"未获取到订单信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/order/list")}),1500))},fail:function(n){e.stopPullDownRefresh(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},onPullDownRefresh:function(){this.getOrderData()},orderClose:function(){var t=this;e.showModal({title:"提示",content:"确定关闭此次兑换？",success:function(e){e.confirm&&t.$api.sendRequest({url:"/pointexchange/api/order/close",data:{order_id:t.orderData.order_id},success:function(e){e.code>=0&&(t.$util.showToast({title:"关闭成功"}),t.getOrderData())}})}})},openChoosePayment:function(){this.$refs.choosePaymentPopup.open()},orderPay:function(){e.setStorageSync("paySource","pointexchange"),this.$refs.choosePaymentPopup.getPayInfo(this.orderData.out_trade_no)},exchangeImage:function(e){var t="";switch(e.type){case 1:t=this.$util.img(e.exchange_image,{size:"mid"});break;case 2:t=e.exchange_image?this.$util.img(e.exchange_image):this.$util.img("public/uniapp/point/coupon.png");break;case 3:t=e.exchange_image?this.$util.img(e.exchange_image):this.$util.img("public/uniapp/point/hongbao.png");break}return t},imageError:function(){switch(this.orderData.type){case 2:this.orderData.exchange_image=this.$util.img("public/uniapp/point/coupon.png");break;case 3:this.orderData.exchange_image=this.$util.img("public/uniapp/point/hongbao.png");break;default:this.orderData.exchange_image=this.$util.getDefaultImage().goods}this.$forceUpdate()}},filters:{abs:function(e){return Math.abs(parseFloat(e)).toFixed(2)}}};t.default=o}).call(this,n("df3c")["default"])},e609:function(e,t,n){"use strict";var o=n("6491"),r=n.n(o);r.a},e703:function(e,t,n){},ec3d:function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("d381");o(n("3240"));var r=o(n("d81f"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["ec3d","common/runtime","common/vendor"]]]);