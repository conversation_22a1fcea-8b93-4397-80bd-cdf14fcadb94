(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/vendor"],{"0088":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"提现记录"}},"011a":function(e,t){function o(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],(function(){})))}catch(t){}return(e.exports=o=function(){return!!t},e.exports.__esModule=!0,e.exports["default"]=e.exports)()}e.exports=o,e.exports.__esModule=!0,e.exports["default"]=e.exports},"013f":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"预售专区"}},"0354":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的拼单"}},"05c7":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"瓜分红包列表"}},"0713":function(e,t,o){"use strict";(function(e,o){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{authInfo:{}}},methods:{getCode:function(t){var i=this;e.login({provider:"weixin",timeout:3e3,success:function(e){e.code&&i.$api.sendRequest({url:"/weapp/api/weapp/authcodetoopenid",data:{code:e.code},success:function(e){e.code>=0?(e.data.openid&&(i.authInfo.weapp_openid=e.data.openid),e.data.unionid&&(i.authInfo.wx_unionid=e.data.unionid),"function"==typeof t&&t(i.authInfo)):i.$util.showToast({title:e.message?e.message:"小程序配置错误"})}})},fail:function(e){var t=o.getLaunchOptionsSync().scene;-1==[1154,1155].indexOf(t)&&i.$util.showToast({title:e.errMsg})}})}}};t.default=i}).call(this,o("df3c")["default"],o("3223")["default"])},"0744":function(e,t,o){var i={"./en-us/common.js":"2516","./zh-cn/article/detail.js":"1148","./zh-cn/article/list.js":"1d8b","./zh-cn/bale/detail.js":"f40e","./zh-cn/bale/payment.js":"cae7","./zh-cn/bargain/detail.js":"34d0","./zh-cn/bargain/launch.js":"4e00","./zh-cn/bargain/list.js":"6854","./zh-cn/bargain/my_bargain.js":"235f","./zh-cn/bargain/payment.js":"f09c","./zh-cn/combo/detail.js":"50fa","./zh-cn/combo/payment.js":"2bbd","./zh-cn/common.js":"4397","./zh-cn/diy/diy.js":"f1ea","./zh-cn/fenxiao/apply.js":"f1a7","./zh-cn/fenxiao/bill.js":"dae7","./zh-cn/fenxiao/follow.js":"9ba6","./zh-cn/fenxiao/goods_list.js":"1fea","./zh-cn/fenxiao/index.js":"d0a6","./zh-cn/fenxiao/level.js":"acc9","./zh-cn/fenxiao/order.js":"c9af","./zh-cn/fenxiao/order_detail.js":"1a5a","./zh-cn/fenxiao/promote_code.js":"eff3","./zh-cn/fenxiao/team.js":"82fa","./zh-cn/fenxiao/withdraw_apply.js":"5c56","./zh-cn/fenxiao/withdraw_list.js":"fb42","./zh-cn/game/cards.js":"0c4a","./zh-cn/game/record.js":"3141","./zh-cn/game/smash_eggs.js":"cf62","./zh-cn/game/turntable.js":"1314","./zh-cn/goods/brand.js":"b99f","./zh-cn/goods/cart.js":"e320","./zh-cn/goods/category.js":"80b5","./zh-cn/goods/consult.js":"7d99","./zh-cn/goods/consult_edit.js":"f3a1","./zh-cn/goods/coupon.js":"15bd","./zh-cn/goods/coupon_receive.js":"18ca","./zh-cn/goods/detail.js":"8108","./zh-cn/goods/evaluate.js":"61a3","./zh-cn/goods/list.js":"f1d6","./zh-cn/goods/not_exist.js":"4c35","./zh-cn/goods/point.js":"9451","./zh-cn/goods/search.js":"7aa2","./zh-cn/groupbuy/detail.js":"7b5d","./zh-cn/groupbuy/list.js":"b3b8","./zh-cn/groupbuy/payment.js":"57c0","./zh-cn/help/detail.js":"5686","./zh-cn/help/list.js":"8671","./zh-cn/hongbao/list.js":"05c7","./zh-cn/hongbao/my_hongbao.js":"7198","./zh-cn/index/index.js":"b253","./zh-cn/live/list.js":"eee7","./zh-cn/login/find.js":"dbec","./zh-cn/login/login.js":"0fbb","./zh-cn/login/register.js":"8526","./zh-cn/member/account.js":"3500","./zh-cn/member/account_edit.js":"83a9","./zh-cn/member/address.js":"277e","./zh-cn/member/address_edit.js":"91ce","./zh-cn/member/apply_withdrawal.js":"cb2f","./zh-cn/member/assets.js":"cf80","./zh-cn/member/balance.js":"f686","./zh-cn/member/balance_detail.js":"347a","./zh-cn/member/cancellation.js":"cb85","./zh-cn/member/cancelrefuse.js":"34b1","./zh-cn/member/cancelstatus.js":"188c","./zh-cn/member/cancelsuccess.js":"e987","./zh-cn/member/card.js":"995e","./zh-cn/member/card_buy.js":"2c6d","./zh-cn/member/collection.js":"fa77","./zh-cn/member/contact.js":"e115","./zh-cn/member/coupon.js":"5f56","./zh-cn/member/footprint.js":"e81a","./zh-cn/member/gift.js":"37f2","./zh-cn/member/gift_detail.js":"aa6a","./zh-cn/member/index.js":"4893","./zh-cn/member/info.js":"c712","./zh-cn/member/info_edit.js":"5f8f","./zh-cn/member/invite_friends.js":"43bb","./zh-cn/member/level.js":"5ebd","./zh-cn/member/message.js":"5fce","./zh-cn/member/modify_face.js":"7e65","./zh-cn/member/pay_password.js":"73f9","./zh-cn/member/point.js":"7f6e","./zh-cn/member/point_detail.js":"0f94","./zh-cn/member/signin.js":"b34a","./zh-cn/member/withdrawal.js":"0088","./zh-cn/member/withdrawal_detail.js":"4a7b","./zh-cn/notice/detail.js":"dfe0","./zh-cn/notice/list.js":"c5bf9","./zh-cn/order/activist.js":"419e","./zh-cn/order/detail.js":"a624","./zh-cn/order/detail_local_delivery.js":"0d70","./zh-cn/order/detail_pickup.js":"334c","./zh-cn/order/detail_point.js":"0992","./zh-cn/order/detail_virtual.js":"8a01","./zh-cn/order/evaluate.js":"36ff","./zh-cn/order/list.js":"435b","./zh-cn/order/logistics.js":"d987","./zh-cn/order/payment.js":"df6a","./zh-cn/order/refund.js":"762f","./zh-cn/order/refund_batch.js":"ef61","./zh-cn/order/refund_detail.js":"8d06","./zh-cn/pay/index.js":"abab","./zh-cn/pay/result.js":"126e","./zh-cn/pintuan/detail.js":"348d","./zh-cn/pintuan/list.js":"49d3","./zh-cn/pintuan/my_spell.js":"0354","./zh-cn/pintuan/payment.js":"7d9d","./zh-cn/pintuan/share.js":"3464","./zh-cn/point/detail.js":"c952","./zh-cn/point/goods_detail.js":"7281","./zh-cn/point/list.js":"e97b","./zh-cn/point/order_detail.js":"f6cc","./zh-cn/point/order_list.js":"0d03","./zh-cn/point/payment.js":"ffa6","./zh-cn/point/result.js":"2929","./zh-cn/presale/detail.js":"1cb1","./zh-cn/presale/list.js":"013f","./zh-cn/presale/order_detail.js":"5176","./zh-cn/presale/order_list.js":"be1b","./zh-cn/presale/payment.js":"10de","./zh-cn/recharge/detail.js":"9ae8","./zh-cn/recharge/list.js":"6315","./zh-cn/recharge/order_list.js":"7df5","./zh-cn/seckill/detail.js":"1aba","./zh-cn/seckill/list.js":"9002","./zh-cn/seckill/payment.js":"d1f2","./zh-cn/store_notes/note_detail.js":"b94c","./zh-cn/store_notes/note_list.js":"fc8d","./zh-cn/storeclose/storeclose.js":"e600","./zh-cn/topics/detail.js":"50f4","./zh-cn/topics/goods_detail.js":"f87c","./zh-cn/topics/list.js":"5c25","./zh-cn/topics/payment.js":"0f10","./zh-cn/verification/detail.js":"aee2","./zh-cn/verification/index.js":"23df","./zh-cn/verification/list.js":"75ec"};function n(e){var t=r(e);return o(t)}function r(e){if(!o.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}n.keys=function(){return Object.keys(i)},n.resolve=r,e.exports=n,n.id="0744"},"0817":function(e,t,o){"use strict";(function(e){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("af87")),r=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,a=/^<\/([-A-Za-z0-9_]+)[^>]*>/,s=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,c=h("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),l=h("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),u=h("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),d=h("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),f=h("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),p=h("script,style");function h(e){for(var t={},o=e.split(","),i=0;i<o.length;i++)t[o[i]]=!0;return t}var g=function(t){t=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(t),t=function(e){return e=e.replace(/<!--[\s\S]*-->/gi,""),e}(t),t=function(t){var o=e.getSystemInfoSync(),i=o.windowWidth;i-=20,i+="px";var r='<img style="width:100% !important;display:block;max-width: '.concat(i,' !important;"');return t=t.replace(/\\/g,"").replace(/<img/g,r),t=t.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(e,t){return r+' src="'+n.default.img(t)+'"/>'})),t}(t),t=function(e){return e=e.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(e,t){return e=e.replace(/[:](\s?)[\s\S]*/gi,(function(e,t){return e.replace(/"/g,"'")})),e})),e}(t);var o=[],i={node:"root",children:[]};return function(e,t){var o,i,n,h=[],g=e;h.last=function(){return this[this.length-1]};while(e){if(i=!0,h.last()&&p[h.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+h.last()+"[^>]*>"),(function(e,o){return o=o.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(o),""})),v("",h.last());else if(0==e.indexOf("\x3c!--")?(o=e.indexOf("--\x3e"),o>=0&&(t.comment&&t.comment(e.substring(4,o)),e=e.substring(o+3),i=!1)):0==e.indexOf("</")?(n=e.match(a),n&&(e=e.substring(n[0].length),n[0].replace(a,v),i=!1)):0==e.indexOf("<")&&(n=e.match(r),n&&(e=e.substring(n[0].length),n[0].replace(r,_),i=!1)),i){o=e.indexOf("<");var m=o<0?e:e.substring(0,o);e=o<0?"":e.substring(o),t.chars&&t.chars(m)}if(e==g)throw"Parse Error: "+e;g=e}function _(e,o,i,n){if(o=o.toLowerCase(),l[o])while(h.last()&&u[h.last()])v("",h.last());if(d[o]&&h.last()==o&&v("",o),n=c[o]||!!n,n||h.push(o),t.start){var r=[];i.replace(s,(function(e,t){var o=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:f[t]?t:"";r.push({name:t,value:o,escaped:o.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(o,r,n)}}function v(e,o){if(o){for(i=h.length-1;i>=0;i--)if(h[i]==o)break}else var i=0;if(i>=0){for(var n=h.length-1;n>=i;n--)t.end&&t.end(h[n]);h.length=i}}v()}(t,{start:function(e,t,n){var r={name:e};if(0!==t.length&&(r.attrs=function(e){return e.reduce((function(e,t){var o=t.value,i=t.name;return e[i]?e[i]=e[i]+" "+o:e[i]=o,e}),{})}(t)),n){var a=o[0]||i;a.children||(a.children=[]),a.children.push(r)}else o.unshift(r)},end:function(e){var t=o.shift();if(t.name!==e&&console.error("invalid state: mismatch end tag"),0===o.length)i.children.push(t);else{var n=o[0];n.children||(n.children=[]),n.children.push(t)}},chars:function(e){var t={type:"text",text:e};if(0===o.length)i.children.push(t);else{var n=o[0];n.children||(n.children=[]),n.children.push(t)}},comment:function(e){var t={node:"comment",text:e},i=o[0];i.children||(i.children=[]),i.children.push(t)}}),i.children};t.default=g}).call(this,o("df3c")["default"])},"0992":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"订单详情"}},"0bdb":function(e,t,o){var i=o("d551");function n(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}e.exports=function(e,t,o){return t&&n(e.prototype,t),o&&n(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"0c4a":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"刮刮乐"}},"0cd5":function(e,t,o){"use strict";(function(e){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=r;var n=i(o("3b2d"));function r(e,t){var o=this;o.version="1.2.3",o.options=e||{},o.isScrollBody=t||!1,o.isDownScrolling=!1,o.isUpScrolling=!1;var i=o.options.down&&o.options.down.callback;o.initDownScroll(),o.initUpScroll(),setTimeout((function(){o.optDown.use&&o.optDown.auto&&i&&(o.optDown.autoShowLoading?o.triggerDownScroll():o.optDown.callback&&o.optDown.callback(o)),setTimeout((function(){o.optUp.use&&o.optUp.auto&&!o.isUpAutoLoad&&o.triggerUpScroll()}),100)}),30)}r.prototype.extendDownScroll=function(e){r.extend(e,{use:!0,auto:!0,native:!1,autoShowLoading:!1,isLock:!1,offset:80,startTop:100,fps:80,inOffsetRate:1,outOffsetRate:.2,bottomOffset:20,minAngle:45,textInOffset:"下拉刷新",textOutOffset:"释放更新",textLoading:"加载中 ...",inited:null,inOffset:null,outOffset:null,onMoving:null,beforeLoading:null,showLoading:null,afterLoading:null,endDownScroll:null,callback:function(e){e.resetUpScroll()}})},r.prototype.extendUpScroll=function(e){r.extend(e,{use:!0,auto:!0,isLock:!1,isBoth:!0,isBounce:!1,callback:null,page:{num:0,size:10,time:null},noMoreSize:5,offset:80,textLoading:"加载中 ...",textNoMore:"-- END --",inited:null,showLoading:null,showNoMore:null,hideUpScroll:null,errDistance:60,toTop:{src:null,offset:1e3,duration:300,btnClick:null,onShow:null,zIndex:9990,left:null,right:20,bottom:120,safearea:!1,width:72,radius:"50%"},empty:{use:!0,icon:null,tip:"~ 暂无相关数据 ~",btnText:"",btnClick:null,onShow:null,fixed:!1,top:"100rpx",zIndex:99},onScroll:!1})},r.extend=function(e,t){if(!e)return t;for(var o in t)if(null==e[o]){var i=t[o];null!=i&&"object"===(0,n.default)(i)?e[o]=r.extend({},i):e[o]=i}else"object"===(0,n.default)(e[o])&&r.extend(e[o],t[o]);return e},r.prototype.initDownScroll=function(){var e=this;e.optDown=e.options.down||{},e.extendDownScroll(e.optDown),e.isScrollBody&&e.optDown.native?e.optDown.use=!1:e.optDown.native=!1,e.downHight=0,e.optDown.use&&e.optDown.inited&&setTimeout((function(){e.optDown.inited(e)}),0)},r.prototype.touchstartEvent=function(e){this.optDown.use&&(this.startPoint=this.getPoint(e),this.startTop=this.getScrollTop(),this.lastPoint=this.startPoint,this.maxTouchmoveY=this.getBodyHeight()-this.optDown.bottomOffset,this.inTouchend=!1)},r.prototype.touchmoveEvent=function(e){if(this.optDown.use&&this.startPoint){var t=this,o=(new Date).getTime();if(!(t.moveTime&&o-t.moveTime<t.moveTimeDiff)){t.moveTime=o,t.moveTimeDiff||(t.moveTimeDiff=1e3/t.optDown.fps);var i=t.getScrollTop(),n=t.getPoint(e),r=n.y-t.startPoint.y;if(r>0&&(t.isScrollBody&&i<=0||!t.isScrollBody&&(i<=0||i<=t.optDown.startTop&&i===t.startTop))&&!t.inTouchend&&!t.isDownScrolling&&!t.optDown.isLock&&(!t.isUpScrolling||t.isUpScrolling&&t.optUp.isBoth)){var a=t.getAngle(t.lastPoint,n);if(a<t.optDown.minAngle)return;if(t.maxTouchmoveY>0&&n.y>=t.maxTouchmoveY)return t.inTouchend=!0,void t.touchendEvent();t.preventDefault(e);var s=n.y-t.lastPoint.y;t.downHight<t.optDown.offset?(1!==t.movetype&&(t.movetype=1,t.optDown.inOffset&&t.optDown.inOffset(t),t.isMoveDown=!0),t.downHight+=s*t.optDown.inOffsetRate):(2!==t.movetype&&(t.movetype=2,t.optDown.outOffset&&t.optDown.outOffset(t),t.isMoveDown=!0),t.downHight+=s>0?Math.round(s*t.optDown.outOffsetRate):s);var c=t.downHight/t.optDown.offset;t.optDown.onMoving&&t.optDown.onMoving(t,c,t.downHight)}t.lastPoint=n}}},r.prototype.touchendEvent=function(e){if(this.optDown.use)if(this.isMoveDown)this.downHight>=this.optDown.offset?this.triggerDownScroll():(this.downHight=0,this.optDown.endDownScroll&&this.optDown.endDownScroll(this)),this.movetype=0,this.isMoveDown=!1;else if(!this.isScrollBody&&this.getScrollTop()===this.startTop){var t=this.getPoint(e).y-this.startPoint.y<0;if(t){var o=this.getAngle(this.getPoint(e),this.startPoint);o>80&&this.triggerUpScroll(!0)}}},r.prototype.getPoint=function(e){return e?e.touches&&e.touches[0]?{x:e.touches[0].pageX,y:e.touches[0].pageY}:e.changedTouches&&e.changedTouches[0]?{x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY}:{x:e.clientX,y:e.clientY}:{x:0,y:0}},r.prototype.getAngle=function(e,t){var o=Math.abs(e.x-t.x),i=Math.abs(e.y-t.y),n=Math.sqrt(o*o+i*i),r=0;return 0!==n&&(r=Math.asin(i/n)/Math.PI*180),r},r.prototype.triggerDownScroll=function(){this.optDown.beforeLoading&&this.optDown.beforeLoading(this)||(this.showDownScroll(),this.optDown.callback&&this.optDown.callback(this))},r.prototype.showDownScroll=function(){this.isDownScrolling=!0,this.optDown.native?(e.startPullDownRefresh(),this.optDown.showLoading&&this.optDown.showLoading(this,0)):(this.downHight=this.optDown.offset,this.optDown.showLoading&&this.optDown.showLoading(this,this.downHight))},r.prototype.onPullDownRefresh=function(){this.isDownScrolling=!0,this.optDown.showLoading&&this.optDown.showLoading(this,0),this.optDown.callback&&this.optDown.callback(this)},r.prototype.endDownScroll=function(){if(this.optDown.native)return this.isDownScrolling=!1,this.optDown.endDownScroll&&this.optDown.endDownScroll(this),void e.stopPullDownRefresh();var t=this,o=function(){t.downHight=0,t.isDownScrolling=!1,t.optDown.endDownScroll&&t.optDown.endDownScroll(t),!t.isScrollBody&&t.setScrollHeight(0)},i=0;t.optDown.afterLoading&&(i=t.optDown.afterLoading(t)),"number"===typeof i&&i>0?setTimeout(o,i):o()},r.prototype.lockDownScroll=function(e){null==e&&(e=!0),this.optDown.isLock=e},r.prototype.lockUpScroll=function(e){null==e&&(e=!0),this.optUp.isLock=e},r.prototype.initUpScroll=function(){var e=this;e.optUp=e.options.up||{use:!1},e.extendUpScroll(e.optUp),e.optUp.isBounce||e.setBounce(!1),!1!==e.optUp.use&&(e.optUp.hasNext=!0,e.startNum=e.optUp.page.num+1,e.optUp.inited&&setTimeout((function(){e.optUp.inited(e)}),0))},r.prototype.onReachBottom=function(){this.isScrollBody&&!this.isUpScrolling&&!this.optUp.isLock&&this.optUp.hasNext&&this.triggerUpScroll()},r.prototype.onPageScroll=function(e){this.isScrollBody&&(this.setScrollTop(e.scrollTop),e.scrollTop>=this.optUp.toTop.offset?this.showTopBtn():this.hideTopBtn())},r.prototype.scroll=function(e,t){this.setScrollTop(e.scrollTop),this.setScrollHeight(e.scrollHeight),null==this.preScrollY&&(this.preScrollY=0),this.isScrollUp=e.scrollTop-this.preScrollY>0,this.preScrollY=e.scrollTop,this.isScrollUp&&this.triggerUpScroll(!0),e.scrollTop>=this.optUp.toTop.offset?this.showTopBtn():this.hideTopBtn(),this.optUp.onScroll&&t&&t()},r.prototype.triggerUpScroll=function(e){if(!this.isUpScrolling&&this.optUp.use&&this.optUp.callback){if(!0===e){var t=!1;if(!this.optUp.hasNext||this.optUp.isLock||this.isDownScrolling||this.getScrollBottom()<=this.optUp.offset&&(t=!0),!1===t)return}this.showUpScroll(),this.optUp.page.num++,this.isUpAutoLoad=!0,this.num=this.optUp.page.num,this.size=this.optUp.page.size,this.time=this.optUp.page.time,this.optUp.callback(this)}},r.prototype.showUpScroll=function(){this.isUpScrolling=!0,this.optUp.showLoading&&this.optUp.showLoading(this)},r.prototype.showNoMore=function(){this.optUp.hasNext=!1,this.optUp.showNoMore&&this.optUp.showNoMore(this)},r.prototype.hideUpScroll=function(){this.optUp.hideUpScroll&&this.optUp.hideUpScroll(this)},r.prototype.endUpScroll=function(e){null!=e&&(e?this.showNoMore():this.hideUpScroll()),this.isUpScrolling=!1},r.prototype.resetUpScroll=function(e){if(this.optUp&&this.optUp.use){var t=this.optUp.page;this.prePageNum=t.num,this.prePageTime=t.time,t.num=this.startNum,t.time=null,this.isDownScrolling||!1===e||(null==e?(this.removeEmpty(),this.showUpScroll()):this.showDownScroll()),this.isUpAutoLoad=!0,this.num=t.num,this.size=t.size,this.time=t.time,this.optUp.callback&&this.optUp.callback(this)}},r.prototype.setPageNum=function(e){this.optUp.page.num=e-1},r.prototype.setPageSize=function(e){this.optUp.page.size=e},r.prototype.endByPage=function(e,t,o){var i;this.optUp.use&&null!=t&&(i=this.optUp.page.num<t),this.endSuccess(e,i,o)},r.prototype.endBySize=function(e,t,o){var i;if(this.optUp.use&&null!=t){var n=(this.optUp.page.num-1)*this.optUp.page.size+e;i=n<t}this.endSuccess(e,i,o)},r.prototype.endSuccess=function(e,t,o){var i=this;if(i.isDownScrolling&&i.endDownScroll(),i.optUp.use){var n;if(null!=e){var r=i.optUp.page.num,a=i.optUp.page.size;if(1===r&&o&&(i.optUp.page.time=o),e<a||!1===t)if(i.optUp.hasNext=!1,0===e&&1===r)n=!1,i.showEmpty();else{var s=(r-1)*a+e;n=!(s<i.optUp.noMoreSize),i.removeEmpty()}else n=!1,i.optUp.hasNext=!0,i.removeEmpty()}i.endUpScroll(n)}},r.prototype.endErr=function(e){if(this.isDownScrolling){var t=this.optUp.page;t&&this.prePageNum&&(t.num=this.prePageNum,t.time=this.prePageTime),this.endDownScroll()}this.isUpScrolling&&(this.optUp.page.num--,this.endUpScroll(!1),this.isScrollBody&&0!==e&&(e||(e=this.optUp.errDistance),this.scrollTo(this.getScrollTop()-e,0)))},r.prototype.showEmpty=function(){this.optUp.empty.use&&this.optUp.empty.onShow&&this.optUp.empty.onShow(!0)},r.prototype.removeEmpty=function(){this.optUp.empty.use&&this.optUp.empty.onShow&&this.optUp.empty.onShow(!1)},r.prototype.showTopBtn=function(){this.topBtnShow||(this.topBtnShow=!0,this.optUp.toTop.onShow&&this.optUp.toTop.onShow(!0))},r.prototype.hideTopBtn=function(){this.topBtnShow&&(this.topBtnShow=!1,this.optUp.toTop.onShow&&this.optUp.toTop.onShow(!1))},r.prototype.getScrollTop=function(){return this.scrollTop||0},r.prototype.setScrollTop=function(e){this.scrollTop=e},r.prototype.scrollTo=function(e,t){this.myScrollTo&&this.myScrollTo(e,t)},r.prototype.resetScrollTo=function(e){this.myScrollTo=e},r.prototype.getScrollBottom=function(){return this.getScrollHeight()-this.getClientHeight()-this.getScrollTop()},r.prototype.getStep=function(e,t,o,i,n){var r=t-e;if(0!==i&&0!==r){i=i||300,n=n||30;var a=i/n,s=r/a,c=0,l=setInterval((function(){c<a-1?(e+=s,o&&o(e,l),c++):(o&&o(t,l),clearInterval(l))}),n)}else o&&o(t)},r.prototype.getClientHeight=function(e){var t=this.clientHeight||0;return 0===t&&!0!==e&&(t=this.getBodyHeight()),t},r.prototype.setClientHeight=function(e){this.clientHeight=e},r.prototype.getScrollHeight=function(){return this.scrollHeight||0},r.prototype.setScrollHeight=function(e){this.scrollHeight=e},r.prototype.getBodyHeight=function(){return this.bodyHeight||0},r.prototype.setBodyHeight=function(e){this.bodyHeight=e},r.prototype.preventDefault=function(e){e&&e.cancelable&&!e.defaultPrevented&&e.preventDefault()},r.prototype.setBounce=function(e){}}).call(this,o("df3c")["default"])},"0d03":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"积分兑换",emptyTips:"暂无更多数据了"}},"0d70":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"订单详情"}},"0ee4":function(e,t){var o;o=function(){return this}();try{o=o||new Function("return this")()}catch(i){"object"===typeof window&&(o=window)}e.exports=o},"0f10":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"待付款订单"}},"0f94":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"积分明细",emptyTpis:"您暂时还没有积分记录哦!",pointExplain:"积分说明"}},"0fbb":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"登录",mobileLogin:"手机号登录",accountLogin:"账号登录",autoLogin:"一键授权登录",login:"登录",mobilePlaceholder:"手机号登录仅限中国大陆用户",dynacodePlaceholder:"请输入动态码",captchaPlaceholder:"请输入验证码",accountPlaceholder:"请输入账号",passwordPlaceholder:"请输入密码",rePasswordPlaceholder:"请确认密码",forgetPassword:"忘记密码",register:"注册",registerTips:"没有账号的用户快来",registerTips1:"注册",registerTips2:"吧",newUserRegister:"新用户注册"}},"10de":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"待付款订单"}},1148:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"文章详情"}},"126e":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"支付结果",paymentSuccess:"支付成功",paymentFail:"支付失败",goHome:"回到首页",memberCenter:"会员中心",payMoney:"支付金额",unit:"元"}},1314:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"大转盘"}},"15bd":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"优惠券领取"}},1757:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={default:{name:"default",main_color:"#F4391c",aux_color:"#F7B500",bg_color:"#FF4646",bg_color_shallow:"#FF4646",promotion_color:"#FF4646",promotion_aux_color:"#F7B500",main_color_shallow:"#FFF4F4",price_color:"rgb(252,82,39)",btn_text_color:"#FFFFFF",goods_detail:{goods_price:"rgb(252,82,39,1)",promotion_tag:"#FF4646",goods_card_bg:"#201A18",goods_card_bg_shallow:"#7C7878",goods_card_color:"#FFD792",goods_coupon:"#FC5227",goods_cart_num_corner:"#FC5227",goods_btn_color:"#FF4646",goods_btn_color_shallow:"#F7B500"},pintuan:{pintuan_label_bg:"#F7B500",pintuan_label_color:"#FFFFFF",pintuan_color:"#FA6400",pintuan_promotion_color:"#FA3A1D",pintuan_promotion_aux_color:"#FD9A01"},super_member:{super_member_start_bg:"#7c7878",super_member_end_bg:"#201a18",super_member_start_text_color:"#FFDBA6",super_member_end_text_color:"#FFEBCA"},bargain:{bargain_promotion_color:"#F0353E",bargain_promotion_aux_color:"#FD9A01"},seckill:{seckill_promotion_color:"#F83530",seckill_promotion_aux_color:"#FD9A01"},giftcard:{giftcard_promotion_color:"#FF3369",giftcard_promotion_aux_color:"#F7B500"},groupby:{groupby_promotion_color:"#E64136",groupby_promotion_aux_color:"#F7B500"}},green:{name:"green",main_color:"#19C650",aux_color:"#FA6400",bg_color:"#19C650",bg_color_shallow:"#19C650",promotion_color:"#19C650",promotion_aux_color:"#FA6400",main_color_shallow:"#F0FFF5",price_color:"rgba(252,82,39,1)",btn_text_color:"#FFFFFF",goods_detail:{goods_price:"rgba(252,82,39,1)",promotion_tag:"#19C650",goods_card_bg:"#201A18",goods_card_bg_shallow:"#7C7878",goods_card_color:"#FFD792",goods_coupon:"#FC5227",goods_cart_num_corner:"#FC5227",goods_btn_color:"#19C650",goods_btn_color_shallow:"#FA6400"},pintuan:{pintuan_label_bg:"#F7B500",pintuan_label_color:"#FFFFFF",pintuan_color:"#FA6400",pintuan_promotion_color:"#FA3A1D",pintuan_promotion_aux_color:"#FD9A01"},super_member:{super_member_start_bg:"#7c7878",super_member_end_bg:"#201a18",super_member_start_text_color:"#FFDBA6",super_member_end_text_color:"#FFEBCA"},bargain:{bargain_promotion_color:"#F0353E",bargain_promotion_aux_color:"#FD9A01"},seckill:{seckill_promotion_color:"#F83530",seckill_promotion_aux_color:"#FD9A01"},giftcard:{giftcard_promotion_color:"#FF3369",giftcard_promotion_aux_color:"#F7B500"},groupby:{groupby_promotion_color:"#E64136",groupby_promotion_aux_color:"#F7B500"}},blue:{name:"blue",main_color:"#36ABFF",aux_color:"#FA6400",bg_color:"#36ABFF",bg_color_shallow:"#36ABFF",promotion_color:"#36ABFF ",promotion_aux_color:"#FA6400",main_color_shallow:"#E2F3FF",price_color:"rgba(252,82,39,1)",btn_text_color:"#FFFFFF",goods_detail:{goods_price:"rgba(252,82,39,1)",promotion_tag:"#36ABFF",goods_card_bg:"#201A18",goods_card_bg_shallow:"#7C7878",goods_card_color:"#FFD792",goods_coupon:"#FC5227",goods_cart_num_corner:"#FC5227",goods_btn_color:"#36ABFF",goods_btn_color_shallow:"#FA6400"},pintuan:{pintuan_label_bg:"#F7B500",pintuan_label_color:"#FFFFFF",pintuan_color:"#FA6400",pintuan_promotion_color:"#FA3A1D",pintuan_promotion_aux_color:"#FD9A01"},super_member:{super_member_start_bg:"#7c7878",super_member_end_bg:"#201a18",super_member_start_text_color:"#FFDBA6",super_member_end_text_color:"#FFEBCA"},bargain:{bargain_promotion_color:"#F0353E",bargain_promotion_aux_color:"#FD9A01"},seckill:{seckill_promotion_color:"#F83530",seckill_promotion_aux_color:"#FD9A01"},giftcard:{giftcard_promotion_color:"#FF3369",giftcard_promotion_aux_color:"#F7B500"},groupby:{groupby_promotion_color:"#E64136",groupby_promotion_aux_color:"#F7B500"}},pink:{name:"pink",main_color:"#FF407E",aux_color:"#F7B500",bg_color:"#FF407E",bg_color_shallow:"#FF407E",promotion_color:"#FF407E",promotion_aux_color:"#F7B500",main_color_shallow:"#FFF5F8",price_color:"rgba(252,82,39,1)",btn_text_color:"#FFFFFF",goods_detail:{goods_price:"rgba(252,82,39,1)",promotion_tag:"#FF407E",goods_card_bg:"#201A18",goods_card_bg_shallow:"#7C7878",goods_card_color:"#FFD792",goods_coupon:"#FC5227",goods_cart_num_corner:"#FC5227",goods_btn_color:"#FF407E",goods_btn_color_shallow:"#F7B500"},pintuan:{pintuan_label_bg:"#F7B500",pintuan_label_color:"#FFFFFF",pintuan_color:"#FA6400",pintuan_promotion_color:"#FA3A1D",pintuan_promotion_aux_color:"#FD9A01"},super_member:{super_member_start_bg:"#7c7878",super_member_end_bg:"#201a18",super_member_start_text_color:"#FFDBA6",super_member_end_text_color:"#FFEBCA"},bargain:{bargain_promotion_color:"#F0353E",bargain_promotion_aux_color:"#FD9A01"},seckill:{seckill_promotion_color:"#F83530",seckill_promotion_aux_color:"#FD9A01"},giftcard:{giftcard_promotion_color:"#FF3369",giftcard_promotion_aux_color:"#F7B500"},groupby:{groupby_promotion_color:"#E64136",groupby_promotion_aux_color:"#F7B500"}},gold:{name:"gold",main_color:"#CFAF70",aux_color:"#444444",bg_color:"#CFAF70",bg_color_shallow:"#CFAF70",promotion_color:"#CFAF70",promotion_aux_color:"#444444",main_color_shallow:"#FFFAF1",price_color:"rgba(252,82,39,1)",btn_text_color:"#FFFFFF",goods_detail:{goods_price:"rgba(252,82,39,1)",promotion_tag:"#CFAF70",goods_card_bg:"#201A18",goods_card_bg_shallow:"#7C7878",goods_card_color:"#FFD792",goods_coupon:"#FC5227",goods_cart_num_corner:"#FC5227",goods_btn_color:"#CFAF70",goods_btn_color_shallow:"#444444"},pintuan:{pintuan_label_bg:"#F7B500",pintuan_label_color:"#FFFFFF",pintuan_color:"#FA6400",pintuan_promotion_color:"#FA3A1D",pintuan_promotion_aux_color:"#FD9A01"},super_member:{super_member_start_bg:"#7c7878",super_member_end_bg:"#201a18",super_member_start_text_color:"#FFDBA6",super_member_end_text_color:"#FFEBCA"},bargain:{bargain_promotion_color:"#F0353E",bargain_promotion_aux_color:"#FD9A01"},seckill:{seckill_promotion_color:"#F83530",seckill_promotion_aux_color:"#FD9A01"},giftcard:{giftcard_promotion_color:"#FF3369",giftcard_promotion_aux_color:"#F7B500"},groupby:{groupby_promotion_color:"#E64136",groupby_promotion_aux_color:"#F7B500"}},purple:{name:"purple",main_color:"#A253FF",aux_color:"#222222",bg_color:"#A253FF",bg_color_shallow:"#A253FF",promotion_color:"#A253FF",promotion_aux_color:"#222222",main_color_shallow:"#F8F3FF",price_color:"rgba(252,82,39,1)",btn_text_color:"#FFFFFF",goods_detail:{goods_price:"rgba(252,82,39,1)",promotion_tag:"#A253FF",goods_card_bg:"#201A18",goods_card_bg_shallow:"#7C7878",goods_card_color:"#FFD792",goods_coupon:"#FC5227",goods_cart_num_corner:"#FC5227",goods_btn_color:"#A253FF",goods_btn_color_shallow:"#222222"},pintuan:{pintuan_label_bg:"#F7B500",pintuan_label_color:"#FFFFFF",pintuan_color:"#FA6400",pintuan_promotion_color:"#FA3A1D",pintuan_promotion_aux_color:"#FD9A01"},super_member:{super_member_start_bg:"#7c7878",super_member_end_bg:"#201a18",super_member_start_text_color:"#FFDBA6",super_member_end_text_color:"#FFEBCA"},bargain:{bargain_promotion_color:"#F0353E",bargain_promotion_aux_color:"#FD9A01"},seckill:{seckill_promotion_color:"#F83530",seckill_promotion_aux_color:"#FD9A01"},giftcard:{giftcard_promotion_color:"#FF3369",giftcard_promotion_aux_color:"#F7B500"},groupby:{groupby_promotion_color:"#E64136",groupby_promotion_aux_color:"#F7B500"}},yellow:{name:"yellow",main_color:"#FFD009",aux_color:"#1D262E",bg_color:"#FFD009",bg_color_shallow:"#FFD009",promotion_color:"#FFD009",promotion_aux_color:"#1D262E",main_color_shallow:"#FFFBEF",price_color:"rgba(252,82,39,1)",btn_text_color:"#303133",goods_detail:{goods_price:"rgba(252,82,39,1)",promotion_tag:"#FFD009",goods_card_bg:"#201A18",goods_card_bg_shallow:"#7C7878",goods_card_color:"#FFD792",goods_coupon:"#FC5227",goods_cart_num_corner:"#FC5227",goods_btn_color:"#FFD009",goods_btn_color_shallow:"#1D262E"},pintuan:{pintuan_label_bg:"#F7B500",pintuan_label_color:"#FFFFFF",pintuan_color:"#FA6400",pintuan_promotion_color:"#FA3A1D",pintuan_promotion_aux_color:"#FD9A01"},super_member:{super_member_start_bg:"#7c7878",super_member_end_bg:"#201a18",super_member_start_text_color:"#FFDBA6",super_member_end_text_color:"#FFEBCA"},bargain:{bargain_promotion_color:"#F0353E",bargain_promotion_aux_color:"#FD9A01"},seckill:{seckill_promotion_color:"#F83530",seckill_promotion_aux_color:"#FD9A01"},giftcard:{giftcard_promotion_color:"#FF3369",giftcard_promotion_aux_color:"#F7B500"},groupby:{groupby_promotion_color:"#E64136",groupby_promotion_aux_color:"#F7B500"}},black:{name:"black",main_color:"#222222",aux_color:"#FFFFFF",bg_color:"#222222",bg_color_shallow:"#333333",promotion_color:"#222222",promotion_aux_color:"#FA8B00",main_color_shallow:"#efefef",price_color:"rgba(255,0,0,1)",btn_text_color:"#FFFFFF",goods_detail:{goods_price:"rgba(255,0,0,1)",promotion_tag:"#222222",goods_card_bg:"#201A18",goods_card_bg_shallow:"#7C7878",goods_card_color:"#FFD792",goods_coupon:"#222222",goods_cart_num_corner:"#FF0000",goods_btn_color:"#222222",goods_btn_color_shallow:"#FA8B00"},pintuan:{pintuan_label_bg:"#F7B500",pintuan_label_color:"#FFFFFF",pintuan_color:"#FA6400",pintuan_promotion_color:"#FA3A1D",pintuan_promotion_aux_color:"#FD9A01"},super_member:{super_member_start_bg:"#fadcb5",super_member_end_bg:"#f6bd74",super_member_start_text_color:"#ab6126",super_member_end_text_color:"#d19336"},bargain:{bargain_promotion_color:"#F0353E",bargain_promotion_aux_color:"#FD9A01"},seckill:{seckill_promotion_color:"#F83530",seckill_promotion_aux_color:"#FD9A01"},giftcard:{giftcard_promotion_color:"#FF3369",giftcard_promotion_aux_color:"#F7B500"},groupby:{groupby_promotion_color:"#E64136",groupby_promotion_aux_color:"#F7B500"}}}},"188c":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"账号注销"}},"18ca":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"领取优惠券"}},"1a5a":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"订单详情"}},"1aba":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"",select:"选择",params:"参数",service:"商品服务",allGoods:"全部商品",image:"图片",video:"视频"}},"1cb1":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"",select:"选择",params:"参数",service:"商品服务",allGoods:"全部商品",image:"图片",video:"视频"}},"1d8b":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"文章列表",emptyText:"当前暂无文章信息"}},"1e68":function(e,t,o){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=["zh-cn","en-us"],n=e.getStorageSync("lang")||"zh-cn",r={langList:["zh-cn","en-us"],lang:function(e){var t=getCurrentPages()[getCurrentPages().length-1];if(t){var i,r="";try{var a=o("2043")("./"+n+"/common.js").lang,s=t.route.split("/");i=s.slice(1,s.length);var c=o("0744")("./"+n+"/"+i.join("/")+".js").lang;for(var l in c)a[l]=c[l];var u=e.split(".");if(u.length>1)for(var d in u){var f=parseInt(d)+1;f<u.length&&(r=a[u[d]][u[f]])}else r=a[e]}catch(h){r=-1!=e.indexOf("common.")||-1!=e.indexOf("tabBar.")?a[e]:e}if(arguments.length>1)for(var p=1;p<arguments.length;p++)r=r.replace("{"+(p-1)+"}",arguments[p]);return(void 0==r||"title"==r&&"title"==e)&&(r=""),r}},change:function(t){var o=getCurrentPages()[getCurrentPages().length-1];o&&(e.setStorageSync("lang",t),n=e.getStorageSync("lang")||"zh-cn",this.refresh(),e.reLaunch({url:"/pages/member/index"}))},refresh:function(){var e=getCurrentPages()[getCurrentPages().length-1];e&&this.title(this.lang("title"))},title:function(t){t&&e.setNavigationBarTitle({title:t,success:function(e){},fail:function(e){}})},list:function(){var e=[];try{for(var t=0;t<i.length;t++){var n=o("2043")("./"+i[t]+"/common.js").lang;e.push({name:n.common.name,value:i[t]})}}catch(r){}return e}};t.default=r}).call(this,o("df3c")["default"])},"1fea":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},2043:function(e,t,o){var i={"./en-us/common.js":"2516","./zh-cn/common.js":"4397"};function n(e){var t=r(e);return o(t)}function r(e){if(!o.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}n.keys=function(){return Object.keys(i)},n.resolve=r,e.exports=n,n.id="2043"},"235f":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的砍价"}},"23df":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"核销台"}},2516:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={tabBar:{home:"index",category:"category",cart:"cart",member:"member"},common:{name:"英文",mescrollTextInOffset:"pull to refresh",mescrollTextOutOffset:"Loading...",mescrollEmpty:"No data available",goodsRecommendTitle:"Guess you like",currencySymbol:"¥"}}},"277e":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"收货地址",newAddAddress:"添加收货地址",getAddress:"一键获取地址",is_default:"默认",modify:"编辑",del:"删除"}},2817:function(e,t,o){"use strict";(function(e,o){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={methods:{orderDelete:function(t,o){var i=this;e.showModal({title:"提示",content:"您确定要删除该订单吗？",success:function(e){e.confirm&&i.$api.sendRequest({url:"/api/order/delete",data:{order_id:t},success:function(e){e.code>=0?(i.$util.showToast({title:"删除订单成功"}),"function"==typeof o&&o()):i.$util.showToast({title:"删除订单失败，"+e.message,duration:2e3})}})}})},orderPay:function(t){var o=this;0==t.adjust_money?this.pay():e.showModal({title:"提示",content:"商家已将支付金额调整为"+t.pay_money+"元，是否继续支付？",success:function(e){e.confirm&&o.pay()}})},pay:function(){var e=this;this.$api.sendRequest({url:"/api/order/pay",data:{order_ids:this.orderData.order_id},success:function(t){t.code>=0?e.$refs.choosePaymentPopup.getPayInfo(t.data):e.$util.showToast({title:t.message})}})},orderClose:function(t,o){var i=this;e.showModal({title:"提示",content:"您确定要关闭该订单吗？",success:function(e){e.confirm&&i.$api.sendRequest({url:"/api/order/close",data:{order_id:t},success:function(e){e.code>=0?"function"==typeof o&&o():i.$util.showToast({title:"关闭失败，"+e.message,duration:2e3})}})}})},orderDelivery:function(t,i){var n=this;"wechatpay"==t.pay_type&&o.openBusinessView&&t.is_trade_managed?o.openBusinessView({businessType:"weappOrderConfirm",extraData:{merchant_id:t.pay_config.mch_id,merchant_trade_no:t.out_trade_no},success:function(e){e.extraData.status,n.$api.sendRequest({url:"/api/order/takedelivery",data:{order_id:t.order_id},success:function(e){n.$util.showToast({title:e.message}),"function"==typeof i&&i()}})},fail:function(e){console.log("fail",e)}}):e.showModal({title:"提示",content:"您确定已经收到货物了吗？",success:function(e){e.confirm&&n.$api.sendRequest({url:"/api/order/takedelivery",data:{order_id:t.order_id},success:function(e){n.$util.showToast({title:e.message}),"function"==typeof i&&i()}})}})},orderVirtualDelivery:function(t,i){var n=this;"wechatpay"==t.pay_type&&o.openBusinessView&&t.is_trade_managed?o.openBusinessView({businessType:"weappOrderConfirm",extraData:{merchant_id:t.pay_config.mch_id,merchant_trade_no:t.out_trade_no},success:function(e){e.extraData.status,n.$api.sendRequest({url:"/api/order/membervirtualtakedelivery",data:{order_id:t.order_id},success:function(e){n.$util.showToast({title:e.message}),"function"==typeof i&&i()}})},fail:function(e){}}):e.showModal({title:"提示",content:"您确定要进行收货吗？",success:function(e){e.confirm&&n.$api.sendRequest({url:"/api/order/membervirtualtakedelivery",data:{order_id:t.order_id},success:function(e){n.$util.showToast({title:e.message}),"function"==typeof i&&i()}})}})}}};t.default=i}).call(this,o("df3c")["default"],o("3223")["default"])},2929:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"兑换结果",exchangeSuccess:"兑换成功",see:"查看兑换记录",goHome:"回到首页"}},"2bbd":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"待付款订单"}},"2c6d":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"会员卡"}},"2d55":function(e,t,o){"use strict";(function(e,i){var n=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(o("7eb4")),a=n(o("ee10")),s=(o("edd0"),{data:function(){return{skuId:0,goodsId:0,isIphoneX:!1,whetherCollection:0,preview:0,videoContext:"",swiperInterval:1,swiperAutoplay:!1,swiperCurrent:1,switchMedia:"img",goodsEvaluate:[{member_headimg:"",member_name:"",content:"",images:[],create_time:0,sku_name:""}],evaluateConfig:{evaluate_audit:1,evaluate_show:0,evaluate_status:1},goodsCircle:!1,service:null,shareUrl:"",source_member:0,isCommunity:!1,poster:"-1",posterMsg:"",posterHeight:0,posterParams:{},goodsRoute:"",posterApi:"",goodsAttrShow:!1,storeList:{data:[],page:1,page_size:10},isShowStore:!1,latitude:null,longitude:null,evaluateCount:0,deliveryType:null,isVirtual:0,hasGlobalStore:!1,saleStore:"all",isInitStoreData:!1}},created:function(){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.location&&(this.latitude=this.location.latitude,this.longitude=this.location.longitude)},watch:{location:function(e){e&&(this.latitude=e.latitude,this.longitude=e.longitude,this.getStoreData())}},methods:{init:function(t){this.skuId=t.sku_id,this.goodsId=t.goods_id,this.preview=t.preview,this.source_member=t.source_member,this.whetherCollection=t.whetherCollection,this.posterParams=t.posterParams,this.shareUrl=t.shareUrl,this.goodsRoute=t.goodsRoute,this.posterApi=t.posterApi,this.isVirtual=t.isVirtual,this.deliveryType=t.deliveryType,this.evaluateConfig=t.evaluateConfig,this.saleStore=t.sale_store,1==this.evaluateConfig.evaluate_show&&(this.getGoodsEvaluate(t.evaluateList),this.evaluateCount=t.evaluateCount),2!=t.goods_class&&3!=t.goods_class&&(this.isShowStore=!0),this.isInitStoreData||(this.isInitStoreData=!0,this.getStoreData()),this.getService(),this.videoContext=e.createVideoContext("goodsVideo"),this.goodsSyncToGoodsCircle()},swiperChange:function(e){this.swiperCurrent=e.detail.current+1},openMerchantsServicePopup:function(){this.$refs.merchantsServicePopup.open()},closeMerchantsServicePopup:function(){this.$refs.merchantsServicePopup.close()},openStoreListPopup:function(){this.$refs.storeListPopup.open()},closeStoreListPopup:function(){this.$refs.storeListPopup.close()},getStoreData:function(){var e=this,t={page:this.storeList.page,page_size:this.storeList.page_size,store_ids:this.saleStore};this.latitude&&this.longitude&&(t.latitude=this.latitude,t.longitude=this.longitude),this.$api.sendRequest({url:"/api/store/page",data:t,success:function(t){1==e.storeList.page&&e.storeList.data,t.code>=0&&t.data?(e.storeList.data=e.storeList.data.concat(t.data.list),t.data.list.forEach((function(t){t.store_id==e.globalStoreInfo.store_id&&(e.hasGlobalStore=!0)}))):e.$util.showToast({title:t.message})}})},selectStore:function(e){this.$util.redirectTo("/pages_tool/store/detail",{store_id:e.store_id}),this.closeStoreListPopup()},switchGoodsAttr:function(){this.goodsAttrShow=!this.goodsAttrShow},getGoodsEvaluate:function(e){var t=this;e&&(this.goodsEvaluate=e,this.goodsEvaluate.forEach((function(e,o){t.goodsEvaluate[o].images&&(t.goodsEvaluate[o].images=t.goodsEvaluate[o].images.split(",")),1==t.goodsEvaluate[o].is_anonymous&&(t.goodsEvaluate[o].member_name=t.goodsEvaluate[o].member_name.replace(t.goodsEvaluate[o].member_name.substring(1,t.goodsEvaluate[o].member_name.length-1),"***"))})))},previewEvaluate:function(t,o,i){for(var n=[],r=0;r<this.goodsEvaluate[t][i].length;r++)n.push(this.$util.img(this.goodsEvaluate[t][i][r]));e.previewImage({current:o,urls:n})},collection:function(){var e=this;return(0,a.default)(r.default.mark((function t(){var o,i,n;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.preview){t.next=2;break}return t.abrupt("return");case 2:if(!e.storeToken){t.next=19;break}if(0!=e.whetherCollection){t.next=11;break}return t.next=6,e.$api.sendRequest({url:"/api/goodscollect/add",data:{sku_id:e.skuId,goods_id:e.goodsSkuDetail.goods_id,sku_name:e.goodsSkuDetail.sku_name,sku_price:e.goodsSkuDetail.show_price,sku_image:e.goodsSkuDetail.sku_image},async:!1});case 6:o=t.sent,i=o.data,i>0&&(e.whetherCollection=1),t.next=16;break;case 11:return t.next=13,e.$api.sendRequest({url:"/api/goodscollect/delete",data:{goods_id:e.goodsSkuDetail.goods_id},async:!1});case 13:n=t.sent,i=n.data,i>0&&(e.whetherCollection=0);case 16:return t.abrupt("return",e.whetherCollection);case 19:e.source_member?e.$refs.login.open(e.shareUrl+"&source_member="+e.source_member):e.$refs.login.open(e.shareUrl);case 20:case"end":return t.stop()}}),t)})))()},openSharePopup:function(){this.$refs.sharePopup.open()},closeSharePopup:function(){this.$refs.sharePopup.close()},copyUrl:function(){var e=this,t=this.$config.h5Domain+this.shareUrl;this.memberInfo&&this.memberInfo.member_id&&(t+="&source_member="+this.memberInfo.member_id);var o=this.$store.state.globalStoreInfo;o&&(t+="&store_id="+o.store_id),this.$util.copy(t,(function(){e.closeSharePopup()}))},openPosterPopup:function(){this.getGoodsPoster(),this.$refs.sharePopup.close()},closePosterPopup:function(){this.$refs.posterPopup.close(),this.poster=""},getGoodsPoster:function(){var t=this;e.showLoading({title:"海报生成中..."}),this.memberInfo&&this.memberInfo.member_id&&(this.posterParams.source_member=this.memberInfo.member_id);var o=this.$store.state.globalStoreInfo;o&&(this.posterParams.store_id=o.store_id),this.$api.sendRequest({url:this.posterApi,data:{page:this.goodsRoute,qrcode_param:JSON.stringify(this.posterParams)},success:function(o){0==o.code?(t.$refs.posterPopup.open(),t.poster=o.data.path+"?time="+(new Date).getTime()):(t.posterMsg=o.message,t.$util.showToast({title:o.message})),e.hideLoading()},fail:function(t){e.hideLoading()}})},previewMedia:function(t){for(var o=[],i=0;i<this.goodsSkuDetail.sku_images.length;i++)o.push(this.$util.img(this.goodsSkuDetail.sku_images[i],{size:"big"}));e.previewImage({current:t,urls:o})},swiperImageError:function(e){this.goodsSkuDetail.sku_images[e]=this.$util.getDefaultImage().goods,this.$forceUpdate()},saveGoodsPoster:function(){var t=this,o=this.$util.img(this.poster);e.downloadFile({url:o,success:function(o){"downloadFile:ok"==o.errMsg&&e.saveImageToPhotosAlbum({filePath:o.tempFilePath,success:function(){t.$util.showToast({title:"保存成功"})},fail:function(e){t.$util.showToast({title:"保存失败，请稍后重试"})}})},fail:function(e){t.$util.showToast({title:"保存失败，请稍后重试"})}})},getService:function(){var e=this;this.$api.sendRequest({url:"/api/goods/aftersale",success:function(t){0==t.code&&t.data&&(e.service=t.data)}})},goodsSyncToGoodsCircle:function(){var e=this;this.$api.sendRequest({url:"/goodscircle/api/goods/sync",data:{goods_id:this.goodsSkuDetail.goods_id},success:function(t){0==t.code&&(e.goodsCircle=!0)}})},openBusinessView:function(){var e=this;i.openBusinessView&&i.openBusinessView({businessType:"friendGoodsRecommend",extraData:{product:{item_code:this.goodsSkuDetail.goods_id,title:this.goodsSkuDetail.sku_name,image_list:this.goodsSkuDetail.sku_images.map((function(t){return e.$util.img(t)}))}},success:function(e){console.log("success",e)},fail:function(e){console.log("fail",e)}})},toEvaluateDetail:function(e){this.$util.redirectTo("/pages_tool/goods/evaluate",{goods_id:e})},showImg:function(t){for(var o=t.target.dataset.nodes,i=[],n=0;n<o.length;n++){var r=o[n].children;if(Array.isArray(r))for(var a=0;a<r.length;a++)r[a].attrs&&"img"==r[a].name&&r[a].attrs.src&&i.push(r[a].attrs.src)}e.previewImage({current:i,urls:i})},onCommunity:function(){this.isCommunity=!0},onCloseCommunity:function(){this.isCommunity=!1}}});t.default=s}).call(this,o("df3c")["default"],o("3223")["default"])},"2f8f":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={baseUrl:"{{$baseUrl}}",imgDomain:"{{$imgDomain}}",h5Domain:"{{$h5Domain}}",mpKey:"{{$mpKey}}",webSocket:"{{$webSocket}}",pingInterval:1500,version:"5.5.1",storagePrefix:"h5_"};t.default=i},3141:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"中奖纪录"}},3223:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],n=["lanDebug","router","worklet"],r="undefined"!==typeof globalThis?globalThis:function(){return this}(),a=["w","x"].join(""),s=r[a],c=s.getLaunchOptionsSync?s.getLaunchOptionsSync():null;function l(e){return(!c||1154!==c.scene||!n.includes(e))&&(i.indexOf(e)>-1||"function"===typeof s[e])}r[a]=function(){var e={};for(var t in s)l(t)&&(e[t]=s[t]);return e}(),r[a].canIUse("getAppBaseInfo")||(r[a].getAppBaseInfo=r[a].getSystemInfoSync),r[a].canIUse("getWindowInfo")||(r[a].getWindowInfo=r[a].getSystemInfoSync),r[a].canIUse("getDeviceInfo")||(r[a].getDeviceInfo=r[a].getSystemInfoSync);var u=r[a];t.default=u},3240:function(e,t,o){"use strict";o.r(t),function(e){
/*!
 * Vue.js v2.6.11
 * (c) 2014-2024 Evan You
 * Released under the MIT License.
 */
var o=Object.freeze({});function i(e){return void 0===e||null===e}function n(e){return void 0!==e&&null!==e}function r(e){return!0===e}function a(e){return"string"===typeof e||"number"===typeof e||"symbol"===typeof e||"boolean"===typeof e}function s(e){return null!==e&&"object"===typeof e}var c=Object.prototype.toString;function l(e){return"[object Object]"===c.call(e)}function u(e){var t=parseFloat(String(e));return t>=0&&Math.floor(t)===t&&isFinite(e)}function d(e){return n(e)&&"function"===typeof e.then&&"function"===typeof e.catch}function f(e){return null==e?"":Array.isArray(e)||l(e)&&e.toString===c?JSON.stringify(e,null,2):String(e)}function p(e){var t=parseFloat(e);return isNaN(t)?e:t}function h(e,t){for(var o=Object.create(null),i=e.split(","),n=0;n<i.length;n++)o[i[n]]=!0;return t?function(e){return o[e.toLowerCase()]}:function(e){return o[e]}}h("slot,component",!0);var g=h("key,ref,slot,slot-scope,is");function m(e,t){if(e.length){var o=e.indexOf(t);if(o>-1)return e.splice(o,1)}}var _=Object.prototype.hasOwnProperty;function v(e,t){return _.call(e,t)}function y(e){var t=Object.create(null);return function(o){var i=t[o];return i||(t[o]=e(o))}}var b=/-(\w)/g,w=y((function(e){return e.replace(b,(function(e,t){return t?t.toUpperCase():""}))})),S=y((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})),k=/\B([A-Z])/g,x=y((function(e){return e.replace(k,"-$1").toLowerCase()}));var D=Function.prototype.bind?function(e,t){return e.bind(t)}:function(e,t){function o(o){var i=arguments.length;return i?i>1?e.apply(t,arguments):e.call(t,o):e.call(t)}return o._length=e.length,o};function C(e,t){t=t||0;var o=e.length-t,i=new Array(o);while(o--)i[o]=e[o+t];return i}function P(e,t){for(var o in t)e[o]=t[o];return e}function O(e){for(var t={},o=0;o<e.length;o++)e[o]&&P(t,e[o]);return t}function $(e,t,o){}var j=function(e,t,o){return!1},M=function(e){return e};function T(e,t){if(e===t)return!0;var o=s(e),i=s(t);if(!o||!i)return!o&&!i&&String(e)===String(t);try{var n=Array.isArray(e),r=Array.isArray(t);if(n&&r)return e.length===t.length&&e.every((function(e,o){return T(e,t[o])}));if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(n||r)return!1;var a=Object.keys(e),c=Object.keys(t);return a.length===c.length&&a.every((function(o){return T(e[o],t[o])}))}catch(l){return!1}}function A(e,t){for(var o=0;o<e.length;o++)if(T(e[o],t))return o;return-1}function I(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}var F=["component","directive","filter"],E=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch"],L={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:j,isReservedAttr:j,isUnknownElement:j,getTagNamespace:$,parsePlatformTagName:M,mustUseProp:j,async:!0,_lifecycleHooks:E},R=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function N(e){var t=(e+"").charCodeAt(0);return 36===t||95===t}function U(e,t,o,i){Object.defineProperty(e,t,{value:o,enumerable:!!i,writable:!0,configurable:!0})}var B=new RegExp("[^"+R.source+".$_\\d]");var z,V="__proto__"in{},G="undefined"!==typeof window,q="undefined"!==typeof WXEnvironment&&!!WXEnvironment.platform,H=q&&WXEnvironment.platform.toLowerCase(),W=G&&window.navigator&&window.navigator.userAgent.toLowerCase(),J=W&&/msie|trident/.test(W),X=(W&&W.indexOf("msie 9.0"),W&&W.indexOf("edge/")>0),Y=(W&&W.indexOf("android"),W&&/iphone|ipad|ipod|ios/.test(W)||"ios"===H),K=(W&&/chrome\/\d+/.test(W),W&&/phantomjs/.test(W),W&&W.match(/firefox\/(\d+)/),{}.watch);if(G)try{var Q={};Object.defineProperty(Q,"passive",{get:function(){}}),window.addEventListener("test-passive",null,Q)}catch(Lo){}var Z=function(){return void 0===z&&(z=!G&&!q&&"undefined"!==typeof e&&(e["process"]&&"server"===e["process"].env.VUE_ENV)),z},ee=G&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function te(e){return"function"===typeof e&&/native code/.test(e.toString())}var oe,ie="undefined"!==typeof Symbol&&te(Symbol)&&"undefined"!==typeof Reflect&&te(Reflect.ownKeys);oe="undefined"!==typeof Set&&te(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var ne=$,re=0,ae=function(){this.id=re++,this.subs=[]};function se(e){ae.SharedObject.targetStack.push(e),ae.SharedObject.target=e,ae.target=e}function ce(){ae.SharedObject.targetStack.pop(),ae.SharedObject.target=ae.SharedObject.targetStack[ae.SharedObject.targetStack.length-1],ae.target=ae.SharedObject.target}ae.prototype.addSub=function(e){this.subs.push(e)},ae.prototype.removeSub=function(e){m(this.subs,e)},ae.prototype.depend=function(){ae.SharedObject.target&&ae.SharedObject.target.addDep(this)},ae.prototype.notify=function(){var e=this.subs.slice();for(var t=0,o=e.length;t<o;t++)e[t].update()},ae.SharedObject={},ae.SharedObject.target=null,ae.SharedObject.targetStack=[];var le=function(e,t,o,i,n,r,a,s){this.tag=e,this.data=t,this.children=o,this.text=i,this.elm=n,this.ns=void 0,this.context=r,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=a,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},ue={child:{configurable:!0}};ue.child.get=function(){return this.componentInstance},Object.defineProperties(le.prototype,ue);var de=function(e){void 0===e&&(e="");var t=new le;return t.text=e,t.isComment=!0,t};function fe(e){return new le(void 0,void 0,void 0,String(e))}var pe=Array.prototype,he=Object.create(pe);["push","pop","shift","unshift","splice","sort","reverse"].forEach((function(e){var t=pe[e];U(he,e,(function(){var o=[],i=arguments.length;while(i--)o[i]=arguments[i];var n,r=t.apply(this,o),a=this.__ob__;switch(e){case"push":case"unshift":n=o;break;case"splice":n=o.slice(2);break}return n&&a.observeArray(n),a.dep.notify(),r}))}));var ge=Object.getOwnPropertyNames(he),me=!0;function _e(e){me=e}var ve=function(e){this.value=e,this.dep=new ae,this.vmCount=0,U(e,"__ob__",this),Array.isArray(e)?(V?e.push!==e.__proto__.push?ye(e,he,ge):function(e,t){e.__proto__=t}(e,he):ye(e,he,ge),this.observeArray(e)):this.walk(e)};function ye(e,t,o){for(var i=0,n=o.length;i<n;i++){var r=o[i];U(e,r,t[r])}}function be(e,t){var o;if(s(e)&&!(e instanceof le))return v(e,"__ob__")&&e.__ob__ instanceof ve?o=e.__ob__:!me||Z()||!Array.isArray(e)&&!l(e)||!Object.isExtensible(e)||e._isVue||e.__v_isMPComponent||(o=new ve(e)),t&&o&&o.vmCount++,o}function we(e,t,o,i,n){var r=new ae,a=Object.getOwnPropertyDescriptor(e,t);if(!a||!1!==a.configurable){var s=a&&a.get,c=a&&a.set;s&&!c||2!==arguments.length||(o=e[t]);var l=!n&&be(o);Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=s?s.call(e):o;return ae.SharedObject.target&&(r.depend(),l&&(l.dep.depend(),Array.isArray(t)&&xe(t))),t},set:function(t){var i=s?s.call(e):o;t===i||t!==t&&i!==i||s&&!c||(c?c.call(e,t):o=t,l=!n&&be(t),r.notify())}})}}function Se(e,t,o){if(Array.isArray(e)&&u(t))return e.length=Math.max(e.length,t),e.splice(t,1,o),o;if(t in e&&!(t in Object.prototype))return e[t]=o,o;var i=e.__ob__;return e._isVue||i&&i.vmCount?o:i?(we(i.value,t,o),i.dep.notify(),o):(e[t]=o,o)}function ke(e,t){if(Array.isArray(e)&&u(t))e.splice(t,1);else{var o=e.__ob__;e._isVue||o&&o.vmCount||v(e,t)&&(delete e[t],o&&o.dep.notify())}}function xe(e){for(var t=void 0,o=0,i=e.length;o<i;o++)t=e[o],t&&t.__ob__&&t.__ob__.dep.depend(),Array.isArray(t)&&xe(t)}ve.prototype.walk=function(e){for(var t=Object.keys(e),o=0;o<t.length;o++)we(e,t[o])},ve.prototype.observeArray=function(e){for(var t=0,o=e.length;t<o;t++)be(e[t])};var De=L.optionMergeStrategies;function Ce(e,t){if(!t)return e;for(var o,i,n,r=ie?Reflect.ownKeys(t):Object.keys(t),a=0;a<r.length;a++)o=r[a],"__ob__"!==o&&(i=e[o],n=t[o],v(e,o)?i!==n&&l(i)&&l(n)&&Ce(i,n):Se(e,o,n));return e}function Pe(e,t,o){return o?function(){var i="function"===typeof t?t.call(o,o):t,n="function"===typeof e?e.call(o,o):e;return i?Ce(i,n):n}:t?e?function(){return Ce("function"===typeof t?t.call(this,this):t,"function"===typeof e?e.call(this,this):e)}:t:e}function Oe(e,t){var o=t?e?e.concat(t):Array.isArray(t)?t:[t]:e;return o?function(e){for(var t=[],o=0;o<e.length;o++)-1===t.indexOf(e[o])&&t.push(e[o]);return t}(o):o}function $e(e,t,o,i){var n=Object.create(e||null);return t?P(n,t):n}De.data=function(e,t,o){return o?Pe(e,t,o):t&&"function"!==typeof t?e:Pe(e,t)},E.forEach((function(e){De[e]=Oe})),F.forEach((function(e){De[e+"s"]=$e})),De.watch=function(e,t,o,i){if(e===K&&(e=void 0),t===K&&(t=void 0),!t)return Object.create(e||null);if(!e)return t;var n={};for(var r in P(n,e),t){var a=n[r],s=t[r];a&&!Array.isArray(a)&&(a=[a]),n[r]=a?a.concat(s):Array.isArray(s)?s:[s]}return n},De.props=De.methods=De.inject=De.computed=function(e,t,o,i){if(!e)return t;var n=Object.create(null);return P(n,e),t&&P(n,t),n},De.provide=Pe;var je=function(e,t){return void 0===t?e:t};function Me(e,t,o){if("function"===typeof t&&(t=t.options),function(e,t){var o=e.props;if(o){var i,n,r,a={};if(Array.isArray(o)){i=o.length;while(i--)n=o[i],"string"===typeof n&&(r=w(n),a[r]={type:null})}else if(l(o))for(var s in o)n=o[s],r=w(s),a[r]=l(n)?n:{type:n};else 0;e.props=a}}(t),function(e,t){var o=e.inject;if(o){var i=e.inject={};if(Array.isArray(o))for(var n=0;n<o.length;n++)i[o[n]]={from:o[n]};else if(l(o))for(var r in o){var a=o[r];i[r]=l(a)?P({from:r},a):{from:a}}else 0}}(t),function(e){var t=e.directives;if(t)for(var o in t){var i=t[o];"function"===typeof i&&(t[o]={bind:i,update:i})}}(t),!t._base&&(t.extends&&(e=Me(e,t.extends,o)),t.mixins))for(var i=0,n=t.mixins.length;i<n;i++)e=Me(e,t.mixins[i],o);var r,a={};for(r in e)s(r);for(r in t)v(e,r)||s(r);function s(i){var n=De[i]||je;a[i]=n(e[i],t[i],o,i)}return a}function Te(e,t,o,i){if("string"===typeof o){var n=e[t];if(v(n,o))return n[o];var r=w(o);if(v(n,r))return n[r];var a=S(r);if(v(n,a))return n[a];var s=n[o]||n[r]||n[a];return s}}function Ae(e,t,o,i){var n=t[e],r=!v(o,e),a=o[e],s=Ee(Boolean,n.type);if(s>-1)if(r&&!v(n,"default"))a=!1;else if(""===a||a===x(e)){var c=Ee(String,n.type);(c<0||s<c)&&(a=!0)}if(void 0===a){a=function(e,t,o){if(!v(t,"default"))return;var i=t.default;0;if(e&&e.$options.propsData&&void 0===e.$options.propsData[o]&&void 0!==e._props[o])return e._props[o];return"function"===typeof i&&"Function"!==Ie(t.type)?i.call(e):i}(i,n,e);var l=me;_e(!0),be(a),_e(l)}return a}function Ie(e){var t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:""}function Fe(e,t){return Ie(e)===Ie(t)}function Ee(e,t){if(!Array.isArray(t))return Fe(t,e)?0:-1;for(var o=0,i=t.length;o<i;o++)if(Fe(t[o],e))return o;return-1}function Le(e,t,o){se();try{if(t){var i=t;while(i=i.$parent){var n=i.$options.errorCaptured;if(n)for(var r=0;r<n.length;r++)try{var a=!1===n[r].call(i,e,t,o);if(a)return}catch(Lo){Ne(Lo,i,"errorCaptured hook")}}}Ne(e,t,o)}finally{ce()}}function Re(e,t,o,i,n){var r;try{r=o?e.apply(t,o):e.call(t),r&&!r._isVue&&d(r)&&!r._handled&&(r.catch((function(e){return Le(e,i,n+" (Promise/async)")})),r._handled=!0)}catch(Lo){Le(Lo,i,n)}return r}function Ne(e,t,o){if(L.errorHandler)try{return L.errorHandler.call(null,e,t,o)}catch(Lo){Lo!==e&&Ue(Lo,null,"config.errorHandler")}Ue(e,t,o)}function Ue(e,t,o){if(!G&&!q||"undefined"===typeof console)throw e;console.error(e)}var Be,ze=[],Ve=!1;function Ge(){Ve=!1;var e=ze.slice(0);ze.length=0;for(var t=0;t<e.length;t++)e[t]()}if("undefined"!==typeof Promise&&te(Promise)){var qe=Promise.resolve();Be=function(){qe.then(Ge),Y&&setTimeout($)}}else if(J||"undefined"===typeof MutationObserver||!te(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())Be="undefined"!==typeof setImmediate&&te(setImmediate)?function(){setImmediate(Ge)}:function(){setTimeout(Ge,0)};else{var He=1,We=new MutationObserver(Ge),Je=document.createTextNode(String(He));We.observe(Je,{characterData:!0}),Be=function(){He=(He+1)%2,Je.data=String(He)}}function Xe(e,t){var o;if(ze.push((function(){if(e)try{e.call(t)}catch(Lo){Le(Lo,t,"nextTick")}else o&&o(t)})),Ve||(Ve=!0,Be()),!e&&"undefined"!==typeof Promise)return new Promise((function(e){o=e}))}var Ye=new oe;function Ke(e){(function e(t,o){var i,n,r=Array.isArray(t);if(!r&&!s(t)||Object.isFrozen(t)||t instanceof le)return;if(t.__ob__){var a=t.__ob__.dep.id;if(o.has(a))return;o.add(a)}if(r){i=t.length;while(i--)e(t[i],o)}else{n=Object.keys(t),i=n.length;while(i--)e(t[n[i]],o)}})(e,Ye),Ye.clear()}var Qe=y((function(e){var t="&"===e.charAt(0);e=t?e.slice(1):e;var o="~"===e.charAt(0);e=o?e.slice(1):e;var i="!"===e.charAt(0);return e=i?e.slice(1):e,{name:e,once:o,capture:i,passive:t}}));function Ze(e,t){function o(){var e=arguments,i=o.fns;if(!Array.isArray(i))return Re(i,null,arguments,t,"v-on handler");for(var n=i.slice(),r=0;r<n.length;r++)Re(n[r],null,e,t,"v-on handler")}return o.fns=e,o}function et(e,t,o,r){var a=t.options.mpOptions&&t.options.mpOptions.properties;if(i(a))return o;var s=t.options.mpOptions.externalClasses||[],c=e.attrs,l=e.props;if(n(c)||n(l))for(var u in a){var d=x(u),f=tt(o,l,u,d,!0)||tt(o,c,u,d,!1);f&&o[u]&&-1!==s.indexOf(d)&&r[w(o[u])]&&(o[u]=r[w(o[u])])}return o}function tt(e,t,o,i,r){if(n(t)){if(v(t,o))return e[o]=t[o],r||delete t[o],!0;if(v(t,i))return e[o]=t[i],r||delete t[i],!0}return!1}function ot(e){return a(e)?[fe(e)]:Array.isArray(e)?function e(t,o){var s,c,l,u,d=[];for(s=0;s<t.length;s++)c=t[s],i(c)||"boolean"===typeof c||(l=d.length-1,u=d[l],Array.isArray(c)?c.length>0&&(c=e(c,(o||"")+"_"+s),it(c[0])&&it(u)&&(d[l]=fe(u.text+c[0].text),c.shift()),d.push.apply(d,c)):a(c)?it(u)?d[l]=fe(u.text+c):""!==c&&d.push(fe(c)):it(c)&&it(u)?d[l]=fe(u.text+c.text):(r(t._isVList)&&n(c.tag)&&i(c.key)&&n(o)&&(c.key="__vlist"+o+"_"+s+"__"),d.push(c)));return d}(e):void 0}function it(e){return n(e)&&n(e.text)&&function(e){return!1===e}(e.isComment)}function nt(e){var t=e.$options.provide;t&&(e._provided="function"===typeof t?t.call(e):t)}function rt(e){var t=at(e.$options.inject,e);t&&(_e(!1),Object.keys(t).forEach((function(o){we(e,o,t[o])})),_e(!0))}function at(e,t){if(e){for(var o=Object.create(null),i=ie?Reflect.ownKeys(e):Object.keys(e),n=0;n<i.length;n++){var r=i[n];if("__ob__"!==r){var a=e[r].from,s=t;while(s){if(s._provided&&v(s._provided,a)){o[r]=s._provided[a];break}s=s.$parent}if(!s)if("default"in e[r]){var c=e[r].default;o[r]="function"===typeof c?c.call(t):c}else 0}}return o}}function st(e,t){if(!e||!e.length)return{};for(var o={},i=0,n=e.length;i<n;i++){var r=e[i],a=r.data;if(a&&a.attrs&&a.attrs.slot&&delete a.attrs.slot,r.context!==t&&r.fnContext!==t||!a||null==a.slot)r.asyncMeta&&r.asyncMeta.data&&"page"===r.asyncMeta.data.slot?(o["page"]||(o["page"]=[])).push(r):(o.default||(o.default=[])).push(r);else{var s=a.slot,c=o[s]||(o[s]=[]);"template"===r.tag?c.push.apply(c,r.children||[]):c.push(r)}}for(var l in o)o[l].every(ct)&&delete o[l];return o}function ct(e){return e.isComment&&!e.asyncFactory||" "===e.text}function lt(e,t,i){var n,r=Object.keys(t).length>0,a=e?!!e.$stable:!r,s=e&&e.$key;if(e){if(e._normalized)return e._normalized;if(a&&i&&i!==o&&s===i.$key&&!r&&!i.$hasNormal)return i;for(var c in n={},e)e[c]&&"$"!==c[0]&&(n[c]=ut(t,c,e[c]))}else n={};for(var l in t)l in n||(n[l]=dt(t,l));return e&&Object.isExtensible(e)&&(e._normalized=n),U(n,"$stable",a),U(n,"$key",s),U(n,"$hasNormal",r),n}function ut(e,t,o){var i=function(){var e=arguments.length?o.apply(null,arguments):o({});return e=e&&"object"===typeof e&&!Array.isArray(e)?[e]:ot(e),e&&(0===e.length||1===e.length&&e[0].isComment)?void 0:e};return o.proxy&&Object.defineProperty(e,t,{get:i,enumerable:!0,configurable:!0}),i}function dt(e,t){return function(){return e[t]}}function ft(e,t){var o,i,r,a,c;if(Array.isArray(e)||"string"===typeof e)for(o=new Array(e.length),i=0,r=e.length;i<r;i++)o[i]=t(e[i],i,i,i);else if("number"===typeof e)for(o=new Array(e),i=0;i<e;i++)o[i]=t(i+1,i,i,i);else if(s(e))if(ie&&e[Symbol.iterator]){o=[];var l=e[Symbol.iterator](),u=l.next();while(!u.done)o.push(t(u.value,o.length,i,i++)),u=l.next()}else for(a=Object.keys(e),o=new Array(a.length),i=0,r=a.length;i<r;i++)c=a[i],o[i]=t(e[c],c,i,i);return n(o)||(o=[]),o._isVList=!0,o}function pt(e,t,o,i){var n,r=this.$scopedSlots[e];r?(o=o||{},i&&(o=P(P({},i),o)),n=r(o,this,o._i)||t):n=this.$slots[e]||t;var a=o&&o.slot;return a?this.$createElement("template",{slot:a},n):n}function ht(e){return Te(this.$options,"filters",e)||M}function gt(e,t){return Array.isArray(e)?-1===e.indexOf(t):e!==t}function mt(e,t,o,i,n){var r=L.keyCodes[t]||o;return n&&i&&!L.keyCodes[t]?gt(n,i):r?gt(r,e):i?x(i)!==t:void 0}function _t(e,t,o,i,n){if(o)if(s(o)){var r;Array.isArray(o)&&(o=O(o));var a=function(a){if("class"===a||"style"===a||g(a))r=e;else{var s=e.attrs&&e.attrs.type;r=i||L.mustUseProp(t,s,a)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var c=w(a),l=x(a);if(!(c in r)&&!(l in r)&&(r[a]=o[a],n)){var u=e.on||(e.on={});u["update:"+a]=function(e){o[a]=e}}};for(var c in o)a(c)}else;return e}function vt(e,t){var o=this._staticTrees||(this._staticTrees=[]),i=o[e];return i&&!t||(i=o[e]=this.$options.staticRenderFns[e].call(this._renderProxy,null,this),bt(i,"__static__"+e,!1)),i}function yt(e,t,o){return bt(e,"__once__"+t+(o?"_"+o:""),!0),e}function bt(e,t,o){if(Array.isArray(e))for(var i=0;i<e.length;i++)e[i]&&"string"!==typeof e[i]&&wt(e[i],t+"_"+i,o);else wt(e,t,o)}function wt(e,t,o){e.isStatic=!0,e.key=t,e.isOnce=o}function St(e,t){if(t)if(l(t)){var o=e.on=e.on?P({},e.on):{};for(var i in t){var n=o[i],r=t[i];o[i]=n?[].concat(n,r):r}}else;return e}function kt(e,t,o,i){t=t||{$stable:!o};for(var n=0;n<e.length;n++){var r=e[n];Array.isArray(r)?kt(r,t,o):r&&(r.proxy&&(r.fn.proxy=!0),t[r.key]=r.fn)}return i&&(t.$key=i),t}function xt(e,t){for(var o=0;o<t.length;o+=2){var i=t[o];"string"===typeof i&&i&&(e[t[o]]=t[o+1])}return e}function Dt(e,t){return"string"===typeof e?t+e:e}function Ct(e){e._o=yt,e._n=p,e._s=f,e._l=ft,e._t=pt,e._q=T,e._i=A,e._m=vt,e._f=ht,e._k=mt,e._b=_t,e._v=fe,e._e=de,e._u=kt,e._g=St,e._d=xt,e._p=Dt}function Pt(e,t,i,n,a){var s,c=this,l=a.options;v(n,"_uid")?(s=Object.create(n),s._original=n):(s=n,n=n._original);var u=r(l._compiled),d=!u;this.data=e,this.props=t,this.children=i,this.parent=n,this.listeners=e.on||o,this.injections=at(l.inject,n),this.slots=function(){return c.$slots||lt(e.scopedSlots,c.$slots=st(i,n)),c.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return lt(e.scopedSlots,this.slots())}}),u&&(this.$options=l,this.$slots=this.slots(),this.$scopedSlots=lt(e.scopedSlots,this.$slots)),l._scopeId?this._c=function(e,t,o,i){var r=It(s,e,t,o,i,d);return r&&!Array.isArray(r)&&(r.fnScopeId=l._scopeId,r.fnContext=n),r}:this._c=function(e,t,o,i){return It(s,e,t,o,i,d)}}function Ot(e,t,o,i,n){var r=function(e){var t=new le(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.key,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMeta=e.asyncMeta,t.isCloned=!0,t}(e);return r.fnContext=o,r.fnOptions=i,t.slot&&((r.data||(r.data={})).slot=t.slot),r}function $t(e,t){for(var o in t)e[w(o)]=t[o]}Ct(Pt.prototype);var jt={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var o=e;jt.prepatch(o,o)}else{var i=e.componentInstance=function(e,t){var o={_isComponent:!0,_parentVnode:e,parent:t},i=e.data.inlineTemplate;n(i)&&(o.render=i.render,o.staticRenderFns=i.staticRenderFns);return new e.componentOptions.Ctor(o)}(e,Vt);i.$mount(t?e.elm:void 0,t)}},prepatch:function(e,t){var i=t.componentOptions,n=t.componentInstance=e.componentInstance;(function(e,t,i,n,r){0;var a=n.data.scopedSlots,s=e.$scopedSlots,c=!!(a&&!a.$stable||s!==o&&!s.$stable||a&&e.$scopedSlots.$key!==a.$key),l=!!(r||e.$options._renderChildren||c);e.$options._parentVnode=n,e.$vnode=n,e._vnode&&(e._vnode.parent=n);if(e.$options._renderChildren=r,e.$attrs=n.data.attrs||o,e.$listeners=i||o,t&&e.$options.props){_e(!1);for(var u=e._props,d=e.$options._propKeys||[],f=0;f<d.length;f++){var p=d[f],h=e.$options.props;u[p]=Ae(p,h,t,e)}_e(!0),e.$options.propsData=t}e._$updateProperties&&e._$updateProperties(e),i=i||o;var g=e.$options._parentListeners;e.$options._parentListeners=i,zt(e,i,g),l&&(e.$slots=st(r,n.context),e.$forceUpdate());0})(n,i.propsData,i.listeners,t,i.children)},insert:function(e){var t=e.context,o=e.componentInstance;o._isMounted||(Ht(o,"onServiceCreated"),Ht(o,"onServiceAttached"),o._isMounted=!0,Ht(o,"mounted")),e.data.keepAlive&&(t._isMounted?function(e){e._inactive=!1,Jt.push(e)}(o):qt(o,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAlive?function e(t,o){if(o&&(t._directInactive=!0,Gt(t)))return;if(!t._inactive){t._inactive=!0;for(var i=0;i<t.$children.length;i++)e(t.$children[i]);Ht(t,"deactivated")}}(t,!0):t.$destroy())}},Mt=Object.keys(jt);function Tt(e,t,a,c,l){if(!i(e)){var u=a.$options._base;if(s(e)&&(e=u.extend(e)),"function"===typeof e){var f;if(i(e.cid)&&(f=e,e=function(e,t){if(r(e.error)&&n(e.errorComp))return e.errorComp;if(n(e.resolved))return e.resolved;var o=Et;o&&n(e.owners)&&-1===e.owners.indexOf(o)&&e.owners.push(o);if(r(e.loading)&&n(e.loadingComp))return e.loadingComp;if(o&&!n(e.owners)){var a=e.owners=[o],c=!0,l=null,u=null;o.$on("hook:destroyed",(function(){return m(a,o)}));var f=function(e){for(var t=0,o=a.length;t<o;t++)a[t].$forceUpdate();e&&(a.length=0,null!==l&&(clearTimeout(l),l=null),null!==u&&(clearTimeout(u),u=null))},p=I((function(o){e.resolved=Lt(o,t),c?a.length=0:f(!0)})),h=I((function(t){n(e.errorComp)&&(e.error=!0,f(!0))})),g=e(p,h);return s(g)&&(d(g)?i(e.resolved)&&g.then(p,h):d(g.component)&&(g.component.then(p,h),n(g.error)&&(e.errorComp=Lt(g.error,t)),n(g.loading)&&(e.loadingComp=Lt(g.loading,t),0===g.delay?e.loading=!0:l=setTimeout((function(){l=null,i(e.resolved)&&i(e.error)&&(e.loading=!0,f(!1))}),g.delay||200)),n(g.timeout)&&(u=setTimeout((function(){u=null,i(e.resolved)&&h(null)}),g.timeout)))),c=!1,e.loading?e.loadingComp:e.resolved}}(f,u),void 0===e))return function(e,t,o,i,n){var r=de();return r.asyncFactory=e,r.asyncMeta={data:t,context:o,children:i,tag:n},r}(f,t,a,c,l);t=t||{},ho(e),n(t.model)&&function(e,t){var o=e.model&&e.model.prop||"value",i=e.model&&e.model.event||"input";(t.attrs||(t.attrs={}))[o]=t.model.value;var r=t.on||(t.on={}),a=r[i],s=t.model.callback;n(a)?(Array.isArray(a)?-1===a.indexOf(s):a!==s)&&(r[i]=[s].concat(a)):r[i]=s}(e.options,t);var p=function(e,t,o,r){var a=t.options.props;if(i(a))return et(e,t,{},r);var s={},c=e.attrs,l=e.props;if(n(c)||n(l))for(var u in a){var d=x(u);tt(s,l,u,d,!0)||tt(s,c,u,d,!1)}return et(e,t,s,r)}(t,e,0,a);if(r(e.options.functional))return function(e,t,i,r,a){var s=e.options,c={},l=s.props;if(n(l))for(var u in l)c[u]=Ae(u,l,t||o);else n(i.attrs)&&$t(c,i.attrs),n(i.props)&&$t(c,i.props);var d=new Pt(i,c,a,r,e),f=s.render.call(null,d._c,d);if(f instanceof le)return Ot(f,i,d.parent,s,d);if(Array.isArray(f)){for(var p=ot(f)||[],h=new Array(p.length),g=0;g<p.length;g++)h[g]=Ot(p[g],i,d.parent,s,d);return h}}(e,p,t,a,c);var h=t.on;if(t.on=t.nativeOn,r(e.options.abstract)){var g=t.slot;t={},g&&(t.slot=g)}(function(e){for(var t=e.hook||(e.hook={}),o=0;o<Mt.length;o++){var i=Mt[o],n=t[i],r=jt[i];n===r||n&&n._merged||(t[i]=n?At(r,n):r)}})(t);var _=e.options.name||l,v=new le("vue-component-"+e.cid+(_?"-"+_:""),t,void 0,void 0,void 0,a,{Ctor:e,propsData:p,listeners:h,tag:l,children:c},f);return v}}}function At(e,t){var o=function(o,i){e(o,i),t(o,i)};return o._merged=!0,o}function It(e,t,o,c,l,u){return(Array.isArray(o)||a(o))&&(l=c,c=o,o=void 0),r(u)&&(l=2),function(e,t,o,a,c){if(n(o)&&n(o.__ob__))return de();n(o)&&n(o.is)&&(t=o.is);if(!t)return de();0;Array.isArray(a)&&"function"===typeof a[0]&&(o=o||{},o.scopedSlots={default:a[0]},a.length=0);2===c?a=ot(a):1===c&&(a=function(e){for(var t=0;t<e.length;t++)if(Array.isArray(e[t]))return Array.prototype.concat.apply([],e);return e}(a));var l,u;if("string"===typeof t){var d;u=e.$vnode&&e.$vnode.ns||L.getTagNamespace(t),l=L.isReservedTag(t)?new le(L.parsePlatformTagName(t),o,a,void 0,void 0,e):o&&o.pre||!n(d=Te(e.$options,"components",t))?new le(t,o,a,void 0,void 0,e):Tt(d,o,e,a,t)}else l=Tt(t,o,e,a);return Array.isArray(l)?l:n(l)?(n(u)&&function e(t,o,a){t.ns=o,"foreignObject"===t.tag&&(o=void 0,a=!0);if(n(t.children))for(var s=0,c=t.children.length;s<c;s++){var l=t.children[s];n(l.tag)&&(i(l.ns)||r(a)&&"svg"!==l.tag)&&e(l,o,a)}}(l,u),n(o)&&function(e){s(e.style)&&Ke(e.style);s(e.class)&&Ke(e.class)}(o),l):de()}(e,t,o,c,l)}var Ft,Et=null;function Lt(e,t){return(e.__esModule||ie&&"Module"===e[Symbol.toStringTag])&&(e=e.default),s(e)?t.extend(e):e}function Rt(e){return e.isComment&&e.asyncFactory}function Nt(e,t){Ft.$on(e,t)}function Ut(e,t){Ft.$off(e,t)}function Bt(e,t){var o=Ft;return function i(){var n=t.apply(null,arguments);null!==n&&o.$off(e,i)}}function zt(e,t,o){Ft=e,function(e,t,o,n,a,s){var c,l,u,d;for(c in e)l=e[c],u=t[c],d=Qe(c),i(l)||(i(u)?(i(l.fns)&&(l=e[c]=Ze(l,s)),r(d.once)&&(l=e[c]=a(d.name,l,d.capture)),o(d.name,l,d.capture,d.passive,d.params)):l!==u&&(u.fns=l,e[c]=u));for(c in t)i(e[c])&&(d=Qe(c),n(d.name,t[c],d.capture))}(t,o||{},Nt,Ut,Bt,e),Ft=void 0}var Vt=null;function Gt(e){while(e&&(e=e.$parent))if(e._inactive)return!0;return!1}function qt(e,t){if(t){if(e._directInactive=!1,Gt(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var o=0;o<e.$children.length;o++)qt(e.$children[o]);Ht(e,"activated")}}function Ht(e,t){se();var o=e.$options[t],i=t+" hook";if(o)for(var n=0,r=o.length;n<r;n++)Re(o[n],e,null,e,i);e._hasHookEvent&&e.$emit("hook:"+t),ce()}var Wt=[],Jt=[],Xt={},Yt=!1,Kt=!1,Qt=0;var Zt=Date.now;if(G&&!J){var eo=window.performance;eo&&"function"===typeof eo.now&&Zt()>document.createEvent("Event").timeStamp&&(Zt=function(){return eo.now()})}function to(){var e,t;for(Zt(),Kt=!0,Wt.sort((function(e,t){return e.id-t.id})),Qt=0;Qt<Wt.length;Qt++)e=Wt[Qt],e.before&&e.before(),t=e.id,Xt[t]=null,e.run();var o=Jt.slice(),i=Wt.slice();(function(){Qt=Wt.length=Jt.length=0,Xt={},Yt=Kt=!1})(),function(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,qt(e[t],!0)}(o),function(e){var t=e.length;while(t--){var o=e[t],i=o.vm;i._watcher===o&&i._isMounted&&!i._isDestroyed&&Ht(i,"updated")}}(i),ee&&L.devtools&&ee.emit("flush")}var oo=0,io=function(e,t,o,i,n){this.vm=e,n&&(e._watcher=this),e._watchers.push(this),i?(this.deep=!!i.deep,this.user=!!i.user,this.lazy=!!i.lazy,this.sync=!!i.sync,this.before=i.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=o,this.id=++oo,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new oe,this.newDepIds=new oe,this.expression="","function"===typeof t?this.getter=t:(this.getter=function(e){if(!B.test(e)){var t=e.split(".");return function(e){for(var o=0;o<t.length;o++){if(!e)return;e=e[t[o]]}return e}}}(t),this.getter||(this.getter=$)),this.value=this.lazy?void 0:this.get()};io.prototype.get=function(){var e;se(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(Lo){if(!this.user)throw Lo;Le(Lo,t,'getter for watcher "'+this.expression+'"')}finally{this.deep&&Ke(e),ce(),this.cleanupDeps()}return e},io.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},io.prototype.cleanupDeps=function(){var e=this.deps.length;while(e--){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var o=this.depIds;this.depIds=this.newDepIds,this.newDepIds=o,this.newDepIds.clear(),o=this.deps,this.deps=this.newDeps,this.newDeps=o,this.newDeps.length=0},io.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():function(e){var t=e.id;if(null==Xt[t]){if(Xt[t]=!0,Kt){var o=Wt.length-1;while(o>Qt&&Wt[o].id>e.id)o--;Wt.splice(o+1,0,e)}else Wt.push(e);Yt||(Yt=!0,Xe(to))}}(this)},io.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||s(e)||this.deep){var t=this.value;if(this.value=e,this.user)try{this.cb.call(this.vm,e,t)}catch(Lo){Le(Lo,this.vm,'callback for watcher "'+this.expression+'"')}else this.cb.call(this.vm,e,t)}}},io.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},io.prototype.depend=function(){var e=this.deps.length;while(e--)this.deps[e].depend()},io.prototype.teardown=function(){if(this.active){this.vm._isBeingDestroyed||m(this.vm._watchers,this);var e=this.deps.length;while(e--)this.deps[e].removeSub(this);this.active=!1}};var no={enumerable:!0,configurable:!0,get:$,set:$};function ro(e,t,o){no.get=function(){return this[t][o]},no.set=function(e){this[t][o]=e},Object.defineProperty(e,o,no)}function ao(e){e._watchers=[];var t=e.$options;t.props&&function(e,t){var o=e.$options.propsData||{},i=e._props={},n=e.$options._propKeys=[],r=!e.$parent;r||_e(!1);var a=function(r){n.push(r);var a=Ae(r,t,o,e);we(i,r,a),r in e||ro(e,"_props",r)};for(var s in t)a(s);_e(!0)}(e,t.props),t.methods&&function(e,t){e.$options.props;for(var o in t)e[o]="function"!==typeof t[o]?$:D(t[o],e)}(e,t.methods),t.data?function(e){var t=e.$options.data;t=e._data="function"===typeof t?function(e,t){se();try{return e.call(t,t)}catch(Lo){return Le(Lo,t,"data()"),{}}finally{ce()}}(t,e):t||{},l(t)||(t={});var o=Object.keys(t),i=e.$options.props,n=(e.$options.methods,o.length);while(n--){var r=o[n];0,i&&v(i,r)||N(r)||ro(e,"_data",r)}be(t,!0)}(e):be(e._data={},!0),t.computed&&function(e,t){var o=e._computedWatchers=Object.create(null),i=Z();for(var n in t){var r=t[n],a="function"===typeof r?r:r.get;0,i||(o[n]=new io(e,a||$,$,so)),n in e||co(e,n,r)}}(e,t.computed),t.watch&&t.watch!==K&&function(e,t){for(var o in t){var i=t[o];if(Array.isArray(i))for(var n=0;n<i.length;n++)fo(e,o,i[n]);else fo(e,o,i)}}(e,t.watch)}var so={lazy:!0};function co(e,t,o){var i=!Z();"function"===typeof o?(no.get=i?lo(t):uo(o),no.set=$):(no.get=o.get?i&&!1!==o.cache?lo(t):uo(o.get):$,no.set=o.set||$),Object.defineProperty(e,t,no)}function lo(e){return function(){var t=this._computedWatchers&&this._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),ae.SharedObject.target&&t.depend(),t.value}}function uo(e){return function(){return e.call(this,this)}}function fo(e,t,o,i){return l(o)&&(i=o,o=o.handler),"string"===typeof o&&(o=e[o]),e.$watch(t,o,i)}var po=0;function ho(e){var t=e.options;if(e.super){var o=ho(e.super),i=e.superOptions;if(o!==i){e.superOptions=o;var n=function(e){var t,o=e.options,i=e.sealedOptions;for(var n in o)o[n]!==i[n]&&(t||(t={}),t[n]=o[n]);return t}(e);n&&P(e.extendOptions,n),t=e.options=Me(o,e.extendOptions),t.name&&(t.components[t.name]=e)}}return t}function go(e){this._init(e)}function mo(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var o=this,i=o.cid,n=e._Ctor||(e._Ctor={});if(n[i])return n[i];var r=e.name||o.options.name;var a=function(e){this._init(e)};return a.prototype=Object.create(o.prototype),a.prototype.constructor=a,a.cid=t++,a.options=Me(o.options,e),a["super"]=o,a.options.props&&function(e){var t=e.options.props;for(var o in t)ro(e.prototype,"_props",o)}(a),a.options.computed&&function(e){var t=e.options.computed;for(var o in t)co(e.prototype,o,t[o])}(a),a.extend=o.extend,a.mixin=o.mixin,a.use=o.use,F.forEach((function(e){a[e]=o[e]})),r&&(a.options.components[r]=a),a.superOptions=o.options,a.extendOptions=e,a.sealedOptions=P({},a.options),n[i]=a,a}}function _o(e){return e&&(e.Ctor.options.name||e.tag)}function vo(e,t){return Array.isArray(e)?e.indexOf(t)>-1:"string"===typeof e?e.split(",").indexOf(t)>-1:!!function(e){return"[object RegExp]"===c.call(e)}(e)&&e.test(t)}function yo(e,t){var o=e.cache,i=e.keys,n=e._vnode;for(var r in o){var a=o[r];if(a){var s=_o(a.componentOptions);s&&!t(s)&&bo(o,r,i,n)}}}function bo(e,t,o,i){var n=e[t];!n||i&&n.tag===i.tag||n.componentInstance.$destroy(),e[t]=null,m(o,t)}(function(e){e.prototype._init=function(e){var t=this;t._uid=po++,t._isVue=!0,e&&e._isComponent?function(e,t){var o=e.$options=Object.create(e.constructor.options),i=t._parentVnode;o.parent=t.parent,o._parentVnode=i;var n=i.componentOptions;o.propsData=n.propsData,o._parentListeners=n.listeners,o._renderChildren=n.children,o._componentTag=n.tag,t.render&&(o.render=t.render,o.staticRenderFns=t.staticRenderFns)}(t,e):t.$options=Me(ho(t.constructor),e||{},t),t._renderProxy=t,t._self=t,function(e){var t=e.$options,o=t.parent;if(o&&!t.abstract){while(o.$options.abstract&&o.$parent)o=o.$parent;o.$children.push(e)}e.$parent=o,e.$root=o?o.$root:e,e.$children=[],e.$refs={},e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}(t),function(e){e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&zt(e,t)}(t),function(e){e._vnode=null,e._staticTrees=null;var t=e.$options,i=e.$vnode=t._parentVnode,n=i&&i.context;e.$slots=st(t._renderChildren,n),e.$scopedSlots=o,e._c=function(t,o,i,n){return It(e,t,o,i,n,!1)},e.$createElement=function(t,o,i,n){return It(e,t,o,i,n,!0)};var r=i&&i.data;we(e,"$attrs",r&&r.attrs||o,null,!0),we(e,"$listeners",t._parentListeners||o,null,!0)}(t),Ht(t,"beforeCreate"),!t._$fallback&&rt(t),ao(t),!t._$fallback&&nt(t),!t._$fallback&&Ht(t,"created"),t.$options.el&&t.$mount(t.$options.el)}})(go),function(e){var t={get:function(){return this._data}},o={get:function(){return this._props}};Object.defineProperty(e.prototype,"$data",t),Object.defineProperty(e.prototype,"$props",o),e.prototype.$set=Se,e.prototype.$delete=ke,e.prototype.$watch=function(e,t,o){if(l(t))return fo(this,e,t,o);o=o||{},o.user=!0;var i=new io(this,e,t,o);if(o.immediate)try{t.call(this,i.value)}catch(n){Le(n,this,'callback for immediate watcher "'+i.expression+'"')}return function(){i.teardown()}}}(go),function(e){var t=/^hook:/;e.prototype.$on=function(e,o){var i=this;if(Array.isArray(e))for(var n=0,r=e.length;n<r;n++)i.$on(e[n],o);else(i._events[e]||(i._events[e]=[])).push(o),t.test(e)&&(i._hasHookEvent=!0);return i},e.prototype.$once=function(e,t){var o=this;function i(){o.$off(e,i),t.apply(o,arguments)}return i.fn=t,o.$on(e,i),o},e.prototype.$off=function(e,t){var o=this;if(!arguments.length)return o._events=Object.create(null),o;if(Array.isArray(e)){for(var i=0,n=e.length;i<n;i++)o.$off(e[i],t);return o}var r,a=o._events[e];if(!a)return o;if(!t)return o._events[e]=null,o;var s=a.length;while(s--)if(r=a[s],r===t||r.fn===t){a.splice(s,1);break}return o},e.prototype.$emit=function(e){var t=this,o=t._events[e];if(o){o=o.length>1?C(o):o;for(var i=C(arguments,1),n='event handler for "'+e+'"',r=0,a=o.length;r<a;r++)Re(o[r],t,i,t,n)}return t}}(go),function(e){e.prototype._update=function(e,t){var o=this,i=o.$el,n=o._vnode,r=function(e){var t=Vt;return Vt=e,function(){Vt=t}}(o);o._vnode=e,o.$el=n?o.__patch__(n,e):o.__patch__(o.$el,e,t,!1),r(),i&&(i.__vue__=null),o.$el&&(o.$el.__vue__=o),o.$vnode&&o.$parent&&o.$vnode===o.$parent._vnode&&(o.$parent.$el=o.$el)},e.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){Ht(e,"beforeDestroy"),e._isBeingDestroyed=!0;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||m(t.$children,e),e._watcher&&e._watcher.teardown();var o=e._watchers.length;while(o--)e._watchers[o].teardown();e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),Ht(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}(go),function(e){Ct(e.prototype),e.prototype.$nextTick=function(e){return Xe(e,this)},e.prototype._render=function(){var e,t=this,o=t.$options,i=o.render,n=o._parentVnode;n&&(t.$scopedSlots=lt(n.data.scopedSlots,t.$slots,t.$scopedSlots)),t.$vnode=n;try{Et=t,e=i.call(t._renderProxy,t.$createElement)}catch(Lo){Le(Lo,t,"render"),e=t._vnode}finally{Et=null}return Array.isArray(e)&&1===e.length&&(e=e[0]),e instanceof le||(e=de()),e.parent=n,e}}(go);var wo=[String,RegExp,Array],So={name:"keep-alive",abstract:!0,props:{include:wo,exclude:wo,max:[String,Number]},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)bo(this.cache,e,this.keys)},mounted:function(){var e=this;this.$watch("include",(function(t){yo(e,(function(e){return vo(t,e)}))})),this.$watch("exclude",(function(t){yo(e,(function(e){return!vo(t,e)}))}))},render:function(){var e=this.$slots.default,t=function(e){if(Array.isArray(e))for(var t=0;t<e.length;t++){var o=e[t];if(n(o)&&(n(o.componentOptions)||Rt(o)))return o}}(e),o=t&&t.componentOptions;if(o){var i=_o(o),r=this.include,a=this.exclude;if(r&&(!i||!vo(r,i))||a&&i&&vo(a,i))return t;var s=this.cache,c=this.keys,l=null==t.key?o.Ctor.cid+(o.tag?"::"+o.tag:""):t.key;s[l]?(t.componentInstance=s[l].componentInstance,m(c,l),c.push(l)):(s[l]=t,c.push(l),this.max&&c.length>parseInt(this.max)&&bo(s,c[0],c,this._vnode)),t.data.keepAlive=!0}return t||e&&e[0]}},ko={KeepAlive:So};(function(e){var t={get:function(){return L}};Object.defineProperty(e,"config",t),e.util={warn:ne,extend:P,mergeOptions:Me,defineReactive:we},e.set=Se,e.delete=ke,e.nextTick=Xe,e.observable=function(e){return be(e),e},e.options=Object.create(null),F.forEach((function(t){e.options[t+"s"]=Object.create(null)})),e.options._base=e,P(e.options.components,ko),function(e){e.use=function(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var o=C(arguments,1);return o.unshift(this),"function"===typeof e.install?e.install.apply(e,o):"function"===typeof e&&e.apply(null,o),t.push(e),this}}(e),function(e){e.mixin=function(e){return this.options=Me(this.options,e),this}}(e),mo(e),function(e){F.forEach((function(t){e[t]=function(e,o){return o?("component"===t&&l(o)&&(o.name=o.name||e,o=this.options._base.extend(o)),"directive"===t&&"function"===typeof o&&(o={bind:o,update:o}),this.options[t+"s"][e]=o,o):this.options[t+"s"][e]}}))}(e)})(go),Object.defineProperty(go.prototype,"$isServer",{get:Z}),Object.defineProperty(go.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(go,"FunctionalRenderContext",{value:Pt}),go.version="2.6.11";var xo="[object Array]",Do="[object Object]";function Co(e,t){var o={};return function e(t,o){if(t===o)return;var i=Oo(t),n=Oo(o);if(i==Do&&n==Do){if(Object.keys(t).length>=Object.keys(o).length)for(var r in o){var a=t[r];void 0===a?t[r]=null:e(a,o[r])}}else i==xo&&n==xo&&t.length>=o.length&&o.forEach((function(o,i){e(t[i],o)}))}(e,t),function e(t,o,i,n){if(t===o)return;var r=Oo(t),a=Oo(o);if(r==Do)if(a!=Do||Object.keys(t).length<Object.keys(o).length)Po(n,i,t);else{var s=function(r){var a=t[r],s=o[r],c=Oo(a),l=Oo(s);if(c!=xo&&c!=Do)a!==o[r]&&function(e,t){if(("[object Null]"===e||"[object Undefined]"===e)&&("[object Null]"===t||"[object Undefined]"===t))return!1;return!0}(c,l)&&Po(n,(""==i?"":i+".")+r,a);else if(c==xo)l!=xo||a.length<s.length?Po(n,(""==i?"":i+".")+r,a):a.forEach((function(t,o){e(t,s[o],(""==i?"":i+".")+r+"["+o+"]",n)}));else if(c==Do)if(l!=Do||Object.keys(a).length<Object.keys(s).length)Po(n,(""==i?"":i+".")+r,a);else for(var u in a)e(a[u],s[u],(""==i?"":i+".")+r+"."+u,n)};for(var c in t)s(c)}else r==xo?a!=xo||t.length<o.length?Po(n,i,t):t.forEach((function(t,r){e(t,o[r],i+"["+r+"]",n)})):Po(n,i,t)}(e,t,"",o),o}function Po(e,t,o){e[t]=o}function Oo(e){return Object.prototype.toString.call(e)}function $o(e){if(e.__next_tick_callbacks&&e.__next_tick_callbacks.length){if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"单商户V5",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var t=e.$scope;console.log("["+ +new Date+"]["+(t.is||t.route)+"]["+e._uid+"]:flushCallbacks["+e.__next_tick_callbacks.length+"]")}var o=e.__next_tick_callbacks.slice(0);e.__next_tick_callbacks.length=0;for(var i=0;i<o.length;i++)o[i]()}}function jo(e,t){if(!e.__next_tick_pending&&!function(e){return Wt.find((function(t){return e._watcher===t}))}(e)){if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"单商户V5",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var o=e.$scope;console.log("["+ +new Date+"]["+(o.is||o.route)+"]["+e._uid+"]:nextVueTick")}return Xe(t,e)}if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"单商户V5",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var i=e.$scope;console.log("["+ +new Date+"]["+(i.is||i.route)+"]["+e._uid+"]:nextMPTick")}var n;if(e.__next_tick_callbacks||(e.__next_tick_callbacks=[]),e.__next_tick_callbacks.push((function(){if(t)try{t.call(e)}catch(Lo){Le(Lo,e,"nextTick")}else n&&n(e)})),!t&&"undefined"!==typeof Promise)return new Promise((function(e){n=e}))}function Mo(e,t){return t&&(t._isVue||t.__v_isMPComponent)?{}:t}function To(){}function Ao(e){return Array.isArray(e)?function(e){for(var t,o="",i=0,r=e.length;i<r;i++)n(t=Ao(e[i]))&&""!==t&&(o&&(o+=" "),o+=t);return o}(e):s(e)?function(e){var t="";for(var o in e)e[o]&&(t&&(t+=" "),t+=o);return t}(e):"string"===typeof e?e:""}var Io=y((function(e){var t={},o=/:(.+)/;return e.split(/;(?![^(]*\))/g).forEach((function(e){if(e){var i=e.split(o);i.length>1&&(t[i[0].trim()]=i[1].trim())}})),t}));var Fo=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];var Eo=["onLaunch","onShow","onHide","onUniNViewMessage","onPageNotFound","onThemeChange","onError","onUnhandledRejection","onInit","onLoad","onReady","onUnload","onPullDownRefresh","onReachBottom","onTabItemTap","onAddToFavorites","onShareTimeline","onShareAppMessage","onResize","onPageScroll","onNavigationBarButtonTap","onBackPress","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputClicked","onUploadDouyinVideo","onNFCReadMessage","onPageShow","onPageHide","onPageResize"];go.prototype.__patch__=function(e,t){var o=this;if(null!==t&&("page"===this.mpType||"component"===this.mpType)){var i=this.$scope,n=Object.create(null);try{n=function(e){var t=Object.create(null),o=[].concat(Object.keys(e._data||{}),Object.keys(e._computedWatchers||{}));o.reduce((function(t,o){return t[o]=e[o],t}),t);var i=e.__composition_api_state__||e.__secret_vfa_state__,n=i&&i.rawBindings;return n&&Object.keys(n).forEach((function(o){t[o]=e[o]})),Object.assign(t,e.$mp.data||{}),Array.isArray(e.$options.behaviors)&&-1!==e.$options.behaviors.indexOf("uni://form-field")&&(t["name"]=e.name,t["value"]=e.value),JSON.parse(JSON.stringify(t,Mo))}(this)}catch(s){console.error(s)}n.__webviewId__=i.data.__webviewId__;var r=Object.create(null);Object.keys(n).forEach((function(e){r[e]=i.data[e]}));var a=!1===this.$shouldDiffData?n:Co(n,r);Object.keys(a).length?(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"单商户V5",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG&&console.log("["+ +new Date+"]["+(i.is||i.route)+"]["+this._uid+"]差量更新",JSON.stringify(a)),this.__next_tick_pending=!0,i.setData(a,(function(){o.__next_tick_pending=!1,$o(o)}))):$o(this)}},go.prototype.$mount=function(e,t){return function(e,t,o){return e.mpType?("app"===e.mpType&&(e.$options.render=To),e.$options.render||(e.$options.render=To),!e._$fallback&&Ht(e,"beforeMount"),new io(e,(function(){e._update(e._render(),o)}),$,{before:function(){e._isMounted&&!e._isDestroyed&&Ht(e,"beforeUpdate")}},!0),o=!1,e):e}(this,0,t)},function(e){var t=e.extend;e.extend=function(e){e=e||{};var o=e.methods;return o&&Object.keys(o).forEach((function(t){-1!==Eo.indexOf(t)&&(e[t]=o[t],delete o[t])})),t.call(this,e)};var o=e.config.optionMergeStrategies,i=o.created;Eo.forEach((function(e){o[e]=i})),e.prototype.__lifecycle_hooks__=Eo}(go),function(e){e.config.errorHandler=function(t,o,i){e.util.warn("Error in "+i+': "'+t.toString()+'"',o),console.error(t);var n="function"===typeof getApp&&getApp();n&&n.onError&&n.onError(t)};var t=e.prototype.$emit;e.prototype.$emit=function(e){if(this.$scope&&e){var o=this.$scope["_triggerEvent"]||this.$scope["triggerEvent"];if(o)try{o.call(this.$scope,e,{__args__:C(arguments,1)})}catch(i){}}return t.apply(this,arguments)},e.prototype.$nextTick=function(e){return jo(this,e)},Fo.forEach((function(t){e.prototype[t]=function(e){return this.$scope&&this.$scope[t]?this.$scope[t](e):"undefined"!==typeof my?"createSelectorQuery"===t?my.createSelectorQuery(e):"createIntersectionObserver"===t?my.createIntersectionObserver(e):void 0:void 0}})),e.prototype.__init_provide=nt,e.prototype.__init_injections=rt,e.prototype.__call_hook=function(e,t){var o=this;se();var i,n=o.$options[e],r=e+" hook";if(n)for(var a=0,s=n.length;a<s;a++)i=Re(n[a],o,t?[t]:null,o,r);return o._hasHookEvent&&o.$emit("hook:"+e,t),ce(),i},e.prototype.__set_model=function(t,o,i,n){Array.isArray(n)&&(-1!==n.indexOf("trim")&&(i=i.trim()),-1!==n.indexOf("number")&&(i=this._n(i))),t||(t=this),e.set(t,o,i)},e.prototype.__set_sync=function(t,o,i){t||(t=this),e.set(t,o,i)},e.prototype.__get_orig=function(e){return l(e)&&e["$orig"]||e},e.prototype.__get_value=function(e,t){return function e(t,o){var i=o.split("."),n=i[0];return 0===n.indexOf("__$n")&&(n=parseInt(n.replace("__$n",""))),1===i.length?t[n]:e(t[n],i.slice(1).join("."))}(t||this,e)},e.prototype.__get_class=function(e,t){return function(e,t){return n(e)||n(t)?function(e,t){return e?t?e+" "+t:e:t||""}(e,Ao(t)):""}(t,e)},e.prototype.__get_style=function(e,t){if(!e&&!t)return"";var o=function(e){return Array.isArray(e)?O(e):"string"===typeof e?Io(e):e}(e),i=t?P(t,o):o;return Object.keys(i).map((function(e){return x(e)+":"+i[e]})).join(";")},e.prototype.__map=function(e,t){var o,i,n,r,a;if(Array.isArray(e)){for(o=new Array(e.length),i=0,n=e.length;i<n;i++)o[i]=t(e[i],i);return o}if(s(e)){for(r=Object.keys(e),o=Object.create(null),i=0,n=r.length;i<n;i++)a=r[i],o[a]=t(e[a],a,i);return o}if("number"===typeof e){for(o=new Array(e),i=0,n=e;i<n;i++)o[i]=t(i,i);return o}return[]}}(go),t["default"]=go}.call(this,o("0ee4"))},"334c":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"订单详情"}},"33ea":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={down:{textInOffset:"下拉刷新",textOutOffset:"释放更新",textLoading:"加载中 ...",offset:80,native:!1},up:{textLoading:"加载中 ...",textNoMore:"",offset:80,isBounce:!1,toTop:{src:"http://www.mescroll.com/img/mescroll-totop.png?v=1",offset:1e3,right:20,bottom:120,width:72},empty:{use:!0,icon:"http://www.mescroll.com/img/mescroll-empty.png?v=1",tip:"~ 暂无相关数据 ~"}}};t.default=i},3464:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"拼团分享"}},"347a":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"余额明细"}},"348d":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"",select:"选择",params:"参数",service:"商品服务",allGoods:"全部商品",image:"图片",video:"视频"}},"34b1":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"账号注销"}},"34cf":function(e,t,o){var i=o("ed45"),n=o("7172"),r=o("6382"),a=o("dd3e");e.exports=function(e,t){return i(e)||n(e,t)||r(e,t)||a()},e.exports.__esModule=!0,e.exports["default"]=e.exports},"34d0":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"",select:"选择",params:"参数",service:"商品服务",allGoods:"全部商品",image:"图片",video:"视频"}},3500:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"账户列表",name:"姓名",accountType:"账号类型",mobilePhone:"手机号码",withdrawalAccount:"提现账号",bankInfo:"支行信息",newAddAccount:"新增账户",del:"删除",update:"修改",emptyText:"当前暂无账户"}},"36ff":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我要评价"}},"37f2":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的礼品",emptyTips:"暂无礼品"}},"387c":function(e,t,o){"use strict";(function(e){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("3b2d")),r=i(o("3240")),a=i(o("8f59")),s=i(o("f944")),c=i(o("1757"));r.default.use(a.default);var l=new a.default.Store({state:{token:null,siteInfo:null,memberInfo:null,tabBarList:"",siteState:1,themeStyle:"",addonIsExist:{bundling:0,coupon:0,discount:0,fenxiao:0,gift:0,groupbuy:0,manjian:0,memberconsume:0,memberrecharge:0,memberregister:0,membersignin:0,memberwithdraw:0,memberrecommend:0,pintuan:0,pointexchange:0,seckill:0,store:0,topic:0,bargain:0,membercancel:0,servicer:0,supermember:0,giftcard:0,divideticket:0,scenefestival:0,birthdaygift:0,pinfan:0,form:0},authInfo:{},flRefresh:0,location:null,defaultImg:{goods:"",head:"",store:"",article:""},cartList:{},cartIds:[],cartNumber:0,cartMoney:0,cartChange:0,wechatConfigStatus:0,bottomNavHidden:!1,globalStoreConfig:null,globalStoreInfo:null,defaultStoreInfo:null,cartPosition:null,componentRefresh:0,servicerConfig:null,diySeckillInterval:0,diyGroupPositionObj:{},diyGroupShowModule:"",tabBarHeight:"56px",mapConfig:{tencent_map_key:"",wap_is_open:1,wap_valid_time:0},copyright:null,initStatus:!1,offlineWhiteList:["pages/order/payment","pages/order/list","pages/order/detail"]},mutations:{setDiyGroupShowModule:function(e,t){e.diyGroupShowModule=t},setDiyGroupPositionObj:function(e,t){e.diyGroupPositionObj=Object.assign({},e.diyGroupPositionObj,t)},setSiteState:function(e,t){e.siteState=t},setThemeStyle:function(t,o){t.themeStyle=o,e.setStorageSync("themeStyle",o)},setTabBarList:function(e,t){e.tabBarList=t},setAddonIsExist:function(t,o){t.addonIsExist=o,e.setStorageSync("addonIsExist",o)},setToken:function(t,o){t.token=o,o?e.setStorageSync("token",o):e.removeStorageSync("token")},setAuthinfo:function(e,t){e.authInfo=t},setflRefresh:function(e,t){e.flRefresh=t},setLocation:function(t,o){var i=new Date;i.setSeconds(60*t.mapConfig.wap_valid_time),o.valid_time=i.getTime()/1e3,t.location=o,e.setStorageSync("location",o)},setDefaultImg:function(t,o){t.defaultImg=o,e.setStorageSync("defaultImg",o)},setSiteInfo:function(t,o){t.siteInfo=o,e.setStorageSync("siteInfo",o)},setCartChange:function(e){e.cartChange+=1},setBottomNavHidden:function(e,t){e.bottomNavHidden=t},setGlobalStoreConfig:function(t,o){t.globalStoreConfig=o,e.setStorageSync("globalStoreConfig",o)},setGlobalStoreInfo:function(t,o){t.globalStoreInfo=o,e.setStorageSync("globalStoreInfo",o)},setDefaultStoreInfo:function(t,o){t.defaultStoreInfo=o,e.setStorageSync("defaultStoreInfo",o)},setCartPosition:function(e,t){e.cartPosition=t},setComponentRefresh:function(e){e.componentRefresh+=1},setServicerConfig:function(t,o){t.servicerConfig=o,e.setStorageSync("servicerConfig",o)},setDiySeckillInterval:function(e,t){e.diySeckillInterval=t},setTabBarHeight:function(e,t){e.tabBarHeight=t},setMapConfig:function(t,o){t.mapConfig=o,e.setStorageSync("mapConfig",o)},setCopyright:function(t,o){t.copyright=o,e.setStorageSync("copyright",o)},setMemberInfo:function(t,o){o&&0==o.status&&(o=null),t.memberInfo=o,o?e.setStorageSync("memberInfo",o):(e.removeStorageSync("memberInfo"),this.commit("setToken",""),this.dispatch("emptyCart"))},setCartNumber:function(e,t){e.cartNumber=t},setCartList:function(e,t){e.cartList=t},setCartIds:function(e,t){e.cartIds=t},setCartMoney:function(e,t){e.cartMoney=t},setInitStatus:function(e,t){e.initStatus=t},setWechatConfigStatus:function(e,t){e.wechatConfigStatus=t}},actions:{init:function(){var e=this;return new Promise((function(t,o){s.default.sendRequest({url:"/api/config/init",success:function(o){var i=o.data;i&&(e.commit("setThemeStyle",c.default[i.style_theme.name]),e.commit("setTabBarList",i.diy_bottom_nav),e.commit("setAddonIsExist",i.addon_is_exist),e.commit("setDefaultImg",i.default_img),e.commit("setSiteInfo",i.site_info),e.commit("setServicerConfig",i.servicer),e.commit("setCopyright",i.copyright),e.commit("setMapConfig",i.map_config),e.commit("setGlobalStoreConfig",i.store_config),e.commit("setWechatConfigStatus",i.wechat_config_status),i.store_info?e.commit("setDefaultStoreInfo",i.store_info):(e.commit("setDefaultStoreInfo",null),e.commit("setGlobalStoreInfo",null)),e.commit("setInitStatus",!0),t(i))}})}))},getCartNumber:function(){var e=this;s.default.sendRequest({url:"/api/cart/lists",data:{},success:function(t){if(0==t.code){var o={},i=[],r=0,a=0;if(t.data.length)for(var s in t.data.forEach((function(e){var t={cart_id:e.cart_id,goods_id:e.goods_id,sku_id:e.sku_id,num:e.num,discount_price:e.discount_price,min_buy:e.min_buy,stock:e.stock};o["goods_"+t.goods_id]||(o["goods_"+t.goods_id]={}),o["goods_"+t.goods_id]["max_buy"]=e.max_buy,o["goods_"+t.goods_id]["goods_name"]=e.goods_name,o["goods_"+t.goods_id]["sku_"+t.sku_id]=t,i.push(t.cart_id)})),o){var c=0,l=0;for(var u in o[s]){var d=o[s][u];"object"==(0,n.default)(d)&&(c+=d.num,l+=parseFloat(d.discount_price)*parseInt(d.num))}o[s].num=c,o[s].total_money=l,a+=c,r+=l}e.commit("setCartList",o),e.commit("setCartIds",i),e.commit("setCartNumber",a),e.commit("setCartMoney",r)}}})},emptyCart:function(){this.commit("setCartList",{}),this.commit("setCartIds",[]),this.commit("setCartNumber",0),this.commit("setCartMoney",0)},cartCalculate:function(){var e=[],t=0,o=0;for(var i in this.state.cartList){var r=this.state.cartList[i],a=0,s=0;for(var c in r)"object"==(0,n.default)(r[c])&&(a+=r[c].num,s+=parseFloat(r[c].discount_price)*parseInt(r[c].num),e.push(r[c].cart_id));r.num=a,r.total_money=s,o+=a,t+=s}this.commit("setCartNumber",o),this.commit("setCartMoney",t),this.commit("setCartIds",e)}}}),u=l;t.default=u}).call(this,o("df3c")["default"])},"3b2d":function(e,t){function o(t){return e.exports=o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports["default"]=e.exports,o(t)}e.exports=o,e.exports.__esModule=!0,e.exports["default"]=e.exports},"3df4":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=52.35987755982988,n=3.141592653589793,r=6378245,a=.006693421622965943;function s(e,t){var o=2*e-100+3*t+.2*t*t+.1*e*t+.2*Math.sqrt(Math.abs(e));return o+=2*(20*Math.sin(6*e*n)+20*Math.sin(2*e*n))/3,o+=2*(20*Math.sin(t*n)+40*Math.sin(t/3*n))/3,o+=2*(160*Math.sin(t/12*n)+320*Math.sin(t*n/30))/3,o}function c(e,t){var o=300+e+2*t+.1*e*e+.1*e*t+.1*Math.sqrt(Math.abs(e));return o+=2*(20*Math.sin(6*e*n)+20*Math.sin(2*e*n))/3,o+=2*(20*Math.sin(e*n)+40*Math.sin(e/3*n))/3,o+=2*(150*Math.sin(e/12*n)+300*Math.sin(e/30*n))/3,o}function l(e,t){return e<72.004||e>137.8347||t<.8293||t>55.8271||!1}var u={bd09togcj02:function(e,t){var o=52.35987755982988,i=e-.0065,n=t-.006,r=Math.sqrt(i*i+n*n)-2e-5*Math.sin(n*o),a=Math.atan2(n,i)-3e-6*Math.cos(i*o),s=r*Math.cos(a),c=r*Math.sin(a);return[s,c]},gcj02tobd09:function(e,t){var o=Math.sqrt(e*e+t*t)+2e-5*Math.sin(t*i),n=Math.atan2(t,e)+3e-6*Math.cos(e*i),r=o*Math.cos(n)+.0065,a=o*Math.sin(n)+.006;return[r,a]},wgs84togcj02:function(e,t){if(l(e,t))return[e,t];var o=s(e-105,t-35),i=c(e-105,t-35),u=t/180*n,d=Math.sin(u);d=1-a*d*d;var f=Math.sqrt(d);o=180*o/(r*(1-a)/(d*f)*n),i=180*i/(r/f*Math.cos(u)*n);var p=t+o,h=e+i;return[h,p]},gcj02towgs84:function(e,t){if(l(e,t))return[e,t];var o=s(e-105,t-35),i=c(e-105,t-35),u=t/180*n,d=Math.sin(u);d=1-a*d*d;var f=Math.sqrt(d);return o=180*o/(r*(1-a)/(d*f)*n),i=180*i/(r/f*Math.cos(u)*n),mglat=t+o,mglng=e+i,[2*e-mglng,2*t-mglat]}};t.default=u},"419e":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"退款",checkDetail:"查看详情",emptyTips:"暂无退款记录"}},"435b":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"订单列表",emptyTips:"暂无相关订单",all:"全部",waitPay:"待付款",readyDelivery:"待发货",waitDelivery:"待收货",waitEvaluate:"待评价",waitUse:"待使用",update:"释放刷新",updateIng:"加载中...",toLogin:"去登录"}},4397:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={tabBar:{home:"首页",category:"分类",cart:"购物车",member:"个人中心"},common:{name:"中文",mescrollTextInOffset:"下拉刷新",mescrollTextOutOffset:"释放更新",mescrollEmpty:"暂无相关数据",goodsRecommendTitle:"猜你喜欢",currencySymbol:"¥",submit:"提交"}}},"43bb":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"邀请好友"}},"47a9":function(e,t){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports["default"]=e.exports},4893:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"会员中心",login:"立即登录",loginTpis:"登录体验更多功能",memberLevel:"会员等级",moreAuthority:"更多权限",allOrders:"全部订单",seeAllOrders:"查看全部订单",waitPay:"待付款",readyDelivery:"待发货",waitDelivery:"待收货",refunding:"退款",sign:"签到",personInfo:"个人资料",receivingAddress:"收货地址",accountList:"账户列表",couponList:"优惠券",mySpellList:"我的拼单",myBargain:"我的砍价",virtualCode:"虚拟码",winningRecord:"我的礼品",myCollection:"我的关注",myTracks:"我的足迹",pintuanOrder:"拼团订单",yushouOrder:"预售订单",verification:"核销台",message:"我的消息",exchangeOrder:"积分兑换",balance:"余额",point:"积分",coupon:"优惠券",memberRecommend:"邀请有礼",myPresale:"我的预售",myGiftcard:"我的礼品卡",myDivideticket:"我的瓜分券",myRebate:"拼团返利",myHongbao:"我的红包列表",myBlindBox:"我的盲盒"}},"49d3":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"拼团专区"}},"4a7b":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"提现详情"}},"4c35":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"商品找不到"}},"4e00":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"砍价详情"}},"50f4":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"专题活动详情"}},"50fa":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"组合套餐"}},5176:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"订单详情"}},5686:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"帮助详情"}},"57c0":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"待付款订单"}},"57df":function(e){e.exports=JSON.parse('{"uni-datetime-picker.selectDate":"选择日期","uni-datetime-picker.selectTime":"选择时间","uni-datetime-picker.selectDateTime":"选择日期时间","uni-datetime-picker.startDate":"开始日期","uni-datetime-picker.endDate":"结束日期","uni-datetime-picker.startTime":"开始时间","uni-datetime-picker.endTime":"结束时间","uni-datetime-picker.ok":"确定","uni-datetime-picker.clear":"清除","uni-datetime-picker.cancel":"取消","uni-calender.SUN":"日","uni-calender.MON":"一","uni-calender.TUE":"二","uni-calender.WED":"三","uni-calender.THU":"四","uni-calender.FRI":"五","uni-calender.SAT":"六"}')},"5c25":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"专题活动列表"}},"5c56":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},"5ebd":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"会员等级",defaultLevelTips:"您已经是最高级别的会员了！",tag:"专属标签",tagDesc:"标签达人",discount:"专享折扣",discountDesc:"专享{0}折",service:"优质服务",serviceDesc:"360度全方位",memberLevel:"会员等级",condition:"条件",equity:"权益",giftPackage:"升级礼包",levelExplain:"等级说明",upgradeTips:"升级会员，享专属权益"}},"5f56":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的优惠券"}},"5f8f":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"个人资料",headImg:"头像",account:"账号",username:"用户名",nickname:"昵称",realName:"真实姓名",sex:"性别",birthday:"生日",password:"密码",paypassword:"支付密码",mobilePhone:"手机",bindMobile:"绑定手机",cancellation:"注销账号",lang:"语言",logout:"退出登录",save:"保存",noset:"未设置",usernamePlaceholder:"请输入新用户名",nickPlaceholder:"请输入新昵称",pleaseRealName:"请输入真实姓名",nowPassword:"当前密码",newPassword:"新密码",confirmPassword:"确认新密码",phoneNumber:"手机号",confirmCode:"验证码",confirmCodeInput:"请输入验证码",confirmCodeInputerror:"验证码错误",findanimateCode:"获取动态码",animateCode:"动态码",animateCodeInput:"请输入动态码",modifyNickname:"修改昵称",modifyPassword:"修改密码",bindPhone:"绑定手机",alikeusername:"与原用户名一致，无需修改",noEmityUsername:"用户名不能为空",alikeNickname:"与原昵称一致，无需修改",noEmityNickname:"昵称不能为空",updateSuccess:"修改成功",pleaseInputOldPassword:"请输入原始密码",pleaseInputNewPassword:"请输入新密码",passwordLength:"密码长度不能小于6位",alikePassword:"两次密码不一致",samePassword:"新密码不能与原密码相同",surePhoneNumber:"请输入正确的手机号",alikePhone:"与原手机号一致，无需修改",modify:"修改"}},"5fce":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的消息"}},"61a3":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"商品评价"}},6315:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"充值列表"}},6382:function(e,t,o){var i=o("6454");e.exports=function(e,t){if(e){if("string"===typeof e)return i(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?i(e,t):void 0}},e.exports.__esModule=!0,e.exports["default"]=e.exports},6454:function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,i=new Array(t);o<t;o++)i[o]=e[o];return i},e.exports.__esModule=!0,e.exports["default"]=e.exports},6594:function(e,t,o){"use strict";var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("3b2d")),r=i(o("67ad")),a=i(o("0bdb")),s=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=(t.date,t.selected),i=t.startDate,n=t.endDate,a=t.range;(0,r.default)(this,e),this.date=this.getDate(new Date),this.selected=o||[],this.startDate=i,this.endDate=n,this.range=a,this.cleanMultipleStatus(),this.weeks={},this.lastHover=!1}return(0,a.default)(e,[{key:"setDate",value:function(e){this.selectDate=this.getDate(e),this._getWeek(this.selectDate.fullDate)}},{key:"cleanMultipleStatus",value:function(){this.multipleStatus={before:"",after:"",data:[]}}},{key:"resetSatrtDate",value:function(e){this.startDate=e}},{key:"resetEndDate",value:function(e){this.endDate=e}},{key:"getDate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day";e||(e=new Date),"object"!==(0,n.default)(e)&&(e=e.replace(/-/g,"/"));var i=new Date(e);switch(o){case"day":i.setDate(i.getDate()+t);break;case"month":31===i.getDate()?i.setDate(i.getDate()+t):i.setMonth(i.getMonth()+t);break;case"year":i.setFullYear(i.getFullYear()+t);break}var r=i.getFullYear(),a=i.getMonth()+1<10?"0"+(i.getMonth()+1):i.getMonth()+1,s=i.getDate()<10?"0"+i.getDate():i.getDate();return{fullDate:r+"-"+a+"-"+s,year:r,month:a,date:s,day:i.getDay()}}},{key:"_getLastMonthDays",value:function(e,t){for(var o=[],i=e;i>0;i--){var n=new Date(t.year,t.month-1,1-i).getDate();o.push({date:n,month:t.month-1,disable:!0})}return o}},{key:"_currentMonthDys",value:function(e,t){for(var o=this,i=[],n=this.date.fullDate,r=function(e){var r=t.year+"-"+(t.month,t.month+"-")+(e<10?"0"+e:e),a=n===r,s=o.selected&&o.selected.find((function(e){if(o.dateEqual(r,e.date))return e})),c=!0,l=!0;o.startDate&&(c=o.dateCompare(o.startDate,r)),o.endDate&&(l=o.dateCompare(r,o.endDate));var u=o.multipleStatus.data,d=!1,f=-1;o.range&&(u&&(f=u.findIndex((function(e){return o.dateEqual(e,r)}))),-1!==f&&(d=!0));var p={fullDate:r,year:t.year,date:e,multiple:!!o.range&&d,beforeMultiple:o.isLogicBefore(r,o.multipleStatus.before,o.multipleStatus.after),afterMultiple:o.isLogicAfter(r,o.multipleStatus.before,o.multipleStatus.after),month:t.month,disable:!(c&&l),isDay:a,userChecked:!1};s&&(p.extraInfo=s),i.push(p)},a=1;a<=e;a++)r(a);return i}},{key:"_getNextMonthDays",value:function(e,t){for(var o=[],i=1;i<e+1;i++)o.push({date:i,month:Number(t.month)+1,disable:!0});return o}},{key:"getInfo",value:function(e){var t=this;e||(e=new Date);var o=this.canlender.find((function(o){return o.fullDate===t.getDate(e).fullDate}));return o}},{key:"dateCompare",value:function(e,t){return e=new Date(e.replace("-","/").replace("-","/")),t=new Date(t.replace("-","/").replace("-","/")),e<=t}},{key:"dateEqual",value:function(e,t){return e=new Date(e.replace("-","/").replace("-","/")),t=new Date(t.replace("-","/").replace("-","/")),e.getTime()-t.getTime()===0}},{key:"isLogicBefore",value:function(e,t,o){var i=t;return t&&o&&(i=this.dateCompare(t,o)?t:o),this.dateEqual(i,e)}},{key:"isLogicAfter",value:function(e,t,o){var i=o;return t&&o&&(i=this.dateCompare(t,o)?o:t),this.dateEqual(i,e)}},{key:"geDateAll",value:function(e,t){var o=[],i=e.split("-"),n=t.split("-"),r=new Date;r.setFullYear(i[0],i[1]-1,i[2]);var a=new Date;a.setFullYear(n[0],n[1]-1,n[2]);for(var s=r.getTime()-864e5,c=a.getTime()-864e5,l=s;l<=c;)l+=864e5,o.push(this.getDate(new Date(parseInt(l))).fullDate);return o}},{key:"setMultiple",value:function(e){var t=this.multipleStatus,o=t.before,i=t.after;if(this.range){if(o&&i){if(!this.lastHover)return void(this.lastHover=!0);this.multipleStatus.before=e,this.multipleStatus.after="",this.multipleStatus.data=[],this.multipleStatus.fulldate="",this.lastHover=!1}else o?(this.multipleStatus.after=e,this.dateCompare(this.multipleStatus.before,this.multipleStatus.after)?this.multipleStatus.data=this.geDateAll(this.multipleStatus.before,this.multipleStatus.after):this.multipleStatus.data=this.geDateAll(this.multipleStatus.after,this.multipleStatus.before),this.lastHover=!0):(this.multipleStatus.before=e,this.lastHover=!1);this._getWeek(e)}}},{key:"setHoverMultiple",value:function(e){var t=this.multipleStatus,o=t.before;t.after;this.range&&(this.lastHover||(o?(this.multipleStatus.after=e,this.dateCompare(this.multipleStatus.before,this.multipleStatus.after)?this.multipleStatus.data=this.geDateAll(this.multipleStatus.before,this.multipleStatus.after):this.multipleStatus.data=this.geDateAll(this.multipleStatus.after,this.multipleStatus.before)):this.multipleStatus.before=e,this._getWeek(e)))}},{key:"setDefaultMultiple",value:function(e,t){this.multipleStatus.before=e,this.multipleStatus.after=t,e&&t&&(this.dateCompare(e,t)?(this.multipleStatus.data=this.geDateAll(e,t),this._getWeek(t)):(this.multipleStatus.data=this.geDateAll(t,e),this._getWeek(e)))}},{key:"_getWeek",value:function(e){var t=this.getDate(e),o=(t.fullDate,t.year),i=t.month,n=(t.date,t.day,new Date(o,i-1,1).getDay()),r=new Date(o,i,0).getDate(),a={lastMonthDays:this._getLastMonthDays(n,this.getDate(e)),currentMonthDys:this._currentMonthDys(r,this.getDate(e)),nextMonthDays:[],weeks:[]},s=[],c=42-(a.lastMonthDays.length+a.currentMonthDys.length);a.nextMonthDays=this._getNextMonthDays(c,this.getDate(e)),s=s.concat(a.lastMonthDays,a.currentMonthDys,a.nextMonthDays);for(var l={},u=0;u<s.length;u++)u%7===0&&(l[parseInt(u/7)]=new Array(7)),l[parseInt(u/7)][u%7]=s[u];this.canlender=s,this.weeks=l}}]),e}(),c=s;t.default=c},"67ad":function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports["default"]=e.exports},6854:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"砍价专区"}},7172:function(e,t){e.exports=function(e,t){var o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var i,n,r,a,s=[],c=!0,l=!1;try{if(r=(o=o.call(e)).next,0===t){if(Object(o)!==o)return;c=!1}else for(;!(c=(i=r.call(o)).done)&&(s.push(i.value),s.length!==t);c=!0);}catch(e){l=!0,n=e}finally{try{if(!c&&null!=o["return"]&&(a=o["return"](),Object(a)!==a))return}finally{if(l)throw n}}return s}},e.exports.__esModule=!0,e.exports["default"]=e.exports},7198:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的红包列表"}},7259:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={contact:"",person:"",personadd:"","contact-filled":"","person-filled":"","personadd-filled":"",phone:"",email:"",chatbubble:"",chatboxes:"","phone-filled":"","email-filled":"","chatbubble-filled":"","chatboxes-filled":"",weibo:"",weixin:"",pengyouquan:"",chat:"",qq:"",videocam:"",camera:"",mic:"",location:"","mic-filled":"",speech:"","location-filled":"",micoff:"",image:"",map:"",compose:"",trash:"",upload:"",download:"",close:"",redo:"",undo:"",refresh:"",star:"",plus:"",minus:"",circle:"",checkbox:"","close-filled":"",clear:"","refresh-filled":"","star-filled":"","plus-filled":"","minus-filled":"","circle-filled":"","checkbox-filled":"",closeempty:"",refreshempty:"",reload:"",starhalf:"",spinner:"","spinner-cycle":"",search:"",plusempty:"",forward:"",back:"","left-nav":"",checkmarkempty:"",home:"",navigate:"",gear:"",paperplane:"",info:"",help:"",locked:"",more:"",flag:"","home-filled":"","gear-filled":"","info-filled":"","help-filled":"","more-filled":"",settings:"",list:"",bars:"",loop:"",paperclip:"",eye:"",arrowup:"",arrowdown:"",arrowleft:"",arrowright:"",arrowthinup:"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",pulldown:"",closefill:"",sound:"",scan:""}},7281:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={}},"73f9":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"支付密码"}},"75ec":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"核销记录",emptyTips:"暂无记录"}},"762f":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"申请退款"}},7647:function(e,t){function o(t,i){return e.exports=o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports["default"]=e.exports,o(t,i)}e.exports=o,e.exports.__esModule=!0,e.exports["default"]=e.exports},"7aa2":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"搜索",history:"历史搜索",hot:"热门搜索",find:"搜索发现",hidefind:"当前搜索发现已隐藏",inputPlaceholder:"搜索商品"}},"7b5d":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"",select:"选择",params:"参数",service:"商品服务",allGoods:"全部商品",image:"图片",video:"视频"}},"7ca3":function(e,t,o){var i=o("d551");e.exports=function(e,t,o){return t=i(t),t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"7d99":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"商品咨询"}},"7d9d":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"待付款订单"}},"7dc6":function(e,t,o){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={data:function(){return{diyRoute:"/pages/member/index"}},computed:{},watch:{storeToken:function(t,o){t&&(this.initData(),e.getStorageSync("source_member")&&this.$util.onSourceMember(e.getStorageSync("source_member")))}},onLoad:function(t){var o=this;e.hideTabBar(),this.name="DIY_VIEW_MEMBER_INDEX",t.code&&this.$api.sendRequest({url:"/wechat/api/wechat/authcodetoopenid",data:{code:t.code},success:function(e){e.code>=0&&(e.data.userinfo.nickName&&o.modifyNickname(e.data.userinfo.nickName),e.data.userinfo.avatarUrl&&o.modifyHeadimg(e.data.userinfo.avatarUrl))}})},onShow:function(){this.$refs.diyGroup&&(this.$refs.diyGroup.$refs.diyMemberIndex&&this.$refs.diyGroup.$refs.diyMemberIndex[0].init(),this.$refs.diyGroup.$refs.diyMemberMyOrder&&this.$refs.diyGroup.$refs.diyMemberMyOrder[0].getOrderNum())},methods:{initData:function(){var e=this;this.storeToken&&this.$nextTick((function(){var t=function(){e.$refs.diyGroup&&e.$refs.diyGroup.$refs.diyMemberIndex&&e.$refs.diyGroup.$refs.diyMemberIndex[0].init()};e.$refs.nsNewGift.init(t),e.$refs.birthdayGift.init(t)}))},modifyNickname:function(e){var t=this;this.$api.sendRequest({url:"/api/member/modifynickname",data:{nickname:e},success:function(o){0==o.code&&(t.memberInfo.nickname=e,t.$store.commit("setMemberInfo",t.memberInfo))}})},modifyHeadimg:function(e){var t=this;this.$api.sendRequest({url:"/api/member/modifyheadimg",data:{headimg:e},success:function(o){0==o.code&&(t.memberInfo.headimg=e,t.$store.commit("setMemberInfo",t.memberInfo))}})}}};t.default=o}).call(this,o("df3c")["default"])},"7df5":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"充值记录"}},"7e65":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"修改头像"}},"7eb4":function(e,t,o){var i=o("9fc1")();e.exports=i},"7f6e":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的积分"}},"80b5":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"商品分类",seeMore:"查看更多"}},"80d8":function(e,t,o){"use strict";(function(t){var i=o("47a9"),n=i(o("67ad")),r=i(o("0bdb")),a=i(o("af87")),s={KEY_ERR:311,KEY_ERR_MSG:"key格式错误",PARAM_ERR:310,PARAM_ERR_MSG:"请求参数信息有误",SYSTEM_ERR:600,SYSTEM_ERR_MSG:"系统错误",WX_ERR_CODE:1e3,WX_OK_CODE:200},c="https://apis.map.qq.com/ws/",l=c+"place/v1/suggestion",u={driving:"driving",transit:"transit"},d={safeAdd:function(e,t){var o=(65535&e)+(65535&t),i=(e>>16)+(t>>16)+(o>>16);return i<<16|65535&o},bitRotateLeft:function(e,t){return e<<t|e>>>32-t},md5cmn:function(e,t,o,i,n,r){return this.safeAdd(this.bitRotateLeft(this.safeAdd(this.safeAdd(t,e),this.safeAdd(i,r)),n),o)},md5ff:function(e,t,o,i,n,r,a){return this.md5cmn(t&o|~t&i,e,t,n,r,a)},md5gg:function(e,t,o,i,n,r,a){return this.md5cmn(t&i|o&~i,e,t,n,r,a)},md5hh:function(e,t,o,i,n,r,a){return this.md5cmn(t^o^i,e,t,n,r,a)},md5ii:function(e,t,o,i,n,r,a){return this.md5cmn(o^(t|~i),e,t,n,r,a)},binlMD5:function(e,t){var o,i,n,r,a;e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;var s=1732584193,c=-271733879,l=-1732584194,u=271733878;for(o=0;o<e.length;o+=16)i=s,n=c,r=l,a=u,s=this.md5ff(s,c,l,u,e[o],7,-680876936),u=this.md5ff(u,s,c,l,e[o+1],12,-389564586),l=this.md5ff(l,u,s,c,e[o+2],17,606105819),c=this.md5ff(c,l,u,s,e[o+3],22,-1044525330),s=this.md5ff(s,c,l,u,e[o+4],7,-176418897),u=this.md5ff(u,s,c,l,e[o+5],12,1200080426),l=this.md5ff(l,u,s,c,e[o+6],17,-1473231341),c=this.md5ff(c,l,u,s,e[o+7],22,-45705983),s=this.md5ff(s,c,l,u,e[o+8],7,1770035416),u=this.md5ff(u,s,c,l,e[o+9],12,-1958414417),l=this.md5ff(l,u,s,c,e[o+10],17,-42063),c=this.md5ff(c,l,u,s,e[o+11],22,-1990404162),s=this.md5ff(s,c,l,u,e[o+12],7,1804603682),u=this.md5ff(u,s,c,l,e[o+13],12,-40341101),l=this.md5ff(l,u,s,c,e[o+14],17,-1502002290),c=this.md5ff(c,l,u,s,e[o+15],22,1236535329),s=this.md5gg(s,c,l,u,e[o+1],5,-165796510),u=this.md5gg(u,s,c,l,e[o+6],9,-1069501632),l=this.md5gg(l,u,s,c,e[o+11],14,643717713),c=this.md5gg(c,l,u,s,e[o],20,-373897302),s=this.md5gg(s,c,l,u,e[o+5],5,-701558691),u=this.md5gg(u,s,c,l,e[o+10],9,38016083),l=this.md5gg(l,u,s,c,e[o+15],14,-660478335),c=this.md5gg(c,l,u,s,e[o+4],20,-405537848),s=this.md5gg(s,c,l,u,e[o+9],5,568446438),u=this.md5gg(u,s,c,l,e[o+14],9,-1019803690),l=this.md5gg(l,u,s,c,e[o+3],14,-187363961),c=this.md5gg(c,l,u,s,e[o+8],20,1163531501),s=this.md5gg(s,c,l,u,e[o+13],5,-1444681467),u=this.md5gg(u,s,c,l,e[o+2],9,-51403784),l=this.md5gg(l,u,s,c,e[o+7],14,1735328473),c=this.md5gg(c,l,u,s,e[o+12],20,-1926607734),s=this.md5hh(s,c,l,u,e[o+5],4,-378558),u=this.md5hh(u,s,c,l,e[o+8],11,-2022574463),l=this.md5hh(l,u,s,c,e[o+11],16,1839030562),c=this.md5hh(c,l,u,s,e[o+14],23,-35309556),s=this.md5hh(s,c,l,u,e[o+1],4,-1530992060),u=this.md5hh(u,s,c,l,e[o+4],11,1272893353),l=this.md5hh(l,u,s,c,e[o+7],16,-155497632),c=this.md5hh(c,l,u,s,e[o+10],23,-1094730640),s=this.md5hh(s,c,l,u,e[o+13],4,681279174),u=this.md5hh(u,s,c,l,e[o],11,-358537222),l=this.md5hh(l,u,s,c,e[o+3],16,-722521979),c=this.md5hh(c,l,u,s,e[o+6],23,76029189),s=this.md5hh(s,c,l,u,e[o+9],4,-640364487),u=this.md5hh(u,s,c,l,e[o+12],11,-421815835),l=this.md5hh(l,u,s,c,e[o+15],16,530742520),c=this.md5hh(c,l,u,s,e[o+2],23,-995338651),s=this.md5ii(s,c,l,u,e[o],6,-198630844),u=this.md5ii(u,s,c,l,e[o+7],10,1126891415),l=this.md5ii(l,u,s,c,e[o+14],15,-1416354905),c=this.md5ii(c,l,u,s,e[o+5],21,-57434055),s=this.md5ii(s,c,l,u,e[o+12],6,1700485571),u=this.md5ii(u,s,c,l,e[o+3],10,-1894986606),l=this.md5ii(l,u,s,c,e[o+10],15,-1051523),c=this.md5ii(c,l,u,s,e[o+1],21,-2054922799),s=this.md5ii(s,c,l,u,e[o+8],6,1873313359),u=this.md5ii(u,s,c,l,e[o+15],10,-30611744),l=this.md5ii(l,u,s,c,e[o+6],15,-1560198380),c=this.md5ii(c,l,u,s,e[o+13],21,1309151649),s=this.md5ii(s,c,l,u,e[o+4],6,-145523070),u=this.md5ii(u,s,c,l,e[o+11],10,-1120210379),l=this.md5ii(l,u,s,c,e[o+2],15,718787259),c=this.md5ii(c,l,u,s,e[o+9],21,-343485551),s=this.safeAdd(s,i),c=this.safeAdd(c,n),l=this.safeAdd(l,r),u=this.safeAdd(u,a);return[s,c,l,u]},binl2rstr:function(e){var t,o="",i=32*e.length;for(t=0;t<i;t+=8)o+=String.fromCharCode(e[t>>5]>>>t%32&255);return o},rstr2binl:function(e){var t,o=[];for(o[(e.length>>2)-1]=void 0,t=0;t<o.length;t+=1)o[t]=0;var i=8*e.length;for(t=0;t<i;t+=8)o[t>>5]|=(255&e.charCodeAt(t/8))<<t%32;return o},rstrMD5:function(e){return this.binl2rstr(this.binlMD5(this.rstr2binl(e),8*e.length))},rstrHMACMD5:function(e,t){var o,i,n=this.rstr2binl(e),r=[],a=[];for(r[15]=a[15]=void 0,n.length>16&&(n=this.binlMD5(n,8*e.length)),o=0;o<16;o+=1)r[o]=909522486^n[o],a[o]=1549556828^n[o];return i=this.binlMD5(r.concat(this.rstr2binl(t)),512+8*t.length),this.binl2rstr(this.binlMD5(a.concat(i),640))},rstr2hex:function(e){var t,o,i="";for(o=0;o<e.length;o+=1)t=e.charCodeAt(o),i+="0123456789abcdef".charAt(t>>>4&15)+"0123456789abcdef".charAt(15&t);return i},str2rstrUTF8:function(e){return unescape(encodeURIComponent(e))},rawMD5:function(e){return this.rstrMD5(this.str2rstrUTF8(e))},hexMD5:function(e){return this.rstr2hex(this.rawMD5(e))},rawHMACMD5:function(e,t){return this.rstrHMACMD5(this.str2rstrUTF8(e),str2rstrUTF8(t))},hexHMACMD5:function(e,t){return this.rstr2hex(this.rawHMACMD5(e,t))},md5:function(e,t,o){return t?o?this.rawHMACMD5(t,e):this.hexHMACMD5(t,e):o?this.rawMD5(e):this.hexMD5(e)},getSig:function(e,t,o,i){var n=null,r=[];return Object.keys(e).sort().forEach((function(t){r.push(t+"="+e[t])})),"search"==o&&(n="/ws/place/v1/search?"+r.join("&")+t),"suggest"==o&&(n="/ws/place/v1/suggestion?"+r.join("&")+t),"reverseGeocoder"==o&&(n="/ws/geocoder/v1/?"+r.join("&")+t),"geocoder"==o&&(n="/ws/geocoder/v1/?"+r.join("&")+t),"getCityList"==o&&(n="/ws/district/v1/list?"+r.join("&")+t),"getDistrictByCityId"==o&&(n="/ws/district/v1/getchildren?"+r.join("&")+t),"calculateDistance"==o&&(n="/ws/distance/v1/?"+r.join("&")+t),"direction"==o&&(n="/ws/direction/v1/"+i+"?"+r.join("&")+t),n=this.md5(n),n},location2query:function(e){if("string"==typeof e)return e;for(var t="",o=0;o<e.length;o++){var i=e[o];t&&(t+=";"),i.location&&(t=t+i.location.lat+","+i.location.lng),i.latitude&&i.longitude&&(t=t+i.latitude+","+i.longitude)}return t},rad:function(e){return e*Math.PI/180},getEndLocation:function(e){for(var t=e.split(";"),o=[],i=0;i<t.length;i++)o.push({lat:parseFloat(t[i].split(",")[0]),lng:parseFloat(t[i].split(",")[1])});return o},getDistance:function(e,t,o,i){var n=this.rad(e),r=this.rad(o),a=n-r,s=this.rad(t)-this.rad(i),c=2*Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2)+Math.cos(n)*Math.cos(r)*Math.pow(Math.sin(s/2),2)));return c*=6378136.49,c=Math.round(1e4*c)/1e4,parseFloat(c.toFixed(0))},getWXLocation:function(e,t,o){a.default.getLocation({type:"gcj02",success:e,fail:t,complete:o})},getLocationParam:function(e){if("string"==typeof e){var t=e.split(",");e=2===t.length?{latitude:e.split(",")[0],longitude:e.split(",")[1]}:{}}return e},polyfillParam:function(e){e.success=e.success||function(){},e.fail=e.fail||function(){},e.complete=e.complete||function(){}},checkParamKeyEmpty:function(e,t){if(!e[t]){var o=this.buildErrorConfig(s.PARAM_ERR,s.PARAM_ERR_MSG+t+"参数格式有误");return e.fail(o),e.complete(o),!0}return!1},checkKeyword:function(e){return!this.checkParamKeyEmpty(e,"keyword")},checkLocation:function(e){var t=this.getLocationParam(e.location);if(!t||!t.latitude||!t.longitude){var o=this.buildErrorConfig(s.PARAM_ERR,s.PARAM_ERR_MSG+" location参数格式有误");return e.fail(o),e.complete(o),!1}return!0},buildErrorConfig:function(e,t){return{status:e,message:t}},handleData:function(e,t,o){if("search"==o){for(var i=t.data,n=[],r=0;r<i.length;r++)n.push({id:i[r].id||null,title:i[r].title||null,latitude:i[r].location&&i[r].location.lat||null,longitude:i[r].location&&i[r].location.lng||null,address:i[r].address||null,category:i[r].category||null,tel:i[r].tel||null,adcode:i[r].ad_info&&i[r].ad_info.adcode||null,city:i[r].ad_info&&i[r].ad_info.city||null,district:i[r].ad_info&&i[r].ad_info.district||null,province:i[r].ad_info&&i[r].ad_info.province||null});e.success(t,{searchResult:i,searchSimplify:n})}else if("suggest"==o){var a=t.data,s=[];for(r=0;r<a.length;r++)s.push({adcode:a[r].adcode||null,address:a[r].address||null,category:a[r].category||null,city:a[r].city||null,district:a[r].district||null,id:a[r].id||null,latitude:a[r].location&&a[r].location.lat||null,longitude:a[r].location&&a[r].location.lng||null,province:a[r].province||null,title:a[r].title||null,type:a[r].type||null});e.success(t,{suggestResult:a,suggestSimplify:s})}else if("reverseGeocoder"==o){var c=t.result,l={address:c.address||null,latitude:c.location&&c.location.lat||null,longitude:c.location&&c.location.lng||null,adcode:c.ad_info&&c.ad_info.adcode||null,city:c.address_component&&c.address_component.city||null,district:c.address_component&&c.address_component.district||null,nation:c.address_component&&c.address_component.nation||null,province:c.address_component&&c.address_component.province||null,street:c.address_component&&c.address_component.street||null,street_number:c.address_component&&c.address_component.street_number||null,recommend:c.formatted_addresses&&c.formatted_addresses.recommend||null,rough:c.formatted_addresses&&c.formatted_addresses.rough||null};if(c.pois){var u=c.pois,d=[];for(r=0;r<u.length;r++)d.push({id:u[r].id||null,title:u[r].title||null,latitude:u[r].location&&u[r].location.lat||null,longitude:u[r].location&&u[r].location.lng||null,address:u[r].address||null,category:u[r].category||null,adcode:u[r].ad_info&&u[r].ad_info.adcode||null,city:u[r].ad_info&&u[r].ad_info.city||null,district:u[r].ad_info&&u[r].ad_info.district||null,province:u[r].ad_info&&u[r].ad_info.province||null});e.success(t,{reverseGeocoderResult:c,reverseGeocoderSimplify:l,pois:u,poisSimplify:d})}else e.success(t,{reverseGeocoderResult:c,reverseGeocoderSimplify:l})}else if("geocoder"==o){var f=t.result,p={title:f.title||null,latitude:f.location&&f.location.lat||null,longitude:f.location&&f.location.lng||null,adcode:f.ad_info&&f.ad_info.adcode||null,province:f.address_components&&f.address_components.province||null,city:f.address_components&&f.address_components.city||null,district:f.address_components&&f.address_components.district||null,street:f.address_components&&f.address_components.street||null,street_number:f.address_components&&f.address_components.street_number||null,level:f.level||null};e.success(t,{geocoderResult:f,geocoderSimplify:p})}else if("getCityList"==o){var h=t.result[0],g=t.result[1],m=t.result[2];e.success(t,{provinceResult:h,cityResult:g,districtResult:m})}else if("getDistrictByCityId"==o){var _=t.result[0];e.success(t,_)}else if("calculateDistance"==o){var v=t.result.elements,y=[];for(r=0;r<v.length;r++)y.push(v[r].distance);e.success(t,{calculateDistanceResult:v,distance:y})}else if("direction"==o){var b=t.result.routes;e.success(t,b)}else e.success(t)},buildWxRequestConfig:function(e,t,o){var i=this;return t.header={"content-type":"application/json"},t.method="GET",t.success=function(t){var n=t.data;0===n.status?i.handleData(e,n,o):e.fail(n)},t.fail=function(t){t.statusCode=s.WX_ERR_CODE,e.fail(i.buildErrorConfig(s.WX_ERR_CODE,t.errMsg))},t.complete=function(t){var o=+t.statusCode;switch(o){case s.WX_ERR_CODE:e.complete(i.buildErrorConfig(s.WX_ERR_CODE,t.errMsg));break;case s.WX_OK_CODE:var n=t.data;0===n.status?e.complete(n):e.complete(i.buildErrorConfig(n.status,n.message));break;default:e.complete(i.buildErrorConfig(s.SYSTEM_ERR,s.SYSTEM_ERR_MSG))}},t},locationProcess:function(e,t,o,i){var n=this;if(o=o||function(t){t.statusCode=s.WX_ERR_CODE,e.fail(n.buildErrorConfig(s.WX_ERR_CODE,t.errMsg))},i=i||function(t){t.statusCode==s.WX_ERR_CODE&&e.complete(n.buildErrorConfig(s.WX_ERR_CODE,t.errMsg))},e.location){if(n.checkLocation(e)){var r=d.getLocationParam(e.location);t(r)}}else n.getWXLocation(t,o,i)}},f=function(){function e(t){if((0,n.default)(this,e),!t.key)throw Error("key值不能为空");this.key=t.key}return(0,r.default)(e,[{key:"search",value:function(e){if(e=e||{},d.polyfillParam(e),d.checkKeyword(e)){var o={keyword:e.keyword,orderby:e.orderby||"_distance",page_size:e.page_size||10,page_index:e.page_index||1,output:"json",key:this.key};e.address_format&&(o.address_format=e.address_format),e.filter&&(o.filter=e.filter);var i=e.distance||"1000",n=e.auto_extend||1,r=null,a=null;e.region&&(r=e.region),e.rectangle&&(a=e.rectangle);d.locationProcess(e,(function(s){r&&!a?(o.boundary="region("+r+","+n+","+s.latitude+","+s.longitude+")",e.sig&&(o.sig=d.getSig(o,e.sig,"search"))):a&&!r?(o.boundary="rectangle("+a+")",e.sig&&(o.sig=d.getSig(o,e.sig,"search"))):(o.boundary="nearby("+s.latitude+","+s.longitude+","+i+","+n+")",e.sig&&(o.sig=d.getSig(o,e.sig,"search"))),t.request(d.buildWxRequestConfig(e,{url:"https://apis.map.qq.com/ws/place/v1/search",data:o},"search"))}))}}},{key:"getSuggestion",value:function(e){if(e=e||{},d.polyfillParam(e),d.checkKeyword(e)){var o={keyword:e.keyword,region:e.region||"全国",region_fix:e.region_fix||0,policy:e.policy||0,page_size:e.page_size||10,page_index:e.page_index||1,get_subpois:e.get_subpois||0,output:"json",key:this.key};if(e.address_format&&(o.address_format=e.address_format),e.filter&&(o.filter=e.filter),e.location){d.locationProcess(e,(function(i){o.location=i.latitude+","+i.longitude,e.sig&&(o.sig=d.getSig(o,e.sig,"suggest")),t.request(d.buildWxRequestConfig(e,{url:l,data:o},"suggest"))}))}else e.sig&&(o.sig=d.getSig(o,e.sig,"suggest")),t.request(d.buildWxRequestConfig(e,{url:l,data:o},"suggest"))}}},{key:"reverseGeocoder",value:function(e){e=e||{},d.polyfillParam(e);var o={coord_type:e.coord_type||5,get_poi:e.get_poi||0,output:"json",key:this.key};e.poi_options&&(o.poi_options=e.poi_options);d.locationProcess(e,(function(i){o.location=i.latitude+","+i.longitude,e.sig&&(o.sig=d.getSig(o,e.sig,"reverseGeocoder")),t.request(d.buildWxRequestConfig(e,{url:"https://apis.map.qq.com/ws/geocoder/v1/",data:o},"reverseGeocoder"))}))}},{key:"geocoder",value:function(e){if(e=e||{},d.polyfillParam(e),!d.checkParamKeyEmpty(e,"address")){var o={address:e.address,output:"json",key:this.key};e.region&&(o.region=e.region),e.sig&&(o.sig=d.getSig(o,e.sig,"geocoder")),t.request(d.buildWxRequestConfig(e,{url:"https://apis.map.qq.com/ws/geocoder/v1/",data:o},"geocoder"))}}},{key:"getCityList",value:function(e){e=e||{},d.polyfillParam(e);var o={output:"json",key:this.key};e.sig&&(o.sig=d.getSig(o,e.sig,"getCityList")),t.request(d.buildWxRequestConfig(e,{url:"https://apis.map.qq.com/ws/district/v1/list",data:o},"getCityList"))}},{key:"getDistrictByCityId",value:function(e){if(e=e||{},d.polyfillParam(e),!d.checkParamKeyEmpty(e,"id")){var o={id:e.id||"",output:"json",key:this.key};e.sig&&(o.sig=d.getSig(o,e.sig,"getDistrictByCityId")),t.request(d.buildWxRequestConfig(e,{url:"https://apis.map.qq.com/ws/district/v1/getchildren",data:o},"getDistrictByCityId"))}}},{key:"calculateDistance",value:function(e){if(e=e||{},d.polyfillParam(e),!d.checkParamKeyEmpty(e,"to")){var o={mode:e.mode||"walking",to:d.location2query(e.to),output:"json",key:this.key};if(e.from&&(e.location=e.from),"straight"==o.mode){var i=function(t){for(var i=d.getEndLocation(o.to),n={message:"query ok",result:{elements:[]},status:0},r=0;r<i.length;r++)n.result.elements.push({distance:d.getDistance(t.latitude,t.longitude,i[r].lat,i[r].lng),duration:0,from:{lat:t.latitude,lng:t.longitude},to:{lat:i[r].lat,lng:i[r].lng}});var a=n.result.elements,s=[];for(r=0;r<a.length;r++)s.push(a[r].distance);return e.success(n,{calculateResult:a,distanceResult:s})};d.locationProcess(e,i)}else{i=function(i){o.from=i.latitude+","+i.longitude,e.sig&&(o.sig=d.getSig(o,e.sig,"calculateDistance")),t.request(d.buildWxRequestConfig(e,{url:"https://apis.map.qq.com/ws/distance/v1/",data:o},"calculateDistance"))};d.locationProcess(e,i)}}}},{key:"direction",value:function(e){if(e=e||{},d.polyfillParam(e),!d.checkParamKeyEmpty(e,"to")){var o={output:"json",key:this.key};"string"==typeof e.to?o.to=e.to:o.to=e.to.latitude+","+e.to.longitude;var i;e.mode=e.mode||u.driving,i="https://apis.map.qq.com/ws/direction/v1/"+e.mode,e.from&&(e.location=e.from),e.mode==u.driving&&(e.from_poi&&(o.from_poi=e.from_poi),e.heading&&(o.heading=e.heading),e.speed&&(o.speed=e.speed),e.accuracy&&(o.accuracy=e.accuracy),e.road_type&&(o.road_type=e.road_type),e.to_poi&&(o.to_poi=e.to_poi),e.from_track&&(o.from_track=e.from_track),e.waypoints&&(o.waypoints=e.waypoints),e.policy&&(o.policy=e.policy),e.plate_number&&(o.plate_number=e.plate_number)),e.mode==u.transit&&(e.departure_time&&(o.departure_time=e.departure_time),e.policy&&(o.policy=e.policy));d.locationProcess(e,(function(n){o.from=n.latitude+","+n.longitude,e.sig&&(o.sig=d.getSig(o,e.sig,"direction",e.mode)),t.request(d.buildWxRequestConfig(e,{url:i,data:o},"direction"))}))}}}]),e}();e.exports={Utils:d,QQMapWX:f}}).call(this,o("3223")["default"])},8108:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"",select:"选择",params:"参数",service:"商品服务",allGoods:"全部商品",image:"图片",video:"视频"}},"828b":function(e,t,o){"use strict";function i(e,t,o,i,n,r,a,s,c,l){var u,d="function"===typeof e?e.options:e;if(c){d.components||(d.components={});var f=Object.prototype.hasOwnProperty;for(var p in c)f.call(c,p)&&!f.call(d.components,p)&&(d.components[p]=c[p])}if(l&&("function"===typeof l.beforeCreate&&(l.beforeCreate=[l.beforeCreate]),(l.beforeCreate||(l.beforeCreate=[])).unshift((function(){this[l.__module]=this})),(d.mixins||(d.mixins=[])).push(l)),t&&(d.render=t,d.staticRenderFns=o,d._compiled=!0),i&&(d.functional=!0),r&&(d._scopeId="data-v-"+r),a?(u=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),n&&n.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(a)},d._ssrRegister=u):n&&(u=s?function(){n.call(this,this.$root.$options.shadowRoot)}:n),u)if(d.functional){d._injectStyles=u;var h=d.render;d.render=function(e,t){return u.call(t),h(e,t)}}else{var g=d.beforeCreate;d.beforeCreate=g?[].concat(g,u):[u]}return{exports:e,options:d}}o.d(t,"a",(function(){return i}))},"82fa":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},"83a9":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"编辑账户",name:"姓名",namePlaceholder:"请输入真实姓名",mobilePhone:"手机号码",mobilePhonePlaceholder:"请输入手机号",accountType:"账号类型",bankInfo:"支行信息",bankInfoPlaceholder:"请输入支行信息",withdrawalAccount:"提现账号",withdrawalAccountPlaceholder:"请输入提现账号",save:"保存"}},8526:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"注册",mobileRegister:"手机号注册",accountRegister:"账号注册",mobilePlaceholder:"手机号登录仅限中国大陆用户",dynacodePlaceholder:"请输入动态码",captchaPlaceholder:"请输入验证码",accountPlaceholder:"请输入用户名",passwordPlaceholder:"请输入密码",rePasswordPlaceholder:"请确认密码",completeRegister:"完成注册，并登录",registerTips:"点击注册即代表您已同意",registerAgreement:"注册协议",next:"下一步",save:"保存"}},8671:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"帮助中心",emptyText:"当前暂无帮助信息"}},"8a01":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"订单详情"}},"8c3d":function(e,t,o){"use strict";var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("7eb4")),r=i(o("af34")),a=i(o("ee10")),s={data:function(){return{goodsRoute:"/pages/goods/detail",couponList:[],couponBtnSwitch:!1,store_id:"",posterApi:"/api/goods/poster",manjian:{type:0,manjian_name:"",rule_json:null},bundling:[{bundling_goods:{bl_name:"",sku_image:""}}],membercard:null,hackReset:!0,cardOff:!1}},computed:{showDiscount:function(){var e=!1;return 0==this.preview&&this.addonIsExist.discount&&1==this.goodsSkuDetail.promotion_type&&this.goodsSkuDetail.discountTimeMachine&&(!this.goodsSkuDetail.member_price||this.goodsSkuDetail.member_price>0&&Number(this.goodsSkuDetail.member_price)>Number(this.goodsSkuDetail.discount_price))&&(e=!0),e},memberCardDiscount:function(){var e=0,t=this.goodsSkuDetail.member_price>0&&Number(this.goodsSkuDetail.member_price)<Number(this.goodsSkuDetail.discount_price)?this.goodsSkuDetail.member_price:this.goodsSkuDetail.discount_price;return this.membercard&&this.membercard.member_price>0&&parseFloat(t)>parseFloat(this.membercard.member_price)&&(e=parseFloat(t)-parseFloat(this.membercard.member_price)),e.toFixed(2)}},onLoad:function(e){var t=this;if(this.skuId=e.sku_id||0,this.goodsId=e.goods_id||0,e.store_id&&!isNaN(parseInt(e.store_id))&&(this.store_id=e.store_id,this.getStoreInfo(e.store_id)),e.scene){var o=decodeURIComponent(e.scene);o=o.split("&"),o.length&&o.forEach((function(e){-1!=e.indexOf("goods_id")&&(t.goodsId=e.split("-")[1])}))}this.getShareImg()},onShow:function(){var e=this;return(0,a.default)(n.default.mark((function t(){return n.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.store_id){t.next=3;break}return t.next=3,e.getGoodsSkuDetail();case 3:case"end":return t.stop()}}),t)})))()},onHide:function(){this.couponBtnSwitch=!1},methods:{getStoreInfo:function(e){var t=this;this.$api.sendRequest({url:"/api/store/info",data:{store_id:e},success:function(e){t.store_id="",e.data&&t.changeStore(e.data),t.getGoodsSkuDetail()},fail:function(e){t.getGoodsSkuDetail()}})},setSkuId:function(e){e&&(this.skuId=e,this.getBundling())},getGoodsSkuDetail:function(){var e=this;return(0,a.default)(n.default.mark((function t(){var o,i,r;return n.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$api.sendRequest({url:"/api/goodssku/detail",async:!1,data:{sku_id:e.skuId,goods_id:e.goodsId}});case 2:if(o=t.sent,i=o.data,null==i.goods_sku_detail){t.next=27;break}if("presale"!=i.goods_sku_detail.promotion_type||!i.goods_sku_detail.presale_id){t.next=8;break}return e.$util.redirectTo("/pages_promotion/presale/detail",{id:i.goods_sku_detail.presale_id,sku_id:i.goods_sku_detail.sku_id},"reLaunch"),t.abrupt("return");case 8:e.goodsSkuDetail=i.goods_sku_detail,e.skuId||(e.skuId=e.goodsSkuDetail.sku_id),e.goodsId||(e.goodsId=e.goodsSkuDetail.goods_id),e.shareQuery="goods_id="+e.goodsSkuDetail.goods_id,e.shareUrl=e.goodsRoute+"?"+e.shareQuery,e.chatRoomParams={sku_id:e.goodsSkuDetail.sku_id},r=e.goodsSkuDetail.goods_promotion[0],r&&r.discount_id&&(e.chatRoomParams.type="discount",e.chatRoomParams.type_id=r.discount_id),e.posterParams={goods_id:e.goodsId},e.handleGoodsSkuData(),1==e.goodsSkuDetail.promotion_type&&e.addonIsExist.discount&&(e.goodsSkuDetail.end_time-o.timestamp>0?e.goodsSkuDetail.discountTimeMachine=e.$util.countDown(e.goodsSkuDetail.end_time-o.timestamp):e.goodsSkuDetail.promotion_type=0),1==e.goodsSkuDetail.promotion_type&&e.goodsSkuDetail.discountTimeMachine?e.goodsSkuDetail.member_price>0&&Number(e.goodsSkuDetail.member_price)<=Number(e.goodsSkuDetail.discount_price)?e.goodsSkuDetail.show_price=e.goodsSkuDetail.member_price:e.goodsSkuDetail.show_price=e.goodsSkuDetail.discount_price:e.goodsSkuDetail.member_price>0?e.goodsSkuDetail.show_price=e.goodsSkuDetail.member_price:e.goodsSkuDetail.show_price=e.goodsSkuDetail.price,e.goodsSkuDetail.manjian&&e.getManjian(e.goodsSkuDetail.manjian),e.goodsSkuDetail.membercard&&(e.membercard=e.goodsSkuDetail.membercard),e.goodsSkuDetail.coupon_list&&(e.couponList=e.goodsSkuDetail.coupon_list,e.couponList.forEach((function(e){e.count==e.lead_count?e.useState=2:0!=e.max_fetch&&e.member_coupon_num&&e.member_coupon_num>=e.max_fetch?e.useState=1:e.useState=0})),e.couponList=e.couponList.sort(e.sortBy("useState"))),e.goodsSkuDetail.bundling_list&&e.handleBundlingData(e.goodsSkuDetail.bundling_list),e.$refs.loadingCover&&e.$refs.loadingCover.hide(),t.next=28;break;case 27:e.$util.redirectTo("/pages_tool/goods/not_exist",{},"redirectTo");case 28:case"end":return t.stop()}}),t)})))()},choiceSku:function(){var e=this;this.$refs.goodsSku.show("buy_now",(function(){e.$store.dispatch("getCartNumber")}))},joinCart:function(){var e=this;this.storeToken||0!=this.preview?1==this.goodsSkuDetail.is_virtual?this.$refs.goodsSku.show("buy_now",(function(){e.$store.dispatch("getCartNumber")})):this.$refs.goodsSku.show("join_cart",(function(){e.$store.dispatch("getCartNumber")})):this.$refs.login.open(this.shareUrl)},buyNow:function(){this.$refs.goodsSku.show("buy_now",(function(){}))},sortBy:function(e){return function(t,o){return t[e]-o[e]}},openCouponPopup:function(){this.$refs.couponPopup.open()},closeCouponPopup:function(){this.$refs.couponPopup.close()},receiveCoupon:function(e){var t=this,o=this;this.preview||this.couponBtnSwitch||(this.couponBtnSwitch=!0,this.storeToken?this.$api.sendRequest({url:"/coupon/api/coupon/receive",data:{coupon_type_id:e.coupon_type_id,get_type:2},success:function(i){var n=i.data,r="",a=t.couponList;if(r=1==n.is_exist&&i.code<0?"您已领取过该优惠券，快去使用吧":0==i.code?"领取成功，快去使用吧":i.message,1==n.is_exist)for(var s=0;s<a.length;s++)a[s].coupon_type_id==e.coupon_type_id&&o.$set(o.couponList[s],"useState",1);else for(var c=0;c<a.length;c++)a[c].coupon_type_id==e.coupon_type_id&&o.$set(o.couponList[c],"useState",2);t.$util.showToast({title:r}),o.$forceUpdate(),t.hackReset=!1,t.$nextTick((function(){t.hackReset=!0})),t.couponBtnSwitch=!1}}):(this.$refs.login.open(this.shareUrl),this.couponBtnSwitch=!1))},getManjian:function(e){var t=this;this.manjian=e;var o=0==e.type?"元":"件";Object.keys(e.rule_json).forEach((function(i){var n=e.rule_json[i];if(n.coupon_data)for(var r=0;r<n.coupon_data.length;r++)n.coupon_data[r].coupon_num=n.coupon_num[r];if(n.limit=0==e.type?parseFloat(n.limit).toFixed(2):parseInt(n.limit),void 0!=n.discount_money&&(void 0==t.manjian.manjian?t.manjian.manjian="满"+n.limit+o+"减"+n.discount_money+"元":t.manjian.manjian+="；满"+n.limit+o+"减"+n.discount_money+"元"),void 0!=n.point||void 0!=n.coupon){var a="";void 0!=n.point&&(a="送"+n.point+"积分"),void 0!=n.coupon&&void 0!=n.coupon_data&&n.coupon_data.forEach((function(e,t){"discount"==e.type?""==a?a="送"+n.coupon_num[t]+"张"+parseFloat(e.discount)+"折优惠券":a+="、送"+n.coupon_num[t]+"张"+parseFloat(e.discount)+"折优惠券":""==a?a="送"+n.coupon_num[t]+"张"+parseFloat(e.money)+"元优惠券":a+="、送"+n.coupon_num[t]+"张"+parseFloat(e.money)+"元优惠券"})),void 0==t.manjian.mansong?t.manjian.mansong="满"+n.limit+o+a:t.manjian.mansong+="；满"+n.limit+o+a}void 0!=n.free_shipping&&(void 0==t.manjian.free_shipping?t.manjian.free_shipping="满"+n.limit+o+"包邮":t.manjian.free_shipping+="；满"+n.limit+o+"包邮")}))},openManjianPopup:function(){this.$refs.manjianPopup.open()},closeManjianPopup:function(){this.$refs.manjianPopup.close()},getBundling:function(){var e=this;this.globalStoreConfig&&"store"==this.globalStoreConfig.store_business||this.$api.sendRequest({url:"/bundling/api/bundling/lists",data:{sku_id:this.skuId},success:function(t){e.handleBundlingData(t.data)}})},handleBundlingData:function(e){if(this.bundling=e,this.bundling.length)for(var t=0;t<this.bundling.length;t++)for(var o=0;o<this.bundling[t].bundling_goods.length;o++)if(this.bundling[t].bundling_goods[o].sku_id==this.skuId){var i,n=this.bundling[t].bundling_goods.splice(o,1);(i=this.bundling[t].bundling_goods).unshift.apply(i,(0,r.default)(n))}},openBundlingPopup:function(){this.$refs.bundlingPopup.open()},closeBundlingPopup:function(){this.$refs.bundlingPopup.close()},imageError:function(){this.goodsSkuDetail.sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},bundlingImageError:function(e,t){this.bundling[e].bundling_goods[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},fenxiao:function(){this.$refs.fenxiaoPopup.show()},toGoodsDetail:function(e){this.$util.redirectTo(this.goodsRoute,{sku_id:e})},toComoDetail:function(e){this.$util.redirectTo("/pages_promotion/bundling/detail",{bl_id:e})},getShareImg:function(){var e=this,t={goods_id:this.goodsId};this.$api.sendRequest({url:"/api/goods/shareimg",data:{qrcode_param:JSON.stringify(t)},success:function(t){0==t.code&&(e.shareImg=t.data.path+"?no="+parseInt((new Date).getTime()/1e3))}})},decimalPointFormat:function(e){if(isNaN(parseFloat(e)))return e;var t=e.toString().split("."),o=t[1].split("").reverse(),i=[];o.forEach((function(e,t){e>1&&(i=o.splice(t))}));var n=i.length?t[0]+"."+i.reverse().join(""):t[0];return n}}};t.default=s},"8d06":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"退款详情"}},"8e32":function(e,t,o){"use strict";var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("7ca3"));function r(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function a(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?r(Object(o),!0).forEach((function(t){(0,n.default)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):r(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var s={methods:{bannerConfigs:function(){return a({padding:"20rpx",gridRows:1,gridColumns:1,gridRowsGap:"40rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"center",headShow:!0,headWidth:"100%",headHeight:"300rpx",headBorderRadius:"20rpx",textShow:!1,textRows:3,textRowsGap:"20rpx",textWidth:"100%",textHeight:"30rpx",textBorderRadius:"6rpx"},this.configs)},infoConfigs:function(){return a({padding:"20rpx",gridRows:1,gridColumns:1,gridRowsGap:"50rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"flex-start",headShow:!0,headWidth:"100rpx",headHeight:"100rpx",headBorderRadius:"50%",textShow:!0,textRows:4,textRowsGap:"30rpx",textWidth:["50%","100%","100%","80%"],textHeight:["40rpx","24rpx","24rpx","24rpx"],textBorderRadius:"6rpx"},this.configs)},textConfigs:function(){return a({padding:"20rpx",gridRows:1,gridColumns:1,gridRowsGap:"50rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"flex-start",headShow:!1,headWidth:"100rpx",headHeight:"100rpx",headBorderRadius:"50%",textShow:!0,textRows:4,textRowsGap:"30rpx",textWidth:["50%","100%","100%","80%"],textHeight:"30rpx",textBorderRadius:"6rpx"},this.configs)},menuConfigs:function(){return a({padding:"20rpx",gridRows:2,gridColumns:5,gridRowsGap:"40rpx",gridColumnsGap:"40rpx",itemDirection:"column",itemGap:"16rpx",itemAlign:"center",headShow:!0,headWidth:"100rpx",headHeight:"100rpx",headBorderRadius:"50%",textShow:!0,textRows:1,textRowsGap:"0rpx",textWidth:"100%",textHeight:"24rpx",textBorderRadius:"6rpx"},this.configs)},listConfigs:function(){return a({padding:"20rpx",gridRows:2,gridColumns:1,gridRowsGap:"50rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"flex-start",headShow:!0,headWidth:"200rpx",headHeight:"200rpx",headBorderRadius:"16rpx",textShow:!0,textRows:4,textRowsGap:"30rpx",textWidth:["50%","100%","100%","80%"],textHeight:["38rpx","24rpx","24rpx","24rpx"],textBorderRadius:"6rpx"},this.configs)},waterfallConfigs:function(){return a({padding:"20rpx",gridRows:2,gridColumns:2,gridRowsGap:"40rpx",gridColumnsGap:"24rpx",itemDirection:"column",itemGap:"16rpx",itemAlign:"center",headShow:!0,headWidth:"100%",headHeight:"400rpx",headBorderRadius:"12rpx",textShow:!0,textRows:3,textRowsGap:"12rpx",textWidth:["40%","85%","60%"],textHeight:["30rpx","20rpx","20rpx"],textBorderRadius:"6rpx"},this.configs)}}};t.default=s},"8f59":function(e,t,o){"use strict";(function(t){var o="undefined"!==typeof window?window:"undefined"!==typeof t?t:{},i=o.__VUE_DEVTOOLS_GLOBAL_HOOK__;function n(e,t){if(void 0===t&&(t=[]),null===e||"object"!==typeof e)return e;var o=function(e,t){return e.filter(t)[0]}(t,(function(t){return t.original===e}));if(o)return o.copy;var i=Array.isArray(e)?[]:{};return t.push({original:e,copy:i}),Object.keys(e).forEach((function(o){i[o]=n(e[o],t)})),i}function r(e,t){Object.keys(e).forEach((function(o){return t(e[o],o)}))}function a(e){return null!==e&&"object"===typeof e}var s=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var o=e.state;this.state=("function"===typeof o?o():o)||{}},c={namespaced:{configurable:!0}};c.namespaced.get=function(){return!!this._rawModule.namespaced},s.prototype.addChild=function(e,t){this._children[e]=t},s.prototype.removeChild=function(e){delete this._children[e]},s.prototype.getChild=function(e){return this._children[e]},s.prototype.hasChild=function(e){return e in this._children},s.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},s.prototype.forEachChild=function(e){r(this._children,e)},s.prototype.forEachGetter=function(e){this._rawModule.getters&&r(this._rawModule.getters,e)},s.prototype.forEachAction=function(e){this._rawModule.actions&&r(this._rawModule.actions,e)},s.prototype.forEachMutation=function(e){this._rawModule.mutations&&r(this._rawModule.mutations,e)},Object.defineProperties(s.prototype,c);var l=function(e){this.register([],e,!1)};l.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},l.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,o){return t=t.getChild(o),e+(t.namespaced?o+"/":"")}),"")},l.prototype.update=function(e){(function e(t,o,i){0;if(o.update(i),i.modules)for(var n in i.modules){if(!o.getChild(n))return void 0;e(t.concat(n),o.getChild(n),i.modules[n])}})([],this.root,e)},l.prototype.register=function(e,t,o){var i=this;void 0===o&&(o=!0);var n=new s(t,o);if(0===e.length)this.root=n;else{var a=this.get(e.slice(0,-1));a.addChild(e[e.length-1],n)}t.modules&&r(t.modules,(function(t,n){i.register(e.concat(n),t,o)}))},l.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),o=e[e.length-1],i=t.getChild(o);i&&i.runtime&&t.removeChild(o)},l.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),o=e[e.length-1];return!!t&&t.hasChild(o)};var u;var d=function(e){var t=this;void 0===e&&(e={}),!u&&"undefined"!==typeof window&&window.Vue&&y(window.Vue);var o=e.plugins;void 0===o&&(o=[]);var n=e.strict;void 0===n&&(n=!1),this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new l(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._watcherVM=new u,this._makeLocalGettersCache=Object.create(null);var r=this,a=this.dispatch,s=this.commit;this.dispatch=function(e,t){return a.call(r,e,t)},this.commit=function(e,t,o){return s.call(r,e,t,o)},this.strict=n;var c=this._modules.root.state;m(this,c,[],this._modules.root),g(this,c),o.forEach((function(e){return e(t)}));var d=void 0!==e.devtools?e.devtools:u.config.devtools;d&&function(e){i&&(e._devtoolHook=i,i.emit("vuex:init",e),i.on("vuex:travel-to-state",(function(t){e.replaceState(t)})),e.subscribe((function(e,t){i.emit("vuex:mutation",e,t)}),{prepend:!0}),e.subscribeAction((function(e,t){i.emit("vuex:action",e,t)}),{prepend:!0}))}(this)},f={state:{configurable:!0}};function p(e,t,o){return t.indexOf(e)<0&&(o&&o.prepend?t.unshift(e):t.push(e)),function(){var o=t.indexOf(e);o>-1&&t.splice(o,1)}}function h(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var o=e.state;m(e,o,[],e._modules.root,!0),g(e,o,t)}function g(e,t,o){var i=e._vm;e.getters={},e._makeLocalGettersCache=Object.create(null);var n=e._wrappedGetters,a={};r(n,(function(t,o){a[o]=function(e,t){return function(){return e(t)}}(t,e),Object.defineProperty(e.getters,o,{get:function(){return e._vm[o]},enumerable:!0})}));var s=u.config.silent;u.config.silent=!0,e._vm=new u({data:{$$state:t},computed:a}),u.config.silent=s,e.strict&&function(e){e._vm.$watch((function(){return this._data.$$state}),(function(){0}),{deep:!0,sync:!0})}(e),i&&(o&&e._withCommit((function(){i._data.$$state=null})),u.nextTick((function(){return i.$destroy()})))}function m(e,t,o,i,n){var r=!o.length,a=e._modules.getNamespace(o);if(i.namespaced&&(e._modulesNamespaceMap[a],e._modulesNamespaceMap[a]=i),!r&&!n){var s=_(t,o.slice(0,-1)),c=o[o.length-1];e._withCommit((function(){u.set(s,c,i.state)}))}var l=i.context=function(e,t,o){var i=""===t,n={dispatch:i?e.dispatch:function(o,i,n){var r=v(o,i,n),a=r.payload,s=r.options,c=r.type;return s&&s.root||(c=t+c),e.dispatch(c,a)},commit:i?e.commit:function(o,i,n){var r=v(o,i,n),a=r.payload,s=r.options,c=r.type;s&&s.root||(c=t+c),e.commit(c,a,s)}};return Object.defineProperties(n,{getters:{get:i?function(){return e.getters}:function(){return function(e,t){if(!e._makeLocalGettersCache[t]){var o={},i=t.length;Object.keys(e.getters).forEach((function(n){if(n.slice(0,i)===t){var r=n.slice(i);Object.defineProperty(o,r,{get:function(){return e.getters[n]},enumerable:!0})}})),e._makeLocalGettersCache[t]=o}return e._makeLocalGettersCache[t]}(e,t)}},state:{get:function(){return _(e.state,o)}}}),n}(e,a,o);i.forEachMutation((function(t,o){var i=a+o;(function(e,t,o,i){var n=e._mutations[t]||(e._mutations[t]=[]);n.push((function(t){o.call(e,i.state,t)}))})(e,i,t,l)})),i.forEachAction((function(t,o){var i=t.root?o:a+o,n=t.handler||t;(function(e,t,o,i){var n=e._actions[t]||(e._actions[t]=[]);n.push((function(t){var n=o.call(e,{dispatch:i.dispatch,commit:i.commit,getters:i.getters,state:i.state,rootGetters:e.getters,rootState:e.state},t);return function(e){return e&&"function"===typeof e.then}(n)||(n=Promise.resolve(n)),e._devtoolHook?n.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):n}))})(e,i,n,l)})),i.forEachGetter((function(t,o){var i=a+o;(function(e,t,o,i){if(e._wrappedGetters[t])return void 0;e._wrappedGetters[t]=function(e){return o(i.state,i.getters,e.state,e.getters)}})(e,i,t,l)})),i.forEachChild((function(i,r){m(e,t,o.concat(r),i,n)}))}function _(e,t){return t.reduce((function(e,t){return e[t]}),e)}function v(e,t,o){return a(e)&&e.type&&(o=t,t=e,e=e.type),{type:e,payload:t,options:o}}function y(e){u&&e===u||(u=e,
/*!
 * vuex v3.6.2
 * (c) 2021 Evan You
 * @license MIT
 */
function(e){var t=Number(e.version.split(".")[0]);if(t>=2)e.mixin({beforeCreate:i});else{var o=e.prototype._init;e.prototype._init=function(e){void 0===e&&(e={}),e.init=e.init?[i].concat(e.init):i,o.call(this,e)}}function i(){var e=this.$options;e.store?this.$store="function"===typeof e.store?e.store():e.store:e.parent&&e.parent.$store&&(this.$store=e.parent.$store)}}(u))}f.state.get=function(){return this._vm._data.$$state},f.state.set=function(e){0},d.prototype.commit=function(e,t,o){var i=this,n=v(e,t,o),r=n.type,a=n.payload,s=(n.options,{type:r,payload:a}),c=this._mutations[r];c&&(this._withCommit((function(){c.forEach((function(e){e(a)}))})),this._subscribers.slice().forEach((function(e){return e(s,i.state)})))},d.prototype.dispatch=function(e,t){var o=this,i=v(e,t),n=i.type,r=i.payload,a={type:n,payload:r},s=this._actions[n];if(s){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(a,o.state)}))}catch(l){0}var c=s.length>1?Promise.all(s.map((function(e){return e(r)}))):s[0](r);return new Promise((function(e,t){c.then((function(t){try{o._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(a,o.state)}))}catch(l){0}e(t)}),(function(e){try{o._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(a,o.state,e)}))}catch(l){0}t(e)}))}))}},d.prototype.subscribe=function(e,t){return p(e,this._subscribers,t)},d.prototype.subscribeAction=function(e,t){var o="function"===typeof e?{before:e}:e;return p(o,this._actionSubscribers,t)},d.prototype.watch=function(e,t,o){var i=this;return this._watcherVM.$watch((function(){return e(i.state,i.getters)}),t,o)},d.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._vm._data.$$state=e}))},d.prototype.registerModule=function(e,t,o){void 0===o&&(o={}),"string"===typeof e&&(e=[e]),this._modules.register(e,t),m(this,this.state,e,this._modules.get(e),o.preserveState),g(this,this.state)},d.prototype.unregisterModule=function(e){var t=this;"string"===typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){var o=_(t.state,e.slice(0,-1));u.delete(o,e[e.length-1])})),h(this)},d.prototype.hasModule=function(e){return"string"===typeof e&&(e=[e]),this._modules.isRegistered(e)},d.prototype[[104,111,116,85,112,100,97,116,101].map((function(e){return String.fromCharCode(e)})).join("")]=function(e){this._modules.update(e),h(this,!0)},d.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(d.prototype,f);var b=D((function(e,t){var o={};return x(t).forEach((function(t){var i=t.key,n=t.val;o[i]=function(){var t=this.$store.state,o=this.$store.getters;if(e){var i=C(this.$store,"mapState",e);if(!i)return;t=i.context.state,o=i.context.getters}return"function"===typeof n?n.call(this,t,o):t[n]},o[i].vuex=!0})),o})),w=D((function(e,t){var o={};return x(t).forEach((function(t){var i=t.key,n=t.val;o[i]=function(){var t=[],o=arguments.length;while(o--)t[o]=arguments[o];var i=this.$store.commit;if(e){var r=C(this.$store,"mapMutations",e);if(!r)return;i=r.context.commit}return"function"===typeof n?n.apply(this,[i].concat(t)):i.apply(this.$store,[n].concat(t))}})),o})),S=D((function(e,t){var o={};return x(t).forEach((function(t){var i=t.key,n=t.val;n=e+n,o[i]=function(){if(!e||C(this.$store,"mapGetters",e))return this.$store.getters[n]},o[i].vuex=!0})),o})),k=D((function(e,t){var o={};return x(t).forEach((function(t){var i=t.key,n=t.val;o[i]=function(){var t=[],o=arguments.length;while(o--)t[o]=arguments[o];var i=this.$store.dispatch;if(e){var r=C(this.$store,"mapActions",e);if(!r)return;i=r.context.dispatch}return"function"===typeof n?n.apply(this,[i].concat(t)):i.apply(this.$store,[n].concat(t))}})),o}));function x(e){return function(e){return Array.isArray(e)||a(e)}(e)?Array.isArray(e)?e.map((function(e){return{key:e,val:e}})):Object.keys(e).map((function(t){return{key:t,val:e[t]}})):[]}function D(e){return function(t,o){return"string"!==typeof t?(o=t,t=""):"/"!==t.charAt(t.length-1)&&(t+="/"),e(t,o)}}function C(e,t,o){var i=e._modulesNamespaceMap[o];return i}function P(e,t,o){var i=o?e.groupCollapsed:e.group;try{i.call(e,t)}catch(n){e.log(t)}}function O(e){try{e.groupEnd()}catch(t){e.log("—— log end ——")}}function $(){var e=new Date;return" @ "+j(e.getHours(),2)+":"+j(e.getMinutes(),2)+":"+j(e.getSeconds(),2)+"."+j(e.getMilliseconds(),3)}function j(e,t){return function(e,t){return new Array(t+1).join(e)}("0",t-e.toString().length)+e}var M={Store:d,install:y,version:"3.6.2",mapState:b,mapMutations:w,mapGetters:S,mapActions:k,createNamespacedHelpers:function(e){return{mapState:b.bind(null,e),mapGetters:S.bind(null,e),mapMutations:w.bind(null,e),mapActions:k.bind(null,e)}},createLogger:function(e){void 0===e&&(e={});var t=e.collapsed;void 0===t&&(t=!0);var o=e.filter;void 0===o&&(o=function(e,t,o){return!0});var i=e.transformer;void 0===i&&(i=function(e){return e});var r=e.mutationTransformer;void 0===r&&(r=function(e){return e});var a=e.actionFilter;void 0===a&&(a=function(e,t){return!0});var s=e.actionTransformer;void 0===s&&(s=function(e){return e});var c=e.logMutations;void 0===c&&(c=!0);var l=e.logActions;void 0===l&&(l=!0);var u=e.logger;return void 0===u&&(u=console),function(e){var d=n(e.state);"undefined"!==typeof u&&(c&&e.subscribe((function(e,a){var s=n(a);if(o(e,d,s)){var c=$(),l=r(e),f="mutation "+e.type+c;P(u,f,t),u.log("%c prev state","color: #9E9E9E; font-weight: bold",i(d)),u.log("%c mutation","color: #03A9F4; font-weight: bold",l),u.log("%c next state","color: #4CAF50; font-weight: bold",i(s)),O(u)}d=s})),l&&e.subscribeAction((function(e,o){if(a(e,o)){var i=$(),n=s(e),r="action "+e.type+i;P(u,r,t),u.log("%c action","color: #03A9F4; font-weight: bold",n),O(u)}})))}}};e.exports=M}).call(this,o("0ee4"))},"8f94":function(e,t,o){"use strict";(function(e){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("7eb4")),r=i(o("af34")),a=i(o("ee10")),s=(o("80d8"),i(o("2f8f")),e.getSystemInfoSync()),c={data:function(){return{diyData:{global:{title:"",popWindow:{imageUrl:"",count:-1,link:{},imgWidth:"",imgHeight:""}}},id:0,name:"",topIndexValue:null,statusBarHeight:s.statusBarHeight,collectTop:44,showTip:!1,mpCollect:!1,mpShareData:null,scrollTop:0,paddingTop:44+s.statusBarHeight+"px",marginTop:-(44+s.statusBarHeight)+"px",followOfficialAccount:null,latitude:null,longitude:null,currentPosition:"",currentStore:null,nearestStore:null,diyRoute:"",openBottomNav:!1,isShowCopyRight:!1,option:null,firstDiy:!0}},onLoad:function(t){if(this.option=t,e.hideTabBar(),t.source_member&&e.setStorageSync("source_member",t.source_member),t.scene){var o=decodeURIComponent(t.scene);o=o.split("&"),o.length&&o.forEach((function(t){-1!=t.indexOf("m")&&e.setStorageSync("source_member",t.split("-")[1])}))}this.id=t.id||0,this.name=t.name||"";var i=this.$util.getCurrentRoute(),n=e.getStorageSync("manual_change_store");i.path.indexOf("/pages/index/index")>-1&&!n&&this.getCurrentStore(t)},onShow:function(){this.firstDiy&&(this.firstDiy=!1,this.getDiyMethod()),this.onShowMethod()},onHide:function(){this.$store.commit("setDiySeckillInterval",0)},computed:{bgColor:function(){var e="";return this.diyData&&this.diyData.global&&(e=this.diyData.global.pageBgColor),e},bgImg:function(){var e="";return this.diyData&&this.diyData.global&&(e=this.diyData.global.topNavBg?"url("+this.$util.img(this.diyData.global.bgUrl)+")":this.diyData.global.pageBgColor),e},bgUrl:function(){var e="";return this.diyData&&this.diyData.global&&(e=this.diyData.global.topNavBg?"transparent":this.diyData.global.bgUrl),e},backgroundUrl:function(){var e=this.diyData.global.bgUrl&&"transparent"!=this.diyData.global.bgUrl?"url("+this.$util.img(this.diyData.global.bgUrl)+") ":"";return e},textNavColor:function(){return this.diyData&&this.diyData.global&&this.diyData.global.textNavColor?this.diyData.global.textNavColor:"#ffffff"},popWindowStyle:function(){var e,t,o=this.diyData.global.popWindow.imgHeight/this.diyData.global.popWindow.imgWidth;o<1?(e=290,t=e*o):(t=410,e=t/o);var i="";return this.diyData.global.popWindow&&-1!=this.diyData.global.popWindow.count&&this.diyData.global.popWindow.imageUrl&&(i+="height:"+2*t+"rpx;",i+="width:"+2*e+"rpx;"),i}},watch:{initStatus:function(e){this.option.store_id||this.getLocation()}},methods:{getDiyMethod:function(){var e=this;return(0,a.default)(n.default.mark((function t(){return n.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getDiyInfo();case 2:e.$store.commit("setDiySeckillInterval",1),e.$store.commit("setComponentRefresh");case 4:case"end":return t.stop()}}),t)})))()},onShowMethod:function(){this.storeToken&&e.getStorageSync("source_member")&&this.$util.onSourceMember(e.getStorageSync("source_member"));var t=e.getStorageSync("manual_change_store");if(t){e.removeStorageSync("manual_change_store");var o=e.getStorageSync("manual_store_info");e.removeStorageSync("manual_store_info"),o&&(this.currentStore=o),this.closeGetLocationFailPopup(),e.pageScrollTo({duration:200,scrollTop:0})}},callback:function(){this.$refs.indexPage&&this.$refs.indexPage.initPageIndex()},getHeight:function(){var t=this,o=setInterval((function(){t.$nextTick((function(){var i=e.createSelectorQuery().in(t);i.select(".page-header").boundingClientRect((function(e){e&&e.height&&(t.diyData.global.topNavBg?(t.paddingTop=e.height+"px",t.marginTop=-e.height+"px"):(t.paddingTop=0,t.marginTop=0),clearInterval(o))})).exec()}))}),50)},getDiyInfo:function(){var t=this;return(0,a.default)(n.default.mark((function o(){var i,a,s,c,l,u,d,f,p,h,g,m,_,v,y,b,w;return n.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.next=2,t.$api.sendRequest({url:"/api/diyview/info",data:{id:t.id,name:t.name},async:!1});case 2:if(i=o.sent,0==i.code&&i.data){o.next=12;break}if(t.$refs.loadingCover&&t.$refs.loadingCover.hide(),-3!=i.code){o.next=9;break}return t.$util.showToast({title:i.message}),t.diyData={},o.abrupt("return");case 9:return t.$util.showToast({title:"未配置自定义页面数据"}),t.diyData={},o.abrupt("return");case 12:if(a=i.data,!a.value){o.next=53;break}t.diyData=JSON.parse(a.value),t.$langConfig.title(t.diyData.global.title),t.mpCollect=t.diyData.global.mpCollect,t.setPublicShare(),t.diyData.global.popWindow&&t.diyData.global.popWindow.imageUrl&&setTimeout((function(){if(1==t.diyData.global.popWindow.count){var o=e.getStorageSync(t.id+t.name+"_popwindow_count");(t.$refs.uniPopupWindow&&""==o||t.$refs.uniPopupWindow&&1==o)&&(t.$refs.uniPopupWindow.open(),e.setStorageSync(t.id+t.name+"_popwindow_count",1))}else 0==t.diyData.global.popWindow.count&&(t.$refs.uniPopupWindow.open(),e.setStorageSync(t.id+t.name+"_popwindow_count",0))}),500),s=-1,c=-1,t.diyData.value.forEach((function(e,t){"Search"==e.componentName&&"fixed"==e.positionWay&&(s=t),"TopCategory"==e.componentName&&(c=t)})),-1!=s&&-1!=c?(l=t.diyData.value.slice(s,s+1),u=t.diyData.value.slice(c,c+1),t.diyData.value.splice(s,1),s>c?(t.diyData.value.splice(c,1),(d=t.diyData.value).splice.apply(d,[0,0].concat((0,r.default)(u))),(f=t.diyData.value).splice.apply(f,[1,0].concat((0,r.default)(l)))):(p=t.diyData.value).splice.apply(p,[0,0].concat((0,r.default)(l)))):-1!=s&&-1==c&&(g=t.diyData.value.slice(s,s+1),t.diyData.value.splice(s,1),(h=t.diyData.value).splice.apply(h,[0,0].concat((0,r.default)(g)))),t.topIndexValue=null,m=0;case 25:if(!(m<t.diyData.value.length)){o.next=37;break}if("TopCategory"!=t.diyData.value[m].componentName){o.next=31;break}return t.topIndexValue=t.diyData.value[m],t.topIndexValue.moduleIndex=m,t.diyData.value.splice(m,1),o.abrupt("continue",34);case 31:if("FollowOfficialAccount"!=t.diyData.value[m].componentName){o.next=34;break}return t.followOfficialAccount=t.diyData.value[m],o.abrupt("continue",34);case 34:m++,o.next=25;break;case 37:if(!e.getStorageSync("isCollect")&&t.diyData.global.mpCollect&&(t.$refs.collectPopupWindow.open(),t.showTip=!0),t.getHeight(),t.diyData&&t.diyData.global&&(t.openBottomNav=t.diyData.global.openBottomNav),t.isShowCopyRight=!0,_=t.$util.getCurrentRoute().path,"/pages/member/index"!=_){o.next=45;break}return t.mpShareData={},o.abrupt("return");case 45:v=_,t.$store.state.memberInfo&&t.$store.state.memberInfo.member_id&&(v=t.$util.getCurrentShareRoute(t.$store.state.memberInfo.member_id).path),y={title:t.diyData.global.weappShareTitle,path:v,imageUrl:t.$util.img(t.diyData.global.weappShareImage),success:function(e){},fail:function(e){}},b={title:t.diyData.global.weappShareTitle,query:v,imageUrl:t.$util.img(t.diyData.global.weappShareImage)},t.mpShareData={appMessage:y,timeLine:b},w=t.$store.state.globalStoreInfo,w&&(t.mpShareData.appMessage.path+=(t.mpShareData.appMessage.path.indexOf("?")>-1?"&":"?")+"store_id="+w.store_id,t.mpShareData.timeLine.query+=(t.mpShareData.timeLine.query.indexOf("?")>-1?"&":"?")+"store_id="+w.store_id),t.mpShareData.timeLine.query=t.mpShareData.timeLine.query.split("?")[1]||"";case 53:case"end":return o.stop()}}),o)})))()},closePopupWindow:function(){this.$refs.uniPopupWindow.close(),e.setStorageSync(this.id+this.name+"_popwindow_count",-1)},closeCollectPopupWindow:function(){this.$refs.collectPopupWindow.close(),e.setStorageSync("isCollect",!0)},uniPopupWindowFn:function(){this.$util.diyRedirectTo(this.diyData.global.popWindow.link),this.closePopupWindow()},getCurrentStore:function(e){e.store_id&&!isNaN(parseInt(e.store_id))?this.getStoreInfoByShare(e.store_id):this.getLocation()},getStoreInfoByShare:function(e){var t=this;this.$api.sendRequest({url:"/api/store/info",data:{store_id:e},success:function(e){e.code>=0&&e.data?t.changeCurrentStore(e.data):t.getLocation()},fail:function(e){t.getLocation()}})},getLocation:function(){var e=this;this.latitude||this.longitude||!this.initStatus||(1==this.mapConfig.wap_is_open?this.$util.getLocation({complete:function(t){if(t.latitude&&t.longitude)e.closeGetLocationFailPopup(),e.latitude=t.latitude,e.longitude=t.longitude,e.getStoreInfoByLocation();else{e.getLocationFail()}}}):this.getLocationFail())},getStoreInfoByLocation:function(){this.latitude&&this.longitude&&(this.getNearestStore(),this.getCurrentLocation())},changeCurrentStore:function(e){this.currentStore=e,this.changeStore(e),this.openChooseStorePopup()},getLocationFail:function(){"shop"==this.globalStoreConfig.store_business?this.enterDefaultStore():this.openGetLocationFailPopup()},openGetLocationFailPopup:function(){this.$refs.getLocationFailRef&&this.$refs.getLocationFailRef.open()},closeGetLocationFailPopup:function(){this.$refs.getLocationFailRef&&this.$refs.getLocationFailRef.close()},openChooseStorePopup:function(){this.globalStoreInfo;this.globalStoreConfig&&1==this.globalStoreConfig.confirm_popup_control&&(this.currentStore.show_address=this.currentStore.full_address.replace(/,/g," ")+" "+this.currentStore.address,this.$refs.chooseStorePopup&&this.$refs.chooseStorePopup.open())},closeChooseStorePopup:function(){this.$refs.chooseStorePopup&&this.$refs.chooseStorePopup.close()},chooseOtherStore:function(){this.$util.redirectTo("/pages_tool/store/list"),this.closeChooseStorePopup()},reGetLocation:function(){var t=this;e.chooseLocation({success:function(e){t.latitude=e.latitude,t.longitude=e.longitude,t.currentPosition=e.name,t.getStoreInfoByLocation()},fail:function(t){e.getSetting({success:function(t){var o=t.authSetting;o["scope.userLocation"]||e.showModal({title:"是否授权当前位置",content:"需要获取您的地理位置，请确认授权，否则地图功能将无法使用",success:function(o){o.confirm?e.openSetting({success:function(o){!0===o.authSetting["scope.userLocation"]&&(this.$util.showToast({title:"授权成功"}),setTimeout((function(){var o=this;e.chooseLocation({success:function(e){o.latitude=t.latitude,o.longitude=t.longitude,o.currentPosition=t.name,o.getStoreInfoByLocation()}})}),1e3))}}):this.$util.showToast({title:"授权失败"})}})}})}})},getNearestStore:function(){var e=this,t={};this.latitude&&this.longitude&&(t.latitude=this.latitude,t.longitude=this.longitude),this.$api.sendRequest({url:"/api/store/nearestStore",data:t,success:function(t){0==t.code&&t.data&&e.changeCurrentStore(t.data)}})},getCurrentLocation:function(){var e=this,t={};this.latitude&&this.longitude&&(t.latitude=this.latitude,t.longitude=this.longitude),this.$api.sendRequest({url:"/api/store/getLocation",data:t,success:function(t){0==t.code&&t.data?e.currentPosition=t.data.formatted_addresses.recommend:e.currentPosition="未获取到定位"}})},enterDefaultStore:function(){this.defaultStoreInfo&&this.changeCurrentStore(this.defaultStoreInfo)},chooseStore:function(){this.$util.redirectTo("/pages_tool/store/list")},openSetting:function(){var t=this;e.openSetting({success:function(e){t.getLocation()}})},setPublicShare:function(){var e=this.$config.h5Domain+this.diyRoute;this.$store.state.globalStoreInfo;e.indexOf("?")>0?e+="&":e+="?",this.id?e+="id="+this.id:this.name&&(e+="name="+this.name),this.$util.setPublicShare({title:this.diyData.global.wechatShareTitle||this.diyData.global.title,desc:this.diyData.global.wechatShareDesc,link:e,imgUrl:this.diyData.global.wechatShareImage?this.$util.img(this.diyData.global.wechatShareImage):this.$util.img(this.siteInfo.logo_square)})}},onPageScroll:function(e){this.scrollTop=e.scrollTop,this.$refs.topNav&&(e.scrollTop>=20?this.$refs.topNav.navTopBg():this.$refs.topNav.unSetnavTopBg())},onPullDownRefresh:function(){this.getDiyMethod(),setTimeout((function(){e.stopPullDownRefresh()}),50)},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine}};t.default=c}).call(this,o("df3c")["default"])},9002:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"秒杀专区"}},9008:function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports["default"]=e.exports},"91ce":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"编辑收货地址",consignee:"姓名",consigneePlaceholder:"收货人姓名",mobile:"手机",mobilePlaceholder:"收货人手机号",telephone:"电话",telephonePlaceholder:"收货人固定电话（选填）",receivingCity:"地区",address:"详细地址",house:"门牌号",housePlaceholder:"请输入楼层门牌号",addressPlaceholder:"小区、街道、写字楼",save:"保存"}},"931d":function(e,t,o){var i=o("7647"),n=o("011a");e.exports=function(e,t,o){if(n())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,t);var a=new(e.bind.apply(e,r));return o&&i(a,o.prototype),a},e.exports.__esModule=!0,e.exports["default"]=e.exports},9451:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"积分中心"}},"96ec":function(e,t,o){"use strict";(function(e){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("7eb4")),r=i(o("3b2d")),a=i(o("ee10")),s=(i(o("0817")),{data:function(){return{skuId:0,goodsId:0,goodsSkuDetail:{goods_id:0,goods_service:[]},preview:0,contactData:{title:"",path:"",img:""},shareQuery:"",shareUrl:"",source_member:0,chatRoomParams:{},isIphoneX:!1,whetherCollection:0,posterParams:{},shareImg:"",navbarData:{title:"",topNavColor:"#ffffff",topNavBg:!1,navBarSwitch:!0,textNavColor:"#333333",moreLink:{name:""},navStyle:1,bgUrl:"",textImgPosLink:"left"},goodsFormVal:[]}},onLoad:function(t){if(this.preview=t.preview||0,this.isIphoneX=this.$util.uniappIsIPhoneX(),t.source_member&&(e.setStorageSync("source_member",t.source_member),this.source_member=t.source_member),this.storeToken&&e.getStorageSync("source_member")&&this.$util.onSourceMember(e.getStorageSync("source_member")),t.scene){var o=decodeURIComponent(t.scene);o=o.split("&"),o.length&&o.forEach((function(t){-1!=t.indexOf("m")&&e.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&e.setStorageSync("is_test",1)}))}},onShow:function(){},methods:{detailChangeVal:function(e){this.goodsFormVal=e},handleGoodsSkuData:function(){this.navbarData.title=this.goodsSkuDetail.goods_name.length>9?this.goodsSkuDetail.goods_name.substr(0,9)+"...":this.goodsSkuDetail.goods_name,this.$langConfig.title(this.navbarData.title),this.goodsSkuDetail.config&&(this.navbarData.navBarSwitch=this.goodsSkuDetail.config.nav_bar_switch),this.whetherCollection=this.goodsSkuDetail.is_collect,this.modifyGoodsInfo(),this.$refs.goodsDetailView&&this.$refs.goodsDetailView.init({sku_id:this.skuId,goods_id:this.goodsSkuDetail.goods_id,preview:this.preview,source_member:this.source_member,posterParams:this.posterParams,posterApi:this.posterApi,shareUrl:this.shareUrl,goodsRoute:this.goodsRoute,isVirtual:this.goodsSkuDetail.is_virtual,deliveryType:this.goodsSkuDetail.express_type,whetherCollection:this.goodsSkuDetail.is_collect,evaluateConfig:this.goodsSkuDetail.evaluate_config,evaluateList:this.goodsSkuDetail.evaluate_list,evaluateCount:this.goodsSkuDetail.evaluate_count,goods_class:this.goodsSkuDetail.goods_class,sale_store:this.goodsSkuDetail.sale_store}),this.goodsSkuDetail.video_url&&(this.switchMedia="video"),Array.isArray(this.goodsSkuDetail.sku_images)||(this.goodsSkuDetail.sku_images?this.goodsSkuDetail.sku_images=this.goodsSkuDetail.sku_images.split(","):this.goodsSkuDetail.sku_images=[]),this.goodsSkuDetail.goods_spec_format&&this.goodsSkuDetail.goods_image&&(Array.isArray(this.goodsSkuDetail.goods_image)||(this.goodsSkuDetail.goods_image=this.goodsSkuDetail.goods_image.split(",")),this.goodsSkuDetail.sku_images=this.goodsSkuDetail.goods_image.concat(this.goodsSkuDetail.sku_images));var t="",o=e.getSystemInfoSync();if(this.goodsSkuDetail.goods_image_list.forEach((function(e,i){"string"==typeof e.pic_spec&&(e.pic_spec=e.pic_spec.split("*"));var n=e.pic_spec[0]/o.windowWidth;e.pic_spec[0]=e.pic_spec[0]/n,e.pic_spec[1]=e.pic_spec[1]/n,(!t||t>e.pic_spec[1])&&(t=e.pic_spec[1])})),this.goodsSkuDetail.swiperHeight=t+"px",this.goodsSkuDetail.unit=this.goodsSkuDetail.unit||"件",this.goodsSkuDetail.sku_spec_format&&(this.goodsSkuDetail.sku_spec_format=JSON.parse(this.goodsSkuDetail.sku_spec_format)),this.goodsSkuDetail.goods_attr_format){var i=JSON.parse(this.goodsSkuDetail.goods_attr_format);this.goodsSkuDetail.goods_attr_format=this.$util.unique(i,"attr_id");for(var n=0;n<this.goodsSkuDetail.goods_attr_format.length;n++)for(var r=0;r<i.length;r++)this.goodsSkuDetail.goods_attr_format[n].attr_id==i[r].attr_id&&this.goodsSkuDetail.goods_attr_format[n].attr_value_id!=i[r].attr_value_id&&(this.goodsSkuDetail.goods_attr_format[n].attr_value_name+="、"+i[r].attr_value_name)}if(this.goodsSkuDetail.goods_spec_format&&(this.goodsSkuDetail.goods_spec_format=JSON.parse(this.goodsSkuDetail.goods_spec_format)),this.goodsSkuDetail.goods_service)for(var a in this.goodsSkuDetail.goods_service)this.goodsSkuDetail.goods_service[a]["icon"]=this.goodsSkuDetail.goods_service[a]["icon"]?JSON.parse(this.goodsSkuDetail.goods_service[a]["icon"]):"";this.contactData={title:this.goodsSkuDetail.sku_name,path:this.shareUrl,img:this.$util.img(this.goodsSkuDetail.sku_image,{size:"big"})},this.$refs.goodsPromotion&&this.$refs.goodsPromotion.refresh(this.goodsSkuDetail.goods_promotion),this.setPublicShare(),this.getBarrageData(),this.addonIsExist.form&&this.getGoodsForm()},refreshGoodsSkuDetail:function(e){var t=this;this.goodsSkuDetail=Object.assign({},this.goodsSkuDetail,e),this.$refs.goodsPromotion&&this.$refs.goodsPromotion.refresh(this.goodsSkuDetail.goods_promotion),this.$refs.goodsDetailView&&(this.goodsSkuDetail.unit=this.goodsSkuDetail.unit||"件",this.swiperCurrent>this.goodsSkuDetail.sku_images.length&&(this.swiperAutoplay=!0,this.swiperCurrent=1,setTimeout((function(){t.swiperAutoplay=!1}),40))),this.navbarData.title=this.goodsSkuDetail.goods_name.length>9?this.goodsSkuDetail.goods_name.substr(0,9)+"...":this.goodsSkuDetail.goods_name,this.$langConfig.title(this.navbarData.title),this.goodsSkuDetail.membercard&&(this.membercard=this.goodsSkuDetail.membercard)},goodsDetailViewInit:function(){this.$refs.goodsDetailView&&this.$refs.goodsDetailView.init({sku_id:this.skuId,goods_id:this.goodsSkuDetail.goods_id,preview:this.preview,source_member:this.source_member,posterParams:this.posterParams,posterApi:this.posterApi,shareUrl:this.shareUrl,goodsRoute:this.goodsRoute,isVirtual:this.goodsSkuDetail.is_virtual,deliveryType:this.goodsSkuDetail.express_type,whetherCollection:this.goodsSkuDetail.is_collect,evaluateConfig:this.goodsSkuDetail.evaluate_config,evaluateList:this.goodsSkuDetail.evaluate_list,evaluateCount:this.goodsSkuDetail.evaluate_count})},goHome:function(){this.preview||this.$util.redirectTo("/pages/index/index")},goCart:function(){this.preview||this.$util.redirectTo("/pages/goods/cart")},modifyGoodsInfo:function(){this.preview||(this.$api.sendRequest({url:"/api/goods/modifyclicks",data:{sku_id:this.skuId},success:function(e){}}),this.$api.sendRequest({url:"/api/goodsbrowse/add",data:{goods_id:this.goodsSkuDetail.goods_id,sku_id:this.skuId},success:function(e){}}))},editCollection:function(){var e=this;return(0,a.default)(n.default.mark((function t(){return n.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.$refs.goodsDetailView){t.next=4;break}return t.next=3,e.$refs.goodsDetailView.collection();case 3:e.whetherCollection=t.sent;case 4:case"end":return t.stop()}}),t)})))()},openSharePopup:function(){this.$refs.goodsDetailView&&this.$refs.goodsDetailView.openSharePopup()},getBarrageData:function(){var e=this;this.$api.sendRequest({url:"/api/goods/goodsbarrage",data:{goods_id:this.goodsSkuDetail.goods_id},success:function(t){if(0==t.code&&t.data){var o=[];for(var i in t.data.list)if(t.data.list[i]["title"]){var n=t.data.list[i]["title"].substr(0,1)+"*"+t.data.list[i]["title"].substr(t.data.list[i]["title"].length-1,1);o.push({img:t.data.list[i]["img"]?t.data.list[i]["img"]:e.$util.getDefaultImage().head,title:n+"已下单"})}e.goodsSkuDetail.barrageData=o}}})},setPublicShare:function(){var e=this.$config.h5Domain+this.shareUrl;this.memberInfo&&this.memberInfo.member_id&&(e+="&source_member="+this.memberInfo.member_id);var t=this.$store.state.globalStoreInfo;t&&(e+="&store_id="+t.store_id),this.$util.setPublicShare({title:this.goodsSkuDetail.goods_name,desc:"",link:e,imgUrl:"object"==(0,r.default)(this.goodsSkuDetail.goods_image)?this.goodsSkuDetail.goods_image[0]:this.goodsSkuDetail.goods_image.split(",")[0]})},getGoodsForm:function(){var e=this;this.$api.sendRequest({url:"/form/api/form/goodsform",data:{goods_id:this.goodsSkuDetail.goods_id},success:function(t){0==t.code&&t.data&&e.$set(e.goodsSkuDetail,"goods_form",t.data)}})}},onShareAppMessage:function(e){var t=this.shareUrl,o=this.$store.state.globalStoreInfo;return o&&(t+="&store_id="+o.store_id),this.memberInfo&&this.memberInfo.member_id&&(t+="&source_member="+this.memberInfo.member_id),{title:this.goodsSkuDetail.sku_name,imageUrl:this.shareImg?this.$util.img(this.shareImg):this.$util.img(this.goodsSkuDetail.sku_image,{size:"big"}),path:t,success:function(e){},fail:function(e){}}},onShareTimeline:function(){var e=this.shareQuery,t=this.$store.state.globalStoreInfo;return t&&(e+="&store_id="+t.store_id),this.memberInfo&&this.memberInfo.member_id&&(e+="&source_member="+this.memberInfo.member_id),{title:this.goodsSkuDetail.sku_name,query:e,imageUrl:this.$util.img(this.goodsSkuDetail.sku_image,{size:"big"})}}});t.default=s}).call(this,o("df3c")["default"])},"995e":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"会员卡"}},"9ae8":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"充值详情"}},"9ba6":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的关注"}},"9fc1":function(e,t,o){var i=o("3b2d")["default"];function n(){"use strict";
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e.exports=n=function(){return o},e.exports.__esModule=!0,e.exports["default"]=e.exports;var t,o={},r=Object.prototype,a=r.hasOwnProperty,s=Object.defineProperty||function(e,t,o){e[t]=o.value},c="function"==typeof Symbol?Symbol:{},l=c.iterator||"@@iterator",u=c.asyncIterator||"@@asyncIterator",d=c.toStringTag||"@@toStringTag";function f(e,t,o){return Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(t){f=function(e,t,o){return e[t]=o}}function p(e,t,o,i){var n=t&&t.prototype instanceof y?t:y,r=Object.create(n.prototype),a=new T(i||[]);return s(r,"_invoke",{value:O(e,o,a)}),r}function h(e,t,o){try{return{type:"normal",arg:e.call(t,o)}}catch(e){return{type:"throw",arg:e}}}o.wrap=p;var g="suspendedStart",m="executing",_="completed",v={};function y(){}function b(){}function w(){}var S={};f(S,l,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(A([])));x&&x!==r&&a.call(x,l)&&(S=x);var D=w.prototype=y.prototype=Object.create(S);function C(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function o(n,r,s,c){var l=h(e[n],e,r);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==i(d)&&a.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,s,c)}),(function(e){o("throw",e,s,c)})):t.resolve(d).then((function(e){u.value=e,s(u)}),(function(e){return o("throw",e,s,c)}))}c(l.arg)}var n;s(this,"_invoke",{value:function(e,i){function r(){return new t((function(t,n){o(e,i,t,n)}))}return n=n?n.then(r,r):r()}})}function O(e,o,i){var n=g;return function(r,a){if(n===m)throw Error("Generator is already running");if(n===_){if("throw"===r)throw a;return{value:t,done:!0}}for(i.method=r,i.arg=a;;){var s=i.delegate;if(s){var c=$(s,i);if(c){if(c===v)continue;return c}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(n===g)throw n=_,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n=m;var l=h(e,o,i);if("normal"===l.type){if(n=i.done?_:"suspendedYield",l.arg===v)continue;return{value:l.arg,done:i.done}}"throw"===l.type&&(n=_,i.method="throw",i.arg=l.arg)}}}function $(e,o){var i=o.method,n=e.iterator[i];if(n===t)return o.delegate=null,"throw"===i&&e.iterator["return"]&&(o.method="return",o.arg=t,$(e,o),"throw"===o.method)||"return"!==i&&(o.method="throw",o.arg=new TypeError("The iterator does not provide a '"+i+"' method")),v;var r=h(n,e.iterator,o.arg);if("throw"===r.type)return o.method="throw",o.arg=r.arg,o.delegate=null,v;var a=r.arg;return a?a.done?(o[e.resultName]=a.value,o.next=e.nextLoc,"return"!==o.method&&(o.method="next",o.arg=t),o.delegate=null,v):a:(o.method="throw",o.arg=new TypeError("iterator result is not an object"),o.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function M(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function A(e){if(e||""===e){var o=e[l];if(o)return o.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function o(){for(;++n<e.length;)if(a.call(e,n))return o.value=e[n],o.done=!1,o;return o.value=t,o.done=!0,o};return r.next=r}}throw new TypeError(i(e)+" is not iterable")}return b.prototype=w,s(D,"constructor",{value:w,configurable:!0}),s(w,"constructor",{value:b,configurable:!0}),b.displayName=f(w,d,"GeneratorFunction"),o.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},o.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,f(e,d,"GeneratorFunction")),e.prototype=Object.create(D),e},o.awrap=function(e){return{__await:e}},C(P.prototype),f(P.prototype,u,(function(){return this})),o.AsyncIterator=P,o.async=function(e,t,i,n,r){void 0===r&&(r=Promise);var a=new P(p(e,t,i,n),r);return o.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},C(D),f(D,d,"Generator"),f(D,l,(function(){return this})),f(D,"toString",(function(){return"[object Generator]"})),o.keys=function(e){var t=Object(e),o=[];for(var i in t)o.push(i);return o.reverse(),function e(){for(;o.length;){var i=o.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},o.values=A,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(M),!e)for(var o in this)"t"===o.charAt(0)&&a.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var o=this;function i(i,n){return s.type="throw",s.arg=e,o.next=i,n&&(o.method="next",o.arg=t),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n],s=r.completion;if("root"===r.tryLoc)return i("end");if(r.tryLoc<=this.prev){var c=a.call(r,"catchLoc"),l=a.call(r,"finallyLoc");if(c&&l){if(this.prev<r.catchLoc)return i(r.catchLoc,!0);if(this.prev<r.finallyLoc)return i(r.finallyLoc)}else if(c){if(this.prev<r.catchLoc)return i(r.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return i(r.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var r=n?n.completion:{};return r.type=e,r.arg=t,n?(this.method="next",this.next=n.finallyLoc,v):this.complete(r)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.finallyLoc===e)return this.complete(o.completion,o.afterLoc),M(o),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.tryLoc===e){var i=o.completion;if("throw"===i.type){var n=i.arg;M(o)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,o,i){return this.delegate={iterator:A(e),resultName:o,nextLoc:i},"next"===this.method&&(this.arg=t),v}},o}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},a29e:function(e,t,o){"use strict";(function(e){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("3df4"));function r(t,o,i){e.openLocation({latitude:t,longitude:o,name:i,fail:function(t){e.showModal({content:"打开地图失败，请稍后重试"})}})}function a(e,t,o){switch(o){case"gcj02":return[e,t];case"bd09":return n.default.bd09togcj02(e,t);case"wgs84":return n.default.wgs84togcj02(e,t);default:return[e,t]}}var s={openMap:function(e,t,o){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"gcj02",n=a(t,e,i);r(n[1],n[0],o)}};t.default=s}).call(this,o("df3c")["default"])},a485:function(e,t,o){"use strict";(function(e,o){function i(e,t){var o="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!o){if(Array.isArray(e)||(o=function(e,t){if(!e)return;if("string"===typeof e)return n(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);"Object"===o&&e.constructor&&(o=e.constructor.name);if("Map"===o||"Set"===o)return Array.from(e);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return n(e,t)}(e))||t&&e&&"number"===typeof e.length){o&&(e=o);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,c=!1;return{s:function(){o=o.call(e)},n:function(){var e=o.next();return s=e.done,e},e:function(e){c=!0,a=e},f:function(){try{s||null==o.return||o.return()}finally{if(c)throw a}}}}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,i=new Array(t);o<t;o++)i[o]=e[o];return i}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={trustTags:f("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,ruby,rt,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"),blockTags:f("address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section"),ignoreTags:f("area,base,canvas,embed,frame,head,iframe,input,link,map,meta,param,rp,script,source,style,textarea,title,track,wbr"),voidTags:f("area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),entities:{lt:"<",gt:">",quot:'"',apos:"'",ensp:" ",emsp:" ",nbsp:" ",semi:";",ndash:"–",mdash:"—",middot:"·",lsquo:"‘",rsquo:"’",ldquo:"“",rdquo:"”",bull:"•",hellip:"…",larr:"←",uarr:"↑",rarr:"→",darr:"↓"},tagStyle:{address:"font-style:italic",big:"display:inline;font-size:1.2em",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",mark:"background-color:yellow",pre:"font-family:monospace;white-space:pre",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",strike:"text-decoration:line-through",u:"text-decoration:underline"},svgDict:{animatetransform:"animateTransform",lineargradient:"linearGradient",viewbox:"viewBox",attributename:"attributeName",repeatcount:"repeatCount",repeatdur:"repeatDur",foreignobject:"foreignObject"}},a={},s=e.getSystemInfoSync(),c=s.windowWidth,l=s.system,u=f(" ,\r,\n,\t,\f"),d=0;function f(e){for(var t=Object.create(null),o=e.split(","),i=o.length;i--;)t[o[i]]=!0;return t}function p(e,t){var o=e.indexOf("&");while(-1!==o){var i=e.indexOf(";",o+3),n=void 0;if(-1===i)break;"#"===e[o+1]?(n=parseInt(("x"===e[o+2]?"0":"")+e.substring(o+2,i)),isNaN(n)||(e=e.substr(0,o)+String.fromCharCode(n)+e.substr(i+1))):(n=e.substring(o+1,i),(r.entities[n]||"amp"===n&&t)&&(e=e.substr(0,o)+(r.entities[n]||"&")+e.substr(i+1))),o=e.indexOf("&",o+1)}return e}function h(e){for(var t=e.length-1,o=t;o>=-1;o--)(-1===o||e[o].c||!e[o].name||"div"!==e[o].name&&"p"!==e[o].name&&"h"!==e[o].name[0]||(e[o].attrs.style||"").includes("inline"))&&(t-o>=5&&e.splice(o+1,t-o,{name:"div",attrs:{},children:e.slice(o+1,t+1)}),t=o-1)}function g(e){this.options=e||{},this.tagStyle=Object.assign({},r.tagStyle,this.options.tagStyle),this.imgList=e.imgList||[],this.imgList._unloadimgs=0,this.plugins=e.plugins||[],this.attrs=Object.create(null),this.stack=[],this.nodes=[],this.pre=(this.options.containerStyle||"").includes("white-space")&&this.options.containerStyle.includes("pre")?2:0}function m(e){this.handler=e}g.prototype.parse=function(e){for(var t=this.plugins.length;t--;)this.plugins[t].onUpdate&&(e=this.plugins[t].onUpdate(e,r)||e);new m(this).parse(e);while(this.stack.length)this.popNode();return this.nodes.length>50&&h(this.nodes),this.nodes},g.prototype.expose=function(){for(var e=this.stack.length;e--;){var t=this.stack[e];if(t.c||"a"===t.name||"video"===t.name||"audio"===t.name)return;t.c=1}},g.prototype.hook=function(e){for(var t=this.plugins.length;t--;)if(this.plugins[t].onParse&&!1===this.plugins[t].onParse(e,this))return!1;return!0},g.prototype.getUrl=function(e){var t=this.options.domain;return"/"===e[0]?"/"===e[1]?e=(t?t.split("://")[0]:"http")+":"+e:t&&(e=t+e):e.includes("data:")||e.includes("://")||t&&(e=t+"/"+e),e},g.prototype.parseStyle=function(e){var t=e.attrs,o=(this.tagStyle[e.name]||"").split(";").concat((t.style||"").split(";")),i={},n="";t.id&&!this.xml&&(this.options.useAnchor?this.expose():"img"!==e.name&&"a"!==e.name&&"video"!==e.name&&"audio"!==e.name&&(t.id=void 0)),t.width&&(i.width=parseFloat(t.width)+(t.width.includes("%")?"%":"px"),t.width=void 0),t.height&&(i.height=parseFloat(t.height)+(t.height.includes("%")?"%":"px"),t.height=void 0);for(var r=0,a=o.length;r<a;r++){var s=o[r].split(":");if(!(s.length<2)){var l=s.shift().trim().toLowerCase(),d=s.join(":").trim();if("-"===d[0]&&d.lastIndexOf("-")>0||d.includes("safe"))n+=";".concat(l,":").concat(d);else if(!i[l]||d.includes("import")||!i[l].includes("import")){if(d.includes("url")){var f=d.indexOf("(")+1;if(f){while('"'===d[f]||"'"===d[f]||u[d[f]])f++;d=d.substr(0,f)+this.getUrl(d.substr(f))}}else d.includes("rpx")&&(d=d.replace(/[0-9.]+\s*rpx/g,(function(e){return parseFloat(e)*c/750+"px"})));i[l]=d}}}return e.attrs.style=n,i},g.prototype.onTagName=function(e){this.tagName=this.xml?e:e.toLowerCase(),"svg"===this.tagName&&(this.xml=(this.xml||0)+1,r.ignoreTags.style=void 0)},g.prototype.onAttrName=function(e){e=this.xml?e:e.toLowerCase(),"data-"===e.substr(0,5)?"data-src"!==e||this.attrs.src?"img"===this.tagName||"a"===this.tagName?this.attrName=e:this.attrName=void 0:this.attrName="src":(this.attrName=e,this.attrs[e]="T")},g.prototype.onAttrVal=function(e){var t=this.attrName||"";"style"===t||"href"===t?this.attrs[t]=p(e,!0):t.includes("src")?this.attrs[t]=this.getUrl(p(e,!0)):t&&(this.attrs[t]=e)},g.prototype.onOpenTag=function(e){var t=Object.create(null);t.name=this.tagName,t.attrs=this.attrs,this.options.nodes.length&&(t.type="node"),this.attrs=Object.create(null);var o=t.attrs,i=this.stack[this.stack.length-1],n=i?i.children:this.nodes,s=this.xml?e:r.voidTags[t.name];if(a[t.name]&&(o.class=a[t.name]+(o.class?" "+o.class:"")),"embed"===t.name){var l=o.src||"";l.includes(".mp4")||l.includes(".3gp")||l.includes(".m3u8")||(o.type||"").includes("video")?t.name="video":(l.includes(".mp3")||l.includes(".wav")||l.includes(".aac")||l.includes(".m4a")||(o.type||"").includes("audio"))&&(t.name="audio"),o.autostart&&(o.autoplay="T"),o.controls="T"}if("video"!==t.name&&"audio"!==t.name||("video"!==t.name||o.id||(o.id="v"+d++),o.controls||o.autoplay||(o.controls="T"),t.src=[],o.src&&(t.src.push(o.src),o.src=void 0),this.expose()),s){if(!this.hook(t)||r.ignoreTags[t.name])return void("base"!==t.name||this.options.domain?"source"===t.name&&i&&("video"===i.name||"audio"===i.name)&&o.src&&i.src.push(o.src):this.options.domain=o.href);var u=this.parseStyle(t);if("img"===t.name){if(o.src&&(o.src.includes("webp")&&(t.webp="T"),o.src.includes("data:")&&"all"!==this.options.previewImg&&!o["original-src"]&&(o.ignore="T"),!o.ignore||t.webp||o.src.includes("cloud://"))){for(var f=this.stack.length;f--;){var p=this.stack[f];"a"===p.name&&(t.a=p.attrs),"table"!==p.name||t.webp||o.src.includes("cloud://")||(!u.display||u.display.includes("inline")?t.t="inline-block":t.t=u.display,u.display=void 0);var h=p.attrs.style||"";if(!h.includes("flex:")||h.includes("flex:0")||h.includes("flex: 0")||u.width&&!(parseInt(u.width)>100))if(h.includes("flex")&&"100%"===u.width)for(var g=f+1;g<this.stack.length;g++){var m=this.stack[g].attrs.style||"";if(!m.includes(";width")&&!m.includes(" width")&&0!==m.indexOf("width")){u.width="";break}}else h.includes("inline-block")&&(u.width&&"%"===u.width[u.width.length-1]?(p.attrs.style+=";max-width:"+u.width,u.width=""):p.attrs.style+=";max-width:100%");else{u.width="100% !important",u.height="";for(var _=f+1;_<this.stack.length;_++)this.stack[_].attrs.style=(this.stack[_].attrs.style||"").replace("inline-","")}p.c=1}o.i=this.imgList.length.toString();var v=o["original-src"]||o.src;if(this.imgList.includes(v)){var y=v.indexOf("://");if(-1!==y){y+=3;for(var b=v.substr(0,y);y<v.length;y++){if("/"===v[y])break;b+=Math.random()>.5?v[y].toUpperCase():v[y]}b+=v.substr(y),v=b}}this.imgList.push(v),t.t||(this.imgList._unloadimgs+=1)}"inline"===u.display&&(u.display=""),o.ignore&&(u["max-width"]=u["max-width"]||"100%",o.style+=";-webkit-touch-callout:none"),parseInt(u.width)>c&&(u.height=void 0),isNaN(parseInt(u.width))||(t.w="T"),!isNaN(parseInt(u.height))&&(!u.height.includes("%")||i&&(i.attrs.style||"").includes("height"))&&(t.h="T"),t.w&&t.h&&u["object-fit"]&&("contain"===u["object-fit"]?t.m="aspectFit":"cover"===u["object-fit"]&&(t.m="aspectFill"))}else if("svg"===t.name)return n.push(t),this.stack.push(t),void this.popNode();for(var w in u)u[w]&&(o.style+=";".concat(w,":").concat(u[w].replace(" !important","")));o.style=o.style.substr(1)||void 0}else("pre"===t.name||(o.style||"").includes("white-space")&&o.style.includes("pre"))&&2!==this.pre&&(this.pre=t.pre=1),t.children=[],this.stack.push(t);n.push(t)},g.prototype.onCloseTag=function(e){var t;for(e=this.xml?e:e.toLowerCase(),t=this.stack.length;t--;)if(this.stack[t].name===e)break;if(-1!==t)while(this.stack.length>t)this.popNode();else if("p"===e||"br"===e){var o=this.stack.length?this.stack[this.stack.length-1].children:this.nodes;o.push({name:e,attrs:{class:a[e]||"",style:this.tagStyle[e]||""}})}},g.prototype.popNode=function(){var t=this.stack.pop(),n=t.attrs,a=t.children,s=this.stack[this.stack.length-1],l=s?s.children:this.nodes;if(!this.hook(t)||r.ignoreTags[t.name])return"title"===t.name&&a.length&&"text"===a[0].type&&this.options.setTitle&&e.setNavigationBarTitle({title:a[0].text}),void l.pop();if(t.pre&&2!==this.pre){this.pre=t.pre=void 0;for(var u=this.stack.length;u--;)this.stack[u].pre&&(this.pre=1)}var d={};if("svg"===t.name){if(this.xml>1)return void this.xml--;var f="",p=n.style;return n.style="",n.xmlns="http://www.w3.org/2000/svg",function e(t){if("text"!==t.type){var o=r.svgDict[t.name]||t.name;if("foreignObject"===o){var n,a=i(t.children||[]);try{for(a.s();!(n=a.n()).done;){var s=n.value;if(s.attrs&&!s.attrs.xmlns){s.attrs.xmlns="http://www.w3.org/1999/xhtml";break}}}catch(d){a.e(d)}finally{a.f()}}for(var c in f+="<"+o,t.attrs){var l=t.attrs[c];l&&(f+=" ".concat(r.svgDict[c]||c,'="').concat(l.replace(/"/g,""),'"'))}if(t.children){f+=">";for(var u=0;u<t.children.length;u++)e(t.children[u]);f+="</"+o+">"}else f+="/>"}else f+=t.text}(t),t.name="img",t.attrs={src:"data:image/svg+xml;utf8,"+f.replace(/#/g,"%23"),style:p,ignore:"T"},t.children=void 0,this.xml=!1,void(r.ignoreTags.style=!0)}if(n.align&&("table"===t.name?"center"===n.align?d["margin-inline-start"]=d["margin-inline-end"]="auto":d.float=n.align:d["text-align"]=n.align,n.align=void 0),n.dir&&(d.direction=n.dir,n.dir=void 0),"font"===t.name&&(n.color&&(d.color=n.color,n.color=void 0),n.face&&(d["font-family"]=n.face,n.face=void 0),n.size)){var g=parseInt(n.size);isNaN(g)||(g<1?g=1:g>7&&(g=7),d["font-size"]=["x-small","small","medium","large","x-large","xx-large","xxx-large"][g-1]),n.size=void 0}if((n.class||"").includes("align-center")&&(d["text-align"]="center"),Object.assign(d,this.parseStyle(t)),"table"!==t.name&&parseInt(d.width)>c&&(d["max-width"]="100%",d["box-sizing"]="border-box"),r.blockTags[t.name]?t.name="div":r.trustTags[t.name]||this.xml||(t.name="span"),"a"===t.name||"ad"===t.name)this.expose();else if("video"===t.name)(d.height||"").includes("auto")&&(d.height=void 0);else if("ul"!==t.name&&"ol"!==t.name||!t.c)if("table"===t.name){var m=parseFloat(n.cellpadding),_=parseFloat(n.cellspacing),v=parseFloat(n.border),y=d["border-color"],b=d["border-style"];if(t.c&&(isNaN(m)&&(m=2),isNaN(_)&&(_=2)),v&&(n.style+=";border:".concat(v,"px ").concat(b||"solid"," ").concat(y||"gray")),t.flag&&t.c){d.display="grid","collapse"===d["border-collapse"]&&(d["border-collapse"]=void 0,_=0),_?(d["grid-gap"]=_+"px",d.padding=_+"px"):v&&(n.style+=";border-left:0;border-top:0");var w=[],S=[],k=[],x={};(function e(t){for(var o=0;o<t.length;o++)if("tr"===t[o].name)S.push(t[o]);else if("colgroup"===t[o].name){var n,r=1,a=i(t[o].children||[]);try{for(a.s();!(n=a.n()).done;){var s=n.value;if("col"===s.name){var c=s.attrs.style||"",l=c.indexOf("width")?c.indexOf(";width"):0;if(-1!==l){var u=c.indexOf(";",l+6);-1===u&&(u=c.length),w[r]=c.substring(l?l+7:6,u)}r+=1}}}catch(d){a.e(d)}finally{a.f()}}else e(t[o].children||[])})(a);for(var D=1;D<=S.length;D++){for(var C=1,P=0;P<S[D-1].children.length;P++){var O=S[D-1].children[P];if("td"===O.name||"th"===O.name){while(x[D+"."+C])C++;var $=O.attrs.style||"",j=$.indexOf("width")?$.indexOf(";width"):0;if(-1!==j){var M=$.indexOf(";",j+6);-1===M&&(M=$.length),O.attrs.colspan||(w[C]=$.substring(j?j+7:6,M)),$=$.substr(0,j)+$.substr(M)}if($+=";display:flex",j=$.indexOf("vertical-align"),-1!==j){var T=$.substr(j+15,10);T.includes("middle")?$+=";align-items:center":T.includes("bottom")&&($+=";align-items:flex-end")}else $+=";align-items:center";if(j=$.indexOf("text-align"),-1!==j){var A=$.substr(j+11,10);A.includes("center")?$+=";justify-content: center":A.includes("right")&&($+=";justify-content: right")}if($=(v?";border:".concat(v,"px ").concat(b||"solid"," ").concat(y||"gray")+(_?"":";border-right:0;border-bottom:0"):"")+(m?";padding:".concat(m,"px"):"")+";"+$,O.attrs.colspan&&($+=";grid-column-start:".concat(C,";grid-column-end:").concat(C+parseInt(O.attrs.colspan)),O.attrs.rowspan||($+=";grid-row-start:".concat(D,";grid-row-end:").concat(D+1)),C+=parseInt(O.attrs.colspan)-1),O.attrs.rowspan){$+=";grid-row-start:".concat(D,";grid-row-end:").concat(D+parseInt(O.attrs.rowspan)),O.attrs.colspan||($+=";grid-column-start:".concat(C,";grid-column-end:").concat(C+1));for(var I=1;I<O.attrs.rowspan;I++)for(var F=0;F<(O.attrs.colspan||1);F++)x[D+I+"."+(C-F)]=1}$&&(O.attrs.style=$),k.push(O),C++}}if(1===D){for(var E="",L=1;L<C;L++)E+=(w[L]?w[L]:"auto")+" ";d["grid-template-columns"]=E}}t.children=k}else t.c&&(d.display="table"),isNaN(_)||(d["border-spacing"]=_+"px"),(v||m)&&function e(t){for(var o=0;o<t.length;o++){var i=t[o];"th"===i.name||"td"===i.name?(v&&(i.attrs.style="border:".concat(v,"px ").concat(b||"solid"," ").concat(y||"gray",";").concat(i.attrs.style||"")),m&&(i.attrs.style="padding:".concat(m,"px;").concat(i.attrs.style||""))):i.children&&e(i.children)}}(a);if(this.options.scrollTable&&!(n.style||"").includes("inline")){var R=Object.assign({},t);t.name="div",t.attrs={style:"overflow:auto"},t.children=[R],n=R.attrs}}else if(("tbody"===t.name||"tr"===t.name)&&t.flag&&t.c)t.flag=void 0,function e(t){for(var o=0;o<t.length;o++)if("td"===t[o].name)for(var i=0,n=["color","background","background-color"];i<n.length;i++){var r=n[i];d[r]&&(t[o].attrs.style=r+":"+d[r]+";"+(t[o].attrs.style||""))}else e(t[o].children||[])}(a);else if("td"!==t.name&&"th"!==t.name||!n.colspan&&!n.rowspan)if("ruby"===t.name){t.name="span";for(var N=0;N<a.length-1;N++)"text"===a[N].type&&"rt"===a[N+1].name&&(a[N]={name:"div",attrs:{style:"display:inline-block;text-align:center"},children:[{name:"div",attrs:{style:"font-size:50%;"+(a[N+1].attrs.style||"")},children:a[N+1].children},a[N]]},a.splice(N+1,1))}else t.c&&function(e){e.c=2;for(var t=e.children.length;t--;){var o=e.children[t];o.c&&"table"!==o.name||(e.c=1)}}(t);else for(var U=this.stack.length;U--;)"table"!==this.stack[U].name&&"tbody"!==this.stack[U].name&&"tr"!==this.stack[U].name||(this.stack[U].flag=1);else{var B={a:"lower-alpha",A:"upper-alpha",i:"lower-roman",I:"upper-roman"};B[n.type]&&(n.style+=";list-style-type:"+B[n.type],n.type=void 0);for(var z=a.length;z--;)"li"===a[z].name&&(a[z].c=1)}if((d.display||"").includes("flex")&&!t.c)for(var V=a.length;V--;){var G=a[V];G.f&&(G.attrs.style=(G.attrs.style||"")+G.f,G.f=void 0)}var q=s&&((s.attrs.style||"").includes("flex")||(s.attrs.style||"").includes("grid"))&&!(t.c&&o.getNFCAdapter);for(var H in q&&(t.f=";max-width:100%"),a.length>=50&&t.c&&!(d.display||"").includes("flex")&&h(a),d)if(d[H]){var W=";".concat(H,":").concat(d[H].replace(" !important",""));q&&(H.includes("flex")&&"flex-direction"!==H||"align-self"===H||H.includes("grid")||"-"===d[H][0]||H.includes("width")&&W.includes("%"))?(t.f+=W,"width"===H&&(n.style+=";width:100%")):n.style+=W}n.style=n.style.substr(1)||void 0},g.prototype.onText=function(t){if(!this.pre){for(var o,i="",n=0,r=t.length;n<r;n++)u[t[n]]?(" "!==i[i.length-1]&&(i+=" "),"\n"!==t[n]||o||(o=!0)):i+=t[n];if(" "===i&&o)return;t=i}var a=Object.create(null);if(a.type="text",a.text=p(t),this.hook(a)){"force"===this.options.selectable&&l.includes("iOS")&&!e.canIUse("rich-text.user-select")&&this.expose();var s=this.stack.length?this.stack[this.stack.length-1].children:this.nodes;s.push(a)}},m.prototype.parse=function(e){this.content=e||"",this.i=0,this.start=0,this.state=this.text;for(var t=this.content.length;-1!==this.i&&this.i<t;)this.state()},m.prototype.checkClose=function(e){var t="/"===this.content[this.i];return!!(">"===this.content[this.i]||t&&">"===this.content[this.i+1])&&(e&&this.handler[e](this.content.substring(this.start,this.i)),this.i+=t?2:1,this.start=this.i,this.handler.onOpenTag(t),"script"===this.handler.tagName?(this.i=this.content.indexOf("</",this.i),-1!==this.i&&(this.i+=2,this.start=this.i),this.state=this.endTag):this.state=this.text,!0)},m.prototype.text=function(){if(this.i=this.content.indexOf("<",this.i),-1!==this.i){var e=this.content[this.i+1];if(e>="a"&&e<="z"||e>="A"&&e<="Z")this.start!==this.i&&this.handler.onText(this.content.substring(this.start,this.i)),this.start=++this.i,this.state=this.tagName;else if("/"===e||"!"===e||"?"===e){this.start!==this.i&&this.handler.onText(this.content.substring(this.start,this.i));var t=this.content[this.i+2];if("/"===e&&(t>="a"&&t<="z"||t>="A"&&t<="Z"))return this.i+=2,this.start=this.i,void(this.state=this.endTag);var o="--\x3e";"!"===e&&"-"===this.content[this.i+2]&&"-"===this.content[this.i+3]||(o=">"),this.i=this.content.indexOf(o,this.i),-1!==this.i&&(this.i+=o.length,this.start=this.i)}else this.i++}else this.start<this.content.length&&this.handler.onText(this.content.substring(this.start,this.content.length))},m.prototype.tagName=function(){if(u[this.content[this.i]]){this.handler.onTagName(this.content.substring(this.start,this.i));while(u[this.content[++this.i]]);this.i<this.content.length&&!this.checkClose()&&(this.start=this.i,this.state=this.attrName)}else this.checkClose("onTagName")||this.i++},m.prototype.attrName=function(){var e=this.content[this.i];if(u[e]||"="===e){this.handler.onAttrName(this.content.substring(this.start,this.i));var t="="===e,o=this.content.length;while(++this.i<o)if(e=this.content[this.i],!u[e]){if(this.checkClose())return;if(t)return this.start=this.i,void(this.state=this.attrVal);if("="!==this.content[this.i])return this.start=this.i,void(this.state=this.attrName);t=!0}}else this.checkClose("onAttrName")||this.i++},m.prototype.attrVal=function(){var e=this.content[this.i],t=this.content.length;if('"'===e||"'"===e){if(this.start=++this.i,this.i=this.content.indexOf(e,this.i),-1===this.i)return;this.handler.onAttrVal(this.content.substring(this.start,this.i))}else for(;this.i<t;this.i++){if(u[this.content[this.i]]){this.handler.onAttrVal(this.content.substring(this.start,this.i));break}if(this.checkClose("onAttrVal"))return}while(u[this.content[++this.i]]);this.i<t&&!this.checkClose()&&(this.start=this.i,this.state=this.attrName)},m.prototype.endTag=function(){var e=this.content[this.i];if(u[e]||">"===e||"/"===e){if(this.handler.onCloseTag(this.content.substring(this.start,this.i)),">"!==e&&(this.i=this.content.indexOf(">",this.i),-1===this.i))return;this.start=++this.i,this.state=this.text}else this.i++};var _=g;t.default=_}).call(this,o("df3c")["default"],o("3223")["default"])},a4ac:function(e,t,o){"use strict";(function(e){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("af34")),r={data:function(){return{listStyle:"",loadingType:"loading",orderType:"",priceOrder:"desc",categoryList:[],goodsList:[],order:"",sort:"desc",showScreen:!1,keyword:"",categoryId:0,minPrice:"",maxPrice:"",isFreeShipping:!1,isIphoneX:!1,coupon:0,emptyShow:!1,isList:!0,share_title:"",count:0,category_title:"",coupon_name:"",listHeight:[],listPosition:[],debounce:null,brandId:0,brandList:[],config:{fontWeight:!1,padding:0,cartEvent:"detail",text:"购买",textColor:"#FFFFFF",theme:"default",aroundRadius:25,control:!0,bgColor:"#FF6A00",style:"button",iconDiy:{iconType:"icon",icon:"",style:{fontSize:"60",iconBgColor:[],iconBgColorDeg:0,iconBgImg:"",bgRadius:0,iconColor:["#000000"],iconColorDeg:0}}}}},onLoad:function(t){var o=this;if(this.categoryId=t.category_id||0,this.keyword=t.keyword||"",this.coupon=t.coupon||0,this.goods_id_arr=t.goods_id_arr||0,this.brandId=t.brand_id||0,this.loadCategoryList(),this.getBrandList(),this.isIphoneX=this.$util.uniappIsIPhoneX(),t.source_member&&e.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(o.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&e.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&e.setStorageSync("is_test",1)}))}e.onWindowResize((function(e){o.debounce&&clearTimeout(o.debounce),o.waterfallflow(0)}))},onShow:function(){this.storeToken&&e.getStorageSync("source_member")&&this.$util.onSourceMember(e.getStorageSync("source_member"))},onShareAppMessage:function(e){var t="搜索到"+this.count+"件“"+this.keyword+this.category_title+this.coupon_name+"”相关的优质商品",o=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),i=o.path;return{title:t,path:i,success:function(e){},fail:function(e){}}},onShareTimeline:function(){var e="搜索到"+this.count+"件“"+this.keyword+this.category_title+this.coupon_name+"”相关的优质商品",t=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),o=t.query;return{title:e,query:o,imageUrl:""}},methods:{couponInfo:function(e){var t=this;return new Promise((function(o){t.$api.sendRequest({url:"/coupon/api/coupon/typeinfo",data:{coupon_type_id:e},success:function(e){e.code>=0&&o(e.data.coupon_name)}})}))},share_select:function(e,t){return new Promise((function(o){e.forEach((function(e){e.category_id==t&&o(e.category_name),e.child_list&&e.child_list.length>0&&e.child_list.forEach((function(e){e.category_id==t&&o(e.category_name),e.child_list&&e.child_list.length>0&&e.child_list.forEach((function(e){e.category_id==t&&o(e.category_name)}))}))}))}))},loadCategoryList:function(){var e=this;this.$api.sendRequest({url:"/api/goodscategory/tree",success:function(t){null!=t.data&&(e.categoryList=t.data)}})},getGoodsList:function(e){var t=this;this.$api.sendRequest({url:"/api/goodssku/page",data:{page:e.num,page_size:e.size,keyword:this.keyword,category_id:this.categoryId,brand_id:this.brandId,min_price:this.minPrice,max_price:this.maxPrice,is_free_shipping:this.isFreeShipping?1:0,order:this.order,sort:this.sort,coupon:this.coupon,goods_id_arr:this.goods_id_arr},success:function(o){var i=[],n=o.message;0==o.code&&o.data?(t.count=o.data.count,0==o.data.page_count&&(t.emptyShow=!0),i=o.data.list,i=i.map((function(e){return e.id=t.genNonDuplicate(),e}))):t.$util.showToast({title:n}),t.category_title="",t.coupon_name="",o.data.config&&(t.config=o.data.config),t.categoryId&&t.share_select(t.categoryList,t.categoryId).then((function(e){t.category_title=e})),t.coupon&&t.couponInfo(t.coupon).then((function(e){t.coupon_name=e})),e.endSuccess(i.length),1==e.num&&(t.goodsList=[]),t.goodsList=t.goodsList.concat(i),t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.waterfallflow(10*(e.num-1))},fail:function(o){e.endErr(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},changeListStyle:function(){this.isList=!this.isList,this.waterfallflow(0)},sortTabClick:function(e){if("sale_num"==e)this.order="sale_num",this.sort="desc";else if("discount_price"==e)this.order="discount_price",this.sort="desc";else{if("screen"==e)return void(this.showScreen=!0);this.order="",this.sort=""}this.orderType===e&&"discount_price"!==e||(this.orderType=e,"discount_price"===e?(this.priceOrder="asc"===this.priceOrder?"desc":"asc",this.sort=this.priceOrder):this.priceOrder="",this.emptyShow=!1,this.goodsList=[],this.$refs.mescroll.refresh())},toDetail:function(e){this.$util.redirectTo("/pages/goods/detail",{goods_id:e.goods_id})},search:function(){this.emptyShow=!1,this.goodsList=[],this.$refs.mescroll.refresh()},selectedCategory:function(e){this.categoryId=e},screenData:function(){if(""!=this.minPrice||""!=this.maxPrice){if(!Number(this.maxPrice)&&this.maxPrice)return void this.$util.showToast({title:"请输入最高价"});if(Number(this.minPrice)<0||Number(this.maxPrice)<0)return void this.$util.showToast({title:"筛选价格不能小于0"});if(""!=this.minPrice&&Number(this.minPrice)>Number(this.maxPrice)&&this.maxPrice)return void this.$util.showToast({title:"最低价不能大于最高价"});if(""!=this.maxPrice&&Number(this.maxPrice)<Number(this.minPrice))return void this.$util.showToast({title:"最高价不能小于最低价"})}this.emptyShow=!1,this.goodsList=[],this.$refs.mescroll.refresh(),this.showScreen=!1},resetData:function(){this.categoryId=0,this.minPrice="",this.maxPrice="",this.isFreeShipping=!1},goodsImg:function(e){var t=e.split(",");return t[0]?this.$util.img(t[0],{size:"mid"}):this.$util.getDefaultImage().goods},imgError:function(e){this.goodsList[e].goods_image=this.$util.getDefaultImage().goods},showPrice:function(e){var t=e.discount_price;return e.member_price&&parseFloat(e.member_price)<parseFloat(t)&&(t=e.member_price),t},showMarketPrice:function(e){if(e.market_price_show){var t=this.showPrice(e);if(e.market_price>0)return e.market_price;if(parseFloat(e.price)>parseFloat(t))return e.price}return""},goodsTag:function(e){return e.label_name||""},waterfallflow:function(){var t=this,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.isList||this.$nextTick((function(){setTimeout((function(){var i=[],r=[];0!=o&&(i=t.listHeight,r=t.listPosition);var a=e.createSelectorQuery().in(t);a.selectAll(".double-column .goods-item").boundingClientRect((function(a){for(var s=o;s<a.length;s++)if(s<2){var c={};c.top=e.upx2px(20)+"px",c.left=s%2==0?a[s].width*s+"px":a[s].width*s+s%2*e.upx2px(30)+"px",r[s]=c,i[s]=a[s].height+e.upx2px(20)}else(function(){var t=Math.min.apply(Math,(0,n.default)(i)),o=i.findIndex((function(e){return e===t})),c={};c.top=t+e.upx2px(20)+"px",c.left=r[o].left,r[s]=c,i[o]+=a[s].height+e.upx2px(20)})();t.listHeight=i,t.listPosition=r})).exec()}),50)}))},getBrandList:function(){var e=this;this.$api.sendRequest({url:"/api/goodsbrand/page",data:{page:1,page_size:0},success:function(t){if(0==t.code&&t.data){var o=t.data;e.brandList=o.list}}})},addCart:function(e){},genNonDuplicate:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;return Number(Math.random().toString().substr(3,e)+Date.now()).toString(36)}}};t.default=r}).call(this,o("df3c")["default"])},a624:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"订单详情"}},a708:function(e,t,o){var i=o("6454");e.exports=function(e){if(Array.isArray(e))return i(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},aa6a:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"礼品订单"}},abab:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"支付方式",paymentAmount:"支付金额",confirmPayment:"确认支付",seeOrder:"查看订单"}},acc9:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},aee2:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"核销明细"}},af34:function(e,t,o){var i=o("a708"),n=o("b893"),r=o("6382"),a=o("9008");e.exports=function(e){return i(e)||n(e)||r(e)||a()},e.exports.__esModule=!0,e.exports["default"]=e.exports},af87:function(e,t,o){"use strict";(function(e,i){var n=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(o("7eb4")),a=n(o("3b2d")),s=n(o("ee10")),c=n(o("2f8f")),l=n(o("387c")),u=n(o("f944")),d=o("edd0"),f={redirectTo:function(t,o,i){var n=t,r=["/pages/index/index","/pages/goods/category","/pages/goods/cart","/pages/member/index"];void 0!=o&&Object.keys(o).forEach((function(e){-1!=n.indexOf("?")?n+="&"+e+"="+o[e]:n+="?"+e+"="+o[e]}));for(var a=0;a<r.length;a++)if(0==n.indexOf(r[a]))return void e.switchTab({url:n});switch(i){case"tabbar":e.switchTab({url:n});break;case"redirectTo":e.redirectTo({url:n});break;case"reLaunch":e.reLaunch({url:n});break;default:e.navigateTo({url:n})}},img:function(e,t){var o="";if(void 0!=e&&""!=e){if(e.split(",").length>1&&(e=e.split(",")[0]),t&&e!=this.getDefaultImage().goods){var i=e.split("."),n=i[i.length-1];i.pop(),i[i.length-1]=i[i.length-1]+"_"+t.size.toUpperCase(),i.push(n),e=i.join(".")}o=-1==e.indexOf("http://")&&-1==e.indexOf("https://")?c.default.imgDomain+"/"+e:e,-1!=c.default.h5Domain.indexOf("https://")&&(o=o.replace("http://","https://"))}return o},timeStampTurnTime:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Y-m-d H:i:s";if(void 0!=e&&""!=e&&e>0){var o=new Date;o.setTime(1e3*e);var i=o.getFullYear(),n=o.getMonth()+1;n=n<10?"0"+n:n;var r=o.getDate();r=r<10?"0"+r:r;var a=o.getHours();a=a<10?"0"+a:a;var s=o.getMinutes(),c=o.getSeconds();return s=s<10?"0"+s:s,c=c<10?"0"+c:c,t=t.replace("Y",i),t=t.replace("m",n),t=t.replace("d",r),t=t.replace("H",a),t=t.replace("i",s),t=t.replace("s",c),t}return""},timeTurnTimeStamp:function(e){var t=e.split(" ",2),o=(t[0]?t[0]:"").split("-",3),i=(t[1]?t[1]:"").split(":",3);return new Date(parseInt(o[0],10)||null,(parseInt(o[1],10)||1)-1,parseInt(o[2],10)||null,parseInt(i[0],10)||null,parseInt(i[1],10)||null,parseInt(i[2],10)||null).getTime()/1e3},countDown:function(e){var t=0,o=0,i=0,n=0;return e>0&&(t=Math.floor(e/86400),o=Math.floor(e/3600)-24*t,i=Math.floor(e/60)-24*t*60-60*o,n=Math.floor(e)-24*t*60*60-60*o*60-60*i),t<10&&(t="0"+t),o<10&&(o="0"+o),i<10&&(i="0"+i),n<10&&(n="0"+n),{d:t,h:o,i:i,s:n}},unique:function(e,t){var o=new Map;return e.filter((function(e){return!o.has(e[t])&&o.set(e[t],1)}))},inArray:function(e,t){return null==t?-1:t.indexOf(e)},getDay:function(e){var t=new Date,o=t.getTime()+864e5*e;t.setTime(o);var i=function(e){var t=e;return 1==e.toString().length&&(t="0"+e),t},n=t.getFullYear(),r=t.getMonth(),a=t.getDate(),s=t.getDay(),c=parseInt(t.getTime()/1e3);r=i(r+1),a=i(a);return{t:c,y:n,m:r,d:a,w:["周日","周一","周二","周三","周四","周五","周六"][s]}},upload:function(t,o,i,n){var a=this,c={token:l.default.state.token,app_type:"weapp",app_type_name:"微信小程序"};c=Object.assign(c,o);var u=t,d=this;e.chooseImage({count:u,sizeType:["compressed"],sourceType:["album","camera"],success:function(){var t=(0,s.default)(r.default.mark((function t(s){var l,u,f,p,h;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:l=s.tempFilePaths,u=c,f=[],e.showLoading({title:"图片上传中"}),p=0;case 5:if(!(p<l.length)){t.next=21;break}return t.prev=6,t.next=9,d.upload_file_server(l[p],u,o.path,n);case 9:h=t.sent,f.push(h),t.next=17;break;case 13:return t.prev=13,t.t0=t["catch"](6),a.showToast({title:t.t0}),t.abrupt("break",21);case 17:f.length==l.length&&(e.hideLoading(),e.showToast({title:"上传成功",icon:"none"}),"function"==typeof i&&i(f));case 18:p++,t.next=5;break;case 21:case"end":return t.stop()}}),t,null,[[6,13]])})));return function(e){return t.apply(this,arguments)}}(),fail:function(t){"chooseImage:fail cancel"!=t.errMsg&&e.showToast({title:"上传失败",icon:"none"})}})},upload_file_server:function(t,o,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";if(n)var r=c.default.baseUrl+n;else r=c.default.baseUrl+"/api/upload/"+i;return new Promise((function(i,n){e.uploadFile({url:r,filePath:t,name:"file",fileType:o.fileType||"image",formData:o,success:function(t){var o=JSON.parse(t.data);o.code>=0?i(o.data.pic_path):n(o.message),e.hideLoading()},fail:function(t){200!=t.statusCode&&(n(t.errMsg||"上传错误"),e.hideLoading())},complete:function(t){200!=t.statusCode&&(n("服务器发生错误"),e.hideLoading())}})}))},copy:function(t,o){e.setClipboardData({data:t,success:function(){"function"==typeof o&&o()}})},isWeiXin:function(){return!1},showToast:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};t.title=t.title||"",t.icon=t.icon||"none",t.duration=t.duration||1500,e.showToast(t),t.success&&t.success()},isIPhoneX:function(){var t=e.getSystemInfoSync();return-1!=t.model.search("iPhone X")},isAndroid:function(){var t=e.getSystemInfoSync().platform;return"ios"!=t&&("android"==t||void 0)},deepClone:function(e){var t=function(e){return"object"==(0,a.default)(e)};if(!t(e))throw new Error("obj 不是一个对象！");var o=Array.isArray(e),i=o?[]:{};for(var n in e)i[n]=t(e[n])?this.deepClone(e[n]):e[n];return i},diyRedirectTo:function(t){if(null!=t&&1!=Object.keys(t).length)if(t.wap_url&&-1!=t.wap_url.indexOf("http")||t.wap_url&&-1!=t.wap_url.indexOf("https"))this.redirectTo("/pages_tool/webview/webview",{src:encodeURIComponent(t.wap_url)});else if(t.appid)e.navigateToMiniProgram({appId:t.appid,path:t.page});else if("MOBILE"!=t.name||t.wap_url)if("MEMBER_CONTACT"==t.name){var o,n=l.default.state.servicerConfig;switch(o=n.weapp,o.type){case"wxwork":i.openCustomerServiceChat({extInfo:{url:o.wxwork_url},corpId:o.corpid,showMessageCard:!0,sendMessageTitle:"this.sendMessageTitle",sendMessagePath:"this.sendMessagePath",sendMessageImg:"this.sendMessageImg"});break;case"third":window.location.href=o.third_url;break;case"niushop":this.redirectTo("/pages_tool/chat/room");break;case"weapp":this.redirectTo(t.wap_url);break;default:var r=l.default.state.siteInfo;r&&r.site_tel?e.makePhoneCall({phoneNumber:r.site_tel}):this.showToast({title:"抱歉，商家暂无客服，请线下联系"})}}else t.wap_url&&this.redirectTo(t.wap_url);else e.makePhoneCall({phoneNumber:t.mobile,success:function(e){},fail:function(e){}})},getDefaultImage:function(){var e=l.default.state.defaultImg;return e.goods=this.img(e.goods),e.head=this.img(e.head),e.store=this.img(e.store),e.article=this.img(e.article),e},uniappIsIPhoneX:function(){var t=!1,o=e.getSystemInfoSync();return(-1!=o.model.search("iPhone X")||-1!=o.model.search("iPhone 11")||-1!=o.model.search("iPhone 12")||-1!=o.model.search("iPhone 13")||parseInt(o.model.split("iPhone")[1])>13)&&(t=!0),t},uniappIsIPhone11:function(){var t=!1,o=e.getSystemInfoSync();return-1!=o.model.search("iPhone 11")&&(t=!0),t},numberFixed:function(e,t){return t||(t=0),Number(e).toFixed(t)},getUrlCode:function(e){var t=location.search,o=new Object;if(-1!=t.indexOf("?"))for(var i=t.substr(1),n=i.split("&"),r=0;r<n.length;r++)o[n[r].split("=")[0]]=n[r].split("=")[1];"function"==typeof e&&e(o)},getCurrRoute:function(){var e=getCurrentPages();return e.length?e[e.length-1].route:""},goBack:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/pages/index/index";1==getCurrentPages().length?this.redirectTo(t):e.navigateBack()},getTimeStr:function(e){var t=parseInt(e/3600).toString(),o=parseInt(e%3600/60).toString();return 1==o.length&&(o="0"+o),1==t.length&&(t="0"+t),t+":"+o},getLocation:function(){var t,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.getLocation({type:null!==(t=o.type)&&void 0!==t?t:"gcj02",success:function(e){l.default.commit("setLocation",e),"function"==typeof o.success&&o.success(e)},fail:function(e){"function"==typeof o.fail&&o.fail(e)},complete:function(e){"function"==typeof o.complete&&o.complete(e)}})},getDistance:function(e,t,o,i){var n=e*Math.PI/180,r=o*Math.PI/180,a=n-r,s=t*Math.PI/180-i*Math.PI/180,c=2*Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2)+Math.cos(n)*Math.cos(r)*Math.pow(Math.sin(s/2),2)));return c*=6378.137,c=Math.round(1e4*c)/1e4,c},onSourceMember:function(t){u.default.sendRequest({url:"/api/Member/alterShareRelation",data:{share_member:t},success:function(t){t.code>=0&&e.removeStorage({key:"source_member",success:function(e){console.log("删除成功",e)}})}})},subscribeMessage:function(t,o){u.default.sendRequest({url:"/weapp/api/weapp/messagetmplids",data:{keywords:t},success:function(t){t.data.length?e.requestSubscribeMessage({tmplIds:t.data,success:function(e){console.log("res",e)},fail:function(e){console.log("fail",e)},complete:function(e){"function"==typeof o&&o()}}):"function"==typeof o&&o()},fail:function(e){"function"==typeof o&&o()}})},getMpShare:function(e){if(!e){var t=this.getCurrentRoute();if(e=t.path,"/pages/member/index"==e)return new Promise((function(e,t){e({})}))}return new Promise((function(t,o){u.default.sendRequest({url:"/weapp/api/weapp/share",data:{path:e},success:function(e){if(e.code>=0){var i=e.data.data;if(i){var n={title:i.title,path:i.path,imageUrl:i.imageUrl,success:function(e){},fail:function(e){}};i.path.indexOf("?")>0&&i.path.split("?")[1];var r={title:i.title,query:i.path,imageUrl:i.imageUrl};t({appMessage:n,timeLine:r})}else o(e.data)}else o(e.data)}})}))},setPublicShare:function(t,o){var i=this;this.isWeiXin()&&u.default.sendRequest({url:"/wechat/api/wechat/jssdkconfig",data:{url:"ios"==e.getSystemInfoSync().platform?e.getStorageSync("initUrl"):window.location.href},success:function(e){if(0==e.code){var n,r,a,s=new d.Weixin;s.init(e.data),s.weixin.showOptionMenu(),s.setShareData({title:null!==(n=t.title)&&void 0!==n?n:"",desc:null!==(r=t.desc)&&void 0!==r?r:"",link:null!==(a=t.link)&&void 0!==a?a:location.href,imgUrl:t.imgUrl?i.img(t.imgUrl):""},(function(e){"function"==typeof o&&o(e)}))}}})},getCurrentRoute:function(){var e=getCurrentPages(),t=e[e.length-1].route,o=e[e.length-1].options,i=[];for(var n in o)i.push(n+"="+o[n]);var r="/"+t,a=i.join("&");return a&&(r+="?"+a),{path:r,query:a}},getCurrentShareRoute:function(e){var t=this.getCurrentRoute();return t.path=t.path.replace(/[?|&]source_member=\d+/,""),e&&(t.path.indexOf("?")>0?t.path+="&":t.path+="?",t.path+="source_member="+e,t.query&&(t.query+="&"),t.query+="source_member="+e),t},objToStyle:function(e){var t=[];for(var o in e)t.push(o+":"+e[o]);return t.join(";")},colourBlend:function(e,t,o){o=Math.max(Math.min(Number(o),1),0);var i=parseInt(e.substring(1,3),16),n=parseInt(e.substring(3,5),16),r=parseInt(e.substring(5,7),16),a=parseInt(t.substring(1,3),16),s=parseInt(t.substring(3,5),16),c=parseInt(t.substring(5,7),16),l=Math.round(i*(1-o)+a*o),u=Math.round(n*(1-o)+s*o),d=Math.round(r*(1-o)+c*o);return l=("0"+(l||0).toString(16)).slice(-2),u=("0"+(u||0).toString(16)).slice(-2),d=("0"+(d||0).toString(16)).slice(-2),"#"+l+u+d},bezier:function(e,t){var o=[],i=[],n=[],r=Math.sqrt(Math.pow(e[1]["x"]-e[0]["x"],2)+Math.pow(e[1]["y"]-e[0]["y"],2)),a=Math.sqrt(Math.pow(e[2]["x"]-e[1]["x"],2)+Math.pow(e[2]["y"]-e[1]["y"],2));if(e[0]["x"]>e[2]["x"])var s=-r/t,c=-a/t;else s=+r/t,c=+a/t;for(var l=(e[1]["y"]-e[0]["y"])/(e[1]["x"]-e[0]["x"]),u=(e[2]["y"]-e[1]["y"])/(e[2]["x"]-e[1]["x"]),d=Math.atan(l),f=Math.atan(u),p=1;p<=t;p++){var h=s*p,g=c*p,m={};m["x"]=h*Math.cos(d)+e[0]["x"],m["y"]=h*Math.sin(d)+e[0]["y"],i.push(m);var _={};_["x"]=g*Math.cos(f)+e[1]["x"],_["y"]=g*Math.sin(f)+e[1]["y"],n.push(_);var v=(_["y"]-m["y"])/(_["x"]-m["x"]),y=Math.atan(v),b=Math.sqrt(Math.pow(_["x"]-m["x"],2)+Math.pow(_["y"]-m["y"],2)),w=h/r*b,S={};S["x"]=w*Math.cos(y)+m["x"],S["y"]=w*Math.sin(y)+m["y"],o.push(S)}return{bezier_points:o}},verifyMobile:function(e){var t=/^\d{11}$/.test(e);return t},getStorageKey:function(e){return c.default.storagePrefix+e},rewriteUniStorageMethod:function(){var t=this,o=e.setStorageSync,i=e.setStorage,n=e.getStorage,r=e.getStorageSync,a=e.removeStorage,s=e.removeStorageSync;e.setStorage=function(e){e.key=t.getStorageKey(e.key),i(e)},e.setStorageSync=function(e,i){o(t.getStorageKey(e),i)},e.getStorage=function(e){e.key=t.getStorageKey(e.key),n(e)},e.getStorageSync=function(e){return r(t.getStorageKey(e))},e.removeStorage=function(e){e.key=t.getStorageKey(e.key),a(e)},e.removeStorageSync=function(e){return s(t.getStorageKey(e))}},generateUUID:function(){var e=(new Date).getTime(),t=performance&&performance.now&&1e3*performance.now()||0;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(o){var i=16*Math.random();return e>0?(i=(e+i)%16|0,e=Math.floor(e/16)):(i=(t+i)%16|0,t=Math.floor(t/16)),("x"===o?i:3&i|8).toString(16)}))},loginComplete:function(t,o){var i=e.getStorageSync("initiateLogin")||t;e.removeStorageSync("initiateLogin");for(var n=!0,r=getCurrentPages().reverse(),a=0;a<r.length;a++)if(-1!=i.indexOf(r[a].route)){n=!1,e.navigateBack({delta:a});break}n&&this.redirectTo(i,{},"redirectTo")},merchantTransfer:function(t,o,n){var r=this;u.default.sendRequest({url:"/wechatpay/api/transfer/transfer",data:t,success:function(t){0==t.code?(e.hideLoading(),i.canIUse("requestMerchantTransfer")?i.requestMerchantTransfer({mchId:o.mch_id,appId:o.app_id,package:t.data.package_info,success:function(e){"function"==typeof n&&n(e)},fail:function(e){"function"==typeof n&&n(e)}}):i.showModal({content:"你的微信版本过低，请更新至最新版本。",showCancel:!1})):r.showToast({title:t.message})},fail:function(e){r.showToast({title:e.message})}})}};t.default=f}).call(this,o("df3c")["default"],o("3223")["default"])},b253:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},b34a:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"签到有礼"}},b3b8:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"团购专区"}},b5e4:function(e){e.exports=JSON.parse('{"uni-datetime-picker.selectDate":"select date","uni-datetime-picker.selectTime":"select time","uni-datetime-picker.selectDateTime":"select datetime","uni-datetime-picker.startDate":"start date","uni-datetime-picker.endDate":"end date","uni-datetime-picker.startTime":"start time","uni-datetime-picker.endTime":"end time","uni-datetime-picker.ok":"ok","uni-datetime-picker.clear":"clear","uni-datetime-picker.cancel":"cancel","uni-calender.MON":"MON","uni-calender.TUE":"TUE","uni-calender.WED":"WED","uni-calender.THU":"THU","uni-calender.FRI":"FRI","uni-calender.SAT":"SAT","uni-calender.SUN":"SUN"}')},b893:function(e,t){e.exports=function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},b94c:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"笔记详情"}},b99f:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"品牌专区"}},be1b:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的预售",emptyTips:"暂无相关预售订单",update:"释放刷新",updateIng:"加载中..."}},c0030:function(e,t,o){"use strict";(function(e,i){var n=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(o("7ca3")),a=n(o("3b2d"));n(o("0817"));function s(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function c(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?s(Object(o),!0).forEach((function(t){(0,r.default)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):s(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var l={options:{styleIsolation:"shared"},data:function(){return{outTradeNo:"",isIphoneX:!1,orderCreateData:{is_balance:0,is_point:1,is_invoice:0,invoice_type:0,invoice_title_type:1,is_tax_invoice:0,coupon:{coupon_id:0},delivery:{},member_goods_card:{},order_key:"",buyer_message:""},paymentData:null,calculateData:null,tempData:null,storeId:0,deliveryTime:"",memberAddress:null,localMemberAddress:null,isRepeat:!1,promotionInfo:null,transactionAgreement:{},tempFormData:null,menuButtonBounding:{},storeConfig:null,localConfig:null,selectGoodsCard:{skuId:0,itemId:0,cardList:{}},storeData:[],latitude:"",longitude:"",coupon_list:[],modules:[]}},inject:["promotion"],created:function(){var t=this;this.menuButtonBounding=e.getMenuButtonBoundingClientRect(),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?(Object.assign(this.orderCreateData,e.getStorageSync(this.createDataKey)),this.location&&(this.orderCreateData.latitude=this.location.latitude,this.orderCreateData.longitude=this.location.longitude,this.latitude=this.location.latitude,this.longitude=this.location.longitude),this.payment()):this.$nextTick((function(){t.$refs.loadingCover.hide(),t.$refs.login.open(t.$util.getCurrentRoute().path)})),this.getTransactionAgreement()},computed:{goodsData:function(){if(this.paymentData)return this.paymentData.goods_list.forEach((function(e){e.sku_spec_format&&"string"==typeof e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format))})),this.paymentData},calculateGoodsData:function(){if(this.calculateData)return this.calculateData.goods_list.forEach((function(e){e.sku_spec_format&&"string"==typeof e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format))})),this.calculateData},balanceDeduct:function(){if(this.calculateData)return this.calculateData.member_account&&this.calculateData.member_account.balance_total<=parseFloat(this.calculateData.order_money).toFixed(2)?parseFloat(this.calculateData.member_account.balance_total).toFixed(2):parseFloat(this.calculateData.order_money).toFixed(2)},storeList:function(){return this.getStoreList()},storeInfo:function(){var e=this.getStoreList();return e&&this.orderCreateData.delivery&&"express"!=this.orderCreateData.delivery.delivery_type&&this.storeId?e[this.orderCreateData.delivery.store_id]:null},cardChargeType:function(){if(this.paymentData&&this.paymentData.recommend_member_card&&Object.keys(this.paymentData.recommend_member_card).length>0){var e=[],t=this.paymentData.recommend_member_card.charge_rule;return Object.keys(t).forEach((function(o,i){switch(o){case"week":e.push({key:o,value:t[o],title:"周卡",unit:"周"});break;case"month":e.push({key:o,value:t[o],title:"月卡",unit:"月"});break;case"quarter":e.push({key:o,value:t[o],title:"季卡",unit:"季"});break;case"year":e.push({key:o,value:t[o],title:"年卡",unit:"年"});break}})),e}}},watch:{storeToken:function(e,t){this.payment()},deliveryTime:function(e){e||this.$refs.timePopup.refresh()},location:function(e){e&&(this.orderCreateData.latitude=e.latitude,this.orderCreateData.longitude=e.longitude,this.latitude=e.latitude,this.longitude=e.longitude,this.payment())},calculateGoodsData:function(e){e&&e.config.local&&e.delivery.local.info.time_is_open&&!this.deliveryTime&&this.localtime("no")}},methods:{pageShow:function(){e.getStorageSync("addressBack")&&(e.removeStorageSync("addressBack"),this.payment()),this.$refs.choosePaymentPopup&&this.$refs.choosePaymentPopup.pageShow()},payment:function(){var t=this,o=this.handleCreateData();this.$api.sendRequest({url:this.api.payment,data:o,success:function(n){if(0==n.code&&n.data){var r=n.data,a=e.getStorageSync("is_test")?1175:i.getLaunchOptionsSync().scene;if(-1!=[1175,1176,1177,1191,1195].indexOf(a)&&r.delivery.express_type&&(r.delivery.express_type=r.delivery.express_type.filter((function(e){return"express"==e.name}))),r&&r.delivery.express_type&&r.delivery.express_type.length){var s=e.getStorageSync("delivery"),c=r.delivery.express_type[0];r.delivery.express_type.forEach((function(e){s&&e.name==s.delivery_type&&(c=e),"local"==e.name&&(t.localConfig=e),"store"==e.name&&(t.storeConfig=e)})),t.selectDeliveryType(c,!1,r.member_account)}if(r.is_virtual&&(t.orderCreateData.member_address={mobile:r.member_account.mobile?r.member_account.mobile:""}),t.orderCreateData.order_key=r.order_key,t.modules=r.modules,r=t.handleGoodsFormData(r),t.promotionInfo=t.promotion(r),t.paymentData=r,t.$refs.form){console.log(t.paymentData.system_form.json_data,JSON.parse(o.form_data).form_data);var l=JSON.parse(o.form_data).form_data,u=t.$util.deepClone(t.paymentData.system_form.json_data);l.forEach((function(e){u.forEach((function(t){e.id===t.id&&(t.value.default=e.val)}))})),t.paymentData.system_form.json_data=u}t.$forceUpdate(),t.getCouponList((function(){t.calculate()}))}else t.$util.showToast({title:n.message}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1e3)}})},getCouponList:function(e){var t=this;if(-1!=this.modules.indexOf("coupon")){var o=this.handleCreateData();this.orderCreateData.coupon.coupon_id=0,this.$api.sendRequest({url:"/api/ordercreate/getcouponlist",data:o,success:function(o){if(0==o.code&&o.data){var i=o.data;t.coupon_list=i,t.coupon_list.length>0&&(t.orderCreateData.coupon.coupon_id=t.coupon_list[0].coupon_id)}else t.$util.showToast({title:o.message});"function"==typeof e&&e()}})}else"function"==typeof e&&e()},handleGoodsFormData:function(t){var o=e.getStorageSync("goodFormData");return this.$refs.goodsForm?t.goods_list=this.$util.deepClone(this.paymentData.goods_list):t.goods_list.forEach((function(e){if(e.goods_form){var t={};e.form_data?e.form_data.map((function(e){t[e.id]=e})):o&&o.goods_id==e.goods_id&&o.form_data.map((function(e){t[e.id]=e})),Object.keys(t).length&&e.goods_form.json_data.forEach((function(e){t[e.id]&&(e.val=t[e.id].val)}))}})),t},calculate:function(){var e=this;this.$api.sendRequest({url:this.api.calculate,data:this.handleCreateData(),success:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.isShow&&e.$refs.loadingCover.hide(),0==t.code&&t.data?(e.calculateData=e.handleGoodsFormData(t.data),e.calculateData.coupon_list=e.coupon_list,t.data.delivery&&("express"==t.data.delivery.delivery_type&&(e.memberAddress=t.data.delivery.member_address),"local"==t.data.delivery.delivery_type&&(e.localMemberAddress=t.data.delivery.member_address)),t.data.goods_list.forEach((function(t){if(t.member_card_list){if(e.orderCreateData.member_goods_card[t.sku_id]){var o=e.orderCreateData.member_goods_card[t.sku_id];t.member_card_list[o]||delete e.orderCreateData.member_goods_card[t.sku_id]}}else e.orderCreateData.member_goods_card[t.sku_id]&&delete e.orderCreateData.member_goods_card[t.sku_id]})),t.data.coupon_id?e.orderCreateData.coupon.coupon_id=t.data.coupon_id:e.orderCreateData.coupon.coupon_id=0,e.$forceUpdate()):e.$util.showToast({title:t.message})}})},create:function(){var t=this;this.verify()&&!this.isRepeat&&(this.isRepeat=!0,e.showLoading({title:""}),this.$api.sendRequest({url:this.api.create,data:this.handleCreateData(),success:function(o){e.hideLoading(),0==o.code?(t.outTradeNo=o.data,e.removeStorageSync("deliveryTime"),e.removeStorageSync("goodFormData"),e.setStorageSync("paySource",""),0==t.calculateData.pay_money?((t.paymentData.is_virtual||"store"==t.orderCreateData.delivery.delivery_type)&&t.$util.subscribeMessage("ORDER_VERIFY_OUT_TIME,VERIFY_CODE_EXPIRE,VERIFY"),t.$util.redirectTo("/pages_tool/pay/result",{code:o.data},"redirectTo")):t.openChoosePayment(),t.$store.dispatch("getCartNumber")):(t.$util.showToast({title:o.message}),t.isRepeat=!1)}}))},handleCreateData:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);return this.$refs.form&&(t.form_data={form_id:this.paymentData.system_form.id,form_data:this.$util.deepClone(this.$refs.form.formData)}),this.$refs.goodsForm&&(t.form_data||(t.form_data={}),t.form_data.goods_form={},this.$refs.goodsForm.forEach((function(o){t.form_data.goods_form[o._props.customAttr.sku_id]={form_id:o._props.customAttr.form_id,form_data:e.$util.deepClone(o.formData)}}))),Object.keys(t).forEach((function(e){var o=t[e];"object"==(0,a.default)(o)&&(t[e]=JSON.stringify(o))})),this.paymentData&&0==this.orderCreateData.is_virtual&&t.member_address&&this.orderCreateData.delivery&&"store"!=this.orderCreateData.delivery.delivery_type&&delete t.member_address,t},openChoosePayment:function(){if(this.paymentData.is_virtual)1==this.paymentData.is_virtual_delivery?this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY,ORDER_DELIVERY"):this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY");else switch(this.orderCreateData.delivery.delivery_type){case"express":this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY,ORDER_DELIVERY");break;case"store":this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY");break;case"local":this.$util.subscribeMessage("ORDER_URGE_PAYMENT,ORDER_PAY,ORDER_DELIVERY");break}this.$refs.choosePaymentPopup.getPayInfo(this.outTradeNo)},verify:function(){if(1==this.paymentData.is_virtual){if(!this.orderCreateData.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号"}),!1}else{if(!this.orderCreateData.delivery||!this.orderCreateData.delivery.delivery_type)return this.$util.showToast({title:"商家未设置配送方式"}),!1;if("express"==this.orderCreateData.delivery.delivery_type&&!this.memberAddress||"local"==this.orderCreateData.delivery.delivery_type&&!this.localMemberAddress)return this.$util.showToast({title:"请先选择您的收货地址"}),!1;if("store"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可提货的门店,请选择其他配送方式"}),!1;if(!this.orderCreateData.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号"}),!1;if(!this.deliveryTime)return this.$util.showToast({title:"请选择提货时间"}),!1}if("local"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可配送的门店,请选择其他配送方式"}),!1;if(this.calculateGoodsData.config.local.is_use&&1==this.calculateGoodsData.delivery.local.info.time_is_open&&!this.deliveryTime)return this.$util.showToast({title:"请选择送达时间"}),!1}}if(this.$refs.goodsForm){for(var e=!0,t=0;t<this.$refs.goodsForm.length;t++){var o=this.$refs.goodsForm[t];if(e=o.verify(),!e)break}if(!e)return!1}if(this.paymentData.system_form){var i=this.$refs.form.verify();if(!i)return!1}return!0},selectAddress:function(){var e={back:this.$util.getCurrentRoute().path,local:0,type:1};"local"==this.orderCreateData.delivery.delivery_type&&(e.local=1,e.type=2),this.$util.redirectTo("/pages_tool/member/address",e)},selectDeliveryType:function(t){var o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!this.orderCreateData.delivery||this.orderCreateData.delivery.delivery_type!=t.name){this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.deliveryTime="";var n={delivery_type:t.name,delivery_type_name:t.title};"store"!=t.name&&"local"!=t.name||(t.store_list[0]&&(n.store_id=t.store_list[0].store_id),this.storeId=n.store_id?n.store_id:0,this.orderCreateData.member_address||(this.paymentData?this.orderCreateData.member_address={name:this.paymentData.member_account.nickname,mobile:this.paymentData.member_account.mobile}:i&&(this.orderCreateData.member_address={name:i.nickname,mobile:i.mobile}))),this.$set(this.orderCreateData,"delivery",n),e.setStorageSync("delivery",n),"express"==this.orderCreateData.delivery.delivery_type||this.location||this.$util.getLocation(),o&&this.payment(),"store"==t.name&&this.storetime("no"),"local"==t.name&&this.localtime("no")}},imageError:function(e){this.paymentData.goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.calculateData.goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},selectPickupPoint:function(t){if(t.store_id!=this.storeId){this.storeId=t.store_id,this.orderCreateData.delivery.store_id=t.store_id,this.payment(),this.resetDeliveryTime();var o=e.getStorageSync("delivery");o.store_id=t.store_id,e.setStorageSync("delivery",o)}this.$refs.deliveryPopup.close()},resetDeliveryTime:function(){this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.deliveryTime="",e.removeStorageSync("deliveryTime")},storetime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.storeInfo){var t=this.$util.deepClone(this.storeInfo);t.delivery_time="string"==typeof t.delivery_time&&t.delivery_time?JSON.parse(t.delivery_time):t.delivery_time,t.delivery_time&&(void 0!=t.delivery_time.length||t.delivery_time.length)||(t.delivery_time=[{start_time:t.start_time,end_time:t.end_time}]);var o={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(o,e),this.$forceUpdate()}},selectPickupTime:function(t){this.deliveryTime=t.data.month+"("+t.data.time+")",this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:t.data.start_date,end_date:t.data.end_date},e.setStorageSync("deliveryTime",{deliveryTime:this.deliveryTime,buyer_ask_delivery_time:this.orderCreateData.delivery.buyer_ask_delivery_time,delivery_type:this.orderCreateData.delivery.delivery_type})},storeImgError:function(){this.storeInfo.store_image=this.$util.getDefaultImage().store},openPopup:function(e){"deliveryPopup"==e&&(!this.storeList||Object.keys(this.storeList).length<=1)||(this.tempData=this.$util.deepClone(this.orderCreateData),this.$refs[e].open())},closePopup:function(e){this.orderCreateData=this.$util.deepClone(this.tempData),this.$refs[e].close(),this.tempData=null},changeIsInvoice:function(){0==this.orderCreateData.is_invoice?(this.orderCreateData.is_invoice=1,this.orderCreateData.invoice_type||(this.orderCreateData.invoice_type=this.goodsData.invoice.invoice_type.split(",")[0])):this.orderCreateData.is_invoice=0},changeInvoiceType:function(e){this.orderCreateData.invoice_type=e},changeInvoiceTitleType:function(e){this.orderCreateData.invoice_title_type=e},changeIsTaxInvoice:function(){0==this.orderCreateData.is_tax_invoice?this.orderCreateData.is_tax_invoice=1:this.orderCreateData.is_tax_invoice=0,this.$forceUpdate()},changeInvoiceContent:function(e){this.orderCreateData.invoice_content=e,this.$forceUpdate()},invoiceVerify:function(){if(!this.orderCreateData.invoice_title)return this.$util.showToast({title:"请填写发票抬头"}),!1;if(!this.orderCreateData.taxpayer_number&&2==this.orderCreateData.invoice_title_type)return this.$util.showToast({title:"请填写纳税人识别号"}),!1;if(1==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_full_address&&1==this.paymentData.is_virtual)return this.$util.showToast({title:"请填写发票邮寄地址"}),!1;if(2==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_email)return this.$util.showToast({title:"请填写邮箱"}),!1;if(2==this.orderCreateData.invoice_type){if(!/^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/.test(this.orderCreateData.invoice_email))return this.$util.showToast({title:"请填写正确的邮箱"}),!1}return!!this.orderCreateData.invoice_content||(this.$util.showToast({title:"请选择发票内容"}),!1)},saveInvoice:function(){(1!=this.orderCreateData.is_invoice||this.invoiceVerify())&&(this.calculate(),this.$refs.invoicePopup.close())},saveBuyerMessage:function(){this.$refs.buyerMessagePopup.close()},selectMemberCard:function(){this.orderCreateData.is_open_card=this.orderCreateData.is_open_card?0:1,this.orderCreateData.member_card_unit||(this.orderCreateData.member_card_unit=this.cardChargeType[0].key),this.payment()},selectMemberCardUnit:function(e){this.orderCreateData.member_card_unit=e,this.calculate()},usePoint:function(){this.orderCreateData.is_point=this.orderCreateData.is_point?0:1,this.calculate()},payClose:function(){this.$store.dispatch("getCartNumber"),this.$util.redirectTo("/pages/order/detail",{order_id:this.$refs.choosePaymentPopup.payInfo.order_id},"redirectTo")},selectCoupon:function(e){this.orderCreateData.coupon.coupon_id==e.coupon_id?this.orderCreateData.coupon={coupon_id:0}:this.orderCreateData.coupon={coupon_id:e.coupon_id}},useCoupon:function(){this.$refs.couponPopup.close(),this.calculate()},localtime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.calculateGoodsData&&this.calculateGoodsData.config.local){var t=this.$util.deepClone(this.calculateGoodsData.delivery.local.info);if(Object.keys(t).length){t.delivery_time&&(t.end_time=t.delivery_time[t.delivery_time.length-1].end_time);var o={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(o,e)}}},surplusStartMoney:function(){var e=0;if(this.calculateData&&this.calculateData.delivery&&"local"==this.calculateData.delivery.delivery_type){var t,o=null!==(t=this.calculateGoodsData.delivery.start_money)&&void 0!==t?t:0;e=parseFloat(o)-parseFloat(this.calculateData.goods_money),e=e<0?0:e}return e},getTransactionAgreement:function(){var e=this;this.$api.sendRequest({url:"/api/order/transactionagreement",success:function(t){t.data&&(e.transactionAgreement=t.data)}})},editForm:function(e){this.tempFormData={index:e,json_data:this.$util.deepClone(this.goodsData.goods_list[e].goods_form.json_data)},this.$refs.editFormPopup.open()},saveForm:function(){this.$refs.tempForm.verify()&&(this.$set(this.paymentData.goods_list[this.tempFormData.index].goods_form,"json_data",this.$refs.tempForm.formData),this.$refs.editFormPopup.close())},selectMemberGoodsCard:function(e){var t=this,o=this.goodsData.goods_list[e].sku_id;this.selectGoodsCard={skuId:o,itemId:this.orderCreateData.member_goods_card[o]?this.orderCreateData.member_goods_card[o]:0,cardList:this.$util.deepClone(this.calculateGoodsData.goods_list[e].member_card_list),click:function(e){t.selectGoodsCard.itemId=t.selectGoodsCard.itemId==e?0:e}},this.$refs.memberGoodsCardPopup.open()},saveMemberGoodsCard:function(){this.orderCreateData.member_goods_card[this.selectGoodsCard.skuId]=this.selectGoodsCard.itemId||0,this.$refs.memberGoodsCardPopup.close(),this.payment()},back:function(){e.navigateBack({delta:1})},getStoreList:function(){var e=null;return this.orderCreateData.delivery&&("local"==this.orderCreateData.delivery.delivery_type&&this.localConfig&&(e=this.localConfig.store_list,e=e.reduce((function(e,t){return c(c({},e),{},(0,r.default)({},t.store_id,t))}),{})),"store"==this.orderCreateData.delivery.delivery_type&&this.storeConfig&&(e=this.storeConfig.store_list,e=e.reduce((function(e,t){return c(c({},e),{},(0,r.default)({},t.store_id,t))}),{}))),e},getStore:function(e){var t,o,i=this;this.$api.sendRequest({url:"/api/store/getStorePage",data:{page_size:e.size,page:e.num,latitude:null!==(t=this.latitude)&&void 0!==t?t:"",longitude:null!==(o=this.longitude)&&void 0!==o?o:"",type:this.orderCreateData.delivery.delivery_type,store_ids:this.paymentData.available_store_ids},success:function(t){var o=[];t.message;0==t.code&&t.data&&(o=t.data.list),e.endSuccess(o.length),1==e.num&&(i.storeData=[]),i.storeData=i.storeData.concat(o)},fail:function(t){e.endErr()}})}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)}}};t.default=l}).call(this,o("df3c")["default"],o("3223")["default"])},c1f3:function(e,t,o){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={data:function(){return{cartData:[],editLock:!1,checkAll:!0,totalPrice:"0.00",totalCount:0,isSub:!1,invalidGoods:[],isIphoneX:!1,isAction:!1,goodsSkuDetail:null,discount:{},manjian:{},receiveSub:!1,discountPopupShow:!1,startX:"",endX:"",refresherTriggered:!1,timeout:{},navbarData:{title:"购物车",topNavColor:"#ffffff",topNavBg:!1,navBarSwitch:!0,textNavColor:"#333333",moreLink:{name:""},navStyle:1,textImgPosLink:"center"}}},onLoad:function(){e.hideTabBar(),this.isIphoneX=this.$util.uniappIsIPhoneX()},onShow:function(){this.storeToken?(this.getCartData(),this.$store.dispatch("getCartNumber")):(this.cartData=[],this.invalidGoods=[],this.calculationTotalPrice())},onHide:function(){this.isAction=!1},onUnload:function(){!this.storeToken&&this.$refs.login&&this.$refs.login.cancelCompleteInfo()},onReady:function(){this.storeToken||this.$refs.loadingCover&&this.$refs.loadingCover.hide()},computed:{hasData:function(){return this.cartData.length>0||this.invalidGoods.length>0}},methods:{initNum:function(e){return e.num},getCartData:function(){var e=this;this.$api.sendRequest({url:"/api/cart/goodslists",success:function(t){t.code>=0&&(t.data.length?e.handleCartData(t.data):e.cartData=[]),e.refresherTriggered=!1,e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},handleCartData:function(e){var t=this;this.invalidGoods=[],this.cartData=[];var o={};e.forEach((function(e,i){1==e.goods_state?void 0!=e.store_goods_status&&1!=e.store_goods_status||e.min_buy>0&&e.min_buy>e.stock?t.invalidGoods.push(e):e.stock?(e.checked=!0,e.edit=!1,void 0!=o["site_"+e.site_id]?o["site_"+e.site_id].cartList.push(e):o["site_"+e.site_id]={siteId:e.site_id,siteName:e.site_name,edit:!1,checked:!0,cartList:[e]}):t.invalidGoods.push(e):t.invalidGoods.push(e)})),this.cartData=[],Object.keys(o).forEach((function(e){t.cartData.push(o[e])})),this.calculationTotalPrice(),this.cartData.length&&this.cartData[0].cartList.forEach((function(e){e.sku_spec_format?e.sku_spec_format=JSON.parse(e.sku_spec_format):e.sku_spec_format=[]})),this.invalidGoods.length&&this.invalidGoods.forEach((function(e){e.sku_spec_format?e.sku_spec_format=JSON.parse(e.sku_spec_format):e.sku_spec_format=[]}))},singleElection:function(e,t){this.cartData[e].cartList[t].checked=!this.cartData[e].cartList[t].checked,this.calculationTotalPrice()},siteAllElection:function(e,t){this.cartData[t].checked=e,this.cartData[t].cartList.forEach((function(t){t.checked=e})),this.calculationTotalPrice()},allElection:function(e){var t=this;this.checkAll="boolean"==typeof e?e:!this.checkAll,this.cartData.length&&this.cartData.forEach((function(e){e.checked=t.checkAll,e.cartList.forEach((function(e){e.checked=t.checkAll}))})),this.calculationTotalPrice()},calculationTotalPrice:function(){if(this.cartData.length){var e=0,t=0,o=0;this.cartData.forEach((function(i){var n=0;i.cartList.forEach((function(o){o.checked&&(n+=1,t+=parseInt(o.num),Number(o.member_price)>0&&Number(o.member_price)<Number(o.discount_price)?e+=o.member_price*o.num:e+=o.discount_price*o.num)})),i.cartList.length==n?(i.checked=!0,o+=1):i.checked=!1})),this.totalPrice=e.toFixed(2),this.totalCount=t,this.checkAll=this.cartData.length==o}else this.totalPrice="0.00",this.totalCount=0;this.discountCalculate()},deleteCart:function(t,o,i){var n=this,r=[];if("all"==t)for(var a=0;a<this.cartData.length;a++)for(var s=0;s<this.cartData[a].cartList.length;s++)this.cartData[a].cartList[s].checked&&r.push(this.cartData[a].cartList[s].cart_id);else r.push(this.cartData[o].cartList[i].cart_id);0!=r.length?e.showModal({title:"提示",content:r.length>1?"确定要删除这些商品吗？":"确定要删除该商品吗？",success:function(e){e.confirm&&(r=r.toString(),n.$api.sendRequest({url:"/api/cart/delete",data:{cart_id:r},success:function(e){if(e.code>=0){if("all"==t){for(var r=0;r<n.cartData.length;r++){for(var a=0;a<n.cartData[r].cartList.length;a++){var s=n.cartData[r].cartList[a];s.checked&&(n.cartData[r].cartList.splice(a,1),a=-1)}0==n.cartData[r].cartList.length&&(n.cartData.splice(r,1),r=-1)}n.$store.dispatch("emptyCart")}else{var c=n.cartData[o].cartList,l=c[i].goods_id,u=c[i].sku_id;delete n.cartList["goods_"+l]["sku_"+u],2==Object.keys(n.cartList["goods_"+l]).length&&delete n.cartList["goods_"+l],n.$store.dispatch("cartCalculate"),c.splice(i,1),0==c.length&&n.cartData.splice(o,1)}n.resetEditStatus(),n.calculationTotalPrice()}else n.$util.showToast({title:e.message})}}))}}):this.$util.showToast({title:"请选择要删除的商品"})},cartNumChange:function(e,t){var o=this;if(!isNaN(e)&&!this.editLock){this.editLock=!0;var i=this.cartData[t.siteIndex].cartList[t.cartIndex],n=i.is_limit&&i.max_buy>0&&i.max_buy<i.stock?i.max_buy:i.stock,r=i.min_buy>0?i.min_buy:1;e>n&&(e=n),e<r&&(e=r);var a=this.cartData[t.siteIndex].cartList[t.cartIndex].cart_id;this.$api.sendRequest({url:"/api/cart/edit",data:{num:e,cart_id:a},success:function(i){if(i.code>=0){var n=o.cartData[t.siteIndex].cartList[t.cartIndex],r=n.goods_id,a=n.sku_id;n.num=e,o.cartList["goods_"+r]["sku_"+a].num=e,o.$store.dispatch("cartCalculate"),o.resetEditStatus(),o.calculationTotalPrice()}else o.$util.showToast({title:i.message});o.editLock=!1}})}},settlement:function(){var t=this;if(this.totalCount>0){var o=!1;try{this.cartData.forEach((function(e,i){e.cartList.forEach((function(e,i){if(e.checked){if(e.num>e.stock)throw t.$util.showToast({title:"商品"+e.goods_name+"商品库存不足"}),o=!0,new Error;if(e.min_buy&&e.num<e.min_buy)throw t.$util.showToast({title:"商品"+e.goods_name+"商品最少要购买"+e.min_buy+"件"}),o=!0,new Error}}))}))}catch(a){}var i={};for(var n in this.cartData.forEach((function(e,t){e.cartList.forEach((function(e,t){e.checked&&(i[e.goods_id]?i[e.goods_id].num+=e.num:i[e.goods_id]={num:e.num,max_buy:e.max_buy,goods_name:e.goods_name})}))})),i)i[n].max_buy&&i[n].num>i[n].max_buy&&(this.$util.showToast({title:"商品"+i[n].goods_name+"最多可购买"+i[n].max_buy+"件"}),o=!0);if(o)return;var r=[];if(this.cartData.forEach((function(e){e.cartList.forEach((function(e){e.checked&&r.push(e.cart_id)}))})),this.discount.coupon_info&&"wait"==this.discount.coupon_info.receive_type&&this.receiveCoupon(this.discount.coupon_info.coupon_type_id,!1),this.isSub)return;this.isSub=!0,e.removeStorageSync("delivery"),e.setStorage({key:"orderCreateData",data:{cart_ids:r.toString()},success:function(){t.$util.redirectTo("/pages/order/payment"),t.isSub=!1}})}},clearInvalidGoods:function(){var t=this;e.showModal({title:"提示",content:"确定要清空这些商品吗？",success:function(e){if(e.confirm){var o=[];t.invalidGoods.forEach((function(e){o.push(e.cart_id)})),o.length&&t.$api.sendRequest({url:"/api/cart/delete",data:{cart_id:o.toString()},success:function(e){e.code>=0?(t.invalidGoods=[],t.refreshCartNumber()):t.$util.showToast({title:e.message})}})}}})},imageError:function(e,t){this.cartData[e].cartList[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},toGoodsDetail:function(e){this.$util.redirectTo("/pages/goods/detail",{sku_id:e.sku_id})},refreshCartNumber:function(){this.storeToken&&(this.$store.dispatch("getCartNumber"),this.resetEditStatus())},goodsLimit:function(e,t){var o=this.cartData[t.siteIndex].cartList[t.cartIndex];"plus"==e.type?o.max_buy>0&&o.max_buy<o.stock?this.$util.showToast({title:"该商品每人限购"+o.max_buy+"件"}):this.$util.showToast({title:"库存不足"}):this.deleteCart("single",t.siteIndex,t.cartIndex)},toLogin:function(){this.$refs.login.open()},resetEditStatus:function(){if(this.cartData.length){for(var e=0;e<this.cartData[0].cartList.length;e++)this.cartData[0].cartList[e].edit=!1;this.$forceUpdate()}},changeAction:function(){this.isAction=!this.isAction,this.resetEditStatus()},selectSku:function(e){var t=this,o=this.$util.deepClone(e);o.goods_spec_format&&(o.goods_spec_format=JSON.parse(o.goods_spec_format)),this.goodsSkuDetail=o,this.$nextTick((function(){t.$refs.selectSku.show("confirm",(function(o,i){t.$api.sendRequest({url:"/api/cart/editcartsku",data:{cart_id:e.cart_id,sku_id:o,num:i},success:function(e){e.code>=0?(t.invalidGoods=[],t.getCartData(),t.refreshCartNumber()):t.$util.showToast({title:e.message})}})}),t.goodsSkuDetail)}))},toggleDiscountPopup:function(){this.$refs.discountPopup.showPopup?this.$refs.discountPopup.close():this.$refs.discountPopup.open(),this.discountPopupShow=!this.discountPopupShow},discountCalculate:function(){var e=this;if(0!=this.cartData.length){var t=[];this.cartData.forEach((function(e){e.cartList.forEach((function(e){e.checked&&t.push({sku_id:e.sku_id,num:e.num})}))})),0!=t.length?this.$api.sendRequest({url:"/api/cartcalculate/calculate",data:{sku_ids:JSON.stringify(t)},success:function(t){if(t.code>=0&&t.data&&(t.data.coupon_money>0||t.data.promotion_money>0)){e.discount=t.data;var o={};t.data.goods_list.forEach((function(e){e.promotion&&e.promotion.manjian?o["sku_"+e.sku_id]=JSON.parse(e.promotion.manjian.rule_json):o["sku_"+e.sku_id]=""})),Object.assign(e.manjian,o),e.refresherTriggered=!1}else e.discount={},e.manjian={}}}):this.discount={}}},receiveCoupon:function(e){var t=this,o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.receiveSub||(this.receiveSub=!0,this.$api.sendRequest({url:"/coupon/api/coupon/receive",data:{coupon_type_id:e,get_type:2},success:function(e){0==e.code?t.$set(t.discount.coupon_info,"receive_type",""):(o&&t.$util.showToast({title:e.message}),t.receiveSub=!1)}}))},touchS:function(e){this.startX=e.touches[0].clientX},touchE:function(e,t){this.endX=e.changedTouches[0].clientX;var o=this.startX-this.endX;o>50?t.edit=!0:o<0&&(t.edit=!1),this.$forceUpdate()},moneyFormat:function(e){return isNaN(parseFloat(e))?e:parseFloat(e).toFixed(2)},refreshSkuDetail:function(e){this.goodsSkuDetail=e},onRefresh:function(e){this.refresherTriggered=!0,this.storeToken?(this.getCartData(),this.refreshCartNumber()):(this.cartData=[],this.invalidGoods=[],this.calculationTotalPrice())}}};t.default=o}).call(this,o("df3c")["default"])},c5bf9:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"公告列表",emptyText:"当前暂无更多信息",contentTitle:"升级公告"}},c712:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"个人资料",headImg:"头像",account:"账号",nickname:"昵称",realName:"真实姓名",sex:"性别",birthday:"生日",password:"密码",paypassword:"支付密码",mobilePhone:"手机",bindMobile:"绑定手机",cancellation:"注销账号",lang:"语言",logout:"退出登录",save:"保存",noset:"未设置",nickPlaceholder:"请输入新昵称",pleaseRealName:"请输入真实姓名",nowPassword:"当前密码",newPassword:"新密码",confirmPassword:"确认新密码",phoneNumber:"手机号",confirmCode:"验证码",confirmCodeInput:"请输入验证码",confirmCodeInputerror:"验证码错误",findanimateCode:"获取动态码",animateCode:"动态码",animateCodeInput:"请输入动态码",modifyNickname:"修改昵称",modifyPassword:"修改密码",bindPhone:"绑定手机",alikeNickname:"与原昵称一致，无需修改",noEmityNickname:"昵称不能为空",updateSuccess:"修改成功",pleaseInputOldPassword:"请输入原始密码",pleaseInputNewPassword:"请输入新密码",passwordLength:"密码长度不能小于6位",alikePassword:"两次密码不一致",samePassword:"新密码不能与原密码相同",surePhoneNumber:"请输入正确的手机号",alikePhone:"与原手机号一致，无需修改",modify:"修改"}},c8d8:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{wechatQrcode:"",diyRoute:"/pages/index/index",refresh:!1}},onLoad:function(e){this.name=e.name||"DIY_VIEW_INDEX"},onShow:function(){this.getFollowQrcode()},methods:{changeCategoryNav:function(e){0==e&&(this.refresh=!this.refresh)},getFollowQrcode:function(){var e=this;this.$util.isWeiXin()&&this.$api.sendRequest({url:"/wechat/api/wechat/followqrcode",success:function(t){t.code>=0&&t.data&&(e.wechatQrcode=t.data.qrcode)}})},officialAccountsOpen:function(){this.$refs.officialAccountsPopup.open()},officialAccountsClose:function(){this.$refs.officialAccountsPopup.close()}}}},c952:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"兑换"}},c9af:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},cae7:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"待付款订单"}},cb2f:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"申请提现"}},cb85:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"账号注销"}},cf62:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"砸金蛋"}},cf80:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"账号注销"}},d0a6:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},d1d6:function(e,t,o){"use strict";(function(e){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("3b2d")),r={data:function(){return{themeColor:""}},onLoad:function(){},onShow:function(){var e=this;this.$langConfig.refresh();var t=setInterval((function(){var o=e.themeStyle;o&&o.main_color&&(e.themeColorSet(),clearInterval(t))}),50)},computed:{themeStyle:function(){return this.$store.state.themeStyle},addonIsExist:function(){return this.$store.state.addonIsExist},tabBarList:function(){return this.$store.state.tabBarList},siteInfo:function(){return this.$store.state.siteInfo},memberInfo:function(){return this.$store.state.memberInfo},storeToken:function(){return this.$store.state.token},bottomNavHidden:function(){return this.$store.state.bottomNavHidden},globalStoreConfig:function(){return this.$store.state.globalStoreConfig},globalStoreInfo:function(){return this.$store.state.globalStoreInfo},location:function(){return this.$store.state.location},locationStorage:function(){var t=e.getStorageSync("location");if(t){var o=new Date;this.mapConfig.wap_valid_time>0?t.is_expired=o.getTime()/1e3>t.valid_time:t.is_expired=!1}return t},defaultStoreInfo:function(){return this.$store.state.defaultStoreInfo},componentRefresh:function(){return this.$store.state.componentRefresh},servicerConfig:function(){return this.$store.state.servicerConfig},diySeckillInterval:function(){return this.$store.state.diySeckillInterval},tabBarHeight:function(){return this.$store.state.tabBarHeight},mapConfig:function(){return this.$store.state.mapConfig},initStatus:function(){return this.$store.state.initStatus},copyright:function(){var e=this.$store.state.copyright;return e&&!e.auth&&(e.logo="public/uniapp/common/logo_copy.png",e.copyright_link="http://www.niushop.com"),e},cartList:function(){return this.$store.state.cartList},cartIds:function(){return this.$store.state.cartIds},cartNumber:function(){return this.$store.state.cartNumber},cartMoney:function(){return this.$store.state.cartMoney}},methods:{themeColorSet:function(){var e=this,t=this.themeStyle;this.themeColor="--base-color:".concat(t.main_color,";--base-help-color:").concat(t.aux_color,";"),"56px"!=this.tabBarHeight&&(this.themeColor+="--tab-bar-height:".concat(this.tabBarHeight,";")),Object.keys(t).forEach((function(o){var i=t[o];"object"==(0,n.default)(i)?Object.keys(i).forEach((function(t){e.themeColor+="--"+t.replace(/_/g,"-")+":"+i[t]+";"})):"string"==typeof o&&o&&(e.themeColor+="--"+o.replace(/_/g,"-")+":"+i+";")}));for(var o=9;o>=5;o--){var i=this.$util.colourBlend(t.main_color,"#ffffff",o/10);this.themeColor+="--base-color-light-".concat(o,":").concat(i,";")}},lightenDarkenColor:function(e,t){var o=!1;"#"==e[0]&&(e=e.slice(1),o=!0);var i=parseInt(e,16),n=(i>>16)+t;n>255?n=255:n<0&&(n=0);var r=(i>>8&255)+t;r>255?r=255:r<0&&(r=0);var a=(255&i)+t;return a>255?a=255:a<0&&(a=0),(o?"#":"")+(a|r<<8|n<<16).toString(16)},changeStore:function(t,o){t&&this.$store.commit("setGlobalStoreInfo",t);var i=this.$util.getCurrRoute();o&&"pages/index/index"!=i&&(e.setStorageSync("manual_change_store",!0),this.$store.dispatch("getCartNumber"),this.$util.redirectTo("/pages/index/index"))}},filters:{moneyFormat:function(e){return isNaN(parseFloat(e))?e:parseFloat(e).toFixed(2)}}};t.default=r}).call(this,o("df3c")["default"])},d1f2:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"待付款订单"}},d381:function(e,t){},d3b4:function(e,t,o){"use strict";(function(e,i){var n=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.LOCALE_ZH_HANT=t.LOCALE_ZH_HANS=t.LOCALE_FR=t.LOCALE_ES=t.LOCALE_EN=t.I18n=t.Formatter=void 0,t.compileI18nJsonStr=function(e,t){var o=t.locale,i=t.locales,n=t.delimiters;if(!D(e,n))return e;k||(k=new d);var r=[];Object.keys(i).forEach((function(e){e!==o&&r.push({locale:e,values:i[e]})})),r.unshift({locale:o,values:i[o]});try{return JSON.stringify(P(JSON.parse(e),r,n),null,2)}catch(a){}return e},t.hasI18nJson=function e(t,o){k||(k=new d);return O(t,(function(t,i){var n=t[i];return x(n)?!!D(n,o)||void 0:e(n,o)}))},t.initVueI18n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0;if("string"!==typeof e){var n=[t,e];e=n[0],t=n[1]}"string"!==typeof e&&(e=S());"string"!==typeof o&&(o="undefined"!==typeof __uniConfig&&__uniConfig.fallbackLocale||"en");var r=new b({locale:e,fallbackLocale:o,messages:t,watcher:i}),a=function(e,t){if("function"!==typeof getApp)a=function(e,t){return r.t(e,t)};else{var o=!1;a=function(e,t){var i=getApp().$vm;return i&&(i.$locale,o||(o=!0,w(i,r))),r.t(e,t)}}return a(e,t)};return{i18n:r,f:function(e,t,o){return r.f(e,t,o)},t:function(e,t){return a(e,t)},add:function(e,t){var o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return r.add(e,t,o)},watch:function(e){return r.watchLocale(e)},getLocale:function(){return r.getLocale()},setLocale:function(e){return r.setLocale(e)}}},t.isI18nStr=D,t.isString=void 0,t.normalizeLocale=y,t.parseI18nJson=function e(t,o,i){k||(k=new d);return O(t,(function(t,n){var r=t[n];x(r)?D(r,i)&&(t[n]=C(r,o,i)):e(r,o,i)})),t},t.resolveLocale=function(e){return function(t){return t?(t=y(t)||t,function(e){var t=[],o=e.split("-");while(o.length)t.push(o.join("-")),o.pop();return t}(t).find((function(t){return e.indexOf(t)>-1}))):t}};var r=n(o("34cf")),a=n(o("67ad")),s=n(o("0bdb")),c=n(o("3b2d")),l=function(e){return null!==e&&"object"===(0,c.default)(e)},u=["{","}"],d=function(){function e(){(0,a.default)(this,e),this._caches=Object.create(null)}return(0,s.default)(e,[{key:"interpolate",value:function(e,t){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u;if(!t)return[e];var i=this._caches[e];return i||(i=h(e,o),this._caches[e]=i),g(i,t)}}]),e}();t.Formatter=d;var f=/^(?:\d)+/,p=/^(?:\w)+/;function h(e,t){var o=(0,r.default)(t,2),i=o[0],n=o[1],a=[],s=0,c="";while(s<e.length){var l=e[s++];if(l===i){c&&a.push({type:"text",value:c}),c="";var u="";l=e[s++];while(void 0!==l&&l!==n)u+=l,l=e[s++];var d=l===n,h=f.test(u)?"list":d&&p.test(u)?"named":"unknown";a.push({value:u,type:h})}else c+=l}return c&&a.push({type:"text",value:c}),a}function g(e,t){var o=[],i=0,n=Array.isArray(t)?"list":l(t)?"named":"unknown";if("unknown"===n)return o;while(i<e.length){var r=e[i];switch(r.type){case"text":o.push(r.value);break;case"list":o.push(t[parseInt(r.value,10)]);break;case"named":"named"===n&&o.push(t[r.value]);break;case"unknown":0;break}i++}return o}t.LOCALE_ZH_HANS="zh-Hans";t.LOCALE_ZH_HANT="zh-Hant";t.LOCALE_EN="en";t.LOCALE_FR="fr";t.LOCALE_ES="es";var m=Object.prototype.hasOwnProperty,_=function(e,t){return m.call(e,t)},v=new d;function y(e,t){if(e){if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if(e=e.toLowerCase(),"chinese"===e)return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1||function(e,t){return!!t.find((function(t){return-1!==e.indexOf(t)}))}(e,["-tw","-hk","-mo","-cht"])?"zh-Hant":"zh-Hans";var o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));var i=function(e,t){return t.find((function(t){return 0===e.indexOf(t)}))}(e,o);return i||void 0}}var b=function(){function e(t){var o=t.locale,i=t.fallbackLocale,n=t.messages,r=t.watcher,s=t.formater;(0,a.default)(this,e),this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],i&&(this.fallbackLocale=i),this.formater=s||v,this.messages=n||{},this.setLocale(o||"en"),r&&this.watchLocale(r)}return(0,s.default)(e,[{key:"setLocale",value:function(e){var t=this,o=this.locale;this.locale=y(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],o!==this.locale&&this.watchers.forEach((function(e){e(t.locale,o)}))}},{key:"getLocale",value:function(){return this.locale}},{key:"watchLocale",value:function(e){var t=this,o=this.watchers.push(e)-1;return function(){t.watchers.splice(o,1)}}},{key:"add",value:function(e,t){var o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=this.messages[e];i?o?Object.assign(i,t):Object.keys(t).forEach((function(e){_(i,e)||(i[e]=t[e])})):this.messages[e]=t}},{key:"f",value:function(e,t,o){return this.formater.interpolate(e,t,o).join("")}},{key:"t",value:function(e,t,o){var i=this.message;return"string"===typeof t?(t=y(t,this.messages),t&&(i=this.messages[t])):o=t,_(i,e)?this.formater.interpolate(i[e],o).join(""):(console.warn("Cannot translate the value of keypath ".concat(e,". Use the value of keypath as default.")),e)}}]),e}();function w(e,t){e.$watchLocale?e.$watchLocale((function(e){t.setLocale(e)})):e.$watch((function(){return e.$locale}),(function(e){t.setLocale(e)}))}function S(){return"undefined"!==typeof e&&e.getLocale?e.getLocale():"undefined"!==typeof i&&i.getLocale?i.getLocale():"en"}t.I18n=b;var k,x=function(e){return"string"===typeof e};function D(e,t){return e.indexOf(t[0])>-1}function C(e,t,o){return k.interpolate(e,t,o).join("")}function P(e,t,o){return O(e,(function(e,i){(function(e,t,o,i){var n=e[t];if(x(n)){if(D(n,i)&&(e[t]=C(n,o[0].values,i),o.length>1)){var r=e[t+"Locales"]={};o.forEach((function(e){r[e.locale]=C(n,e.values,i)}))}}else P(n,o,i)})(e,i,t,o)})),e}function O(e,t){if(Array.isArray(e)){for(var o=0;o<e.length;o++)if(t(e,o))return!0}else if(l(e))for(var i in e)if(t(e,i))return!0;return!1}t.isString=x}).call(this,o("df3c")["default"],o("0ee4"))},d551:function(e,t,o){var i=o("3b2d")["default"],n=o("e6db");e.exports=function(e){var t=n(e,"string");return"symbol"==i(t)?t:t+""},e.exports.__esModule=!0,e.exports["default"]=e.exports},d626:function(e,t,o){"use strict";var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("b5e4")),r=i(o("57df")),a=i(o("eff4")),s={en:n.default,"zh-Hans":r.default,"zh-Hant":a.default};t.default=s},d659:function(e,t,o){(function(e,i){var n,r=o("7ca3"),a=o("3b2d");!function(e,r){"object"==a(t)&&"object"==a(i)?i.exports=r(e):(n=function(){return r(e)}.call(t,o,t,i),void 0===n||(i.exports=n))}(window,(function(t,o){function i(e,o,i){t.WeixinJSBridge?WeixinJSBridge.invoke(e,a(o),(function(t){c(e,t,i)})):u(e,i)}function n(e,o,i){t.WeixinJSBridge?WeixinJSBridge.on(e,(function(t){i&&i.trigger&&i.trigger(t),c(e,t,o)})):u(e,i||o)}function a(e){return(e=e||{}).appId=O.appId,e.verifyAppId=O.appId,e.verifySignType="sha1",e.verifyTimestamp=O.timestamp+"",e.verifyNonceStr=O.nonceStr,e.verifySignature=O.signature,e}function s(e){return{timeStamp:e.timestamp+"",nonceStr:e.nonceStr,package:e.package,paySign:e.paySign,signType:e.signType||"SHA1"}}function c(e,t,o){"openEnterpriseChat"==e&&(t.errCode=t.err_code),delete t.err_code,delete t.err_desc,delete t.err_detail;var i=t.errMsg;i||(i=t.err_msg,delete t.err_msg,i=function(e,t){var o=e,i=g[o];i&&(o=i);var n="ok";if(t){var r=t.indexOf(":");"confirm"==(n=t.substring(r+1))&&(n="ok"),"failed"==n&&(n="fail"),-1!=n.indexOf("failed_")&&(n=n.substring(7)),-1!=n.indexOf("fail_")&&(n=n.substring(5)),"access denied"!=(n=(n=n.replace(/_/g," ")).toLowerCase())&&"no permission to execute"!=n||(n="permission denied"),"config"==o&&"function not exist"==n&&(n="ok"),""==n&&(n="fail")}return o+":"+n}(e,i),t.errMsg=i),(o=o||{})._complete&&(o._complete(t),delete o._complete),i=t.errMsg||"",O.debug&&!o.isInnerInvoke&&alert(JSON.stringify(t));var n=i.indexOf(":");switch(i.substring(n+1)){case"ok":o.success&&o.success(t);break;case"cancel":o.cancel&&o.cancel(t);break;default:o.fail&&o.fail(t)}o.complete&&o.complete(t)}function l(e){if(e){for(var t=0,o=e.length;t<o;++t){var i=e[t],n=h[i];n&&(e[t]=n)}return e}}function u(e,t){if(!(!O.debug||t&&t.isInnerInvoke)){var o=g[e];o&&(e=o),t&&t._complete&&delete t._complete,console.log('"'+e+'",',t||"")}}function d(){return(new Date).getTime()}function f(e){S&&(t.WeixinJSBridge?e():m.addEventListener&&m.addEventListener("WeixinJSBridgeReady",e,!1))}if(!t.jWeixin){var p,h={config:"preVerifyJSAPI",onMenuShareTimeline:"menu:share:timeline",onMenuShareAppMessage:"menu:share:appmessage",onMenuShareQQ:"menu:share:qq",onMenuShareWeibo:"menu:share:weiboApp",onMenuShareQZone:"menu:share:QZone",previewImage:"imagePreview",getLocation:"geoLocation",openProductSpecificView:"openProductViewWithPid",addCard:"batchAddCard",openCard:"batchViewCard",chooseWXPay:"getBrandWCPayRequest",openEnterpriseRedPacket:"getRecevieBizHongBaoRequest",startSearchBeacons:"startMonitoringBeacons",stopSearchBeacons:"stopMonitoringBeacons",onSearchBeacons:"onBeaconsInRange",consumeAndShareCard:"consumedShareCard",openAddress:"editAddress"},g=function(){var e={};for(var t in h)e[h[t]]=t;return e}(),m=t.document,_=m.title,v=navigator.userAgent.toLowerCase(),y=navigator.platform.toLowerCase(),b=!(!y.match("mac")&&!y.match("win")),w=-1!=v.indexOf("wxdebugger"),S=-1!=v.indexOf("micromessenger"),k=-1!=v.indexOf("android"),x=-1!=v.indexOf("iphone")||-1!=v.indexOf("ipad"),D=(E=v.match(/micromessenger\/(\d+\.\d+\.\d+)/)||v.match(/micromessenger\/(\d+\.\d+)/))?E[1]:"",C={initStartTime:d(),initEndTime:0,preVerifyStartTime:0,preVerifyEndTime:0},P={version:1,appId:"",initTime:0,preVerifyTime:0,networkType:"",isPreVerifyOk:1,systemType:x?1:k?2:-1,clientVersion:D,url:encodeURIComponent(location.href)},O={},$={_completes:[]},j={state:0,data:{}};f((function(){C.initEndTime=d()}));var M=!1,T=[],A=(p={config:function(e){u("config",O=e);var o=!1!==O.check;f((function(){if(o)i(h.config,{verifyJsApiList:l(O.jsApiList)},function(){$._complete=function(e){C.preVerifyEndTime=d(),j.state=1,j.data=e},$.success=function(e){P.isPreVerifyOk=0},$.fail=function(e){$._fail?$._fail(e):j.state=-1};var e=$._completes;return e.push((function(){!function(e){if(!(b||w||O.debug||D<"6.0.2"||P.systemType<0)){var t=new Image;P.appId=O.appId,P.initTime=C.initEndTime-C.initStartTime,P.preVerifyTime=C.preVerifyEndTime-C.preVerifyStartTime,A.getNetworkType({isInnerInvoke:!0,success:function(e){P.networkType=e.networkType;var o="https://open.weixin.qq.com/sdk/report?v="+P.version+"&o="+P.isPreVerifyOk+"&s="+P.systemType+"&c="+P.clientVersion+"&a="+P.appId+"&n="+P.networkType+"&i="+P.initTime+"&p="+P.preVerifyTime+"&u="+P.url;t.src=o}})}}()})),$.complete=function(t){for(var o=0,i=e.length;o<i;++o)e[o]();$._completes=[]},$}()),C.preVerifyStartTime=d();else{j.state=1;for(var e=$._completes,t=0,n=e.length;t<n;++t)e[t]();$._completes=[]}})),A.invoke||(A.invoke=function(e,o,i){t.WeixinJSBridge&&WeixinJSBridge.invoke(e,a(o),i)},A.on=function(e,o){t.WeixinJSBridge&&WeixinJSBridge.on(e,o)})},ready:function(e){0!=j.state?e():($._completes.push(e),!S&&O.debug&&e())},error:function(e){D<"6.0.2"||(-1==j.state?e(j.data):$._fail=e)},checkJsApi:function(e){i("checkJsApi",{jsApiList:l(e.jsApiList)},(e._complete=function(e){if(k){var t=e.checkResult;t&&(e.checkResult=JSON.parse(t))}e=function(e){var t=e.checkResult;for(var o in t){var i=g[o];i&&(t[i]=t[o],delete t[o])}return e}(e)},e))},onMenuShareTimeline:function(e){n(h.onMenuShareTimeline,{complete:function(){i("shareTimeline",{title:e.title||_,desc:e.title||_,img_url:e.imgUrl||"",link:e.link||location.href,type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareAppMessage:function(e){n(h.onMenuShareAppMessage,{complete:function(t){"favorite"===t.scene?i("sendAppMessage",{title:e.title||_,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""}):i("sendAppMessage",{title:e.title||_,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareQQ:function(e){n(h.onMenuShareQQ,{complete:function(){i("shareQQ",{title:e.title||_,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareWeibo:function(e){n(h.onMenuShareWeibo,{complete:function(){i("shareWeiboApp",{title:e.title||_,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareQZone:function(e){n(h.onMenuShareQZone,{complete:function(){i("shareQZone",{title:e.title||_,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},updateTimelineShareData:function(e){i("updateTimelineShareData",{title:e.title,link:e.link,imgUrl:e.imgUrl},e)},updateAppMessageShareData:function(e){i("updateAppMessageShareData",{title:e.title,desc:e.desc,link:e.link,imgUrl:e.imgUrl},e)},startRecord:function(e){i("startRecord",{},e)},stopRecord:function(e){i("stopRecord",{},e)},onVoiceRecordEnd:function(e){n("onVoiceRecordEnd",e)},playVoice:function(e){i("playVoice",{localId:e.localId},e)},pauseVoice:function(e){i("pauseVoice",{localId:e.localId},e)},stopVoice:function(e){i("stopVoice",{localId:e.localId},e)},onVoicePlayEnd:function(e){n("onVoicePlayEnd",e)},uploadVoice:function(e){i("uploadVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadVoice:function(e){i("downloadVoice",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},translateVoice:function(e){i("translateVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},chooseImage:function(e){i("chooseImage",{scene:"1|2",count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"]},(e._complete=function(e){if(k){var t=e.localIds;try{t&&(e.localIds=JSON.parse(t))}catch(e){}}},e))},getLocation:function(e){},previewImage:function(e){i(h.previewImage,{current:e.current,urls:e.urls},e)},uploadImage:function(e){i("uploadImage",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadImage:function(e){i("downloadImage",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},getLocalImgData:function(t){!1===M?(M=!0,i("getLocalImgData",{localId:t.localId},(t._complete=function(t){if(M=!1,0<T.length){var o=T.shift();e.getLocalImgData(o)}},t))):T.push(t)},getNetworkType:function(e){i("getNetworkType",{},(e._complete=function(e){e=function(e){var t=e.errMsg;e.errMsg="getNetworkType:ok";var o=e.subtype;if(delete e.subtype,o)e.networkType=o;else{var i=t.indexOf(":"),n=t.substring(i+1);switch(n){case"wifi":case"edge":case"wwan":e.networkType=n;break;default:e.errMsg="getNetworkType:fail"}}return e}(e)},e))},openLocation:function(e){i("openLocation",{latitude:e.latitude,longitude:e.longitude,name:e.name||"",address:e.address||"",scale:e.scale||28,infoUrl:e.infoUrl||""},e)}},r(p,"getLocation",(function(e){i(h.getLocation,{type:(e=e||{}).type||"wgs84"},(e._complete=function(e){delete e.type},e))})),r(p,"hideOptionMenu",(function(e){i("hideOptionMenu",{},e)})),r(p,"showOptionMenu",(function(e){i("showOptionMenu",{},e)})),r(p,"closeWindow",(function(e){i("closeWindow",{},e=e||{})})),r(p,"hideMenuItems",(function(e){i("hideMenuItems",{menuList:e.menuList},e)})),r(p,"showMenuItems",(function(e){i("showMenuItems",{menuList:e.menuList},e)})),r(p,"hideAllNonBaseMenuItem",(function(e){i("hideAllNonBaseMenuItem",{},e)})),r(p,"showAllNonBaseMenuItem",(function(e){i("showAllNonBaseMenuItem",{},e)})),r(p,"scanQRCode",(function(e){i("scanQRCode",{needResult:(e=e||{}).needResult||0,scanType:e.scanType||["qrCode","barCode"]},(e._complete=function(e){if(x){var t=e.resultStr;if(t){var o=JSON.parse(t);e.resultStr=o&&o.scan_code&&o.scan_code.scan_result}}},e))})),r(p,"openAddress",(function(e){i(h.openAddress,{},(e._complete=function(e){var t;(t=e).postalCode=t.addressPostalCode,delete t.addressPostalCode,t.provinceName=t.proviceFirstStageName,delete t.proviceFirstStageName,t.cityName=t.addressCitySecondStageName,delete t.addressCitySecondStageName,t.countryName=t.addressCountiesThirdStageName,delete t.addressCountiesThirdStageName,t.detailInfo=t.addressDetailInfo,delete t.addressDetailInfo,e=t},e))})),r(p,"openProductSpecificView",(function(e){i(h.openProductSpecificView,{pid:e.productId,view_type:e.viewType||0,ext_info:e.extInfo},e)})),r(p,"addCard",(function(e){for(var t=e.cardList,o=[],n=0,r=t.length;n<r;++n){var a=t[n],s={card_id:a.cardId,card_ext:a.cardExt};o.push(s)}i(h.addCard,{card_list:o},(e._complete=function(e){var t=e.card_list;if(t){for(var o=0,i=(t=JSON.parse(t)).length;o<i;++o){var n=t[o];n.cardId=n.card_id,n.cardExt=n.card_ext,n.isSuccess=!!n.is_succ,delete n.card_id,delete n.card_ext,delete n.is_succ}e.cardList=t,delete e.card_list}},e))})),r(p,"chooseCard",(function(e){i("chooseCard",{app_id:O.appId,location_id:e.shopId||"",sign_type:e.signType||"SHA1",card_id:e.cardId||"",card_type:e.cardType||"",card_sign:e.cardSign,time_stamp:e.timestamp+"",nonce_str:e.nonceStr},(e._complete=function(e){e.cardList=e.choose_card_info,delete e.choose_card_info},e))})),r(p,"openCard",(function(e){for(var t=e.cardList,o=[],n=0,r=t.length;n<r;++n){var a=t[n],s={card_id:a.cardId,code:a.code};o.push(s)}i(h.openCard,{card_list:o},e)})),r(p,"consumeAndShareCard",(function(e){i(h.consumeAndShareCard,{consumedCardId:e.cardId,consumedCode:e.code},e)})),r(p,"chooseWXPay",(function(e){i(h.chooseWXPay,s(e),e)})),r(p,"openEnterpriseRedPacket",(function(e){i(h.openEnterpriseRedPacket,s(e),e)})),r(p,"startSearchBeacons",(function(e){i(h.startSearchBeacons,{ticket:e.ticket},e)})),r(p,"stopSearchBeacons",(function(e){i(h.stopSearchBeacons,{},e)})),r(p,"onSearchBeacons",(function(e){n(h.onSearchBeacons,e)})),r(p,"openEnterpriseChat",(function(e){i("openEnterpriseChat",{useridlist:e.userIds,chatname:e.groupName},e)})),r(p,"launchMiniProgram",(function(e){i("launchMiniProgram",{targetAppId:e.targetAppId,path:function(e){if("string"==typeof e&&0<e.length){var t=e.split("?")[0],o=e.split("?")[1];return t+=".html",void 0!==o?t+"?"+o:t}}(e.path),envVersion:e.envVersion},e)})),r(p,"miniProgram",{navigateBack:function(e){e=e||{},f((function(){i("invokeMiniProgramAPI",{name:"navigateBack",arg:{delta:e.delta||1}},e)}))},navigateTo:function(e){f((function(){i("invokeMiniProgramAPI",{name:"navigateTo",arg:{url:e.url}},e)}))},redirectTo:function(e){f((function(){i("invokeMiniProgramAPI",{name:"redirectTo",arg:{url:e.url}},e)}))},switchTab:function(e){f((function(){i("invokeMiniProgramAPI",{name:"switchTab",arg:{url:e.url}},e)}))},reLaunch:function(e){f((function(){i("invokeMiniProgramAPI",{name:"reLaunch",arg:{url:e.url}},e)}))},postMessage:function(e){f((function(){i("invokeMiniProgramAPI",{name:"postMessage",arg:e.data||{}},e)}))},getEnv:function(e){f((function(){e({miniprogram:"miniprogram"===t.__wxjs_environment})}))}}),p),I=1,F={};return m.addEventListener("error",(function(t){if(!k){var o=t.target,i=o.tagName,n=o.src;if(("IMG"==i||"VIDEO"==i||"AUDIO"==i||"SOURCE"==i)&&-1!=n.indexOf("wxlocalresource://")){t.preventDefault(),t.stopPropagation();var r=o["wx-id"];if(r||(r=I++,o["wx-id"]=r),F[r])return;F[r]=!0,e.ready((function(){e.getLocalImgData({localId:n,success:function(e){o.src=e.localData}})}))}}}),!0),m.addEventListener("load",(function(e){if(!k){var t=e.target,o=t.tagName;if(t.src,"IMG"==o||"VIDEO"==o||"AUDIO"==o||"SOURCE"==o){var i=t["wx-id"];i&&(F[i]=!1)}}}),!0),o&&(t.wx=t.jWeixin=A),A}var E}))}).call(this,o("3223")["default"],o("dc84")(e))},d987:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"物流信息"}},dae7:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"账单"}},dbec:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"找回密码",findPassword:"找回密码",accountPlaceholder:"请输入手机号",captchaPlaceholder:"请输入验证码",dynacodePlaceholder:"请输入动态码",passwordPlaceholder:"请输入新密码",rePasswordPlaceholder:"请确认新密码",next:"下一步",save:"确认修改"}},dc84:function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},dd3e:function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports["default"]=e.exports},df3c:function(e,t,o){"use strict";(function(e,i){var n=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.createApp=It,t.createComponent=Gt,t.createPage=Vt,t.createPlugin=Ht,t.createSubpackageApp=qt,t.default=void 0;var r,a=n(o("34cf")),s=n(o("7ca3")),c=n(o("931d")),l=n(o("af34")),u=n(o("3b2d")),d=o("d3b4"),f=n(o("3240"));function p(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,i)}return o}function h(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?p(Object(o),!0).forEach((function(t){(0,s.default)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):p(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",m=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function _(){var t,o=e.getStorageSync("uni_id_token")||"",i=o.split(".");if(!o||3!==i.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{t=JSON.parse(function(e){return decodeURIComponent(r(e).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))}(i[1]))}catch(n){throw new Error("获取当前用户信息出错，详细错误信息为："+n.message)}return t.tokenExpired=1e3*t.exp,delete t.exp,delete t.iat,t}r="function"!==typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!m.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var o,i,n="",r=0;r<e.length;)t=g.indexOf(e.charAt(r++))<<18|g.indexOf(e.charAt(r++))<<12|(o=g.indexOf(e.charAt(r++)))<<6|(i=g.indexOf(e.charAt(r++))),n+=64===o?String.fromCharCode(t>>16&255):64===i?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return n}:atob;var v=Object.prototype.toString,y=Object.prototype.hasOwnProperty;function b(e){return"function"===typeof e}function w(e){return"string"===typeof e}function S(e){return"[object Object]"===v.call(e)}function k(e,t){return y.call(e,t)}function x(){}function D(e){var t=Object.create(null);return function(o){var i=t[o];return i||(t[o]=e(o))}}var C=/-(\w)/g,P=D((function(e){return e.replace(C,(function(e,t){return t?t.toUpperCase():""}))}));function O(e){var t={};return S(e)&&Object.keys(e).sort().forEach((function(o){t[o]=e[o]})),Object.keys(t)?t:e}var $=["invoke","success","fail","complete","returnValue"],j={},M={};function T(e,t){Object.keys(t).forEach((function(o){-1!==$.indexOf(o)&&b(t[o])&&(e[o]=function(e,t){var o=t?e?e.concat(t):Array.isArray(t)?t:[t]:e;return o?function(e){for(var t=[],o=0;o<e.length;o++)-1===t.indexOf(e[o])&&t.push(e[o]);return t}(o):o}(e[o],t[o]))}))}function A(e,t){e&&t&&Object.keys(t).forEach((function(o){-1!==$.indexOf(o)&&b(t[o])&&function(e,t){var o=e.indexOf(t);-1!==o&&e.splice(o,1)}(e[o],t[o])}))}function I(e,t){return function(o){return e(o,t)||o}}function F(e){return!!e&&("object"===(0,u.default)(e)||"function"===typeof e)&&"function"===typeof e.then}function E(e,t,o){for(var i=!1,n=0;n<e.length;n++){var r=e[n];if(i)i=Promise.resolve(I(r,o));else{var a=r(t,o);if(F(a)&&(i=Promise.resolve(a)),!1===a)return{then:function(){}}}}return i||{then:function(e){return e(t)}}}function L(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return["success","fail","complete"].forEach((function(o){if(Array.isArray(e[o])){var i=t[o];t[o]=function(n){E(e[o],n,t).then((function(e){return b(i)&&i(e)||e}))}}})),t}function R(e,t){var o=[];Array.isArray(j.returnValue)&&o.push.apply(o,(0,l.default)(j.returnValue));var i=M[e];return i&&Array.isArray(i.returnValue)&&o.push.apply(o,(0,l.default)(i.returnValue)),o.forEach((function(e){t=e(t)||t})),t}function N(e){var t=Object.create(null);Object.keys(j).forEach((function(e){"returnValue"!==e&&(t[e]=j[e].slice())}));var o=M[e];return o&&Object.keys(o).forEach((function(e){"returnValue"!==e&&(t[e]=(t[e]||[]).concat(o[e]))})),t}function U(e,t,o){for(var i=arguments.length,n=new Array(i>3?i-3:0),r=3;r<i;r++)n[r-3]=arguments[r];var a=N(e);if(a&&Object.keys(a).length){if(Array.isArray(a.invoke)){var s=E(a.invoke,o);return s.then((function(o){return t.apply(void 0,[L(N(e),o)].concat(n))}))}return t.apply(void 0,[L(a,o)].concat(n))}return t.apply(void 0,[o].concat(n))}var B={returnValue:function(e){return F(e)?new Promise((function(t,o){e.then((function(e){e?e[0]?o(e[0]):t(e[1]):t(e)}))})):e}},z=/^\$|__f__|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|rpx2px|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting|initUTS|requireUTS|registerUTS/,V=/^create|Manager$/,G=["createBLEConnection"],q=["createBLEConnection","createPushMessage"],H=/^on|^off/;function W(e){return V.test(e)&&-1===G.indexOf(e)}function J(e){return z.test(e)&&-1===q.indexOf(e)}function X(e){return e.then((function(e){return[null,e]})).catch((function(e){return[e]}))}function Y(e){return!(W(e)||J(e)||function(e){return H.test(e)&&"onPush"!==e}(e))}function K(e,t){return Y(e)&&b(t)?function(){for(var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length,n=new Array(i>1?i-1:0),r=1;r<i;r++)n[r-1]=arguments[r];return b(o.success)||b(o.fail)||b(o.complete)?R(e,U.apply(void 0,[e,t,o].concat(n))):R(e,X(new Promise((function(i,r){U.apply(void 0,[e,t,Object.assign({},o,{success:i,fail:r})].concat(n))}))))}:t}Promise.prototype.finally||(Promise.prototype.finally=function(e){var t=this.constructor;return this.then((function(o){return t.resolve(e()).then((function(){return o}))}),(function(o){return t.resolve(e()).then((function(){throw o}))}))});var Q=!1,Z=0,ee=0;function te(t,o){if(0===Z&&function(){var t,o,i,n="function"===typeof e.getWindowInfo&&e.getWindowInfo()?e.getWindowInfo():e.getSystemInfoSync(),r="function"===typeof e.getDeviceInfo&&e.getDeviceInfo()?e.getDeviceInfo():e.getSystemInfoSync();t=n.windowWidth,o=n.pixelRatio,i=r.platform,Z=t,ee=o,Q="ios"===i}(),t=Number(t),0===t)return 0;var i=t/750*(o||Z);return i<0&&(i=-i),i=Math.floor(i+1e-4),0===i&&(i=1!==ee&&Q?.5:1),t<0?-i:i}var oe,ie={};function ne(){var t,o="function"===typeof e.getAppBaseInfo&&e.getAppBaseInfo()?e.getAppBaseInfo():e.getSystemInfoSync(),i=o&&o.language?o.language:"en";return t=se(i)||"en",t}oe=ne(),function(){if(function(){return"undefined"!==typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length}()){var e=Object.keys(__uniConfig.locales);e.length&&e.forEach((function(e){var t=ie[e],o=__uniConfig.locales[e];t?Object.assign(t,o):ie[e]=o}))}}();var re=(0,d.initVueI18n)(oe,{}),ae=re.t;re.mixin={beforeCreate:function(){var e=this,t=re.i18n.watchLocale((function(){e.$forceUpdate()}));this.$once("hook:beforeDestroy",(function(){t()}))},methods:{$$t:function(e,t){return ae(e,t)}}},re.setLocale,re.getLocale;function se(e,t){if(e){if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if(e=e.toLowerCase(),"chinese"===e)return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1||function(e,t){return!!t.find((function(t){return-1!==e.indexOf(t)}))}(e,["-tw","-hk","-mo","-cht"])?"zh-Hant":"zh-Hans";var o=function(e,t){return t.find((function(t){return 0===e.indexOf(t)}))}(e,["en","fr","es"]);return o||void 0}}function ce(){if(b(getApp)){var e=getApp({allowDefault:!0});if(e&&e.$vm)return e.$vm.$locale}return ne()}var le=[];"undefined"!==typeof i&&(i.getLocale=ce);var ue={promiseInterceptor:B},de=Object.freeze({__proto__:null,upx2px:te,rpx2px:te,getLocale:ce,setLocale:function(e){var t=!!b(getApp)&&getApp();if(!t)return!1;var o=t.$vm.$locale;return o!==e&&(t.$vm.$locale=e,le.forEach((function(t){return t({locale:e})})),!0)},onLocaleChange:function(e){-1===le.indexOf(e)&&le.push(e)},addInterceptor:function(e,t){"string"===typeof e&&S(t)?T(M[e]||(M[e]={}),t):S(e)&&T(j,e)},removeInterceptor:function(e,t){"string"===typeof e?S(t)?A(M[e],t):delete M[e]:S(e)&&A(j,e)},interceptors:ue});var fe,pe={name:function(e){return"back"===e.exists&&e.delta?"navigateBack":"redirectTo"},args:function(e){if("back"===e.exists&&e.url){var t=function(e){var t=getCurrentPages(),o=t.length;while(o--){var i=t[o];if(i.$page&&i.$page.fullPath===e)return o}return-1}(e.url);if(-1!==t){var o=getCurrentPages().length-1-t;o>0&&(e.delta=o)}}}},he={args:function(e){var t=parseInt(e.current);if(!isNaN(t)){var o=e.urls;if(Array.isArray(o)){var i=o.length;if(i)return t<0?t=0:t>=i&&(t=i-1),t>0?(e.current=o[t],e.urls=o.filter((function(e,i){return!(i<t)||e!==o[t]}))):e.current=o[0],{indicator:!1,loop:!1}}}}};function ge(t){fe=fe||e.getStorageSync("__DC_STAT_UUID"),fe||(fe=Date.now()+""+Math.floor(1e7*Math.random()),e.setStorage({key:"__DC_STAT_UUID",data:fe})),t.deviceId=fe}function me(e){if(e.safeArea){var t=e.safeArea;e.safeAreaInsets={top:t.top,left:t.left,right:e.windowWidth-t.right,bottom:e.screenHeight-t.bottom}}}function _e(e,t){var o="",i="";switch(o=e.split(" ")[0]||t,i=e.split(" ")[1]||"",o=o.toLocaleLowerCase(),o){case"harmony":case"ohos":case"openharmony":o="harmonyos";break;case"iphone os":o="ios";break;case"mac":case"darwin":o="macos";break;case"windows_nt":o="windows";break}return{osName:o,osVersion:i}}function ve(e,t){for(var o=e.deviceType||"phone",i={ipad:"pad",windows:"pc",mac:"pc"},n=Object.keys(i),r=t.toLocaleLowerCase(),a=0;a<n.length;a++){var s=n[a];if(-1!==r.indexOf(s)){o=i[s];break}}return o}function ye(e){var t=e;return t&&(t=e.toLocaleLowerCase()),t}function be(e){return ce?ce():e}function we(e){var t=e.hostName||"WeChat";return e.environment?t=e.environment:e.host&&e.host.env&&(t=e.host.env),t}var Se={returnValue:function(e){ge(e),me(e),function(e){var t=e.brand,o=void 0===t?"":t,i=e.model,n=void 0===i?"":i,r=e.system,a=void 0===r?"":r,s=e.language,c=void 0===s?"":s,l=e.theme,u=e.version,d=e.platform,f=e.fontSizeSetting,p=e.SDKVersion,h=e.pixelRatio,g=e.deviceOrientation,m=_e(a,d),_=m.osName,v=m.osVersion,y=u,b=ve(e,n),w=ye(o),S=we(e),k=g,x=h,D=p,C=(c||"").replace(/_/g,"-"),P={appId:"__UNI__09B25F9",appName:"单商户V5",appVersion:"1.0.0",appVersionCode:"100",appLanguage:be(C),uniCompileVersion:"4.65",uniCompilerVersion:"4.65",uniRuntimeVersion:"4.65",uniPlatform:"mp-weixin",deviceBrand:w,deviceModel:n,deviceType:b,devicePixelRatio:x,deviceOrientation:k,osName:_.toLocaleLowerCase(),osVersion:v,hostTheme:l,hostVersion:y,hostLanguage:C,hostName:S,hostSDKVersion:D,hostFontSizeSetting:f,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};Object.assign(e,P,{})}(e)}},ke={args:function(e){"object"===(0,u.default)(e)&&(e.alertText=e.title)}},xe={returnValue:function(e){var t=e,o=t.version,i=t.language,n=t.SDKVersion,r=t.theme,a=we(e),s=(i||"").replace("_","-");e=O(Object.assign(e,{appId:"__UNI__09B25F9",appName:"单商户V5",appVersion:"1.0.0",appVersionCode:"100",appLanguage:be(s),hostVersion:o,hostLanguage:s,hostName:a,hostSDKVersion:n,hostTheme:r,isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.65",uniCompilerVersion:"4.65",uniRuntimeVersion:"4.65"}))}},De={returnValue:function(e){var t=e,o=t.brand,i=t.model,n=t.system,r=void 0===n?"":n,a=t.platform,s=void 0===a?"":a,c=ve(e,i),l=ye(o);ge(e);var u=_e(r,s),d=u.osName,f=u.osVersion;e=O(Object.assign(e,{deviceType:c,deviceBrand:l,deviceModel:i,osName:d,osVersion:f}))}},Ce={returnValue:function(e){me(e),e=O(Object.assign(e,{windowTop:0,windowBottom:0}))}},Pe={redirectTo:pe,previewImage:he,getSystemInfo:Se,getSystemInfoSync:Se,showActionSheet:ke,getAppBaseInfo:xe,getDeviceInfo:De,getWindowInfo:Ce,getAppAuthorizeSetting:{returnValue:function(e){var t=e.locationReducedAccuracy;e.locationAccuracy="unsupported",!0===t?e.locationAccuracy="reduced":!1===t&&(e.locationAccuracy="full")}},compressImage:{args:function(e){e.compressedHeight&&!e.compressHeight&&(e.compressHeight=e.compressedHeight),e.compressedWidth&&!e.compressWidth&&(e.compressWidth=e.compressedWidth)}}},Oe=["success","fail","cancel","complete"];function $e(e,t,o){return function(i){return t(Me(e,i,o))}}function je(e,t){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(S(t)){var r=!0===n?t:{};for(var a in b(o)&&(o=o(t,r)||{}),t)if(k(o,a)){var s=o[a];b(s)&&(s=s(t[a],t,r)),s?w(s)?r[s]=t[a]:S(s)&&(r[s.name?s.name:a]=s.value):console.warn("The '".concat(e,"' method of platform '微信小程序' does not support option '").concat(a,"'"))}else-1!==Oe.indexOf(a)?b(t[a])&&(r[a]=$e(e,t[a],i)):n||(r[a]=t[a]);return r}return b(t)&&(t=$e(e,t,i)),t}function Me(e,t,o){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return b(Pe.returnValue)&&(t=Pe.returnValue(e,t)),je(e,t,o,{},i)}function Te(t,o){if(k(Pe,t)){var i=Pe[t];return i?function(o,n){var r=i;b(i)&&(r=i(o)),o=je(t,o,r.args,r.returnValue);var a=[o];"undefined"!==typeof n&&a.push(n),b(r.name)?t=r.name(o):w(r.name)&&(t=r.name);var s=e[t].apply(e,a);return J(t)?Me(t,s,r.returnValue,W(t)):s}:function(){console.error("Platform '微信小程序' does not support '".concat(t,"'."))}}return o}var Ae=Object.create(null);["onTabBarMidButtonTap","subscribePush","unsubscribePush","onPush","offPush","share"].forEach((function(e){Ae[e]=function(e){return function(t){var o=t.fail,i=t.complete,n={errMsg:"".concat(e,":fail method '").concat(e,"' not supported")};b(o)&&o(n),b(i)&&i(n)}}(e)}));var Ie={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]};var Fe=Object.freeze({__proto__:null,getProvider:function(e){var t=e.service,o=e.success,i=e.fail,n=e.complete,r=!1;Ie[t]?(r={errMsg:"getProvider:ok",service:t,provider:Ie[t]},b(o)&&o(r)):(r={errMsg:"getProvider:fail service not found"},b(i)&&i(r)),b(n)&&n(r)}}),Ee=function(){var e;return function(){return e||(e=new f.default),e}}();function Le(e,t,o){return e[t].apply(e,o)}var Re,Ne,Ue,Be=Object.freeze({__proto__:null,$on:function(){return Le(Ee(),"$on",Array.prototype.slice.call(arguments))},$off:function(){return Le(Ee(),"$off",Array.prototype.slice.call(arguments))},$once:function(){return Le(Ee(),"$once",Array.prototype.slice.call(arguments))},$emit:function(){return Le(Ee(),"$emit",Array.prototype.slice.call(arguments))}});function ze(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}function Ve(e){try{return JSON.parse(e)}catch(t){}return e}var Ge=[];function qe(e,t){Ge.forEach((function(o){o(e,t)})),Ge.length=0}var He=[];var We=e.getAppBaseInfo&&e.getAppBaseInfo();We||(We=e.getSystemInfoSync());var Je=We?We.host:null,Xe=Je&&"SAAASDK"===Je.env?e.miniapp.shareVideoMessage:e.shareVideoMessage,Ye=Object.freeze({__proto__:null,shareVideoMessage:Xe,getPushClientId:function(e){S(e)||(e={});var t=function(e){var t={};for(var o in e){var i=e[o];b(i)&&(t[o]=ze(i),delete e[o])}return t}(e),o=t.success,i=t.fail,n=t.complete,r=b(o),a=b(i),s=b(n);Promise.resolve().then((function(){"undefined"===typeof Ue&&(Ue=!1,Re="",Ne="uniPush is not enabled"),Ge.push((function(e,t){var c;e?(c={errMsg:"getPushClientId:ok",cid:e},r&&o(c)):(c={errMsg:"getPushClientId:fail"+(t?" "+t:"")},a&&i(c)),s&&n(c)})),"undefined"!==typeof Re&&qe(Re,Ne)}))},onPushMessage:function(e){-1===He.indexOf(e)&&He.push(e)},offPushMessage:function(e){if(e){var t=He.indexOf(e);t>-1&&He.splice(t,1)}else He.length=0},invokePushCallback:function(e){if("enabled"===e.type)Ue=!0;else if("clientId"===e.type)Re=e.cid,Ne=e.errMsg,qe(Re,e.errMsg);else if("pushMsg"===e.type)for(var t={type:"receive",data:Ve(e.message)},o=0;o<He.length;o++){var i=He[o];if(i(t),t.stopped)break}else"click"===e.type&&He.forEach((function(t){t({type:"click",data:Ve(e.message)})}))},__f__:function(e){for(var t=arguments.length,o=new Array(t>1?t-1:0),i=1;i<t;i++)o[i-1]=arguments[i];console[e].apply(console,o)}}),Ke=["__route__","__wxExparserNodeId__","__wxWebviewId__"];function Qe(e){return Behavior(e)}function Ze(){return!!this.route}function et(e){this.triggerEvent("__l",e)}function tt(e){var t=e.$scope,o={};Object.defineProperty(e,"$refs",{get:function(){var e={};(function e(t,o,i){var n=t.selectAllComponents(o)||[];n.forEach((function(t){var n=t.dataset.ref;i[n]=t.$vm||nt(t),"scoped"===t.dataset.vueGeneric&&t.selectAllComponents(".scoped-ref").forEach((function(t){e(t,o,i)}))}))})(t,".vue-ref",e);var i=t.selectAllComponents(".vue-ref-in-for")||[];return i.forEach((function(t){var o=t.dataset.ref;e[o]||(e[o]=[]),e[o].push(t.$vm||nt(t))})),function(e,t){var o=(0,c.default)(Set,(0,l.default)(Object.keys(e))),i=Object.keys(t);return i.forEach((function(i){var n=e[i],r=t[i];Array.isArray(n)&&Array.isArray(r)&&n.length===r.length&&r.every((function(e){return n.includes(e)}))||(e[i]=r,o.delete(i))})),o.forEach((function(t){delete e[t]})),e}(o,e)}})}function ot(e){var t,o=e.detail||e.value,i=o.vuePid,n=o.vueOptions;i&&(t=function e(t,o){for(var i,n=t.$children,r=n.length-1;r>=0;r--){var a=n[r];if(a.$scope._$vueId===o)return a}for(var s=n.length-1;s>=0;s--)if(i=e(n[s],o),i)return i}(this.$vm,i)),t||(t=this.$vm),n.parent=t}function it(e){return Object.defineProperty(e,"__v_isMPComponent",{configurable:!0,enumerable:!1,value:!0}),e}function nt(e){return function(e){return null!==e&&"object"===(0,u.default)(e)}(e)&&Object.isExtensible(e)&&Object.defineProperty(e,"__ob__",{configurable:!0,enumerable:!1,value:(0,s.default)({},"__v_skip",!0)}),e}var rt=/_(.*)_worklet_factory_/;var at=Page,st=Component,ct=/:/g,lt=D((function(e){return P(e.replace(ct,"-"))}));function ut(e){var t=e.triggerEvent,o=function(e){for(var o=arguments.length,i=new Array(o>1?o-1:0),n=1;n<o;n++)i[n-1]=arguments[n];if(this.$vm||this.dataset&&this.dataset.comType)e=lt(e);else{var r=lt(e);r!==e&&t.apply(this,[r].concat(i))}return t.apply(this,[e].concat(i))};try{e.triggerEvent=o}catch(i){e._triggerEvent=o}}function dt(e,t,o){var i=t[e];t[e]=function(){if(it(this),ut(this),i){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return i.apply(this,t)}}}at.__$wrappered||(at.__$wrappered=!0,Page=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return dt("onLoad",e),at(e)},Page.after=at.after,Component=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return dt("created",e),st(e)});function ft(e,t,o){t.forEach((function(t){(function e(t,o){if(!o)return!0;if(f.default.options&&Array.isArray(f.default.options[t]))return!0;if(o=o.default||o,b(o))return!!b(o.extendOptions[t])||!!(o.super&&o.super.options&&Array.isArray(o.super.options[t]));if(b(o[t])||Array.isArray(o[t]))return!0;var i=o.mixins;return Array.isArray(i)?!!i.find((function(o){return e(t,o)})):void 0})(t,o)&&(e[t]=function(e){return this.$vm&&this.$vm.__call_hook(t,e)})}))}function pt(e,t){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];ht(t).forEach((function(t){return gt(e,t,o)}))}function ht(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e&&Object.keys(e).forEach((function(o){0===o.indexOf("on")&&b(e[o])&&t.push(o)})),t}function gt(e,t,o){-1!==o.indexOf(t)||k(e,t)||(e[t]=function(e){return this.$vm&&this.$vm.__call_hook(t,e)})}function mt(e,t){var o;return t=t.default||t,o=b(t)?t:e.extend(t),t=o.options,[o,t]}function _t(e,t){if(Array.isArray(t)&&t.length){var o=Object.create(null);t.forEach((function(e){o[e]=!0})),e.$scopedSlots=e.$slots=o}}function vt(e,t){e=(e||"").split(",");var o=e.length;1===o?t._$vueId=e[0]:2===o&&(t._$vueId=e[0],t._$vuePid=e[1])}function yt(e,t){var o=e.data||{},i=e.methods||{};if("function"===typeof o)try{o=o.call(t)}catch(n){Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"单商户V5",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG&&console.warn("根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。",o)}else try{o=JSON.parse(JSON.stringify(o))}catch(n){}return S(o)||(o={}),Object.keys(i).forEach((function(e){-1!==t.__lifecycle_hooks__.indexOf(e)||k(o,e)||(o[e]=i[e])})),o}var bt=[String,Number,Boolean,Object,Array,null];function wt(e){return function(t,o){this.$vm&&(this.$vm[e]=t)}}function St(e,t){var o=e.behaviors,i=e.extends,n=e.mixins,r=e.props;r||(e.props=r=[]);var a=[];return Array.isArray(o)&&o.forEach((function(e){a.push(e.replace("uni://","wx".concat("://"))),"uni://form-field"===e&&(Array.isArray(r)?(r.push("name"),r.push("value")):(r.name={type:String,default:""},r.value={type:[String,Number,Boolean,Array,Object,Date],default:""}))})),S(i)&&i.props&&a.push(t({properties:xt(i.props,!0)})),Array.isArray(n)&&n.forEach((function(e){S(e)&&e.props&&a.push(t({properties:xt(e.props,!0)}))})),a}function kt(e,t,o,i){return Array.isArray(t)&&1===t.length?t[0]:t}function xt(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>3?arguments[3]:void 0,i={};return t||(i.vueId={type:String,value:""},o.virtualHost&&(i.virtualHostStyle={type:null,value:""},i.virtualHostClass={type:null,value:""}),i.scopedSlotsCompiler={type:String,value:""},i.vueSlots={type:null,value:[],observer:function(e,t){var o=Object.create(null);e.forEach((function(e){o[e]=!0})),this.setData({$slots:o})}}),Array.isArray(e)?e.forEach((function(e){i[e]={type:null,observer:wt(e)}})):S(e)&&Object.keys(e).forEach((function(t){var o=e[t];if(S(o)){var n=o.default;b(n)&&(n=n()),o.type=kt(0,o.type),i[t]={type:-1!==bt.indexOf(o.type)?o.type:null,value:n,observer:wt(t)}}else{var r=kt(0,o);i[t]={type:-1!==bt.indexOf(r)?r:null,observer:wt(t)}}})),i}function Dt(e,t,o,i){var n={};return Array.isArray(t)&&t.length&&t.forEach((function(t,r){"string"===typeof t?t?"$event"===t?n["$"+r]=o:"arguments"===t?n["$"+r]=o.detail&&o.detail.__args__||i:0===t.indexOf("$event.")?n["$"+r]=e.__get_value(t.replace("$event.",""),o):n["$"+r]=e.__get_value(t):n["$"+r]=e:n["$"+r]=function(e,t){var o=e;return t.forEach((function(t){var i=t[0],n=t[2];if(i||"undefined"!==typeof n){var r,a=t[1],s=t[3];Number.isInteger(i)?r=i:i?"string"===typeof i&&i&&(r=0===i.indexOf("#s#")?i.substr(3):e.__get_value(i,o)):r=o,Number.isInteger(r)?o=n:a?Array.isArray(r)?o=r.find((function(t){return e.__get_value(a,t)===n})):S(r)?o=Object.keys(r).find((function(t){return e.__get_value(a,r[t])===n})):console.error("v-for 暂不支持循环数据：",r):o=r[n],s&&(o=e.__get_value(s,o))}})),o}(e,t)})),n}function Ct(e){for(var t={},o=1;o<e.length;o++){var i=e[o];t[i[0]]=i[1]}return t}function Pt(e,t){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],n=arguments.length>4?arguments[4]:void 0,r=arguments.length>5?arguments[5]:void 0,a=!1,s=S(t.detail)&&t.detail.__args__||[t.detail];if(n&&(a=t.currentTarget&&t.currentTarget.dataset&&"wx"===t.currentTarget.dataset.comType,!o.length))return a?[t]:s;var c=Dt(e,i,t,s),l=[];return o.forEach((function(e){"$event"===e?"__set_model"!==r||n?n&&!a?l.push(s[0]):l.push(t):l.push(t.target.value):Array.isArray(e)&&"o"===e[0]?l.push(Ct(e)):"string"===typeof e&&k(c,e)?l.push(c[e]):l.push(e)})),l}function Ot(e){var t=this;e=function(e){try{e.mp=JSON.parse(JSON.stringify(e))}catch(t){}return e.stopPropagation=x,e.preventDefault=x,e.target=e.target||{},k(e,"detail")||(e.detail={}),k(e,"markerId")&&(e.detail="object"===(0,u.default)(e.detail)?e.detail:{},e.detail.markerId=e.markerId),S(e.detail)&&(e.target=Object.assign({},e.target,e.detail)),e}(e);var o=(e.currentTarget||e.target).dataset;if(!o)return console.warn("事件信息不存在");var i=o.eventOpts||o["event-opts"];if(!i)return console.warn("事件信息不存在");var n=e.type,r=[];return i.forEach((function(o){var i=o[0],a=o[1],s="^"===i.charAt(0);i=s?i.slice(1):i;var c="~"===i.charAt(0);i=c?i.slice(1):i,a&&function(e,t){return e===t||"regionchange"===t&&("begin"===e||"end"===e)}(n,i)&&a.forEach((function(o){var i=o[0];if(i){var n=t.$vm;if(n.$options.generic&&(n=function(e){var t=e.$parent;while(t&&t.$parent&&(t.$options.generic||t.$parent.$options.generic||t.$scope._$vuePid))t=t.$parent;return t&&t.$parent}(n)||n),"$emit"===i)return void n.$emit.apply(n,Pt(t.$vm,e,o[1],o[2],s,i));var a=n[i];if(!b(a)){var l="page"===t.$vm.mpType?"Page":"Component",u=t.route||t.is;throw new Error("".concat(l,' "').concat(u,'" does not have a method "').concat(i,'"'))}if(c){if(a.once)return;a.once=!0}var d=Pt(t.$vm,e,o[1],o[2],s,i);d=Array.isArray(d)?d:[],/=\s*\S+\.eventParams\s*\|\|\s*\S+\[['"]event-params['"]\]/.test(a.toString())&&(d=d.concat([,,,,,,,,,,e])),r.push(a.apply(n,d))}}))})),"input"===n&&1===r.length&&"undefined"!==typeof r[0]?r[0]:void 0}var $t={};var jt=["onShow","onHide","onError","onPageNotFound","onThemeChange","onUnhandledRejection"];function Mt(){f.default.prototype.getOpenerEventChannel=function(){return this.$scope.getOpenerEventChannel()};var e=f.default.prototype.__call_hook;f.default.prototype.__call_hook=function(t,o){return"onLoad"===t&&o&&o.__id__&&(this.__eventChannel__=function(e){var t=$t[e];return delete $t[e],t}(o.__id__),delete o.__id__),e.call(this,t,o)}}function Tt(t,o){var i=o.mocks,n=o.initRefs;Mt(),function(){var e={},t={};function o(e){var t=this.$options.propsData.vueId;if(t){var o=t.split(",")[0];e(o)}}f.default.prototype.$hasSSP=function(o){var i=e[o];return i||(t[o]=this,this.$on("hook:destroyed",(function(){delete t[o]}))),i},f.default.prototype.$getSSP=function(t,o,i){var n=e[t];if(n){var r=n[o]||[];return i?r:r[0]}},f.default.prototype.$setSSP=function(t,i){var n=0;return o.call(this,(function(o){var r=e[o],a=r[t]=r[t]||[];a.push(i),n=a.length-1})),n},f.default.prototype.$initSSP=function(){o.call(this,(function(t){e[t]={}}))},f.default.prototype.$callSSP=function(){o.call(this,(function(e){t[e]&&t[e].$forceUpdate()}))},f.default.mixin({destroyed:function(){var o=this.$options.propsData,i=o&&o.vueId;i&&(delete e[i],delete t[i])}})}(),t.$options.store&&(f.default.prototype.$store=t.$options.store),function(e){e.prototype.uniIDHasRole=function(e){var t=_(),o=t.role;return o.indexOf(e)>-1},e.prototype.uniIDHasPermission=function(e){var t=_(),o=t.permission;return this.uniIDHasRole("admin")||o.indexOf(e)>-1},e.prototype.uniIDTokenValid=function(){var e=_(),t=e.tokenExpired;return t>Date.now()}}(f.default),f.default.prototype.mpHost="mp-weixin",f.default.mixin({beforeCreate:function(){if(this.$options.mpType){if(this.mpType=this.$options.mpType,this.$mp=(0,s.default)({data:{}},this.mpType,this.$options.mpInstance),this.$scope=this.$options.mpInstance,delete this.$options.mpType,delete this.$options.mpInstance,"page"===this.mpType&&"function"===typeof getApp){var e=getApp();e.$vm&&e.$vm.$i18n&&(this._i18n=e.$vm.$i18n)}"app"!==this.mpType&&(n(this),function(e,t){var o=e.$mp[e.mpType];t.forEach((function(t){k(o,t)&&(e[t]=o[t])}))}(this,i))}}});var r={onLaunch:function(o){this.$vm||(e.canIUse&&!e.canIUse("nextTick")&&console.error("当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上"),this.$vm=t,this.$vm.$mp={app:this},this.$vm.$scope=this,this.$vm.globalData=this.globalData,this.$vm._isMounted=!0,this.$vm.__call_hook("mounted",o),this.$vm.__call_hook("onLaunch",o))}};r.globalData=t.$options.globalData||{};var a=t.$options.methods;return a&&Object.keys(a).forEach((function(e){r[e]=a[e]})),function(e,t,o){var i=e.observable({locale:o||re.getLocale()}),n=[];t.$watchLocale=function(e){n.push(e)},Object.defineProperty(t,"$locale",{get:function(){return i.locale},set:function(e){i.locale=e,n.forEach((function(t){return t(e)}))}})}(f.default,t,function(){var t,o=e.getAppBaseInfo(),i=o&&o.language?o.language:"en";return t=se(i)||"en",t}()),ft(r,jt),pt(r,t.$options),r}function At(e){return Tt(e,{mocks:Ke,initRefs:tt})}function It(e){return App(At(e)),e}var Ft=/[!'()*]/g,Et=function(e){return"%"+e.charCodeAt(0).toString(16)},Lt=/%2C/g,Rt=function(e){return encodeURIComponent(e).replace(Ft,Et).replace(Lt,",")};function Nt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Rt,o=e?Object.keys(e).map((function(o){var i=e[o];if(void 0===i)return"";if(null===i)return t(o);if(Array.isArray(i)){var n=[];return i.forEach((function(e){void 0!==e&&(null===e?n.push(t(o)):n.push(t(o)+"="+t(e)))})),n.join("&")}return t(o)+"="+t(i)})).filter((function(e){return e.length>0})).join("&"):null;return o?"?".concat(o):""}function Ut(e,t){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=t.isPage,i=t.initRelation,n=arguments.length>2?arguments[2]:void 0,r=mt(f.default,e),s=(0,a.default)(r,2),c=s[0],l=s[1],u=h({multipleSlots:!0,addGlobalClass:!0},l.options||{});l["mp-weixin"]&&l["mp-weixin"].options&&Object.assign(u,l["mp-weixin"].options);var d={options:u,data:yt(l,f.default.prototype),behaviors:St(l,Qe),properties:xt(l.props,!1,l.__file,u),lifetimes:{attached:function(){var e=this.properties,t={mpType:o.call(this)?"page":"component",mpInstance:this,propsData:e};vt(e.vueId,this),i.call(this,{vuePid:this._$vuePid,vueOptions:t}),this.$vm=new c(t),_t(this.$vm,e.vueSlots),this.$vm.$mount()},ready:function(){this.$vm&&(this.$vm._isMounted=!0,this.$vm.__call_hook("mounted"),this.$vm.__call_hook("onReady"))},detached:function(){this.$vm&&this.$vm.$destroy()}},pageLifetimes:{show:function(e){this.$vm&&this.$vm.__call_hook("onPageShow",e)},hide:function(){this.$vm&&this.$vm.__call_hook("onPageHide")},resize:function(e){this.$vm&&this.$vm.__call_hook("onPageResize",e)}},methods:{__l:ot,__e:Ot}};return l.externalClasses&&(d.externalClasses=l.externalClasses),Array.isArray(l.wxsCallMethods)&&l.wxsCallMethods.forEach((function(e){d.methods[e]=function(t){return this.$vm[e](t)}})),n?[d,l,c]:o?d:[d,c]}(e,{isPage:Ze,initRelation:et},t)}var Bt=["onShow","onHide","onUnload"];function zt(e){var t=Ut(e,!0),o=(0,a.default)(t,2),i=o[0],n=o[1];return ft(i.methods,Bt,n),i.methods.onLoad=function(e){this.options=e;var t=Object.assign({},e);delete t.__id__,this.$page={fullPath:"/"+(this.route||this.is)+Nt(t)},this.$vm.$mp.query=e,this.$vm.__call_hook("onLoad",e)},pt(i.methods,e,["onReady"]),function(e,t){t&&Object.keys(t).forEach((function(o){var i=o.match(rt);if(i){var n=i[1];e[o]=t[o],e[n]=t[n]}}))}(i.methods,n.methods),i}function Vt(e){return Component(function(e){return zt(e)}(e))}function Gt(e){return Component(Ut(e))}function qt(t){var o=At(t),i=getApp({allowDefault:!0});t.$scope=i;var n=i.globalData;if(n&&Object.keys(o.globalData).forEach((function(e){k(n,e)||(n[e]=o.globalData[e])})),Object.keys(o).forEach((function(e){k(i,e)||(i[e]=o[e])})),b(o.onShow)&&e.onAppShow&&e.onAppShow((function(){for(var e=arguments.length,o=new Array(e),i=0;i<e;i++)o[i]=arguments[i];t.__call_hook("onShow",o)})),b(o.onHide)&&e.onAppHide&&e.onAppHide((function(){for(var e=arguments.length,o=new Array(e),i=0;i<e;i++)o[i]=arguments[i];t.__call_hook("onHide",o)})),b(o.onLaunch)){var r=e.getLaunchOptionsSync&&e.getLaunchOptionsSync();t.__call_hook("onLaunch",r)}return t}function Ht(t){var o=At(t);if(b(o.onShow)&&e.onAppShow&&e.onAppShow((function(){for(var e=arguments.length,o=new Array(e),i=0;i<e;i++)o[i]=arguments[i];t.__call_hook("onShow",o)})),b(o.onHide)&&e.onAppHide&&e.onAppHide((function(){for(var e=arguments.length,o=new Array(e),i=0;i<e;i++)o[i]=arguments[i];t.__call_hook("onHide",o)})),b(o.onLaunch)){var i=e.getLaunchOptionsSync&&e.getLaunchOptionsSync();t.__call_hook("onLaunch",i)}return t}Bt.push.apply(Bt,["onPullDownRefresh","onReachBottom","onAddToFavorites","onShareTimeline","onShareAppMessage","onPageScroll","onResize","onTabItemTap"]),["vibrate","preloadPage","unPreloadPage","loadSubPackage"].forEach((function(e){Pe[e]=!1})),[].forEach((function(t){var o=Pe[t]&&Pe[t].name?Pe[t].name:t;e.canIUse(o)||(Pe[t]=!1)}));var Wt={};"undefined"!==typeof Proxy?Wt=new Proxy({},{get:function(t,o){return k(t,o)?t[o]:de[o]?de[o]:Ye[o]?K(o,Ye[o]):Fe[o]?K(o,Fe[o]):Ae[o]?K(o,Ae[o]):Be[o]?Be[o]:K(o,Te(o,e[o]))},set:function(e,t,o){return e[t]=o,!0}}):(Object.keys(de).forEach((function(e){Wt[e]=de[e]})),Object.keys(Ae).forEach((function(e){Wt[e]=K(e,Ae[e])})),Object.keys(Fe).forEach((function(e){Wt[e]=K(e,Fe[e])})),Object.keys(Be).forEach((function(e){Wt[e]=Be[e]})),Object.keys(Ye).forEach((function(e){Wt[e]=K(e,Ye[e])})),Object.keys(e).forEach((function(t){(k(e,t)||k(Pe,t))&&(Wt[t]=K(t,Te(t,e[t])))}))),e.createApp=It,e.createPage=Vt,e.createComponent=Gt,e.createSubpackageApp=qt,e.createPlugin=Ht;var Jt=Wt,Xt=Jt;t.default=Xt}).call(this,o("3223")["default"],o("0ee4"))},df6a:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"待付款订单"}},dfe0:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"公告详情"}},e115:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"客服"}},e320:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"购物车",complete:"完成",edit:"管理",allElection:"全选",total:"合计",settlement:"结算",emptyTips:"暂无商品",goForStroll:"去逛逛",del:"删除",login:"去登录"}},e600:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"店铺打烊"}},e6db:function(e,t,o){var i=o("3b2d")["default"];e.exports=function(e,t){if("object"!=i(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},e78f:function(e,t,o){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={data:function(){return{showTop:!1,scrollTop:0,oldLocation:0}},methods:{scrollToTopNative:function(){e.pageScrollTo({duration:200,scrollTop:0})}},onReachBottom:function(){this.$refs.goodrecommend&&this.$refs.goodrecommend.getLikeList(10)},onPageScroll:function(e){this.oldLocation=e.scrollTop,e.scrollTop>400?this.showTop=!0:this.showTop=!1}};t.default=o}).call(this,o("df3c")["default"])},e81a:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的足迹",emptyTpis:"您还未浏览过任何商品！"}},e97b:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"积分商城"}},e987:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"账号注销"}},ed45:function(e,t){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports["default"]=e.exports},edd0:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Weixin=void 0;t.Weixin=function(){var e=o("d659");this.weixin=e,this.init=function(t){e.config({debug:!1,appId:t.appId,timestamp:t.timestamp,nonceStr:t.nonceStr,signature:t.signature,jsApiList:["chooseWXPay","openAddress","updateAppMessageShareData","updateTimelineShareData","scanQRCode","hideMenuItems"]})},this.pay=function(t,o,i){e.ready((function(){e.chooseWXPay({timestamp:t.timestamp,nonceStr:t.nonceStr,package:t.package,signType:t.signType,paySign:t.paySign,success:function(e){"function"==typeof o&&o(e)},cancel:function(e){"function"==typeof i&&i(e)}})}))},this.openAddress=function(t){e.ready((function(){e.openAddress({success:function(e){"function"==typeof t&&t(e)},fail:function(e){console.log("获取收货地址 fail",e),alert(JSON.stringify(e))}})}))},this.setShareData=function(t,o){e.ready((function(){e.updateAppMessageShareData({title:t.title||"",desc:t.desc||"",link:t.link||"",imgUrl:t.imgUrl||"",success:function(e){"function"==typeof o&&o(e)},fail:function(e){}}),e.updateTimelineShareData({title:t.title||"",link:t.link||"",imgUrl:t.imgUrl||"",success:function(e){"function"==typeof o&&o(e)}})}))},this.scanQRCode=function(t){e.ready((function(){e.scanQRCode({needResult:1,scanType:["qrCode"],success:function(e){"function"==typeof t&&t(e)}})}))},this.withdrawWechat=function(t,o){e.ready((function(){e.checkJsApi({jsApiList:["requestMerchantTransfer"],success:function(e){e.checkResult["requestMerchantTransfer"]?WeixinJSBridge.invoke("requestMerchantTransfer",{mchId:t.mch_id,appId:t.wechat_appid,package:t.package_info},(function(e){"function"==typeof o&&o(e)})):alert("你的微信版本过低，请更新至最新版本。")}})}))}}},ee10:function(e,t){function o(e,t,o,i,n,r,a){try{var s=e[r](a),c=s.value}catch(l){return void o(l)}s.done?t(c):Promise.resolve(c).then(i,n)}e.exports=function(e){return function(){var t=this,i=arguments;return new Promise((function(n,r){var a=e.apply(t,i);function s(e){o(a,n,r,s,c,"next",e)}function c(e){o(a,n,r,s,c,"throw",e)}s(void 0)}))}},e.exports.__esModule=!0,e.exports["default"]=e.exports},eee7:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"直播"}},ef61:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"申请退款"}},eff3:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"推广海报"}},eff4:function(e){e.exports=JSON.parse('{"uni-datetime-picker.selectDate":"選擇日期","uni-datetime-picker.selectTime":"選擇時間","uni-datetime-picker.selectDateTime":"選擇日期時間","uni-datetime-picker.startDate":"開始日期","uni-datetime-picker.endDate":"結束日期","uni-datetime-picker.startTime":"開始时间","uni-datetime-picker.endTime":"結束时间","uni-datetime-picker.ok":"確定","uni-datetime-picker.clear":"清除","uni-datetime-picker.cancel":"取消","uni-calender.SUN":"日","uni-calender.MON":"一","uni-calender.TUE":"二","uni-calender.WED":"三","uni-calender.THU":"四","uni-calender.FRI":"五","uni-calender.SAT":"六"}')},f09c:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"待付款订单"}},f1a7:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},f1d6:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"商品列表"}},f1ea:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},f3a1:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我要咨询"}},f40e:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},f686:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的余额",accountBalance:"账户余额 ",money:" (元)",recharge:"充值",withdrawal:"提现",balanceDetailed:"余额明细",emptyTips:"暂无余额记录",rechargeRecord:"充值记录",ableAccountBalance:"现金余额 ",noAccountBalance:"储值余额 "}},f6cc:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"积分兑换订单详情"}},f87c:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"",select:"选择",params:"参数",service:"商品服务",allGoods:"全部商品",image:"图片",video:"视频"}},f944:function(e,t,o){"use strict";(function(e){var i=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(o("2f8f")),r=i(o("af87")),a=i(o("387c")),s=o("80d8"),c={sendRequest:function(t){if(n.default.baseUrl){var o=void 0!=t.data?"POST":"GET",i=n.default.baseUrl+t.url,s={app_type:"weapp",app_type_name:"微信小程序"};s.token=a.default.state.token||"";var c=a.default.state.defaultStoreInfo;c&&(s.store_id=c.store_id);var l=a.default.state.globalStoreInfo;if(l&&(s.store_id=l.store_id),void 0!=t.data&&Object.assign(s,t.data),!1===t.async)return new Promise((function(c,l){e.request({url:i,method:o,data:s,header:t.header||{"content-type":"application/x-www-form-urlencoded;application/json"},dataType:t.dataType||"json",responseType:t.responseType||"text",success:function(e){if(-3==e.data.code&&a.default.state.siteState>0)return a.default.commit("setSiteState",-3),void r.default.redirectTo("/pages_tool/storeclose/storeclose",{},"reLaunch");e.data.refreshtoken&&a.default.commit("setToken",e.data.refreshtoken),-10009!=e.data.code&&-10010!=e.data.code||(a.default.commit("setToken",""),a.default.commit("setMemberInfo","")),c(e.data)},fail:function(t){t.errMsg&&"request:fail url not in domain list"==t.errMsg?e.showToast({title:n.default.baseUrl+"不在request 合法域名列表中",icon:"none",duration:1e4}):l(t)},complete:function(e){e.errMsg&&"request:ok"!=e.errMsg||e.statusCode&&e.statusCode>=400&&e.statusCode<500||l(e.data)}})}));e.request({url:i,method:o,data:s,header:t.header||{"content-type":"application/x-www-form-urlencoded;application/json"},dataType:t.dataType||"json",responseType:t.responseType||"text",success:function(e){if(-3==e.data.code&&a.default.state.siteState>0)return a.default.commit("setSiteState",-3),void r.default.redirectTo("/pages_tool/storeclose/storeclose",{},"reLaunch");e.data.refreshtoken&&a.default.commit("setToken",e.data.refreshtoken),-10009!=e.data.code&&-10010!=e.data.code||(a.default.commit("setToken",""),a.default.commit("setMemberInfo","")),"function"==typeof t.success&&t.success(e.data)},fail:function(o){o.errMsg&&"request:fail url not in domain list"==o.errMsg?e.showToast({title:n.default.baseUrl+"不在request 合法域名列表中",icon:"none",duration:1e4}):"function"==typeof t.fail&&t.fail(o)},complete:function(e){e.errMsg&&"request:ok"!=e.errMsg||e.statusCode&&e.statusCode>=400&&e.statusCode<500||"function"==typeof t.complete&&t.complete(e.data)}})}else e.showToast({title:"未配置请求域名",icon:"none",duration:1e4})},needMd5Fn:function(t,o){e.request({url:n.default.baseUrl+"/api/config/getApiConfig",method:"POST",header:{"content-type":"application/x-www-form-urlencoded;application/json"},data:{app_type:"weapp",app_type_name:"微信小程序"},dataType:"json",responseType:"text",success:function(e){var t=s.Utils.hexMD5("key="+e.data.data.key+"&time="+e.data.data.time);"function"==typeof o&&o(t,e.data.data.time)},fail:function(){"function"==typeof t.fail&&t.fail(res)}})},uploadBase64:function(t){this.needMd5Fn(t,(function(o,i){e.request({url:n.default.baseUrl+"/api/upload/headimgBase64",method:"POST",header:{"content-type":"application/x-www-form-urlencoded;application/json"},data:{app_type:"weapp",app_type_name:"微信小程序",images:t.base64,token:a.default.state.token||"",sign:o,time:i},dataType:"json",responseType:"text",success:function(e){"function"==typeof t.success&&t.success(e.data)},fail:function(){"function"==typeof t.fail&&t.fail(res)}})}))},pullImage:function(t){this.needMd5Fn(t,(function(o,i){e.request({url:n.default.baseUrl+"/api/upload/headimgPull",method:"POST",header:{"content-type":"application/x-www-form-urlencoded;application/json"},data:{app_type:"weapp",app_type_name:"微信小程序",path:t.path,token:a.default.state.token||"",sign:o,time:i},dataType:"json",responseType:"text",success:function(e){"function"==typeof t.success&&t.success(e.data)},fail:function(){"function"==typeof t.fail&&t.fail(res)}})}))},upload:function(t){e.uploadFile({url:n.default.baseUrl+t.url,filePath:t.filePath,name:t.name||"file",fileType:t.fileType||"image",formData:{app_type:"weapp",app_type_name:"微信小程序",token:a.default.state.token||""},header:{"content-type":"application/x-www-form-urlencoded;application/json"},success:function(e){"function"==typeof t.success&&t.success(JSON.parse(e.data))},fail:function(e){"function"==typeof t.fail&&t.fail(e)}})}};t.default=c}).call(this,o("df3c")["default"])},fa77:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"我的关注"}},fb42:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:""}},fc8d:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"店铺笔记"}},fe8d:function(e,t){e.exports={error:"",check:function(e,t){for(var o=0;o<t.length;o++){if(!t[o].checkType)return!0;if(!t[o].name)return!0;if(!t[o].errorMsg)return!0;if(!e[t[o].name])return this.error=t[o].errorMsg,!1;switch(t[o].checkType){case"custom":if("function"==typeof t[o].validate&&!t[o].validate(e[t[o].name]))return this.error=t[o].errorMsg,!1;break;case"required":var i=new RegExp("/[S]+/");if(i.test(e[t[o].name]))return this.error=t[o].errorMsg,!1;break;case"string":i=new RegExp("^.{"+t[o].checkRule+"}$");if(!i.test(e[t[o].name]))return this.error=t[o].errorMsg,!1;break;case"int":i=new RegExp("^(-[1-9]|[1-9])[0-9]{"+t[o].checkRule+"}$");if(!i.test(e[t[o].name]))return this.error=t[o].errorMsg,!1;break;case"between":if(!this.isNumber(e[t[o].name]))return this.error=t[o].errorMsg,!1;var n=t[o].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[o].name]>n[1]||e[t[o].name]<n[0])return this.error=t[o].errorMsg,!1;break;case"betweenD":i=/^-?[1-9][0-9]?$/;if(!i.test(e[t[o].name]))return this.error=t[o].errorMsg,!1;n=t[o].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[o].name]>n[1]||e[t[o].name]<n[0])return this.error=t[o].errorMsg,!1;break;case"betweenF":i=/^-?[0-9][0-9]?.+[0-9]+$/;if(!i.test(e[t[o].name]))return this.error=t[o].errorMsg,!1;n=t[o].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[o].name]>n[1]||e[t[o].name]<n[0])return this.error=t[o].errorMsg,!1;break;case"same":if(e[t[o].name]!=t[o].checkRule)return this.error=t[o].errorMsg,!1;break;case"notsame":if(e[t[o].name]==t[o].checkRule)return this.error=t[o].errorMsg,!1;break;case"email":i=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!i.test(e[t[o].name]))return this.error=t[o].errorMsg,!1;break;case"phoneno":i=/^\d{11}$/;if(!i.test(e[t[o].name]))return this.error=t[o].errorMsg,!1;break;case"zipcode":i=/^[0-9]{6}$/;if(!i.test(e[t[o].name]))return this.error=t[o].errorMsg,!1;break;case"reg":i=new RegExp(t[o].checkRule);if(!i.test(e[t[o].name]))return this.error=t[o].errorMsg,!1;break;case"in":if(-1==t[o].checkRule.indexOf(e[t[o].name]))return this.error=t[o].errorMsg,!1;break;case"notnull":if(0==e[t[o].name]||void 0==e[t[o].name]||null==e[t[o].name]||e[t[o].name].length<1)return this.error=t[o].errorMsg,!1;break;case"lengthMin":if(e[t[o].name].length<t[o].checkRule)return this.error=t[o].errorMsg,!1;break;case"lengthMax":if(e[t[o].name].length>t[o].checkRule)return this.error=t[o].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},ffa6:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lang=void 0;t.lang={title:"待付款订单"}}}]);