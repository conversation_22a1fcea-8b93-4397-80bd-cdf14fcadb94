(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/main"],{"035e":function(e,t,o){"use strict";o.r(t);var n=o("b27a"),i=o.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"6d1e":function(e,t,o){},b27a:function(e,t,o){"use strict";(function(e){var n=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(o("0713")),r=n(o("1757")),a=o("edd0"),c={mixins:[i.default],onLaunch:function(t){e.hideTabBar();var o=e.getUpdateManager();o.onCheckForUpdate((function(e){})),o.onUpdateReady((function(t){e.showModal({title:"更新提示",content:"新版本已经准备好，是否重启应用？",success:function(e){e.confirm&&o.applyUpdate()}})})),o.onUpdateFailed((function(e){})),e.onNetworkStatusChange((function(t){t.isConnected||e.showModal({title:"网络失去链接",content:"请检查网络链接",showCancel:!1})})),this.$store.dispatch("init"),e.getStorageSync("themeStyle")&&this.$store.commit("setThemeStyle",r.default[e.getStorageSync("themeStyle")]),e.getStorageSync("addonIsExist")&&this.$store.commit("setAddonIsExist",e.getStorageSync("addonIsExist")),e.getStorageSync("defaultImg")&&this.$store.commit("setDefaultImg",e.getStorageSync("defaultImg")),e.getStorageSync("siteInfo")&&this.$store.commit("setSiteInfo",e.getStorageSync("siteInfo")),e.getStorageSync("globalStoreConfig")&&this.$store.commit("setGlobalStoreConfig",e.getStorageSync("globalStoreConfig")),e.getStorageSync("globalStoreInfo")&&this.$store.commit("setGlobalStoreInfo",e.getStorageSync("globalStoreInfo")),e.getStorageSync("defaultStoreInfo")&&this.$store.commit("setDefaultStoreInfo",e.getStorageSync("defaultStoreInfo")),e.getStorageSync("servicerConfig")&&this.$store.commit("setServicerConfig",e.getStorageSync("servicerConfig")),e.getStorageSync("copyright")&&this.$store.commit("setCopyright",e.getStorageSync("copyright")),e.getStorageSync("mapConfig")&&this.$store.commit("setMapConfig",e.getStorageSync("mapConfig")),e.getStorageSync("token")&&this.$store.commit("setToken",e.getStorageSync("token")),e.getStorageSync("memberInfo")&&(this.$store.commit("setMemberInfo",e.getStorageSync("memberInfo")),this.$store.dispatch("getCartNumber"))},onShow:function(e){var t=this;this.$store.state.token?this.$api.sendRequest({url:"/api/member/info",success:function(e){e.code>=0&&t.$store.commit("setMemberInfo",e.data)}}):this.getAuthInfo()},onHide:function(){},methods:{getAuthInfo:function(){var e=this;this.getCode((function(t){e.authLogin(t,"authOnlyLogin")}))},authLogin:function(t){var o=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"authLogin";e.getStorageSync("source_member")&&(t.source_member=e.getStorageSync("source_member")),e.setStorageSync("authInfo",t),this.$api.sendRequest({url:"authLogin"==n?"/api/login/auth":"/api/login/authonlylogin",data:t,success:function(e){e.code>=0&&(o.$store.commit("setToken",e.data.token),o.getMemberInfo(),o.$store.dispatch("getCartNumber"))}})},shareConfig:function(){var e=this;this.$api.sendRequest({url:"/wechat/api/wechat/share",data:{url:window.location.href},success:function(t){if(0==t.code){var o=new a.Weixin;o.init(t.data.jssdk_config);var n=JSON.parse(JSON.stringify(t.data.share_config.data));n&&o.setShareData({title:n.title,desc:n.desc,link:n.link,imgUrl:e.$util.img(n.imgUrl)},(function(e){console.log(e)}));var i=t.data.share_config.permission.hideOptionMenu;t.data.share_config.permission.hideMenuItems;i?o.weixin.hideOptionMenu():o.weixin.showOptionMenu()}},fail:function(e){}})},getMemberInfo:function(){var e=this;this.$api.sendRequest({url:"/api/member/info",success:function(t){t.code>=0&&e.$store.commit("setMemberInfo",t.data)}})}},watch:{$route:{handler:function(e,t){this.$util.isWeiXin()&&this.shareConfig()},immediate:!0}}};t.default=c}).call(this,o("df3c")["default"])},c5bf:function(e,t,o){"use strict";o.r(t);var n=o("035e");for(var i in n)["default"].indexOf(i)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(i);o("faf8");var r=o("828b"),a=Object(r["a"])(n["default"],void 0,void 0,!1,null,null,null,!1,void 0,void 0);t["default"]=a.exports},d5ad:function(e,t,o){"use strict";(function(e,t){var n=o("47a9"),i=n(o("7ca3"));o("d381");var r=n(o("3240")),a=n(o("c5bf")),c=n(o("387c")),s=n(o("af87")),u=n(o("f944")),l=n(o("1e68")),f=n(o("2f8f")),d=n(o("d1d6"));function g(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function m(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?g(Object(o),!0).forEach((function(t){(0,i.default)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):g(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}e.__webpack_require_UNI_MP_PLUGIN__=o,r.default.prototype.$store=c.default,r.default.config.productionTip=!1,r.default.prototype.$util=s.default,r.default.prototype.$api=u.default,r.default.prototype.$langConfig=l.default,r.default.prototype.$lang=l.default.lang,r.default.prototype.$config=f.default,r.default.mixin(d.default),a.default.mpType="app",s.default.rewriteUniStorageMethod();r.default.component("loading-cover",(function(){o.e("components/loading-cover/loading-cover").then(function(){return resolve(o("c003"))}.bind(null,o)).catch(o.oe)}));r.default.component("ns-mp-html",(function(){o.e("components/ns-mp-html/ns-mp-html").then(function(){return resolve(o("d108"))}.bind(null,o)).catch(o.oe)}));r.default.component("ns-empty",(function(){o.e("components/ns-empty/ns-empty").then(function(){return resolve(o("52a6"))}.bind(null,o)).catch(o.oe)}));r.default.component("mescroll-uni",(function(){o.e("components/mescroll/my-list-mescroll").then(function(){return resolve(o("5ce0"))}.bind(null,o)).catch(o.oe)}));r.default.component("mescroll-body",(function(){Promise.all([o.e("common/vendor"),o.e("components/mescroll/mescroll-body")]).then(function(){return resolve(o("20a8"))}.bind(null,o)).catch(o.oe)}));r.default.component("ns-login",(function(){Promise.all([o.e("common/vendor"),o.e("components/ns-login/ns-login")]).then(function(){return resolve(o("2910"))}.bind(null,o)).catch(o.oe)}));r.default.component("privacy-popup",(function(){o.e("components/wx-privacy-popup/privacy-popup").then(function(){return resolve(o("633d"))}.bind(null,o)).catch(o.oe)}));var p=new r.default(m(m({},a.default),{},{store:c.default}));t(p).$mount()}).call(this,o("3223")["default"],o("df3c")["createApp"])},faf8:function(e,t,o){"use strict";var n=o("6d1e"),i=o.n(n);i.a}},[["d5ad","common/runtime","common/vendor"]]]);