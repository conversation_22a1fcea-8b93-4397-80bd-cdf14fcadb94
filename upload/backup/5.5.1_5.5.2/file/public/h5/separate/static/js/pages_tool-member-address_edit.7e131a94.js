(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-address_edit"],{4005:function(a,t,e){"use strict";e("6a54");var i=e("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("5c47"),e("af8f"),e("0c26"),e("aa9c");var s=i(e("04c1")),d=i(e("fe8d")),r=i(e("2f8f")),n={components:{pickRegions:s.default},data:function(){return{formData:{id:0,name:"",mobile:"",telephone:"",province_id:"",city_id:"",district_id:"",community_id:"",address:"",full_address:"",house:"",latitude:0,longitude:0,is_default:1},pasteAddress:"",address:"",addressValue:"",back:"",redirect:"redirectTo",flag:!1,defaultRegions:[],localType:1,isEdit:!1,webSign:!1,isOpenIdentify:!1}},onLoad:function(a){if(a.back&&(this.back=a.back),a.redirect&&(this.redirect=a.redirect),a.type&&(this.localType=a.type),a.id&&!a.name)this.formData.id=a.id,this.getAddressDetail();else if(a.name){this.isEdit=!0,this.webSign=!0,uni.getStorageSync("addressInfo")&&(this.formData=uni.getStorageSync("addressInfo")),this.formData.address=a.name,this.localType=2,this.getAddress(a.latng);var t=this.getQueryVariable("latng").split(",");this.formData.latitude=t[0],this.formData.longitude=t[1],this.formData.house=""}else this.$refs.loadingCover&&this.$refs.loadingCover.hide();this.getMapConfig()},onBackPress:function(){uni.setStorageSync("addressInfo","")},onShow:function(){this.formData.id?uni.setNavigationBarTitle({title:"编辑收货地址"}):uni.setNavigationBarTitle({title:"新增收货地址"})},onReady:function(){this.$refs.loadingCover.hide()},onHide:function(){this.flag=!1},methods:{getMapConfig:function(){var a=this;this.$api.sendRequest({url:"/api/config/geMapConfig",success:function(t){t.data.key?a.isOpenIdentify=!0:a.isOpenIdentify=!1},fail:function(a){}})},identifyAddr:function(){var a=this;this.pasteAddress?this.$api.sendRequest({url:"/api/address/analysesAddress",data:{address:this.pasteAddress},success:function(t){if(t.code>=0){t.data.name&&(a.formData.name=t.data.name),t.data.mobile&&(a.formData.mobile=t.data.mobile),(t.data.province_name||t.data.city_name||t.data.district_name)&&(a.formData.full_address="",a.formData.full_address+=t.data.province_name?t.data.province_name:"",a.formData.full_address+=t.data.city_name?"-"+t.data.city_name:"",a.formData.full_address+=t.data.district_name?"-"+t.data.district_name:"");var e=a.addressValue.split("-");t.data.province_id!=e[0]||t.data.city_id!=e[1]||t.data.district_id!=e[2]?(a.addressValue=t.data.province_id+"-"+t.data.city_id+"-"+t.data.district_id,a.formData.address=t.data.detail):t.data.detail&&(a.formData.address=t.data.detail),a.formData.latitude=t.data.lat||"",a.formData.longitude=t.data.lng||""}else a.$util.showToast({title:t.message})},fail:function(t){a.$util.showToast({title:t.message})}}):this.$util.showToast({title:"请粘贴或输入文本信息"})},getAddressDetail:function(){var a=this;this.$api.sendRequest({url:"/api/memberaddress/info",data:{id:this.formData.id},success:function(t){var e=t.data;null!=e&&(a.formData.name=e.name,a.formData.mobile=e.mobile,a.formData.telephone=e.telephone,a.formData.address=e.address,a.formData.full_address=e.full_address,a.formData.latitude=e.latitude,a.formData.longitude=e.longitude,a.formData.is_default=e.is_default,a.localType=e.type,a.defaultRegions=[e.province_id,e.city_id,e.district_id],a.addressValue+=void 0!=e.province_id?e.province_id:"",a.addressValue+=void 0!=e.city_id?"-"+e.city_id:"",a.addressValue+=void 0!=e.district_id?"-"+e.district_id:""),a.$refs.loadingCover&&a.$refs.loadingCover.hide()},fail:function(t){a.$refs.loadingCover&&a.$refs.loadingCover.hide()}})},getAddress:function(a){var t=this;this.$api.sendRequest({url:"/api/memberaddress/tranAddressInfo",data:{latlng:a},success:function(a){0==a.code?(t.formData.full_address="",t.formData.full_address+=void 0!=a.data.province?a.data.province:"",t.formData.full_address+=void 0!=a.data.city?"-"+a.data.city:"",t.formData.full_address+=void 0!=a.data.district?"-"+a.data.district:"",t.addressValue="",t.addressValue+=void 0!=a.data.province_id?a.data.province_id:"",t.addressValue+=void 0!=a.data.city_id?"-"+a.data.city_id:"",t.addressValue+=void 0!=a.data.district_id?"-"+a.data.district_id:""):t.showToast({title:"数据有误"})}})},handleGetRegions:function(a){this.formData.full_address="",this.formData.full_address+=void 0!=a[0]?a[0].label:"",this.formData.full_address+=void 0!=a[1]?"-"+a[1].label:"",this.formData.full_address+=void 0!=a[2]?"-"+a[2].label:"",this.addressValue="",this.addressValue+=void 0!=a[0]?a[0].value:"",this.addressValue+=void 0!=a[1]?"-"+a[1].value:"",this.addressValue+=void 0!=a[2]?"-"+a[2].value:""},selectAddress:function(){var a=this.formData;uni.setStorageSync("addressInfo",a);var t=r.default.h5Domain+"/pages_tool/member/address_edit?type="+this.localType;this.formData.id&&(t+="&id="+this.formData.id),this.back&&(t+="&back="+this.back),window.location.href="https://apis.map.qq.com/tools/locpicker?search=1&type=0&backurl="+encodeURIComponent(t)+"&key="+r.default.mpKey+"&referer=myapp"},getQueryVariable:function(a){for(var t=window.location.search.substring(1),e=t.split("&"),i=0;i<e.length;i++){var s=e[i].split("=");if(s[0]==a)return s[1]}return!1},vertify:function(){this.formData.name=this.formData.name.trim(),this.formData.mobile=this.formData.mobile.trim(),this.formData.address=this.formData.address.trim();var a=[{name:"name",checkType:"required",errorMsg:"请输入姓名"},{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"},{name:"full_address",checkType:"required",errorMsg:"请选择省市区县"},{name:"address",checkType:"required",errorMsg:"详细地址不能为空"}];this.isEdit&&a.push({name:"house",checkType:"required",errorMsg:"门牌不能为空"});var t=d.default.check(this.formData,a);if(t){var e=this.addressValue.split("-");return e[0]?e[1]?!!e[2]||(this.$util.showToast({title:"请选择区"}),this.flag=!1,!1):(this.$util.showToast({title:"请选择市"}),this.flag=!1,!1):(this.$util.showToast({title:"请选择省"}),this.flag=!1,!1)}return this.$util.showToast({title:d.default.error}),this.flag=!1,!1},saveAddress:function(){var a=this;if(!this.flag&&(this.flag=!0,this.vertify())){var t=this.addressValue.split("-"),e={},i="";e={name:this.formData.name,mobile:this.formData.mobile,telephone:this.formData.telephone,province_id:t[0],city_id:t[1],district_id:t[2]?t[2]:"",community_id:0,address:this.isEdit?this.formData.address+this.formData.house:this.formData.address,full_address:this.formData.full_address,latitude:1==this.localType?"":this.formData.latitude,longitude:1==this.localType?"":this.formData.longitude,is_default:this.formData.is_default,type:this.localType},i="add",this.formData.id&&(i="edit",e.id=this.formData.id,""!=this.back&&(e.is_default=1)),this.$api.sendRequest({url:"/api/memberaddress/"+i,data:e,success:function(t){if(a.flag=!1,0==t.code){if(""!=a.back){if(console.log(a.webSign),a.webSign)return void window.history.go(-3);uni.navigateBack({delta:2})}else a.$util.showToast({title:t.message}),uni.navigateBack({delta:1});uni.removeStorageSync("addressInfo")}else a.$util.showToast({title:t.message})},fail:function(t){a.flag=!1}})}}}};t.default=n},"466ab":function(a,t,e){"use strict";e.r(t);var i=e("7f0c"),s=e("a921");for(var d in s)["default"].indexOf(d)<0&&function(a){e.d(t,a,(function(){return s[a]}))}(d);e("8cf6");var r=e("828b"),n=Object(r["a"])(s["default"],i["b"],i["c"],!1,null,"b103eac8",null,!1,i["a"],void 0);t["default"]=n.exports},"6ceb":function(a,t,e){var i=e("8765");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[a.i,i,""]]),i.locals&&(a.exports=i.locals);var s=e("967d").default;s("a19612c2",i,!0,{sourceMap:!1,shadowMode:!1})},"7f0c":function(a,t,e){"use strict";e.d(t,"b",(function(){return s})),e.d(t,"c",(function(){return d})),e.d(t,"a",(function(){return i}));var i={pageMeta:e("7854").default,pickRegions:e("04c1").default,loadingCover:e("c003").default},s=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":a.themeColor}}),e("v-uni-view",{staticClass:"address-edit-content"},[e("v-uni-view",{staticClass:"edit-wrap"},[e("v-uni-view",{staticClass:"tip"},[a._v("地址信息")]),e("v-uni-view",{staticClass:"edit-item"},[e("v-uni-text",{staticClass:"tit"},[a._v(a._s(a.$lang("consignee"))),e("v-uni-text",[a._v("*")])],1),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"text","placeholder-class":"placeholder-class",placeholder:a.$lang("consigneePlaceholder"),maxlength:"30",name:"name"},model:{value:a.formData.name,callback:function(t){a.$set(a.formData,"name",t)},expression:"formData.name"}})],1),e("v-uni-view",{staticClass:"edit-item"},[e("v-uni-text",{staticClass:"tit"},[a._v(a._s(a.$lang("mobile"))),e("v-uni-text",[a._v("*")])],1),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"number","placeholder-class":"placeholder-class",placeholder:a.$lang("mobilePlaceholder"),maxlength:"11"},model:{value:a.formData.mobile,callback:function(t){a.$set(a.formData,"mobile",t)},expression:"formData.mobile"}})],1),e("v-uni-view",{staticClass:"edit-item"},[e("v-uni-text",{staticClass:"tit"},[a._v(a._s(a.$lang("telephone")))]),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"text","placeholder-class":"placeholder-class",placeholder:a.$lang("telephonePlaceholder"),maxlength:"20"},model:{value:a.formData.telephone,callback:function(t){a.$set(a.formData,"telephone",t)},expression:"formData.telephone"}})],1),2==a.localType?[e("v-uni-view",{staticClass:"edit-item"},[e("v-uni-text",{staticClass:"tit"},[a._v(a._s(a.$lang("receivingCity"))),e("v-uni-text",[a._v("*")])],1),e("v-uni-view",{staticClass:"text_inp",class:{empty:!a.formData.full_address,"color-tip":!a.formData.full_address},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.selectAddress.apply(void 0,arguments)}}},[a._v(a._s(a.formData.full_address?a.formData.full_address:"请选择省市区县"))]),e("v-uni-text",{staticClass:"padding-left iconfont icon-location",on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.selectAddress.apply(void 0,arguments)}}})],1),e("v-uni-view",{staticClass:"edit-item"},[e("v-uni-text",{staticClass:"tit"},[a._v(a._s(a.$lang("address"))),e("v-uni-text",[a._v("*")])],1),e("v-uni-text",{staticClass:"select-address",class:{empty:!a.formData.address,"color-tip":!a.formData.address},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.selectAddress.apply(void 0,arguments)}}},[a._v(a._s(a.formData.address?a.formData.address:a.$lang("addressPlaceholder")))])],1),a.isEdit?e("v-uni-view",{staticClass:"edit-item"},[e("v-uni-text",{staticClass:"tit"},[a._v(a._s(a.$lang("house"))),e("v-uni-text",[a._v("*")])],1),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"text","placeholder-class":"placeholder-class",placeholder:a.$lang("housePlaceholder"),maxlength:"50"},model:{value:a.formData.house,callback:function(t){a.$set(a.formData,"house",t)},expression:"formData.house"}})],1):a._e()]:[e("v-uni-view",{staticClass:"edit-item"},[e("v-uni-text",{staticClass:"tit"},[a._v(a._s(a.$lang("receivingCity"))),e("v-uni-text",[a._v("*")])],1),e("pick-regions",{attrs:{"default-regions":a.defaultRegions},on:{getRegions:function(t){arguments[0]=t=a.$handleEvent(t),a.handleGetRegions.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"select-address ",class:{empty:!a.formData.full_address,"color-tip":!a.formData.full_address}},[a._v(a._s(a.formData.full_address?a.formData.full_address:"请选择省市区县"))])],1)],1),e("v-uni-view",{staticClass:"edit-item"},[e("v-uni-text",{staticClass:"tit"},[a._v(a._s(a.$lang("address"))),e("v-uni-text",[a._v("*")])],1),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"text","placeholder-class":"placeholder-class",placeholder:a.$lang("addressPlaceholder"),maxlength:"50"},model:{value:a.formData.address,callback:function(t){a.$set(a.formData,"address",t)},expression:"formData.address"}})],1)]],2),a.isOpenIdentify?e("v-uni-view",{staticClass:"identify-area"},[e("v-uni-view",{staticClass:"tip"},[a._v("智能识别地址")]),e("v-uni-view",{staticClass:"paste-address"},[e("v-uni-view",{staticClass:"sample-area"},[a._v("示例：小红152********山西省太原市小店区**路**号")]),e("v-uni-view",{staticClass:"intelligent-identify"},[e("v-uni-textarea",{staticClass:"input-addr",attrs:{placeholder:"粘贴或输入文本，智能识别姓名、电话和地址",name:"",id:"",cols:"30",rows:"10"},model:{value:a.pasteAddress,callback:function(t){a.pasteAddress=t},expression:"pasteAddress"}}),e("v-uni-view",{staticClass:"action-area"},[e("v-uni-view",{staticClass:"identify-btn color-base-bg",on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.identifyAddr()}}},[a._v("识别")])],1)],1)],1)],1):a._e(),e("v-uni-view",{staticClass:"btn"},[e("v-uni-button",{staticClass:"add",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=a.$handleEvent(t),a.saveAddress.apply(void 0,arguments)}}},[a._v(a._s(a.$lang("save")))])],1),e("loading-cover",{ref:"loadingCover"})],1)],1)},d=[]},8765:function(a,t,e){var i=e("c86c");t=i(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-b103eac8] pick-regions,\r\n.pick-regions[data-v-b103eac8]{flex:1}.identify-area .tip[data-v-b103eac8]{padding:%?20?% %?30?% %?10?%;background-color:#f8f8f8;color:#909399}.identify-area .paste-address[data-v-b103eac8]{margin:0 %?30?%}.identify-area .paste-address .sample-area[data-v-b103eac8]{background-color:#f8ecc5;color:#7d5329;font-size:%?24?%;line-height:%?32?%;padding:%?16?% %?30?% %?60?%;border-radius:%?30?% %?30?% 0 0}.identify-area .paste-address .intelligent-identify[data-v-b103eac8]{margin-top:%?-44?%;background-color:#fff;border-radius:%?30?%;padding:%?30?%}.identify-area .paste-address .intelligent-identify .action-area[data-v-b103eac8]{display:flex;align-items:center;justify-content:flex-end}.identify-area .paste-address .intelligent-identify .action-area .identify-btn[data-v-b103eac8]{color:#fff;font-size:%?26?%;line-height:%?30?%;border-radius:%?30?%;padding:%?12?% %?20?%}.identify-area .paste-address .intelligent-identify .input-addr[data-v-b103eac8]{width:100%;height:%?200?%;color:#333;font-size:%?24?%}.edit-wrap[data-v-b103eac8]{background:#fff;overflow:hidden}.edit-wrap .tip[data-v-b103eac8]{padding:%?20?% %?30?% %?10?%;background-color:#f8f8f8;color:#909399}.edit-item[data-v-b103eac8]{display:flex;align-items:center;margin:0 %?30?%;min-height:%?100?%;background-color:#fff}.edit-item .text_inp[data-v-b103eac8]{margin-left:%?20?%;flex:1}.edit-item .tit[data-v-b103eac8]{width:%?148?%}.edit-item .tit uni-text[data-v-b103eac8]{margin-left:%?10?%;color:#ff4544}.edit-item .tit.margin_tit[data-v-b103eac8]{align-self:flex-start;margin-top:%?24?%}.edit-item .icon-location[data-v-b103eac8]{color:#606266;align-self:flex-start;margin-top:%?20?%}.edit-item .select-address[data-v-b103eac8]{display:block;margin-left:%?10?%}.edit-item .select-address.empty[data-v-b103eac8]{color:grey}.edit-item uni-textarea[data-v-b103eac8],\r\n.edit-item uni-input[data-v-b103eac8]{flex:1;font-size:%?28?%;margin-left:%?20?%;padding:0}.edit-item uni-textarea[data-v-b103eac8]{margin-top:%?6?%;height:%?100?%;padding-bottom:%?20?%;padding-top:%?20?%;line-height:%?50?%}.edit-wrap > .edit-item + .edit-item[data-v-b103eac8]{border-top:%?2?% solid #ebedf0}.add[data-v-b103eac8]{margin-top:%?60?%;height:%?80?%;line-height:%?80?%!important;border-radius:%?80?%;font-weight:500;width:calc(100% - %?60?%);margin-left:%?30?%;font-size:%?32?%}.btn[data-v-b103eac8]{position:fixed;width:100%;bottom:%?30?%;height:auto;padding-bottom:constant(safe-area-inset-bottom);\r\n  /*兼容 IOS<11.2*/padding-bottom:env(safe-area-inset-bottom)\r\n  /*兼容 IOS>11.2*/}',""]),a.exports=t},"8cf6":function(a,t,e){"use strict";var i=e("6ceb"),s=e.n(i);s.a},a921:function(a,t,e){"use strict";e.r(t);var i=e("4005"),s=e.n(i);for(var d in i)["default"].indexOf(d)<0&&function(a){e.d(t,a,(function(){return i[a]}))}(d);t["default"]=s.a}}]);