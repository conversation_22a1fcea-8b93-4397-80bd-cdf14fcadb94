(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-presale-order_list"],{"1b76":function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,"[data-v-26121a98] .uni-page{overflow:hidden}[data-v-26121a98] .mescroll-upwarp{padding-bottom:%?100?%}",""]),t.exports=e},"27ab":function(t,e,o){var a=o("ceaa");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("488081ba",a,!0,{sourceMap:!1,shadowMode:!1})},"4dc7":function(t,e,o){var a=o("1b76");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("7d959202",a,!0,{sourceMap:!1,shadowMode:!1})},"6b55":function(t,e,o){"use strict";o("6a54");var a=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("e838"),o("c223");var i=a(o("870a")),r={data:function(){return{scrollInto:"",orderStatus:"",statusList:[],orderList:[],contentText:{},isIphoneX:!1,orderData:null,payMoney:0,payType:"",timestamp:0,memberBalance:0,isBalance:0,isSub:!1}},mixins:[i.default],onLoad:function(t){t.status&&(this.orderStatus=t.status)},computed:{balanceDeduct:function(){if(this.orderData&&1==this.orderData.order_status&&""==this.orderData.final_out_trade_no&&this.memberBalance>0){var t=this.orderData.order_money-this.orderData.presale_deposit_money;return(this.memberBalance>t?t:this.memberBalance).toFixed(2)}return 0}},onShow:function(){var t=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getOrderStatus(),this.storeToken?this.getMemberBalance():this.$nextTick((function(){t.$refs.login.open("/pages_promotion/presale/order_list")}))},methods:{getMemberBalance:function(){var t=this;this.$api.sendRequest({url:"/api/memberaccount/info",data:{account_type:"balance,balance_money"},success:function(e){e.data&&(t.memberBalance=parseFloat(e.data.balance)+parseFloat(e.data.balance_money))}})},ontabtap:function(t){var e=t.target.dataset.current||t.currentTarget.dataset.current;this.orderStatus=this.statusList[e].status,this.$refs.loadingCover.show(),this.$refs.mescroll.refresh()},getListData:function(t){var e=this;this.$api.sendRequest({url:"/presale/api/order/page",data:{page:t.num,page_size:t.size,order_status:this.orderStatus},success:function(o){e.timestamp=o.timestamp;var a=[],i=o.message;0==o.code&&o.data?a=o.data.list:e.$util.showToast({title:i}),t.endSuccess(a.length),1==t.num&&(e.orderList=[]),e.orderList=e.orderList.concat(a),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(o){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},getOrderStatus:function(){this.statusList=[{status:"",name:"全部",id:"status_0"},{status:0,name:"待付款",id:"status_0"},{status:1,name:"待付尾款",id:"status_1"},{status:2,name:"已完成",id:"status_2"},{status:-1,name:"已关闭",id:"status_3"}]},operation:function(t,e){var o=this;switch(t){case"deleteOrder":this.deleteOrder(e.id,(function(){o.$refs.mescroll.refresh()}));break;case"orderClose":this.orderClose(e.id,(function(){o.$refs.mescroll.refresh()}));break;case"orderPayDeposit":this.orderData=e,this.openPaymentPopup(e,"presale_deposit_money");break;case"refundDeposit":this.refundDeposit(e.id,(function(){o.$refs.mescroll.refresh()}));break;case"orderPayFinal":this.orderData=e,this.openPaymentPopup(e,"final_money");break}},orderDetail:function(t){this.$util.redirectTo("/pages_promotion/presale/order_detail",{order_id:t.id})},imageError:function(t){this.orderList[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},pay:function(){var t=this;this.isSub||(this.isSub=!0,"final_money"==this.payType?this.orderPayFinal(this.orderData,(function(){t.$refs.mescroll.refresh()})):"presale_deposit_money"==this.payType&&this.orderPayDeposit(this.orderData,(function(){t.$refs.mescroll.refresh()})))},useBalance:function(){this.isBalance?(this.isBalance=0,this.payMoney+=parseFloat(this.balanceDeduct)):(this.isBalance=1,this.payMoney-=parseFloat(this.balanceDeduct))}},watch:{storeToken:function(t,e){t&&(this.getMemberBalance(),this.$refs.mescroll.refresh())}}};e.default=r},7854:function(t,e,o){"use strict";o.r(e);var a=o("8ba8"),i=o("f48d");for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);var s=o("828b"),n=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=n.exports},"78a7":function(t,e,o){"use strict";o.r(e);var a=o("6b55"),i=o.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"870a":function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={methods:{orderClose:function(t,e){var o=this;uni.showModal({title:"提示",content:"您确定要关闭该订单吗？",success:function(a){a.confirm&&o.$api.sendRequest({url:"/presale/api/order/close",data:{order_id:t},success:function(t){0==t.code?"function"==typeof e&&e():o.$util.showToast({title:t.message,duration:2e3})}})}})},deleteOrder:function(t,e){var o=this;uni.showModal({title:"提示",content:"您确定要删除该订单吗？",success:function(a){a.confirm&&o.$api.sendRequest({url:"/presale/api/order/delete",data:{order_id:t},success:function(t){0==t.code?"function"==typeof e&&e():o.$util.showToast({title:t.message,duration:2e3})}})}})},refundDeposit:function(t,e){var o=this;uni.showModal({title:"提示",content:"您确定要退定金吗？",success:function(a){a.confirm&&o.$api.sendRequest({url:"/presale/api/refund/applyRefund",data:{order_id:t},success:function(t){0==t.code?"function"==typeof e&&e():o.$util.showToast({title:t.message,duration:2e3})}})}})},orderPayFinal:function(t,e){var o=this,a="/presale/api/order/pay",i={id:t.id};""==t.final_out_trade_no&&(a="/presale/api/ordercreate/finalCreate",i.is_balance=this.isBalance),this.$api.sendRequest({url:a,data:i,success:function(t){o.isSub=!1,t.code>=0?0==o.payMoney?o.$util.redirectTo("/pages_tool/pay/result",{code:t.data},"redirectTo"):o.$refs.choosePaymentPopup.getPayInfo(t.data):o.$util.showToast({title:t.message}),o.isBalance=0},fail:function(t){o.isSub=!1}})},orderPayDeposit:function(t,e){var o=this;this.$api.sendRequest({url:"/presale/api/order/pay",data:{id:t.id},success:function(t){o.isSub=!1,t.code>=0?o.$refs.choosePaymentPopup.getPayInfo(t.data):o.$util.showToast({title:t.message})},fail:function(t){o.isSub=!1}})},openPaymentPopup:function(t,e){this.payType=e,"final_money"==e?""==t.final_out_trade_no&&(this.payMoney=t.order_money-t.presale_deposit_money):"presale_deposit_money"==e&&(this.payMoney=t.pay_deposit_money),uni.setStorageSync("paySource","presale"),this.$refs.choosePaymentPopup.open()}}};e.default=a},"8ba8":function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"c",(function(){return i})),o.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},i=[]},"8e59":function(t,e,o){"use strict";var a=o("4dc7"),i=o.n(a);i.a},cc1b:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("5ef2"),o("64aa"),o("5c47"),o("a1c1"),o("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},i={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var o=function o(i){i.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",o),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",o)}})}}}};e.default=i},ceaa:function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.order-container[data-v-26121a98]{width:100vw;height:100vh}.align-right[data-v-26121a98]{text-align:right}.order-nav[data-v-26121a98]{width:100vw;height:%?90?%;flex-direction:row;white-space:nowrap;background:#fff;display:flex;position:fixed;left:0;z-index:998;justify-content:space-around}.order-nav .uni-tab-item[data-v-26121a98]{width:%?130?%;text-align:center}.order-nav .uni-tab-item-title[data-v-26121a98]{display:inline-block;height:%?86?%;line-height:%?90?%;border-bottom:1px solid #fff;flex-wrap:nowrap;white-space:nowrap;text-align:center;font-size:%?30?%}.order-nav .uni-tab-item-title-active[data-v-26121a98]{height:%?86?%;border-bottom:2px solid #fff}.order-nav[data-v-26121a98] ::-webkit-scrollbar{width:0;height:0;color:transparent}.order-item[data-v-26121a98]{margin:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative}.order-item .order-header[data-v-26121a98]{display:flex;align-items:center;position:relative;padding:%?20?% %?30?% %?26?% %?30?%}.order-item .order-header.waitpay[data-v-26121a98]{padding-left:%?70?%}.order-item .order-header.waitpay .icon-yuan_checked[data-v-26121a98],\r\n.order-item .order-header.waitpay .icon-yuan_checkbox[data-v-26121a98]{font-size:%?32?%;position:absolute;top:48%;left:%?20?%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.order-item .order-header.waitpay .icon-yuan_checkbox[data-v-26121a98]{color:#909399}.order-item .order-header .icon-dianpu[data-v-26121a98]{display:inline-block;line-height:1;margin-right:%?12?%;font-size:%?28?%}.order-item .order-header .status-name[data-v-26121a98]{flex:1;text-align:right;font-size:%?24?%}.order-item .order-body .goods-wrap[data-v-26121a98]{display:flex;position:relative;padding:0 %?30?% %?30?% %?30?%}.order-item .order-body .goods-wrap[data-v-26121a98]:last-of-type{margin-bottom:0}.order-item .order-body .goods-wrap .goods-img[data-v-26121a98]{width:%?160?%;height:%?160?%;margin-right:%?20?%}.order-item .order-body .goods-wrap .goods-img uni-image[data-v-26121a98]{width:100%;height:100%;border-radius:%?10?%}.order-item .order-body .goods-wrap .goods-info[data-v-26121a98]{flex:1;position:relative;max-width:calc(100% - %?180?%);display:flex;flex-direction:column}.order-item .order-body .goods-wrap .goods-info .pro-info[data-v-26121a98]{flex:1}.order-item .order-body .goods-wrap .goods-info .goods-name[data-v-26121a98]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;color:#303133}.order-item .order-body .goods-wrap .goods-info .goods-sub-section[data-v-26121a98]{width:100%;line-height:1.3;display:flex;margin-top:%?14?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-26121a98]{font-size:%?24?%;flex:1;font-weight:700;color:var(--price-color)}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-num[data-v-26121a98]{font-size:%?24?%;color:#909399;flex:1;text-align:right;line-height:1}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-num .iconfont[data-v-26121a98]{font-size:%?24?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-type[data-v-26121a98]{font-size:%?24?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-26121a98]{font-size:%?24?%;margin-right:%?2?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-26121a98]{flex:1;line-height:1.3;display:flex;flex-direction:column}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-26121a98]:last-of-type{text-align:right}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view:last-of-type .iconfont[data-v-26121a98]{line-height:1;font-size:%?24?%}.order-item .order-body .goods-wrap .goods-info .goods-action[data-v-26121a98]{text-align:right}.order-item .order-body .goods-wrap .goods-info .goods-action .action-btn[data-v-26121a98]{line-height:1;padding:%?14?% %?20?%;color:#303133;display:inline-block;border-radius:%?28?%;background:#fff;border:.5px solid #999;font-size:%?24?%;margin-left:%?10?%}.order-item .order-footer .order-base-info .total[data-v-26121a98]{padding:%?20?%;font-size:%?24?%;background:hsla(0,0%,97.3%,.5);display:flex}.order-item .order-footer .order-base-info .total > uni-text[data-v-26121a98]{flex:1;line-height:1;margin-left:%?10?%}.order-item .order-footer .order-base-info .order-type[data-v-26121a98]{padding-top:%?20?%;flex:0.5}.order-item .order-footer .order-base-info .order-type > uni-text[data-v-26121a98]{line-height:1}.order-item .order-footer .order-action[data-v-26121a98]{text-align:right;padding:%?30?%}.order-item .order-footer .order-action .order-box-btn[data-v-26121a98]{line-height:1;padding:%?20?% %?26?%;color:#333;display:inline-block;background:#fff;border:.5px solid #999;font-size:%?24?%;margin-left:%?20?%}.order-item .order-footer .order-action .order-box-btn.disabled[data-v-26121a98]{background:#eee;border-color:#e5e5e5;color:#999}.order-batch-action[data-v-26121a98]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?100?%;background:#fff;box-shadow:0 0 10px rgba(0,0,0,.1);text-align:right}.order-batch-action.bottom-safe-area[data-v-26121a98]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.order-batch-action .action-btn[data-v-26121a98]{height:%?68?%;line-height:%?68?%;background:#fff;padding:0 %?40?%;display:inline-block;text-align:center;margin:%?16?% %?20?% %?16?% 0;border-radius:%?10?%;border:1px solid #fff}.order-batch-action .action-btn.white[data-v-26121a98]{height:%?68?%;line-height:%?68?%;color:#333;border:1px solid #999;background:#fff}.sku[data-v-26121a98]{display:flex;line-height:1;margin-top:%?10?%}.goods-spec[data-v-26121a98]{color:#909399;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}',""]),t.exports=e},d125:function(t,e,o){"use strict";var a=o("27ab"),i=o.n(a);i.a},d5c3:function(t,e,o){"use strict";o.r(e);var a=o("e41e"),i=o("78a7");for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);o("d125"),o("8e59");var s=o("828b"),n=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"26121a98",null,!1,a["a"],void 0);e["default"]=n.exports},e41e:function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return r})),o.d(e,"a",(function(){return a}));var a={pageMeta:o("7854").default,nsEmpty:o("52a6").default,nsPayment:o("7aec").default,nsLogin:o("2910").default,loadingCover:o("c003").default},i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",[o("page-meta",{attrs:{"page-style":t.themeColor}}),o("v-uni-view",{staticClass:"order-container"},[t.storeToken?o("v-uni-view",{staticClass:"order-nav"},t._l(t.statusList,(function(e,a){return o("v-uni-view",{key:a,staticClass:"uni-tab-item",attrs:{id:e.id,"data-current":a},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.ontabtap.apply(void 0,arguments)}}},[o("v-uni-text",{staticClass:"uni-tab-item-title",class:e.status===t.orderStatus?"uni-tab-item-title-active color-base-border  color-base-text":""},[t._v(t._s(e.name))])],1)})),1):t._e(),t.storeToken?o("mescroll-uni",{ref:"mescroll",attrs:{top:"100rpx"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getListData.apply(void 0,arguments)}}},[o("template",{attrs:{slot:"list"},slot:"list"},[t.orderList.length>0?o("v-uni-view",{staticClass:"order-list"},t._l(t.orderList,(function(e,a){return o("v-uni-view",{key:a,staticClass:"order-item"},[o("v-uni-view",{staticClass:"order-header"},[o("v-uni-text",{staticClass:"font-size-base"},[t._v("订单号："+t._s(e.order_no))]),o("v-uni-text",{staticClass:"status-name color-base-text"},[t._v(t._s(e.order_status_name))])],1),o("v-uni-view",{staticClass:"order-body",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.orderDetail(e)}}},[o("v-uni-view",{staticClass:"goods-wrap"},[o("v-uni-view",{staticClass:"goods-img"},[o("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"aspectFill","lazy-load":!0},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a)}}})],1),o("v-uni-view",{staticClass:"goods-info"},[o("v-uni-view",{staticClass:"pro-info"},[o("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.sku_name))])],1),o("v-uni-view",{staticClass:"goods-sub-section"},[o("v-uni-text",{staticClass:"goods-price"},[o("v-uni-text",{staticClass:"unit  price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",{staticClass:"price-style large"},[t._v(t._s(parseFloat(e.price).toFixed(2).split(".")[0]))]),o("v-uni-text",{staticClass:"unit  price-style small"},[t._v("."+t._s(parseFloat(e.price).toFixed(2).split(".")[1]))])],1),o("v-uni-text",{staticClass:"goods-num"},[o("v-uni-text",{staticClass:"iconfont icon-close"}),t._v(t._s(e.num))],1)],1),o("v-uni-view",{staticClass:"goods-action"})],1)],1)],1),o("v-uni-view",{staticClass:"order-footer"},[o("v-uni-view",{staticClass:"order-base-info"},[o("v-uni-view",{staticClass:"total"},[o("v-uni-text",{staticClass:"font-size-sub"},[t._v("共"+t._s(e.num)+"件商品")]),0===e.order_status?o("v-uni-text",{staticClass:"align-right font-size-base"},[t._v("待付定金："),o("v-uni-text",{staticClass:"ns-font-size-lg ns-text-color"},[t._v(t._s(t.$lang("common.currencySymbol"))+t._s(e.pay_deposit_money))])],1):1===e.order_status?o("v-uni-text",{staticClass:"align-right font-size-base"},[t._v("待付尾款："),o("v-uni-text",{staticClass:"ns-font-size-lg ns-text-color"},[t._v(t._s(t.$lang("common.currencySymbol"))+t._s(e.final_money))])],1):2===e.order_status?o("v-uni-text",{staticClass:"align-right font-size-base"},[t._v("合计："),o("v-uni-text",{staticClass:"ns-font-size-lg ns-text-color"},[t._v(t._s(t.$lang("common.currencySymbol"))+"0.00")])],1):-1===e.order_status?o("v-uni-text",{staticClass:"align-right font-size-base"},[t._v("合计："),o("v-uni-text",{staticClass:"ns-font-size-lg ns-text-color"},[t._v(t._s(t.$lang("common.currencySymbol"))+t._s(e.order_money))])],1):t._e()],1)],1),e.action.length>0?o("v-uni-view",{staticClass:"order-action"},[t._l(e.action,(function(a,i){return["orderPayFinal"==a.action?[0!=e.refund_status&&-1!=e.refund_status||-1==e.order_status?o("v-uni-view",{staticClass:"order-box-btn disabled"},[t._v(t._s(a.title))]):[e.pay_start_time<t.timestamp&&e.pay_end_time>t.timestamp?o("v-uni-view",{staticClass:"order-box-btn color-base-border color-base-text",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.operation(a.action,e)}}},[t._v(t._s(a.title))]):o("v-uni-view",{staticClass:"order-box-btn disabled"},[t._v(t._s(a.title))])]]:"refundDeposit"==a.action?[0==e.refund_status&&0==e.is_deposit_back||-1==e.refund_status&&0==e.is_deposit_back?o("v-uni-view",{staticClass:"order-box-btn",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.operation(a.action,e)}}},[t._v(t._s(a.title))]):t._e()]:o("v-uni-view",{staticClass:"order-box-btn",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.operation(a.action,e)}}},[t._v(t._s(a.title))])]}))],2):o("v-uni-view",{staticClass:"order-action"},[o("v-uni-view",{staticClass:"order-box-btn color-base-border color-base-text",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.orderDetail(e)}}},[t._v("查看详情")])],1)],1)],1)})),1):o("v-uni-view",[o("ns-empty",{attrs:{isIndex:!0,emptyBtn:{url:"/pages_promotion/presale/list",text:"去逛逛"},text:"暂无相关预售订单"}})],1)],1)],2):t._e(),o("ns-payment",{ref:"choosePaymentPopup",attrs:{payMoney:t.payMoney,balanceDeduct:t.balanceDeduct,isBalance:t.isBalance},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.pay.apply(void 0,arguments)},useBalance:function(e){arguments[0]=e=t.$handleEvent(e),t.useBalance.apply(void 0,arguments)}}}),o("ns-login",{ref:"login"}),o("loading-cover",{ref:"loadingCover"})],1)],1)},r=[]},f48d:function(t,e,o){"use strict";o.r(e);var a=o("cc1b"),i=o.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a}}]);