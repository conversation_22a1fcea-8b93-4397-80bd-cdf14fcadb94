(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-pintuan-list"],{"015d":function(t,e,i){"use strict";i.r(e);var n=i("0f46"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=n},2407:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.advList.length?i("v-uni-view",{class:["container-box",t.className]},[t.advList.length>1?i("v-uni-swiper",{staticClass:"item-wrap",style:{height:t.swiperHeight+"px"},attrs:{"indicator-dots":t.advList.length>1,"indicator-active-color":"#ffffff",autoplay:!0,interval:3e3,duration:1e3,current:t.currentIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSwiper.apply(void 0,arguments)}}},t._l(t.advList,(function(e,n){return i("v-uni-swiper-item",{key:n,on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.jumppage(e.adv_url)}}},[i("v-uni-view",{staticClass:"image-box"},[i("v-uni-image",{attrs:{src:t.$util.img(e.adv_image),mode:"widthFix",id:"content-wrap"+n}})],1)],1)})),1):i("v-uni-view",{staticClass:"container-box item-wrap"},[i("v-uni-image",{attrs:{src:t.$util.img(t.advList[0]["adv_image"]),mode:"widthFix","lazy-load":"true"},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imageLoad.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumppage(t.advList[0].adv_url)}}})],1)],1):t._e()},o=[]},6102:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47");var n={name:"ns-advert",props:{keyword:{type:String},className:{type:String}},data:function(){return{advList:[],isImage:!1,swiperHeight:150,currentIndex:0}},created:function(){this.getAdvList()},methods:{getAdvList:function(){var t=this;this.$api.sendRequest({url:"/api/adv/detail",data:{keyword:this.keyword},success:function(e){if(0==e.code){var i=e.data.adv_list;for(var n in i)i[n].adv_url&&(i[n].adv_url=JSON.parse(i[n].adv_url));t.advList=e.data.adv_list,t.$nextTick((function(){t.setSwiperHeight()}))}}})},jumppage:function(t){this.$util.diyRedirectTo(t)},imageLoad:function(t){this.isImage=!0},changeSwiper:function(t){var e=this;this.currentIndex=t.detail.current,this.$nextTick((function(){e.setSwiperHeight()}))},setSwiperHeight:function(){var t=this;this.advList.length>1&&setTimeout((function(){var e="#content-wrap"+t.currentIndex,i=uni.createSelectorQuery().in(t);i.select(e).boundingClientRect(),i.exec((function(e){e&&e[0]&&(t.swiperHeight=e[0].height)}))}),10)}}};e.default=n},"61d8":function(t,e,i){"use strict";i.r(e);var n=i("bcee7"),o=i("c168");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("f78f");var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"21cde2e2",null,!1,n["a"],void 0);e["default"]=s.exports},7854:function(t,e,i){"use strict";i.r(e);var n=i("8ba8"),o=i("f48d");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"7e88":function(t,e,i){"use strict";i.r(e);var n=i("2407"),o=i("f016");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("a44f");var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"9caa2b5c",null,!1,n["a"],void 0);e["default"]=s.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},9583:function(t,e,i){var n=i("ba40");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("1b779df0",n,!0,{sourceMap:!1,shadowMode:!1})},a44f:function(t,e,i){"use strict";var n=i("d87f"),o=i.n(n);o.a},a725:function(t,e,i){"use strict";var n=i("ac2a"),o=i.n(n);o.a},ac2a:function(t,e,i){var n=i("f714");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("1a69ffc2",n,!0,{sourceMap:!1,shadowMode:!1})},b02d:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(i("2634")),a=n(i("2fdc"));i("bf0f"),i("2797"),i("5ef2"),i("c223");var r=n(i("7e88")),s={components:{nsAdv:r.default},data:function(){return{dataList:[],mpShareData:null,bgColor:""}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.pintuan||(e.$util.showToast({title:"商家未开启拼团",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}},onShow:function(){var t=this;return(0,a.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.storeToken&&uni.getStorageSync("source_member")&&t.$util.onSourceMember(uni.getStorageSync("source_member")),e.next=3,t.getZoneConfig();case 3:case"end":return e.stop()}}),e)})))()},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},methods:{getZoneConfig:function(){var t=this;return(0,a.default)((0,o.default)().mark((function e(){var i,n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$api.sendRequest({url:"/api/config/promotionZoneConfig",data:{name:"pintuan"},async:!1});case 2:i=e.sent,n=i.data,n&&(t.bgColor=n.bg_color);case 5:case"end":return e.stop()}}),e)})))()},getData:function(t){var e=this;this.$api.sendRequest({url:"/pintuan/api/goods/page",data:{page_size:t.size,page:t.num},success:function(i){var n=[],o=i.message;0==i.code&&i.data?n=i.data.list:e.$util.showToast({title:o}),t.endSuccess(n.length),1==t.num&&(e.dataList=[]),e.dataList=e.dataList.concat(n),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(){t.endErr(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},toDetail:function(t){this.$util.redirectTo("/pages_promotion/pintuan/detail",{pintuan_id:t.pintuan_id})},imgError:function(t){this.dataList[t].goods_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},goodsImg:function(t){var e=t.split(",");return e[0]?this.$util.img(e[0],{size:"mid"}):this.$util.getDefaultImage().goods},goodsTag:function(t){return t.label_name||""}}};e.default=s},ba40:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.ns-adv[data-v-21cde2e2]{margin:0;border-radius:%?10?%;overflow:hidden;line-height:1}.ns-adv uni-image[data-v-21cde2e2]{width:100%}.page[data-v-21cde2e2]{width:100%;min-height:100vh;background:var(--pintuan-promotion-color)}.lineheight-clear[data-v-21cde2e2]{line-height:1!important}.goods-list.single-column .goods-item[data-v-21cde2e2]{padding:%?26?%;background:#fff;margin:%?20?% %?30?%;border-radius:%?26?%;display:flex;position:relative}.goods-list.single-column .goods-item .goods-img[data-v-21cde2e2]{width:%?200?%;height:%?200?%;overflow:hidden;border-radius:%?10?%;margin-right:%?20?%}.goods-list.single-column .goods-item .goods-img uni-image[data-v-21cde2e2]{width:100%;height:100%}.goods-list.single-column .goods-item .goods-tag[data-v-21cde2e2]{color:#fff;line-height:1;padding:%?8?% %?12?%;position:absolute;border-top-left-radius:%?10?%;border-bottom-right-radius:%?10?%;top:%?26?%;left:%?26?%;font-size:%?22?%}.goods-list.single-column .goods-item .info-wrap[data-v-21cde2e2]{flex:1;display:flex;flex-direction:column;width:calc(100% - %?220?%)}.goods-list.single-column .goods-item .name-wrap[data-v-21cde2e2]{flex:1}.goods-list.single-column .goods-item .name-wrap .name-label[data-v-21cde2e2]{border:0;border-radius:%?8?%;margin-right:%?8?%;padding:0 8px;background:linear-gradient(270deg,var(--pintuan-promotion-color),var(--pintuan-promotion-aux-color));color:#fff;font-size:%?24?%}.goods-list.single-column .goods-item .goods-name[data-v-21cde2e2]{font-size:%?28?%;line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;height:%?68?%;width:100%;word-wrap:break-word}.goods-list.single-column .goods-item .pintuan-info uni-text[data-v-21cde2e2]{line-height:1;margin-right:%?20?%;border:1px solid #fff;font-size:%?24?%;color:var(--pintuan-color)}.goods-list.single-column .goods-item .discount-price[data-v-21cde2e2]{display:inline-block;font-weight:700;line-height:1;margin-top:%?16?%;color:var(--price-color)}.goods-list.single-column .goods-item .discount-price .unit[data-v-21cde2e2]{margin-right:%?6?%}.goods-list.single-column .goods-item .discount-price .txt[data-v-21cde2e2]{font-weight:400}.goods-list.single-column .goods-item .pro-info[data-v-21cde2e2]{position:relative;margin-top:%?16?%}.goods-list.single-column .goods-item .pro-info .delete-price[data-v-21cde2e2]{flex:1;display:flex;align-items:flex-end;text-decoration:line-through}.goods-list.single-column .goods-item .pro-info .delete-price .unit[data-v-21cde2e2]{margin-right:%?6?%}.goods-list.single-column .goods-item .pro-info .delete-price .txt[data-v-21cde2e2]{text-decoration:none}.goods-list.single-column .goods-item .pro-info > uni-view[data-v-21cde2e2]{line-height:1}.goods-list.single-column .goods-item .pro-info > uni-view uni-button[data-v-21cde2e2]{color:#fff;background-color:var(--pintuan-promotion-color);line-height:%?60?%;height:%?60?%;padding:0 %?30?%}.goods-list.single-column .goods-item .pro-info > uni-view[data-v-21cde2e2]:nth-child(2){position:absolute;right:0;bottom:0;font-weight:700}.goods-list.single-column .goods-item .member-price-tag[data-v-21cde2e2]{display:inline-block;width:%?60?%;line-height:1;margin-left:%?6?%}.goods-list.single-column .goods-item .member-price-tag uni-image[data-v-21cde2e2]{width:100%}',""]),t.exports=e},bcee7:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={pageMeta:i("7854").default,nsAdv:i("7e88").default,nsEmpty:i("52a6").default,hoverNav:i("c1f1").default,loadingCover:i("c003").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"page",style:{background:t.bgColor}},[t.addonIsExist.pintuan?i("mescroll-uni",{ref:"mescroll",attrs:{size:10},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getData.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[i("v-uni-view",{staticClass:"ns-adv"},[i("ns-adv",{attrs:{keyword:"NS_PINTUAN"}})],1),t.dataList.length?i("v-uni-view",{staticClass:"goods-list single-column"},t._l(t.dataList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"goods-item margin-bottom"},[i("v-uni-view",{staticClass:"goods-img",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-image",{attrs:{src:t.goodsImg(e.goods_image),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError(n)}}}),""!=t.goodsTag(e)?i("v-uni-view",{staticClass:"color-base-bg goods-tag"},[t._v(t._s(t.goodsTag(e)))]):t._e()],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap"},[i("v-uni-view",{staticClass:"goods-name",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-text",{staticClass:"name-label"},[t._v(t._s(e.pintuan_num)+"人团")]),t._v(t._s(e.goods_name))],1),i("v-uni-view",{staticClass:"pintuan-info"},[i("v-uni-text",{staticClass:"pintuan-num"},[t._v("已团"+t._s(e.sale_num)+"件")])],1)],1),i("v-uni-view",{staticClass:"lineheight-clear"},[i("v-uni-view",{staticClass:"discount-price"},[i("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),i("v-uni-text",{staticClass:"price  price-style large"},[t._v(t._s(parseFloat(e.pintuan_price).toFixed(2).split(".")[0]))]),i("v-uni-text",{staticClass:"unit  price-style small"},[t._v("."+t._s(parseFloat(e.pintuan_price).toFixed(2).split(".")[1]))])],1)],1),i("v-uni-view",{staticClass:"pro-info"},[i("v-uni-view",{staticClass:"delete-price font-size-activity-tag color-tip price-font"},[i("v-uni-text",{staticClass:"font-size-tag lineheight-clear txt"}),i("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(e.price))],1),i("v-uni-view",{on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"}},[t._v("去拼团")])],1)],1)],1)],1)})),1):t._e(),t.dataList.length?t._e():i("v-uni-view",[i("ns-empty",{attrs:{textColor:"#fff",isIndex:!1,text:"暂无拼团"}})],1)],1)],2):t._e(),i("hover-nav"),i("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},c168:function(t,e,i){"use strict";i.r(e);var n=i("b02d"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},c1f1:function(t,e,i){"use strict";i.r(e);var n=i("fa1d"),o=i("015d");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("a725");var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"c1934e78",null,!1,n["a"],void 0);e["default"]=s.exports},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(o){o.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=o},d87f:function(t,e,i){var n=i("d915");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("03d75754",n,!0,{sourceMap:!1,shadowMode:!1})},d915:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-9caa2b5c]{width:100%}.container-box .item-wrap[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-9caa2b5c]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}',""]),t.exports=e},f016:function(t,e,i){"use strict";i.r(e);var n=i("6102"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},f48d:function(t,e,i){"use strict";i.r(e);var n=i("cc1b"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},f714:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},f78f:function(t,e,i){"use strict";var n=i("9583"),o=i.n(n);o.a},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},o=[]}}]);