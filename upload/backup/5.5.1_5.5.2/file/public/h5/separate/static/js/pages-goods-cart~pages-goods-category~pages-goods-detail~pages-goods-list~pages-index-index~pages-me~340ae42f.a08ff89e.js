(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-cart~pages-goods-category~pages-goods-detail~pages-goods-list~pages-index-index~pages-me~340ae42f"],{"04c1":function(e,t,r){"use strict";r.r(t);var a=r("a796"),n=r("6b13");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);r("0738");var l=r("828b"),u=Object(l["a"])(n["default"],a["b"],a["c"],!1,null,"60f883a1",null,!1,a["a"],void 0);t["default"]=u.exports},"0738":function(e,t,r){"use strict";var a=r("6c58"),n=r.n(a);n.a},"254f":function(e,t,r){var a=r("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.regions-picker-container[data-v-60f883a1]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.regions-picker-container .picker-header[data-v-60f883a1]{display:flex;height:45px;align-items:center;border-bottom:%?2?% solid #f5f5f5;justify-content:space-between}.regions-picker-container .picker-header .picker-action[data-v-60f883a1]{padding:0 %?28?%;font-size:%?34?%}.regions-picker-container .picker-header .confirm[data-v-60f883a1]{color:#007aff}',""]),e.exports=t},"3c2e":function(e,t,r){"use strict";r("6a54");var a=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(r("5de6")),i=a(r("2634")),l=a(r("2fdc"));r("fd3c"),r("bf0f"),r("2797"),r("aa9c");var u={props:{defaultRegions:{type:Array},selectArr:{type:String}},data:function(){return{pickerValueArray:[],cityArr:[],districtArr:[],multiIndex:[0,0,0],isInitMultiArray:!1,isLoadDefaultAreas:!1}},watch:{defaultRegions:{handler:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.length==this.selectArr&&e.join("")!==t.join("")&&(this.isInitMultiArray=!1,this.handleDefaultRegions())},immediate:!0}},computed:{multiArray:function(){if(this.isLoadDefaultAreas){var e=this.pickedArr.map((function(e){return e.map((function(e){return e.label}))}));return e}},pickedArr:function(){return this.isInitMultiArray?"2"==this.selectArr?[this.pickerValueArray[0],this.pickerValueArray[1]]:[this.pickerValueArray[0],this.pickerValueArray[1],this.pickerValueArray[2]]:"2"==this.selectArr?[this.pickerValueArray[0],this.cityArr]:[this.pickerValueArray[0],this.cityArr,this.districtArr]}},created:function(){this.getDefaultAreas(0,{level:0})},methods:{pickerViewColumnChange:function(e){var t=this;return(0,l.default)((0,i.default)().mark((function r(){var a;return(0,i.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(a=e.detail.value,a[0]==t.multiIndex[0]){r.next=10;break}return r.next=4,t.handleColumnChange({detail:{column:0,value:a[0]}});case 4:return t.multiIndex[1]=e.detail.value[1]||0,r.next=7,t.handleColumnChange({detail:{column:1,value:a[1]}});case 7:t.multiIndex[2]=e.detail.value[2]||0,r.next=17;break;case 10:if(a[1]==t.multiIndex[1]){r.next=16;break}return r.next=13,t.handleColumnChange({detail:{column:1,value:a[1]}});case 13:t.multiIndex[2]=e.detail.value[2]||0,r.next=17;break;case 16:t.multiIndex=a;case 17:case"end":return r.stop()}}),r)})))()},confirmRegions:function(){this.handleValueChange({detail:{value:this.multiIndex}}),this.$refs.regionsRef.close()},handleColumnChange:function(e){var t=this;return(0,l.default)((0,i.default)().mark((function r(){var a,n;return(0,i.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.isInitMultiArray=!1,a=e.detail.column,n=e.detail.value,t.multiIndex[a]=n,r.t0=a,r.next=0===r.t0?7:1===r.t0?14:2===r.t0?18:27;break;case 7:return r.next=9,t.getAreasAsync(t.pickerValueArray[0][t.multiIndex[a]].value);case 9:return t.cityArr=r.sent,r.next=12,t.getAreasAsync(t.cityArr[0].value);case 12:return t.districtArr=r.sent,r.abrupt("break",27);case 14:return r.next=16,t.getAreasAsync(t.cityArr[t.multiIndex[a]].value);case 16:return t.districtArr=r.sent,r.abrupt("break",27);case 18:if(t.cityArr.length){r.next=22;break}return r.next=21,t.getAreasAsync(t.pickerValueArray[0][0].value);case 21:t.cityArr=r.sent;case 22:if(t.districtArr.length){r.next=26;break}return r.next=25,t.getAreasAsync(t.cityArr[0].value);case 25:t.districtArr=r.sent;case 26:return r.abrupt("break",27);case 27:case"end":return r.stop()}}),r)})))()},handleValueChange:function(e){var t=(0,n.default)(e.detail.value,3),r=t[0],a=t[1],i=t[2],l=(0,n.default)(this.pickedArr,3),u=l[0],o=l[1],s=l[2],c="";c="2"==this.selectArr?[u[r],o[a]]:[u[r],o[a],s[i]],this.$emit("getRegions",c)},handleDefaultRegions:function(){var e=this,t=setInterval((function(){for(var r=0;r<e.defaultRegions.length;r++)if(null!=e.pickerValueArray[r])for(var a=function(t){e.defaultRegions[r]!=e.pickerValueArray[r][t].value&&e.defaultRegions[r]!=e.pickerValueArray[r][t].label||1!=e.pickerValueArray[r][t].level||(e.$set(e.multiIndex,r,t),e.getAreas(e.pickerValueArray[r][t].value,(function(r){e.cityArr=r,e.$set(e.pickerValueArray,1,r);for(var a=function(r){if(e.defaultRegions[1]==e.cityArr[r].value||e.defaultRegions[1]==e.cityArr[r].label)return e.$set(e.multiIndex,1,r),e.getAreas(e.cityArr[r].value,(function(a){e.districtArr=a,e.$set(e.pickerValueArray,2,a);for(var n=0;n<e.districtArr.length;n++)if(e.defaultRegions[2]==e.districtArr[n].value||e.defaultRegions[2]==e.districtArr[n].label){e.$set(e.multiIndex,2,n),e.handleValueChange({detail:{value:[t,r,n]}});break}})),"break"},n=0;n<e.cityArr.length;n++){var i=a(n);if("break"===i)break}})))},n=0;n<e.pickerValueArray[r].length;n++)a(n);e.isLoadDefaultAreas&&clearInterval(t)}),100)},getDefaultAreas:function(e,t){var r=this;this.$api.sendRequest({url:"/api/address/lists",data:{pid:e},success:function(e){if(0==e.code){var a=[],n=void 0;e.data.forEach((function(e,r){void 0!=t&&(0==t.level&&void 0!=t.province_id?n=t.province_id:1==t.level&&void 0!=t.city_id?n=t.city_id:2==t.level&&void 0!=t.district_id&&(n=t.district_id)),void 0==n&&0==r&&(n=e.id),a.push({value:e.id,label:e.name,level:e.level})})),r.pickerValueArray[t.level]=a,t.level+1<3?(t.level++,r.getDefaultAreas(n,t)):(r.isInitMultiArray=!0,r.isLoadDefaultAreas=!0,r.handleDefaultRegions())}}})},getAreasAsync:function(e){var t=this;return(0,l.default)((0,i.default)().mark((function r(){var a,n;return(0,i.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.$api.sendRequest({url:"/api/address/lists",data:{pid:e},async:!1});case 2:if(a=r.sent,0!=a.code){r.next=7;break}return n=[],a.data.forEach((function(e,t){n.push({value:e.id,label:e.name,level:e.level})})),r.abrupt("return",n);case 7:case"end":return r.stop()}}),r)})))()},getAreas:function(e,t){this.$api.sendRequest({url:"/api/address/lists",data:{pid:e},success:function(e){if(0==e.code){var r=[];e.data.forEach((function(e,t){r.push({value:e.id,label:e.name,level:e.level})})),t&&t(r)}}})}}};t.default=u},"6b13":function(e,t,r){"use strict";r.r(t);var a=r("3c2e"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);t["default"]=n.a},"6c58":function(e,t,r){var a=r("254f");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=r("967d").default;n("0db4f3fd",a,!0,{sourceMap:!1,shadowMode:!1})},7854:function(e,t,r){"use strict";r.r(t);var a=r("8ba8"),n=r("f48d");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);var l=r("828b"),u=Object(l["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=u.exports},"8ba8":function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){}));var a=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},a796:function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",{staticClass:"pick-regions"},[r("v-uni-picker",{attrs:{mode:"multiSelector",value:e.multiIndex,range:e.multiArray},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.handleValueChange.apply(void 0,arguments)},columnchange:function(t){arguments[0]=t=e.$handleEvent(t),e.handleColumnChange.apply(void 0,arguments)}}},[e._t("default")],2)],1)},n=[]},cc1b:function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("5ef2"),r("64aa"),r("5c47"),r("a1c1"),r("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(e){return-1!==["dark","light"].indexOf(e)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var e=this,t=getCurrentPages()[0];this.$pageVm=t.$vm||t,uni.onWindowResize((function(t){e.$emit("resize",t)})),this.$pageVm.$on("hook:onPageScroll",(function(t){e.$emit("scroll",t)})),this.$watch("backgroundTextStyle",(function(){e.setBackgroundTextStyle()})),this.$watch((function(){return[e.rootFontSize,e.pageStyle]}),(function(){e.setPageMeta()})),this.$watch((function(){return[e.backgroundColor,e.backgroundColorTop,e.backgroundColorBottom]}),(function(){e.setBackgroundColor()})),this.$watch((function(){return[e.scrollTop,e.scrollDuration]}),(function(){e.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(e,t){e.setStyle({pullToRefresh:{support:t,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var e=this,t=String(this.scrollTop);if(-1!==t.indexOf("rpx")&&(t=uni.upx2px(t.replace("rpx",""))),t=parseFloat(t),!isNaN(t)){var r=function r(n){n.scrollTop===t&&(e.$pageVm.$off("hook:onPageScroll",r),e.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:t,duration:this.scrollDuration,success:function(){e.$pageVm.$on("hook:onPageScroll",r)}})}}}};t.default=n},f48d:function(e,t,r){"use strict";r.r(t);var a=r("cc1b"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);t["default"]=n.a}}]);