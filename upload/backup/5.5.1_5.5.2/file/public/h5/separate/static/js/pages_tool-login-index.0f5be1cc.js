(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-login-index"],{2435:function(t,e,i){"use strict";var a=i("9e3f"),n=i.n(a);n.a},"2f9f":function(t,e,i){"use strict";i.r(e);var a=i("d5c3f"),n=i("7289");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("2435");var r=i("828b"),s=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"f557a414",null,!1,a["a"],void 0);e["default"]=s.exports},7289:function(t,e,i){"use strict";i.r(e);var a=i("76a5"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"76a5":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n=a(i("0713")),o={mixins:[n.default],data:function(){return{back:"",registerConfig:{register:"",login:""},isAgree:!1}},computed:{isQuickLogin:function(){return this.$util.isWeiXin()&&this.wechatConfigStatus&&this.registerConfig&&Number(this.registerConfig.third_party)},warpStyle:function(){var t="";return this.registerConfig.wap_bg&&(t+="background-image:url("+this.$util.img(this.registerConfig.wap_bg)+");",t+="background-size: 100%;",t+="background-position: top;",t+="background-repeat: no-repeat;"),t},wechatConfigStatus:function(){return this.$store.state.wechatConfigStatus}},onLoad:function(t){this.back=t.back||"",this.back&&uni.setStorageSync("initiateLogin",this.back),uni.getStorageSync("authInfo")||(this.$util.isWeiXin()&&!t.code&&this.getCode((function(t){uni.setStorageSync("authInfo",t)})),t.code&&this.$api.sendRequest({url:"/wechat/api/wechat/authcodetoopenid",data:{code:urlParams.code},success:function(t){if(t.code>=0){var e={};t.data.openid&&(e.wx_openid=t.data.openid),t.data.unionid&&(e.wx_unionid=t.data.unionid),t.data.userinfo&&Object.assign(e,t.data.userinfo),uni.setStorageSync("authInfo",e)}}}))},onShow:function(){this.getRegisterConfig()},methods:{toAggrement:function(t){this.$util.redirectTo("/pages_tool/login/aggrement",{type:t})},quickLogin:function(){!this.registerConfig.agreement_show||this.isAgree?this.$refs.login.open("",!0):this.$util.showToast({title:"请先阅读并同意协议"})},toLogin:function(t){this.$util.redirectTo("/pages_tool/login/login",{loginMode:t})},getRegisterConfig:function(){var t=this;this.$api.sendRequest({url:"/api/register/config",success:function(e){e.code>=0&&(t.registerConfig=e.data.value)}})}}};e.default=o},7854:function(t,e,i){"use strict";i.r(e);var a=i("8ba8"),n=i("f48d");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);var r=i("828b"),s=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=s.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"9e3f":function(t,e,i){var a=i("e455");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("2e85e140",a,!0,{sourceMap:!1,shadowMode:!1})},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=n},d5c3f:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={pageMeta:i("7854").default,nsLogin:i("2910").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"auth-index",style:t.warpStyle},[i("v-uni-view",{staticClass:"website-logo"},[t.siteInfo.logo?i("v-uni-image",{staticClass:"logo",attrs:{src:t.$util.img(t.siteInfo.logo),mode:"aspectFit"}}):i("v-uni-view",{staticClass:"logo"})],1),i("v-uni-view",{staticClass:"login-desc"},[t._v(t._s(t.registerConfig.wap_desc))]),i("v-uni-view",{staticClass:"login-area"},[t.$util.isWeiXin()&&t.wechatConfigStatus&&t.registerConfig&&Number(t.registerConfig.third_party)?i("v-uni-view",{staticClass:"btn quick-login",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.quickLogin.apply(void 0,arguments)}}},[t._v("快捷登录/注册")]):t._e(),-1!=t.registerConfig.login.indexOf("mobile")?i("v-uni-view",{staticClass:"btn",class:t.isQuickLogin?"":"quick-login",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toLogin("mobile")}}},[t._v("手机号登录")]):t._e(),-1==t.registerConfig.login.indexOf("mobile")&&-1!=t.registerConfig.login.indexOf("username")?i("v-uni-view",{staticClass:"btn",class:t.isQuickLogin?"":"quick-login",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toLogin("account")}}},[t._v("账号密码登录")]):t._e(),t.registerConfig.agreement_show?i("v-uni-view",{staticClass:"agreement"},[i("v-uni-text",{staticClass:"iconfont agree",class:t.isAgree?"icon-yuan_checked color-base-text":"icon-yuan_checkbox",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.isAgree=!t.isAgree}}}),i("v-uni-view",{staticClass:"tips-text"},[i("v-uni-text",{staticClass:"tips"},[t._v("请阅读并同意")]),i("v-uni-text",{staticClass:"agreement-name color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAggrement("PRIVACY")}}},[t._v("《隐私协议》")]),i("v-uni-text",{staticClass:"tips"},[t._v("和")]),i("v-uni-text",{staticClass:"agreement-name color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAggrement("SERVICE")}}},[t._v("《用户协议》")])],1)],1):t._e(),-1!=t.registerConfig.login.indexOf("mobile")&&-1!=t.registerConfig.login.indexOf("username")?i("v-uni-view",{staticClass:"footer"},[i("v-uni-view",{staticClass:"text"},[t._v("其他方式登录")]),i("v-uni-view",{staticClass:"mine icondiy icon-system-wodi2",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toLogin("account")}}}),i("v-uni-view",{staticClass:"mode-name"},[t._v("账号密码登录")])],1):t._e()],1),i("ns-login",{ref:"login"})],1)],1)},o=[]},e455:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-f557a414] .uni-scroll-view{background-color:#fff}[data-v-f557a414] .uni-scroll-view::-webkit-scrollbar{\r\n  /* 隐藏滚动条，但依旧具备可以滚动的功能 */display:none}uni-page-body[data-v-f557a414]{width:100%;background:#fff!important}body.?%PAGE?%[data-v-f557a414]{background:#fff!important}.align-right[data-v-f557a414]{color:#838383}.container[data-v-f557a414]{width:100vw;height:100vh}.header-wrap[data-v-f557a414]{width:80%;margin:calc(%?120?% + 44px) auto 0;background-repeat:no-repeat;background-size:contain;background-position:bottom;position:relative}.header-wrap .title[data-v-f557a414]{font-size:%?60?%;font-weight:700}.body-wrap[data-v-f557a414]{margin-top:%?100?%;padding-bottom:%?100?%}.body-wrap .form-wrap[data-v-f557a414]{width:80%;margin:0 auto}.body-wrap .form-wrap .input-wrap[data-v-f557a414]{position:relative;width:100%;box-sizing:border-box;height:%?60?%;margin-top:%?60?%}.body-wrap .form-wrap .input-wrap .iconfont[data-v-f557a414]{width:%?60?%;height:%?60?%;position:absolute;left:0;right:0;line-height:%?60?%;font-size:%?32?%;color:#303133;font-weight:600}.body-wrap .form-wrap .input-wrap .content[data-v-f557a414]{display:flex;height:%?60?%;border-bottom:%?2?% solid #eee;align-items:center}.body-wrap .form-wrap .input-wrap .content .input[data-v-f557a414]{flex:1;height:%?60?%;line-height:%?60?%;font-size:%?28?%}.body-wrap .form-wrap .input-wrap .content .input-placeholder[data-v-f557a414]{font-size:%?28?%;color:#bfbfbf;line-height:%?60?%}.body-wrap .form-wrap .input-wrap .content .captcha[data-v-f557a414]{margin:%?4?%;height:%?52?%;width:%?140?%}.body-wrap .form-wrap .input-wrap .content .dynacode[data-v-f557a414]{line-height:%?60?%;font-size:%?24?%}.body-wrap .form-wrap .input-wrap .content .area-code[data-v-f557a414]{line-height:%?60?%;margin-right:%?20?%;font-size:%?28?%}.body-wrap .forget-section[data-v-f557a414]{display:flex;width:80%;margin:%?40?% auto}.body-wrap .forget-section uni-view[data-v-f557a414]{flex:1;font-size:%?24?%;line-height:1}.body-wrap .btn_view[data-v-f557a414]{width:100%;margin:%?94?% auto auto;padding:0 %?30?%;box-sizing:border-box}.body-wrap .login-btn[data-v-f557a414]{height:%?90?%;line-height:%?90?%;border-radius:%?90?%;text-align:center;border:%?2?% solid;width:100%;margin:0}.body-wrap .auth-login[data-v-f557a414]{margin-top:%?20?%;width:calc(100% - %?4?%);height:%?90?%;line-height:%?90?%;border-radius:%?10?%;border:%?2?% solid;color:#fff;text-align:center;margin-left:0;background-color:#fff}.body-wrap .auth-login uni-text[data-v-f557a414]{color:#d0d0d0}.body-wrap .auth-login .iconfont[data-v-f557a414]{font-size:%?70?%}.body-wrap .auth-login .icon-weixin[data-v-f557a414]{color:#1aad19}.body-wrap .regisiter-agreement[data-v-f557a414]{text-align:center;margin-top:%?30?%;color:#838383;line-height:%?60?%;font-size:%?24?%}.body-wrap .regisiter-agreement .tips[data-v-f557a414]{margin:0 %?10?%}.body-wrap .regisiter-agreement .is-agree[data-v-f557a414]{font-size:%?26?%}.login-btn-box[data-v-f557a414]{margin-top:%?50?%}.login-btn-box.active[data-v-f557a414]{margin:%?30?% 0 %?50?%}.back-btn[data-v-f557a414]{font-size:%?52?%;position:fixed;left:%?24?%;top:%?72?%;z-index:9;color:#000}.login-mode-box[data-v-f557a414]{display:flex;justify-content:flex-end;color:#909399;margin:auto;margin-top:%?44?%;font-size:%?26?%;width:80%}.auth-index[data-v-f557a414]{width:100vw;height:100vh;box-sizing:border-box;padding:0 %?44?%}.auth-index .website-logo[data-v-f557a414]{padding-top:%?154?%;display:flex;justify-content:center}.auth-index .website-logo .logo[data-v-f557a414]{width:%?300?%;height:%?90?%;display:block}.auth-index .login-desc[data-v-f557a414]{color:#333;font-size:%?28?%;text-align:center;line-height:%?34?%;min-height:%?34?%;margin-top:%?40?%}.auth-index .login-area[data-v-f557a414]{margin-top:%?181?%;display:flex;flex-direction:column;align-items:center}.auth-index .login-area .btn[data-v-f557a414]{background-color:#fff;border:%?2?% solid var(--base-color);color:var(--base-color);box-sizing:border-box;width:%?630?%;height:%?88?%;font-size:%?26?%;border-radius:%?44?%;line-height:%?86?%;font-weight:500;text-align:center;margin-bottom:%?40?%}.auth-index .login-area .btn.quick-login[data-v-f557a414]{color:#fff;background-color:var(--base-color)}.auth-index .login-area .agreement[data-v-f557a414]{display:flex;align-items:center;justify-content:center;width:100%;margin-top:%?28?%;padding:%?10?% 0;font-size:%?24?%;line-height:1}.auth-index .login-area .agreement .agree[data-v-f557a414]{color:#c8c9cc;font-size:%?26?%;line-height:%?22?%;margin-right:%?12?%}.auth-index .login-area .agreement .tips-text[data-v-f557a414]{display:flex;align-items:center;line-height:%?28?%;font-size:%?24?%}.auth-index .login-area .agreement .tips-text .tips[data-v-f557a414]{color:#666}.auth-index .login-area .footer[data-v-f557a414]{margin-top:%?200?%;width:100%;box-sizing:border-box;display:flex;flex-direction:column;align-items:center}.auth-index .login-area .footer .text[data-v-f557a414]{font-size:%?26?%;line-height:%?36?%;color:#333;text-align:center;margin-bottom:%?30?%;font-weight:400}.auth-index .login-area .footer .mine[data-v-f557a414]{width:%?80?%;height:%?80?%;line-height:%?78?%;border:%?2?% solid #ddd;border-radius:50%;font-size:%?46?%;display:flex;align-items:center;justify-content:center;color:var(--base-color)}.auth-index .login-area .footer .mode-name[data-v-f557a414]{font-size:%?24?%;line-height:%?36?%;color:#999;font-weight:400;margin-top:%?30?%}',""]),t.exports=e},f48d:function(t,e,i){"use strict";i.r(e);var a=i("cc1b"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a}}]);