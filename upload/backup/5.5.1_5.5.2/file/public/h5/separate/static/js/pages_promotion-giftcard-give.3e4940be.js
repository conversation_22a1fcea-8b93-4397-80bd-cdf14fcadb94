(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-giftcard-give"],{"08a5":function(e,t,a){var o=a("97aa");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var n=a("967d").default;n("32bb30ec",o,!0,{sourceMap:!1,shadowMode:!1})},"0afb":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return o}));var o={pageMeta:a("7854").default,nsLogin:a("2910").default,uniPopup:a("d745").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":e.themeColor}}),a("v-uni-view",{staticClass:"page"},[a("v-uni-view",{staticClass:"detail-box"},[a("v-uni-view",{staticClass:"title"},[e._v("文字祝福"),a("v-uni-text",[e._v("(自定义)")])],1),a("v-uni-view",{staticClass:"textarea"},[a("v-uni-textarea",{attrs:{maxlength:"15",placeholder:"赠你一份好礼，祝你每天都开心快乐！","placeholder-style":"font-size:28rpx;color:#999999"},model:{value:e.message,callback:function(t){e.message=t},expression:"message"}})],1),a("v-uni-view",{staticClass:"bottom-btn"},[a("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSharePopup.apply(void 0,arguments)}}},[e._v("送给朋友")]),a("v-uni-button",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.redirectTo("/pages_promotion/giftcard/list")}}},[e._v("放入卡包")])],1)],1),a("ns-login",{ref:"login"}),a("v-uni-view",{on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("uni-popup",{ref:"sharePopup",staticClass:"share-popup",attrs:{type:"bottom"}},[a("v-uni-view",[a("v-uni-view",{staticClass:"share-title"},[e._v("分享")]),a("v-uni-view",{staticClass:"share-content"},[e.$util.isWeiXin()?a("v-uni-view",{staticClass:"share-box"},[a("v-uni-button",{staticClass:"share-btn",attrs:{plain:!0},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.maskShow()}}},[a("v-uni-view",{staticClass:"iconfont icon-share-friend"}),a("v-uni-text",[e._v("分享给好友")])],1)],1):e._e(),a("v-uni-view",{staticClass:"share-box",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.copyUrl.apply(void 0,arguments)}}},[a("v-uni-button",{staticClass:"share-btn",attrs:{plain:!0}},[a("v-uni-view",{staticClass:"iconfont icon-fuzhilianjie"}),a("v-uni-text",[e._v("复制链接")])],1)],1),a("v-uni-view",{staticClass:"share-box",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPosterPopup.apply(void 0,arguments)}}},[a("v-uni-button",{staticClass:"share-btn",attrs:{plain:!0}},[a("v-uni-view",{staticClass:"iconfont icon-pengyouquan"}),a("v-uni-text",[e._v("生成分享海报")])],1)],1)],1),a("v-uni-view",{staticClass:"share-footer",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closeSharePopup.apply(void 0,arguments)}}},[a("v-uni-text",[e._v("取消分享")])],1)],1)],1)],1),a("v-uni-view",{on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("uni-popup",{ref:"posterPopup",staticClass:"poster-layer",attrs:{type:"bottom"}},["-1"!=e.poster?[a("v-uni-view",[a("v-uni-view",{staticClass:"image-wrap"},[a("v-uni-image",{attrs:{src:e.$util.img(e.poster),"show-menu-by-longpress":!0}})],1),a("v-uni-view",{staticClass:"save"},[e._v("长按保存图片")])],1),a("v-uni-view",{staticClass:"close iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePosterPopup()}}})]:a("v-uni-view",{staticClass:"msg"},[e._v(e._s(e.posterMsg))])],2),e.shareMask?a("v-uni-view",{staticClass:"wechat-share",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.shareMask=!1}}},[a("v-uni-image",{attrs:{src:e.$util.img("public/uniapp/giftcard/wechat_share.png"),mode:"widthFix"}})],1):e._e()],1)],1)],1)},i=[]},"450a":function(e,t,a){"use strict";a.r(t);var o=a("6a47"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},"6a47":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("bf0f"),a("2797"),a("5ef2"),a("5c47"),a("e838"),a("e966"),a("d4b5");var o={data:function(){return{memberCardId:0,cardInfo:null,message:"",poster:"-1",posterMsg:"",posterHeight:0,no:"",shareMask:!1}},onLoad:function(e){var t=this;if(e.member_card_id&&(this.memberCardId=e.member_card_id),e.scene){var a=decodeURIComponent(e.scene);a=a.split("&"),a.length&&a.forEach((function(e){-1!=e.indexOf("member_card_id")&&(t.memberCardId=e.split("-")[1])}))}},onShow:function(){this.getData()},methods:{getData:function(){var e=this;this.$api.sendRequest({url:"/giftcard/api/membercard/detail",data:{member_card_id:this.memberCardId},success:function(t){t.code>=0?(e.cardInfo=t.data,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):(e.$util.showToast({title:t.message,mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages_promotion/giftcard/member")}),1500))},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},maskShow:function(){this.shareMask=!0,this.closeSharePopup()},openSharePopup:function(){var e=this;this.$api.sendRequest({url:"/giftcard/api/membercard/blessing",data:{member_card_id:e.memberCardId,blessing:e.message?e.message:"赠你一份好礼，祝你每天都开心快乐！"},success:function(t){t.code>=0?(e.no=t.data,e.$refs.sharePopup.open(),e.setPublicShare()):e.$util.showToast({title:t.message})},fail:function(e){console.log(e)}})},closeSharePopup:function(){this.$refs.sharePopup.close()},copyUrl:function(){var e=this,t="嘿！朋友送你一张礼品卡，快来领取吧~"+this.$config.h5Domain+"/pages_promotion/giftcard/give_info?no="+this.no;this.memberInfo&&this.memberInfo.member_id&&(t+="&source_member="+this.memberInfo.member_id),this.$util.copy(t,(function(){e.closeSharePopup()}))},openPosterPopup:function(){var e=this;this.getGoodsPoster(),this.$refs.sharePopup.close(),this.$refs.posterPopup.open(),"-1"!=this.poster&&setTimeout((function(){var t=uni.createSelectorQuery().in(e).select(".poster-layer .image-wrap");t.fields({size:!0},(function(t){var a=t.width,o=parseFloat((740/a).toFixed(2));""!=e.storeToken?e.posterHeight=parseInt(1240/o):e.posterHeight=parseInt(1100/o)})).exec()}),100)},closePosterPopup:function(){this.$refs.posterPopup.close()},getGoodsPoster:function(){var e=this;uni.showLoading({title:"海报生成中..."});var t={no:this.no};this.memberInfo&&this.memberInfo.member_id&&(t.source_member=this.memberInfo.member_id),this.$api.sendRequest({url:"/giftcard/api/membercard/poster",data:{page:"/pages_promotion/giftcard/give_info",qrcode_param:JSON.stringify(t)},success:function(t){0==t.code?e.poster=t.data.path+"?time="+(new Date).getTime():e.posterMsg=t.message,uni.hideLoading()},fail:function(e){uni.hideLoading()}})},setPublicShare:function(){var e=this.$config.h5Domain+"/pages_promotion/giftcard/give_info?no="+this.no;this.memberInfo&&this.memberInfo.member_id&&(e+="&source_member="+this.memberInfo.member_id),this.$util.setPublicShare({title:this.cardInfo.card_name,desc:this.message?this.message:"赠你一份好礼，祝你每天都开心快乐！",link:e,imgUrl:this.$util.img(this.cardInfo.card_cover.split(",")[0])},(function(e){}))}},onShareAppMessage:function(){var e="/pages_promotion/giftcard/give_info?no="+this.no;return this.memberInfo&&this.memberInfo.member_id&&(e+="&source_member="+this.memberInfo.member_id),{title:"朋友送你一张礼品卡，快来领取吧~",imageUrl:this.$util.img(this.cardInfo.card_cover.split(",")[0]),path:e,success:function(e){},fail:function(e){},complete:function(e){}}}};t.default=o},"6eca":function(e,t,a){"use strict";a.r(t);var o=a("0afb"),n=a("450a");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("c468");var r=a("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,"2d8e623e",null,!1,o["a"],void 0);t["default"]=s.exports},7854:function(e,t,a){"use strict";a.r(t);var o=a("8ba8"),n=a("f48d");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);var r=a("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=s.exports},"8ba8":function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var o=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"97aa":function(e,t,a){var o=a("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.page[data-v-2d8e623e]{background:#fff;min-height:100vh}.detail-box[data-v-2d8e623e]{padding:%?30?% %?24?%}.detail-box .title[data-v-2d8e623e]{font-weight:700;font-size:%?32?%}.detail-box .title uni-text[data-v-2d8e623e]{font-size:%?26?%;font-weight:500;color:#999;margin-left:%?10?%}.detail-box .textarea[data-v-2d8e623e]{height:%?180?%;background:#f5f6f9;border-radius:%?8?%;padding:%?24?%;font-size:%?24?%;margin-top:%?20?%;overflow:hidden}.detail-box .textarea uni-textarea[data-v-2d8e623e]{font-size:%?28?%;width:100%;height:100%}.bottom-btn[data-v-2d8e623e]{margin-top:%?120?%}.bottom-btn uni-button[data-v-2d8e623e]{margin-bottom:%?20?%;border:%?2?% solid var(--main-color)}.bottom-btn uni-button[data-v-2d8e623e]:last-child{color:var(--main-color);border:%?2?% solid var(--main-color);background-color:#fff}.share-popup .share-title[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-title[data-v-2d8e623e]{line-height:%?60?%;font-size:%?32?%;padding:%?15?% 0;text-align:center}.share-popup .share-content[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-content[data-v-2d8e623e]{display:flex;display:-webkit-flex;-webkit-flex-wrap:wrap;-moz-flex-wrap:wrap;-ms-flex-wrap:wrap;-o-flex-wrap:wrap;flex-wrap:wrap;padding:%?15?%}.share-popup .share-content .share-box[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-content .share-box[data-v-2d8e623e]{flex:1;text-align:center}.share-popup .share-content .share-box .share-btn[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn[data-v-2d8e623e]{margin:0;padding:0;border:none;line-height:1;height:auto}.share-popup .share-content .share-box .share-btn uni-text[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-content .share-box .share-btn uni-text[data-v-2d8e623e]{margin-top:%?20?%;font-size:%?24?%;display:block;color:#303133}.share-popup .share-content .share-box .iconfont[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-content .share-box .iconfont[data-v-2d8e623e]{font-size:%?80?%;line-height:normal}.share-popup .share-content .share-box .icon-fuzhilianjie[data-v-2d8e623e],\r\n.share-popup .share-content .share-box .icon-pengyouquan[data-v-2d8e623e],\r\n.share-popup .share-content .share-box .icon-haowuquan[data-v-2d8e623e],\r\n.share-popup .share-content .share-box .icon-share-friend[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-content .share-box .icon-fuzhilianjie[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-content .share-box .icon-pengyouquan[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-content .share-box .icon-haowuquan[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-content .share-box .icon-share-friend[data-v-2d8e623e]{color:#07c160}.share-popup .share-footer[data-v-2d8e623e],\r\n.uni-popup__wrapper-box .share-footer[data-v-2d8e623e]{height:%?90?%;line-height:%?90?%;border-top:%?2?% #f5f5f5 solid;text-align:center;color:#666}.poster-layer .generate-poster[data-v-2d8e623e]{padding:%?40?% 0}.poster-layer .generate-poster .iconfont[data-v-2d8e623e]{font-size:%?80?%;color:#07c160;line-height:normal}.poster-layer .generate-poster > uni-view[data-v-2d8e623e]{text-align:center}.poster-layer .generate-poster > uni-view[data-v-2d8e623e]:last-child{margin-top:%?20?%}.poster-layer .image-wrap[data-v-2d8e623e]{width:64%;height:%?854?%;margin:%?60?% auto %?40?% auto;box-shadow:0 0 %?32?% hsla(0,0%,39.2%,.3)}.poster-layer .image-wrap uni-image[data-v-2d8e623e]{width:%?480?%;height:%?854?%}.poster-layer .msg[data-v-2d8e623e]{padding:%?40?%}.poster-layer .save[data-v-2d8e623e]{text-align:center;height:%?80?%;line-height:%?80?%}.poster-layer .close[data-v-2d8e623e]{position:absolute;top:0;right:%?20?%;width:%?40?%;height:%?80?%;font-size:%?50?%}.goods-details img[data-v-2d8e623e]{max-width:100%}.wechat-share[data-v-2d8e623e]{position:fixed;width:100vw;height:100vh;left:0;top:0;background-color:rgba(0,0,0,.6)}.wechat-share uni-image[data-v-2d8e623e]{width:100%}',""]),e.exports=t},c468:function(e,t,a){"use strict";var o=a("08a5"),n=a.n(o);n.a},cc1b:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(e){return-1!==["dark","light"].indexOf(e)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var e=this,t=getCurrentPages()[0];this.$pageVm=t.$vm||t,uni.onWindowResize((function(t){e.$emit("resize",t)})),this.$pageVm.$on("hook:onPageScroll",(function(t){e.$emit("scroll",t)})),this.$watch("backgroundTextStyle",(function(){e.setBackgroundTextStyle()})),this.$watch((function(){return[e.rootFontSize,e.pageStyle]}),(function(){e.setPageMeta()})),this.$watch((function(){return[e.backgroundColor,e.backgroundColorTop,e.backgroundColorBottom]}),(function(){e.setBackgroundColor()})),this.$watch((function(){return[e.scrollTop,e.scrollDuration]}),(function(){e.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(e,t){e.setStyle({pullToRefresh:{support:t,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var e=this,t=String(this.scrollTop);if(-1!==t.indexOf("rpx")&&(t=uni.upx2px(t.replace("rpx",""))),t=parseFloat(t),!isNaN(t)){var a=function a(n){n.scrollTop===t&&(e.$pageVm.$off("hook:onPageScroll",a),e.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:t,duration:this.scrollDuration,success:function(){e.$pageVm.$on("hook:onPageScroll",a)}})}}}};t.default=n},f48d:function(e,t,a){"use strict";a.r(t);var o=a("cc1b"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a}}]);