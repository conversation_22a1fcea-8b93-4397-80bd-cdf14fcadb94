(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-cardservice-card-my_detail"],{"025c":function(t,e,a){var i=a("a8c4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=a("967d").default;r("b6cdf110",i,!0,{sourceMap:!1,shadowMode:!1})},"0817":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("5c47"),a("2c10"),a("a1c1"),a("23f4"),a("7d2f"),a("9c4e"),a("ab80"),a("aa9c"),a("473f"),a("bf0f"),a("3efd");var r=i(a("af87")),n=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,o=/^<\/([-A-Za-z0-9_]+)[^>]*>/,c=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,d=g("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),s=g("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),l=g("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),u=g("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),f=g("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),p=g("script,style");function g(t){for(var e={},a=t.split(","),i=0;i<a.length;i++)e[a[i]]=!0;return e}var v=function(t){t=function(t){return t.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(t),t=function(t){return t=t.replace(/<!--[\s\S]*-->/gi,""),t}(t),t=function(t){var e='<img style="width:100% !important;display:block;max-width: '.concat("100%",' !important;"');return t=t.replace(/\\/g,"").replace(/<img/g,e),t=t.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(t,a){return e+' src="'+r.default.img(a)+'"/>'})),t}(t),t=function(t){return t=t.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(t,e){return t=t.replace(/[:](\s?)[\s\S]*/gi,(function(t,e){return t.replace(/"/g,"'")})),t})),t}(t);var e=[],a={node:"root",children:[]};return function(t,e){var a,i,r,g=[],v=t;g.last=function(){return this[this.length-1]};while(t){if(i=!0,g.last()&&p[g.last()])t=t.replace(new RegExp("([\\s\\S]*?)</"+g.last()+"[^>]*>"),(function(t,a){return a=a.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),e.chars&&e.chars(a),""})),m("",g.last());else if(0==t.indexOf("\x3c!--")?(a=t.indexOf("--\x3e"),a>=0&&(e.comment&&e.comment(t.substring(4,a)),t=t.substring(a+3),i=!1)):0==t.indexOf("</")?(r=t.match(o),r&&(t=t.substring(r[0].length),r[0].replace(o,m),i=!1)):0==t.indexOf("<")&&(r=t.match(n),r&&(t=t.substring(r[0].length),r[0].replace(n,b),i=!1)),i){a=t.indexOf("<");var h=a<0?t:t.substring(0,a);t=a<0?"":t.substring(a),e.chars&&e.chars(h)}if(t==v)throw"Parse Error: "+t;v=t}function b(t,a,i,r){if(a=a.toLowerCase(),s[a])while(g.last()&&l[g.last()])m("",g.last());if(u[a]&&g.last()==a&&m("",a),r=d[a]||!!r,r||g.push(a),e.start){var n=[];i.replace(c,(function(t,e){var a=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:f[e]?e:"";n.push({name:e,value:a,escaped:a.replace(/(^|[^\\])"/g,'$1\\"')})})),e.start&&e.start(a,n,r)}}function m(t,a){if(a){for(i=g.length-1;i>=0;i--)if(g[i]==a)break}else var i=0;if(i>=0){for(var r=g.length-1;r>=i;r--)e.end&&e.end(g[r]);g.length=i}}m()}(t,{start:function(t,i,r){var n={name:t};if(0!==i.length&&(n.attrs=function(t){return t.reduce((function(t,e){var a=e.value,i=e.name;return t[i]?t[i]=t[i]+" "+a:t[i]=a,t}),{})}(i)),r){var o=e[0]||a;o.children||(o.children=[]),o.children.push(n)}else e.unshift(n)},end:function(t){var i=e.shift();if(i.name!==t&&console.error("invalid state: mismatch end tag"),0===e.length)a.children.push(i);else{var r=e[0];r.children||(r.children=[]),r.children.push(i)}},chars:function(t){var i={type:"text",text:t};if(0===e.length)a.children.push(i);else{var r=e[0];r.children||(r.children=[]),r.children.push(i)}},comment:function(t){var a={node:"comment",text:t},i=e[0];i.children||(i.children=[]),i.children.push(a)}}),a.children};e.default=v},"248e":function(t,e,a){"use strict";var i=a("025c"),r=a.n(i);r.a},"3a7e":function(t,e,a){"use strict";a.r(e);var i=a("d028"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=r.a},7854:function(t,e,a){"use strict";a.r(e);var i=a("8ba8"),r=a("f48d");for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(n);var o=a("828b"),c=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=c.exports},"81c3":function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={pageMeta:a("7854").default,nsMpHtml:a("d108").default,uniPopup:a("d745").default,nsLogin:a("2910").default,loadingCover:a("c003").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-view",{staticClass:"page"},[Object.keys(t.cardDetail).length?a("v-uni-view",{staticClass:"detail-wrap"},[a("v-uni-view",{staticClass:"goods-item",style:{backgroundImage:"url("+t.$util.img(t.cardDetail.goods_image||"public/uniapp/cardservice/card_bg.png")+")"}},[a("v-uni-view",{staticClass:"conten"},[a("v-uni-view",{staticClass:"name using-hidden"},[t._v(t._s(t.cardDetail.goods_name))]),a("v-uni-view",{staticClass:"desc using-hidden"},[t._v(t._s(t.cardDetail.introduction))]),a("v-uni-view",{staticClass:"time-info",class:0==t.cardDetail.status?"warning":""},[1==t.cardDetail.status?a("v-uni-text",{staticClass:"indate-time"},[t._v(t._s(0==parseInt(t.cardDetail.end_time)?"长期有效":"至 "+t.$util.timeStampTurnTime(t.cardDetail.end_time,"Y-m-d")))]):a("v-uni-text",[t._v(t._s(t.cardDetail.invalid_reason||"已失效"))])],1)],1)],1),t.cardDetail.card_item.length?a("v-uni-view",{staticClass:"card-info"},[a("v-uni-view",{staticClass:"card-title"},[t._v("- 套餐包含以下的服务及商品 -")]),"commoncard"==t.cardDetail.card_type?a("v-uni-view",{staticClass:"card-desc"},[t._v("卡项内项目/商品总的可用次数为"+t._s(t.cardDetail.total_num)+"次，剩余"+t._s(t.cardDetail.total_num-t.cardDetail.total_use_num)+"次可用")]):t._e(),a("v-uni-view",{staticClass:"card-content"},t._l(t.cardDetail.card_item,(function(e,i){return a("v-uni-view",{staticClass:"card-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.$util.redirectTo("/pages/goods/detail",{goods_id:e.goods_id})}}},[a("v-uni-image",{attrs:{src:t.$util.img(e.sku_image),mode:"aspectFill"}}),a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"name multi-hidden"},[t._v(t._s(e.sku_name))]),"oncecard"==t.cardDetail.card_type?a("v-uni-view",{staticClass:"total-num"},[t._v("总次数："+t._s(e.num))]):t._e(),["timecard","oncecard"].includes(t.cardDetail.card_type)?a("v-uni-text",{staticClass:"total-use-num"},[t._v(t._s("timecard"==t.cardDetail.card_type?"使用次数：不限次数":"已使用次数："+e.use_num))]):t._e()],1),1==t.cardDetail.status&&("commoncard"==t.cardDetail.card_type&&t.cardDetail.total_num||"commoncard"!=t.cardDetail.card_type&&e.num-e.use_num>0)?a("v-uni-button",{class:["button",{"charge-off":4==e.goods_class},{"pick-goods":1==e.goods_class}],attrs:{type:"default"},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.toUseFn(e)}}},[t._v(t._s(4==e.goods_class?"去核销":1==e.goods_class&&"去提货"))]):t._e()],1)})),1),a("v-uni-view",{staticClass:"card-off",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_promotion/cardservice/card/card_record",{card_id:t.cardDetail.card_id})}}},[a("v-uni-text",[t._v("使用记录")]),a("v-uni-text",{staticClass:"icondiy icon-system-jiantouyou"})],1)],1):t._e(),t.cardDetail.goods_content?a("v-uni-view",{staticClass:"introduction"},[a("v-uni-view",{staticClass:"title"},[t._v("- 商品详情 -")]),a("v-uni-view",{staticClass:"content"},[a("ns-mp-html",{attrs:{content:t.cardDetail.goods_content}})],1)],1):t._e(),a("uni-popup",{ref:"chargeOffPopup",staticClass:"charge-off",attrs:{type:"center","mask-click":!1}},[a("v-uni-view",{staticClass:"charge-off-wrap",style:{backgroundImage:"url("+t.$util.img("public/uniapp/cardservice/verify_bg.png")+")"}},[a("v-uni-view",{staticClass:"code"},[a("v-uni-image",{staticClass:"barcode",attrs:{src:t.virtualData.barcode,mode:"aspectFit"}}),a("v-uni-image",{staticClass:"qrcode",attrs:{src:t.virtualData.verify_code_path,mode:"aspectFit"}})],1),a("v-uni-view",{staticClass:"qrcode-desc"},[t._v("向收银员展示此核销码")]),a("v-uni-view",{staticClass:"charge-list"},[a("v-uni-view",{staticClass:"charge-item"},[a("v-uni-text",[t._v("名称")]),a("v-uni-text",[t._v(t._s(t.virtualData.name))])],1),a("v-uni-view",{staticClass:"charge-item"},[a("v-uni-text",[t._v("剩余次数")]),a("v-uni-text",[t._v("x "+t._s(t.virtualData.num))])],1),a("v-uni-view",{staticClass:"charge-item"},[a("v-uni-text",[t._v("核销码")]),a("v-uni-text",{staticClass:"color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.virtualData.verify_code)}}},[t._v("点击复制")])],1),a("v-uni-view",{staticClass:"charge-item"},[a("v-uni-text",[t._v("有效期")]),a("v-uni-text",[t._v(t._s(t.virtualData.time))])],1)],1),a("v-uni-view",{staticClass:"close icondiy icon-system-guanbi",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.chargeOffPopup.close()}}})],1)],1)],1):t._e(),a("ns-login",{ref:"login"}),a("loading-cover",{ref:"loadingCover"})],1)],1)},n=[]},"8ba8":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},r=[]},"9aec":function(t,e,a){"use strict";a.r(e);var i=a("81c3"),r=a("3a7e");for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(n);a("248e");var o=a("828b"),c=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"1a1b6b20",null,!1,i["a"],void 0);e["default"]=c.exports},a8c4:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.detail-wrap[data-v-1a1b6b20]{overflow:hidden;padding-top:%?24?%}.detail-wrap .goods-item[data-v-1a1b6b20]{position:relative;border-radius:%?18?%;height:%?320?%;margin:0 %?30?% %?24?%;background-repeat:no-repeat;background-size:cover;box-sizing:border-box;background-position:50%}.detail-wrap .goods-item .conten[data-v-1a1b6b20]{border-radius:%?18?%;width:100%;height:100%;padding:%?44?% %?36?%;box-sizing:border-box;display:flex;flex-direction:column;justify-content:space-between;background-color:rgba(0,0,0,.4)}.detail-wrap .goods-item .conten .name[data-v-1a1b6b20]{color:#fff;font-size:%?34?%;font-weight:700;line-height:1}.detail-wrap .goods-item .conten .desc[data-v-1a1b6b20]{height:%?52?%;color:#fff;font-size:%?28?%;margin-top:%?20?%}.detail-wrap .goods-item .conten .time-info[data-v-1a1b6b20]{margin-top:%?20?%;display:flex;justify-content:flex-end;color:#fff;font-size:%?24?%;line-height:1}.detail-wrap .goods-item .conten .time-info.warning[data-v-1a1b6b20]{color:red}.detail-wrap .card-info[data-v-1a1b6b20]{margin:0 %?24?% %?24?%;padding:0 %?24?% %?30?%;background-color:#fff;border-radius:%?18?%;box-sizing:border-box}.detail-wrap .card-info .card-title[data-v-1a1b6b20]{padding-top:%?24?%;text-align:center;font-weight:700}.detail-wrap .card-info .card-desc[data-v-1a1b6b20]{padding-left:%?10?%;font-size:%?24?%;color:#606266}.detail-wrap .card-info .card-content[data-v-1a1b6b20]{margin-top:%?20?%;overflow:hidden}.detail-wrap .card-info .card-item[data-v-1a1b6b20]{position:relative;margin-bottom:%?28?%;display:flex;padding:%?20?%;background-color:#fbf9fc;border-radius:%?12?%}.detail-wrap .card-info .card-item[data-v-1a1b6b20]:last-of-type{margin-bottom:0}.detail-wrap .card-info .card-item uni-image[data-v-1a1b6b20]{overflow:hidden;margin-right:%?24?%;width:%?160?%;height:%?160?%;border-radius:%?10?%}.detail-wrap .card-info .card-item .content[data-v-1a1b6b20]{position:relative;flex:1;display:flex;flex-direction:column}.detail-wrap .card-info .card-item .content .name[data-v-1a1b6b20]{padding-right:%?30?%;font-weight:700;line-height:1.3}.detail-wrap .card-info .card-item .content .total-num[data-v-1a1b6b20]{margin-top:auto;line-height:1.5}.detail-wrap .card-info .card-item .content .total-use-num[data-v-1a1b6b20],\r\n.detail-wrap .card-info .card-item .content .total-num[data-v-1a1b6b20]{font-size:%?24?%;color:#666}.detail-wrap .card-info .card-item .button[data-v-1a1b6b20]{position:absolute;top:70%;right:%?10?%;-webkit-transform:translateY(-50%);transform:translateY(-50%);padding:0;margin:0;width:%?128?%;height:%?48?%;line-height:%?46?%;border-radius:%?50?%}.detail-wrap .card-info .card-item .button.charge-off[data-v-1a1b6b20]{color:var(--base-color);border:%?2?% solid var(--base-color);font-size:%?24?%}.detail-wrap .card-info .card-item .button.pick-goods[data-v-1a1b6b20]{color:#fff;background-color:var(--base-color);font-size:%?24?%}.detail-wrap .card-info .card-off[data-v-1a1b6b20]{display:flex;justify-content:center;align-items:center;margin:%?30?% 0 %?10?%;line-height:1}.detail-wrap .card-info .card-off uni-text[data-v-1a1b6b20]{font-size:%?24?%;color:#888}.detail-wrap .card-info .card-off uni-text[data-v-1a1b6b20]:last-of-type{margin-left:%?6?%;font-size:%?24?%}.detail-wrap .introduction[data-v-1a1b6b20]{overflow:hidden;margin:0 %?24?% %?24?%;padding:0 %?24?% %?30?%;background-color:#fff;border-radius:%?18?%;box-sizing:border-box;margin-bottom:%?40?%}.detail-wrap .introduction .title[data-v-1a1b6b20]{margin-top:%?24?%;text-align:center;font-size:%?30?%;font-weight:700}.detail-wrap .introduction .content[data-v-1a1b6b20]{margin-top:%?30?%;overflow:hidden;word-break:break-all}.detail-wrap .introduction .content *[data-v-1a1b6b20]{max-width:100%;word-break:break-all}[data-v-1a1b6b20] .uni-popup__wrapper-box{max-width:%?630?%!important}.charge-off[data-v-1a1b6b20] .uni-popup__wrapper-box{overflow:initial!important;background-color:initial!important}.charge-off .charge-off-wrap[data-v-1a1b6b20]{padding:%?20?% %?40?%;width:%?630?%;height:%?800?%;box-sizing:border-box;background-size:contain;background-repeat:no-repeat;background-position:50%}.charge-off .charge-off-wrap .code[data-v-1a1b6b20]{display:flex;flex-direction:column;align-items:center;justify-content:center}.charge-off .charge-off-wrap .code .barcode[data-v-1a1b6b20]{width:%?480?%;height:%?100?%}.charge-off .charge-off-wrap .code .qrcode[data-v-1a1b6b20]{margin-top:%?50?%;width:%?330?%;height:%?330?%}.charge-off .charge-off-wrap .qrcode-desc[data-v-1a1b6b20]{margin:%?10?% 0;text-align:center;font-size:%?30?%;color:#666}.charge-off .charge-off-wrap .charge-list[data-v-1a1b6b20]{margin-top:%?44?%;font-size:%?24?%}.charge-off .charge-off-wrap .charge-list .charge-item[data-v-1a1b6b20]{display:flex;justify-content:space-between;align-items:center;height:%?50?%;line-height:%?50?%}.charge-off .charge-off-wrap .charge-list .charge-item uni-text[data-v-1a1b6b20]:first-of-type{color:#666}.charge-off .charge-off-wrap .close[data-v-1a1b6b20]{position:absolute;left:50%;bottom:%?-146?%;-webkit-transform:translateX(-50%);transform:translateX(-50%);color:#fff;font-size:30px}',""]),t.exports=e},cc1b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},r={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var a=function a(r){r.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",a),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",a)}})}}}};e.default=r},d028:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;i(a("0817"));var r={data:function(){return{cardId:0,cardDetail:{},emptyShow:!1,virtualData:{name:"",num:"",time:"",barcode:""}}},onLoad:function(t){this.cardId=t.card_id},onShow:function(){this.getData(this.cardId)},methods:{getData:function(t){var e=this;this.$api.sendRequest({url:"/cardservice/api/membercard/detail",data:{card_id:t},success:function(t){t.code>=0?(e.cardDetail=t.data,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):(e.$util.showToast({title:"未获取到卡信息！"}),setTimeout((function(){e.$util.redirectTo("/pages_promotion/cardservice/card/my_card")}),1500))},fail:function(t){uni.stopPullDownRefresh(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},toUseFn:function(t){1==t.goods_class?this.$util.redirectTo("/pages_promotion/cardservice/card/pick_goods",{card_id:t.card_id}):4==t.goods_class&&(this.virtualData.name=t.sku_name,this.virtualData.num=t.num-t.use_num,this.virtualData.time=0==t.expire_time?"永久":this.$util.timeStampTurnTime(t.expire_time),this.virtualData.barcode=this.$util.img(t.barcode),this.virtualData.verify_code_path=this.$util.img(t.verify_code_data.h5.path),this.virtualData.verify_code=t.verify_code,this.$forceUpdate(),this.$refs.chargeOffPopup.open())},toDetail:function(t){if(this.storeToken)this.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id});else{this.$refs.login.open("/pages_promotion/cardservice/card/list")}}}};e.default=r},f48d:function(t,e,a){"use strict";a.r(e);var i=a("cc1b"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=r.a}}]);