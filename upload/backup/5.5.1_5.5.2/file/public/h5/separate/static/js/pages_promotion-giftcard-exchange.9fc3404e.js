(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-giftcard-exchange"],{"1e4b":function(t,e,a){"use strict";a.r(e);var n=a("b6a1e"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},2319:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.exchange-card-box[data-v-63fd323a]{height:100vh;background-size:100% auto;background-repeat:no-repeat;min-height:100vh;background-color:#f6f9ff}.exchange-card-box .card-box[data-v-63fd323a]{background:#fff;position:relative;top:%?320?%;width:90%;margin:0 auto;border-radius:%?10?%;padding-bottom:10px}.exchange-card-box .card-box .card-title[data-v-63fd323a]{text-align:center;font-size:%?32?%;padding:%?20?% 0}.exchange-card-box .card-box .card-input[data-v-63fd323a]{background:#f8f8f8;width:80%;margin:0 auto;margin-bottom:%?40?%;padding-left:%?86?%;padding-right:%?20?%;box-sizing:border-box;position:relative}.exchange-card-box .card-box .card-input .uni-input[data-v-63fd323a]{height:%?88?%}.exchange-card-box .card-box .card-input uni-image[data-v-63fd323a]{position:absolute;width:%?40?%;max-height:%?40?%;left:%?25?%;top:%?25?%}.cate-image[data-v-63fd323a]{display:flex}.cate-image uni-image[data-v-63fd323a]{width:%?170?%;height:%?116?%;margin:%?274?% auto %?100?% auto}.cate-search[data-v-63fd323a]{width:100%;height:%?80?%;padding:%?10?% %?30?%;box-sizing:border-box;padding-top:%?30?%}.cate-search uni-input[data-v-63fd323a]{font-size:%?28?%;height:%?80?%;padding:%?15?% %?25?% %?15?% %?30?%;line-height:%?60?%;width:calc(100% - %?120?%)}.cate-search .search-box[data-v-63fd323a]{width:100%;background:#fff;text-align:center;display:flex;justify-content:center;align-items:center;border-radius:%?20?%}.cate-search .input-placeholder[data-v-63fd323a]{text-align:center}.exchange-btn[data-v-63fd323a]{width:80%;margin:%?100?% auto 0}.exchange-btn uni-button[data-v-63fd323a]{width:100%;margin:0}.exchange-list[data-v-63fd323a]{color:#1a1ff1;margin:%?60?% auto;text-align:center}',""]),t.exports=e},"2bcc":function(t,e,a){"use strict";a.r(e);var n=a("e882"),i=a("1e4b");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("51f5");var r=a("828b"),c=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"63fd323a",null,!1,n["a"],void 0);e["default"]=c.exports},"51f5":function(t,e,a){"use strict";var n=a("961d"),i=a.n(n);i.a},7854:function(t,e,a){"use strict";a.r(e);var n=a("8ba8"),i=a("f48d");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);var r=a("828b"),c=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=c.exports},"8ba8":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},i=[]},"961d":function(t,e,a){var n=a("2319");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("4d889713",n,!0,{sourceMap:!1,shadowMode:!1})},b6a1e:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{cardNum:"",card:"",isRepeat:!1}},onLoad:function(){var t=this;this.storeToken||setTimeout((function(){t.$refs.login.open()}),500)},onShow:function(){},methods:{onInput:function(t){var e=t.target.value;this.cardNum=e},onInputTo:function(t){var e=t.target.value;this.card=e},goToExchange:function(){this.$util.redirectTo("/pages_promotion/giftcard/list")},exchange:function(){var t=this;this.isRepeat||(this.isRepeat=!0,this.$api.sendRequest({url:"/giftcard/api/activate/activate",data:{card_no:this.cardNum,card_cdk:this.card},success:function(e){e.code>=0?e.data?(t.$util.showToast({title:"兑换成功"}),setTimeout((function(){t.$util.redirectTo("/pages_promotion/giftcard/card_info",{member_card_id:e.data},"redirectTo")}),1500)):t.$util.showToast({title:"卡号或密码错误，请重新输入"}):t.$util.showToast({title:e.message}),t.isRepeat=!1}}))}}};e.default=n},cc1b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},i={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var a=function a(i){i.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",a),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",a)}})}}}};e.default=i},e882:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={pageMeta:a("7854").default,nsLogin:a("2910").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-view",{staticClass:"exchange-card-box",style:{backgroundImage:"url("+t.$util.img("public/uniapp/giftcard/exchange-bg.png")+")"}},[a("v-uni-view",{staticClass:"card-box"},[a("v-uni-view",{staticClass:"card-title"},[t._v("卡密兑换")]),a("v-uni-view",{staticClass:"card-input"},[a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/giftcard/exchange-icon-account.png"),mode:"widthFix"}}),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入您的卡号码",value:t.cardNum},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"card-input"},[a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/giftcard/exchange-icon-pwd.png"),mode:"widthFix"}}),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入您的卡密码",value:t.card},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInputTo.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"exchange-btn"},[a("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.exchange()}}},[t._v("立即兑换")])],1),a("v-uni-view",{staticClass:"exchange-list",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goToExchange()}}},[t._v("我的兑换列表")])],1),a("ns-login",{ref:"login"})],1)],1)},o=[]},f48d:function(t,e,a){"use strict";a.r(e);var n=a("cc1b"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a}}]);