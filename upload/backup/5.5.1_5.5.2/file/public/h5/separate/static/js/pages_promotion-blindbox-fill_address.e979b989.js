(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-blindbox-fill_address"],{"0597":function(e,t,i){"use strict";i.r(t);var o=i("4f87"),a=i("5e6e");for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);i("5532"),i("7016");var s=i("828b"),n=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,"4b78b627",null,!1,o["a"],void 0);t["default"]=n.exports},3672:function(e,t,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("bf0f"),i("de6c"),i("5c47"),i("a1c1"),i("64aa"),i("e966"),i("5ef2"),i("c9b5"),i("ab80"),i("aa9c"),i("d4b5"),i("2797");var a=o(i("d745")),r={name:"nsSelectTime",components:{uniPopup:a.default},data:function(){return{key:0,keys:0,obj:{},dayData:[],timeData:[],judge:!1,keyJudge:0,dayTime:0}},methods:{refresh:function(){this.key=0,this.keys=0,this.keyJudge=0},open:function(e,t){this.dayData=[],this.timeData=[],this.obj=e,this.toDay(e.dataTime.time_type,e.dataTime.time_week),this.judge&&("no"==t?this.selectTime("","",t):this.$refs.selectTime.open())},selectTime:function(e,t,i){if("days"==e)this.keyJudge=t,this.toTime();else if("time"==e){this.keys=t,this.key=this.keyJudge;var o=this.dayData[this.key];o.time=this.timeData[this.keys];var a=o.time.replace("立即配送（","").replace("）",""),r=new Date,s=a.split("-"),n=s[0].split(":"),d=s[1].split(":"),l=o.month.split("月"),c=l[0],b=l[1].split("日")[0];r.setHours(n[0],n[1],0,0),o.start_time=r.getTime()/1e3,o.start_date=r.getFullYear()+"-"+c+"-"+b+" "+s[0],r.setHours(d[0],d[1],0,0),o.end_time=r.getTime()/1e3,o.end_date=r.getFullYear()+"-"+c+"-"+b+" "+s[1],this.$emit("selectTime",{data:o,type:i}),this.$refs.selectTime.close()}if("no"==i){this.toTime(i);var p=this.dayData[0];p.time=this.timeData[0];var u=new Date,v=p.time.replace("立即配送（","").replace("）","").split("-"),f=v[0].split(":"),m=v[1].split(":"),g=p.month.split("月"),h=g[0],y=g[1].split("日")[0];u.setHours(f[0],f[1],0,0),p.start_time=u.getTime()/1e3,p.start_date=u.getFullYear()+"-"+h+"-"+y+" "+v[0],u.setHours(m[0],m[1],0,0),p.end_time=u.getTime()/1e3,p.end_date=u.getFullYear()+"-"+h+"-"+y+" "+v[1],this.$emit("selectTime",{data:p,type:i})}this.$forceUpdate()},close:function(){this.$refs.selectTime.close()},toDay:function(e,t){var i=new Date;this.obj.dataTime.advance_day&&(i=new Date(i.getTime()+864e5*this.obj.dataTime.advance_day));var o=i.getFullYear(),a=i.getMonth()+1,r=i.getDate(),s=i.getDay(),n=new Date(o,a,0).getDate(),d=i.getHours(),l=i.getMinutes();this.dayTime=this.obj.dataTime.advance_day?0:3600*Number(d)+60*Number(l);var c=!1,b=1,p=this.obj.dataTime.most_day?this.obj.dataTime.most_day+1:1,u=parseInt(i.getTime()/1e3),v=["周日","周一","周二","周三","周四","周五","周六"];t.time_week&&7==t.time_week.length&&(c=!0);for(var f=0;f<p;f++){var m={},g=v[s];if(this.obj.dataTime.most_day>0&&u+86400*b>u+86400*this.obj.dataTime.most_day){this.judge=!0;break}if(0==e||c||-1!=t.indexOf(s.toString())){var h=this.obj.dataTime.delivery_time[this.obj.dataTime.delivery_time.length-1].end_time;switch(h-=60*this.obj.dataTime.time_interval,b){case 1:0==f&&(h<this.dayTime?f-=1:(m={title:0==this.obj.dataTime.advance_day?"今天":"",type:"special",month:a+"月"+r+"日",Day:"("+g+")"},this.dayData.push(m)));break;case 2:0!=f&&1!=f||(m={title:0==this.obj.dataTime.advance_day?"明天":"",month:a+"月"+r+"日",Day:"("+g+")"},this.dayData.push(m));break;default:m={title:"",month:a+"月"+r+"日",Day:"("+g+")"},this.dayData.push(m)}}else f-=1;r!=n?r+=1:(12!=a?a+=1:a=1,r=1),6!=s?s+=1:s=0,b+=1,0==this.obj.dataTime.most_day&&0==f&&(this.judge=!0)}this.toTime()},toTime:function(e){var t=this;"no"==e&&(this.key=0,this.keys=0,this.keyJudge=0);var i=[];this.obj.dataTime.delivery_time||(this.obj.dataTime.delivery_time=[{start_time:this.obj.dataTime.start_time,end_time:this.obj.dataTime.end_time}]);var o=JSON.parse(JSON.stringify(this.dayTime)),a=!1;this.dayData[this.keyJudge]&&this.dayData[this.keyJudge].type&&o>this.obj.dataTime.start_time&&(a=!0);var r=this.obj.dataTime.time_interval?60*this.obj.dataTime.time_interval:1200;this.obj.dataTime.delivery_time.forEach((function(e){e.end_time=e.end_time?e.end_time:86400;for(var s=parseInt((parseInt(e.end_time)-parseInt(e.start_time))/r),n=a?parseInt(o):parseInt(e.start_time),d=0;d<s;d++){if(parseInt(n)+parseInt(r)>e.end_time)break;if(a){if(n>=o)if(t.obj.dataTime.time_interval){if(n<=e.end_time){var l="";l="local"==t.obj.delivery.delivery_type&&0==d?"立即配送（"+t.$util.getTimeStr(n)+"-"+t.$util.getTimeStr(n+r)+"）":t.$util.getTimeStr(n)+"-"+t.$util.getTimeStr(n+r),i.push(l)}}else i.push(t.$util.getTimeStr(n))}else t.obj.dataTime.time_interval?n<=e.end_time&&i.push(t.$util.getTimeStr(n)+"-"+t.$util.getTimeStr(n+r)):i.push(t.$util.getTimeStr(n));n=parseInt(n)+r}})),this.timeData=i,this.$forceUpdate()}}};t.default=r},"4a0d":function(e,t,i){var o=i("574c");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var a=i("967d").default;a("6b267ede",o,!0,{sourceMap:!1,shadowMode:!1})},"4f1c":function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-4b78b627] uni-input,[data-v-4b78b627] uni-view{font-size:%?24?%}.font-bold[data-v-4b78b627]{font-weight:700}.order-container[data-v-4b78b627]{width:100vw;height:100vh;display:flex;flex-direction:column;background:linear-gradient(180deg,var(--base-color) 10%,#f8f8f8);background-size:100% %?260?%;background-repeat:no-repeat}.order-container .order-scroll-container[data-v-4b78b627]{width:100%;height:0;flex:1}.order-container .payment-navbar-block[data-v-4b78b627]{height:%?60?%}.payment-navbar[data-v-4b78b627]{width:100vw;padding-bottom:%?20?%;position:fixed;left:0;top:0;z-index:100;background:linear-gradient(180deg,var(--base-color) 10%,#f8f8f8);background-size:100% %?260?%}.payment-navbar .nav-wrap[data-v-4b78b627]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;box-sizing:border-box;position:relative}.payment-navbar .navbar-title[data-v-4b78b627]{color:#fff;font-size:%?32?%}.payment-navbar .icon-back_light[data-v-4b78b627]{color:#fff;position:absolute;left:%?24?%;font-size:%?40?%}.payment-navbar-block[data-v-4b78b627]{padding-bottom:%?20?%}.mobile-wrap[data-v-4b78b627]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%}.mobile-wrap .tips[data-v-4b78b627]{font-size:%?22?%;margin-bottom:%?30?%;background:var(--main-color-shallow);border-radius:%?10?%;padding:%?20?% %?30?%;line-height:1;display:flex;align-items:center}.mobile-wrap .tips .iconfont[data-v-4b78b627]{margin-right:%?5?%}.mobile-wrap.local-mobile[data-v-4b78b627]{border-bottom:%?2?% solid #f4f4f6;margin:0}.mobile-wrap.store-mobile[data-v-4b78b627]{border-top:%?2?% solid #f4f4f6;margin:%?20?% 0 0 0;padding:%?20?% 0;border-radius:0}.mobile-wrap .form-group[data-v-4b78b627]{display:flex;align-items:center;width:100%}.mobile-wrap .form-group .iconfont[data-v-4b78b627]{margin-right:%?26?%;font-size:%?32?%}.mobile-wrap .form-group .text[data-v-4b78b627]{display:inline-block;line-height:%?50?%;padding-right:%?10?%;font-size:%?28?%;font-weight:700}.mobile-wrap .form-group .placeholder[data-v-4b78b627]{line-height:%?50?%}.mobile-wrap .form-group .input[data-v-4b78b627]{flex:1;height:%?50?%;line-height:%?50?%;text-align:right;font-size:%?28?%}.order-cell[data-v-4b78b627]{display:flex;margin:0 0 %?30?% 0;align-items:center;background:#fff;line-height:%?40?%;position:relative}.order-cell.clear-flex[data-v-4b78b627]{display:block}.order-cell.textarea-box[data-v-4b78b627]{display:flex;align-items:baseline;font-size:%?28?%}.order-cell uni-text[data-v-4b78b627]{font-size:%?28?%}.order-cell .tit[data-v-4b78b627]{text-align:left;font-size:%?28?%;min-width:%?160?%;color:#000;font-weight:700}.order-cell .tit uni-text[data-v-4b78b627]{font-size:%?28?%}.order-cell .tit .tit-content[data-v-4b78b627]{max-width:%?540?%;font-size:%?24?%;line-height:%?35?%;margin-bottom:%?10?%}.order-cell .box[data-v-4b78b627]{flex:1;padding:0 %?10?%;line-height:inherit;text-align:right}.order-cell .box.text-overflow[data-v-4b78b627]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.order-cell .box.text-overflow .money[data-v-4b78b627]{overflow:hidden;max-width:40%;display:inline-block;text-overflow:ellipsis;vertical-align:top}.order-cell .box .icon-right[data-v-4b78b627]{color:#303133;margin-left:%?20?%}.order-cell .box .operator[data-v-4b78b627]{font-size:%?24?%;margin-right:%?6?%;font-weight:700;color:var(--price-color)}.order-cell .box uni-textarea[data-v-4b78b627]{width:auto;height:%?88?%;font-size:%?28?%}.order-cell .iconfont[data-v-4b78b627]{color:#909399;line-height:normal;font-size:%?24?%}.order-cell .unit[data-v-4b78b627]{margin-right:%?4?%;font-weight:700;font-size:%?28?%!important;margin-left:%?4?%;color:var(--price-color)}.order-cell .money[data-v-4b78b627]{font-size:%?28?%!important;font-weight:700;color:var(--price-color)}.site-wrap[data-v-4b78b627]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;padding:%?40?% 0}.site-wrap.order-goods[data-v-4b78b627]{padding:0}.site-wrap .site-body[data-v-4b78b627]{margin:0 %?24?%}.site-wrap .site-body .goods-item[data-v-4b78b627]{border-bottom:%?2?% solid #f4f4f6}.site-wrap .site-body .goods-item[data-v-4b78b627]:last-child{border-bottom:0}.site-wrap .site-body .goods-item .error-tips[data-v-4b78b627]{color:#ff443f;padding:%?10?% %?20?%;display:inline-flex;align-items:center;line-height:1;background:#ffecec;margin-top:%?20?%;border-radius:%?12?%;width:auto}.site-wrap .site-body .goods-item .error-tips .iconfont[data-v-4b78b627]{margin-right:%?10?%}.site-wrap .site-body .goods-wrap[data-v-4b78b627]{display:flex;position:relative;padding:%?30?% 0}.site-wrap .site-body .goods-wrap .goods-img[data-v-4b78b627]{width:%?180?%;height:%?180?%;margin-right:%?20?%;border-radius:%?10?%;overflow:hidden}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-4b78b627]{width:100%;height:100%;border-radius:%?10?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-4b78b627]{flex:1;position:relative;width:0;margin-top:%?-4?%;display:flex;flex-direction:column;justify-content:space-between}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-4b78b627]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;font-weight:700}.site-wrap .site-body .goods-wrap .goods-info .sku[data-v-4b78b627]{display:flex;line-height:1;margin-top:%?8?%}.site-wrap .site-body .goods-wrap .goods-info .sku .goods-spec[data-v-4b78b627]{color:#909399;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1;display:flex}.site-wrap .site-body .goods-wrap .goods-info .sku .goods-spec uni-view[data-v-4b78b627]{background-color:#f4f4f4;color:#666;padding:%?6?% %?10?%;margin-right:%?12?%;line-height:1}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-4b78b627]{font-size:%?24?%;margin-right:%?4?%;font-weight:700;color:var(--price-color)}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-4b78b627]{font-weight:700;font-size:%?32?%;color:var(--price-color)}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-4b78b627]:first-of-type{width:80%;overflow:hidden;text-overflow:ellipsis}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-4b78b627]:last-of-type{text-align:right;position:absolute;right:0;bottom:0;font-weight:700}.site-wrap .site-footer[data-v-4b78b627]{margin:0 %?24?% 0}.site-wrap .site-footer .order-cell[data-v-4b78b627]:last-of-type{margin-bottom:0}[data-v-4b78b627] .goods-form{display:flex;align-items:center;position:relative}[data-v-4b78b627] .goods-form ns-form{display:flex;width:100%}[data-v-4b78b627] .goods-form .shade{position:absolute;left:0;top:0;width:100%;height:100%;z-index:5}[data-v-4b78b627] .goods-form .cell-more{margin-left:%?50?%;float:right;color:#909399;font-size:%?24?%}[data-v-4b78b627] .goods-form .form-wrap{flex:1;width:0}[data-v-4b78b627] .goods-form .form-wrap .icon-right{display:none}[data-v-4b78b627] .goods-form .form-wrap > uni-view,[data-v-4b78b627] .goods-form .form-wrap > uni-picker{display:none}[data-v-4b78b627] .goods-form .form-wrap > uni-view:first-child,[data-v-4b78b627] .goods-form .form-wrap > uni-picker:first-child{display:block;border-bottom:none}[data-v-4b78b627] .goods-form .form-wrap > uni-view:first-child .required,[data-v-4b78b627] .goods-form .form-wrap > uni-picker:first-child .required{display:none}[data-v-4b78b627] .goods-form .order-cell .name{width:auto}[data-v-4b78b627] .goods-form .order-cell .tit{font-weight:700}[data-v-4b78b627] .goods-form .order-cell .tit:after{content:"："}.member-goods-card[data-v-4b78b627]{margin-bottom:0;padding-bottom:%?30?%}.member-goods-card .text[data-v-4b78b627]{margin-right:%?10?%;color:#999}.member-goods-card .price-font[data-v-4b78b627]{color:var(--price-color)}.order-money[data-v-4b78b627]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%}.order-money .order-cell[data-v-4b78b627]:last-child{margin-bottom:0}.error-message[data-v-4b78b627]{position:fixed;z-index:5;left:0;bottom:%?100?%;width:100vw;background:#f6f6cb;text-align:left;padding:%?10?% %?20?%;color:red}.order-submit[data-v-4b78b627]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?100?%;background:#fff;text-align:right;display:flex;align-items:center;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.order-submit .order-settlement-info[data-v-4b78b627]{flex:1;height:%?100?%;line-height:%?100?%;display:flex;padding-left:%?30?%;align-items:baseline}.order-submit .order-settlement-info .unit[data-v-4b78b627]{font-weight:700;font-size:%?24?%;margin-right:%?4?%;color:var(--price-color)}.order-submit .order-settlement-info .money[data-v-4b78b627]{font-weight:700;font-size:%?32?%;color:var(--price-color)}.order-submit .submit-btn[data-v-4b78b627]{height:%?80?%;margin:0 %?30?%;display:flex;justify-content:center;align-items:center}.order-submit .submit-btn uni-button[data-v-4b78b627]{line-height:%?70?%;width:%?180?%;height:%?70?%;padding:0;font-size:%?28?%;font-weight:700}.order-submit .submit-btn .no-submit[data-v-4b78b627]{width:unset;background-color:#ccc;color:#fff;padding:0 %?20?%;font-size:%?28?%}.order-submit-block[data-v-4b78b627]{height:%?120?%;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.popup[data-v-4b78b627]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-4b78b627]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-4b78b627]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-4b78b627]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-4b78b627]{height:calc(100% - %?250?%)}.popup .popup-body.store-popup[data-v-4b78b627]{height:calc(100% - %?120?%)}.popup .popup-body.safe-area[data-v-4b78b627]{height:calc(100% - %?270?%)}.popup .popup-body.store-popup.safe-area[data-v-4b78b627]{height:calc(100% - %?140?%)}.popup .popup-footer[data-v-4b78b627]{height:%?120?%}.popup .popup-footer .confirm-btn[data-v-4b78b627]{height:%?80?%;line-height:%?80?%;color:#fff;text-align:center;margin:%?20?% %?32?% %?40?%;border-radius:%?10?%;font-size:%?28?%}.popup .popup-footer .confirm-btn.color-base-bg[data-v-4b78b627]{color:var(--btn-text-color)}.popup .popup-footer.bottom-safe-area[data-v-4b78b627]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.invoice-popup[data-v-4b78b627]{height:83vh;padding:%?18?% 0;box-sizing:border-box;position:relative}.invoice-popup .invoice-close[data-v-4b78b627]{position:absolute;line-height:1;top:%?48?%;right:%?48?%;font-size:%?32?%;z-index:9}.invoice-popup .popup-body .invoice-cell[data-v-4b78b627]{padding:%?30?% 0;border-top:%?2?% solid #eee;margin:0 %?48?%}.invoice-popup .popup-body .invoice-cell[data-v-4b78b627]:first-of-type{border-top:none}.invoice-popup .popup-body .invoice-cell .tit[data-v-4b78b627]{font-size:%?28?%}.invoice-popup .popup-body .invoice-cell .option-grpup[data-v-4b78b627]{padding-top:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-4b78b627]{height:%?54?%;line-height:%?54?%;display:inline-block;font-size:%?22?%;padding:0 %?36?%;background:#f8f8f8;border:%?2?% solid #eee;border-radius:%?10?%;margin-right:%?30?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.active[data-v-4b78b627]{color:var(--btn-text-color)}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.content[data-v-4b78b627]{margin-bottom:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.content[data-v-4b78b627]:last-child{margin-bottom:0}.invoice-popup .popup-body .invoice-cell .invoice-form-group uni-input[data-v-4b78b627]{background:#f8f8f8;border-radius:%?10?%;height:%?66?%;margin-top:%?22?%;padding:0 %?32?%;font-size:%?24?%}.invoice-popup .popup-body .invoice-tops[data-v-4b78b627]{font-size:%?20?%;margin:0 %?48?%;color:#909399}.buyermessag-popup[data-v-4b78b627]{box-sizing:border-box;position:relative}.buyermessag-popup .buyermessag-close[data-v-4b78b627]{position:absolute;line-height:1;top:%?48?%;right:%?48?%;font-size:%?32?%;z-index:9}.buyermessag-popup .popup-body .buyermessag-cell[data-v-4b78b627]{padding:%?30?% 0;border-top:%?2?% solid #eee;margin:0 %?32?%}.buyermessag-popup .popup-body .buyermessag-cell[data-v-4b78b627]:first-of-type{border-top:none}.buyermessag-popup .popup-body .buyermessag-cell .buyermessag-form-group uni-textarea[data-v-4b78b627]{display:flex;align-items:baseline;font-size:%?28?%;width:100%;background-color:#f8f8f8;padding:%?20?%;box-sizing:border-box;border-radius:%?10?%}.coupon-popup[data-v-4b78b627]{height:65vh}.coupon-popup .popup-body[data-v-4b78b627]{background:#fff}.coupon-popup .coupon-empty[data-v-4b78b627]{display:flex;align-items:center;justify-content:center;height:100%;color:#909399!important}.coupon-popup .coupon-item[data-v-4b78b627]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;margin:%?20?% %?32?% 0;padding:0;position:relative;background-color:#fff2f0}.coupon-popup .coupon-item[data-v-4b78b627]:before, .coupon-popup .coupon-item[data-v-4b78b627]:after{position:absolute;content:"";background-color:#fff;top:50%;width:%?30?%;height:%?30?%;border-radius:50%;z-index:5}.coupon-popup .coupon-item[data-v-4b78b627]:before{left:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.coupon-popup .coupon-item[data-v-4b78b627]:after{right:0;-webkit-transform:translate(50%,-50%);transform:translate(50%,-50%)}.coupon-popup .coupon-item .coupon-info[data-v-4b78b627]{height:%?190?%;display:flex;width:100%;position:relative}.coupon-popup .coupon-item .coupon-info .info-wrap[data-v-4b78b627]{width:%?220?%;height:%?190?%;display:flex;justify-content:center;align-items:center;margin-right:%?20?%;background-repeat:no-repeat;background-size:100% 100%;position:relative;background:linear-gradient(270deg,var(--bg-color),var(--bg-color-shallow))}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-line[data-v-4b78b627]{position:absolute;right:0;top:0;height:100%}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money[data-v-4b78b627]{color:#fff;text-align:center;line-height:1}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money .unit[data-v-4b78b627]{font-size:%?30?%}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money .money[data-v-4b78b627]{font-size:%?60?%}.coupon-popup .coupon-item .coupon-info .info-wrap .at-least[data-v-4b78b627]{font-size:%?24?%;color:#fff;text-align:center;margin-top:%?20?%}.coupon-popup .coupon-item .coupon-info .desc-wrap[data-v-4b78b627]{flex:1;max-width:calc(100% - %?360?%)}.coupon-popup .coupon-item .coupon-info .desc-wrap uni-view[data-v-4b78b627]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.coupon-popup .coupon-item .coupon-info .desc-wrap .coupon-name[data-v-4b78b627]{margin-top:%?10?%;margin-bottom:%?4?%;font-size:%?28?%}.coupon-popup .coupon-item .coupon-info .desc-wrap .limit[data-v-4b78b627]{font-size:%?20?%}.coupon-popup .coupon-item .coupon-info .desc-wrap .time[data-v-4b78b627]{border-top:%?2?% dashed #ccc;position:absolute;bottom:%?30?%;color:#909399;padding-top:%?10?%;line-height:1.5;font-size:%?20?%}.coupon-popup .coupon-item .coupon-info .iconfont[data-v-4b78b627]{font-size:%?44?%;position:absolute;top:50%;right:%?20?%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.coupon-popup .coupon-item .coupon-info .icon-yuan_checkbox[data-v-4b78b627]{color:#909399}.promotion-popup[data-v-4b78b627]{height:40vh}.promotion-popup .order-cell[data-v-4b78b627]{margin:%?30?% %?30?%}.promotion-popup .order-cell .tit[data-v-4b78b627]{width:auto;min-width:unset}.promotion-popup .order-cell .promotion-mark[data-v-4b78b627]{padding:%?4?% %?10?%;line-height:1;border-radius:%?10?%;font-size:%?24?%;margin-right:%?10?%;color:var(--main-color);background-color:var(--main-color-shallow)}.delivery-popup[data-v-4b78b627]{height:80vh;box-sizing:border-box}.delivery-popup .delivery-content[data-v-4b78b627]{height:100%;overflow-y:scroll;padding:%?30?% 0;box-sizing:border-box}.delivery-popup .delivery-content .item-wrap[data-v-4b78b627]{padding:%?20?% 0;box-sizing:border-box;border-top:%?2?% solid #eee;display:flex;justify-content:space-between;align-items:center;margin:0 %?48?%}.delivery-popup .delivery-content .item-wrap .detail[data-v-4b78b627]{width:90%}.delivery-popup .delivery-content .item-wrap .detail .name[data-v-4b78b627]{display:flex}.delivery-popup .delivery-content .item-wrap .detail .name uni-text[data-v-4b78b627]{font-size:%?28?%}.delivery-popup .delivery-content .item-wrap .detail .info[data-v-4b78b627]{line-height:1.2}.delivery-popup .delivery-content .item-wrap .detail .info uni-view[data-v-4b78b627]{font-size:%?24?%}.delivery-popup .delivery-content .item-wrap .detail .info .close-desc[data-v-4b78b627]{color:red}.delivery-popup .delivery-content .item-wrap .icon[data-v-4b78b627]{flex:1;text-align:right;max-height:%?50?%}.delivery-popup .delivery-content .item-wrap .icon .iconfont[data-v-4b78b627]{line-height:1;font-size:%?44?%}.delivery-popup .delivery-content .item-wrap[data-v-4b78b627]:first-of-type{padding-top:0;border-top:none}.delivery-popup .delivery-content .empty[data-v-4b78b627]{text-align:center;font-size:%?24?%}.balance-switch[data-v-4b78b627]{-webkit-transform:scale(.8);transform:scale(.8)}.address-box[data-v-4b78b627]{margin:0 %?24?% 0;background-color:#fff;position:relative;overflow:hidden;border-bottom-left-radius:%?16?%;border-bottom-right-radius:%?16?%;padding:%?30?% %?24?%}.address-box.not-delivery-type[data-v-4b78b627]{border-radius:%?16?%}.address-box .address-line[data-v-4b78b627]{position:absolute;bottom:%?0?%;left:0;width:100%;height:%?6?%}.address-box .info-wrap[data-v-4b78b627]{display:flex;align-items:center}.address-box .info-wrap.local[data-v-4b78b627]{padding-bottom:%?20?%}.address-box .info-wrap .content[data-v-4b78b627]{flex:1}.address-box .info-wrap .content .name[data-v-4b78b627]{margin-right:%?10?%;font-weight:700;font-size:%?28?%}.address-box .info-wrap .content .mobile[data-v-4b78b627]{font-weight:700;font-size:%?28?%}.address-box .info-wrap .desc-wrap[data-v-4b78b627]{word-break:break-word;font-size:%?26?%;color:#666}.address-box .icon-wrap[data-v-4b78b627]{width:%?24?%;height:%?42?%;position:relative;margin-right:%?26?%;align-self:flex-start;padding-top:%?6?%}.address-box .icon-wrap.empty[data-v-4b78b627]{padding-top:0}.address-box .icon-wrap .iconfont[data-v-4b78b627]{font-size:%?32?%;display:inline-block;vertical-align:middle}.address-box .empty-wrap[data-v-4b78b627]{height:%?80?%;line-height:%?80?%;display:flex;align-items:center}.address-box .empty-wrap .info[data-v-4b78b627]{flex:1;font-size:%?28?%}.address-box .cell-more[data-v-4b78b627]{margin-left:%?50?%;float:right;color:#909399;font-size:%?24?%}.address-box .cell-more .iconfont[data-v-4b78b627]{color:#909399}.address-box .local-delivery-store[data-v-4b78b627]{display:flex;align-items:center;padding-bottom:%?20?%;margin-bottom:%?20?%;border-bottom:%?2?% solid #eee}.address-box .local-delivery-store .info[data-v-4b78b627]{flex:1;width:0;font-size:%?28?%}.address-box .local-delivery-store .store-name[data-v-4b78b627]{color:var(--base-color);margin:0 %?10?%}.address-box .local-delivery-store .cell-more[data-v-4b78b627]{font-size:%?28?%;display:flex;align-items:center}.address-box .local-delivery-store .icon-right[data-v-4b78b627]{float:right;color:#909399;font-size:%?24?%}.local-box[data-v-4b78b627]{border-top:%?2?% solid #eee}.local-box .order-cell[data-v-4b78b627]{padding-top:%?30?%;margin-bottom:0}.local-box .order-cell .box[data-v-4b78b627]{padding:0}.local-box .pick-block[data-v-4b78b627]{padding-top:%?20?%;display:flex;align-items:center}.local-box .pick-block > uni-view[data-v-4b78b627]{flex:1}.local-box .pick-block .title[data-v-4b78b627]{font-weight:700}.local-box .pick-block .time-picker[data-v-4b78b627]{display:flex;align-items:center;justify-content:flex-end}.local-box .pick-block .time-picker .cell-more[data-v-4b78b627]{float:right;margin-left:%?10?%;color:#909399;font-size:%?24?%}.local-box .pick-block .time-picker .cell-more .iconfont[data-v-4b78b627]{color:#909399}.local-box .pick-block .time-picker uni-text[data-v-4b78b627]{white-space:nowrap}.empty-local[data-v-4b78b627]{color:#ff443f}.delivery-mode[data-v-4b78b627]{margin:0 %?24?%;overflow:hidden;border-top-left-radius:%?16?%;border-top-right-radius:%?16?%;background-color:var(--base-color)}.delivery-mode .action[data-v-4b78b627]{display:flex;background:var(--base-color-light-7)}.delivery-mode .action > uni-view[data-v-4b78b627]{flex:1;text-align:center;height:%?76?%;line-height:%?76?%;font-size:%?30?%;color:#000;position:relative}.delivery-mode .action > uni-view:nth-child(2).active[data-v-4b78b627], .delivery-mode .action > uni-view:nth-child(3).active[data-v-4b78b627]{border-top-left-radius:%?16?%}.delivery-mode .action > uni-view .out-radio[data-v-4b78b627]:after, .delivery-mode .action > uni-view .out-radio[data-v-4b78b627]:before{position:absolute;content:"";width:%?20?%;height:%?20?%;background-color:#fff;bottom:0;display:none}.delivery-mode .action > uni-view .out-radio[data-v-4b78b627]:after{-webkit-transform:translateX(100%);transform:translateX(100%);right:0}.delivery-mode .action > uni-view .out-radio[data-v-4b78b627]:before{left:0;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.delivery-mode .action .active[data-v-4b78b627]{background:#fff;color:var(--base-color);border-top-right-radius:%?16?%}.delivery-mode .action .active[data-v-4b78b627]:after, .delivery-mode .action .active[data-v-4b78b627]:before{position:absolute;content:"";width:%?40?%;height:%?40?%;background-color:var(--base-color-light-7);bottom:0;-webkit-transform:translateX(100%);transform:translateX(100%);border-radius:50%;z-index:5}.delivery-mode .action .active[data-v-4b78b627]:after{-webkit-transform:translateX(100%);transform:translateX(100%);right:0}.delivery-mode .action .active[data-v-4b78b627]:before{left:0;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.delivery-mode .action .active .out-radio[data-v-4b78b627]:after, .delivery-mode .action .active .out-radio[data-v-4b78b627]:before{display:block}.store-box[data-v-4b78b627]{position:relative;padding:%?30?% %?24?%;margin:0 %?24?% 0;background-color:#fff;border-bottom-left-radius:%?16?%;border-bottom-right-radius:%?16?%;overflow:hidden}.store-box.not-delivery-type[data-v-4b78b627]{border-radius:%?16?%}.store-box .address-line[data-v-4b78b627]{position:absolute;bottom:0;left:0;width:100%;height:%?6?%}.store-box .store-info[data-v-4b78b627]{display:flex;align-items:baseline}.store-box .store-info .icon[data-v-4b78b627]{position:relative;margin-right:%?12?%;align-self:flex-start;margin-top:%?-2?%}.store-box .store-info .icon.img[data-v-4b78b627]{background-color:unset;margin-right:%?8?%;width:%?46?%;height:%?46?%;border-radius:50%;margin-top:%?12?%}.store-box .store-info .icon.img uni-image[data-v-4b78b627]{width:100%;height:100%}.store-box .store-info .icon .iconfont[data-v-4b78b627]{font-size:%?32?%}.store-box .store-info .store-address-info[data-v-4b78b627]{width:100%;display:flex;align-items:center}.store-box .store-info .store-address-info .info-wrap[data-v-4b78b627]{flex:1;width:0}.store-box .store-info .store-address-info .info-wrap .title[data-v-4b78b627]{margin-bottom:%?10?%;font-size:%?28?%;font-weight:700}.store-box .store-info .store-address-info .info-wrap .title .cell-more[data-v-4b78b627]{float:right;margin-left:%?50?%;color:#909399;font-size:%?24?%;font-weight:500}.store-box .store-info .store-address-info .info-wrap .store-detail uni-view[data-v-4b78b627]{word-break:break-word;font-size:%?26?%}.store-box .store-info .store-address-info .info-wrap .store-detail .close-desc[data-v-4b78b627]{color:red}.store-box .store-info .store-address-info .info-wrap .store-detail .address[data-v-4b78b627]{color:#606266;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis}.store-box .store-info .store-address-info .cell-more[data-v-4b78b627]{color:#909399}.store-box .empty[data-v-4b78b627]{text-align:center}.store-box .store-time[data-v-4b78b627]{border-top:%?2?% solid #f4f4f6;display:flex;align-items:center;justify-content:space-between;padding:%?20?% 0 0;box-sizing:border-box}.store-box .store-time uni-view[data-v-4b78b627]{font-size:%?28?%}.store-box .store-time .left[data-v-4b78b627]{font-weight:700}.store-box .store-time .right[data-v-4b78b627]{display:flex;align-items:center;line-height:1;font-size:%?24?%}.store-box .store-time .right .iconfont[data-v-4b78b627]{font-size:%?24?%;margin-left:%?14?%;color:#909399}.buyer-message[data-v-4b78b627]{padding:%?30?% %?24?%}.buyer-message .order-cell[data-v-4b78b627]{margin-bottom:0}.member-card-wrap[data-v-4b78b627]{background-color:#fffbf4;padding:0 %?30?%!important}.member-card-wrap .head[data-v-4b78b627]{display:flex;align-items:center;height:%?80?%}.member-card-wrap .icon-yuan_checked[data-v-4b78b627], .member-card-wrap .icon-yuan_checkbox[data-v-4b78b627]{font-size:%?32?%}.member-card-wrap .icon-huiyuan[data-v-4b78b627]{margin-right:%?10?%;line-height:1;font-size:%?36?%;background-image:linear-gradient(156deg,#814635,#3a221b);-webkit-background-clip:text;-webkit-text-fill-color:transparent}.member-card-wrap .info[data-v-4b78b627]{text-align:left;flex:1;color:#e5ce75;font-size:%?24?%;color:#333}.member-card-wrap .body[data-v-4b78b627]{display:flex;overflow-x:scroll;padding:%?10?% 0 %?20?% 0}.member-card-wrap .body .item[data-v-4b78b627]{padding:%?20?% 0 %?30?% 0;width:calc((100% - %?60?%) / 4);text-align:center;background:#fff;margin-right:%?20?%;border:%?4?% solid #fff;border-radius:%?10?%;position:relative;overflow:hidden}.member-card-wrap .body .item .icon-icon[data-v-4b78b627]{position:absolute;right:0;bottom:0;font-size:%?32?%;display:none;line-height:1}.member-card-wrap .body .item[data-v-4b78b627]:last-child{margin-right:0}.member-card-wrap .body .item .title[data-v-4b78b627]{margin-top:%?20?%;font-weight:700}.member-card-wrap .body .item .price[data-v-4b78b627]{margin-top:%?10?%}.member-card-wrap .body .active .icon-icon[data-v-4b78b627]{display:block}.system-form-wrap[data-v-4b78b627]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;padding:0;overflow:hidden}.system-form-wrap .order-cell[data-v-4b78b627]{padding:%?30?% %?24?%;margin-bottom:0;border-bottom:%?2?% solid #f4f4f6}.system-form-wrap[data-v-4b78b627] .form-wrap{margin:0 %?24?%}.system-form-wrap[data-v-4b78b627] .form-wrap .icon-right{color:#909399;font-size:%?24?%}.agreement[data-v-4b78b627]{margin:%?20?% %?24?% 0}.agreement uni-text[data-v-4b78b627]{color:var(--base-color)}.agreement-conten-box[data-v-4b78b627]{background:#fff;padding:%?30?% %?30?%}.agreement-conten-box .title[data-v-4b78b627]{text-align:center;margin-bottom:%?20?%;font-weight:bolder}.agreement-conten-box .close[data-v-4b78b627]{position:absolute;right:%?30?%;top:%?10?%}.agreement-conten-box .con[data-v-4b78b627]{height:60vh}.icon[data-v-4b78b627]{line-height:1;margin-right:%?14?%;max-height:%?50?%}.icon uni-image[data-v-4b78b627]{width:%?38?%;margin:%?-6?% auto;max-height:%?50?%}.form-popup[data-v-4b78b627]{height:60vh!important}.form-popup .popup-body[data-v-4b78b627]{padding:%?20?% %?30?%;box-sizing:border-box}.member-card-popup[data-v-4b78b627]{height:60vh}.member-card-popup .popup-body .card-item[data-v-4b78b627]{display:flex;padding:%?30?%;background:var(--base-color-light-9);margin:%?24?% %?20?%;border-radius:%?18?%}.member-card-popup .popup-body .card-item .content[data-v-4b78b627]{flex:1;width:0;margin-right:%?30?%}.member-card-popup .popup-body .card-item .content .title[data-v-4b78b627]{line-height:%?40?%;font-size:%?28?%;font-weight:600}.member-card-popup .popup-body .card-item .content .info uni-text[data-v-4b78b627]{line-height:1;font-size:%?24?%;color:#666;margin-top:%?20?%;margin-right:%?8?%;display:inline-block}.member-card-popup .popup-body .card-item .iconfont[data-v-4b78b627]{font-size:%?44?%}.member-card-popup .popup-body .card-item .icon-yuan_checkbox[data-v-4b78b627]{color:#909399}',""]),e.exports=t},"4f87":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return o}));var o={pageMeta:i("7854").default,nsSelectTime:i("a523").default,uniPopup:i("d745").default,loadingCover:i("c003").default,nsLogin:i("2910").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":e.themeColor}}),i("v-uni-view",{staticClass:"order-container",class:{"safe-area":e.isIphoneX}},[i("v-uni-scroll-view",{staticClass:"order-scroll-container",attrs:{"scroll-y":"true"}},[i("v-uni-view",{staticClass:"payment-navbar-block"}),e.goodsData?[0==e.orderPaymentData.is_virtual?[e.goodsData.delivery.express_type.length>1?i("v-uni-view",{staticClass:"delivery-mode"},[i("v-uni-view",{staticClass:"action"},e._l(e.goodsData.delivery.express_type,(function(t,o){return i("v-uni-view",{key:o,class:{active:t.name==e.orderCreateData.delivery.delivery_type},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.selectDeliveryType(t)}}},[e._v(e._s(t.title)),i("v-uni-view",{staticClass:"out-radio"})],1)})),1)],1):e._e(),"express"==e.orderCreateData.delivery.delivery_type?i("v-uni-view",{staticClass:"address-box",class:{"not-delivery-type":e.goodsData.delivery.express_type.length<=1}},[e.memberAddress?i("v-uni-view",{staticClass:"info-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"content"},[i("v-uni-text",{staticClass:"name"},[e._v(e._s(e.memberAddress.name?e.memberAddress.name:""))]),i("v-uni-text",{staticClass:"mobile"},[e._v(e._s(e.memberAddress.mobile?e.memberAddress.mobile:""))]),i("v-uni-view",{staticClass:"desc-wrap"},[e._v(e._s(e.memberAddress.full_address?e.memberAddress.full_address:"")+"\n\t\t\t\t\t\t\t\t"+e._s(e.memberAddress.address?e.memberAddress.address:""))])],1),i("v-uni-text",{staticClass:"cell-more iconfont icon-right"})],1):i("v-uni-view",{staticClass:"empty-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"info"},[e._v("请设置收货地址")]),i("v-uni-view",{staticClass:"cell-more"},[i("v-uni-view",{staticClass:"iconfont icon-right"})],1)],1),i("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],1):e._e(),"local"==e.orderCreateData.delivery.delivery_type?i("v-uni-view",{staticClass:"address-box",class:{"not-delivery-type":e.goodsData.delivery.express_type.length<=1}},[e.localMemberAddress?i("v-uni-view",[e.storeInfo.storeList&&Object.keys(e.storeInfo.storeList).length>1?[e.storeInfo.currStore?i("v-uni-view",{staticClass:"local-delivery-store",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.deliveryPopup.open()}}},[i("v-uni-view",{staticClass:"info"},[e._v("由"),i("v-uni-text",{staticClass:"store-name"},[e._v(e._s(e.storeInfo.currStore.store_name))]),e._v("提供配送")],1),i("v-uni-view",{staticClass:"cell-more"},[i("v-uni-text",[e._v("点击切换")]),i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1):i("v-uni-view",{staticClass:"local-delivery-store"},[i("v-uni-view",{staticClass:"info"},[i("v-uni-text",{staticClass:"store-name"},[e._v("您的附近没有可配送的门店，请选择其他配送方式")])],1)],1)]:e._e(),i("v-uni-view",{staticClass:"info-wrap local",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"content"},[i("v-uni-text",{staticClass:"name"},[e._v(e._s(e.localMemberAddress.name?e.localMemberAddress.name:""))]),i("v-uni-text",{staticClass:"mobile"},[e._v(e._s(e.localMemberAddress.mobile?e.localMemberAddress.mobile:""))]),i("v-uni-view",{staticClass:"desc-wrap"},[e._v(e._s(e.localMemberAddress.full_address?e.localMemberAddress.full_address:"")+"\n\t\t\t\t\t\t\t\t\t"+e._s(e.localMemberAddress.address?e.localMemberAddress.address:""))])],1),i("v-uni-text",{staticClass:"cell-more iconfont icon-right"})],1),e.orderPaymentData.config.local&&e.orderPaymentData.config.local.is_use&&e.orderPaymentData.delivery.local&&1==e.orderPaymentData.delivery.local.info.time_is_open?i("v-uni-view",{staticClass:"local-box"},[i("v-uni-view",{staticClass:"pick-block",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.localtime("")}}},[i("v-uni-view",{staticClass:"title font-size-base"},[e._v("送达时间")]),i("v-uni-view",{staticClass:"time-picker"},[i("v-uni-text",{class:{"color-tip":!e.orderCreateData.buyer_ask_delivery_title}},[e._v(e._s(e.orderCreateData.buyer_ask_delivery_title?e.orderCreateData.buyer_ask_delivery_title:"请选择送达时间"))]),i("v-uni-text",{staticClass:"iconfont icon-right cell-more"})],1)],1)],1):e._e()],2):i("v-uni-view",{staticClass:"empty-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"info"},[e._v("请设置收货地址")]),i("v-uni-view",{staticClass:"cell-more"},[i("v-uni-view",{staticClass:"iconfont icon-right"})],1)],1),i("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],1):e._e(),"store"==e.orderCreateData.delivery.delivery_type?i("v-uni-view",{staticClass:"store-box",class:{"not-delivery-type":e.goodsData.delivery.express_type.length<=1}},[e.storeInfo.currStore?[i("v-uni-view",{staticClass:"store-info",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.deliveryPopup.open()}}},[i("v-uni-view",{staticClass:"store-address-info"},[i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"title"},[i("v-uni-text",[e._v(e._s(e.storeInfo.currStore.store_name))])],1),i("v-uni-view",{staticClass:"store-detail"},[e.storeInfo.currStore.open_date?i("v-uni-view",[e._v("营业时间："+e._s(e.storeInfo.currStore.open_date))]):e._e(),i("v-uni-view",{staticClass:"address"},[e._v(e._s(e.storeInfo.currStore.full_address)+"\n\t\t\t\t\t\t\t\t\t\t\t"+e._s(e.storeInfo.currStore.address))])],1)],1),i("v-uni-view",{staticClass:"cell-more iconfont icon-right"})],1)],1),i("v-uni-view",{staticClass:"mobile-wrap store-mobile"},[i("v-uni-view",{staticClass:"form-group"},[i("v-uni-text",{staticClass:"text"},[e._v("姓名")]),i("v-uni-input",{staticClass:"input",attrs:{type:"text","placeholder-class":"color-tip placeholder",disabled:!0},model:{value:e.member_address.name,callback:function(t){e.$set(e.member_address,"name",t)},expression:"member_address.name"}})],1)],1),i("v-uni-view",{staticClass:"mobile-wrap store-mobile"},[i("v-uni-view",{staticClass:"form-group"},[i("v-uni-text",{staticClass:"text"},[e._v("预留手机")]),i("v-uni-input",{staticClass:"input",attrs:{type:"number",maxlength:"11",placeholder:"请输入您的手机号码","placeholder-class":"color-tip placeholder"},model:{value:e.member_address.mobile,callback:function(t){e.$set(e.member_address,"mobile",t)},expression:"member_address.mobile"}})],1)],1),i("v-uni-view",{staticClass:"store-time",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.storetime("")}}},[i("v-uni-view",{staticClass:"left"},[e._v("提货时间")]),i("v-uni-view",{staticClass:"right"},[i("v-uni-text",{class:{"color-tip":!e.orderCreateData.buyer_ask_delivery_title}},[e._v(e._s(e.orderCreateData.buyer_ask_delivery_title?e.orderCreateData.buyer_ask_delivery_title:"请选择提货时间"))]),i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1)]:i("v-uni-view",{staticClass:"empty"},[e._v("当前无自提门店，请选择其它配送方式")]),i("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],2):e._e()]:e._e(),i("v-uni-view",{staticClass:"site-wrap order-goods"},[i("v-uni-view",{staticClass:"site-body"},e._l(e.goodsData.goods_list,(function(t,o){return i("v-uni-view",{key:o,staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-wrap"},[i("v-uni-view",{staticClass:"goods-img",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})}}},[i("v-uni-image",{attrs:{src:e.$util.img(t.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.imageError(o)}}})],1),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"top-wrap"},[i("v-uni-view",{staticClass:"goods-name",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})}}},[e._v(e._s(t.sku_name))]),t.sku_spec_format?i("v-uni-view",{staticClass:"sku"},[i("v-uni-view",{staticClass:"goods-spec"},[e._l(t.sku_spec_format,(function(t,o){return[i("v-uni-view",[e._v(e._s(t.spec_value_name))])]}))],2)],1):e._e(),0==t.is_virtual?[e.orderCreateData.delivery&&t.support_trade_type&&-1==t.support_trade_type.indexOf(e.orderCreateData.delivery.delivery_type)?i("v-uni-view",{staticClass:"error-tips"},[i("v-uni-text",{staticClass:"iconfont icon-gantanhao"}),i("v-uni-text",[e._v("该商品不支持"+e._s(e.orderCreateData.delivery.delivery_type_name))])],1):e._e()]:e._e()],2),i("v-uni-view",{staticClass:"goods-sub-section"},[i("v-uni-view",{staticClass:"color-base-text"},[i("v-uni-text",{staticClass:"unit price-style small"},[e._v(e._s(e.$lang("common.currencySymbol")))]),i("v-uni-text",{staticClass:"goods-price price-style large"},[e._v(e._s(parseFloat(t.price).toFixed(2).split(".")[0]))]),i("v-uni-text",{staticClass:"unit price-style small"},[e._v("."+e._s(parseFloat(t.price).toFixed(2).split(".")[1]))])],1),i("v-uni-view",[i("v-uni-text",{staticClass:"font-size-tag"},[e._v("x")]),i("v-uni-text",{staticClass:"font-size-base"},[e._v(e._s(t.num))])],1)],1)],1)],1)],1)})),1)],1),i("v-uni-view",{staticClass:"site-wrap buyer-message"},[i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[e._v("买家留言")]),i("v-uni-view",{staticClass:"box text-overflow ",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("buyerMessagePopup")}}},[e.orderCreateData.buyer_message?i("v-uni-text",[e._v(e._s(e.orderCreateData.buyer_message))]):i("v-uni-text",{staticClass:"color-sub"},[e._v("无留言")])],1),i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1),i("v-uni-view",{staticClass:"order-submit bottom-safe-area"},[i("v-uni-view",{staticClass:"order-settlement-info"}),i("v-uni-view",{staticClass:"submit-btn"},[i("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.orderCreate()}}},[e._v("确定")])],1)],1),i("v-uni-view",{staticClass:"order-submit-block"}),i("ns-select-time",{ref:"timePopup",on:{selectTime:function(t){arguments[0]=t=e.$handleEvent(t),e.selectPickupTime.apply(void 0,arguments)}}})]:e._e()],2),i("uni-popup",{ref:"buyerMessagePopup",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"buyermessag-popup popup",staticStyle:{height:"auto"},on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[i("v-uni-view",{staticClass:"popup-header"},[i("v-uni-text",{staticClass:"tit"},[e._v("买家留言")]),i("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("buyerMessagePopup")}}})],1),i("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[i("v-uni-view",[i("v-uni-view",{staticClass:"buyermessag-cell"},[i("v-uni-view",{staticClass:"buyermessag-form-group"},[i("v-uni-textarea",{attrs:{type:"text",maxlength:"100",placeholder:"留言前建议先与商家协调一致","placeholder-class":"color-tip"},model:{value:e.orderCreateData.buyer_message,callback:function(t){e.$set(e.orderCreateData,"buyer_message",t)},expression:"orderCreateData.buyer_message"}})],1)],1)],1)],1),i("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("buyerMessagePopup")}}},[i("v-uni-view",{staticClass:"confirm-btn color-base-bg"},[e._v("确定")])],1)],1)],1),i("uni-popup",{ref:"deliveryPopup",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"delivery-popup popup"},[i("v-uni-view",{staticClass:"popup-header"},[i("v-uni-text",{staticClass:"tit"},[e._v("已为您甄选出附近所有相关门店")]),i("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("deliveryPopup")}}})],1),i("v-uni-view",{staticClass:"popup-body store-popup",class:{"safe-area":e.isIphoneX}},[i("v-uni-view",{staticClass:"delivery-content"},[e._l(e.storeInfo.storeList,(function(t,o){return i("v-uni-view",{key:o,staticClass:"item-wrap",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.selectPickupPoint(t)}}},[i("v-uni-view",{staticClass:"detail"},[i("v-uni-view",{staticClass:"name",class:t.store_id==e.orderPaymentData.delivery.store_id?"color-base-text":""},[i("v-uni-text",[e._v(e._s(t.store_name))]),t.distance?i("v-uni-text",[e._v("("+e._s(t.distance)+"km)")]):e._e()],1),i("v-uni-view",{staticClass:"info"},[i("v-uni-view",{staticClass:"font-size-goods-tag",class:t.store_id==e.orderPaymentData.delivery.store_id?"color-base-text":""},[e._v("营业时间："+e._s(t.open_date))]),i("v-uni-view",{staticClass:"font-size-goods-tag",class:t.store_id==e.orderPaymentData.delivery.store_id?"color-base-text":""},[e._v("地址："+e._s(t.full_address)+e._s(t.address))])],1)],1),t.store_id==e.orderPaymentData.delivery.store_id?i("v-uni-view",{staticClass:"icon"},[i("v-uni-text",{staticClass:"iconfont icon-yuan_checked color-base-text"})],1):e._e()],1)})),e.storeInfo.storeList?e._e():i("v-uni-view",{staticClass:"empty"},[e._v("所选择收货地址附近没有可以自提的门店")])],2)],1)],1)],1),i("loading-cover",{ref:"loadingCover"}),i("ns-login",{ref:"login"})],1)],1)},r=[]},5532:function(e,t,i){"use strict";var o=i("908c"),a=i.n(o);a.a},"574c":function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,"[data-v-4b78b627] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:none;max-height:unset!important;overflow-y:hidden!important}[data-v-4b78b627] .uni-popup__wrapper{border-radius:%?20?% %?20?% 0 0}[data-v-4b78b627] .uni-popup{z-index:8}",""]),e.exports=t},"5e6e":function(e,t,i){"use strict";i.r(t);var o=i("b6c2"),a=i.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},6064:function(e,t,i){"use strict";var o=i("b42f"),a=i.n(o);a.a},7016:function(e,t,i){"use strict";var o=i("4a0d"),a=i.n(o);a.a},7854:function(e,t,i){"use strict";i.r(t);var o=i("8ba8"),a=i("f48d");for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);var s=i("828b"),n=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=n.exports},"8ba8":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){}));var o=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},"908c":function(e,t,i){var o=i("4f1c");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var a=i("967d").default;a("44058067",o,!0,{sourceMap:!1,shadowMode:!1})},a523:function(e,t,i){"use strict";i.r(t);var o=i("d68f"),a=i("c4af");for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);i("6064");var s=i("828b"),n=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,"8e6fd976",null,!1,o["a"],void 0);t["default"]=n.exports},b42f:function(e,t,i){var o=i("f7d5");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var a=i("967d").default;a("599afa74",o,!0,{sourceMap:!1,shadowMode:!1})},b6c2:function(e,t,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(i("b7e4")),r={mixins:[a.default]};t.default=r},b7e4:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("d4b5"),i("bf0f"),i("2797"),i("e838");var o={data:function(){return{isIphoneX:!1,orderCreateData:{is_balance:0,is_point:1,buyer_message:"",is_invoice:0,invoice_type:1,invoice_title_type:1,is_tax_invoice:0,invoice_title:"",taxpayer_number:"",invoice_content:"",invoice_full_address:"",invoice_email:"",member_address:{name:"",mobile:""},is_open_card:0,member_card_unit:"",delivery:{delivery_type:""}},orderPaymentData:{site_name:"",express_type:[],coupon_list:[],invoice:{invoice_content_array:[]},member_account:{balance:0,is_pay_password:0},delivery:{delivery_type:"",express_type:[],member_address:{name:"",mobile:""},local:{info:{start_time:0,end_time:0,time_week:[]}},delivery_store_info:{}},member_address:{name:"",mobile:""},local_config:{info:{start_time:0,end_time:0,time_week:[]}},delivery_store_info:{}},isSub:!1,tempData:null,manjian:[],storeInfo:{storeList:[],currStore:{}},member_address:{name:"",mobile:""},timeInfo:{week:0,start_time:0,end_time:0,showTime:!1,showTimeBar:!1},post_free:{},deliveryWeek:"",out_trade_no:null,menuButtonBounding:{},memberAddress:null,localMemberAddress:null}},onLoad:function(){var e=this;this.location||this.$util.getLocation(),this.isIphoneX=this.$util.uniappIsIPhoneX(),uni.getStorageSync("addressBack")&&uni.removeStorageSync("addressBack"),this.storeToken?this.getOrderPaymentData():this.$nextTick((function(){e.$refs.login.open("/pages_promotion/blindbox/fill_address")}))},methods:{getOrderPaymentData:function(){var e=this;if(!this.out_trade_no){this.orderCreateData=uni.getStorageSync("blindOrderCreateData");var t=uni.getStorageSync("pay_flag");this.orderCreateData?(this.location&&(this.orderCreateData.latitude=this.location.latitude,this.orderCreateData.longitude=this.location.longitude),this.$api.sendRequest({url:"/blindbox/api/order/payment",data:this.orderCreateData,success:function(t){t.code>=0?(e.orderPaymentData=t.data,e.handlePaymentData(),e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.orderCalculate()):(e.$util.showToast({title:"未获取到创建订单所需数据！"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1500))},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})):1==t?uni.removeStorageSync("pay_flag"):(this.$util.showToast({title:"未获取到创建订单所需数据！"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1500))}},handlePaymentData:function(){var e=this;this.orderCreateData.delivery={},this.orderCreateData.coupon={},this.orderCreateData.is_balance=0,this.orderCreateData.is_point=0,this.orderCreateData.is_invoice=0,this.orderCreateData.invoice_type=1,this.orderCreateData.invoice_title_type=1,this.orderCreateData.is_tax_invoice=0,this.orderCreateData.invoice_title="";var t=JSON.parse(JSON.stringify(this.orderPaymentData));if(this.orderCreateData.order_key=t.order_key,void 0!=t.delivery.express_type&&void 0!=t.delivery.express_type[0]){var i=t.delivery.express_type;this.orderCreateData.delivery.store_id=0;var o=uni.getStorageSync("delivery");if(o){var a=o.delivery_type,r=o.delivery_type_name;i.forEach((function(t){("store"==a&&t.name==a||"local"==a&&t.name==a)&&e.storeSelected(t)})),"store"==a&&(this.member_address={name:t.member_account.nickname,mobile:""!=t.member_account.mobile?t.member_account.mobile:""})}else{a=i[0].name;"store"==a&&(this.member_address={name:t.member_account.nickname,mobile:""!=t.member_account.mobile?t.member_account.mobile:""});r=i[0].title}this.orderCreateData.delivery.delivery_type=a,this.orderCreateData.delivery.delivery_type_name=r,"store"!=i[0].name&&"local"!=i[0].name||this.storeSelected(i[0])}t.is_virtual&&(this.member_address={name:t.member_account.nickname,mobile:""!=t.member_account.mobile?t.member_account.mobile:""})},openPopup:function(e){this.$refs[e].open()},closePopup:function(e){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[e].close()},selectAddress:function(){var e={back:encodeURIComponent("/pages_promotion/blindbox/fill_address?blindbox_goods_id="+this.blindbox_goods_id+"&out_trade_no="+this.outTradeNo),local:0,type:1};"local"==this.orderPaymentData.delivery.delivery_type&&(e.local=1,e.type=2),this.$util.redirectTo("/pages_tool/member/address",e)},orderCalculate:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);t.delivery=JSON.stringify(t.delivery),this.$api.sendRequest({url:"/blindbox/api/order/calculate",data:t,success:function(t){t.code>=0?(t.data.delivery&&("express"==t.data.delivery.delivery_type&&(e.memberAddress=t.data.delivery.member_address),"local"==t.data.delivery.delivery_type&&(e.localMemberAddress=t.data.delivery.member_address)),t.data.config.local&&t.data.config.local.is_use&&(e.orderPaymentData.local_config=t.data.config.local),t.data.delivery.delivery_store_info&&(e.orderPaymentData.delivery_store_info=t.data.delivery.delivery_store_info),e.$forceUpdate()):e.$util.showToast({title:t.message})}})},orderCreate:function(){var e=this;if(this.verify()){if(this.isSub)return;this.isSub=!0,uni.showLoading({title:""});var t=this.$util.deepClone(this.orderCreateData);t.delivery=JSON.stringify(t.delivery),"store"==this.orderCreateData.delivery.delivery_type?t.member_address=JSON.stringify(this.member_address):t.member_address=JSON.stringify(t.member_address),this.$api.sendRequest({url:"/blindbox/api/order/create",data:t,success:function(t){uni.hideLoading(),0==t.code?e.$util.redirectTo("/pages/order/list",{},"redirectTo"):(e.$util.showToast({title:t.message}),e.isSub=!1)},fail:function(t){uni.hideLoading(),e.isSub=!1}})}},verify:function(){if(0==this.orderPaymentData.is_virtual){if(!this.orderCreateData.delivery||!this.orderCreateData.delivery.delivery_type)return this.$util.showToast({title:"商家未设置配送方式"}),!1;if("store"!=this.orderCreateData.delivery.delivery_type&&!this.member_address)return this.$util.showToast({title:"请先选择您的收货地址"}),!1;if("store"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可提货的门店,请选择其他配送方式"}),!1;if(!this.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.member_address.mobile))return this.$util.showToast({title:"请输入正确的预留手机"}),!1;if(!this.orderCreateData.delivery.buyer_ask_delivery_time.start_date||!this.orderCreateData.delivery.buyer_ask_delivery_time.end_date)return this.$util.showToast({title:"请选择自提时间"}),!1}if("local"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可配送的门店,请选择其他配送方式"}),!1;if(this.orderPaymentData.config.local&&this.orderPaymentData.config.local.is_use&&this.orderPaymentData.delivery.local&&this.orderPaymentData.delivery.local.info&&1==this.orderPaymentData.delivery.local.info.time_is_open&&(!this.orderCreateData.delivery.buyer_ask_delivery_time.start_date||!this.orderCreateData.delivery.buyer_ask_delivery_time.end_date))return this.$util.showToast({title:"请选择配送时间"}),!1}}return!0},selectDeliveryType:function(e){uni.setStorageSync("delivery",{delivery_type:e.name,delivery_type_name:e.title}),this.orderCreateData.delivery.delivery_type=e.name,this.orderCreateData.delivery.delivery_type_name=e.title,"store"==e.name&&(this.storeSelected(e),this.member_address.name=this.orderPaymentData.member_account.nickname,this.member_address.mobile||(this.member_address.mobile=""!=this.orderPaymentData.member_account.mobile?this.orderPaymentData.member_account.mobile:"")),"local"==e.name&&this.storeSelected(e),this.orderCalculate(),this.$forceUpdate()},storeSelected:function(e){this.storeInfo.storeList=e.store_list;var t=e.store_list[0]?e.store_list[0]:null;this.selectPickupPoint(t)},selectPickupPoint:function(e){if(e){this.orderCreateData.delivery.store_id=e.store_id,this.storeInfo.currStore=e;var t=uni.getStorageSync("delivery");t&&(t.store_id=e.store_id),uni.setStorageSync("delivery",t)}else this.orderCreateData.delivery.store_id=0,this.storeInfo.currStore={};this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.orderCreateData.buyer_ask_delivery_title="",this.orderCalculate(),this.$forceUpdate(),this.$refs["deliveryPopup"].close()},popupConfirm:function(e){this.orderCalculate(),this.$forceUpdate(),this.tempData=null,this.$refs[e].close()},imageError:function(e){this.orderPaymentData.goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},navigateTo:function(e){this.$util.redirectTo("/pages/goods/detail",{sku_id:e})},localtime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=this.$util.deepClone(this.orderPaymentData.local_config.info);t.delivery_time&&(t.end_time=t.delivery_time[t.delivery_time.length-1].end_time);var i={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(i,e)},storetime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.orderPaymentData.delivery_store_info){var t=this.$util.deepClone(this.storeInfo.currStore);t.delivery_time?(t.delivery_time=JSON.parse(t.delivery_time),t.end_time=t.delivery_time[t.delivery_time.length-1].end_time):t.delivery_time=[{start_time:t.start_time,end_time:t.end_time}];var i={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(i,e),this.$forceUpdate()}},selectPickupTime:function(e){e.data&&e.data.month&&(this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:e.data.start_date,end_date:e.data.end_date},"今天"==e.data.title||"明天"==e.data.title?this.orderCreateData.buyer_ask_delivery_title=e.data.title+"("+e.data.time+")":this.orderCreateData.buyer_ask_delivery_title=e.data.month+"("+e.data.time+")",this.$forceUpdate())},saveBuyerMessage:function(){this.$refs.buyerMessagePopup.close()},subscribeMessage:function(){var e="ORDER_PAY,ORDER_DELIVERY,ORDER_TAKE_DELIVERY";this.orderCreateData.delivery&&"store"==this.orderCreateData.delivery.delivery_type&&(e="ORDER_PAY,ORDER_TAKE_DELIVERY"),this.$util.subscribeMessage(e)},back:function(){uni.navigateBack({delta:1})}},computed:{goodsData:function(){if(this.orderPaymentData.goods_list)return this.orderPaymentData.goods_list.forEach((function(e){e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format))})),this.orderPaymentData}},watch:{location:function(e){e&&this.getOrderPaymentData()},storeToken:function(e,t){e&&this.getOrderPaymentData()}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)}}};t.default=o},c4af:function(e,t,i){"use strict";i.r(t);var o=i("3672"),a=i.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},cc1b:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(e){return-1!==["dark","light"].indexOf(e)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var e=this,t=getCurrentPages()[0];this.$pageVm=t.$vm||t,uni.onWindowResize((function(t){e.$emit("resize",t)})),this.$pageVm.$on("hook:onPageScroll",(function(t){e.$emit("scroll",t)})),this.$watch("backgroundTextStyle",(function(){e.setBackgroundTextStyle()})),this.$watch((function(){return[e.rootFontSize,e.pageStyle]}),(function(){e.setPageMeta()})),this.$watch((function(){return[e.backgroundColor,e.backgroundColorTop,e.backgroundColorBottom]}),(function(){e.setBackgroundColor()})),this.$watch((function(){return[e.scrollTop,e.scrollDuration]}),(function(){e.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(e,t){e.setStyle({pullToRefresh:{support:t,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var e=this,t=String(this.scrollTop);if(-1!==t.indexOf("rpx")&&(t=uni.upx2px(t.replace("rpx",""))),t=parseFloat(t),!isNaN(t)){var i=function i(a){a.scrollTop===t&&(e.$pageVm.$off("hook:onPageScroll",i),e.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:t,duration:this.scrollDuration,success:function(){e.$pageVm.$on("hook:onPageScroll",i)}})}}}};t.default=a},d68f:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return o}));var o={uniPopup:i("d745").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"ns-time"},[i("uni-popup",{ref:"selectTime",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"box"},[i("v-uni-view",{staticClass:"title"},[e.obj.delivery&&"local"==e.obj.delivery.delivery_type?[e._v("选择送达时间")]:e._e(),e.obj.delivery&&"store"==e.obj.delivery.delivery_type?[e._v("选择自提时间")]:e._e(),i("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.close.apply(void 0,arguments)}}})],2),i("v-uni-view",{staticClass:"body"},[i("v-uni-scroll-view",{staticClass:"left",attrs:{"scroll-y":!0}},e._l(e.dayData,(function(t,o){return i("v-uni-view",{key:o,staticClass:"item",class:o==e.keyJudge?"itemDay":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectTime("days",o,"yes")}}},[t.title?[e._v(e._s(t.title))]:[e._v(e._s(t.month))],i("v-uni-text",{staticClass:"itemtext"},[e._v(e._s(t.Day))])],2)})),1),i("v-uni-scroll-view",{staticClass:"right",attrs:{"scroll-y":!0}},e._l(e.timeData,(function(t,o){return i("v-uni-view",{key:o,staticClass:"item",class:e.key==e.keyJudge&&o==e.keys?"itemTime":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectTime("time",o,"yes")}}},[e._v(e._s(t)),e.key==e.keyJudge&&o==e.keys?i("v-uni-text",{staticClass:"iconfont icon-yuan_checked color-base-text"}):e._e()],1)})),1)],1)],1)],1)],1)},r=[]},f48d:function(e,t,i){"use strict";i.r(t);var o=i("cc1b"),a=i.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},f7d5:function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.box[data-v-8e6fd976]{height:%?728?%}.box .title[data-v-8e6fd976]{padding:0 %?30?%;box-sizing:border-box;text-align:center;font-size:%?28?%;font-weight:700;position:relative;height:%?90?%;line-height:%?90?%;border-bottom:%?1?% solid #f7f4f4}.box .title .icon-close[data-v-8e6fd976]{font-size:%?26?%;color:#909399;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.box .body[data-v-8e6fd976]{width:100%;height:calc(100% - %?90?%);display:flex;align-items:center}.box .body .left[data-v-8e6fd976]{width:%?230?%;background:#f8f8f8;height:100%}.box .body .left .item[data-v-8e6fd976]{width:100%;padding:%?16?% %?30?%;box-sizing:border-box;text-align:center;font-size:%?24?%;display:flex;align-items:center}.box .body .left .itemDay[data-v-8e6fd976]{background:#fff}.box .body .right[data-v-8e6fd976]{width:calc(100% - %?230?%);height:100%;padding:0 %?30?%;box-sizing:border-box}.box .body .right .item[data-v-8e6fd976]{width:100%;font-size:%?24?%;border-bottom:%?1?% solid #eee;display:flex;align-items:center;justify-content:space-between;height:%?72?%}.box .body .right .item .icon-yuan_checked[data-v-8e6fd976]{font-size:%?38?%;margin-right:%?30?%}.box .body .right .itemTime[data-v-8e6fd976]{color:var(--main-color)}',""]),e.exports=t}}]);