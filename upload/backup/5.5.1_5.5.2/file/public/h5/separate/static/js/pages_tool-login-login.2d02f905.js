(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-login-login"],{"3e2b":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,"[data-v-5e100056] .reward-popup .uni-popup__wrapper-box{background:none!important;max-width:unset!important;max-height:unset!important;overflow:unset!important}[data-v-5e100056] uni-toast .uni-simple-toast__text{background:red!important}",""]),e.exports=t},"41fa":function(e,t,a){"use strict";var i=a("72b8"),o=a.n(i);o.a},"5dea":function(e,t,a){"use strict";a.r(t);var i=a("a3d0"),o=a("b2f8");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("41fa"),a("b007");var r=a("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"5e100056",null,!1,i["a"],void 0);t["default"]=s.exports},6501:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2"),a("5c47"),a("a1c1"),a("aa9c"),a("dc8a");var o=i(a("fe8d")),n=i(a("349c")),r={data:function(){return{isAgree:!1,loginMode:"",formData:{mobile:"",account:"",password:"",vercode:"",dynacode:"",key:""},captcha:{id:"",img:""},isSub:!1,back:"",redirect:"redirectTo",dynacodeData:{seconds:120,timer:null,codeText:"获取动态码",isSend:!1},registerConfig:{register:"",login:""},captchaConfig:1,authInfo:null}},components:{registerReward:n.default},onLoad:function(e){e.loginMode&&(this.loginMode=e.loginMode),e.back&&(this.back=e.back),this.getRegisterConfig(),this.getCaptchaConfig(),this.authInfo=uni.getStorageSync("authInfo")},onShow:function(){},onReady:function(){this.$refs.loadingCover&&this.$refs.loadingCover.hide()},methods:{toAggrement:function(e){this.$util.redirectTo("/pages_tool/login/aggrement",{type:e})},getCaptchaConfig:function(){var e=this;this.$api.sendRequest({url:"/api/config/getCaptchaConfig",success:function(t){t.code>=0&&(e.captchaConfig=t.data.shop_reception_login,1==e.captchaConfig&&e.getCaptcha())}})},getRegisterConfig:function(){var e=this;this.$api.sendRequest({url:"/api/register/config",success:function(t){t.code>=0&&(e.registerConfig=t.data.value,e.loginMode||(-1!=e.registerConfig.login.indexOf("mobile")?e.loginMode="mobile":e.loginMode="account"))}})},switchLoginMode:function(){this.loginMode="mobile"==this.loginMode?"account":"mobile"},getCaptcha:function(){var e=this;0!=this.captchaConfig&&this.$api.sendRequest({url:"/api/captcha/captcha",data:{captcha_id:this.captcha.id},success:function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))}})},toRegister:function(){this.back?this.$util.redirectTo("/pages_tool/login/register",{back:encodeURIComponent(this.back)}):this.$util.redirectTo("/pages_tool/login/register")},forgetPassword:function(){this.back?this.$util.redirectTo("/pages_tool/login/find",{back:encodeURIComponent(this.back)}):this.$util.redirectTo("/pages_tool/login/find")},login:function(){var e=this;if("account"==this.loginMode){var t="/api/login/login";a={username:this.formData.account,password:this.formData.password}}else{t="/api/login/mobile";var a={mobile:this.formData.mobile,key:this.formData.key,code:this.formData.dynacode}}if(""!=this.captcha.id&&(a.captcha_id=this.captcha.id,a.captcha_code=this.formData.vercode),this.authInfo&&Object.assign(a,this.authInfo),uni.getStorageSync("source_member")&&(a.source_member=uni.getStorageSync("source_member")),this.verify(a)){if(this.isSub)return;this.isSub=!0,this.$api.sendRequest({url:t,data:a,success:function(t){if(t.code>=0){var a=t.data.can_receive_registergift;e.$store.commit("setToken",t.data.token),e.$store.dispatch("getCartNumber"),e.getMemberInfo((function(){if(1==a){e.$util.showToast({title:"登录成功"});var t=e.back?e.back:"/pages/member/index";e.$refs.registerReward&&e.$refs.registerReward.open(t)}else e.$util.loginComplete("/pages/member/index/index",{},e.redirect)}))}else e.isSub=!1,e.getCaptcha(),e.$util.showToast({title:t.message})},fail:function(t){e.isSub=!1,e.getCaptcha()}})}},verify:function(e){if(!this.registerConfig.agreement_show||this.isAgree){var t=[];"mobile"==this.loginMode&&(t=[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}],1==this.captchaConfig&&""!=this.captcha.id&&t.push({name:"captcha_code",checkType:"required",errorMsg:this.$lang("captchaPlaceholder")}),t.push({name:"code",checkType:"required",errorMsg:this.$lang("dynacodePlaceholder")})),"account"==this.loginMode&&(t=[{name:"username",checkType:"required",errorMsg:this.$lang("accountPlaceholder")},{name:"password",checkType:"required",errorMsg:this.$lang("passwordPlaceholder")}],1==this.captchaConfig&&""!=this.captcha.id&&t.push({name:"captcha_code",checkType:"required",errorMsg:this.$lang("captchaPlaceholder")}));var a=o.default.check(e,t);return!!a||(this.$util.showToast({title:o.default.error}),!1)}this.$util.showToast({title:"请先阅读并同意协议"})},mobileAuthLogin:function(e){var t=this;if("getPhoneNumber:ok"==e.detail.errMsg){var a={iv:e.detail.iv,encryptedData:e.detail.encryptedData};if(Object.keys(this.authInfo).length&&(Object.assign(a,this.authInfo),this.authInfo.nickName&&(a.nickname=this.authInfo.nickName),this.authInfo.avatarUrl&&(a.headimg=this.authInfo.avatarUrl)),uni.getStorageSync("source_member")&&(a.source_member=uni.getStorageSync("source_member")),this.isSub)return;this.isSub=!0,this.$api.sendRequest({url:"/api/tripartite/mobileauth",data:a,success:function(e){if(e.code>=0){var a=e.data.can_receive_registergift;t.$store.commit("setToken",e.data.token),t.$store.dispatch("getCartNumber"),t.getMemberInfo((function(){if(1==a){var e=t.back?t.back:"/pages/member/index";t.$refs.registerReward&&t.$refs.registerReward.open(e)}else""!=t.back?t.$util.redirectTo(decodeURIComponent(t.back),{},t.redirect):t.$util.redirectTo("/pages/member/index",{},t.redirect)}))}else t.isSub=!1,t.$util.showToast({title:e.message})},fail:function(e){t.isSub=!1,t.$util.showToast({title:"request:fail"})}})}},sendMobileCode:function(){var e=this;if(120==this.dynacodeData.seconds&&!this.dynacodeData.isSend){var t={mobile:this.formData.mobile,captcha_id:this.captcha.id,captcha_code:this.formData.vercode},a=[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}];1==this.captchaConfig&&a.push({name:"captcha_code",checkType:"required",errorMsg:"请输入验证码"});var i=o.default.check(t,a);i?(this.dynacodeData.isSend=!0,this.dynacodeData.timer=setInterval((function(){e.dynacodeData.seconds--,e.dynacodeData.codeText=e.dynacodeData.seconds+"s后可重新获取"}),1e3),this.$api.sendRequest({url:"/api/login/mobileCode",data:t,success:function(t){t.code>=0?e.formData.key=t.data.key:(e.refreshDynacodeData(),e.$util.showToast({title:t.message}))},fail:function(){e.$util.showToast({title:"request:fail"}),e.refreshDynacodeData()}})):this.$util.showToast({title:o.default.error})}},refreshDynacodeData:function(){this.getCaptcha(),clearInterval(this.dynacodeData.timer),this.dynacodeData={seconds:120,timer:null,codeText:"获取动态码",isSend:!1}},getMemberInfo:function(e){var t=this;this.$api.sendRequest({url:"/api/member/info",success:function(a){a.code>=0&&(t.$store.commit("setMemberInfo",a.data),e&&e())}})}},watch:{"dynacodeData.seconds":{handler:function(e,t){0==e&&this.refreshDynacodeData()},immediate:!0,deep:!0}}};t.default=r},"72b8":function(e,t,a){var i=a("77cf");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("03d23b29",i,!0,{sourceMap:!1,shadowMode:!1})},"77cf":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-5e100056] .uni-scroll-view{background-color:#fff}[data-v-5e100056] .uni-scroll-view::-webkit-scrollbar{\r\n  /* 隐藏滚动条，但依旧具备可以滚动的功能 */display:none}uni-page-body[data-v-5e100056]{width:100%;background:#fff!important}body.?%PAGE?%[data-v-5e100056]{background:#fff!important}.align-right[data-v-5e100056]{color:#838383}.container[data-v-5e100056]{width:100vw;height:100vh}.header-wrap[data-v-5e100056]{width:80%;margin:calc(%?120?% + 44px) auto 0;background-repeat:no-repeat;background-size:contain;background-position:bottom;position:relative}.header-wrap .title[data-v-5e100056]{font-size:%?60?%;font-weight:700}.body-wrap[data-v-5e100056]{margin-top:%?100?%;padding-bottom:%?100?%}.body-wrap .form-wrap[data-v-5e100056]{width:80%;margin:0 auto}.body-wrap .form-wrap .input-wrap[data-v-5e100056]{position:relative;width:100%;box-sizing:border-box;height:%?60?%;margin-top:%?60?%}.body-wrap .form-wrap .input-wrap .iconfont[data-v-5e100056]{width:%?60?%;height:%?60?%;position:absolute;left:0;right:0;line-height:%?60?%;font-size:%?32?%;color:#303133;font-weight:600}.body-wrap .form-wrap .input-wrap .content[data-v-5e100056]{display:flex;height:%?60?%;border-bottom:%?2?% solid #eee;align-items:center}.body-wrap .form-wrap .input-wrap .content .input[data-v-5e100056]{flex:1;height:%?60?%;line-height:%?60?%;font-size:%?28?%}.body-wrap .form-wrap .input-wrap .content .input-placeholder[data-v-5e100056]{font-size:%?28?%;color:#bfbfbf;line-height:%?60?%}.body-wrap .form-wrap .input-wrap .content .captcha[data-v-5e100056]{margin:%?4?%;height:%?52?%;width:%?140?%}.body-wrap .form-wrap .input-wrap .content .dynacode[data-v-5e100056]{line-height:%?60?%;font-size:%?24?%}.body-wrap .form-wrap .input-wrap .content .area-code[data-v-5e100056]{line-height:%?60?%;margin-right:%?20?%;font-size:%?28?%}.body-wrap .forget-section[data-v-5e100056]{display:flex;width:80%;margin:%?40?% auto}.body-wrap .forget-section uni-view[data-v-5e100056]{flex:1;font-size:%?24?%;line-height:1}.body-wrap .btn_view[data-v-5e100056]{width:100%;margin:%?94?% auto auto;padding:0 %?30?%;box-sizing:border-box}.body-wrap .login-btn[data-v-5e100056]{height:%?90?%;line-height:%?90?%;border-radius:%?90?%;text-align:center;border:%?2?% solid;width:100%;margin:0}.body-wrap .auth-login[data-v-5e100056]{margin-top:%?20?%;width:calc(100% - %?4?%);height:%?90?%;line-height:%?90?%;border-radius:%?10?%;border:%?2?% solid;color:#fff;text-align:center;margin-left:0;background-color:#fff}.body-wrap .auth-login uni-text[data-v-5e100056]{color:#d0d0d0}.body-wrap .auth-login .iconfont[data-v-5e100056]{font-size:%?70?%}.body-wrap .auth-login .icon-weixin[data-v-5e100056]{color:#1aad19}.body-wrap .regisiter-agreement[data-v-5e100056]{text-align:center;margin-top:%?30?%;color:#838383;line-height:%?60?%;font-size:%?24?%}.body-wrap .regisiter-agreement .tips[data-v-5e100056]{margin:0 %?10?%}.body-wrap .regisiter-agreement .is-agree[data-v-5e100056]{font-size:%?26?%}.login-btn-box[data-v-5e100056]{margin-top:%?50?%}.login-btn-box.active[data-v-5e100056]{margin:%?30?% 0 %?50?%}.back-btn[data-v-5e100056]{font-size:%?52?%;position:fixed;left:%?24?%;top:%?72?%;z-index:9;color:#000}.login-mode-box[data-v-5e100056]{display:flex;justify-content:flex-end;color:#909399;margin:auto;margin-top:%?44?%;font-size:%?26?%;width:80%}.auth-index[data-v-5e100056]{width:100vw;height:100vh;box-sizing:border-box;padding:0 %?44?%}.auth-index .website-logo[data-v-5e100056]{padding-top:%?154?%;display:flex;justify-content:center}.auth-index .website-logo .logo[data-v-5e100056]{width:%?300?%;height:%?90?%;display:block}.auth-index .login-desc[data-v-5e100056]{color:#333;font-size:%?28?%;text-align:center;line-height:%?34?%;min-height:%?34?%;margin-top:%?40?%}.auth-index .login-area[data-v-5e100056]{margin-top:%?181?%;display:flex;flex-direction:column;align-items:center}.auth-index .login-area .btn[data-v-5e100056]{background-color:#fff;border:%?2?% solid var(--base-color);color:var(--base-color);box-sizing:border-box;width:%?630?%;height:%?88?%;font-size:%?26?%;border-radius:%?44?%;line-height:%?86?%;font-weight:500;text-align:center;margin-bottom:%?40?%}.auth-index .login-area .btn.quick-login[data-v-5e100056]{color:#fff;background-color:var(--base-color)}.auth-index .login-area .agreement[data-v-5e100056]{display:flex;align-items:center;justify-content:center;width:100%;margin-top:%?28?%;padding:%?10?% 0;font-size:%?24?%;line-height:1}.auth-index .login-area .agreement .agree[data-v-5e100056]{color:#c8c9cc;font-size:%?26?%;line-height:%?22?%;margin-right:%?12?%}.auth-index .login-area .agreement .tips-text[data-v-5e100056]{display:flex;align-items:center;line-height:%?28?%;font-size:%?24?%}.auth-index .login-area .agreement .tips-text .tips[data-v-5e100056]{color:#666}.auth-index .login-area .footer[data-v-5e100056]{margin-top:%?200?%;width:100%;box-sizing:border-box;display:flex;flex-direction:column;align-items:center}.auth-index .login-area .footer .text[data-v-5e100056]{font-size:%?26?%;line-height:%?36?%;color:#333;text-align:center;margin-bottom:%?30?%;font-weight:400}.auth-index .login-area .footer .mine[data-v-5e100056]{width:%?80?%;height:%?80?%;line-height:%?78?%;border:%?2?% solid #ddd;border-radius:50%;font-size:%?46?%;display:flex;align-items:center;justify-content:center;color:var(--base-color)}.auth-index .login-area .footer .mode-name[data-v-5e100056]{font-size:%?24?%;line-height:%?36?%;color:#999;font-weight:400;margin-top:%?30?%}',""]),e.exports=t},7854:function(e,t,a){"use strict";a.r(t);var i=a("8ba8"),o=a("f48d");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);var r=a("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=s.exports},"8ba8":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){}));var i=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},a3d0:function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return i}));var i={pageMeta:a("7854").default,loadingCover:a("c003").default,registerReward:a("349c").default},o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":e.themeColor}}),a("v-uni-scroll-view",{staticClass:"container",attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"header-wrap"},[a("v-uni-view",{staticClass:"title"},[e._v("登录")]),""!=e.registerConfig.register?a("v-uni-view",{staticClass:"regisiter-agreement"},[a("v-uni-text",{staticClass:"color-tip"},[e._v("还没有账号,")]),a("v-uni-text",{staticClass:"color-base-text",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toRegister.apply(void 0,arguments)}}},[e._v("立即注册")])],1):e._e()],1),a("v-uni-view",{staticClass:"body-wrap"},[a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"mobile"==e.loginMode,expression:"loginMode == 'mobile'"}],staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"area-code"},[e._v("+86")]),a("v-uni-input",{staticClass:"input",attrs:{type:"number",placeholder:"仅限中国大陆手机号登录","placeholder-class":"input-placeholder",maxlength:"11"},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}})],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"account"==e.loginMode,expression:"loginMode == 'account'"}],staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-input",{staticClass:"input",attrs:{type:"text",placeholder:"请输入账号","placeholder-class":"input-placeholder"},model:{value:e.formData.account,callback:function(t){e.$set(e.formData,"account",t)},expression:"formData.account"}})],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"account"==e.loginMode,expression:"loginMode == 'account'"}],staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-input",{staticClass:"input",attrs:{type:"password",placeholder:"请输入密码","placeholder-class":"input-placeholder"},model:{value:e.formData.password,callback:function(t){e.$set(e.formData,"password",t)},expression:"formData.password"}}),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"account"==e.loginMode,expression:"loginMode == 'account'"}],staticClass:"align-right"},[a("v-uni-text",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.forgetPassword.apply(void 0,arguments)}}},[e._v("忘记密码?")])],1)],1)],1),1==e.captchaConfig?a("v-uni-view",{staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-input",{staticClass:"input",attrs:{type:"text",placeholder:"请输入验证码","placeholder-class":"input-placeholder"},model:{value:e.formData.vercode,callback:function(t){e.$set(e.formData,"vercode",t)},expression:"formData.vercode"}}),a("v-uni-image",{staticClass:"captcha",attrs:{src:e.captcha.img},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getCaptcha.apply(void 0,arguments)}}})],1)],1):e._e(),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"mobile"==e.loginMode,expression:"loginMode == 'mobile'"}],staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-input",{staticClass:"input",attrs:{type:"text",placeholder:"请输入动态码","placeholder-class":"input-placeholder"},model:{value:e.formData.dynacode,callback:function(t){e.$set(e.formData,"dynacode",t)},expression:"formData.dynacode"}}),a("v-uni-view",{staticClass:"dynacode",class:120==e.dynacodeData.seconds?"color-base-text":"color-tip",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendMobileCode.apply(void 0,arguments)}}},[e._v(e._s(e.dynacodeData.codeText))])],1)],1)],1),a("v-uni-view",{staticClass:"login-mode-box"},[a("v-uni-text",{directives:[{name:"show",rawName:"v-show",value:"mobile"==e.loginMode&&-1!=e.registerConfig.login.indexOf("username"),expression:"loginMode == 'mobile' && registerConfig.login.indexOf('username') != -1"}],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.switchLoginMode.apply(void 0,arguments)}}},[e._v("使用账号登录")]),a("v-uni-text",{directives:[{name:"show",rawName:"v-show",value:"account"==e.loginMode&&-1!=e.registerConfig.login.indexOf("mobile"),expression:"loginMode == 'account' && registerConfig.login.indexOf('mobile') != -1"}],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.switchLoginMode.apply(void 0,arguments)}}},[e._v("使用手机号登录")])],1),a("v-uni-view",{staticClass:"btn_view"},[a("v-uni-button",{staticClass:"login-btn color-base-border color-base-bg",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.login.apply(void 0,arguments)}}},[e._v("登录")])],1),e.registerConfig.agreement_show?a("v-uni-view",{staticClass:"regisiter-agreement"},[a("v-uni-text",{staticClass:"iconfont is-agree",class:e.isAgree?"icon-yuan_checked color-base-text":"icon-yuan_checkbox",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isAgree=!e.isAgree}}}),a("v-uni-text",{staticClass:"tips"},[e._v("请阅读并同意")]),a("v-uni-text",{staticClass:"color-base-text",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toAggrement("PRIVACY")}}},[e._v("《隐私协议》")]),a("v-uni-text",{staticClass:"tips"},[e._v("和")]),a("v-uni-text",{staticClass:"color-base-text",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toAggrement("SERVICE")}}},[e._v("《用户协议》")])],1):e._e()],1),a("loading-cover",{ref:"loadingCover"}),a("register-reward",{ref:"registerReward"})],1)],1)},n=[]},b007:function(e,t,a){"use strict";var i=a("e809"),o=a.n(i);o.a},b2f8:function(e,t,a){"use strict";a.r(t);var i=a("6501"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},cc1b:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(e){return-1!==["dark","light"].indexOf(e)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var e=this,t=getCurrentPages()[0];this.$pageVm=t.$vm||t,uni.onWindowResize((function(t){e.$emit("resize",t)})),this.$pageVm.$on("hook:onPageScroll",(function(t){e.$emit("scroll",t)})),this.$watch("backgroundTextStyle",(function(){e.setBackgroundTextStyle()})),this.$watch((function(){return[e.rootFontSize,e.pageStyle]}),(function(){e.setPageMeta()})),this.$watch((function(){return[e.backgroundColor,e.backgroundColorTop,e.backgroundColorBottom]}),(function(){e.setBackgroundColor()})),this.$watch((function(){return[e.scrollTop,e.scrollDuration]}),(function(){e.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(e,t){e.setStyle({pullToRefresh:{support:t,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var e=this,t=String(this.scrollTop);if(-1!==t.indexOf("rpx")&&(t=uni.upx2px(t.replace("rpx",""))),t=parseFloat(t),!isNaN(t)){var a=function a(o){o.scrollTop===t&&(e.$pageVm.$off("hook:onPageScroll",a),e.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:t,duration:this.scrollDuration,success:function(){e.$pageVm.$on("hook:onPageScroll",a)}})}}}};t.default=o},e809:function(e,t,a){var i=a("3e2b");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("72e83a7e",i,!0,{sourceMap:!1,shadowMode:!1})},f48d:function(e,t,a){"use strict";a.r(t);var i=a("cc1b"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a}}]);