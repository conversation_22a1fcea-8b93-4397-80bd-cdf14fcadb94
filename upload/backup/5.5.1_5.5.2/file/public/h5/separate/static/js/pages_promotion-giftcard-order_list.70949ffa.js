(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-giftcard-order_list"],{"08fc":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.active[data-v-145757b0]{border-bottom:%?0?% solid}.cf-container[data-v-145757b0]{background:#fff;overflow:hidden}.tab[data-v-145757b0]{display:flex;justify-content:space-between;height:%?86?%}.tab > uni-view[data-v-145757b0]{text-align:center;width:33%;height:%?86?%}.tab > uni-view uni-text[data-v-145757b0]{display:inline-block;line-height:%?86?%;height:%?80?%;font-size:%?30?%}.card-item[data-v-145757b0]{background:#fff;margin:%?20?% %?30?%;border-radius:%?10?%;padding:%?30?% %?30?%}.card-item .card-head[data-v-145757b0]{display:flex;justify-content:space-between;align-items:baseline}.card-item .card-head .head-box[data-v-145757b0]{display:flex;align-items:center}.card-item .card-head .head-box .head-icon[data-v-145757b0]{width:%?34?%;margin-right:%?10?%;margin-bottom:%?-4?%}.card-item .card-head .head-box .head-icon uni-image[data-v-145757b0]{width:100%;max-height:%?28?%}.card-item .card-head .head-box .head-title[data-v-145757b0]{display:flex;align-items:center}.card-item .card-head .head-box .head-title .head-type[data-v-145757b0]{font-weight:700;font-size:%?24?%}.card-item .card-head .head-box .head-title .head-time[data-v-145757b0]{margin-left:%?20?%;font-size:%?20?%}.card-item .card-head .pay-status[data-v-145757b0]{font-size:%?24?%;color:var(--main-color);line-height:1}.card-item .goods-list[data-v-145757b0]{border-top:%?0?% solid #f0f0f0;padding:%?30?% 0;display:flex}.card-item .goods-list .goods-left[data-v-145757b0]{display:flex;width:calc(100% - %?108?%);overflow:hidden;white-space:nowrap;position:relative;align-items:center}.card-item .goods-list .goods-left uni-image[data-v-145757b0]{width:%?108?%;max-height:%?108?%;margin-right:%?22?%;flex-shrink:0;border-radius:%?16?%}.card-item .goods-list .goods-left[data-v-145757b0]:after{content:" ";box-shadow:%?-4?% 0 %?24?% rgba(0,0,0,.8);width:%?1?%;height:%?80?%;right:%?-1?%;top:%?14?%;position:absolute;background:hsla(0,0%,100%,0)}.card-item .goods-list .goods-more[data-v-145757b0]{width:%?108?%;height:%?108?%;display:flex;align-items:center;justify-content:center;font-size:%?26?%;position:relative}.card-item .goods-list .goods-more uni-text[data-v-145757b0]{font-size:%?28?%;line-height:1}.card-item .card-content[data-v-145757b0]{display:flex;justify-content:space-between;margin-top:%?20?%}.card-item .card-content .card-left[data-v-145757b0]{display:flex}.card-item .card-content .card-left .card-img[data-v-145757b0]{width:%?145?%;height:%?88?%;margin-right:%?15?%;border-radius:%?10?%;overflow:hidden}.card-item .card-content .card-left .card-img uni-image[data-v-145757b0]{width:100%;max-height:%?88?%}.card-item .card-content .card-left .card-name[data-v-145757b0]{width:%?300?%;font-weight:700;font-size:%?26?%;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.card-item .card-content .card-price[data-v-145757b0]{display:flex;flex-direction:column;align-items:end;font-size:%?24?%;color:#666}.card-item .card-content .card-price > uni-view[data-v-145757b0]{font-size:%?24?%;color:#666}.card-item .order-price[data-v-145757b0]{display:flex;align-items:flex-end;justify-content:flex-end;width:100%;margin-top:%?10?%}.card-item .order-price > uni-view[data-v-145757b0]{font-size:%?24?%}.card-item .order-price > uni-view[data-v-145757b0]:last-child{font-size:%?28?%;font-weight:700;margin-left:%?10?%}.card-item .button[data-v-145757b0]{display:flex;align-items:flex-end;justify-content:flex-end;width:100%;margin-top:%?20?%}.card-item .button .button-left[data-v-145757b0]{margin-right:%?20?%}.card-item .button .button-left uni-button[data-v-145757b0]{border:%?2?% solid #979797;color:#666}.card-item .button .button-right uni-button[data-v-145757b0]{border:%?2?% solid var(--giftcard-promotion-color);color:var(--giftcard-promotion-color)}.card-item .button uni-button[data-v-145757b0]{background-color:#fff;border-radius:%?50?%;line-height:1;height:%?50?%;display:flex;align-items:center}.card-no-data[data-v-145757b0]{width:100%;text-align:center}.card-no-data .card-image[data-v-145757b0]{margin-top:%?200?%;display:flex}.card-no-data .card-image uni-image[data-v-145757b0]{width:%?340?%;max-height:%?290?%;margin:auto auto}.card-no-data .text[data-v-145757b0]{font-size:%?26?%}.card-no-data .btn[data-v-145757b0]{margin-top:%?26?%}.card-no-data .btn uni-button[data-v-145757b0]{border-radius:%?80?%;padding:0 %?50?%;font-size:%?30?%;background-color:var(--giftcard-promotion-color);height:%?60?%;line-height:%?60?%}',""]),t.exports=e},"0ebf":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("c223");var a={data:function(){return{order_id:"",order_status:"all",orderList:[],price:"",out_trade_no:""}},methods:{changeState:function(t){this.list=[],this.order_status=t,this.$refs.mescroll.refresh()},getData:function(t){var e=this;this.$api.sendRequest({url:"/giftcard/api/order/lists",data:{page_size:t.size,page:t.num,order_status:this.order_status},success:function(i){var a=[];0==i.code&&i.data&&(a=i.data.list),t.endSuccess&&t.endSuccess(a.length),1==t.num&&(e.orderList=[]),e.orderList=e.orderList.concat(a),setTimeout((function(){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}),300)},fail:function(i){t.endErr&&t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},openChoosePayment:function(t,e,i){this.order_id=t,this.price=e,this.out_trade_no=i,this.storeToken?(uni.setStorageSync("paySource","giftcard"),this.$refs.choosePaymentPopup.open()):this.$util.showToast({title:"您尚未登录，请先登录"})},gotoBuy:function(){this.$refs.choosePaymentPopup.getPayInfo(this.out_trade_no)},closeOrder:function(t){var e=this;uni.showModal({title:"提示",content:"您确定要关闭该订单吗？",success:function(i){i.confirm&&e.$api.sendRequest({url:"/giftcard/api/order/close",data:{order_id:t},success:function(t){t.code>=0?e.$refs.mescroll.refresh():e.$util.showToast({title:t.message})}})}})},orderDetail:function(t){this.$util.redirectTo("/pages_promotion/giftcard/order_detail",{order_id:t})}}};e.default=a},"4ef7":function(t,e,i){"use strict";i.r(e);var a=i("dda8"),o=i("b874");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("63a7");var r=i("828b"),d=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"145757b0",null,!1,a["a"],void 0);e["default"]=d.exports},"63a7":function(t,e,i){"use strict";var a=i("80cb"),o=i.n(a);o.a},7854:function(t,e,i){"use strict";i.r(e);var a=i("8ba8"),o=i("f48d");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);var r=i("828b"),d=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=d.exports},"80cb":function(t,e,i){var a=i("08fc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("3dad0984",a,!0,{sourceMap:!1,shadowMode:!1})},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},b874:function(t,e,i){"use strict";i.r(e);var a=i("0ebf"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(o){o.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=o},dda8:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){return a}));var a={pageMeta:i("7854").default,loadingCover:i("c003").default,nsPayment:i("7aec").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",[i("mescroll-uni",{ref:"mescroll",attrs:{size:10},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getData.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[i("v-uni-view",{staticClass:"cf-container color-line-border"},[i("v-uni-view",{staticClass:"tab"},[i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeState("all")}}},[i("v-uni-text",{class:"all"==t.order_status?"color-base-text active color-base-border-bottom":""},[t._v("全部")])],1),i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeState("topay")}}},[i("v-uni-text",{class:"topay"==t.order_status?"color-base-text active color-base-border-bottom":""},[t._v("待支付")])],1),i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeState("complete")}}},[i("v-uni-text",{class:"complete"==t.order_status?"color-base-text active color-base-border-bottom":""},[t._v("已完成")])],1),i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeState("close")}}},[i("v-uni-text",{class:"close"==t.order_status?"color-base-text active color-base-border-bottom":""},[t._v("已关闭")])],1)],1)],1),t.orderList.length>0?i("v-uni-view",{staticClass:"card-box"},t._l(t.orderList,(function(e,a){return i("v-uni-view",{key:a},[i("v-uni-view",{staticClass:"card-item"},[i("v-uni-view",{staticClass:"card-head",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.orderDetail(e.order_id)}}},[i("v-uni-view",{staticClass:"head-box"},[i("v-uni-view",{staticClass:"head-icon"},[i("v-uni-image",{attrs:{src:t.$util.img("balance"==e.card_right_type?"public/uniapp/giftcard/order-icon-recharge.png":"public/uniapp/giftcard/order-icon-gift.png"),mode:"widthFix"}})],1),i("v-uni-view",{staticClass:"head-title"},[i("v-uni-view",{staticClass:"head-type"},[t._v(t._s("balance"==e.card_right_type?"储值卡":"礼品卡"))]),i("v-uni-view",{staticClass:"head-time"},[t._v(t._s(t.$util.timeStampTurnTime(e.create_time)))])],1)],1),i("v-uni-view",{staticClass:"pay-status"},[t._v(t._s(e.order_status_name))])],1),i("v-uni-view",{staticClass:"card-content",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.orderDetail(e.order_id)}}},[i("v-uni-view",{staticClass:"card-left"},[i("v-uni-view",{staticClass:"card-img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.card_cover),mode:"aspectFill"}})],1),i("v-uni-view",{staticClass:"card-name"},[t._v(t._s(e.order_name))])],1),i("v-uni-view",{staticClass:"card-price"},[i("v-uni-view",[t._v("￥"+t._s(e.card_price))]),i("v-uni-view",[t._v("x"+t._s(e.num))])],1)],1),i("v-uni-view",{staticClass:"order-price"},[i("v-uni-view",[t._v("￥"+t._s(e.pay_money))])],1),"topay"==e.order_status?i("v-uni-view",{staticClass:"button"},[i("v-uni-view",{staticClass:"button-left"},[i("v-uni-button",{staticClass:"mini",attrs:{size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.orderDetail(e.order_id)}}},[t._v("查看详情")])],1),i("v-uni-view",{staticClass:"button-right"},[i("v-uni-button",{staticClass:"mini",attrs:{size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.openChoosePayment(e.order_id,e.pay_money,e.out_trade_no)}}},[t._v("立即支付")])],1)],1):t._e(),"complete"==e.order_status?i("v-uni-view",{staticClass:"button"},[i("v-uni-view",{staticClass:"button-left"},[i("v-uni-button",{staticClass:"mini",attrs:{size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.orderDetail(e.order_id)}}},[t._v("查看详情")])],1),i("v-uni-view",{staticClass:"button-right"},[i("v-uni-button",{staticClass:"mini",attrs:{size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/pages_promotion/giftcard/list",{order_id:e.order_id})}}},[t._v("查看卡包")])],1)],1):t._e()],1)],1)})),1):i("v-uni-view",{staticClass:"card-no-data"},[i("v-uni-view",{staticClass:"card-image"},[i("v-uni-image",{attrs:{mode:"widthFix",src:t.$util.img("public/uniapp/giftcard/no_order.png")}})],1),i("v-uni-view",{staticClass:"text"},[t._v("暂无订单记录")]),i("v-uni-view",{staticClass:"btn"},[i("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_promotion/giftcard/index")}}},[t._v("去挑礼物")])],1)],1)],1)],2),i("loading-cover",{ref:"loadingCover"}),i("ns-payment",{ref:"choosePaymentPopup",attrs:{payMoney:t.price},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.gotoBuy.apply(void 0,arguments)}}})],1)],1)},n=[]},f48d:function(t,e,i){"use strict";i.r(e);var a=i("cc1b"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a}}]);