(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-pinfan-list"],{"015d":function(t,e,i){"use strict";i.r(e);var n=i("0f46"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"09f8":function(t,e,i){var n=i("d66c");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("0f67d003",n,!0,{sourceMap:!1,shadowMode:!1})},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=n},2407:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.advList.length?i("v-uni-view",{class:["container-box",t.className]},[t.advList.length>1?i("v-uni-swiper",{staticClass:"item-wrap",style:{height:t.swiperHeight+"px"},attrs:{"indicator-dots":t.advList.length>1,"indicator-active-color":"#ffffff",autoplay:!0,interval:3e3,duration:1e3,current:t.currentIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSwiper.apply(void 0,arguments)}}},t._l(t.advList,(function(e,n){return i("v-uni-swiper-item",{key:n,on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.jumppage(e.adv_url)}}},[i("v-uni-view",{staticClass:"image-box"},[i("v-uni-image",{attrs:{src:t.$util.img(e.adv_image),mode:"widthFix",id:"content-wrap"+n}})],1)],1)})),1):i("v-uni-view",{staticClass:"container-box item-wrap"},[i("v-uni-image",{attrs:{src:t.$util.img(t.advList[0]["adv_image"]),mode:"widthFix","lazy-load":"true"},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imageLoad.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumppage(t.advList[0].adv_url)}}})],1)],1):t._e()},a=[]},"2ae6":function(t,e,i){"use strict";var n=i("09f8"),a=i.n(n);a.a},"3b73":function(t,e,i){"use strict";var n=i("d548"),a=i.n(n);a.a},"3d58":function(t,e,i){"use strict";i.r(e);var n=i("9243"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"43db":function(t,e,i){"use strict";i.r(e);var n=i("55e2"),a=i("3d58");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("2ae6");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"65fbc3dd",null,!1,n["a"],void 0);e["default"]=s.exports},"55e2":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.text?i("v-uni-view",{staticClass:"uni-tag",class:[!0===t.disabled||"true"===t.disabled?"uni-tag--disabled":"",!0===t.inverted||"true"===t.inverted?"uni-tag--inverted":"",!0===t.circle||"true"===t.circle?"uni-tag--circle":"",!0===t.mark||"true"===t.mark?"uni-tag--mark":"","uni-tag--"+t.size,"uni-tag--"+t.type],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick()}}},[t._v(t._s(t.text))]):t._e()},a=[]},6102:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47");var n={name:"ns-advert",props:{keyword:{type:String},className:{type:String}},data:function(){return{advList:[],isImage:!1,swiperHeight:150,currentIndex:0}},created:function(){this.getAdvList()},methods:{getAdvList:function(){var t=this;this.$api.sendRequest({url:"/api/adv/detail",data:{keyword:this.keyword},success:function(e){if(0==e.code){var i=e.data.adv_list;for(var n in i)i[n].adv_url&&(i[n].adv_url=JSON.parse(i[n].adv_url));t.advList=e.data.adv_list,t.$nextTick((function(){t.setSwiperHeight()}))}}})},jumppage:function(t){this.$util.diyRedirectTo(t)},imageLoad:function(t){this.isImage=!0},changeSwiper:function(t){var e=this;this.currentIndex=t.detail.current,this.$nextTick((function(){e.setSwiperHeight()}))},setSwiperHeight:function(){var t=this;this.advList.length>1&&setTimeout((function(){var e="#content-wrap"+t.currentIndex,i=uni.createSelectorQuery().in(t);i.select(e).boundingClientRect(),i.exec((function(e){e&&e[0]&&(t.swiperHeight=e[0].height)}))}),10)}}};e.default=n},"61a8":function(t,e,i){"use strict";i.r(e);var n=i("8ab3"),a=i("c630");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("3b73");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"56515bf9",null,!1,n["a"],void 0);e["default"]=s.exports},7854:function(t,e,i){"use strict";i.r(e);var n=i("8ba8"),a=i("f48d");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"7bce":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.ns-adv[data-v-56515bf9]{margin:%?20?% %?30?%;border-radius:%?10?%;overflow:hidden;line-height:1}.ns-adv uni-image[data-v-56515bf9]{width:100%}.pinfan-step[data-v-56515bf9]{font-size:%?30?%;padding:15px;background:#fff;margin:10px 15px}.pinfan-step .pinfan-title[data-v-56515bf9]{display:flex;justify-content:center;align-items:center}.pinfan-step .pinfan-title uni-image[data-v-56515bf9]{width:%?106?%;height:%?8?%!important}.pinfan-step .pinfan-title uni-view[data-v-56515bf9]{margin:0 %?14?%}.pinfan-step .step[data-v-56515bf9]{display:flex;align-items:center;justify-content:space-around;margin-top:%?40?%}.pinfan-step .step uni-view[data-v-56515bf9]{width:%?100?%;text-align:center}.pinfan-step .step uni-view uni-image[data-v-56515bf9]{width:%?48?%;height:%?48?%}.pinfan-step .step uni-view uni-view[data-v-56515bf9]{font-size:%?24?%}.pinfan-step .step uni-image[data-v-56515bf9]{width:%?40?%;height:%?10?%}.lineheight-clear[data-v-56515bf9]{line-height:1!important}.goods-list.single-column .goods-item[data-v-56515bf9]{padding:%?26?%;background:#fff;margin:%?20?% %?30?%;border-radius:%?10?%;position:relative\r\n  /* .introduction {\r\n\t\t\tmargin-top: 10rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\t.pin-intro{\r\n\t\t\t\t// background-color: #FFF0F0;\r\n\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\tline-height: 1;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t}\r\n\t\t\t.tuan-intro{\r\n\t\t\t\tborder: 1px solid;\r\n\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\tline-height: 1;\r\n\t\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t}\r\n\t\t} */}.goods-list.single-column .goods-item .step-status[data-v-56515bf9]{background-color:#fff5ed;padding:%?18?% %?26?%;border-radius:%?10?%;line-height:1;margin-top:%?30?%}.goods-list.single-column .goods-item .list-item[data-v-56515bf9]{display:flex}.goods-list.single-column .goods-item .goods-img[data-v-56515bf9]{width:%?200?%;height:%?200?%;overflow:hidden;border-radius:%?10?%;margin-right:%?20?%}.goods-list.single-column .goods-item .goods-img uni-image[data-v-56515bf9]{width:100%;height:100%}.goods-list.single-column .goods-item .goods-tag[data-v-56515bf9]{color:#fff;line-height:1;padding:%?8?% %?12?%;position:absolute;border-top-left-radius:%?10?%;border-bottom-right-radius:%?10?%;top:%?26?%;left:%?26?%;font-size:%?22?%}.goods-list.single-column .goods-item .info-wrap[data-v-56515bf9]{flex:1;display:flex;flex-direction:column}.goods-list.single-column .goods-item .name-wrap[data-v-56515bf9]{flex:1\r\n  /* .box {\r\n\t\t\t\tmargin: 20rpx 0;\r\n\t\t\t\twidth: 240rpx;\r\n\t\t\t\theight: 20rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\tbackground-color: #FCECD7;\r\n\t\t\t\t.con {\r\n\t\t\t\t\theight: 20rpx;\r\n\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\tbackground-color: #FDBE6C;\r\n\t\t\t\t}\r\n\t\t\t} */}.goods-list.single-column .goods-item .goods-name[data-v-56515bf9]{line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;height:%?68?%}.goods-list.single-column .goods-item .goods-name uni-text[data-v-56515bf9]{border-radius:2px;margin-right:%?10?%;padding:%?4?% %?12?%}.goods-list.single-column .goods-item .pintuan-info .pinfan-num[data-v-56515bf9]{padding:%?2?% %?12?%;border-radius:%?4?%;font-size:%?20?%}.goods-list.single-column .goods-item .pintuan-info .pinfan-box[data-v-56515bf9]{border-radius:%?4?%;font-size:%?20?%;padding:0 %?12?%;border:%?2?% solid;margin-left:%?14?%}.goods-list.single-column .goods-item .discount-price[data-v-56515bf9]{display:inline-block;font-weight:700;line-height:1;margin-top:%?16?%;color:var(--price-color)}.goods-list.single-column .goods-item .discount-price .unit[data-v-56515bf9]{margin-right:%?6?%}.goods-list.single-column .goods-item .discount-price .txt[data-v-56515bf9]{font-weight:400}.goods-list.single-column .goods-item .pro-info[data-v-56515bf9]{position:relative;margin-top:%?16?%}.goods-list.single-column .goods-item .pro-info .delete-price[data-v-56515bf9]{line-height:1;flex:1;display:flex;align-items:flex-end}.goods-list.single-column .goods-item .pro-info .delete-price .unit[data-v-56515bf9]{margin-right:%?6?%}.goods-list.single-column .goods-item .pro-info .delete-price .txt[data-v-56515bf9]{text-decoration:none}.goods-list.single-column .goods-item .pro-info .detail-btn[data-v-56515bf9]{line-height:1;position:absolute;right:0;bottom:0}.goods-list.single-column .goods-item .member-price-tag[data-v-56515bf9]{display:inline-block;width:%?60?%;line-height:1;margin-left:%?6?%}.goods-list.single-column .goods-item .member-price-tag uni-image[data-v-56515bf9]{width:100%}',""]),t.exports=e},"7e88":function(t,e,i){"use strict";i.r(e);var n=i("2407"),a=i("f016");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("a44f");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"9caa2b5c",null,!1,n["a"],void 0);e["default"]=s.exports},"8ab3":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={pageMeta:i("7854").default,nsAdv:i("7e88").default,nsEmpty:i("52a6").default,hoverNav:i("c1f1").default,loadingCover:i("c003").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",[t.addonIsExist.pinfan?i("mescroll-uni",{ref:"mescroll",attrs:{size:10},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getData.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[i("v-uni-view",{staticClass:"ns-adv"},[i("ns-adv",{attrs:{keyword:"NS_PINFAN"}})],1),i("v-uni-view",{staticClass:"pinfan-step"},[i("v-uni-view",{staticClass:"pinfan-title"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/pinfan/rebate_left.png"),mode:""}}),i("v-uni-view",{staticClass:"step-title color-base-text"},[t._v("拼团返利玩法")]),i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/pinfan/rebate_right.png"),mode:""}})],1),i("v-uni-view",{staticClass:"step"},[i("v-uni-view",[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/pinfan/open_group.png"),mode:""}}),i("v-uni-view",[t._v("支付开团或参团")])],1),i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/pinfan/spot.png"),mode:""}}),i("v-uni-view",[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/pinfan/deliver_goods.png"),mode:""}}),i("v-uni-view",[t._v("拼中发货未中退款")])],1),i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/pinfan/spot.png"),mode:""}}),i("v-uni-view",[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/pinfan/gift.png"),mode:""}}),i("v-uni-view",[t._v("未拼中获得奖励")])],1)],1)],1),t.dataList.length?i("v-uni-view",{staticClass:"goods-list single-column"},t._l(t.dataList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"goods-item margin-bottom"},[i("v-uni-view",{staticClass:"list-item"},[i("v-uni-view",{staticClass:"goods-img",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-image",{attrs:{src:t.goodsImg(e.goods_image),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError(n)}}}),""!=t.goodsTag(e)?i("v-uni-view",{staticClass:"color-base-bg goods-tag"},[t._v(t._s(t.goodsTag(e)))]):t._e()],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap"},[i("v-uni-view",{staticClass:"goods-name",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[t._v(t._s(e.goods_name))]),i("v-uni-view",{staticClass:"pintuan-info"},[i("v-uni-text",{staticClass:"color-base-text pinfan-num"},[t._v("已拼"+t._s(e.order_num)+"件")]),i("v-uni-text",{staticClass:"color-base-border color-base-text pinfan-box"},[t._v(t._s(e.pintuan_num)+"人团")])],1)],1),i("v-uni-view",{staticClass:"lineheight-clear"},[i("v-uni-view",{staticClass:"discount-price"},[i("v-uni-text",{staticClass:"unit  price-style small"},[t._v("￥")]),i("v-uni-text",{staticClass:"price  price-style large"},[t._v(t._s(parseFloat(e.pintuan_price).toFixed(2).split(".")[0]))]),i("v-uni-text",{staticClass:"unit  price-style small"},[t._v("."+t._s(parseFloat(e.pintuan_price).toFixed(2).split(".")[1]))])],1)],1),i("v-uni-view",{staticClass:"pro-info"},[1==e.is_single_buy?i("v-uni-view",{staticClass:"delete-price font-size-activity-tag color-tip"},[i("v-uni-text",{staticClass:"font-size-tag lineheight-clear txt"},[t._v("单购价：")]),i("v-uni-text",{staticClass:"unit price-font"},[t._v("￥")]),i("v-uni-text",{staticClass:"price-font"},[t._v(t._s(e.price))])],1):t._e(),i("v-uni-view",{staticClass:"detail-btn",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-button",{attrs:{type:"primary",size:"mini"}},[t._v("立即拼团")])],1)],1)],1)],1),i("v-uni-view",{staticClass:"step-status font-size-tag"},[i("v-uni-text",{staticClass:"color-base-text"},[t._v(t._s(e.pintuan_num))]),t._v("人拼团，"),i("v-uni-text",{staticClass:"color-base-text"},[t._v(t._s(e.chengtuan_num))]),t._v("人拼中，未拼中返实付金额"),4==e.reward_type?i("v-uni-text",{staticClass:"color-base-text"},[t._v(t._s(e.reward_type_num)+"积分")]):t._e(),1==e.reward_type||2==e.reward_type?i("v-uni-text",{staticClass:"color-base-text"},[t._v("￥"+t._s(e.reward_type_num))]):t._e(),3==e.reward_type?i("v-uni-text",{staticClass:"color-base-text"},[t._v("优惠券")]):t._e()],1)],1)})),1):t._e(),t.dataList.length?t._e():i("v-uni-view",[i("ns-empty",{attrs:{text:"暂无拼团返利"}})],1)],1)],2):t._e(),i("hover-nav"),i("loading-cover",{ref:"loadingCover"})],1)],1)},o=[]},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},9243:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"UniTag",props:{type:{type:String,default:"default"},size:{type:String,default:"normal"},text:{type:String,default:""},disabled:{type:[String,Boolean],default:!1},inverted:{type:[String,Boolean],default:!1},circle:{type:[String,Boolean],default:!1},mark:{type:[String,Boolean],default:!1}},methods:{onClick:function(){!0!==this.disabled&&"true"!==this.disabled&&this.$emit("click")}}};e.default=n},a2bd:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("c223");var a=n(i("43db")),o=n(i("7e88")),r={components:{uniTag:a.default,nsAdv:o.default},data:function(){return{dataList:[],mpShareData:null}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.pinfan||(e.$util.showToast({title:"商家未开启拼团返利",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member"))},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},methods:{getData:function(t){var e=this;this.$api.sendRequest({url:"/pinfan/api/goods/page",data:{page_size:t.size,page:t.num},success:function(i){var n=[],a=i.message;0==i.code&&i.data?n=i.data.list:e.$util.showToast({title:a}),t.endSuccess(n.length),1==t.num&&(e.dataList=[]),e.dataList=e.dataList.concat(n),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(){t.endErr(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},toDetail:function(t){this.$util.redirectTo("/pages_promotion/pinfan/detail",{pinfan_id:t.pintuan_id})},imgError:function(t){this.dataList[t].goods_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},goodsImg:function(t){var e=t.split(",");return e[0]?this.$util.img(e[0],{size:"mid"}):this.$util.getDefaultImage().goods},goodsTag:function(t){return t.label_name||""}}};e.default=r},a44f:function(t,e,i){"use strict";var n=i("d87f"),a=i.n(n);a.a},a725:function(t,e,i){"use strict";var n=i("ac2a"),a=i.n(n);a.a},ac2a:function(t,e,i){var n=i("f714");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("1a69ffc2",n,!0,{sourceMap:!1,shadowMode:!1})},c1f1:function(t,e,i){"use strict";i.r(e);var n=i("fa1d"),a=i("015d");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("a725");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"c1934e78",null,!1,n["a"],void 0);e["default"]=s.exports},c630:function(t,e,i){"use strict";i.r(e);var n=i("a2bd"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=a},d548:function(t,e,i){var n=i("7bce");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("f288b854",n,!0,{sourceMap:!1,shadowMode:!1})},d66c:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,".uni-tag[data-v-65fbc3dd]{box-sizing:border-box;padding:0 %?32?%;height:%?60?%;line-height:calc(%?60?% - 2px);font-size:%?28?%;display:inline-flex;align-items:center;color:#333;border-radius:%?6?%;background-color:#f8f8f8;border:1px solid #f8f8f8}.uni-tag--circle[data-v-65fbc3dd]{border-radius:%?30?%}.uni-tag--mark[data-v-65fbc3dd]{border-radius:0 %?30?% %?30?% 0}.uni-tag--disabled[data-v-65fbc3dd]{opacity:.5}.uni-tag--small[data-v-65fbc3dd]{height:%?40?%;padding:0 %?16?%;line-height:calc(%?40?% - 2px);font-size:%?24?%}.uni-tag--primary[data-v-65fbc3dd]{color:#fff;background-color:#007aff;border:1px solid #007aff}.uni-tag--primary.uni-tag--inverted[data-v-65fbc3dd]{color:#007aff;background-color:#fff;border:1px solid #007aff}.uni-tag--success[data-v-65fbc3dd]{color:#fff;background-color:#4cd964;border:1px solid #4cd964}.uni-tag--success.uni-tag--inverted[data-v-65fbc3dd]{color:#4cd964;background-color:#fff;border:1px solid #4cd964}.uni-tag--warning[data-v-65fbc3dd]{color:#fff;background-color:#f0ad4e;border:1px solid #f0ad4e}.uni-tag--warning.uni-tag--inverted[data-v-65fbc3dd]{color:#f0ad4e;background-color:#fff;border:1px solid #f0ad4e}.uni-tag--error[data-v-65fbc3dd]{color:#fff;background-color:#dd524d;border:1px solid #dd524d}.uni-tag--error.uni-tag--inverted[data-v-65fbc3dd]{color:#dd524d;background-color:#fff;border:1px solid #dd524d}.uni-tag--inverted[data-v-65fbc3dd]{color:#333;background-color:#fff;border:1px solid #f8f8f8}",""]),t.exports=e},d87f:function(t,e,i){var n=i("d915");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("03d75754",n,!0,{sourceMap:!1,shadowMode:!1})},d915:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-9caa2b5c]{width:100%}.container-box .item-wrap[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-9caa2b5c]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}',""]),t.exports=e},f016:function(t,e,i){"use strict";i.r(e);var n=i("6102"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f48d:function(t,e,i){"use strict";i.r(e);var n=i("cc1b"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f714:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},a=[]}}]);