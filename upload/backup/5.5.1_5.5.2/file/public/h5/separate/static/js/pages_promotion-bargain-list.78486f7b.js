(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-bargain-list"],{"00ba":function(t,o,e){"use strict";e.d(o,"b",(function(){return i})),e.d(o,"c",(function(){return n})),e.d(o,"a",(function(){}));var i=function(){var t=this,o=t.$createElement,e=t._self._c||o;return e("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?e("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?e("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),e("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),e("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),e("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),e("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),e("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():e("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},n=[]},"015d":function(t,o,e){"use strict";e.r(o);var i=e("0f46"),n=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return i[t]}))}(a);o["default"]=n.a},"0473":function(t,o,e){"use strict";e("6a54");var i=e("f5bd").default;Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var n=i(e("2634")),a=i(e("2fdc"));e("bf0f"),e("2797"),e("5ef2"),e("c223"),e("e966"),e("e838");var s=i(e("e12a")),r={components:{uniCountDown:s.default},data:function(){return{dataList:[],dataListing:[],isLoading:!1,isLoad:!1,skuId:0,mpShareData:null,progressBorder:"10",bgColor:""}},onLoad:function(t){var o=this;if(t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var e=decodeURIComponent(t.scene);e=e.split("&"),e.length&&e.forEach((function(t){-1!=t.indexOf("sku_id")&&(o.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}},onShow:function(){var t=this;return(0,a.default)((0,n.default)().mark((function o(){return(0,n.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return setTimeout((function(){t.addonIsExist.bargain||(t.$util.showToast({title:"商家未开启砍价",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.storeToken&&uni.getStorageSync("source_member")&&t.$util.onSourceMember(uni.getStorageSync("source_member")),o.next=4,t.getZoneConfig();case 4:t.$refs.mescroll&&t.$refs.mescroll.refresh(),t.getDataing();case 6:case"end":return o.stop()}}),o)})))()},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},methods:{getZoneConfig:function(){var t=this;return(0,a.default)((0,n.default)().mark((function o(){var e,i;return(0,n.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.next=2,t.$api.sendRequest({url:"/api/config/promotionZoneConfig",data:{name:"bargain"},async:!1});case 2:e=o.sent,i=e.data,i&&(t.bgColor=i.bg_color);case 5:case"end":return o.stop()}}),o)})))()},getData:function(t){var o=this;this.$api.sendRequest({url:"/bargain/api/goods/page",data:{page_size:t.size,page:t.num,is_exclude_bargaining:1},success:function(e){var i=[],n=e.message;0==e.code&&e.data?i=e.data.list:o.$util.showToast({title:n}),t.endSuccess&&t.endSuccess(i.length),1==t.num&&(o.dataList=[]),o.dataList=o.dataList.concat(i),o.isLoad=!0,o.$forceUpdate(),setTimeout((function(){o.$refs.loadingCover&&o.$refs.loadingCover.hide()}),300)},fail:function(){var o=this;t.endErr&&t.endErr(),setTimeout((function(){o.$refs.loadingCover&&o.$refs.loadingCover.hide()}),300)}})},getDataing:function(){var t=this;this.$api.sendRequest({url:"/bargain/api/goods/bargainingList",data:{},success:function(o){for(var e in t.dataListing=o.data,o.data)t.dataListing[e].time=t.$util.countDown(o.data[e].end_time-o.timestamp);t.isLoading=!0,t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(){mescroll.endErr(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},toDetail:function(t){this.$util.redirectTo("/pages_promotion/bargain/detail",{b_id:t.bargain_id})},toDetailP:function(t){this.$util.redirectTo("/pages_promotion/bargain/detail",{b_id:t.bargain_id,l_id:t.launch_id})},imgError:function(t){this.dataList[t].goods_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},goodsImg:function(t){var o=t.split(",");return o[0]?this.$util.img(o[0],{size:"mid"}):this.$util.getDefaultImage().goods},progress:function(t){var o=(parseInt(t.sale_num)/(parseInt(t.bargain_stock)+parseInt(t.sale_num))*100).toFixed();return"NaN"==o&&(o=0),o},progressP:function(t){var o=((parseFloat(t.price)-parseFloat(t.curr_price))/parseFloat(t.price)*100).toFixed();return"NaN"==o&&(o=0),o},goodsTag:function(t){return t.label_name||""}},onHide:function(){this.$refs.loadingCover&&this.$refs.loadingCover.show()}};o.default=r},"0f46":function(t,o,e){"use strict";e("6a54"),Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var i={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};o.default=i},"11ac":function(t,o,e){"use strict";var i=e("9c4f"),n=e.n(i);n.a},2407:function(t,o,e){"use strict";e.d(o,"b",(function(){return i})),e.d(o,"c",(function(){return n})),e.d(o,"a",(function(){}));var i=function(){var t=this,o=t.$createElement,e=t._self._c||o;return t.advList.length?e("v-uni-view",{class:["container-box",t.className]},[t.advList.length>1?e("v-uni-swiper",{staticClass:"item-wrap",style:{height:t.swiperHeight+"px"},attrs:{"indicator-dots":t.advList.length>1,"indicator-active-color":"#ffffff",autoplay:!0,interval:3e3,duration:1e3,current:t.currentIndex},on:{change:function(o){arguments[0]=o=t.$handleEvent(o),t.changeSwiper.apply(void 0,arguments)}}},t._l(t.advList,(function(o,i){return e("v-uni-swiper-item",{key:i,on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumppage(o.adv_url)}}},[e("v-uni-view",{staticClass:"image-box"},[e("v-uni-image",{attrs:{src:t.$util.img(o.adv_image),mode:"widthFix",id:"content-wrap"+i}})],1)],1)})),1):e("v-uni-view",{staticClass:"container-box item-wrap"},[e("v-uni-image",{attrs:{src:t.$util.img(t.advList[0]["adv_image"]),mode:"widthFix","lazy-load":"true"},on:{load:function(o){arguments[0]=o=t.$handleEvent(o),t.imageLoad.apply(void 0,arguments)},click:function(o){arguments[0]=o=t.$handleEvent(o),t.jumppage(t.advList[0].adv_url)}}})],1)],1):t._e()},n=[]},"3cd0":function(t,o,e){var i=e("f580");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=e("967d").default;n("69a7caca",i,!0,{sourceMap:!1,shadowMode:!1})},4054:function(t,o,e){var i=e("c86c");o=i(!1),o.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.page[data-v-4fa24919]{width:100%;min-height:100vh;background:var(--bargain-promotion-color)}.adv-wrap[data-v-4fa24919]{margin:%?20?% %?30?%;width:auto}.lineheight-clear[data-v-4fa24919]{line-height:1!important}.goods-list.single-column .goods-item[data-v-4fa24919]{padding:%?26?% %?26?% %?20?%;background:#fff;margin:%?20?% %?30?%;border-radius:%?24?%;display:flex;position:relative;flex-direction:column}.goods-list.single-column .goods-item .goods-item-content[data-v-4fa24919]{display:flex}.goods-list.single-column .goods-item .goods-item-bottom[data-v-4fa24919]{display:flex;justify-content:space-between;margin-top:%?10?%;line-height:1}.goods-list.single-column .goods-item .goods-item-bottom .item-bottom-left[data-v-4fa24919]{display:flex;align-items:baseline;margin-top:%?10?%}.goods-list.single-column .goods-item .goods-item-bottom .item-bottom-left .delete-pirce[data-v-4fa24919]{text-decoration:line-through;color:#606266;margin-left:%?20?%}.goods-list.single-column .goods-item .goods-item-bottom .item-bottom-left .txt[data-v-4fa24919]{margin-left:%?10?%;font-weight:%?26?%}.goods-list.single-column .goods-item .goods-item-bottom .item-bottom-left .unit[data-v-4fa24919],\r\n.goods-list.single-column .goods-item .goods-item-bottom .item-bottom-left .price[data-v-4fa24919]{color:var(--bargain-promotion-color)!important}.goods-list.single-column .goods-item .goods-item-bottom .item-bottom-right[data-v-4fa24919]{display:flex;align-items:center;font-weight:700}.goods-list.single-column .goods-item .goods-item-bottom .item-bottom-right uni-button[data-v-4fa24919]{color:#fff;background-color:var(--bargain-promotion-color)}.goods-list.single-column .goods-item .goods-img[data-v-4fa24919]{width:%?200?%;height:%?200?%;overflow:hidden;border-radius:%?10?%;margin-right:%?20?%}.goods-list.single-column .goods-item .goods-img uni-image[data-v-4fa24919]{width:100%;height:100%}.goods-list.single-column .goods-item .goods-tag[data-v-4fa24919]{color:#fff;line-height:1;padding:%?8?% %?12?%;position:absolute;border-top-left-radius:%?10?%;border-bottom-right-radius:%?10?%;top:%?26?%;left:%?26?%;font-size:%?22?%}.goods-list.single-column .goods-item .info-wrap[data-v-4fa24919]{flex:1;display:flex;flex-direction:column;width:calc(100% - %?220?%)}.goods-list.single-column .goods-item .info-wrap .info-sub-title[data-v-4fa24919]{color:#909399;font-size:%?24?%;margin-top:%?6?%}.goods-list.single-column .goods-item .info-bottom[data-v-4fa24919]{display:flex;justify-content:space-between;margin-top:%?10?%;align-items:center}.goods-list.single-column .goods-item .info-bottom .sale-box[data-v-4fa24919]{color:var(--bargain-promotion-color)}.goods-list.single-column .goods-item .info-bottom .price-box[data-v-4fa24919]{display:flex;line-height:1}.goods-list.single-column .goods-item .info-bottom .price-box .discount-price[data-v-4fa24919]{display:flex;font-size:%?26?%;line-height:1;margin-top:%?4?%}.goods-list.single-column .goods-item .info-bottom .price-box .delete-price[data-v-4fa24919]{display:flex;font-size:%?26?%;line-height:1;margin-top:%?4?%}.goods-list.single-column .goods-item .info-bottom .price-box .price[data-v-4fa24919]{line-height:1.2;color:var(--bargain-promotion-color)}.goods-list.single-column .goods-item .info-bottom .pro-info[data-v-4fa24919]{line-height:1;display:flex;align-items:center}.goods-list.single-column .goods-item .info-bottom .pro-info .button-border[data-v-4fa24919]{border:%?2?% solid var(--bargain-promotion-color);color:var(--bargain-promotion-color);font-size:%?24?%;padding:%?4?% %?6?%;line-height:1;border-radius:%?4?%;background-color:var(--bargain-promotion-color-shallow);position:relative;border-top-right-radius:0}.goods-list.single-column .goods-item .info-bottom .pro-info .button-border[data-v-4fa24919]::before{content:"";display:block;position:absolute;top:%?-10?%;right:%?-2?%;border-left:%?10?% solid transparent;border-right:0 solid transparent;border-bottom:%?10?% solid var(--bargain-promotion-color)}.goods-list.single-column .goods-item .info-bottom .pro-info .button-border[data-v-4fa24919]::after{content:"";display:block;position:absolute;top:%?-6?%;right:%?0?%;border-left:%?10?% solid transparent;border-right:0 solid transparent;border-bottom:%?10?% solid var(--bargain-promotion-color-shallow)}.goods-list.single-column .goods-item .name-wrap[data-v-4fa24919]{flex:1}.goods-list.single-column .goods-item .goods-name[data-v-4fa24919]{font-size:%?28?%;line-height:1.4;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;font-weight:700;word-wrap:break-word;height:%?80?%}.goods-list.single-column .goods-item .progress-wrap[data-v-4fa24919]{display:flex;position:relative;width:calc(100% - %?32?%);margin:%?10?% auto}.goods-list.single-column .goods-item .progress-wrap uni-progress[data-v-4fa24919]{flex:1}.goods-list.single-column .goods-item .progress-wrap .progress-point[data-v-4fa24919]{background:var(--bargain-promotion-color);width:%?24?%;height:%?24?%;border-radius:50%;position:absolute;right:%?-12?%;top:%?-4?%;z-index:1}.goods-list.single-column .goods-item .progress-wrap .progress-select[data-v-4fa24919]{background-color:var(--bargain-promotion-color);color:var(--bargain-promotion-aux-color);width:%?24?%;height:%?24?%;border-radius:50%;position:absolute;left:0;top:%?-4?%;font-size:%?24?%;z-index:11}.goods-list.single-column .goods-item .progress-wrap .progress-select.icon[data-v-4fa24919]{width:%?34?%;height:%?34?%;display:flex;align-items:center;justify-content:center;top:%?-10?%}.goods-list.single-column .goods-item .progress-wrap .progress-select .iconfont[data-v-4fa24919]{background-color:unset;color:#fff;font-size:%?20?%}.goods-list.single-column .goods-item .progress-wrap .txt[data-v-4fa24919]{margin:0 %?100?% 0 %?20?%}.goods-list.single-column .goods-item .member-price-tag[data-v-4fa24919]{display:inline-block;width:%?60?%;line-height:1;margin-left:%?6?%}.goods-list.single-column .goods-item .member-price-tag uni-image[data-v-4fa24919]{width:100%}',""]),t.exports=o},"4b21":function(t,o,e){"use strict";e.r(o);var i=e("0473"),n=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return i[t]}))}(a);o["default"]=n.a},"5c1e":function(t,o,e){"use strict";e.d(o,"b",(function(){return n})),e.d(o,"c",(function(){return a})),e.d(o,"a",(function(){return i}));var i={pageMeta:e("7854").default,nsAdv:e("7e88").default,uniCountDown:e("e12a").default,nsEmpty:e("52a6").default,hoverNav:e("c1f1").default,loadingCover:e("c003").default},n=function(){var t=this,o=t.$createElement,e=t._self._c||o;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":t.themeColor}}),e("v-uni-view",{staticClass:"page",style:{background:t.bgColor}},[t.addonIsExist.bargain?e("mescroll-uni",{ref:"mescroll",attrs:{size:10},on:{getData:function(o){arguments[0]=o=t.$handleEvent(o),t.getData.apply(void 0,arguments)}}},[e("template",{attrs:{slot:"list"},slot:"list"},[e("ns-adv",{attrs:{keyword:"NS_BARGAIN","class-name":"adv-wrap"}}),t.dataList.length>0||t.dataListing.length>0?[t.dataListing.length?e("v-uni-view",{staticClass:"goods-list single-column"},t._l(t.dataListing,(function(o,i){return e("v-uni-view",{key:i,staticClass:"goods-item margin-bottom"},[e("v-uni-view",{staticClass:"goods-item-content"},[e("v-uni-view",{staticClass:"goods-img",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetailP(o)}}},[e("v-uni-image",{attrs:{src:t.goodsImg(o.goods_image),mode:"widthFix"},on:{error:function(o){arguments[0]=o=t.$handleEvent(o),t.imgError(i)}}}),""!=t.goodsTag(o)?e("v-uni-view",{staticClass:"color-base-bg goods-tag"},[t._v(t._s(t.goodsTag(o)))]):t._e()],1),e("v-uni-view",{staticClass:"info-wrap"},[e("v-uni-view",{staticClass:"name-wrap"},[e("v-uni-view",{staticClass:"goods-name",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetailP(o)}}},[t._v(t._s(o.goods_name))]),e("v-uni-text",{staticClass:"info-sub-title"},[t._v("已有"+t._s(o.join_num)+"人参与")]),e("v-uni-view",{staticClass:"progress-wrap"},[e("v-uni-progress",{attrs:{percent:"20","show-info":!1,"stroke-width":"8",backgroundColor:"#FFF4F4",activeColor:t.themeStyle.main_color,"border-radius":"6",percent:t.progressP(o)}}),e("v-uni-view",{staticClass:"progress-point"}),e("v-uni-view",{staticClass:"progress-select icon",style:{left:"calc("+t.progressP(o)+"% - 16rpx)"}},[e("v-uni-text",{staticClass:"iconfont icon-futou"})],1)],1)],1),e("v-uni-view",{staticClass:"info-bottom"},[e("v-uni-view",{staticClass:"price-box"},[e("v-uni-view",{staticClass:"discount-price"},[t._v("已砍"),e("v-uni-text",{staticClass:"price price-font"},[t._v(t._s((o.price-o.curr_price).toFixed(2)))]),t._v("，")],1),e("v-uni-view",{staticClass:"delete-price "},[t._v("还剩"),e("v-uni-text",{staticClass:"price price-font"},[t._v(t._s(t.$lang("common.currencySymbol"))+t._s((o.curr_price-o.floor_price).toFixed(2)))])],1)],1),e("v-uni-view",{staticClass:"pro-info"},[e("v-uni-view",{staticClass:"button-border"},[t._v("免费拿")])],1)],1)],1)],1),e("v-uni-view",{staticClass:"goods-item-bottom"},[e("v-uni-view",{staticClass:"item-bottom-left"},[e("uni-count-down",{attrs:{day:o.time.d,hour:o.time.h,minute:o.time.i,second:o.time.s,color:"#fff",borderColor:"none",splitorColor:"#303133",backgroundColor:"#303133"}}),e("v-uni-view",{staticClass:"color-sub txt"},[t._v("后结束")])],1),e("v-uni-view",{staticClass:"item-bottom-right"},[e("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetailP(o)}}},[t._v("继续砍价")])],1)],1)],1)})),1):t._e(),t.dataList.length?e("v-uni-view",{staticClass:"goods-list single-column"},t._l(t.dataList,(function(o,i){return e("v-uni-view",{key:i,staticClass:"goods-item margin-bottom"},[e("v-uni-view",{staticClass:"goods-item-content"},[e("v-uni-view",{staticClass:"goods-img",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetail(o)}}},[e("v-uni-image",{attrs:{src:t.goodsImg(o.goods_image),mode:"widthFix"},on:{error:function(o){arguments[0]=o=t.$handleEvent(o),t.imgError(i)}}}),""!=t.goodsTag(o)?e("v-uni-view",{staticClass:"color-base-bg goods-tag"},[t._v(t._s(t.goodsTag(o)))]):t._e()],1),e("v-uni-view",{staticClass:"info-wrap"},[e("v-uni-view",{staticClass:"name-wrap"},[e("v-uni-view",{staticClass:"goods-name",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetail(o)}}},[t._v(t._s(o.sku_name))]),e("v-uni-text",{staticClass:"info-sub-title"},[t._v("已有"+t._s(o.join_num)+"人参与")]),e("v-uni-view",{staticClass:"progress-wrap"},[e("v-uni-progress",{attrs:{percent:"20","show-info":!1,"stroke-width":"8",backgroundColor:"#FFF4F4",activeColor:t.themeStyle.main_color,"border-radius":"6",percent:t.progress(o)}}),e("v-uni-view",{staticClass:"progress-point"}),e("v-uni-view",{staticClass:"progress-select icon",style:{left:"calc("+t.progress(o)+"% - 8rpx)"}},[e("v-uni-text",{staticClass:"iconfont icon-futou"})],1)],1)],1),e("v-uni-view",{staticClass:"info-bottom"},[e("v-uni-view",{staticClass:"sale-box price-font"},[t._v("已砍"+t._s(o.sale_num)+"件")]),e("v-uni-view",{staticClass:"pro-info"},[e("v-uni-view",{staticClass:"button-border"},[t._v("免费拿")])],1)],1)],1)],1),e("v-uni-view",{staticClass:"goods-item-bottom"},[e("v-uni-view",{staticClass:"item-bottom-left"},[e("v-uni-view",{staticClass:"txt"},[t._v("底价：")]),e("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(o.floor_price).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit price-style large"},[t._v("."+t._s(parseFloat(o.floor_price).toFixed(2).split(".")[1]))]),e("v-uni-view",{staticClass:"delete-pirce"},[t._v(t._s(t.$lang("common.currencySymbol"))+t._s(o.price))])],1),e("v-uni-view",{staticClass:"item-bottom-right"},[e("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetail(o)}}},[t._v("去砍价")])],1)],1)],1)})),1):t._e()]:t.isLoad&&t.isLoading?e("ns-empty",{attrs:{textColor:"#fff",isIndex:!1,text:"暂无砍价商品"}}):t._e()],2)],2):t._e(),e("hover-nav"),e("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},6102:function(t,o,e){"use strict";e("6a54"),Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0,e("5c47");var i={name:"ns-advert",props:{keyword:{type:String},className:{type:String}},data:function(){return{advList:[],isImage:!1,swiperHeight:150,currentIndex:0}},created:function(){this.getAdvList()},methods:{getAdvList:function(){var t=this;this.$api.sendRequest({url:"/api/adv/detail",data:{keyword:this.keyword},success:function(o){if(0==o.code){var e=o.data.adv_list;for(var i in e)e[i].adv_url&&(e[i].adv_url=JSON.parse(e[i].adv_url));t.advList=o.data.adv_list,t.$nextTick((function(){t.setSwiperHeight()}))}}})},jumppage:function(t){this.$util.diyRedirectTo(t)},imageLoad:function(t){this.isImage=!0},changeSwiper:function(t){var o=this;this.currentIndex=t.detail.current,this.$nextTick((function(){o.setSwiperHeight()}))},setSwiperHeight:function(){var t=this;this.advList.length>1&&setTimeout((function(){var o="#content-wrap"+t.currentIndex,e=uni.createSelectorQuery().in(t);e.select(o).boundingClientRect(),e.exec((function(o){o&&o[0]&&(t.swiperHeight=o[0].height)}))}),10)}}};o.default=i},7854:function(t,o,e){"use strict";e.r(o);var i=e("8ba8"),n=e("f48d");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(a);var s=e("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);o["default"]=r.exports},"7e88":function(t,o,e){"use strict";e.r(o);var i=e("2407"),n=e("f016");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(a);e("a44f");var s=e("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"9caa2b5c",null,!1,i["a"],void 0);o["default"]=r.exports},"8ba8":function(t,o,e){"use strict";e.d(o,"b",(function(){return i})),e.d(o,"c",(function(){return n})),e.d(o,"a",(function(){}));var i=function(){var t=this.$createElement,o=this._self._c||t;return o("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"8f20":function(t,o,e){var i=e("c86c");o=i(!1),o.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-4fa24919] .fixed{position:relative;top:0}[data-v-4fa24919] .empty{margin-top:0!important}.clockrun .delete-price[data-v-4fa24919]{max-width:60%}[data-v-4fa24919] .uni-countdown__number{min-width:%?32?%;height:%?32?%;text-align:center;line-height:%?32?%;border-radius:4px;display:inline-block;padding:%?4?%;margin:0;border:none!important}[data-v-4fa24919] .uni-countdown__splitor{width:%?10?%;height:%?32?%;line-height:%?36?%;text-align:center;display:inline-block}[data-v-4fa24919] .uni-countdown__splitor.day{width:auto}',""]),t.exports=o},"95da":function(t,o,e){var i=e("8f20");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=e("967d").default;n("092af950",i,!0,{sourceMap:!1,shadowMode:!1})},"9c4f":function(t,o,e){var i=e("bb37");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=e("967d").default;n("1a51f0e1",i,!0,{sourceMap:!1,shadowMode:!1})},a2fd:function(t,o,e){"use strict";var i=e("95da"),n=e.n(i);n.a},a44f:function(t,o,e){"use strict";var i=e("d87f"),n=e.n(i);n.a},a725:function(t,o,e){"use strict";var i=e("ac2a"),n=e.n(i);n.a},ac2a:function(t,o,e){var i=e("f714");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=e("967d").default;n("1a69ffc2",i,!0,{sourceMap:!1,shadowMode:!1})},bb37:function(t,o,e){var i=e("c86c");o=i(!1),o.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-countdown[data-v-45a7f114]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-45a7f114]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?24?%}.uni-countdown__splitor.day[data-v-45a7f114]{line-height:%?50?%}.uni-countdown__number[data-v-45a7f114]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;border:%?2?% solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=o},c1f1:function(t,o,e){"use strict";e.r(o);var i=e("fa1d"),n=e("015d");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(a);e("a725");var s=e("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"c1934e78",null,!1,i["a"],void 0);o["default"]=r.exports},c5b9:function(t,o,e){"use strict";e.r(o);var i=e("5c1e"),n=e("4b21");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(a);e("e224"),e("a2fd"),e("f5ec");var s=e("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"4fa24919",null,!1,i["a"],void 0);o["default"]=r.exports},cc1b:function(t,o,e){"use strict";e("6a54"),Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0,e("5ef2"),e("64aa"),e("5c47"),e("a1c1"),e("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,o=getCurrentPages()[0];this.$pageVm=o.$vm||o,uni.onWindowResize((function(o){t.$emit("resize",o)})),this.$pageVm.$on("hook:onPageScroll",(function(o){t.$emit("scroll",o)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,o){t.setStyle({pullToRefresh:{support:o,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,o=String(this.scrollTop);if(-1!==o.indexOf("rpx")&&(o=uni.upx2px(o.replace("rpx",""))),o=parseFloat(o),!isNaN(o)){var e=function e(n){n.scrollTop===o&&(t.$pageVm.$off("hook:onPageScroll",e),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:o,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",e)}})}}}};o.default=n},d87f:function(t,o,e){var i=e("d915");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=e("967d").default;n("03d75754",i,!0,{sourceMap:!1,shadowMode:!1})},d915:function(t,o,e){var i=e("c86c");o=i(!1),o.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-9caa2b5c]{width:100%}.container-box .item-wrap[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-9caa2b5c]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}',""]),t.exports=o},e12a:function(t,o,e){"use strict";e.r(o);var i=e("00ba"),n=e("ea5a");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(a);e("11ac");var s=e("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"45a7f114",null,!1,i["a"],void 0);o["default"]=r.exports},e224:function(t,o,e){"use strict";var i=e("fdb5"),n=e.n(i);n.a},ea5a:function(t,o,e){"use strict";e.r(o);var i=e("f9fd"),n=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return i[t]}))}(a);o["default"]=n.a},f016:function(t,o,e){"use strict";e.r(o);var i=e("6102"),n=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return i[t]}))}(a);o["default"]=n.a},f48d:function(t,o,e){"use strict";e.r(o);var i=e("cc1b"),n=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return i[t]}))}(a);o["default"]=n.a},f580:function(t,o,e){var i=e("c86c");o=i(!1),o.push([t.i,".progress-wrap[data-v-4fa24919] .uni-progress .uni-progress-bar{border-radius:%?16?%;overflow:hidden}.progress-wrap[data-v-4fa24919] .uni-progress-bar .uni-progress-inner-bar{background:linear-gradient(270deg,var(--bargain-promotion-color),var(--bargain-promotion-aux-color))!important}",""]),t.exports=o},f5ec:function(t,o,e){"use strict";var i=e("3cd0"),n=e.n(i);n.a},f714:function(t,o,e){var i=e("c86c");o=i(!1),o.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=o},f9fd:function(t,o,e){"use strict";e("6a54"),Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0,e("64aa");var i={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:[Number,String],default:0},hour:{type:[Number,String],default:0},minute:{type:[Number,String],default:0},second:{type:[Number,String],default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},mounted:function(t){var o=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){o.seconds--,o.seconds<0?o.timeUp():o.countDown()}),1e3)},watch:{day:function(t){var o=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){o.seconds--,o.seconds<0?o.timeUp():o.countDown()}),1e3)},hour:function(t){var o=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){o.seconds--,o.seconds<0?o.timeUp():o.countDown()}),1e3)},minute:function(t){var o=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){o.seconds--,o.seconds<0?o.timeUp():o.countDown()}),1e3)},second:function(t){var o=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){o.seconds--,o.seconds<0?o.timeUp():o.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,o,e,i){return t=Number(t),o=Number(o),e=Number(e),i=Number(i),60*t*60*24+60*o*60+60*e+i},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,o=0,e=0,i=0,n=0;t>0?(o=Math.floor(t/86400),e=Math.floor(t/3600)-24*o,i=Math.floor(t/60)-24*o*60-60*e,n=Math.floor(t)-24*o*60*60-60*e*60-60*i):this.timeUp(),o<10&&(o="0"+o),e<10&&(e="0"+e),i<10&&(i="0"+i),n<10&&(n="0"+n),this.d=o,this.h=e,this.i=i,this.s=n}}};o.default=i},fa1d:function(t,o,e){"use strict";e.d(o,"b",(function(){return i})),e.d(o,"c",(function(){return n})),e.d(o,"a",(function(){}));var i=function(){var t=this,o=t.$createElement,e=t._self._c||o;return 1==t.pageCount||t.need?e("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?e("v-uni-view",{staticClass:"btn-item",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.$util.redirectTo("/pages/index/index")}}},[e("v-uni-text",{staticClass:"iconfont icon-shouye1"}),e("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?e("v-uni-view",{staticClass:"btn-item",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.$util.redirectTo("/pages/member/index")}}},[e("v-uni-text",{staticClass:"iconfont icon-yonghu"}),e("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?e("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[e("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):e("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[e("v-uni-view",[t._v("快捷")]),e("v-uni-view",[t._v("导航")])],1)],1):t._e()},n=[]},fdb5:function(t,o,e){var i=e("4054");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=e("967d").default;n("bb419052",i,!0,{sourceMap:!1,shadowMode:!1})}}]);