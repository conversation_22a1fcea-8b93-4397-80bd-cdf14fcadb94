(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-level_growth_rules"],{"5a54":function(e,t,i){var n=i("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */uni-page-body[data-v-630b028c]{background-color:#fff}body.?%PAGE?%[data-v-630b028c]{background-color:#fff}.grow-explain[data-v-630b028c]{padding:%?30?%;margin-top:%?30?%}.grow-explain .explain-title[data-v-630b028c]{display:flex;align-items:center;justify-content:center;line-height:1;margin-bottom:%?40?%}.grow-explain .explain-title uni-image[data-v-630b028c]{margin:0 %?20?%;width:%?54?%;height:%?18?%}.grow-explain .explain-tr[data-v-630b028c]{display:flex}.grow-explain .explain-th[data-v-630b028c]{padding:%?10?% %?30?%;flex:1;background-color:#f6f1e4}.grow-explain .explain-th ~ .explain-th[data-v-630b028c]{border-left:%?4?% solid #fff}.grow-explain .explain-td[data-v-630b028c]{padding:%?10?% %?30?%;height:%?60?%;line-height:%?60?%;flex:1;background-color:#fcfbf7}.grow-explain .explain-td ~ .explain-td[data-v-630b028c]{border-left:%?4?% solid #fff}.grow-value[data-v-630b028c],\r\n.acquisition-grow[data-v-630b028c]{padding:0 %?30?% %?30?%}.grow-value .title[data-v-630b028c],\r\n.acquisition-grow .title[data-v-630b028c]{display:flex;align-items:center}.grow-value .title uni-image[data-v-630b028c],\r\n.acquisition-grow .title uni-image[data-v-630b028c]{width:%?30?%;height:%?30?%;margin-right:%?10?%}.grow-value .content[data-v-630b028c],\r\n.acquisition-grow .content[data-v-630b028c]{font-size:%?24?%;margin-left:%?40?%}.grow-value .content uni-text[data-v-630b028c],\r\n.acquisition-grow .content uni-text[data-v-630b028c]{display:block}',""]),e.exports=t},"6c0f":function(e,t,i){"use strict";i.r(t);var n=i("865d"),a=i("8b63");for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);i("a7eb");var o=i("828b"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"630b028c",null,!1,n["a"],void 0);t["default"]=l.exports},"865d":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={pageMeta:i("7854").default,loadingCover:i("c003").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":e.themeColor}}),i("v-uni-view",{staticClass:"member-level"},[i("v-uni-view",{staticClass:"grow-explain"},[i("v-uni-view",{staticClass:"explain-title"},[i("v-uni-image",{attrs:{src:e.$util.img("public/uniapp/level/growth_that_left.png"),mode:"aspectFit"}}),e._v("成长值说明"),i("v-uni-image",{attrs:{src:e.$util.img("public/uniapp/level/growth_that_right.png"),mode:"aspectFit"}})],1),i("v-uni-view",{staticClass:"explain-table"},[i("v-uni-view",{staticClass:"explain-tr"},[i("v-uni-text",{staticClass:"explain-th"},[e._v("等级")]),i("v-uni-text",{staticClass:"explain-th"},[e._v("成长值")])],1),e._l(e.levelList,(function(t,n){return i("v-uni-view",{key:n,staticClass:"explain-tr"},[i("v-uni-text",{staticClass:"explain-td"},[e._v(e._s(t.level_name))]),i("v-uni-text",{staticClass:"explain-td"},[e._v(e._s(t.growth))])],1)}))],2)],1),i("v-uni-view",{staticClass:"grow-value"},[i("v-uni-view",{staticClass:"title"},[i("v-uni-image",{attrs:{src:e.$util.img("public/uniapp/level/explain.png"),mode:"aspectFit"}}),i("v-uni-text",[e._v("什么是成长值")])],1),i("v-uni-view",{staticClass:"content color-tip"},[e._v("成长值是消费者在店铺成为会员后，通过消费计算出来的值。成长值决定会员等级，会员等级越高，所享受的会员权益和会员礼包就越多。")])],1),i("v-uni-view",{staticClass:"acquisition-grow"},[i("v-uni-view",{staticClass:"title"},[i("v-uni-image",{attrs:{src:e.$util.img("public/uniapp/level/explain.png"),mode:"aspectFit"}}),i("v-uni-text",[e._v("如何获得成长值")])],1),i("v-uni-view",{staticClass:"content color-tip"},[i("v-uni-text",[e._v("1、注册会员送x成长值。")]),i("v-uni-text",[e._v("2、会员充值到余额送x成长值。")]),i("v-uni-text",[e._v("3、会员签到送x成长值。")]),i("v-uni-text",[e._v("4、会员消费x元，交易完成即可获得x个成长值。")])],1)],1),e.showTop?i("to-top",{on:{toTop:function(t){arguments[0]=t=e.$handleEvent(t),e.scrollToTopNative()}}}):e._e(),i("loading-cover",{ref:"loadingCover"})],1)],1)},r=[]},"8b63":function(e,t,i){"use strict";i.r(t);var n=i("e172"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=a.a},a7eb:function(e,t,i){"use strict";var n=i("e446"),a=i.n(n);a.a},e172:function(e,t,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa"),i("bf0f"),i("2797");var a=n(i("3e58")),r=n(i("8f75")),o=n(i("e78f")),l={components:{nsProgress:a.default,toTop:r.default},mixins:[o.default],data:function(){return{curIndex:0,descIndex:0,isDescAnimating:!1,scaleX:(634/540).toFixed(4),scaleY:(378/330).toFixed(4),swiperConfig:{indicatorDots:!1,indicatorColor:"rgba(255, 255, 255, .4)",indicatorActiveColor:"rgba(255, 255, 255, 1)",interval:3e3,duration:300,circular:!1,previousMargin:"58rpx",nextMargin:"58rpx"},levelList:[{needGrowth:0,growth:0}],levelId:0,growth:0,nowIndex:0,rule:[]}},computed:{listLen:function(){return this.levelList.length}},onLoad:function(){this.getLevelList(),this.getLevelRule()},onShow:function(){},filters:{rate:function(e,t,i){var n=Number(i),a=Number(t[e].growth);if(e==t.length-1)return n>a?100:0;var r=Number(t[e+1].growth),o=n-a,l=r-a,s=Math.floor(o/l*100);return s>100?100:s}},methods:{swiperChange:function(e){var t=this;this.curIndex=e.detail.current,this.isDescAnimating=!0;var i=setTimeout((function(){t.descIndex=e.detail.current,clearTimeout(i)}),150)},animationfinish:function(e){this.isDescAnimating=!1},getBannerDetail:function(e){uni.showLoading({title:"将前往详情页面",duration:2e3,mask:!0})},getLevelList:function(){var e=this;this.$api.sendRequest({url:"/api/memberlevel/lists",success:function(t){if(t.data&&0==t.code){e.levelList=t.data,e.levelId=e.memberInfo.member_level,e.growth=e.memberInfo.growth;for(var i=0;i<e.levelList.length;i++)if(e.levelList[i].level_id==e.levelId){e.curIndex=i,e.descIndex=i,e.nowIndex=i;break}e.levelList.forEach((function(t,i){var n=0;i!=e.levelList.length-1?(t.needGrowth=Number(e.levelList[i+1].growth)-Number(e.growth),n=t.needGrowth<=0?100:100*(e.growth/e.levelList[i+1].growth).toFixed(2)):(t.needGrowth=Number(e.levelList[i].growth)-Number(e.growth),n=t.needGrowth<=0?100:100*(e.growth/e.levelList[i].growth).toFixed(2)),t.rate=n})),e.levelList.forEach((function(e){e.consume_discount&&(e.consume_discount=(e.consume_discount/10).toFixed(2))})),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}else e.$util.showToast({title:t.message}),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},getLevelRule:function(){var e=this;this.$api.sendRequest({url:"/api/member/accountrule",success:function(t){0==t.code&&t.data&&t.data.growth&&(e.rule=t.data.growth)}})}}};t.default=l},e446:function(e,t,i){var n=i("5a54");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("967d").default;a("67ba53b8",n,!0,{sourceMap:!1,shadowMode:!1})}}]);