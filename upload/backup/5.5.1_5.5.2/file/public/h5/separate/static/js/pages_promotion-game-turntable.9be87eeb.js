(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-game-turntable"],{"015d":function(t,e,a){"use strict";a.r(e);var i=a("0f46"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"0f46":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=i},"3fb8":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container[data-v-d632656a]{width:100vw;height:100vh;background-image:linear-gradient(#f3623f,#f74222)}.head-wrap[data-v-d632656a]{position:relative}.head-wrap uni-image[data-v-d632656a]{width:90%;margin:0 auto;display:block}.head-wrap .rule-mark[data-v-d632656a]{position:absolute;right:0;top:%?60?%;background-color:#ffe854;color:#ff7908;height:%?60?%;line-height:%?60?%;padding:0 %?30?%;border-top-left-radius:%?60?%;border-bottom-left-radius:%?60?%}.turntable-wrap[data-v-d632656a]{margin:%?30?%;padding:%?70?% %?50?% %?70?% %?70?%;background-size:100% 100%;border-radius:%?16?%;-webkit-transform:translateY(%?-200?%);transform:translateY(%?-200?%)}.turntable-wrap .wrap[data-v-d632656a]{width:100%;position:relative;overflow:hidden;height:%?536?%}.turntable-wrap .star-box[data-v-d632656a]{position:absolute;width:calc(33.33333333% - %?20?%);height:%?100?%;background:linear-gradient(#ffe1ac,#fec965);top:%?182?%;left:33.33333333%;height:%?162?%;border-radius:%?8?%;box-shadow:0 .25em 0 #e19d1f;text-align:center}.turntable-wrap .star-box .text[data-v-d632656a]{font-size:%?34?%;margin:%?30?% 0 0 0;line-height:%?60?%;font-weight:600;color:#ff3301;text-align:center}.turntable-wrap .star-box .text.no-tips[data-v-d632656a]{margin-top:%?50?%}.turntable-wrap .star-box .tips[data-v-d632656a]{font-size:%?24?%;display:inline-block;margin:0 auto;background:#f0a71d;line-height:1;color:#ff3301;padding:%?6?% %?12?%;border-radius:%?24?%}.turntable-wrap .star-box.disabled[data-v-d632656a]{box-shadow:0 .25em 0 #999;background:linear-gradient(#ccc,#aaa)}.turntable-wrap .star-box.disabled .text[data-v-d632656a]{color:#888}.turntable-wrap .star-box.disabled .tips[data-v-d632656a]{color:#777;background:#aaa}.turntable-wrap .status-box[data-v-d632656a]{position:absolute;width:calc(33.33333333% - %?20?%);background:#ed9580;top:%?182?%;left:33.33333333%;height:%?168?%;border-radius:%?8?%;text-align:center;padding:0 %?20?%;box-sizing:border-box}.turntable-wrap .status-box uni-view[data-v-d632656a]{color:#fff;font-size:%?32?%;letter-spacing:%?4?%;margin-top:%?30?%}.turntable-wrap .award-wrap[data-v-d632656a]{width:33.33333333%;padding-right:%?20?%;position:absolute;box-sizing:border-box}.turntable-wrap .award-wrap .box[data-v-d632656a]{width:100%;background:#fff;border-radius:%?8?%;padding:%?20?% 0;box-shadow:inset 0 0 %?48?% 0 #ffd6ab,0 .25em 0 rgba(248,166,131,.96)}.turntable-wrap .award-wrap .box.on[data-v-d632656a]{background:linear-gradient(#ffe1ac,#fec965);box-shadow:0 .25em 0 #e19d1f}.turntable-wrap .award-wrap .box.on .award-text[data-v-d632656a]{color:#ff3301}.turntable-wrap .award-wrap .box .award-img[data-v-d632656a]{width:%?70?%;height:%?70?%;margin:0 auto;display:flex;align-items:center}.turntable-wrap .award-wrap .box .award-img uni-image[data-v-d632656a]{width:%?70?%;height:auto}.turntable-wrap .award-wrap .box .award-text[data-v-d632656a]{text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-top:%?10?%;font-size:%?24?%;padding:0 %?20?%;color:#666}.turntable-wrap .award-wrap[data-v-d632656a]:nth-child(1){top:0}.turntable-wrap .award-wrap[data-v-d632656a]:nth-child(2){top:0;left:33.33333333%}.turntable-wrap .award-wrap[data-v-d632656a]:nth-child(3){top:0;left:66.66666666%}.turntable-wrap .award-wrap[data-v-d632656a]:nth-child(4){top:%?182?%;left:66.66666666%}.turntable-wrap .award-wrap[data-v-d632656a]:nth-child(5){top:%?364?%;left:66.66666666%}.turntable-wrap .award-wrap[data-v-d632656a]:nth-child(6){top:%?364?%;left:33.33333333%}.turntable-wrap .award-wrap[data-v-d632656a]:nth-child(7){top:%?364?%;left:0}.turntable-wrap .award-wrap[data-v-d632656a]:nth-child(8){top:%?182?%;left:0}.action-text[data-v-d632656a]{margin:0 %?50?%;display:flex;-webkit-transform:translateY(%?-200?%);transform:translateY(%?-200?%)}.action-text > uni-view[data-v-d632656a]{flex:1;line-height:1}.action-text .point[data-v-d632656a]{color:#fee331}.action-text .record[data-v-d632656a]{color:#fff;text-align:right}.record-wrap[data-v-d632656a]{-webkit-transform:translateY(%?-200?%);transform:translateY(%?-200?%);margin:%?80?% %?50?% %?30?% %?50?%;border-radius:%?10?%;background-color:#ff6e43;padding:%?12?%;box-shadow:0 .45em 0 #d92a00;position:relative;height:%?430?%}.record-wrap .body-shade[data-v-d632656a]{width:calc(100% - %?24?%);height:%?60?%;top:%?12?%;left:%?12?%;background-color:#da2b00;position:absolute;z-index:2;border-radius:%?8?%}.record-wrap .head[data-v-d632656a]{border:%?6?% solid #ff6e43;box-shadow:inset 0 0 %?10?% 0 #d92a00,0 .45em 0 #d92a00;background-color:#da2b00;position:absolute;z-index:5;text-align:center;width:%?300?%;height:%?70?%;line-height:%?70?%;border-radius:%?70?%;top:0;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);color:#fff;font-weight:600;letter-spacing:%?4?%}.record-wrap .body[data-v-d632656a]{background-color:#da2b00;border-radius:%?8?%;height:%?340?%;box-shadow:inset 0 0 %?10?% 0 #d92a00;padding:%?60?% %?30?% %?30?% %?30?%;overflow:hidden}.record-wrap .body .wrap > uni-view[data-v-d632656a]{display:flex}.record-wrap .body .wrap.animate[data-v-d632656a]{transition:all 1s ease-in-out;-webkit-transform:translateY(%?-60?%);transform:translateY(%?-60?%)}.record-wrap .body .tit[data-v-d632656a]{line-height:%?60?%;width:%?220?%;color:#fff}.record-wrap .body .txt[data-v-d632656a]{line-height:%?60?%;flex:1;color:#fee331}.result-wrap[data-v-d632656a]{position:relative}.result-wrap .bg-img[data-v-d632656a]{width:80vw}.result-wrap .content-wrap[data-v-d632656a]{position:absolute;z-index:5;width:100%;height:100%;top:0;left:0;text-align:center}.result-wrap .content-wrap .look[data-v-d632656a]{margin:%?70?% auto 0 auto;display:block;width:%?120?%}.result-wrap .content-wrap .msg[data-v-d632656a]{margin:%?10?% auto 0 auto;padding:0 %?20?%;color:#da2b00;width:70%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.result-wrap .content-wrap .btn[data-v-d632656a]{width:50%;height:%?70?%;line-height:%?70?%;border-radius:%?10?%;text-align:center;color:#da2b00;background:linear-gradient(#ffe1ac,#fec965);margin:%?146?% auto 0 auto}.result-wrap .content-wrap .icon-round-close[data-v-d632656a]{position:absolute;color:#fff;font-size:%?70?%;top:%?-120?%;right:%?0?%}.result-wrap .yes .look[data-v-d632656a]{width:%?80?%;height:%?80?%;margin-top:%?60?%}.result-wrap .yes .btn[data-v-d632656a]{margin-top:%?214?%}.rule-wrap[data-v-d632656a]{border-radius:%?10?%;background-color:#ffd697;width:80vw;padding:%?12?%;box-sizing:border-box}.rule-wrap .content-wrap[data-v-d632656a]{background-color:#fff2dd;width:100%;border-radius:%?8?%;position:relative}.rule-wrap .content-wrap .rule-head[data-v-d632656a]{width:100%;position:absolute;-webkit-transform:translateY(-50%);transform:translateY(-50%);left:0;top:0}.rule-wrap .content-wrap .rule[data-v-d632656a]{max-height:%?880?%;overflow:hidden;padding:%?80?% %?30?% 0 %?30?%;box-sizing:border-box}.rule-wrap .content-wrap .rule .tit[data-v-d632656a]{font-weight:600;color:#da2b00;margin-top:%?10?%}.rule-wrap .content-wrap .rule .text[data-v-d632656a]{font-size:%?26?%;color:#da2b00}.rule-wrap .content-wrap .icon-round-close[data-v-d632656a]{color:#fff;text-align:center;position:absolute;bottom:%?-150?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);font-size:%?70?%}',""]),t.exports=e},4192:function(t,e,a){"use strict";var i=a("6059"),n=a.n(i);n.a},6059:function(t,e,a){var i=a("3fb8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("2ededbc0",i,!0,{sourceMap:!1,shadowMode:!1})},7477:function(t,e,a){"use strict";a.r(e);var i=a("7dc7"),n=a("aaa6");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"3f50a906",null,!1,i["a"],void 0);e["default"]=s.exports},7854:function(t,e,a){"use strict";a.r(e);var i=a("8ba8"),n=a("f48d");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},"7dc7":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-text",[this._v(this._s(this.temp))])},n=[]},"7e38":function(t,e,a){var i=a("faad");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("534fb80f",i,!0,{sourceMap:!1,shadowMode:!1})},8157:function(t,e,a){"use strict";a.r(e);var i=a("a95d"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"8ba8":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},9327:function(t,e,a){"use strict";var i=a("8bdb"),n=a("9f69"),r=a("1ded").f,o=a("c435"),s=a("9e70"),u=a("b6a1"),d=a("862c"),l=a("0931"),c=a("a734"),f=n("".slice),p=Math.min,v=l("endsWith"),g=!c&&!v&&!!function(){var t=r(String.prototype,"endsWith");return t&&!t.writable}();i({target:"String",proto:!0,forced:!g&&!v},{endsWith:function(t){var e=s(d(this));u(t);var a=arguments.length>1?arguments[1]:void 0,i=e.length,n=void 0===a?i:p(o(a),i),r=s(t);return f(e,n-r.length,n)===r}})},9851:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5c47"),a("0506"),a("a1c1"),a("23f4"),a("7d2f"),a("9c4e"),a("ab80"),a("bf0f"),a("c223"),Function.prototype.asyAfter=function(t){var e=this;return function(){var a=e.apply(this,arguments);return"next"===a?t.apply(this,arguments):a}},Date.prototype.pattern=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours()%12==0?12:this.getHours()%12,"H+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};for(var a in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length))),/(E+)/.test(t)&&(t=t.replace(RegExp.$1,(RegExp.$1.length>1?RegExp.$1.length>2?"星期":"周":"")+{0:"日",1:"一",2:"二",3:"三",4:"四",5:"五",6:"六"}[this.getDay()+""])),e)new RegExp("("+a+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?e[a]:("00"+e[a]).substr((""+e[a]).length)));return t};var i=function(){},n=i.prototype;n.getUnix=function(){return(new Date).getTime()},n.getTodayUnix=function(){var t=new Date,e="".concat(t.getFullYear(),"/").concat(t.getMonth()+1,"/").concat(t.getDate()," 00:00:00");return new Date(e).getTime()},n.getYearUnix=function(){var t=new Date;return t.setMonth(0),t.setDate(1),t.setHours(0),t.setMinutes(0),t.setSeconds(0),t.setMilliseconds(0),t.getTime()},n.getLastDate=function(t){if(t){var e=new Date(t);if(e.pattern)return e.pattern("yyyy-MM-dd");var a=e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1,i=e.getDate()<10?"0"+e.getDate():e.getDate();return e.getFullYear()+"-"+a+"-"+i}};var r=new RegExp("-","g");n.getFormatTime=function(t,e){if(!t)return"";switch(function(t){return/^\[object\s(.*)\]$/.exec(Object.prototype.toString.call(t))[1]}(t)){case"Date":t=t.getTime();break;case"String":t=t.replace(r,"/");break;default:t=new Date(t).getTime();break}var a=this.getUnix(),i=(this.getYearUnix(),(a-t)/1e3);if(t>a&&e)return this.getLastDate(t);return function(t,e){var a=function(t){return t<=0||Math.floor(t/60)<=0?"刚刚":"next"}.asyAfter((function(t){return t<3600?Math.floor(t/60)+"分钟前":"next"})).asyAfter((function(t,e){var a=o.getTodayUnix();return t>=3600&&e-a>=0?Math.floor(t/60/60)+"小时前":"next"})).asyAfter((function(t,e){var a=o.getTodayUnix();return t=(a-e)/1e3,t/86400<=31?Math.ceil(t/86400)+"天前":"next"})).asyAfter((function(t,e){return o.getLastDate(e)}));return a(t,e)}(i,t)};var o=new i,s=o;e.default=s},a173:function(t,e,a){"use strict";a.r(e);var i=a("a936"),n=a("8157");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("4192"),a("ca08");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"d632656a",null,!1,i["a"],void 0);e["default"]=s.exports},a725:function(t,e,a){"use strict";var i=a("ac2a"),n=a.n(i);n.a},a936:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={pageMeta:a("7854").default,uniPopup:a("d745").default,loadingCover:a("c003").default,nsLogin:a("2910").default,hoverNav:a("c1f1").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-scroll-view",{staticClass:"container",attrs:{"scroll-y":"true"}},[a("v-uni-view",[a("v-uni-view",{staticClass:"head-wrap"},[a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/game/turntable_head.png"),mode:"widthFix"}}),a("v-uni-view",{staticClass:"rule-mark",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openRulePopup.apply(void 0,arguments)}}},[t._v("规则")])],1),a("v-uni-view",{staticClass:"turntable-wrap",style:{backgroundImage:"url("+t.$util.img("public/uniapp/game/turntable_bg.png")+")"}},[a("v-uni-view",{staticClass:"wrap"},[t._l(t.gameInfo.award,(function(e,i){return a("v-uni-view",{key:i,staticClass:"award-wrap"},[a("v-uni-view",{staticClass:"box",class:{on:i==t.currentIndex}},[a("v-uni-view",{staticClass:"award-img"},[a("v-uni-image",{attrs:{src:t.$util.img(e.award_img),mode:"widthFix"}})],1),a("v-uni-view",{staticClass:"award-text"},[t._v(t._s(e.award_name))])],1)],1)})),1==t.gameInfo.status?[t.gameInfo.surplus_num>0||!t.storeToken?[a("v-uni-view",{staticClass:"star-box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.lottery.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"text"},[t._v("立即抽奖")]),a("v-uni-view",{staticClass:"tips"},[t._v("剩余"+t._s(t.gameInfo.surplus_num)+"次")])],1)]:[a("v-uni-view",{staticClass:"star-box disabled"},[a("v-uni-view",{staticClass:"text"},[t._v("立即抽奖")]),a("v-uni-view",{staticClass:"tips"},[t._v("剩余"+t._s(t.gameInfo.surplus_num)+"次")])],1)]]:0==t.gameInfo.status?[a("v-uni-view",{staticClass:"status-box"},[a("v-uni-view",[t._v("活动尚未开始")])],1)]:[a("v-uni-view",{staticClass:"status-box"},[a("v-uni-view",[t._v("活动已经结束")])],1)]],2)],1),a("v-uni-view",{staticClass:"action-text"},[a("v-uni-view",{staticClass:"point"},[t._v("我的积分："+t._s(t.point))]),a("v-uni-view",{staticClass:"record",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_promotion/game/record",{id:t.id})}}},[t._v("我的中奖记录")])],1),t.gameInfo.is_show_winner&&t.gameInfo.draw_record.length?a("v-uni-view",{staticClass:"record-wrap"},[a("v-uni-view",{staticClass:"body-shade"}),a("v-uni-view",{staticClass:"head"},[t._v("中奖名单")]),a("v-uni-view",{staticClass:"body"},[a("v-uni-view",{staticClass:"wrap",class:{animate:t.animate}},t._l(t.gameInfo.draw_record,(function(e,i){return a("v-uni-view",{key:i},[a("v-uni-view",{staticClass:"tit"},[t._v(t._s(t._f("cover")(e.member_nick_name)))]),a("v-uni-view",{staticClass:"txt"},[a("l-time",{attrs:{text:1e3*e.create_time}}),t._v("获得 "+t._s(e.award_name))],1)],1)})),1)],1)],1):t._e()],1),a("uni-popup",{ref:"resultPopup",attrs:{type:"center",maskClick:!1}},[a("v-uni-view",{staticClass:"result-wrap"},[t.result.is_winning?[a("v-uni-view",{staticClass:"content-wrap yes"},[a("v-uni-image",{staticClass:"look",attrs:{src:t.$util.img(t.gameInfo.award[t.resultIndex].award_img),mode:"widthFix"}}),a("v-uni-view",{staticClass:"msg"},[t._v("恭喜您抽中"+t._s(t.gameInfo.award[t.resultIndex].award_name))]),a("v-uni-view",{staticClass:"btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}},[t._v("继续抽奖")]),a("v-uni-text",{staticClass:"iconfont icon-round-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}})],1),a("v-uni-image",{staticClass:"bg-img",attrs:{src:t.$util.img("public/uniapp/game/result_yes.png"),mode:"widthFix"}})]:[a("v-uni-view",{staticClass:"content-wrap"},[a("v-uni-image",{staticClass:"look",attrs:{src:t.$util.img("public/uniapp/game/result_look.png"),mode:"widthFix"}}),a("v-uni-view",{staticClass:"msg"},[t._v(t._s(t.gameInfo.no_winning_desc))]),a("v-uni-view",{staticClass:"btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}},[t._v("继续抽奖")]),a("v-uni-text",{staticClass:"iconfont icon-round-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}})],1),a("v-uni-image",{staticClass:"bg-img",attrs:{src:t.$util.img("public/uniapp/game/result_no.png"),mode:"widthFix"}})]],2)],1),a("uni-popup",{ref:"rulePopup",attrs:{type:"center",maskClick:!1}},[a("v-uni-view",{staticClass:"rule-wrap"},[a("v-uni-view",{staticClass:"content-wrap"},[a("v-uni-image",{staticClass:"rule-head",attrs:{src:t.$util.img("public/uniapp/game/rule_head.png"),mode:"widthFix"}}),a("v-uni-scroll-view",{staticClass:"rule",attrs:{"scroll-y":"true"}},[a("v-uni-view",[a("v-uni-view",{staticClass:"tit"},[t._v("活动时间")]),a("v-uni-view",{staticClass:"text"},[t._v(t._s(t.$util.timeStampTurnTime(t.gameInfo.start_time))+" - "+t._s(t.$util.timeStampTurnTime(t.gameInfo.end_time)))]),a("v-uni-view",{staticClass:"tit"},[t._v("参与规则")]),0==t.gameInfo.join_type?a("v-uni-view",{staticClass:"text"},[t._v("每个用户活动期间共有"+t._s(t.gameInfo.join_frequency)+"次抽奖机会。")]):a("v-uni-view",{staticClass:"text"},[t._v("每个用户活动期间每天都有"+t._s(t.gameInfo.join_frequency)+"次抽奖机会，每天0点更新。")]),a("v-uni-view",{staticClass:"text"},[t._v("每次抽奖需消耗 "+t._s(t.gameInfo.points)+" 积分")]),0!=t.gameInfo.level_id?a("v-uni-view",{staticClass:"text"},[t._v("该活动只有"+t._s(t.gameInfo.level_name)+"等级的会员可参与。")]):t._e(),""!=t.gameInfo.remark?[a("v-uni-view",{staticClass:"tit"},[t._v("活动说明")]),a("v-uni-view",{staticClass:"text"},[t._v(t._s(t.gameInfo.remark))])]:t._e()],2)],1),a("v-uni-text",{staticClass:"iconfont icon-round-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeRulePopup.apply(void 0,arguments)}}})],1)],1)],1),a("loading-cover",{ref:"loadingCover"}),a("ns-login",{ref:"login"}),a("hover-nav")],1)],1)},r=[]},a95d:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797"),a("5ef2"),a("e966"),a("aa9c");var n=i(a("d745")),r=i(a("7477")),o={components:{uniPopup:n.default,LTime:r.default},data:function(){return{isClick:!1,currentIndex:-1,maxRing:6,currentRing:1,speed:300,timer:null,id:0,gameInfo:{award:[{award_img:"",award_name:""}],surplus_num:""},award:[],resultIndex:0,result:{is_winning:1},point:0,animate:!1,scrollTimer:null,shareImg:""}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.turntable||(e.$util.showToast({title:"商家未开启幸运抽奖",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.id&&(this.id=t.id),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var a=decodeURIComponent(t.scene);a=a.split("&"),a.length&&a.forEach((function(t){-1!=t.indexOf("id")&&(e.id=t.split("-")[1]),-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}},onShow:function(){this.storeToken&&(uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member")),this.getMemberPointInfo()),this.getGameInfo()},onShareAppMessage:function(t){var e=this.gameInfo.game_name,a=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),i=a.path;return{title:e,path:i,imageUrl:"",success:function(t){},fail:function(t){}}},watch:{storeToken:function(t,e){t&&(this.getMemberPointInfo(),this.getGameInfo())}},methods:{getMemberPointInfo:function(){var t=this;this.$api.sendRequest({url:"/api/memberaccount/info",data:{account_type:"point"},success:function(e){0==e.code&&e.data&&(t.point=parseInt(e.data.point))}})},lottery:function(){var t=this;if(this.storeToken){if(1==this.gameInfo.status)if(this.gameInfo.surplus_num<=0)this.$util.showToast({title:"您的抽奖次数已用完"});else{var e=","+this.gameInfo.level_id+",";"0"==this.gameInfo.level_id||-1!=e.indexOf(","+this.memberInfo.member_level+",")?this.gameInfo.points>0&&this.point<this.gameInfo.points?this.$util.showToast({title:"积分不足"}):this.isClick||(this.isClick=!0,this.currentIndex=0,this.currentRing=1,this.speed=300,this.timer=setInterval(this.startRoll,this.speed),this.$api.sendRequest({url:"/turntable/api/turntable/lottery",data:{id:this.id},success:function(e){e.code>=0?(t.result=e.data,e.data.is_winning?t.resultIndex=t.$util.inArray(e.data.award_id,t.award):t.resultIndex=t.$util.inArray(-1,t.award),t.point-=t.gameInfo.points,t.gameInfo.surplus_num-=1):t.$util.showToast({title:e.message})},fail:function(e){t.resultIndex=t.$util.inArray(-1,t.award)}})):this.$util.showToast({title:"该活动只有"+this.gameInfo.level_name+"等级的会员可参与该活动"})}}else this.$refs.login.open("/pages_promotion/game/turntable?id="+this.id)},getGameInfo:function(){var t=this;this.$api.sendRequest({url:"/turntable/api/turntable/info",data:{id:this.id},success:function(e){e.code>=0&&e.data?(t.gameInfo=e.data,e.data.award.forEach((function(e){t.award.push(e.award_id)})),t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.gameInfo.is_show_winner&&t.gameInfo.draw_record.length>6&&(t.scrollTimer=setInterval(t.scrollRecord,2e3))):(t.$util.showToast({title:"未获取到活动信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))},fail:function(e){t.$util.showToast({title:"未获取到活动信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500)}})},startRoll:function(){if(this.speed>50&&(this.speed-=50),this.currentRing>=this.maxRing-1&&this.speed<700&&(this.speed+=100),this.currentRing==this.maxRing&&this.currentIndex==this.resultIndex)return clearInterval(this.timer),void this.showResult();7==this.currentIndex?(this.currentRing+=1,this.currentIndex=0):this.currentIndex+=1,clearInterval(this.timer),this.timer=setTimeout(this.startRoll,this.speed)},showResult:function(){this.$refs.resultPopup.open()},closePopup:function(){this.isClick=!1,this.currentIndex=-1,this.$refs.resultPopup.close()},openRulePopup:function(){this.$refs.rulePopup.open()},closeRulePopup:function(){this.$refs.rulePopup.close()},scrollRecord:function(){var t=this;this.animate=!0,setTimeout((function(){t.gameInfo.draw_record.push(t.gameInfo.draw_record[0]),t.gameInfo.draw_record.shift(),t.animate=!1}),1e3)}},filters:{cover:function(t){return"string"==typeof t&&t.length>0?t.substr(0,1)+"******"+t.substr(-1):""}},onHide:function(){clearInterval(this.scrollTimer)}};e.default=o},aaa6:function(t,e,a){"use strict";a.r(e);var i=a("c2c7"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},ac2a:function(t,e,a){var i=a("f714");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("1a69ffc2",i,!0,{sourceMap:!1,shadowMode:!1})},c1f1:function(t,e,a){"use strict";a.r(e);var i=a("fa1d"),n=a("015d");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("a725");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"c1934e78",null,!1,i["a"],void 0);e["default"]=s.exports},c2c7:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("9327");var n=i(a("9851")),r={name:"l-time",props:{text:{type:[String,Number,Date],default:""},maxDate:{type:Boolean,default:!1}},data:function(){return{textVal:this.text}},watch:{text:function(){this.textVal=this.text}},computed:{temp:function(){return this.getText()}},methods:{getText:function(){var t=this,e=n.default.getFormatTime(t.textVal,t.maxDate);return e&&(e.endsWith("刚刚")||e.endsWith("分钟前"))&&setTimeout((function(){var e=t.textVal;t.textVal="",t.textVal=e}),6e4),this.textVal?e:""},onClick:function(){this.$emit("on-tap",this.textVal)}}};e.default=r},ca08:function(t,e,a){"use strict";var i=a("7e38"),n=a.n(i);n.a},cc1b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var a=function a(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",a),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",a)}})}}}};e.default=n},f48d:function(t,e,a){"use strict";a.r(e);var i=a("cc1b"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},f714:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return 1==t.pageCount||t.need?a("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?a("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[a("v-uni-text",{staticClass:"iconfont icon-shouye1"}),a("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?a("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[a("v-uni-text",{staticClass:"iconfont icon-yonghu"}),a("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?a("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[a("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):a("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[a("v-uni-view",[t._v("快捷")]),a("v-uni-view",[t._v("导航")])],1)],1):t._e()},n=[]},faad:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,"[data-v-d632656a] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:none!important}[data-v-d632656a] .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box{overflow:unset}",""]),t.exports=e}}]);