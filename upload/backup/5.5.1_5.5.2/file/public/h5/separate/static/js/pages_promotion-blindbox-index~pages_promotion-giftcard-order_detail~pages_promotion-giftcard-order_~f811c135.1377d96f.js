(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-blindbox-index~pages_promotion-giftcard-order_detail~pages_promotion-giftcard-order_~f811c135"],{"171a":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"nsSwitch",props:{checked:{type:Boolean,default:!1}},methods:{change:function(){this.$emit("change")}}};e.default=o},"1dd9":function(t,e,a){var o=a("2a42");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=a("967d").default;n("8c18b280",o,!0,{sourceMap:!1,shadowMode:!1})},"2a42":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.popup[data-v-4252f8a9]{width:75vw;background:#fff;border-top-left-radius:%?10?%;border-top-right-radius:%?10?%}.popup .popup-header[data-v-4252f8a9]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-4252f8a9]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-4252f8a9]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-4252f8a9]{height:calc(100% - %?250?%)}.popup .popup-body.safe-area[data-v-4252f8a9]{height:calc(100% - %?270?%)}.popup .popup-footer[data-v-4252f8a9]{height:%?100?%}.popup .popup-footer .confirm-btn[data-v-4252f8a9]{height:%?72?%;line-height:%?72?%;color:#fff;text-align:center;margin:%?20?% %?30?% 0;border-radius:%?10?%;font-size:%?28?%}.popup .popup-footer.bottom-safe-area[data-v-4252f8a9]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.choose-payment-popup .payment-item[data-v-4252f8a9]{display:flex;align-items:center;justify-content:space-between;height:%?90?%;margin:0 %?30?%;border-bottom:%?2?% solid #eee;padding:%?20?% 0}.choose-payment-popup .payment-item[data-v-4252f8a9]:nth-child(2){padding-top:0}.choose-payment-popup .payment-item[data-v-4252f8a9]:last-child{border-bottom:none}.choose-payment-popup .payment-item .iconfont[data-v-4252f8a9]{font-size:%?64?%}.choose-payment-popup .payment-item .icon-yue[data-v-4252f8a9]{color:#faa218}.choose-payment-popup .payment-item .icon-weixin1[data-v-4252f8a9]{color:#24af41}.choose-payment-popup .payment-item .icon-zhifubaozhifu-[data-v-4252f8a9]{color:#00a0e9}.choose-payment-popup .payment-item .icon-checkboxblank[data-v-4252f8a9]{font-size:%?40?%;color:#eee}.choose-payment-popup .payment-item .icon-yuan_checked[data-v-4252f8a9]{font-size:%?40?%}.choose-payment-popup .payment-item .name[data-v-4252f8a9]{margin-left:%?20?%;font-size:%?28?%;flex:1}.choose-payment-popup .payment-item .info-wrap[data-v-4252f8a9]{flex:1;margin-left:%?20?%}.choose-payment-popup .payment-item .info-wrap .name[data-v-4252f8a9]{margin-left:0;font-size:%?28?%;flex:1}.choose-payment-popup .payment-item .info-wrap .money[data-v-4252f8a9]{color:#909399;font-size:%?24?%}.choose-payment-popup .payment-item .box[data-v-4252f8a9]{flex:1;padding:0 %?10?%;line-height:inherit;text-align:right}.choose-payment-popup .payment-item .box uni-input[data-v-4252f8a9]{font-size:%?24?%!important}.choose-payment-popup .payment-item.set-pay-password[data-v-4252f8a9]{height:auto}.choose-payment-popup .payment-item.set-pay-password .box[data-v-4252f8a9]{font-size:%?24?%!important}.choose-payment-popup .pay-money[data-v-4252f8a9]{text-align:center;padding:%?20?% 0 %?40?% 0;background-color:#fff;font-weight:700;margin-top:%?30?%;line-height:1}.choose-payment-popup .pay-money .unit[data-v-4252f8a9]{margin-right:%?4?%;font-size:%?24?%}.choose-payment-popup .pay-money .money[data-v-4252f8a9]{font-size:%?32?%}.empty[data-v-4252f8a9]{width:100%;text-align:center;padding:%?40?% 0;color:#606266;font-size:%?24?%}',""]),t.exports=e},"2a87":function(t,e,a){"use strict";a.r(e);var o=a("a985"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=n.a},"4c42":function(t,e,a){"use strict";var o=a("c46e"),n=a.n(o);n.a},"554a":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'.weui-switch[data-v-7e2a453e]{display:block;position:relative;width:%?94?%;height:%?45?%;outline:0;border-radius:%?30?%;border:%?2?% solid;border-color:#dfdfdf;transition:background-color .1s,border .1s}.weui-switch .bgview[data-v-7e2a453e]{content:" ";position:absolute;top:0;left:0;width:%?94?%;height:%?45?%;border-radius:%?30?%;transition:-webkit-transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1),-webkit-transform .35s cubic-bezier(.45,1,.4,1)}.weui-switch .spotview[data-v-7e2a453e]{content:" ";position:absolute;top:%?2?%;left:%?4?%;width:%?40?%;height:%?40?%;border-radius:50%;background-color:#fff;box-shadow:0 %?2?% %?6?% rgba(0,0,0,.4);transition:-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35),-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35)}.weui-switch-on[data-v-7e2a453e]{border-color:#6f6f6f}.weui-switch-on .bgview[data-v-7e2a453e]{border-color:#1aad19}.weui-switch-on .spotview[data-v-7e2a453e]{-webkit-transform:translateX(%?48?%);transform:translateX(%?48?%)}',""]),t.exports=e},"67dd":function(t,e,a){"use strict";var o=a("1dd9"),n=a.n(o);n.a},7258:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return o}));var o={uniPopup:a("d745").default,nsSwitch:a("b0ec").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("uni-popup",{ref:"choosePaymentPopup",attrs:{type:"center","mask-click":!1}},[a("v-uni-view",{staticClass:"choose-payment-popup popup",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[t._v("支付方式")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close()}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":t.isIphoneX},attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"pay-money"},[a("v-uni-text",{staticClass:"money"},[t._v("支付金额"+t._s(t._f("moneyFormat")(t.payMoney))+"元")])],1),t.balanceDeduct>0&&1==t.balanceConfig&&t.sale?a("v-uni-view",{staticClass:"payment-item"},[a("v-uni-view",{staticClass:"iconfont icon-yue"}),a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-text",{staticClass:"name"},[t._v("余额抵扣")]),a("v-uni-view",{staticClass:"money"},[t._v("可用¥"+t._s(t.balanceDeduct))])],1),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==t.isBalance},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.useBalance.apply(void 0,arguments)}}})],1):t._e(),t.payMoney>0?[t.payTypeList.length?t._l(t.payTypeList,(function(e,o){return a("v-uni-view",{key:o,staticClass:"payment-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.payIndex=o}}},[a("v-uni-view",{staticClass:"iconfont",class:e.icon}),a("v-uni-text",{staticClass:"name"},[t._v(t._s(e.name))]),a("v-uni-text",{staticClass:"iconfont",class:t.payIndex==o?"icon-yuan_checked color-base-text":"icon-checkboxblank"})],1)})):[a("v-uni-view",{staticClass:"empty"},[t._v("平台尚未配置支付方式！")])]]:t._e()],2),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":t.isIphoneX}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm()}}},[t._v("确认支付")])],1)],1)],1)],1)},i=[]},"7aec":function(t,e,a){"use strict";a.r(e);var o=a("7258"),n=a("2a87");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("67dd");var s=a("828b"),c=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"4252f8a9",null,!1,o["a"],void 0);e["default"]=c.exports},"7e0a":function(t,e,a){"use strict";a.r(e);var o=a("171a"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=n.a},a985:function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("5c47"),a("2c10"),a("bf0f"),a("2797"),a("5ef2"),a("dd2b");var n=o(a("d745")),i=o(a("b0ec")),s=a("edd0"),c={name:"ns-payment",components:{uniPopup:n.default,nsSwitch:i.default},props:{balanceDeduct:{type:[Number,String],default:""},isBalance:{type:Number,default:0},payMoney:{type:[Number,String],default:0}},data:function(){return{isIphoneX:!1,payIndex:0,payTypeList:[{name:"支付宝支付",icon:"icon-zhifubaozhifu-",type:"alipay"},{name:"微信支付",icon:"icon-weixin1",type:"wechatpay"}],timer:null,payInfo:{},balanceConfig:0,sale:!0}},created:function(t){var e=window.location.href,a=e.match("presale/order_list/order_list"),o=e.match("presale/order_detail/order_detail");(a||o)&&(this.sale=!1),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getPayType(),this.getBalanceConfig()},methods:{open:function(){this.$refs.choosePaymentPopup.open()},close:function(){this.$refs.choosePaymentPopup.close()},useBalance:function(){this.$emit("useBalance")},confirm:function(){0==this.payTypeList.length&&this.payMoney>0?this.$util.showToast({title:"请选择支付方式！"}):(uni.showLoading({title:"支付中...",mask:!0}),this.$refs.choosePaymentPopup.close(),this.$emit("confirm"),uni.setStorageSync("pay_flag",1))},getPayInfo:function(t){var e=this;this.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:t},success:function(t){t.code>=0&&t.data?(e.payInfo=t.data,2==e.payInfo["pay_status"]?e.$util.redirectTo("/pages_tool/pay/result",{code:e.payInfo.out_trade_no},"","redirectTo"):e.pay()):(e.$util.showToast({title:"未获取到支付信息！"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1500))}})},getBalanceConfig:function(){var t=this;this.$api.sendRequest({url:"/api/pay/getBalanceConfig",data:{},success:function(e){t.balanceConfig=e.data.balance_show}})},getPayType:function(){var t=this;this.$api.sendRequest({url:"/api/pay/type",success:function(e){0==e.code&&(""==e.data.pay_type?t.payTypeList=[]:t.payTypeList.forEach((function(a,o){-1==e.data.pay_type.indexOf(a.type)&&t.payTypeList.splice(o,1)})))}})},pay:function(){var t=this,e=this.payTypeList[this.payIndex];if(e){var a="";a="BlindboxGoodsOrderPayNotify"==this.payInfo.event?"/pages_promotion/blindbox/index?outTradeNo=":"/pages_tool/pay/result?code=",this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:this.payInfo.out_trade_no,pay_type:e.type,return_url:encodeURIComponent(this.$config.h5Domain+a+this.payInfo.out_trade_no)},success:function(a){if(uni.hideLoading(),a.code>=0)switch(e.type){case"alipay":if(t.$util.isWeiXin()){var o=encodeURIComponent(a.data.data);t.$util.redirectTo("/pages_tool/pay/wx_pay",{wx_alipay:o,out_trade_no:t.payInfo.out_trade_no},"","redirectTo")}else location.href=a.data.data,t.checkPayStatus();break;case"wechatpay":if(t.$util.isWeiXin()){if("ios"==uni.getSystemInfoSync().platform)var n=uni.getStorageSync("initUrl");else n=location.href;t.$api.sendRequest({url:"/wechat/api/wechat/jssdkconfig",data:{url:n},success:function(e){var o=new s.Weixin,n=a.data.data;o.init(e.data),o.pay({timestamp:n.timestamp?n.timestamp:n.timeStamp,nonceStr:n.nonceStr,package:n.package,signType:n.signType,paySign:n.paySign},(function(e){"chooseWXPay:ok"==e.errMsg?"BlindboxGoodsOrderPayNotify"==t.payInfo.event?t.$util.redirectTo("/pages_promotion/blindbox/index",{outTradeNo:t.payInfo.out_trade_no},"","redirectTo"):t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo"):t.$util.showToast({title:e.errMsg})}),(function(e){t.$util.showToast({title:"您已取消支付"}),setTimeout((function(){t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"redirectTo")}),2e3)}))}})}else location.href=a.data.url,t.checkPayStatus();break}else t.$util.showToast({title:a.message})},fail:function(e){uni.hideLoading(),t.$util.showToast({title:"request:fail"})}})}},checkPayStatus:function(){var t=this;this.timer=setInterval((function(){t.$api.sendRequest({url:"/api/pay/status",data:{out_trade_no:t.payInfo.out_trade_no},success:function(e){0==e.code?2==e.data.pay_status&&(clearInterval(t.timer),t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo")):clearInterval(t.timer)}})}),1e3)}},deactivated:function(){clearInterval(this.timer)}};e.default=c},b0ec:function(t,e,a){"use strict";a.r(e);var o=a("bf29"),n=a("7e0a");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("4c42");var s=a("828b"),c=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"7e2a453e",null,!1,o["a"],void 0);e["default"]=c.exports},bf29:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("v-uni-view",{staticClass:"weui-switch",class:{"weui-switch-on":t.checked,"color-base-border":t.checked},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.change()}}},[a("v-uni-view",{staticClass:"bgview",class:{"color-base-bg":t.checked}}),a("v-uni-view",{staticClass:"spotview"})],1)],1)},n=[]},c46e:function(t,e,a){var o=a("554a");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=a("967d").default;n("95c2f08a",o,!0,{sourceMap:!1,shadowMode:!1})}}]);