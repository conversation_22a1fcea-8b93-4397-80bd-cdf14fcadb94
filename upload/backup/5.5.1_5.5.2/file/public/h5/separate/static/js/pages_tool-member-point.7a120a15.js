(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-point"],{"205d":function(t,e,i){"use strict";var a=i("56f7"),n=i.n(a);n.a},"2a6c":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.custom-navbar[data-v-318f00a0]{width:100vw;padding-bottom:%?20?%;position:fixed;left:0;top:0;z-index:100;background:unset}.custom-navbar .navbar-wrap[data-v-318f00a0]{position:relative;display:flex;align-items:center;justify-content:center;width:100%;height:100%}.custom-navbar .navbar-title[data-v-318f00a0]{color:#fff;font-size:%?32?%;font-weight:600}.custom-navbar .back[data-v-318f00a0]{position:absolute;color:#fff;left:%?30?%;font-size:%?40?%}.custom-navbar-block[data-v-318f00a0]{padding-bottom:%?20?%}.head-wrap[data-v-318f00a0]{width:100vw;background-size:100%;padding:%?60?% %?68?% %?140?% %?68?%;box-sizing:border-box;border-radius:0 0 100% 100%/0 0 %?70?% %?70?%;overflow:hidden}.head-wrap .title[data-v-318f00a0]{text-align:left;line-height:1;color:#f6f6f6}.head-wrap .point[data-v-318f00a0]{color:var(--btn-text-color);text-align:left;line-height:1;margin-bottom:%?20?%;font-size:%?64?%}.head-wrap .flex-box[data-v-318f00a0]{display:flex;margin-top:%?56?%}.head-wrap .flex-box .flex-item[data-v-318f00a0]{flex:1}.head-wrap .flex-box .flex-item .num[data-v-318f00a0]{font-size:%?34?%;margin-bottom:%?20?%;color:#fff;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.head-wrap .flex-box .flex-item uni-view[data-v-318f00a0]{text-align:left;color:#f6f6f6;line-height:1}.menu-wrap[data-v-318f00a0]{border-radius:%?20?%;margin:0 %?24?%;padding:%?30?%;background:#fff;display:flex;-webkit-transform:translateY(%?-90?%);transform:translateY(%?-90?%)}.menu-wrap .menu-item[data-v-318f00a0]{flex:1;text-align:left;display:flex;align-items:center}.menu-wrap .menu-item .icon[data-v-318f00a0]{width:%?88?%;height:%?88?%;background:#f3f3f3;border-radius:%?20?%;display:flex;align-items:center;justify-content:center;color:#fff;margin-right:%?24?%}.menu-wrap .menu-item .icon uni-image[data-v-318f00a0]{width:70%}.menu-wrap .menu-item .title[data-v-318f00a0]{font-size:%?32?%;font-weight:700;color:#333}.task-wrap[data-v-318f00a0]{background-color:#fff;margin:%?30?% %?24?%;border-radius:%?18?%;padding:%?32?%;-webkit-transform:translateY(%?-90?%);transform:translateY(%?-90?%)}.task-wrap .title[data-v-318f00a0]{font-size:%?32?%;text-align:left;margin-bottom:%?40?%;font-weight:700}.task-wrap .task-item[data-v-318f00a0]{border-radius:%?10?%;background:#fff;display:flex;align-items:center;margin-bottom:%?80?%}.task-wrap .task-item[data-v-318f00a0]:last-child{margin-bottom:%?30?%}.task-wrap .task-item .icon[data-v-318f00a0]{width:%?62?%;height:%?62?%;background:#f3f3f3;border-radius:%?20?%;display:flex;align-items:center;justify-content:center;color:#fff}.task-wrap .task-item .iconfont[data-v-318f00a0]{font-size:%?52?%;-webkit-background-clip:text!important;-webkit-text-fill-color:transparent;background:linear-gradient(135deg,#fe7849,#ff1959)}.task-wrap .task-item .iconshangpin[data-v-318f00a0]{font-size:%?48?%}.task-wrap .task-item .wrap[data-v-318f00a0]{flex:1;padding-left:%?26?%}.task-wrap .task-item .wrap .title[data-v-318f00a0]{line-height:1;font-size:%?28?%;font-weight:700;margin-bottom:0}.task-wrap .task-item .wrap .desc[data-v-318f00a0]{line-height:1;margin-top:%?10?%}.task-wrap .task-item .btn[data-v-318f00a0]{height:%?60?%;line-height:%?60?%;border-radius:%?60?%;text-align:center;width:%?140?%;color:#fff;font-size:%?26?%;font-weight:600;background:linear-gradient(135deg,#fe7849,#ff1959)}',""]),t.exports=e},4096:function(t,e,i){"use strict";i.r(e);var a=i("4afa"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"4afa":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e966");var a={data:function(){return{pointInfo:{point:0,totalPoint:0,totalConsumePoint:0,todayPoint:0},menuButtonBounding:{}}},onShow:function(){var t=this;this.storeToken?this.getMemberPoint():this.$nextTick((function(){t.$refs.login.open("/pages_tool/member/point")}))},onLoad:function(){},methods:{toSign:function(){this.$util.redirectTo("/pages_tool/member/signin")},getMemberPoint:function(){var t=this;this.$api.sendRequest({url:"/api/memberaccount/point",data:{},success:function(e){0==e.code&&(t.pointInfo.point=parseInt(e.data.point),t.pointInfo.totalPoint=parseInt(e.data.point_all),t.pointInfo.totalConsumePoint=parseInt(e.data.point_use),t.pointInfo.todayPoint=parseInt(e.data.point_today)),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})}},onBackPress:function(t){return"navigateBack"!==t.from&&(this.$util.redirectTo("/pages/member/index",{},"reLaunch"),!0)},watch:{storeToken:function(t,e){t&&this.getMemberPoint()}}};e.default=a},"52acf":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={pageMeta:i("7854").default,nsLogin:i("2910").default,loadingCover:i("c003").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"point"},[i("v-uni-view",{staticClass:"head-wrap",style:{background:"url("+t.$util.img("public/uniapp/point/point_bg.png")+") no-repeat right bottom/ auto 340rpx, linear-gradient(314deg, #F16914 0%, #FEAA4C 100%)"}},[i("v-uni-view",{staticClass:"point price-font"},[t._v(t._s(t.pointInfo.point))]),i("v-uni-view",{staticClass:"title"},[t._v("当前积分")]),i("v-uni-view",{staticClass:"flex-box"},[i("v-uni-view",{staticClass:"flex-item"},[i("v-uni-view",{staticClass:"num price-font"},[t._v(t._s(t.pointInfo.totalPoint))]),i("v-uni-view",{staticClass:"font-size-tag"},[t._v("累计积分")])],1),i("v-uni-view",{staticClass:"flex-item"},[i("v-uni-view",{staticClass:"num price-font"},[t._v(t._s(t.pointInfo.totalConsumePoint))]),i("v-uni-view",{staticClass:"font-size-tag"},[t._v("累计消费")])],1),i("v-uni-view",{staticClass:"flex-item"},[i("v-uni-view",{staticClass:"num price-font"},[t._v(t._s(t.pointInfo.todayPoint))]),i("v-uni-view",{staticClass:"font-size-tag"},[t._v("今日获得")])],1)],1)],1),i("v-uni-view",{staticClass:"menu-wrap"},[i("v-uni-view",{staticClass:"menu-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_tool/member/point_detail")}}},[i("v-uni-view",{staticClass:"icon"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/point/point_detail_icon.png"),mode:"widthFix"}})],1),i("v-uni-text",{staticClass:"title"},[t._v("积分明细")])],1),i("v-uni-view",{staticClass:"menu-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_promotion/point/list")}}},[i("v-uni-view",{staticClass:"icon"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/point/point_shop.png"),mode:"widthFix"}})],1),i("v-uni-text",{staticClass:"title"},[t._v("积分商城")])],1)],1),i("v-uni-view",{staticClass:"task-wrap"},[i("v-uni-view",{staticClass:"title"},[t._v("做任务赚积分")]),i("v-uni-view",{staticClass:"task-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toSign.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"icon"},[i("v-uni-text",{staticClass:"iconfont icon-qiandao1"})],1),i("v-uni-view",{staticClass:"wrap"},[i("v-uni-view",{staticClass:"title"},[t._v("每日签到")]),i("v-uni-view",{staticClass:"desc color-tip font-size-tag"},[t._v("连续签到可获得更多积分")])],1),i("v-uni-view",{staticClass:"btn"},[t._v("去签到")])],1),i("v-uni-view",{staticClass:"task-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-view",{staticClass:"icon"},[i("v-uni-text",{staticClass:"iconfont icon-shangpin"})],1),i("v-uni-view",{staticClass:"wrap"},[i("v-uni-view",{staticClass:"title"},[t._v("购买商品")]),i("v-uni-view",{staticClass:"desc color-tip font-size-tag"},[t._v("购买商品可获得积分")])],1),i("v-uni-view",{staticClass:"btn"},[t._v("去下单")])],1)],1),i("ns-login",{ref:"login"}),i("loading-cover",{ref:"loadingCover"})],1)],1)},o=[]},"56f7":function(t,e,i){var a=i("2a6c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("4b4ec4fe",a,!0,{sourceMap:!1,shadowMode:!1})},7854:function(t,e,i){"use strict";i.r(e);var a=i("8ba8"),n=i("f48d");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);var r=i("828b"),s=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=s.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=n},d6dd:function(t,e,i){"use strict";i.r(e);var a=i("52acf"),n=i("4096");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("205d");var r=i("828b"),s=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"318f00a0",null,!1,a["a"],void 0);e["default"]=s.exports},f48d:function(t,e,i){"use strict";i.r(e);var a=i("cc1b"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a}}]);