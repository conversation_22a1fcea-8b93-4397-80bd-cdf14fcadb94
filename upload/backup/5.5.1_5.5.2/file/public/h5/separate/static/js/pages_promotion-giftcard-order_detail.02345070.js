(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-giftcard-order_detail"],{"03df":function(t,e,o){"use strict";o.r(e);var i=o("d307"),a=o.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(n);e["default"]=a.a},"14aa":function(t,e,o){"use strict";var i=o("38ee"),a=o.n(i);a.a},"38ee":function(t,e,o){var i=o("e4d4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=o("967d").default;a("2952f38e",i,!0,{sourceMap:!1,shadowMode:!1})},"396b":function(t,e,o){"use strict";o.r(e);var i=o("8db3"),a=o("03df");for(var n in a)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(n);o("14aa");var r=o("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"a6e51f06",null,!1,i["a"],void 0);e["default"]=s.exports},7854:function(t,e,o){"use strict";o.r(e);var i=o("8ba8"),a=o("f48d");for(var n in a)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(n);var r=o("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},"8ba8":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},"8db3":function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){return i}));var i={pageMeta:o("7854").default,nsPayment:o("7aec").default,loadingCover:o("c003").default,nsLogin:o("2910").default},a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",[o("page-meta",{attrs:{"page-style":t.themeColor}}),o("v-uni-view",{staticClass:"order-container",class:{"safe-area":t.isIphoneX}},[o("v-uni-view",{staticClass:"site-wrap"},[o("v-uni-view",{staticClass:"site-body"},[o("v-uni-view",{staticClass:"card-head"},[o("v-uni-image",{attrs:{src:t.$util.img("balance"==t.orderDetail.card_right_type?"public/uniapp/giftcard/order-icon-recharge.png":"public/uniapp/giftcard/order-icon-gift.png"),mode:"widthFix"}}),o("v-uni-text",[t._v(t._s("balance"==t.orderDetail.card_right_type?"储值卡":"礼品卡"))]),o("v-uni-view",{staticClass:"order-status "},[t._v(t._s(t.orderDetail.order_status_name))])],1),o("v-uni-view",{staticClass:"giftcard-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toCardInfo()}}},[o("v-uni-view",{staticClass:"card-img"},[o("v-uni-image",{attrs:{src:t.orderDetail.card_cover?t.$util.img(t.orderDetail.card_cover):t.$util.img("public/uniapp/giftcard/default_card.png"),mode:"aspectFill"}})],1),o("v-uni-view",{staticClass:"goods-info"},[o("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(t.orderDetail.order_name))]),o("v-uni-view",{staticClass:"goods-price price-font"},[t._v("￥"+t._s(t.orderDetail.goods_money))])],1)],1),o("v-uni-view",{staticClass:"order-content"},[o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"label"},[t._v("订单号")]),o("v-uni-view",{staticClass:"value"},[t._v(t._s(t.orderDetail.order_no))])],1),o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"label"},[t._v("下单时间")]),o("v-uni-view",{staticClass:"value"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderDetail.create_time)))])],1),o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"label"},[t._v("买家留言")]),o("v-uni-view",{staticClass:"value"},[t._v(t._s(t.orderDetail.buyer_message?t.orderDetail.buyer_message:"无"))])],1),o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"label"},[t._v("购买数量")]),o("v-uni-view",{staticClass:"value"},[t._v(t._s(t.orderDetail.num))])],1),o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"label"},[t._v("订单金额")]),o("v-uni-view",{staticClass:"value"},[t._v("￥"+t._s(t.orderDetail.order_money))])],1)],1),"topay"==t.orderDetail.order_status?o("v-uni-view",{staticClass:"button"},[o("v-uni-view",{staticClass:"button-left"},[o("v-uni-button",{attrs:{size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeOrder(t.orderDetail.order_id)}}},[t._v("关闭订单")])],1),o("v-uni-view",{staticClass:"button-right"},[o("v-uni-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openChoosePayment()}}},[t._v("立即支付")])],1)],1):t._e(),"complete"==t.orderDetail.order_status?o("v-uni-view",{staticClass:"button"},[o("v-uni-view",{staticClass:"button-right"},[o("v-uni-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_promotion/giftcard/list",{order_id:t.orderDetail.order_id})}}},[t._v("查看卡包")])],1)],1):t._e()],1)],1),o("v-uni-view",{staticClass:"tab-bar-placeholder"}),o("ns-payment",{ref:"choosePaymentPopup",attrs:{payMoney:t.orderDetail.pay_money,isBalance:t.orderDetail&&t.orderDetail.giftcard_info&&"balance"!=t.orderDetail.giftcard_info.card_right_type?1:0},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.gotoBuy()}}}),o("loading-cover",{ref:"loadingCover"}),o("ns-login",{ref:"login"})],1)],1)},n=[]},cc1b:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("5ef2"),o("64aa"),o("5c47"),o("a1c1"),o("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var o=function o(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",o),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",o)}})}}}};e.default=a},d307:function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("bf0f"),o("2797"),o("5ef2");var a=i(o("d745")),n={components:{uniPopup:a.default},data:function(){return{isIphoneX:!1,orderId:"",orderDetail:"all",goodsOpen:!0}},onLoad:function(t){var e=this;if(t.order_id&&(this.orderId=t.order_id),t.scene){var o=decodeURIComponent(t.scene);o=o.split("&"),o.length&&o.forEach((function(t){-1!=t.indexOf("order_id")&&(e.orderId=t.split("-")[1])}))}},onShow:function(){var t=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?this.getData():this.$nextTick((function(){t.$refs.login.open("/pages_promotion/giftcard/order_detail?order_id="+t.orderId)}))},methods:{getData:function(){var t=this;this.$api.sendRequest({url:"/giftcard/api/order/detail",data:{order_id:this.orderId},success:function(e){e.code>=0?(t.orderDetail=e.data,setTimeout((function(){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}),300)):(t.$util.showToast({title:e.message}),setTimeout((function(){t.$util.redirectTo("/pages_promotion/giftcard/order_list")}),1500))},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},openChoosePayment:function(){this.storeToken?(uni.setStorageSync("paySource","giftcard"),this.$refs.choosePaymentPopup.open()):this.$util.showToast({title:"您尚未登录，请先登录"})},gotoBuy:function(){this.$refs.choosePaymentPopup.getPayInfo(this.orderDetail.out_trade_no)},imageError:function(t){this.orderDetail.order_goods_list[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},toCardInfo:function(){this.orderDetail.card_list.length>0?this.$util.redirectTo("/pages_promotion/giftcard/card_info",{member_card_id:this.orderDetail.card_list[0]["member_card_id"]}):this.$util.showToast({title:"礼品卡不存在或已送出"})},closeOrder:function(t){var e=this;uni.showModal({title:"提示",content:"您确定要关闭该订单吗？",success:function(o){o.confirm&&e.$api.sendRequest({url:"/giftcard/api/order/close",data:{order_id:t},success:function(t){t.code>=0?e.getData():e.$util.showToast({title:t.message})}})}})}}};e.default=n},e4d4:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.site-wrap[data-v-a6e51f06]{margin:0 %?24?% 0;background:#fff;border-radius:%?16?%;padding:%?30?% 0 %?1?%}.site-wrap .site-body[data-v-a6e51f06]{margin:0 %?30?%}.order-container[data-v-a6e51f06]{padding-bottom:%?160?%;background-color:#fff;min-height:100vh;box-sizing:border-box}.order-container.safe-area[data-v-a6e51f06]{padding-bottom:%?188?%}.card-head[data-v-a6e51f06]{display:flex;align-items:center;margin-bottom:%?20?%;position:relative}.card-head uni-image[data-v-a6e51f06]{width:%?34?%;max-height:%?28?%;margin-right:%?10?%}.card-head uni-text[data-v-a6e51f06]{font-size:%?24?%;font-weight:700}.card-head .order-status[data-v-a6e51f06]{position:absolute;right:0;color:var(--giftcard-promotion-color)}.giftcard-wrap[data-v-a6e51f06]{display:flex;padding-bottom:%?30?%}.giftcard-wrap .card-img[data-v-a6e51f06]{width:%?270?%;height:%?164?%;max-height:%?164?%;border-radius:%?18?%;overflow:hidden}.giftcard-wrap .card-img uni-image[data-v-a6e51f06]{width:100%;height:100%}.giftcard-wrap .goods-info[data-v-a6e51f06]{display:flex;flex-direction:column;margin-left:%?30?%;width:calc(100% - %?300?%);height:%?164?%;position:relative}.giftcard-wrap .goods-info .goods-name[data-v-a6e51f06]{font-weight:700;font-size:%?30?%;overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;line-height:1.5}.giftcard-wrap .goods-info .goods-price[data-v-a6e51f06]{position:absolute;color:var(--price-color);bottom:0;left:0;font-size:%?32?%}.goods-list[data-v-a6e51f06]{border-top:%?0?% solid #f0f0f0;padding:%?30?% 0;display:flex}.goods-list .goods-left[data-v-a6e51f06]{display:flex;width:calc(100% - %?108?%);overflow:hidden;white-space:nowrap;position:relative;align-items:center}.goods-list .goods-left uni-image[data-v-a6e51f06]{width:%?108?%;max-height:%?108?%;margin-right:%?22?%;flex-shrink:0;border-radius:%?16?%}.goods-list .goods-left[data-v-a6e51f06]:after{content:" ";box-shadow:%?-4?% 0 %?24?% rgba(0,0,0,.8);width:%?1?%;height:%?80?%;right:%?-1?%;top:%?14?%;position:absolute;background:hsla(0,0%,100%,0)}.goods-list .goods-more[data-v-a6e51f06]{width:%?108?%;height:%?108?%;display:flex;align-items:center;justify-content:center;font-size:%?26?%;position:relative}.goods-list .goods-more uni-text[data-v-a6e51f06]{font-size:%?28?%;line-height:1}.goods-list.goodsOpen[data-v-a6e51f06]{flex-direction:column;position:relative}.goods-list.goodsOpen .btn[data-v-a6e51f06]{position:absolute;right:%?20?%;top:%?50?%;font-size:%?26?%;display:flex;align-items:baseline}.goods-list.goodsOpen .btn uni-text[data-v-a6e51f06]{line-height:1.1;font-size:%?24?%;margin-left:%?8?%}.goods-list .goods-item[data-v-a6e51f06]{display:flex;margin-bottom:%?20?%}.goods-list .goods-item .goods-image[data-v-a6e51f06]{width:%?108?%;height:%?108?%;overflow:hidden;border-radius:%?18?%;margin-right:%?20?%}.goods-list .goods-item .goods-image uni-image[data-v-a6e51f06]{width:%?108?%;height:%?108?%;max-height:%?108?%}.goods-list .goods-item .goods-info[data-v-a6e51f06]{width:calc(100% - %?128?%)}.goods-list .goods-item .goods-info .goods-name[data-v-a6e51f06]{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;line-height:1.5;font-size:%?28?%}.goods-list .goods-item .goods-info .goods-num[data-v-a6e51f06]{margin-top:%?20?%}.goods-list .goods-item .goods-info .goods-num uni-text[data-v-a6e51f06]{color:#666;font-size:%?24?%}.goods-list .goods-item .goods-info .goods-num uni-text .num[data-v-a6e51f06]{margin-left:%?50?%}.order-content[data-v-a6e51f06]{padding:%?20?% 0;border-top:%?2?% solid #f0f0f0}.order-content .content-item[data-v-a6e51f06]{display:flex;justify-content:space-between;align-items:baseline;padding:%?10?% 0}.order-content .content-item .label[data-v-a6e51f06]{color:#888}.order-content .content-item .value[data-v-a6e51f06]{width:calc(100% - %?150?%);text-align:right}.button[data-v-a6e51f06]{background-color:#fff;position:fixed;bottom:0;left:0;padding:%?20?% %?30?% %?20?%;box-sizing:border-box;display:flex;align-items:center;flex-direction:column;justify-content:center;width:100%}.button .button-left[data-v-a6e51f06]{width:100%;margin-bottom:%?20?%}.button .button-left uni-button[data-v-a6e51f06]{background-color:#fff;border:%?2?% solid #979797;color:#666}.button .button-right[data-v-a6e51f06]{width:100%}.button .button-right uni-button[data-v-a6e51f06]{border:%?2?% solid var(--giftcard-promotion-color);color:#fff;background-color:var(--giftcard-promotion-color)}.button uni-button[data-v-a6e51f06]{height:%?80?%;border-radius:%?6?%;line-height:1;display:flex;align-items:center;justify-content:center}.tab-bar-placeholder[data-v-a6e51f06]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?220?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?220?%)}',""]),t.exports=e},f48d:function(t,e,o){"use strict";o.r(e);var i=o("cc1b"),a=o.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(n);e["default"]=a.a}}]);