(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-point-result"],{"4aca":function(t,e,n){"use strict";n.r(e);var o=n("ab0a"),a=n("63af");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("7744");var r=n("828b"),c=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"4ce7ec29",null,!1,o["a"],void 0);e["default"]=c.exports},"549b":function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container[data-v-4ce7ec29]{width:100vw;height:100vh;background:#fff}.container .image-wrap[data-v-4ce7ec29]{display:flex;justify-content:center;padding:%?200?% 0 %?40?% 0}.container .image-wrap .result-image[data-v-4ce7ec29]{width:%?166?%}.container .msg[data-v-4ce7ec29]{text-align:center;line-height:1;margin-bottom:%?50?%;font-size:%?28?%;color:#000}.container .pay-amount[data-v-4ce7ec29]{color:#999;text-align:center;line-height:1;margin-bottom:%?30?%}.container .action[data-v-4ce7ec29]{width:90%;margin:0 auto;text-align:center;margin-top:%?150?%;display:flex;justify-content:space-between}.container .action .btn[data-v-4ce7ec29]{width:%?310?%;height:%?78?%;border:%?2?% solid #fff;border-radius:%?10?%;font-size:%?24?%;display:flex;align-items:center;justify-content:center}.container .action .alone[data-v-4ce7ec29]{margin-left:0;width:60%}.container .action .go-home[data-v-4ce7ec29]{color:#fff}',""]),t.exports=e},"63af":function(t,e,n){"use strict";n.r(e);var o=n("fca9"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},7744:function(t,e,n){"use strict";var o=n("8302"),a=n.n(o);a.a},7854:function(t,e,n){"use strict";n.r(e);var o=n("8ba8"),a=n("f48d");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);var r=n("828b"),c=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=c.exports},8302:function(t,e,n){var o=n("549b");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("2b030ba2",o,!0,{sourceMap:!1,shadowMode:!1})},"8ba8":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},ab0a:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={pageMeta:n("7854").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("page-meta",{attrs:{"page-style":t.themeColor}}),n("v-uni-view",{staticClass:"container"},[n("v-uni-view",{staticClass:"image-wrap"},[n("v-uni-image",{staticClass:"result-image",attrs:{src:t.$util.img("public/uniapp/pay/pay_success.png"),mode:"widthFix"}})],1),n("v-uni-view",{staticClass:"msg"},[t._v(t._s(t.$lang("exchangeSuccess")))]),n("v-uni-view",{staticClass:"action"},[n("v-uni-view",{staticClass:"btn color-base-border color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toOrderList()}}},[t._v(t._s(t.$lang("see")))]),n("v-uni-view",{staticClass:"btn go-home color-base-bg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toIndex.apply(void 0,arguments)}}},[t._v(t._s(t.$lang("goHome")))])],1)],1)],1)},i=[]},cc1b:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("64aa"),n("5c47"),n("a1c1"),n("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var n=function n(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",n),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",n)}})}}}};e.default=a},f48d:function(t,e,n){"use strict";n.r(e);var o=n("cc1b"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},fca9:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{}},onShow:function(){},methods:{toOrderList:function(){this.$util.redirectTo("/pages_promotion/point/order_list",{},"redirectTo")},toIndex:function(){this.$util.redirectTo("/pages/index/index")}}}}}]);