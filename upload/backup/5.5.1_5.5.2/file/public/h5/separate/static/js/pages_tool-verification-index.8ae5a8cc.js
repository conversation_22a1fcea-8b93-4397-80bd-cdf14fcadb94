(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-verification-index"],{"0365":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container[data-v-5d9a3cf2]{width:100vw;height:100vh}.container .action-wrap[data-v-5d9a3cf2]{padding:%?100?% 0;background:#fff;position:relative}.container .action-wrap .record-wrap[data-v-5d9a3cf2]{position:absolute;top:%?30?%;right:%?30?%}.container .action-wrap .record-wrap .iconfont[data-v-5d9a3cf2]{font-size:%?24?%;margin-right:%?10?%}.container .action-wrap .sweep-code[data-v-5d9a3cf2]{width:%?400?%;height:%?400?%;box-shadow:0 8px 8px 0 rgba(0,0,0,.03),0 6px 3px 0 rgba(0,0,0,.02);border-radius:50%;margin:0 auto;text-align:center;line-height:%?400?%;background:var(--base-color)}.container .action-wrap .sweep-code .iconfont[data-v-5d9a3cf2]{color:#fff;font-size:%?150?%}.container .action-wrap .manual-input[data-v-5d9a3cf2]{width:70%;margin:auto}.container .action-wrap .manual-input .process-wrap[data-v-5d9a3cf2]{height:%?140?%;display:flex;padding-top:%?60?%}.container .action-wrap .manual-input .process-wrap .wrap[data-v-5d9a3cf2]{flex:1;text-align:center}.container .action-wrap .manual-input .process-wrap .wrap ._icon[data-v-5d9a3cf2]{width:%?60?%;height:%?60?%;background:#eee;border-radius:50%;margin:0 auto;color:#909399}.container .action-wrap .manual-input .process-wrap .wrap ._icon .iconfont[data-v-5d9a3cf2]{font-size:%?32?%}.container .action-wrap .manual-input .process-wrap .wrap ._text[data-v-5d9a3cf2]{font-size:%?24?%;margin-top:%?10?%;color:#909399}.container .action-wrap .manual-input ._input[data-v-5d9a3cf2]{height:%?80?%;border:1px solid #eee;border-radius:%?8?%;box-sizing:border-box;padding:%?20?%;font-size:%?28?%;text-align:center}.container .action-wrap .manual-input ._placeholder[data-v-5d9a3cf2]{font-size:%?28?%;text-align:center}.container .action-wrap .manual-input ._btn[data-v-5d9a3cf2]{margin-top:%?40?%;height:%?80?%;line-height:%?80?%}.container .arc-edge[data-v-5d9a3cf2]{width:100%;height:%?80?%;background:#fff;border-radius:%?400?%/%?40?%;\r\n  /*上下有弧度的边*/-webkit-transform:translateY(-50%);transform:translateY(-50%)}.container .action-type-wrap[data-v-5d9a3cf2]{width:70%;height:%?90?%;background:#fff;border-radius:%?90?%;display:flex;position:relative;box-shadow:0 6px 6px 0 rgba(0,0,0,.03),0 4px 2px 0 rgba(0,0,0,.04);margin:%?100?% auto}.container .action-type-wrap .action[data-v-5d9a3cf2]{flex:1;text-align:center;color:#303133}.container .action-type-wrap .action ._icon[data-v-5d9a3cf2]{line-height:25px;height:25px}.container .action-type-wrap .action ._text[data-v-5d9a3cf2]{font-size:%?24?%;line-height:1}.container .action-type-wrap .icon-tiaoxingmasaomiao[data-v-5d9a3cf2]{width:%?110?%;height:%?110?%;border-radius:50%;-webkit-transform:translateY(%?-10?%);transform:translateY(%?-10?%);box-shadow:0 8px 8px 0 rgba(0,0,0,.03),0 6px 3px 0 rgba(0,0,0,.02);text-align:center;line-height:%?110?%;background:var(--base-color);color:#fff;font-size:%?32?%}',""]),t.exports=e},1031:function(t,e,a){"use strict";a.r(e);var i=a("3bf0"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"3bf0":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("5c47"),a("2c10"),a("0506");var n=a("edd0"),o=(i(a("2f8f")),{data:function(){return{operationType:"manualInput",verify_code:"",isFocus:!1,detail_path:"/pages_tool/verification/detail"}},onLoad:function(){},onShow:function(){var t=this;this.storeToken?this.checkIsVerifier():this.$nextTick((function(){t.$refs.login.open("/pages_tool/verification/index")}))},methods:{focus:function(){this.isFocus=!this.isFocus},scanCode:function(){var t=this;if(this.$util.isWeiXin()){if("ios"==uni.getSystemInfoSync().platform)var e=uni.getStorageSync("initUrl");else e=location.href;this.$api.sendRequest({url:"/wechat/api/wechat/jssdkconfig",data:{url:e},success:function(e){if(0==e.code){var a=new n.Weixin;a.init(e.data),a.scanQRCode((function(e){if(e.resultStr){var a=e.resultStr,i="";if(-1!=a.indexOf(t.detail_path)){var n=a.match(/\?code=(.+)/);2==n.length&&(i=n[1])}else-1!=a.indexOf("CODE_128")&&(i=a.split(",")[1]);if(!i)return void t.$util.showToast({title:"请扫码正确的条码或二维码"});t.$util.redirectTo(t.detail_path+"?code="+i)}}))}else t.$util.showToast({title:e.message})}})}},changeOperationType:function(t){"sweepCode"!=t||this.$util.isWeiXin()?this.operationType=t:this.$util.showToast({title:"H5端不支持扫码核销"})},checkIsVerifier:function(){var t=this;this.$api.sendRequest({url:"/api/verify/checkisverifier",success:function(e){e.data||(t.$util.showToast({title:"非核销员无此权限"}),setTimeout((function(){t.$util.redirectTo("/pages/member/index")}),1e3)),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},confirm:function(){var t=this;if(!/[\S]+/.test(this.verify_code))return this.$util.showToast({title:"请输入核销码"}),!1;this.$api.sendRequest({url:"/api/verify/verifyInfo",data:{verify_code:this.verify_code},success:function(e){e.code>=0?t.$util.redirectTo("/pages_tool/verification/detail",{code:t.verify_code}):t.$util.showToast({title:e.message})}})}},watch:{storeToken:function(t,e){t&&this.checkIsVerifier()}}});e.default=o},"527d":function(t,e,a){var i=a("0365");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("54ea2dce",i,!0,{sourceMap:!1,shadowMode:!1})},"6e79":function(t,e,a){"use strict";var i=a("527d"),n=a.n(i);n.a},7854:function(t,e,a){"use strict";a.r(e);var i=a("8ba8"),n=a("f48d");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);var r=a("828b"),c=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=c.exports},"8ba8":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},cc1b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var a=function a(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",a),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",a)}})}}}};e.default=n},d2a7:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={pageMeta:a("7854").default,nsLogin:a("2910").default,loadingCover:a("c003").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"action-wrap"},[a("v-uni-view",{staticClass:"record-wrap color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_tool/verification/list")}}},[a("v-uni-text",{staticClass:"iconfont icon-jilu color-base-text"}),a("v-uni-text",[t._v("核销记录")])],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"sweepCode"==t.operationType,expression:"operationType == 'sweepCode'"}],staticClass:"sweep-code ns-gradient-otherpages-member-balance-balance-rechange",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.scanCode.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"iconfont icon-saoma"})],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"manualInput"==t.operationType,expression:"operationType == 'manualInput'"}],staticClass:"manual-input"},[a("v-uni-view",{staticClass:"process-wrap"},[a("v-uni-view",{staticClass:"wrap"},[a("v-uni-view",{staticClass:"_icon"},[a("v-uni-text",{staticClass:"iconfont icon-shurutianxiebi color-base-text"})],1),a("v-uni-view",{staticClass:"_text"},[t._v("输入核销码")])],1),a("v-uni-view",[a("v-uni-view",[a("v-uni-text",{staticClass:"iconfont icon-jiang-copy color-tip"})],1)],1),a("v-uni-view",{staticClass:"wrap"},[a("v-uni-view",{staticClass:"_icon"},[a("v-uni-text",{staticClass:"iconfont icon-hexiao color-base-text"})],1),a("v-uni-view",{staticClass:"_text"},[t._v("核销")])],1)],1),a("v-uni-input",{ref:"input",staticClass:"_input",attrs:{type:"text",placeholder:"请输入核销码","placeholder-class":"_placeholder",focus:t.isFocus},model:{value:t.verify_code,callback:function(e){t.verify_code=e},expression:"verify_code"}}),a("v-uni-view",{staticClass:"_btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm.apply(void 0,arguments)}}},[a("v-uni-button",{attrs:{type:"primary"}},[t._v("确认")])],1)],1)],1),a("v-uni-view",{staticClass:"arc-edge"}),a("v-uni-view",{staticClass:"action-type-wrap"},[a("v-uni-view",{staticClass:"action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeOperationType("sweepCode")}}},[a("v-uni-view",{staticClass:"_icon"},[a("v-uni-text",{staticClass:"iconfont icon-saoma"})],1),a("v-uni-view",{staticClass:"_text"},[t._v("扫码核销")])],1),a("v-uni-view",{staticClass:"iconfont icon-tiaoxingmasaomiao ns-gradient-otherpages-member-balance-balance-rechange"}),a("v-uni-view",{staticClass:"action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeOperationType("manualInput")}}},[a("v-uni-view",{staticClass:"_icon"},[a("v-uni-text",{staticClass:"iconfont icon-shuru"})],1),a("v-uni-view",{staticClass:"_text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.focus.apply(void 0,arguments)}}},[t._v("手动输入")])],1)],1),a("ns-login",{ref:"login"}),a("loading-cover",{ref:"loadingCover"})],1)],1)},o=[]},dc0bb:function(t,e,a){"use strict";a.r(e);var i=a("d2a7"),n=a("1031");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("6e79");var r=a("828b"),c=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"5d9a3cf2",null,!1,i["a"],void 0);e["default"]=c.exports},f48d:function(t,e,a){"use strict";a.r(e);var i=a("cc1b"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a}}]);