(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-store_notes-note_detail"],{"015d":function(t,e,i){"use strict";i.r(e);var o=i("0f46"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},"0817":function(t,e,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("5c47"),i("2c10"),i("a1c1"),i("23f4"),i("7d2f"),i("9c4e"),i("ab80"),i("aa9c"),i("473f"),i("bf0f"),i("3efd");var n=o(i("af87")),a=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,s=/^<\/([-A-Za-z0-9_]+)[^>]*>/,d=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,r=m("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),c=m("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),l=m("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),f=m("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),u=m("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),g=m("script,style");function m(t){for(var e={},i=t.split(","),o=0;o<i.length;o++)e[i[o]]=!0;return e}var h=function(t){t=function(t){return t.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(t),t=function(t){return t=t.replace(/<!--[\s\S]*-->/gi,""),t}(t),t=function(t){var e='<img style="width:100% !important;display:block;max-width: '.concat("100%",' !important;"');return t=t.replace(/\\/g,"").replace(/<img/g,e),t=t.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(t,i){return e+' src="'+n.default.img(i)+'"/>'})),t}(t),t=function(t){return t=t.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(t,e){return t=t.replace(/[:](\s?)[\s\S]*/gi,(function(t,e){return t.replace(/"/g,"'")})),t})),t}(t);var e=[],i={node:"root",children:[]};return function(t,e){var i,o,n,m=[],h=t;m.last=function(){return this[this.length-1]};while(t){if(o=!0,m.last()&&g[m.last()])t=t.replace(new RegExp("([\\s\\S]*?)</"+m.last()+"[^>]*>"),(function(t,i){return i=i.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),e.chars&&e.chars(i),""})),b("",m.last());else if(0==t.indexOf("\x3c!--")?(i=t.indexOf("--\x3e"),i>=0&&(e.comment&&e.comment(t.substring(4,i)),t=t.substring(i+3),o=!1)):0==t.indexOf("</")?(n=t.match(s),n&&(t=t.substring(n[0].length),n[0].replace(s,b),o=!1)):0==t.indexOf("<")&&(n=t.match(a),n&&(t=t.substring(n[0].length),n[0].replace(a,v),o=!1)),o){i=t.indexOf("<");var p=i<0?t:t.substring(0,i);t=i<0?"":t.substring(i),e.chars&&e.chars(p)}if(t==h)throw"Parse Error: "+t;h=t}function v(t,i,o,n){if(i=i.toLowerCase(),c[i])while(m.last()&&l[m.last()])b("",m.last());if(f[i]&&m.last()==i&&b("",i),n=r[i]||!!n,n||m.push(i),e.start){var a=[];o.replace(d,(function(t,e){var i=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:u[e]?e:"";a.push({name:e,value:i,escaped:i.replace(/(^|[^\\])"/g,'$1\\"')})})),e.start&&e.start(i,a,n)}}function b(t,i){if(i){for(o=m.length-1;o>=0;o--)if(m[o]==i)break}else var o=0;if(o>=0){for(var n=m.length-1;n>=o;n--)e.end&&e.end(m[n]);m.length=o}}b()}(t,{start:function(t,o,n){var a={name:t};if(0!==o.length&&(a.attrs=function(t){return t.reduce((function(t,e){var i=e.value,o=e.name;return t[o]?t[o]=t[o]+" "+i:t[o]=i,t}),{})}(o)),n){var s=e[0]||i;s.children||(s.children=[]),s.children.push(a)}else e.unshift(a)},end:function(t){var o=e.shift();if(o.name!==t&&console.error("invalid state: mismatch end tag"),0===e.length)i.children.push(o);else{var n=e[0];n.children||(n.children=[]),n.children.push(o)}},chars:function(t){var o={type:"text",text:t};if(0===e.length)i.children.push(o);else{var n=e[0];n.children||(n.children=[]),n.children.push(o)}},comment:function(t){var i={node:"comment",text:t},o=e[0];o.children||(o.children=[]),o.children.push(i)}}),i.children};e.default=h},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=o},"4d00":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={pageMeta:i("7854").default,nsMpHtml:i("d108").default,loadingCover:i("c003").default,hoverNav:i("c1f1").default,nsLogin:i("2910").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"goods-detail"},["goods_item"==t.noteType?i("v-uni-view",{staticClass:"goods-item"},[i("v-uni-image",{staticClass:"item-img",attrs:{src:t.$util.img(t.goodsItemInfo.goods_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError()}}}),1==t.goodsItemInfo.is_show_release_time?i("v-uni-text",{staticClass:"item-title"},[t._v(t._s(t.goodsItemInfo.note_title))]):t._e(),t.goodsItemInfo.goods_highlights.length?i("v-uni-view",{staticClass:"item-lightspot"},t._l(t.goodsItemInfo.goods_highlights,(function(e,o){return i("v-uni-text",{key:o,staticClass:"color-base-bg"},[t._v(t._s(e))])})),1):t._e(),1==t.goodsItemInfo.is_show_release_time?i("v-uni-text",{staticClass:"item-time"},[t._v(t._s(t.$util.timeStampTurnTime(t.goodsItemInfo.create_time,"Y-m-d")))]):t._e(),i("v-uni-view",{staticClass:"item-content"},[i("ns-mp-html",{attrs:{content:t.goodsItemInfo.note_content}})],1),i("v-uni-view",{staticClass:"rest-info"},[1==t.goodsItemInfo.is_show_read_num?i("v-uni-text",[t._v("阅读"),i("v-uni-text",[t._v(t._s(t.goodsItemInfo.initial_read_num+t.goodsItemInfo.read_num))])],1):t._e(),1==t.goodsItemInfo.is_show_dianzan_num?i("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.giveLike.apply(void 0,arguments)}}},[t.giveLikeIdent?i("v-uni-text",{staticClass:"iconfont icon-likefill color-base-text"}):t._e(),t.giveLikeIdent?t._e():i("v-uni-text",{staticClass:"iconfont icon-gz"}),i("v-uni-text",[t._v(t._s(t.goodsItemInfo.initial_dianzan_num+t.goodsItemInfo.dianzan_num))])],1):t._e()],1),i("v-uni-view",{staticClass:"item-action"},[i("v-uni-view",{staticClass:"action-left"},[t.giveLikeIdent?t._e():i("v-uni-text",{staticClass:"iconfont icon-dianzan",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.giveLike.apply(void 0,arguments)}}}),t.giveLikeIdent?i("v-uni-text",{staticClass:"iconfont icon-dianzan1 active color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.giveLike.apply(void 0,arguments)}}}):t._e()],1),t.goodsItemInfo.goods_list.length?i("v-uni-button",{staticClass:"color-base-bg action-right",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirectToGoods(t.goodsItemInfo)}}},[t._v("购买")]):t._e()],1)],1):"shop_said"==t.noteType?i("v-uni-view",{staticClass:"shop-said"},[i("v-uni-text",{staticClass:"said-title"},[t._v(t._s(t.shopSaidInfo.note_title))]),1==t.shopSaidInfo.is_show_release_time?i("v-uni-text",{staticClass:"said-time"},[t._v(t._s(t.$util.timeStampTurnTime(t.shopSaidInfo.create_time,"Y-m-d")))]):t._e(),i("v-uni-view",{staticClass:"said-content"},[i("ns-mp-html",{attrs:{content:t.shopSaidInfo.note_content}})],1),i("v-uni-view",{staticClass:"said-goods"},t._l(t.shopSaidInfo.goods_list,(function(e,o){return t.shopSaidInfo.goods_list?i("v-uni-view",{key:o,staticClass:"commodity-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.redirectToGoods(e.goods_id,"shop_said")}}},[i("v-uni-image",{staticClass:"commodity-img",attrs:{src:t.$util.img(e.goods_image.split(",")[0]),mode:"aspectFit"}}),i("v-uni-view",{staticClass:"commodity-content"},[i("v-uni-text",{staticClass:"commodity-name"},[t._v(t._s(e.goods_name))]),i("v-uni-text",{staticClass:"commodity-price color-base-text"},[t._v(t._s(e.price))])],1)],1):t._e()})),1),i("v-uni-view",{staticClass:"rest-info"},[1==t.shopSaidInfo.is_show_read_num?i("v-uni-text",[t._v("阅读"),i("v-uni-text",[t._v(t._s(t.shopSaidInfo.initial_read_num+t.shopSaidInfo.read_num))])],1):t._e(),1==t.shopSaidInfo.is_show_dianzan_num?i("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.giveLike.apply(void 0,arguments)}}},[t.giveLikeIdent?i("v-uni-text",{staticClass:"iconfont icon-likefill color-base-text"}):t._e(),t.giveLikeIdent?t._e():i("v-uni-text",{staticClass:"iconfont icon-gz"}),i("v-uni-text",[t._v(t._s(t.shopSaidInfo.initial_dianzan_num+t.shopSaidInfo.dianzan_num))])],1):t._e()],1),i("v-uni-view",{staticClass:"said-action"},[t.giveLikeIdent?t._e():i("v-uni-text",{staticClass:"iconfont icon-dianzan",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.giveLike.apply(void 0,arguments)}}}),t.giveLikeIdent?i("v-uni-text",{staticClass:"iconfont icon-dianzan1 color-base-text active",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.giveLike.apply(void 0,arguments)}}}):t._e()],1)],1):t._e(),i("loading-cover",{ref:"loadingCover"}),i("hover-nav"),i("ns-login",{ref:"login"})],1)],1)},a=[]},"55f5":function(t,e,i){"use strict";var o=i("b551"),n=i.n(o);n.a},"61c7":function(t,e,i){"use strict";i.r(e);var o=i("4d00"),n=i("a282");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("55f5");var s=i("828b"),d=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"027931bf",null,!1,o["a"],void 0);e["default"]=d.exports},7854:function(t,e,i){"use strict";i.r(e);var o=i("8ba8"),n=i("f48d");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);var s=i("828b"),d=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=d.exports},"7d40":function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */uni-page-body[data-v-027931bf]{background-color:#fff}body.?%PAGE?%[data-v-027931bf]{background-color:#fff}.goods-detail[data-v-027931bf]{padding:%?30?% %?24?% %?180?%}.goods-detail .said-content[data-v-027931bf],\r\n.goods-detail .item-content[data-v-027931bf]{padding:%?4?%}.goods-detail .said-content uni-rich-text[data-v-027931bf],\r\n.goods-detail .item-content uni-rich-text[data-v-027931bf]{word-wrap:break-word}.goods-detail .goods-item .item-img[data-v-027931bf]{width:100%;height:%?400?%;border-radius:%?10?%}.goods-detail .goods-item .item-title[data-v-027931bf]{display:block;margin:%?40?% 0 %?44?%;font-size:%?32?%;line-height:1}.goods-detail .goods-item .item-lightspot uni-text[data-v-027931bf]{font-size:%?24?%;padding:%?6?% %?10?%;line-height:1;border-radius:%?4?%;color:#fff}.goods-detail .goods-item .item-lightspot uni-text ~ uni-text[data-v-027931bf]{margin-left:%?10?%}.goods-detail .goods-item .item-time[data-v-027931bf]{display:block;margin:%?44?% 0 %?40?%;font-size:%?24?%;color:#b6b6b6}.goods-detail .goods-item .rest-info[data-v-027931bf]{display:flex;justify-content:space-between;align-items:center;margin-top:%?40?%;color:#6b6b6b;font-size:%?24?%}.goods-detail .goods-item .rest-info uni-text uni-text[data-v-027931bf]{margin-left:%?8?%}.goods-detail .goods-item .rest-info uni-text.iconfont[data-v-027931bf]{font-size:%?26?%}.goods-detail .goods-item .item-action[data-v-027931bf]{position:fixed;bottom:0;width:calc(100% - 24px);display:flex;margin:%?80?% 0;justify-content:space-between}.goods-detail .goods-item .item-action .action-left[data-v-027931bf]{display:flex}.goods-detail .goods-item .item-action .action-left uni-text[data-v-027931bf]{display:flex;justify-content:center;align-items:center;width:%?70?%;height:%?70?%;background-color:rgba(0,0,0,.4)!important;border-radius:50%;border:%?2?% solid transparent}.goods-detail .goods-item .item-action .action-left uni-text.active[data-v-027931bf]{margin:0;background-color:#fff!important;border:%?2?% solid #ddd}.goods-detail .goods-item .item-action .action-left uni-button[data-v-027931bf]{display:flex;justify-content:center;align-items:center;margin:0;margin-left:%?16?%;padding:0;width:%?70?%;height:%?70?%;background-color:rgba(0,0,0,.4)!important;border-radius:50%}.goods-detail .goods-item .item-action .action-left .iconfont[data-v-027931bf]{color:#fff;font-size:%?32?%}.goods-detail .goods-item .item-action .action-right[data-v-027931bf]{margin:0;width:%?180?%;height:%?70?%;color:#fff}.goods-detail .shop-said .said-title[data-v-027931bf]{display:block;margin:%?40?% 0 %?44?%;font-size:%?32?%;line-height:1}.goods-detail .shop-said .said-time[data-v-027931bf]{display:block;margin:%?44?% 0 %?40?%;font-size:%?24?%;color:#b6b6b6}.goods-detail .shop-said .rest-info[data-v-027931bf]{display:flex;justify-content:space-between;align-items:center;margin-top:%?40?%;color:#6b6b6b;font-size:%?24?%}.goods-detail .shop-said .rest-info uni-text uni-text[data-v-027931bf]{margin-left:%?8?%}.goods-detail .shop-said .rest-info uni-text.iconfont[data-v-027931bf]{font-size:%?26?%}.goods-detail .shop-said .said-action[data-v-027931bf]{position:fixed;bottom:0;width:calc(100% - 24px);display:flex;justify-content:center;margin:%?80?% 0}.goods-detail .shop-said .said-action uni-text[data-v-027931bf]{display:flex;justify-content:center;align-items:center;width:%?70?%;height:%?70?%;background-color:rgba(0,0,0,.4)!important;border-radius:50%}.goods-detail .shop-said .said-action uni-text.active[data-v-027931bf]{margin:0;background-color:#fff!important;border:%?2?% solid #ddd}.goods-detail .shop-said .said-action uni-button[data-v-027931bf]{display:flex;justify-content:center;align-items:center;margin:0;margin-left:%?16?%;padding:0;width:%?70?%;height:%?70?%;background-color:rgba(0,0,0,.4)!important;border-radius:50%}.goods-detail .shop-said .said-action .iconfont[data-v-027931bf]{color:#fff;font-size:%?32?%}.goods-detail .shop-said .said-goods[data-v-027931bf]{display:flex;flex-wrap:wrap;justify-content:space-between;margin-top:%?40?%}.goods-detail .shop-said .said-goods .commodity-item[data-v-027931bf]{display:flex;flex-direction:column;margin-bottom:%?22?%;width:%?338?%;border:%?2?% solid #f1f1f1}.goods-detail .shop-said .said-goods .commodity-item uni-image[data-v-027931bf]{width:%?338?%;height:%?338?%}.goods-detail .shop-said .said-goods .commodity-item .commodity-content[data-v-027931bf]{padding:%?20?%}.goods-detail .shop-said .said-goods .commodity-item .commodity-content .commodity-name[data-v-027931bf]{overflow:hidden;display:block;font-size:%?24?%;text-overflow:ellipsis;white-space:nowrap;color:#383838}.goods-detail .shop-said .said-goods .commodity-item .commodity-content .commodity-price[data-v-027931bf]{display:block;font-size:%?24?%}',""]),t.exports=e},8469:function(t,e,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2");o(i("0817"));var n={components:{},data:function(){return{noteId:"",noteType:"",goodsItemInfo:{},shopSaidInfo:{},giveLikeIdent:!1,giveLikeFlag:!1,shareImg:""}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.notes||(e.$util.showToast({title:"商家未开启店铺笔记",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}t.note_id?(this.noteId=t.note_id,this.getNoteDetail()):this.$util.redirectTo("/pages_tool/store_notes/note_list",{},"redirectTo")},onShow:function(){this.storeToken&&(uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member")),this.isDianzan())},onShareAppMessage:function(){var t="goods_item"==this.noteType?this.goodsItemInfo.note_title:this.shopSaidInfo.note_title,e="goods_item"==this.noteType?this.goodsItemInfo.cover_img:this.shopSaidInfo.cover_img;e=this.$util.img(e.split(",")[0]);var i=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),o=i.path;return{title:t,path:o,imageUrl:e}},methods:{getNoteDetail:function(){var t=this;this.$api.sendRequest({url:"/notes/api/notes/detail",data:{note_id:this.noteId},success:function(e){0==e.code&&e.data?(t.noteType=e.data.note_type,"goods_item"==t.noteType?(t.goodsItemInfo=e.data,t.goodsItemInfo.goods_image?t.shareImg=t.$util.img(t.goodsItemInfo.goods_image):t.shareImg=t.$util.getDefaultImage().goods,t.goodsItemInfo.goods_list.length?t.goodsItemInfo.goods_image=t.goodsItemInfo.goods_list[0].goods_image.split(",")[0]:t.goodsItemInfo.goods_image=t.$util.getDefaultImage().goods,t.goodsItemInfo.goods_highlights&&(t.goodsItemInfo.goods_highlights=t.goodsItemInfo.goods_highlights.split(","))):t.shopSaidInfo=e.data):t.$util.redirectTo("/pages_tool/store_notes/note_list",{},"redirectTo"),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},giveLike:function(){var t=this;if(this.storeToken){if(this.giveLikeFlag)return!1;this.giveLikeFlag=!0;var e=this.giveLikeIdent?"/notes/api/record/delete":"/notes/api/record/add";this.$api.sendRequest({url:e,data:{note_id:this.noteId},success:function(e){t.giveLikeFlag=!1,0==e.code&&e.data>0?("goods_item"!=t.noteType?t.shopSaidInfo.dianzan_num=t.giveLikeIdent?t.shopSaidInfo.dianzan_num-1:t.shopSaidInfo.dianzan_num+1:t.goodsItemInfo.dianzan_num=t.giveLikeIdent?t.goodsItemInfo.dianzan_num-1:t.goodsItemInfo.dianzan_num+1,t.giveLikeIdent=!t.giveLikeIdent):t.$util.showToast({title:e.message})}})}else this.$refs.login.open("/pages/index/index")},isDianzan:function(){var t=this;this.$api.sendRequest({url:"/notes/api/record/isDianzan",data:{note_id:this.noteId},success:function(e){0==e.code?t.giveLikeIdent=1==e.data:t.$util.showToast({title:e.message})}})},redirectToGoods:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=e?t:t.goods_list[0].goods_id;this.$util.redirectTo("/pages/goods/detail",{goods_id:i})},imageError:function(){this.goodsItemInfo.goods_image&&(this.goodsItemInfo.goods_image=this.$util.getDefaultImage().goods),this.$forceUpdate()}}};e.default=n},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},a282:function(t,e,i){"use strict";i.r(e);var o=i("8469"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},a725:function(t,e,i){"use strict";var o=i("ac2a"),n=i.n(o);n.a},ac2a:function(t,e,i){var o=i("f714");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("1a69ffc2",o,!0,{sourceMap:!1,shadowMode:!1})},b551:function(t,e,i){var o=i("7d40");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("b68f488e",o,!0,{sourceMap:!1,shadowMode:!1})},c1f1:function(t,e,i){"use strict";i.r(e);var o=i("fa1d"),n=i("015d");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("a725");var s=i("828b"),d=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"c1934e78",null,!1,o["a"],void 0);e["default"]=d.exports},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=n},f48d:function(t,e,i){"use strict";i.r(e);var o=i("cc1b"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},f714:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},n=[]}}]);