(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-cart"],{"06f6":function(t,i,a){"use strict";var e=a("3192"),o=a.n(e);o.a},"12a1":function(t,i,a){"use strict";a.d(i,"b",(function(){return o})),a.d(i,"c",(function(){return n})),a.d(i,"a",(function(){return e}));var e={diyIcon:a("a68f").default},o=function(){var t=this,i=t.$createElement,a=t._self._c||i;return t.tabBarList&&t.tabBarList.list?a("v-uni-view",[a("v-uni-view",{staticClass:"tab-bar",style:{backgroundColor:t.tabBarList.backgroundColor}},[a("v-uni-view",{staticClass:"tabbar-border"}),t._l(t.tabBarList.list,(function(i,e){return a("v-uni-view",{key:i.id,staticClass:"item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.redirectTo(i.link)}}},[a("v-uni-view",{staticClass:"bd"},["/pages/goods/cart"==i.link.wap_url?[1==t.tabBarList.type||2==t.tabBarList.type?a("v-uni-view",{staticClass:"icon",attrs:{animation:t.cartAnimation,id:"tabbarCart"}},[t.verify(i.link)?["img"==i.selected_icon_type?a("v-uni-image",{attrs:{src:t.$util.img(i.selectedIconPath)}}):t._e(),"icon"==i.selected_icon_type?a("diy-icon",{attrs:{icon:i.selectedIconPath,value:i.selected_style?i.selected_style:null}}):t._e()]:["img"==i.icon_type?a("v-uni-image",{attrs:{src:t.$util.img(i.iconPath)}}):t._e(),"icon"==i.icon_type?a("diy-icon",{attrs:{icon:i.iconPath,value:i.style?i.style:null}}):t._e()],t.cartNumber>0?a("v-uni-view",{staticClass:"cart-count-mark font-size-activity-tag",class:{max:"/pages/goods/cart"==i.link.wap_url&&t.cartNumber>99},style:{background:"var(--price-color)"}},[t._v(t._s(t.cartNumber>99?"99+":t.cartNumber))]):t._e()],2):t._e()]:[1==t.tabBarList.type||2==t.tabBarList.type?a("v-uni-view",{staticClass:"icon"},[t.verify(i.link)?["img"==i.selected_icon_type?a("v-uni-image",{attrs:{src:t.$util.img(i.selectedIconPath)}}):t._e(),"icon"==i.selected_icon_type?a("diy-icon",{attrs:{icon:i.selectedIconPath,value:i.selected_style?i.selected_style:null}}):t._e()]:["img"==i.icon_type?a("v-uni-image",{attrs:{src:t.$util.img(i.iconPath)}}):t._e(),"icon"==i.icon_type?a("diy-icon",{attrs:{icon:i.iconPath,value:i.style?i.style:null}}):t._e()]],2):t._e()],1!=t.tabBarList.type&&3!=t.tabBarList.type||"diy"!=t.tabBarList.theme?t._e():a("v-uni-view",{staticClass:"label",style:{color:t.verify(i.link)?t.tabBarList.textHoverColor:t.tabBarList.textColor}},[t._v(t._s(i.text))]),1!=t.tabBarList.type&&3!=t.tabBarList.type||"default"!=t.tabBarList.theme?t._e():a("v-uni-view",{staticClass:"label",style:{color:t.verify(i.link)?"var(--base-color)":"#333333"}},[t._v(t._s(i.text))])],2)],1)}))],2),a("v-uni-view",{staticClass:"tab-bar-placeholder"})],1):t._e()},n=[]},1739:function(t,i,a){var e=a("c86c");i=e(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-0c10a173] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:none;max-height:unset!important;overflow-y:hidden!important}[data-v-0c10a173] .uni-popup__wrapper{border-radius:%?20?% %?20?% 0 0}.container[data-v-0c10a173]{width:100vw;height:100vh;display:flex;flex-direction:column}.scroll-view[data-v-0c10a173]{flex:1;height:0;-webkit-transform:translateX(0);transform:translateX(0)}.cart-header[data-v-0c10a173]{padding:%?20?% 0;display:flex;align-items:center;justify-content:space-between;line-height:%?36?%;background:#f7f7f7}.cart-header .num-wrap[data-v-0c10a173]{margin-left:%?30?%;color:#666;font-size:%?26?%}.cart-header .cart-action[data-v-0c10a173]{line-height:inherit;margin-right:%?30?%;color:#666;font-size:%?26?%}.cart-header.invalid[data-v-0c10a173]{margin-left:%?30?%;margin-top:%?20?%;flex:1;line-height:inherit}.cart-wrap[data-v-0c10a173]{margin:0 %?24?% %?24?%;border-radius:%?16?%;overflow:hidden}.cart-wrap .fixed-wrap[data-v-0c10a173]{height:%?116?%}.cart-wrap .cart-goods[data-v-0c10a173]{background:#fff;box-sizing:border-box;position:relative;padding:%?30?% 0 %?30?% %?30?%}.cart-wrap .cart-goods[data-v-0c10a173]::after{content:"";position:absolute;left:%?20?%;right:%?20?%;height:%?2?%;bottom:0;background-color:#f2f2f2}.cart-wrap .cart-goods[data-v-0c10a173]:last-of-type::after{height:0}.cart-wrap .cart-goods .goods-wrap[data-v-0c10a173]{display:flex;position:relative;padding-left:%?64?%;transition:all .1s}.cart-wrap .cart-goods .goods-wrap > .iconfont[data-v-0c10a173]{font-size:%?40?%;position:absolute;top:50%;left:0;-webkit-transform:translateY(-50%);transform:translateY(-50%);transition:all .2s}.cart-wrap .cart-goods .goods-wrap > .icon-yuan_checkbox[data-v-0c10a173]{color:#ccc}.cart-wrap .cart-goods .goods-wrap.edit[data-v-0c10a173]{-webkit-transform:translateX(%?-70?%);transform:translateX(%?-70?%)}.cart-wrap .cart-goods .goods-wrap.edit > .iconfont[data-v-0c10a173]{opacity:0}.cart-wrap .cart-goods .goods-wrap .goods-img[data-v-0c10a173]{width:%?180?%;height:%?180?%}.cart-wrap .cart-goods .goods-wrap .goods-img uni-image[data-v-0c10a173]{width:100%;height:100%;border-radius:%?8?%}.cart-wrap .cart-goods .goods-wrap .goods-info[data-v-0c10a173]{flex:1;width:0;padding:0 %?30?%;display:flex;flex-direction:column;justify-content:space-between}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-name[data-v-0c10a173]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%}.cart-wrap .cart-goods .goods-wrap .goods-info .sku-wrap[data-v-0c10a173]{white-space:nowrap;text-overflow:ellipsis;overflow:hidden;width:100%}.cart-wrap .cart-goods .goods-wrap .goods-info .sku-wrap .sku[data-v-0c10a173]{line-height:1;margin:%?10?% 0 %?18?% 0;display:inline-flex;align-items:center;background:#f4f4f4;border-radius:%?8?%;padding:0 %?10?% 0 %?20?%}.cart-wrap .cart-goods .goods-wrap .goods-info .sku-wrap .sku .goods-spec[data-v-0c10a173]{color:#666;font-size:%?24?%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.cart-wrap .cart-goods .goods-wrap .goods-info .sku-wrap .sku .iconfont[data-v-0c10a173]{font-size:%?28?%;padding-left:%?10?%;color:#666}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section[data-v-0c10a173]{display:flex;justify-content:space-between;width:100%;align-items:center}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .unit[data-v-0c10a173]{font-size:%?24?%;margin-right:%?4?%}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-0c10a173]{display:flex;flex-direction:row;font-weight:700;color:var(--price-color)}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .goods-price .bottom-price[data-v-0c10a173]{width:100%;font-size:%?32?%;line-height:1;color:var(--price-color)}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section .goods-price .bottom-price uni-image[data-v-0c10a173]{width:%?56?%;height:%?22?%;margin-left:%?6?%}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section[data-v-0c10a173] .decrease{width:%?52?%;height:%?52?%;line-height:%?48?%;font-size:%?40?%;border-radius:%?10?% 0 0 %?10?%}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section[data-v-0c10a173] uni-input{height:%?52?%;line-height:%?52?%}.cart-wrap .cart-goods .goods-wrap .goods-info .goods-sub-section[data-v-0c10a173] .increase{width:%?52?%;height:%?52?%;line-height:%?48?%;font-size:%?40?%;border-radius:0 %?10?% %?10?% 0}.cart-wrap .cart-goods .item-del[data-v-0c10a173]{position:absolute;width:0;height:99%;color:#fff;right:0;top:0;display:flex;align-items:center;justify-content:center;transition:all .3s;overflow:hidden;white-space:nowrap;font-size:%?24?%}.cart-wrap .cart-goods .item-del.show[data-v-0c10a173]{width:%?90?%}.cart-wrap .cart-goods[data-v-0c10a173]:first-child{padding-top:%?30?%}.cart-wrap .cart-goods:last-child .goods-info[data-v-0c10a173]{border-bottom:none;padding-bottom:0}.cart-wrap .cart-goods .discount-wrap[data-v-0c10a173]{line-height:1.5;font-size:%?24?%;margin-top:%?20?%;display:flex}.cart-wrap .cart-goods .discount-wrap .discount-tag[data-v-0c10a173]{color:var(--base-color);line-height:1;padding:0 %?10?%;border-radius:%?10?%;border:1.5px solid var(--base-color);margin-right:%?10?%;white-space:nowrap;line-height:%?32?%;background:var(--main-color-shallow)}.cart-wrap .cart-goods .discount-wrap .interval[data-v-0c10a173]{height:%?20?%;margin:0 %?10?%;border-left:%?2?% solid #ddd;white-space:nowrap;margin-top:%?10?%}.cart-wrap .cart-goods .discount-wrap .interval[data-v-0c10a173]:last-child{display:none}.cart-wrap .cart-goods .discount-wrap .scroll-view[data-v-0c10a173]{flex:1;width:0;height:100%;white-space:nowrap}.cart-wrap .invalid-goods .invalid-mark[data-v-0c10a173]{color:#909399;padding:%?6?% %?16?%;display:inline-block;font-size:%?22?%}.invalid .cart-header[data-v-0c10a173]{padding-top:0}.invalid .cart-header + .cart-goods[data-v-0c10a173]{padding-top:%?30?%}.cart-bottom-block[data-v-0c10a173]{height:%?100?%;margin-top:%?20?%}.cart-bottom[data-v-0c10a173]{width:100vw;height:%?100?%;position:fixed;left:0;bottom:var(--tab-bar-height,0);background:#fff;overflow:hidden;display:flex;z-index:9}.cart-bottom .all-election[data-v-0c10a173]{height:%?100?%;position:relative;display:inline-block}.cart-bottom .all-election > .iconfont[data-v-0c10a173]{font-size:%?40?%;position:absolute;top:50%;left:%?30?%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.cart-bottom .all-election > .icon-yuan_checkbox[data-v-0c10a173]{color:#ccc}.cart-bottom .all-election > uni-text[data-v-0c10a173]{margin-left:%?56?%;line-height:%?100?%;padding-left:%?30?%}.cart-bottom .settlement-info[data-v-0c10a173]{flex:1;width:0;padding-right:%?10?%;display:flex;flex-direction:column;justify-content:center;text-align:right}.cart-bottom .settlement-info .money[data-v-0c10a173]{line-height:1;font-size:%?32?%}.cart-bottom .settlement-info .money .value[data-v-0c10a173]{font-weight:700;color:var(--price-color)}.cart-bottom .settlement-info .money .unit[data-v-0c10a173]{font-size:%?24?%;margin-right:%?4?%;color:var(--price-color);font-weight:700}.cart-bottom .settlement-info .detail[data-v-0c10a173]{line-height:1;font-size:%?22?%;color:#666;margin-top:%?10?%}.cart-bottom .settlement-info .detail .iconfont[data-v-0c10a173]{font-size:%?28?%;margin-left:%?6?%;transition:all .1s;display:inline-block}.cart-bottom .settlement-info .detail .iconfont.open[data-v-0c10a173]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.cart-bottom .action-btn[data-v-0c10a173]{height:%?100?%;line-height:%?100?%;border-radius:0;margin:0;display:flex;justify-content:flex-end;align-items:center;margin-right:%?30?%;white-space:nowrap}.cart-bottom .action-btn uni-button[data-v-0c10a173]{padding:0 %?30?%;height:%?70?%;line-height:%?70?%;font-size:%?28?%;font-weight:700;border-radius:%?50?%}.cart-bottom .action-btn uni-button.delete[data-v-0c10a173]{height:%?50?%;line-height:%?46?%}.cart-bottom.active[data-v-0c10a173]{bottom:calc(constant(safe-area-inset-bottom) + %?110?%)!important;bottom:calc(env(safe-area-inset-bottom) + %?110?%)!important}.cart-empty[data-v-0c10a173]{text-align:center;padding:%?140?% %?20?% %?80?% %?20?%}.cart-empty uni-image[data-v-0c10a173]{width:%?380?%}.cart-empty uni-button[data-v-0c10a173]{min-width:%?300?%;margin-top:%?100?%;height:%?70?%;line-height:%?70?%!important;font-size:%?28?%;border-radius:%?50?%}.cart-empty uni-button.visit-the[data-v-0c10a173]{font-weight:700}.popup[data-v-0c10a173]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-0c10a173]{display:flex;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-0c10a173]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-0c10a173]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.discount-popup .popup-body[data-v-0c10a173]{min-height:%?300?%}.discount-popup .detail-item[data-v-0c10a173]{height:%?60?%;display:flex;align-items:center;justify-content:space-between;padding:0 %?30?%}.discount-popup .detail-item .money[data-v-0c10a173]{font-weight:700}.discount-popup .detail-item .reduce[data-v-0c10a173]{color:var(--price-color)}.discount-popup .total[data-v-0c10a173]{margin-top:%?20?%}.discount-popup .total .title[data-v-0c10a173]{font-size:%?36?%;font-weight:700}.coupon-use-tips[data-v-0c10a173]{padding:%?30?%;line-height:1;background:#fff;border-bottom:%?2?% solid #eee;display:flex;justify-content:space-between;align-items:center}.coupon-use-tips uni-view[data-v-0c10a173]{line-height:1}.coupon-use-tips .iconfont[data-v-0c10a173]{font-size:%?28?%;margin-left:%?4?%}.coupon-use-tips .title[data-v-0c10a173]{font-size:%?28?%;font-weight:600}.coupon-use-tips .desc[data-v-0c10a173]{font-size:%?26?%;margin-left:%?24?%}.coupon-item[data-v-0c10a173]{margin:%?20?% %?30?%;border-radius:%?4?%;padding:0;position:relative;background-color:#fff2f0}.coupon-item[data-v-0c10a173]:before, .coupon-item[data-v-0c10a173]:after{position:absolute;content:"";background-color:#fff;top:50%;width:%?30?%;height:%?30?%;border-radius:50%;z-index:5}.coupon-item[data-v-0c10a173]:before{left:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.coupon-item[data-v-0c10a173]:after{right:0;-webkit-transform:translate(50%,-50%);transform:translate(50%,-50%)}.coupon-item .coupon-info[data-v-0c10a173]{height:%?190?%;display:flex;width:100%;position:relative}.coupon-item .coupon-info .info-wrap[data-v-0c10a173]{width:%?220?%;height:%?190?%;display:flex;justify-content:center;align-items:center;margin-right:%?20?%;background-repeat:no-repeat;background-size:100% 100%;background:linear-gradient(270deg,var(--bg-color),var(--bg-color-shallow));position:relative}.coupon-item .coupon-info .info-wrap .coupon-line[data-v-0c10a173]{position:absolute;right:0;top:0;height:100%}.coupon-item .coupon-info .info-wrap .coupon-money[data-v-0c10a173]{color:#fff;text-align:center;line-height:1}.coupon-item .coupon-info .info-wrap .coupon-money .unit[data-v-0c10a173]{font-size:%?30?%}.coupon-item .coupon-info .info-wrap .coupon-money .money[data-v-0c10a173]{font-size:%?60?%}.coupon-item .coupon-info .info-wrap .at-least[data-v-0c10a173]{font-size:%?24?%;color:#fff;text-align:center;margin-top:%?20?%}.coupon-item .coupon-info .desc-wrap[data-v-0c10a173]{flex:1;max-width:calc(100% - %?400?%)}.coupon-item .coupon-info .desc-wrap uni-view[data-v-0c10a173]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.coupon-item .coupon-info .desc-wrap .coupon-name[data-v-0c10a173]{margin-top:%?10?%;margin-bottom:%?4?%;font-size:%?28?%}.coupon-item .coupon-info .desc-wrap .limit[data-v-0c10a173]{font-size:%?20?%}.coupon-item .coupon-info .desc-wrap .time[data-v-0c10a173]{border-top:%?2?% dashed #ccc;position:absolute;bottom:%?30?%;color:#909399;padding-top:%?10?%;line-height:1.5;font-size:%?20?%}.coupon-item .coupon-info uni-button[data-v-0c10a173]{font-size:%?24?%;position:absolute;top:50%;right:%?20?%;-webkit-transform:translateY(-50%);transform:translateY(-50%);margin:0;height:%?50?%;line-height:%?50?%;width:%?100?%;padding:0}.coupon-item .coupon-info uni-button[disabled][data-v-0c10a173]{background-color:#dedede!important}.coupon-item.disabled[data-v-0c10a173]{background-color:#f2f2f2}.coupon-item.disabled .coupon-money[data-v-0c10a173]{color:#909399!important}.coupon-item.disabled .at-least[data-v-0c10a173]{color:#909399!important}.uni-popup-discount[data-v-0c10a173]{z-index:5}.store-wrap[data-v-0c10a173]{display:flex;align-items:center;background-color:#fff;padding:%?26?% %?30?% 0;font-weight:700;line-height:1}.store-wrap .name[data-v-0c10a173]{font-size:%?28?%;margin-left:%?10?%}',""]),t.exports=i},2532:function(t,i,a){"use strict";a.r(i);var e=a("12a1"),o=a("e926");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(i,t,(function(){return o[t]}))}(n);a("7d44");var s=a("828b"),c=Object(s["a"])(o["default"],e["b"],e["c"],!1,null,"09d90d92",null,!1,e["a"],void 0);i["default"]=c.exports},"2ff5":function(t,i,a){"use strict";a.r(i);var e=a("5aa7"),o=a.n(e);for(var n in e)["default"].indexOf(n)<0&&function(t){a.d(i,t,(function(){return e[t]}))}(n);i["default"]=o.a},"30db":function(t,i,a){"use strict";a.d(i,"b",(function(){return e})),a.d(i,"c",(function(){return o})),a.d(i,"a",(function(){}));var e=function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("v-uni-image",{staticClass:"mescroll-totop",class:[t.value?"mescroll-totop-in":"mescroll-totop-out"],attrs:{src:t.$util.img("public/uniapp/common/mescroll-totop.png"),mode:"widthFix"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toTopClick.apply(void 0,arguments)}}})},o=[]},3192:function(t,i,a){var e=a("1739");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var o=a("967d").default;o("21903f05",e,!0,{sourceMap:!1,shadowMode:!1})},"3c76":function(t,i,a){var e=a("89ae");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var o=a("967d").default;o("79af6a8a",e,!0,{sourceMap:!1,shadowMode:!1})},"3d39":function(t,i,a){"use strict";a.r(i);var e=a("6c84"),o=a("2ff5");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(i,t,(function(){return o[t]}))}(n);a("06f6");var s=a("828b"),c=Object(s["a"])(o["default"],e["b"],e["c"],!1,null,"0c10a173",null,!1,e["a"],void 0);i["default"]=c.exports},4380:function(t,i,a){"use strict";var e=a("3c76"),o=a.n(e);o.a},"499c":function(t,i,a){"use strict";a.r(i);var e=a("f7ad"),o=a("9b45");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(i,t,(function(){return o[t]}))}(n);a("97dc");var s=a("828b"),c=Object(s["a"])(o["default"],e["b"],e["c"],!1,null,"04ee6604",null,!1,e["a"],void 0);i["default"]=c.exports},"5aa7":function(t,i,a){"use strict";a("6a54");var e=a("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o=e(a("7254")),n=e(a("499c")),s=e(a("8f75")),c=e(a("e78f")),r=e(a("c1f3")),l={components:{nsGoodsRecommend:o.default,uniNumberBox:n.default,toTop:s.default},mixins:[c.default,r.default]};i.default=l},"64a3":function(t,i,a){"use strict";a("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,a("5c47"),a("5ef2");var e={name:"diy-bottom-nav",props:{value:{type:Object},name:{type:String,default:""}},data:function(){return{currentRoute:"",jumpFlag:!0,cartAnimation:{}}},mounted:function(){var t=this,i=getCurrentPages()[getCurrentPages().length-1];i&&i.route&&(this.currentRoute=i.route),this.$nextTick((function(){if(!t.$store.state.cartPosition){var i=uni.createSelectorQuery().in(t);i.select("#tabbarCart").boundingClientRect((function(i){i&&t.$store.commit("setCartPosition",i)})).exec(),i.select(".tab-bar").boundingClientRect((function(i){i&&t.$store.commit("setTabBarHeight",i.height+"px")})).exec()}}))},computed:{cartChange:function(){return this.$store.state.cartChange}},watch:{cartChange:function(t,i){var a=this;if(t>i){var e=uni.createAnimation({duration:200,timingFunction:"ease"});e.scale(1.2).step(),this.cartAnimation=e.export(),setTimeout((function(){e.scale(1).step(),a.cartAnimation=e.export()}),300)}}},methods:{redirectTo:function(t){this.$emit("callback"),this.$util.diyRedirectTo(t)},verify:function(t){if(null==t||""==t||!t.wap_url)return!1;if(this.name)var i=this.currentRoute+"?name="+this.name;else i=this.currentRoute;return"/pages/index/index"==t.wap_url&&"DIY_VIEW_INDEX"==this.name||!(!i||-1==t.wap_url.indexOf(i))}}};i.default=e},"6c84":function(t,i,a){"use strict";a.d(i,"b",(function(){return o})),a.d(i,"c",(function(){return n})),a.d(i,"a",(function(){return e}));var e={pageMeta:a("7854").default,uniNumberBox:a("499c").default,nsEmpty:a("52a6").default,nsGoodsRecommend:a("7254").default,uniPopup:a("d745").default,diyBottomNav:a("2532").default,nsGoodsSku:a("132d").default,loadingCover:a("c003").default,nsLogin:a("2910").default},o=function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-view",{staticStyle:{height:"100vh",width:"100vw",overflow:"hidden"}},[a("v-uni-view",{staticClass:"container"},[a("v-uni-scroll-view",{staticClass:"scroll-view",attrs:{"scroll-y":!0,"show-scrollbar":!1,"refresher-enabled":!0,"refresher-triggered":t.refresherTriggered},on:{refresherrefresh:function(i){arguments[0]=i=t.$handleEvent(i),t.onRefresh.apply(void 0,arguments)}}},[t.hasData?[t.cartData.length?a("v-uni-view",{staticClass:"cart-header"},[a("v-uni-view",{staticClass:"num-wrap"},[t._v("共"+t._s(t.cartData[0].cartList.length)+"种商品")]),a("v-uni-view",{staticClass:"cart-action",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changeAction.apply(void 0,arguments)}}},[t._v(t._s(t.isAction?t.$lang("complete"):t.$lang("edit")))])],1):t._e(),t._l(t.cartData,(function(i,e){return a("v-uni-view",{key:e,staticClass:"cart-wrap"},[t.discount.coupon_info?a("v-uni-view",{staticClass:"coupon-use-tips"},[a("v-uni-view",[a("v-uni-text",{staticClass:"title color-base-text"},[t._v("优惠券")]),a("v-uni-text",{staticClass:"desc"},[t._v("领券结算最高可减"+t._s(t._f("moneyFormat")(t.discount.coupon_info.coupon_money))+"元")])],1),a("v-uni-view",{staticClass:"color-base-text",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$refs.couponPopup.open()}}},[t._v("点击"+t._s("wait"==t.discount.coupon_info.receive_type?"领取":"查看")),a("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1):t._e(),t.globalStoreConfig&&"store"==t.globalStoreConfig.store_business&&t.globalStoreInfo?a("v-uni-view",{staticClass:"store-wrap"},[a("v-uni-text",{staticClass:"iconfont icon-dianpu"}),a("v-uni-text",{staticClass:"name"},[t._v(t._s(t.globalStoreInfo.store_name))])],1):t._e(),t._l(i.cartList,(function(i,o){return[a("v-uni-view",{key:i.cart_id+"_0",staticClass:"cart-goods",on:{touchstart:function(i){arguments[0]=i=t.$handleEvent(i),t.touchS(i)},touchend:function(a){arguments[0]=a=t.$handleEvent(a),t.touchE(a,i)}}},[a("v-uni-view",{staticClass:"goods-wrap",class:{edit:i.edit}},[a("v-uni-view",{staticClass:"iconfont",class:i.checked?"icon-yuan_checked color-base-text":"icon-yuan_checkbox",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.singleElection(e,o)}}}),a("v-uni-view",{staticClass:"goods-img",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.toGoodsDetail(i)}}},[a("v-uni-image",{attrs:{src:t.$util.img(i.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.imageError(e,o)}}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-view",[a("v-uni-view",{staticClass:"goods-name",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.toGoodsDetail(i)}}},[t._v(t._s(i.goods_name))]),a("v-uni-view",{staticClass:"sku-wrap"},[a("v-uni-view",{staticClass:"sku"},[i.sku_spec_format.length?a("v-uni-view",{staticClass:"goods-spec",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectSku(i)}}},[t._l(i.sku_spec_format,(function(a,e){return[t._v(t._s(a.spec_name)+":"+t._s(a.spec_value_name)+"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t"+t._s(e<i.sku_spec_format.length-1?";":""))]}))],2):t._e(),i.sku_spec_format.length?a("v-uni-text",{staticClass:"iconfont icon-unfold"}):t._e()],1)],1)],1),a("v-uni-view",{staticClass:"goods-sub-section"},[1==i.promotion_type?[Number(i.member_price)>0&&Number(i.member_price)<Number(i.discount_price)?[a("v-uni-view",{staticClass:"goods-price "},[a("v-uni-view",{staticClass:"bottom-price price-style large"},[a("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(parseFloat(i.member_price).toFixed(2).split(".")[0])),a("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(i.member_price).toFixed(2).split(".")[1]))]),a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/VIP.png")}})],1)],1)]:[a("v-uni-view",{staticClass:"goods-price "},[a("v-uni-view",{staticClass:"bottom-price price-style large"},[a("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(parseFloat(i.discount_price).toFixed(2).split(".")[0])),a("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(i.discount_price).toFixed(2).split(".")[1]))]),a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/discount.png")}})],1)],1)]]:[Number(i.member_price)>0?[a("v-uni-view",{staticClass:"goods-price"},[a("v-uni-view",{staticClass:"bottom-price price-style large"},[a("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(parseFloat(i.member_price).toFixed(2).split(".")[0])),a("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(i.member_price).toFixed(2).split(".")[1]))]),a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/VIP.png")}})],1)],1)]:[a("v-uni-view",{staticClass:"goods-price"},[a("v-uni-view",{staticClass:"bottom-price price-style large"},[a("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(parseFloat(i.price).toFixed(2).split(".")[0])),a("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(i.price).toFixed(2).split(".")[1]))])],1)],1)]],a("uni-number-box",{attrs:{min:i.min_buy>1?i.min_buy:1,max:i.max_buy>0&&i.max_buy<i.stock?i.max_buy:i.stock,size:"small",value:t.initNum(i)},on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.cartNumChange(i,{siteIndex:e,cartIndex:o})},limit:function(i){arguments[0]=i=t.$handleEvent(i),t.goodsLimit(i,{siteIndex:e,cartIndex:o})}}})],2),t.manjian&&t.manjian["sku_"+i.sku_id]?a("v-uni-view",{staticClass:"discount-wrap"},[a("v-uni-text",{staticClass:"discount-tag"},[t._v("满减")]),a("v-uni-scroll-view",{staticClass:"scroll-view",attrs:{"scroll-x":"true"}},[t._l(t.manjian["sku_"+i.sku_id],(function(i,e){return[i.discount_money?a("v-uni-text",[t._v(t._s(Number(i.limit))+"减"+t._s(i.discount_money))]):t._e(),i.discount_money?a("v-uni-text",{key:e+"_1",staticClass:"interval"}):t._e()]}))],2)],1):t._e()],1)],1),a("v-uni-view",{staticClass:"item-del color-base-bg",class:{show:i.edit},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.deleteCart("single",e,o)}}},[t._v(t._s(t.$lang("del")))])],1)]}))],2)})),t.invalidGoods.length?a("v-uni-view",{staticClass:"cart-wrap invalid"},[a("v-uni-view",{staticClass:"cart-header"},[a("v-uni-view",{staticClass:"num-wrap"},[a("v-uni-text",{staticClass:"font-size-base"},[t._v("失效商品"+t._s(t.invalidGoods.length)+"件")])],1),a("v-uni-view",{staticClass:"cart-action color-base-text",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.clearInvalidGoods.apply(void 0,arguments)}}},[t._v("清空")])],1),t._l(t.invalidGoods,(function(i,e){return[a("v-uni-view",{key:e+"_0",staticClass:"cart-goods invalid-goods"},[a("v-uni-view",{staticClass:"goods-wrap"},[a("v-uni-view",{staticClass:"iconfont icon-yuan_checked color-tip"}),a("v-uni-view",{staticClass:"goods-img"},[a("v-uni-image",{attrs:{src:t.$util.img(i.sku_image,{size:"mid"}),mode:"aspectFill"}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(i.sku_name))]),a("v-uni-view",[a("v-uni-view",{staticClass:"sku"},[i.sku_spec_format.length?a("v-uni-view",{staticClass:"goods-spec"},[t._l(i.sku_spec_format,(function(a,e){return[t._v(t._s(a.spec_name)+":"+t._s(a.spec_value_name)+"\n\t\t\t\t\t\t\t\t\t\t\t\t\t"+t._s(e<i.sku_spec_format.length-1?"; ":""))]}))],2):t._e()],1)],1),a("v-uni-view",{staticClass:"goods-sub-section"},[a("v-uni-view",{staticClass:"goods-price"},[a("v-uni-text",{staticClass:"bottom-price price-style large"},[i.member_price>0&&i.member_price<i.discount_price?[a("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(parseFloat(i.member_price).toFixed(2).split(".")[0])),a("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(i.member_price).toFixed(2).split(".")[1]))]),a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/VIP.png")}})]:[a("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(parseFloat(i.discount_price).toFixed(2).split(".")[0])),a("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(i.discount_price).toFixed(2).split(".")[1]))])]],2)],1),a("v-uni-text",{staticClass:"invalid-mark"},[t._v("已失效")])],1)],1)],1)],1)]}))],2):t._e()]:[a("v-uni-view",{staticClass:"cart-empty"},[a("ns-empty",{attrs:{text:"购物车为空",subText:"赶紧去逛逛, 购买心仪的商品吧",isIndex:Boolean(t.storeToken)}}),t.storeToken?t._e():a("v-uni-button",{staticClass:"button mini",attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toLogin.apply(void 0,arguments)}}},[t._v("去登录")])],1)],a("ns-goods-recommend",{ref:"goodrecommend",attrs:{route:"cart"}}),a("v-uni-view",{staticClass:"cart-bottom-block"}),a("v-uni-view",{staticClass:"page-bottom",style:{height:t.tabBarHeight}})],2),a("uni-popup",{ref:"discountPopup",attrs:{"custom-class":"uni-popup-discount",type:"bottom"}},[Object.keys(t.discount).length?a("v-uni-view",{staticClass:"discount-popup popup"},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[t._v("优惠明细")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toggleDiscountPopup.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"popup-body",class:{"safe-area":t.isIphoneX},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toggleDiscountPopup.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"detail-item"},[a("v-uni-view",{staticClass:"title"},[t._v("商品总额")]),a("v-uni-view",{staticClass:"money price-font"},[t._v("￥"+t._s(t._f("moneyFormat")(t.discount.goods_money)))])],1),t.discount.coupon_money>0?a("v-uni-view",{staticClass:"detail-item"},[a("v-uni-view",{staticClass:"title"},[t._v("优惠券")]),a("v-uni-view",{staticClass:"money price-font reduce"},[t._v("-￥"+t._s(t._f("moneyFormat")(t.discount.coupon_money)))])],1):t._e(),t.discount.promotion_money>0?a("v-uni-view",{staticClass:"detail-item"},[a("v-uni-view",{staticClass:"title"},[t._v("满减")]),a("v-uni-view",{staticClass:"money reduce price-font"},[t._v("-￥"+t._s(t._f("moneyFormat")(t.discount.promotion_money)))])],1):t._e(),a("v-uni-view",{staticClass:"detail-item total"},[a("v-uni-view",{staticClass:"title"},[t._v("合计")]),a("v-uni-view",{staticClass:"money price-font "},[t._v("￥"+t._s(t._f("moneyFormat")(t.discount.order_money)))])],1)],1),a("v-uni-view",{style:{height:t.tabBarHeight}})],1):t._e()],1),t.discount.coupon_info?a("uni-popup",{ref:"couponPopup",attrs:{"custom-class":"uni-popup-discount",type:"bottom"}},[a("v-uni-view",{staticClass:"coupon-popup popup"},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[t._v("优惠券")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$refs.couponPopup.close()}}})],1),a("v-uni-view",{staticClass:"popup-body",class:{"safe-area":t.isIphoneX},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$refs.couponPopup.close()}}},[a("v-uni-view",{staticClass:"coupon-item"},[a("v-uni-view",{staticClass:"coupon-info",style:{backgroundColor:"wait"!=t.discount.coupon_info.receive_type?"#F2F2F2":"var(--main-color-shallow)"}},[a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-image",{staticClass:"coupon-line",attrs:{mode:"heightFix",src:t.$util.img("public/uniapp/coupon/coupon_line.png")}}),a("v-uni-view",{staticClass:"coupon-money"},["reward"==t.discount.coupon_info.type?[a("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money"},[t._v(t._s(parseFloat(t.discount.coupon_info.money)))])]:"discount"==t.discount.coupon_info.type?[a("v-uni-text",{staticClass:"money"},[t._v(t._s(parseFloat(t.discount.coupon_info.discount)))]),a("v-uni-text",{staticClass:"unit"},[t._v("折")])]:t._e(),a("v-uni-view",{staticClass:"at-least"},[t.discount.coupon_info.at_least>0?[t._v("满"+t._s(t.discount.coupon_info.at_least)+"可用")]:[t._v("无门槛")]],2)],2)],1),a("v-uni-view",{staticClass:"desc-wrap"},[a("v-uni-view",{staticClass:"coupon-name"},[t._v(t._s(t.discount.coupon_info.coupon_name))]),"discount"==t.discount.coupon_info.type&&t.discount.coupon_info.discount_limit>0?a("v-uni-view",{staticClass:"limit"},[t._v("最多可抵￥"+t._s(t.discount.coupon_info.discount_limit))]):t._e(),0==t.discount.coupon_info.validity_type?a("v-uni-view",{staticClass:"time font-size-goods-tag"},[t._v("有效期："+t._s(t.$util.timeStampTurnTime(t.discount.coupon_info.end_time)))]):1==t.discount.coupon_info.validity_type?a("v-uni-view",{staticClass:"time font-size-goods-tag"},[t._v("有效期：领取之日起"+t._s(t.discount.coupon_info.fixed_term)+"天内有效")]):a("v-uni-view",{staticClass:"time font-size-goods-tag"},[t._v("有效期：长期有效")])],1),"wait"!=t.discount.coupon_info.receive_type?a("v-uni-button",{attrs:{type:"primary",disabled:!0}},[t._v("已领取")]):a("v-uni-button",{attrs:{type:"primary"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.receiveCoupon(t.discount.coupon_info.coupon_type_id)}}},[t._v("领取")])],1)],1)],1),a("v-uni-view",{style:{height:t.tabBarHeight}}),a("v-uni-view",{staticClass:"cart-bottom-block"})],1)],1):t._e()],1),t.hasData?a("v-uni-view",{staticClass:"cart-bottom",class:{active:t.isIphoneX},style:{bottom:t.tabBarHeight}},[a("v-uni-view",{staticClass:"all-election",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.allElection.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"iconfont",class:t.checkAll?"icon-yuan_checked color-base-text":"icon-yuan_checkbox"}),a("v-uni-text",[t._v(t._s(t.$lang("allElection")))])],1),a("v-uni-view",{staticClass:"settlement-info",style:{visibility:t.isAction?"hidden":"visible"}},[a("v-uni-view",{staticClass:"money"},[t._v(t._s(t.$lang("total"))+"："),a("v-uni-text",{staticClass:"unit price-font"},[t._v(t._s(t.$lang("common.currencySymbol")))]),Object.keys(t.discount).length?[a("v-uni-text",{staticClass:"value price-font"},[t._v(t._s(parseFloat(t.discount.order_money).toFixed(2).split(".")[0]))]),a("v-uni-text",{staticClass:"unit price-font"},[t._v("."+t._s(parseFloat(t.discount.order_money).toFixed(2).split(".")[1]))])]:[a("v-uni-text",{staticClass:"value price-font"},[t._v(t._s(parseFloat(t.totalPrice).toFixed(2).split(".")[0]))]),a("v-uni-text",{staticClass:"unit price-font"},[t._v("."+t._s(parseFloat(t.totalPrice).toFixed(2).split(".")[1]))])]],2),Object.keys(t.discount).length?a("v-uni-view",{staticClass:"detail",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toggleDiscountPopup.apply(void 0,arguments)}}},[t._v("优惠明细"),a("v-uni-text",{staticClass:"iconfont icon-unfold",class:{open:!t.discountPopupShow}})],1):t._e()],1),t.isAction?a("v-uni-view",{staticClass:"action-btn"},[a("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.deleteCart("all")}}},[t._v(t._s(t.$lang("del")))])],1):a("v-uni-view",{staticClass:"action-btn"},[0!=t.totalCount?a("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.settlement.apply(void 0,arguments)}}},[t._v(t._s(t.discount.coupon_info&&"wait"==t.discount.coupon_info.receive_type?"领券":"立即")+"结算("+t._s(t.totalCount)+")")]):a("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini",disabled:!0},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.settlement.apply(void 0,arguments)}}},[t._v(t._s(t.$lang("settlement"))+"("+t._s(t.totalCount)+")")])],1)],1):t._e(),a("diy-bottom-nav"),t.goodsSkuDetail?a("ns-goods-sku",{ref:"selectSku",attrs:{"goods-detail":t.goodsSkuDetail,"goods-id":t.goodsSkuDetail.goods_id,"max-buy":t.goodsSkuDetail.max_buy,"min-buy":t.goodsSkuDetail.min_buy},on:{refresh:function(i){arguments[0]=i=t.$handleEvent(i),t.refreshSkuDetail.apply(void 0,arguments)}}}):t._e(),a("loading-cover",{ref:"loadingCover"}),t.showTop?a("to-top",{on:{toTop:function(i){arguments[0]=i=t.$handleEvent(i),t.scrollToTopNative()}}}):t._e(),a("ns-login",{ref:"login"})],1)],1)},n=[]},"74b8":function(t,i,a){"use strict";a("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,a("64aa");var e={name:"UniNumberBox",props:{value:{type:[Number,String],default:1},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},disabled:{type:Boolean,default:!1},inputDisabled:{type:Boolean,default:!1},size:{type:String,default:"default"}},data:function(){return{inputValue:0,initialValue:0,load:!0}},watch:{value:function(t){this.inputValue=+t}},created:function(){this.initialValue=+this.value,this.inputValue=+this.value},methods:{_calcValue:function(t){if(!this.disabled){var i=this._getDecimalScale(),a=this.inputValue*i,e=this.step*i;"minus"===t?a-=e:"plus"===t&&(a+=e),a<this.min&&"minus"===t||a>this.max&&"plus"===t?this.$emit("limit",{value:this.inputValue,type:t}):(this.inputValue=a/i,this.$emit("change",this.inputValue))}},_getDecimalScale:function(){var t=1;return~~this.step!==this.step&&(t=Math.pow(10,(this.step+"").split(".")[1].length)),t},_onInput:function(t){var i=this;setTimeout((function(){var a=t.detail.value;a=+a,a>i.max?(a=i.max,i.$util.showToast({title:"商品库存不足"})):a<i.min&&(i.$util.showToast({title:"商品最少购买"+i.min+"件"}),a=i.min),a||(a=1),i.inputValue=a,i.$forceUpdate(),i.$emit("change",a)}),0)}}};i.default=e},"78c0":function(t,i,a){"use strict";a("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;i.default={data:function(){return{value:!0}},methods:{toTopClick:function(){this.$emit("toTop")}}}},"7d44":function(t,i,a){"use strict";var e=a("d1d0"),o=a.n(e);o.a},"89ae":function(t,i,a){var e=a("c86c");i=e(!1),i.push([t.i,"\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n/* 回到顶部的按钮 */.mescroll-totop[data-v-6bcd6eba]{z-index:99;position:fixed!important; /* 加上important避免编译到H5,在多mescroll中定位失效 */right:%?46?%!important;bottom:%?272?%!important;width:%?72?%;height:%?72?%;border-radius:50%;opacity:0;transition:opacity .5s; /* 过渡 */margin-bottom:var(--window-bottom) /* css变量 */}\r\n/* 适配 iPhoneX */.mescroll-safe-bottom[data-v-6bcd6eba]{margin-bottom:calc(var(--window-bottom) + constant(safe-area-inset-bottom)); /* window-bottom + 适配 iPhoneX */margin-bottom:calc(var(--window-bottom) + env(safe-area-inset-bottom))}\r\n/* 显示 -- 淡入 */.mescroll-totop-in[data-v-6bcd6eba]{opacity:1}\r\n/* 隐藏 -- 淡出且不接收事件*/.mescroll-totop-out[data-v-6bcd6eba]{opacity:0;pointer-events:none}",""]),t.exports=i},"8f75":function(t,i,a){"use strict";a.r(i);var e=a("30db"),o=a("edc3");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(i,t,(function(){return o[t]}))}(n);a("4380");var s=a("828b"),c=Object(s["a"])(o["default"],e["b"],e["c"],!1,null,"6bcd6eba",null,!1,e["a"],void 0);i["default"]=c.exports},"97dc":function(t,i,a){"use strict";var e=a("f964"),o=a.n(e);o.a},"9a0a":function(t,i,a){var e=a("c86c");i=e(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-numbox[data-v-04ee6604]{display:inline-flex;flex-direction:row;justify-content:flex-start;align-items:center;height:%?70?%;position:relative}.uni-numbox.small[data-v-04ee6604]{height:%?44?%}.uni-numbox[data-v-04ee6604]:after{content:"";position:absolute;-webkit-transform-origin:center;transform-origin:center;box-sizing:border-box;pointer-events:none;top:-50%;left:-50%;right:-50%;bottom:-50%;border-radius:%?12?%;-webkit-transform:scale(.5);transform:scale(.5)}.uni-numbox__minus[data-v-04ee6604],\r\n.uni-numbox__plus[data-v-04ee6604]{width:%?40?%;height:%?40?%;border-radius:50%;background-size:100% 100%;background-position:50%}.uni-numbox__value[data-v-04ee6604]{position:relative;background-color:#f8f8f8;width:%?80?%;height:%?40?%;text-align:center;border:1px solid #eee;display:inline-block;line-height:%?36?%;font-weight:700;margin:0;padding:0;vertical-align:top;min-height:0;border-left:none;border-right:none}.uni-numbox__value.small[data-v-04ee6604]{width:%?60?%;font-size:%?24?%}.uni-numbox__value[data-v-04ee6604]:after{content:"";position:absolute;-webkit-transform-origin:center;transform-origin:center;box-sizing:border-box;pointer-events:none;top:-50%;left:-50%;right:-50%;bottom:-50%;border-top-width:0;border-bottom-width:0;-webkit-transform:scale(.5);transform:scale(.5)}.uni-numbox--disabled[data-v-04ee6604]{color:silver}.uni-numbox uni-button[data-v-04ee6604]{width:%?40?%;height:%?40?%;display:inline-block;box-sizing:initial;border:1px solid #eee;padding:0;margin:0;border-radius:0;background-color:#fff;font-weight:700}.uni-numbox uni-button.disabled[data-v-04ee6604]{color:#eee;background-color:#f8f8f8!important}.uni-numbox uni-button.decrease[data-v-04ee6604]{font-size:%?44?%;line-height:%?32?%}.uni-numbox uni-button.increase[data-v-04ee6604]{font-size:%?32?%;line-height:%?36?%}',""]),t.exports=i},"9b45":function(t,i,a){"use strict";a.r(i);var e=a("74b8"),o=a.n(e);for(var n in e)["default"].indexOf(n)<0&&function(t){a.d(i,t,(function(){return e[t]}))}(n);i["default"]=o.a},c1f3:function(t,i,a){"use strict";a("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,a("bf0f"),a("2797"),a("aa9c"),a("dc8a"),a("e966"),a("64aa"),a("c9b5"),a("ab80"),a("dd2b"),a("7a76"),a("d4b5"),a("e838");var e={data:function(){return{cartData:[],editLock:!1,checkAll:!0,totalPrice:"0.00",totalCount:0,isSub:!1,invalidGoods:[],isIphoneX:!1,isAction:!1,goodsSkuDetail:null,discount:{},manjian:{},receiveSub:!1,discountPopupShow:!1,startX:"",endX:"",refresherTriggered:!1,timeout:{},navbarData:{title:"购物车",topNavColor:"#ffffff",topNavBg:!1,navBarSwitch:!0,textNavColor:"#333333",moreLink:{name:""},navStyle:1,textImgPosLink:"center"}}},onLoad:function(){uni.hideTabBar(),this.isIphoneX=this.$util.uniappIsIPhoneX()},onShow:function(){this.storeToken?(this.getCartData(),this.$store.dispatch("getCartNumber")):(this.cartData=[],this.invalidGoods=[],this.calculationTotalPrice())},onHide:function(){this.isAction=!1},onUnload:function(){!this.storeToken&&this.$refs.login&&this.$refs.login.cancelCompleteInfo()},onReady:function(){this.storeToken||this.$refs.loadingCover&&this.$refs.loadingCover.hide()},computed:{hasData:function(){return this.cartData.length>0||this.invalidGoods.length>0}},methods:{initNum:function(t){return t.num},getCartData:function(){var t=this;this.$api.sendRequest({url:"/api/cart/goodslists",success:function(i){i.code>=0&&(i.data.length?t.handleCartData(i.data):t.cartData=[]),t.refresherTriggered=!1,t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(i){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},handleCartData:function(t){var i=this;this.invalidGoods=[],this.cartData=[];var a={};t.forEach((function(t,e){1==t.goods_state?void 0!=t.store_goods_status&&1!=t.store_goods_status||t.min_buy>0&&t.min_buy>t.stock?i.invalidGoods.push(t):t.stock?(t.checked=!0,t.edit=!1,void 0!=a["site_"+t.site_id]?a["site_"+t.site_id].cartList.push(t):a["site_"+t.site_id]={siteId:t.site_id,siteName:t.site_name,edit:!1,checked:!0,cartList:[t]}):i.invalidGoods.push(t):i.invalidGoods.push(t)})),this.cartData=[],Object.keys(a).forEach((function(t){i.cartData.push(a[t])})),this.calculationTotalPrice(),this.cartData.length&&this.cartData[0].cartList.forEach((function(t){t.sku_spec_format?t.sku_spec_format=JSON.parse(t.sku_spec_format):t.sku_spec_format=[]})),this.invalidGoods.length&&this.invalidGoods.forEach((function(t){t.sku_spec_format?t.sku_spec_format=JSON.parse(t.sku_spec_format):t.sku_spec_format=[]}))},singleElection:function(t,i){this.cartData[t].cartList[i].checked=!this.cartData[t].cartList[i].checked,this.calculationTotalPrice()},siteAllElection:function(t,i){this.cartData[i].checked=t,this.cartData[i].cartList.forEach((function(i){i.checked=t})),this.calculationTotalPrice()},allElection:function(t){var i=this;this.checkAll="boolean"==typeof t?t:!this.checkAll,this.cartData.length&&this.cartData.forEach((function(t){t.checked=i.checkAll,t.cartList.forEach((function(t){t.checked=i.checkAll}))})),this.calculationTotalPrice()},calculationTotalPrice:function(){if(this.cartData.length){var t=0,i=0,a=0;this.cartData.forEach((function(e){var o=0;e.cartList.forEach((function(a){a.checked&&(o+=1,i+=parseInt(a.num),Number(a.member_price)>0&&Number(a.member_price)<Number(a.discount_price)?t+=a.member_price*a.num:t+=a.discount_price*a.num)})),e.cartList.length==o?(e.checked=!0,a+=1):e.checked=!1})),this.totalPrice=t.toFixed(2),this.totalCount=i,this.checkAll=this.cartData.length==a}else this.totalPrice="0.00",this.totalCount=0;this.discountCalculate()},deleteCart:function(t,i,a){var e=this,o=[];if("all"==t)for(var n=0;n<this.cartData.length;n++)for(var s=0;s<this.cartData[n].cartList.length;s++)this.cartData[n].cartList[s].checked&&o.push(this.cartData[n].cartList[s].cart_id);else o.push(this.cartData[i].cartList[a].cart_id);0!=o.length?uni.showModal({title:"提示",content:o.length>1?"确定要删除这些商品吗？":"确定要删除该商品吗？",success:function(n){n.confirm&&(o=o.toString(),e.$api.sendRequest({url:"/api/cart/delete",data:{cart_id:o},success:function(o){if(o.code>=0){if("all"==t){for(var n=0;n<e.cartData.length;n++){for(var s=0;s<e.cartData[n].cartList.length;s++){var c=e.cartData[n].cartList[s];c.checked&&(e.cartData[n].cartList.splice(s,1),s=-1)}0==e.cartData[n].cartList.length&&(e.cartData.splice(n,1),n=-1)}e.$store.dispatch("emptyCart")}else{var r=e.cartData[i].cartList,l=r[a].goods_id,u=r[a].sku_id;delete e.cartList["goods_"+l]["sku_"+u],2==Object.keys(e.cartList["goods_"+l]).length&&delete e.cartList["goods_"+l],e.$store.dispatch("cartCalculate"),r.splice(a,1),0==r.length&&e.cartData.splice(i,1)}e.resetEditStatus(),e.calculationTotalPrice()}else e.$util.showToast({title:o.message})}}))}}):this.$util.showToast({title:"请选择要删除的商品"})},cartNumChange:function(t,i){var a=this;if(!isNaN(t)&&!this.editLock){this.editLock=!0;var e=this.cartData[i.siteIndex].cartList[i.cartIndex],o=e.is_limit&&e.max_buy>0&&e.max_buy<e.stock?e.max_buy:e.stock,n=e.min_buy>0?e.min_buy:1;t>o&&(t=o),t<n&&(t=n);var s=this.cartData[i.siteIndex].cartList[i.cartIndex].cart_id;this.$api.sendRequest({url:"/api/cart/edit",data:{num:t,cart_id:s},success:function(e){if(e.code>=0){var o=a.cartData[i.siteIndex].cartList[i.cartIndex],n=o.goods_id,s=o.sku_id;o.num=t,a.cartList["goods_"+n]["sku_"+s].num=t,a.$store.dispatch("cartCalculate"),a.resetEditStatus(),a.calculationTotalPrice()}else a.$util.showToast({title:e.message});a.editLock=!1}})}},settlement:function(){var t=this;if(this.totalCount>0){var i=!1;try{this.cartData.forEach((function(a,e){a.cartList.forEach((function(a,e){if(a.checked){if(a.num>a.stock)throw t.$util.showToast({title:"商品"+a.goods_name+"商品库存不足"}),i=!0,new Error;if(a.min_buy&&a.num<a.min_buy)throw t.$util.showToast({title:"商品"+a.goods_name+"商品最少要购买"+a.min_buy+"件"}),i=!0,new Error}}))}))}catch(n){}var a={};for(var e in this.cartData.forEach((function(t,i){t.cartList.forEach((function(t,i){t.checked&&(a[t.goods_id]?a[t.goods_id].num+=t.num:a[t.goods_id]={num:t.num,max_buy:t.max_buy,goods_name:t.goods_name})}))})),a)a[e].max_buy&&a[e].num>a[e].max_buy&&(this.$util.showToast({title:"商品"+a[e].goods_name+"最多可购买"+a[e].max_buy+"件"}),i=!0);if(i)return;var o=[];if(this.cartData.forEach((function(t){t.cartList.forEach((function(t){t.checked&&o.push(t.cart_id)}))})),this.discount.coupon_info&&"wait"==this.discount.coupon_info.receive_type&&this.receiveCoupon(this.discount.coupon_info.coupon_type_id,!1),this.isSub)return;this.isSub=!0,uni.removeStorageSync("delivery"),uni.setStorage({key:"orderCreateData",data:{cart_ids:o.toString()},success:function(){t.$util.redirectTo("/pages/order/payment"),t.isSub=!1}})}},clearInvalidGoods:function(){var t=this;uni.showModal({title:"提示",content:"确定要清空这些商品吗？",success:function(i){if(i.confirm){var a=[];t.invalidGoods.forEach((function(t){a.push(t.cart_id)})),a.length&&t.$api.sendRequest({url:"/api/cart/delete",data:{cart_id:a.toString()},success:function(i){i.code>=0?(t.invalidGoods=[],t.refreshCartNumber()):t.$util.showToast({title:i.message})}})}}})},imageError:function(t,i){this.cartData[t].cartList[i].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},toGoodsDetail:function(t){this.$util.redirectTo("/pages/goods/detail",{sku_id:t.sku_id})},refreshCartNumber:function(){this.storeToken&&(this.$store.dispatch("getCartNumber"),this.resetEditStatus())},goodsLimit:function(t,i){var a=this.cartData[i.siteIndex].cartList[i.cartIndex];"plus"==t.type?a.max_buy>0&&a.max_buy<a.stock?this.$util.showToast({title:"该商品每人限购"+a.max_buy+"件"}):this.$util.showToast({title:"库存不足"}):this.deleteCart("single",i.siteIndex,i.cartIndex)},toLogin:function(){this.$refs.login.open()},resetEditStatus:function(){if(this.cartData.length){for(var t=0;t<this.cartData[0].cartList.length;t++)this.cartData[0].cartList[t].edit=!1;this.$forceUpdate()}},changeAction:function(){this.isAction=!this.isAction,this.resetEditStatus()},selectSku:function(t){var i=this,a=this.$util.deepClone(t);a.goods_spec_format&&(a.goods_spec_format=JSON.parse(a.goods_spec_format)),this.goodsSkuDetail=a,this.$nextTick((function(){i.$refs.selectSku.show("confirm",(function(a,e){i.$api.sendRequest({url:"/api/cart/editcartsku",data:{cart_id:t.cart_id,sku_id:a,num:e},success:function(t){t.code>=0?(i.invalidGoods=[],i.getCartData(),i.refreshCartNumber()):i.$util.showToast({title:t.message})}})}),i.goodsSkuDetail)}))},toggleDiscountPopup:function(){this.$refs.discountPopup.showPopup?this.$refs.discountPopup.close():this.$refs.discountPopup.open(),this.discountPopupShow=!this.discountPopupShow},discountCalculate:function(){var t=this;if(0!=this.cartData.length){var i=[];this.cartData.forEach((function(t){t.cartList.forEach((function(t){t.checked&&i.push({sku_id:t.sku_id,num:t.num})}))})),0!=i.length?this.$api.sendRequest({url:"/api/cartcalculate/calculate",data:{sku_ids:JSON.stringify(i)},success:function(i){if(i.code>=0&&i.data&&(i.data.coupon_money>0||i.data.promotion_money>0)){t.discount=i.data;var a={};i.data.goods_list.forEach((function(t){t.promotion&&t.promotion.manjian?a["sku_"+t.sku_id]=JSON.parse(t.promotion.manjian.rule_json):a["sku_"+t.sku_id]=""})),Object.assign(t.manjian,a),t.refresherTriggered=!1}else t.discount={},t.manjian={}}}):this.discount={}}},receiveCoupon:function(t){var i=this,a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.receiveSub||(this.receiveSub=!0,this.$api.sendRequest({url:"/coupon/api/coupon/receive",data:{coupon_type_id:t,get_type:2},success:function(t){0==t.code?i.$set(i.discount.coupon_info,"receive_type",""):(a&&i.$util.showToast({title:t.message}),i.receiveSub=!1)}}))},touchS:function(t){this.startX=t.touches[0].clientX},touchE:function(t,i){this.endX=t.changedTouches[0].clientX;var a=this.startX-this.endX;a>50?i.edit=!0:a<0&&(i.edit=!1),this.$forceUpdate()},moneyFormat:function(t){return isNaN(parseFloat(t))?t:parseFloat(t).toFixed(2)},refreshSkuDetail:function(t){this.goodsSkuDetail=t},onRefresh:function(t){this.refresherTriggered=!0,this.storeToken?(this.getCartData(),this.refreshCartNumber()):(this.cartData=[],this.invalidGoods=[],this.calculationTotalPrice())}}};i.default=e},d1d0:function(t,i,a){var e=a("d644");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var o=a("967d").default;o("088dfa75",e,!0,{sourceMap:!1,shadowMode:!1})},d644:function(t,i,a){var e=a("c86c");i=e(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.placeholder[data-v-09d90d92]{height:%?112?%}.placeholder.bluge[data-v-09d90d92]{height:%?180?%}.safe-area[data-v-09d90d92]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.tab-bar[data-v-09d90d92]{background-color:#fff;box-sizing:border-box;position:fixed;left:0;bottom:0;width:100%;z-index:998;display:flex;border-top:%?2?% solid #f5f5f5;padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.tab-bar .tabbar-border[data-v-09d90d92]{background-color:hsla(0,0%,100%,.329412);position:absolute;left:0;top:0;width:100%;height:%?2?%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.tab-bar .item[data-v-09d90d92]{display:flex;align-items:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;flex:1;flex-direction:column;padding-bottom:%?10?%;box-sizing:border-box}.tab-bar .item .bd[data-v-09d90d92]{position:relative;height:%?100?%;flex-direction:column;text-align:center;display:flex;justify-content:center;align-items:center}.tab-bar .item .bd .icon[data-v-09d90d92]{position:relative;display:inline-block;margin-top:%?10?%;width:%?40?%;height:%?40?%;font-size:%?40?%;line-height:%?40?%}.tab-bar .item .bd .icon uni-image[data-v-09d90d92]{width:100%;height:100%}.tab-bar .item .bd .icon > uni-view[data-v-09d90d92]{height:inherit;display:flex;align-items:center}.tab-bar .item .bd .icon .bar-icon[data-v-09d90d92]{font-size:%?42?%}.tab-bar .item .bd .label[data-v-09d90d92]{position:relative;text-align:center;font-size:%?24?%;line-height:1;margin-top:%?12?%}.tab-bar .item.bulge .bd[data-v-09d90d92]{position:relative;height:%?100?%;flex-direction:column;text-align:center}.tab-bar .item.bulge .bd .icon[data-v-09d90d92]{margin-top:%?-60?%;margin-bottom:%?4?%;border-radius:50%;width:%?100?%;height:%?102?%;padding:%?10?%;border-top:%?2?% solid #f5f5f5;background-color:#fff;box-sizing:border-box}.tab-bar .item.bulge .bd .icon uni-image[data-v-09d90d92]{width:100%;height:100%;border-radius:50%}.tab-bar .item.bulge .bd .label[data-v-09d90d92]{position:relative;text-align:center;font-size:%?24?%;height:%?40?%;line-height:%?40?%}.tab-bar .item .cart-count-mark[data-v-09d90d92]{position:absolute;top:%?-8?%;right:%?-18?%;width:%?24?%;height:%?24?%!important;display:flex;justify-content:center;align-items:center;color:#fff;padding:%?6?%;border-radius:50%;z-index:99}.tab-bar .item .cart-count-mark.max[data-v-09d90d92]{width:%?40?%;border-radius:%?24?%;right:%?-28?%}.tab-bar-placeholder[data-v-09d90d92]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?112?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?112?%)}',""]),t.exports=i},e78f:function(t,i,a){"use strict";a("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var e={data:function(){return{showTop:!1,scrollTop:0,oldLocation:0}},methods:{scrollToTopNative:function(){uni.pageScrollTo({duration:200,scrollTop:0})}},onReachBottom:function(){this.$refs.goodrecommend&&this.$refs.goodrecommend.getLikeList(10)},onPageScroll:function(t){this.oldLocation=t.scrollTop,t.scrollTop>400?this.showTop=!0:this.showTop=!1}};i.default=e},e926:function(t,i,a){"use strict";a.r(i);var e=a("64a3"),o=a.n(e);for(var n in e)["default"].indexOf(n)<0&&function(t){a.d(i,t,(function(){return e[t]}))}(n);i["default"]=o.a},edc3:function(t,i,a){"use strict";a.r(i);var e=a("78c0"),o=a.n(e);for(var n in e)["default"].indexOf(n)<0&&function(t){a.d(i,t,(function(){return e[t]}))}(n);i["default"]=o.a},f7ad:function(t,i,a){"use strict";a.d(i,"b",(function(){return e})),a.d(i,"c",(function(){return o})),a.d(i,"a",(function(){}));var e=function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("v-uni-view",{staticClass:"uni-numbox",class:{small:"small"==t.size}},[a("v-uni-button",{staticClass:"decrease",class:{disabled:t.inputValue<=t.min||t.disabled,small:"small"==t.size},attrs:{type:"default"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t._calcValue("minus")}}},[t._v("-")]),a("v-uni-input",{staticClass:"uni-input uni-numbox__value",class:{small:"small"==t.size},attrs:{disabled:t.disabled||t.inputDisabled,type:"number"},on:{blur:function(i){arguments[0]=i=t.$handleEvent(i),t._onInput.apply(void 0,arguments)}},model:{value:t.inputValue,callback:function(i){t.inputValue=i},expression:"inputValue"}}),a("v-uni-button",{staticClass:"increase",class:{disabled:t.inputValue>=t.max||t.disabled,small:"small"==t.size},attrs:{type:"default"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t._calcValue("plus")}}},[t._v("+")])],1)},o=[]},f964:function(t,i,a){var e=a("9a0a");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var o=a("967d").default;o("bcc1b586",e,!0,{sourceMap:!1,shadowMode:!1})}}]);