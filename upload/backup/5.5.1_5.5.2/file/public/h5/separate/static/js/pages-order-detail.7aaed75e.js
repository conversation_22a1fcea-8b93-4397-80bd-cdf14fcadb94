(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-detail","pages-order-list~pages-order-payment~pages_promotion-bale-payment~pages_promotion-bargain-payment~pa~d33874aa","pages_promotion-blindbox-index~pages_promotion-giftcard-order_detail~pages_promotion-giftcard-order_~f811c135"],{"00ba":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?a("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?a("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),a("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),a("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),a("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),a("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),a("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():a("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},o=[]},"00dc":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={uniPopup:a("d745").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"contact-wrap"},[t._t("default"),a("v-uni-button",{staticClass:"contact-button",attrs:{type:"default","hover-class":"none","open-type":t.openType,"send-message-title":t.sendMessageTitle,"send-message-path":t.sendMessagePath,"send-message-img":t.sendMessageImg,"show-message-card":!0},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.contactServicer.apply(void 0,arguments)}}}),a("uni-popup",{ref:"servicePopup",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"service-popup-wrap"},[a("v-uni-view",{staticClass:"head-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.servicePopup.close()}}},[a("v-uni-text",[t._v("联系客服")]),a("v-uni-text",{staticClass:"iconfont icon-close"})],1),a("v-uni-view",{staticClass:"body-wrap"},[t._v(t._s(t.siteInfo.site_tel?"请联系客服，客服电话是"+t.siteInfo.site_tel:"抱歉，商家暂无客服，请线下联系"))])],1)],1)],2)},s=[]},"0619":function(t,e,a){"use strict";var i=a("36f2"),o=a.n(i);o.a},"11ac":function(t,e,a){"use strict";var i=a("9c4f"),o=a.n(i);o.a},"171a":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"nsSwitch",props:{checked:{type:Boolean,default:!1}},methods:{change:function(){this.$emit("change")}}};e.default=i},"1dc6":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"ns-contact",props:{niushop:{type:Object,default:function(){return{}}},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""}},data:function(){return{config:null,openType:""}},created:function(){this.servicerConfig&&(this.config=this.servicerConfig.h5)},methods:{contactServicer:function(){if("none"==this.config.type&&this.$refs.servicePopup.open(),"contact"!=this.openType)switch(this.config.type){case"wxwork":location.href=this.config.wxwork_url;break;case"third":location.href=this.config.third_url;break;case"niushop":this.$util.redirectTo("/pages_tool/chat/room",this.niushop);break;default:this.makePhoneCall()}},makePhoneCall:function(){this.$api.sendRequest({url:"/api/site/shopcontact",success:function(t){0==t.code&&t.data.mobile&&uni.makePhoneCall({phoneNumber:t.data.mobile})}})}}};e.default=i},"1dd9":function(t,e,a){var i=a("2a42");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("8c18b280",i,!0,{sourceMap:!1,shadowMode:!1})},"1f3b":function(t,e,a){"use strict";a.r(e);var i=a("be4e"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},2519:function(t,e,a){"use strict";var i=a("9e59"),o=a.n(i);o.a},2523:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.contact-wrap[data-v-142659c1]{width:100%;height:100%;position:relative}.contact-wrap .contact-button[data-v-142659c1]{width:100%;height:100%;position:absolute;left:0;top:0;z-index:5;padding:0;margin:0;opacity:0;overflow:hidden}.service-popup-wrap[data-v-142659c1]{width:%?600?%}.service-popup-wrap .head-wrap[data-v-142659c1]{display:flex;justify-content:space-between;align-items:center;padding:0 %?30?%;height:%?90?%}.service-popup-wrap .body-wrap[data-v-142659c1]{text-align:center;padding:%?30?%;height:%?100?%}',""]),t.exports=e},2817:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={methods:{orderDelete:function(t,e){var a=this;uni.showModal({title:"提示",content:"您确定要删除该订单吗？",success:function(i){i.confirm&&a.$api.sendRequest({url:"/api/order/delete",data:{order_id:t},success:function(t){t.code>=0?(a.$util.showToast({title:"删除订单成功"}),"function"==typeof e&&e()):a.$util.showToast({title:"删除订单失败，"+t.message,duration:2e3})}})}})},orderPay:function(t){var e=this;0==t.adjust_money?this.pay():uni.showModal({title:"提示",content:"商家已将支付金额调整为"+t.pay_money+"元，是否继续支付？",success:function(t){t.confirm&&e.pay()}})},pay:function(){var t=this;this.$api.sendRequest({url:"/api/order/pay",data:{order_ids:this.orderData.order_id},success:function(e){e.code>=0?t.$refs.choosePaymentPopup.getPayInfo(e.data):t.$util.showToast({title:e.message})}})},orderClose:function(t,e){var a=this;uni.showModal({title:"提示",content:"您确定要关闭该订单吗？",success:function(i){i.confirm&&a.$api.sendRequest({url:"/api/order/close",data:{order_id:t},success:function(t){t.code>=0?"function"==typeof e&&e():a.$util.showToast({title:"关闭失败，"+t.message,duration:2e3})}})}})},orderDelivery:function(t,e){var a=this;uni.showModal({title:"提示",content:"您确定已经收到货物了吗？",success:function(i){i.confirm&&a.$api.sendRequest({url:"/api/order/takedelivery",data:{order_id:t.order_id},success:function(t){a.$util.showToast({title:t.message}),"function"==typeof e&&e()}})}})},orderVirtualDelivery:function(t,e){var a=this;uni.showModal({title:"提示",content:"您确定要进行收货吗？",success:function(i){i.confirm&&a.$api.sendRequest({url:"/api/order/membervirtualtakedelivery",data:{order_id:t.order_id},success:function(t){a.$util.showToast({title:t.message}),"function"==typeof e&&e()}})}})}}};e.default=i},"2a42":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.popup[data-v-4252f8a9]{width:75vw;background:#fff;border-top-left-radius:%?10?%;border-top-right-radius:%?10?%}.popup .popup-header[data-v-4252f8a9]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-4252f8a9]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-4252f8a9]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-4252f8a9]{height:calc(100% - %?250?%)}.popup .popup-body.safe-area[data-v-4252f8a9]{height:calc(100% - %?270?%)}.popup .popup-footer[data-v-4252f8a9]{height:%?100?%}.popup .popup-footer .confirm-btn[data-v-4252f8a9]{height:%?72?%;line-height:%?72?%;color:#fff;text-align:center;margin:%?20?% %?30?% 0;border-radius:%?10?%;font-size:%?28?%}.popup .popup-footer.bottom-safe-area[data-v-4252f8a9]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.choose-payment-popup .payment-item[data-v-4252f8a9]{display:flex;align-items:center;justify-content:space-between;height:%?90?%;margin:0 %?30?%;border-bottom:%?2?% solid #eee;padding:%?20?% 0}.choose-payment-popup .payment-item[data-v-4252f8a9]:nth-child(2){padding-top:0}.choose-payment-popup .payment-item[data-v-4252f8a9]:last-child{border-bottom:none}.choose-payment-popup .payment-item .iconfont[data-v-4252f8a9]{font-size:%?64?%}.choose-payment-popup .payment-item .icon-yue[data-v-4252f8a9]{color:#faa218}.choose-payment-popup .payment-item .icon-weixin1[data-v-4252f8a9]{color:#24af41}.choose-payment-popup .payment-item .icon-zhifubaozhifu-[data-v-4252f8a9]{color:#00a0e9}.choose-payment-popup .payment-item .icon-checkboxblank[data-v-4252f8a9]{font-size:%?40?%;color:#eee}.choose-payment-popup .payment-item .icon-yuan_checked[data-v-4252f8a9]{font-size:%?40?%}.choose-payment-popup .payment-item .name[data-v-4252f8a9]{margin-left:%?20?%;font-size:%?28?%;flex:1}.choose-payment-popup .payment-item .info-wrap[data-v-4252f8a9]{flex:1;margin-left:%?20?%}.choose-payment-popup .payment-item .info-wrap .name[data-v-4252f8a9]{margin-left:0;font-size:%?28?%;flex:1}.choose-payment-popup .payment-item .info-wrap .money[data-v-4252f8a9]{color:#909399;font-size:%?24?%}.choose-payment-popup .payment-item .box[data-v-4252f8a9]{flex:1;padding:0 %?10?%;line-height:inherit;text-align:right}.choose-payment-popup .payment-item .box uni-input[data-v-4252f8a9]{font-size:%?24?%!important}.choose-payment-popup .payment-item.set-pay-password[data-v-4252f8a9]{height:auto}.choose-payment-popup .payment-item.set-pay-password .box[data-v-4252f8a9]{font-size:%?24?%!important}.choose-payment-popup .pay-money[data-v-4252f8a9]{text-align:center;padding:%?20?% 0 %?40?% 0;background-color:#fff;font-weight:700;margin-top:%?30?%;line-height:1}.choose-payment-popup .pay-money .unit[data-v-4252f8a9]{margin-right:%?4?%;font-size:%?24?%}.choose-payment-popup .pay-money .money[data-v-4252f8a9]{font-size:%?32?%}.empty[data-v-4252f8a9]{width:100%;text-align:center;padding:%?40?% 0;color:#606266;font-size:%?24?%}',""]),t.exports=e},"2a87":function(t,e,a){"use strict";a.r(e);var i=a("a985"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},"2dda":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={pageMeta:a("7854").default,uniCountDown:a("e12a").default,nsContact:a("5036").default,nsGoodsRecommend:a("7254").default,nsPayment:a("7aec").default,loadingCover:a("c003").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-view",{staticClass:"detail-container",class:{"safe-area":t.isIphoneX}},[a("v-uni-view",{staticClass:"status-wrap color-base-bg",style:{backgroundImage:"url("+t.$util.img("public/uniapp/order/status-wrap-bg.png")+")"}},[a("v-uni-view",{staticClass:"order-status-left"},[a("v-uni-image",{attrs:{src:t.$util.img(t.action.icon)}}),a("v-uni-view",{staticClass:"status-name"},[a("v-uni-view",{staticClass:"name"},[t._v(t._s(t.orderData.order_status_name)),t.orderData.promotion_status_name?a("v-uni-text",[t._v("（"+t._s(t.orderData.promotion_status_name)+"）")]):t._e()],1),"presale"==t.orderData.promotion_type&&1==t.orderData.order_status?a("v-uni-view",{staticClass:"desc"},[t._v("预计"+t._s(t.$util.timeStampTurnTime(t.orderData.predict_delivery_time,"Y-m-d"))+"发货")]):t._e(),t.orderData.close_cause?a("v-uni-view",{staticClass:"desc"},[t._v("订单关闭原因："+t._s(t.orderData.close_cause))]):t._e()],1)],1),0==t.orderData.order_status&&"offlinepay"!=t.orderData.pay_type?a("v-uni-view",{staticClass:"order-time",attrs:{id:"action-date"}},[t._v("剩余时间："),a("uni-count-down",{attrs:{day:t.orderData.closeTimeMachine.d,hour:t.orderData.closeTimeMachine.h,minute:t.orderData.closeTimeMachine.i,second:t.orderData.closeTimeMachine.s,color:"#fff",splitorColor:"#fff"}})],1):t._e()],1),1==t.orderData.order_type||3==t.orderData.order_type?a("v-uni-view",{staticClass:"address-wrap"},[a("v-uni-view",{staticClass:"icon"},[a("v-uni-view",{staticClass:"iconfont icon-location"})],1),a("v-uni-view",{staticClass:"address-info"},[a("v-uni-view",{staticClass:"info"},[a("v-uni-text",{staticClass:"font-size-base"},[t._v(t._s(t.orderData.name)+" "+t._s(t.orderData.mobile))])],1),a("v-uni-view",{staticClass:"detail"},[a("v-uni-text",{staticClass:"font-size-base"},[t._v("收货地址："+t._s(t.orderData.full_address)+" "+t._s(t.orderData.address))])],1)],1),3==t.orderData.order_type?[a("v-uni-view",{staticClass:"pick-block first-pick-block"},[a("v-uni-view",{staticClass:"font-size-base"},[t._v("送达时间：")]),a("v-uni-view",{staticClass:"last-child"},[t._v(t._s(t.orderData.buyer_ask_delivery_time))])],1),t.orderData.package_list.deliverer?[a("v-uni-view",{staticClass:"pick-block"},[a("v-uni-view",[t._v("配送员：")]),a("v-uni-view",{staticClass:"last-child"},[t._v(t._s(t.orderData.package_list.deliverer))])],1),a("v-uni-view",{staticClass:"pick-block"},[a("v-uni-view",[t._v("外卖电话：")]),a("v-uni-view",{staticClass:"last-child"},[t._v(t._s(t.orderData.package_list.deliverer_mobile))])],1)]:t._e()]:t._e()],2):t._e(),2==t.orderData.order_type?[a("v-uni-view",{staticClass:"store-wrap"},[a("v-uni-view",[t.orderData.delivery_store_info?a("v-uni-view",{staticClass:"store-info"},[a("v-uni-view",{staticClass:"icon"},[a("v-uni-view",{staticClass:"iconfont icon-mendian"})],1),a("v-uni-view",{staticClass:"store-info-detail"},[a("v-uni-view",{staticClass:"store-name",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_tool/store/detail",{store_id:t.orderData.delivery_store_id})}}},[a("v-uni-text",{staticClass:"name font-size-base"},[t._v(t._s(t.orderData.delivery_store_name))]),a("v-uni-text",{staticClass:"iconfont icon-right font-size-base"})],1),a("v-uni-view",{staticClass:"detail store-detail"},[t.orderData.delivery_store_info.open_date?a("v-uni-view",{staticClass:"color-tip"},[t._v("营业时间："+t._s(t.orderData.delivery_store_info.open_date))]):t._e(),a("v-uni-view",{staticClass:"color-tip"},[t._v("地址："+t._s(t.orderData.delivery_store_info.full_address))])],1)],1)],1):a("v-uni-view",[a("v-uni-view",{staticClass:"address-empty"},[a("v-uni-view",{staticClass:"color-base-text"},[t._v("当前无自提门店")])],1)],1)],1),a("v-uni-view",{staticClass:"pick-block first-pick-block"},[a("v-uni-view",{staticClass:"font-size-base"},[t._v("姓名")]),a("v-uni-input",{staticClass:"last-child",attrs:{type:"text",disabled:!0,value:t.orderData.name}})],1),a("v-uni-view",{staticClass:"pick-block first-pick-block"},[a("v-uni-view",{staticClass:"font-size-base"},[t._v("预留手机")]),a("v-uni-input",{staticClass:"last-child",attrs:{type:"number",disabled:!0,value:t.orderData.mobile}})],1),a("v-uni-view",{staticClass:"pick-block first-pick-block"},[a("v-uni-view",{staticClass:"font-size-base"},[t._v("提货时间")]),a("v-uni-input",{staticClass:"last-child",attrs:{disabled:!0,value:t.orderData.buyer_ask_delivery_time}})],1)],1),t.orderData.pay_status?a("v-uni-view",{staticClass:"pickup-info"},[a("v-uni-view",{staticClass:"pickup-code-info"},[a("v-uni-view",{staticClass:"info"},[a("v-uni-text",{staticClass:"font-size-base"},[a("v-uni-text",{staticClass:"color-tip font-size-base"},[t._v("提货码：")]),t._v(t._s(t.orderData.delivery_code))],1),a("v-uni-text",{staticClass:"copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.orderData.delivery_code)}}},[t._v("复制")])],1),a("v-uni-view",{staticClass:"code"},[a("v-uni-image",{staticClass:"barcode",attrs:{src:t.$util.img(t.orderData.pickup_barcode),mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewMedia(t.$util.img(t.orderData.pickup_barcode))}}}),a("v-uni-image",{staticClass:"qrcode",attrs:{src:t.$util.img(t.orderData.pickup),mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewMedia(t.$util.img(t.orderData.pickup))}}})],1)],1)],1):t._e()]:t._e(),a("v-uni-view",{staticClass:"site-wrap",style:4==t.orderData.order_type?"margin-top: -69rpx;":""},[a("v-uni-view",{staticClass:"site-body"},t._l(t.orderData.order_goods,(function(e,i){return a("v-uni-view",{key:i,staticClass:"goods-item"},[a("v-uni-view",{staticClass:"goods-wrap"},[a("v-uni-view",{staticClass:"goods-img",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goDetail(e)}}},[a("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i)}}})],1),a("v-uni-view",{staticClass:"goods-info",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goDetail(e)}}},[a("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.sku_name))]),e.sku_spec_format?a("v-uni-view",{staticClass:"sku"},[a("v-uni-view",{staticClass:"goods-spec"},[t._l(e.sku_spec_format,(function(a,i){return[t._v(t._s(a.spec_value_name)+"\n\t\t\t\t\t\t\t\t\t"+t._s(i<e.sku_spec_format.length-1?"; ":""))]}))],2),a("v-uni-view",{staticClass:"goods-num"},[a("v-uni-text",{staticClass:"iconfont icon-close"}),a("v-uni-text",[t._v(t._s(e.num))])],1)],1):t._e(),a("v-uni-view",{staticClass:"goods-sub-section"},[a("v-uni-view",[a("v-uni-text",{staticClass:"goods-price "},[a("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price-style large"},[t._v(t._s(parseFloat(e.price).toFixed(2).split(".")[0]))]),a("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(e.price).toFixed(2).split(".")[1]))])],1)],1),1==t.orderData.order_type&&1==t.orderData.order_status&&1==e.delivery_status?a("v-uni-view",{staticClass:"delivery-status color-base-text"},[t._v(t._s(e.delivery_status_name))]):t._e()],1),e.card_item_id?a("v-uni-view",{staticClass:"goods-card"},[t._v("次卡抵扣"),a("v-uni-text",{staticClass:"goods-price "},[a("v-uni-text",{staticClass:"unit price-style small"},[t._v("-"+t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price-style large"},[t._v(t._s(parseFloat(e.card_promotion_money).toFixed(2).split(".")[0]))]),a("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(e.card_promotion_money).toFixed(2).split(".")[1]))])],1)],1):t._e()],1)],1),e.form?a("v-uni-view",{staticClass:"goods-form"},t._l(e.form,(function(e,i){return a("v-uni-view",{key:i,staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(e.value.title)+"：")]),"Img"==e.controller?a("v-uni-view",{staticClass:"box img-box"},t._l(e.img_lists,(function(e,i){return a("v-uni-view",{key:i,staticClass:"img"},[a("v-uni-image",{attrs:{src:t.$util.img(e),mode:"widthFix"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.previewMedia(t.$util.img(e))}}})],1)})),1):a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(e.val))]),a("v-uni-text",{staticClass:"copy",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.$util.copy(e.val)}}},[t._v("复制")])],1)],1)})),1):t._e(),a("v-uni-view",{staticClass:"goods-action"},[t.orderData.is_enable_refund&&"online"==t.orderData.order_scene&&0==e.refund_status&&"blindbox"!=t.orderData.promotion_type?a("v-uni-view",{staticClass:"order-box-btn",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goRefund(e.order_goods_id)}}},[t._v(t._s(10==t.orderData.order_status?"申请售后":"申请退款"))]):t._e(),0!=e.refund_status?a("v-uni-view",{staticClass:"order-box-btn",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goRefundDetail(e.order_goods_id)}}},[t._v(t._s(e.refund_status_name))]):t._e()],1)],1)})),1)],1),a("v-uni-view",{staticClass:"order-summary"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("订单类型：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.order_type_name))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("订单编号：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.order_no))]),a("v-uni-view",{staticClass:"copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.orderData.order_no)}}},[t._v("复制")])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("订单交易号：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.out_trade_no))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("创建时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.create_time)))])],1)],1),t.orderData.close_time>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("关闭时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.close_time)))])],1)],1):t._e(),t.orderData.pay_status>0?[a("v-uni-view",{staticClass:"hr"}),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("支付方式：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.pay_type_name))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("支付时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.pay_time)))])],1)],1)]:t._e(),t.orderData.delivery_type_name?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("配送方式：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.delivery_type_name))])],1)],1):t._e(),""!=t.orderData.buyer_message?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("买家留言：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.buyer_message))])],1)],1):t._e(),""!=t.orderData.promotion_type_name?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("活动优惠：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.promotion_type_name))])],1)],1):t._e(),t.orderData.is_invoice>0?[a("v-uni-view",{staticClass:"hr"}),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("发票类型：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(1==t.orderData.invoice_type?"纸质发票":"电子发票"))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("发票抬头类型：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(1==t.orderData.invoice_title_type?"个人":"企业"))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("发票抬头：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.invoice_title))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("发票内容：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.invoice_content))])],1)],1),1==t.orderData.invoice_type?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("发票邮寄地址：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.invoice_full_address))])],1)],1):a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("发票接收邮件：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.invoice_email))])],1)],1)]:t._e(),t.orderData.form?[a("v-uni-view",{staticClass:"hr"}),t._l(t.orderData.form,(function(e,i){return a("v-uni-view",{key:i,staticClass:"order-cell order-form"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(e.value.title)+"：")]),"Img"==e.controller?a("v-uni-view",{staticClass:"box img-box"},t._l(e.img_lists,(function(e,i){return a("v-uni-view",{key:i,staticClass:"img"},[a("v-uni-image",{attrs:{src:t.$util.img(e),mode:"widthFix"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.previewMedia(t.$util.img(e))}}})],1)})),1):a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(e.val))]),a("v-uni-text",{staticClass:"copy",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.$util.copy(e.val)}}},[t._v("复制")])],1)],1)}))]:t._e(),5==t.orderData.goods_class?[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("我的卡包：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_promotion/cardservice/card/my_card")}}},[t._v("查看")])],1)],1)]:t._e(),"offlinepay"==t.orderData.pay_type&&t.orderData.offline_pay_info?[a("v-uni-view",{staticClass:"hr"}),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("支付方式：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v("线下支付")])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("支付状态：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.offline_pay_info.status_info.name))])],1)],1),"AUDIT_REFUSE"==t.orderData.offline_pay_info.status_info.const?a("v-uni-view",{staticClass:"order-cell remark"},[a("v-uni-text",{staticClass:"tit"},[t._v("审核备注：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.offline_pay_info.audit_remark))])],1)],1):t._e()]:t._e(),a("ns-contact",{attrs:{niushop:{order_id:t.orderData.order_id}}},[a("v-uni-view",{staticClass:"kefu"},[a("v-uni-view",[a("v-uni-text",{staticClass:"iconfont icon-ziyuan"}),a("v-uni-text",[t._v("联系客服")])],1)],1)],1)],2),t.orderData.virtual_goods&&2==t.orderData.goods_class&&0==t.orderData.virtual_goods.is_veirfy?[a("v-uni-view",{staticClass:"verify-code-wrap"},[a("v-uni-view",{staticClass:"code"},[a("v-uni-image",{staticClass:"barcode",attrs:{src:t.$util.img(t.orderData.virtualgoods_barcode),mode:"widthFix"}}),a("v-uni-image",{staticClass:"qrcode",attrs:{src:t.$util.img(t.orderData.virtualgoods),mode:"widthFix"}}),a("v-uni-text",[t._v("请将条形码或二维码出示给核销员")])],1),a("v-uni-view",{staticClass:"hr"}),a("v-uni-view",{staticClass:"wrap"},[a("v-uni-text",{staticClass:"font-size-base virtual-code"},[t._v(t._s(t.orderData.virtual_code))]),a("v-uni-view",{staticClass:"copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.orderData.virtual_code)}}},[t._v("复制")])],1)],1),a("v-uni-view",{staticClass:"verify-info-wrap"},[a("v-uni-view",{staticClass:"head"},[t._v("核销信息")]),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("核销次数")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-title"},[t._v("剩余"+t._s(t.orderData.virtual_goods.verify_total_count-t.orderData.virtual_goods.verify_use_num)+"次/共"+t._s(t.orderData.virtual_goods.verify_total_count)+"次")])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("有效期")]),a("v-uni-view",{staticClass:"box align-right"},[t.orderData.virtual_goods.expire_time>0?a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.virtual_goods.expire_time)))]):a("v-uni-text",[t._v("永久有效")])],1)],1)],1),a("v-uni-view",{staticClass:"verify-info-wrap"},[a("v-uni-view",{staticClass:"head"},[t._v("核销记录")]),t.orderData.virtual_goods.verify_record.length?a("v-uni-view",t._l(t.orderData.virtual_goods.verify_record,(function(e,i){return a("v-uni-view",{key:i,staticClass:"record-item"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("核销人")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(e.verifier_name))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("核销时间")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(e.verify_time)))])],1)],1)],1)})),1):a("v-uni-view",{staticClass:"record-empty"},[t._v("暂无核销记录")])],1)]:t._e(),t.orderData.virtual_goods&&3==t.orderData.goods_class?[a("v-uni-view",{staticClass:"verify-info-wrap carmichael"},[a("v-uni-view",{staticClass:"head"},[t._v("卡密信息")]),t._l(t.orderData.virtual_goods,(function(e,i){return a("v-uni-view",{key:i,staticClass:"record-item"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("卡号：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(e.card_info.cardno))]),a("v-uni-view",{staticClass:"copy",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.$util.copy(e.card_info.cardno)}}},[t._v("复制")])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("密码：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(e.card_info.password))]),a("v-uni-view",{staticClass:"copy",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.$util.copy(e.card_info.password)}}},[t._v("复制")])],1)],1)],1)}))],2)]:t._e(),t.orderData.virtual_goods&&4==t.orderData.goods_class?[0==t.orderData.virtual_goods.is_veirfy?a("v-uni-view",{staticClass:"verify-code-wrap"},[a("v-uni-view",{staticClass:"code"},[a("v-uni-image",{staticClass:"barcode",attrs:{src:t.$util.img(t.orderData.virtualgoods_barcode),mode:"widthFix"}}),a("v-uni-image",{staticClass:"qrcode",attrs:{src:t.$util.img(t.orderData.virtualgoods),mode:"widthFix"}}),a("v-uni-text",[t._v("请将条形码或二维码出示给核销员")])],1),a("v-uni-view",{staticClass:"hr"}),a("v-uni-view",{staticClass:"wrap"},[a("v-uni-text",{staticClass:"font-size-base virtual-code"},[t._v(t._s(t.orderData.virtual_code))]),a("v-uni-view",{staticClass:"copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.orderData.virtual_code)}}},[t._v("复制")])],1)],1):t._e(),a("v-uni-view",{staticClass:"verify-info-wrap"},[a("v-uni-view",{staticClass:"head"},[t._v("核销信息")]),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("核销次数")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-title"},[t._v("剩余"+t._s(t.orderData.virtual_goods.verify_total_count-t.orderData.virtual_goods.verify_use_num)+"次/共"+t._s(t.orderData.virtual_goods.verify_total_count)+"次")])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("有效期")]),a("v-uni-view",{staticClass:"box align-right"},[t.orderData.virtual_goods.expire_time>0?a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.virtual_goods.expire_time)))]):a("v-uni-text",[t._v("永久有效")])],1)],1)],1),a("v-uni-view",{staticClass:"verify-info-wrap"},[a("v-uni-view",{staticClass:"head"},[t._v("核销记录")]),t.orderData.virtual_goods.verify_record.length?a("v-uni-view",t._l(t.orderData.virtual_goods.verify_record,(function(e,i){return a("v-uni-view",{key:i,staticClass:"record-item"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("核销人")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(e.verifier_name))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("核销时间")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(e.verify_time)))])],1)],1)],1)})),1):a("v-uni-view",{staticClass:"record-empty"},[t._v("暂无核销记录")])],1)]:t._e(),a("v-uni-view",{staticClass:"order-money"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("商品金额")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-title price-font"},[a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(t.orderData.goods_money))],1)],1)],1),4!=t.orderData.order_type?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("运费")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t.orderData.delivery_money))])],1)],1)],1):t._e(),t.orderData.member_card_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("会员卡")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t.orderData.member_card_money))])],1)],1)],1):t._e(),t.orderData.invoice_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("税费"),a("v-uni-text",{staticClass:"color-base-text"},[t._v("("+t._s(t.orderData.invoice_rate)+"%)")])],1),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator"},[t._v("+")]),a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t.orderData.invoice_money))])],1)],1)],1):t._e(),t.orderData.invoice_delivery_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("发票邮寄费")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator"},[t._v("+")]),a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t.orderData.invoice_delivery_money))])],1)],1)],1):t._e(),0!=t.orderData.adjust_money?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("订单调整")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[t.orderData.adjust_money<0?a("v-uni-text",{staticClass:"operator"},[t._v("-")]):a("v-uni-text",{staticClass:"operator"},[t._v("+")]),a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t._f("abs")(t.orderData.adjust_money)))])],1)],1)],1):t._e(),t.orderData.promotion_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("优惠")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator"},[t._v("-")]),a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t.orderData.promotion_money))])],1)],1)],1):t._e(),t.orderData.coupon_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("优惠券")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator"},[t._v("-")]),a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t.orderData.coupon_money))])],1)],1)],1):t._e(),t.orderData.balance_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("使用余额")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator"},[t._v("-")]),a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t.orderData.balance_money))])],1)],1)],1):t._e(),t.orderData.point_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("积分抵扣")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator"},[t._v("-")]),a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",[t._v(t._s(t.orderData.point_money))])],1)],1)],1):t._e(),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",[t._v("实付金额：")]),a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"font-size-base"},[t._v(t._s(t.orderData.order_money))])],1)],1)],1),t.orderData.action.length>0?a("v-uni-view",{staticClass:"order-action fixed-bottom bottom-safe-area"},[1==t.evaluateConfig.evaluate_status&&1==t.orderData.is_evaluate?a("v-uni-view",{staticClass:"order-box-btn white",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.operation("memberOrderEvaluation")}}},[0==t.orderData.evaluate_status?a("v-uni-text",[t._v("评价")]):1==t.orderData.evaluate_status?a("v-uni-text",[t._v("追评")]):t._e()],1):t._e(),"online"==t.orderData.order_scene?t._l(t.orderData.action,(function(e,i){return a("v-uni-view",{key:i,staticClass:"order-box-btn",class:{"color-base-border color-base-bg":"orderPay"==e.action},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.operation(e.action)}}},[t._v(t._s(e.title))])})):t._e()],2):0==t.orderData.action.length&&1==t.orderData.is_evaluate&&1==t.evaluateConfig.evaluate_status?a("v-uni-view",{staticClass:"order-action fixed-bottom bottom-safe-area"},[a("v-uni-view",{staticClass:"order-box-btn white",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.operation("memberOrderEvaluation")}}},[0==t.orderData.evaluate_status?a("v-uni-text",[t._v("评价")]):1==t.orderData.evaluate_status?a("v-uni-text",[t._v("追评")]):t._e()],1)],1):t._e()],1),t.orderData.action.length>0||1==t.orderData.is_evaluate&&1==t.evaluateConfig.evaluate_status?a("v-uni-view",{staticClass:"fixed-bottom-box bottom-safe-area"}):t._e(),a("ns-goods-recommend",{ref:"goodrecommend",attrs:{route:"order_detail"}}),a("ns-payment",{ref:"choosePaymentPopup"}),a("loading-cover",{ref:"loadingCover"})],2)],1)},s=[]},"36f2":function(t,e,a){var i=a("bfb8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("043c1984",i,!0,{sourceMap:!1,shadowMode:!1})},"4c42":function(t,e,a){"use strict";var i=a("c46e"),o=a.n(i);o.a},5036:function(t,e,a){"use strict";a.r(e);var i=a("00dc"),o=a("5323");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("bb68");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"142659c1",null,!1,i["a"],void 0);e["default"]=r.exports},5323:function(t,e,a){"use strict";a.r(e);var i=a("1dc6"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},"554a":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.weui-switch[data-v-7e2a453e]{display:block;position:relative;width:%?94?%;height:%?45?%;outline:0;border-radius:%?30?%;border:%?2?% solid;border-color:#dfdfdf;transition:background-color .1s,border .1s}.weui-switch .bgview[data-v-7e2a453e]{content:" ";position:absolute;top:0;left:0;width:%?94?%;height:%?45?%;border-radius:%?30?%;transition:-webkit-transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1),-webkit-transform .35s cubic-bezier(.45,1,.4,1)}.weui-switch .spotview[data-v-7e2a453e]{content:" ";position:absolute;top:%?2?%;left:%?4?%;width:%?40?%;height:%?40?%;border-radius:50%;background-color:#fff;box-shadow:0 %?2?% %?6?% rgba(0,0,0,.4);transition:-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35),-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35)}.weui-switch-on[data-v-7e2a453e]{border-color:#6f6f6f}.weui-switch-on .bgview[data-v-7e2a453e]{border-color:#1aad19}.weui-switch-on .spotview[data-v-7e2a453e]{-webkit-transform:translateX(%?48?%);transform:translateX(%?48?%)}',""]),t.exports=e},"5e08":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={uniPopup:a("d745").default,nsSwitch:a("b0ec").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[t.payInfo?a("uni-popup",{ref:"choosePaymentPopup",attrs:{type:"center","mask-click":!1}},[a("v-uni-view",{staticClass:"choose-payment-popup popup",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[t._v("支付方式")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close()}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"pay-money"},[a("v-uni-text",{staticClass:"money"},[t._v("支付金额"+t._s(t._f("moneyFormat")(t.payMoney))+"元")])],1),t.balanceDeduct>0&&t.balanceUsable&&1==t.balanceConfig?a("v-uni-view",{staticClass:"payment-item"},[a("v-uni-view",{staticClass:"iconfont icon-yue"}),a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-text",{staticClass:"name"},[t._v("余额抵扣")]),a("v-uni-view",{staticClass:"money"},[t._v("可用¥"+t._s(t._f("moneyFormat")(t.balanceDeduct)))])],1),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==t.isBalance},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.useBalance.apply(void 0,arguments)}}})],1):t._e(),t.payMoney>0?[t.payTypeList.length?[t._l(t.payTypeList,(function(e,i){return[t.offlineShow||"offlinepay"!=e.type?a("v-uni-view",{key:i,staticClass:"payment-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.payIndex=i}}},[a("v-uni-view",{staticClass:"iconfont",class:e.icon}),a("v-uni-text",{staticClass:"name"},[t._v(t._s(e.name))]),a("v-uni-text",{staticClass:"iconfont",class:t.payIndex==i?"icon-yuan_checked color-base-text":"icon-checkboxblank"})],1):t._e()]}))]:[a("v-uni-view",{staticClass:"empty"},[t._v("平台尚未配置支付方式！")])]]:t._e()],2),a("v-uni-view",{staticClass:"popup-footer"},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm()}}},[t._v("确认支付")])],1)],1)],1):t._e()],1)},s=[]},"67dd":function(t,e,a){"use strict";var i=a("1dd9"),o=a.n(i);o.a},"6c0a":function(t,e,a){var i=a("ff56");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("1815c73c",i,!0,{sourceMap:!1,shadowMode:!1})},7258:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={uniPopup:a("d745").default,nsSwitch:a("b0ec").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("uni-popup",{ref:"choosePaymentPopup",attrs:{type:"center","mask-click":!1}},[a("v-uni-view",{staticClass:"choose-payment-popup popup",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[t._v("支付方式")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close()}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":t.isIphoneX},attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"pay-money"},[a("v-uni-text",{staticClass:"money"},[t._v("支付金额"+t._s(t._f("moneyFormat")(t.payMoney))+"元")])],1),t.balanceDeduct>0&&1==t.balanceConfig&&t.sale?a("v-uni-view",{staticClass:"payment-item"},[a("v-uni-view",{staticClass:"iconfont icon-yue"}),a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-text",{staticClass:"name"},[t._v("余额抵扣")]),a("v-uni-view",{staticClass:"money"},[t._v("可用¥"+t._s(t.balanceDeduct))])],1),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==t.isBalance},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.useBalance.apply(void 0,arguments)}}})],1):t._e(),t.payMoney>0?[t.payTypeList.length?t._l(t.payTypeList,(function(e,i){return a("v-uni-view",{key:i,staticClass:"payment-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.payIndex=i}}},[a("v-uni-view",{staticClass:"iconfont",class:e.icon}),a("v-uni-text",{staticClass:"name"},[t._v(t._s(e.name))]),a("v-uni-text",{staticClass:"iconfont",class:t.payIndex==i?"icon-yuan_checked color-base-text":"icon-checkboxblank"})],1)})):[a("v-uni-view",{staticClass:"empty"},[t._v("平台尚未配置支付方式！")])]]:t._e()],2),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":t.isIphoneX}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm()}}},[t._v("确认支付")])],1)],1)],1)],1)},s=[]},"7aec":function(t,e,a){"use strict";a.r(e);var i=a("7258"),o=a("2a87");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("67dd");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"4252f8a9",null,!1,i["a"],void 0);e["default"]=r.exports},"7c21":function(t,e,a){var i=a("2523");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("1ca7a40c",i,!0,{sourceMap:!1,shadowMode:!1})},"7e0a":function(t,e,a){"use strict";a.r(e);var i=a("171a"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},"8e70":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */uni-text[data-v-55c689f0],\r\nuni-view[data-v-55c689f0]{font-size:%?24?%}.bottom-safe-area[data-v-55c689f0]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?10?%)!important;padding-bottom:calc(env(safe-area-inset-bottom) + %?10?%)!important}.align-right[data-v-55c689f0]{text-align:right}.color-text-white[data-v-55c689f0]{color:#fff}.detail-container .height-box[data-v-55c689f0]{display:block;padding-bottom:%?100?%}.detail-container.safe-area .height-box[data-v-55c689f0]{display:block;padding-bottom:%?168?%}.status-wrap[data-v-55c689f0]{background-size:100% 100%;padding:%?40?%;height:%?180?%;position:relative}.status-wrap uni-image[data-v-55c689f0]{width:%?104?%;height:%?86?%;margin-right:%?20?%;margin-top:%?20?%}.status-wrap .order-status-left[data-v-55c689f0]{display:flex}.status-wrap .order-time[data-v-55c689f0]{position:absolute;top:%?70?%;right:%?30?%;display:flex;align-items:center;font-size:10px;color:#fff}.status-wrap .order-time uni-image[data-v-55c689f0]{width:%?26?%;height:%?26?%;margin-right:%?6?%}.status-wrap > uni-view[data-v-55c689f0]{text-align:center;color:#fff}.status-wrap .desc[data-v-55c689f0]{margin-left:%?20?%}.status-wrap .price[data-v-55c689f0]{font-weight:600}.status-wrap .action-group[data-v-55c689f0]{text-align:center;padding-top:%?20?%}.status-wrap .action-group .action-btn[data-v-55c689f0]{line-height:1;padding:%?16?% %?50?%;display:inline-block;border-radius:%?10?%;background:#fff;box-shadow:0 0 %?14?% hsla(0,0%,62%,.6)}[data-v-55c689f0] #action-date .uni-countdown .uni-countdown__number{border:none!important;padding:0!important;margin:0!important;background:transparent!important}.address-wrap[data-v-55c689f0]{margin:%?20?% %?24?%;padding:%?30?%;border-radius:%?16?%;background:#fff;position:relative;min-height:%?100?%;margin-top:%?-69?%}.address-wrap .icon[data-v-55c689f0]{position:absolute;top:%?20?%;margin-right:%?20?%}.address-wrap .icon uni-image[data-v-55c689f0]{width:%?60?%;height:%?60?%}.address-wrap .icon .iconfont[data-v-55c689f0]{line-height:%?50?%;font-size:%?28?%}.address-wrap .icon .icon-mendian[data-v-55c689f0]{font-size:%?32?%}.address-wrap .address-info[data-v-55c689f0]{padding-left:%?40?%}.address-wrap .address-info .info[data-v-55c689f0]{display:flex;line-height:1;color:#333}.address-wrap .address-info .detail[data-v-55c689f0]{line-height:1.3;color:#333;margin-top:%?20?%}.address-wrap .store-info[data-v-55c689f0]{padding-left:%?100?%}.address-wrap .cell-more[data-v-55c689f0]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);right:%?10?%}.address-wrap .cell-more .iconfont[data-v-55c689f0]{color:#999}.pickup-info[data-v-55c689f0]{margin:%?20?% %?24?%;padding:%?30?%;border-radius:%?16?%;background:#fff;position:relative}.pickup-info .pickup-point-info .name[data-v-55c689f0]{display:flex;height:%?50?%;align-items:flex-end;margin-bottom:10px}.pickup-info .pickup-point-info .name uni-text[data-v-55c689f0]{line-height:1}.pickup-info .pickup-point-info .name uni-text.mark[data-v-55c689f0]{font-size:%?20?%;padding:1px %?10?%;border:.5px solid #fff;border-radius:%?4?%;margin-left:%?10?%}.pickup-info .pickup-point-info .address[data-v-55c689f0],\r\n.pickup-info .pickup-point-info .time[data-v-55c689f0],\r\n.pickup-info .pickup-point-info .contact[data-v-55c689f0]{font-size:%?24?%;line-height:1;margin-top:%?16?%}.pickup-info .pickup-point-info .address .iconfont[data-v-55c689f0],\r\n.pickup-info .pickup-point-info .time .iconfont[data-v-55c689f0],\r\n.pickup-info .pickup-point-info .contact .iconfont[data-v-55c689f0]{color:#999;font-size:%?24?%;line-height:1;margin-right:%?10?%}.pickup-info .hr[data-v-55c689f0]{border-top:1px dashed #e5e5e5;margin:%?20?% 0}.pickup-info .pickup-code-info .info[data-v-55c689f0]{text-align:center}.pickup-info .pickup-code-info .code[data-v-55c689f0]{display:flex;flex-direction:column;align-items:center}.pickup-info .pickup-code-info .code uni-image.barcode[data-v-55c689f0]{width:%?360?%;height:auto;will-change:transform;margin-top:%?20?%}.pickup-info .pickup-code-info .code uni-image.qrcode[data-v-55c689f0]{width:%?240?%;height:auto;will-change:transform;margin-top:%?50?%}.pickup-info .pickup-code-info .copy[data-v-55c689f0]{font-size:%?24?%;display:inline-block;color:#666;background:#f7f7f7;line-height:1;padding:%?6?% %?14?%;margin-left:%?10?%;border-radius:%?10?%;border:.5px solid #666}.virtual-mobile-wrap[data-v-55c689f0]{margin:%?20?% %?24?%;padding:%?30?%;border-radius:%?16?%;background:#fff;position:relative;margin-top:%?-69?%;display:flex}.virtual-mobile-wrap uni-view[data-v-55c689f0]:nth-child(2){flex:1;text-align:right}.verify-code-wrap[data-v-55c689f0]{margin:%?20?% %?24?%;padding:%?30?%;border-radius:%?16?%;background:#fff;position:relative}.verify-code-wrap .wrap[data-v-55c689f0]{text-align:center;line-height:2}.verify-code-wrap .wrap .copy[data-v-55c689f0]{font-size:%?24?%;display:inline-block;color:#666;background:#f7f7f7;line-height:1;padding:%?6?% %?14?%;margin-left:%?10?%;border-radius:%?10?%;border:.5px solid #666}.verify-code-wrap .wrap .virtual-code[data-v-55c689f0]{font-weight:700}.verify-code-wrap .hr[data-v-55c689f0]{border-top:1px dashed #e5e5e5;margin:%?20?% 0}.verify-code-wrap .code[data-v-55c689f0]{display:flex;flex-direction:column;align-items:center;text-align:center}.verify-code-wrap .code uni-image.barcode[data-v-55c689f0]{width:%?400?%;margin-top:%?10?%}.verify-code-wrap .code uni-image.qrcode[data-v-55c689f0]{width:%?300?%;margin-top:%?50?%}.verify-code-wrap .code uni-text[data-v-55c689f0]{margin-top:%?20?%}.verify-info-wrap[data-v-55c689f0]{margin:%?20?% %?24?%;padding:%?30?%;border-radius:%?16?%;background:#fff;position:relative}.verify-info-wrap .head[data-v-55c689f0]{font-size:%?28?%;border-bottom:1px dashed #f7f7f7;line-height:1;padding:%?10?% 0 %?30?% 0}.verify-info-wrap .order-cell .tit[data-v-55c689f0]{font-size:%?28?%}.verify-info-wrap .order-cell .box uni-text[data-v-55c689f0]{font-size:%?28?%;font-weight:700}.verify-info-wrap .record-empty[data-v-55c689f0]{text-align:center;padding-top:%?30?%}.verify-info-wrap .record-item[data-v-55c689f0]{margin-bottom:%?40?%}.verify-info-wrap .record-item[data-v-55c689f0]:last-child{margin-bottom:0}.carmichael .order-cell .tit[data-v-55c689f0]{font-size:%?28?%}.carmichael .order-cell .box uni-text[data-v-55c689f0]{font-size:%?28?%;font-weight:400}.carmichael .order-cell .copy[data-v-55c689f0]{font-size:%?20?%;display:inline-block;background:#f7f7f7;line-height:1;padding:%?6?% %?10?%;margin-left:%?10?%;border-radius:%?10?%;border:%?2?% solid #d2d2d2}.site-wrap[data-v-55c689f0]{margin:%?20?% %?24?%;padding:%?30?%;border-radius:%?16?%;background:#fff;position:relative;padding:%?10?% %?30?%}.site-wrap .site-header[data-v-55c689f0]{display:flex;align-items:center}.site-wrap .site-header .icon-dianpu[data-v-55c689f0]{display:inline-block;line-height:1;margin-right:%?12?%;font-size:%?28?%}.site-wrap .site-body .goods-item[data-v-55c689f0]{padding-top:%?20?%;border-bottom:%?2?% solid #f7f7f7}.site-wrap .site-body .goods-item[data-v-55c689f0]:last-child{border-bottom:0}.site-wrap .site-body .goods-wrap[data-v-55c689f0]{margin-bottom:%?20?%;display:flex;position:relative}.site-wrap .site-body .goods-wrap[data-v-55c689f0]:last-of-type{margin-bottom:0}.site-wrap .site-body .goods-wrap .goods-img[data-v-55c689f0]{width:%?160?%;height:%?160?%;margin-right:%?20?%}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-55c689f0]{width:100%;height:100%;border-radius:%?10?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-55c689f0]{flex:1;position:relative;display:flex;flex-direction:column;justify-content:space-between;max-width:calc(100% - %?180?%)}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-55c689f0]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;font-weight:700}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section[data-v-55c689f0]{width:100%;line-height:1.3;display:flex;margin-top:%?20?%;align-items:center}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-55c689f0]{font-weight:700;font-size:%?20?%;color:var(--price-color)}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-55c689f0]{font-weight:700;font-size:%?24?%;margin-right:%?2?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-55c689f0]{flex:1;line-height:1.3}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-55c689f0]:last-of-type{text-align:right;font-weight:700}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view:last-of-type .iconfont[data-v-55c689f0]{line-height:1;font-size:%?24?%}.site-wrap .site-body .goods-wrap .goods-info .goods-card[data-v-55c689f0]{text-align:right}.site-wrap .site-body .goods-form .tit[data-v-55c689f0]{font-size:%?28?%;width:%?190?%}.site-wrap .site-body .goods-form .box[data-v-55c689f0]{padding-right:0}.site-wrap .site-body .goods-form .box uni-text[data-v-55c689f0]{font-size:%?28?%;word-wrap:break-word;word-break:break-all}.site-wrap .site-body .goods-form .box .copy[data-v-55c689f0]{font-size:%?20?%!important;display:inline-block;background:#f7f7f7;line-height:1;padding:%?6?% %?10?%;margin-left:%?20?%;border-radius:%?10?%;border:%?2?% solid #d2d2d2;float:right}.site-wrap .site-body .goods-action[data-v-55c689f0]{text-align:right;margin:%?20?% 0}.site-wrap .site-body .goods-action uni-navigator[data-v-55c689f0]{display:inline-block}.site-wrap .site-body .goods-action .order-box-btn[data-v-55c689f0]{height:%?48?%!important;line-height:%?48?%!important;font-size:%?24?%!important;display:inline-block;background:#fff;border:%?2?% solid #999;margin-left:%?10?%;box-sizing:initial}.order-cell[data-v-55c689f0]{display:flex;margin:%?20?% 0;align-items:center;background:#fff;line-height:%?40?%}.order-cell.remark[data-v-55c689f0]{align-items:flex-start!important}.order-cell .tit[data-v-55c689f0]{text-align:left}.order-cell .box[data-v-55c689f0]{flex:1;padding:0 %?20?%;line-height:inherit}.order-cell .box .textarea[data-v-55c689f0]{height:%?40?%}.order-cell .img-box[data-v-55c689f0]{display:flex;flex-wrap:wrap}.order-cell .img-box .img[data-v-55c689f0]{width:%?100?%;height:%?100?%;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-right:%?30?%;margin-bottom:%?30?%;position:relative;border-radius:%?10?%;line-height:1;overflow:hidden}.order-cell .img-box .img uni-image[data-v-55c689f0]{width:100%}.order-cell .iconfont[data-v-55c689f0]{color:#bbb;font-size:%?28?%}.order-cell .order-pay[data-v-55c689f0]{padding:0}.order-cell .order-pay uni-text[data-v-55c689f0]{display:inline-block;margin-left:%?6?%}.order-summary[data-v-55c689f0]{margin:%?20?% %?24?%;padding:%?30?%;border-radius:%?16?%;background:#fff;position:relative}.order-summary .order-cell[data-v-55c689f0]:first-child{margin-top:0}.order-summary .order-cell .tit[data-v-55c689f0]{font-size:%?28?%;width:%?190?%}.order-summary .order-cell .box[data-v-55c689f0]{display:flex;align-items:center}.order-summary .order-cell .box uni-text[data-v-55c689f0]{font-size:%?28?%;word-wrap:break-word;word-break:break-all}.order-summary .order-cell .copy[data-v-55c689f0]{white-space:nowrap;font-size:%?20?%!important;display:inline-block;background:#f7f7f7;line-height:1;padding:%?6?% %?10?%;margin-left:%?10?%;border-radius:%?10?%;border:%?2?% solid #d2d2d2}.order-summary .hr[data-v-55c689f0]{width:100%;height:%?2?%;background:#f7f7f7;margin-bottom:%?20?%}.order-money[data-v-55c689f0]{margin:%?20?% %?24?%;padding:%?30?%;border-radius:%?16?%;background:#fff;position:relative}.order-money .order-cell .tit[data-v-55c689f0]{font-size:%?28?%}.order-money .order-cell .box[data-v-55c689f0]{font-weight:600;padding:0;text-align:right}.order-money .order-cell .box uni-text[data-v-55c689f0]{font-size:%?28?%;font-weight:700}.order-money .order-cell .box > uni-text.color-text[data-v-55c689f0]{color:var(--price-color)}.order-money .order-cell .box .operator[data-v-55c689f0]{font-size:%?24?%;margin-right:%?6?%}.order-money .order-cell .box.align-right .color-base-text uni-text[data-v-55c689f0]{color:var(--price-color)}.kefu[data-v-55c689f0]{margin:%?20?% %?24?%;padding:%?30?%;border-radius:%?16?%;background:#fff;position:relative;margin:%?30?% 0 %?10?%;border-top:%?2?% solid #f7f7f7;padding-bottom:0;padding-top:%?30?%}.kefu > uni-view[data-v-55c689f0]{display:flex;justify-content:center;align-items:center}.kefu > uni-view .iconfont[data-v-55c689f0]{font-weight:700;margin-right:%?10?%;font-size:%?28?%;line-height:1}.kefu uni-button[data-v-55c689f0]{width:100%;border:none;z-index:1;padding:0;margin:0;background:none;height:%?50?%;line-height:%?50?%;display:flex;justify-content:center}.kefu uni-button[data-v-55c689f0]::after{border:none!important}.kefu uni-button .iconfont[data-v-55c689f0]{margin-right:%?10?%}.fixed-bottom-box[data-v-55c689f0]{height:%?80?%}.order-action[data-v-55c689f0]{text-align:right}.order-action .order-box-btn[data-v-55c689f0]{margin-right:%?30?%;margin-left:0;font-size:%?24?%;height:%?60?%;line-height:%?60?%;box-sizing:initial;min-width:%?60?%;text-align:center}.order-action .order-box-btn.color-base-bg[data-v-55c689f0]{color:var(--btn-text-color)}.order-action .order-box-btn[data-v-55c689f0]:last-child{margin-right:0}.status-name uni-view[data-v-55c689f0],\r\n.status-name uni-text[data-v-55c689f0]{font-size:%?32?%;color:#fff;line-height:1;margin-top:%?40?%;text-align:left}.status-name .desc[data-v-55c689f0]{font-size:%?24?%;margin:%?10?% 0 0 0}.head-nav[data-v-55c689f0]{width:100%;height:0}.head-nav.active[data-v-55c689f0]{padding-top:%?40?%}.head-return[data-v-55c689f0]{height:%?90?%;line-height:%?90?%;color:#fff;font-weight:600;font-size:%?32?%;position:relative;text-align:center}.head-return uni-text[data-v-55c689f0]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);left:%?20?%;display:inline-block;margin-right:%?10?%;font-size:%?32?%}.store-detail uni-view[data-v-55c689f0]{font-size:%?20?%}.store-wrap[data-v-55c689f0]{margin:%?20?% %?24?%;padding:%?30?%;border-radius:%?16?%;background:#fff;position:relative;margin-top:%?-76?%}.store-wrap .store-info[data-v-55c689f0]{display:flex;align-items:center;padding-left:%?50?%;position:relative}.store-wrap .store-info .icon[data-v-55c689f0]{left:0;position:absolute;top:%?4?%}.store-wrap .store-info .icon .iconfont[data-v-55c689f0]{line-height:%?50?%;font-size:%?28?%}.store-wrap .store-info .icon .icon-mendian[data-v-55c689f0]{font-size:%?32?%}.store-wrap .store-info .store-name[data-v-55c689f0]{display:flex}.store-wrap .store-info .store-name .name[data-v-55c689f0]{flex:1}.store-wrap .store-info .store-info-detail[data-v-55c689f0]{flex:1}.store-wrap .store-info .store-info-detail .store-detail uni-view[data-v-55c689f0]{font-size:%?24?%}.store-wrap .store-info .store-info-detail > uni-view[data-v-55c689f0]:first-of-type{font-size:%?26?%}.store-wrap .store-info .cell-more[data-v-55c689f0]{margin-left:%?50?%}.pick-block[data-v-55c689f0]{display:flex;align-items:center;margin-top:%?20?%;padding-top:%?20?%}.pick-block.first-pick-block[data-v-55c689f0]{border-top:%?2?% solid #f1f1f1}.pick-block uni-input[data-v-55c689f0],\r\n.pick-block .last-child[data-v-55c689f0]{flex:1;text-align:right;font-size:%?24?%}.sku[data-v-55c689f0]{display:flex;line-height:1;margin-top:%?10?%;margin-bottom:%?10?%}.goods-spec[data-v-55c689f0]{color:#838383;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.goods-num[data-v-55c689f0]{font-size:%?22?%;margin-left:%?20?%}.delivery-status[data-v-55c689f0]{line-height:1.3}.fixed-bottom[data-v-55c689f0]{width:100%;position:fixed;left:0;bottom:0;padding:%?10?% %?30?%;box-sizing:border-box;background:#fff;z-index:5}.order-cell.order-form .box[data-v-55c689f0]{display:block;padding-right:0}.order-cell.order-form .box .copy[data-v-55c689f0]{margin-left:%?20?%;float:right}',""]),t.exports=e},"9c4f":function(t,e,a){var i=a("bb37");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("1a51f0e1",i,!0,{sourceMap:!1,shadowMode:!1})},"9e59":function(t,e,a){var i=a("8e70");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("3b883b2d",i,!0,{sourceMap:!1,shadowMode:!1})},a985:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("5c47"),a("2c10"),a("bf0f"),a("2797"),a("5ef2"),a("dd2b");var o=i(a("d745")),s=i(a("b0ec")),n=a("edd0"),r={name:"ns-payment",components:{uniPopup:o.default,nsSwitch:s.default},props:{balanceDeduct:{type:[Number,String],default:""},isBalance:{type:Number,default:0},payMoney:{type:[Number,String],default:0}},data:function(){return{isIphoneX:!1,payIndex:0,payTypeList:[{name:"支付宝支付",icon:"icon-zhifubaozhifu-",type:"alipay"},{name:"微信支付",icon:"icon-weixin1",type:"wechatpay"}],timer:null,payInfo:{},balanceConfig:0,sale:!0}},created:function(t){var e=window.location.href,a=e.match("presale/order_list/order_list"),i=e.match("presale/order_detail/order_detail");(a||i)&&(this.sale=!1),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getPayType(),this.getBalanceConfig()},methods:{open:function(){this.$refs.choosePaymentPopup.open()},close:function(){this.$refs.choosePaymentPopup.close()},useBalance:function(){this.$emit("useBalance")},confirm:function(){0==this.payTypeList.length&&this.payMoney>0?this.$util.showToast({title:"请选择支付方式！"}):(uni.showLoading({title:"支付中...",mask:!0}),this.$refs.choosePaymentPopup.close(),this.$emit("confirm"),uni.setStorageSync("pay_flag",1))},getPayInfo:function(t){var e=this;this.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:t},success:function(t){t.code>=0&&t.data?(e.payInfo=t.data,2==e.payInfo["pay_status"]?e.$util.redirectTo("/pages_tool/pay/result",{code:e.payInfo.out_trade_no},"","redirectTo"):e.pay()):(e.$util.showToast({title:"未获取到支付信息！"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1500))}})},getBalanceConfig:function(){var t=this;this.$api.sendRequest({url:"/api/pay/getBalanceConfig",data:{},success:function(e){t.balanceConfig=e.data.balance_show}})},getPayType:function(){var t=this;this.$api.sendRequest({url:"/api/pay/type",success:function(e){0==e.code&&(""==e.data.pay_type?t.payTypeList=[]:t.payTypeList.forEach((function(a,i){-1==e.data.pay_type.indexOf(a.type)&&t.payTypeList.splice(i,1)})))}})},pay:function(){var t=this,e=this.payTypeList[this.payIndex];if(e){var a="";a="BlindboxGoodsOrderPayNotify"==this.payInfo.event?"/pages_promotion/blindbox/index?outTradeNo=":"/pages_tool/pay/result?code=",this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:this.payInfo.out_trade_no,pay_type:e.type,return_url:encodeURIComponent(this.$config.h5Domain+a+this.payInfo.out_trade_no)},success:function(a){if(uni.hideLoading(),a.code>=0)switch(e.type){case"alipay":if(t.$util.isWeiXin()){var i=encodeURIComponent(a.data.data);t.$util.redirectTo("/pages_tool/pay/wx_pay",{wx_alipay:i,out_trade_no:t.payInfo.out_trade_no},"","redirectTo")}else location.href=a.data.data,t.checkPayStatus();break;case"wechatpay":if(t.$util.isWeiXin()){if("ios"==uni.getSystemInfoSync().platform)var o=uni.getStorageSync("initUrl");else o=location.href;t.$api.sendRequest({url:"/wechat/api/wechat/jssdkconfig",data:{url:o},success:function(e){var i=new n.Weixin,o=a.data.data;i.init(e.data),i.pay({timestamp:o.timestamp?o.timestamp:o.timeStamp,nonceStr:o.nonceStr,package:o.package,signType:o.signType,paySign:o.paySign},(function(e){"chooseWXPay:ok"==e.errMsg?"BlindboxGoodsOrderPayNotify"==t.payInfo.event?t.$util.redirectTo("/pages_promotion/blindbox/index",{outTradeNo:t.payInfo.out_trade_no},"","redirectTo"):t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo"):t.$util.showToast({title:e.errMsg})}),(function(e){t.$util.showToast({title:"您已取消支付"}),setTimeout((function(){t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"redirectTo")}),2e3)}))}})}else location.href=a.data.url,t.checkPayStatus();break}else t.$util.showToast({title:a.message})},fail:function(e){uni.hideLoading(),t.$util.showToast({title:"request:fail"})}})}},checkPayStatus:function(){var t=this;this.timer=setInterval((function(){t.$api.sendRequest({url:"/api/pay/status",data:{out_trade_no:t.payInfo.out_trade_no},success:function(e){0==e.code?2==e.data.pay_status&&(clearInterval(t.timer),t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo")):clearInterval(t.timer)}})}),1e3)}},deactivated:function(){clearInterval(this.timer)}};e.default=r},b0ec:function(t,e,a){"use strict";a.r(e);var i=a("bf29"),o=a("7e0a");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("4c42");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"7e2a453e",null,!1,i["a"],void 0);e["default"]=r.exports},b6f2:function(t,e,a){"use strict";a.r(e);var i=a("5e08"),o=a("1f3b");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("0619");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"0f26514c",null,!1,i["a"],void 0);e["default"]=r.exports},bb37:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-countdown[data-v-45a7f114]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-45a7f114]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?24?%}.uni-countdown__splitor.day[data-v-45a7f114]{line-height:%?50?%}.uni-countdown__number[data-v-45a7f114]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;border:%?2?% solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=e},bb68:function(t,e,a){"use strict";var i=a("7c21"),o=a.n(i);o.a},be4e:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4626"),a("5ac7"),a("e838"),a("8f71"),a("bf0f"),a("5ef2"),a("5c47"),a("a1c1");var o=i(a("d745")),s=i(a("b0ec")),n=a("edd0"),r={name:"payment",components:{uniPopup:o.default,nsSwitch:s.default},props:{balanceUsable:{type:Boolean,default:!0}},data:function(){return{payIndex:0,payTypeList:[{name:"支付宝支付",icon:"icon-zhifubaozhifu-",type:"alipay"},{name:"微信支付",icon:"icon-weixin1",type:"wechatpay"},{name:"线下支付",icon:"icondiy icon-yuezhifu",type:"offlinepay"}],timer:null,payInfo:null,balanceConfig:0,sale:!0,isBalance:0,balance:0,resetPayComplete:!0,repeatFlag:!1}},created:function(t){this.getPayType(),this.balanceUsable&&this.getBalanceConfig()},computed:{balanceDeduct:function(){var t=0;return this.payInfo&&this.balance&&(t=this.balance>this.payInfo.pay_money?this.payInfo.pay_money:this.balance),t},payMoney:function(){var t=0;return this.payInfo&&(t=this.payInfo.pay_money,this.balanceDeduct&&this.isBalance&&this.balanceUsable&&(t=this.payInfo.pay_money-this.balanceDeduct)),t},offlineShow:function(){var t=getCurrentPages(),e=t[t.length-1],a=e.route;return!!this.$store.state.offlineWhiteList.length&&this.$store.state.offlineWhiteList.includes(a)}},methods:{pageShow:function(){if(this.payInfo){var t=uni.getStorageSync("offlinepay");t&&(uni.removeStorageSync("offlinepay"),this.close())}else uni.removeStorageSync("offlinepay")},close:function(){this.$emit("close"),this.$refs.choosePaymentPopup.close()},useBalance:function(){this.isBalance=this.isBalance?0:1,this.$emit("useBalance",this.isBalance)},confirm:function(){0==this.payTypeList.length&&this.payMoney>0?this.$util.showToast({title:"请选择支付方式！"}):0!=this.resetPayComplete?(uni.showLoading({title:"支付中...",mask:!0}),this.repeatFlag||(this.repeatFlag=!0,this.pay(),uni.setStorageSync("pay_flag",1))):this.$util.showToast({title:"支付取消中，请稍后再试！"})},getPayInfo:function(t,e){var a=this;this.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:t},success:function(t){t.code>=0&&t.data?(a.payInfo=t.data,a.balanceConfig&&a.balanceUsable&&a.getMemberBalance(),setTimeout((function(){a.$refs.choosePaymentPopup.open(),"function"==typeof e&&e()}))):a.$util.showToast({title:"未获取到支付信息！"})}})},getBalanceConfig:function(){var t=this;this.$api.sendRequest({url:"/api/pay/getBalanceConfig",data:{},success:function(e){t.balanceConfig=e.data.balance_show}})},getMemberBalance:function(){var t=this;this.$api.sendRequest({url:"/api/memberaccount/usablebalance",success:function(e){0==e.code&&e.data&&(t.balance=parseFloat(e.data.usable_balance))}})},getPayType:function(){var t=this;this.$api.sendRequest({url:"/api/pay/type",success:function(e){0==e.code&&(""==e.data.pay_type?t.payTypeList=[]:t.payTypeList=t.payTypeList.filter((function(t,a){return-1!=e.data.pay_type.indexOf(t.type)})))}})},pay:function(){var t=this,e=this.payTypeList[this.payIndex],a="";a="BlindboxGoodsOrderPayNotify"==this.payInfo.event?"/pages_promotion/blindbox/index?outTradeNo=":"/pages_tool/pay/result?code=",this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:this.payInfo.out_trade_no,pay_type:e?e.type:"",return_url:encodeURIComponent(this.$config.h5Domain+a+this.payInfo.out_trade_no),is_balance:this.isBalance},success:function(a){if(uni.hideLoading(),a.code>=0){if(a.data.pay_success)return void t.paySuccess();switch(e.type){case"alipay":if(t.$util.isWeiXin()){var i=encodeURIComponent(a.data.data);t.$util.redirectTo("/pages_tool/pay/wx_pay",{wx_alipay:i,out_trade_no:t.payInfo.out_trade_no},"","redirectTo"),t.repeatFlag=!1}else t.repeatFlag=!1,location.href=a.data.data,t.checkPayStatus();break;case"wechatpay":if(t.$util.isWeiXin()){if("ios"==uni.getSystemInfoSync().platform)var o=uni.getStorageSync("initUrl");else o=location.href;t.$api.sendRequest({url:"/wechat/api/wechat/jssdkconfig",data:{url:o},success:function(e){var i=new n.Weixin,o=a.data.data;i.init(e.data),i.pay({timestamp:o.timestamp?o.timestamp:o.timeStamp,nonceStr:o.nonceStr,package:o.package,signType:o.signType,paySign:o.paySign},(function(e){"chooseWXPay:ok"==e.errMsg?(t.paySuccess(),t.repeatFlag=!1):(t.$util.showToast({title:e.errMsg}),setTimeout((function(){t.close(),t.repeatFlag=!1}),1500))}),(function(e){t.$util.showToast({title:"您已取消支付"}),t.resetpay(),t.repeatFlag=!1}))}})}else t.repeatFlag=!1,location.href=a.data.url,t.checkPayStatus();break;case"offlinepay":t.$util.redirectTo("/pages_tool/pay/offlinepay",{outTradeNo:t.payInfo.out_trade_no}),t.repeatFlag=!1;break}}else t.$util.showToast({title:a.message}),t.repeatFlag=!1},fail:function(e){uni.hideLoading(),t.$util.showToast({title:"request:fail"}),t.repeatFlag=!1}})},checkPayStatus:function(){var t=this;this.timer=setInterval((function(){t.$api.sendRequest({url:"/api/pay/status",data:{out_trade_no:t.payInfo.out_trade_no},success:function(e){0==e.code?2==e.data.pay_status&&(clearInterval(t.timer),t.paySuccess()):clearInterval(t.timer)}})}),1e3)},paySuccess:function(){"BlindboxGoodsOrderPayNotify"==this.payInfo.event?this.$util.redirectTo("/pages_promotion/blindbox/index",{outTradeNo:this.payInfo.out_trade_no},"redirectTo"):this.payInfo.return_url?-1!=this.payInfo.return_url.indexOf("http://")||-1!=this.payInfo.return_url.indexOf("https://")?location.replace(this.payInfo.return_url):this.$util.redirectTo(this.payInfo.return_url,{},"redirectTo"):this.$util.redirectTo("/pages_tool/pay/result",{code:this.payInfo.out_trade_no},"redirectTo")},resetpay:function(){var t=this;this.resetPayComplete=!1,this.$api.sendRequest({url:"/api/pay/resetpay",data:{out_trade_no:this.payInfo.out_trade_no},success:function(e){0==e.code?t.getPayInfo(e.data,(function(){t.resetPayComplete=!0})):t.resetPayComplete=!0},fail:function(e){t.resetPayComplete=!0}})}},deactivated:function(){clearInterval(this.timer)}};e.default=r},bf29:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("v-uni-view",{staticClass:"weui-switch",class:{"weui-switch-on":t.checked,"color-base-border":t.checked},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.change()}}},[a("v-uni-view",{staticClass:"bgview",class:{"color-base-bg":t.checked}}),a("v-uni-view",{staticClass:"spotview"})],1)],1)},o=[]},bfb8:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.popup[data-v-0f26514c]{width:75vw;background:#fff;border-top-left-radius:%?10?%;border-top-right-radius:%?10?%}.popup .popup-header[data-v-0f26514c]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-0f26514c]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-0f26514c]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-0f26514c]{height:calc(100% - %?250?%)}.popup .popup-body.safe-area[data-v-0f26514c]{height:calc(100% - %?270?%)}.popup .popup-footer[data-v-0f26514c]{height:%?100?%}.popup .popup-footer .confirm-btn[data-v-0f26514c]{height:%?72?%;line-height:%?72?%;color:#fff;text-align:center;margin:%?20?% %?30?% 0;border-radius:%?10?%}.popup .popup-footer.bottom-safe-area[data-v-0f26514c]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.choose-payment-popup .payment-item[data-v-0f26514c]{display:flex;align-items:center;justify-content:space-between;height:%?90?%;margin:0 %?30?%;border-bottom:%?2?% solid #eee;padding:%?20?% 0}.choose-payment-popup .payment-item[data-v-0f26514c]:nth-child(2){padding-top:0}.choose-payment-popup .payment-item[data-v-0f26514c]:last-child{border-bottom:none}.choose-payment-popup .payment-item .iconfont[data-v-0f26514c]{font-size:%?64?%}.choose-payment-popup .payment-item .icon-yue[data-v-0f26514c]{color:#faa218}.choose-payment-popup .payment-item .icon-weixin1[data-v-0f26514c]{color:#24af41}.choose-payment-popup .payment-item .icon-yuezhifu[data-v-0f26514c]{color:#f9a647}.choose-payment-popup .payment-item .icon-zhifubaozhifu-[data-v-0f26514c]{color:#00a0e9}.choose-payment-popup .payment-item .icon-checkboxblank[data-v-0f26514c]{font-size:%?40?%;color:#eee}.choose-payment-popup .payment-item .icon-yuan_checked[data-v-0f26514c]{font-size:%?40?%}.choose-payment-popup .payment-item .name[data-v-0f26514c]{margin-left:%?20?%;font-size:%?28?%;flex:1}.choose-payment-popup .payment-item .info-wrap[data-v-0f26514c]{flex:1;margin-left:%?20?%}.choose-payment-popup .payment-item .info-wrap .name[data-v-0f26514c]{margin-left:0;font-size:%?28?%;flex:1}.choose-payment-popup .payment-item .info-wrap .money[data-v-0f26514c]{color:#909399;font-size:%?24?%}.choose-payment-popup .payment-item .box[data-v-0f26514c]{flex:1;padding:0 %?10?%;line-height:inherit;text-align:right}.choose-payment-popup .payment-item .box uni-input[data-v-0f26514c]{font-size:%?24?%!important}.choose-payment-popup .payment-item.set-pay-password[data-v-0f26514c]{height:auto}.choose-payment-popup .payment-item.set-pay-password .box[data-v-0f26514c]{font-size:%?24?%!important}.choose-payment-popup .pay-money[data-v-0f26514c]{text-align:center;padding:%?20?% 0 %?40?% 0;background-color:#fff;font-weight:700;margin-top:%?30?%;line-height:1}.choose-payment-popup .pay-money .unit[data-v-0f26514c]{margin-right:%?4?%;font-size:%?24?%}.choose-payment-popup .pay-money .money[data-v-0f26514c]{font-size:%?32?%}.empty[data-v-0f26514c]{width:100%;text-align:center;padding:%?40?% 0;color:#606266;font-size:%?24?%}',""]),t.exports=e},c46e:function(t,e,a){var i=a("554a");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("95c2f08a",i,!0,{sourceMap:!1,shadowMode:!1})},cb69:function(t,e,a){"use strict";a.r(e);var i=a("e4a4"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},db18:function(t,e,a){"use strict";var i=a("6c0a"),o=a.n(i);o.a},e12a:function(t,e,a){"use strict";a.r(e);var i=a("00ba"),o=a("ea5a");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("11ac");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"45a7f114",null,!1,i["a"],void 0);e["default"]=r.exports},e4a4:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797"),a("aa9c"),a("e838");var o=i(a("7254")),s=i(a("2817")),n=i(a("b6f2")),r=i(a("5036")),c={data:function(){return{isIphoneX:!1,orderId:0,merchantTradeNo:"",orderData:{action:[],virtual_goods:{is_veirfy:0,verify_record:[]}},action:{icon:""},evaluateConfig:{evaluate_audit:1,evaluate_show:0,evaluate_status:1}}},mixins:[s.default],components:{nsGoodsRecommend:o.default,nsPayment:n.default,nsContact:r.default},onLoad:function(t){t.order_id&&(this.orderId=t.order_id),t.merchant_trade_no&&(this.merchantTradeNo=t.merchant_trade_no)},onPullDownRefresh:function(){this.getOrderData(),setTimeout((function(){uni.stopPullDownRefresh()}),50)},onShow:function(){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?(this.getEvaluateConfig(),this.getOrderData()):this.$util.redirectTo("/pages_tool/login/index",{back:"/pages/order/detail?order_id="+this.orderId+"&merchant_trade_no="+this.merchantTradeNo}),this.$refs.choosePaymentPopup&&this.$refs.choosePaymentPopup.pageShow()},methods:{goDetail:function(t){this.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})},goRefund:function(t){this.$util.redirectTo("/pages_tool/order/refund",{order_goods_id:t})},goRefundDetail:function(t){this.$util.redirectTo("/pages_tool/order/refund_detail",{order_goods_id:t})},getOrderData:function(){var t=this;this.$api.sendRequest({url:"/api/order/detail",data:{order_id:this.orderId,merchant_trade_no:this.merchantTradeNo},success:function(e){if(e.code>=0){if(0==e.data.order_status){var a=Date.parse(new Date)/1e3;e.data.closeTimeMachine=t.$util.countDown(e.data.create_time+e.data.auto_close-a)}var i=0;t.orderData=e.data,t.orderId=t.orderData.order_id;var o=[];t.orderData.order_goods.forEach((function(t){t.sku_spec_format?t.sku_spec_format=JSON.parse(t.sku_spec_format):t.sku_spec_format=[],0!=t.refund_status&&-1!=t.refund_status||(i+=1,o.push(t.order_goods_id))})),t.action=JSON.parse(e.data.order_status_action),i>1?(t.orderData.refund_batch_status=!0,t.orderData.refund_order_goods_ids=o):t.orderData.refund_batch_status=!1,""!=t.orderData.delivery_store_info&&(t.orderData.delivery_store_info=JSON.parse(t.orderData.delivery_store_info)),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}else t.$util.showToast({title:"未获取到订单信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/order/list")}),1500)},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},operation:function(t){var e=this;switch(t){case"orderDelete":this.orderDelete(this.orderData.order_id,(function(){setTimeout((function(){1==getCurrentPages().length?e.$util.redirectTo("/pages/member/index"):uni.navigateBack()}),500)}));break;case"orderPay":this.orderPay(this.orderData);break;case"orderClose":this.orderClose(this.orderData.order_id,(function(){e.getOrderData()}));break;case"memberTakeDelivery":this.orderDelivery(this.orderData,(function(){e.getOrderData()}));break;case"trace":this.$util.redirectTo("/pages_tool/order/logistics",{order_id:this.orderData.order_id});break;case"memberOrderEvaluation":this.$util.redirectTo("/pages_tool/order/evaluate",{order_id:this.orderData.order_id});break;case"memberBatchRefund":this.$util.redirectTo("/pages_tool/order/refund_type_select",{order_id:this.orderId});break;case"memberVirtualTakeDelivery":this.orderVirtualDelivery(this.orderData,(function(){e.getOrderData()}));break;case"orderOfflinePay":this.$util.redirectTo("/pages_tool/pay/offlinepay",{outTradeNo:this.orderData.out_trade_no});break}},imageError:function(t){this.orderData.order_goods[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},getEvaluateConfig:function(){var t=this;this.$api.sendRequest({url:"/api/goodsevaluate/config",success:function(e){if(0==e.code){var a=e.data;t.evaluateConfig=a}}})},openChoosePayment:function(){this.$refs.choosePaymentPopup.open()},previewMedia:function(t){var e=[];e.push(t),uni.previewImage({urls:e})}},filters:{abs:function(t){return Math.abs(parseFloat(t)).toFixed(2)}}};e.default=c},ea5a:function(t,e,a){"use strict";a.r(e);var i=a("f9fd"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},ee0a:function(t,e,a){"use strict";a.r(e);var i=a("2dda"),o=a("cb69");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("2519"),a("db18");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"55c689f0",null,!1,i["a"],void 0);e["default"]=r.exports},f9fd:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa");var i={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:[Number,String],default:0},hour:{type:[Number,String],default:0},minute:{type:[Number,String],default:0},second:{type:[Number,String],default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},mounted:function(t){var e=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},watch:{day:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},hour:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},minute:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},second:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,e,a,i){return t=Number(t),e=Number(e),a=Number(a),i=Number(i),60*t*60*24+60*e*60+60*a+i},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,a=0,i=0,o=0;t>0?(e=Math.floor(t/86400),a=Math.floor(t/3600)-24*e,i=Math.floor(t/60)-24*e*60-60*a,o=Math.floor(t)-24*e*60*60-60*a*60-60*i):this.timeUp(),e<10&&(e="0"+e),a<10&&(a="0"+a),i<10&&(i="0"+i),o<10&&(o="0"+o),this.d=e,this.h=a,this.i=i,this.s=o}}};e.default=i},ff56:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".pickup-code-info .code img[data-v-55c689f0]:nth-child(1){margin-bottom:%?30?%}[data-v-55c689f0] .sku-layer .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{max-height:unset!important}",""]),t.exports=e}}]);