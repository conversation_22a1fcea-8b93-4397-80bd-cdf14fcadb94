(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-contact"],{"00dc":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={uniPopup:n("d745").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"contact-wrap"},[t._t("default"),n("v-uni-button",{staticClass:"contact-button",attrs:{type:"default","hover-class":"none","open-type":t.openType,"send-message-title":t.sendMessageTitle,"send-message-path":t.sendMessagePath,"send-message-img":t.sendMessageImg,"show-message-card":!0},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.contactServicer.apply(void 0,arguments)}}}),n("uni-popup",{ref:"servicePopup",attrs:{type:"center"}},[n("v-uni-view",{staticClass:"service-popup-wrap"},[n("v-uni-view",{staticClass:"head-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.servicePopup.close()}}},[n("v-uni-text",[t._v("联系客服")]),n("v-uni-text",{staticClass:"iconfont icon-close"})],1),n("v-uni-view",{staticClass:"body-wrap"},[t._v(t._s(t.siteInfo.site_tel?"请联系客服，客服电话是"+t.siteInfo.site_tel:"抱歉，商家暂无客服，请线下联系"))])],1)],1)],2)},i=[]},"0457":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{}},onLoad:function(t){},onShow:function(){},methods:{}}},"1dc6":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"ns-contact",props:{niushop:{type:Object,default:function(){return{}}},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""}},data:function(){return{config:null,openType:""}},created:function(){this.servicerConfig&&(this.config=this.servicerConfig.h5)},methods:{contactServicer:function(){if("none"==this.config.type&&this.$refs.servicePopup.open(),"contact"!=this.openType)switch(this.config.type){case"wxwork":location.href=this.config.wxwork_url;break;case"third":location.href=this.config.third_url;break;case"niushop":this.$util.redirectTo("/pages_tool/chat/room",this.niushop);break;default:this.makePhoneCall()}},makePhoneCall:function(){this.$api.sendRequest({url:"/api/site/shopcontact",success:function(t){0==t.code&&t.data.mobile&&uni.makePhoneCall({phoneNumber:t.data.mobile})}})}}};e.default=o},2523:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.contact-wrap[data-v-142659c1]{width:100%;height:100%;position:relative}.contact-wrap .contact-button[data-v-142659c1]{width:100%;height:100%;position:absolute;left:0;top:0;z-index:5;padding:0;margin:0;opacity:0;overflow:hidden}.service-popup-wrap[data-v-142659c1]{width:%?600?%}.service-popup-wrap .head-wrap[data-v-142659c1]{display:flex;justify-content:space-between;align-items:center;padding:0 %?30?%;height:%?90?%}.service-popup-wrap .body-wrap[data-v-142659c1]{text-align:center;padding:%?30?%;height:%?100?%}',""]),t.exports=e},5036:function(t,e,n){"use strict";n.r(e);var o=n("00dc"),a=n("5323");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("bb68");var r=n("828b"),c=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"142659c1",null,!1,o["a"],void 0);e["default"]=c.exports},5323:function(t,e,n){"use strict";n.r(e);var o=n("1dc6"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},5892:function(t,e,n){"use strict";n.r(e);var o=n("c5d6"),a=n("ed9a");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("9d5e");var r=n("828b"),c=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"7d5b9b40",null,!1,o["a"],void 0);e["default"]=c.exports},"711c":function(t,e,n){var o=n("a02c");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("73378abd",o,!0,{sourceMap:!1,shadowMode:!1})},7854:function(t,e,n){"use strict";n.r(e);var o=n("8ba8"),a=n("f48d");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);var r=n("828b"),c=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=c.exports},"7c21":function(t,e,n){var o=n("2523");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("1ca7a40c",o,!0,{sourceMap:!1,shadowMode:!1})},"8ba8":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},"9d5e":function(t,e,n){"use strict";var o=n("711c"),a=n.n(o);a.a},a02c:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.contact[data-v-7d5b9b40]{width:80%;display:flex;flex-direction:column;align-items:center;text-align:center;margin:%?150?% auto 0}.contact uni-image[data-v-7d5b9b40]{width:%?500?%}.contact .tips[data-v-7d5b9b40]{font-size:%?24?%;color:#999;margin-bottom:%?20?%}',""]),t.exports=e},bb68:function(t,e,n){"use strict";var o=n("7c21"),a=n.n(o);a.a},c5d6:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={pageMeta:n("7854").default,nsContact:n("5036").default},a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":this.themeColor}}),e("v-uni-view",{staticClass:"contact"},[e("v-uni-image",{attrs:{src:this.$util.img("public/uniapp/member/contact_service.png"),mode:"widthFix"}}),e("v-uni-view",{staticClass:"tips"},[this._v("请点击下方按钮，联系客服")]),e("ns-contact",[e("v-uni-button",{attrs:{type:"primary"}},[this._v("联系客服")])],1)],1)],1)},i=[]},cc1b:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("64aa"),n("5c47"),n("a1c1"),n("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var n=function n(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",n),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",n)}})}}}};e.default=a},ed9a:function(t,e,n){"use strict";n.r(e);var o=n("0457"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},f48d:function(t,e,n){"use strict";n.r(e);var o=n("cc1b"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a}}]);