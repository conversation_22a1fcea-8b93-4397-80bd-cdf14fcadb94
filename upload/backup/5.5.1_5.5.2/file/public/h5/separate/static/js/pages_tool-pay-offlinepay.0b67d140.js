(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-pay-offlinepay"],{"07da5":function(t,e,i){"use strict";i.r(e);var a=i("84f1"),n=i("dba1");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("6d3d");var f=i("828b"),s=Object(f["a"])(n["default"],a["b"],a["c"],!1,null,"5e17d84f",null,!1,a["a"],void 0);e["default"]=s.exports},"40ce":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.offlinepay[data-v-5e17d84f]{width:100%;min-height:100vh;box-sizing:border-box;background:#f6f6f6;padding-top:%?20?%;padding-left:%?30?%;padding-right:%?30?%;padding-bottom:calc(%?164?% + constant(safe-area-inset-bottom))!important;padding-bottom:calc(%?164?% + env(safe-area-inset-bottom))!important}.offlinepay .pay-info[data-v-5e17d84f]{width:100%;padding:%?40?% %?30?%;background-color:#fff;border-radius:%?16?%;box-sizing:border-box}.offlinepay .pay-info .title[data-v-5e17d84f]{font-size:%?24?%;color:#666;line-height:%?34?%;font-weight:600;text-align:center}.offlinepay .pay-info .pay-price[data-v-5e17d84f]{width:100%;display:flex;justify-content:center;align-items:baseline;color:#ef000c;margin-top:%?6?%}.offlinepay .pay-info .pay-price .icon[data-v-5e17d84f]{line-height:%?33?%;font-weight:700;font-size:%?26?%}.offlinepay .pay-info .pay-price .price[data-v-5e17d84f]{line-height:%?59?%;font-weight:700;font-size:%?46?%}.offlinepay .pay-info .pay-time[data-v-5e17d84f]{display:flex;justify-content:center;align-items:center;margin-top:%?14?%}.offlinepay .pay-info .pay-time .text[data-v-5e17d84f]{line-height:%?36?%;font-weight:400;font-size:%?26?%;color:#666;margin-right:%?11?%}.offlinepay .pay-info .pay-time .time[data-v-5e17d84f]{width:%?34?%;height:%?34?%;background:#f0f0f3;border-radius:%?2?% %?2?% %?2?% %?2?%;font-weight:500;font-size:%?26?%;color:#333;line-height:%?34?%;text-align:center}.offlinepay .pay-info .pay-time .separator[data-v-5e17d84f]{font-weight:500;font-size:%?28?%;margin:0 %?10?%;height:%?34?%;line-height:%?28?%}.offlinepay .pay-type[data-v-5e17d84f]{margin-top:%?40?%}.offlinepay .pay-type .top[data-v-5e17d84f]{width:100%;display:flex;align-items:center;height:%?80?%;background:#f1f2f5;border-radius:%?16?% %?16?% %?0?% %?0?%}.offlinepay .pay-type .top .item[data-v-5e17d84f]{width:%?230?%;height:%?80?%;position:relative}.offlinepay .pay-type .top .item .center[data-v-5e17d84f]{width:100%;height:100%;position:absolute;left:0;bottom:0;z-index:9;line-height:%?80?%;text-align:center;font-weight:400;font-size:%?32?%}.offlinepay .pay-type .top .item .center.active[data-v-5e17d84f]{color:var(--base-color);font-weight:700}.offlinepay .pay-type .top .item .image[data-v-5e17d84f]{height:%?104?%;width:%?267?%;position:absolute;bottom:0;z-index:2}.offlinepay .pay-type .top .item .image.left[data-v-5e17d84f]{left:%?-2?%}.offlinepay .pay-type .top .item .image.center[data-v-5e17d84f]{width:%?315?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.offlinepay .pay-type .top .item .image.right[data-v-5e17d84f]{right:%?-2?%}.offlinepay .pay-type .bank[data-v-5e17d84f]{padding:%?30?%;background:#fff;border-radius:0 0 %?16?% %?16?%}.offlinepay .pay-type .bank .item[data-v-5e17d84f]{display:flex;align-items:center;margin-top:%?30?%}.offlinepay .pay-type .bank .item[data-v-5e17d84f]:first-of-type{margin-top:0!important}.offlinepay .pay-type .bank .item .label[data-v-5e17d84f]{line-height:%?36?%;font-weight:400;font-size:%?26?%;color:#666}.offlinepay .pay-type .bank .item .center[data-v-5e17d84f]{width:%?500?%;line-height:%?36?%;font-weight:500;font-size:%?26?%}.offlinepay .pay-type .bank .item .center .copy[data-v-5e17d84f]{color:var(--base-color);margin-left:%?20?%}.offlinepay .pay-type .code[data-v-5e17d84f]{padding:%?50?% 0;display:flex;justify-content:center;align-items:center;flex-direction:column;background:#fff;border-radius:0 0 %?16?% %?16?%}.offlinepay .pay-type .code .centent[data-v-5e17d84f]{width:%?360?%;height:%?360?%;border-radius:%?16?%;border:%?1?% solid #dedede;padding:%?30?%;box-sizing:border-box}.offlinepay .pay-type .code .centent .image[data-v-5e17d84f]{width:%?300?%;height:%?300?%}.offlinepay .pay-type .code .bottom[data-v-5e17d84f]{height:%?39?%;line-height:%?39?%;font-weight:500;font-size:%?28?%;margin-top:%?30?%}.offlinepay .pay-form[data-v-5e17d84f]{margin-top:%?20?%;padding:%?30?%;border-radius:%?16?%;background-color:#fff}.offlinepay .pay-form .title[data-v-5e17d84f]{line-height:%?33?%;font-weight:500;font-size:%?26?%;color:#333}.offlinepay .pay-form .image-list[data-v-5e17d84f]{display:flex;align-items:center;margin-top:%?30?%}.offlinepay .pay-form .image-list .image-info-box[data-v-5e17d84f]{width:%?110?%;height:%?110?%;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-left:%?20?%;position:relative;-ms-flex-negative:0;-webkit-flex-shrink:0;flex-shrink:0}.offlinepay .pay-form .image-list .image-info-box[data-v-5e17d84f]:first-of-type{margin-left:0!important}.offlinepay .pay-form .image-list .image-info-box uni-image[data-v-5e17d84f]{width:100%;border-radius:%?10?%}.offlinepay .pay-form .image-list .image-info-box .iconfont[data-v-5e17d84f]{font-size:%?60?%;color:#898989;line-height:1}.offlinepay .pay-form .image-list .image-info-box uni-text[data-v-5e17d84f]{line-height:1}.offlinepay .pay-form .image-list .image-info-box .imgDel[data-v-5e17d84f]{width:%?40?%;height:%?40?%;position:absolute;right:%?-20?%;top:%?-20?%;display:flex;justify-content:center;align-items:center}.offlinepay .pay-form .image-list .image-info-box .imgDel .iconfont[data-v-5e17d84f]{font-size:%?32?%}.offlinepay .pay-form .image-list .image-info-box.active[data-v-5e17d84f]{border:%?1?% dashed #898989}.offlinepay .pay-form .image-list .image-info-box.active[data-v-5e17d84f]:active{background:hsla(0,0%,80%,.6)}.offlinepay .pay-form .desc[data-v-5e17d84f]{margin-top:%?40?%;border-top:%?1?% dashed #898989;padding-top:%?20?%}.offlinepay .pay-form .desc .input[data-v-5e17d84f]{width:100%;font-weight:400;font-size:%?24?%}.offlinepay .pay-footer[data-v-5e17d84f]{position:fixed;display:flex;justify-content:space-between;align-items:center;left:0;right:0;bottom:0;z-index:10;padding:%?28?% %?30?%;padding-bottom:calc(%?28?% + constant(safe-area-inset-bottom))!important;padding-bottom:calc(%?28?% + env(safe-area-inset-bottom))!important;background:#f6f6f6}.offlinepay .pay-footer .back[data-v-5e17d84f]{width:%?220?%;height:%?88?%;line-height:%?82?%;font-size:%?32?%;font-weight:500;color:var(--base-color);background:#fff;border-radius:%?50?% %?50?% %?50?% %?50?%;border:%?3?% solid var(--base-color);box-sizing:border-box}.offlinepay .pay-footer .save[data-v-5e17d84f]{width:%?430?%;height:%?88?%;font-size:%?32?%;font-weight:500;color:#fff;background:var(--base-color);border-radius:%?50?% %?50?% %?50?% %?50?%}',""]),t.exports=e},"4be9":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("dc8a"),i("aa9c"),i("c223"),i("c9b5"),i("ab80"),i("dd2b");var a={data:function(){return{config:[],payInfo:{},offlinepayInfo:{out_trade_no:"",imgs:"",imgList:[],desc:""},actionStatus:"add",outTradeNo:"",time:null,activeIndex:0,repeat_flag:!1,routePath:""}},onLoad:function(t){this.outTradeNo=t.outTradeNo,this.offlinepayInfo.out_trade_no=t.outTradeNo,this.getOfflinepayConfig(),this.getOfflinepayPayInfo(this.outTradeNo),this.getPayInfo(this.outTradeNo);var e=getCurrentPages();e.length<1&&this.getroutePath(this.outTradeNo)},methods:{getOfflinepayConfig:function(){var t=this;this.$api.sendRequest({url:"/offlinepay/api/pay/config",success:function(e){if(e.code>=0&&e.data){var i=e.data.value;Object.keys(i).forEach((function(e){"1"==i[e].status&&(i[e].key=e,t.config.push(i[e]))}))}else t.$util.showToast({title:"未获取到支付配置！"})}})},getPayInfo:function(t){var e=this;this.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:t},success:function(t){t.code>=0&&t.data?(e.payInfo=t.data,e.payInfo.timestamp=t.timestamp,e.payInfo.timestamp<e.payInfo.auto_close_time&&(e.payInfo.time=e.$util.countDown(e.payInfo.auto_close_time-e.payInfo.timestamp),e.time=setInterval((function(){e.payInfo.timestamp>=e.payInfo.auto_close_time&&clearInterval(e.time),e.payInfo.timestamp+=1,e.payInfo.time=e.$util.countDown(e.payInfo.auto_close_time-e.payInfo.timestamp),e.$forceUpdate()}),1e3))):e.$util.showToast({title:"未获取到支付信息！"})}})},getOfflinepayPayInfo:function(t){var e=this;this.$api.sendRequest({url:"/offlinepay/api/pay/info",data:{out_trade_no:t},success:function(t){t.code>=0&&t.data&&(e.actionStatus="edit",e.offlinepayInfo=t.data,e.offlinepayInfo.imgList=e.offlinepayInfo.imgs?e.offlinepayInfo.imgs.split(","):[])}})},getroutePath:function(t){var e=this;this.$api.sendRequest({url:"/api/pay/outTradeNoToOrderDetailPath",data:{out_trade_no:t},success:function(t){t.code>=0&&t.data&&(e.routePath=t.data)}})},offlinepayTypeChange:function(t){this.activeIndex=t},copy:function(t){this.$util.copy(t)},addImg:function(){var t=this,e=this.offlinepayInfo.imgList.length;this.$util.upload(5-e,{path:""},(function(e){t.offlinepayInfo.imgList=t.offlinepayInfo.imgList.concat(e),t.offlinepayInfo.imgs=t.offlinepayInfo.imgList.toString()}),"/offlinepay/api/pay/uploadimg")},deleteImg:function(t){this.offlinepayInfo.imgList.splice(t,1),this.offlinepayInfo.imgs=this.offlinepayInfo.imgList.toString()},back:function(){var t=getCurrentPages();t.length>1?uni.navigateBack({delta:1}):this.$util.redirectTo(this.routePath,{},"redirectTo")},save:function(){this.repeat_flag||(this.offlinepayInfo.imgList.length?(this.repeat_flag=!0,this.saveSubmit()):uni.showToast({title:"请至少上传一张凭证",icon:"none"}))},saveSubmit:function(){var t=this;this.$api.sendRequest({url:"/offlinepay/api/pay/pay",data:this.offlinepayInfo,success:function(e){t.repeat_flag=!1,e.code>=0?(uni.setStorageSync("offlinepay","offlinepay"),t.back()):uni.showToast({title:e.message,icon:"none"})}})}}};e.default=a},"6d3d":function(t,e,i){"use strict";var a=i("9c89"),n=i.n(a);n.a},7854:function(t,e,i){"use strict";i.r(e);var a=i("8ba8"),n=i("f48d");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);var f=i("828b"),s=Object(f["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=s.exports},"84f1":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={pageMeta:i("7854").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"offlinepay"},[i("v-uni-view",{staticClass:"pay-info"},[i("v-uni-view",{staticClass:"title"},[t._v("实付金额")]),i("v-uni-view",{staticClass:"pay-price price-style"},[i("v-uni-text",{staticClass:"icon"},[t._v("￥")]),i("v-uni-text",{staticClass:"price"},[t._v(t._s(t.payInfo.pay_money?parseFloat(t.payInfo.pay_money).toFixed(2):"0.00"))])],1),"add"==t.actionStatus?i("v-uni-view",{staticClass:"pay-time"},[i("v-uni-view",{staticClass:"text"},[t._v("支付剩余时间")]),t.payInfo.time?[parseInt(t.payInfo.time.h)?i("v-uni-view",{staticClass:"time"},[t._v(t._s(t.payInfo.time.h))]):t._e(),parseInt(t.payInfo.time.h)?i("v-uni-view",{staticClass:"separator"},[t._v(":")]):t._e(),i("v-uni-view",{staticClass:"time"},[t._v(t._s(t.payInfo.time.i||"00"))]),i("v-uni-view",{staticClass:"separator"},[t._v(":")]),i("v-uni-view",{staticClass:"time"},[t._v(t._s(t.payInfo.time.s||"00"))])]:[i("v-uni-view",{staticClass:"time"},[t._v("00")]),i("v-uni-view",{staticClass:"separator"},[t._v(":")]),i("v-uni-view",{staticClass:"time"},[t._v("00")])]],2):t._e()],1),t.config.length?i("v-uni-view",{staticClass:"pay-type"},[i("v-uni-view",{staticClass:"top"},[t._l(t.config,(function(e,a){return[i("v-uni-view",{key:a+"_0",staticClass:"item",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.offlinepayTypeChange(a)}}},[i("v-uni-view",{staticClass:"center",class:{active:a===t.activeIndex}},[t._v(t._s("bank"==e.key?"银行卡":"wechat"==e.key?"微信支付":"支付宝"))]),t.activeIndex==a?[0==t.activeIndex?i("v-uni-image",{staticClass:"image left",attrs:{src:t.$util.img("public/uniapp/offlinepay/head_style_left.png")}}):t._e(),1==t.activeIndex?i("v-uni-image",{staticClass:"image center",attrs:{src:t.$util.img("public/uniapp/offlinepay/head_style_center.png")}}):t._e(),2==t.activeIndex?i("v-uni-image",{staticClass:"image right",attrs:{src:t.$util.img("public/uniapp/offlinepay/head_style_right.png")}}):t._e()]:t._e()],2)]}))],2),"bank"==t.config[t.activeIndex].key?i("v-uni-view",{staticClass:"bank"},[i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"label"},[t._v("银行名称：")]),i("v-uni-view",{staticClass:"center using-hidden"},[t._v(t._s(t.config[t.activeIndex].bank_name))])],1),i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"label"},[t._v("账号名称：")]),i("v-uni-view",{staticClass:"center using-hidden"},[t._v(t._s(t.config[t.activeIndex].account_name))])],1),i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"label"},[t._v("银行账号：")]),i("v-uni-view",{staticClass:"center using-hidden"},[i("v-uni-text",[t._v(t._s(t.config[t.activeIndex].account_number))]),i("v-uni-text",{staticClass:"copy",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.copy(t.config[t.activeIndex].account_number)}}},[t._v("复制")])],1)],1),i("v-uni-view",{staticClass:"item"},[i("v-uni-view",{staticClass:"label"},[t._v("开户支行：")]),i("v-uni-view",{staticClass:"center using-hidden"},[t._v(t._s(t.config[t.activeIndex].branch_name))])],1)],1):i("v-uni-view",{staticClass:"code"},[i("v-uni-view",{staticClass:"centent"},[i("v-uni-image",{staticClass:"image",attrs:{src:t.$util.img(t.config[t.activeIndex].payment_code)}})],1),i("v-uni-view",{staticClass:"bottom"},[t._v(t._s(t.config[t.activeIndex].account_name))])],1)],1):t._e(),t.config.length?i("v-uni-view",{staticClass:"pay-form"},[i("v-uni-view",{staticClass:"title"},[t._v("支付凭证（最多5张）")]),i("v-uni-view",{staticClass:"image-list"},[t._l(t.offlinepayInfo.imgList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"image-info-box"},[i("v-uni-image",{attrs:{src:t.$util.img(e),mode:"aspectFill"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.preview(a)}}}),i("v-uni-view",{staticClass:"imgDel",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteImg(a)}}},[i("v-uni-text",{staticClass:" icon iconfont icon-delete"})],1)],1)})),t.offlinepayInfo.imgList.length<5?i("v-uni-view",{staticClass:"image-info-box active",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addImg.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"icon iconfont icon-zhaoxiangji"}),i("v-uni-text",[t._v(t._s(t.offlinepayInfo.imgList.length?5-t.offlinepayInfo.imgList.length:0)+"/5")])],1):t._e()],2),i("v-uni-view",{staticClass:"desc"},[i("v-uni-textarea",{staticClass:"input",attrs:{"placeholder-style":"color:#999;font-weight: 400;font-size: 24rpx;",placeholder:"请详细说明您的支付情况",maxlength:200},model:{value:t.offlinepayInfo.desc,callback:function(e){t.$set(t.offlinepayInfo,"desc",e)},expression:"offlinepayInfo.desc"}})],1)],1):t._e(),t.config.length?i("v-uni-view",{staticClass:"pay-footer"},[i("v-uni-button",{staticClass:"back",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.back.apply(void 0,arguments)}}},[t._v("返回")]),i("v-uni-button",{staticClass:"save",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.save.apply(void 0,arguments)}}},[t._v("确定提交")])],1):t._e()],1)],1)},o=[]},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"9c89":function(t,e,i){var a=i("40ce");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("0b6b061e",a,!0,{sourceMap:!1,shadowMode:!1})},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=n},dba1:function(t,e,i){"use strict";i.r(e);var a=i("4be9"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},f48d:function(t,e,i){"use strict";i.r(e);var a=i("cc1b"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a}}]);