(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-game-cards"],{"015d":function(t,e,i){"use strict";i.r(e);var n=i("0f46"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"07bb":function(t,e,i){"use strict";i.r(e);var n=i("39bc"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=n},1851:function(t,e,i){"use strict";var n=i("8bdb"),a=i("84d6"),o=i("1cb5");n({target:"Array",proto:!0},{fill:a}),o("fill")},"39bc":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("fe6b"),i("64aa"),i("e966"),i("1851"),i("aa9c"),i("5c47");var a=n(i("d745")),o=n(i("7477")),r={components:{uniPopup:a.default,LTime:o.default},data:function(){return{ctxData:null,ctx:null,ctxW:0,ctxH:0,canvasX:0,canvasY:0,scaleRatio:1,showGuide:!0,id:0,gameInfo:{no_winning_desc:"",surplus_num:0},result:{is_winning:0},isClick:!1,point:0,animate:!1,scrollTimer:null,popState:!1,add_point:0,shareImg:""}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.cards||(e.$util.showToast({title:"商家未开启刮刮乐",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.id&&(this.id=t.id),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("id")&&(e.id=t.split("-")[1]),-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}this.getGameInfo()},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member")),this.storeToken&&this.getMemberPointInfo()},onShareAppMessage:function(t){var e=this.gameInfo.game_name,i=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),n=i.path;return{title:e,path:n,imageUrl:"",success:function(t){},fail:function(t){}}},watch:{storeToken:function(t,e){t&&(this.getMemberPointInfo(),this.getGameInfo())}},methods:{pxToRpx:function(t){var e=uni.getSystemInfoSync().screenWidth;return 750*Number.parseInt(t)/e},again:function(){this.$util.redirectTo("/pages_promotion/game/cards",{id:this.id},"redirectTo")},popChange:function(t){this.popState=t.show},getMemberPointInfo:function(){var t=this;this.$api.sendRequest({url:"/api/memberaccount/info",data:{account_type:"point"},success:function(e){e.data&&(t.point=parseInt(e.data.point))}})},createCtx:function(){this.ctx=uni.createCanvasContext("cardsCanvas",this),this.ctx.setFillStyle("#e5e5e5"),this.ctx.fillRect(0,0,this.ctxW,this.ctxH),this.ctx.globalCompositeOperation="destination-out",this.ctx.draw()},contentMove:function(t){t&&t.preventDefault(),this.add_point=0},touchMove:function(t){this.add_point=1,t.preventDefault();this.ctxData;this.ctx.beginPath(),this.ctx.arc(t.touches[0].x,t.touches[0].y,10*this.scaleRatio,0,2*Math.PI),this.ctx.globalCompositeOperation="destination-out",this.ctx.setFillStyle("#fff"),this.ctx.fill(),this.ctx.draw(!0)},touchend:function(t){},lottery:function(){var t=this;this.storeToken?1==this.gameInfo.status&&(this.gameInfo.surplus_num<=0?this.$util.showToast({title:"您的抽奖次数已用完"}):this.gameInfo.points>0&&this.point<this.gameInfo.points?this.$util.showToast({title:"积分不足"}):this.isClick||(this.isClick=!0,this.$api.sendRequest({url:"/cards/api/cards/lottery",data:{id:this.id},success:function(e){e.code>=0?(t.showGuide=!1,t.result=e.data,t.point-=t.gameInfo.points,t.gameInfo.surplus_num-=1):(t.$util.showToast({title:e.message}),t.isClick=!1)}}))):this.$refs.login.open("/pages_promotion/game/cards?id="+this.id)},getGameInfo:function(){var t=this;this.$api.sendRequest({url:"/cards/api/cards/info",data:{id:this.id},success:function(e){e.code>=0&&e.data?(t.gameInfo=e.data,t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.gameInfo.is_show_winner&&t.gameInfo.draw_record.length>6&&(t.scrollTimer=setInterval(t.scrollRecord,2e3))):(t.$util.showToast({title:"未获取到活动信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))},fail:function(e){t.$util.showToast({title:"未获取到活动信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500)}})},openRulePopup:function(){this.$refs.rulePopup.open()},closeRulePopup:function(){this.$refs.rulePopup.close()},scrollRecord:function(){var t=this;this.animate=!0,setTimeout((function(){t.gameInfo.draw_record.push(t.gameInfo.draw_record[0]),t.gameInfo.draw_record.shift(),t.animate=!1}),1e3)},reCards:function(){var t=this;if(!this.showGuide){Object.assign(this.$data,{ctxData:null,ctx:null,ctxW:0,ctxH:0,scaleRatio:1,showGuide:!0,gameInfo:{no_winning_desc:"",surplus_num:0},result:{is_winning:0},isClick:!1,point:0,animate:!1,scrollTimer:null}),this.getGameInfo(),this.storeToken&&this.getMemberPointInfo();var e=uni.createSelectorQuery().in(this);e.select("#canvas").boundingClientRect((function(e){t.ctxData=e,t.ctxW=e.width,t.ctxH=e.height,t.createCtx()})).exec()}}},onReady:function(){var t=this,e=uni.createSelectorQuery().in(this);e.select("#canvas").boundingClientRect((function(e){t.ctxData=e,t.ctxW=e.width,t.ctxH=e.height,t.createCtx()})).exec()},filters:{cover:function(t){return t.substr(0,1)+"******"+t.substr(-1)}},onHide:function(){clearInterval(this.scrollTimer)}};e.default=r},"3d65":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container[data-v-607b7c39]{width:100vw;min-height:100vh;background-image:linear-gradient(#f3623f,#f74222)}.add-point[data-v-607b7c39]{pointer-events:none}.head-wrap[data-v-607b7c39]{position:relative}.head-wrap uni-image[data-v-607b7c39]{width:100%;display:block}.head-wrap .rule-mark[data-v-607b7c39]{position:absolute;right:0;top:%?60?%;background-color:#ffe854;color:#ff7908;height:%?60?%;line-height:%?60?%;padding:0 %?30?%;border-top-left-radius:%?60?%;border-bottom-left-radius:%?60?%}.prize-area[data-v-607b7c39]{margin:0 auto;width:85%;position:relative;line-height:1;-webkit-transform:translateY(%?-420?%);transform:translateY(%?-420?%)}.prize-area uni-image[data-v-607b7c39]{width:100%}.prize-area .content-wrap[data-v-607b7c39]{position:absolute;width:%?500?%;height:%?300?%;z-index:4;left:%?70?%;top:%?210?%}.prize-area .content-wrap > uni-view[data-v-607b7c39]{position:relative;width:100%;height:100%}.prize-area .canvas-shade[data-v-607b7c39]{width:100%;height:100%;position:absolute;z-index:5;background:#e5e5e5}.prize-area .canvas[data-v-607b7c39]{width:100%;height:100%;position:absolute;z-index:6}.prize-area .result-wrap[data-v-607b7c39]{width:100%;height:100%;position:absolute;left:0;top:0;z-index:3;display:flex;align-items:center;justify-content:center;flex-direction:column;background-color:#fff5e9}.prize-area .result-wrap .title[data-v-607b7c39]{font-size:%?28?%;line-height:1}.prize-area .result-wrap .text[data-v-607b7c39]{font-size:%?52?%;font-weight:800;text-align:center;line-height:1;margin-top:%?20?%}.prize-area .result-wrap .tips[data-v-607b7c39]{font-size:%?24?%;color:#999;line-height:1;margin-top:%?20?%}.prize-area .guide-wrap[data-v-607b7c39]{width:100%;height:100%;position:absolute;left:0;top:0;z-index:10;display:flex;flex-direction:column;justify-content:center;align-items:center}.prize-area .guide-wrap .text[data-v-607b7c39]{letter-spacing:%?12?%;color:#999}.prize-area .guide-wrap .btn[data-v-607b7c39]{line-height:1;padding:%?22?% %?90?%;color:#fff;display:inline-block;border-radius:%?50?%;margin-top:%?10?%}.prize-area .guide-wrap .btn.disabled[data-v-607b7c39]{background-color:#ccc!important}.action-text[data-v-607b7c39]{margin:%?60?% %?50?% 0 %?50?%;display:flex;-webkit-transform:translateY(%?-420?%);transform:translateY(%?-420?%)}.action-text > uni-view[data-v-607b7c39]{flex:1;line-height:1}.action-text .point[data-v-607b7c39]{color:#fee331}.action-text .record[data-v-607b7c39]{color:#fff;text-align:right}.record-wrap[data-v-607b7c39]{-webkit-transform:translateY(%?-420?%);transform:translateY(%?-420?%);margin:%?80?% %?50?% %?30?% %?50?%;border-radius:%?10?%;background-color:#ff6e43;padding:%?12?%;box-shadow:0 .45em 0 #d92a00;position:relative;height:%?430?%}.record-wrap .head[data-v-607b7c39]{border:%?6?% solid #ff6e43;box-shadow:inset 0 0 %?10?% 0 #d92a00,0 .45em 0 #d92a00;background-color:#da2b00;position:absolute;z-index:5;text-align:center;width:%?300?%;height:%?70?%;line-height:%?70?%;border-radius:%?70?%;top:0;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);color:#fff;font-weight:600;letter-spacing:%?4?%}.record-wrap .body-shade[data-v-607b7c39]{width:calc(100% - %?24?%);height:%?60?%;top:%?12?%;left:%?12?%;background-color:#da2b00;position:absolute;z-index:2;border-radius:%?8?%}.record-wrap .body[data-v-607b7c39]{background-color:#da2b00;border-radius:%?8?%;height:%?340?%;box-shadow:inset 0 0 %?10?% 0 #d92a00;padding:%?60?% %?30?% %?30?% %?30?%;overflow:hidden}.record-wrap .body .wrap > uni-view[data-v-607b7c39]{display:flex}.record-wrap .body .wrap.animate[data-v-607b7c39]{transition:all 1s ease-in-out;-webkit-transform:translateY(%?-60?%);transform:translateY(%?-60?%)}.record-wrap .body .tit[data-v-607b7c39]{line-height:%?60?%;width:%?220?%;color:#fff}.record-wrap .body .txt[data-v-607b7c39]{line-height:%?60?%;flex:1;color:#fee331}.remain-box[data-v-607b7c39]{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.remain[data-v-607b7c39]{min-width:%?550?%;height:%?54?%;background:rgba(0,0,0,.1);line-height:%?54?%;border-radius:%?10?%;margin:0 auto;text-align:center;-webkit-transform:translateY(%?-390?%);transform:translateY(%?-390?%);color:#fff;padding:0 %?25?%;font-size:%?24?%}.rule-wrap[data-v-607b7c39]{border-radius:%?10?%;background-color:#ffd697;width:80vw;padding:%?12?%;box-sizing:border-box}.rule-wrap .content-wrap[data-v-607b7c39]{background-color:#fff2dd;width:100%;border-radius:%?8?%;position:relative}.rule-wrap .content-wrap .rule-head[data-v-607b7c39]{width:100%;position:absolute;-webkit-transform:translateY(-50%);transform:translateY(-50%);left:0;top:0}.rule-wrap .content-wrap .rule[data-v-607b7c39]{max-height:%?880?%;overflow:hidden;padding:%?80?% %?30?% 0 %?30?%;box-sizing:border-box}.rule-wrap .content-wrap .rule .tit[data-v-607b7c39]{font-weight:600;color:#da2b00;margin-top:%?10?%}.rule-wrap .content-wrap .rule .text[data-v-607b7c39]{font-size:%?26?%;color:#da2b00}.rule-wrap .content-wrap .icon-round-close[data-v-607b7c39]{color:#fff;text-align:center;position:absolute;bottom:%?-150?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);font-size:%?70?%}.warn[data-v-607b7c39]{margin-top:%?20?%;border-radius:%?20?%}',""]),t.exports=e},"6a089":function(t,e,i){"use strict";var n=i("6df0"),a=i.n(n);a.a},"6df0":function(t,e,i){var n=i("3d65");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("1164727c",n,!0,{sourceMap:!1,shadowMode:!1})},7362:function(t,e,i){var n=i("8205");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("4192cd57",n,!0,{sourceMap:!1,shadowMode:!1})},7477:function(t,e,i){"use strict";i.r(e);var n=i("7dc7"),a=i("aaa6");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"3f50a906",null,!1,n["a"],void 0);e["default"]=s.exports},"74f7":function(t,e,i){"use strict";var n=i("7362"),a=i.n(n);a.a},7854:function(t,e,i){"use strict";i.r(e);var n=i("8ba8"),a=i("f48d");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"7dc7":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-text",[this._v(this._s(this.temp))])},a=[]},8205:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,"[data-v-607b7c39] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:none!important}[data-v-607b7c39] .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box{overflow:unset}",""]),t.exports=e},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},9327:function(t,e,i){"use strict";var n=i("8bdb"),a=i("9f69"),o=i("1ded").f,r=i("c435"),s=i("9e70"),c=i("b6a1"),u=i("862c"),l=i("0931"),d=i("a734"),f=a("".slice),p=Math.min,v=l("endsWith"),h=!d&&!v&&!!function(){var t=o(String.prototype,"endsWith");return t&&!t.writable}();n({target:"String",proto:!0,forced:!h&&!v},{endsWith:function(t){var e=s(u(this));c(t);var i=arguments.length>1?arguments[1]:void 0,n=e.length,a=void 0===i?n:p(r(i),n),o=s(t);return f(e,a-o.length,a)===o}})},9851:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47"),i("0506"),i("a1c1"),i("23f4"),i("7d2f"),i("9c4e"),i("ab80"),i("bf0f"),i("c223"),Function.prototype.asyAfter=function(t){var e=this;return function(){var i=e.apply(this,arguments);return"next"===i?t.apply(this,arguments):i}},Date.prototype.pattern=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours()%12==0?12:this.getHours()%12,"H+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};for(var i in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length))),/(E+)/.test(t)&&(t=t.replace(RegExp.$1,(RegExp.$1.length>1?RegExp.$1.length>2?"星期":"周":"")+{0:"日",1:"一",2:"二",3:"三",4:"四",5:"五",6:"六"}[this.getDay()+""])),e)new RegExp("("+i+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?e[i]:("00"+e[i]).substr((""+e[i]).length)));return t};var n=function(){},a=n.prototype;a.getUnix=function(){return(new Date).getTime()},a.getTodayUnix=function(){var t=new Date,e="".concat(t.getFullYear(),"/").concat(t.getMonth()+1,"/").concat(t.getDate()," 00:00:00");return new Date(e).getTime()},a.getYearUnix=function(){var t=new Date;return t.setMonth(0),t.setDate(1),t.setHours(0),t.setMinutes(0),t.setSeconds(0),t.setMilliseconds(0),t.getTime()},a.getLastDate=function(t){if(t){var e=new Date(t);if(e.pattern)return e.pattern("yyyy-MM-dd");var i=e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1,n=e.getDate()<10?"0"+e.getDate():e.getDate();return e.getFullYear()+"-"+i+"-"+n}};var o=new RegExp("-","g");a.getFormatTime=function(t,e){if(!t)return"";switch(function(t){return/^\[object\s(.*)\]$/.exec(Object.prototype.toString.call(t))[1]}(t)){case"Date":t=t.getTime();break;case"String":t=t.replace(o,"/");break;default:t=new Date(t).getTime();break}var i=this.getUnix(),n=(this.getYearUnix(),(i-t)/1e3);if(t>i&&e)return this.getLastDate(t);return function(t,e){var i=function(t){return t<=0||Math.floor(t/60)<=0?"刚刚":"next"}.asyAfter((function(t){return t<3600?Math.floor(t/60)+"分钟前":"next"})).asyAfter((function(t,e){var i=r.getTodayUnix();return t>=3600&&e-i>=0?Math.floor(t/60/60)+"小时前":"next"})).asyAfter((function(t,e){var i=r.getTodayUnix();return t=(i-e)/1e3,t/86400<=31?Math.ceil(t/86400)+"天前":"next"})).asyAfter((function(t,e){return r.getLastDate(e)}));return i(t,e)}(n,t)};var r=new n,s=r;e.default=s},"9fb7":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={pageMeta:i("7854").default,uniPopup:i("d745").default,loadingCover:i("c003").default,nsLogin:i("2910").default,hoverNav:i("c1f1").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"container"},[i("v-uni-view",[i("v-uni-view",{staticClass:"head-wrap"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/game/cards_head.png"),mode:"widthFix"}}),i("v-uni-view",{staticClass:"rule-mark",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openRulePopup.apply(void 0,arguments)}}},[t._v("规则")])],1),i("v-uni-view",{staticClass:"prize-area"},[i("v-uni-view",{staticClass:"content-wrap"},[i("v-uni-view",[t.showGuide?i("v-uni-view",{staticClass:"guide-wrap"},[1==t.gameInfo.status?[i("v-uni-view",{staticClass:"text"},[t._v("刮开试试手气")]),i("v-uni-view",{staticClass:"btn color-base-bg",class:{disabled:0==t.gameInfo.surplus_num},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.lottery.apply(void 0,arguments)}}},[t._v("点我刮奖"+t._s("("+t.gameInfo.points+"积分)"))])]:0==t.gameInfo.status?i("v-uni-view",{staticClass:"text"},[t._v("活动尚未开始")]):i("v-uni-view",{staticClass:"text"},[t._v("活动已经结束")])],2):t._e(),i("v-uni-canvas",{staticClass:"canvas",class:{"add-point":1==t.add_point},attrs:{"disable-scroll":!0,"canvas-id":"cardsCanvas",id:"canvas"},on:{touchmove:function(e){arguments[0]=e=t.$handleEvent(e),t.touchMove.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"result-wrap",on:{touchmove:function(e){arguments[0]=e=t.$handleEvent(e),t.contentMove.apply(void 0,arguments)}}},[t.result.is_winning?[i("v-uni-view",{staticClass:"title"},[t._v("恭喜您中奖了")]),i("v-uni-view",{staticClass:"text color-base-text"},[t._v(t._s(t.result.award_name))]),i("v-uni-button",{staticClass:"warn",attrs:{type:"warn"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.again.apply(void 0,arguments)}}},[t._v("再刮一次")]),i("v-uni-view",{staticClass:"tips"},[t._v("可到“我的中奖记录”中查看")])]:[i("v-uni-view",{staticClass:"text color-base-text"},[t._v(t._s(t.gameInfo.no_winning_desc))]),i("v-uni-button",{staticClass:"warn",attrs:{type:"warn",id:"btn"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.again.apply(void 0,arguments)}}},[t._v("再刮一次")])]],2)],1)],1),i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/game/cards_bg.png"),mode:"widthFix"}})],1),i("v-uni-view",{staticClass:"action-text"},[i("v-uni-view",{staticClass:"point"},[t._v("我的积分："+t._s(t.point))]),i("v-uni-view",{staticClass:"record",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_promotion/game/record",{id:t.id})}}},[t._v("我的中奖记录")])],1),t.gameInfo.is_show_winner&&t.gameInfo.draw_record.length?i("v-uni-view",{staticClass:"record-wrap"},[i("v-uni-view",{staticClass:"body-shade"}),i("v-uni-view",{staticClass:"head"},[t._v("中奖名单")]),i("v-uni-view",{staticClass:"body"},[i("v-uni-view",{staticClass:"wrap",class:{animate:t.animate}},t._l(t.gameInfo.draw_record,(function(e,n){return i("v-uni-view",{key:n},[i("v-uni-view",{staticClass:"tit"},[t._v(t._s(t._f("cover")(e.member_nick_name)))]),i("v-uni-view",{staticClass:"txt"},[i("l-time",{attrs:{text:1e3*e.create_time}}),t._v("获得 "+t._s(e.award_name))],1)],1)})),1)],1)],1):t._e()],1),i("uni-popup",{ref:"rulePopup",attrs:{type:"center",maskClick:!1},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.popChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"rule-wrap"},[i("v-uni-view",{staticClass:"content-wrap"},[i("v-uni-image",{staticClass:"rule-head",attrs:{src:t.$util.img("public/uniapp/game/rule_head.png"),mode:"widthFix"}}),i("v-uni-scroll-view",{staticClass:"rule",attrs:{"scroll-y":"true"}},[i("v-uni-view",[i("v-uni-view",{staticClass:"tit"},[t._v("活动时间")]),i("v-uni-view",{staticClass:"text"},[t._v(t._s(t.$util.timeStampTurnTime(t.gameInfo.start_time))+" - "+t._s(t.$util.timeStampTurnTime(t.gameInfo.end_time)))]),i("v-uni-view",{staticClass:"tit"},[t._v("参与规则")]),0==t.gameInfo.join_type?i("v-uni-view",{staticClass:"text"},[t._v("每个用户活动期间共有"+t._s(t.gameInfo.join_frequency)+"次抽奖机会。")]):i("v-uni-view",{staticClass:"text"},[t._v("每个用户活动期间每天都有"+t._s(t.gameInfo.join_frequency)+"次抽奖机会，每天0点更新。")]),i("v-uni-view",{staticClass:"text"},[t._v("每次抽奖需消耗 "+t._s(t.gameInfo.points)+" 积分")]),0!=t.gameInfo.level_id?i("v-uni-view",{staticClass:"text"},[t._v("该活动只有"+t._s(t.gameInfo.level_name)+"等级的会员可参与。")]):t._e(),""!=t.gameInfo.remark?[i("v-uni-view",{staticClass:"tit"},[t._v("活动说明")]),i("v-uni-view",{staticClass:"text"},[t._v(t._s(t.gameInfo.remark))])]:t._e()],2)],1),i("v-uni-text",{staticClass:"iconfont icon-round-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeRulePopup.apply(void 0,arguments)}}})],1)],1)],1),i("loading-cover",{ref:"loadingCover"}),i("ns-login",{ref:"login"}),i("hover-nav")],1)],1)},o=[]},a725:function(t,e,i){"use strict";var n=i("ac2a"),a=i.n(n);a.a},aaa6:function(t,e,i){"use strict";i.r(e);var n=i("c2c7"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},ac2a:function(t,e,i){var n=i("f714");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("1a69ffc2",n,!0,{sourceMap:!1,shadowMode:!1})},c1f1:function(t,e,i){"use strict";i.r(e);var n=i("fa1d"),a=i("015d");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("a725");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"c1934e78",null,!1,n["a"],void 0);e["default"]=s.exports},c2c7:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("9327");var a=n(i("9851")),o={name:"l-time",props:{text:{type:[String,Number,Date],default:""},maxDate:{type:Boolean,default:!1}},data:function(){return{textVal:this.text}},watch:{text:function(){this.textVal=this.text}},computed:{temp:function(){return this.getText()}},methods:{getText:function(){var t=this,e=a.default.getFormatTime(t.textVal,t.maxDate);return e&&(e.endsWith("刚刚")||e.endsWith("分钟前"))&&setTimeout((function(){var e=t.textVal;t.textVal="",t.textVal=e}),6e4),this.textVal?e:""},onClick:function(){this.$emit("on-tap",this.textVal)}}};e.default=o},c2e5:function(t,e,i){"use strict";i.r(e);var n=i("9fb7"),a=i("07bb");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("6a089"),i("74f7");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"607b7c39",null,!1,n["a"],void 0);e["default"]=s.exports},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=a},f48d:function(t,e,i){"use strict";i.r(e);var n=i("cc1b"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f714:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},a=[]},fe6b:function(t,e,i){"use strict";var n=i("8bdb"),a=i("2c57");n({target:"Number",stat:!0,forced:Number.parseInt!==a},{parseInt:a})}}]);