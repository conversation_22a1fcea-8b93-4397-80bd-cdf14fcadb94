(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-giftcard-use_select"],{"0866":function(t,e,i){"use strict";i.r(e);var o=i("1f64"),n=i("6198");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("2802");var r=i("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,"6bd5caf8",null,!1,o["a"],void 0);e["default"]=s.exports},"1f64":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={pageMeta:i("7854").default,uniNumberBox:i("499c").default,loadingCover:i("c003").default,nsLogin:i("2910").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",[i("v-uni-view",{staticClass:"page"},["goods"==t.cardInfo.card_right_type?i("v-uni-view",{staticClass:"goods-list"},["item"==t.cardInfo.card_right_goods_type?i("v-uni-view",{staticClass:"goods-title"},[t._v("本礼品卡包含以下商品")]):t._e(),"all"==t.cardInfo.card_right_goods_type?i("v-uni-view",{staticClass:"goods-title"},[t._v("请在以下商品中任选"+t._s(t.cardInfo.card_right_goods_count)+"件")]):t._e(),t._l(t.cardInfo.card_goods_list,(function(e,o){return i("v-uni-view",{key:e.id,staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-image"},[i("v-uni-image",{attrs:{src:t.$util.img(e.sku_image),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(o)}}})],1),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.sku_name))]),i("v-uni-view",{staticClass:"goods-num"},[i("v-uni-text"),"to_use"==t.cardInfo.status?["item"==t.cardInfo.card_right_goods_type?i("v-uni-text",[t._v("x "+t._s(e.total_num))]):i("v-uni-view",[i("uni-number-box",{attrs:{min:t.min,max:t.max,value:e.total_num,size:"small"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.cartNumChange(e,o)}}})],1)]:t._e()],2)],1)],1)}))],2):t._e()],1),i("v-uni-view",{staticClass:"tab-bar-placeholder"}),"to_use"==t.cardInfo.status?i("v-uni-view",{staticClass:"btn"},[i("v-uni-button",{staticClass:"use-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toUse()}}},[t._v("确定")])],1):t._e(),i("loading-cover",{ref:"loadingCover"}),i("ns-login",{ref:"login"})],1)],1)},a=[]},2802:function(t,e,i){"use strict";var o=i("7914"),n=i.n(o);n.a},"36e1":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("dc8a"),i("aa9c"),i("d4b5");var o={data:function(){return{memberCardId:0,cardInfo:[],btnSwitch:!1,shopInfo:null,min:0,goodsList:[],max:0}},onLoad:function(t){var e=this;if(t.member_card_id&&(this.memberCardId=t.member_card_id),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("member_card_id")&&(e.memberCardId=t.split("-")[1])}))}uni.getStorageSync("shop_info")&&(this.shopInfo=JSON.parse(uni.getStorageSync("shop_info")))},onShow:function(){this.getData()},methods:{getData:function(){var t=this;this.$api.sendRequest({url:"/giftcard/api/membercard/detail",data:{member_card_id:this.memberCardId},success:function(e){e.code>=0&&e.data?(t.cardInfo=e.data,t.max=t.cardInfo.card_right_goods_count,t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:e.message,mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages_promotion/giftcard/not_exist")}),1500))},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},cartNumChange:function(t,e){this.cardInfo.card_goods_list[e]["total_num"]=t,this.getGoodsList()},getGoodsList:function(){var t=this,e=0;this.goodsList=[],Object.keys(this.cardInfo.card_goods_list).forEach((function(i){var o=t.cardInfo.card_goods_list[i];o.total_num>0&&(t.goodsList.push({sku_id:o.sku_id,num:o.total_num}),e+=o.total_num)})),this.cardInfo.card_right_goods_count-e<=0?this.max=0:this.max=this.cardInfo.card_right_goods_count},toUse:function(){var t=this;if("balance"==this.cardInfo.card_right_type)this.balanceUse();else{if(this.btnSwitch)return!1;var e={member_card_id:this.memberCardId};if("all"==this.cardInfo.card_right_goods_type){if(0==this.goodsList.length)return this.$util.showToast({title:"请选择商品"}),!1;if(this.max>0)return this.$util.showToast({title:"请选择"+this.cardInfo.card_right_goods_count+"件商品"}),!1;e.goods_sku_list=JSON.stringify(this.goodsList)}this.btnSwitch=!0,uni.setStorage({key:"giftcarduse",data:e,success:function(){t.$util.redirectTo("/pages_promotion/giftcard/card_use"),t.btnSwitch=!1}})}},balanceUse:function(){var t=this;uni.showModal({title:"提示",content:"您确定要使用该储值卡吗？",success:function(e){e.confirm&&t.$api.sendRequest({url:"/giftcard/api/carduse/balanceuse",data:{member_card_id:t.memberCardId},success:function(e){e.code>=0&&t.getData(),t.$util.showToast({title:e.message})}})}})},imageError:function(t){this.cardInfo.card_goods_list[t].sku_image=this.$util.getDefaultImage().goods}}};e.default=o},"499c":function(t,e,i){"use strict";i.r(e);var o=i("f7ad"),n=i("9b45");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("97dc");var r=i("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,"04ee6604",null,!1,o["a"],void 0);e["default"]=s.exports},6198:function(t,e,i){"use strict";i.r(e);var o=i("36e1"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},"74b8":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var o={name:"UniNumberBox",props:{value:{type:[Number,String],default:1},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},disabled:{type:Boolean,default:!1},inputDisabled:{type:Boolean,default:!1},size:{type:String,default:"default"}},data:function(){return{inputValue:0,initialValue:0,load:!0}},watch:{value:function(t){this.inputValue=+t}},created:function(){this.initialValue=+this.value,this.inputValue=+this.value},methods:{_calcValue:function(t){if(!this.disabled){var e=this._getDecimalScale(),i=this.inputValue*e,o=this.step*e;"minus"===t?i-=o:"plus"===t&&(i+=o),i<this.min&&"minus"===t||i>this.max&&"plus"===t?this.$emit("limit",{value:this.inputValue,type:t}):(this.inputValue=i/e,this.$emit("change",this.inputValue))}},_getDecimalScale:function(){var t=1;return~~this.step!==this.step&&(t=Math.pow(10,(this.step+"").split(".")[1].length)),t},_onInput:function(t){var e=this;setTimeout((function(){var i=t.detail.value;i=+i,i>e.max?(i=e.max,e.$util.showToast({title:"商品库存不足"})):i<e.min&&(e.$util.showToast({title:"商品最少购买"+e.min+"件"}),i=e.min),i||(i=1),e.inputValue=i,e.$forceUpdate(),e.$emit("change",i)}),0)}}};e.default=o},7854:function(t,e,i){"use strict";i.r(e);var o=i("8ba8"),n=i("f48d");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);var r=i("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=s.exports},7914:function(t,e,i){var o=i("db19");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("26302e10",o,!0,{sourceMap:!1,shadowMode:!1})},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"97dc":function(t,e,i){"use strict";var o=i("f964"),n=i.n(o);n.a},"9a0a":function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-numbox[data-v-04ee6604]{display:inline-flex;flex-direction:row;justify-content:flex-start;align-items:center;height:%?70?%;position:relative}.uni-numbox.small[data-v-04ee6604]{height:%?44?%}.uni-numbox[data-v-04ee6604]:after{content:"";position:absolute;-webkit-transform-origin:center;transform-origin:center;box-sizing:border-box;pointer-events:none;top:-50%;left:-50%;right:-50%;bottom:-50%;border-radius:%?12?%;-webkit-transform:scale(.5);transform:scale(.5)}.uni-numbox__minus[data-v-04ee6604],\r\n.uni-numbox__plus[data-v-04ee6604]{width:%?40?%;height:%?40?%;border-radius:50%;background-size:100% 100%;background-position:50%}.uni-numbox__value[data-v-04ee6604]{position:relative;background-color:#f8f8f8;width:%?80?%;height:%?40?%;text-align:center;border:1px solid #eee;display:inline-block;line-height:%?36?%;font-weight:700;margin:0;padding:0;vertical-align:top;min-height:0;border-left:none;border-right:none}.uni-numbox__value.small[data-v-04ee6604]{width:%?60?%;font-size:%?24?%}.uni-numbox__value[data-v-04ee6604]:after{content:"";position:absolute;-webkit-transform-origin:center;transform-origin:center;box-sizing:border-box;pointer-events:none;top:-50%;left:-50%;right:-50%;bottom:-50%;border-top-width:0;border-bottom-width:0;-webkit-transform:scale(.5);transform:scale(.5)}.uni-numbox--disabled[data-v-04ee6604]{color:silver}.uni-numbox uni-button[data-v-04ee6604]{width:%?40?%;height:%?40?%;display:inline-block;box-sizing:initial;border:1px solid #eee;padding:0;margin:0;border-radius:0;background-color:#fff;font-weight:700}.uni-numbox uni-button.disabled[data-v-04ee6604]{color:#eee;background-color:#f8f8f8!important}.uni-numbox uni-button.decrease[data-v-04ee6604]{font-size:%?44?%;line-height:%?32?%}.uni-numbox uni-button.increase[data-v-04ee6604]{font-size:%?32?%;line-height:%?36?%}',""]),t.exports=e},"9b45":function(t,e,i){"use strict";i.r(e);var o=i("74b8"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=n},db19:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.page[data-v-6bd5caf8]{background:#f6f9ff;min-height:100vh;padding:%?30?%}.goods-list[data-v-6bd5caf8]{background-color:#fff;padding:%?20?% %?24?%;border-radius:%?18?%;margin-bottom:%?20?%}.goods-list .goods-title[data-v-6bd5caf8]{text-align:center;width:100%;padding:%?10?% 0;margin-bottom:%?20?%;font-weight:700}.goods-item[data-v-6bd5caf8]{display:flex;margin-bottom:%?20?%;background:#fbf9fc;padding:%?20?%;border-radius:%?12?%}.goods-item .goods-image[data-v-6bd5caf8]{width:%?160?%;height:%?160?%;overflow:hidden;border-radius:%?18?%;margin-right:%?20?%}.goods-item .goods-image uni-image[data-v-6bd5caf8]{width:%?160?%;height:%?160?%;max-height:%?160?%}.goods-item .goods-info[data-v-6bd5caf8]{width:calc(100% - %?180?%)}.goods-item .goods-info .goods-name[data-v-6bd5caf8]{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;line-height:1.5;font-size:%?28?%;font-weight:700;height:%?84?%}.goods-item .goods-info .goods-num[data-v-6bd5caf8]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;margin-top:%?20?%}.goods-item .goods-info .goods-num uni-text[data-v-6bd5caf8]{color:#666;font-size:%?24?%}.goods-item .goods-info .goods-num uni-text[data-v-6bd5caf8]:last-child{color:#333;margin-top:%?35?%;font-weight:700}.btn[data-v-6bd5caf8]{display:flex;align-items:center;width:100%;justify-content:center;margin-top:%?40?%;position:fixed;bottom:0;left:0;background-color:#fff;padding:%?20?% %?30?%;box-sizing:border-box;padding-bottom:calc(constant(safe-area-inset-bottom) + %?20?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?20?%);z-index:10}.btn uni-button[data-v-6bd5caf8]{width:100%;margin:0}.btn uni-button[data-v-6bd5caf8]:nth-child(2){margin-left:%?20?%}.btn .give-btn[data-v-6bd5caf8]{background-color:var(--giftcard-promotion-aux-color);color:#fff}.btn .use-btn[data-v-6bd5caf8]{background-color:var(--giftcard-promotion-color)}.tab-bar-placeholder[data-v-6bd5caf8]{padding-bottom:%?120?%;padding-bottom:calc(constant(safe-area-inset-bottom) + %?120?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?120?%)}',""]),t.exports=e},f48d:function(t,e,i){"use strict";i.r(e);var o=i("cc1b"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},f7ad:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"uni-numbox",class:{small:"small"==t.size}},[i("v-uni-button",{staticClass:"decrease",class:{disabled:t.inputValue<=t.min||t.disabled,small:"small"==t.size},attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._calcValue("minus")}}},[t._v("-")]),i("v-uni-input",{staticClass:"uni-input uni-numbox__value",class:{small:"small"==t.size},attrs:{disabled:t.disabled||t.inputDisabled,type:"number"},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t._onInput.apply(void 0,arguments)}},model:{value:t.inputValue,callback:function(e){t.inputValue=e},expression:"inputValue"}}),i("v-uni-button",{staticClass:"increase",class:{disabled:t.inputValue>=t.max||t.disabled,small:"small"==t.size},attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._calcValue("plus")}}},[t._v("+")])],1)},n=[]},f964:function(t,e,i){var o=i("9a0a");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("bcc1b586",o,!0,{sourceMap:!1,shadowMode:!1})}}]);