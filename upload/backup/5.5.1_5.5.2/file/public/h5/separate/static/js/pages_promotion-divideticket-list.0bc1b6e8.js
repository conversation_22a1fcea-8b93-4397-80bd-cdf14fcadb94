(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-divideticket-list"],{"015d":function(t,e,i){"use strict";i.r(e);var n=i("0f46"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=n},"1eda":function(t,e,i){var n=i("3974");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("552ddb6e",n,!0,{sourceMap:!1,shadowMode:!1})},"356b":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={pageMeta:i("7854").default,nsEmpty:i("52a6").default,nsLogin:i("2910").default,hoverNav:i("c1f1").default,loadingCover:i("c003").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{class:t.isIphoneX?"iphone-x":""},[i("mescroll-uni",{ref:"mescroll",attrs:{top:"20"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getMemberCouponList.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[t.list.length>0?i("v-uni-view",{staticClass:"coupon-listone"},t._l(t.list,(function(e,n){return i("v-uni-view",{key:n,staticClass:"item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toGoodsList(e)}}},[i("v-uni-view",{staticClass:"item-left"},[i("v-uni-view",{staticClass:"item-flex"},[i("v-uni-view",{staticClass:"item-base"},[""!=e.image?i("v-uni-image",{attrs:{src:t.$util.img(e.image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(n)}}}):i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/divideticket/coupon_list_img.png"),mode:"aspectFit"}})],1),i("v-uni-view",{staticClass:"item-info"},[i("v-uni-view",{staticClass:"use_name"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"use_title"},[t._v(t._s(e.divide_num)+"名好友瓜分"+t._s(t._f("int")(e.money))+"元优惠券")]),e.validity_type?i("v-uni-view",{staticClass:"use_time"},[t._v("有效期：领取之日起"+t._s(e.fixed_term)+"日内有效")]):i("v-uni-view",{staticClass:"use_time"},[t._v("有效期："+t._s(t.$util.timeStampTurnTime(e.end_time)))])],1)],1)],1),i("v-uni-view",{staticClass:"item-right"},[i("v-uni-view",{staticClass:"use_price"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(parseFloat(e.money).toFixed(2).split(".")[0])),i("v-uni-text",[t._v("."+t._s(parseFloat(e.money).toFixed(2).split(".")[1]))])],1),2==e.g_status?i("v-uni-view",{staticClass:"tag",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.toGoodsList(e)}}},[t._v("去瓜分")]):t._e(),1==e.g_status?i("v-uni-view",{staticClass:"tag",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.toGoodsList(e)}}},[t._v("去查看")]):t._e(),0==e.g_status?i("v-uni-view",{staticClass:"tag",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.toGoodsList(e)}}},[t._v("组队中")]):t._e()],1)],1)})),1):i("v-uni-view",[i("ns-empty",{attrs:{isIndex:!1,text:"暂无数据"}})],1)],1)],2),i("ns-login",{ref:"login"}),i("hover-nav"),i("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},3974:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.cart-empty[data-v-76293391]{margin-top:104px!important}.active[data-v-76293391]{border-bottom:%?4?% solid}.coupon-head[data-v-76293391]{display:flex;background:#fff;padding:%?20?% %?50?%}.coupon-head .sort[data-v-76293391]{border:1px solid #c5c5c5;padding:%?1?% %?20?%;border-radius:%?50?%;cursor:pointer;margin-right:%?15?%}.cf-container[data-v-76293391]{background:#fff;overflow:hidden}.tab[data-v-76293391]{display:flex;justify-content:space-between;height:%?86?%}.tab > uni-view[data-v-76293391]{text-align:center;width:33%;height:%?86?%}.tab > uni-view uni-text[data-v-76293391]{display:inline-block;line-height:%?86?%;height:%?80?%;font-size:%?30?%}.coupon-listone[data-v-76293391]{margin:0 %?30?%}.coupon-listone .item[data-v-76293391]{display:flex;flex-direction:column;background-color:#fff;background-size:100% 100%;border-radius:%?20?%;align-items:stretch;margin-top:%?20?%;overflow:hidden;padding:%?20?%}.coupon-listone .item .item-left[data-v-76293391]{margin-bottom:%?30?%}.coupon-listone .item .item-left .use_name[data-v-76293391]{font-weight:700;width:%?460?%;word-break:break-word;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.coupon-listone .item .item-left .item-flex[data-v-76293391]{display:flex;align-items:center}.coupon-listone .item .item-left .use_time[data-v-76293391]{font-size:%?24?%;color:#909399}.coupon-listone .item .item-left .item-base[data-v-76293391]{width:%?160?%;height:%?160?%;border-radius:%?10?%}.coupon-listone .item .item-left .item-base uni-image[data-v-76293391]{width:100%;height:100%;border-radius:%?10?%}.coupon-listone .item .item-left .item-info[data-v-76293391]{margin-left:%?20?%;overflow:hidden;background-repeat-x:no-repeat;background-repeat-y:repeat}.coupon-listone .item .item-left .item-info .use_title[data-v-76293391]{font-size:%?24?%;max-width:%?330?%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;color:#606266}.coupon-listone .item .item-left .item-info .use_title .max_price[data-v-76293391]{font-weight:400;font-size:%?24?%}.coupon-listone .item .item-left .item-info .use_type[data-v-76293391]{max-width:%?330?%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;border-radius:%?5?%;background-color:#f98}.coupon-listone .item .item-right[data-v-76293391]{padding-top:%?20?%;display:flex;justify-content:space-between;border-top:1px solid #eee}.coupon-listone .item .item-right .use_price[data-v-76293391]{font-weight:700;font-size:%?36?%;color:var(--price-color)}.coupon-listone .item .item-right .use_price uni-text[data-v-76293391]{font-size:%?28?%;margin-right:%?10?%;font-weight:700}.coupon-listone .item .item-right .tag[data-v-76293391]{height:%?50?%;border-radius:%?10?%;line-height:%?50?%;padding:0 19.5px;text-align:center;background:linear-gradient(90deg,var(--bg-color),var(--bg-color-shallow));color:#fff;font-size:%?24?%}.coupon-listone .item .item-right .tag.disabled[data-v-76293391]{background:#eee!important;color:#909399!important}',""]),t.exports=e},4262:function(t,e,i){"use strict";var n=i("1eda"),o=i.n(n);o.a},"5c0d":function(t,e,i){"use strict";i.r(e);var n=i("c29f"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},7854:function(t,e,i){"use strict";i.r(e);var n=i("8ba8"),o=i("f48d");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},9286:function(t,e,i){"use strict";i.r(e);var n=i("356b"),o=i("5c0d");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("4262");var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"76293391",null,!1,n["a"],void 0);e["default"]=s.exports},a725:function(t,e,i){"use strict";var n=i("ac2a"),o=i.n(n);o.a},ac2a:function(t,e,i){var n=i("f714");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("1a69ffc2",n,!0,{sourceMap:!1,shadowMode:!1})},c1f1:function(t,e,i){"use strict";i.r(e);var n=i("fa1d"),o=i("015d");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("a725");var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"c1934e78",null,!1,n["a"],void 0);e["default"]=s.exports},c29f:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("e966"),i("c223");var n={data:function(){return{type:"",types:"",state:1,sort:1,list:[],isIphoneX:!1,showEmpty:!1,mpShareData:null}},onLoad:function(t){var e=this;if(this.isIphoneX=this.$util.uniappIsIPhoneX(),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}},filters:{int:function(t){var e=String(t),i=e.split(".");return parseInt(i[1])>0?e:i[0]}},onShow:function(){var t=this;setTimeout((function(){t.addonIsExist.divideticket||(t.$util.showToast({title:"商家未开启瓜分券",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member"))},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},methods:{getMemberCouponList:function(t){var e=this;this.showEmpty=!1,this.$api.sendRequest({url:"/divideticket/api/divideticket/lists",data:{page:t.num,page_size:t.size},success:function(i){e.showEmpty=!0;var n=[],o=i.message;0==i.code&&i.data?n=i.data.list:e.$util.showToast({title:o}),t.endSuccess(n.length),1==t.num&&(e.list=[]),e.list=e.list.concat(n);var a=i.data;a&&(e.couponList=a),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(i){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},imageError:function(t){this.list[t].image=this.$util.getDefaultImage().goods,this.$forceUpdate()},toGoodsList:function(t){this.$util.redirectTo("/pages_promotion/divideticket/index",{coupon_id:t.coupon_id})}}};e.default=n},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(o){o.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=o},f48d:function(t,e,i){"use strict";i.r(e);var n=i("cc1b"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},f714:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},o=[]}}]);