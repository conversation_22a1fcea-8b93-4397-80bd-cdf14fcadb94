(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-info~pages_tool-member-info_edit"],{"0209":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=2*uni.getSystemInfoSync().statusBarHeight+"rpx",n={name:"UniStatusBar",data:function(){return{statusBarHeight:i}}};t.default=n},"049b":function(e,t,a){"use strict";a.r(t);var i=a("3091"),n=a("a26a");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("d50d");var s=a("828b"),o=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"610bd3bc",null,!1,i["a"],void 0);t["default"]=o.exports},"2c8e":function(e,t,a){var i=a("9b91");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("a6bd5d92",i,!0,{sourceMap:!1,shadowMode:!1})},3091:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var i=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"uni-status-bar",style:{height:this.statusBarHeight}},[this._t("default")],2)},n=[]},4239:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("2634")),r=i(a("2fdc"));a("aa9c"),a("5c47"),a("a1c1"),a("c223"),a("5ef2");var s=i(a("fe8d")),o={data:function(){return{registerConfig:{},indent:"all",customNavTitle:"",formData:{userHeadImg:"",number:"",nickName:"",sex:"",realName:"",birthday:"",currentPassword:"",newPassword:"",confirmPassword:"",mobile:"",mobileVercode:"",mobileDynacode:"",mobileCodeText:"",username:"",provinceId:0,cityId:0,districtId:0,fullAddress:"",address:""},memberInfoformData:{userHeadImg:"",number:"",nickName:"",sex:"",realName:"",birthday:"",currentPassword:"",newPassword:"",confirmPassword:"",mobile:"",mobileVercode:"",mobileDynacode:"",mobileCodeText:""},langList:[],langIndex:0,seconds:120,timer:null,isSend:!1,captcha:{id:"",img:""},isIphoneX:!1,items:[{value:"0",name:"未知"},{value:"1",name:"男",checked:"true"},{value:"2",name:"女"}],current:0,memberConfig:{is_audit:0,is_enable:0},defaultRegions:[]}},onLoad:function(e){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.formData.mobileCodeText=this.$lang("findanimateCode"),e.back&&(this.back=e.back),this.getCaptcha(),e.action&&(this.indent=e.action,this.setNavbarTitle()),this.getRegisterConfig(),this.initLang(),this.getMemberConfig()},onShow:function(){var e=this;this.storeToken?this.getInfo():this.$nextTick((function(){e.$refs.login.open("/pages_tool/member/info")}))},onHide:function(){this.seconds=120,this.formData.mobileCodeText="获取动态码",this.isSend=!1,clearInterval(this.timer)},watch:{seconds:function(e){0==e&&(this.seconds=120,this.formData.mobileCodeText="获取动态码",this.isSend=!1,clearInterval(this.timer))}},computed:{startDate:function(){return this.getDate("start")},endDate:function(){return this.getDate("end")}},filters:{mobile:function(e){return e.substring(0,3)+"****"+e.substring(7)}},methods:{initLang:function(){if(this.langList=this.$langConfig.list(),uni.getStorageSync("lang")){for(var e=0;e<this.langList.length;e++)if(this.langList[e].value==uni.getStorageSync("lang")){this.langIndex=e;break}}else this.langIndex=0},setNavbarTitle:function(){var e="个人资料";switch(this.indent){case"name":e=this.$lang("modifyNickname");break;case"realName":e=this.$lang("realName");break;case"sex":e=this.$lang("sex");break;case"birthday":e=this.$lang("birthday");break;case"password":e=this.$lang("password");break;case"mobile":e=this.$lang("mobile");break}uni.setNavigationBarTitle({title:e})},getInfo:function(){this.memberInfoformData.userHeadImg=this.memberInfo.headimg,this.memberInfoformData.number=this.memberInfo.username,this.memberInfoformData.nickName=this.memberInfo.nickname,this.memberInfoformData.realName=this.memberInfo.realname?this.memberInfo.realname:"请输入真实姓名",this.memberInfoformData.sex=0==this.memberInfo.sex?"未知":1==this.memberInfo.sex?"男":"女",this.memberInfoformData.birthday=this.memberInfo.birthday?this.$util.timeStampTurnTime(this.memberInfo.birthday,"Y-m-d"):"请选择生日",this.memberInfoformData.mobile=this.memberInfo.mobile,this.formData.username=this.memberInfo.username,this.formData.nickName=this.memberInfo.nickname,this.formData.realName=this.memberInfo.realname,this.formData.sex=this.memberInfo.sex,this.formData.birthday=this.memberInfo.birthday?this.$util.timeStampTurnTime(this.memberInfo.birthday,"Y-m-d"):"",this.formData.provinceId=this.memberInfo.province_id,this.formData.cityId=this.memberInfo.city_id,this.formData.districtId=this.memberInfo.district_id,this.formData.fullAddress=this.memberInfo.full_address,this.formData.address=this.memberInfo.address,this.memberInfo.full_address&&(this.defaultRegions=[this.memberInfo.province_id,this.memberInfo.city_id,this.memberInfo.district_id])},modifyInfo:function(e){switch(e){case"cancellation":this.getCancelStatus();break;case"language":for(var t=[],a=0;a<this.langList.length;a++)t.push(this.langList[a].name);uni.showActionSheet({itemList:t,success:function(e){vm.langIndex!=e.tapIndex&&vm.$langConfig.change(vm.langList[e.tapIndex].value)}});break;case"mobile":this.$util.redirectTo("/pages_tool/member/info_edit",{action:e});break;default:this.$util.redirectTo("/pages_tool/member/info_edit",{action:e})}},getCancelStatus:function(){var e=this;this.$api.sendRequest({url:"/membercancel/api/membercancel/info",success:function(t){t.code>=0&&(t.data?0==t.data.status?e.$util.redirectTo("/pages_tool/member/cancelstatus",{back:"/pages_tool/member/info"}):1==t.data.status?e.$util.redirectTo("/pages_tool/member/cancelsuccess",{back:"/pages_tool/member/info"}):e.$util.redirectTo("/pages_tool/member/cancelrefuse",{back:"/pages_tool/member/info"}):e.$util.redirectTo("/pages_tool/member/cancellation",{back:"/pages_tool/member/info"}))}})},NavReturn:function(){uni.navigateBack({delta:1})},getCaptcha:function(){var e=this;this.$api.sendRequest({url:"/api/captcha/captcha",data:{captcha_id:this.captcha.id},success:function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))}})},logout:function(){var e=this;uni.showModal({title:"提示",content:"确定要退出登录吗",success:function(t){t.confirm&&(e.$store.commit("setToken",""),e.$store.commit("setMemberInfo",""),e.$store.dispatch("emptyCart"),e.$util.redirectTo("/pages/member/index"))}})},headImage:function(){this.$util.redirectTo("/pages_tool/member/modify_face")},testBinding:function(e){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.checkMobile();case 2:return a=e.sent,e.abrupt("return",a);case 4:case"end":return e.stop()}}),e)})))()},getMemberConfig:function(){var e=this;this.$api.sendRequest({url:"/membercancel/api/membercancel/config",success:function(t){t.code>=0&&(e.memberConfig=t.data)}})},save:function(e){switch(e){case"username":this.modifyUserName();break;case"name":this.modifyNickName();break;case"realName":this.modifyRealName();break;case"sex":this.modifySex();break;case"birthday":this.modifyBirthday();break;case"password":this.modifyPassword();break;case"mobile":this.modifyMobile();break;case"address":this.modifyAddress();break}},modifyUserName:function(){var e=this;if(this.formData.username!=this.memberInfo.username){var t=[{name:"username",checkType:"required",errorMsg:this.$lang("noEmityUsername")}];if(t.length){var a=s.default.check(this.formData,t);a?this.$api.sendRequest({url:"/api/member/modifyusername",data:{username:this.formData.username},success:function(t){0==t.code?(e.memberInfo.username=e.formData.username,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}}):this.$util.showToast({title:s.default.error})}}else this.$util.showToast({title:this.$lang("alikeusername")})},modifyNickName:function(){var e=this;if(this.formData.nickName!=this.memberInfo.nickname){var t=[{name:"nickName",checkType:"required",errorMsg:this.$lang("noEmityNickname")}];if(t.length){var a=s.default.check(this.formData,t);a?this.$api.sendRequest({url:"/api/member/modifynickname",data:{nickname:this.formData.nickName},success:function(t){0==t.code?(e.memberInfo.nickname=e.formData.nickName,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}}):this.$util.showToast({title:s.default.error})}}else this.$util.showToast({title:this.$lang("alikeNickname")})},modifyRealName:function(){var e=this;if(this.formData.realName==this.memberInfo.realname&&this.memberInfo.realname)this.$util.showToast({title:"与原真实姓名一致，无需修改"});else{var t=[{name:"realName",checkType:"required",errorMsg:"真实姓名不能为空"}];if(t.length){var a=s.default.check(this.formData,t);a?this.$api.sendRequest({url:"/api/member/modifyrealname",data:{realname:this.formData.realName},success:function(t){0==t.code?(e.memberInfo.realname=e.formData.realName,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}}):this.$util.showToast({title:s.default.error})}}},radioChange:function(e){for(var t=0;t<this.items.length;t++)if(this.items[t].value===e.target.value){this.formData.sex=t;break}},modifySex:function(){var e=this;this.$api.sendRequest({url:"/api/member/modifysex",data:{sex:this.formData.sex},success:function(t){0==t.code?(e.memberInfo.sex=e.formData.sex,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}})},bindDateChange:function(e){this.formData.birthday=e.target.value},getDate:function(e){var t=new Date,a=t.getFullYear(),i=t.getMonth()+1,n=t.getDate();return"start"===e?a-=60:"end"===e&&(a+=2),i=i>9?i:"0"+i,n=n>9?n:"0"+n,"".concat(a,"-").concat(i,"-").concat(n)},modifyBirthday:function(){var e=this;0!=this.formData.birthday.length?this.$api.sendRequest({url:"/api/member/modifybirthday",data:{birthday:this.$util.timeTurnTimeStamp(this.formData.birthday)},success:function(t){0==t.code?(e.memberInfo.birthday=e.$util.timeTurnTimeStamp(e.formData.birthday),e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}}):this.$util.showToast({title:"请选择生日"})},getRegisterConfig:function(){var e=this;this.$api.sendRequest({url:"/api/register/config",success:function(t){t.code>=0&&(e.registerConfig=t.data.value)}})},modifyPassword:function(){var e=this;if(this.memberInfo.password)var t=[{name:"currentPassword",checkType:"required",errorMsg:this.$lang("pleaseInputOldPassword")},{name:"newPassword",checkType:"required",errorMsg:this.$lang("pleaseInputNewPassword")}];else t=[{name:"mobileVercode",checkType:"required",errorMsg:this.$lang("confirmCodeInput")},{name:"mobileDynacode",checkType:"required",errorMsg:this.$lang("animateCodeInput")},{name:"newPassword",checkType:"required",errorMsg:this.$lang("pleaseInputNewPassword")}];var a=this.registerConfig;if(a.pwd_len>0&&t.push({name:"newPassword",checkType:"lengthMin",checkRule:a.pwd_len,errorMsg:"新密码长度不能小于"+a.pwd_len+"位"}),a.pwd_complexity){var i="密码需包含",n="";-1!=a.pwd_complexity.indexOf("number")&&(n+="(?=.*?[0-9])",i+="数字"),-1!=a.pwd_complexity.indexOf("letter")&&(n+="(?=.*?[a-z])",i+="、小写字母"),-1!=a.pwd_complexity.indexOf("upper_case")&&(n+="(?=.*?[A-Z])",i+="、大写字母"),-1!=a.pwd_complexity.indexOf("symbol")&&(n+="(?=.*?[#?!@$%^&*-])",i+="、特殊字符"),t.push({name:"newPassword",checkType:"reg",checkRule:n,errorMsg:i})}var r=s.default.check(this.formData,t);if(r){if(this.formData.currentPassword==this.formData.newPassword)return void this.$util.showToast({title:"新密码不能与原密码相同"});if(this.formData.newPassword!=this.formData.confirmPassword)return void this.$util.showToast({title:"两次密码不一致"});this.$api.sendRequest({url:"/api/member/modifypassword",data:{new_password:this.formData.newPassword,old_password:this.formData.currentPassword,code:this.formData.mobileDynacode,key:uni.getStorageSync("password_mobile_key")},success:function(t){0==t.code?(e.memberInfo.password=1,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn(),uni.removeStorageSync("password_mobile_key")):(e.$util.showToast({title:t.message}),e.getCaptcha())}})}else this.$util.showToast({title:s.default.error})},verifyMobile:function(){var e=s.default.check(this.formData,[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}]);return!!e||(this.$util.showToast({title:s.default.error}),!1)},checkMobile:function(){var e=this;return(0,r.default)((0,n.default)().mark((function t(){var a;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.verifyMobile()){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,e.$api.sendRequest({url:"/api/member/checkmobile",data:{mobile:e.formData.mobile},async:!1});case 4:if(a=t.sent,0==a.code){t.next=8;break}return e.$util.showToast({title:a.message}),t.abrupt("return",!1);case 8:return t.abrupt("return",!0);case 9:case"end":return t.stop()}}),t)})))()},bindMobileCode:function(){var e=this;return(0,r.default)((0,n.default)().mark((function t(){var a,i;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(120==e.seconds){t.next=2;break}return t.abrupt("return");case 2:a=[{name:"mobile",checkType:"phoneno",errorMsg:e.$lang("surePhoneNumber")},{name:"mobileVercode",checkType:"required",errorMsg:e.$lang("confirmCodeInput")}],i=s.default.check(e.formData,a),i&&!e.isSend?(e.isSend=!0,e.$api.sendRequest({url:"/api/member/bindmobliecode",data:{mobile:e.formData.mobile,captcha_id:e.captcha.id,captcha_code:e.formData.mobileVercode},success:function(t){var a=t.data;a.key?(120==e.seconds&&null==e.timer&&(e.timer=setInterval((function(){e.seconds--,e.formData.mobileCodeText="已发送("+e.seconds+"s)"}),1e3)),uni.setStorageSync("mobile_key",a.key)):(e.$util.showToast({title:t.message}),e.isSend=!1)},fail:function(t){e.isSend=!1,e.getCaptcha()}})):e.$util.showToast({title:s.default.error?s.default.error:"请勿重复点击"});case 5:case"end":return t.stop()}}),t)})))()},modifyMobile:function(){var e=this;return(0,r.default)((0,n.default)().mark((function t(){var a,i;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=[{name:"mobile",checkType:"phoneno",errorMsg:e.$lang("surePhoneNumber")},{name:"mobileVercode",checkType:"required",errorMsg:e.$lang("confirmCodeInput")},{name:"mobileDynacode",checkType:"required",errorMsg:e.$lang("animateCodeInput")}],i=s.default.check(e.formData,a),!i){t.next=9;break}if(e.formData.mobile!=e.memberInfo.mobile){t.next=6;break}return e.$util.showToast({title:e.$lang("alikePhone")}),t.abrupt("return");case 6:e.$api.sendRequest({url:"/api/member/modifymobile",data:{mobile:e.formData.mobile,captcha_id:e.captcha.id,captcha_code:e.formData.mobileVercode,code:e.formData.mobileDynacode,key:uni.getStorageSync("mobile_key")},success:function(t){0==t.code?(e.memberInfo.mobile=e.formData.mobile,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.back?e.$util.redirectTo("/pages_tool/member/pay_password",{back:e.back},"redirectTo"):e.NavReturn()):(e.$util.showToast({title:t.message}),e.getCaptcha())},fail:function(t){e.isSend=!1,e.getCaptcha()}}),t.next=10;break;case 9:e.$util.showToast({title:s.default.error});case 10:case"end":return t.stop()}}),t)})))()},passwordMoblieCode:function(){var e=this;120==this.seconds&&(""!=this.formData.mobileVercode?this.isSend?this.$util.showToast({title:"请勿重复点击"}):(this.isSend=!0,this.$api.sendRequest({url:"/api/member/pwdmobliecode",data:{captcha_id:this.captcha.id,captcha_code:this.formData.mobileVercode},success:function(t){var a=t.data;a.key?(120==e.seconds&&null==e.timer&&(e.timer=setInterval((function(){e.seconds--,e.formData.mobileCodeText="已发送("+e.seconds+"s)"}),1e3)),uni.setStorageSync("password_mobile_key",a.key)):(e.$util.showToast({title:t.message}),e.isSend=!1)},fail:function(t){e.isSend=!1,e.getCaptcha()}})):this.$util.showToast({title:this.$lang("confirmCodeInput")}))},modifyAddress:function(){var e=this,t=[{name:"fullAddress",checkType:"required",errorMsg:"请选择所在地区"},{name:"address",checkType:"required",errorMsg:"请输入详细地址"}];if(t.length){var a=s.default.check(this.formData,t);a?this.$api.sendRequest({url:"/api/member/modifyaddress",data:{province_id:this.formData.provinceId,city_id:this.formData.cityId,district_id:this.formData.districtId,address:this.formData.address,full_address:this.formData.fullAddress},success:function(t){0==t.code?(e.memberInfo.province_id=e.formData.provinceId,e.memberInfo.city_id=e.formData.cityId,e.memberInfo.district_id=e.formData.districtId,e.memberInfo.address=e.formData.address,e.memberInfo.full_address=e.formData.fullAddress,e.$store.commit("setMemberInfo",e.memberInfo),e.$util.showToast({title:e.$lang("updateSuccess")}),e.NavReturn()):e.$util.showToast({title:t.message})}}):this.$util.showToast({title:s.default.error})}},initFormData:function(){this.formData.currentPassword="",this.formData.newPassword="",this.formData.confirmPassword="",this.formData.mobileVercode="",this.formData.mobileDynacode="",this.formData.mobile=""},handleGetRegions:function(e){this.formData.fullAddress="",this.formData.fullAddress+=void 0!=e[0]?e[0].label:"",this.formData.fullAddress+=void 0!=e[1]?"-"+e[1].label:"",this.formData.fullAddress+=void 0!=e[2]?"-"+e[2].label:"",this.formData.provinceId=e[0]?e[0].value:0,this.formData.cityId=e[1]?e[1].value:0,this.formData.districtId=e[2]?e[2].value:0},manualBinding:function(){this.indent="mobile"},mobileAuth:function(e){var t=this;if("getPhoneNumber:ok"==e.detail.errMsg){var a=e.detail;this.$api.sendRequest({url:"/api/member/mobileauth",data:a,success:function(e){0==e.code?(t.$util.showToast({title:t.$lang("updateSuccess")}),t.NavReturn()):t.$util.showToast({title:e.message})}})}}}};t.default=o},"89d2":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-nav-bar-text[data-v-a4111932]{font-size:%?32?%}.uni-nav-bar-right-text[data-v-a4111932]{font-size:%?28?%}.uni-navbar[data-v-a4111932]{width:%?750?%}.uni-navbar__content[data-v-a4111932]{position:relative;width:%?750?%;background-color:#fff;overflow:hidden}.uni-navbar__content_view[data-v-a4111932]{display:flex;align-items:center;flex-direction:row}.uni-navbar__header[data-v-a4111932]{display:flex;flex-direction:row;width:%?750?%;height:44px;line-height:44px;font-size:16px}.uni-navbar__header-btns[data-v-a4111932]{display:flex;flex-wrap:nowrap;width:%?120?%;padding:0 6px;justify-content:center;align-items:center}.uni-navbar__header-btns-left[data-v-a4111932]{display:flex;width:%?150?%;justify-content:flex-start}.uni-navbar__header-btns-right[data-v-a4111932]{display:flex;width:%?150?%;padding-right:%?30?%;justify-content:flex-end}.uni-navbar__header-container[data-v-a4111932]{flex:1}.uni-navbar__header-container-inner[data-v-a4111932]{display:flex;flex:1;align-items:center;justify-content:center;font-size:%?28?%}.uni-navbar__placeholder-view[data-v-a4111932]{height:44px}.uni-navbar--fixed[data-v-a4111932]{position:fixed;z-index:998}.uni-navbar--shadow[data-v-a4111932]{box-shadow:0 1px 6px #ccc}.uni-navbar--border[data-v-a4111932]{border-bottom-width:%?1?%;border-bottom-style:solid;border-bottom-color:#c8c7cc}',""]),e.exports=t},"9b91":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-status-bar[data-v-610bd3bc]{width:%?750?%;height:20px}',""]),e.exports=t},a26a:function(e,t,a){"use strict";a.r(t);var i=a("0209"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},a52c:function(e,t,a){"use strict";a.r(t);var i=a("e94c"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},a9b1:function(e,t,a){var i=a("89d2");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("5492269a",i,!0,{sourceMap:!1,shadowMode:!1})},c1df:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i}));var i={uniIcons:a("c580").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"uni-navbar"},[a("v-uni-view",{staticClass:"uni-navbar__content",class:{"uni-navbar--fixed":e.fixed,"uni-navbar--shadow":e.shadow,"uni-navbar--border":e.border},style:{"background-color":e.backgroundColor}},[e.statusBar?a("uni-status-bar"):e._e(),a("v-uni-view",{staticClass:"uni-navbar__header uni-navbar__content_view",style:{color:e.color,backgroundColor:e.backgroundColor}},[a("v-uni-view",{staticClass:"uni-navbar__header-btns uni-navbar__header-btns-left uni-navbar__content_view",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClickLeft.apply(void 0,arguments)}}},[e.leftIcon.length?a("v-uni-view",{staticClass:"uni-navbar__content_view"},[a("uni-icons",{attrs:{color:e.color,type:e.leftIcon,size:"24"}})],1):e._e(),e.leftText.length?a("v-uni-view",{staticClass:"uni-navbar-btn-text uni-navbar__content_view",class:{"uni-navbar-btn-icon-left":!e.leftIcon.length}},[a("v-uni-text",{style:{color:e.color,fontSize:"14px"}},[e._v(e._s(e.leftText))])],1):e._e(),e._t("left")],2),a("v-uni-view",{staticClass:"uni-navbar__header-container uni-navbar__content_view"},[e.title.length?a("v-uni-view",{staticClass:"uni-navbar__header-container-inner uni-navbar__content_view"},[a("v-uni-text",{staticClass:"uni-nav-bar-text",style:{color:e.color}},[e._v(e._s(e.title))])],1):e._e(),e._t("default")],2),a("v-uni-view",{staticClass:"uni-navbar__header-btns uni-navbar__content_view",class:e.title.length?"uni-navbar__header-btns-right":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClickRight.apply(void 0,arguments)}}},[e.rightIcon.length?a("v-uni-view",{staticClass:"uni-navbar__content_view"},[a("uni-icons",{attrs:{color:e.color,type:e.rightIcon,size:"24"}})],1):e._e(),e.rightText.length&&!e.rightIcon.length?a("v-uni-view",{staticClass:"uni-navbar-btn-text uni-navbar__content_view"},[a("v-uni-text",{staticClass:"uni-nav-bar-right-text"},[e._v(e._s(e.rightText))])],1):e._e(),e._t("right")],2)],1)],1),e.fixed?a("v-uni-view",{staticClass:"uni-navbar__placeholder"},[e.statusBar?a("uni-status-bar"):e._e(),a("v-uni-view",{staticClass:"uni-navbar__placeholder-view"})],1):e._e()],1)},r=[]},d1d4:function(e,t,a){"use strict";var i=a("a9b1"),n=a.n(i);n.a},d50d:function(e,t,a){"use strict";var i=a("2c8e"),n=a.n(i);n.a},e94c:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("049b")),r=i(a("c580")),s={name:"UniNavBar",components:{uniStatusBar:n.default,uniIcons:r.default},props:{title:{type:String,default:""},leftText:{type:String,default:""},rightText:{type:String,default:""},leftIcon:{type:String,default:""},rightIcon:{type:String,default:""},fixed:{type:[Boolean,String],default:!1},color:{type:String,default:"#000000"},backgroundColor:{type:String,default:"#FFFFFF"},statusBar:{type:[Boolean,String],default:!1},shadow:{type:[String,Boolean],default:!1},border:{type:[String,Boolean],default:!0}},mounted:function(){uni.report&&""!==this.title&&uni.report("title",this.title)},methods:{onClickLeft:function(){this.$emit("clickLeft")},onClickRight:function(){this.$emit("clickRight")}}};t.default=s},fa00:function(e,t,a){"use strict";a.r(t);var i=a("c1df"),n=a("a52c");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("d1d4");var s=a("828b"),o=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"a4111932",null,!1,i["a"],void 0);t["default"]=o.exports}}]);