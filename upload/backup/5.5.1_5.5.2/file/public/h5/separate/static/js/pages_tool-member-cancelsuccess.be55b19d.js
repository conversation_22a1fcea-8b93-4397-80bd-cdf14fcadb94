(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-cancelsuccess"],{"00b4":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{}},onLoad:function(t){this.storeToken?this.init():this.$util.redirectTo("/pages_tool/login/index")},methods:{init:function(){this.$store.commit("setToken",""),this.$store.commit("setMemberInfo",""),this.$store.dispatch("emptyCart"),this.$util.redirectTo("/pages/index/index")}}}},"0255":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={pageMeta:n("7854").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("page-meta",{attrs:{"page-style":t.themeColor}}),n("v-uni-view",{staticClass:"container"},[n("v-uni-view",{staticClass:"cancel-wrap"},[n("v-uni-view",{staticClass:"cancel-img"},[n("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/member/success.png")}})],1),n("v-uni-view",{staticClass:"cancel-title"},[t._v("您已成功注销账号")]),n("v-uni-view",{staticClass:"cancel-reason"},[t._v("待下次与您更好的相遇，如需再次使用，请重新注册")]),n("v-uni-view",{staticClass:"cancel-btn"},[n("v-uni-button",{staticClass:"color-base-bg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.success.apply(void 0,arguments)}}},[t._v("完成")])],1)],1)],1)],1)},i=[]},"593d":function(t,e,n){"use strict";var o=n("ff3a"),a=n.n(o);a.a},"628b":function(t,e,n){"use strict";n.r(e);var o=n("00b4"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},7854:function(t,e,n){"use strict";n.r(e);var o=n("8ba8"),a=n("f48d");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);var r=n("828b"),c=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=c.exports},"8ba8":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},cc1b:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("64aa"),n("5c47"),n("a1c1"),n("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var n=function n(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",n),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",n)}})}}}};e.default=a},ce70:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.cancel-wrap[data-v-5013fe3a]{padding-top:%?84?%;text-align:center}.cancel-wrap .cancel-img[data-v-5013fe3a]{width:%?100?%;height:%?100?%;display:inline-block}.cancel-wrap .cancel-img uni-image[data-v-5013fe3a]{width:100%;height:100%}.cancel-wrap .cancel-title[data-v-5013fe3a]{text-align:center;font-size:%?24?%;line-height:%?24?%;margin-top:%?30?%}.cancel-wrap .cancel-reason[data-v-5013fe3a]{color:#838383;font-size:%?20?%;line-height:%?40?%;margin-top:%?20?%;padding:0 %?175?%}.cancel-wrap .cancel-btn[data-v-5013fe3a]{width:100%;margin-top:%?173?%}.cancel-wrap .cancel-btn uni-button[data-v-5013fe3a]{display:inline-block;width:%?300?%;height:%?80?%;font-size:%?28?%;line-height:%?80?%;margin:0 %?15?%;color:#fff}',""]),t.exports=e},d65a:function(t,e,n){"use strict";n.r(e);var o=n("0255"),a=n("628b");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("593d");var r=n("828b"),c=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"5013fe3a",null,!1,o["a"],void 0);e["default"]=c.exports},f48d:function(t,e,n){"use strict";n.r(e);var o=n("cc1b"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},ff3a:function(t,e,n){var o=n("ce70");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("8d129678",o,!0,{sourceMap:!1,shadowMode:!1})}}]);