(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-bundling-detail"],{"015d":function(t,e,o){"use strict";o.r(e);var i=o("0f46"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"0f46":function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=i},1692:function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(o("2634")),a=i(o("2fdc"));o("e966"),o("e838");var c={data:function(){return{num:1,blId:0,combo:[],packagePrice:[],saveThePrice:0,isDisabled:!1,isIphoneX:!1}},onLoad:function(t){this.blId=t.bl_id||0,this.isIphoneX=this.$util.uniappIsIPhoneX()},onShow:function(){var t=this;setTimeout((function(){t.addonIsExist.bundling||(t.$util.showToast({title:"商家未开启组合套餐",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.getDetail()},onHide:function(){this.btnSwitch=!0},methods:{getDetail:function(){var t=this;this.$api.sendRequest({url:"/bundling/api/bundling/detail",data:{bl_id:this.blId},success:function(e){e.data?(t.combo=e.data,t.numberChange()):t.$util.showToast({title:e.message}),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},toGoodsDetail:function(t){this.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})},numberChange:function(t,e){var o=this;setTimeout((function(){var i=0;t&&0==o.num.length&&(o.num=1,i++),t&&(o.num<=0||isNaN(o.num))&&(o.number=1,i++),t&&(o.num=parseInt(o.num));for(var n=0,a=0;a<o.combo.bundling_goods.length;a++)n+=parseFloat(o.combo.bundling_goods[a].price),o.combo.bundling_goods[a].stock<o.num&&i++;o.isDisabled=!(i>0),o.saveThePrice=((n-o.combo.bl_price)*o.num).toFixed(2),o.packagePrice=(o.combo.bl_price*o.num).toFixed(2),e&&e()}),0)},comboBuy:function(){var t=this;return(0,a.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.isDisabled){e.next=2;break}return e.abrupt("return");case 2:t.numberChange(!0,(function(){if(t.storeToken){if(0!=t.btnSwitch){t.btnSwitch=!1;var e={bl_id:t.blId,num:t.num};uni.setStorage({key:"comboOrderCreateData",data:e,success:function(){t.$util.redirectTo("/pages_promotion/bundling/payment"),t.btnSwitch=!0}})}}else t.$refs.login.open("/pages_promotion/bundling/detail?bl_id="+t.blId)}));case 3:case"end":return e.stop()}}),e)})))()},imageError:function(t){this.combo.bundling_goods[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}},onShareAppMessage:function(t){var e="/pages_promotion/combo/detail?bl_id="+this.blId;return{title:"购买套餐，优惠多多哦",path:e,success:function(t){},fail:function(t){}}}};e.default=c},3409:function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return i}));var i={pageMeta:o("7854").default,hoverNav:o("c1f1").default,loadingCover:o("c003").default,nsLogin:o("2910").default},n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",[o("page-meta",{attrs:{"page-style":t.themeColor}}),o("v-uni-view",[o("v-uni-view",{staticClass:"combo-package",class:t.isIphoneX?"combo-iphonex":""},[o("v-uni-view",{staticClass:"combo-package-content"},[o("v-uni-view",{staticClass:"combo-package-name color-title"},[t._v(t._s(t.combo.bl_name))]),t._l(t.combo.bundling_goods,(function(e,i){return o("v-uni-view",{key:i,staticClass:"goods-info",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toGoodsDetail(e)}}},[o("v-uni-view",{staticClass:"goods-img"},[o("v-uni-view",{staticClass:"img-wrap"},[o("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i)}}})],1)],1),o("v-uni-view",{staticClass:"data-info"},[o("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.sku_name))]),o("v-uni-view",{staticClass:"price-wrap"},[o("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(e.price).toFixed(2).split(".")[0]))]),o("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(e.price).toFixed(2).split(".")[1]))]),o("v-uni-text",{staticClass:"num"},[t._v("x1")])],1),e.stock<t.num?o("v-uni-view",{staticClass:"stock-tips color-base-text"},[t._v("库存不足，剩余："+t._s(e.stock)),e.unit?[t._v(t._s(e.unit))]:[t._v("件")]],2):t._e()],1)],1)}))],2),o("v-uni-view",{staticClass:"footer",class:t.isIphoneX?"padding-bottom":""},[o("v-uni-view",{staticClass:"price-wrap"},[o("v-uni-text",{staticClass:"label"},[t._v("套餐价：")]),o("v-uni-text",{staticClass:"unit price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",{staticClass:"price price-color"},[t._v(t._s(parseFloat(t.packagePrice).toFixed(2).split(".")[0]))]),o("v-uni-text",{staticClass:"unit price-color"},[t._v("."+t._s(parseFloat(t.packagePrice).toFixed(2).split(".")[1]))])],1),t.isDisabled?o("v-uni-button",{staticClass:"footer-btn mini",attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.comboBuy()}}},[t._v("立即购买")]):o("v-uni-button",{staticClass:"footer-btn mini",attrs:{disabled:!0}},[t._v("立即购买")])],1)],1),o("hover-nav"),o("loading-cover",{ref:"loadingCover"}),o("ns-login",{ref:"login"})],1)],1)},a=[]},"370d":function(t,e,o){"use strict";o.r(e);var i=o("3409"),n=o("954d");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("43d5");var c=o("828b"),r=Object(c["a"])(n["default"],i["b"],i["c"],!1,null,"13eebdaf",null,!1,i["a"],void 0);e["default"]=r.exports},"43d5":function(t,e,o){"use strict";var i=o("bd16"),n=o.n(i);n.a},7854:function(t,e,o){"use strict";o.r(e);var i=o("8ba8"),n=o("f48d");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);var c=o("828b"),r=Object(c["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=r.exports},"8ba8":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"954d":function(t,e,o){"use strict";o.r(e);var i=o("1692"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},a725:function(t,e,o){"use strict";var i=o("ac2a"),n=o.n(i);n.a},ac2a:function(t,e,o){var i=o("f714");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=o("967d").default;n("1a69ffc2",i,!0,{sourceMap:!1,shadowMode:!1})},b6ef:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.combo-package[data-v-13eebdaf]{border-radius:%?10?%;margin:%?20?% %?30?%;margin-bottom:%?160?%;background:#fff}.combo-package.combo-iphonex[data-v-13eebdaf]{margin-bottom:%?230?%}.combo-package .combo-package-content .combo-package-name[data-v-13eebdaf]{padding:%?20?% %?30?%}.combo-package .combo-package-content .goods-info[data-v-13eebdaf]{overflow:hidden;padding:%?20?% %?30?%;border-radius:%?10?%}.combo-package .combo-package-content .goods-info .goods-img[data-v-13eebdaf]{display:inline-block;width:30%;text-align:center;line-height:100%;float:left}.combo-package .combo-package-content .goods-info .goods-img .img-wrap[data-v-13eebdaf]{display:inline-block;width:%?180?%;height:%?180?%;text-align:center;border-radius:%?10?%}.combo-package .combo-package-content .goods-info .goods-img .img-wrap uni-image[data-v-13eebdaf]{height:100%;width:100%;vertical-align:middle}.combo-package .combo-package-content .goods-info .data-info[data-v-13eebdaf]{display:inline-block;width:66%;float:left;position:relative;margin-left:%?20?%;height:%?180?%}.combo-package .combo-package-content .goods-info .data-info .goods-name[data-v-13eebdaf]{overflow:hidden;text-overflow:ellipsis;-webkit-line-clamp:2;-webkit-box-orient:vertical;display:-webkit-box;line-height:150%}.combo-package .combo-package-content .goods-info .data-info .stock-tips[data-v-13eebdaf]{position:absolute;width:100%;bottom:%?50?%;height:%?40?%;line-height:%?40?%;font-size:%?24?%}.combo-package .combo-package-content .goods-info .data-info .price-wrap[data-v-13eebdaf]{height:%?50?%;line-height:%?50?%;position:absolute;width:100%;bottom:0}.combo-package .combo-package-content .goods-info .data-info .price-wrap .unit[data-v-13eebdaf]{font-weight:700;font-size:%?24?%;margin-right:%?4?%}.combo-package .combo-package-content .goods-info .data-info .price-wrap .price[data-v-13eebdaf]{font-weight:700;font-size:%?32?%}.combo-package .combo-package-content .goods-info .data-info .price-wrap .num[data-v-13eebdaf]{float:right}.combo-package .footer[data-v-13eebdaf]{width:100%;background:#fff;display:flex;justify-content:space-between;padding:%?20?% %?24?%;box-sizing:border-box;position:fixed;bottom:0;left:0}.combo-package .footer .price-wrap[data-v-13eebdaf]{flex:1;text-align:right;vertical-align:middle;line-height:%?70?%;margin-right:%?30?%}.combo-package .footer .price-wrap .label[data-v-13eebdaf]{font-size:%?28?%}.combo-package .footer .price-wrap .unit[data-v-13eebdaf]{font-weight:700;font-size:%?24?%;margin-right:%?4?%}.combo-package .footer .price-wrap .price[data-v-13eebdaf]{font-weight:700;font-size:%?32?%}.combo-package .footer .price-wrap .info-num[data-v-13eebdaf]{font-size:%?20?%;color:#909399;line-height:1;margin-top:%?12?%}.combo-package .footer .footer-btn[data-v-13eebdaf]{display:flex;align-items:center;justify-content:center;height:%?70?%;line-height:%?70?%}.combo-package .padding-bottom[data-v-13eebdaf]{padding-bottom:%?80?%}',""]),t.exports=e},bd16:function(t,e,o){var i=o("b6ef");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=o("967d").default;n("16a803a2",i,!0,{sourceMap:!1,shadowMode:!1})},c1f1:function(t,e,o){"use strict";o.r(e);var i=o("fa1d"),n=o("015d");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("a725");var c=o("828b"),r=Object(c["a"])(n["default"],i["b"],i["c"],!1,null,"c1934e78",null,!1,i["a"],void 0);e["default"]=r.exports},cc1b:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("5ef2"),o("64aa"),o("5c47"),o("a1c1"),o("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var o=function o(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",o),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",o)}})}}}};e.default=n},f48d:function(t,e,o){"use strict";o.r(e);var i=o("cc1b"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},f714:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return 1==t.pageCount||t.need?o("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?o("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[o("v-uni-text",{staticClass:"iconfont icon-shouye1"}),o("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?o("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[o("v-uni-text",{staticClass:"iconfont icon-yonghu"}),o("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?o("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[o("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):o("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[o("v-uni-view",[t._v("快捷")]),o("v-uni-view",[t._v("导航")])],1)],1):t._e()},n=[]}}]);