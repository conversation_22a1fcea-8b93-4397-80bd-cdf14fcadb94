(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-topics-list"],{"015d":function(t,e,i){"use strict";i.r(e);var n=i("0f46"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"073b":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={pageMeta:i("7854").default,nsAdv:i("7e88").default,nsEmpty:i("52a6").default,hoverNav:i("c1f1").default,loadingCover:i("c003").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",[t.addonIsExist.topic?i("mescroll-uni",{ref:"mescroll",attrs:{size:10},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getData.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[i("v-uni-view",{staticClass:"ns-adv"},[i("ns-adv",{attrs:{keyword:"NS_TOPIC"}})],1),t._l(t.dataList,(function(e,n){return t.dataList.length?i("v-uni-view",{key:n,staticClass:"topic-list",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e.topic_id,e.topic_name)}}},[e.topic_adv?i("v-uni-view",{staticClass:"list-img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.topic_adv),mode:"widthFix"}})],1):t._e(),i("v-uni-view",{staticClass:"list-info"},[i("v-uni-view",{staticClass:"info-left"},[i("v-uni-text",{staticClass:"left-title"},[t._v(t._s(e.topic_name))]),i("v-uni-text",{staticClass:"left-time"},[t._v("活动时间："+t._s(e.start_time)+"~"+t._s(e.end_time))])],1),i("v-uni-view",{staticClass:"info-right"},[i("v-uni-text",{staticClass:"iconfont icon-shijian1"}),t._v(t._s(t.nowTime<e.end_time?"进行中":"已结束"))],1)],1)],1):t._e()})),t.dataList.length?t._e():i("v-uni-view",{staticClass:"cart-empty"},[i("ns-empty",{attrs:{text:"暂无专题活动"}})],1)],2)],2):t._e(),i("hover-nav"),i("loading-cover",{ref:"loadingCover"})],1)],1)},o=[]},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=n},2407:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.advList.length?i("v-uni-view",{class:["container-box",t.className]},[t.advList.length>1?i("v-uni-swiper",{staticClass:"item-wrap",style:{height:t.swiperHeight+"px"},attrs:{"indicator-dots":t.advList.length>1,"indicator-active-color":"#ffffff",autoplay:!0,interval:3e3,duration:1e3,current:t.currentIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSwiper.apply(void 0,arguments)}}},t._l(t.advList,(function(e,n){return i("v-uni-swiper-item",{key:n,on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.jumppage(e.adv_url)}}},[i("v-uni-view",{staticClass:"image-box"},[i("v-uni-image",{attrs:{src:t.$util.img(e.adv_image),mode:"widthFix",id:"content-wrap"+n}})],1)],1)})),1):i("v-uni-view",{staticClass:"container-box item-wrap"},[i("v-uni-image",{attrs:{src:t.$util.img(t.advList[0]["adv_image"]),mode:"widthFix","lazy-load":"true"},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imageLoad.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumppage(t.advList[0].adv_url)}}})],1)],1):t._e()},a=[]},2673:function(t,e,i){var n=i("269f");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("0c9ef6a4",n,!0,{sourceMap:!1,shadowMode:!1})},"269f":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.ns-adv[data-v-0f32535a]{margin:%?20?% %?24?%;border-radius:%?10?%;overflow:hidden;line-height:1}.ns-adv uni-image[data-v-0f32535a]{width:100%}[data-v-0f32535a] .fixed{position:relative;top:0}[data-v-0f32535a] .empty{margin-top:0!important}.topic-pic[data-v-0f32535a]{height:%?300?%}.topic-pic uni-image[data-v-0f32535a]{height:%?300?%}.topic-list[data-v-0f32535a]{margin:0 %?24?% %?24?%;border-radius:%?10?%;overflow:hidden;display:flex;flex-direction:column;margin-bottom:%?16?%;background-color:#fff}.topic-list .list-img[data-v-0f32535a]{width:100%;overflow:hidden}.topic-list .list-img uni-image[data-v-0f32535a]{width:100%;height:100%}.topic-list .list-info[data-v-0f32535a]{flex:1;display:flex;padding:0 %?24?%;box-sizing:border-box;align-items:center;justify-content:space-between;padding-top:%?29?%;padding-bottom:%?29?%}.topic-list .list-info .info-left[data-v-0f32535a]{display:flex;flex-direction:column;line-height:1;flex:1;margin-right:%?30?%}.topic-list .list-info .info-left .left-title[data-v-0f32535a]{font-size:%?28?%;color:#383838;line-height:1.5;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.topic-list .list-info .info-left .left-time[data-v-0f32535a]{font-size:%?22?%;color:#838383;margin-top:%?18?%;line-height:1}.topic-list .list-info .info-right[data-v-0f32535a]{font-size:%?28?%;color:#838383;line-height:1}.topic-list .list-info .info-right .iconfont[data-v-0f32535a]{font-size:%?32?%;vertical-align:middle}.topic-list .list-info .info-right uni-text[data-v-0f32535a]{font-size:%?28?%;margin-right:%?8?%}',""]),t.exports=e},6102:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47");var n={name:"ns-advert",props:{keyword:{type:String},className:{type:String}},data:function(){return{advList:[],isImage:!1,swiperHeight:150,currentIndex:0}},created:function(){this.getAdvList()},methods:{getAdvList:function(){var t=this;this.$api.sendRequest({url:"/api/adv/detail",data:{keyword:this.keyword},success:function(e){if(0==e.code){var i=e.data.adv_list;for(var n in i)i[n].adv_url&&(i[n].adv_url=JSON.parse(i[n].adv_url));t.advList=e.data.adv_list,t.$nextTick((function(){t.setSwiperHeight()}))}}})},jumppage:function(t){this.$util.diyRedirectTo(t)},imageLoad:function(t){this.isImage=!0},changeSwiper:function(t){var e=this;this.currentIndex=t.detail.current,this.$nextTick((function(){e.setSwiperHeight()}))},setSwiperHeight:function(){var t=this;this.advList.length>1&&setTimeout((function(){var e="#content-wrap"+t.currentIndex,i=uni.createSelectorQuery().in(t);i.select(e).boundingClientRect(),i.exec((function(e){e&&e[0]&&(t.swiperHeight=e[0].height)}))}),10)}}};e.default=n},7854:function(t,e,i){"use strict";i.r(e);var n=i("8ba8"),a=i("f48d");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"7e88":function(t,e,i){"use strict";i.r(e);var n=i("2407"),a=i("f016");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("a44f");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"9caa2b5c",null,!1,n["a"],void 0);e["default"]=s.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},a44f:function(t,e,i){"use strict";var n=i("d87f"),a=i.n(n);a.a},a725:function(t,e,i){"use strict";var n=i("ac2a"),a=i.n(n);a.a},ac2a:function(t,e,i){var n=i("f714");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("1a69ffc2",n,!0,{sourceMap:!1,shadowMode:!1})},adf4:function(t,e,i){"use strict";var n=i("2673"),a=i.n(n);a.a},be54:function(t,e,i){"use strict";i.r(e);var n=i("073b"),a=i("c748");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("adf4");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"0f32535a",null,!1,n["a"],void 0);e["default"]=s.exports},c1f1:function(t,e,i){"use strict";i.r(e);var n=i("fa1d"),a=i("015d");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("a725");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"c1934e78",null,!1,n["a"],void 0);e["default"]=s.exports},c748:function(t,e,i){"use strict";i.r(e);var n=i("f169"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=a},d87f:function(t,e,i){var n=i("d915");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("03d75754",n,!0,{sourceMap:!1,shadowMode:!1})},d915:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-9caa2b5c]{width:100%}.container-box .item-wrap[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-9caa2b5c]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}',""]),t.exports=e},f016:function(t,e,i){"use strict";i.r(e);var n=i("6102"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f169:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("c223");var a=n(i("7e88")),o={components:{nsAdv:a.default},data:function(){return{dataList:[],nowTime:"",mpShareData:null}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.topic||(e.$util.showToast({title:"商家未开启专题活动",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member"))},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},methods:{getData:function(t){var e=this;this.$api.sendRequest({url:"/topic/api/topic/page",data:{page_size:t.size,page:t.num},success:function(i){var n=[],a=i.message;0==i.code&&i.data?n=i.data.list:e.$util.showToast({title:a}),t.endSuccess(n.length),1==t.num&&(e.dataList=[]),e.dataList=e.dataList.concat(n),e.dataList.forEach((function(t){t.start_time=e.$util.timeStampTurnTime(t.start_time,"Y-m-d"),t.end_time=e.$util.timeStampTurnTime(t.end_time,"Y-m-d")})),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(i){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},toDetail:function(t,e){this.$util.redirectTo("/pages_promotion/topics/detail",{topic_id:t,title:e})}}};e.default=o},f48d:function(t,e,i){"use strict";i.r(e);var n=i("cc1b"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f714:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},a=[]}}]);