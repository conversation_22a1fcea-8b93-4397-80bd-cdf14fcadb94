(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-modify_face"],{"0206":function(t,i,e){var a=e("c8e9");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var s=e("967d").default;s("1f0312b7",a,!0,{sourceMap:!1,shadowMode:!1})},"1bd9":function(t,i,e){"use strict";e("6a54");var a=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var s=a(e("2634")),n=a(e("2fdc")),o=a(e("3471"));e("e838"),e("e966"),e("0c26"),e("5ef2"),e("c9b5"),e("bf0f"),e("ab80"),e("4db2"),e("9480"),e("4d8f"),e("7b97"),e("668a"),e("c5b7"),e("8ff5"),e("2378"),e("641a"),e("64e0"),e("cce3"),e("efba"),e("d009"),e("bd7d"),e("7edd"),e("d798"),e("f547"),e("5e54"),e("b60a"),e("8c18"),e("12973"),e("f991"),e("198e"),e("8557"),e("63b1"),e("1954"),e("1cf1"),e("5c47"),e("2c10"),e("15d1"),e("d5c6"),e("5a56"),e("f074"),e("c976"),e("18f7"),e("de6c"),e("dc89"),e("2425");var h={name:"yq-avatar",data:function(){return{csH:"0",sD:"none",sT:"-10000px",pT:"-10000px",iS:{},sS:{},sO:!0,bW:"19%",bD:"flex",tp:0,imgSrc:{imgSrc:""}}},watch:{avatarSrc:function(){this.imgSrc.imgSrc=this.avatarSrc}},props:{avatarSrc:"",avatarStyle:"",selWidth:"",selHeight:"",expWidth:"",expHeight:"",minScale:"",maxScale:"",canScale:"",canRotate:"",lockWidth:"",lockHeight:"",stretch:"",lock:"",fileType:"",noTab:"",inner:"",quality:"",index:"",bgImage:""},created:function(){var t=this;this.cc=uni.createCanvasContext("avatar-canvas",this),this.cco=uni.createCanvasContext("oper-canvas",this),this.ccp=uni.createCanvasContext("prv-canvas",this),this.qlty=parseFloat(this.quality)||1,this.imgSrc.imgSrc=this.avatarSrc,this.letRotate=!1===this.canRotate||!0===this.inner||"true"===this.inner||"false"===this.canRotate?0:1,this.letScale=!1===this.canScale||"false"===this.canScale?0:1,this.isin=!0===this.inner||"true"===this.inner?1:0,this.indx=this.index||void 0,this.mnScale=parseFloat(this.minScale)||.3,this.mxScale=parseFloat(this.maxScale)||4,this.noBar=!0===this.noTab||"true"===this.noTab?1:0,this.stc=this.stretch,this.lck=this.lock,this.fType="jpg"===this.fileType?"jpg":"png",this.isin||!this.letRotate?(this.bW="24%",this.bD="none"):(this.bW="19%",this.bD="flex"),this.noBar?this.fWindowResize():uni.showTabBar({fail:function(){t.noBar=1},success:function(){t.noBar=0},complete:function(i){t.fWindowResize()}})},methods:{fWindowResize:function(){var t=uni.getSystemInfoSync();this.platform=t.platform,this.wW=t.windowWidth,this.drawTop=t.windowTop,this.wH=t.windowHeight,this.noBar||(this.wH+=50),this.csH=this.wH-50+"px",this.tp=this.csH,this.tp=t.windowTop+parseInt(this.csH)+"px",this.pxRatio=this.wW/750;var i=this.avatarStyle;if(i&&!0!==i&&(i=i.trim())){i=i.split(";");var e,a={},s=(0,o.default)(i);try{for(s.s();!(e=s.n()).done;){var n=e.value;if(n){if(n=n.trim().split(":"),n[1].toString().indexOf("upx")>=0){var h=n[1].trim().split(" ");for(var r in h)h[r]&&h[r].toString().indexOf("upx")>=0&&(h[r]=parseFloat(h[r])*this.pxRatio+"px");n[1]=h.join(" ")}a[n[0].trim()]=n[1].trim()}}}catch(c){s.e(c)}finally{s.f()}this.iS=a}this.expWidth&&(this.eW=this.expWidth.toString().indexOf("upx")>=0?parseInt(this.expWidth)*this.pxRatio:parseInt(this.expWidth)),this.expHeight&&(this.eH=this.expHeight.toString().indexOf("upx")>=0?parseInt(this.expHeight)*this.pxRatio:parseInt(this.expHeight)),"flex"===this.sD&&this.fDrawInit(!0),this.fHideImg()},fSelect:function(){var t=this;this.fSelecting||(this.fSelecting=!0,setTimeout((function(){t.fSelecting=!1}),500),uni.chooseImage({count:1,sizeType:["original","compressed"],sourceType:["album","camera"],success:function(i){uni.showLoading({title:"加载中...",mask:!0});var e=t.imgPath=i.tempFilePaths[0];uni.getImageInfo({src:e,success:function(i){if(t.imgWidth=i.width,t.imgHeight=i.height,t.path=e,!t.hasSel){var a=t.sS||{};if(!t.selWidth||!t.selHeight)return void uni.showModal({title:"裁剪框的宽或高没有设置",showCancel:!1});var s=t.selWidth.toString().indexOf("upx")>=0?parseInt(t.selWidth)*t.pxRatio:parseInt(t.selWidth),n=t.selHeight.toString().indexOf("upx")>=0?parseInt(t.selHeight)*t.pxRatio:parseInt(t.selHeight);a.width=s+"px",a.height=n+"px",a.top=(t.wH-n-50|0)/2+"px",a.left=(t.wW-s|0)/2+"px",t.sS=a}t.noBar?t.fDrawInit(!0):uni.hideTabBar({complete:function(){t.fDrawInit(!0)}})},fail:function(){t.$util.showToast({title:"请选择正确图片"})},complete:function(){uni.hideLoading()}})}}))},fUpload:function(){var t=this;if(!this.fUploading){this.fUploading=!0,setTimeout((function(){t.fUploading=!1}),1e3);var i=this.sS,e=parseInt(i.left),a=parseInt(i.top),s=parseInt(i.width),n=parseInt(i.height),o=this.eW||s*this.pixelRatio,h=this.eH||n*this.pixelRatio;uni.showLoading({title:"加载中...",mask:!0}),this.sD="none",this.sT="-10000px",this.hasSel=!1,this.fHideImg(),uni.canvasToTempFilePath({x:e,y:a,width:s,height:n,destWidth:o,destHeight:h,canvasId:"avatar-canvas",fileType:this.fType,quality:this.qlty,success:function(i){i=i.tempFilePath,t.btop(i).then((function(i){t.$emit("upload",{avatar:t.imgSrc,path:i,index:t.indx,data:t.rtn,base64:t.base64||null})}))},fail:function(t){uni.showToast({title:"error1",duration:2e3})},complete:function(){uni.hideLoading(),t.noBar||uni.showTabBar(),t.$emit("end")}},this)}},fPrvUpload:function(){var t=this;if(!this.fPrvUploading){this.fPrvUploading=!0,setTimeout((function(){t.fPrvUploading=!1}),1e3);var i=this.sS,e=(parseInt(i.width),parseInt(i.height),this.prvX),a=this.prvY,s=this.prvWidth,n=this.prvHeight,o=this.eW||parseInt(i.width)*this.pixelRatio,h=this.eH||parseInt(i.height)*this.pixelRatio;uni.showLoading({title:"加载中...",mask:!0}),this.sD="none",this.sT="-10000px",this.hasSel=!1,this.fHideImg(),uni.canvasToTempFilePath({x:e,y:a,width:s,height:n,destWidth:o,destHeight:h,canvasId:"prv-canvas",fileType:this.fType,quality:this.qlty,success:function(i){i=i.tempFilePath,t.btop(i).then((function(i){t.$emit("upload",{avatar:t.imgSrc,path:i,index:t.indx,data:t.rtn,base64:t.base64||null})}))},fail:function(){uni.showToast({title:"error_prv",duration:2e3})},complete:function(){uni.hideLoading(),t.noBar||uni.showTabBar(),t.$emit("end")}},this)}},fDrawInit:function(){var t=this,i=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.wW,a=this.wH,s=this.imgWidth,n=this.imgHeight,o=s/n,h=e-40,r=a-50-80,c=h/r,l=parseInt(this.sS.width),u=parseInt(this.sS.height);switch(this.fixWidth=0,this.fixHeight=0,this.lckWidth=0,this.lckHeight=0,this.stc){case"x":this.fixWidth=1;break;case"y":this.fixHeight=1;break;case"long":o>1?this.fixWidth=1:this.fixHeight=1;break;case"short":o>1?this.fixHeight=1:this.fixWidth=1;break;case"longSel":l>u?this.fixWidth=1:this.fixHeight=1;break;case"shortSel":l>u?this.fixHeight=1:this.fixWidth=1;break}switch(this.lck){case"x":this.lckWidth=1;break;case"y":this.lckHeight=1;break;case"long":o>1?this.lckWidth=1:this.lckHeight=1;break;case"short":o>1?this.lckHeight=1:this.lckWidth=1;break;case"longSel":l>u?this.lckWidth=1:this.lckHeight=1;break;case"shortSel":l>u?this.lckHeight=1:this.lckWidth=1;break}this.fixWidth?(h=l,r=h/o):this.fixHeight?(r=u,h=r*o):o<c?n<r?(h=s,r=n):h=r*o:s<h?(h=s,r=n):r=h/o,this.isin&&(h<l&&(h=l,r=h/o,this.lckHeight=0),r<u&&(r=u,h=r*o,this.lckWidth=0)),this.scaleSize=1,this.rotateDeg=0,this.posWidth=(e-h)/2|0,this.posHeight=(a-r-50)/2|0,this.useWidth=0|h,this.useHeight=0|r,this.centerX=this.posWidth+h/2,this.centerY=this.posHeight+r/2,this.focusX=0,this.focusY=0;var d=this.sS,f=parseInt(d.left),p=parseInt(d.top),g=parseInt(d.width),v=parseInt(d.height),m=(this.canvas,this.canvasOper,this.cc,this.cco);m.beginPath(),m.setLineWidth(3),m.setGlobalAlpha(1),m.setStrokeStyle("white"),m.strokeRect(f,p,g,v),m.setFillStyle("black"),m.setGlobalAlpha(.5),m.fillRect(0,0,this.wW,p),m.fillRect(0,p,f,v),m.fillRect(0,p+v,this.wW,this.wH-v-p-50),m.fillRect(f+g,p,this.wW-g-f,v),m.setGlobalAlpha(1),m.setStrokeStyle("red"),m.moveTo(f+15,p),m.lineTo(f,p),m.lineTo(f,p+15),m.moveTo(f+g-15,p),m.lineTo(f+g,p),m.lineTo(f+g,p+15),m.moveTo(f+15,p+v),m.lineTo(f,p+v),m.lineTo(f,p+v-15),m.moveTo(f+g-15,p+v),m.lineTo(f+g,p+v),m.lineTo(f+g,p+v-15),m.stroke(),m.draw(!1,(function(){i&&(t.sD="flex",t.sT=t.drawTop+"px",t.fDrawImage(!0))})),this.$emit("init")},fDrawImage:function(){var t=Date.now();if(!(t-this.drawTm<20)){this.drawTm=t;var i=this.cc,e=this.useWidth*this.scaleSize,a=this.useHeight*this.scaleSize;if(this.bgImage?i.drawImage(this.bgImage,0,0,this.wW,this.wH-50):i.fillRect(0,0,this.wW,this.wH-50),this.isin){var s=this.focusX*(this.scaleSize-1),n=this.focusY*(this.scaleSize-1);i.translate(this.centerX,this.centerY),i.rotate(this.rotateDeg*Math.PI/180),i.drawImage(this.imgPath,this.posWidth-this.centerX-s,this.posHeight-this.centerY-n,e,a)}else i.translate(this.posWidth+e/2,this.posHeight+a/2),i.rotate(this.rotateDeg*Math.PI/180),i.drawImage(this.imgPath,-e/2,-a/2,e,a);i.draw(!1)}},fPreview:function(){var t=this;if(!this.fPreviewing){this.fPreviewing=!0,setTimeout((function(){t.fPreviewing=!1}),1e3);var i=this.sS,e=parseInt(i.left),a=parseInt(i.top),s=parseInt(i.width),n=parseInt(i.height);uni.showLoading({title:"加载中...",mask:!0}),uni.canvasToTempFilePath({x:e,y:a,width:s,height:n,expWidth:s*this.pixelRatio,expHeight:n*this.pixelRatio,canvasId:"avatar-canvas",fileType:this.fType,quality:this.qlty,success:function(i){t.prvImgTmp=i=i.tempFilePath;var e=t.ccp,a=t.wW,s=parseInt(t.csH),n=parseInt(t.sS.width),o=parseInt(t.sS.height),h=a-40,r=s-80,c=h/n,l=o*c;l<r?(n=h,o=l):(c=r/o,n*=c,o=r),e.fillRect(0,0,a,s),t.prvX=a=(a-n)/2|0,t.prvY=s=(s-o)/2|0,t.prvWidth=n|=0,t.prvHeight=o|=0,e.drawImage(i,a,s,n,o),e.draw(!1),t.btop(i).then((function(i){t.sO=!1,t.pT=t.drawTop+"px"})),t.sO=!1,t.pT=t.drawTop+"px"},fail:function(){uni.showToast({title:"error2",duration:2e3})},complete:function(){uni.hideLoading()}},this)}},fChooseImg:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if(i){var a=i.selWidth,s=i.selHeight,n=i.expWidth,o=i.expHeight,h=i.quality,r=i.canRotate,c=i.canScale,l=i.minScale,u=i.maxScale,d=i.stretch,f=i.fileType,p=i.inner,g=i.lock;n&&(this.eW=n.toString().indexOf("upx")>=0?parseInt(n)*this.pxRatio:parseInt(n)),o&&(this.eH=o.toString().indexOf("upx")>=0?parseInt(o)*this.pxRatio:parseInt(o)),this.letRotate=!1===r||!0===p||"true"===p||"false"===r?0:1,this.letScale=!1===c||"false"===c?0:1,this.qlty=parseFloat(h)||1,this.mnScale=parseFloat(l)||.3,this.mxScale=parseFloat(u)||4,this.stc=d,this.isin=!0===p||"true"===p?1:0,this.fType="jpg"===f?"jpg":"png",this.lck=g,this.isin||!this.letRotate?(this.bW="24%",this.bD="none"):(this.bW="19%",this.bD="flex"),a&&s&&(a=a.toString().indexOf("upx")>=0?parseInt(a)*this.pxRatio:parseInt(a),s=s.toString().indexOf("upx")>=0?parseInt(s)*this.pxRatio:parseInt(s),this.sS.width=a+"px",this.sS.height=s+"px",this.sS.top=(this.wH-s-50|0)/2+"px",this.sS.left=(this.wW-a|0)/2+"px",this.hasSel=!0)}this.rtn=e,this.indx=t,this.fSelect()},fRotate:function(){this.rotateDeg+=90-this.rotateDeg%90,this.fDrawImage()},fStart:function(t){var i=t.touches,e=i[0],a=i[1];if(this.touch0=e,this.touch1=a,a){var s=a.x-e.x,n=a.y-e.y;this.fgDistance=Math.sqrt(s*s+n*n)}},fMove:function(t){var i=t.touches,e=i[0],a=i[1];if(a){var s=a.x-e.x,n=a.y-e.y,o=Math.sqrt(s*s+n*n),h=.005*(o-this.fgDistance),r=this.scaleSize+h;do{if(!this.letScale)break;if(r<this.mnScale)break;if(r>this.mxScale)break;var c=this.useWidth*h/2,l=this.useHeight*h/2;if(this.isin){var u,d,f=this.useWidth*r,p=this.useHeight*r,g=(this.posWidth,this.posHeight,parseInt(this.sS.left)),v=parseInt(this.sS.top),m=parseInt(this.sS.width),b=parseInt(this.sS.height),x=g+m,w=v+b;if(f<=m||p<=b)break;this.cx=u=this.focusX*r-this.focusX,this.cy=d=this.focusY*r-this.focusY,this.posWidth-=c,this.posHeight-=l,this.posWidth-u>g&&(this.posWidth=g+u),this.posWidth+f-u<x&&(this.posWidth=x-f+u),this.posHeight-d>v&&(this.posHeight=v+d),this.posHeight+p-d<w&&(this.posHeight=w-p+d)}else this.posWidth-=c,this.posHeight-=l;this.scaleSize=r}while(0);this.fgDistance=o,a.x!==e.x&&this.letRotate&&(s=(this.touch1.y-this.touch0.y)/(this.touch1.x-this.touch0.x),n=(a.y-e.y)/(a.x-e.x),this.rotateDeg+=180*Math.atan((n-s)/(1+s*n))/Math.PI,this.touch0=e,this.touch1=a),this.fDrawImage()}else if(this.touch0){var y=e.x-this.touch0.x,S=e.y-this.touch0.y,I=this.posWidth+y,T=this.posHeight+S;if(this.isin){var k,H,W=this.useWidth*this.scaleSize,$=this.useHeight*this.scaleSize,C=I,D=T,R=C+W,P=D+$,_=parseInt(this.sS.left),z=parseInt(this.sS.top),M=_+parseInt(this.sS.width),O=z+parseInt(this.sS.height);this.cx=k=this.focusX*this.scaleSize-this.focusX,this.cy=H=this.focusY*this.scaleSize-this.focusY,!this.lckWidth&&Math.abs(y)<100&&(_<C-k?this.posWidth=_+k:M>R-k?this.posWidth=M-W+k:(this.posWidth=I,this.focusX-=y)),!this.lckHeight&&Math.abs(S)<100&&(z<D-H?(this.focusY-=z+H-this.posHeight,this.posHeight=z+H):O>P-H?(this.focusY-=O+H-(this.posHeight+$),this.posHeight=O-$+H):(this.posHeight=T,this.focusY-=S))}else Math.abs(y)<100&&!this.lckWidth&&(this.posWidth=I),Math.abs(S)<100&&!this.lckHeight&&(this.posHeight=T),this.focusX-=y,this.focusY-=S;this.touch0=e,this.fDrawImage()}},fEnd:function(t){var i=t.touches,e=i&&i[0];i&&i[1];e?this.touch0=e:(this.touch0=null,this.touch1=null)},fHideImg:function(){this.prvImg="",this.pT="-10000px",this.sO=!0,this.prvImgData=null,this.target=null},fClose:function(){this.sD="none",this.sT="-10000px",this.hasSel=!1,this.fHideImg(),this.noBar||uni.showTabBar(),this.$emit("end")},fGetImgData:function(){var t=this;return new Promise((function(i,e){var a=t.prvX,s=t.prvY,n=t.prvWidth,o=t.prvHeight;uni.canvasGetImageData({canvasId:"prv-canvas",x:a,y:s,width:n,height:o,success:function(t){i(t.data)},fail:function(t){e(t)}},t)}))},fColorChange:function(t){var i=this;return(0,n.default)((0,s.default)().mark((function e(){var a,n,o,h,r,c,l,u,d,f,p,g,v,m,b,x,w,y,S,I,T,k,H,W,$,C,D;return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=Date.now(),!(a-i.prvTm<100)){e.next=3;break}return e.abrupt("return");case 3:if(i.prvTm=a,uni.showLoading({title:"加载中...",mask:!0}),i.prvImgData){e.next=11;break}return e.next=8,i.fGetImgData().catch((function(){uni.showToast({title:"error_read",duration:2e3})}));case 8:if(i.prvImgData=e.sent){e.next=10;break}return e.abrupt("return");case 10:i.target=new Uint8ClampedArray(i.prvImgData.length);case 11:if(n=i.prvImgData,o=i.target,h=t.detail.value,0===h)o=n;else for(h=(h+100)/200,h<.005&&(h=0),h>.995&&(h=1),T=n.length-1;T>=0;T-=4)r=n[T-3]/255,c=n[T-2]/255,l=n[T-1]/255,x=Math.max(r,c,l),b=Math.min(r,c,l),g=x-b,x===b?d=0:x===r&&c>=l?d=(c-l)/g*60:x===r&&c<l?d=(c-l)/g*60+360:x===c?d=(l-r)/g*60+120:x===l&&(d=(r-c)/g*60+240),p=(x+b)/2,0===p||x===b?f=0:0<p&&p<=.5?f=g/(2*p):p>.5&&(f=g/(2-2*p)),n[T]&&(u=n[T]),h<.5?f=f*h/.5:h>.5&&(f=2*f+2*h-f*h/.5-1),0===f?r=c=l=Math.round(255*p):(p<.5?m=p*(1+f):p>=.5&&(m=p+f-p*f),v=2*p-m,w=d/360,y=w+1/3,S=w,I=w-1/3,k=function(t){return t<0?t+1:t>1?t-1:t},H=function(t){return t<1/6?v+6*(m-v)*t:t>=1/6&&t<.5?m:t>=.5&&t<2/3?v+6*(m-v)*(2/3-t):v},r=y=Math.round(255*H(k(y))),c=S=Math.round(255*H(k(S))),l=I=Math.round(255*H(k(I)))),u&&(o[T]=u),o[T-3]=r,o[T-2]=c,o[T-1]=l;W=i.prvX,$=i.prvY,C=i.prvWidth,D=i.prvHeight,uni.canvasPutImageData({canvasId:"prv-canvas",x:W,y:$,width:C,height:D,data:o,fail:function(){uni.showToast({title:"error_put",duration:2e3})},complete:function(){uni.hideLoading()}},i);case 15:case"end":return e.stop()}}),e)})))()},btop:function(t){return this.base64=t,new Promise((function(i,e){var a=t.split(","),s=a[0].match(/:(.*?);/)[1],n=atob(a[1]),o=n.length,h=new Uint8Array(o);while(o--)h[o]=n.charCodeAt(o);return i((window.URL||window.webkitURL).createObjectURL(new Blob([h],{type:s})))}))},blob:function(t,i){uni.getFileSystemManager().readFile({filePath:t,encoding:"base64",success:function(t){var e="data:image/jpeg;base64,"+t.data;"function"==typeof i&&i(e)}})}}};i.default=h},"4c1f":function(t,i,e){"use strict";e.d(i,"b",(function(){return s})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){return a}));var a={pageMeta:e("7854").default},s=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":t.themeColor}}),e("v-uni-view",{staticClass:"nc-modify-content"},[e("v-uni-view",{staticClass:"modify"},[e("v-uni-view",[""==t.newImg?e("v-uni-image",{attrs:{src:t.memberImg?t.$util.img(t.memberImg):t.$util.getDefaultImage().head,mode:"aspectFill"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.memberImg=t.$util.getDefaultImage().head}}}):e("v-uni-image",{attrs:{src:t.$util.img(t.newImg),mode:"aspectFill"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.newImg=t.$util.getDefaultImage().head}}})],1)],1),e("v-uni-view",{staticClass:"opection-box"},[""==t.newImg?[e("v-uni-button",{attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.chooseImage()}}},[t._v("点击上传")])]:[e("v-uni-view",{staticClass:"opec"},[e("v-uni-button",{staticClass:"mini",attrs:{size:"mini",type:"primary"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.save()}}},[t._v("确认保存")]),e("v-uni-button",{staticClass:"mini",attrs:{size:"mini",type:"primary"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.chooseImage()}}},[t._v("重新上传")])],1)]],2),e("img-cropping",{ref:"imgCropping",attrs:{selWidth:"300",selHeight:"300"},on:{upload:function(i){arguments[0]=i=t.$handleEvent(i),t.myUpload.apply(void 0,arguments)}}})],1)],1)},n=[]},"700e":function(t,i,e){var a=e("c86c");i=a(!1),i.push([t.i,".my-canvas[data-v-044cab99]{display:flex;position:fixed!important;background:#000;left:0;z-index:100000;width:100%}.my-avatar[data-v-044cab99]{width:%?150?%;height:%?150?%;border-radius:100%}.oper-canvas[data-v-044cab99]{display:flex;position:fixed!important;left:0;z-index:100001;width:100%}.prv-canvas[data-v-044cab99]{display:flex;position:fixed!important;background:#000;left:0;z-index:200000;width:100%}.oper-wrapper[data-v-044cab99]{height:50px;position:fixed!important;box-sizing:border-box;background:#000;width:100%;left:0;bottom:0;z-index:100009;flex-direction:row}.oper[data-v-044cab99]{display:flex;flex-direction:column;justify-content:center;\r\n\t/* padding: 10upx 20upx 80upx 20upx; */width:100%;height:100%;box-sizing:border-box;align-self:center}.btn-wrapper[data-v-044cab99]{display:flex;flex-direction:row;\r\n\r\n\r\nheight:50px;\r\njustify-content:space-between}.btn-wrapper uni-view[data-v-044cab99]{display:flex;align-items:center;justify-content:center;font-size:%?28?%}.btn-wrapper .cancel[data-v-044cab99]{color:#fff;font-weight:700;text-align:center}.btn-wrapper .upload[data-v-044cab99]{background:#31bb6d;color:#fff;font-weight:700;height:%?60?%;line-height:%?60?%;padding:0 %?30?%;border-radius:%?8?%}.btn-wrapper .iconfont[data-v-044cab99]{color:#fff;font-size:18px}.hover[data-v-044cab99]{background:#f1f1f1;border-radius:6%}.clr-wrapper[data-v-044cab99]{display:flex;flex-direction:row;flex-grow:1}.clr-wrapper uni-view[data-v-044cab99]{display:flex;align-items:center;justify-content:center;font-size:14px;color:#333;border:1px solid #f1f1f1;border-radius:6%}.my-slider[data-v-044cab99]{flex-grow:1}",""]),t.exports=i},"70be1":function(t,i,e){"use strict";e.r(i);var a=e("74b9"),s=e("b26e");for(var n in s)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(n);e("aab2");var o=e("828b"),h=Object(o["a"])(s["default"],a["b"],a["c"],!1,null,"044cab99",null,!1,a["a"],void 0);i["default"]=h.exports},"74b9":function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){}));var a=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",[t.imgSrc.imgSrc?e("v-uni-image",{staticClass:"my-avatar",style:[t.iS],attrs:{src:t.imgSrc.imgSrc},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.fSelect.apply(void 0,arguments)}}}):t._e(),e("v-uni-canvas",{staticClass:"my-canvas",style:{top:t.sT,height:t.csH},attrs:{"canvas-id":"avatar-canvas",id:"avatar-canvas","disable-scroll":"false"}}),e("v-uni-canvas",{staticClass:"oper-canvas",style:{top:t.sT,height:t.csH},attrs:{"canvas-id":"oper-canvas",id:"oper-canvas","disable-scroll":"false"},on:{touchstart:function(i){arguments[0]=i=t.$handleEvent(i),t.fStart.apply(void 0,arguments)},touchmove:function(i){arguments[0]=i=t.$handleEvent(i),t.fMove.apply(void 0,arguments)},touchend:function(i){arguments[0]=i=t.$handleEvent(i),t.fEnd.apply(void 0,arguments)}}}),e("v-uni-canvas",{staticClass:"prv-canvas",style:{height:t.csH,top:t.pT},attrs:{"canvas-id":"prv-canvas",id:"prv-canvas","disable-scroll":"false"},on:{touchstart:function(i){arguments[0]=i=t.$handleEvent(i),t.fHideImg.apply(void 0,arguments)}}}),e("v-uni-view",{staticClass:"oper-wrapper",style:{display:t.sD,top:t.tp}},[e("v-uni-view",{staticClass:"oper"},[t.sO?e("v-uni-view",{staticClass:"btn-wrapper"},[e("v-uni-view",{staticClass:"cancel",style:{width:t.bW},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.fClose.apply(void 0,arguments)}}},[e("v-uni-text",[t._v("取消")])],1),e("v-uni-view",{style:{width:t.bW},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.fSelect.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"iconfont icon-shangchuan"})],1),e("v-uni-view",{style:{width:t.bW,display:t.bD},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.fRotate.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"iconfont icon-xuanzhuan"})],1),e("v-uni-view",{style:{width:t.bW},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.fUpload.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"upload"},[t._v("上传")])],1)],1):e("v-uni-view",{staticClass:"clr-wrapper"},[e("v-uni-slider",{staticClass:"my-slider",attrs:{"block-size":"25",value:"0",min:"-100",max:"100",activeColor:"red",backgroundColor:"green","block-color":"grey","show-value":!0},on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.fColorChange.apply(void 0,arguments)}}}),e("v-uni-view",{style:{width:t.bW},attrs:{"hover-class":"hover"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.fPrvUpload.apply(void 0,arguments)}}},[e("v-uni-text",[t._v("上传")])],1)],1)],1)],1)],1)},s=[]},7854:function(t,i,e){"use strict";e.r(i);var a=e("8ba8"),s=e("f48d");for(var n in s)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(n);var o=e("828b"),h=Object(o["a"])(s["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);i["default"]=h.exports},8733:function(t,i,e){"use strict";var a=e("0206"),s=e.n(a);s.a},"8ba8":function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){}));var a=function(){var t=this.$createElement,i=this._self._c||t;return i("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},s=[]},"915e":function(t,i,e){"use strict";e.r(i);var a=e("4c1f"),s=e("b9ab");for(var n in s)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(n);e("8733");var o=e("828b"),h=Object(o["a"])(s["default"],a["b"],a["c"],!1,null,"08160282",null,!1,a["a"],void 0);i["default"]=h.exports},aab2:function(t,i,e){"use strict";var a=e("bcb9"),s=e.n(a);s.a},b26e:function(t,i,e){"use strict";e.r(i);var a=e("1bd9"),s=e.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(n);i["default"]=s.a},b7b6:function(t,i,e){"use strict";e("6a54");var a=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var s=a(e("70be1")),n={data:function(){return{memberImg:"",newImg:"",imgurl:""}},components:{imgCropping:s.default},onShow:function(){this.storeToken?(this.memberImg=this.memberInfo.headimg,this.imgurl=this.memberInfo.headimg):this.$util.redirectTo("/pages_tool/login/index",{back:"/pages_tool/member/modify_face"},"redirectTo")},methods:{chooseImage:function(){this.$refs.imgCropping.fSelect()},myUpload:function(t){var i=this;uni.request({url:this.$config.baseUrl+"/api/upload/headimgBase64",method:"POST",data:{app_type:"h5",app_type_name:"H5",images:t.base64,token:this.$store.state.token||""},header:{"content-type":"application/x-www-form-urlencoded;application/json"},dataType:"json",responseType:"text",success:function(t){0==t.data.code&&(i.newImg=t.data.data.pic_path,i.imgurl=t.data.data.pic_path)},fail:function(){i.$util.showToast({title:"头像上传失败"})}})},previewImage:function(){uni.previewImage({current:0,urls:this.images})},save:function(){var t=this;this.$api.sendRequest({url:"/api/member/modifyheadimg",data:{headimg:this.imgurl},success:function(i){0==i.code?(t.memberInfo.headimg=t.imgurl,t.$store.commit("setMemberInfo",t.memberInfo),t.$util.showToast({title:"头像修改成功"}),setTimeout((function(){t.$util.redirectTo("/pages_tool/member/info",{},"redirectTo")}),2e3)):t.$util.showToast({title:i.message})}})},uploadFace:function(){var t=this;uni.chooseImage({count:1,sizeType:["compressed"],success:function(i){var e=i.tempFilePaths;t.$api.upload({url:"/api/upload/headimg",filePath:e[0],fileType:"image",success:function(i){i.code&&(t.newImg=i.data.pic_path,t.imgurl=i.data.pic_path)}})}})}}};i.default=n},b9ab:function(t,i,e){"use strict";e.r(i);var a=e("b7b6"),s=e.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(n);i["default"]=s.a},bcb9:function(t,i,e){var a=e("700e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var s=e("967d").default;s("03cf5efd",a,!0,{sourceMap:!1,shadowMode:!1})},c8e9:function(t,i,e){var a=e("c86c");i=a(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */uni-page-body[data-v-08160282]{overflow:hidden}.modify[data-v-08160282]{position:relative;padding-top:%?50?%}.modify uni-view[data-v-08160282]{width:%?500?%;height:%?500?%;margin:0 auto;overflow:hidden;background-color:#fff;border:%?4?% solid #fff;border-radius:100%}.modify uni-view uni-image[data-v-08160282]{width:100%;height:100%}.opection-box[data-v-08160282]{margin-top:%?50?%}.opec[data-v-08160282]{width:100%;padding:0 10%;box-sizing:border-box;display:flex;justify-content:space-between}.opec uni-button[data-v-08160282]{padding:0 %?30?%;height:%?60?%;line-height:%?60?%;border:none}',""]),t.exports=i},cc1b:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("5ef2"),e("64aa"),e("5c47"),e("a1c1"),e("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},s={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,i=getCurrentPages()[0];this.$pageVm=i.$vm||i,uni.onWindowResize((function(i){t.$emit("resize",i)})),this.$pageVm.$on("hook:onPageScroll",(function(i){t.$emit("scroll",i)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,i){t.setStyle({pullToRefresh:{support:i,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,i=String(this.scrollTop);if(-1!==i.indexOf("rpx")&&(i=uni.upx2px(i.replace("rpx",""))),i=parseFloat(i),!isNaN(i)){var e=function e(s){s.scrollTop===i&&(t.$pageVm.$off("hook:onPageScroll",e),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:i,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",e)}})}}}};i.default=s},f48d:function(t,i,e){"use strict";e.r(i);var a=e("cc1b"),s=e.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(n);i["default"]=s.a}}]);