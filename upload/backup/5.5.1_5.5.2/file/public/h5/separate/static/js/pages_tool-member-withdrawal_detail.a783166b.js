(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-withdrawal_detail"],{"05f0":function(t,e,a){"use strict";var i=a("5c3c"),n=a.n(i);n.a},"26b1":function(t,e,a){"use strict";a.r(e);var i=a("f781"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},"2af6":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={pageMeta:a("7854").default,loadingCover:a("c003").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-view",[a("v-uni-view",{staticClass:"money-wrap"},[a("v-uni-text",[t._v(t._s(t.detail.apply_money))])],1),a("v-uni-view",{staticClass:"item"},[a("v-uni-view",{staticClass:"line-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("当前状态")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.detail.status_name))])],1),a("v-uni-view",{staticClass:"line-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("交易号")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.detail.withdraw_no))])],1),a("v-uni-view",{staticClass:"line-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("手续费")]),a("v-uni-text",{staticClass:"value"},[t._v("￥"+t._s(t.detail.service_money))])],1),a("v-uni-view",{staticClass:"line-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("申请时间")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.apply_time)))])],1),t.detail.status?a("v-uni-view",{staticClass:"line-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("审核时间")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.audit_time)))])],1):t._e(),t.detail.bank_name?a("v-uni-view",{staticClass:"line-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("银行名称")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.detail.bank_name))])],1):t._e(),a("v-uni-view",{staticClass:"line-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("收款账号")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.detail.account_number))])],1),-1==t.detail.status&&t.detail.refuse_reason?a("v-uni-view",{staticClass:"line-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("拒绝理由")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.detail.refuse_reason))])],1):t._e(),2==t.detail.status?a("v-uni-view",{staticClass:"line-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("转账方式名称")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.detail.transfer_type_name))])],1):t._e(),2==t.detail.status?a("v-uni-view",{staticClass:"line-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("转账时间")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.payment_time)))])],1):t._e()],1),t.$util.isWeiXin()&&t.withdrawInfo.transfer_type&&"wechatpay"==t.detail.transfer_type&&1==t.detail.status?a("v-uni-view",{staticClass:"operations"},[a("v-uni-button",{staticClass:"operation",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.merchantTransfer()}}},[t._v("收款")])],1):t._e(),a("loading-cover",{ref:"loadingCover"})],1)],1)},s=[]},"5c3c":function(t,e,a){var i=a("fc8e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("261ab550",i,!0,{sourceMap:!1,shadowMode:!1})},7854:function(t,e,a){"use strict";a.r(e);var i=a("8ba8"),n=a("f48d");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);var o=a("828b"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=r.exports},"8ba8":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},cc1b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var a=function a(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",a),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",a)}})}}}};e.default=n},d2d2:function(t,e,a){"use strict";a.r(e);var i=a("2af6"),n=a("26b1");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("05f0");var o=a("828b"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"341c73d7",null,!1,i["a"],void 0);e["default"]=r.exports},f48d:function(t,e,a){"use strict";a.r(e);var i=a("cc1b"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},f781:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("2634")),s=i(a("2fdc")),o={data:function(){return{id:0,detail:{},withdrawInfo:{},requestCount:0}},onLoad:function(t){var e=this;return(0,s.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.id=t.id||0,a.next=3,e.getWithdrawConfig();case 3:t.action&&e.merchantTransfer();case 4:case"end":return a.stop()}}),a)})))()},onShow:function(){this.storeToken?this.getDetail():this.$util.redirectTo("/pages_tool/login/index",{back:"/pages_tool/member/point"},"redirectTo")},onPullDownRefresh:function(){this.getDetail()},methods:{merchantTransfer:function(){var t=this;uni.showLoading({});var e="";this.$util.isWeiXin()&&(e=this.withdrawInfo.wechat_appid),this.$util.merchantTransfer({transfer_type:"member_withdraw",id:this.id},{mch_id:this.withdrawInfo.mch_id,app_id:e},(function(e){"requestMerchantTransfer:ok"===e.err_msg&&t.updateStatusToInProcess((function(){t.getDetail(!0)}))}))},getWithdrawConfig:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$api.sendRequest({url:"/wechatpay/api/transfer/getWithdrawConfig",async:!1});case 2:a=e.sent,0==a.code&&(t.withdrawInfo=a.data);case 4:case"end":return e.stop()}}),e)})))()},updateStatusToInProcess:function(t){var e=this;this.$refs.loadingCover&&this.$refs.loadingCover.show(),this.$api.sendRequest({url:"/wechatpay/api/transfer/inprocess",data:{from_type:"member_withdraw",relate_tag:this.id},success:function(a){a.code>=0&&(e.$refs.loadingCover&&e.$refs.loadingCover.hide(),"function"==typeof t&&t())}})},getDetail:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$api.sendRequest({url:"/api/memberwithdraw/detail",data:{id:this.id},success:function(a){a.data&&(t.detail=a.data,e&&t.requestCount<10&&3==t.detail.status&&(t.requestCount++,setTimeout((function(){t.getDetail(!0)}),1e3)),uni.stopPullDownRefresh()),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})}}};e.default=o},fc8e:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.money-wrap[data-v-341c73d7]{text-align:center;font-size:%?50?%;font-weight:700;margin:%?40?%;border-bottom:%?2?% solid #eee;padding:%?40?%}.item[data-v-341c73d7]{margin:%?40?%}.item .line-wrap[data-v-341c73d7]{margin-bottom:%?20?%}.item .line-wrap .label[data-v-341c73d7]{display:inline-block;width:%?200?%;color:#909399;font-size:%?28?%}.item .line-wrap .value[data-v-341c73d7]{display:inline-block;font-size:%?28?%}.operations[data-v-341c73d7]{margin-top:%?60?%;bottom:0;width:100%;position:fixed;padding:0 %?30?%;box-sizing:border-box;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom);z-index:10}.operations .operation[data-v-341c73d7]{height:%?80?%;line-height:%?80?%;border-radius:%?80?%;margin:%?30?% 0 %?30?%;font-size:%?32?%}.operations .operation uni-text[data-v-341c73d7]{margin-right:%?10?%;font-size:%?28?%}',""]),t.exports=e}}]);