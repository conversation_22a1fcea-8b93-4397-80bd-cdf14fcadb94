(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-giftcard-detail"],{"0817":function(t,i,e){"use strict";e("6a54");var a=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("5ef2"),e("5c47"),e("2c10"),e("a1c1"),e("23f4"),e("7d2f"),e("9c4e"),e("ab80"),e("aa9c"),e("473f"),e("bf0f"),e("3efd");var o=a(e("af87")),n=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,r=/^<\/([-A-Za-z0-9_]+)[^>]*>/,s=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,l=v("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),d=v("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),c=v("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),f=v("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),u=v("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),g=v("script,style");function v(t){for(var i={},e=t.split(","),a=0;a<e.length;a++)i[e[a]]=!0;return i}var m=function(t){t=function(t){return t.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(t),t=function(t){return t=t.replace(/<!--[\s\S]*-->/gi,""),t}(t),t=function(t){var i='<img style="width:100% !important;display:block;max-width: '.concat("100%",' !important;"');return t=t.replace(/\\/g,"").replace(/<img/g,i),t=t.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(t,e){return i+' src="'+o.default.img(e)+'"/>'})),t}(t),t=function(t){return t=t.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(t,i){return t=t.replace(/[:](\s?)[\s\S]*/gi,(function(t,i){return t.replace(/"/g,"'")})),t})),t}(t);var i=[],e={node:"root",children:[]};return function(t,i){var e,a,o,v=[],m=t;v.last=function(){return this[this.length-1]};while(t){if(a=!0,v.last()&&g[v.last()])t=t.replace(new RegExp("([\\s\\S]*?)</"+v.last()+"[^>]*>"),(function(t,e){return e=e.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),i.chars&&i.chars(e),""})),b("",v.last());else if(0==t.indexOf("\x3c!--")?(e=t.indexOf("--\x3e"),e>=0&&(i.comment&&i.comment(t.substring(4,e)),t=t.substring(e+3),a=!1)):0==t.indexOf("</")?(o=t.match(r),o&&(t=t.substring(o[0].length),o[0].replace(r,b),a=!1)):0==t.indexOf("<")&&(o=t.match(n),o&&(t=t.substring(o[0].length),o[0].replace(n,p),a=!1)),a){e=t.indexOf("<");var h=e<0?t:t.substring(0,e);t=e<0?"":t.substring(e),i.chars&&i.chars(h)}if(t==m)throw"Parse Error: "+t;m=t}function p(t,e,a,o){if(e=e.toLowerCase(),d[e])while(v.last()&&c[v.last()])b("",v.last());if(f[e]&&v.last()==e&&b("",e),o=l[e]||!!o,o||v.push(e),i.start){var n=[];a.replace(s,(function(t,i){var e=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:u[i]?i:"";n.push({name:i,value:e,escaped:e.replace(/(^|[^\\])"/g,'$1\\"')})})),i.start&&i.start(e,n,o)}}function b(t,e){if(e){for(a=v.length-1;a>=0;a--)if(v[a]==e)break}else var a=0;if(a>=0){for(var o=v.length-1;o>=a;o--)i.end&&i.end(v[o]);v.length=a}}b()}(t,{start:function(t,a,o){var n={name:t};if(0!==a.length&&(n.attrs=function(t){return t.reduce((function(t,i){var e=i.value,a=i.name;return t[a]?t[a]=t[a]+" "+e:t[a]=e,t}),{})}(a)),o){var r=i[0]||e;r.children||(r.children=[]),r.children.push(n)}else i.unshift(n)},end:function(t){var a=i.shift();if(a.name!==t&&console.error("invalid state: mismatch end tag"),0===i.length)e.children.push(a);else{var o=i[0];o.children||(o.children=[]),o.children.push(a)}},chars:function(t){var a={type:"text",text:t};if(0===i.length)e.children.push(a);else{var o=i[0];o.children||(o.children=[]),o.children.push(a)}},comment:function(t){var e={node:"comment",text:t},a=i[0];a.children||(a.children=[]),a.children.push(e)}}),e.children};i.default=m},"0f72":function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){return a}));var a={pageMeta:e("7854").default,nsMpHtml:e("d108").default,loadingCover:e("c003").default,nsLogin:e("2910").default},o=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":t.themeColor}}),e("v-uni-view",{staticClass:"page"},[e("v-uni-view",{staticClass:"detail-box"},[e("v-uni-view",{staticClass:"detail-img"},[e("v-uni-image",{attrs:{src:t.$util.img(t.image.media_path)}}),t.giftDetail&&"balance"==t.giftDetail.card_right_type?e("v-uni-view",{staticClass:"card-label"},[t._v(t._s(t.giftDetail.balance)+"元储值卡")]):t._e(),t.giftDetail&&"goods"==t.giftDetail.card_right_type?e("v-uni-view",{staticClass:"card-label-img"},[e("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/giftcard/giftcard-label.png"),mode:"heightFix"}})],1):t._e()],1),e("v-uni-view",{staticClass:"detail-head"},[t._v("选择卡面")]),t.imageList?e("v-uni-scroll-view",{staticClass:"img-list",attrs:{"scroll-x":!0,"show-scrollbar":!1,"enable-flex":"true"}},t._l(t.imageList,(function(i,a){return e("v-uni-view",{key:a,staticClass:"img-item",class:{selected:t.image.media_id==i.media_id},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.image=i}}},[e("v-uni-image",{attrs:{src:t.$util.img(i.media_path),mode:"heightFix"}}),t.image.media_id==i.media_id?e("v-uni-text",{staticClass:"iconfont icon-right1"}):t._e()],1)})),1):t._e(),t.giftDetail&&"goods"==t.giftDetail.card_right_type?[t.giftDetail.goods_list?e("v-uni-view",{staticClass:"gift-list"},["all"==t.giftDetail.card_right_goods_type?e("v-uni-view",{staticClass:"detail-head"},[t._v("以下商品 在使用时任选"+t._s(t.giftDetail.card_right_goods_count)+"件")]):e("v-uni-view",{staticClass:"detail-head"},[t._v("本礼品卡包含以下商品")]),t._l(t.giftDetail.goods_list,(function(i,a){return e("v-uni-view",{key:a,staticClass:"gift-item"},[e("v-uni-view",{staticClass:"goods-img"},[e("v-uni-image",{attrs:{src:t.$util.img(i.sku_info.sku_image),mode:"widthFix"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.imgError(a)}}})],1),e("v-uni-view",{staticClass:"goods-info"},[e("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(i.sku_info.sku_name))]),e("v-uni-view",{staticClass:"gift-bottom"},[e("v-uni-view",{staticClass:"gift-price price-font"}),"item"==t.giftDetail.card_right_goods_type?e("v-uni-view",{staticClass:"gift-num"},[e("v-uni-view",{staticClass:"num"},[t._v("x"+t._s(i.goods_num))])],1):t._e()],1)],1)],1)}))],2):t._e(),e("v-uni-view",{staticClass:"card-box"},[e("v-uni-view",{staticClass:"detail-head"},[t._v("使用须知")]),e("v-uni-view",{staticClass:"card-info"},[e("v-uni-view",{staticClass:"goods-details"},[t.giftDetail.instruction?e("ns-mp-html",{attrs:{content:t.giftDetail.instruction}}):e("ns-mp-html",{attrs:{content:t.giftDetail.giftcard_desc}})],1)],1)],1),t.giftDetail&&t.giftDetail.desc?e("v-uni-view",{staticClass:"card-box"},[e("v-uni-view",{staticClass:"detail-head"},[t._v("卡片详情")]),e("v-uni-view",{staticClass:"card-info"},[e("v-uni-view",{staticClass:"goods-details"},[e("ns-mp-html",{attrs:{content:t.giftDetail.desc}})],1)],1)],1):t._e(),e("v-uni-view",{staticClass:"bottom-btn"},[e("v-uni-view",{staticClass:"bottom-left"},[e("v-uni-view",{staticClass:"price-box"},[e("v-uni-view",[t._v("销售价")]),e("v-uni-view",{staticClass:" price-style small"},[t._v("￥")]),e("v-uni-view",{staticClass:" price-style large"},[t._v(t._s(t.giftDetail.card_price))])],1),e("v-uni-view",{staticClass:"title-sub"})],1),e("v-uni-button",{attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.buy("goods")}}},[t._v("制作礼包")])],1)]:t._e(),t.giftDetail&&"balance"==t.giftDetail.card_right_type?[e("v-uni-view",{staticClass:"card-box"},[e("v-uni-view",{staticClass:"detail-head"},[t._v("使用须知")]),e("v-uni-view",{staticClass:"card-info"},[e("v-uni-view",{staticClass:"goods-details"},[t.giftDetail.instruction?e("ns-mp-html",{attrs:{content:t.giftDetail.instruction}}):e("ns-mp-html",{attrs:{content:t.giftDetail.giftcard_desc}})],1)],1)],1),t.giftDetail&&t.giftDetail.desc?e("v-uni-view",{staticClass:"card-box"},[e("v-uni-view",{staticClass:"detail-head"},[t._v("卡片详情")]),e("v-uni-view",{staticClass:"card-info"},[e("v-uni-view",{staticClass:"goods-details"},[e("ns-mp-html",{attrs:{content:t.giftDetail.desc}})],1)],1)],1):t._e(),e("v-uni-view",{staticClass:"bottom-btn"},[e("v-uni-view",{staticClass:"bottom-left"},[e("v-uni-view",{staticClass:"price-box"},[e("v-uni-view",[t._v("销售价")]),e("v-uni-view",{staticClass:" price-style small"},[t._v("￥")]),e("v-uni-view",{staticClass:" price-style large"},[t._v(t._s(t.giftDetail.card_price))])],1),e("v-uni-view",{staticClass:"title-sub"})],1),e("v-uni-button",{attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.buy("balance")}}},[t._v("制作礼包")])],1)]:t._e()],2),e("v-uni-view",{staticClass:"tab-bar-placeholder"}),e("loading-cover",{ref:"loadingCover"}),e("ns-login",{ref:"login"})],1)],1)},n=[]},"36d9":function(t,i,e){"use strict";e("6a54");var a=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("bf0f"),e("2797"),e("5ef2");a(e("0817"));var o={data:function(){return{image:"",imageList:null,giftcardId:0,giftDetail:null,goodsList:null,btnSwitch:!1}},onLoad:function(t){var i=this;if(t.source_member&&uni.setStorageSync("source_member",t.source_member),t.id&&(this.giftcardId=t.id),t.scene){var e=decodeURIComponent(t.scene);e=e.split("&"),e.length&&e.forEach((function(t){-1!=t.indexOf("id")&&(i.giftcardId=t.split("-")[1])}))}this.getGiftcardDetail()},onShow:function(){},methods:{getGiftcardDetail:function(){var t=this;this.$api.sendRequest({url:"/giftcard/api/giftcard/detail",data:{giftcard_id:this.giftcardId},success:function(i){i.code>=0&&i.data?(t.giftDetail=i.data,t.imageList=i.data.media_list,t.image=t.imageList.length>0?t.imageList[0]:"",t.giftDetail&&t.$langConfig.title(t.giftDetail.card_name),t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:i.message,mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages_promotion/giftcard/not_exist")}),1500))}})},buy:function(t){var i=this,e={media_id:this.image.media_id,media_path:this.image.media_path,num:1,giftcard_id:this.giftcardId};if(this.btnSwitch)return!1;this.btnSwitch=!0,uni.setStorage({key:"giftcardOrderCreateData",data:e,success:function(){i.$util.redirectTo("/pages_promotion/giftcard/payment"),i.btnSwitch=!1}})},imgError:function(t){this.giftDetail.goods_list[t].sku_info.sku_image=this.$util.getDefaultImage().goods}}};i.default=o},7650:function(t,i,e){"use strict";e.r(i);var a=e("36d9"),o=e.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(n);i["default"]=o.a},7683:function(t,i,e){"use strict";var a=e("e9cf"),o=e.n(a);o.a},7854:function(t,i,e){"use strict";e.r(i);var a=e("8ba8"),o=e("f48d");for(var n in o)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(n);var r=e("828b"),s=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);i["default"]=s.exports},"8ba8":function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return o})),e.d(i,"a",(function(){}));var a=function(){var t=this.$createElement,i=this._self._c||t;return i("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},"96e4":function(t,i,e){"use strict";var a=e("f75a"),o=e.n(a);o.a},a78c:function(t,i,e){"use strict";e.r(i);var a=e("0f72"),o=e("7650");for(var n in o)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(n);e("7683"),e("96e4");var r=e("828b"),s=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"683f8c51",null,!1,a["a"],void 0);i["default"]=s.exports},cc1b:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("5ef2"),e("64aa"),e("5c47"),e("a1c1"),e("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,i=getCurrentPages()[0];this.$pageVm=i.$vm||i,uni.onWindowResize((function(i){t.$emit("resize",i)})),this.$pageVm.$on("hook:onPageScroll",(function(i){t.$emit("scroll",i)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,i){t.setStyle({pullToRefresh:{support:i,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,i=String(this.scrollTop);if(-1!==i.indexOf("rpx")&&(i=uni.upx2px(i.replace("rpx",""))),i=parseFloat(i),!isNaN(i)){var e=function e(o){o.scrollTop===i&&(t.$pageVm.$off("hook:onPageScroll",e),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:i,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",e)}})}}}};i.default=o},e955:function(t,i,e){var a=e("c86c");i=a(!1),i.push([t.i,".img-list[data-v-683f8c51] .uni-scroll-view-content{display:flex}",""]),t.exports=i},e9cf:function(t,i,e){var a=e("ed20");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=e("967d").default;o("15dd3bfb",a,!0,{sourceMap:!1,shadowMode:!1})},ed20:function(t,i,e){var a=e("c86c");i=a(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.page[data-v-683f8c51]{background:#f9fbff;min-height:100vh}.detail-box[data-v-683f8c51]{padding:%?30?% %?24?%}.detail-box .detail-img[data-v-683f8c51]{border-radius:%?18?%;overflow:hidden;display:flex;height:%?400?%;width:100%;align-items:center;position:relative;justify-content:center}.detail-box .detail-img uni-image[data-v-683f8c51]{width:100%;height:100%;border-radius:%?18?%}.detail-box .detail-img .card-label[data-v-683f8c51]{position:absolute;line-height:1;padding:%?6?% %?10?%;background-color:#ff2c27;color:#fff;right:0;bottom:0;border-top-left-radius:%?20?%;border-bottom-right-radius:0;font-size:%?34?%;font-weight:700}.detail-box .detail-img .card-label-img[data-v-683f8c51]{position:absolute;line-height:1;right:%?-6?%;bottom:%?-8?%;width:%?100?%}.detail-box .detail-img .card-label-img uni-image[data-v-683f8c51]{width:100%;height:%?100?%}.detail-box .detail-head[data-v-683f8c51]{display:flex;align-items:center;justify-content:center;font-size:%?28?%;color:#222;padding:0;font-weight:700;margin-top:%?50?%}.detail-box .detail-head[data-v-683f8c51]::before{content:"";background-color:#222;width:%?20?%;height:%?5?%;display:block;margin-right:%?10?%}.detail-box .detail-head[data-v-683f8c51]::after{content:"";background-color:#222;width:%?20?%;height:%?5?%;display:block;margin-left:%?10?%}.detail-box[data-v-683f8c51] .img-list{display:flex;flex-direction:row;white-space:nowrap;margin-top:%?20?%;margin-bottom:%?40?%;height:%?160?%}.detail-box[data-v-683f8c51] .img-list .uni-scroll-view-content{display:flex}.detail-box[data-v-683f8c51] .img-list .img-item{overflow:hidden;margin-right:%?20?%;height:%?160?%;box-sizing:border-box;position:relative;border:%?4?% solid hsla(0,0%,100%,0);flex-shrink:0;display:flex;line-height:1;padding:0;border-radius:%?18?%;background-color:hsla(0,0%,100%,0);width:-webkit-max-content;width:max-content}.detail-box[data-v-683f8c51] .img-list .img-item uni-image{width:100%;height:%?160?%;border-radius:%?18?%}.detail-box[data-v-683f8c51] .img-list .img-item.selected{border:%?4?% solid var(--giftcard-promotion-color);background-color:var(--giftcard-promotion-color)}.detail-box[data-v-683f8c51] .img-list .img-item.selected::after{content:" ";width:0;height:0;position:absolute;color:#fff;right:%?-2?%;top:%?-2?%;display:flex;align-items:center;justify-content:center;font-size:%?16?%;border-style:solid;border-width:0 %?40?% %?40?% 0;border-color:transparent var(--giftcard-promotion-color) transparent}.detail-box[data-v-683f8c51] .img-list .img-item.selected .iconfont{position:absolute;right:%?2?%;top:%?2?%;color:#fff;font-size:%?16?%;z-index:1}.detail-box .gift-list[data-v-683f8c51]{display:flex;margin-top:%?30?%;flex-wrap:wrap;background-color:#fff;padding:%?24?% 0;border-radius:%?16?%}.detail-box .gift-list .detail-head[data-v-683f8c51]{width:100%;margin:0 0 %?20?%;padding-bottom:%?20?%;border-bottom:%?2?% solid #f4f4f4}.detail-box .gift-list .gift-item[data-v-683f8c51]{width:100%;background-color:#fff;margin:0 %?24?% %?24?%;display:flex}.detail-box .gift-list .gift-item .goods-img[data-v-683f8c51]{position:relative;overflow:hidden;width:%?200?%;height:%?200?%}.detail-box .gift-list .gift-item .goods-img uni-image[data-v-683f8c51]{width:100%;position:absolute;top:50%;bottom:0;left:0;right:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.detail-box .gift-list .gift-item .goods-info[data-v-683f8c51]{display:flex;flex-direction:column;width:calc(100% - %?200?%)}.detail-box .gift-list .gift-item .goods-name[data-v-683f8c51]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;padding:0 %?20?%;height:%?100?%;box-sizing:border-box;font-weight:700}.detail-box .gift-list .gift-item .gift-bottom[data-v-683f8c51]{padding:0 %?20?%;display:flex;line-height:1;margin-top:%?50?%;justify-content:space-between}.detail-box .gift-list .gift-item .gift-bottom .gift-price[data-v-683f8c51]{color:var(--price-color)}.detail-box .gift-list .gift-item .gift-num[data-v-683f8c51]{display:flex}.detail-box .gift-list .gift-item .gift-num .num[data-v-683f8c51]{width:%?52?%;text-align:center;height:%?50?%;line-height:%?50?%;display:flex;align-items:center;justify-content:center;color:#999;font-weight:700}.detail-box .bottom-btn[data-v-683f8c51]{position:fixed;bottom:0;width:100%;background-color:#fff;left:0;padding:%?24?% 0;display:flex;align-items:center;justify-content:space-between;flex-direction:row;padding-bottom:%?24?%;padding-bottom:calc(constant(safe-area-inset-bottom) + %?24?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?24?%)}.detail-box .bottom-btn .bottom-left[data-v-683f8c51]{display:flex;align-items:center;padding:0 %?24?%;padding-left:%?40?%}.detail-box .bottom-btn .title-sub[data-v-683f8c51]{font-size:%?22?%;color:#999}.detail-box .bottom-btn .price-box[data-v-683f8c51]{display:flex;align-items:baseline;margin-right:%?20?%}.detail-box .bottom-btn .price-box > uni-view[data-v-683f8c51]:first-child{font-weight:700;color:var(--price-color);margin-right:%?10?%}.detail-box .bottom-btn uni-button[data-v-683f8c51]{width:50%;background-color:var(--giftcard-promotion-color)}.tab-bar-placeholder[data-v-683f8c51]{width:100%;padding-bottom:calc(constant(safe-area-inset-bottom) + %?130?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?130?%)}.card-box[data-v-683f8c51]{background-color:#fff;padding:%?24?%;margin-top:%?20?%}.card-box .detail-head[data-v-683f8c51]{margin-top:%?20?%}.card-box .card-info[data-v-683f8c51]{display:flex;flex-direction:column;margin-top:%?15?%}.card-box .card-info .card-item[data-v-683f8c51]{display:flex;flex-direction:row;margin:%?10?% 0}.card-box .card-info .card-item > uni-view[data-v-683f8c51]:first-child{width:%?140?%;font-size:%?28?%}.card-box .card-info .card-item > uni-view[data-v-683f8c51]:last-child{width:calc(100% - %?140?%);font-size:%?28?%}.card-box .card-info .card-item > uni-view.color[data-v-683f8c51]{color:#364385}.goods-details[data-v-683f8c51]{overflow:hidden;word-break:break-all}.goods-details *[data-v-683f8c51]{max-width:100%;word-break:break-all}',""]),t.exports=i},f48d:function(t,i,e){"use strict";e.r(i);var a=e("cc1b"),o=e.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(n);i["default"]=o.a},f75a:function(t,i,e){var a=e("e955");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=e("967d").default;o("06c4d1f0",a,!0,{sourceMap:!1,shadowMode:!1})}}]);