(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-fenxiao-withdraw_apply"],{"312f":function(t,a,i){var n=i("c86c");a=n(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container[data-v-051a55a0]{width:100vw;height:100vh;background:#fff}.empty-box[data-v-051a55a0]{height:%?20?%}.bank-account-wrap[data-v-051a55a0]{margin:0 %?20?%;padding:%?20?% %?30?%;border-bottom:%?2?% solid #f7f7f7;position:relative}.bank-account-wrap .tx-wrap[data-v-051a55a0]{display:flex;justify-content:space-between;margin-right:%?60?%}.bank-account-wrap .tx-wrap .tx-bank[data-v-051a55a0]{margin-right:%?60?%;flex:1;margin-left:%?10?%;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.bank-account-wrap .tx-wrap .tx-img[data-v-051a55a0]{position:absolute;right:%?100?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:%?40?%;height:%?40?%}.bank-account-wrap .tx-wrap .tx-img uni-image[data-v-051a55a0]{width:100%;height:100%}.bank-account-wrap .iconfont[data-v-051a55a0]{position:absolute;right:%?40?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.withdraw-wrap[data-v-051a55a0]{margin:0 %?20?%;padding:%?30?%;border-radius:%?16?%;box-shadow:hsla(0,0%,43.1%,.09) 0 0 %?20?% 0}.withdraw-wrap .money-wrap[data-v-051a55a0]{padding:%?20?% 0;border-bottom:%?2?% solid #eee;display:flex;align-items:baseline}.withdraw-wrap .money-wrap .unit[data-v-051a55a0]{font-size:%?60?%;line-height:1.3}.withdraw-wrap .money-wrap .withdraw-money[data-v-051a55a0]{height:%?70?%;line-height:%?70?%;min-height:%?70?%;padding-left:%?20?%;font-size:%?60?%;flex:1;font-weight:bolder}.withdraw-wrap .money-wrap .delete[data-v-051a55a0]{width:%?40?%;height:%?40?%}.withdraw-wrap .money-wrap .delete uni-image[data-v-051a55a0]{width:100%;height:100%}.withdraw-wrap .bottom[data-v-051a55a0]{display:flex;padding-top:%?20?%}.withdraw-wrap .bottom uni-text[data-v-051a55a0]{line-height:1;flex:2}.withdraw-wrap .bottom .all-tx[data-v-051a55a0]{padding-left:%?10?%}.btn[data-v-051a55a0]{margin:0 %?30?%;margin-top:%?60?%;height:%?80?%;line-height:%?80?%;border-radius:%?10?%;color:#fff;text-align:center;background-color:var(--main-color)}.btn.disabled[data-v-051a55a0]{background:#ccc;border-color:#ccc;color:#fff}.recoend[data-v-051a55a0]{margin-top:%?40?%}.recoend .recoend-con[data-v-051a55a0]{text-align:center}.desc[data-v-051a55a0]{font-size:%?24?%;color:#999}',""]),t.exports=a},"4b20":function(t,a,i){"use strict";i.r(a);var n=i("8d99"),e=i("4be7");for(var o in e)["default"].indexOf(o)<0&&function(t){i.d(a,t,(function(){return e[t]}))}(o);i("81ae");var r=i("828b"),s=Object(r["a"])(e["default"],n["b"],n["c"],!1,null,"051a55a0",null,!1,n["a"],void 0);a["default"]=s.exports},"4be7":function(t,a,i){"use strict";i.r(a);var n=i("c079"),e=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(a,t,(function(){return n[t]}))}(o);a["default"]=e.a},7854:function(t,a,i){"use strict";i.r(a);var n=i("8ba8"),e=i("f48d");for(var o in e)["default"].indexOf(o)<0&&function(t){i.d(a,t,(function(){return e[t]}))}(o);var r=i("828b"),s=Object(r["a"])(e["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);a["default"]=s.exports},"81ae":function(t,a,i){"use strict";var n=i("dee7"),e=i.n(n);e.a},"8ba8":function(t,a,i){"use strict";i.d(a,"b",(function(){return n})),i.d(a,"c",(function(){return e})),i.d(a,"a",(function(){}));var n=function(){var t=this.$createElement,a=this._self._c||t;return a("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},e=[]},"8d99":function(t,a,i){"use strict";i.d(a,"b",(function(){return e})),i.d(a,"c",(function(){return o})),i.d(a,"a",(function(){return n}));var n={pageMeta:i("7854").default,loadingCover:i("c003").default},e=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"container"},[i("v-uni-view",{staticClass:"bank-account-wrap",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goAccount()}}},[t.bankAccountInfo.withdraw_type&&!t.isBalance?i("v-uni-view",{staticClass:"tx-wrap"},[i("v-uni-text",{staticClass:"tx-to"},[t._v("提现到")]),"wechatpay"==t.bankAccountInfo.withdraw_type?i("v-uni-view",{staticClass:"tx-bank"},[t._v("微信默认钱包")]):i("v-uni-view",{staticClass:"tx-bank"},[t._v(t._s(t.bankAccountInfo.bank_account))]),"alipay"==t.bankAccountInfo.withdraw_type?i("v-uni-view",{staticClass:"tx-img"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/member/apply_withdrawal/alipay.png"),mode:"widthFix"}})],1):"bank"==t.bankAccountInfo.withdraw_type?i("v-uni-view",{staticClass:"tx-img"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/member/apply_withdrawal/bank.png"),mode:"widthFix"}})],1):"wechatpay"==t.bankAccountInfo.withdraw_type?i("v-uni-view",{staticClass:"tx-img"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/member/apply_withdrawal/wechatpay.png"),mode:"widthFix"}})],1):t._e()],1):t.isBalance?i("v-uni-view",{staticClass:"tx-wrap"},[i("v-uni-text",{staticClass:"tx-to"},[t._v("提现到")]),i("v-uni-view",{staticClass:"tx-bank"},[t._v("余额")]),i("v-uni-view",{staticClass:"tx-img"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/member/apply_withdrawal/tixian.png"),mode:"widthFix"}})],1)],1):i("v-uni-text",{staticClass:"tx-to"},[t._v("请添加提现账户")]),i("v-uni-view",{staticClass:"iconfont icon-right"})],1),i("v-uni-view",{staticClass:"empty-box"}),i("v-uni-view",{staticClass:"withdraw-wrap"},[i("v-uni-view",{staticClass:"withdraw-wrap-title"},[t._v("提现金额：")]),i("v-uni-view",{staticClass:"money-wrap"},[i("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),i("v-uni-input",{staticClass:"withdraw-money",attrs:{type:"digit"},model:{value:t.withdrawMoney,callback:function(a){t.withdrawMoney=a},expression:"withdrawMoney"}}),t.withdrawMoney?i("v-uni-view",{staticClass:"delete",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.remove.apply(void 0,arguments)}}},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/member/apply_withdrawal/close.png"),mode:"widthFix"}})],1):t._e()],1),i("v-uni-view",{staticClass:"bottom"},[i("v-uni-view",[i("v-uni-text",{staticClass:"color-tip"},[t._v("可提现佣金："+t._s(t.$lang("common.currencySymbol"))+t._s(t._f("moneyFormat")(t.withdrawInfo.account)))]),i("v-uni-text",{staticClass:"all-tx color-base-text",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.allTx.apply(void 0,arguments)}}},[t._v("全部提现")])],1)],1),i("v-uni-view",{staticClass:"desc"},[t.withdrawConfigInfo.withdraw>0?i("v-uni-text",[t._v("最小提现金额为"+t._s(t.$lang("common.currencySymbol"))+t._s(t._f("moneyFormat")(t.withdrawConfigInfo.withdraw))+"，")]):t._e(),t.withdrawConfigInfo.max>0?i("v-uni-text",[t._v("最大提现金额为"+t._s(t.$lang("common.currencySymbol"))+t._s(t._f("moneyFormat")(t.withdrawConfigInfo.max>t.withdrawInfo.account?t.withdrawInfo.account:t.withdrawConfigInfo.max))+"，")]):t._e(),i("v-uni-text",[t._v("手续费为"+t._s(t.withdrawConfigInfo.withdraw_rate+"%"))])],1)],1),i("v-uni-view",{staticClass:"btn color-base-border ns-gradient-otherpages-member-widthdrawal-withdrawal",class:{disabled:""==t.withdrawMoney||0==t.withdrawMoney},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.withdraw.apply(void 0,arguments)}}},[t._v("提现")]),i("v-uni-view",{staticClass:"recoend",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.toWithdrawal.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"recoend-con"},[t._v("提现记录")])],1),i("loading-cover",{ref:"loadingCover"})],1)],1)},o=[]},c079:function(t,a,i){"use strict";i("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,i("e838");var n={data:function(){return{withdrawInfo:{},withdrawConfigInfo:{},bankAccountInfo:{},withdrawMoney:"",isSub:!1,isBalance:0,payList:null}},onLoad:function(t){t.is_balance&&(this.isBalance=t.is_balance)},onShow:function(){this.storeToken?(this.getTransferType(),this.getWithdrawConfigInfo(),this.getBankAccountInfo(),this.getWithdrawInfo()):this.$util.redirectTo("/pages_tool/login/index",{back:"/pages_promotion/fenxiao/withdraw_apply"})},methods:{toWithdrawal:function(){this.$util.redirectTo("/pages_promotion/fenxiao/withdraw_list")},allTx:function(){this.withdrawMoney=this.withdrawInfo.account},remove:function(){this.withdrawMoney=""},getWithdrawInfo:function(){var t=this;this.$api.sendRequest({url:"/fenxiao/api/fenxiao/detail",success:function(a){a.code>=0&&a.data&&(t.withdrawInfo=a.data),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(a){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},getWithdrawConfigInfo:function(){var t=this;this.$api.sendRequest({url:"/fenxiao/api/config/withdraw",success:function(a){a.code>=0&&a.data&&(t.withdrawConfigInfo=a.data)},fail:function(a){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},getBankAccountInfo:function(){var t=this;this.$api.sendRequest({url:"/api/memberbankaccount/defaultinfo",success:function(a){a.code>=0&&(a.data?t.bankAccountInfo=a.data:t.payList&&t.payList.balance&&(t.isBalance=1))}})},getTransferType:function(){var t=this;this.payList=null,this.$api.sendRequest({url:"/fenxiao/api/withdraw/transferType",success:function(a){a.code>=0&&a.data&&(t.payList=a.data)}})},verify:function(){if(""==this.withdrawMoney||this.withdrawMoney<0||isNaN(parseFloat(this.withdrawMoney)))return this.$util.showToast({title:"请输入提现金额"}),!1;var t=this.withdrawInfo.account;return this.withdrawConfigInfo.max>0&&this.withdrawInfo.account>this.withdrawConfigInfo.max&&(t=this.withdrawConfigInfo.max),parseFloat(this.withdrawMoney)>0&&parseFloat(this.withdrawMoney)>parseFloat(t)?(this.$util.showToast({title:"提现金额超出可提现金额"}),!1):!(parseFloat(this.withdrawMoney)>0&&parseFloat(this.withdrawMoney)<parseFloat(this.withdrawConfigInfo.withdraw))||(this.$util.showToast({title:"提现金额小于最低提现金额"}),!1)},withdraw:function(){var t=this;if(this.bankAccountInfo.withdraw_type||this.isBalance){if(this.verify()){if(this.isSub)return;this.isSub=!0;this.bankAccountInfo.withdraw_type;var a=this.isBalance?"balance":this.bankAccountInfo.withdraw_type,i=this.isBalance?"余额":this.bankAccountInfo.branch_bank_name;this.$api.sendRequest({url:"/fenxiao/api/withdraw/apply",data:{apply_money:this.withdrawMoney,transfer_type:a,realname:this.bankAccountInfo.realname,mobile:this.bankAccountInfo.mobile,bank_name:i,account_number:this.bankAccountInfo.bank_account,applet_type:0},success:function(a){a.code>=0?(t.$util.showToast({title:"提现申请成功"}),setTimeout((function(){t.$util.redirectTo("/pages_promotion/fenxiao/withdraw_list",{},"redirectTo")}),1500)):(t.isSub=!1,t.$util.showToast({title:a.message}))},fail:function(a){t.isSub=!1}})}}else this.$util.showToast({title:"请先添加提现方式"})},goAccount:function(){this.$util.redirectTo("/pages_tool/member/account",{back:"/pages_promotion/fenxiao/withdraw_apply",type:"fenxiao"},"redirectTo")},subscribeMessage:function(t){this.$util.subscribeMessage("USER_WITHDRAWAL_SUCCESS",t)}}};a.default=n},cc1b:function(t,a,i){"use strict";i("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},e={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,a=getCurrentPages()[0];this.$pageVm=a.$vm||a,uni.onWindowResize((function(a){t.$emit("resize",a)})),this.$pageVm.$on("hook:onPageScroll",(function(a){t.$emit("scroll",a)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,a){t.setStyle({pullToRefresh:{support:a,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,a=String(this.scrollTop);if(-1!==a.indexOf("rpx")&&(a=uni.upx2px(a.replace("rpx",""))),a=parseFloat(a),!isNaN(a)){var i=function i(e){e.scrollTop===a&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:a,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};a.default=e},dee7:function(t,a,i){var n=i("312f");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var e=i("967d").default;e("a5f70ba2",n,!0,{sourceMap:!1,shadowMode:!1})},f48d:function(t,a,i){"use strict";i.r(a);var n=i("cc1b"),e=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(a,t,(function(){return n[t]}))}(o);a["default"]=e.a}}]);