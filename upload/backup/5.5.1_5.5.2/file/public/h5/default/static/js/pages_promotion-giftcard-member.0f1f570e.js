(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-giftcard-member"],{"015d":function(t,e,i){"use strict";i.r(e);var n=i("0f46"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=n},2407:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.advList.length?i("v-uni-view",{class:["container-box",t.className]},[t.advList.length>1?i("v-uni-swiper",{staticClass:"item-wrap",style:{height:t.swiperHeight+"px"},attrs:{"indicator-dots":t.advList.length>1,"indicator-active-color":"#ffffff",autoplay:!0,interval:3e3,duration:1e3,current:t.currentIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSwiper.apply(void 0,arguments)}}},t._l(t.advList,(function(e,n){return i("v-uni-swiper-item",{key:n,on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.jumppage(e.adv_url)}}},[i("v-uni-view",{staticClass:"image-box"},[i("v-uni-image",{attrs:{src:t.$util.img(e.adv_image),mode:"widthFix",id:"content-wrap"+n}})],1)],1)})),1):i("v-uni-view",{staticClass:"container-box item-wrap"},[i("v-uni-image",{attrs:{src:t.$util.img(t.advList[0]["adv_image"]),mode:"widthFix","lazy-load":"true"},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imageLoad.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumppage(t.advList[0].adv_url)}}})],1)],1):t._e()},a=[]},"2c65":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("7e88")),o={components:{nsAdv:a.default},data:function(){return{tabList:[{link:"/pages_promotion/giftcard/index",text:"首页",path:"public/uniapp/giftcard/icon-index.png",selectedPath:"public/uniapp/giftcard/icon-index-selectd.png",selected:!1},{link:"/pages_promotion/giftcard/list",text:"卡包",path:"public/uniapp/giftcard/icon-card.png",selectedPath:"public/uniapp/giftcard/icon-card-selected.png",selected:!1},{link:"/pages_promotion/giftcard/member",text:"我的",path:"public/uniapp/giftcard/icon-member.png",selectedPath:"public/uniapp/giftcard/icon-member-selected.png",selected:!0}]}},onLoad:function(t){},onShow:function(){},filters:{mobileHide:function(t){return t.substr(0,3)+"****"+t.substr(t.length-4,4)}},methods:{redirectTo:function(t){this.storeToken?this.$util.redirectTo(t):this.$refs.login.open(t)},tabRedirectTo:function(t){this.storeToken?this.$util.redirectTo(t,{},"reLaunch"):this.$refs.login.open(t)},login:function(){if(!this.storeToken){this.$refs.login.open("/pages_promotion/giftcard/member")}}}};e.default=o},"2e5b":function(t,e,i){"use strict";i.r(e);var n=i("54b8"),a=i("ea7b");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("7ce3");var r=i("828b"),c=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"04c9ed05",null,!1,n["a"],void 0);e["default"]=c.exports},"54b8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={pageMeta:i("7854").default,nsLogin:i("2910").default,hoverNav:i("c1f1").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"page"},[i("v-uni-view",{staticClass:"ns-adv"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/giftcard/member-bg.png"),mode:"widthFix"}})],1),i("v-uni-view",{staticClass:"member"},[t.memberInfo?i("v-uni-view",{staticClass:"member-info"},[i("v-uni-view",{staticClass:"member-headimg"},[i("v-uni-image",{attrs:{src:t.memberInfo.headimg?t.$util.img(t.memberInfo.headimg):t.$util.getDefaultImage().head,mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.memberInfo.headimg=t.$util.getDefaultImage().head}}})],1),i("v-uni-view",{staticClass:"member-name"},[i("v-uni-view",{staticClass:"nickname"},[t._v(t._s(t.memberInfo.nickname))]),t.memberInfo.mobile?i("v-uni-view",{staticClass:"mobile"},[t._v("手机号码："+t._s(t._f("mobileHide")(t.memberInfo.mobile)))]):t._e()],1)],1):i("v-uni-view",{staticClass:"member-info",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.login.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"member-headimg"},[i("v-uni-image",{attrs:{src:t.$util.getDefaultImage().head,mode:"widthFix"}})],1),i("v-uni-view",{staticClass:"member-name"},[t._v("登录/注册")])],1)],1),t.memberInfo?i("v-uni-view",{staticClass:"member-box"},[i("v-uni-view",{staticClass:"member-action"},[i("v-uni-view",{staticClass:"action-item",style:{"background-image":"url("+t.$util.img("public/uniapp/giftcard/icon-card-yellow.png")+") "},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirectTo("/pages_promotion/giftcard/receive_list")}}},[i("v-uni-view",{staticClass:"title"},[t._v("收到的卡片")]),i("v-uni-view",{staticClass:"desc"},[t._v("可赠送他人")])],1),i("v-uni-view",{staticClass:"action-item",style:{"background-image":"url("+t.$util.img("public/uniapp/giftcard/icon-card-red.png")+") "},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirectTo("/pages_promotion/giftcard/give_list")}}},[i("v-uni-view",{staticClass:"title"},[t._v("送出的卡片")]),i("v-uni-view",{staticClass:"desc"},[t._v("查看送出的卡片")])],1)],1)],1):t._e(),i("v-uni-view",{staticClass:"member-tool"},[i("v-uni-view",{staticClass:"tool-title"},[t._v("常用功能")]),i("v-uni-view",{staticClass:"tool-list"},[i("v-uni-view",{staticClass:"tool-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirectTo("/pages_promotion/giftcard/order_list")}}},[i("v-uni-view",{staticClass:"tool-left"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/giftcard/icon-history.png"),mode:"widthFix"}}),i("v-uni-text",{staticClass:"tool-name"},[t._v("购买历史")])],1),i("v-uni-view",{staticClass:"tool-icon"},[i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1),i("v-uni-view",{staticClass:"tool-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirectTo("/pages_promotion/giftcard/list")}}},[i("v-uni-view",{staticClass:"tool-left"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/giftcard/member-icon-card.png"),mode:"widthFix"}}),i("v-uni-text",{staticClass:"tool-name"},[t._v("我的卡包")])],1),i("v-uni-view",{staticClass:"tool-icon"},[i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1),i("v-uni-view",{staticClass:"tool-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirectTo("/pages_promotion/giftcard/exchange")}}},[i("v-uni-view",{staticClass:"tool-left"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/giftcard/member-icon-exchange.png"),mode:"widthFix"}}),i("v-uni-text",{staticClass:"tool-name"},[t._v("卡密激活")])],1),i("v-uni-view",{staticClass:"tool-icon"},[i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1)],1)],1),i("v-uni-view",{staticClass:"tab-bar"},[i("v-uni-view",{staticClass:"tabbar-border"}),t._l(t.tabList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.tabRedirectTo(e.link)}}},[i("v-uni-view",{staticClass:"bd"},[i("v-uni-view",{staticClass:"icon"},[i("v-uni-image",{attrs:{src:t.$util.img(e.selected?e.selectedPath:e.path)}})],1),i("v-uni-view",{staticClass:"label",style:{color:e.selected?t.themeStyle&&t.themeStyle.giftcard.giftcard_promotion_color:""}},[t._v(t._s(e.text))])],1)],1)}))],2),i("v-uni-view",{staticClass:"tab-bar-placeholder"}),i("ns-login",{ref:"login"}),i("hover-nav",{attrs:{need:!0}})],1)],1)},o=[]},6102:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47");var n={name:"ns-advert",props:{keyword:{type:String},className:{type:String}},data:function(){return{advList:[],isImage:!1,swiperHeight:150,currentIndex:0}},created:function(){this.getAdvList()},methods:{getAdvList:function(){var t=this;this.$api.sendRequest({url:"/api/adv/detail",data:{keyword:this.keyword},success:function(e){if(0==e.code){var i=e.data.adv_list;for(var n in i)i[n].adv_url&&(i[n].adv_url=JSON.parse(i[n].adv_url));t.advList=e.data.adv_list,t.$nextTick((function(){t.setSwiperHeight()}))}}})},jumppage:function(t){this.$util.diyRedirectTo(t)},imageLoad:function(t){this.isImage=!0},changeSwiper:function(t){var e=this;this.currentIndex=t.detail.current,this.$nextTick((function(){e.setSwiperHeight()}))},setSwiperHeight:function(){var t=this;this.advList.length>1&&setTimeout((function(){var e="#content-wrap"+t.currentIndex,i=uni.createSelectorQuery().in(t);i.select(e).boundingClientRect(),i.exec((function(e){e&&e[0]&&(t.swiperHeight=e[0].height)}))}),10)}}};e.default=n},7854:function(t,e,i){"use strict";i.r(e);var n=i("8ba8"),a=i("f48d");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);var r=i("828b"),c=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=c.exports},"7ce3":function(t,e,i){"use strict";var n=i("a443"),a=i.n(n);a.a},"7e88":function(t,e,i){"use strict";i.r(e);var n=i("2407"),a=i("f016");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("a44f");var r=i("828b"),c=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"9caa2b5c",null,!1,n["a"],void 0);e["default"]=c.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},a443:function(t,e,i){var n=i("a72b");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("56f6e2cb",n,!0,{sourceMap:!1,shadowMode:!1})},a44f:function(t,e,i){"use strict";var n=i("d87f"),a=i.n(n);a.a},a725:function(t,e,i){"use strict";var n=i("ac2a"),a=i.n(n);a.a},a72b:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.page[data-v-04c9ed05]{background:#f9fbff;min-height:100vh}.ns-adv[data-v-04c9ed05]{margin:0;border-radius:0;line-height:1;height:-webkit-fit-content;height:fit-content;background:#f9fbff;width:100%;position:absolute}.ns-adv uni-image[data-v-04c9ed05]{width:100%}.member-tool[data-v-04c9ed05]{margin:%?24?% %?50?%;background-color:#fff;box-shadow:0 %?6?% %?18?% 0 rgba(38,56,120,.06);border-radius:%?18?%;display:flex;flex-direction:column;position:relative;padding:%?24?% %?30?%}.member-tool .tool-title[data-v-04c9ed05]{font-size:%?32?%;font-weight:700}.member-tool .tool-list[data-v-04c9ed05]{margin-top:%?20?%}.member-tool .tool-list .tool-item[data-v-04c9ed05]{display:flex;justify-content:space-between;padding:%?20?% 0;color:#333}.member-tool .tool-list .tool-item .iconfont[data-v-04c9ed05]{color:#333}.member-tool .tool-list .tool-item .tool-left[data-v-04c9ed05]{display:flex;align-items:center}.member-tool .tool-list .tool-item .tool-left uni-image[data-v-04c9ed05]{width:%?60?%;max-height:%?60?%}.member-tool .tool-list .tool-item .tool-left .tool-name[data-v-04c9ed05]{color:#333;margin-left:%?14?%;font-size:%?26?%;line-height:1;font-weight:700}.member[data-v-04c9ed05]{margin:0 25px;padding:%?40?% 0}.member .member-info[data-v-04c9ed05]{position:relative;display:flex;align-items:center}.member .member-info .member-headimg[data-v-04c9ed05]{width:%?100?%;height:%?100?%;border:%?2?% solid #fff;border-radius:50%;overflow:hidden;background-color:#fff}.member .member-info .member-headimg uni-image[data-v-04c9ed05]{width:100%;height:100%}.member .member-info .member-name[data-v-04c9ed05]{text-align:left;font-size:%?32?%;font-weight:700;margin-left:%?20?%}.member .member-info .member-name .nickname[data-v-04c9ed05]{font-size:%?32?%;font-weight:700}.member .member-info .member-name .mobile[data-v-04c9ed05]{font-size:%?24?%;color:#666;font-weight:500}.member-box[data-v-04c9ed05]{margin:0 %?50?%;border-radius:%?18?%;display:flex;flex-direction:column;position:relative}.member-box .member-action[data-v-04c9ed05]{display:flex;margin-top:%?20?%;justify-content:space-between}.member-box .member-action .action-item[data-v-04c9ed05]{width:calc((100% - %?22?%) / 2);height:%?140?%;border-radius:%?18?%;display:flex;align-items:flex-start;justify-content:center;flex-direction:column;background-size:100% 100%;padding-left:%?25?%;box-sizing:border-box}.member-box .member-action .action-item uni-image[data-v-04c9ed05]{width:%?40?%;max-height:%?36?%;margin-bottom:%?20?%}.member-box .member-action .action-item .title[data-v-04c9ed05]{font-weight:600;color:#fff;font-size:%?28?%}.member-box .member-action .action-item .desc[data-v-04c9ed05]{font-weight:500;color:#fff;font-size:%?22?%}.tab-bar[data-v-04c9ed05]{background-color:#fff;box-sizing:border-box;position:fixed;left:0;bottom:0;width:100%;z-index:998;display:flex;border-top:%?2?% solid #f5f5f5;padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.tab-bar .tabbar-border[data-v-04c9ed05]{background-color:hsla(0,0%,100%,.329412);position:absolute;left:0;top:0;width:100%;height:%?2?%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.tab-bar .item[data-v-04c9ed05]{display:flex;align-items:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;flex:1;flex-direction:column;padding-bottom:%?10?%;box-sizing:border-box}.tab-bar .item .bd[data-v-04c9ed05]{position:relative;height:%?100?%;flex-direction:column;text-align:center;display:flex;justify-content:center;align-items:center}.tab-bar .item .bd .icon[data-v-04c9ed05]{position:relative;display:inline-block;margin-top:%?10?%;width:%?40?%;height:%?40?%;font-size:%?40?%}.tab-bar .item .bd .icon uni-image[data-v-04c9ed05]{width:100%;height:100%;display:block}.tab-bar .item .bd .icon > uni-view[data-v-04c9ed05]{height:inherit;display:flex;align-items:center}.tab-bar .item .bd .label[data-v-04c9ed05]{position:relative;text-align:center;font-size:%?24?%;line-height:1;margin-top:%?12?%}.tab-bar-placeholder[data-v-04c9ed05]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?112?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?112?%)}',""]),t.exports=e},ac2a:function(t,e,i){var n=i("f714");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("1a69ffc2",n,!0,{sourceMap:!1,shadowMode:!1})},c1f1:function(t,e,i){"use strict";i.r(e);var n=i("fa1d"),a=i("015d");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("a725");var r=i("828b"),c=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"c1934e78",null,!1,n["a"],void 0);e["default"]=c.exports},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=a},d87f:function(t,e,i){var n=i("d915");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("03d75754",n,!0,{sourceMap:!1,shadowMode:!1})},d915:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-9caa2b5c]{width:100%}.container-box .item-wrap[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-9caa2b5c]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}',""]),t.exports=e},ea7b:function(t,e,i){"use strict";i.r(e);var n=i("2c65"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f016:function(t,e,i){"use strict";i.r(e);var n=i("6102"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f48d:function(t,e,i){"use strict";i.r(e);var n=i("cc1b"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f714:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},a=[]}}]);