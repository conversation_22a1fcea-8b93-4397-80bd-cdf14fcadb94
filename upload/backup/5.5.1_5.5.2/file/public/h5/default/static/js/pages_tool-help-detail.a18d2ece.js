(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-help-detail"],{"015d":function(t,e,n){"use strict";n.r(e);var i=n("0f46"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"05b8":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.page[data-v-2f2c9094]{width:100%;height:100%;padding:%?30?%;box-sizing:border-box;background:#fff}.help-title[data-v-2f2c9094]{font-size:%?32?%;text-align:center}.help-content[data-v-2f2c9094]{margin-top:%?20?%;word-break:break-all}.help-meta[data-v-2f2c9094]{text-align:right;margin-top:%?20?%;color:#909399}.help-meta .help-time[data-v-2f2c9094]{font-size:%?24?%}',""]),t.exports=e},"0817":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("5c47"),n("2c10"),n("a1c1"),n("23f4"),n("7d2f"),n("9c4e"),n("ab80"),n("aa9c"),n("473f"),n("bf0f"),n("3efd");var a=i(n("af87")),o=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,r=/^<\/([-A-Za-z0-9_]+)[^>]*>/,c=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,s=p("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),l=p("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),u=p("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),d=p("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),f=p("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),h=p("script,style");function p(t){for(var e={},n=t.split(","),i=0;i<n.length;i++)e[n[i]]=!0;return e}var g=function(t){t=function(t){return t.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(t),t=function(t){return t=t.replace(/<!--[\s\S]*-->/gi,""),t}(t),t=function(t){var e='<img style="width:100% !important;display:block;max-width: '.concat("100%",' !important;"');return t=t.replace(/\\/g,"").replace(/<img/g,e),t=t.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(t,n){return e+' src="'+a.default.img(n)+'"/>'})),t}(t),t=function(t){return t=t.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(t,e){return t=t.replace(/[:](\s?)[\s\S]*/gi,(function(t,e){return t.replace(/"/g,"'")})),t})),t}(t);var e=[],n={node:"root",children:[]};return function(t,e){var n,i,a,p=[],g=t;p.last=function(){return this[this.length-1]};while(t){if(i=!0,p.last()&&h[p.last()])t=t.replace(new RegExp("([\\s\\S]*?)</"+p.last()+"[^>]*>"),(function(t,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),e.chars&&e.chars(n),""})),m("",p.last());else if(0==t.indexOf("\x3c!--")?(n=t.indexOf("--\x3e"),n>=0&&(e.comment&&e.comment(t.substring(4,n)),t=t.substring(n+3),i=!1)):0==t.indexOf("</")?(a=t.match(r),a&&(t=t.substring(a[0].length),a[0].replace(r,m),i=!1)):0==t.indexOf("<")&&(a=t.match(o),a&&(t=t.substring(a[0].length),a[0].replace(o,b),i=!1)),i){n=t.indexOf("<");var v=n<0?t:t.substring(0,n);t=n<0?"":t.substring(n),e.chars&&e.chars(v)}if(t==g)throw"Parse Error: "+t;g=t}function b(t,n,i,a){if(n=n.toLowerCase(),l[n])while(p.last()&&u[p.last()])m("",p.last());if(d[n]&&p.last()==n&&m("",n),a=s[n]||!!a,a||p.push(n),e.start){var o=[];i.replace(c,(function(t,e){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:f[e]?e:"";o.push({name:e,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),e.start&&e.start(n,o,a)}}function m(t,n){if(n){for(i=p.length-1;i>=0;i--)if(p[i]==n)break}else var i=0;if(i>=0){for(var a=p.length-1;a>=i;a--)e.end&&e.end(p[a]);p.length=i}}m()}(t,{start:function(t,i,a){var o={name:t};if(0!==i.length&&(o.attrs=function(t){return t.reduce((function(t,e){var n=e.value,i=e.name;return t[i]?t[i]=t[i]+" "+n:t[i]=n,t}),{})}(i)),a){var r=e[0]||n;r.children||(r.children=[]),r.children.push(o)}else e.unshift(o)},end:function(t){var i=e.shift();if(i.name!==t&&console.error("invalid state: mismatch end tag"),0===e.length)n.children.push(i);else{var a=e[0];a.children||(a.children=[]),a.children.push(i)}},chars:function(t){var i={type:"text",text:t};if(0===e.length)n.children.push(i);else{var a=e[0];a.children||(a.children=[]),a.children.push(i)}},comment:function(t){var n={node:"comment",text:t},i=e[0];i.children||(i.children=[]),i.children.push(n)}}),n.children};e.default=g},"0f46":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=i},4250:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;i(n("0817"));var a={data:function(){return{id:0,detail:{},content:""}},onLoad:function(t){if(this.id=t.id||0,t.scene){var e=decodeURIComponent(t.scene);this.id=e.split("-")[1]}0==this.id&&this.$util.redirectTo("/pages_tool/help/list",{},"redirectTo")},onShow:function(){this.getData()},methods:{getData:function(){var t=this;this.$api.sendRequest({url:"/api/help/info",data:{id:this.id},success:function(e){0==e.code&&e.data?(t.detail=e.data,t.$langConfig.title(t.detail.title),t.content=e.data.content,t.setPublicShare()):(t.$util.showToast({title:e.message}),setTimeout((function(){t.$util.redirectTo("/pages_tool/help/list",{},"redirectTo")}),2e3)),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},setPublicShare:function(){var t=this.$config.h5Domain+"/pages_tool/help/detail?id="+this.id;this.$util.setPublicShare({title:this.detail.title,desc:"",link:t,imgUrl:this.siteInfo?this.$util.img(this.siteInfo.logo_square):""})}},onShareAppMessage:function(t){var e=this.detail.title,n="/pages_tool/help/detail?id="+this.id;return{title:e,path:n,success:function(t){},fail:function(t){}}},onShareTimeline:function(){var t=this.detail.title,e="id="+this.id;return{title:t,query:e,imageUrl:""}}};e.default=a},"67b3":function(t,e,n){"use strict";n.r(e);var i=n("4250"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},7854:function(t,e,n){"use strict";n.r(e);var i=n("8ba8"),a=n("f48d");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);var r=n("828b"),c=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=c.exports},"8ba8":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},"9aad":function(t,e,n){var i=n("05b8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("4194268c",i,!0,{sourceMap:!1,shadowMode:!1})},a725:function(t,e,n){"use strict";var i=n("ac2a"),a=n.n(i);a.a},ac2a:function(t,e,n){var i=n("f714");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("1a69ffc2",i,!0,{sourceMap:!1,shadowMode:!1})},bc0c:function(t,e,n){"use strict";var i=n("9aad"),a=n.n(i);a.a},c1f1:function(t,e,n){"use strict";n.r(e);var i=n("fa1d"),a=n("015d");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("a725");var r=n("828b"),c=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"c1934e78",null,!1,i["a"],void 0);e["default"]=c.exports},cc1b:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("64aa"),n("5c47"),n("a1c1"),n("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var n=function n(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",n),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",n)}})}}}};e.default=a},d6ddb:function(t,e,n){"use strict";n.r(e);var i=n("e9d7"),a=n("67b3");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("bc0c");var r=n("828b"),c=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"2f2c9094",null,!1,i["a"],void 0);e["default"]=c.exports},e9d7:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={pageMeta:n("7854").default,nsMpHtml:n("d108").default,hoverNav:n("c1f1").default,loadingCover:n("c003").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("page-meta",{attrs:{"page-style":t.themeColor}}),n("v-uni-view",{staticClass:"page"},[n("v-uni-view",{staticClass:"help-title"},[t._v(t._s(t.detail.title))]),n("v-uni-view",{staticClass:"help-content"},[n("ns-mp-html",{attrs:{content:t.content}})],1),n("v-uni-view",{staticClass:"help-meta"},[n("v-uni-text",{staticClass:"help-time"},[t._v("发表时间: "+t._s(t.$util.timeStampTurnTime(t.detail.create_time)))])],1),n("hover-nav"),n("loading-cover",{ref:"loadingCover"})],1)],1)},o=[]},f48d:function(t,e,n){"use strict";n.r(e);var i=n("cc1b"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},f714:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return 1==t.pageCount||t.need?n("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?n("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[n("v-uni-text",{staticClass:"iconfont icon-shouye1"}),n("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?n("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[n("v-uni-text",{staticClass:"iconfont icon-yonghu"}),n("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?n("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[n("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):n("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[n("v-uni-view",[t._v("快捷")]),n("v-uni-view",[t._v("导航")])],1)],1):t._e()},a=[]}}]);