(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-giftcard-card_use"],{"093c":function(e,t,i){"use strict";i.r(t);var a=i("9da1"),o=i("1ba8");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("1dd3"),i("69da");var s=i("828b"),n=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"567b126c",null,!1,a["a"],void 0);t["default"]=n.exports},"1ba8":function(e,t,i){"use strict";i.r(t);var a=i("4d2d"),o=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"1dd3":function(e,t,i){"use strict";var a=i("b32b"),o=i.n(a);o.a},"30dc":function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-567b126c] uni-input,[data-v-567b126c] uni-view{font-size:%?24?%}.font-bold[data-v-567b126c]{font-weight:700}.order-container[data-v-567b126c]{width:100vw;height:100vh;display:flex;flex-direction:column;background:linear-gradient(180deg,var(--base-color) 10%,#f8f8f8);background-size:100% %?260?%;background-repeat:no-repeat}.order-container .order-scroll-container[data-v-567b126c]{width:100%;height:0;flex:1}.order-container .payment-navbar-block[data-v-567b126c]{height:%?60?%}.payment-navbar[data-v-567b126c]{width:100vw;padding-bottom:%?20?%;position:fixed;left:0;top:0;z-index:100;background:linear-gradient(180deg,var(--base-color) 10%,#f8f8f8);background-size:100% %?260?%}.payment-navbar .nav-wrap[data-v-567b126c]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;box-sizing:border-box;position:relative}.payment-navbar .navbar-title[data-v-567b126c]{color:#fff;font-size:%?32?%}.payment-navbar .icon-back_light[data-v-567b126c]{color:#fff;position:absolute;left:%?24?%;font-size:%?40?%}.payment-navbar-block[data-v-567b126c]{padding-bottom:%?20?%}.mobile-wrap[data-v-567b126c]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%}.mobile-wrap .tips[data-v-567b126c]{font-size:%?22?%;margin-bottom:%?30?%;background:var(--main-color-shallow);border-radius:%?10?%;padding:%?20?% %?30?%;line-height:1;display:flex;align-items:center}.mobile-wrap .tips .iconfont[data-v-567b126c]{margin-right:%?5?%}.mobile-wrap.local-mobile[data-v-567b126c]{border-bottom:%?2?% solid #f4f4f6;margin:0}.mobile-wrap.store-mobile[data-v-567b126c]{border-top:%?2?% solid #f4f4f6;margin:%?20?% 0 0 0;padding:%?20?% 0;border-radius:0}.mobile-wrap .form-group[data-v-567b126c]{display:flex;align-items:center;width:100%}.mobile-wrap .form-group .iconfont[data-v-567b126c]{margin-right:%?26?%;font-size:%?32?%}.mobile-wrap .form-group .text[data-v-567b126c]{display:inline-block;line-height:%?50?%;padding-right:%?10?%;font-size:%?28?%;font-weight:700}.mobile-wrap .form-group .placeholder[data-v-567b126c]{line-height:%?50?%}.mobile-wrap .form-group .input[data-v-567b126c]{flex:1;height:%?50?%;line-height:%?50?%;text-align:right;font-size:%?28?%}.order-cell[data-v-567b126c]{display:flex;margin:0 0 %?30?% 0;align-items:center;background:#fff;line-height:%?40?%;position:relative}.order-cell.clear-flex[data-v-567b126c]{display:block}.order-cell.textarea-box[data-v-567b126c]{display:flex;align-items:baseline;font-size:%?28?%}.order-cell uni-text[data-v-567b126c]{font-size:%?28?%}.order-cell .tit[data-v-567b126c]{text-align:left;font-size:%?28?%;min-width:%?160?%;color:#000;font-weight:700}.order-cell .tit uni-text[data-v-567b126c]{font-size:%?28?%}.order-cell .tit .tit-content[data-v-567b126c]{max-width:%?540?%;font-size:%?24?%;line-height:%?35?%;margin-bottom:%?10?%}.order-cell .box[data-v-567b126c]{flex:1;padding:0 %?10?%;line-height:inherit;text-align:right}.order-cell .box.text-overflow[data-v-567b126c]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.order-cell .box.text-overflow .money[data-v-567b126c]{overflow:hidden;max-width:40%;display:inline-block;text-overflow:ellipsis;vertical-align:top}.order-cell .box .icon-right[data-v-567b126c]{color:#303133;margin-left:%?20?%}.order-cell .box .operator[data-v-567b126c]{font-size:%?24?%;margin-right:%?6?%;font-weight:700;color:var(--price-color)}.order-cell .box uni-textarea[data-v-567b126c]{width:auto;height:%?88?%;font-size:%?28?%}.order-cell .iconfont[data-v-567b126c]{color:#909399;line-height:normal;font-size:%?24?%}.order-cell .unit[data-v-567b126c]{margin-right:%?4?%;font-weight:700;font-size:%?28?%!important;margin-left:%?4?%;color:var(--price-color)}.order-cell .money[data-v-567b126c]{font-size:%?28?%!important;font-weight:700;color:var(--price-color)}.site-wrap[data-v-567b126c]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;padding:%?40?% 0}.site-wrap.order-goods[data-v-567b126c]{padding:0}.site-wrap .site-body[data-v-567b126c]{margin:0 %?24?%}.site-wrap .site-body .goods-item[data-v-567b126c]{border-bottom:%?2?% solid #f4f4f6}.site-wrap .site-body .goods-item[data-v-567b126c]:last-child{border-bottom:0}.site-wrap .site-body .goods-item .error-tips[data-v-567b126c]{color:#ff443f;padding:%?10?% %?20?%;display:inline-flex;align-items:center;line-height:1;background:#ffecec;margin-top:%?20?%;border-radius:%?12?%;width:auto}.site-wrap .site-body .goods-item .error-tips .iconfont[data-v-567b126c]{margin-right:%?10?%}.site-wrap .site-body .goods-wrap[data-v-567b126c]{display:flex;position:relative;padding:%?30?% 0}.site-wrap .site-body .goods-wrap .goods-img[data-v-567b126c]{width:%?180?%;height:%?180?%;margin-right:%?20?%;border-radius:%?10?%;overflow:hidden}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-567b126c]{width:100%;height:100%;border-radius:%?10?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-567b126c]{flex:1;position:relative;width:0;margin-top:%?-4?%;display:flex;flex-direction:column;justify-content:space-between}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-567b126c]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;font-weight:700}.site-wrap .site-body .goods-wrap .goods-info .sku[data-v-567b126c]{display:flex;line-height:1;margin-top:%?8?%}.site-wrap .site-body .goods-wrap .goods-info .sku .goods-spec[data-v-567b126c]{color:#909399;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1;display:flex}.site-wrap .site-body .goods-wrap .goods-info .sku .goods-spec uni-view[data-v-567b126c]{background-color:#f4f4f4;color:#666;padding:%?6?% %?10?%;margin-right:%?12?%;line-height:1}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-567b126c]{font-size:%?24?%;margin-right:%?4?%;font-weight:700;color:var(--price-color)}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-567b126c]{font-weight:700;font-size:%?32?%;color:var(--price-color)}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-567b126c]:first-of-type{width:80%;overflow:hidden;text-overflow:ellipsis}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-567b126c]:last-of-type{text-align:right;position:absolute;right:0;bottom:0;font-weight:700}.site-wrap .site-footer[data-v-567b126c]{margin:0 %?24?% 0}.site-wrap .site-footer .order-cell[data-v-567b126c]:last-of-type{margin-bottom:0}[data-v-567b126c] .goods-form{display:flex;align-items:center;position:relative}[data-v-567b126c] .goods-form ns-form{display:flex;width:100%}[data-v-567b126c] .goods-form .shade{position:absolute;left:0;top:0;width:100%;height:100%;z-index:5}[data-v-567b126c] .goods-form .cell-more{margin-left:%?50?%;float:right;color:#909399;font-size:%?24?%}[data-v-567b126c] .goods-form .form-wrap{flex:1;width:0}[data-v-567b126c] .goods-form .form-wrap .icon-right{display:none}[data-v-567b126c] .goods-form .form-wrap > uni-view,[data-v-567b126c] .goods-form .form-wrap > uni-picker{display:none}[data-v-567b126c] .goods-form .form-wrap > uni-view:first-child,[data-v-567b126c] .goods-form .form-wrap > uni-picker:first-child{display:block;border-bottom:none}[data-v-567b126c] .goods-form .form-wrap > uni-view:first-child .required,[data-v-567b126c] .goods-form .form-wrap > uni-picker:first-child .required{display:none}[data-v-567b126c] .goods-form .order-cell .name{width:auto}[data-v-567b126c] .goods-form .order-cell .tit{font-weight:700}[data-v-567b126c] .goods-form .order-cell .tit:after{content:"："}.member-goods-card[data-v-567b126c]{margin-bottom:0;padding-bottom:%?30?%}.member-goods-card .text[data-v-567b126c]{margin-right:%?10?%;color:#999}.member-goods-card .price-font[data-v-567b126c]{color:var(--price-color)}.order-money[data-v-567b126c]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%}.order-money .order-cell[data-v-567b126c]:last-child{margin-bottom:0}.error-message[data-v-567b126c]{position:fixed;z-index:5;left:0;bottom:%?100?%;width:100vw;background:#f6f6cb;text-align:left;padding:%?10?% %?20?%;color:red}.order-submit[data-v-567b126c]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?100?%;background:#fff;text-align:right;display:flex;align-items:center;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.order-submit .order-settlement-info[data-v-567b126c]{flex:1;height:%?100?%;line-height:%?100?%;display:flex;padding-left:%?30?%;align-items:baseline}.order-submit .order-settlement-info .unit[data-v-567b126c]{font-weight:700;font-size:%?24?%;margin-right:%?4?%;color:var(--price-color)}.order-submit .order-settlement-info .money[data-v-567b126c]{font-weight:700;font-size:%?32?%;color:var(--price-color)}.order-submit .submit-btn[data-v-567b126c]{height:%?80?%;margin:0 %?30?%;display:flex;justify-content:center;align-items:center}.order-submit .submit-btn uni-button[data-v-567b126c]{line-height:%?70?%;width:%?180?%;height:%?70?%;padding:0;font-size:%?28?%;font-weight:700}.order-submit .submit-btn .no-submit[data-v-567b126c]{width:unset;background-color:#ccc;color:#fff;padding:0 %?20?%;font-size:%?28?%}.order-submit-block[data-v-567b126c]{height:%?120?%;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.popup[data-v-567b126c]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-567b126c]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-567b126c]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-567b126c]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-567b126c]{height:calc(100% - %?250?%)}.popup .popup-body.store-popup[data-v-567b126c]{height:calc(100% - %?120?%)}.popup .popup-body.safe-area[data-v-567b126c]{height:calc(100% - %?270?%)}.popup .popup-body.store-popup.safe-area[data-v-567b126c]{height:calc(100% - %?140?%)}.popup .popup-footer[data-v-567b126c]{height:%?120?%}.popup .popup-footer .confirm-btn[data-v-567b126c]{height:%?80?%;line-height:%?80?%;color:#fff;text-align:center;margin:%?20?% %?32?% %?40?%;border-radius:%?10?%;font-size:%?28?%}.popup .popup-footer .confirm-btn.color-base-bg[data-v-567b126c]{color:var(--btn-text-color)}.popup .popup-footer.bottom-safe-area[data-v-567b126c]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.invoice-popup[data-v-567b126c]{height:83vh;padding:%?18?% 0;box-sizing:border-box;position:relative}.invoice-popup .invoice-close[data-v-567b126c]{position:absolute;line-height:1;top:%?48?%;right:%?48?%;font-size:%?32?%;z-index:9}.invoice-popup .popup-body .invoice-cell[data-v-567b126c]{padding:%?30?% 0;border-top:%?2?% solid #eee;margin:0 %?48?%}.invoice-popup .popup-body .invoice-cell[data-v-567b126c]:first-of-type{border-top:none}.invoice-popup .popup-body .invoice-cell .tit[data-v-567b126c]{font-size:%?28?%}.invoice-popup .popup-body .invoice-cell .option-grpup[data-v-567b126c]{padding-top:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-567b126c]{height:%?54?%;line-height:%?54?%;display:inline-block;font-size:%?22?%;padding:0 %?36?%;background:#f8f8f8;border:%?2?% solid #eee;border-radius:%?10?%;margin-right:%?30?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.active[data-v-567b126c]{color:var(--btn-text-color)}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.content[data-v-567b126c]{margin-bottom:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.content[data-v-567b126c]:last-child{margin-bottom:0}.invoice-popup .popup-body .invoice-cell .invoice-form-group uni-input[data-v-567b126c]{background:#f8f8f8;border-radius:%?10?%;height:%?66?%;margin-top:%?22?%;padding:0 %?32?%;font-size:%?24?%}.invoice-popup .popup-body .invoice-tops[data-v-567b126c]{font-size:%?20?%;margin:0 %?48?%;color:#909399}.buyermessag-popup[data-v-567b126c]{box-sizing:border-box;position:relative}.buyermessag-popup .buyermessag-close[data-v-567b126c]{position:absolute;line-height:1;top:%?48?%;right:%?48?%;font-size:%?32?%;z-index:9}.buyermessag-popup .popup-body .buyermessag-cell[data-v-567b126c]{padding:%?30?% 0;border-top:%?2?% solid #eee;margin:0 %?32?%}.buyermessag-popup .popup-body .buyermessag-cell[data-v-567b126c]:first-of-type{border-top:none}.buyermessag-popup .popup-body .buyermessag-cell .buyermessag-form-group uni-textarea[data-v-567b126c]{display:flex;align-items:baseline;font-size:%?28?%;width:100%;background-color:#f8f8f8;padding:%?20?%;box-sizing:border-box;border-radius:%?10?%}.coupon-popup[data-v-567b126c]{height:65vh}.coupon-popup .popup-body[data-v-567b126c]{background:#fff}.coupon-popup .coupon-empty[data-v-567b126c]{display:flex;align-items:center;justify-content:center;height:100%;color:#909399!important}.coupon-popup .coupon-item[data-v-567b126c]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;margin:%?20?% %?32?% 0;padding:0;position:relative;background-color:#fff2f0}.coupon-popup .coupon-item[data-v-567b126c]:before, .coupon-popup .coupon-item[data-v-567b126c]:after{position:absolute;content:"";background-color:#fff;top:50%;width:%?30?%;height:%?30?%;border-radius:50%;z-index:5}.coupon-popup .coupon-item[data-v-567b126c]:before{left:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.coupon-popup .coupon-item[data-v-567b126c]:after{right:0;-webkit-transform:translate(50%,-50%);transform:translate(50%,-50%)}.coupon-popup .coupon-item .coupon-info[data-v-567b126c]{height:%?190?%;display:flex;width:100%;position:relative}.coupon-popup .coupon-item .coupon-info .info-wrap[data-v-567b126c]{width:%?220?%;height:%?190?%;display:flex;justify-content:center;align-items:center;margin-right:%?20?%;background-repeat:no-repeat;background-size:100% 100%;position:relative;background:linear-gradient(270deg,var(--bg-color),var(--bg-color-shallow))}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-line[data-v-567b126c]{position:absolute;right:0;top:0;height:100%}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money[data-v-567b126c]{color:#fff;text-align:center;line-height:1}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money .unit[data-v-567b126c]{font-size:%?30?%}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money .money[data-v-567b126c]{font-size:%?60?%}.coupon-popup .coupon-item .coupon-info .info-wrap .at-least[data-v-567b126c]{font-size:%?24?%;color:#fff;text-align:center;margin-top:%?20?%}.coupon-popup .coupon-item .coupon-info .desc-wrap[data-v-567b126c]{flex:1;max-width:calc(100% - %?360?%)}.coupon-popup .coupon-item .coupon-info .desc-wrap uni-view[data-v-567b126c]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.coupon-popup .coupon-item .coupon-info .desc-wrap .coupon-name[data-v-567b126c]{margin-top:%?10?%;margin-bottom:%?4?%;font-size:%?28?%}.coupon-popup .coupon-item .coupon-info .desc-wrap .limit[data-v-567b126c]{font-size:%?20?%}.coupon-popup .coupon-item .coupon-info .desc-wrap .time[data-v-567b126c]{border-top:%?2?% dashed #ccc;position:absolute;bottom:%?30?%;color:#909399;padding-top:%?10?%;line-height:1.5;font-size:%?20?%}.coupon-popup .coupon-item .coupon-info .iconfont[data-v-567b126c]{font-size:%?44?%;position:absolute;top:50%;right:%?20?%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.coupon-popup .coupon-item .coupon-info .icon-yuan_checkbox[data-v-567b126c]{color:#909399}.promotion-popup[data-v-567b126c]{height:40vh}.promotion-popup .order-cell[data-v-567b126c]{margin:%?30?% %?30?%}.promotion-popup .order-cell .tit[data-v-567b126c]{width:auto;min-width:unset}.promotion-popup .order-cell .promotion-mark[data-v-567b126c]{padding:%?4?% %?10?%;line-height:1;border-radius:%?10?%;font-size:%?24?%;margin-right:%?10?%;color:var(--main-color);background-color:var(--main-color-shallow)}.delivery-popup[data-v-567b126c]{height:80vh;box-sizing:border-box}.delivery-popup .delivery-content[data-v-567b126c]{height:100%;overflow-y:scroll;padding:%?30?% 0;box-sizing:border-box}.delivery-popup .delivery-content .item-wrap[data-v-567b126c]{padding:%?20?% 0;box-sizing:border-box;border-top:%?2?% solid #eee;display:flex;justify-content:space-between;align-items:center;margin:0 %?48?%}.delivery-popup .delivery-content .item-wrap .detail[data-v-567b126c]{width:90%}.delivery-popup .delivery-content .item-wrap .detail .name[data-v-567b126c]{display:flex}.delivery-popup .delivery-content .item-wrap .detail .name uni-text[data-v-567b126c]{font-size:%?28?%}.delivery-popup .delivery-content .item-wrap .detail .info[data-v-567b126c]{line-height:1.2}.delivery-popup .delivery-content .item-wrap .detail .info uni-view[data-v-567b126c]{font-size:%?24?%}.delivery-popup .delivery-content .item-wrap .detail .info .close-desc[data-v-567b126c]{color:red}.delivery-popup .delivery-content .item-wrap .icon[data-v-567b126c]{flex:1;text-align:right;max-height:%?50?%}.delivery-popup .delivery-content .item-wrap .icon .iconfont[data-v-567b126c]{line-height:1;font-size:%?44?%}.delivery-popup .delivery-content .item-wrap[data-v-567b126c]:first-of-type{padding-top:0;border-top:none}.delivery-popup .delivery-content .empty[data-v-567b126c]{text-align:center;font-size:%?24?%}.balance-switch[data-v-567b126c]{-webkit-transform:scale(.8);transform:scale(.8)}.address-box[data-v-567b126c]{margin:0 %?24?% 0;background-color:#fff;position:relative;overflow:hidden;border-bottom-left-radius:%?16?%;border-bottom-right-radius:%?16?%;padding:%?30?% %?24?%}.address-box.not-delivery-type[data-v-567b126c]{border-radius:%?16?%}.address-box .address-line[data-v-567b126c]{position:absolute;bottom:%?0?%;left:0;width:100%;height:%?6?%}.address-box .info-wrap[data-v-567b126c]{display:flex;align-items:center}.address-box .info-wrap.local[data-v-567b126c]{padding-bottom:%?20?%}.address-box .info-wrap .content[data-v-567b126c]{flex:1}.address-box .info-wrap .content .name[data-v-567b126c]{margin-right:%?10?%;font-weight:700;font-size:%?28?%}.address-box .info-wrap .content .mobile[data-v-567b126c]{font-weight:700;font-size:%?28?%}.address-box .info-wrap .desc-wrap[data-v-567b126c]{word-break:break-word;font-size:%?26?%;color:#666}.address-box .icon-wrap[data-v-567b126c]{width:%?24?%;height:%?42?%;position:relative;margin-right:%?26?%;align-self:flex-start;padding-top:%?6?%}.address-box .icon-wrap.empty[data-v-567b126c]{padding-top:0}.address-box .icon-wrap .iconfont[data-v-567b126c]{font-size:%?32?%;display:inline-block;vertical-align:middle}.address-box .empty-wrap[data-v-567b126c]{height:%?80?%;line-height:%?80?%;display:flex;align-items:center}.address-box .empty-wrap .info[data-v-567b126c]{flex:1;font-size:%?28?%}.address-box .cell-more[data-v-567b126c]{margin-left:%?50?%;float:right;color:#909399;font-size:%?24?%}.address-box .cell-more .iconfont[data-v-567b126c]{color:#909399}.address-box .local-delivery-store[data-v-567b126c]{display:flex;align-items:center;padding-bottom:%?20?%;margin-bottom:%?20?%;border-bottom:%?2?% solid #eee}.address-box .local-delivery-store .info[data-v-567b126c]{flex:1;width:0;font-size:%?28?%}.address-box .local-delivery-store .store-name[data-v-567b126c]{color:var(--base-color);margin:0 %?10?%}.address-box .local-delivery-store .cell-more[data-v-567b126c]{font-size:%?28?%;display:flex;align-items:center}.address-box .local-delivery-store .icon-right[data-v-567b126c]{float:right;color:#909399;font-size:%?24?%}.local-box[data-v-567b126c]{border-top:%?2?% solid #eee}.local-box .order-cell[data-v-567b126c]{padding-top:%?30?%;margin-bottom:0}.local-box .order-cell .box[data-v-567b126c]{padding:0}.local-box .pick-block[data-v-567b126c]{padding-top:%?20?%;display:flex;align-items:center}.local-box .pick-block > uni-view[data-v-567b126c]{flex:1}.local-box .pick-block .title[data-v-567b126c]{font-weight:700}.local-box .pick-block .time-picker[data-v-567b126c]{display:flex;align-items:center;justify-content:flex-end}.local-box .pick-block .time-picker .cell-more[data-v-567b126c]{float:right;margin-left:%?10?%;color:#909399;font-size:%?24?%}.local-box .pick-block .time-picker .cell-more .iconfont[data-v-567b126c]{color:#909399}.local-box .pick-block .time-picker uni-text[data-v-567b126c]{white-space:nowrap}.empty-local[data-v-567b126c]{color:#ff443f}.delivery-mode[data-v-567b126c]{margin:0 %?24?%;overflow:hidden;border-top-left-radius:%?16?%;border-top-right-radius:%?16?%;background-color:var(--base-color)}.delivery-mode .action[data-v-567b126c]{display:flex;background:var(--base-color-light-7)}.delivery-mode .action > uni-view[data-v-567b126c]{flex:1;text-align:center;height:%?76?%;line-height:%?76?%;font-size:%?30?%;color:#000;position:relative}.delivery-mode .action > uni-view:nth-child(2).active[data-v-567b126c], .delivery-mode .action > uni-view:nth-child(3).active[data-v-567b126c]{border-top-left-radius:%?16?%}.delivery-mode .action > uni-view .out-radio[data-v-567b126c]:after, .delivery-mode .action > uni-view .out-radio[data-v-567b126c]:before{position:absolute;content:"";width:%?20?%;height:%?20?%;background-color:#fff;bottom:0;display:none}.delivery-mode .action > uni-view .out-radio[data-v-567b126c]:after{-webkit-transform:translateX(100%);transform:translateX(100%);right:0}.delivery-mode .action > uni-view .out-radio[data-v-567b126c]:before{left:0;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.delivery-mode .action .active[data-v-567b126c]{background:#fff;color:var(--base-color);border-top-right-radius:%?16?%}.delivery-mode .action .active[data-v-567b126c]:after, .delivery-mode .action .active[data-v-567b126c]:before{position:absolute;content:"";width:%?40?%;height:%?40?%;background-color:var(--base-color-light-7);bottom:0;-webkit-transform:translateX(100%);transform:translateX(100%);border-radius:50%;z-index:5}.delivery-mode .action .active[data-v-567b126c]:after{-webkit-transform:translateX(100%);transform:translateX(100%);right:0}.delivery-mode .action .active[data-v-567b126c]:before{left:0;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.delivery-mode .action .active .out-radio[data-v-567b126c]:after, .delivery-mode .action .active .out-radio[data-v-567b126c]:before{display:block}.store-box[data-v-567b126c]{position:relative;padding:%?30?% %?24?%;margin:0 %?24?% 0;background-color:#fff;border-bottom-left-radius:%?16?%;border-bottom-right-radius:%?16?%;overflow:hidden}.store-box.not-delivery-type[data-v-567b126c]{border-radius:%?16?%}.store-box .address-line[data-v-567b126c]{position:absolute;bottom:0;left:0;width:100%;height:%?6?%}.store-box .store-info[data-v-567b126c]{display:flex;align-items:baseline}.store-box .store-info .icon[data-v-567b126c]{position:relative;margin-right:%?12?%;align-self:flex-start;margin-top:%?-2?%}.store-box .store-info .icon.img[data-v-567b126c]{background-color:unset;margin-right:%?8?%;width:%?46?%;height:%?46?%;border-radius:50%;margin-top:%?12?%}.store-box .store-info .icon.img uni-image[data-v-567b126c]{width:100%;height:100%}.store-box .store-info .icon .iconfont[data-v-567b126c]{font-size:%?32?%}.store-box .store-info .store-address-info[data-v-567b126c]{width:100%;display:flex;align-items:center}.store-box .store-info .store-address-info .info-wrap[data-v-567b126c]{flex:1;width:0}.store-box .store-info .store-address-info .info-wrap .title[data-v-567b126c]{margin-bottom:%?10?%;font-size:%?28?%;font-weight:700}.store-box .store-info .store-address-info .info-wrap .title .cell-more[data-v-567b126c]{float:right;margin-left:%?50?%;color:#909399;font-size:%?24?%;font-weight:500}.store-box .store-info .store-address-info .info-wrap .store-detail uni-view[data-v-567b126c]{word-break:break-word;font-size:%?26?%}.store-box .store-info .store-address-info .info-wrap .store-detail .close-desc[data-v-567b126c]{color:red}.store-box .store-info .store-address-info .info-wrap .store-detail .address[data-v-567b126c]{color:#606266;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis}.store-box .store-info .store-address-info .cell-more[data-v-567b126c]{color:#909399}.store-box .empty[data-v-567b126c]{text-align:center}.store-box .store-time[data-v-567b126c]{border-top:%?2?% solid #f4f4f6;display:flex;align-items:center;justify-content:space-between;padding:%?20?% 0 0;box-sizing:border-box}.store-box .store-time uni-view[data-v-567b126c]{font-size:%?28?%}.store-box .store-time .left[data-v-567b126c]{font-weight:700}.store-box .store-time .right[data-v-567b126c]{display:flex;align-items:center;line-height:1;font-size:%?24?%}.store-box .store-time .right .iconfont[data-v-567b126c]{font-size:%?24?%;margin-left:%?14?%;color:#909399}.buyer-message[data-v-567b126c]{padding:%?30?% %?24?%}.buyer-message .order-cell[data-v-567b126c]{margin-bottom:0}.member-card-wrap[data-v-567b126c]{background-color:#fffbf4;padding:0 %?30?%!important}.member-card-wrap .head[data-v-567b126c]{display:flex;align-items:center;height:%?80?%}.member-card-wrap .icon-yuan_checked[data-v-567b126c], .member-card-wrap .icon-yuan_checkbox[data-v-567b126c]{font-size:%?32?%}.member-card-wrap .icon-huiyuan[data-v-567b126c]{margin-right:%?10?%;line-height:1;font-size:%?36?%;background-image:linear-gradient(156deg,#814635,#3a221b);-webkit-background-clip:text;-webkit-text-fill-color:transparent}.member-card-wrap .info[data-v-567b126c]{text-align:left;flex:1;color:#e5ce75;font-size:%?24?%;color:#333}.member-card-wrap .body[data-v-567b126c]{display:flex;overflow-x:scroll;padding:%?10?% 0 %?20?% 0}.member-card-wrap .body .item[data-v-567b126c]{padding:%?20?% 0 %?30?% 0;width:calc((100% - %?60?%) / 4);text-align:center;background:#fff;margin-right:%?20?%;border:%?4?% solid #fff;border-radius:%?10?%;position:relative;overflow:hidden}.member-card-wrap .body .item .icon-icon[data-v-567b126c]{position:absolute;right:0;bottom:0;font-size:%?32?%;display:none;line-height:1}.member-card-wrap .body .item[data-v-567b126c]:last-child{margin-right:0}.member-card-wrap .body .item .title[data-v-567b126c]{margin-top:%?20?%;font-weight:700}.member-card-wrap .body .item .price[data-v-567b126c]{margin-top:%?10?%}.member-card-wrap .body .active .icon-icon[data-v-567b126c]{display:block}.system-form-wrap[data-v-567b126c]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;padding:0;overflow:hidden}.system-form-wrap .order-cell[data-v-567b126c]{padding:%?30?% %?24?%;margin-bottom:0;border-bottom:%?2?% solid #f4f4f6}.system-form-wrap[data-v-567b126c] .form-wrap{margin:0 %?24?%}.system-form-wrap[data-v-567b126c] .form-wrap .icon-right{color:#909399;font-size:%?24?%}.agreement[data-v-567b126c]{margin:%?20?% %?24?% 0}.agreement uni-text[data-v-567b126c]{color:var(--base-color)}.agreement-conten-box[data-v-567b126c]{background:#fff;padding:%?30?% %?30?%}.agreement-conten-box .title[data-v-567b126c]{text-align:center;margin-bottom:%?20?%;font-weight:bolder}.agreement-conten-box .close[data-v-567b126c]{position:absolute;right:%?30?%;top:%?10?%}.agreement-conten-box .con[data-v-567b126c]{height:60vh}.icon[data-v-567b126c]{line-height:1;margin-right:%?14?%;max-height:%?50?%}.icon uni-image[data-v-567b126c]{width:%?38?%;margin:%?-6?% auto;max-height:%?50?%}.form-popup[data-v-567b126c]{height:60vh!important}.form-popup .popup-body[data-v-567b126c]{padding:%?20?% %?30?%;box-sizing:border-box}.member-card-popup[data-v-567b126c]{height:60vh}.member-card-popup .popup-body .card-item[data-v-567b126c]{display:flex;padding:%?30?%;background:var(--base-color-light-9);margin:%?24?% %?20?%;border-radius:%?18?%}.member-card-popup .popup-body .card-item .content[data-v-567b126c]{flex:1;width:0;margin-right:%?30?%}.member-card-popup .popup-body .card-item .content .title[data-v-567b126c]{line-height:%?40?%;font-size:%?28?%;font-weight:600}.member-card-popup .popup-body .card-item .content .info uni-text[data-v-567b126c]{line-height:1;font-size:%?24?%;color:#666;margin-top:%?20?%;margin-right:%?8?%;display:inline-block}.member-card-popup .popup-body .card-item .iconfont[data-v-567b126c]{font-size:%?44?%}.member-card-popup .popup-body .card-item .icon-yuan_checkbox[data-v-567b126c]{color:#909399}',""]),e.exports=t},3672:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("bf0f"),i("de6c"),i("5c47"),i("a1c1"),i("64aa"),i("e966"),i("5ef2"),i("c9b5"),i("ab80"),i("aa9c"),i("d4b5"),i("2797");var o=a(i("d745")),r={name:"nsSelectTime",components:{uniPopup:o.default},data:function(){return{key:0,keys:0,obj:{},dayData:[],timeData:[],judge:!1,keyJudge:0,dayTime:0}},methods:{refresh:function(){this.key=0,this.keys=0,this.keyJudge=0},open:function(e,t){this.dayData=[],this.timeData=[],this.obj=e,this.toDay(e.dataTime.time_type,e.dataTime.time_week),this.judge&&("no"==t?this.selectTime("","",t):this.$refs.selectTime.open())},selectTime:function(e,t,i){if("days"==e)this.keyJudge=t,this.toTime();else if("time"==e){this.keys=t,this.key=this.keyJudge;var a=this.dayData[this.key];a.time=this.timeData[this.keys];var o=a.time.replace("立即配送（","").replace("）",""),r=new Date,s=o.split("-"),n=s[0].split(":"),d=s[1].split(":"),l=a.month.split("月"),c=l[0],p=l[1].split("日")[0];r.setHours(n[0],n[1],0,0),a.start_time=r.getTime()/1e3,a.start_date=r.getFullYear()+"-"+c+"-"+p+" "+s[0],r.setHours(d[0],d[1],0,0),a.end_time=r.getTime()/1e3,a.end_date=r.getFullYear()+"-"+c+"-"+p+" "+s[1],this.$emit("selectTime",{data:a,type:i}),this.$refs.selectTime.close()}if("no"==i){this.toTime(i);var u=this.dayData[0];u.time=this.timeData[0];var v=new Date,m=u.time.replace("立即配送（","").replace("）","").split("-"),f=m[0].split(":"),b=m[1].split(":"),g=u.month.split("月"),h=g[0],y=g[1].split("日")[0];v.setHours(f[0],f[1],0,0),u.start_time=v.getTime()/1e3,u.start_date=v.getFullYear()+"-"+h+"-"+y+" "+m[0],v.setHours(b[0],b[1],0,0),u.end_time=v.getTime()/1e3,u.end_date=v.getFullYear()+"-"+h+"-"+y+" "+m[1],this.$emit("selectTime",{data:u,type:i})}this.$forceUpdate()},close:function(){this.$refs.selectTime.close()},toDay:function(e,t){var i=new Date;this.obj.dataTime.advance_day&&(i=new Date(i.getTime()+864e5*this.obj.dataTime.advance_day));var a=i.getFullYear(),o=i.getMonth()+1,r=i.getDate(),s=i.getDay(),n=new Date(a,o,0).getDate(),d=i.getHours(),l=i.getMinutes();this.dayTime=this.obj.dataTime.advance_day?0:3600*Number(d)+60*Number(l);var c=!1,p=1,u=this.obj.dataTime.most_day?this.obj.dataTime.most_day+1:1,v=parseInt(i.getTime()/1e3),m=["周日","周一","周二","周三","周四","周五","周六"];t.time_week&&7==t.time_week.length&&(c=!0);for(var f=0;f<u;f++){var b={},g=m[s];if(this.obj.dataTime.most_day>0&&v+86400*p>v+86400*this.obj.dataTime.most_day){this.judge=!0;break}if(0==e||c||-1!=t.indexOf(s.toString())){var h=this.obj.dataTime.delivery_time[this.obj.dataTime.delivery_time.length-1].end_time;switch(h-=60*this.obj.dataTime.time_interval,p){case 1:0==f&&(h<this.dayTime?f-=1:(b={title:0==this.obj.dataTime.advance_day?"今天":"",type:"special",month:o+"月"+r+"日",Day:"("+g+")"},this.dayData.push(b)));break;case 2:0!=f&&1!=f||(b={title:0==this.obj.dataTime.advance_day?"明天":"",month:o+"月"+r+"日",Day:"("+g+")"},this.dayData.push(b));break;default:b={title:"",month:o+"月"+r+"日",Day:"("+g+")"},this.dayData.push(b)}}else f-=1;r!=n?r+=1:(12!=o?o+=1:o=1,r=1),6!=s?s+=1:s=0,p+=1,0==this.obj.dataTime.most_day&&0==f&&(this.judge=!0)}this.toTime()},toTime:function(e){var t=this;"no"==e&&(this.key=0,this.keys=0,this.keyJudge=0);var i=[];this.obj.dataTime.delivery_time||(this.obj.dataTime.delivery_time=[{start_time:this.obj.dataTime.start_time,end_time:this.obj.dataTime.end_time}]);var a=JSON.parse(JSON.stringify(this.dayTime)),o=!1;this.dayData[this.keyJudge]&&this.dayData[this.keyJudge].type&&a>this.obj.dataTime.start_time&&(o=!0);var r=this.obj.dataTime.time_interval?60*this.obj.dataTime.time_interval:1200;this.obj.dataTime.delivery_time.forEach((function(e){e.end_time=e.end_time?e.end_time:86400;for(var s=parseInt((parseInt(e.end_time)-parseInt(e.start_time))/r),n=o?parseInt(a):parseInt(e.start_time),d=0;d<s;d++){if(parseInt(n)+parseInt(r)>e.end_time)break;if(o){if(n>=a)if(t.obj.dataTime.time_interval){if(n<=e.end_time){var l="";l="local"==t.obj.delivery.delivery_type&&0==d?"立即配送（"+t.$util.getTimeStr(n)+"-"+t.$util.getTimeStr(n+r)+"）":t.$util.getTimeStr(n)+"-"+t.$util.getTimeStr(n+r),i.push(l)}}else i.push(t.$util.getTimeStr(n))}else t.obj.dataTime.time_interval?n<=e.end_time&&i.push(t.$util.getTimeStr(n)+"-"+t.$util.getTimeStr(n+r)):i.push(t.$util.getTimeStr(n));n=parseInt(n)+r}})),this.timeData=i,this.$forceUpdate()}}};t.default=r},"4d2d":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("39d8")),r=a(i("9b1b")),s=a(i("fcf3"));i("bf0f"),i("2797"),i("e838"),i("fd3c"),i("dc8a"),i("d4b5"),i("473f");var n={options:{styleIsolation:"shared"},data:function(){return{api:{payment:"/giftcard/api/giftcardordercreate/payment",calculate:"/giftcard/api/giftcardordercreate/calculate",create:"/giftcard/api/giftcardordercreate/create"},createDataKey:"giftcarduse",outTradeNo:"",isIphoneX:!1,orderCreateData:{is_balance:0,is_point:1,delivery:{}},paymentData:null,calculateData:null,tempData:null,storeId:0,deliveryTime:"",memberAddress:null,localMemberAddress:null,isRepeat:!1,promotionInfo:null,tempFormData:null,menuButtonBounding:{},storeConfig:null,LocalConfig:null}},created:function(){var e=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?(Object.assign(this.orderCreateData,uni.getStorageSync(this.createDataKey)),this.location&&(this.orderCreateData.latitude=this.location.latitude,this.orderCreateData.longitude=this.location.longitude),this.payment()):this.$nextTick((function(){e.$refs.loadingCover.hide(),e.$refs.login.open(e.$util.getCurrentRoute().path)}))},computed:{goodsData:function(){if(this.paymentData)return this.paymentData.goods_list.forEach((function(e){e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format))})),this.paymentData},calculateGoodsData:function(){if(this.calculateData)return this.calculateData},balanceDeduct:function(){if(this.calculateData)return this.calculateData.member_account.balance_total<=parseFloat(this.calculateData.order_money).toFixed(2)?parseFloat(this.calculateData.member_account.balance_total).toFixed(2):parseFloat(this.calculateData.order_money).toFixed(2)},storeList:function(){return this.getStoreList()},storeInfo:function(){var e=this.getStoreList();return e&&this.orderCreateData.delivery&&"express"!=this.orderCreateData.delivery.delivery_type&&this.storeId?e[this.orderCreateData.delivery.store_id]:null}},watch:{storeToken:function(e,t){this.payment()},deliveryTime:function(e){e||this.$refs.timePopup.refresh()},location:function(e){e&&(this.orderCreateData.latitude=e.latitude,this.orderCreateData.longitude=e.longitude,this.payment())},calculateGoodsData:function(e){e&&e.config.local&&e.delivery.local.info.time_is_open&&!this.deliveryTime&&this.localtime("no")}},methods:{pageShow:function(){uni.getStorageSync("addressBack")&&(uni.removeStorageSync("addressBack"),this.payment())},payment:function(){var e=this;this.$api.sendRequest({url:this.api.payment,data:this.orderCreateData,success:function(t){if(0==t.code&&t.data){var i,a=t.data;if(a&&a.delivery.express_type&&a.delivery.express_type.length){var o=uni.getStorageSync("delivery"),r=a.delivery.express_type[0];o&&a.delivery.express_type.forEach((function(t){t.name==o.delivery_type&&(r=t),"local"==t.name&&(e.localConfig=t),"store"==t.name&&(e.storeConfig=t)})),e.selectDeliveryType(r,!1,a.member_account),uni.getStorageSync("deliveryTime")&&uni.getStorageSync("deliveryTime")["delivery_type"]&&uni.getStorageSync("deliveryTime")["delivery_type"]==e.orderCreateData.delivery.delivery_type&&(e.deliveryTime=uni.getStorageSync("deliveryTime")["deliveryTime"],e.orderCreateData.delivery.buyer_ask_delivery_time=uni.getStorageSync("deliveryTime")["buyer_ask_delivery_time"])}if(a.is_virtual)e.orderCreateData.delivery.member_address={name:a.member_account.nickname,mobile:null!==(i=a.member_account.mobile)&&void 0!==i?i:""};e.orderCreateData.order_key=a.order_key,a=e.handleGoodsFormData(a),e.paymentData=a,e.$forceUpdate(),e.calculate()}}})},handleGoodsFormData:function(e){var t=uni.getStorageSync("goodFormData");return e.goods_list.forEach((function(e){if(e.goods_form){var i={};e.form_data?e.form_data.map((function(e){i[e.id]=e})):t&&t.goods_id==e.goods_id&&t.form_data.map((function(e){i[e.id]=e})),Object.keys(i).length&&e.goods_form.json_data.forEach((function(e){i[e.id]&&(e.val=i[e.id].val)}))}})),e},calculate:function(){var e=this;this.$api.sendRequest({url:this.api.calculate,data:this.handleCreateData(),success:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.isShow&&e.$refs.loadingCover.hide(),0==t.code&&t.data?(e.calculateData=t.data,t.data.delivery&&("express"==t.data.delivery.delivery_type&&(e.memberAddress=t.data.delivery.member_address),"local"==t.data.delivery.delivery_type&&(e.localMemberAddress=t.data.delivery.member_address)),e.$forceUpdate()):e.$util.showToast({title:t.message})}})},create:function(){var e=this;this.verify()&&!this.isRepeat&&(this.isRepeat=!0,uni.showLoading({title:""}),this.$api.sendRequest({url:this.api.create,data:this.handleCreateData(),success:function(t){uni.hideLoading(),0==t.code?(e.outTradeNo=t.data,uni.removeStorageSync("deliveryTime"),uni.removeStorageSync("goodFormData"),0==e.calculateData.pay_money?e.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:e.outTradeNo},success:function(t){t.code>=0&&t.data&&t.data.order_id>0?e.$util.redirectTo("/pages/order/detail",{order_id:t.data.order_id},"redirectTo"):e.$util.redirectTo("/pages/order/list",{},"redirectTo")},fail:function(t){e.$util.redirectTo("/pages/order/list",{},"redirectTo")}}):e.openChoosePayment()):(e.$util.showToast({title:t.message}),e.isRepeat=!1)}}))},handleCreateData:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);return this.$refs.form&&(t.form_data={form_id:this.paymentData.system_form.id,form_data:this.$util.deepClone(this.$refs.form.formData)}),this.$refs.goodsForm&&(t.form_data||(t.form_data={}),t.form_data.goods_form={},this.$refs.goodsForm.forEach((function(i){t.form_data.goods_form[i._props.customAttr.sku_id]={form_id:i._props.customAttr.form_id,form_data:e.$util.deepClone(i.formData)}}))),Object.keys(t).forEach((function(e){var i=t[e];"object"==(0,s.default)(i)&&(t[e]=JSON.stringify(i))})),t.member_address&&this.orderCreateData.delivery&&"store"!=this.orderCreateData.delivery.delivery_type&&delete t.member_address,t},openChoosePayment:function(){uni.setStorageSync("paySource",""),this.$refs.choosePaymentPopup.getPayInfo(this.outTradeNo)},verify:function(){if(1==this.paymentData.is_virtual){if(!this.orderCreateData.member_address.mobile.length)return this.$util.showToast({title:"请输入您的手机号码"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号码"}),!1}else{if(!this.orderCreateData.delivery||!this.orderCreateData.delivery.delivery_type)return this.$util.showToast({title:"商家未设置配送方式"}),!1;if("express"==this.orderCreateData.delivery.delivery_type&&!this.memberAddress||"local"==this.orderCreateData.delivery.delivery_type&&!this.localMemberAddress)return this.$util.showToast({title:"请先选择您的收货地址"}),!1;if("store"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可提货的门店,请选择其他配送方式"}),!1;if(!this.orderCreateData.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号"}),!1;if(!this.deliveryTime)return this.$util.showToast({title:"请选择提货时间"}),!1}if("local"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可配送的门店,请选择其他配送方式"}),!1;if(this.calculateGoodsData.config.local.is_use&&1==this.calculateGoodsData.delivery.local.info.time_is_open&&!this.deliveryTime)return this.$util.showToast({title:"请选择送达时间"}),!1}}if(this.$refs.goodsForm){for(var e=!0,t=0;t<this.$refs.goodsForm.length;t++){var i=this.$refs.goodsForm[t];if(e=i.verify(),!e)break}if(!e)return!1}if(this.paymentData.system_form){var a=this.$refs.form.verify();if(!a)return!1}return!0},selectAddress:function(){var e={back:this.$util.getCurrentRoute().path,local:0,type:1};"local"==this.orderCreateData.delivery.delivery_type&&(e.local=1,e.type=2),this.$util.redirectTo("/pages_tool/member/address",e)},selectDeliveryType:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!this.orderCreateData.delivery||this.orderCreateData.delivery.delivery_type!=e.name){this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""};var a={delivery_type:e.name,delivery_type_name:e.title};"store"!=e.name&&"local"!=e.name||(e.store_list[0]&&(a.store_id=e.store_list[0].store_id),this.storeId=a.store_id?a.store_id:0,this.orderCreateData.member_address||(this.paymentData?this.orderCreateData.member_address={name:this.paymentData.member_account.nickname,mobile:this.paymentData.member_account.mobile}:i&&(this.orderCreateData.member_address={name:i.nickname,mobile:i.mobile}))),this.$set(this.orderCreateData,"delivery",a),this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.deliveryTime="",uni.removeStorageSync("deliveryTime"),uni.setStorageSync("delivery",a),"express"==this.orderCreateData.delivery.delivery_type||this.location||this.$util.getLocation(),t&&this.calculate(),"store"==e.name&&this.storetime("no"),"local"==e.name&&this.localtime("no")}},imageError:function(e){this.paymentData.goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},selectPickupPoint:function(e){if(e.store_id!=this.storeId){this.storeId=e.store_id,this.orderCreateData.delivery.store_id=e.store_id,this.calculate(),this.resetDeliveryTime();var t=uni.getStorageSync("delivery");t.store_id=e.store_id,uni.setStorageSync("delivery",t)}this.$refs.deliveryPopup.close()},resetDeliveryTime:function(){this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.deliveryTime="",uni.removeStorageSync("deliveryTime")},storetime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.storeInfo){var t=this.$util.deepClone(this.storeInfo);t.delivery_time="string"==typeof t.delivery_time&&t.delivery_time?JSON.parse(t.delivery_time):t.delivery_time,t.delivery_time&&(void 0!=t.delivery_time.length||t.delivery_time.length)||(t.delivery_time=[{start_time:t.start_time,end_time:t.end_time}]);var i={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(i,e),this.$forceUpdate()}},selectPickupTime:function(e){this.deliveryTime=e.data.month+"("+e.data.time+")",this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:e.data.start_date,end_date:e.data.end_date},uni.setStorageSync("deliveryTime",{deliveryTime:this.deliveryTime,buyer_ask_delivery_time:this.orderCreateData.delivery.buyer_ask_delivery_time,delivery_type:this.orderCreateData.delivery.delivery_type})},openPopup:function(e){this.tempData=this.$util.deepClone(this.orderCreateData),this.$refs[e].open()},closePopup:function(e){this.orderCreateData=this.$util.deepClone(this.tempData),this.$refs[e].close(),this.tempData=null},saveBuyerMessage:function(){this.calculate(),this.$refs.buyerMessagePopup.close()},payClose:function(){this.$util.redirectTo("/pages/order/detail",{order_id:this.$refs.choosePaymentPopup.payInfo.order_id},"redirectTo")},localtime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.calculateGoodsData&&this.calculateGoodsData.config.local){var t=this.$util.deepClone(this.calculateGoodsData.delivery.local.info);if(Object.keys(t).length){t.delivery_time&&(t.end_time=t.delivery_time[t.delivery_time.length-1].end_time);var i={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(i,e)}}},editForm:function(e){this.tempFormData={index:e,json_data:this.$util.deepClone(this.goodsData.goods_list[e].goods_form.json_data)},this.$refs.editFormPopup.open()},saveForm:function(){this.$refs.tempForm.verify()&&(this.$set(this.paymentData.goods_list[this.tempFormData.index].goods_form,"json_data",this.$refs.tempForm.formData),this.$refs.editFormPopup.close())},back:function(){uni.navigateBack({delta:1})},getStoreList:function(){var e=null;return this.orderCreateData.delivery&&("local"==this.orderCreateData.delivery.delivery_type&&this.localConfig&&(e=this.localConfig.store_list,e=e.reduce((function(e,t){return(0,r.default)((0,r.default)({},e),{},(0,o.default)({},t.store_id,t))}),{})),"store"==this.orderCreateData.delivery.delivery_type&&this.storeConfig&&(e=this.storeConfig.store_list,e=e.reduce((function(e,t){return(0,r.default)((0,r.default)({},e),{},(0,o.default)({},t.store_id,t))}),{}))),e}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)}}};t.default=n},6064:function(e,t,i){"use strict";var a=i("b42f"),o=i.n(a);o.a},"69da":function(e,t,i){"use strict";var a=i("bf89"),o=i.n(a);o.a},"9da1":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={pageMeta:i("7854").default,nsForm:i("ae30").default,payment:i("b6f2").default,uniPopup:i("d745").default,nsSelectTime:i("a523").default,nsLogin:i("2910").default,loadingCover:i("c003").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":e.themeColor}}),i("v-uni-view",[i("v-uni-view",{staticClass:"order-container",class:{"safe-area":e.isIphoneX}},[i("v-uni-scroll-view",{staticClass:"order-scroll-container",attrs:{"scroll-y":"true"}},[i("v-uni-view",{staticClass:"payment-navbar-block"}),e.paymentData?[e.paymentData.is_virtual?[i("v-uni-view",{staticClass:"mobile-wrap"},[i("v-uni-view",{staticClass:"tips color-base-text"},[i("v-uni-text",{staticClass:"iconfont icon-gantanhao"}),e._v("购买虚拟类商品需填写手机号，方便商家与您联系")],1),i("v-uni-view",{staticClass:"form-group"},[i("v-uni-text",{staticClass:"icon"},[i("v-uni-image",{attrs:{src:e.$util.img("public/uniapp/order/icon-mobile.png"),mode:"widthFix"}})],1),i("v-uni-text",{staticClass:"text"},[e._v("手机号码")]),i("v-uni-input",{staticClass:"input",attrs:{type:"number",maxlength:"11",placeholder:"请输入您的手机号码","placeholder-class":"color-tip placeholder"},model:{value:e.orderCreateData.delivery.member_address.mobile,callback:function(t){e.$set(e.orderCreateData.delivery.member_address,"mobile",t)},expression:"orderCreateData.delivery.member_address.mobile"}})],1)],1)]:[e.goodsData.delivery.express_type.length>1?i("v-uni-view",{staticClass:"delivery-mode"},[i("v-uni-view",{staticClass:"action"},e._l(e.goodsData.delivery.express_type,(function(t,a){return i("v-uni-view",{key:a,class:{active:t.name==e.orderCreateData.delivery.delivery_type},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.selectDeliveryType(t)}}},[e._v(e._s(t.title)),i("v-uni-view",{staticClass:"out-radio"})],1)})),1)],1):e._e(),"express"==e.orderCreateData.delivery.delivery_type?i("v-uni-view",{staticClass:"address-box",class:{"not-delivery-type":e.goodsData.delivery.express_type.length<=1}},[e.memberAddress?i("v-uni-view",{staticClass:"info-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"content"},[i("v-uni-text",{staticClass:"name"},[e._v(e._s(e.memberAddress.name?e.memberAddress.name:""))]),i("v-uni-text",{staticClass:"mobile"},[e._v(e._s(e.memberAddress.mobile?e.memberAddress.mobile:""))]),i("v-uni-view",{staticClass:"desc-wrap"},[e._v(e._s(e.memberAddress.full_address?e.memberAddress.full_address:"")+"\n\t\t\t\t\t\t\t\t\t"+e._s(e.memberAddress.address?e.memberAddress.address:""))])],1),i("v-uni-text",{staticClass:"cell-more iconfont icon-right"})],1):i("v-uni-view",{staticClass:"empty-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"info"},[e._v("请设置收货地址")]),i("v-uni-view",{staticClass:"cell-more"},[i("v-uni-view",{staticClass:"iconfont icon-right"})],1)],1),i("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],1):e._e(),"local"==e.orderCreateData.delivery.delivery_type?i("v-uni-view",{staticClass:"address-box",class:{"not-delivery-type":e.goodsData.delivery.express_type.length<=1}},[e.localMemberAddress?i("v-uni-view",[e.storeList&&Object.keys(e.storeList).length>1?[e.storeInfo?i("v-uni-view",{staticClass:"local-delivery-store",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.deliveryPopup.open()}}},[i("v-uni-view",{staticClass:"info"},[e._v("由"),i("v-uni-text",{staticClass:"store-name"},[e._v(e._s(e.storeInfo.store_name))]),e._v("提供配送")],1),i("v-uni-view",{staticClass:"cell-more"},[i("v-uni-text",[e._v("点击切换")]),i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1):i("v-uni-view",{staticClass:"local-delivery-store"},[i("v-uni-view",{staticClass:"info"},[i("v-uni-text",{staticClass:"store-name"},[e._v("您的附近没有可配送的门店，请选择其他配送方式")])],1)],1)]:e._e(),i("v-uni-view",{staticClass:"info-wrap local",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"content"},[i("v-uni-text",{staticClass:"name"},[e._v(e._s(e.localMemberAddress.name?e.localMemberAddress.name:""))]),i("v-uni-text",{staticClass:"mobile"},[e._v(e._s(e.localMemberAddress.mobile?e.localMemberAddress.mobile:""))]),i("v-uni-view",{staticClass:"desc-wrap"},[e._v(e._s(e.localMemberAddress.full_address?e.localMemberAddress.full_address:"")+"\n\t\t\t\t\t\t\t\t\t\t"+e._s(e.localMemberAddress.address?e.localMemberAddress.address:""))])],1),i("v-uni-text",{staticClass:"cell-more iconfont icon-right"})],1),e.calculateGoodsData.config.local.is_use&&1==e.calculateGoodsData.delivery.local.info.time_is_open?i("v-uni-view",{staticClass:"local-box"},[i("v-uni-view",{staticClass:"pick-block",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.localtime("")}}},[i("v-uni-view",{staticClass:"title font-size-base"},[e._v("送达时间")]),i("v-uni-view",{staticClass:"time-picker"},[i("v-uni-text",{class:{"color-tip":!e.deliveryTime}},[e._v(e._s(e.deliveryTime?e.deliveryTime:"请选择送达时间"))]),i("v-uni-text",{staticClass:"iconfont icon-right cell-more"})],1)],1)],1):e._e()],2):i("v-uni-view",{staticClass:"empty-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"info"},[e._v("请设置收货地址")]),i("v-uni-view",{staticClass:"cell-more"},[i("v-uni-view",{staticClass:"iconfont icon-right"})],1)],1),i("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],1):e._e(),"store"==e.orderCreateData.delivery.delivery_type?i("v-uni-view",{staticClass:"store-box",class:{"not-delivery-type":e.goodsData.delivery.express_type.length<=1}},[e.storeInfo?[i("v-uni-view",{staticClass:"store-info",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.deliveryPopup.open()}}},[i("v-uni-view",{staticClass:"store-address-info"},[i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"title"},[i("v-uni-text",[e._v(e._s(e.storeInfo.store_name))])],1),i("v-uni-view",{staticClass:"store-detail"},[e.storeInfo.open_date?i("v-uni-view",[e._v("营业时间："+e._s(e.storeInfo.open_date))]):e._e(),i("v-uni-view",{staticClass:"address"},[e._v(e._s(e.storeInfo.full_address)+" "+e._s(e.storeInfo.address))])],1)],1),i("v-uni-view",{staticClass:"cell-more iconfont icon-right"})],1)],1),i("v-uni-view",{staticClass:"mobile-wrap store-mobile"},[i("v-uni-view",{staticClass:"form-group"},[i("v-uni-text",{staticClass:"text"},[e._v("姓名")]),i("v-uni-input",{staticClass:"input",attrs:{type:"text","placeholder-class":"color-tip placeholder",disabled:!0},model:{value:e.orderCreateData.member_address.name,callback:function(t){e.$set(e.orderCreateData.member_address,"name",t)},expression:"orderCreateData.member_address.name"}})],1)],1),i("v-uni-view",{staticClass:"mobile-wrap store-mobile"},[i("v-uni-view",{staticClass:"form-group"},[i("v-uni-text",{staticClass:"text"},[e._v("预留手机")]),i("v-uni-input",{staticClass:"input",attrs:{type:"number",maxlength:"11",placeholder:"请输入您的手机号码","placeholder-class":"color-tip placeholder"},model:{value:e.orderCreateData.member_address.mobile,callback:function(t){e.$set(e.orderCreateData.member_address,"mobile",t)},expression:"orderCreateData.member_address.mobile"}})],1)],1),i("v-uni-view",{staticClass:"store-time",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.storetime("")}}},[i("v-uni-view",{staticClass:"left"},[e._v("提货时间")]),i("v-uni-view",{staticClass:"right"},[e._v(e._s(e.deliveryTime)),i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1)]:i("v-uni-view",{staticClass:"empty"},[e._v("当前无自提门店，请选择其它配送方式")]),i("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],2):e._e()],i("v-uni-view",{staticClass:"site-wrap order-goods"},[i("v-uni-view",{staticClass:"site-body"},e._l(e.goodsData.goods_list,(function(t,a){return i("v-uni-view",{key:a,staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-wrap"},[i("v-uni-view",{staticClass:"goods-img",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})}}},[i("v-uni-image",{attrs:{src:e.$util.img(t.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.imageError(a)}}})],1),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"goods-name",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})}}},[e._v(e._s(t.sku_name))]),t.sku_spec_format?i("v-uni-view",{staticClass:"sku"},[i("v-uni-view",{staticClass:"goods-spec"},[e._l(t.sku_spec_format,(function(t,a){return[i("v-uni-view",[e._v(e._s(t.spec_value_name))])]}))],2)],1):e._e(),0==t.is_virtual?[e.orderCreateData.delivery&&e.orderCreateData.delivery.delivery_type&&t.support_trade_type&&-1==t.support_trade_type.indexOf(e.orderCreateData.delivery.delivery_type)?i("v-uni-view",{staticClass:"error-tips"},[i("v-uni-text",{staticClass:"iconfont icon-gantanhao"}),i("v-uni-text",[e._v("该商品不支持"+e._s(e.orderCreateData.delivery.delivery_type_name))])],1):e._e()]:e._e(),t.error&&t.error.message?i("v-uni-view",{staticClass:"error-tips"},[i("v-uni-text",{staticClass:"iconfont icon-gantanhao"}),i("v-uni-text",[e._v(e._s(t.error.message))])],1):e._e(),i("v-uni-view",{staticClass:"goods-sub-section"},[i("v-uni-view",{staticClass:"color-base-text"},[i("v-uni-text",{staticClass:"unit price-style small"}),i("v-uni-text",{staticClass:"goods-price price-style large"}),i("v-uni-text",{staticClass:"unit price-style small"})],1),i("v-uni-view",[i("v-uni-text",{staticClass:"font-size-tag"},[e._v("x")]),i("v-uni-text",{staticClass:"font-size-base"},[e._v(e._s(t.num))])],1)],1)],2)],1),t.goods_form?i("v-uni-view",{staticClass:"goods-form",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.editForm(a)}}},[i("ns-form",{ref:"goodsForm",refInFor:!0,attrs:{data:t.goods_form.json_data,"custom-attr":{sku_id:t.sku_id,form_id:t.goods_form.id}}}),i("v-uni-text",{staticClass:"cell-more iconfont icon-right"}),i("v-uni-view",{staticClass:"shade"})],1):e._e()],1)})),1)],1),i("v-uni-view",{staticClass:"site-wrap buyer-message"},[i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[e._v("买家留言")]),i("v-uni-view",{staticClass:"box text-overflow ",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("buyerMessagePopup")}}},[e.orderCreateData.buyer_message?i("v-uni-text",[e._v(e._s(e.orderCreateData.buyer_message))]):i("v-uni-text",{staticClass:"color-sub"},[e._v("无留言")])],1),i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1),e.paymentData.system_form?i("v-uni-view",{staticClass:"system-form-wrap"},[i("ns-form",{ref:"form",attrs:{data:e.paymentData.system_form.json_data}})],1):e._e(),e.calculateData?[i("v-uni-view",{staticClass:"order-submit bottom-safe-area"},[i("v-uni-view",{staticClass:"submit-btn"},[i("v-uni-button",{staticClass:"sava-btn mini",attrs:{type:"primary",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.create()}}},[e._v("立即兑换")])],1)],1),i("v-uni-view",{staticClass:"order-submit-block"}),e.calculateData?i("payment",{ref:"choosePaymentPopup",on:{close:function(t){arguments[0]=t=e.$handleEvent(t),e.payClose.apply(void 0,arguments)}}}):e._e()]:e._e(),e.storeList?i("uni-popup",{ref:"deliveryPopup",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"delivery-popup popup"},[i("v-uni-view",{staticClass:"popup-header"},[i("v-uni-text",{staticClass:"tit"},[e._v("已为您甄选出附近所有相关门店")]),i("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("deliveryPopup")}}})],1),i("v-uni-view",{staticClass:"popup-body store-popup",class:{"safe-area":e.isIphoneX}},[i("v-uni-view",{staticClass:"delivery-content"},[e._l(e.storeList,(function(t,a){return i("v-uni-view",{key:a,staticClass:"item-wrap",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.selectPickupPoint(t)}}},[i("v-uni-view",{staticClass:"detail"},[i("v-uni-view",{staticClass:"name",class:t.store_id==e.orderCreateData.delivery.store_id?"color-base-text":""},[i("v-uni-text",[e._v(e._s(t.store_name))]),t.distance?i("v-uni-text",[e._v("("+e._s(t.distance)+"km)")]):e._e()],1),i("v-uni-view",{staticClass:"info"},[i("v-uni-view",{staticClass:"font-size-goods-tag",class:t.store_id==e.orderCreateData.delivery.store_id?"color-base-text":""},[e._v("营业时间："+e._s(t.open_date))]),i("v-uni-view",{staticClass:"font-size-goods-tag",class:t.store_id==e.orderCreateData.delivery.store_id?"color-base-text":""},[e._v("地址："+e._s(t.full_address)+e._s(t.address))])],1)],1),t.store_id==e.orderCreateData.delivery.store_id?i("v-uni-view",{staticClass:"icon"},[i("v-uni-text",{staticClass:"iconfont icon-yuan_checked color-base-text"})],1):e._e()],1)})),e.storeList?e._e():i("v-uni-view",{staticClass:"empty"},[e._v("所选择收货地址附近没有可以自提的门店")])],2)],1)],1)],1):e._e(),i("uni-popup",{ref:"buyerMessagePopup",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"buyermessag-popup popup",staticStyle:{height:"auto"},on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[i("v-uni-view",{staticClass:"popup-header"},[i("v-uni-text",{staticClass:"tit"},[e._v("买家留言")]),i("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("buyerMessagePopup")}}})],1),i("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[i("v-uni-view",[i("v-uni-view",{staticClass:"buyermessag-cell"},[i("v-uni-view",{staticClass:"buyermessag-form-group"},[i("v-uni-textarea",{attrs:{type:"text",maxlength:"100",placeholder:"留言前建议先与商家协调一致","placeholder-class":"color-tip"},model:{value:e.orderCreateData.buyer_message,callback:function(t){e.$set(e.orderCreateData,"buyer_message",t)},expression:"orderCreateData.buyer_message"}})],1)],1)],1)],1),i("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveBuyerMessage.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"confirm-btn color-base-bg"},[e._v("确定")])],1)],1)],1),i("uni-popup",{ref:"editFormPopup",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"form-popup popup",staticStyle:{height:"auto"},on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[i("v-uni-view",{staticClass:"popup-header"},[i("v-uni-text",{staticClass:"tit"},[e._v("买家信息")]),i("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.editFormPopup.close()}}})],1),i("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[e.tempFormData?i("ns-form",{ref:"tempForm",attrs:{data:e.tempFormData.json_data}}):e._e()],1),i("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveForm.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"confirm-btn color-base-bg"},[e._v("确定")])],1)],1)],1)]:e._e()],2),i("ns-select-time",{ref:"timePopup",on:{selectTime:function(t){arguments[0]=t=e.$handleEvent(t),e.selectPickupTime.apply(void 0,arguments)}}}),i("ns-login",{ref:"login"}),i("loading-cover",{ref:"loadingCover"})],1)],1)],1)},r=[]},a523:function(e,t,i){"use strict";i.r(t);var a=i("d68f"),o=i("c4af");for(var r in o)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(r);i("6064");var s=i("828b"),n=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"8e6fd976",null,!1,a["a"],void 0);t["default"]=n.exports},b32b:function(e,t,i){var a=i("30dc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("967d").default;o("bd9dd32a",a,!0,{sourceMap:!1,shadowMode:!1})},b42f:function(e,t,i){var a=i("f7d5");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("967d").default;o("599afa74",a,!0,{sourceMap:!1,shadowMode:!1})},bf89:function(e,t,i){var a=i("e640");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("967d").default;o("510a744a",a,!0,{sourceMap:!1,shadowMode:!1})},c4af:function(e,t,i){"use strict";i.r(t);var a=i("3672"),o=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},d68f:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={uniPopup:i("d745").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"ns-time"},[i("uni-popup",{ref:"selectTime",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"box"},[i("v-uni-view",{staticClass:"title"},[e.obj.delivery&&"local"==e.obj.delivery.delivery_type?[e._v("选择送达时间")]:e._e(),e.obj.delivery&&"store"==e.obj.delivery.delivery_type?[e._v("选择自提时间")]:e._e(),i("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.close.apply(void 0,arguments)}}})],2),i("v-uni-view",{staticClass:"body"},[i("v-uni-scroll-view",{staticClass:"left",attrs:{"scroll-y":!0}},e._l(e.dayData,(function(t,a){return i("v-uni-view",{key:a,staticClass:"item",class:a==e.keyJudge?"itemDay":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectTime("days",a,"yes")}}},[t.title?[e._v(e._s(t.title))]:[e._v(e._s(t.month))],i("v-uni-text",{staticClass:"itemtext"},[e._v(e._s(t.Day))])],2)})),1),i("v-uni-scroll-view",{staticClass:"right",attrs:{"scroll-y":!0}},e._l(e.timeData,(function(t,a){return i("v-uni-view",{key:a,staticClass:"item",class:e.key==e.keyJudge&&a==e.keys?"itemTime":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectTime("time",a,"yes")}}},[e._v(e._s(t)),e.key==e.keyJudge&&a==e.keys?i("v-uni-text",{staticClass:"iconfont icon-yuan_checked color-base-text"}):e._e()],1)})),1)],1)],1)],1)],1)},r=[]},e640:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-567b126c] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:none;max-height:unset!important;overflow-y:hidden!important}[data-v-567b126c] .uni-popup__wrapper{border-radius:%?20?% %?20?% 0 0}[data-v-567b126c] .uni-popup{z-index:8}.sava-btn[data-v-567b126c],\r\n.submit-btn[data-v-567b126c]{width:100%!important}',""]),e.exports=t},f7d5:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.box[data-v-8e6fd976]{height:%?728?%}.box .title[data-v-8e6fd976]{padding:0 %?30?%;box-sizing:border-box;text-align:center;font-size:%?28?%;font-weight:700;position:relative;height:%?90?%;line-height:%?90?%;border-bottom:%?1?% solid #f7f4f4}.box .title .icon-close[data-v-8e6fd976]{font-size:%?26?%;color:#909399;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.box .body[data-v-8e6fd976]{width:100%;height:calc(100% - %?90?%);display:flex;align-items:center}.box .body .left[data-v-8e6fd976]{width:%?230?%;background:#f8f8f8;height:100%}.box .body .left .item[data-v-8e6fd976]{width:100%;padding:%?16?% %?30?%;box-sizing:border-box;text-align:center;font-size:%?24?%;display:flex;align-items:center}.box .body .left .itemDay[data-v-8e6fd976]{background:#fff}.box .body .right[data-v-8e6fd976]{width:calc(100% - %?230?%);height:100%;padding:0 %?30?%;box-sizing:border-box}.box .body .right .item[data-v-8e6fd976]{width:100%;font-size:%?24?%;border-bottom:%?1?% solid #eee;display:flex;align-items:center;justify-content:space-between;height:%?72?%}.box .body .right .item .icon-yuan_checked[data-v-8e6fd976]{font-size:%?38?%;margin-right:%?30?%}.box .body .right .itemTime[data-v-8e6fd976]{color:var(--main-color)}',""]),e.exports=t}}]);