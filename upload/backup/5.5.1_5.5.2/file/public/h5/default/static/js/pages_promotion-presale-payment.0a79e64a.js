(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-presale-payment"],{"094b":function(e,t,a){var o=a("ced8");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var i=a("967d").default;i("3e0e2dd8",o,!0,{sourceMap:!1,shadowMode:!1})},"0e09":function(e,t,a){var o=a("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-edad5242] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:none;max-height:unset!important;overflow-y:hidden!important}[data-v-edad5242] .uni-popup__wrapper{border-radius:%?20?% %?20?% 0 0}[data-v-edad5242] .uni-popup{z-index:8}.store-box .store-mobile[data-v-edad5242]{padding-bottom:0}.depositRefund-wrap[data-v-edad5242]{background-color:#fff;padding:%?50?% %?80?%;text-align:center}.depositRefund-wrap .depositRefund-con[data-v-edad5242]{font-size:%?30?%}.depositRefund-wrap .popup-footer[data-v-edad5242]{margin-top:%?30?%;display:flex;justify-content:space-between;align-items:center}.depositRefund-wrap .confirm-btn[data-v-edad5242]{width:%?200?%;height:%?70?%;font-size:%?28?%;border-radius:%?10?%;border:%?2?% solid #eee;color:#909399;display:flex;justify-content:center;align-items:center}.depositRefund-wrap .confirm-btn.color-base-bg[data-v-edad5242]{color:#fff}.pre-sale-money[data-v-edad5242]{margin:%?20?% %?30?%;border-radius:%?10?%;background:#fff;padding:%?20?% %?30?%;display:flex;justify-content:space-between;align-items:center}.pre-sale-money uni-view[data-v-edad5242]{display:flex;align-items:center}.pre-sale-money uni-view uni-text[data-v-edad5242]{margin-left:%?10?%;font-size:%?32?%}.agreement-wrap[data-v-edad5242]{width:70vw;background-color:#fff;padding:%?30?% %?40?%}.agreement-wrap .agreement-title[data-v-edad5242]{font-weight:700;text-align:center;margin-bottom:%?24?%}.agreement-wrap .agreement-con[data-v-edad5242]{min-height:10vh;max-height:46vh;overflow:auto}.agreement-wrap .popup-footer[data-v-edad5242]{margin-top:%?30?%;display:flex;justify-content:center;align-items:center}.agreement-wrap .confirm-btn[data-v-edad5242]{width:%?200?%;height:%?70?%;font-size:%?28?%;border-radius:%?10?%;border:%?2?% solid #eee;color:#fff;display:flex;justify-content:center;align-items:center}',""]),e.exports=t},"30a2":function(e,t,a){"use strict";a.r(t);var o=a("b85a"),i=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);t["default"]=i.a},"3f67":function(e,t,a){"use strict";var o=a("fe4a"),i=a.n(o);i.a},"629b":function(e,t,a){"use strict";var o=a("094b"),i=a.n(o);i.a},7854:function(e,t,a){"use strict";a.r(t);var o=a("8ba8"),i=a("f48d");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);var n=a("828b"),s=Object(n["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=s.exports},"7c33":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d4b5"),a("bf0f"),a("2797"),a("c9b5"),a("ab80"),a("e966"),a("5c47"),a("0506"),a("e838"),a("dc8a");var o={data:function(){return{currTime:"",isIphoneX:!1,orderCreateData:{is_balance:0,pay_password:"",is_invoice:0,invoice_type:0,invoice_title_type:1,is_tax_invoice:0,invoice_title:"",taxpayer_number:"",invoice_content:"",invoice_full_address:"",invoice_email:"",buyer_message:"",buyer_ask_delivery_title:""},orderPaymentData:{shop_goods_list:{site_name:"",express_type:[],coupon_list:[],invoice:{invoice_content_array:[]}},delivery:{site_name:"",express_type:[],coupon_list:[],invoice:{invoice_content_array:[]}},member_account:{balance:0,is_pay_password:0},local_config:{info:{start_time:0,end_time:0,time_week:[]}},invoice:{invoice_status:0},goods_num:0},isSub:!1,tempData:null,is_deposit_back:0,switch_state:!0,storeInfo:{storeList:[],currStore:{}},member_address:{name:"",mobile:""},timeInfo:{week:0,start_time:0,end_time:0,showTimeBar:!1},isFocus:!1,presale_id:0,pay_password:"",action:{icon:""},deliveryWeek:"",menuButtonBounding:{},memberAddress:null,localMemberAddress:null}},methods:{openPopup:function(e){this.$refs[e].open()},closePopup:function(e){this.tempData&&(Object.assign(this.orderCreateData,this.tempData),Object.assign(this.orderPaymentData,this.tempData),this.tempData=null,this.$forceUpdate()),this.$refs[e].close()},selectAddress:function(){var e={back:this.$util.getCurrentRoute().path,local:0,type:1};"local"==this.orderCreateData.delivery.delivery_type&&(e.local=1,e.type=2),this.$util.redirectTo("/pages_tool/member/address",e)},getOrderPaymentData:function(){var e=this;Object.assign(this.orderCreateData,uni.getStorageSync("presaleOrderCreateData")),this.location&&(this.orderCreateData.latitude=this.location.latitude,this.orderCreateData.longitude=this.location.longitude),this.orderCreateData?this.$api.sendRequest({url:"/presale/api/ordercreate/depositPayment",data:this.orderCreateData,success:function(t){t.code>=0?(e.orderPaymentData=t.data,e.is_deposit_back=t.data.promotion_presale_info.is_deposit_back,e.handlePaymentData(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()):e.$util.showToast({title:"未获取到创建订单所需数据!！",success:function(){}})},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}}):this.$util.showToast({title:"未获取到创建订单所需数据!！",success:function(){setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1500)}})},handlePaymentData:function(){var e=this;this.orderCreateData.delivery={},this.orderCreateData.coupon={},this.orderCreateData.buyer_message="",this.orderCreateData.is_balance=0,this.orderCreateData.pay_password="",this.orderCreateData.is_invoice=0,this.orderCreateData.invoice_type=1,this.orderCreateData.invoice_title_type=1,this.orderCreateData.is_tax_invoice=0,this.orderCreateData.invoice_title="",1==this.orderPaymentData.invoice.invoice_status&&(this.orderPaymentData.invoice.invoice_type=this.orderPaymentData.invoice.invoice_type.split(","),this.orderCreateData.invoice_type=this.orderPaymentData.invoice.invoice_type[0]);var t=JSON.parse(JSON.stringify(this.orderPaymentData));if(this.orderCreateData.order_key=t.order_key,void 0!=t.delivery.express_type&&void 0!=t.delivery.express_type[0]){var a=t.delivery.express_type;this.orderCreateData.delivery.store_id=0;var o=uni.getStorageSync("delivery");if(o){var i=o.delivery_type,r=o.delivery_type_name;a.forEach((function(t){("store"==i&&t.name==i||"local"==i&&t.name==i)&&e.storeSelected(t)})),"store"==i&&(this.member_address={name:t.member_account.nickname,mobile:""!=t.member_account.mobile?t.member_account.mobile:""})}else{i=a[0].name;"store"==i&&(this.member_address={name:t.member_account.nickname,mobile:""!=t.member_account.mobile?t.member_account.mobile:""});r=a[0].title}this.orderCreateData.delivery.delivery_type=i,this.orderCreateData.delivery.delivery_type_name=r,"store"!=a[0].name&&"local"!=a[0].name||this.storeSelected(a[0])}if(this.orderPaymentData.is_virtual&&(this.orderCreateData.member_address={name:t.member_account.nickname,mobile:""!=t.member_account.mobile?t.member_account.mobile:""}),1==this.orderPaymentData.invoice.invoice_status){var n=this.orderPaymentData.invoice.invoice_content_array;n.length&&(this.orderCreateData.invoice_content=n[0])}this.orderPaymentData.goods_list.forEach((function(e){e.sku_spec_format?e.sku_spec_format=JSON.parse(e.sku_spec_format):e.sku_spec_format=[]})),this.orderCalculate()},getTimeStr:function(e){var t=parseInt(e/3600).toString(),a=parseInt(e%3600/60).toString();return 1==a.length&&(a="0"+a),1==t.length&&(t="0"+t),t+":"+a},orderCalculate:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);t.delivery=JSON.stringify(t.delivery),t.coupon=JSON.stringify(t.coupon),"store"==this.orderCreateData.delivery.delivery_type?t.member_address=JSON.stringify(this.member_address):t.member_address=JSON.stringify(t.member_address),this.$api.sendRequest({url:"/presale/api/ordercreate/depositCalculate",data:t,success:function(t){t.code>=0?(!t.data.is_virtual&&t.data.delivery&&("express"==t.data.delivery.delivery_type&&(e.memberAddress=t.data.delivery.member_address),"local"==t.data.delivery.delivery_type&&(e.localMemberAddress=t.data.delivery.member_address)),e.orderPaymentData.delivery_money=t.data.delivery_money,e.orderPaymentData.coupon_money=t.data.coupon_money,e.orderPaymentData.invoice_money=t.data.invoice_money,e.orderPaymentData.invoice_delivery_money=t.data.invoice_delivery_money,e.orderPaymentData.promotion_money=t.data.promotion_money,e.orderPaymentData.order_money=t.data.order_money,e.orderPaymentData.balance_money=t.data.balance_money,e.orderPaymentData.pay_money=t.data.pay_money,e.orderPaymentData.goods_money=t.data.goods_money,e.orderPaymentData.final_money=t.data.final_money,e.orderPaymentData.goods_num=t.data.goods_num,e.orderPaymentData.delivery.member_address=t.data.delivery.member_address,t.data.config.local&&(e.orderPaymentData.local_config=t.data.config.local),e.createBtn(),e.$forceUpdate()):e.$util.showToast({title:t.message})}})},createBtn:function(){return!(this.orderPaymentData.delivery&&"local"==this.orderPaymentData.delivery.delivery_type&&this.orderPaymentData.delivery&&this.orderPaymentData.delivery.error&&this.orderPaymentData.delivery.start_money>this.orderPaymentData.presale_deposit_money)&&!(this.orderPaymentData.delivery&&"local"==this.orderPaymentData.delivery.delivery_type&&this.orderPaymentData.delivery&&this.orderPaymentData.delivery.error&&""!==this.orderPaymentData.delivery.error)},orderCreate:function(){var e=this;if(this.verify()){if(this.isSub)return;this.isSub=!0;var t=this.$util.deepClone(this.orderCreateData);t.delivery=JSON.stringify(t.delivery),t.coupon=JSON.stringify(t.coupon),"store"==this.orderCreateData.delivery.delivery_type?t.member_address=JSON.stringify(this.member_address):t.member_address=JSON.stringify(t.member_address),this.$api.sendRequest({url:"/presale/api/ordercreate/depositCreate",data:t,success:function(t){if(t.code>=0)if(0==e.orderPaymentData.pay_money)e.$util.redirectTo("/pages_tool/pay/result",{code:t.data},"redirectTo");else{var a=uni.getStorageSync("presaleOrderCreateData");a.out_trade_no=t.data,uni.setStorageSync("presaleOrderCreateData",a),e.$refs.choosePaymentPopup.getPayInfo(t.data),e.isSub=!1}else e.isSub=!1,uni.hideLoading(),e.$refs.payPassword&&e.$refs.payPassword.close(),10==t.data.error_code||12==t.data.error_code?uni.showModal({title:"订单未创建",content:t.message,confirmText:"去设置",success:function(t){t.confirm&&e.selectAddress()}}):e.$util.showToast({title:t.message})}})}},verify:function(){if(1==this.orderPaymentData.is_virtual){if(!this.orderCreateData.member_address.mobile.length)return this.$util.showToast({title:"请输入您的手机号码"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号码"}),!1}if(0==this.orderPaymentData.is_virtual){if("store"!=this.orderCreateData.delivery.delivery_type&&!this.orderPaymentData.delivery.member_address)return this.$util.showToast({title:"express"==this.orderCreateData.delivery.delivery_type?"请先选择您的收货地址":"当前地址不在该门店配送区域，请重新选择可配送该区域的门店"}),!1;if("{}"==JSON.stringify(this.orderCreateData.delivery))return this.$util.showToast({title:"店铺未设置配送方式"}),!1;if("store"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可提货的门店,请选择其他配送方式"}),!1;if(!this.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.member_address.mobile))return this.$util.showToast({title:"请输入正确的预留手机"}),!1}if("local"==this.orderCreateData.delivery.delivery_type&&!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可配送的门店,请选择其他配送方式"}),!1}return!(1==this.orderCreateData.is_invoice&&!this.invoiceVerify())},openSitePromotion:function(){this.$refs.sitePromotionPopup.open()},openSiteDelivery:function(){this.tempData={delivery:this.$util.deepClone(this.orderPaymentData.delivery)},this.$refs.deliveryPopup.open()},selectDeliveryType:function(e){uni.setStorageSync("delivery",{delivery_type:e.name,delivery_type_name:e.title}),this.orderCreateData.delivery.delivery_type=e.name,this.orderCreateData.delivery.delivery_type_name=e.title,"store"==e.name&&(this.storeSelected(e),this.member_address.name=this.orderPaymentData.member_account.nickname,this.member_address.mobile||(this.member_address.mobile=""!=this.orderPaymentData.member_account.mobile?this.orderPaymentData.member_account.mobile:"")),"local"==e.name&&this.storeSelected(e),this.orderCalculate(),this.$forceUpdate()},storeSelected:function(e){this.storeInfo.storeList=e.store_list;var t=e.store_list[0]?e.store_list[0]:null;this.selectPickupPoint(t)},selectPickupPoint:function(e){if(e){this.orderCreateData.delivery.store_id=e.store_id,this.storeInfo.currStore=e;var t=uni.getStorageSync("delivery");t&&(t.store_id=e.store_id,uni.setStorageSync("delivery",t))}else this.orderCreateData.delivery.store_id=0,this.storeInfo.currStore={};this.orderCalculate(),this.$forceUpdate(),this.$refs["deliveryPopup"].close()},popupConfirm:function(e){this.$refs[e].close(),this.orderCalculate(),this.$forceUpdate(),this.tempData=null},useBalance:function(){this.orderCreateData.is_balance?this.orderCreateData.is_balance=0:this.orderCreateData.is_balance=1,this.orderCalculate(),this.$forceUpdate()},setPayPassword:function(){this.$util.redirectTo("/pages_tool/member/pay_password",{back:"/pages_promotion/presale/payment"})},noSet:function(){this.orderCreateData.is_balance=0,this.$refs.payPassword.close(),this.orderCalculate(),this.$forceUpdate()},input:function(e){var t=this;6==e.length&&(uni.showLoading({title:"支付中...",mask:!0}),this.$api.sendRequest({url:"/api/member/checkpaypassword",data:{pay_password:e},success:function(a){a.code>=0?t.finalPay?(t.finalPay.pay_password=e,t.finalPay.member_id=t.memberInfo.member_id,t.finalPay.is_balance=t.finalPay.is_use_balance):(t.orderCreateData.pay_password=e,t.orderCreate()):(uni.hideLoading(),t.$util.showToast({title:a.message}))},fail:function(e){uni.hideLoading()}}))},imageError:function(e){this.orderPaymentData.goods_list[e].sku_image=this.$util.getDefaultImage().goods_img,this.$forceUpdate()},navigateBack:function(){this.$util.goBack()},changeIsInvoice:function(){0==this.orderCreateData.is_invoice?(this.orderCreateData.is_invoice=1,this.orderCreateData.invoice_type||(this.orderCreateData.invoice_type=this.orderPaymentData.invoice.invoice_type.split(",")[0])):this.orderCreateData.is_invoice=0,this.orderCalculate(),this.$forceUpdate()},changeInvoiceType:function(e){this.orderCreateData.invoice_type=e,this.orderCalculate(),this.$forceUpdate()},changeInvoiceTitleType:function(e){this.orderCreateData.invoice_title_type=e,this.orderCalculate(),this.$forceUpdate()},changeIsTaxInvoice:function(){0==this.orderCreateData.is_tax_invoice?this.orderCreateData.is_tax_invoice=1:this.orderCreateData.is_tax_invoice=0,this.$forceUpdate()},changeInvoiceContent:function(e){this.orderCreateData.invoice_content=e,this.$forceUpdate()},invoiceVerify:function(){if(!this.orderCreateData.invoice_title)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写发票抬头"}),!1;if(!this.orderCreateData.taxpayer_number&&2==this.orderCreateData.invoice_title_type)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写纳税人识别号"}),!1;if(1==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_full_address&&1==this.orderPaymentData.is_virtual)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写发票邮寄地址"}),!1;if(2==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_email)return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写邮箱"}),!1;if(2==this.orderCreateData.invoice_type){if(!/^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/.test(this.orderCreateData.invoice_email))return this.$refs.invoicePopup.open(),this.$util.showToast({title:"请填写正确的邮箱"}),!1}return!!this.orderCreateData.invoice_content||(this.$refs.invoicePopup.open(),this.$util.showToast({title:"请选择发票内容"}),!1)},saveInvoice:function(){1==this.orderCreateData.is_invoice?this.invoiceVerify()&&this.closePopup("invoicePopup"):this.closePopup("invoicePopup")},getTime:function(){var e=(new Date).getDay();this.timeInfo.week=["0","1","2","3","4","5","6"][e]},closeInvoicePopup:function(){this.$refs.invoicePopup.close()},switchChange:function(e){this.switch_state=e.detail.value},navigateTo:function(e){this.$util.redirectTo("/pages/goods/detail",{goods_id:e})},openChoosePayment:function(){if(1==this.is_deposit_back){if(!this.switch_state)return this.$util.showToast({title:"预售商品定金不支持退款，请确定同意定金不退款协议。"}),!1;this.$refs.depositRefund.open()}else uni.setStorageSync("paySource","presale"),console.log(111),this.verify()&&(console.log(222),this.$refs.choosePaymentPopup.open())},subscribeMessage:function(){this.$util.subscribeMessage("ORDER_PAY,ORDER_DELIVERY,ORDER_TAKE_DELIVERY")},toPayOrder:function(){uni.setStorageSync("paySource","presale"),this.verify()&&(this.$refs.depositRefund.close(),this.$refs.choosePaymentPopup.open())},closeDepositRefund:function(){this.$refs.depositRefund.close()},presaleAgreement:function(){this.$refs.presaleAgreement.open()},closePresaleAgreement:function(){this.$refs.presaleAgreement.close()},saveBuyerMessage:function(){this.$refs.buyerMessagePopup.close()},back:function(){uni.navigateBack({delta:1})}},onShow:function(){this.storeToken?this.getOrderPaymentData():this.$util.redirectTo("/pages_tool/login/index");var e=uni.getStorageSync("presaleOrderCreateData");e&&e.out_trade_no&&this.$util.redirectTo("/pages_promotion/presale/order_list",{},"redirectTo"),this.getTime(),this.isIphoneX=this.$util.uniappIsIPhoneX()},onLoad:function(e){e.id&&(this.presale_id=e.id),this.location||this.$util.getLocation()},onHide:function(){this.$refs.loadingCover&&this.$refs.loadingCover.show()},computed:{balanceDeduct:function(){var e=parseFloat(this.orderPaymentData.member_account.balance_total)<parseFloat(this.orderPaymentData.presale_deposit_money)?parseFloat(this.orderPaymentData.member_account.balance_total):parseFloat(this.orderPaymentData.presale_deposit_money);return e.toFixed(2)},presaleDiscount:function(){return(parseFloat(this.orderPaymentData.presale_money)-parseFloat(this.orderPaymentData.presale_deposit_money)).toFixed(2)}},watch:{location:function(e){e&&this.getOrderPaymentData()}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)},promotion:function(e){var t="";return e&&Object.keys(e).forEach((function(a){t+=e[a].content+"　"})),t}}};t.default=o},"8ba8":function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var o=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},i=[]},b85a:function(e,t,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(a("7c33")),r=o(a("d745")),n={components:{uniPopup:r.default},mixins:[i.default]};t.default=n},c384:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return o}));var o={pageMeta:a("7854").default,uniPopup:a("d745").default,nsPayment:a("7aec").default,loadingCover:a("c003").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":e.themeColor}}),a("v-uni-view",{staticClass:"order-container",class:{"safe-area":e.isIphoneX}},[a("v-uni-scroll-view",{staticClass:"order-scroll-container",attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"payment-navbar-block"}),0==e.orderPaymentData.is_virtual?[e.orderPaymentData.delivery.express_type.length>1?a("v-uni-view",{staticClass:"delivery-mode"},[a("v-uni-view",{staticClass:"action"},e._l(e.orderPaymentData.delivery.express_type,(function(t,o){return a("v-uni-view",{key:o,class:{active:t.name==e.orderCreateData.delivery.delivery_type},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectDeliveryType(t)}}},[e._v(e._s(t.title)),a("v-uni-view",{staticClass:"out-radio"})],1)})),1)],1):e._e(),"store"!=e.orderCreateData.delivery.delivery_type?a("v-uni-view",{staticClass:"address-box",class:{"not-delivery-type":e.orderPaymentData.delivery.express_type.length<=1}},["local"==e.orderCreateData.delivery.delivery_type?[e.storeInfo.storeList.length>1?[Object.keys(e.storeInfo.currStore).length?a("v-uni-view",{staticClass:"local-delivery-store",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.deliveryPopup.open()}}},[a("v-uni-view",{staticClass:"info"},[e._v("由"),a("v-uni-text",{staticClass:"store-name"},[e._v(e._s(e.storeInfo.currStore.store_name))]),e._v("提供配送")],1),a("v-uni-view",{staticClass:"cell-more"},[a("v-uni-text",[e._v("点击切换")]),a("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1):a("v-uni-view",{staticClass:"local-delivery-store"},[a("v-uni-view",{staticClass:"info"},[a("v-uni-text",{staticClass:"store-name"},[e._v("您的附近没有可配送的门店，请选择其他配送方式")])],1)],1)]:e._e(),e.localMemberAddress?a("v-uni-view",{staticClass:"info-wrap local",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"content"},[a("v-uni-text",{staticClass:"name font-size-base"},[e._v(e._s(e.localMemberAddress.name?e.localMemberAddress.name:""))]),a("v-uni-text",{staticClass:"font-size-base mobile"},[e._v(e._s(e.localMemberAddress.mobile?e.localMemberAddress.mobile:""))]),a("v-uni-text",{staticClass:"cell-more iconfont icon-right"}),a("v-uni-view",{staticClass:"desc-wrap"},[e._v(e._s(e.localMemberAddress.full_address?e.localMemberAddress.full_address:"")+"\n\t\t\t\t\t\t\t\t"+e._s(e.localMemberAddress.address?e.localMemberAddress.address:""))])],1)],1):a("v-uni-view",{staticClass:"empty-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"info"},[e._v("请设置收货地址")]),a("v-uni-view",{staticClass:"cell-more"},[a("v-uni-view",{staticClass:"iconfont icon-right"})],1)],1)]:e._e(),"express"==e.orderCreateData.delivery.delivery_type?[e.memberAddress?a("v-uni-view",{staticClass:"info-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"content"},[a("v-uni-text",{staticClass:"name font-size-base"},[e._v(e._s(e.memberAddress.name?e.memberAddress.name:""))]),a("v-uni-text",{staticClass:"font-size-base mobile"},[e._v(e._s(e.memberAddress.mobile?e.memberAddress.mobile:""))]),a("v-uni-text",{staticClass:"cell-more iconfont icon-right"}),a("v-uni-view",{staticClass:"desc-wrap"},[e._v(e._s(e.memberAddress.full_address?e.memberAddress.full_address:"")+"\n\t\t\t\t\t\t\t\t"+e._s(e.memberAddress.address?e.memberAddress.address:""))])],1)],1):a("v-uni-view",{staticClass:"empty-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"info"},[e._v("请设置收货地址")]),a("v-uni-view",{staticClass:"cell-more"},[a("v-uni-view",{staticClass:"iconfont icon-right"})],1)],1)]:e._e(),a("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],2):e._e(),"store"==e.orderCreateData.delivery.delivery_type?a("v-uni-view",{staticClass:"store-box",class:{"not-delivery-type":e.orderPaymentData.delivery.express_type.length<=1}},[e.storeInfo.currStore?[a("v-uni-view",{staticClass:"store-info",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSiteDelivery.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"store-address-info"},[a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-view",{staticClass:"title"},[a("v-uni-text",[e._v(e._s(e.storeInfo.currStore.store_name))])],1),a("v-uni-view",{staticClass:"store-detail"},[e.storeInfo.currStore.open_date?a("v-uni-view",[e._v("营业时间："+e._s(e.storeInfo.currStore.open_date))]):e._e(),a("v-uni-view",{staticClass:"address"},[e._v(e._s(e.storeInfo.currStore.full_address)+"\n\t\t\t\t\t\t\t\t\t\t"+e._s(e.storeInfo.currStore.address))])],1)],1),a("v-uni-view",{staticClass:"cell-more iconfont icon-right"})],1)],1),a("v-uni-view",{staticClass:"mobile-wrap store-mobile"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"text"},[e._v("姓名")]),a("v-uni-input",{staticClass:"input",attrs:{type:"text","placeholder-class":"color-tip placeholder",disabled:!0},model:{value:e.member_address.name,callback:function(t){e.$set(e.member_address,"name",t)},expression:"member_address.name"}})],1)],1),a("v-uni-view",{staticClass:"mobile-wrap store-mobile"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"text"},[e._v("预留手机")]),a("v-uni-input",{staticClass:"input",attrs:{type:"number",maxlength:"11",placeholder:"请输入您的手机号码","placeholder-class":"color-tip placeholder"},model:{value:e.member_address.mobile,callback:function(t){e.$set(e.member_address,"mobile",t)},expression:"member_address.mobile"}})],1)],1)]:a("v-uni-view",{staticClass:"empty"},[e._v("当前无自提门店，请选择其它配送方式")]),a("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],2):e._e()]:e._e(),1==e.orderPaymentData.is_virtual&&e.orderCreateData.member_address?a("v-uni-view",{staticClass:"mobile-wrap"},[a("v-uni-view",{staticClass:"tips color-base-text"},[a("v-uni-text",{staticClass:"iconfont icon-gantanhao"}),e._v("购买虚拟类商品需填写手机号，方便商家与您联系")],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"iconfont icon-dianhua2"}),a("v-uni-text",{staticClass:"text"},[e._v("手机号码")]),a("v-uni-input",{staticClass:"input",attrs:{type:"number",maxlength:"11",placeholder:"请输入您的手机号码","placeholder-class":"color-tip placeholder"},model:{value:e.orderCreateData.member_address.mobile,callback:function(t){e.$set(e.orderCreateData.member_address,"mobile",t)},expression:"orderCreateData.member_address.mobile"}})],1)],1):e._e(),a("v-uni-view",{staticClass:"site-wrap order-goods"},[a("v-uni-view",{staticClass:"site-body"},e._l(e.orderPaymentData.goods_list,(function(t,o){return a("v-uni-view",{key:o,staticClass:"goods-item"},[a("v-uni-view",{staticClass:"goods-wrap"},[a("v-uni-view",{staticClass:"goods-img",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.navigateTo(t.goods_id)}}},[a("v-uni-image",{attrs:{src:e.$util.img(t.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.imageError(o)}}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-view",{staticClass:"top-wrap"},[a("v-uni-view",{staticClass:"goods-name",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.navigateTo(t.goods_id)}}},[e._v(e._s(t.sku_name))]),t.sku_spec_format?a("v-uni-view",{staticClass:"sku"},[a("v-uni-view",{staticClass:"goods-spec"},[e._l(t.sku_spec_format,(function(a,o){return[e._v(e._s(a.spec_value_name)+"\n\t\t\t\t\t\t\t\t\t\t\t"+e._s(o<t.sku_spec_format.length-1?"; ":""))]}))],2)],1):e._e(),0==t.is_virtual&&e.orderCreateData.delivery&&-1==t.support_trade_type.indexOf(e.orderCreateData.delivery.delivery_type)?a("v-uni-view",{staticClass:"error-tips"},[a("v-uni-text",{staticClass:"iconfont icon-gantanhao"}),a("v-uni-text",[e._v("该商品不支持"+e._s(e.orderCreateData.delivery.delivery_type_name))])],1):e._e()],1),a("v-uni-view",{staticClass:"goods-sub-section"},[a("v-uni-view",[a("v-uni-text",{staticClass:"unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"goods-price"},[e._v(e._s(parseFloat(t.price).toFixed(2).split(".")[0]))]),a("v-uni-text",{staticClass:"unit"},[e._v("."+e._s(parseFloat(t.price).toFixed(2).split(".")[1]))])],1),a("v-uni-view",[a("v-uni-text",{staticClass:"font-size-tag"},[e._v("x")]),a("v-uni-text",{staticClass:"font-size-base"},[e._v(e._s(t.num))])],1)],1)],1)],1)],1)})),1)],1),a("v-uni-view",{staticClass:"site-wrap buyer-message"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("买家留言")]),a("v-uni-view",{staticClass:"box text-overflow ",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("buyerMessagePopup")}}},[e.orderCreateData.buyer_message?a("v-uni-text",[e._v(e._s(e.orderCreateData.buyer_message))]):a("v-uni-text",{staticClass:"color-sub"},[e._v("无留言")])],1),a("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1),1==e.orderPaymentData.invoice.invoice_status?a("v-uni-view",{staticClass:"site-wrap"},[a("v-uni-view",{staticClass:"site-footer"},[a("v-uni-view",{staticClass:"order-cell order-invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票")]),a("v-uni-view",{staticClass:"box text-overflow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("invoicePopup")}}},[1==e.orderCreateData.is_invoice?a("v-uni-text",[e._v(e._s(1==e.orderCreateData.invoice_type?"纸质":"电子")+"发票("+e._s(e.orderCreateData.invoice_content)+")")]):a("v-uni-text",[e._v("无需发票")])],1),a("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1)],1):e._e(),a("v-uni-view",{staticClass:"order-money"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("商品金额")]),a("v-uni-view",{staticClass:"box color-title"},[a("v-uni-text",{staticClass:"unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money"},[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.goods_money)))])],1)],1),e.presaleDiscount>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("定金膨胀")]),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("-")]),a("v-uni-text",{staticClass:"unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money"},[e._v(e._s(e.presaleDiscount))])],1)],1):e._e(),0==e.orderPaymentData.is_virtual&&e.orderPaymentData.delivery_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("运费")]),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("+")]),a("v-uni-text",{staticClass:"unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money"},[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.delivery_money)))])],1)],1):e._e(),e.orderCreateData.is_invoice&&e.orderPaymentData.invoice_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[a("v-uni-text",[e._v("税费")]),a("v-uni-text",{staticClass:"color-base-text font-bold"},[e._v("("+e._s(e.orderPaymentData.invoice.invoice_rate)+"%)")])],1),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("+")]),a("v-uni-text",{staticClass:"unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money"},[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.invoice_money)))])],1)],1):e._e(),e.orderCreateData.is_invoice&&e.orderPaymentData.invoice_delivery_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票邮寄费")]),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("+")]),a("v-uni-text",{staticClass:"unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money"},[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.invoice_delivery_money)))])],1)],1):e._e(),e.orderPaymentData.promotion_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("优惠")]),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("-")]),a("v-uni-text",{staticClass:"unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money"},[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.promotion_money)))])],1)],1):e._e()],1),a("v-uni-view",{staticClass:"order-money"},[0==e.orderPaymentData.is_virtual&&e.orderPaymentData.presale_info.presale_deposit>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit color-base-text"},[e._v("阶段一：付定金")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"ns-text-color-black color-base-text"},[a("v-uni-text",{staticClass:"font-size-tag"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"font-size-base"},[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.presale_deposit_money)))])],1)],1)],1):e._e(),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("阶段二：付尾款")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",[a("v-uni-text",{staticClass:"font-size-tag"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"font-size-base"},[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.final_money)))])],1)],1)],1)],1),1==e.is_deposit_back?[a("v-uni-view",{staticClass:"pre-sale-money"},[a("v-uni-view",[e._v("我已同意定金不退等预售协议"),a("v-uni-text",{staticClass:"iconfont icon-bangzhu1",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.presaleAgreement.apply(void 0,arguments)}}})],1),a("v-uni-switch",{staticClass:"balance-switch",attrs:{checked:e.switch_state},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.switchChange.apply(void 0,arguments)}}})],1)]:e._e(),e.orderPaymentData.delivery&&"local"==e.orderPaymentData.delivery.delivery_type&&e.orderPaymentData.delivery&&e.orderPaymentData.delivery.error&&""!==e.orderPaymentData.delivery.error?a("v-uni-view",{staticClass:"error-message"},[e._v(e._s(e.orderPaymentData.delivery.error_msg))]):e._e(),a("v-uni-view",{staticClass:"order-submit",class:{"bottom-safe-area":e.isIphoneX}},[a("v-uni-view",{staticClass:"order-settlement-info"},[a("v-uni-text",{staticClass:"font-size-base color-tip margin-right"},[e._v("共"+e._s(e.orderPaymentData.goods_num)+"件")]),a("v-uni-text",{staticClass:"font-size-base"},[e._v("定金：")]),a("v-uni-text",{staticClass:"color-base-text unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"color-base-text money"},[e._v(e._s(e._f("moneyFormat")(e.orderPaymentData.pay_money)))])],1),a("v-uni-view",{staticClass:"submit-btn"},[e.createBtn()?a("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openChoosePayment()}}},[e._v("提交订单")]):a("v-uni-button",{staticClass:"no-submit mini",attrs:{size:"mini"}},[e.orderPaymentData.delivery&&"local"==e.orderPaymentData.delivery.delivery_type&&e.orderPaymentData.delivery&&e.orderPaymentData.delivery.error&&e.orderPaymentData.delivery.start_money>e.orderPaymentData.presale_deposit_money?[e._v("差"+e._s(e._f("moneyFormat")(e.orderPaymentData.delivery.start_money-e.orderPaymentData.presale_deposit_money))+"起送")]:[e._v("提交订单")]],2)],1)],1),a("div",{staticClass:"order-submit-block"})],2),a("uni-popup",{ref:"depositRefund",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"depositRefund-wrap"},[a("v-uni-view",{staticClass:"depositRefund-con"},[e._v("预售商品定金不支持退款,同意后可继续下单。")]),a("v-uni-view",{staticClass:"popup-footer"},[a("v-uni-view",{staticClass:"confirm-btn color-tip",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closeDepositRefund.apply(void 0,arguments)}}},[e._v("我再想想")]),a("v-uni-view",{staticClass:"confirm-btn color-base-bg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toPayOrder.apply(void 0,arguments)}}},[e._v("同意并下单")])],1)],1)],1),a("uni-popup",{ref:"presaleAgreement",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"agreement-wrap"},[a("v-uni-view",{staticClass:"agreement-title font-size-toolbar"},[e._v("定金不退协议")]),a("v-uni-view",{staticClass:"agreement-con"},[e.orderPaymentData.presale_info?[e._v(e._s(e.orderPaymentData.presale_info.deposit_agreement))]:[a("v-uni-text",{staticClass:"color-tip"},[e._v("暂无退定金协议")])]],2),a("v-uni-view",{staticClass:"popup-footer"},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePresaleAgreement.apply(void 0,arguments)}}},[e._v("确定")])],1)],1)],1),a("uni-popup",{ref:"buyerMessagePopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"buyermessag-popup popup",staticStyle:{height:"auto"},on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("买家留言")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("buyerMessagePopup")}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[a("v-uni-view",[a("v-uni-view",{staticClass:"buyermessag-cell"},[a("v-uni-view",{staticClass:"buyermessag-form-group"},[a("v-uni-textarea",{attrs:{type:"text",maxlength:"100",placeholder:"留言前建议先与商家协调一致","placeholder-class":"color-tip"},model:{value:e.orderCreateData.buyer_message,callback:function(t){e.$set(e.orderCreateData,"buyer_message",t)},expression:"orderCreateData.buyer_message"}})],1)],1)],1)],1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveBuyerMessage.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg"},[e._v("确定")])],1)],1)],1),a("uni-popup",{ref:"invoicePopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"invoice-popup popup",style:1==e.orderCreateData.is_invoice?"height: 83vh;":"height: 48vh;",on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closeInvoicePopup()}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[a("v-uni-view",[e.orderPaymentData.invoice?a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("需要发票")]),a("v-uni-view",{staticClass:"option-grpup"},[a("v-uni-view",{staticClass:"option-item",class:{"color-base-bg active":0==e.orderCreateData.is_invoice},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeIsInvoice.apply(void 0,arguments)}}},[e._v("不需要")]),a("v-uni-view",{staticClass:"option-item",class:{"color-base-bg active":1==e.orderCreateData.is_invoice},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeIsInvoice.apply(void 0,arguments)}}},[e._v("需要")])],1)],1):e._e(),1==e.orderCreateData.is_invoice?[a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票类型")]),a("v-uni-view",{staticClass:"option-grpup"},e._l(e.orderPaymentData.invoice.invoice_type,(function(t,o){return a("v-uni-view",{key:o,staticClass:"option-item",class:{"color-base-bg active":e.orderCreateData.invoice_type==t},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.changeInvoiceType(t)}}},[e._v(e._s(1==t?"纸质":"电子"))])})),1)],1),a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("抬头类型")]),a("v-uni-view",{staticClass:"option-grpup"},[a("v-uni-view",{staticClass:"option-item",class:{"color-base-bg active":1==e.orderCreateData.invoice_title_type},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeInvoiceTitleType(1)}}},[e._v("个人")]),a("v-uni-view",{staticClass:"option-item",class:{"color-base-bg active":2==e.orderCreateData.invoice_title_type},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeInvoiceTitleType(2)}}},[e._v("企业")])],1)],1),a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票信息")]),a("v-uni-view",{staticClass:"invoice-form-group"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请填写抬头名称"},model:{value:e.orderCreateData.invoice_title,callback:function(t){e.$set(e.orderCreateData,"invoice_title","string"===typeof t?t.trim():t)},expression:"orderCreateData.invoice_title"}}),2==e.orderCreateData.invoice_title_type?a("v-uni-input",{attrs:{type:"text",placeholder:"请填写纳税人识别号"},model:{value:e.orderCreateData.taxpayer_number,callback:function(t){e.$set(e.orderCreateData,"taxpayer_number","string"===typeof t?t.trim():t)},expression:"orderCreateData.taxpayer_number"}}):e._e(),1==e.orderCreateData.invoice_type?a("v-uni-input",{attrs:{type:"text",placeholder:"请填写邮寄地址"},model:{value:e.orderCreateData.invoice_full_address,callback:function(t){e.$set(e.orderCreateData,"invoice_full_address","string"===typeof t?t.trim():t)},expression:"orderCreateData.invoice_full_address"}}):e._e(),2==e.orderCreateData.invoice_type?a("v-uni-input",{attrs:{type:"text",placeholder:"请填写邮箱"},model:{value:e.orderCreateData.invoice_email,callback:function(t){e.$set(e.orderCreateData,"invoice_email","string"===typeof t?t.trim():t)},expression:"orderCreateData.invoice_email"}}):e._e()],1)],1),a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票内容")]),a("v-uni-view",{staticClass:"option-grpup"},e._l(e.orderPaymentData.invoice.invoice_content_array,(function(t,o){return a("v-uni-view",{key:o,staticClass:"option-item content",class:{"color-base-bg active":t==e.orderCreateData.invoice_content},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.changeInvoiceContent(t)}}},[e._v(e._s(t))])})),1)],1)]:e._e(),a("v-uni-view",{staticClass:"invoice-tops"},[e._v("发票内容将以根据税法调整，具体请以展示为准，发票内容显示详细商品名 称及价格信息")])],2)],1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveInvoice.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg"},[e._v("确定")])],1)],1)],1),a("uni-popup",{ref:"deliveryPopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"delivery-popup popup"},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("已为您甄选出附近所有相关门店")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("deliveryPopup")}}})],1),a("v-uni-view",{staticClass:"popup-body store-popup",class:{"safe-area":e.isIphoneX}},[a("v-uni-view",{staticClass:"delivery-content"},[e._l(e.storeInfo.storeList,(function(t,o){return a("v-uni-view",{key:o,staticClass:"item-wrap",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectPickupPoint(t)}}},[a("v-uni-view",{staticClass:"detail"},[a("v-uni-view",{staticClass:"name",class:t.store_id==e.orderPaymentData.delivery.store_id?"color-base-text":""},[a("v-uni-text",[e._v(e._s(t.store_name))]),t.distance?a("v-uni-text",[e._v("("+e._s(t.distance)+"km)")]):e._e()],1),a("v-uni-view",{staticClass:"info"},[a("v-uni-view",{staticClass:"font-size-goods-tag",class:t.store_id==e.orderPaymentData.delivery.store_id?"color-base-text":""},[e._v("营业时间："+e._s(t.open_date))]),a("v-uni-view",{staticClass:"font-size-goods-tag",class:t.store_id==e.orderPaymentData.delivery.store_id?"color-base-text":""},[e._v("地址："+e._s(t.full_address)+e._s(t.address))])],1)],1),t.store_id==e.orderPaymentData.delivery.store_id?a("v-uni-view",{staticClass:"icon"},[a("v-uni-text",{staticClass:"iconfont icon-yuan_checked color-base-text"})],1):e._e()],1)})),e.storeInfo.storeList?e._e():a("v-uni-view",{staticClass:"empty"},[e._v("所选择收货地址附近没有可以自提的门店")])],2)],1)],1)],1),a("ns-payment",{ref:"choosePaymentPopup",attrs:{isBalance:e.orderCreateData.is_balance,isPayPassWord:e.orderPaymentData.member_account.is_pay_password,balanceDeduct:e.orderPaymentData.order_money>0&&e.orderPaymentData.member_account.balance_total>0?e.balanceDeduct:"0",payMoney:e.orderPaymentData.pay_money},on:{useBalance:function(t){arguments[0]=t=e.$handleEvent(t),e.useBalance.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.orderCreate.apply(void 0,arguments)}}}),a("loading-cover",{ref:"loadingCover"})],1)],1)},r=[]},cc1b:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},i={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(e){return-1!==["dark","light"].indexOf(e)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var e=this,t=getCurrentPages()[0];this.$pageVm=t.$vm||t,uni.onWindowResize((function(t){e.$emit("resize",t)})),this.$pageVm.$on("hook:onPageScroll",(function(t){e.$emit("scroll",t)})),this.$watch("backgroundTextStyle",(function(){e.setBackgroundTextStyle()})),this.$watch((function(){return[e.rootFontSize,e.pageStyle]}),(function(){e.setPageMeta()})),this.$watch((function(){return[e.backgroundColor,e.backgroundColorTop,e.backgroundColorBottom]}),(function(){e.setBackgroundColor()})),this.$watch((function(){return[e.scrollTop,e.scrollDuration]}),(function(){e.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(e,t){e.setStyle({pullToRefresh:{support:t,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var e=this,t=String(this.scrollTop);if(-1!==t.indexOf("rpx")&&(t=uni.upx2px(t.replace("rpx",""))),t=parseFloat(t),!isNaN(t)){var a=function a(i){i.scrollTop===t&&(e.$pageVm.$off("hook:onPageScroll",a),e.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:t,duration:this.scrollDuration,success:function(){e.$pageVm.$on("hook:onPageScroll",a)}})}}}};t.default=i},ced8:function(e,t,a){var o=a("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-edad5242] uni-input,[data-v-edad5242] uni-view{font-size:%?24?%}.font-bold[data-v-edad5242]{font-weight:700}.order-container[data-v-edad5242]{width:100vw;height:100vh;display:flex;flex-direction:column;background:linear-gradient(180deg,var(--base-color) 10%,#f8f8f8);background-size:100% %?260?%;background-repeat:no-repeat}.order-container .order-scroll-container[data-v-edad5242]{width:100%;height:0;flex:1}.order-container .payment-navbar-block[data-v-edad5242]{height:%?60?%}.payment-navbar[data-v-edad5242]{width:100vw;padding-bottom:%?20?%;position:fixed;left:0;top:0;z-index:100;background:linear-gradient(180deg,var(--base-color) 10%,#f8f8f8);background-size:100% %?260?%}.payment-navbar .nav-wrap[data-v-edad5242]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;box-sizing:border-box;position:relative}.payment-navbar .navbar-title[data-v-edad5242]{color:#fff;font-size:%?32?%}.payment-navbar .icon-back_light[data-v-edad5242]{color:#fff;position:absolute;left:%?24?%;font-size:%?40?%}.payment-navbar-block[data-v-edad5242]{padding-bottom:%?20?%}.mobile-wrap[data-v-edad5242]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%}.mobile-wrap .tips[data-v-edad5242]{font-size:%?22?%;margin-bottom:%?30?%;background:var(--main-color-shallow);border-radius:%?10?%;padding:%?20?% %?30?%;line-height:1;display:flex;align-items:center}.mobile-wrap .tips .iconfont[data-v-edad5242]{margin-right:%?5?%}.mobile-wrap.local-mobile[data-v-edad5242]{border-bottom:%?2?% solid #f4f4f6;margin:0}.mobile-wrap.store-mobile[data-v-edad5242]{border-top:%?2?% solid #f4f4f6;margin:%?20?% 0 0 0;padding:%?20?% 0;border-radius:0}.mobile-wrap .form-group[data-v-edad5242]{display:flex;align-items:center;width:100%}.mobile-wrap .form-group .iconfont[data-v-edad5242]{margin-right:%?26?%;font-size:%?32?%}.mobile-wrap .form-group .text[data-v-edad5242]{display:inline-block;line-height:%?50?%;padding-right:%?10?%;font-size:%?28?%;font-weight:700}.mobile-wrap .form-group .placeholder[data-v-edad5242]{line-height:%?50?%}.mobile-wrap .form-group .input[data-v-edad5242]{flex:1;height:%?50?%;line-height:%?50?%;text-align:right;font-size:%?28?%}.order-cell[data-v-edad5242]{display:flex;margin:0 0 %?30?% 0;align-items:center;background:#fff;line-height:%?40?%;position:relative}.order-cell.clear-flex[data-v-edad5242]{display:block}.order-cell.textarea-box[data-v-edad5242]{display:flex;align-items:baseline;font-size:%?28?%}.order-cell uni-text[data-v-edad5242]{font-size:%?28?%}.order-cell .tit[data-v-edad5242]{text-align:left;font-size:%?28?%;min-width:%?160?%;color:#000;font-weight:700}.order-cell .tit uni-text[data-v-edad5242]{font-size:%?28?%}.order-cell .tit .tit-content[data-v-edad5242]{max-width:%?540?%;font-size:%?24?%;line-height:%?35?%;margin-bottom:%?10?%}.order-cell .box[data-v-edad5242]{flex:1;padding:0 %?10?%;line-height:inherit;text-align:right}.order-cell .box.text-overflow[data-v-edad5242]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.order-cell .box.text-overflow .money[data-v-edad5242]{overflow:hidden;max-width:40%;display:inline-block;text-overflow:ellipsis;vertical-align:top}.order-cell .box .icon-right[data-v-edad5242]{color:#303133;margin-left:%?20?%}.order-cell .box .operator[data-v-edad5242]{font-size:%?24?%;margin-right:%?6?%;font-weight:700;color:var(--price-color)}.order-cell .box uni-textarea[data-v-edad5242]{width:auto;height:%?88?%;font-size:%?28?%}.order-cell .iconfont[data-v-edad5242]{color:#909399;line-height:normal;font-size:%?24?%}.order-cell .unit[data-v-edad5242]{margin-right:%?4?%;font-weight:700;font-size:%?28?%!important;margin-left:%?4?%;color:var(--price-color)}.order-cell .money[data-v-edad5242]{font-size:%?28?%!important;font-weight:700;color:var(--price-color)}.site-wrap[data-v-edad5242]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;padding:%?40?% 0}.site-wrap.order-goods[data-v-edad5242]{padding:0}.site-wrap .site-body[data-v-edad5242]{margin:0 %?24?%}.site-wrap .site-body .goods-item[data-v-edad5242]{border-bottom:%?2?% solid #f4f4f6}.site-wrap .site-body .goods-item[data-v-edad5242]:last-child{border-bottom:0}.site-wrap .site-body .goods-item .error-tips[data-v-edad5242]{color:#ff443f;padding:%?10?% %?20?%;display:inline-flex;align-items:center;line-height:1;background:#ffecec;margin-top:%?20?%;border-radius:%?12?%;width:auto}.site-wrap .site-body .goods-item .error-tips .iconfont[data-v-edad5242]{margin-right:%?10?%}.site-wrap .site-body .goods-wrap[data-v-edad5242]{display:flex;position:relative;padding:%?30?% 0}.site-wrap .site-body .goods-wrap .goods-img[data-v-edad5242]{width:%?180?%;height:%?180?%;margin-right:%?20?%;border-radius:%?10?%;overflow:hidden}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-edad5242]{width:100%;height:100%;border-radius:%?10?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-edad5242]{flex:1;position:relative;width:0;margin-top:%?-4?%;display:flex;flex-direction:column;justify-content:space-between}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-edad5242]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;font-weight:700}.site-wrap .site-body .goods-wrap .goods-info .sku[data-v-edad5242]{display:flex;line-height:1;margin-top:%?8?%}.site-wrap .site-body .goods-wrap .goods-info .sku .goods-spec[data-v-edad5242]{color:#909399;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1;display:flex}.site-wrap .site-body .goods-wrap .goods-info .sku .goods-spec uni-view[data-v-edad5242]{background-color:#f4f4f4;color:#666;padding:%?6?% %?10?%;margin-right:%?12?%;line-height:1}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-edad5242]{font-size:%?24?%;margin-right:%?4?%;font-weight:700;color:var(--price-color)}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-edad5242]{font-weight:700;font-size:%?32?%;color:var(--price-color)}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-edad5242]:first-of-type{width:80%;overflow:hidden;text-overflow:ellipsis}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-edad5242]:last-of-type{text-align:right;position:absolute;right:0;bottom:0;font-weight:700}.site-wrap .site-footer[data-v-edad5242]{margin:0 %?24?% 0}.site-wrap .site-footer .order-cell[data-v-edad5242]:last-of-type{margin-bottom:0}[data-v-edad5242] .goods-form{display:flex;align-items:center;position:relative}[data-v-edad5242] .goods-form ns-form{display:flex;width:100%}[data-v-edad5242] .goods-form .shade{position:absolute;left:0;top:0;width:100%;height:100%;z-index:5}[data-v-edad5242] .goods-form .cell-more{margin-left:%?50?%;float:right;color:#909399;font-size:%?24?%}[data-v-edad5242] .goods-form .form-wrap{flex:1;width:0}[data-v-edad5242] .goods-form .form-wrap .icon-right{display:none}[data-v-edad5242] .goods-form .form-wrap > uni-view,[data-v-edad5242] .goods-form .form-wrap > uni-picker{display:none}[data-v-edad5242] .goods-form .form-wrap > uni-view:first-child,[data-v-edad5242] .goods-form .form-wrap > uni-picker:first-child{display:block;border-bottom:none}[data-v-edad5242] .goods-form .form-wrap > uni-view:first-child .required,[data-v-edad5242] .goods-form .form-wrap > uni-picker:first-child .required{display:none}[data-v-edad5242] .goods-form .order-cell .name{width:auto}[data-v-edad5242] .goods-form .order-cell .tit{font-weight:700}[data-v-edad5242] .goods-form .order-cell .tit:after{content:"："}.member-goods-card[data-v-edad5242]{margin-bottom:0;padding-bottom:%?30?%}.member-goods-card .text[data-v-edad5242]{margin-right:%?10?%;color:#999}.member-goods-card .price-font[data-v-edad5242]{color:var(--price-color)}.order-money[data-v-edad5242]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%}.order-money .order-cell[data-v-edad5242]:last-child{margin-bottom:0}.error-message[data-v-edad5242]{position:fixed;z-index:5;left:0;bottom:%?100?%;width:100vw;background:#f6f6cb;text-align:left;padding:%?10?% %?20?%;color:red}.order-submit[data-v-edad5242]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?100?%;background:#fff;text-align:right;display:flex;align-items:center;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.order-submit .order-settlement-info[data-v-edad5242]{flex:1;height:%?100?%;line-height:%?100?%;display:flex;padding-left:%?30?%;align-items:baseline}.order-submit .order-settlement-info .unit[data-v-edad5242]{font-weight:700;font-size:%?24?%;margin-right:%?4?%;color:var(--price-color)}.order-submit .order-settlement-info .money[data-v-edad5242]{font-weight:700;font-size:%?32?%;color:var(--price-color)}.order-submit .submit-btn[data-v-edad5242]{height:%?80?%;margin:0 %?30?%;display:flex;justify-content:center;align-items:center}.order-submit .submit-btn uni-button[data-v-edad5242]{line-height:%?70?%;width:%?180?%;height:%?70?%;padding:0;font-size:%?28?%;font-weight:700}.order-submit .submit-btn .no-submit[data-v-edad5242]{width:unset;background-color:#ccc;color:#fff;padding:0 %?20?%;font-size:%?28?%}.order-submit-block[data-v-edad5242]{height:%?120?%;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.popup[data-v-edad5242]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-edad5242]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-edad5242]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-edad5242]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-edad5242]{height:calc(100% - %?250?%)}.popup .popup-body.store-popup[data-v-edad5242]{height:calc(100% - %?120?%)}.popup .popup-body.safe-area[data-v-edad5242]{height:calc(100% - %?270?%)}.popup .popup-body.store-popup.safe-area[data-v-edad5242]{height:calc(100% - %?140?%)}.popup .popup-footer[data-v-edad5242]{height:%?120?%}.popup .popup-footer .confirm-btn[data-v-edad5242]{height:%?80?%;line-height:%?80?%;color:#fff;text-align:center;margin:%?20?% %?32?% %?40?%;border-radius:%?10?%;font-size:%?28?%}.popup .popup-footer .confirm-btn.color-base-bg[data-v-edad5242]{color:var(--btn-text-color)}.popup .popup-footer.bottom-safe-area[data-v-edad5242]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.invoice-popup[data-v-edad5242]{height:83vh;padding:%?18?% 0;box-sizing:border-box;position:relative}.invoice-popup .invoice-close[data-v-edad5242]{position:absolute;line-height:1;top:%?48?%;right:%?48?%;font-size:%?32?%;z-index:9}.invoice-popup .popup-body .invoice-cell[data-v-edad5242]{padding:%?30?% 0;border-top:%?2?% solid #eee;margin:0 %?48?%}.invoice-popup .popup-body .invoice-cell[data-v-edad5242]:first-of-type{border-top:none}.invoice-popup .popup-body .invoice-cell .tit[data-v-edad5242]{font-size:%?28?%}.invoice-popup .popup-body .invoice-cell .option-grpup[data-v-edad5242]{padding-top:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-edad5242]{height:%?54?%;line-height:%?54?%;display:inline-block;font-size:%?22?%;padding:0 %?36?%;background:#f8f8f8;border:%?2?% solid #eee;border-radius:%?10?%;margin-right:%?30?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.active[data-v-edad5242]{color:var(--btn-text-color)}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.content[data-v-edad5242]{margin-bottom:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.content[data-v-edad5242]:last-child{margin-bottom:0}.invoice-popup .popup-body .invoice-cell .invoice-form-group uni-input[data-v-edad5242]{background:#f8f8f8;border-radius:%?10?%;height:%?66?%;margin-top:%?22?%;padding:0 %?32?%;font-size:%?24?%}.invoice-popup .popup-body .invoice-tops[data-v-edad5242]{font-size:%?20?%;margin:0 %?48?%;color:#909399}.buyermessag-popup[data-v-edad5242]{box-sizing:border-box;position:relative}.buyermessag-popup .buyermessag-close[data-v-edad5242]{position:absolute;line-height:1;top:%?48?%;right:%?48?%;font-size:%?32?%;z-index:9}.buyermessag-popup .popup-body .buyermessag-cell[data-v-edad5242]{padding:%?30?% 0;border-top:%?2?% solid #eee;margin:0 %?32?%}.buyermessag-popup .popup-body .buyermessag-cell[data-v-edad5242]:first-of-type{border-top:none}.buyermessag-popup .popup-body .buyermessag-cell .buyermessag-form-group uni-textarea[data-v-edad5242]{display:flex;align-items:baseline;font-size:%?28?%;width:100%;background-color:#f8f8f8;padding:%?20?%;box-sizing:border-box;border-radius:%?10?%}.coupon-popup[data-v-edad5242]{height:65vh}.coupon-popup .popup-body[data-v-edad5242]{background:#fff}.coupon-popup .coupon-empty[data-v-edad5242]{display:flex;align-items:center;justify-content:center;height:100%;color:#909399!important}.coupon-popup .coupon-item[data-v-edad5242]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;margin:%?20?% %?32?% 0;padding:0;position:relative;background-color:#fff2f0}.coupon-popup .coupon-item[data-v-edad5242]:before, .coupon-popup .coupon-item[data-v-edad5242]:after{position:absolute;content:"";background-color:#fff;top:50%;width:%?30?%;height:%?30?%;border-radius:50%;z-index:5}.coupon-popup .coupon-item[data-v-edad5242]:before{left:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.coupon-popup .coupon-item[data-v-edad5242]:after{right:0;-webkit-transform:translate(50%,-50%);transform:translate(50%,-50%)}.coupon-popup .coupon-item .coupon-info[data-v-edad5242]{height:%?190?%;display:flex;width:100%;position:relative}.coupon-popup .coupon-item .coupon-info .info-wrap[data-v-edad5242]{width:%?220?%;height:%?190?%;display:flex;justify-content:center;align-items:center;margin-right:%?20?%;background-repeat:no-repeat;background-size:100% 100%;position:relative;background:linear-gradient(270deg,var(--bg-color),var(--bg-color-shallow))}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-line[data-v-edad5242]{position:absolute;right:0;top:0;height:100%}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money[data-v-edad5242]{color:#fff;text-align:center;line-height:1}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money .unit[data-v-edad5242]{font-size:%?30?%}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money .money[data-v-edad5242]{font-size:%?60?%}.coupon-popup .coupon-item .coupon-info .info-wrap .at-least[data-v-edad5242]{font-size:%?24?%;color:#fff;text-align:center;margin-top:%?20?%}.coupon-popup .coupon-item .coupon-info .desc-wrap[data-v-edad5242]{flex:1;max-width:calc(100% - %?360?%)}.coupon-popup .coupon-item .coupon-info .desc-wrap uni-view[data-v-edad5242]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.coupon-popup .coupon-item .coupon-info .desc-wrap .coupon-name[data-v-edad5242]{margin-top:%?10?%;margin-bottom:%?4?%;font-size:%?28?%}.coupon-popup .coupon-item .coupon-info .desc-wrap .limit[data-v-edad5242]{font-size:%?20?%}.coupon-popup .coupon-item .coupon-info .desc-wrap .time[data-v-edad5242]{border-top:%?2?% dashed #ccc;position:absolute;bottom:%?30?%;color:#909399;padding-top:%?10?%;line-height:1.5;font-size:%?20?%}.coupon-popup .coupon-item .coupon-info .iconfont[data-v-edad5242]{font-size:%?44?%;position:absolute;top:50%;right:%?20?%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.coupon-popup .coupon-item .coupon-info .icon-yuan_checkbox[data-v-edad5242]{color:#909399}.promotion-popup[data-v-edad5242]{height:40vh}.promotion-popup .order-cell[data-v-edad5242]{margin:%?30?% %?30?%}.promotion-popup .order-cell .tit[data-v-edad5242]{width:auto;min-width:unset}.promotion-popup .order-cell .promotion-mark[data-v-edad5242]{padding:%?4?% %?10?%;line-height:1;border-radius:%?10?%;font-size:%?24?%;margin-right:%?10?%;color:var(--main-color);background-color:var(--main-color-shallow)}.delivery-popup[data-v-edad5242]{height:80vh;box-sizing:border-box}.delivery-popup .delivery-content[data-v-edad5242]{height:100%;overflow-y:scroll;padding:%?30?% 0;box-sizing:border-box}.delivery-popup .delivery-content .item-wrap[data-v-edad5242]{padding:%?20?% 0;box-sizing:border-box;border-top:%?2?% solid #eee;display:flex;justify-content:space-between;align-items:center;margin:0 %?48?%}.delivery-popup .delivery-content .item-wrap .detail[data-v-edad5242]{width:90%}.delivery-popup .delivery-content .item-wrap .detail .name[data-v-edad5242]{display:flex}.delivery-popup .delivery-content .item-wrap .detail .name uni-text[data-v-edad5242]{font-size:%?28?%}.delivery-popup .delivery-content .item-wrap .detail .info[data-v-edad5242]{line-height:1.2}.delivery-popup .delivery-content .item-wrap .detail .info uni-view[data-v-edad5242]{font-size:%?24?%}.delivery-popup .delivery-content .item-wrap .detail .info .close-desc[data-v-edad5242]{color:red}.delivery-popup .delivery-content .item-wrap .icon[data-v-edad5242]{flex:1;text-align:right;max-height:%?50?%}.delivery-popup .delivery-content .item-wrap .icon .iconfont[data-v-edad5242]{line-height:1;font-size:%?44?%}.delivery-popup .delivery-content .item-wrap[data-v-edad5242]:first-of-type{padding-top:0;border-top:none}.delivery-popup .delivery-content .empty[data-v-edad5242]{text-align:center;font-size:%?24?%}.balance-switch[data-v-edad5242]{-webkit-transform:scale(.8);transform:scale(.8)}.address-box[data-v-edad5242]{margin:0 %?24?% 0;background-color:#fff;position:relative;overflow:hidden;border-bottom-left-radius:%?16?%;border-bottom-right-radius:%?16?%;padding:%?30?% %?24?%}.address-box.not-delivery-type[data-v-edad5242]{border-radius:%?16?%}.address-box .address-line[data-v-edad5242]{position:absolute;bottom:%?0?%;left:0;width:100%;height:%?6?%}.address-box .info-wrap[data-v-edad5242]{display:flex;align-items:center}.address-box .info-wrap.local[data-v-edad5242]{padding-bottom:%?20?%}.address-box .info-wrap .content[data-v-edad5242]{flex:1}.address-box .info-wrap .content .name[data-v-edad5242]{margin-right:%?10?%;font-weight:700;font-size:%?28?%}.address-box .info-wrap .content .mobile[data-v-edad5242]{font-weight:700;font-size:%?28?%}.address-box .info-wrap .desc-wrap[data-v-edad5242]{word-break:break-word;font-size:%?26?%;color:#666}.address-box .icon-wrap[data-v-edad5242]{width:%?24?%;height:%?42?%;position:relative;margin-right:%?26?%;align-self:flex-start;padding-top:%?6?%}.address-box .icon-wrap.empty[data-v-edad5242]{padding-top:0}.address-box .icon-wrap .iconfont[data-v-edad5242]{font-size:%?32?%;display:inline-block;vertical-align:middle}.address-box .empty-wrap[data-v-edad5242]{height:%?80?%;line-height:%?80?%;display:flex;align-items:center}.address-box .empty-wrap .info[data-v-edad5242]{flex:1;font-size:%?28?%}.address-box .cell-more[data-v-edad5242]{margin-left:%?50?%;float:right;color:#909399;font-size:%?24?%}.address-box .cell-more .iconfont[data-v-edad5242]{color:#909399}.address-box .local-delivery-store[data-v-edad5242]{display:flex;align-items:center;padding-bottom:%?20?%;margin-bottom:%?20?%;border-bottom:%?2?% solid #eee}.address-box .local-delivery-store .info[data-v-edad5242]{flex:1;width:0;font-size:%?28?%}.address-box .local-delivery-store .store-name[data-v-edad5242]{color:var(--base-color);margin:0 %?10?%}.address-box .local-delivery-store .cell-more[data-v-edad5242]{font-size:%?28?%;display:flex;align-items:center}.address-box .local-delivery-store .icon-right[data-v-edad5242]{float:right;color:#909399;font-size:%?24?%}.local-box[data-v-edad5242]{border-top:%?2?% solid #eee}.local-box .order-cell[data-v-edad5242]{padding-top:%?30?%;margin-bottom:0}.local-box .order-cell .box[data-v-edad5242]{padding:0}.local-box .pick-block[data-v-edad5242]{padding-top:%?20?%;display:flex;align-items:center}.local-box .pick-block > uni-view[data-v-edad5242]{flex:1}.local-box .pick-block .title[data-v-edad5242]{font-weight:700}.local-box .pick-block .time-picker[data-v-edad5242]{display:flex;align-items:center;justify-content:flex-end}.local-box .pick-block .time-picker .cell-more[data-v-edad5242]{float:right;margin-left:%?10?%;color:#909399;font-size:%?24?%}.local-box .pick-block .time-picker .cell-more .iconfont[data-v-edad5242]{color:#909399}.local-box .pick-block .time-picker uni-text[data-v-edad5242]{white-space:nowrap}.empty-local[data-v-edad5242]{color:#ff443f}.delivery-mode[data-v-edad5242]{margin:0 %?24?%;overflow:hidden;border-top-left-radius:%?16?%;border-top-right-radius:%?16?%;background-color:var(--base-color)}.delivery-mode .action[data-v-edad5242]{display:flex;background:var(--base-color-light-7)}.delivery-mode .action > uni-view[data-v-edad5242]{flex:1;text-align:center;height:%?76?%;line-height:%?76?%;font-size:%?30?%;color:#000;position:relative}.delivery-mode .action > uni-view:nth-child(2).active[data-v-edad5242], .delivery-mode .action > uni-view:nth-child(3).active[data-v-edad5242]{border-top-left-radius:%?16?%}.delivery-mode .action > uni-view .out-radio[data-v-edad5242]:after, .delivery-mode .action > uni-view .out-radio[data-v-edad5242]:before{position:absolute;content:"";width:%?20?%;height:%?20?%;background-color:#fff;bottom:0;display:none}.delivery-mode .action > uni-view .out-radio[data-v-edad5242]:after{-webkit-transform:translateX(100%);transform:translateX(100%);right:0}.delivery-mode .action > uni-view .out-radio[data-v-edad5242]:before{left:0;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.delivery-mode .action .active[data-v-edad5242]{background:#fff;color:var(--base-color);border-top-right-radius:%?16?%}.delivery-mode .action .active[data-v-edad5242]:after, .delivery-mode .action .active[data-v-edad5242]:before{position:absolute;content:"";width:%?40?%;height:%?40?%;background-color:var(--base-color-light-7);bottom:0;-webkit-transform:translateX(100%);transform:translateX(100%);border-radius:50%;z-index:5}.delivery-mode .action .active[data-v-edad5242]:after{-webkit-transform:translateX(100%);transform:translateX(100%);right:0}.delivery-mode .action .active[data-v-edad5242]:before{left:0;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.delivery-mode .action .active .out-radio[data-v-edad5242]:after, .delivery-mode .action .active .out-radio[data-v-edad5242]:before{display:block}.store-box[data-v-edad5242]{position:relative;padding:%?30?% %?24?%;margin:0 %?24?% 0;background-color:#fff;border-bottom-left-radius:%?16?%;border-bottom-right-radius:%?16?%;overflow:hidden}.store-box.not-delivery-type[data-v-edad5242]{border-radius:%?16?%}.store-box .address-line[data-v-edad5242]{position:absolute;bottom:0;left:0;width:100%;height:%?6?%}.store-box .store-info[data-v-edad5242]{display:flex;align-items:baseline}.store-box .store-info .icon[data-v-edad5242]{position:relative;margin-right:%?12?%;align-self:flex-start;margin-top:%?-2?%}.store-box .store-info .icon.img[data-v-edad5242]{background-color:unset;margin-right:%?8?%;width:%?46?%;height:%?46?%;border-radius:50%;margin-top:%?12?%}.store-box .store-info .icon.img uni-image[data-v-edad5242]{width:100%;height:100%}.store-box .store-info .icon .iconfont[data-v-edad5242]{font-size:%?32?%}.store-box .store-info .store-address-info[data-v-edad5242]{width:100%;display:flex;align-items:center}.store-box .store-info .store-address-info .info-wrap[data-v-edad5242]{flex:1;width:0}.store-box .store-info .store-address-info .info-wrap .title[data-v-edad5242]{margin-bottom:%?10?%;font-size:%?28?%;font-weight:700}.store-box .store-info .store-address-info .info-wrap .title .cell-more[data-v-edad5242]{float:right;margin-left:%?50?%;color:#909399;font-size:%?24?%;font-weight:500}.store-box .store-info .store-address-info .info-wrap .store-detail uni-view[data-v-edad5242]{word-break:break-word;font-size:%?26?%}.store-box .store-info .store-address-info .info-wrap .store-detail .close-desc[data-v-edad5242]{color:red}.store-box .store-info .store-address-info .info-wrap .store-detail .address[data-v-edad5242]{color:#606266;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis}.store-box .store-info .store-address-info .cell-more[data-v-edad5242]{color:#909399}.store-box .empty[data-v-edad5242]{text-align:center}.store-box .store-time[data-v-edad5242]{border-top:%?2?% solid #f4f4f6;display:flex;align-items:center;justify-content:space-between;padding:%?20?% 0 0;box-sizing:border-box}.store-box .store-time uni-view[data-v-edad5242]{font-size:%?28?%}.store-box .store-time .left[data-v-edad5242]{font-weight:700}.store-box .store-time .right[data-v-edad5242]{display:flex;align-items:center;line-height:1;font-size:%?24?%}.store-box .store-time .right .iconfont[data-v-edad5242]{font-size:%?24?%;margin-left:%?14?%;color:#909399}.buyer-message[data-v-edad5242]{padding:%?30?% %?24?%}.buyer-message .order-cell[data-v-edad5242]{margin-bottom:0}.member-card-wrap[data-v-edad5242]{background-color:#fffbf4;padding:0 %?30?%!important}.member-card-wrap .head[data-v-edad5242]{display:flex;align-items:center;height:%?80?%}.member-card-wrap .icon-yuan_checked[data-v-edad5242], .member-card-wrap .icon-yuan_checkbox[data-v-edad5242]{font-size:%?32?%}.member-card-wrap .icon-huiyuan[data-v-edad5242]{margin-right:%?10?%;line-height:1;font-size:%?36?%;background-image:linear-gradient(156deg,#814635,#3a221b);-webkit-background-clip:text;-webkit-text-fill-color:transparent}.member-card-wrap .info[data-v-edad5242]{text-align:left;flex:1;color:#e5ce75;font-size:%?24?%;color:#333}.member-card-wrap .body[data-v-edad5242]{display:flex;overflow-x:scroll;padding:%?10?% 0 %?20?% 0}.member-card-wrap .body .item[data-v-edad5242]{padding:%?20?% 0 %?30?% 0;width:calc((100% - %?60?%) / 4);text-align:center;background:#fff;margin-right:%?20?%;border:%?4?% solid #fff;border-radius:%?10?%;position:relative;overflow:hidden}.member-card-wrap .body .item .icon-icon[data-v-edad5242]{position:absolute;right:0;bottom:0;font-size:%?32?%;display:none;line-height:1}.member-card-wrap .body .item[data-v-edad5242]:last-child{margin-right:0}.member-card-wrap .body .item .title[data-v-edad5242]{margin-top:%?20?%;font-weight:700}.member-card-wrap .body .item .price[data-v-edad5242]{margin-top:%?10?%}.member-card-wrap .body .active .icon-icon[data-v-edad5242]{display:block}.system-form-wrap[data-v-edad5242]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;padding:0;overflow:hidden}.system-form-wrap .order-cell[data-v-edad5242]{padding:%?30?% %?24?%;margin-bottom:0;border-bottom:%?2?% solid #f4f4f6}.system-form-wrap[data-v-edad5242] .form-wrap{margin:0 %?24?%}.system-form-wrap[data-v-edad5242] .form-wrap .icon-right{color:#909399;font-size:%?24?%}.agreement[data-v-edad5242]{margin:%?20?% %?24?% 0}.agreement uni-text[data-v-edad5242]{color:var(--base-color)}.agreement-conten-box[data-v-edad5242]{background:#fff;padding:%?30?% %?30?%}.agreement-conten-box .title[data-v-edad5242]{text-align:center;margin-bottom:%?20?%;font-weight:bolder}.agreement-conten-box .close[data-v-edad5242]{position:absolute;right:%?30?%;top:%?10?%}.agreement-conten-box .con[data-v-edad5242]{height:60vh}.icon[data-v-edad5242]{line-height:1;margin-right:%?14?%;max-height:%?50?%}.icon uni-image[data-v-edad5242]{width:%?38?%;margin:%?-6?% auto;max-height:%?50?%}.form-popup[data-v-edad5242]{height:60vh!important}.form-popup .popup-body[data-v-edad5242]{padding:%?20?% %?30?%;box-sizing:border-box}.member-card-popup[data-v-edad5242]{height:60vh}.member-card-popup .popup-body .card-item[data-v-edad5242]{display:flex;padding:%?30?%;background:var(--base-color-light-9);margin:%?24?% %?20?%;border-radius:%?18?%}.member-card-popup .popup-body .card-item .content[data-v-edad5242]{flex:1;width:0;margin-right:%?30?%}.member-card-popup .popup-body .card-item .content .title[data-v-edad5242]{line-height:%?40?%;font-size:%?28?%;font-weight:600}.member-card-popup .popup-body .card-item .content .info uni-text[data-v-edad5242]{line-height:1;font-size:%?24?%;color:#666;margin-top:%?20?%;margin-right:%?8?%;display:inline-block}.member-card-popup .popup-body .card-item .iconfont[data-v-edad5242]{font-size:%?44?%}.member-card-popup .popup-body .card-item .icon-yuan_checkbox[data-v-edad5242]{color:#909399}',""]),e.exports=t},f48d:function(e,t,a){"use strict";a.r(t);var o=a("cc1b"),i=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);t["default"]=i.a},fcf6:function(e,t,a){"use strict";a.r(t);var o=a("c384"),i=a("30a2");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("629b"),a("3f67");var n=a("828b"),s=Object(n["a"])(i["default"],o["b"],o["c"],!1,null,"edad5242",null,!1,o["a"],void 0);t["default"]=s.exports},fe4a:function(e,t,a){var o=a("0e09");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var i=a("967d").default;i("4bd91112",o,!0,{sourceMap:!1,shadowMode:!1})}}]);