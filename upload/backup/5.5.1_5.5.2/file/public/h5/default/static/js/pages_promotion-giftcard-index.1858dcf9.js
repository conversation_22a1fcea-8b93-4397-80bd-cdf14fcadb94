(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-giftcard-index"],{"015d":function(t,e,i){"use strict";i.r(e);var a=i("0f46"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"06b7":function(t,e,i){"use strict";i.r(e);var a=i("ba49"),n=i("d202");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("1775"),i("a3f2");var r=i("828b"),c=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"105b9368",null,!1,a["a"],void 0);e["default"]=c.exports},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=a},1775:function(t,e,i){"use strict";var a=i("338f"),n=i.n(a);n.a},2407:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.advList.length?i("v-uni-view",{class:["container-box",t.className]},[t.advList.length>1?i("v-uni-swiper",{staticClass:"item-wrap",style:{height:t.swiperHeight+"px"},attrs:{"indicator-dots":t.advList.length>1,"indicator-active-color":"#ffffff",autoplay:!0,interval:3e3,duration:1e3,current:t.currentIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSwiper.apply(void 0,arguments)}}},t._l(t.advList,(function(e,a){return i("v-uni-swiper-item",{key:a,on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.jumppage(e.adv_url)}}},[i("v-uni-view",{staticClass:"image-box"},[i("v-uni-image",{attrs:{src:t.$util.img(e.adv_image),mode:"widthFix",id:"content-wrap"+a}})],1)],1)})),1):i("v-uni-view",{staticClass:"container-box item-wrap"},[i("v-uni-image",{attrs:{src:t.$util.img(t.advList[0]["adv_image"]),mode:"widthFix","lazy-load":"true"},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imageLoad.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumppage(t.advList[0].adv_url)}}})],1)],1):t._e()},n=[]},"243b":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("7e88")),o=(uni.getSystemInfoSync(),{components:{nsAdv:n.default},data:function(){return{cardList:[],tabList:[{link:"/pages_promotion/giftcard/index",text:"首页",path:"public/uniapp/giftcard/icon-index.png",selectedPath:"public/uniapp/giftcard/icon-index-selected.png",selected:!0},{link:"/pages_promotion/giftcard/list",text:"卡包",path:"public/uniapp/giftcard/icon-card.png",selectedPath:"public/uniapp/giftcard/icon-card-selected.png",selected:!1},{link:"/pages_promotion/giftcard/member",text:"我的",path:"public/uniapp/giftcard/icon-member.png",selectedPath:"public/uniapp/giftcard/icon-member-selected.png",selected:!1}]}},computed:{navbarInnerStyle:function(){return""}},onLoad:function(t){t.source_member&&uni.setStorageSync("source_member",t.source_member)},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member")),this.getData()},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},methods:{changeOrder:function(t){this.list=[],this.order=t,this.$refs.mescroll.refresh()},getData:function(){var t=this;this.$api.sendRequest({url:"/giftcard/api/giftcard/giftcardlistbycategory",success:function(e){t.cardList=e.data,t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},toDetail:function(t){if(this.storeToken)this.$util.redirectTo("/pages_promotion/giftcard/detail",{id:t.giftcard_id});else{this.$refs.login.open("/pages_promotion/giftcard/index")}},redirectTo:function(t){this.$util.redirectTo(t)},tabRedirectTo:function(t){this.storeToken?this.$util.redirectTo(t,{},"reLaunch"):this.$refs.login.open(t)}}});e.default=o},"32a2":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.page[data-v-105b9368]{background:#f9fbff;min-height:100vh}.bg[data-v-105b9368]{margin:0;border-radius:0;line-height:1;height:-webkit-fit-content;height:fit-content;background:#f9fbff;width:100%}.bg uni-image[data-v-105b9368]{width:100%}[data-v-105b9368] .fixed{position:relative;top:0}[data-v-105b9368] .empty{margin-top:0!important}.topic-pic[data-v-105b9368]{height:%?300?%}.topic-pic uni-image[data-v-105b9368]{height:%?300?%}.card-box[data-v-105b9368]{padding:%?24?%}.card-box .card-category-title[data-v-105b9368]{display:flex;align-items:center;justify-content:center;font-size:%?28?%;color:#222;padding:0;font-weight:700}.card-box .card-category-title .before-line[data-v-105b9368],\r\n.card-box .card-category-title .after-line[data-v-105b9368]{width:%?30?%;height:%?4?%;margin:0 %?10?%}.card-box .card-list[data-v-105b9368]{margin-top:%?20?%;display:flex;flex-wrap:wrap}.card-box .card-list .card-item[data-v-105b9368]{border-radius:%?10?%;background-color:#fff;margin-right:%?22?%;width:calc((100% - %?22?%) / 2);overflow:hidden;margin-bottom:%?22?%;position:relative}.card-box .card-list .card-item .card-img[data-v-105b9368]{width:100%;height:%?220?%;position:relative;overflow:hidden;border-radius:%?18?%}.card-box .card-list .card-item .card-img uni-image[data-v-105b9368]{width:100%;height:100%}.card-box .card-list .card-item .card-title[data-v-105b9368]{text-align:center;padding:%?8?% 0;font-size:%?26?%}.card-box .card-list .card-item[data-v-105b9368]:nth-child(2n){margin-right:0}.card-box .card-list .card-item .card-label[data-v-105b9368]{position:absolute;line-height:1;padding:%?6?% %?10?%;background-color:#ff2c27;color:#fff;right:0;bottom:0;border-top-left-radius:%?20?%;border-bottom-right-radius:0;font-size:%?28?%;font-weight:700}.card-box .card-list .card-item .card-label-img[data-v-105b9368]{position:absolute;line-height:1;right:0;bottom:%?-4?%;width:%?100?%}.card-box .card-list .card-item .card-label-img uni-image[data-v-105b9368]{width:100%;height:%?100?%}.tab-bar[data-v-105b9368]{background-color:#fff;box-sizing:border-box;position:fixed;left:0;bottom:0;width:100%;z-index:998;display:flex;border-top:%?2?% solid #f5f5f5;padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.tab-bar .tabbar-border[data-v-105b9368]{background-color:hsla(0,0%,100%,.329412);position:absolute;left:0;top:0;width:100%;height:%?2?%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.tab-bar .item[data-v-105b9368]{display:flex;align-items:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;flex:1;flex-direction:column;padding-bottom:%?10?%;box-sizing:border-box}.tab-bar .item .bd[data-v-105b9368]{position:relative;height:%?100?%;flex-direction:column;text-align:center;display:flex;justify-content:center;align-items:center}.tab-bar .item .bd .icon[data-v-105b9368]{position:relative;display:inline-block;margin-top:%?10?%;width:%?40?%;height:%?40?%;font-size:%?40?%}.tab-bar .item .bd .icon uni-image[data-v-105b9368]{width:100%;height:100%;display:block}.tab-bar .item .bd .icon > uni-view[data-v-105b9368]{height:inherit;display:flex;align-items:center}.tab-bar .item .bd .label[data-v-105b9368]{position:relative;text-align:center;font-size:%?24?%;line-height:1;margin-top:%?12?%}.tab-bar-placeholder[data-v-105b9368]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?112?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?112?%)}',""]),t.exports=e},"338f":function(t,e,i){var a=i("32a2");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("832b7e8a",a,!0,{sourceMap:!1,shadowMode:!1})},6102:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47");var a={name:"ns-advert",props:{keyword:{type:String},className:{type:String}},data:function(){return{advList:[],isImage:!1,swiperHeight:150,currentIndex:0}},created:function(){this.getAdvList()},methods:{getAdvList:function(){var t=this;this.$api.sendRequest({url:"/api/adv/detail",data:{keyword:this.keyword},success:function(e){if(0==e.code){var i=e.data.adv_list;for(var a in i)i[a].adv_url&&(i[a].adv_url=JSON.parse(i[a].adv_url));t.advList=e.data.adv_list,t.$nextTick((function(){t.setSwiperHeight()}))}}})},jumppage:function(t){this.$util.diyRedirectTo(t)},imageLoad:function(t){this.isImage=!0},changeSwiper:function(t){var e=this;this.currentIndex=t.detail.current,this.$nextTick((function(){e.setSwiperHeight()}))},setSwiperHeight:function(){var t=this;this.advList.length>1&&setTimeout((function(){var e="#content-wrap"+t.currentIndex,i=uni.createSelectorQuery().in(t);i.select(e).boundingClientRect(),i.exec((function(e){e&&e[0]&&(t.swiperHeight=e[0].height)}))}),10)}}};e.default=a},7854:function(t,e,i){"use strict";i.r(e);var a=i("8ba8"),n=i("f48d");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);var r=i("828b"),c=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=c.exports},"7e88":function(t,e,i){"use strict";i.r(e);var a=i("2407"),n=i("f016");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("a44f");var r=i("828b"),c=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"9caa2b5c",null,!1,a["a"],void 0);e["default"]=c.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"9c75":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".adv[data-v-105b9368]{margin:%?20?% %?20?% %?30?%;border-radius:%?24?%;overflow:hidden}.adv[data-v-105b9368] uni-image{max-height:100%;width:100%;border-radius:%?24?%}",""]),t.exports=e},a3f2:function(t,e,i){"use strict";var a=i("b789"),n=i.n(a);n.a},a44f:function(t,e,i){"use strict";var a=i("d87f"),n=i.n(a);n.a},a725:function(t,e,i){"use strict";var a=i("ac2a"),n=i.n(a);n.a},ac2a:function(t,e,i){var a=i("f714");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("1a69ffc2",a,!0,{sourceMap:!1,shadowMode:!1})},b789:function(t,e,i){var a=i("9c75");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("ad4bd65c",a,!0,{sourceMap:!1,shadowMode:!1})},ba49:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={pageMeta:i("7854").default,nsAdv:i("7e88").default,hoverNav:i("c1f1").default,loadingCover:i("c003").default,nsLogin:i("2910").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"page"},[i("v-uni-view",{staticClass:"adv"},[i("ns-adv",{attrs:{keyword:"NS_GIFTCARD"}})],1),t._l(t.cardList,(function(e,a){return i("v-uni-view",{staticClass:"card-box"},[i("v-uni-view",{staticClass:"card-category-title",style:{color:e.font_color}},[i("v-uni-text",{staticClass:"before-line",style:{backgroundColor:e.font_color}}),i("v-uni-text",[t._v(t._s(e.category_name))]),i("v-uni-text",{staticClass:"after-line",style:{backgroundColor:e.font_color}})],1),i("v-uni-view",{staticClass:"card-list"},t._l(e.giftcard_list,(function(e,a){return i("v-uni-view",{staticClass:"card-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"card-img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.card_cover.split(",")[0]),mode:"aspectFill"}}),"balance"==e.card_right_type?i("v-uni-view",{staticClass:"card-label"},[t._v(t._s(e.balance)+"元储值卡")]):t._e(),"goods"==e.card_right_type?i("v-uni-view",{staticClass:"card-label-img"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/giftcard/giftcard-label.png"),mode:"heightFix"}})],1):t._e()],1),i("v-uni-view",{staticClass:"card-title"},[t._v(t._s(e.card_name))])],1)})),1)],1)})),i("v-uni-view",{staticClass:"tab-bar"},[i("v-uni-view",{staticClass:"tabbar-border"}),t._l(t.tabList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.tabRedirectTo(e.link)}}},[i("v-uni-view",{staticClass:"bd"},[i("v-uni-view",{staticClass:"icon"},[i("v-uni-image",{attrs:{src:t.$util.img(e.selected?e.selectedPath:e.path)}})],1),i("v-uni-view",{staticClass:"label",style:{color:e.selected?t.themeStyle&&t.themeStyle.giftcard.giftcard_promotion_color:""}},[t._v(t._s(e.text))])],1)],1)}))],2),i("hover-nav",{attrs:{need:!0}}),i("loading-cover",{ref:"loadingCover"}),i("v-uni-view",{staticClass:"tab-bar-placeholder"}),i("ns-login",{ref:"login"})],2)],1)},o=[]},c1f1:function(t,e,i){"use strict";i.r(e);var a=i("fa1d"),n=i("015d");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("a725");var r=i("828b"),c=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"c1934e78",null,!1,a["a"],void 0);e["default"]=c.exports},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=n},d202:function(t,e,i){"use strict";i.r(e);var a=i("243b"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},d87f:function(t,e,i){var a=i("d915");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("03d75754",a,!0,{sourceMap:!1,shadowMode:!1})},d915:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-9caa2b5c]{width:100%}.container-box .item-wrap[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-9caa2b5c]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}',""]),t.exports=e},f016:function(t,e,i){"use strict";i.r(e);var a=i("6102"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},f48d:function(t,e,i){"use strict";i.r(e);var a=i("cc1b"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},f714:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},n=[]}}]);