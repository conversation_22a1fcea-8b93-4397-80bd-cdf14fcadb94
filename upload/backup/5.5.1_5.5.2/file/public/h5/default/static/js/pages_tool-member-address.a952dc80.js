(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-address"],{1199:function(t,e,s){var d=s("3c4e");d.__esModule&&(d=d.default),"string"===typeof d&&(d=[[t.i,d,""]]),d.locals&&(t.exports=d.locals);var a=s("967d").default;a("c071c57a",d,!0,{sourceMap:!1,shadowMode:!1})},"196b":function(t,e,s){var d=s("c86c");e=d(!1),e.push([t.i,".address-item[data-v-bdd1dc0c] .uni-switch-wrapper .uni-switch-input{height:%?48?%!important;width:%?88?%!important}.address-item[data-v-bdd1dc0c] .uni-switch-wrapper .uni-switch-input:after{height:%?44?%!important;width:%?44?%!important}.address-item[data-v-bdd1dc0c] .uni-switch-wrapper .uni-switch-input:before{background-color:#ededed!important;height:%?44?%!important;width:%?90?%!important}",""]),t.exports=e},"1b1f":function(t,e,s){"use strict";s("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,s("c223");var d=s("edd0"),a={data:function(){return{addressList:[],back:"",redirect:"redirectTo",isIndex:!1,showEmpty:!1,local:0,localType:1}},onLoad:function(t){t.back?(this.back=t.back,uni.setStorageSync("addressBack",t.back)):uni.getStorageSync("addressBack")&&(this.back=uni.getStorageSync("addressBack"),uni.removeStorageSync("addressBack")),t.redirect&&(this.redirect=t.redirect),t.local&&(this.local=t.local),t.type&&(this.localType=t.type)},onShow:function(){var t=this;uni.removeStorageSync("addressInfo"),this.storeToken?this.$refs.mescroll&&this.$refs.mescroll.refresh():this.$nextTick((function(){t.$refs.login.open("/pages_tool/member/address")}))},methods:{getListData:function(t){var e=this,s=0,d=uni.getStorageSync("delivery");d&&"local"==d.delivery_type&&(s=d.store_id||0),this.showEmpty=!1,this.$api.sendRequest({url:"/api/memberaddress/page",data:{page:t.num,page_size:t.size,type:this.localType,store_id:s},success:function(s){e.showEmpty=!0;var d=[],a=s.message;0==s.code&&s.data?d=s.data.list:e.$util.showToast({title:a}),t.endSuccess(d.length),1==t.num&&(e.addressList=[]),e.addressList=e.addressList.concat(d),e.$forceUpdate(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(s){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},addAddress:function(t,e){var s={type:this.localType};"edit"==t&&(s.id=e),this.back&&(s.back=this.back),this.$util.redirectTo("/pages_tool/member/address_edit",s)},deleteAddress:function(t,e){var s=this;uni.showModal({title:"操作提示",content:"确定要删除该地址吗？",success:function(d){if(d.confirm){if(1==e)return void s.$util.showToast({title:"默认地址，不能删除"});s.$api.sendRequest({url:"/api/memberaddress/delete",data:{id:t},success:function(t){0==t.code?s.$util.showToast({title:"删除成功"}):s.$util.showToast({title:"删除失败"}),s.$refs.mescroll.refresh()},fail:function(t){mescroll.endErr()}})}}})},setDefault:function(t,e){var s=this;1!=e&&this.$api.sendRequest({url:"/api/memberaddress/setdefault",data:{id:t},success:function(t){t.data>0?(s.$refs.mescroll.refresh(),""!=s.back?uni.navigateBack({delta:1}):(s.$refs.loadingCover&&s.$refs.loadingCover.show(),s.addressList=[],s.$refs.mescroll.refresh(),s.$util.showToast({title:"修改默认地址成功"}))):s.$util.showToast({title:t.message})}})},getChooseAddress:function(){var t=this;if(this.$util.isWeiXin()){if("ios"==uni.getSystemInfoSync().platform)var e=uni.getStorageSync("initUrl");else e=location.href;this.$api.sendRequest({url:"/wechat/api/wechat/jssdkconfig",data:{url:e},success:function(e){if(0==e.code){var s=new d.Weixin;s.init(e.data),s.openAddress((function(e){"openAddress:ok"==e.errMsg?t.saveAddress({name:e.userName,mobile:e.telNumber,province:e.provinceName,city:e.cityName,district:e.countryName,address:e.detailInfo,full_address:e.provinceName+"-"+e.cityName+"-"+e.countryName,is_default:1}):t.$util.showToast({title:"openAddress:function not implement"==e.errMsg?"PC端微信不支持一键获取地址":e.errMsg})}))}else t.$util.showToast({title:e.message})}})}},saveAddress:function(t){var e=this;this.$api.sendRequest({url:"/api/memberaddress/addthreeparties",data:t,success:function(t){t.code>=0?""!=e.back?uni.navigateBack({delta:1}):e.$refs.mescroll.refresh():e.$util.showToast({title:t.message})}})}},watch:{storeToken:function(t,e){t&&this.$refs.mescroll.refresh()}}};e.default=a},2288:function(t,e,s){"use strict";var d=s("93e8"),a=s.n(d);a.a},3346:function(t,e,s){"use strict";s.r(e);var d=s("c7d6"),a=s("4b9e");for(var i in a)["default"].indexOf(i)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(i);s("9af4"),s("2288");var o=s("828b"),n=Object(o["a"])(a["default"],d["b"],d["c"],!1,null,"bdd1dc0c",null,!1,d["a"],void 0);e["default"]=n.exports},"3c4e":function(t,e,s){var d=s("c86c");e=d(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-bdd1dc0c] .fixed{position:relative;top:0}.cart-empty[data-v-bdd1dc0c]{padding-top:%?208?%!important}.address-list[data-v-bdd1dc0c]{width:100%;height:100%;padding-bottom:%?40?%}.address-list .local[data-v-bdd1dc0c]{display:flex;align-items:center;margin:%?20?% %?30?%;background-color:#fff;padding:%?30?%;border-radius:%?10?%}.address-list .local uni-text[data-v-bdd1dc0c]{margin-right:%?10?%}.address-list .address-item[data-v-bdd1dc0c]{margin:%?20?% 0 0;display:flex;flex-direction:column;background-color:#fff;padding:%?24?% %?30?%;box-sizing:border-box;border-radius:0}.address-list .address-item .address-item-top[data-v-bdd1dc0c]{display:flex;flex-direction:row;border-bottom:%?1?% solid #eee;justify-content:space-between;align-items:center}.address-list .address-item .address-item-top .address-item-left[data-v-bdd1dc0c]{display:flex;flex-direction:column;width:calc(100% - %?100?%)}.address-list .address-item .address-item-top .address-item-edit[data-v-bdd1dc0c]{color:#999;font-size:%?24?%;width:%?100?%;text-align:right}.address-list .address-item .address-item-top .address-top-info[data-v-bdd1dc0c]{display:flex;justify-content:flex-start;position:relative}.address-list .address-item .address-item-top .address-top-info .address-name[data-v-bdd1dc0c]{color:#333;font-size:%?30?%;font-weight:700}.address-list .address-item .address-item-top .address-top-info .address-tel[data-v-bdd1dc0c]{color:#333;font-size:%?30?%;margin-left:%?26?%;font-weight:700}.address-list .address-item .address-item-top .address-top-info .address-status[data-v-bdd1dc0c]{color:red;font-size:%?28?%;position:absolute;right:0}.address-list .address-item .address-item-top .address-info[data-v-bdd1dc0c]{font-size:%?26?%;color:#888;margin-top:%?10?%;margin-bottom:%?28?%}.address-list .address-item .address-item-bottom[data-v-bdd1dc0c]{display:flex;justify-content:space-between;padding-top:%?24?%}.address-list .address-item .address-item-bottom .address-default[data-v-bdd1dc0c]{display:flex;align-items:center;font-size:%?24?%;line-height:1;color:#666}.address-list .address-item .address-item-bottom .address-default .iconfont[data-v-bdd1dc0c]{line-height:1}.address-list .address-item .address-item-bottom .address-btn[data-v-bdd1dc0c]{font-size:%?28?%;line-height:1;display:flex;align-items:center}.address-list .address-item .address-item-bottom .address-btn .edit uni-text[data-v-bdd1dc0c]{vertical-align:center;margin-right:%?10?%;font-size:%?30?%}.address-list .address-item .address-item-bottom .address-btn .delete[data-v-bdd1dc0c]{background:#f1f1f1;justify-content:center;align-items:center;border-radius:50%;padding:%?10?%;text-align:center;width:%?48?%;height:%?48?%;box-sizing:border-box}.address-list .address-item .address-item-bottom .address-btn .delete uni-text[data-v-bdd1dc0c]{font-size:%?26?%}.btn-add[data-v-bdd1dc0c]{margin-top:%?60?%;bottom:0;width:100%;background:#fff;position:fixed;padding:0 %?30?%;box-sizing:border-box;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom);z-index:10}.btn-add .add-address[data-v-bdd1dc0c]{height:%?80?%;line-height:%?80?%;border-radius:%?80?%;margin:%?30?% 0 %?30?%;font-size:%?32?%}.btn-add .add-address uni-text[data-v-bdd1dc0c]{margin-right:%?10?%;font-size:%?28?%}.wx-add[data-v-bdd1dc0c]{margin-top:%?30?%;margin-bottom:%?30?%;text-align:center;border-radius:%?80?%;height:%?80?%;line-height:%?80?%;background-color:var(--main-color);border:%?2?% solid var(--main-color);color:#fff;font-size:%?32?%}.empty-box[data-v-bdd1dc0c]{width:100vw;height:100vh;display:flex;flex-direction:column;align-items:center;justify-content:center}.empty-box uni-image[data-v-bdd1dc0c]{width:50%}.empty-box .tips[data-v-bdd1dc0c]{color:#999;font-size:%?26?%}.empty-box .get-address[data-v-bdd1dc0c],\r\n.empty-box .add-address[data-v-bdd1dc0c]{width:50%;height:%?78?%;line-height:%?78?%;border-radius:%?78?%;margin-top:%?50?%;font-size:%?32?%}.empty-box .get-address[data-v-bdd1dc0c]{width:50%;border:%?2?% solid var(--base-color);margin-top:%?20?%;box-sizing:border-box}.mescroll-downwarp + .empty-box[data-v-bdd1dc0c]{height:calc(100vh - %?260?%)}',""]),t.exports=e},"4b9e":function(t,e,s){"use strict";s.r(e);var d=s("1b1f"),a=s.n(d);for(var i in d)["default"].indexOf(i)<0&&function(t){s.d(e,t,(function(){return d[t]}))}(i);e["default"]=a.a},7854:function(t,e,s){"use strict";s.r(e);var d=s("8ba8"),a=s("f48d");for(var i in a)["default"].indexOf(i)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(i);var o=s("828b"),n=Object(o["a"])(a["default"],d["b"],d["c"],!1,null,null,null,!1,d["a"],void 0);e["default"]=n.exports},"8ba8":function(t,e,s){"use strict";s.d(e,"b",(function(){return d})),s.d(e,"c",(function(){return a})),s.d(e,"a",(function(){}));var d=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},"93e8":function(t,e,s){var d=s("196b");d.__esModule&&(d=d.default),"string"===typeof d&&(d=[[t.i,d,""]]),d.locals&&(t.exports=d.locals);var a=s("967d").default;a("559c1f5a",d,!0,{sourceMap:!1,shadowMode:!1})},"9af4":function(t,e,s){"use strict";var d=s("1199"),a=s.n(d);a.a},c7d6:function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"c",(function(){return i})),s.d(e,"a",(function(){return d}));var d={pageMeta:s("7854").default,nsLogin:s("2910").default,loadingCover:s("c003").default},a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("v-uni-view",[s("page-meta",{attrs:{"page-style":t.themeColor}}),s("v-uni-view",[t.storeToken?s("mescroll-uni",{ref:"mescroll",on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getListData.apply(void 0,arguments)}}},[s("template",{attrs:{slot:"list"},slot:"list"},[s("v-uni-view",{staticClass:"address-list"},[0!==t.addressList.length?t._l(t.addressList,(function(e,d){return s("v-uni-view",{key:d,staticClass:"address-item"},[2==t.localType&&e.local_data?s("v-uni-view",{staticClass:"address-item-top"},[s("v-uni-view",{staticClass:"address-item-left"},[s("v-uni-view",{staticClass:"address-top-info"},[s("v-uni-view",{staticClass:"address-name"},[t._v(t._s(e.name))]),s("v-uni-view",{staticClass:"address-tel"},[t._v(t._s(e.mobile))]),2==t.localType&&e.local_data?s("v-uni-view",{staticClass:"address-status"},[t._v(t._s(e.local_data))]):t._e()],1),s("v-uni-view",{staticClass:"address-info"},[t._v(t._s(e.full_address)+t._s(e.address))])],1),s("v-uni-view",{staticClass:"address-item-edit",on:{click:function(s){arguments[0]=s=t.$handleEvent(s),t.addAddress("edit",e.id)}}},[t._v(t._s(t.$lang("modify")))])],1):s("v-uni-view",{staticClass:"address-item-top",on:{click:function(s){arguments[0]=s=t.$handleEvent(s),t.setDefault(e.id)}}},[s("v-uni-view",{staticClass:"address-item-left"},[s("v-uni-view",{staticClass:"address-top-info"},[s("v-uni-view",{staticClass:"address-name"},[t._v(t._s(e.name))]),s("v-uni-view",{staticClass:"address-tel"},[t._v(t._s(e.mobile))])],1),s("v-uni-view",{staticClass:"address-info"},[t._v(t._s(e.full_address)+t._s(e.address))])],1),s("v-uni-view",{staticClass:"address-item-edit",on:{click:function(s){s.stopPropagation(),arguments[0]=s=t.$handleEvent(s),t.addAddress("edit",e.id)}}},[t._v(t._s(t.$lang("modify")))])],1),s("v-uni-view",{staticClass:"address-item-bottom"},[s("v-uni-view",{staticClass:"address-default",on:{click:function(s){arguments[0]=s=t.$handleEvent(s),t.setDefault(e.id,e.is_default)}}},[(2==t.localType&&e.local_data,s("v-uni-text",{staticClass:"default"},[t._v("设为默认地址")])),1==e.is_default?s("v-uni-switch",{staticStyle:{transform:"scale(0.7)"},attrs:{checked:!0,disabled:!0,color:t.themeStyle.main_color}}):s("v-uni-switch",{staticStyle:{transform:"scale(0.7)"},attrs:{color:t.themeStyle.main_color}})],1),s("v-uni-view",{staticClass:"address-btn"},[1!=e.is_default?s("v-uni-text",{staticClass:"delete",on:{click:function(s){arguments[0]=s=t.$handleEvent(s),t.deleteAddress(e.id,e.is_default)}}},[s("v-uni-text",{staticClass:"iconfont icon-icon7"})],1):t._e()],1)],1)],1)})):t._e(),0==t.addressList.length&&t.showEmpty?s("v-uni-view",{staticClass:"empty-box"},[s("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/member/address/empty.png"),mode:"widthFix"}}),s("v-uni-view",{staticClass:"tips"},[t._v("暂无收货地址，请添加")]),s("v-uni-button",{staticClass:"add-address",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addAddress("add")}}},[t._v(t._s(t.$lang("newAddAddress")))]),t.$util.isWeiXin()&&1!=t.local?s("v-uni-button",{staticClass:"get-address",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getChooseAddress()}}},[t._v(t._s(t.$lang("getAddress")))]):t._e()],1):t._e()],2)],1)],2):t._e(),0!==t.addressList.length?s("v-uni-view",{staticClass:"btn-add"},[t.$util.isWeiXin()&&1!=t.local?s("v-uni-button",{staticClass:"add-address",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getChooseAddress()}}},[t._v(t._s(t.$lang("getAddress")))]):t._e(),s("v-uni-button",{staticClass:"add-address",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addAddress("add")}}},[s("v-uni-text",{staticClass:"iconfont icon-add1"}),t._v(t._s(t.$lang("newAddAddress")))],1)],1):t._e(),s("ns-login",{ref:"login"}),s("loading-cover",{ref:"loadingCover"})],1)],1)},i=[]},cc1b:function(t,e,s){"use strict";s("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,s("5ef2"),s("64aa"),s("5c47"),s("a1c1"),s("e838");var d={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var s=function s(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",s),t.$emit("scrolldone",d))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",s)}})}}}};e.default=a},f48d:function(t,e,s){"use strict";s.r(e);var d=s("cc1b"),a=s.n(d);for(var i in d)["default"].indexOf(i)<0&&function(t){s.d(e,t,(function(){return d[t]}))}(i);e["default"]=a.a}}]);