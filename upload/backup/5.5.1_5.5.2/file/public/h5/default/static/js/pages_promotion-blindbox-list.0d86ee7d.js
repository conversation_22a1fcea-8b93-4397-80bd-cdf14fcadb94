(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-blindbox-list"],{"015d":function(t,e,i){"use strict";i.r(e);var o=i("0f46"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},"0cdc":function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.ns-adv[data-v-5e3b6628]{padding:%?20?% %?30?% 0;overflow:hidden;line-height:1;background-color:#fff;margin-bottom:%?20?%}.ns-adv .my_box_category[data-v-5e3b6628]{line-height:1;width:100%;display:flex;z-index:999;box-sizing:border-box}.ns-adv .my_box_category .category-item[data-v-5e3b6628]{text-align:center;margin:0 %?20?%;white-space:nowrap}.ns-adv .my_box_category .category-item .item-con[data-v-5e3b6628]{display:inline-block;height:%?88?%;font-size:%?30?%;position:relative;line-height:%?88?%}.ns-adv .my_box_category .category-item .item-con.active[data-v-5e3b6628]:after{content:"";display:block;text-align:center;width:100%;height:%?4?%;border-radius:%?10?%;position:absolute;left:0;bottom:0}.goods-list.double-column[data-v-5e3b6628]{display:flex;flex-wrap:wrap;margin:0 %?30?%}.goods-list.double-column .goods-item[data-v-5e3b6628]{flex:1;position:relative;background-color:#fff;flex-basis:48%;max-width:calc((100% - %?30?%) / 2);margin-right:%?30?%;margin-bottom:%?20?%;border-radius:%?10?%}.goods-list.double-column .goods-item[data-v-5e3b6628]:nth-child(2n){margin-right:0}.goods-list.double-column .goods-item .goods-img[data-v-5e3b6628]{position:relative;overflow:hidden;padding-top:100%;border-top-left-radius:%?10?%;border-top-right-radius:%?10?%;background-color:#fff}.goods-list.double-column .goods-item .goods-img uni-image[data-v-5e3b6628]{height:%?332?%;width:100%;position:absolute;top:50%;left:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.goods-list.double-column .goods-item .info-wrap[data-v-5e3b6628]{padding:0 %?26?% %?26?% %?26?%}.goods-list.double-column .goods-item .info-wrap .goods-name[data-v-5e3b6628]{font-size:%?28?%;line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;word-break:break-word;margin-top:%?20?%;height:%?68?%}.goods-list.double-column .goods-item .info-wrap .discount-price[data-v-5e3b6628]{display:inline-block;font-weight:700;line-height:1;margin-top:%?16?%;color:var(--price-color)}.goods-list.double-column .goods-item .info-wrap .discount-price .unit[data-v-5e3b6628]{margin-right:%?6?%}.goods-list.double-column .goods-item .info-wrap .discount-price .delete-price[data-v-5e3b6628]{text-decoration:line-through;flex:1;font-weight:400}.goods-list.double-column .goods-item .info-wrap .discount-price .delete-price .unit[data-v-5e3b6628]{margin-right:%?6?%}',""]),t.exports=e},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=o},"23ef":function(t,e,i){"use strict";i.r(e);var o=i("28ed"),n=i("c05c");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("36f6"),i("5e13");var r=i("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,"5e3b6628",null,!1,o["a"],void 0);e["default"]=s.exports},2407:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.advList.length?i("v-uni-view",{class:["container-box",t.className]},[t.advList.length>1?i("v-uni-swiper",{staticClass:"item-wrap",style:{height:t.swiperHeight+"px"},attrs:{"indicator-dots":t.advList.length>1,"indicator-active-color":"#ffffff",autoplay:!0,interval:3e3,duration:1e3,current:t.currentIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSwiper.apply(void 0,arguments)}}},t._l(t.advList,(function(e,o){return i("v-uni-swiper-item",{key:o,on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.jumppage(e.adv_url)}}},[i("v-uni-view",{staticClass:"image-box"},[i("v-uni-image",{attrs:{src:t.$util.img(e.adv_image),mode:"widthFix",id:"content-wrap"+o}})],1)],1)})),1):i("v-uni-view",{staticClass:"container-box item-wrap"},[i("v-uni-image",{attrs:{src:t.$util.img(t.advList[0]["adv_image"]),mode:"widthFix","lazy-load":"true"},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imageLoad.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumppage(t.advList[0].adv_url)}}})],1)],1):t._e()},n=[]},"28ed":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={pageMeta:i("7854").default,nsAdv:i("7e88").default,nsEmpty:i("52a6").default,hoverNav:i("c1f1").default,loadingCover:i("c003").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{class:t.isIphoneX?"iphone-x":""},[i("v-uni-scroll-view",{staticClass:"topHeight",attrs:{"scroll-y":"true"},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.watchScroll.apply(void 0,arguments)},scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.getDate.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"ns-adv"},[i("ns-adv",{attrs:{keyword:"NS_BLINDBOX"}}),i("v-uni-view",{class:t.topJudge?"my_box_category_box":""},[i("v-uni-view",{staticClass:"my_box_category-big my_box_category"},[i("v-uni-view",{staticClass:"category-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.categoryChange("0")}}},[i("v-uni-view",{staticClass:"item-con",class:0==t.boxStatus?"active color-base-text color-base-bg-before":""},[t._v("全部")])],1),i("v-uni-scroll-view",{staticClass:"coupon-all",attrs:{"scroll-x":!0,"show-scrollbar":!1}},[i("v-uni-view",{staticClass:"my_box_category"},t._l(t.categoryList,(function(e,o){return i("v-uni-view",{key:o,staticClass:"category-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.categoryChange(e.category_id)}}},[i("v-uni-view",{staticClass:"item-con",class:e.category_id==t.boxStatus?"active color-base-text color-base-bg-before":""},[t._v(t._s(e.category_name))])],1)})),1)],1)],1)],1),t.topJudge?i("v-uni-view",{staticClass:"hover_category"}):t._e()],1),t.list.length>0?i("v-uni-view",{staticClass:"goods-list double-column"},t._l(t.list,(function(e,o){return i("v-uni-view",{key:o,staticClass:"goods-item margin-bottom",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e.blindbox_id)}}},[i("v-uni-view",{staticClass:"goods-img"},[""!=e.blindbox_images?i("v-uni-image",{attrs:{src:t.$util.img(e.blindbox_images),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(o)}}}):i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/blindbox/default.png")}})],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap"},[i("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.blindbox_name))])],1),i("v-uni-view",{staticClass:"discount-price"},[i("v-uni-text",{staticClass:"unit price-style small"},[t._v("￥"),i("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(e.price).toFixed(2).split(".")[0]))]),t._v("."+t._s(parseFloat(e.price).toFixed(2).split(".")[1]))],1)],1)],1)],1)})),1):t._e(),t.bottom_tips_judge?i("v-uni-view",{staticClass:"bottom_tips_judge"},[t._v("已经到底了")]):t._e(),0==t.list.length?i("v-uni-view",[i("ns-empty",{attrs:{isIndex:!1,text:"暂无数据"}})],1):t._e()],1),i("hover-nav"),i("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},"36f6":function(t,e,i){"use strict";var o=i("9d2f"),n=i.n(o);n.a},4884:function(t,e,i){var o=i("ba18");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("321fa375",o,!0,{sourceMap:!1,shadowMode:!1})},"5e13":function(t,e,i){"use strict";var o=i("4884"),n=i.n(o);n.a},6102:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47");var o={name:"ns-advert",props:{keyword:{type:String},className:{type:String}},data:function(){return{advList:[],isImage:!1,swiperHeight:150,currentIndex:0}},created:function(){this.getAdvList()},methods:{getAdvList:function(){var t=this;this.$api.sendRequest({url:"/api/adv/detail",data:{keyword:this.keyword},success:function(e){if(0==e.code){var i=e.data.adv_list;for(var o in i)i[o].adv_url&&(i[o].adv_url=JSON.parse(i[o].adv_url));t.advList=e.data.adv_list,t.$nextTick((function(){t.setSwiperHeight()}))}}})},jumppage:function(t){this.$util.diyRedirectTo(t)},imageLoad:function(t){this.isImage=!0},changeSwiper:function(t){var e=this;this.currentIndex=t.detail.current,this.$nextTick((function(){e.setSwiperHeight()}))},setSwiperHeight:function(){var t=this;this.advList.length>1&&setTimeout((function(){var e="#content-wrap"+t.currentIndex,i=uni.createSelectorQuery().in(t);i.select(e).boundingClientRect(),i.exec((function(e){e&&e[0]&&(t.swiperHeight=e[0].height)}))}),10)}}};e.default=o},7854:function(t,e,i){"use strict";i.r(e);var o=i("8ba8"),n=i("f48d");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);var r=i("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=s.exports},"7e88":function(t,e,i){"use strict";i.r(e);var o=i("2407"),n=i("f016");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("a44f");var r=i("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,"9caa2b5c",null,!1,o["a"],void 0);e["default"]=s.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"9d2f":function(t,e,i){var o=i("0cdc");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("69c17ed6",o,!0,{sourceMap:!1,shadowMode:!1})},a423:function(t,e,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("c223");var n=o(i("7e88")),a={components:{nsAdv:n.default},data:function(){return{boxStatus:0,list:[],categoryList:[],showEmpty:!1,isIphoneX:!1,state:1,adv:{},mescroll:{num:1,size:8},topJudge:!1,bottom_tips_judge:!0,skuId:0,mpShareData:null}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.blindbox||(e.$util.showToast({title:"商家未开启盲盒",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.getCategory(),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getDate(),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member"))},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},methods:{getCategory:function(){var t=this;this.$api.sendRequest({url:"/blindbox/api/goods/categoryList",success:function(e){e.code>=0?t.categoryList=e.data:t.$util.showToast({title:e.message})}})},getDate:function(){var t=this;this.showEmpty=!1,this.$api.sendRequest({url:"/blindbox/api/goods/page",data:{page:this.mescroll.num,page_size:this.mescroll.size,category_id:this.boxStatus},success:function(e){if(t.showEmpty=!0,e.code>=0)if(e.data.list.length>0){var i;i=e.data.list,i.length<t.mescroll.size&&(t.bottom_tips_judge=!0),t.list=t.list.concat(i),t.mescroll.num+=1}else t.mescroll.num>1&&(t.bottom_tips_judge=!0);t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},categoryChange:function(t){this.boxStatus=t,this.mescroll.num=1,this.list=[],this.bottom_tips_judge=!1,this.getDate()},toDetail:function(t){this.$util.redirectTo("/pages_promotion/blindbox/index",{blindbox_id:t})},imageError:function(t){this.list[t].blindbox_images=this.$util.getDefaultImage().goods,this.$forceUpdate()},watchScroll:function(t){this.topHeight=t.detail.scrollTop,this.topHeight<165?this.topJudge=!1:this.topJudge=!0}}};e.default=a},a44f:function(t,e,i){"use strict";var o=i("d87f"),n=i.n(o);n.a},a725:function(t,e,i){"use strict";var o=i("ac2a"),n=i.n(o);n.a},ac2a:function(t,e,i){var o=i("f714");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("1a69ffc2",o,!0,{sourceMap:!1,shadowMode:!1})},ba18:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-5e3b6628] .fixed{top:40vh}[data-v-5e3b6628] .coupon-all{width:85%}.topHeight[data-v-5e3b6628]{height:100vh}.my_box_category_box[data-v-5e3b6628]{position:fixed;top:0;left:0;width:100%;background:#fff;z-index:999999;padding:0 %?30?%;box-sizing:border-box}.hover_category[data-v-5e3b6628]{height:%?90?%}.bottom_tips_judge[data-v-5e3b6628]{color:#ccc;font-size:%?28?%;text-align:center;padding:%?20?% 0}',""]),t.exports=e},c05c:function(t,e,i){"use strict";i.r(e);var o=i("a423"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},c1f1:function(t,e,i){"use strict";i.r(e);var o=i("fa1d"),n=i("015d");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("a725");var r=i("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,"c1934e78",null,!1,o["a"],void 0);e["default"]=s.exports},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=n},d87f:function(t,e,i){var o=i("d915");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("03d75754",o,!0,{sourceMap:!1,shadowMode:!1})},d915:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-9caa2b5c]{width:100%}.container-box .item-wrap[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-9caa2b5c]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}',""]),t.exports=e},f016:function(t,e,i){"use strict";i.r(e);var o=i("6102"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},f48d:function(t,e,i){"use strict";i.r(e);var o=i("cc1b"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},f714:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},n=[]}}]);