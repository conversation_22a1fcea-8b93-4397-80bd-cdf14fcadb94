(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-store-list"],{"237e":function(t,e,i){"use strict";i.r(e);var o=i("7a3d"),s=i("785d");for(var a in s)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(a);i("9af42"),i("e5d9");var r=i("828b"),n=Object(r["a"])(s["default"],o["b"],o["c"],!1,null,"15c4aaee",null,!1,o["a"],void 0);e["default"]=n.exports},6750:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.store-list-wrap .curr-store[data-v-15c4aaee]{background-color:#fff;margin-bottom:%?20?%;padding:%?20?% %?24?% 0}.store-list-wrap .curr-store .store-desc[data-v-15c4aaee]{font-size:%?24?%;color:#636363}.store-list-wrap .curr-store .store-name-wrap[data-v-15c4aaee]{display:flex;align-items:center;justify-content:space-between;padding:%?12?% 0 %?30?%}.store-list-wrap .curr-store .store-name-wrap .store-name[data-v-15c4aaee]{width:%?500?%;font-size:%?26?%;font-weight:700;line-height:1.5}.store-list-wrap .curr-store .store-name-wrap .store-position[data-v-15c4aaee]{font-size:%?24?%;color:#df5948}.store-list-wrap .curr-store .store-name-wrap .store-position .iconfont[data-v-15c4aaee]{margin-right:%?10?%}.store-list-wrap .store-list-box[data-v-15c4aaee]{background-color:#fff;padding:0 %?24?% %?24?%}.store-list-wrap .store-list-box .store-list-head[data-v-15c4aaee]{padding:%?34?% 0 %?10?%;display:flex;align-items:center;justify-content:space-between}.store-list-wrap .store-list-box .store-list-head .head-name[data-v-15c4aaee]{font-size:%?26?%;color:#666}.store-list-wrap .store-list-box .store-list-head .head-search[data-v-15c4aaee]{display:flex;align-items:center;width:%?218?%;height:%?68?%;background-color:#f0f1f3;border-radius:%?50?%;color:#b3b4b9;padding:0 %?26?%;box-sizing:border-box}.store-list-wrap .store-list-box .store-list-head .head-search .iconfont[data-v-15c4aaee]{font-size:%?26?%;margin-right:%?10?%}.store-list-wrap .store-list-box .store-list-head .head-search uni-input[data-v-15c4aaee]{color:#666}.store-list-wrap .store-list-box .store-list-body .store-item[data-v-15c4aaee]{margin:%?20?% %?6?% %?30?%;padding:%?26?% %?28?%;display:flex;flex-direction:column;align-items:baseline;box-shadow:0 0 %?10?% 0 rgba(128,132,148,.3);border-radius:%?10?%}.store-list-wrap .store-list-box .store-list-body .store-item.active[data-v-15c4aaee]{border:%?2?% solid #df5948}.store-list-wrap .store-list-box .store-list-body .store-item .item-state[data-v-15c4aaee]{padding:%?8?% %?10?%;font-size:%?24?%;border:%?2?% solid #66ad95;color:#66ad95;border-radius:%?4?%;line-height:1}.store-list-wrap .store-list-box .store-list-body .store-item .item-state.warning[data-v-15c4aaee]{color:red;border-color:red}.store-list-wrap .store-list-box .store-list-body .store-item .item-name[data-v-15c4aaee]{margin:%?24?% 0 %?10?%;font-size:%?32?%;font-weight:700;line-height:1.5}.store-list-wrap .store-list-box .store-list-body .store-item .item-close-desc[data-v-15c4aaee]{font-size:%?24?%;color:red}.store-list-wrap .store-list-box .store-list-body .store-item .item-time[data-v-15c4aaee]{display:flex;align-items:center;justify-content:space-between;width:100%}.store-list-wrap .store-list-box .store-list-body .store-item .item-time .item-time-left[data-v-15c4aaee]{display:flex;align-items:center;justify-content:space-between;font-size:%?24?%;color:#5f6067}.store-list-wrap .store-list-box .store-list-body .store-item .item-time .item-time-left .iconfont[data-v-15c4aaee]{margin-right:%?10?%}.store-list-wrap .store-list-box .store-list-body .store-item .item-time .item-time-right[data-v-15c4aaee]{color:#5f6067;font-size:%?24?%}.store-list-wrap .store-list-box .store-list-body .store-item .item-address[data-v-15c4aaee]{margin-top:%?6?%;font-size:%?24?%;color:#5f6067;line-height:1.3}.store-list-wrap .store-list-box .store-list-body .store-item .item-address .iconfont[data-v-15c4aaee]{margin-right:%?10?%}.store-list-wrap .store-list-box .store-list-body .store-item .item-other[data-v-15c4aaee]{width:100%;display:flex;align-items:center;justify-content:space-between;margin-top:%?26?%}.store-list-wrap .store-list-box .store-list-body .store-item .item-other .other-tag-wrap .tag-item[data-v-15c4aaee]{padding:%?8?% %?12?%;margin-right:%?20?%;font-size:%?24?%;color:#77ab69;background-color:#f3f9ed;border-radius:%?4?%}.store-list-wrap .store-list-box .store-list-body .store-item .item-other .other-action[data-v-15c4aaee]{display:flex;align-items:baseline;font-size:%?24?%;color:#df5948;line-height:1}.store-list-wrap .store-list-box .store-list-body .store-item .item-other .other-action .iconfont[data-v-15c4aaee]{font-size:%?24?%}',""]),t.exports=e},"69b2":function(t,e,i){var o=i("c80c");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var s=i("967d").default;s("4722cd7e",o,!0,{sourceMap:!1,shadowMode:!1})},7854:function(t,e,i){"use strict";i.r(e);var o=i("8ba8"),s=i("f48d");for(var a in s)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(a);var r=i("828b"),n=Object(r["a"])(s["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=n.exports},"785d":function(t,e,i){"use strict";i.r(e);var o=i("ee01"),s=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=s.a},"7a3d":function(t,e,i){"use strict";i.d(e,"b",(function(){return s})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={pageMeta:i("7854").default,nsEmpty:i("52a6").default,loadingCover:i("c003").default},s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"store-list-wrap"},[t.globalStoreConfig&&"store"==t.globalStoreConfig.store_business?i("v-uni-view",{staticClass:"curr-store"},[i("v-uni-view",{staticClass:"store-desc"},[t._v("当前定位")]),i("v-uni-view",{staticClass:"store-name-wrap"},[i("v-uni-view",{staticClass:"store-name multi-hidden"},[t._v(t._s(t.currentPosition||"定位中..."))]),i("v-uni-view",{staticClass:"store-position",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.reposition()}}},[i("v-uni-text",{staticClass:"iconfont icon-dingwei"}),i("v-uni-text",[t._v("重新定位")])],1)],1)],1):t._e(),i("v-uni-view",{staticClass:"store-list-box"},[i("v-uni-view",{staticClass:"store-list-head"},[i("v-uni-view",{staticClass:"head-name"},[t._v("门店列表")]),i("v-uni-view",{staticClass:"head-search"},[i("v-uni-text",{staticClass:"iconfont icon-sousuo",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getData()}}}),i("v-uni-input",{attrs:{type:"text","placeholder-class":"input-placeholder",placeholder:"搜索门店"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.getData()}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1)],1),i("v-uni-scroll-view",{staticClass:"store-list-body",style:{height:t.globalStoreConfig&&"store"==t.globalStoreConfig.store_business?"calc(100vh - 320rpx)":""},attrs:{"scroll-y":"true"}},[t._l(t.dataList,(function(e,o){return i("v-uni-view",{key:o,class:["store-item",{active:t.globalStoreInfo&&e.store_id==t.globalStoreInfo.store_id}],on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.storeTap(e)}}},[i("v-uni-view",{staticClass:"item-state",class:1==e.is_frozen.is_frozen||0==e.status?"warning":""},[t._v(t._s((1==e.is_frozen.is_frozen?"已停业":0==e.status&&"休息中")||1==e.status&&"营业中"||"--"))]),i("v-uni-view",{staticClass:"item-name multi-hidden"},[t._v(t._s(e.store_name))]),0==e.status&&e.close_desc?i("v-uni-view",{staticClass:"item-close-desc"},[t._v(t._s(e.close_desc))]):t._e(),i("v-uni-view",{staticClass:"item-time"},[i("v-uni-view",{staticClass:"item-time-left"},[i("v-uni-text",{staticClass:"iconfont icon-shijian1"}),i("v-uni-text",[t._v(t._s(e.open_date||"--"))])],1),e.distance?i("v-uni-view",{staticClass:"item-time-right"},[t._v(t._s(e.distance>1?e.distance+"km":1e3*e.distance+"m"))]):t._e()],1),i("v-uni-view",{staticClass:"item-address"},[i("v-uni-text",{staticClass:"iconfont icon-location"}),i("v-uni-text",[t._v(t._s(e.show_address))])],1),i("v-uni-view",{staticClass:"item-other"},[i("v-uni-view",{staticClass:"other-tag-wrap"},[1==e.is_default?i("v-uni-text",{staticClass:"tag-item"},[t._v("总店")]):t._e(),1==e.is_pickup?i("v-uni-text",{staticClass:"tag-item"},[t._v("门店自提")]):t._e(),1==e.is_o2o?i("v-uni-text",{staticClass:"tag-item"},[t._v("同城配送")]):t._e(),1==e.is_express?i("v-uni-text",{staticClass:"tag-item"},[t._v("物流配送")]):t._e()],1),i("v-uni-view",{staticClass:"other-action",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.selectStore(e)}}},[i("v-uni-text",[t._v("详情")]),i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1)],1)})),t.dataList.length?t._e():i("ns-empty",{attrs:{text:"您的附近暂无可选门店",isIndex:!1}})],2)],1),i("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},"7c27":function(t,e,i){var o=i("6750");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var s=i("967d").default;s("edc97f46",o,!0,{sourceMap:!1,shadowMode:!1})},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},s=[]},"9af42":function(t,e,i){"use strict";var o=i("69b2"),s=i.n(o);s.a},c80c:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-15c4aaee] .input-placeholder{color:#b3b4b9;font-size:%?24?%}',""]),t.exports=e},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},s={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(s){s.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=s},e5d9:function(t,e,i){"use strict";var o=i("7c27"),s=i.n(o);s.a},ee01:function(t,e,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5c47"),i("a1c1");var s=o(i("2f8f")),a={components:{},data:function(){return{dataList:[],latitude:null,longitude:null,currentPosition:"",keyword:""}},watch:{location:function(t){t&&(this.latitude=t.latitude,this.longitude=t.longitude,this.getData(),this.getCurrentLocation())}},onLoad:function(t){var e=this;t.module&&"locationPicker"==t.module&&(this.latitude=t.latng.split(",")[0],this.longitude=t.latng.split(",")[1],this.currentPosition=t.addr+t.name),this.currentPosition||(this.location?(this.latitude=this.location.latitude,this.longitude=this.location.longitude,this.getCurrentLocation()):1==this.mapConfig.wap_is_open&&this.$nextTick((function(){e.$util.getLocation({fail:function(t){e.currentPosition="未获取到定位"}})}))),this.getData()},onShow:function(){var t=this;1==this.mapConfig.wap_is_open&&this.locationStorage&&this.locationStorage.is_expired&&this.$util.getLocation({fail:function(e){t.currentPosition="未获取到定位"}})},methods:{getData:function(){var t=this,e={};e.keyword=this.keyword,this.latitude&&this.longitude&&(e.latitude=this.latitude,e.longitude=this.longitude),this.$api.sendRequest({url:"/api/store/page",data:e,success:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide(),0==e.code&&e.data?(t.dataList=e.data.list,t.dataList.forEach((function(t){t.show_address=t.full_address.replace(/,/g," ")+" "+t.address}))):t.$util.showToast({title:e.message})},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},storeTap:function(t){uni.setStorageSync("manual_store_info",t),this.changeStore(t,!0)},selectStore:function(t){this.$util.redirectTo("/pages_tool/store/detail",{store_id:t.store_id})},getCurrentLocation:function(){var t=this,e={};this.latitude&&this.longitude&&(e.latitude=this.latitude,e.longitude=this.longitude),this.$api.sendRequest({url:"/api/store/getLocation",data:e,success:function(e){0==e.code&&e.data?t.currentPosition=e.data.formatted_addresses.recommend:t.currentPosition="未获取到定位"}})},reposition:function(){var t=s.default.h5Domain+"/pages_tool/store/list";window.location.href="https://apis.map.qq.com/tools/locpicker?search=1&type=0&backurl="+encodeURIComponent(t)+"&key="+s.default.mpKey+"&referer=myapp"}}};e.default=a},f48d:function(t,e,i){"use strict";i.r(e);var o=i("cc1b"),s=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=s.a}}]);