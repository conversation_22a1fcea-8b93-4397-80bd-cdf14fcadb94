(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-account_edit"],{"020e":function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("aa9c"),a("dc69");var i=n(a("fe8d")),o={data:function(){return{formData:{realname:"",mobile:"",withdraw_type:"",bank_account:"",branch_bank_name:""},payList:[],index:0,flag:!1,transferType:[],accountInfo:null,back:"",type:"member"}},onLoad:function(t){t.id&&(this.formData.id=t.id),t.back&&(this.back=t.back),t.type&&(this.type=t.type)},onShow:function(){this.formData.id?this.getAccountDetail():this.getTransferType(),this.formData.id?uni.setNavigationBarTitle({title:"编辑账户"}):uni.setNavigationBarTitle({title:"新增账户"})},methods:{getAccountDetail:function(){var t=this;this.$api.sendRequest({url:"/api/memberbankaccount/info",data:{id:this.formData.id},success:function(e){0==e.code&&e.data&&(t.accountInfo=e.data,t.formData.realname=e.data.realname,t.formData.mobile=e.data.mobile,t.formData.bank_account=e.data.bank_account,t.formData.branch_bank_name=e.data.branch_bank_name,t.formData.withdraw_type=e.data.withdraw_type),t.getTransferType(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(e){t.getTransferType(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},getTransferType:function(){var t=this;this.payList=[];var e="member"==this.type?"/api/memberwithdraw/transferType":"/fenxiao/api/withdraw/transferType";this.$api.sendRequest({url:e,success:function(e){if(e.code>=0&&e.data){for(var a in delete e.data.balance,t.transferType=e.data,t.transferType)t.payList.push(t.transferType[a]);if(1==t.payList.length&&"银行卡"==t.payList[0]&&(t.formData.withdraw_type="bank"),t.payList.reverse(),!t.formData.id&&t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.accountInfo&&-1==t.$util.inArray(t.accountInfo.withdraw_type_name,t.payList)&&t.payList.push(t.accountInfo.withdraw_type_name),t.payList.length&&t.accountInfo&&(t.index=t.$util.inArray(t.accountInfo.withdraw_type_name,t.payList)),!t.formData.withdraw_type&&t.payList.length)switch(t.payList[0]){case"银行卡":t.formData.withdraw_type="bank";break;case"支付宝":t.formData.withdraw_type="alipay";break;case"微信零钱":t.formData.withdraw_type="wechatpay";break}}}})},bindPickerChange:function(t){this.index=t.detail.value;var e="";for(var a in this.transferType)this.transferType[a]==this.payList[this.index]&&(e=a);""!=e&&(this.formData.withdraw_type=e)},vertify:function(){var t=[{name:"realname",checkType:"required",errorMsg:"请输入姓名"},{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"},{name:"withdraw_type",checkType:"required",errorMsg:"请选择账户类型"}];"bank"==this.formData.withdraw_type&&t.push({name:"branch_bank_name",checkType:"required",errorMsg:"请输入银行名称"}),"wechatpay"!=this.formData.withdraw_type&&t.push({name:"bank_account",checkType:"required",errorMsg:"请输入提现账号"});var e=i.default.check(this.formData,t);return!!e||(this.$util.showToast({title:i.default.error}),this.flag=!1,!1)},saveAccount:function(){var t=this;if(!this.flag&&(this.flag=!0,this.vertify())){var e=this.formData.id?"edit":"add";this.$api.sendRequest({url:"/api/memberbankaccount/"+e,data:{id:this.formData.id,realname:this.formData.realname,mobile:this.formData.mobile,withdraw_type:this.formData.withdraw_type,bank_account:this.formData.bank_account,branch_bank_name:this.formData.branch_bank_name},success:function(e){0==e.code?(t.formData.id?t.$util.showToast({title:"修改成功"}):t.$util.showToast({title:"添加成功"}),""!=t.back?t.$util.redirectTo(t.back,{},t.redirect):t.$util.redirectTo("/pages_tool/member/account")):(t.flag=!1,t.$util.showToast({title:e.message}))},fail:function(e){t.flag=!1}})}}}};e.default=o},"0e92":function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.account-list-content[data-v-6df91f90]{margin:%?20?% %?30?%;padding:0 %?30?%;background-color:#fff;border-radius:%?10?%}.account-list-content .edit-item[data-v-6df91f90]{display:flex;align-items:center;padding:%?30?% 0;background-color:#fff}.account-list-content .edit-item .tit[data-v-6df91f90]{width:%?120?%}.account-list-content .edit-item .desc[data-v-6df91f90]{flex:1;margin-left:%?20?%;padding:0}.account-list-content .edit-item[data-v-6df91f90]:first-of-type{margin-top:%?20?%}.account-list-content .edit-item .picker[data-v-6df91f90]{flex:1}.account-list-content .edit-item .picker uni-text[data-v-6df91f90]:last-child{line-height:%?50?%;float:right;color:#909399;font-size:%?20?%}.account-list-content > .edit-item + .edit-item[data-v-6df91f90]{border-top:%?2?% solid #eee}.add[data-v-6df91f90]{margin-top:%?60?%;height:%?80?%;line-height:%?80?%!important;border-radius:%?80?%;font-weight:500;width:calc(100% - %?60?%);margin-left:%?30?%;font-size:%?32?%}.btn[data-v-6df91f90]{position:fixed;left:0;width:100%;bottom:%?30?%;height:auto;padding-bottom:constant(safe-area-inset-bottom);\r\n  /*兼容 IOS<11.2*/padding-bottom:env(safe-area-inset-bottom)\r\n  /*兼容 IOS>11.2*/}',""]),t.exports=e},"25c2":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={pageMeta:a("7854").default,loadingCover:a("c003").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-view",{staticClass:"account-list-content"},[a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("name")))]),a("v-uni-input",{staticClass:"desc uni-input",attrs:{type:"text",maxlength:"30",placeholder:"请输入真实姓名",name:"name"},model:{value:t.formData.realname,callback:function(e){t.$set(t.formData,"realname",e)},expression:"formData.realname"}})],1),a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("mobilePhone")))]),a("v-uni-input",{staticClass:"desc uni-input",attrs:{type:"number",maxlength:"11",placeholder:"请输入手机号"},model:{value:t.formData.mobile,callback:function(e){t.$set(t.formData,"mobile",e)},expression:"formData.mobile"}})],1),a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("accountType")))]),a("v-uni-picker",{staticClass:"picker",attrs:{value:t.index,range:t.payList},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindPickerChange.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"desc uni-input"},[t._v(t._s(t.payList[t.index]))]),a("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1),"bank"==t.formData.withdraw_type?a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v("银行名称")]),a("v-uni-input",{staticClass:"desc uni-input",attrs:{type:"text",maxlength:"50",placeholder:"请输入银行名称"},model:{value:t.formData.branch_bank_name,callback:function(e){t.$set(t.formData,"branch_bank_name","string"===typeof e?e.trim():e)},expression:"formData.branch_bank_name"}})],1):t._e(),"wechatpay"!=t.formData.withdraw_type?a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v("提现账号")]),a("v-uni-input",{staticClass:"desc uni-input",attrs:{type:"text",maxlength:"30",placeholder:"请输入提现账号"},model:{value:t.formData.bank_account,callback:function(e){t.$set(t.formData,"bank_account","string"===typeof e?e.trim():e)},expression:"formData.bank_account"}})],1):t._e(),a("v-uni-view",{staticClass:"btn"},[a("v-uni-button",{staticClass:"add",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveAccount.apply(void 0,arguments)}}},[t._v(t._s(t.$lang("save")))])],1),a("loading-cover",{ref:"loadingCover"})],1)],1)},o=[]},"3c05":function(t,e,a){"use strict";a.r(e);var n=a("25c2"),i=a("6228");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("f510");var r=a("828b"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"6df91f90",null,!1,n["a"],void 0);e["default"]=s.exports},6228:function(t,e,a){"use strict";a.r(e);var n=a("020e"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},7854:function(t,e,a){"use strict";a.r(e);var n=a("8ba8"),i=a("f48d");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);var r=a("828b"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"8ba8":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},i=[]},bdb5:function(t,e,a){var n=a("0e92");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("1ce4ffee",n,!0,{sourceMap:!1,shadowMode:!1})},cc1b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},i={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var a=function a(i){i.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",a),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",a)}})}}}};e.default=i},f48d:function(t,e,a){"use strict";a.r(e);var n=a("cc1b"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},f510:function(t,e,a){"use strict";var n=a("bdb5"),i=a.n(n);i.a}}]);