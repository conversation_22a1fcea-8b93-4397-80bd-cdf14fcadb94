(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-pay_password"],{"04cd":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"code-box"},[i("v-uni-view",{staticClass:"flex-box"},[i("v-uni-input",{staticClass:"hide-input",attrs:{value:t.inputValue,type:"number",focus:t.autoFocus,maxlength:t.maxlength},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.getVal.apply(void 0,arguments)}}}),t._l(t.ranges,(function(e,a){return[i("v-uni-view",{key:a+"_0",class:["item",{active:t.codeIndex===e,middle:"middle"===t.type,bottom:"bottom"===t.type,box:"box"===t.type}]},["middle"!==t.type?i("v-uni-view",{staticClass:"line"}):t._e(),"middle"===t.type&&t.codeIndex<=e?i("v-uni-view",{staticClass:"bottom-line"}):t._e(),t.isPwd&&t.codeArr.length>=e?i("v-uni-text",{staticClass:"dot"},[t._v("●")]):i("v-uni-text",{staticClass:"number"},[t._v(t._s(t.codeArr[a]?t.codeArr[a]:""))])],1)]}))],2)],1)},o=[]},1163:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){return a}));var a={pageMeta:i("7854").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"container"},[0!=t.step?i("v-uni-view",{staticClass:"tips"},[t._v("请输入6位支付密码，建议不要使用重复或连续数字")]):i("v-uni-view",{staticClass:"tips"},[t._v("验证码已发送至"+t._s(t._f("mobile")(t.memberInfo.mobile))+"请在下方输入4位数字验证码")]),i("v-uni-view",{staticClass:"password-wrap"},[i("myp-one",{ref:"input",attrs:{maxlength:0==t.step?4:6,"is-pwd":0!=t.step,"auto-focus":!0},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.input.apply(void 0,arguments)}}}),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:0==t.step,expression:"step == 0"}],staticClass:"dynacode",class:120==t.dynacodeData.seconds?"color-base-text":"color-tip",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.sendMobileCode.apply(void 0,arguments)}}},[t._v(t._s(t.dynacodeData.codeText))]),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:0==t.step,expression:"step == 0"}],staticClass:"action-tips"},[t._v("输入短信验证码")]),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==t.step,expression:"step == 1"}],staticClass:"action-tips"},[t._v("请设置支付密码")]),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:2==t.step,expression:"step == 2"}],staticClass:"action-tips"},[t._v("请再次输入")]),i("v-uni-view",{staticClass:"btn color-base-bg color-base-border",class:{disabled:!t.isClick},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm.apply(void 0,arguments)}}},[t._v("确认")])],1)],1)],1)},n=[]},"24b0":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container[data-v-93f85074]{width:100vw;height:100vh;background:#fff}.container .tips[data-v-93f85074]{width:60%;margin:0 auto;text-align:center;padding-top:%?100?%}.container .password-wrap[data-v-93f85074]{width:80%;margin:0 auto;margin-top:%?40?%}.container .password-wrap .action-tips[data-v-93f85074]{text-align:center;font-weight:600;margin-top:%?80?%}.container .password-wrap .dynacode[data-v-93f85074]{line-height:1;margin-top:%?20?%;font-size:%?24?%}.container .password-wrap .btn[data-v-93f85074]{margin:0 auto;margin-top:%?30?%;height:%?80?%;line-height:%?80?%;border-radius:%?10?%;color:#fff;text-align:center}.container .password-wrap .btn.disabled[data-v-93f85074]{background:#ccc!important;border-color:#ccc!important;color:#fff}',""]),t.exports=e},"574b":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(i("b2c6")),n={components:{mypOne:o.default},data:function(){return{isClick:!1,step:1,key:"",code:"",password:"",repassword:"",isSub:!1,back:"",dynacodeData:{seconds:120,timer:null,codeText:"获取验证码",isSend:!1}}},onLoad:function(t){var e=this;t.back&&(this.back=t.back),this.storeToken?""==this.memberInfo.mobile?uni.showModal({title:"提示",content:"设置支付密码需要先绑定手机号,是否立即绑定?",success:function(t){t.confirm?e.$util.redirectTo("/pages_tool/member/info",{action:"mobile",back:e.back},"redirectTo"):e.back?e.$util.redirectTo(e.back):e.$util.redirectTo("/pages/member/index")}}):(this.step=0,this.sendMobileCode()):this.$util.redirectTo("/pages_tool/login/index")},methods:{input:function(t){0==this.step?4==t.length?(this.isClick=!0,this.code=t):this.isClick=!1:1==this.step?6==t.length?(this.isClick=!0,this.password=t):this.isClick=!1:6==t.length?(this.isClick=!0,this.repassword=t):this.isClick=!1},confirm:function(){var t=this;if(this.isClick)if(0==this.step)this.$api.sendRequest({url:"/api/member/verifypaypwdcode",data:{code:this.code,key:this.key},success:function(e){0==e.code?(t.$refs.input.clear(),t.isClick=!1,t.step=1):t.$util.showToast({title:e.message})}});else if(1==this.step)this.$refs.input.clear(),this.isClick=!1,this.step=2;else if(this.password==this.repassword){if(this.isSub)return;this.isSub=!0,this.$api.sendRequest({url:"/api/member/modifypaypassword",data:{key:this.key,code:this.code,password:this.password},success:function(e){e.code>=0?(t.$util.showToast({title:"修改成功"}),setTimeout((function(){t.back?t.$util.redirectTo(t.back,{},"redirectTo"):t.$util.redirectTo("/pages/member/index")}),2e3)):(t.initInfo(),t.$util.showToast({title:e.message}))}})}else this.$util.showToast({title:"两次输入的密码不一致",success:function(e){t.initInfo()}})},initInfo:function(){this.isClick=!1,this.step=1,this.password="",this.repassword="",this.isSub=!1,this.$refs.input.clear()},sendMobileCode:function(){var t=this;120==this.dynacodeData.seconds&&(this.dynacodeData.isSend||(this.dynacodeData.isSend=!0,this.$api.sendRequest({url:"/api/member/paypwdcode",success:function(e){t.dynacodeData.isSend=!1,e.code>=0?(t.key=e.data.key,120==t.dynacodeData.seconds&&null==t.dynacodeData.timer&&(t.dynacodeData.timer=setInterval((function(){t.dynacodeData.seconds--,t.dynacodeData.codeText=t.dynacodeData.seconds+"s后可重新获取"}),1e3))):t.$util.showToast({title:e.message})},fail:function(){t.$util.showToast({title:"request:fail"}),t.dynacodeData.isSend=!1}})))}},filters:{mobile:function(t){return t.substring(0,3)+"****"+t.substring(7)}},watch:{"dynacodeData.seconds":{handler:function(t,e){0==t&&(clearInterval(this.dynacodeData.timer),this.dynacodeData={seconds:120,timer:null,codeText:"获取动态码",isSend:!1})},immediate:!0,deep:!0}}};e.default=n},"6f85":function(t,e,i){"use strict";var a=i("9230"),o=i.n(a);o.a},7357:function(t,e,i){"use strict";i.r(e);var a=i("1163"),o=i("8412");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("a139");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"93f85074",null,!1,a["a"],void 0);e["default"]=r.exports},7854:function(t,e,i){"use strict";i.r(e);var a=i("8ba8"),o=i("f48d");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=r.exports},"78ff":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@-webkit-keyframes twinkling-data-v-44ea86e1{0%{opacity:.2}50%{opacity:.5}100%{opacity:.2}}@keyframes twinkling-data-v-44ea86e1{0%{opacity:.2}50%{opacity:.5}100%{opacity:.2}}.code-box[data-v-44ea86e1]{text-align:center}.flex-box[data-v-44ea86e1]{display:flex;justify-content:center;flex-wrap:wrap;position:relative}.flex-box .hide-input[data-v-44ea86e1]{position:absolute;top:0;left:-100%;width:200%;height:100%;text-align:left;z-index:9;opacity:1}.flex-box .item[data-v-44ea86e1]{position:relative;flex:1;margin-right:%?18?%;font-size:%?70?%;font-weight:700;color:#333;line-height:%?100?%}.flex-box .item[data-v-44ea86e1]::before{content:"";padding-top:100%;display:block}.flex-box .item[data-v-44ea86e1]:last-child{margin-right:0}.flex-box .middle[data-v-44ea86e1]{border:none}.flex-box .box[data-v-44ea86e1]{box-sizing:border-box;border:%?2?% solid #ccc;border-width:%?2?% 0 %?2?% %?2?%;margin-right:0}.flex-box .box[data-v-44ea86e1]:first-of-type{border-top-left-radius:%?8?%;border-bottom-left-radius:%?8?%}.flex-box .box[data-v-44ea86e1]:last-child{border-right:%?2?% solid #ccc;border-top-right-radius:%?8?%;border-bottom-right-radius:%?8?%}.flex-box .bottom[data-v-44ea86e1]{box-sizing:border-box;border-bottom:%?2?% solid #ddd}.flex-box .active[data-v-44ea86e1]{border-color:#ddd}.flex-box .active .line[data-v-44ea86e1]{display:block}.flex-box .line[data-v-44ea86e1]{display:none;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:%?2?%;height:%?40?%;background:#333;-webkit-animation:twinkling-data-v-44ea86e1 1s infinite ease;animation:twinkling-data-v-44ea86e1 1s infinite ease}.flex-box .dot[data-v-44ea86e1],\r\n.flex-box .number[data-v-44ea86e1]{line-height:%?40?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.flex-box .bottom-line[data-v-44ea86e1]{height:%?8?%;background:#000;width:80%;position:absolute;border-radius:%?4?%;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}',""]),t.exports=e},"808d":function(t,e,i){var a=i("24b0");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("31722340",a,!0,{sourceMap:!1,shadowMode:!1})},8412:function(t,e,i){"use strict";i.r(e);var a=i("574b"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},9230:function(t,e,i){var a=i("78ff");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("443eb308",a,!0,{sourceMap:!1,shadowMode:!1})},a139:function(t,e,i){"use strict";var a=i("808d"),o=i.n(a);o.a},a1d8:function(t,e,i){"use strict";i.r(e);var a=i("fcf5"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},b2c6:function(t,e,i){"use strict";i.r(e);var a=i("04cd"),o=i("a1d8");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("6f85");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"44ea86e1",null,!1,a["a"],void 0);e["default"]=r.exports},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(o){o.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=o},f48d:function(t,e,i){"use strict";i.r(e);var a=i("cc1b"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},fcf5:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"mypOneInput",props:{value:{type:String,default:""},maxlength:{type:Number,default:4},autoFocus:{type:Boolean,default:!1},isPwd:{type:Boolean,default:!1},type:{type:String,default:"bottom"}},watch:{maxlength:{immediate:!0,handler:function(t){this.ranges=6===t?[1,2,3,4,5,6]:[1,2,3,4]}},value:{immediate:!0,handler:function(t){t!==this.inputValue&&(this.inputValue=t,this.toMakeAndCheck(t))}}},data:function(){return{inputValue:"",codeIndex:1,codeArr:[],ranges:[1,2,3,4]}},methods:{getVal:function(t){var e=t.detail.value;this.inputValue=e,this.$emit("input",e),this.toMakeAndCheck(e)},toMakeAndCheck:function(t){var e=t.split("");this.codeIndex=e.length+1,this.codeArr=e,this.codeIndex>Number(this.maxlength)&&this.$emit("finish",this.codeArr.join(""))},set:function(t){this.inputValue=t,this.toMakeAndCheck(t)},clear:function(){this.inputValue="",this.codeArr=[],this.codeIndex=1}}};e.default=a}}]);