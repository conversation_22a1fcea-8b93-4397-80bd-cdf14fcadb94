(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-list"],{"015d":function(t,i,e){"use strict";e.r(i);var o=e("0f46"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a},"080d":function(t,i,e){"use strict";e.r(i);var o=e("d498"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a},"09f8":function(t,i,e){var o=e("d66c");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("0f67d003",o,!0,{sourceMap:!1,shadowMode:!1})},"0c16":function(t,i,e){"use strict";e.r(i);var o=e("bcef"),n=e("cd53");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);var s=e("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);i["default"]=r.exports},"0f46":function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};i.default=o},"1f645":function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){}));var o=function(){var t=this,i=t.$createElement,e=t._self._c||i;return t.visibleSync?e("v-uni-view",{staticClass:"uni-drawer",class:{"uni-drawer--visible":t.showDrawer,"uni-drawer--right":t.rightMode},on:{touchmove:function(i){i.stopPropagation(),i.preventDefault(),arguments[0]=i=t.$handleEvent(i),t.moveHandle.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"uni-drawer__mask",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.close.apply(void 0,arguments)}}}),e("v-uni-view",{staticClass:"uni-drawer__content",class:{"safe-area":t.isIphoneX}},[t._t("default")],2)],1):t._e()},n=[]},"29ac":function(t,i,e){"use strict";e.r(i);var o=e("b791"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a},"2ae6":function(t,i,e){"use strict";var o=e("09f8"),n=e.n(o);n.a},"2d4a":function(t,i,e){"use strict";var o=e("47bba"),n=e.n(o);n.a},"30f7":function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e("7a76"),e("c9b5")},3488:function(t,i,e){var o=e("c4cd");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("38184dc6",o,!0,{sourceMap:!1,shadowMode:!1})},"3d58":function(t,i,e){"use strict";e.r(i);var o=e("9243"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a},"43db":function(t,i,e){"use strict";e.r(i);var o=e("55e2"),n=e("3d58");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);e("2ae6");var s=e("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"65fbc3dd",null,!1,o["a"],void 0);i["default"]=r.exports},4733:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(t){if(Array.isArray(t))return(0,o.default)(t)};var o=function(t){return t&&t.__esModule?t:{default:t}}(e("8d0b"))},"47bba":function(t,i,e){var o=e("a243");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("5c1d7642",o,!0,{sourceMap:!1,shadowMode:!1})},5589:function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){return o}));var o={pageMeta:e("7854").default,diyIcon:e("a68f").default,nsEmpty:e("52a6").default,uniDrawer:e("bd5e").default,hoverNav:e("c1f1").default,loadingCover:e("c003").default},n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":t.themeColor}}),e("v-uni-view",{staticClass:"content"},[e("v-uni-view",{staticClass:"head-wrap"},[e("v-uni-view",{staticClass:"search-wrap uni-flex uni-row"},[e("v-uni-view",{staticClass:"flex-item input-wrap"},[e("v-uni-input",{staticClass:"uni-input",attrs:{maxlength:"50",placeholder:"请输入您要搜索的商品"},on:{confirm:function(i){arguments[0]=i=t.$handleEvent(i),t.search()}},model:{value:t.keyword,callback:function(i){t.keyword=i},expression:"keyword"}}),e("v-uni-text",{staticClass:"iconfont icon-sousuo3",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.search()}}})],1),e("v-uni-view",{staticClass:"iconfont",class:{"icon-apps":t.isList,"icon-list":!t.isList},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changeListStyle()}}})],1),e("v-uni-view",{staticClass:"sort-wrap"},[e("v-uni-view",{staticClass:"comprehensive-wrap",class:{"color-base-text":""===t.orderType},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.sortTabClick("")}}},[e("v-uni-text",{class:{"color-base-text":""===t.orderType}},[t._v("综合")])],1),e("v-uni-view",{class:{"color-base-text":"sale_num"===t.orderType},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.sortTabClick("sale_num")}}},[t._v("销量")]),e("v-uni-view",{staticClass:"price-wrap",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.sortTabClick("discount_price")}}},[e("v-uni-text",{class:{"color-base-text":"discount_price"===t.orderType}},[t._v("价格")]),e("v-uni-view",{staticClass:"iconfont-wrap"},[e("v-uni-view",{staticClass:"iconfont icon-iconangledown-copy asc",class:{"color-base-text":"asc"===t.priceOrder&&"discount_price"===t.orderType}}),e("v-uni-view",{staticClass:"iconfont icon-iconangledown desc",class:{"color-base-text":"desc"===t.priceOrder&&"discount_price"===t.orderType}})],1)],1),e("v-uni-view",{staticClass:"screen-wrap",class:{"color-base-text":"screen"===t.orderType}},[e("v-uni-text",{on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.sortTabClick("screen")}}},[t._v("筛选")]),e("v-uni-view",{staticClass:"iconfont-wrap",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.sortTabClick("screen")}}},[e("v-uni-view",{staticClass:"iconfont icon-shaixuan color-tip"})],1)],1)],1)],1),e("mescroll-uni",{ref:"mescroll",attrs:{top:"180"},on:{getData:function(i){arguments[0]=i=t.$handleEvent(i),t.getGoodsList.apply(void 0,arguments)}}},[e("template",{attrs:{slot:"list"},slot:"list"},[e("v-uni-view",{staticClass:"goods-list single-column",class:{show:t.isList}},t._l(t.goodsList,(function(i,o){return e("v-uni-view",{key:o,staticClass:"goods-item margin-bottom",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetail(i)}}},[e("v-uni-view",{staticClass:"goods-img"},[e("v-uni-image",{attrs:{src:t.goodsImg(i.goods_image),mode:"widthFix"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.imgError(o)}}}),""!=t.goodsTag(i)?e("v-uni-view",{staticClass:"color-base-bg goods-tag"},[t._v(t._s(t.goodsTag(i)))]):t._e(),i.goods_stock<=0?e("v-uni-view",{staticClass:"sell-out"},[e("v-uni-text",{staticClass:"iconfont icon-shuqing"})],1):t._e()],1),e("v-uni-view",{staticClass:"info-wrap"},[e("v-uni-view",{staticClass:"name-wrap"},[e("v-uni-view",{staticClass:"goods-name",class:[{"using-hidden":"single"==t.config.nameLineMode},{"multi-hidden":"multiple"==t.config.nameLineMode}]},[t._v(t._s(i.goods_name))])],1),e("v-uni-view",{staticClass:"lineheight-clear"},[e("v-uni-view",{staticClass:"discount-price"},[e("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.showPrice(i)).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.showPrice(i)).toFixed(2).split(".")[1]))])],1),i.member_price&&i.member_price==t.showPrice(i)?e("v-uni-view",{staticClass:"member-price-tag"},[e("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/VIP.png"),mode:"widthFix"}})],1):1==i.promotion_type?e("v-uni-view",{staticClass:"member-price-tag"},[e("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/discount.png"),mode:"widthFix"}})],1):t._e()],1),e("v-uni-view",{staticClass:"pro-info"},[t.showMarketPrice(i)?e("v-uni-view",{staticClass:"delete-price color-tip price-font"},[e("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),e("v-uni-text",[t._v(t._s(t.showMarketPrice(i)))])],1):t._e(),e("v-uni-view",{staticClass:"block-wrap"},[i.sale_show?e("v-uni-view",{staticClass:"sale color-tip"},[t._v("已售"+t._s(i.sale_num)+t._s(i.unit?i.unit:"件"))]):t._e()],1),t.config.control&&0==i.is_virtual?e("v-uni-view",{staticClass:"cart-action-wrap"},["icon-cart"==t.config.style?e("v-uni-view",{staticClass:"cart shopping-cart-btn iconfont icon-gouwuche click-wrap",style:{color:"diy"==t.config.theme?t.config.textColor:"",borderColor:"diy"==t.config.theme?t.config.textColor:""},attrs:{id:"goods-"+i.id},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,i,e)}}},[e("v-uni-view",{staticClass:"click-event"})],1):"icon-add"==t.config.style?e("v-uni-view",{staticClass:"cart plus-sign-btn iconfont icon-add1 click-wrap",style:{color:"diy"==t.config.theme?t.config.textColor:"",borderColor:"diy"==t.config.theme?t.config.textColor:""},attrs:{id:"goods-"+i.id},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,i,e)}}},[e("v-uni-view",{staticClass:"click-event"})],1):"button"==t.config.style?e("v-uni-view",{staticClass:"cart buy-btn click-wrap",style:{backgroundColor:"diy"==t.config.theme?t.config.bgColor:"",color:"diy"==t.config.theme?t.config.textColor:"",fontWeight:"diy"==t.config.theme?t.config.fontWeight?"bold":"normal":"",padding:"diy"==t.config.theme?"12rpx "+2*t.config.padding+"rpx":""},attrs:{id:"goods-"+i.id},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,i,e)}}},[t._v(t._s(t.config.text)),e("v-uni-view",{staticClass:"click-event"})],1):"icon-diy"==t.config.style?e("v-uni-view",{staticClass:"icon-diy click-wrap",style:{color:"diy"==t.config.theme?t.config.textColor:""},attrs:{id:"goods-"+i.id},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,i,e)}}},[e("v-uni-view",{staticClass:"click-event"}),e("diy-icon",{attrs:{icon:t.config.iconDiy.icon,value:t.config.iconDiy.style?t.config.iconDiy.style:null}})],1):t._e()],1):t._e()],1)],1)],1)})),1),e("v-uni-view",{staticClass:"goods-list double-column",class:{show:!t.isList}},t._l(t.goodsList,(function(i,o){return e("v-uni-view",{key:o,staticClass:"goods-item margin-bottom",style:{left:t.listPosition[o]?t.listPosition[o].left:"",top:t.listPosition[o]?t.listPosition[o].top:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetail(i)}}},[e("v-uni-view",{staticClass:"goods-img"},[e("v-uni-image",{attrs:{src:t.goodsImg(i.goods_image),mode:"widthFix"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.imgError(o)}}}),""!=t.goodsTag(i)?e("v-uni-view",{staticClass:"color-base-bg goods-tag"},[t._v(t._s(t.goodsTag(i)))]):t._e(),i.stock<=0?e("v-uni-view",{staticClass:"sell-out"},[e("v-uni-text",{staticClass:"iconfont icon-shuqing"})],1):t._e()],1),e("v-uni-view",{staticClass:"info-wrap"},[e("v-uni-view",{staticClass:"goods-name",class:[{"using-hidden":"single"==t.config.nameLineMode},{"multi-hidden":"multiple"==t.config.nameLineMode}]},[t._v(t._s(i.goods_name))]),e("v-uni-view",{staticClass:"lineheight-clear"},[e("v-uni-view",{staticClass:"discount-price"},[e("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.showPrice(i)).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.showPrice(i)).toFixed(2).split(".")[1]))])],1),i.member_price&&i.member_price==t.showPrice(i)?e("v-uni-view",{staticClass:"member-price-tag"},[e("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/VIP.png"),mode:"widthFix"}})],1):1==i.promotion_type?e("v-uni-view",{staticClass:"member-price-tag"},[e("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/discount.png"),mode:"widthFix"}})],1):t._e(),t.showMarketPrice(i)?e("v-uni-view",{staticClass:"delete-price color-tip price-font"},[e("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),e("v-uni-text",[t._v(t._s(t.showMarketPrice(i)))])],1):t._e()],1),e("v-uni-view",{staticClass:"pro-info"},[e("v-uni-view",{staticClass:"block-wrap"},[i.sale_show?e("v-uni-view",{staticClass:"sale color-tip"},[t._v("已售"+t._s(i.sale_num)+t._s(i.unit?i.unit:"件"))]):t._e()],1),t.config.control&&0==i.is_virtual?e("v-uni-view",{staticClass:"cart-action-wrap"},["icon-cart"==t.config.style?e("v-uni-view",{staticClass:"cart shopping-cart-btn iconfont icon-gouwuche click-wrap",style:{color:"diy"==t.config.theme?t.config.textColor:"",borderColor:"diy"==t.config.theme?t.config.textColor:""},attrs:{id:"goods-"+i.id},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,i,e)}}},[e("v-uni-view",{staticClass:"click-event"})],1):"icon-add"==t.config.style?e("v-uni-view",{staticClass:"cart plus-sign-btn iconfont icon-add1 click-wrap",style:{color:"diy"==t.config.theme?t.config.textColor:"",borderColor:"diy"==t.config.theme?t.config.textColor:""},attrs:{id:"goods-"+i.id},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,i,e)}}},[e("v-uni-view",{staticClass:"click-event"})],1):"button"==t.config.style?e("v-uni-view",{staticClass:"cart buy-btn click-wrap",style:{backgroundColor:"diy"==t.config.theme?t.config.bgColor:"",color:"diy"==t.config.theme?t.config.textColor:"",fontWeight:"diy"==t.config.theme?t.config.fontWeight?"bold":"normal":"",padding:"diy"==t.config.theme?"12rpx "+2*t.config.padding+"rpx":""},attrs:{id:"goods-"+i.id},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,i,e)}}},[t._v(t._s(t.config.text)),e("v-uni-view",{staticClass:"click-event"})],1):"icon-diy"==t.config.style?e("v-uni-view",{staticClass:"icon-diy click-wrap",style:{color:"diy"==t.config.theme?t.config.textColor:""},attrs:{id:"goods-"+i.id},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.$refs.goodsSkuIndex.addCart(t.config.cartEvent,i,e)}}},[e("v-uni-view",{staticClass:"click-event"}),e("diy-icon",{attrs:{icon:t.config.iconDiy.icon,value:t.config.iconDiy.style?t.config.iconDiy.style:null}})],1):t._e()],1):t._e()],1)],1)],1)})),1),0==t.goodsList.length&&t.emptyShow?e("v-uni-view",[e("ns-empty",{attrs:{text:"暂无商品"}})],1):t._e()],1)],2),e("ns-goods-sku-index",{ref:"goodsSkuIndex",on:{addCart:function(i){arguments[0]=i=t.$handleEvent(i),t.addCart.apply(void 0,arguments)}}}),e("uni-drawer",{staticClass:"screen-wrap",attrs:{visible:t.showScreen,mode:"right"},on:{close:function(i){arguments[0]=i=t.$handleEvent(i),t.showScreen=!1}}},[e("v-uni-view",{staticClass:"title color-tip"},[t._v("筛选")]),e("v-uni-scroll-view",{attrs:{"scroll-y":!0}},[e("v-uni-view",{staticClass:"item-wrap"},[e("v-uni-view",{staticClass:"label"},[e("v-uni-text",[t._v("是否包邮")])],1),e("v-uni-view",{staticClass:"list"},[e("v-uni-view",{staticClass:"list-wrap",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.isFreeShipping=!t.isFreeShipping}}},[e("v-uni-text",{class:{"color-base-text":t.isFreeShipping}},[t._v("包邮")])],1)],1)],1),e("v-uni-view",{staticClass:"item-wrap"},[e("v-uni-view",{staticClass:"label"},[e("v-uni-text",[t._v("价格区间(元)")])],1),e("v-uni-view",{staticClass:"price-wrap"},[e("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"最低价"},model:{value:t.minPrice,callback:function(i){t.minPrice=i},expression:"minPrice"}}),e("v-uni-view",{staticClass:"h-line"}),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"最高价"},model:{value:t.maxPrice,callback:function(i){t.maxPrice=i},expression:"maxPrice"}})],1)],1),t.brandList.length>0?e("v-uni-view",{staticClass:"item-wrap"},[e("v-uni-view",{staticClass:"label"},[e("v-uni-text",[t._v("品牌")])],1),e("v-uni-view",{staticClass:"list"},t._l(t.brandList,(function(i,o){return e("v-uni-view",{key:o,staticClass:"list-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.brandId==i.brand_id?t.brandId=0:t.brandId=i.brand_id}}},[e("v-uni-text",{class:{"color-base-text":i.brand_id==t.brandId}},[t._v(t._s(i.brand_name))])],1)})),1)],1):t._e(),e("v-uni-view",{staticClass:"category-list-wrap"},[e("v-uni-text",{staticClass:"first"},[t._v("全部分类")]),e("v-uni-view",{staticClass:"class-box"},[e("v-uni-view",{staticClass:"list-wrap",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.selectedCategory("")}}},[e("v-uni-text",{class:{selected:!t.categoryId,"color-base-text":!t.categoryId}},[t._v("全部")])],1),t._l(t.categoryList,(function(i,o){return e("v-uni-view",{key:o,staticClass:"list-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectedCategory(i.category_id)}}},[e("v-uni-text",{class:{selected:i.category_id==t.categoryId,"color-base-text":i.category_id==t.categoryId}},[t._v(t._s(i.category_name))])],1)}))],2)],1)],1),e("v-uni-view",{staticClass:"footer",class:{"safe-area":t.isIphoneX}},[e("v-uni-button",{staticClass:"footer-box",attrs:{type:"default"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.resetData.apply(void 0,arguments)}}},[t._v("重置")]),e("v-uni-button",{staticClass:"footer-box1",attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.screenData.apply(void 0,arguments)}}},[t._v("确定")])],1)],1),e("hover-nav"),e("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},"55e2":function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){}));var o=function(){var t=this,i=t.$createElement,e=t._self._c||i;return t.text?e("v-uni-view",{staticClass:"uni-tag",class:[!0===t.disabled||"true"===t.disabled?"uni-tag--disabled":"",!0===t.inverted||"true"===t.inverted?"uni-tag--inverted":"",!0===t.circle||"true"===t.circle?"uni-tag--circle":"",!0===t.mark||"true"===t.mark?"uni-tag--mark":"","uni-tag--"+t.size,"uni-tag--"+t.type],on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onClick()}}},[t._v(t._s(t.text))]):t._e()},n=[]},"618c":function(t,i,e){"use strict";var o=e("85b3"),n=e.n(o);n.a},"70be":function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,".uni-drawer[data-v-03428368]{display:block;position:fixed;top:0;left:0;right:0;bottom:0;overflow:hidden;visibility:hidden;z-index:999;height:100%}.uni-drawer.uni-drawer--right .uni-drawer__content[data-v-03428368]{left:auto;right:0;-webkit-transform:translatex(100%);transform:translatex(100%)}.uni-drawer.uni-drawer--visible[data-v-03428368]{visibility:visible}.uni-drawer.uni-drawer--visible .uni-drawer__content[data-v-03428368]{-webkit-transform:translatex(0);transform:translatex(0)}.uni-drawer.uni-drawer--visible .uni-drawer__mask[data-v-03428368]{display:block;opacity:1}.uni-drawer__mask[data-v-03428368]{display:block;opacity:0;position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.4);transition:opacity .3s}.uni-drawer__content[data-v-03428368]{display:block;position:absolute;top:0;left:0;width:61.8%;height:100%;background:#fff;transition:all .3s ease-out;-webkit-transform:translatex(-100%);transform:translatex(-100%)}.safe-area[data-v-03428368]{padding-bottom:%?68?%;padding-top:%?44?%;box-sizing:border-box}",""]),t.exports=i},7158:function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.head-wrap[data-v-7545544c]{background:#fff;position:fixed;width:100%;left:0;z-index:1}.head-wrap .search-wrap[data-v-7545544c]{flex:0.5;padding:%?30?% %?30?% 0;font-size:%?24?%;display:flex;align-items:center}.head-wrap .search-wrap .iconfont[data-v-7545544c]{margin-left:%?16?%;font-size:%?36?%}.head-wrap .search-wrap .input-wrap[data-v-7545544c]{flex:1;display:flex;justify-content:space-between;align-items:center;background:#f8f8f8;height:%?64?%;padding-left:%?10?%;border-radius:%?70?%}.head-wrap .search-wrap .input-wrap uni-input[data-v-7545544c]{width:90%;background:#f8f8f8;font-size:%?24?%;height:100%;padding:0 %?25?% 0 %?40?%;line-height:%?50?%;border-radius:%?40?%}.head-wrap .search-wrap .input-wrap uni-text[data-v-7545544c]{font-size:%?32?%;color:#909399;width:%?80?%;text-align:center;margin-right:%?20?%}.head-wrap .search-wrap .category-wrap[data-v-7545544c],\r\n.head-wrap .search-wrap .list-style[data-v-7545544c]{display:flex;justify-content:center;align-items:center}.head-wrap .search-wrap .category-wrap .iconfont[data-v-7545544c],\r\n.head-wrap .search-wrap .list-style .iconfont[data-v-7545544c]{font-size:%?50?%;color:#909399}.head-wrap .search-wrap .category-wrap uni-text[data-v-7545544c],\r\n.head-wrap .search-wrap .list-style uni-text[data-v-7545544c]{display:block;margin-top:%?60?%}.head-wrap .sort-wrap[data-v-7545544c]{display:flex;padding:%?10?% %?20?% %?10?% 0}.head-wrap .sort-wrap > uni-view[data-v-7545544c]{flex:1;text-align:center;font-size:%?28?%;height:%?60?%;line-height:%?60?%;font-weight:700}.head-wrap .sort-wrap .comprehensive-wrap[data-v-7545544c]{display:flex;justify-content:center;align-items:center}.head-wrap .sort-wrap .comprehensive-wrap .iconfont-wrap[data-v-7545544c]{display:inline-block;margin-left:%?10?%;width:%?40?%}.head-wrap .sort-wrap .comprehensive-wrap .iconfont-wrap .iconfont[data-v-7545544c]{font-size:%?32?%;line-height:1;margin-bottom:%?5?%}.head-wrap .sort-wrap .price-wrap[data-v-7545544c]{display:flex;justify-content:center;align-items:center}.head-wrap .sort-wrap .price-wrap .iconfont-wrap[data-v-7545544c]{display:flex;justify-content:center;align-items:center;flex-direction:column;width:%?40?%}.head-wrap .sort-wrap .price-wrap .iconfont-wrap .iconfont[data-v-7545544c]{position:relative;float:left;font-size:%?32?%;line-height:1;height:%?20?%;color:#909399}.head-wrap .sort-wrap .price-wrap .iconfont-wrap .iconfont.asc[data-v-7545544c]{top:%?-2?%}.head-wrap .sort-wrap .price-wrap .iconfont-wrap .iconfont.desc[data-v-7545544c]{top:%?-6?%}.head-wrap .sort-wrap .screen-wrap[data-v-7545544c]{display:flex;justify-content:center;align-items:center}.head-wrap .sort-wrap .screen-wrap .iconfont-wrap[data-v-7545544c]{display:inline-block;margin-left:%?10?%;width:%?40?%}.head-wrap .sort-wrap .screen-wrap .iconfont-wrap .iconfont[data-v-7545544c]{font-size:%?32?%;line-height:1}.category-list-wrap[data-v-7545544c]{height:100%}.category-list-wrap .class-box[data-v-7545544c]{display:flex;flex-wrap:wrap;padding:0 %?20?%}.category-list-wrap .class-box uni-view[data-v-7545544c]{width:calc((100% - %?60?%) / 3);font-size:%?22?%;margin-right:%?20?%;height:%?60?%;line-height:%?60?%;text-align:center;margin-bottom:%?12?%;flex-shrink:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;background:#f5f5f5;border-radius:%?5?%}.category-list-wrap .class-box uni-view[data-v-7545544c]:nth-of-type(3n){margin-right:0}.category-list-wrap .first[data-v-7545544c]{font-size:%?24?%;display:block;padding:%?20?%}.category-list-wrap .second[data-v-7545544c]{border-bottom:%?2?% solid #eee;padding:%?20?%;display:block;font-size:%?24?%}.category-list-wrap .third[data-v-7545544c]{padding:0 %?20?% %?20?%;overflow:hidden;font-size:%?24?%}.category-list-wrap .third > uni-view[data-v-7545544c]{display:inline-block;margin-right:%?20?%;margin-top:%?20?%}.category-list-wrap .third .uni-tag[data-v-7545544c]{padding:0 %?20?%}.screen-wrap .title[data-v-7545544c]{font-size:%?24?%;padding:%?20?%;background:#f6f4f5}.screen-wrap uni-scroll-view[data-v-7545544c]{height:85%}.screen-wrap uni-scroll-view .item-wrap[data-v-7545544c]{border-bottom:1px solid #f0f0f0}.screen-wrap uni-scroll-view .item-wrap .label[data-v-7545544c]{font-size:%?24?%;padding:%?20?%}.screen-wrap uni-scroll-view .item-wrap .label uni-view[data-v-7545544c]{display:inline-block;font-size:%?60?%;height:%?40?%;vertical-align:middle;line-height:%?40?%}.screen-wrap uni-scroll-view .item-wrap .list[data-v-7545544c]{display:flex;flex-wrap:wrap;padding:0 %?20?%}.screen-wrap uni-scroll-view .item-wrap .list .list-wrap[data-v-7545544c]{padding:0 %?14?%;font-size:%?22?%;margin-right:%?20?%;height:%?60?%;line-height:%?60?%;text-align:center;margin-bottom:%?12?%;flex-shrink:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;background:#f5f5f5;border-radius:%?5?%}.screen-wrap uni-scroll-view .item-wrap .price-wrap[data-v-7545544c]{display:flex;justify-content:center;align-items:center;padding:%?20?%}.screen-wrap uni-scroll-view .item-wrap .price-wrap uni-input[data-v-7545544c]{flex:1;background:#f5f5f5;height:%?52?%;width:%?182?%;line-height:%?50?%;font-size:%?22?%;border-radius:%?50?%;text-align:center}.screen-wrap uni-scroll-view .item-wrap .price-wrap uni-input[data-v-7545544c]:first-child{margin-right:%?10?%}.screen-wrap uni-scroll-view .item-wrap .price-wrap uni-input[data-v-7545544c]:last-child{margin-left:%?10?%}.screen-wrap .footer[data-v-7545544c]{height:%?90?%;display:flex;justify-content:center;align-items:flex-start;display:flex;bottom:0;width:100%}.screen-wrap .footer .footer-box[data-v-7545544c]{border-top-right-radius:0;border-bottom-right-radius:0;margin:0;width:40%}.screen-wrap .footer .footer-box1[data-v-7545544c]{border-top-left-radius:0;border-bottom-left-radius:0;margin:0;width:40%}.safe-area[data-v-7545544c]{bottom:%?68?%!important}.empty[data-v-7545544c]{margin-top:%?100?%}.buy-num[data-v-7545544c]{font-size:%?20?%}.icon[data-v-7545544c]{width:%?34?%;height:%?30?%}.list-style-new[data-v-7545544c]{display:flex;align-items:center}.list-style-new .line[data-v-7545544c]{width:%?4?%;height:%?28?%;background-color:#e3e3e3;margin-right:%?60?%}.h-line[data-v-7545544c]{width:%?37?%;height:%?2?%;background-color:#909399}.goods-list.single-column[data-v-7545544c]{display:none}.goods-list.single-column.show[data-v-7545544c]{display:block}.goods-list.single-column .goods-item[data-v-7545544c]{padding:%?26?%;background:#fff;margin:%?20?% %?30?%;border-radius:%?10?%;display:flex;position:relative}.goods-list.single-column .goods-item .goods-img[data-v-7545544c]{position:relative;width:%?200?%;height:%?200?%;border-radius:%?10?%;margin-right:%?20?%;overflow:hidden}.goods-list.single-column .goods-item .goods-img uni-image[data-v-7545544c]{width:%?200?%;height:%?200?%}.goods-list.single-column .goods-item .goods-tag[data-v-7545544c]{color:#fff;line-height:1;padding:%?8?% %?12?%;position:absolute;border-top-left-radius:%?10?%;border-bottom-right-radius:%?10?%;top:0;left:0;font-size:%?22?%}.goods-list.single-column .goods-item .goods-tag-img[data-v-7545544c]{position:absolute;border-top-left-radius:%?10?%;width:%?80?%;height:%?80?%;top:%?26?%;left:%?26?%;z-index:5;overflow:hidden}.goods-list.single-column .goods-item .goods-tag-img uni-image[data-v-7545544c]{width:100%;height:100%}.goods-list.single-column .goods-item .info-wrap[data-v-7545544c]{flex:1;display:flex;flex-direction:column}.goods-list.single-column .goods-item .name-wrap[data-v-7545544c]{flex:1}.goods-list.single-column .goods-item .goods-name[data-v-7545544c]{font-size:%?28?%;line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.goods-list.single-column .goods-item .introduction[data-v-7545544c]{line-height:1;margin-top:%?10?%}.goods-list.single-column .goods-item .discount-price[data-v-7545544c]{display:inline-block;font-weight:700;line-height:1;margin-top:%?16?%}.goods-list.single-column .goods-item .discount-price .unit[data-v-7545544c]{margin-right:%?6?%;color:var(--price-color)}.goods-list.single-column .goods-item .discount-price .price[data-v-7545544c]{color:var(--price-color)}.goods-list.single-column .goods-item .pro-info[data-v-7545544c]{display:flex;margin-top:auto;align-items:center;position:relative}.goods-list.single-column .goods-item .pro-info .delete-price[data-v-7545544c]{text-decoration:line-through;font-size:%?24?%!important;flex:1}.goods-list.single-column .goods-item .pro-info .delete-price .unit[data-v-7545544c]{margin-right:0}.goods-list.single-column .goods-item .pro-info .block-wrap[data-v-7545544c]{flex:1;line-height:1;margin-right:%?20?%}.goods-list.single-column .goods-item .pro-info .block-wrap .sale[data-v-7545544c]{font-size:%?24?%!important}.goods-list.single-column .goods-item .member-price-tag[data-v-7545544c]{display:inline-block;width:%?60?%;line-height:1;margin-left:%?6?%}.goods-list.single-column .goods-item .member-price-tag uni-image[data-v-7545544c]{width:100%;display:flex;max-height:%?30?%}.goods-list.single-column .goods-item .sell-out[data-v-7545544c]{position:absolute;z-index:1;width:100%;height:100%;top:0;left:0;display:flex;align-items:center;justify-content:center;background:rgba(0,0,0,.5)}.goods-list.single-column .goods-item .sell-out uni-text[data-v-7545544c]{color:#fff;font-size:%?150?%}.goods-list.double-column[data-v-7545544c]{display:none;margin:0 %?24?%;padding-top:%?20?%;position:relative;flex-wrap:wrap;justify-content:space-between}.goods-list.double-column.show[data-v-7545544c]{display:flex}.goods-list.double-column .goods-item[data-v-7545544c]{display:flex;flex-direction:column;width:calc(50% - %?10?%);border-radius:%?10?%;overflow:hidden;background-color:#fff}.goods-list.double-column .goods-item[data-v-7545544c]:nth-child(2n + 2){margin-right:0}.goods-list.double-column .goods-item .goods-img[data-v-7545544c]{position:relative;overflow:hidden;padding-top:100%;border-top-left-radius:%?10?%;border-top-right-radius:%?10?%}.goods-list.double-column .goods-item .goods-img uni-image[data-v-7545544c]{width:100%;position:absolute!important;top:50%;left:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.goods-list.double-column .goods-item .goods-tag[data-v-7545544c]{color:#fff;line-height:1;padding:%?8?% %?16?%;position:absolute;border-bottom-right-radius:%?10?%;top:0;left:0;font-size:%?22?%}.goods-list.double-column .goods-item .goods-tag-img[data-v-7545544c]{position:absolute;border-top-left-radius:%?10?%;width:%?80?%;height:%?80?%;top:0;left:0;z-index:5;overflow:hidden}.goods-list.double-column .goods-item .goods-tag-img uni-image[data-v-7545544c]{width:100%;height:100%}.goods-list.double-column .goods-item .info-wrap[data-v-7545544c]{padding:%?20?%;display:flex;flex-direction:column;flex:1}.goods-list.double-column .goods-item .goods-name[data-v-7545544c]{font-size:%?28?%;line-height:1.3;margin-top:%?20?%}.goods-list.double-column .goods-item .lineheight-clear[data-v-7545544c]{margin-top:%?16?%}.goods-list.double-column .goods-item .discount-price[data-v-7545544c]{display:inline-block;font-weight:700;line-height:1}.goods-list.double-column .goods-item .discount-price .unit[data-v-7545544c]{margin-right:%?6?%;color:var(--price-color)}.goods-list.double-column .goods-item .discount-price .price[data-v-7545544c]{color:var(--price-color)}.goods-list.double-column .goods-item .pro-info[data-v-7545544c]{display:flex;margin-top:auto;align-items:center}.goods-list.double-column .goods-item .pro-info .block-wrap[data-v-7545544c]{flex:1;line-height:1;margin-right:%?20?%}.goods-list.double-column .goods-item .pro-info .block-wrap .sale[data-v-7545544c]{font-size:%?24?%!important}.goods-list.double-column .goods-item .delete-price .unit[data-v-7545544c]{margin-right:%?6?%}.goods-list.double-column .goods-item .delete-price uni-text[data-v-7545544c]{line-height:1;font-size:%?24?%!important}.goods-list.double-column .goods-item .member-price-tag[data-v-7545544c]{display:inline-block;width:%?60?%;line-height:1;margin-left:%?6?%}.goods-list.double-column .goods-item .member-price-tag uni-image[data-v-7545544c]{width:100%}.goods-list.double-column .goods-item .sell-out[data-v-7545544c]{position:absolute;z-index:1;width:100%;height:100%;top:0;left:0;display:flex;align-items:center;justify-content:center;background:rgba(0,0,0,.5)}.goods-list.double-column .goods-item .sell-out uni-text[data-v-7545544c]{color:#fff;font-size:%?250?%}.cart-action-wrap[data-v-7545544c]{position:relative}.cart-action-wrap .shopping-cart-btn[data-v-7545544c]{font-size:%?36?%;border:%?2?% solid var(--base-color);border-radius:50%;padding:%?10?%;color:var(--base-color);width:%?36?%;height:%?36?%;text-align:center;line-height:%?36?%}.cart-action-wrap .plus-sign-btn[data-v-7545544c]{font-size:%?36?%;border:%?2?% solid var(--base-color);border-radius:50%;padding:%?10?%;color:var(--base-color);width:%?36?%;height:%?36?%;text-align:center;line-height:%?36?%}.cart-action-wrap .buy-btn[data-v-7545544c]{background-color:var(--base-color);color:var(--btn-text-color);border-radius:%?50?%;font-size:%?24?%;padding:%?12?% %?30?%;line-height:1}.cart-action-wrap .icon-diy[data-v-7545544c]{font-size:%?80?%}',""]),t.exports=i},"769b":function(t,i,e){"use strict";e.r(i);var o=e("5589"),n=e("080d");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);e("618c"),e("2d4a");var s=e("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"7545544c",null,!1,o["a"],void 0);i["default"]=r.exports},"7f15":function(t,i,e){"use strict";var o=e("c570"),n=e.n(o);n.a},"7fbd":function(t,i,e){"use strict";e("6a54");var o=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("e966"),e("c223"),e("64aa");var n=o(e("132d")),a={name:"ns-goods-sku-index",components:{nsGoodsSku:n.default},data:function(){return{timeout:{},isRepeat:!1,goodsDetail:{}}},created:function(){},methods:{addCart:function(t,i,e){if(this.storeToken)return"detail"==t||i.is_virtual?(this.$util.redirectTo("/pages/goods/detail",{goods_id:i.goods_id}),!1):void(i.goods_spec_format?this.multiSpecificationGoods(i):this.singleSpecificationGoods(i,e));this.$refs.login.open("/pages/index/index")},singleSpecificationGoods:function(t,i){var e=this,o=this.cartList["goods_"+t.goods_id]&&this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id]?this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id]:null,n=o?o.num:0,a=o&&o.cart_id?"/api/cart/edit":"/api/cart/add",s=t.min_buy>0?t.min_buy:1,r=n>=s?n:s,c=r;o&&o.cart_id&&(c+=t.min_buy>0?t.min_buy:1);var d=o?o.cart_id:0;if(c>parseInt(t.stock))this.$util.showToast({title:"商品库存不足"});else if(t.is_limit&&t.max_buy&&c>parseInt(t.max_buy))this.$util.showToast({title:"该商品每人限购".concat(t.max_buy).concat(t.unit||"件")});else{if(o)this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id].num=c;else{this.cartList["goods_"+t.goods_id]||(this.cartList["goods_"+t.goods_id]={});var l=t.discount_price;t.member_price>0&&Number(t.member_price)<=Number(t.discount_price)&&(l=t.member_price),this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id]={cart_id:d,goods_id:t.goods_id,sku_id:t.sku_id,num:c,discount_price:l}}this.isRepeat||(this.isRepeat=!0,this.$emit("addCart",i.currentTarget.id),this.$api.sendRequest({url:a,data:{cart_id:d,sku_id:t.sku_id,num:c},success:function(i){e.isRepeat=!1,0==i.code?(0==d&&(e.cartList["goods_"+t.goods_id]["sku_"+t.sku_id].cart_id=i.data),e.$util.showToast({title:"商品添加购物车成功"}),e.$store.commit("setCartChange"),e.$store.dispatch("cartCalculate"),e.$emit("cartListChange",e.cartList)):e.$util.showToast({title:i.message})}}))}},multiSpecificationGoods:function(t){var i=this;this.$api.sendRequest({url:"/api/goodssku/getInfoForCategory",data:{sku_id:t.sku_id},success:function(t){if(t.code>=0){var e=t.data;e.unit=e.unit||"件",e.sku_images?e.sku_images=e.sku_images.split(","):e.sku_images=[],e.goods_spec_format&&e.goods_image&&(e.goods_image=e.goods_image.split(","),e.sku_images=e.goods_image.concat(e.sku_images)),e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format)),e.goods_spec_format&&(e.goods_spec_format=JSON.parse(e.goods_spec_format)),1==e.promotion_type&&(e.discountTimeMachine=i.$util.countDown(e.end_time-t.timestamp)),1==e.promotion_type&&e.discountTimeMachine?e.member_price>0&&Number(e.member_price)<=Number(e.discount_price)?e.show_price=e.member_price:e.show_price=e.discount_price:e.member_price>0?e.show_price=e.member_price:e.show_price=e.price,i.goodsDetail=e,i.$nextTick((function(){i.$refs.goodsSku&&i.$refs.goodsSku.show("join_cart",(function(t){var e=i.cartList["goods_"+t.goods_id],o=null;e&&e["sku_"+t.sku_id]&&(o=e["sku_"+t.sku_id]),o?i.cartList["goods_"+t.goods_id]["sku_"+t.sku_id].num=t.num:(i.cartList["goods_"+t.goods_id]||(i.cartList["goods_"+t.goods_id]={}),i.cartList["goods_"+t.goods_id]["sku_"+t.sku_id]={cart_id:t.cart_id,goods_id:t.goods_id,sku_id:t.sku_id,num:t.num,discount_price:t.discount_price}),i.$store.dispatch("cartCalculate"),i.$emit("cartListChange",i.cartList),setTimeout((function(){i.$store.commit("setCartChange")}),100)}))}))}}})},refreshGoodsSkuDetail:function(t){this.goodsDetail=Object.assign({},this.goodsDetail,t)}}};i.default=a},"85b3":function(t,i,e){var o=e("7158");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("48666354",o,!0,{sourceMap:!1,shadowMode:!1})},9243:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o={name:"UniTag",props:{type:{type:String,default:"default"},size:{type:String,default:"normal"},text:{type:String,default:""},disabled:{type:[String,Boolean],default:!1},inverted:{type:[String,Boolean],default:!1},circle:{type:[String,Boolean],default:!1},mark:{type:[String,Boolean],default:!1}},methods:{onClick:function(){!0!==this.disabled&&"true"!==this.disabled&&this.$emit("click")}}};i.default=o},a243:function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,"[data-v-7545544c] .uni-tag--primary.uni-tag--inverted{background-color:#f5f5f5!important}[data-v-7545544c] .sku-layer .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{max-height:unset!important}",""]),t.exports=i},a4ac:function(t,i,e){"use strict";e("6a54");var o=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n=o(e("b7c7"));e("bf0f"),e("2797"),e("5ef2"),e("4100"),e("fd3c"),e("c223"),e("64aa"),e("e838"),e("5c47"),e("bd06"),e("c9b5"),e("ab80");var a={data:function(){return{listStyle:"",loadingType:"loading",orderType:"",priceOrder:"desc",categoryList:[],goodsList:[],order:"",sort:"desc",showScreen:!1,keyword:"",categoryId:0,minPrice:"",maxPrice:"",isFreeShipping:!1,isIphoneX:!1,coupon:0,emptyShow:!1,isList:!0,share_title:"",count:0,category_title:"",coupon_name:"",listHeight:[],listPosition:[],debounce:null,brandId:0,brandList:[],config:{fontWeight:!1,padding:0,cartEvent:"detail",text:"购买",textColor:"#FFFFFF",theme:"default",aroundRadius:25,control:!0,bgColor:"#FF6A00",style:"button",iconDiy:{iconType:"icon",icon:"",style:{fontSize:"60",iconBgColor:[],iconBgColorDeg:0,iconBgImg:"",bgRadius:0,iconColor:["#000000"],iconColorDeg:0}}}}},onLoad:function(t){var i=this;if(this.categoryId=t.category_id||0,this.keyword=t.keyword||"",this.coupon=t.coupon||0,this.goods_id_arr=t.goods_id_arr||0,this.brandId=t.brand_id||0,this.loadCategoryList(),this.getBrandList(),this.isIphoneX=this.$util.uniappIsIPhoneX(),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var e=decodeURIComponent(t.scene);e=e.split("&"),e.length&&e.forEach((function(t){-1!=t.indexOf("sku_id")&&(i.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}uni.onWindowResize((function(t){i.debounce&&clearTimeout(i.debounce),i.waterfallflow(0)}))},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member"))},onShareAppMessage:function(t){var i="搜索到"+this.count+"件“"+this.keyword+this.category_title+this.coupon_name+"”相关的优质商品",e=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),o=e.path;return{title:i,path:o,success:function(t){},fail:function(t){}}},onShareTimeline:function(){var t="搜索到"+this.count+"件“"+this.keyword+this.category_title+this.coupon_name+"”相关的优质商品",i=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),e=i.query;return{title:t,query:e,imageUrl:""}},methods:{couponInfo:function(t){var i=this;return new Promise((function(e){i.$api.sendRequest({url:"/coupon/api/coupon/typeinfo",data:{coupon_type_id:t},success:function(t){t.code>=0&&e(t.data.coupon_name)}})}))},share_select:function(t,i){return new Promise((function(e){t.forEach((function(t){t.category_id==i&&e(t.category_name),t.child_list&&t.child_list.length>0&&t.child_list.forEach((function(t){t.category_id==i&&e(t.category_name),t.child_list&&t.child_list.length>0&&t.child_list.forEach((function(t){t.category_id==i&&e(t.category_name)}))}))}))}))},loadCategoryList:function(){var t=this;this.$api.sendRequest({url:"/api/goodscategory/tree",success:function(i){null!=i.data&&(t.categoryList=i.data)}})},getGoodsList:function(t){var i=this;this.$api.sendRequest({url:"/api/goodssku/page",data:{page:t.num,page_size:t.size,keyword:this.keyword,category_id:this.categoryId,brand_id:this.brandId,min_price:this.minPrice,max_price:this.maxPrice,is_free_shipping:this.isFreeShipping?1:0,order:this.order,sort:this.sort,coupon:this.coupon,goods_id_arr:this.goods_id_arr},success:function(e){var o=[],n=e.message;0==e.code&&e.data?(i.count=e.data.count,0==e.data.page_count&&(i.emptyShow=!0),o=e.data.list,o=o.map((function(t){return t.id=i.genNonDuplicate(),t}))):i.$util.showToast({title:n}),i.category_title="",i.coupon_name="",e.data.config&&(i.config=e.data.config),i.categoryId&&i.share_select(i.categoryList,i.categoryId).then((function(t){i.category_title=t})),i.coupon&&i.couponInfo(i.coupon).then((function(t){i.coupon_name=t})),t.endSuccess(o.length),1==t.num&&(i.goodsList=[]),i.goodsList=i.goodsList.concat(o),i.$refs.loadingCover&&i.$refs.loadingCover.hide(),i.waterfallflow(10*(t.num-1))},fail:function(e){t.endErr(),i.$refs.loadingCover&&i.$refs.loadingCover.hide()}})},changeListStyle:function(){this.isList=!this.isList,this.waterfallflow(0)},sortTabClick:function(t){if("sale_num"==t)this.order="sale_num",this.sort="desc";else if("discount_price"==t)this.order="discount_price",this.sort="desc";else{if("screen"==t)return void(this.showScreen=!0);this.order="",this.sort=""}this.orderType===t&&"discount_price"!==t||(this.orderType=t,"discount_price"===t?(this.priceOrder="asc"===this.priceOrder?"desc":"asc",this.sort=this.priceOrder):this.priceOrder="",this.emptyShow=!1,this.goodsList=[],this.$refs.mescroll.refresh())},toDetail:function(t){this.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})},search:function(){this.emptyShow=!1,this.goodsList=[],this.$refs.mescroll.refresh()},selectedCategory:function(t){this.categoryId=t},screenData:function(){if(""!=this.minPrice||""!=this.maxPrice){if(!Number(this.maxPrice)&&this.maxPrice)return void this.$util.showToast({title:"请输入最高价"});if(Number(this.minPrice)<0||Number(this.maxPrice)<0)return void this.$util.showToast({title:"筛选价格不能小于0"});if(""!=this.minPrice&&Number(this.minPrice)>Number(this.maxPrice)&&this.maxPrice)return void this.$util.showToast({title:"最低价不能大于最高价"});if(""!=this.maxPrice&&Number(this.maxPrice)<Number(this.minPrice))return void this.$util.showToast({title:"最高价不能小于最低价"})}this.emptyShow=!1,this.goodsList=[],this.$refs.mescroll.refresh(),this.showScreen=!1},resetData:function(){this.categoryId=0,this.minPrice="",this.maxPrice="",this.isFreeShipping=!1},goodsImg:function(t){var i=t.split(",");return i[0]?this.$util.img(i[0],{size:"mid"}):this.$util.getDefaultImage().goods},imgError:function(t){this.goodsList[t].goods_image=this.$util.getDefaultImage().goods},showPrice:function(t){var i=t.discount_price;return t.member_price&&parseFloat(t.member_price)<parseFloat(i)&&(i=t.member_price),i},showMarketPrice:function(t){if(t.market_price_show){var i=this.showPrice(t);if(t.market_price>0)return t.market_price;if(parseFloat(t.price)>parseFloat(i))return t.price}return""},goodsTag:function(t){return t.label_name||""},waterfallflow:function(){var t=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.isList||this.$nextTick((function(){setTimeout((function(){var e=[],o=[];0!=i&&(e=t.listHeight,o=t.listPosition);var a=uni.createSelectorQuery().in(t);a.selectAll(".double-column .goods-item").boundingClientRect((function(a){for(var s=i;s<a.length;s++)if(s<2){var r={};r.top=uni.upx2px(20)+"px",r.left=s%2==0?a[s].width*s+"px":a[s].width*s+s%2*uni.upx2px(30)+"px",o[s]=r,e[s]=a[s].height+uni.upx2px(20)}else(function(){var t=Math.min.apply(Math,(0,n.default)(e)),i=e.findIndex((function(i){return i===t})),r={};r.top=t+uni.upx2px(20)+"px",r.left=o[i].left,o[s]=r,e[i]+=a[s].height+uni.upx2px(20)})();t.listHeight=e,t.listPosition=o})).exec()}),50)}))},getBrandList:function(){var t=this;this.$api.sendRequest({url:"/api/goodsbrand/page",data:{page:1,page_size:0},success:function(i){if(0==i.code&&i.data){var e=i.data;t.brandList=e.list}}})},addCart:function(t){},genNonDuplicate:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;return Number(Math.random().toString().substr(3,t)+Date.now()).toString(36)}}};i.default=a},a68f:function(t,i,e){"use strict";e.r(i);var o=e("acc8"),n=e("29ac");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);e("c225");var s=e("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"1839a53e",null,!1,o["a"],void 0);i["default"]=r.exports},a725:function(t,i,e){"use strict";var o=e("ac2a"),n=e.n(o);n.a},ac2a:function(t,i,e){var o=e("f714");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("1a69ffc2",o,!0,{sourceMap:!1,shadowMode:!1})},acc8:function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){}));var o=function(){var t=this.$createElement,i=this._self._c||t;return i("v-uni-view",{staticClass:"diy-icon",style:this.iconBgStyle},[i("v-uni-text",{staticClass:"js-icon",class:this.iconClass,style:this.iconStyle})],1)},n=[]},b791:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o={name:"diy-icon",props:{icon:{type:String,default:""},value:{type:Object,default:function(){return null}}},computed:{iconClass:function(){var t=" "+this.icon;return this.value&&this.value.iconColor.length>1&&(t+=" gradient"),t},iconBgStyle:function(){if(!this.value)return{};var t={"border-radius":this.value.bgRadius+"%",background:""};return this.value.iconBgImg&&(t["background"]+="url("+this.$util.img(this.value.iconBgImg)+") no-repeat bottom / contain"),this.value.iconBgColor.length&&(t.background&&(t.background+=","),1==this.value.iconBgColor.length?t.background+=this.value.iconBgColor[0]:t["background"]+="linear-gradient("+this.value.iconBgColorDeg+"deg, "+this.value.iconBgColor.join(",")+")"),this.$util.objToStyle(t)},iconStyle:function(){if(!this.value)return{};var t={"font-size":this.value.fontSize+"%"};return 1==this.value.iconColor.length?t.color=this.value.iconColor[0]:t["background"]="linear-gradient("+this.value.iconColorDeg+"deg, "+this.value.iconColor.join(",")+")",this.$util.objToStyle(t)}}};i.default=o},b7c7:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(t){return(0,o.default)(t)||(0,n.default)(t)||(0,a.default)(t)||(0,s.default)()};var o=r(e("4733")),n=r(e("d14d")),a=r(e("5d6b")),s=r(e("30f7"));function r(t){return t&&t.__esModule?t:{default:t}}},bcef:function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){return o}));var o={nsLogin:e("2910").default,nsGoodsSku:e("132d").default},n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{staticClass:"goods-sku"},[e("ns-login",{ref:"login"}),t.goodsDetail.goods_id?e("ns-goods-sku",{ref:"goodsSku",attrs:{"goods-id":t.goodsDetail.goods_id,"goods-detail":t.goodsDetail,"max-buy":t.goodsDetail.max_buy,"min-buy":t.goodsDetail.min_buy},on:{refresh:function(i){arguments[0]=i=t.$handleEvent(i),t.refreshGoodsSkuDetail.apply(void 0,arguments)}}}):t._e()],1)},a=[]},bd5e:function(t,i,e){"use strict";e.r(i);var o=e("1f645"),n=e("cb98");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);e("7f15");var s=e("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"03428368",null,!1,o["a"],void 0);i["default"]=r.exports},c1f1:function(t,i,e){"use strict";e.r(i);var o=e("fa1d"),n=e("015d");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);e("a725");var s=e("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"c1934e78",null,!1,o["a"],void 0);i["default"]=r.exports},c225:function(t,i,e){"use strict";var o=e("3488"),n=e.n(o);n.a},c4cd:function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.diy-icon[data-v-1839a53e]{width:100%;height:100%;font-size:100%;color:#000;display:flex;align-items:center;justify-content:center}.diy-icon .js-icon[data-v-1839a53e]{font-size:50%;line-height:1;padding:%?1?%}.diy-icon .js-icon.gradient[data-v-1839a53e]{-webkit-background-clip:text!important;-webkit-text-fill-color:transparent}',""]),t.exports=i},c570:function(t,i,e){var o=e("70be");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("4be6a133",o,!0,{sourceMap:!1,shadowMode:!1})},cb98:function(t,i,e){"use strict";e.r(i);var o=e("e043"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a},cd53:function(t,i,e){"use strict";e.r(i);var o=e("7fbd"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a},d14d:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},e("01a2"),e("e39c"),e("bf0f"),e("844d"),e("18f7"),e("de6c"),e("08eb")},d498:function(t,i,e){"use strict";e("6a54");var o=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n=o(e("bd5e")),a=o(e("43db")),s=o(e("0c16")),r=o(e("a4ac")),c={components:{uniDrawer:n.default,uniTag:a.default,nsGoodsSkuIndex:s.default},data:function(){return{}},mixins:[r.default]};i.default=c},d66c:function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,".uni-tag[data-v-65fbc3dd]{box-sizing:border-box;padding:0 %?32?%;height:%?60?%;line-height:calc(%?60?% - 2px);font-size:%?28?%;display:inline-flex;align-items:center;color:#333;border-radius:%?6?%;background-color:#f8f8f8;border:1px solid #f8f8f8}.uni-tag--circle[data-v-65fbc3dd]{border-radius:%?30?%}.uni-tag--mark[data-v-65fbc3dd]{border-radius:0 %?30?% %?30?% 0}.uni-tag--disabled[data-v-65fbc3dd]{opacity:.5}.uni-tag--small[data-v-65fbc3dd]{height:%?40?%;padding:0 %?16?%;line-height:calc(%?40?% - 2px);font-size:%?24?%}.uni-tag--primary[data-v-65fbc3dd]{color:#fff;background-color:#007aff;border:1px solid #007aff}.uni-tag--primary.uni-tag--inverted[data-v-65fbc3dd]{color:#007aff;background-color:#fff;border:1px solid #007aff}.uni-tag--success[data-v-65fbc3dd]{color:#fff;background-color:#4cd964;border:1px solid #4cd964}.uni-tag--success.uni-tag--inverted[data-v-65fbc3dd]{color:#4cd964;background-color:#fff;border:1px solid #4cd964}.uni-tag--warning[data-v-65fbc3dd]{color:#fff;background-color:#f0ad4e;border:1px solid #f0ad4e}.uni-tag--warning.uni-tag--inverted[data-v-65fbc3dd]{color:#f0ad4e;background-color:#fff;border:1px solid #f0ad4e}.uni-tag--error[data-v-65fbc3dd]{color:#fff;background-color:#dd524d;border:1px solid #dd524d}.uni-tag--error.uni-tag--inverted[data-v-65fbc3dd]{color:#dd524d;background-color:#fff;border:1px solid #dd524d}.uni-tag--inverted[data-v-65fbc3dd]{color:#333;background-color:#fff;border:1px solid #f8f8f8}",""]),t.exports=i},e043:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o={name:"UniDrawer",props:{visible:{type:Boolean,default:!1},mode:{type:String,default:""},mask:{type:Boolean,default:!0}},data:function(){return{visibleSync:!1,showDrawer:!1,rightMode:!1,closeTimer:null,watchTimer:null,isIphoneX:!1}},watch:{visible:function(t){var i=this;clearTimeout(this.watchTimer),setTimeout((function(){i.showDrawer=t}),100),this.visibleSync&&clearTimeout(this.closeTimer),t?this.visibleSync=t:this.watchTimer=setTimeout((function(){i.visibleSync=t}),300)}},created:function(){var t=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.visibleSync=this.visible,setTimeout((function(){t.showDrawer=t.visible}),100),this.rightMode="right"===this.mode},methods:{close:function(){var t=this;this.showDrawer=!1,this.closeTimer=setTimeout((function(){t.visibleSync=!1,t.$emit("close")}),200)},moveHandle:function(){}}};i.default=o},f714:function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=i},fa1d:function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){}));var o=function(){var t=this,i=t.$createElement,e=t._self._c||i;return 1==t.pageCount||t.need?e("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?e("v-uni-view",{staticClass:"btn-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/pages/index/index")}}},[e("v-uni-text",{staticClass:"iconfont icon-shouye1"}),e("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?e("v-uni-view",{staticClass:"btn-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/pages/member/index")}}},[e("v-uni-text",{staticClass:"iconfont icon-yonghu"}),e("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?e("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[e("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):e("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[e("v-uni-view",[t._v("快捷")]),e("v-uni-view",[t._v("导航")])],1)],1):t._e()},n=[]}}]);