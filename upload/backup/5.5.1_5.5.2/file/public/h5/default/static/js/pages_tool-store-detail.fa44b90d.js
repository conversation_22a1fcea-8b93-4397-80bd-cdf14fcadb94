(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-store-detail"],{1146:function(t,e,a){"use strict";a.r(e);var i=a("f1a6"),o=a("9db2");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("cac1");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"03d7f81f",null,!1,i["a"],void 0);e["default"]=r.exports},"233e":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("aa9c"),a("5c47"),a("a1c1"),a("473f"),a("bf0f"),a("2797"),a("64aa"),a("dc8a");var o=i(a("a29e")),s={data:function(){return{storeId:0,latitude:null,longitude:null,covers:[],store:null,swiperCurrent:1,swiperHeight:""}},onLoad:function(t){this.storeId=t.store_id||0,this.location?(this.latitude=this.location.latitude,this.longitude=this.location.longitude):1==this.mapConfig.wap_is_open&&this.$util.getLocation(),this.getInfo()},watch:{location:function(t){t&&(this.latitude=t.latitude,this.longitude=t.longitude,this.getInfo())}},methods:{phoneCall:function(){uni.makePhoneCall({phoneNumber:this.store.telphone})},getInfo:function(){var t=this,e={store_id:this.storeId};this.latitude&&this.longitude&&(e.latitude=this.latitude,e.longitude=this.longitude),this.$api.sendRequest({url:"/api/store/info",data:e,success:function(e){if(e.data){t.store=e.data||{full_address:"",address:"",store_images:[]},t.covers.push({id:1,latitude:t.store.latitude,longitude:t.store.longitude,iconPath:t.$util.img("public/uniapp/store/map_icon.png"),height:25}),t.store.show_address=t.store.full_address.replace(/,/g," ")+" "+t.store.address,t.handleStoreImage()}else t.$util.showToast({title:"门店不存在"}),setTimeout((function(){t.$util.redirectTo("/pages_tool/store/list",{},"redirectTo")}),2e3)}})},handleStoreImage:function(){this.store.store_images||(this.store.store_images=[]),this.store.store_images=this.store.store_images.reduce((function(t,e){return t.images||(t.images=[]),t.images&&t.images.push(e.pic_path),t.spec||(t.spec=[]),t.spec&&t.spec.push(e.pic_spec),t}),{});var t="";this.store.store_images.spec&&this.store.store_images.spec.forEach((function(e,a){"string"==typeof e&&(e=e.split("*")),uni.getSystemInfo({success:function(t){var a=e[0]/t.windowWidth;e[0]=e[0]/a,e[1]=e[1]/a}}),(!t||t>e[1])&&(t=e[1])})),this.swiperHeight=Number(t)+"px",Object.keys(this.store.store_images).length||(this.store.store_images={},this.store.store_images.images=[this.$util.img("public/static/img/default_img/square.png")],this.store.store_images.spec=["350*350"],this.swiperHeight="380px")},swiperChange:function(t){this.swiperCurrent=t.detail.current+1},mapRoute:function(){o.default.openMap(Number(this.store.latitude),Number(this.store.longitude),this.store.store_name,"gcj02")},swiperImageError:function(){this.store.store_images.images=this.$util.img("public/static/img/default_img/square.png")}}};e.default=s},"3df4":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=52.35987755982988,o=3.141592653589793,s=6378245,n=.006693421622965943;function r(t,e){var a=2*t-100+3*e+.2*e*e+.1*t*e+.2*Math.sqrt(Math.abs(t));return a+=2*(20*Math.sin(6*t*o)+20*Math.sin(2*t*o))/3,a+=2*(20*Math.sin(e*o)+40*Math.sin(e/3*o))/3,a+=2*(160*Math.sin(e/12*o)+320*Math.sin(e*o/30))/3,a}function d(t,e){var a=300+t+2*e+.1*t*t+.1*t*e+.1*Math.sqrt(Math.abs(t));return a+=2*(20*Math.sin(6*t*o)+20*Math.sin(2*t*o))/3,a+=2*(20*Math.sin(t*o)+40*Math.sin(t/3*o))/3,a+=2*(150*Math.sin(t/12*o)+300*Math.sin(t/30*o))/3,a}function l(t,e){return t<72.004||t>137.8347||e<.8293||e>55.8271||!1}var c={bd09togcj02:function(t,e){var a=52.35987755982988,i=t-.0065,o=e-.006,s=Math.sqrt(i*i+o*o)-2e-5*Math.sin(o*a),n=Math.atan2(o,i)-3e-6*Math.cos(i*a),r=s*Math.cos(n),d=s*Math.sin(n);return[r,d]},gcj02tobd09:function(t,e){var a=Math.sqrt(t*t+e*e)+2e-5*Math.sin(e*i),o=Math.atan2(e,t)+3e-6*Math.cos(t*i),s=a*Math.cos(o)+.0065,n=a*Math.sin(o)+.006;return[s,n]},wgs84togcj02:function(t,e){if(l(t,e))return[t,e];var a=r(t-105,e-35),i=d(t-105,e-35),c=e/180*o,u=Math.sin(c);u=1-n*u*u;var f=Math.sqrt(u);a=180*a/(s*(1-n)/(u*f)*o),i=180*i/(s/f*Math.cos(c)*o);var h=e+a,g=t+i;return[g,h]},gcj02towgs84:function(t,e){if(l(t,e))return[t,e];var a=r(t-105,e-35),i=d(t-105,e-35),c=e/180*o,u=Math.sin(c);u=1-n*u*u;var f=Math.sqrt(u);return a=180*a/(s*(1-n)/(u*f)*o),i=180*i/(s/f*Math.cos(c)*o),mglat=e+a,mglng=t+i,[2*t-mglng,2*e-mglat]}};e.default=c},7854:function(t,e,a){"use strict";a.r(e);var i=a("8ba8"),o=a("f48d");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=r.exports},"8ba8":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},"9db2":function(t,e,a){"use strict";a.r(e);var i=a("233e"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},a29e:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c223");var o=i(a("3df4"));function s(t,e,a){uni.openLocation({latitude:t,longitude:e,name:a,fail:function(t){uni.showModal({content:"打开地图失败，请稍后重试"})}})}function n(t,e,a){switch(a){case"gcj02":return[t,e];case"bd09":return o.default.bd09togcj02(t,e);case"wgs84":return o.default.wgs84togcj02(t,e);default:return[t,e]}}var r={openMap:function(t,e,a){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"gcj02",o=n(e,t,i);s(o[1],o[0],a)}};e.default=r},a55f:function(t,e,a){var i=a("c343");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("7303a57c",i,!0,{sourceMap:!1,shadowMode:!1})},c343:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */uni-page-body[data-v-03d7f81f]{background-color:#f5f6fa}body.?%PAGE?%[data-v-03d7f81f]{background-color:#f5f6fa}.store-detail .detail-head[data-v-03d7f81f]{position:relative;width:100%;height:%?300?%;background-color:#fff}.store-detail .detail-head[data-v-03d7f81f]::after{content:"";position:absolute;left:0;right:0;bottom:0;height:%?112?%;background-image:linear-gradient(transparent 10%,#f5f6fa)}.store-detail .detail-head .swiper[data-v-03d7f81f]{width:100%;height:100%}.store-detail .detail-head .swiper .item[data-v-03d7f81f]{width:100%;height:100%}.store-detail .detail-head .swiper uni-image[data-v-03d7f81f]{width:100%;height:100%}.store-detail .detail-head .img-indicator-dots[data-v-03d7f81f]{position:absolute;z-index:5;bottom:%?60?%;right:%?40?%;background:hsla(0,0%,39.2%,.4);color:#fff;font-size:%?24?%;line-height:%?40?%;border-radius:%?20?%;padding:0 %?20?%}.store-detail .detail-content[data-v-03d7f81f]{position:relative;background-color:#fff;margin:%?-30?% %?30?% %?30?%;padding:0 %?24?%;border-radius:%?18?%;z-index:9}.store-detail .detail-content .content-item[data-v-03d7f81f]{display:flex;align-items:center;justify-content:space-between;border-bottom:%?2?% solid #ededed;padding:%?24?% 0}.store-detail .detail-content .content-item[data-v-03d7f81f]:last-of-type{border-bottom:0}.store-detail .detail-content .store-name[data-v-03d7f81f]{font-size:%?32?%;font-weight:700;line-height:1.5;padding:%?6?% 0}.store-detail .detail-content .store-state[data-v-03d7f81f]{padding:%?8?% %?10?%;font-size:%?24?%;border:%?2?% solid #66ad95;color:#66ad95;border-radius:%?4?%;line-height:1}.store-detail .detail-content .store-state.warning[data-v-03d7f81f]{color:red;border-color:red}.store-detail .detail-content .store-time-wrap[data-v-03d7f81f]{flex-direction:column;align-items:baseline}.store-detail .detail-content .store-time-wrap .store-time[data-v-03d7f81f]{font-size:%?24?%;color:#606266}.store-detail .detail-content .store-time-wrap .close-desc[data-v-03d7f81f]{color:red;font-size:%?24?%}.store-detail .detail-content .store-time-wrap .tag-wrap[data-v-03d7f81f]{margin-top:%?20?%;display:flex;flex-wrap:wrap}.store-detail .detail-content .store-time-wrap .tag-wrap .tag-item[data-v-03d7f81f]{padding:%?8?% %?10?%;margin-right:%?10?%;color:#6f7dad;background:#f4f5fa;border-radius:%?6?%;line-height:1;font-size:%?24?%}.store-detail .detail-content .telphone-wrap[data-v-03d7f81f]{padding:%?26?% 0}.store-detail .detail-content .telphone-wrap .telphone[data-v-03d7f81f]{font-weight:700;color:var(--base-color);font-size:%?26?%}.store-detail .detail-content .telphone-wrap > .iconfont[data-v-03d7f81f]{width:%?60?%;height:%?48?%;line-height:%?48?%;text-align:center;background-color:#f4f5fa;border-radius:%?6?%}.store-detail .detail-content .address-wrap .address-name[data-v-03d7f81f]{width:%?520?%;line-height:1.5;color:#606266;font-size:%?24?%}.store-detail .detail-content .address-wrap .address-location[data-v-03d7f81f]{margin-top:%?12?%;display:flex;align-items:center;font-size:%?24?%;color:#999ca7}.store-detail .detail-content .address-wrap .address-location .icondiy[data-v-03d7f81f]{font-size:%?24?%;margin-right:%?4?%}.store-detail .detail-content .address-wrap > .icondiy[data-v-03d7f81f]{width:%?60?%;height:%?48?%;line-height:%?48?%;text-align:center;background-color:#f4f5fa;border-radius:%?6?%}.store-detail .detail-map[data-v-03d7f81f]{background-color:#fff;margin:0 %?30?% %?30?%;border-radius:%?18?%;margin-bottom:calc(constant(safe-area-inset-bottom) + %?170?%);margin-bottom:calc(env(safe-area-inset-bottom) + %?170?%)}.store-detail .detail-map .map-head[data-v-03d7f81f]{padding-left:%?24?%;height:%?100?%;line-height:%?100?%;font-size:%?32?%;font-weight:700}.store-detail .detail-map .map-body[data-v-03d7f81f]{width:100%;height:%?460?%}.store-detail .store-action-fill[data-v-03d7f81f]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?170?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?170?%)}.store-detail .store-action[data-v-03d7f81f]{position:fixed;background-color:#fff;bottom:0;right:0;left:0;display:flex;justify-content:center;padding:%?30?% 0}.store-detail .store-action uni-button[data-v-03d7f81f]{width:%?406?%;color:#fff;font-size:%?30?%;border-radius:%?50?%}',""]),t.exports=e},cac1:function(t,e,a){"use strict";var i=a("a55f"),o=a.n(i);o.a},cc1b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var a=function a(o){o.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",a),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",a)}})}}}};e.default=o},f1a6:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={pageMeta:a("7854").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),t.store?a("v-uni-view",{staticClass:"store-detail"},[a("v-uni-view",{staticClass:"detail-head",style:{height:t.swiperHeight}},[a("v-uni-swiper",{staticClass:"swiper",attrs:{autoplay:"true",interval:"4000",circular:"true"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},t._l(t.store.store_images.images,(function(e,i){return a("v-uni-swiper-item",{key:i,attrs:{"item-id":"store_id_"+i}},[a("v-uni-view",{staticClass:"item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewMedia(i)}}},[a("v-uni-image",{attrs:{src:t.$util.img(e),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperImageError(i)}}})],1)],1)})),1),t.store.store_images.images&&t.store.store_images.images.length?a("v-uni-view",{staticClass:"img-indicator-dots"},[a("v-uni-text",[t._v(t._s(t.swiperCurrent))]),a("v-uni-text",[t._v("/"+t._s(t.store.store_images.images.length))])],1):t._e()],1),a("v-uni-view",{staticClass:"detail-content"},[a("v-uni-view",{staticClass:"content-item"},[a("v-uni-view",{staticClass:"store-name multi-hidden"},[t._v(t._s(t.store.store_name))]),a("v-uni-view",{staticClass:"store-state",class:1==t.store.is_frozen.is_frozen||0==t.store.status?"warning":""},[t._v(t._s((1==t.store.is_frozen.is_frozen?"已停业":0==t.store.status&&"休息中")||1==t.store.status&&"营业中"||"--"))])],1),t.store.open_date||t.store.is_default||t.store.is_pickup||t.store.is_o2o||t.store.is_express?a("v-uni-view",{staticClass:"content-item store-time-wrap"},[0==t.store.status&&t.store.close_desc?a("v-uni-view",{staticClass:"close-desc"},[t._v(t._s(t.store.close_desc))]):t._e(),t.store.open_date?a("v-uni-view",{staticClass:"store-time"},[t._v(t._s(t.store.open_date))]):t._e(),t.store.is_default||t.store.is_pickup||t.store.is_o2o||t.store.is_express?a("v-uni-view",{staticClass:"tag-wrap"},[1==t.store.is_default?a("v-uni-text",{staticClass:"tag-item"},[t._v("总店")]):t._e(),1==t.store.is_pickup?a("v-uni-text",{staticClass:"tag-item"},[t._v("门店自提")]):t._e(),1==t.store.is_o2o?a("v-uni-text",{staticClass:"tag-item"},[t._v("同城配送")]):t._e(),1==t.store.is_express?a("v-uni-text",{staticClass:"tag-item"},[t._v("物流配送")]):t._e()],1):t._e()],1):t._e(),t.store.show_address||t.store.distance?a("v-uni-view",{staticClass:"content-item address-wrap"},[a("v-uni-view",{staticClass:"address-box"},[t.store.show_address?a("v-uni-view",{staticClass:"address-name"},[t._v(t._s(t.store.show_address))]):t._e(),t.store.distance?a("v-uni-view",{staticClass:"address-location"},[a("v-uni-text",{staticClass:"icondiy icon-system-weizhi"}),a("v-uni-text",[t._v("距您当前位置"+t._s(t.store.distance)+"km")])],1):t._e()],1),a("v-uni-text",{staticClass:"icondiy icon-daohang",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.mapRoute()}}})],1):t._e(),t.store.telphone?a("v-uni-view",{staticClass:"content-item telphone-wrap"},[t.store.telphone?a("v-uni-text",{staticClass:"telphone"},[t._v(t._s(t.store.telphone))]):t._e(),a("v-uni-text",{staticClass:"iconfont icon-dianhua",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.phoneCall.apply(void 0,arguments)}}})],1):t._e()],1),a("v-uni-view",{staticClass:"detail-map"},[a("v-uni-view",{staticClass:"map-head"},[t._v("门店地图")]),a("v-uni-map",{staticClass:"map-body",attrs:{latitude:t.store.latitude,longitude:t.store.longitude,markers:t.covers}})],1)],1):t._e()],1)},s=[]},f48d:function(t,e,a){"use strict";a.r(e);var i=a("cc1b"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a}}]);