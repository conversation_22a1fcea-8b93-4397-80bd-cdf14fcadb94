(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-list"],{"00ba":function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"c",(function(){return i})),o.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?o("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?o("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),o("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),o("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),o("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},i=[]},"11ac":function(t,e,o){"use strict";var a=o("9c4f"),i=o.n(a);i.a},"1bc0":function(t,e,o){"use strict";var a=o("a761"),i=o.n(a);i.a},2817:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={methods:{orderDelete:function(t,e){var o=this;uni.showModal({title:"提示",content:"您确定要删除该订单吗？",success:function(a){a.confirm&&o.$api.sendRequest({url:"/api/order/delete",data:{order_id:t},success:function(t){t.code>=0?(o.$util.showToast({title:"删除订单成功"}),"function"==typeof e&&e()):o.$util.showToast({title:"删除订单失败，"+t.message,duration:2e3})}})}})},orderPay:function(t){var e=this;0==t.adjust_money?this.pay():uni.showModal({title:"提示",content:"商家已将支付金额调整为"+t.pay_money+"元，是否继续支付？",success:function(t){t.confirm&&e.pay()}})},pay:function(){var t=this;this.$api.sendRequest({url:"/api/order/pay",data:{order_ids:this.orderData.order_id},success:function(e){e.code>=0?t.$refs.choosePaymentPopup.getPayInfo(e.data):t.$util.showToast({title:e.message})}})},orderClose:function(t,e){var o=this;uni.showModal({title:"提示",content:"您确定要关闭该订单吗？",success:function(a){a.confirm&&o.$api.sendRequest({url:"/api/order/close",data:{order_id:t},success:function(t){t.code>=0?"function"==typeof e&&e():o.$util.showToast({title:"关闭失败，"+t.message,duration:2e3})}})}})},orderDelivery:function(t,e){var o=this;uni.showModal({title:"提示",content:"您确定已经收到货物了吗？",success:function(a){a.confirm&&o.$api.sendRequest({url:"/api/order/takedelivery",data:{order_id:t.order_id},success:function(t){o.$util.showToast({title:t.message}),"function"==typeof e&&e()}})}})},orderVirtualDelivery:function(t,e){var o=this;uni.showModal({title:"提示",content:"您确定要进行收货吗？",success:function(a){a.confirm&&o.$api.sendRequest({url:"/api/order/membervirtualtakedelivery",data:{order_id:t.order_id},success:function(t){o.$util.showToast({title:t.message}),"function"==typeof e&&e()}})}})}}};e.default=a},3037:function(t,e,o){"use strict";o("6a54");var a=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("c223"),o("bf0f"),o("2797"),o("e838"),o("dd2b"),o("aa9c");var i=a(o("2817")),r=a(o("b6f2")),s={data:function(){return{scrollInto:"",orderStatus:"all",statusList:[],orderList:[],contentText:{},mergePayOrder:[],isIphoneX:!1,evaluateConfig:{evaluate_audit:1,evaluate_show:0,evaluate_status:1},orderData:{},payMoney:0,payMoneyMerge:0,order_id:0,searchText:"",pageText:"",payConfig:null,isTradeManaged:!1}},components:{payment:r.default},mixins:[i.default],onLoad:function(t){t.status&&(this.orderStatus=t.status),t.order_id&&(this.order_id=t.order_id)},onShow:function(){var t=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getEvaluateConfig(),this.getOrderStatus(),this.storeToken?this.$refs.mescroll&&this.$refs.mescroll.refresh():this.$nextTick((function(){t.$refs.loadingCover&&t.$refs.loadingCover.hide()})),this.$refs.choosePaymentPopup&&this.$refs.choosePaymentPopup.pageShow()},onUnload:function(){!this.storeToken&&this.$refs.login&&this.$refs.login.cancelCompleteInfo()},methods:{toLogin:function(){this.$refs.login.open()},ontabtap:function(t){var e=t.target.dataset.current||t.currentTarget.dataset.current;this.orderStatus=this.statusList[e].status,""==this.orderStatus&&(this.mergePayOrder=[]),this.$refs.loadingCover.show(),this.$refs.mescroll.refresh()},getListData:function(t){var e=this;this.$api.sendRequest({url:"/api/order/lists",data:{page:t.num,page_size:t.size,order_status:this.orderStatus,order_id:this.order_id,searchText:this.pageText},success:function(o){var a=[],i=o.message,r=0;0==o.code&&o.data?(a=o.data.list,r=o.data.auto_close,e.payConfig=o.data.pay_config,e.isTradeManaged=o.data.is_trade_managed):e.$util.showToast({title:i}),t.endSuccess(a.length),1==t.num&&(e.orderList=[],e.order_id=0),e.orderList=e.orderList.concat(a);var s=Date.parse(new Date)/1e3;e.orderList.forEach((function(t){t.discountTimeMachine=e.$util.countDown(t.create_time+r-s),t.order_goods.forEach((function(t){if(t.sku_spec_format)try{t.sku_spec_format=JSON.parse(t.sku_spec_format)}catch(e){t.sku_spec_format=t.sku_spec_format}else t.sku_spec_format=[]}))})),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(o){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},getOrderStatus:function(){this.statusList=[{status:"all",name:this.$lang("all"),id:"status_0"},{status:"waitpay",name:this.$lang("waitPay"),id:"status_1"},{status:"waitsend",name:this.$lang("readyDelivery"),id:"status_2"},{status:"waitconfirm",name:this.$lang("waitDelivery"),id:"status_3"},{status:"wait_use",name:this.$lang("waitUse"),id:"status_4"}]},operation:function(t,e){var o=this;this.status;switch(t){case"orderDelete":this.orderDelete(e.order_id,(function(){o.$refs.mescroll.refresh()}));break;case"orderPay":this.orderData=e,this.payMoney=parseFloat(e.pay_money),this.orderPay(e);break;case"orderClose":this.orderClose(e.order_id,(function(){o.$refs.mescroll.refresh()}));break;case"memberTakeDelivery":this.orderData=e,this.orderData.pay_config={},this.orderData.pay_config.mch_id=this.payConfig.mch_id,this.orderData.is_trade_managed=this.isTradeManaged,this.orderDelivery(this.orderData,(function(){o.$refs.mescroll.refresh()}));break;case"trace":this.$util.redirectTo("/pages_tool/order/logistics",{order_id:e.order_id});break;case"memberOrderEvaluation":this.$util.redirectTo("/pages_tool/order/evaluate",{order_id:e.order_id});break;case"memberVirtualTakeDelivery":this.orderData=e,this.orderData.pay_config={},this.orderData.pay_config.mch_id=this.payConfig.mch_id,this.orderData.is_trade_managed=this.isTradeManaged,this.orderVirtualDelivery(this.orderData,(function(){o.$refs.mescroll.refresh()}));break;case"orderOfflinePay":this.orderData=e,this.$util.redirectTo("/pages_tool/pay/offlinepay",{outTradeNo:this.orderData.out_trade_no});break}},orderDetail:function(t){this.$util.redirectTo("/pages/order/detail",{order_id:t.order_id})},selectOrder:function(t,e){-1!=this.$util.inArray(t,this.mergePayOrder)?(this.mergePayOrder.splice(this.$util.inArray(t,this.mergePayOrder),1),this.payMoneyMerge-=parseFloat(e)):(this.payMoneyMerge+=parseFloat(e),this.mergePayOrder.push(t))},imageError:function(t,e){this.orderList[t].order_goods[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},getEvaluateConfig:function(){var t=this;this.$api.sendRequest({url:"/api/goodsevaluate/config",success:function(e){if(0==e.code){var o=e.data;t.evaluateConfig=o}}})},search:function(){this.pageText=this.searchText,this.$refs.mescroll.refresh()}},computed:{mpOrderList:function(){if(this.orderList[this.status])return this.orderList[this.status].list||[]}},watch:{storeToken:function(t,e){t&&this.$refs.mescroll.refresh()}}};e.default=s},7854:function(t,e,o){"use strict";o.r(e);var a=o("8ba8"),i=o("f48d");for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);var s=o("828b"),n=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=n.exports},"7cd3":function(t,e,o){var a=o("80bd");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("74134114",a,!0,{sourceMap:!1,shadowMode:!1})},"80bd":function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.order-container[data-v-67608daa]{width:100vw}.align-right[data-v-67608daa]{text-align:right}.cate-search[data-v-67608daa]{width:calc(100% - %?48?%);background:#fff;padding:%?10?% %?24?% 0 %?24?%;padding-top:%?30?%;position:relative;z-index:998}.cate-search uni-input[data-v-67608daa]{font-size:%?28?%;height:%?76?%;padding:0 %?25?% 0 %?30?%;line-height:%?60?%;width:calc(100% - %?120?%);background:none}.cate-search uni-text[data-v-67608daa]{font-size:%?32?%;color:#909399;width:%?120?%;text-align:center}.cate-search .search-box[data-v-67608daa]{width:100%;background:#f8f8f8;display:flex;justify-content:center;align-items:center;border-radius:%?100?%}.order-nav[data-v-67608daa]{width:100vw;height:%?80?%;flex-direction:row;white-space:nowrap;background:#fff;display:flex;position:fixed;left:0;z-index:998;justify-content:space-around;border-radius:0 0 %?24?% %?24?%}.order-nav .uni-tab-item[data-v-67608daa]{width:%?120?%;text-align:center}.order-nav .uni-tab-item-title[data-v-67608daa]{display:inline-block;height:%?80?%;line-height:%?80?%;border-bottom:1px solid #fff;flex-wrap:nowrap;white-space:nowrap;text-align:center;font-size:%?30?%;position:relative}.order-nav .uni-tab-item-title-active[data-v-67608daa]::after{content:" ";display:block;position:absolute;left:0;bottom:0;width:100%;height:%?6?%;background:linear-gradient(270deg,var(--base-color-light-9),var(--base-color))}.order-nav[data-v-67608daa] ::-webkit-scrollbar{width:0;height:0;color:transparent}.order-item[data-v-67608daa]{margin:%?20?% %?24?%;border-radius:%?12?%;background:#fff;position:relative}.order-item .order-header[data-v-67608daa]{display:flex;align-items:center;position:relative;padding:%?20?% %?24?% %?26?% %?24?%}.order-item .order-header.waitpay[data-v-67608daa]{padding-left:%?70?%}.order-item .order-header.waitpay .icon-yuan_checked[data-v-67608daa],\r\n.order-item .order-header.waitpay .icon-yuan_checkbox[data-v-67608daa]{font-size:%?32?%;position:absolute;top:48%;left:%?20?%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.order-item .order-header.waitpay .icon-yuan_checkbox[data-v-67608daa]{color:#909399}.order-item .order-header .icon-dianpu[data-v-67608daa]{display:inline-block;line-height:1;margin-right:%?12?%;font-size:%?28?%}.order-item .order-header .order-no[data-v-67608daa]{font-size:%?26?%}.order-item .order-header .order-type-name[data-v-67608daa]{font-size:%?26?%;margin-left:%?10?%}.order-item .order-header .status-name[data-v-67608daa]{flex:1;text-align:right;font-size:%?26?%;font-weight:600}.order-item .order-body .goods-wrap[data-v-67608daa]{display:flex;position:relative;padding:0 %?24?% %?30?% %?24?%}.order-item .order-body .goods-wrap[data-v-67608daa]:last-of-type{margin-bottom:0}.order-item .order-body .goods-wrap .goods-img[data-v-67608daa]{width:%?160?%;height:%?160?%;margin-right:%?20?%}.order-item .order-body .goods-wrap .goods-img uni-image[data-v-67608daa]{width:100%;height:100%;border-radius:%?10?%}.order-item .order-body .goods-wrap .goods-info[data-v-67608daa]{flex:1;position:relative;max-width:calc(100% - %?180?%);display:flex;flex-direction:column}.order-item .order-body .goods-wrap .goods-info .pro-info[data-v-67608daa]{flex:1}.order-item .order-body .goods-wrap .goods-info .goods-name[data-v-67608daa]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;color:#303133}.order-item .order-body .goods-wrap .goods-info .goods-sub-section[data-v-67608daa]{width:100%;line-height:1.3;display:flex;margin-top:%?14?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-67608daa]{font-size:%?24?%;color:var(--price-color);flex:1;font-weight:700}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-num[data-v-67608daa]{font-size:%?24?%;color:#909399;flex:1;text-align:right;line-height:1}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-num .iconfont[data-v-67608daa]{font-size:%?24?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-type[data-v-67608daa]{font-size:%?24?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-67608daa]{font-size:%?24?%;margin-right:%?2?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-67608daa]{flex:1;line-height:1.3;display:flex;flex-direction:column}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-67608daa]:last-of-type{text-align:right}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view:last-of-type .iconfont[data-v-67608daa]{line-height:1;font-size:%?24?%}.order-item .order-body .goods-wrap .goods-info .goods-action[data-v-67608daa]{text-align:right}.order-item .order-body .goods-wrap .goods-info .goods-action .action-btn[data-v-67608daa]{line-height:1;padding:%?14?% %?20?%;color:#303133;display:inline-block;border-radius:%?10?%;background:#fff;border:%?2?% solid #999;font-size:%?24?%;margin-left:%?10?%}.order-item .order-body .multi-order-goods[data-v-67608daa]{width:calc(100vw - %?96?%);white-space:nowrap;margin:0 %?24?% %?30?% %?24?%!important;position:relative}.order-item .order-body .multi-order-goods .scroll-view[data-v-67608daa]{width:100%}.order-item .order-body .multi-order-goods .goods-wrap[data-v-67608daa]{padding:0}.order-item .order-body .multi-order-goods .goods-img[data-v-67608daa]{min-width:%?160?%}.order-item .order-body .multi-order-goods .shade[data-v-67608daa]{position:absolute;z-index:5;height:100%;width:%?44?%;right:0;top:0}.order-item .order-body .multi-order-goods .shade uni-image[data-v-67608daa]{width:100%;height:100%}.order-item .order-footer .order-base-info .total[data-v-67608daa]{padding:%?20?%;font-size:%?24?%;background:hsla(0,0%,97.3%,.5);display:flex;margin:0 %?24?%}.order-item .order-footer .order-base-info .total > uni-text[data-v-67608daa]{flex:1;line-height:1;margin-left:%?10?%}.order-item .order-footer .order-base-info .order-type[data-v-67608daa]{padding-top:%?20?%;flex:0.5}.order-item .order-footer .order-base-info .order-type > uni-text[data-v-67608daa]{line-height:1}.order-item .order-footer .order-action[data-v-67608daa]{text-align:right;padding:%?30?% %?24?%;position:relative}.order-item .order-footer .order-action .order-time[data-v-67608daa]{position:absolute;top:%?35?%;left:%?30?%;display:flex;align-items:center;font-size:10px;color:#b5b6b9}.order-item .order-footer .order-action .order-time uni-image[data-v-67608daa]{width:%?26?%;height:%?26?%;margin-right:%?6?%}.order-item .order-footer .order-action .action-btn[data-v-67608daa]{line-height:1;padding:%?20?% %?26?%;color:#333;display:inline-block;border-radius:%?10?%;background:#fff;border:%?2?% solid #999;font-size:%?24?%;margin-left:%?10?%}[data-v-67608daa] #action-date .uni-countdown .uni-countdown__number{border:none!important;padding:0!important;margin:0!important}.order-batch-action[data-v-67608daa]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?100?%;background:#fff;box-shadow:0 0 10px rgba(0,0,0,.1);text-align:right}.order-batch-action.bottom-safe-area[data-v-67608daa]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.order-batch-action .action-btn[data-v-67608daa]{height:%?68?%;line-height:%?68?%;background:#fff;padding:0 %?40?%;display:inline-block;text-align:center;margin:%?16?% %?20?% %?16?% 0;border-radius:%?10?%;border:1px solid #fff}.order-batch-action .action-btn.white[data-v-67608daa]{height:%?68?%;line-height:%?68?%;color:#333;border:1px solid #999;background:#fff}.sku[data-v-67608daa]{display:flex;line-height:1;margin-top:%?10?%}.goods-spec[data-v-67608daa]{color:#909399;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}',""]),t.exports=e},8710:function(t,e,o){"use strict";o.r(e);var a=o("3037"),i=o.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"8ba8":function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"c",(function(){return i})),o.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},i=[]},"9c4f":function(t,e,o){var a=o("bb37");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("1a51f0e1",a,!0,{sourceMap:!1,shadowMode:!1})},a64b:function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,"[data-v-67608daa] .uni-page{overflow:hidden}[data-v-67608daa] .mescroll-upwarp{padding-bottom:%?100?%}.no-login[data-v-67608daa]{display:flex;flex-direction:column;align-items:center}.no-login .button[data-v-67608daa]{width:%?300?%;margin-top:%?100?%;height:%?70?%;line-height:%?70?%!important;font-size:%?28?%;border-radius:%?50?%}",""]),t.exports=e},a761:function(t,e,o){var a=o("a64b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=o("967d").default;i("7f524b8d",a,!0,{sourceMap:!1,shadowMode:!1})},bb37:function(t,e,o){var a=o("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-countdown[data-v-45a7f114]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-45a7f114]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?24?%}.uni-countdown__splitor.day[data-v-45a7f114]{line-height:%?50?%}.uni-countdown__number[data-v-45a7f114]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;border:%?2?% solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=e},bcaa:function(t,e,o){"use strict";o.r(e);var a=o("f963"),i=o("8710");for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);o("f32d"),o("1bc0");var s=o("828b"),n=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"67608daa",null,!1,a["a"],void 0);e["default"]=n.exports},cc1b:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("5ef2"),o("64aa"),o("5c47"),o("a1c1"),o("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},i={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var o=function o(i){i.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",o),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",o)}})}}}};e.default=i},e12a:function(t,e,o){"use strict";o.r(e);var a=o("00ba"),i=o("ea5a");for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);o("11ac");var s=o("828b"),n=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"45a7f114",null,!1,a["a"],void 0);e["default"]=n.exports},ea5a:function(t,e,o){"use strict";o.r(e);var a=o("f9fd"),i=o.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},f32d:function(t,e,o){"use strict";var a=o("7cd3"),i=o.n(a);i.a},f48d:function(t,e,o){"use strict";o.r(e);var a=o("cc1b"),i=o.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},f963:function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return r})),o.d(e,"a",(function(){return a}));var a={pageMeta:o("7854").default,uniCountDown:o("e12a").default,nsEmpty:o("52a6").default,payment:o("b6f2").default,nsLogin:o("2910").default,loadingCover:o("c003").default},i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",[o("page-meta",{attrs:{"page-style":t.themeColor}}),o("v-uni-view",{staticClass:"order-container"},[t.storeToken?o("v-uni-view",{staticClass:"cate-search"},[o("v-uni-view",{staticClass:"search-box"},[o("v-uni-input",{staticClass:"uni-input",attrs:{maxlength:"50","confirm-type":"search",placeholder:"请输入商品名称/订单编号"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.search()}},model:{value:t.searchText,callback:function(e){t.searchText=e},expression:"searchText"}}),o("v-uni-text",{staticClass:"iconfont icon-sousuo3",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.search()}}})],1)],1):t._e(),t.storeToken?o("v-uni-view",{staticClass:"order-nav"},t._l(t.statusList,(function(e,a){return o("v-uni-view",{key:a,staticClass:"uni-tab-item",attrs:{id:e.id,"data-current":a},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.ontabtap.apply(void 0,arguments)}}},[o("v-uni-text",{staticClass:"uni-tab-item-title",class:e.status==t.orderStatus?"uni-tab-item-title-active color-base-text":""},[t._v(t._s(e.name))])],1)})),1):t._e(),t.storeToken?o("mescroll-uni",{ref:"mescroll",attrs:{top:"196rpx"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getListData.apply(void 0,arguments)}}},[o("template",{attrs:{slot:"list"},slot:"list"},[t.orderList.length>0?o("v-uni-view",{staticClass:"order-list"},t._l(t.orderList,(function(e,a){return o("v-uni-view",{key:a,staticClass:"order-item"},[o("v-uni-view",{staticClass:"order-header",class:{waitpay:"waitpay"==t.orderStatus&&0==e.order_status}},[o("v-uni-text",{staticClass:"order-no"},[t._v("订单号："+t._s(e.order_no))]),o("v-uni-text",{staticClass:"order-type-name"},[t._v(t._s(e.order_type_name))]),o("v-uni-text",{staticClass:"status-name"},[t._v(t._s(e.order_status_name))])],1),o("v-uni-view",{staticClass:"order-body",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.orderDetail(e)}}},[1==e.order_goods.length?t._l(e.order_goods,(function(e,i){return o("v-uni-view",{key:i,staticClass:"goods-wrap"},[o("v-uni-view",{staticClass:"goods-img"},[o("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"aspectFill","lazy-load":!0},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a,i)}}})],1),o("v-uni-view",{staticClass:"goods-info"},[o("v-uni-view",{staticClass:"pro-info"},[2==e.goods_class?o("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.goods_name))]):o("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.sku_name))]),e.sku_spec_format?o("v-uni-view",{staticClass:"sku"},[o("v-uni-view",{staticClass:"goods-spec"},[t._l(e.sku_spec_format,(function(o,a){return[t._v(t._s(o.spec_value_name)+"\n\t\t\t\t\t\t\t\t\t\t\t\t\t"+t._s(a<e.sku_spec_format.length-1?"; ":""))]}))],2)],1):t._e()],1),o("v-uni-view",{staticClass:"goods-action"})],1)],1)})):[o("v-uni-view",{staticClass:"multi-order-goods"},[o("v-uni-scroll-view",{staticClass:"scroll-view",attrs:{"scroll-x":"true"}},[o("v-uni-view",{staticClass:"goods-wrap"},t._l(e.order_goods,(function(e,i){return o("v-uni-view",{key:i,staticClass:"goods-img"},[o("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"aspectFill","lazy-load":!0},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(a,i)}}})],1)})),1)],1),o("v-uni-view",{staticClass:"shade"},[o("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/order/order-shade.png")}})],1)],1)]],2),o("v-uni-view",{staticClass:"order-footer"},[o("v-uni-view",{staticClass:"order-base-info"},[o("v-uni-view",{staticClass:"total"},[o("v-uni-text",{staticClass:"font-size-sub"},[t._v("共"+t._s(e.goods_num)+"件商品")]),o("v-uni-text",{staticClass:"align-right font-size-base"},[t._v("实付款："),o("v-uni-text",{staticClass:"font-size-base price-font"},[t._v(t._s(t.$lang("common.currencySymbol"))+t._s(e.order_money))])],1)],1)],1),e.action.length>0?o("v-uni-view",{staticClass:"order-action"},[0==e.order_status&&"offlinepay"!==e.pay_type?o("v-uni-view",{staticClass:"order-time",attrs:{id:"action-date"}},[o("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/order/time.png")}}),t._v("剩余时间："),o("uni-count-down",{attrs:{day:e.discountTimeMachine.d,hour:e.discountTimeMachine.h,minute:e.discountTimeMachine.i,second:e.discountTimeMachine.s,color:"#FF4644",splitorColor:"#FF4644"}})],1):t._e(),1==t.evaluateConfig.evaluate_status&&1==e.is_evaluate?o("v-uni-view",{staticClass:"order-box-btn",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.operation("memberOrderEvaluation",e)}}},[0==e.evaluate_status?o("v-uni-text",[t._v("评价")]):1==e.evaluate_status?o("v-uni-text",[t._v("追评")]):t._e()],1):t._e(),t._l(e.action,(function(a,i){return o("v-uni-view",{key:i,staticClass:"order-box-btn",class:{"color-base-border color-base-bg":"orderPay"==a.action},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.operation(a.action,e)}}},[t._v(t._s(a.title))])}))],2):0==e.action.length&&1==e.is_evaluate&&1==t.evaluateConfig.evaluate_status?o("v-uni-view",{staticClass:"order-action"},[1==e.is_evaluate?o("v-uni-view",{staticClass:"order-box-btn",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.operation("memberOrderEvaluation",e)}}},[0==e.evaluate_status?o("v-uni-text",[t._v("评价")]):1==e.evaluate_status?o("v-uni-text",[t._v("追评")]):t._e()],1):t._e()],1):o("v-uni-view",{staticClass:"order-action"},[o("v-uni-view",{staticClass:"order-box-btn",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.orderDetail(e)}}},[t._v("查看详情")])],1)],1)],1)})),1):o("v-uni-view",[o("ns-empty",{attrs:{isIndex:!1,text:t.$lang("emptyTips")}})],1)],1)],2):t._e(),t.storeToken?t._e():o("v-uni-view",{staticClass:"no-login"},[o("v-uni-view",[o("ns-empty",{attrs:{isIndex:!1,text:t.$lang("emptyTips")}})],1),o("v-uni-button",{staticClass:"button mini",attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toLogin.apply(void 0,arguments)}}},[t._v("去登录")])],1),o("payment",{ref:"choosePaymentPopup"}),o("ns-login",{ref:"login"}),o("loading-cover",{ref:"loadingCover"})],1)],1)},r=[]},f9fd:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("64aa");var a={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:[Number,String],default:0},hour:{type:[Number,String],default:0},minute:{type:[Number,String],default:0},second:{type:[Number,String],default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},mounted:function(t){var e=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},watch:{day:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},hour:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},minute:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},second:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,e,o,a){return t=Number(t),e=Number(e),o=Number(o),a=Number(a),60*t*60*24+60*e*60+60*o+a},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,o=0,a=0,i=0;t>0?(e=Math.floor(t/86400),o=Math.floor(t/3600)-24*e,a=Math.floor(t/60)-24*e*60-60*o,i=Math.floor(t)-24*e*60*60-60*o*60-60*a):this.timeUp(),e<10&&(e="0"+e),o<10&&(o="0"+o),a<10&&(a="0"+a),i<10&&(i="0"+i),this.d=e,this.h=o,this.i=a,this.s=i}}};e.default=a}}]);