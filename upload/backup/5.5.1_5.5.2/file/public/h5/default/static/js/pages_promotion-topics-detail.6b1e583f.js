(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-topics-detail"],{"015d":function(t,i,e){"use strict";e.r(i);var o=e("0f46"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a},"0f46":function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};i.default=o},"331a":function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){return o}));var o={pageMeta:e("7854").default,nsEmpty:e("52a6").default,hoverNav:e("c1f1").default,loadingCover:e("c003").default},n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":t.themeColor}}),e("v-uni-view",{staticClass:"topic-detail",style:{backgroundColor:t.bgColor}},[e("mescroll-uni",{ref:"mescroll",attrs:{size:10},on:{getData:function(i){arguments[0]=i=t.$handleEvent(i),t.getData.apply(void 0,arguments)}}},[e("template",{attrs:{slot:"list"},slot:"list"},[t.topicAdv?e("v-uni-view",{staticClass:"topic-pic"},[e("v-uni-image",{attrs:{src:t.$util.img(t.topicAdv),mode:"widthFix"}})],1):t._e(),t.dataList.length?e("v-uni-view",{staticClass:"goods-list double-column"},t._l(t.dataList,(function(i,o){return e("v-uni-view",{key:o,staticClass:"goods-item"},[e("v-uni-view",{staticClass:"goods-img",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetail(i)}}},[e("v-uni-image",{attrs:{src:t.goodsImg(i.sku_image),mode:"widthFix"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.imgError(o)}}})],1),e("v-uni-view",{staticClass:"info-wrap"},[e("v-uni-view",{staticClass:"name-wrap"},[e("v-uni-view",{staticClass:"goods-name",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetail(i)}}},[t._v(t._s(i.sku_name))])],1),e("v-uni-view",{staticClass:"lineheight-clear"},[e("v-uni-view",{staticClass:"discount-price"},[e("v-uni-text",{staticClass:"unit  price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),e("v-uni-text",{staticClass:"price  price-style large"},[t._v(t._s(parseFloat(i.topic_price).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit  price-style small"},[t._v("."+t._s(parseFloat(i.topic_price).toFixed(2).split(".")[1]))])],1)],1),e("v-uni-view",{staticClass:"pro-info"},[t.showMarketPrice(i)?e("v-uni-view",{staticClass:"delete-price font-size-activity-tag color-tip price-font"},[e("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),e("v-uni-text",[t._v(t._s(t.showMarketPrice(i)))])],1):t._e(),i.sale_show?e("v-uni-view",{staticClass:"sale font-size-activity-tag color-tip"},[t._v("已售"+t._s(i.sale_num)+t._s(i.unit?i.unit:"件"))]):t._e()],1)],1)],1)})),1):t._e(),t.dataList.length?t._e():e("v-uni-view",[e("ns-empty",{attrs:{text:"暂无有相应的商品"}})],1)],1)],2),e("hover-nav"),e("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},"342c":function(t,i,e){"use strict";var o=e("9803"),n=e.n(o);n.a},7854:function(t,i,e){"use strict";e.r(i);var o=e("8ba8"),n=e("f48d");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);var s=e("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);i["default"]=r.exports},"791e":function(t,i,e){"use strict";e.r(i);var o=e("331a"),n=e("8939");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);e("342c");var s=e("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"44018259",null,!1,o["a"],void 0);i["default"]=r.exports},8939:function(t,i,e){"use strict";e.r(i);var o=e("8b6d"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a},"8b6d":function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("c223"),e("e838");var o={data:function(){return{dataList:[],topicId:0,bgColor:"#f7f7f7",topicAdv:"",datainfo:{image:"",title:""}}},components:{},onLoad:function(t){t.topic_id?this.topicId=t.topic_id:uni.navigateBack({delta:1})},onShow:function(){},methods:{getData:function(t){var i=this;this.$api.sendRequest({url:"/topic/api/topicgoods/page",data:{topic_id:this.topicId,page_size:t.size,page:t.num},success:function(e){var o=[],n=e.message;0==e.code&&e.data?(i.topicAdv=e.data.topic_adv,i.bgColor=e.data.bg_color,o=e.data.list,i.datainfo.image=e.data.topic_adv,i.datainfo.title=e.data.topic_name):i.$util.showToast({title:n}),t.endSuccess(o.length),1==t.num&&(i.dataList=[]),i.dataList=i.dataList.concat(o),i.$refs.loadingCover&&i.$refs.loadingCover.hide()},fail:function(e){t.endErr(),i.$refs.loadingCover&&i.$refs.loadingCover.hide()}})},toDetail:function(t){this.$util.redirectTo("/pages_promotion/topics/goods_detail",{id:t.id})},goodsImg:function(t){var i=t.split(",");return i[0]?this.$util.img(i[0],{size:"mid"}):this.$util.getDefaultImage().goods},imgError:function(t){this.dataList[t].sku_image=this.$util.getDefaultImage().goods},showMarketPrice:function(t){if(t.market_price_show){if(t.market_price>0)return t.market_price;if(parseFloat(t.price)>parseFloat(t.topic_price))return t.price}return""}},onShareAppMessage:function(t){var i="/pages_promotion/topics/detail?topic_id="+this.topicId;return this.memberInfo&&this.memberInfo.member_id&&(i+="&source_member="+this.memberInfo.member_id),{title:this.datainfo.title,path:i,imageUrl:this.$util.img(this.datainfo.image),success:function(t){},fail:function(t){}}}};i.default=o},"8ba8":function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){}));var o=function(){var t=this.$createElement,i=this._self._c||t;return i("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},9803:function(t,i,e){var o=e("baac");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("2e01b618",o,!0,{sourceMap:!1,shadowMode:!1})},a725:function(t,i,e){"use strict";var o=e("ac2a"),n=e.n(o);n.a},ac2a:function(t,i,e){var o=e("f714");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("1a69ffc2",o,!0,{sourceMap:!1,shadowMode:!1})},baac:function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-44018259] .fixed{top:35vh}.topic-detail[data-v-44018259]{width:100%;min-height:100vh}.topic-pic uni-image[data-v-44018259]{width:100%;display:block}.goods-list.double-column[data-v-44018259]{display:flex;flex-wrap:wrap;margin:%?30?% %?30?%}.goods-list.double-column .goods-item[data-v-44018259]{flex:1;position:relative;background-color:#fff;flex-basis:48%;max-width:calc((100% - %?30?%) / 2);margin-right:%?30?%;margin-bottom:%?20?%;border-radius:%?10?%}.goods-list.double-column .goods-item[data-v-44018259]:nth-child(2n){margin-right:0}.goods-list.double-column .goods-item .goods-img[data-v-44018259]{position:relative;overflow:hidden;padding-top:100%;border-top-left-radius:%?10?%;border-top-right-radius:%?10?%}.goods-list.double-column .goods-item .goods-img uni-image[data-v-44018259]{width:100%;position:absolute;top:50%;left:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.goods-list.double-column .goods-item .goods-tag[data-v-44018259]{color:#fff;line-height:1;padding:%?8?% %?16?%;position:absolute;border-bottom-right-radius:%?10?%;top:0;left:0;font-size:%?22?%}.goods-list.double-column .goods-item .goods-tag-img[data-v-44018259]{position:absolute;border-top-left-radius:%?10?%;width:%?80?%;height:%?80?%;top:0;left:0;z-index:5;overflow:hidden}.goods-list.double-column .goods-item .goods-tag-img uni-image[data-v-44018259]{width:100%;height:100%}.goods-list.double-column .goods-item .info-wrap[data-v-44018259]{padding:0 %?26?% %?26?% %?26?%}.goods-list.double-column .goods-item .goods-name[data-v-44018259]{font-size:%?28?%;line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-top:%?20?%;height:%?68?%}.goods-list.double-column .goods-item .discount-price[data-v-44018259]{display:inline-block;font-weight:700;line-height:1;margin-top:%?16?%;color:var(--price-color)}.goods-list.double-column .goods-item .discount-price .unit[data-v-44018259]{margin-right:%?6?%}.goods-list.double-column .goods-item .pro-info[data-v-44018259]{display:flex;margin-top:auto}.goods-list.double-column .goods-item .pro-info .delete-price[data-v-44018259]{text-decoration:line-through;flex:1}.goods-list.double-column .goods-item .pro-info .delete-price .unit[data-v-44018259]{margin-right:%?6?%}.goods-list.double-column .goods-item .pro-info > uni-view[data-v-44018259]{line-height:1}.goods-list.double-column .goods-item .pro-info > uni-view[data-v-44018259]:nth-child(2){text-align:right}.goods-list.double-column .goods-item .member-price-tag[data-v-44018259]{display:inline-block;width:%?60?%;line-height:1;margin-left:%?6?%}.goods-list.double-column .goods-item .member-price-tag uni-image[data-v-44018259]{width:100%}',""]),t.exports=i},c1f1:function(t,i,e){"use strict";e.r(i);var o=e("fa1d"),n=e("015d");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);e("a725");var s=e("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"c1934e78",null,!1,o["a"],void 0);i["default"]=r.exports},cc1b:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("5ef2"),e("64aa"),e("5c47"),e("a1c1"),e("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,i=getCurrentPages()[0];this.$pageVm=i.$vm||i,uni.onWindowResize((function(i){t.$emit("resize",i)})),this.$pageVm.$on("hook:onPageScroll",(function(i){t.$emit("scroll",i)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,i){t.setStyle({pullToRefresh:{support:i,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,i=String(this.scrollTop);if(-1!==i.indexOf("rpx")&&(i=uni.upx2px(i.replace("rpx",""))),i=parseFloat(i),!isNaN(i)){var e=function e(n){n.scrollTop===i&&(t.$pageVm.$off("hook:onPageScroll",e),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:i,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",e)}})}}}};i.default=n},f48d:function(t,i,e){"use strict";e.r(i);var o=e("cc1b"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a},f714:function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=i},fa1d:function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){}));var o=function(){var t=this,i=t.$createElement,e=t._self._c||i;return 1==t.pageCount||t.need?e("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?e("v-uni-view",{staticClass:"btn-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/pages/index/index")}}},[e("v-uni-text",{staticClass:"iconfont icon-shouye1"}),e("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?e("v-uni-view",{staticClass:"btn-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/pages/member/index")}}},[e("v-uni-text",{staticClass:"iconfont icon-yonghu"}),e("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?e("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[e("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):e("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[e("v-uni-view",[t._v("快捷")]),e("v-uni-view",[t._v("导航")])],1)],1):t._e()},n=[]}}]);