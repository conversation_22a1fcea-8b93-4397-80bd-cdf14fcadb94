(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-card_agreement"],{"0817":function(t,e,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("5c47"),n("2c10"),n("a1c1"),n("23f4"),n("7d2f"),n("9c4e"),n("ab80"),n("aa9c"),n("473f"),n("bf0f"),n("3efd");var a=r(n("af87")),o=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,i=/^<\/([-A-Za-z0-9_]+)[^>]*>/,c=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,s=h("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),l=h("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),u=h("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),f=h("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),d=h("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),p=h("script,style");function h(t){for(var e={},n=t.split(","),r=0;r<n.length;r++)e[n[r]]=!0;return e}var g=function(t){t=function(t){return t.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(t),t=function(t){return t=t.replace(/<!--[\s\S]*-->/gi,""),t}(t),t=function(t){var e='<img style="width:100% !important;display:block;max-width: '.concat("100%",' !important;"');return t=t.replace(/\\/g,"").replace(/<img/g,e),t=t.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(t,n){return e+' src="'+a.default.img(n)+'"/>'})),t}(t),t=function(t){return t=t.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(t,e){return t=t.replace(/[:](\s?)[\s\S]*/gi,(function(t,e){return t.replace(/"/g,"'")})),t})),t}(t);var e=[],n={node:"root",children:[]};return function(t,e){var n,r,a,h=[],g=t;h.last=function(){return this[this.length-1]};while(t){if(r=!0,h.last()&&p[h.last()])t=t.replace(new RegExp("([\\s\\S]*?)</"+h.last()+"[^>]*>"),(function(t,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),e.chars&&e.chars(n),""})),v("",h.last());else if(0==t.indexOf("\x3c!--")?(n=t.indexOf("--\x3e"),n>=0&&(e.comment&&e.comment(t.substring(4,n)),t=t.substring(n+3),r=!1)):0==t.indexOf("</")?(a=t.match(i),a&&(t=t.substring(a[0].length),a[0].replace(i,v),r=!1)):0==t.indexOf("<")&&(a=t.match(o),a&&(t=t.substring(a[0].length),a[0].replace(o,b),r=!1)),r){n=t.indexOf("<");var m=n<0?t:t.substring(0,n);t=n<0?"":t.substring(n),e.chars&&e.chars(m)}if(t==g)throw"Parse Error: "+t;g=t}function b(t,n,r,a){if(n=n.toLowerCase(),l[n])while(h.last()&&u[h.last()])v("",h.last());if(f[n]&&h.last()==n&&v("",n),a=s[n]||!!a,a||h.push(n),e.start){var o=[];r.replace(c,(function(t,e){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:d[e]?e:"";o.push({name:e,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),e.start&&e.start(n,o,a)}}function v(t,n){if(n){for(r=h.length-1;r>=0;r--)if(h[r]==n)break}else var r=0;if(r>=0){for(var a=h.length-1;a>=r;a--)e.end&&e.end(h[a]);h.length=r}}v()}(t,{start:function(t,r,a){var o={name:t};if(0!==r.length&&(o.attrs=function(t){return t.reduce((function(t,e){var n=e.value,r=e.name;return t[r]?t[r]=t[r]+" "+n:t[r]=n,t}),{})}(r)),a){var i=e[0]||n;i.children||(i.children=[]),i.children.push(o)}else e.unshift(o)},end:function(t){var r=e.shift();if(r.name!==t&&console.error("invalid state: mismatch end tag"),0===e.length)n.children.push(r);else{var a=e[0];a.children||(a.children=[]),a.children.push(r)}},chars:function(t){var r={type:"text",text:t};if(0===e.length)n.children.push(r);else{var a=e[0];a.children||(a.children=[]),a.children.push(r)}},comment:function(t){var n={node:"comment",text:t},r=e[0];r.children||(r.children=[]),r.children.push(n)}}),n.children};e.default=g},"22cd":function(t,e,n){"use strict";n.r(e);var r=n("d94c"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a},"28ce":function(t,e,n){var r=n("f2b6");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var a=n("967d").default;a("37fd0880",r,!0,{sourceMap:!1,shadowMode:!1})},"59ca":function(t,e,n){"use strict";var r=n("28ce"),a=n.n(r);a.a},"6dfb":function(t,e,n){"use strict";n.r(e);var r=n("859f"),a=n("22cd");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("59ca");var i=n("828b"),c=Object(i["a"])(a["default"],r["b"],r["c"],!1,null,"338a39b7",null,!1,r["a"],void 0);e["default"]=c.exports},7854:function(t,e,n){"use strict";n.r(e);var r=n("8ba8"),a=n("f48d");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);var i=n("828b"),c=Object(i["a"])(a["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);e["default"]=c.exports},"859f":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return r}));var r={pageMeta:n("7854").default,nsMpHtml:n("d108").default},a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":this.themeColor}}),e("v-uni-view",{staticClass:"page"},[e("v-uni-view",{staticClass:"agreement-title"},[this._v(this._s(this.title))]),e("v-uni-view",{staticClass:"agreement-content"},[e("ns-mp-html",{attrs:{content:this.content}})],1)],1)],1)},o=[]},"8ba8":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var r=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},cc1b:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("64aa"),n("5c47"),n("a1c1"),n("e838");var r={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var n=function n(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",n),t.$emit("scrolldone",r))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",n)}})}}}};e.default=a},d94c:function(t,e,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;r(n("0817"));var a={data:function(){return{title:"",content:""}},onLoad:function(){this.getAgreement()},onShow:function(){},methods:{getAgreement:function(){var t=this;this.$api.sendRequest({url:"/supermember/api/membercard/agreement",success:function(e){e.data&&0==e.code&&(t.title=e.data.title,t.content=e.data.content,uni.setNavigationBarTitle({title:t.title}))}})}},onBackPress:function(t){return"navigateBack"!==t.from&&(this.$util.redirectTo("/pages_tool/member/card_buy",{},"redirectTo"),!0)}};e.default=a},f2b6:function(t,e,n){var r=n("c86c");e=r(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.page[data-v-338a39b7]{width:100%;height:100%;padding:%?30?%;box-sizing:border-box;background-color:#fff}.agreement-title[data-v-338a39b7]{font-size:%?32?%;text-align:center}.agreement-content[data-v-338a39b7]{margin-top:%?20?%;word-break:break-all;font-size:%?28?%}',""]),t.exports=e},f48d:function(t,e,n){"use strict";n.r(e);var r=n("cc1b"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a}}]);