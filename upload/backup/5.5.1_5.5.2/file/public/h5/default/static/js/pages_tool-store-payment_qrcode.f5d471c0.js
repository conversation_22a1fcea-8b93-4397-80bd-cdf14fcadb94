(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-store-payment_qrcode"],{"6abd":function(e,t,a){"use strict";a.r(t);var o=a("a428"),n=a("838c");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("8353");var r=a("828b"),c=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,"65f9fea8",null,!1,o["a"],void 0);t["default"]=c.exports},7854:function(e,t,a){"use strict";a.r(t);var o=a("8ba8"),n=a("f48d");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);var r=a("828b"),c=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=c.exports},8353:function(e,t,a){"use strict";var o=a("d3fb"),n=a.n(o);n.a},"838c":function(e,t,a){"use strict";a.r(t);var o=a("9734"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},"8ba8":function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var o=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},9734:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("23f4"),a("7d2f"),a("5c47"),a("9c4e"),a("ab80"),a("2c10");var o={data:function(){return{isRepeat:!1,payInfo:null,error:0,timer:null,show:!1,memberrechargeConfig:null,screenBrightness:0}},onShow:function(){var e=this;uni.setStorageSync("paySource",""),this.storeToken?(this.getCouponNum(),this.getMemberrechargeConfig(),this.getPayAuthCode()):this.$nextTick((function(){e.$refs.login.open("/pages_tool/store/payment_qrcode")}))},onLoad:function(){},methods:{getPayAuthCode:function(){var e=this;this.isRepeat||(this.isRepeat=!0,this.timer&&clearInterval(this.timer),this.$api.sendRequest({url:"/api/pay/memberpaycode",success:function(t){e.isRepeat=!1,0==t.code&&t.data?(e.payInfo=t.data,e.error=0,e.show=!1,setTimeout((function(){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}),100)):e.error<5?(e.error++,e.getPayAuthCode()):e.$util.showToast({title:t.message})}}))},refreshPaymentCode:function(){var e=this;this.timer=setInterval((function(){e.getPayAuthCode()}),3e4)},showAuthCode:function(e){this.show=e},getMemberrechargeConfig:function(){var e=this;this.$api.sendRequest({url:"/memberrecharge/api/memberrecharge/config",success:function(t){t.code>=0&&t.data&&(e.memberrechargeConfig=t.data)}})},getCouponNum:function(){var e=this;this.$api.sendRequest({url:"/coupon/api/coupon/num",success:function(t){0==t.code&&(e.memberInfo.coupon_num=t.data,e.$forceUpdate(),e.$store.commit("setMemberInfo",e.memberInfo))}})},splitFn:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4,a=new RegExp("[^\n]{1,"+t+"}","g"),o=e.match(a);return o.join(" ")}},watch:{storeToken:function(e,t){this.getPayAuthCode()}},onHide:function(){this.timer&&clearInterval(this.timer),uni.setScreenBrightness({value:this.screenBrightness,success:function(){}})},onUnload:function(){this.timer&&clearInterval(this.timer),uni.setScreenBrightness({value:this.screenBrightness,success:function(){}})}};t.default=o},a428:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return o}));var o={pageMeta:a("7854").default,loadingCover:a("c003").default,nsLogin:a("2910").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":e.themeColor}}),a("v-uni-view",{staticClass:"container"},[e.payInfo&&e.memberInfo?a("v-uni-view",[a("v-uni-view",{staticClass:"paycode-wrap"},[a("v-uni-view",{staticClass:"member-wrap"},[a("v-uni-view",{staticClass:"headimg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getWxAuth.apply(void 0,arguments)}}},[a("v-uni-image",{attrs:{src:e.memberInfo.headimg?e.$util.img(e.memberInfo.headimg):e.$util.getDefaultImage().head,mode:"widthFix"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.memberInfo.headimg=e.$util.getDefaultImage().head}}})],1),a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-view",{staticClass:"nickname"},[e._v(e._s(e.memberInfo.nickname))]),e.memberInfo.member_level?a("v-uni-view",{staticClass:"member-level",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.redirectTo(e.memberInfo.member_level_type?"/pages_tool/member/card":"/pages_tool/member/level")}}},[a("v-uni-image",{staticClass:"level-icon",attrs:{src:e.$util.img("app/component/view/member_info/img/style_4_vip_tag.png"),mode:"widthFix"}}),a("v-uni-view",{staticClass:"level-name"},[e._v(e._s(e.memberInfo.member_level_name))])],1):e._e()],1),e.addonIsExist.memberrecharge&&e.memberrechargeConfig&&e.memberrechargeConfig.is_use?a("v-uni-view",{staticClass:"recharge",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.redirectTo("/pages_tool/recharge/list")}}},[e._v("去充值")]):e._e()],1),a("v-uni-view",{staticClass:"body-wrap"},[a("v-uni-view",{staticClass:"barcode-wrap"},[a("v-uni-image",{staticClass:"barcode",attrs:{src:e.payInfo.barcode}})],1),a("v-uni-view",{staticClass:"auth-code"},[a("v-uni-text",{staticClass:"price-font"},[e._v(e._s(e.show?e.splitFn(e.payInfo.auth_code):e.payInfo.auth_code.substring(0,5)+"******"))]),e.show?a("v-uni-text",{staticClass:"show",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showAuthCode(!1)}}},[e._v("隐藏数字")]):a("v-uni-text",{staticClass:"show",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showAuthCode(!0)}}},[e._v("查看数字")])],1),a("v-uni-image",{staticClass:"qrcode",attrs:{src:e.payInfo.qrcode,mode:"widthFix"}}),a("v-uni-view",{staticClass:"dynamic-code",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getPayAuthCode.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"code"},[e._v("动态码"),a("v-uni-text",[e._v(e._s(e.payInfo.dynamic_code))]),a("v-uni-text",{staticClass:"iconfont icon-shuaxin"})],1)],1),a("v-uni-view",{staticClass:"tips"},[e._v("付款码仅用于支付时向收银员出示，请勿发送给他人")])],1),a("v-uni-view",{staticClass:"footer-wrap"},[a("v-uni-view",{staticClass:"account-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.redirectTo("/pages_tool/member/point")}}},[a("v-uni-view",{staticClass:"value price-font"},[e._v(e._s(parseInt(e.memberInfo.point)))]),a("v-uni-view",{staticClass:"title"},[e._v("积分")])],1),a("v-uni-view",{staticClass:"split"}),a("v-uni-view",{staticClass:"account-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.redirectTo("/pages_tool/member/balance")}}},[a("v-uni-view",{staticClass:"value price-font"},[e._v(e._s(e._f("moneyFormat")(parseFloat(e.memberInfo.balance)+parseFloat(e.memberInfo.balance_money))))]),a("v-uni-view",{staticClass:"title"},[e._v("余额")])],1),a("v-uni-view",{staticClass:"split"}),a("v-uni-view",{staticClass:"account-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.redirectTo("/pages_tool/member/coupon")}}},[a("v-uni-view",{staticClass:"value price-font"},[e._v(e._s(e.memberInfo.coupon_num?e.memberInfo.coupon_num:0))]),a("v-uni-view",{staticClass:"title"},[e._v("优惠券")])],1)],1)],1)],1):e._e(),a("loading-cover",{ref:"loadingCover"}),a("ns-login",{ref:"login"})],1)],1)},i=[]},aa3c:function(e,t,a){var o=a("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container[data-v-65f9fea8]{width:100vw;min-height:100vh;background:var(--base-color);padding:%?30?%;box-sizing:border-box;overflow-y:auto}.paycode-wrap[data-v-65f9fea8]{overflow:hidden;background:#fff;border-radius:%?20?%}.paycode-wrap .member-wrap[data-v-65f9fea8]{padding:%?36?% %?32?%;background:#f6f6f6;display:flex;align-items:center}.paycode-wrap .member-wrap .headimg[data-v-65f9fea8]{width:%?88?%;height:%?88?%;overflow:hidden;border-radius:50%;margin-right:%?20?%}.paycode-wrap .member-wrap .headimg uni-image[data-v-65f9fea8]{width:%?88?%;height:%?88?%}.paycode-wrap .member-wrap .info-wrap[data-v-65f9fea8]{flex:1;width:0}.paycode-wrap .member-wrap .nickname[data-v-65f9fea8]{font-size:%?30?%;font-weight:600;white-space:nowrap;overflow:hidden;line-height:1}.paycode-wrap .member-wrap .member-level[data-v-65f9fea8]{background:#474758;padding:0;margin:%?16?% 0 0 0;height:%?40?%;border-radius:%?40?%;display:inline-flex;align-items:center}.paycode-wrap .member-wrap .member-level .level-icon[data-v-65f9fea8]{width:%?40?%;vertical-align:middle;margin-left:%?-2?%}.paycode-wrap .member-wrap .member-level .level-name[data-v-65f9fea8]{padding:0 %?20?% 0 %?6?%;color:#ddc095;font-size:%?24?%;display:inline-block;line-height:1}.paycode-wrap .member-wrap .recharge[data-v-65f9fea8]{color:var(--base-color);border:%?2?% solid var(--base-color);height:%?64?%;line-height:%?64?%;border-radius:%?64?%;font-size:%?26?%;padding:0 %?30?%;letter-spacing:%?4?%}.paycode-wrap .body-wrap[data-v-65f9fea8]{margin:%?40?% %?40?% 0 %?40?%;width:calc(100% %?-80?%);box-sizing:border-box;text-align:center;padding-bottom:%?40?%;position:relative;border-bottom:%?2?% dashed #dedede}.paycode-wrap .body-wrap .barcode-wrap[data-v-65f9fea8]{width:%?590?%;height:%?200?%;overflow:hidden;margin:0 auto}.paycode-wrap .body-wrap .barcode-wrap .barcode[data-v-65f9fea8]{width:%?590?%;height:%?250?%}.paycode-wrap .body-wrap .qrcode[data-v-65f9fea8]{width:%?320?%;margin-top:%?30?%}.paycode-wrap .body-wrap .tips[data-v-65f9fea8]{color:#999;font-size:%?24?%;margin-top:%?20?%}.paycode-wrap .body-wrap .dynamic-code[data-v-65f9fea8]{display:flex;align-items:center;justify-content:center}.paycode-wrap .body-wrap .dynamic-code .code[data-v-65f9fea8]{background:#f6f6f6;color:#666;padding:%?4?% %?26?%;border-radius:%?60?%}.paycode-wrap .body-wrap .dynamic-code .code uni-text[data-v-65f9fea8]{margin-left:%?10?%}.paycode-wrap .body-wrap .auth-code[data-v-65f9fea8]{color:#999;font-size:%?24?%;margin-top:%?20?%}.paycode-wrap .body-wrap .auth-code .price-font[data-v-65f9fea8]{letter-spacing:%?2?%}.paycode-wrap .body-wrap .auth-code .show[data-v-65f9fea8]{color:#163d8f;font-size:%?26?%;margin-left:%?20?%}.paycode-wrap .body-wrap[data-v-65f9fea8]:after, .paycode-wrap .body-wrap[data-v-65f9fea8]:before{content:" ";width:%?40?%;height:%?40?%;background:var(--base-color);border-radius:50%;z-index:5;bottom:0;display:block;position:absolute}.paycode-wrap .body-wrap[data-v-65f9fea8]:after{right:0;-webkit-transform:translate(calc(50% + %?40?%),50%);transform:translate(calc(50% + %?40?%),50%)}.paycode-wrap .body-wrap[data-v-65f9fea8]:before{left:0;-webkit-transform:translate(calc(-50% - %?40?%),50%);transform:translate(calc(-50% - %?40?%),50%)}.paycode-wrap .footer-wrap[data-v-65f9fea8]{padding:%?50?% 0;display:flex;align-items:center}.paycode-wrap .footer-wrap .split[data-v-65f9fea8]{width:%?2?%;background:#ddd;height:%?50?%}.paycode-wrap .footer-wrap .account-item[data-v-65f9fea8]{flex:1;text-align:center}.paycode-wrap .footer-wrap .account-item .value[data-v-65f9fea8]{font-size:%?32?%;color:var(--base-color);line-height:1.5}.paycode-wrap .footer-wrap .account-item .title[data-v-65f9fea8]{color:#999;font-size:%?24?%;margin-top:%?10?%}',""]),e.exports=t},cc1b:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(e){return-1!==["dark","light"].indexOf(e)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var e=this,t=getCurrentPages()[0];this.$pageVm=t.$vm||t,uni.onWindowResize((function(t){e.$emit("resize",t)})),this.$pageVm.$on("hook:onPageScroll",(function(t){e.$emit("scroll",t)})),this.$watch("backgroundTextStyle",(function(){e.setBackgroundTextStyle()})),this.$watch((function(){return[e.rootFontSize,e.pageStyle]}),(function(){e.setPageMeta()})),this.$watch((function(){return[e.backgroundColor,e.backgroundColorTop,e.backgroundColorBottom]}),(function(){e.setBackgroundColor()})),this.$watch((function(){return[e.scrollTop,e.scrollDuration]}),(function(){e.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(e,t){e.setStyle({pullToRefresh:{support:t,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var e=this,t=String(this.scrollTop);if(-1!==t.indexOf("rpx")&&(t=uni.upx2px(t.replace("rpx",""))),t=parseFloat(t),!isNaN(t)){var a=function a(n){n.scrollTop===t&&(e.$pageVm.$off("hook:onPageScroll",a),e.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:t,duration:this.scrollDuration,success:function(){e.$pageVm.$on("hook:onPageScroll",a)}})}}}};t.default=n},d3fb:function(e,t,a){var o=a("aa3c");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var n=a("967d").default;n("055b9bbd",o,!0,{sourceMap:!1,shadowMode:!1})},f48d:function(e,t,a){"use strict";a.r(t);var o=a("cc1b"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a}}]);