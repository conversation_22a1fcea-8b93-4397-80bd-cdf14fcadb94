(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-fenxiao-order_detail"],{"0aaa":function(e,t,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(o("9fbb")),r={data:function(){return{isIphoneX:!1,orderId:0,orderData:{action:[]}}},components:{},onLoad:function(e){e.id?this.orderId=e.id:uni.navigateBack({delta:1})},mixins:[a.default],onShow:function(){var e=this;setTimeout((function(){e.addonIsExist.fenxiao||(e.$util.showToast({title:"商家未开启分销",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?this.getOrderData():this.$nextTick((function(){e.$refs.login.open("/pages_promotion/fenxiao/order_detail?id="+e.orderId)}))},methods:{getOrderData:function(){var e=this;this.$api.sendRequest({url:"/fenxiao/api/order/info",data:{fenxiao_order_id:this.orderId},success:function(t){t.code>=0?(e.$refs.loadingCover&&e.$refs.loadingCover.hide(),e.orderData=t.data):(e.$util.showToast({title:"未获取到订单信息！"}),setTimeout((function(){e.$util.redirectTo("/pages_promotion/fenxiao/order",{},"redirectTo")}),1500))},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},imageError:function(){this.orderData.sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}},watch:{storeToken:function(e,t){e&&this.getOrderData()}}};t.default=r},"5f72":function(e,t,o){"use strict";var i=o("d9b4"),a=o.n(i);a.a},"5fa8":function(e,t,o){"use strict";o.r(t);var i=o("0aaa"),a=o.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a},7854:function(e,t,o){"use strict";o.r(t);var i=o("8ba8"),a=o("f48d");for(var r in a)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(r);var d=o("828b"),n=Object(d["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=n.exports},"79d0":function(e,t,o){var i=o("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.order-detail[data-v-73eb3f26]{width:100%;padding:0 %?20?%;box-sizing:border-box;margin-top:%?20?%}.order-detail .order-detail-box[data-v-73eb3f26]{width:100%;height:100%;box-sizing:border-box;background:#fff;border-radius:%?10?%}.order-detail .order-detail-box .header[data-v-73eb3f26]{width:100%;padding:%?30?%;display:flex;justify-content:space-between;align-items:center;box-sizing:border-box}.order-detail .order-detail-box .header .title[data-v-73eb3f26]{display:inline-block;position:relative;line-height:1}.order-detail .order-detail-box .detail-body[data-v-73eb3f26]{width:100%;padding:0 %?30?% %?30?% %?30?%;box-sizing:border-box}.order-detail .order-detail-box .detail-body .detail-body-box[data-v-73eb3f26]{width:100%;height:100%;display:flex}.order-detail .order-detail-box .detail-body .detail-body-box .goods-image[data-v-73eb3f26]{width:%?180?%;height:%?180?%;border-radius:%?10?%}.order-detail .order-detail-box .detail-body .detail-body-box .goods-image uni-image[data-v-73eb3f26]{width:100%;height:100%;border:%?1?% solid #eee;border-radius:%?10?%}.order-detail .order-detail-box .detail-body .detail-body-box .order-info[data-v-73eb3f26]{width:calc(100% - %?200?%);height:%?180?%;padding-left:%?20?%;box-sizing:border-box;display:flex;flex-direction:column;justify-content:space-between}.order-detail .order-detail-box .detail-body .detail-body-box .order-info .goods-name[data-v-73eb3f26]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%}.order-detail .order-detail-box .detail-body .detail-body-box .order-info .goods-sub-section[data-v-73eb3f26]{width:100%;line-height:1.3;display:flex}.order-detail .order-detail-box .detail-body .detail-body-box .order-info .goods-sub-section .goods-price[data-v-73eb3f26]{font-size:%?28?%}.order-detail .order-detail-box .detail-body .detail-body-box .order-info .goods-sub-section .unit[data-v-73eb3f26]{font-weight:400;font-size:%?24?%;margin-right:%?2?%}.order-detail .order-detail-box .detail-body .detail-body-box .order-info .goods-sub-section uni-view[data-v-73eb3f26]{flex:1;line-height:1.3}.order-detail .order-detail-box .detail-body .detail-body-box .order-info .goods-sub-section uni-view[data-v-73eb3f26]:last-of-type{text-align:right}.order-detail .order-detail-box .detail-body .detail-body-box .order-info .goods-sub-section uni-view:last-of-type .iconfont[data-v-73eb3f26]{line-height:1;font-size:%?28?%}.order-detail .order-detail-box .detail-content[data-v-73eb3f26]{width:100%;padding:0 %?30?% %?30?% %?30?%;box-sizing:border-box;border-bottom:%?1?% solid #eee}.order-detail .order-detail-box .detail-content uni-text[data-v-73eb3f26]{font-size:%?28?%}.order-detail .order-detail-box .detail-content .order-info-item .tit[data-v-73eb3f26]{display:inline-block}.order-detail .order-detail-box .detail-footer[data-v-73eb3f26]{width:100%;height:%?100?%;padding:%?20?%;box-sizing:border-box;display:flex;justify-content:space-between;align-items:center}.order-detail .order-detail-box .detail-footer .total[data-v-73eb3f26]{font-weight:600}.commission[data-v-73eb3f26]{margin-top:%?20?%}.commission .detail-content[data-v-73eb3f26]{border:0!important}.order-money-detail[data-v-73eb3f26]{width:100%;padding:0 %?20?%;box-sizing:border-box;margin-top:%?20?%}.order-money-detail .order-money-detail-box[data-v-73eb3f26]{width:100%;height:100%;padding-top:%?20?%;box-sizing:border-box;background:#fff;border-radius:%?10?%}.order-money-detail .order-money-detail-box .header[data-v-73eb3f26]{width:100%;height:%?70?%;padding:0 %?20?%;border-bottom:%?1?% solid #eee;display:flex;align-items:center;box-sizing:border-box}.order-money-detail .order-money-detail-box .header .title[data-v-73eb3f26]{padding-left:%?20?%;display:inline-block;position:relative;line-height:1;font-weight:600}.order-money-detail .order-money-detail-box .header .title[data-v-73eb3f26]::before{content:"";display:block;width:%?4?%;height:100%;position:absolute;left:0;top:0;border-radius:%?6?%}.price-color[data-v-73eb3f26]{color:var(--price-color)}',""]),e.exports=t},"8ba8":function(e,t,o){"use strict";o.d(t,"b",(function(){return i})),o.d(t,"c",(function(){return a})),o.d(t,"a",(function(){}));var i=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},"9fbb":function(e,t,o){"use strict";o("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{fenxiaoWords:{}}},methods:{getFenxiaoWrods:function(){var e=this;this.$api.sendRequest({url:"/fenxiao/api/config/words",success:function(t){t.code>=0&&t.data&&(e.fenxiaoWords=t.data,uni.setStorageSync("fenxiaoWords",t.data))}})}},onShow:function(){uni.getStorageSync("fenxiaoWords")&&(this.fenxiaoWords=uni.getStorageSync("fenxiaoWords")),this.getFenxiaoWrods()}};t.default=i},b095:function(e,t,o){"use strict";o.d(t,"b",(function(){return a})),o.d(t,"c",(function(){return r})),o.d(t,"a",(function(){return i}));var i={pageMeta:o("7854").default,nsLogin:o("2910").default,loadingCover:o("c003").default},a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("v-uni-view",[o("page-meta",{attrs:{"page-style":e.themeColor}}),o("v-uni-view",[o("v-uni-view",{staticClass:"order-detail"},[o("v-uni-view",{staticClass:"order-detail-box"},[o("v-uni-view",{staticClass:"header"},[o("v-uni-view",{staticClass:"title"},[o("v-uni-text",[e._v("共"+e._s(e.orderData.num)+"件商品")])],1),1==e.orderData.is_refund?o("v-uni-text",{staticClass:"color-base-text font-size-tag"},[e._v("已退款")]):1==e.orderData.is_settlement?o("v-uni-text",{staticClass:"color-base-text font-size-tag"},[e._v("已结算")]):o("v-uni-text",{staticClass:"color-base-text font-size-tag"},[e._v("待结算")])],1),o("v-uni-view",{staticClass:"detail-body"},[o("v-uni-view",{staticClass:"detail-body-box"},[o("v-uni-view",{staticClass:"goods-image"},[o("v-uni-image",{attrs:{src:e.$util.img(e.orderData.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.imageError()}}})],1),o("v-uni-view",{staticClass:"order-info"},[o("v-uni-view",{staticClass:"goods-name"},[e._v(e._s(e.orderData.sku_name))]),o("v-uni-view",{staticClass:"goods-sub-section margin-top"},[o("v-uni-view",[o("v-uni-text",{staticClass:"goods-price"},[o("v-uni-text",{staticClass:"unit price-color"},[e._v("￥")]),o("v-uni-text",{staticClass:"price-color font-size-toolbar"},[e._v(e._s(parseFloat(e.orderData.price).toFixed(2).split(".")[0]))]),o("v-uni-text",{staticClass:"unit price-color"},[e._v("."+e._s(parseFloat(e.orderData.price).toFixed(2).split(".")[1]))])],1)],1),o("v-uni-view",[o("v-uni-text",[o("v-uni-text",{staticClass:"iconfont icon-close"}),e._v(e._s(e.orderData.num))],1)],1)],1)],1)],1)],1),o("v-uni-view",{staticClass:"detail-footer"},[o("v-uni-text"),o("v-uni-text",[o("v-uni-text",[e._v("合计：")]),o("v-uni-text",{staticClass:"price-color total"},[e._v("￥"+e._s(e.orderData.real_goods_money))])],1)],1)],1),o("v-uni-view",{staticClass:"order-detail-box commission"},[o("v-uni-view",{staticClass:"header"},[o("v-uni-view",{staticClass:"title color-base-bg-before"},[o("v-uni-text",[e._v("返佣详情")])],1),o("v-uni-text",{staticClass:"color-base-text"},[e._v("")])],1),o("v-uni-view",{staticClass:"detail-content"},[o("v-uni-view",{staticClass:"order-info-item"},[o("v-uni-text",{staticClass:"tit"},[e._v("订单编号：")]),o("v-uni-text",[e._v(e._s(e.orderData.order_no))])],1),o("v-uni-view",{staticClass:"order-info-item"},[o("v-uni-text",{staticClass:"tit"},[e._v("分佣层级：")]),o("v-uni-text",[e._v(e._s(e.orderData.commission_level)+"级")])],1),o("v-uni-view",{staticClass:"order-info-item"},[o("v-uni-text",{staticClass:"tit"},[e._v("返佣金额：")]),o("v-uni-text",{staticClass:"price-color font-size-toolbar"},[o("v-uni-text",{staticClass:"font-size-goods-tag"},[e._v("￥")]),e._v(e._s(parseFloat(e.orderData.commission).toFixed(2).split(".")[0])),o("v-uni-text",{staticClass:"font-size-goods-tag"},[e._v("."+e._s(parseFloat(e.orderData.commission).toFixed(2).split(".")[1]))])],1)],1)],1)],1)],1),o("ns-login",{ref:"login"}),o("loading-cover",{ref:"loadingCover"})],1)],1)},r=[]},b2d4:function(e,t,o){"use strict";o.r(t);var i=o("b095"),a=o("5fa8");for(var r in a)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(r);o("5f72");var d=o("828b"),n=Object(d["a"])(a["default"],i["b"],i["c"],!1,null,"73eb3f26",null,!1,i["a"],void 0);t["default"]=n.exports},cc1b:function(e,t,o){"use strict";o("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("5ef2"),o("64aa"),o("5c47"),o("a1c1"),o("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(e){return-1!==["dark","light"].indexOf(e)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var e=this,t=getCurrentPages()[0];this.$pageVm=t.$vm||t,uni.onWindowResize((function(t){e.$emit("resize",t)})),this.$pageVm.$on("hook:onPageScroll",(function(t){e.$emit("scroll",t)})),this.$watch("backgroundTextStyle",(function(){e.setBackgroundTextStyle()})),this.$watch((function(){return[e.rootFontSize,e.pageStyle]}),(function(){e.setPageMeta()})),this.$watch((function(){return[e.backgroundColor,e.backgroundColorTop,e.backgroundColorBottom]}),(function(){e.setBackgroundColor()})),this.$watch((function(){return[e.scrollTop,e.scrollDuration]}),(function(){e.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(e,t){e.setStyle({pullToRefresh:{support:t,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var e=this,t=String(this.scrollTop);if(-1!==t.indexOf("rpx")&&(t=uni.upx2px(t.replace("rpx",""))),t=parseFloat(t),!isNaN(t)){var o=function o(a){a.scrollTop===t&&(e.$pageVm.$off("hook:onPageScroll",o),e.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:t,duration:this.scrollDuration,success:function(){e.$pageVm.$on("hook:onPageScroll",o)}})}}}};t.default=a},d9b4:function(e,t,o){var i=o("79d0");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=o("967d").default;a("94e1220a",i,!0,{sourceMap:!1,shadowMode:!1})},f48d:function(e,t,o){"use strict";o.r(t);var i=o("cc1b"),a=o.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a}}]);