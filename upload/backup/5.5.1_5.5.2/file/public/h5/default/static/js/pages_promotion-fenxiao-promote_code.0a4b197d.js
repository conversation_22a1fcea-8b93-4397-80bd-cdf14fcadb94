(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-fenxiao-promote_code"],{"015d":function(t,e,n){"use strict";n.r(e);var i=n("0f46"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},"0f46":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=i},"30f7":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n("7a76"),n("c9b5")},"36ea":function(t,e,n){"use strict";var i=n("f2c1"),o=n.n(i);o.a},4733:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(n("8d0b"))},"77c6":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={pageMeta:n("7854").default,uniPopup:n("d745").default,nsLogin:n("2910").default,hoverNav:n("c1f1").default,loadingCover:n("c003").default},o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("page-meta",{attrs:{"page-style":t.themeColor}}),n("v-uni-view",{staticClass:"container"},[n("v-uni-swiper",{staticClass:"swiper",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.getIndex.apply(void 0,arguments)}}},t._l(t.poster,(function(e,i){return n("v-uni-swiper-item",{key:i},[n("v-uni-view",{staticClass:"swiper-item"},[n("v-uni-view",{staticClass:"poster-wrap"},[n("v-uni-image",{attrs:{src:t.$util.img(e),mode:"widthFix","show-menu-by-longpress":!0}})],1)],1)],1)})),1),n("v-uni-view",{staticClass:"tips"},[t._v("长按识别图中二维码")]),n("uni-popup",{ref:"popupDialog",attrs:{custom:!0,"mask-click":!1}},[n("v-uni-view",{staticClass:"dialog-popup"},[n("v-uni-view",{staticClass:"title"},[t._v("提示")]),n("v-uni-view",{staticClass:"message"},[t._v("您拒绝了保存图片到相册的授权请求，无法保存图片到相册，如需正常使用，请授权之后再进行操作。")]),n("v-uni-view",{staticClass:"action-wrap"},[n("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeDialog.apply(void 0,arguments)}}},[t._v("取消")]),n("v-uni-view",[n("v-uni-button",{attrs:{type:"default","open-type":"openSetting","hover-class":"none"},on:{opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.closeDialog.apply(void 0,arguments)}}},[t._v("立即授权")])],1)],1)],1)],1),n("ns-login",{ref:"login"}),n("hover-nav"),n("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},7854:function(t,e,n){"use strict";n.r(e);var i=n("8ba8"),o=n("f48d");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);var r=n("828b"),c=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=c.exports},"795e":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container[data-v-cb5ecfba]{width:100vw;min-height:100vh;background-color:#f5f5f5}.poster-wrap[data-v-cb5ecfba]{padding:%?40?% 0;width:calc(100vw - %?80?%);margin:0 %?40?%;line-height:1}.poster-wrap uni-image[data-v-cb5ecfba]{border-radius:%?20?%;overflow:hidden;width:100%}.swiper[data-v-cb5ecfba]{height:%?1240?%}.btn[data-v-cb5ecfba]{margin:0 %?80?%;margin-top:%?30?%;height:%?80?%;line-height:%?80?%;border-radius:%?10?%;color:#fff;text-align:center}.tips[data-v-cb5ecfba]{text-align:center;font-size:%?28?%;color:#999;font-weight:600;margin-top:%?20?%}.dialog-popup[data-v-cb5ecfba]{width:%?580?%;background:#fff;box-sizing:border-box;border-radius:%?10?%;overflow:hidden;height:auto}.dialog-popup .title[data-v-cb5ecfba]{padding:%?30?% %?30?% 0 %?30?%;text-align:center;font-size:%?32?%;font-weight:700}.dialog-popup .message[data-v-cb5ecfba]{padding:0 %?30?%;color:#666;text-align:center;font-size:%?28?%;line-height:1.3;margin-top:%?30?%}.dialog-popup .action-wrap[data-v-cb5ecfba]{margin-top:%?50?%;height:%?80?%;display:flex;border-top:%?2?% solid #eee}.dialog-popup .action-wrap > uni-view[data-v-cb5ecfba]{flex:1;text-align:center;line-height:%?80?%}.dialog-popup .action-wrap > uni-view[data-v-cb5ecfba]:first-child{border-right:%?2?% solid #eee;color:#999}.dialog-popup .action-wrap > uni-view uni-button[data-v-cb5ecfba]{border:none;line-height:%?80?%;padding:0;margin:0;width:100%;height:100%}',""]),t.exports=e},"81ea":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("b7c7"));n("c9b5"),n("bf0f"),n("ab80"),n("d4b5"),n("2797"),n("aa9c");n("edd0");var a=i(n("d745")),r={data:function(){return{poster:[],fenxiaoInfo:{},posterIndex:0,templateId:["default"],mpShareData:null}},components:{uniPopup:a.default},methods:{getPosterData:function(){var t=this;this.$api.sendRequest({url:"/fenxiao/api/fenxiao/posterList",success:function(e){e.code>=0&&(t.poster=e.data.toString(),t.poster=encodeURIComponent(t.poster),t.poster?(t.poster=decodeURIComponent(t.poster).split(","),setTimeout((function(){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}),500)):t.getFenxiaoDetail())}})},getPoster:function(t){var e=this;return new Promise((function(n,i){e.$api.sendRequest({url:"/fenxiao/api/fenxiao/poster",data:{page:"/pages/index/index",qrcode_param:JSON.stringify({}),template_id:t},success:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide(),t.code>=0&&n(t.data.path)},fail:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.hide(),i()}})}))},getIndex:function(t){this.posterIndex=t.detail.current},save:function(){},getFenxiaoDetail:function(){var t=this;this.poster=[];try{this.templateId.forEach((function(e,n){t.getPoster(e).then((function(e){t.poster.push(e),t.$refs.loadingCover&&t.$refs.loadingCover.hide()})).catch((function(t){throw t}))}))}catch(e){this.$util.showToast({title:"海报生成失败"})}},closeDialog:function(){this.$refs.popupDialog.close()},getTemplateId:function(){var t=this;this.$api.sendRequest({url:"/fenxiao/api/fenxiao/posterTemplateIds",success:function(e){e.code>=0&&(t.templateId=(0,o.default)(e.data).join())}})}},onLoad:function(t){var e=this;setTimeout((function(){e.addonIsExist.fenxiao||(e.$util.showToast({title:"商家未开启分销",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.templateId&&(this.templateId=t.templateId.split(",")),this.storeToken?(this.getTemplateId(),this.getPosterData()):this.$nextTick((function(){e.$refs.login.open("/pages_promotion/fenxiao/promote_code")}))},onShow:function(){},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},watch:{storeToken:function(t,e){t&&this.getFenxiaoDetail()}}};e.default=r},"8ba8":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},"971a":function(t,e,n){"use strict";n.r(e);var i=n("77c6"),o=n("e9dc");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("36ea");var r=n("828b"),c=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"cb5ecfba",null,!1,i["a"],void 0);e["default"]=c.exports},a725:function(t,e,n){"use strict";var i=n("ac2a"),o=n.n(i);o.a},ac2a:function(t,e,n){var i=n("f714");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=n("967d").default;o("1a69ffc2",i,!0,{sourceMap:!1,shadowMode:!1})},b7c7:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,o.default)(t)||(0,a.default)(t)||(0,r.default)()};var i=c(n("4733")),o=c(n("d14d")),a=c(n("5d6b")),r=c(n("30f7"));function c(t){return t&&t.__esModule?t:{default:t}}},c1f1:function(t,e,n){"use strict";n.r(e);var i=n("fa1d"),o=n("015d");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("a725");var r=n("828b"),c=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"c1934e78",null,!1,i["a"],void 0);e["default"]=c.exports},cc1b:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("64aa"),n("5c47"),n("a1c1"),n("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var n=function n(o){o.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",n),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",n)}})}}}};e.default=o},d14d:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("08eb")},e9dc:function(t,e,n){"use strict";n.r(e);var i=n("81ea"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},f2c1:function(t,e,n){var i=n("795e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=n("967d").default;o("c6a57b08",i,!0,{sourceMap:!1,shadowMode:!1})},f48d:function(t,e,n){"use strict";n.r(e);var i=n("cc1b"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},f714:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return 1==t.pageCount||t.need?n("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?n("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[n("v-uni-text",{staticClass:"iconfont icon-shouye1"}),n("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?n("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[n("v-uni-text",{staticClass:"iconfont icon-yonghu"}),n("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?n("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[n("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):n("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[n("v-uni-view",[t._v("快捷")]),n("v-uni-view",[t._v("导航")])],1)],1):t._e()},o=[]}}]);