(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-goods-brand"],{"015d":function(t,e,n){"use strict";n.r(e);var i=n("0f46"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"0f46":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=i},2407:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.advList.length?n("v-uni-view",{class:["container-box",t.className]},[t.advList.length>1?n("v-uni-swiper",{staticClass:"item-wrap",style:{height:t.swiperHeight+"px"},attrs:{"indicator-dots":t.advList.length>1,"indicator-active-color":"#ffffff",autoplay:!0,interval:3e3,duration:1e3,current:t.currentIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSwiper.apply(void 0,arguments)}}},t._l(t.advList,(function(e,i){return n("v-uni-swiper-item",{key:i,on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.jumppage(e.adv_url)}}},[n("v-uni-view",{staticClass:"image-box"},[n("v-uni-image",{attrs:{src:t.$util.img(e.adv_image),mode:"widthFix",id:"content-wrap"+i}})],1)],1)})),1):n("v-uni-view",{staticClass:"container-box item-wrap"},[n("v-uni-image",{attrs:{src:t.$util.img(t.advList[0]["adv_image"]),mode:"widthFix","lazy-load":"true"},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imageLoad.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumppage(t.advList[0].adv_url)}}})],1)],1):t._e()},a=[]},"46b5":function(t,e,n){var i=n("d00f");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("38659d82",i,!0,{sourceMap:!1,shadowMode:!1})},6102:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5c47");var i={name:"ns-advert",props:{keyword:{type:String},className:{type:String}},data:function(){return{advList:[],isImage:!1,swiperHeight:150,currentIndex:0}},created:function(){this.getAdvList()},methods:{getAdvList:function(){var t=this;this.$api.sendRequest({url:"/api/adv/detail",data:{keyword:this.keyword},success:function(e){if(0==e.code){var n=e.data.adv_list;for(var i in n)n[i].adv_url&&(n[i].adv_url=JSON.parse(n[i].adv_url));t.advList=e.data.adv_list,t.$nextTick((function(){t.setSwiperHeight()}))}}})},jumppage:function(t){this.$util.diyRedirectTo(t)},imageLoad:function(t){this.isImage=!0},changeSwiper:function(t){var e=this;this.currentIndex=t.detail.current,this.$nextTick((function(){e.setSwiperHeight()}))},setSwiperHeight:function(){var t=this;this.advList.length>1&&setTimeout((function(){var e="#content-wrap"+t.currentIndex,n=uni.createSelectorQuery().in(t);n.select(e).boundingClientRect(),n.exec((function(e){e&&e[0]&&(t.swiperHeight=e[0].height)}))}),10)}}};e.default=i},"7e88":function(t,e,n){"use strict";n.r(e);var i=n("2407"),a=n("f016");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("a44f");var o=n("828b"),d=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"9caa2b5c",null,!1,i["a"],void 0);e["default"]=d.exports},"9bf1":function(t,e,n){"use strict";var i=n("46b5"),a=n.n(i);a.a},a284:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("c223");var a=i(n("cf69")),r=i(n("0466")),o=i(n("7e88")),d={components:{uniGrid:a.default,uniGridItem:r.default,nsAdv:o.default},data:function(){return{brandList:[],siteId:0}},onLoad:function(t){t.site_id&&(this.siteId=t.site_id)},onShow:function(){},methods:{change:function(t){this.$util.redirectTo("/pages/goods/list",{brand_id:this.brandList[t.detail.index].brand_id})},getBrandList:function(t){var e=this;this.$api.sendRequest({url:"/api/goodsbrand/page",data:{page_size:t.size,page:t.num,site_id:this.siteId},success:function(n){var i=[],a=n.message;0==n.code&&n.data?i=n.data.list:e.$util.showToast({title:a}),t.endSuccess(i.length),1==t.num&&(e.brandList=[]),e.brandList=e.brandList.concat(i),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(){t.endErr(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})}},onShareAppMessage:function(t){return{title:"你想要的大牌都在这里",path:"/pages_tool/goods/brand",success:function(t){},fail:function(t){}}}};e.default=d},a44f:function(t,e,n){"use strict";var i=n("d87f"),a=n.n(i);a.a},a725:function(t,e,n){"use strict";var i=n("ac2a"),a=n.n(i);a.a},ac2a:function(t,e,n){var i=n("f714");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("1a69ffc2",i,!0,{sourceMap:!1,shadowMode:!1})},c1f1:function(t,e,n){"use strict";n.r(e);var i=n("fa1d"),a=n("015d");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("a725");var o=n("828b"),d=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"c1934e78",null,!1,i["a"],void 0);e["default"]=d.exports},d00f:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-4d835f22] .uni-grid-item{width:calc((100vw - (%?30?% * 2)) / 3)!important}.adv-wrap[data-v-4d835f22]{margin:%?20?% %?30?%;width:auto}.brand-content[data-v-4d835f22]{padding:%?20?% 0;box-sizing:border-box;background:#fff;margin:%?20?% %?30?% 0}.brand-content .brand-pic[data-v-4d835f22]{width:60%;height:50%}.brand-content .brand_name[data-v-4d835f22]{width:70%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-align:center}',""]),t.exports=e},d87f:function(t,e,n){var i=n("d915");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("03d75754",i,!0,{sourceMap:!1,shadowMode:!1})},d915:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-9caa2b5c]{width:100%}.container-box .item-wrap[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-9caa2b5c]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-9caa2b5c]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}',""]),t.exports=e},e170:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={nsAdv:n("7e88").default,uniGrid:n("cf69").default,uniGridItem:n("0466").default,nsEmpty:n("52a6").default,hoverNav:n("c1f1").default,loadingCover:n("c003").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{attrs:{"data-theme":t.themeStyle}},[n("mescroll-uni",{ref:"mescroll",attrs:{size:"20"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getBrandList.apply(void 0,arguments)}}},[n("template",{attrs:{slot:"list"},slot:"list"},[n("ns-adv",{attrs:{keyword:"NS_BRAND","class-name":"adv-wrap"}}),t.brandList.length>0?n("v-uni-view",{staticClass:"brand-content"},[n("uni-grid",{attrs:{column:3,showBorder:!1},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)}}},t._l(t.brandList,(function(e,i){return n("uni-grid-item",{key:i,attrs:{index:"index"}},[n("v-uni-image",{staticClass:"brand-pic",attrs:{src:t.$util.img(e.image_url),mode:"widthFix"}}),n("v-uni-view",{staticClass:"brand_name"},[t._v(t._s(e.brand_name))])],1)})),1)],1):t._e(),0==t.brandList.length?n("v-uni-view",[n("ns-empty",{attrs:{text:"暂无更多品牌,去首页看看吧"}})],1):t._e()],1)],2),n("hover-nav"),n("loading-cover",{ref:"loadingCover"})],1)},r=[]},e45a:function(t,e,n){"use strict";n.r(e);var i=n("a284"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},ecf0:function(t,e,n){"use strict";n.r(e);var i=n("e170"),a=n("e45a");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("9bf1");var o=n("828b"),d=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"4d835f22",null,!1,i["a"],void 0);e["default"]=d.exports},f016:function(t,e,n){"use strict";n.r(e);var i=n("6102"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},f714:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return 1==t.pageCount||t.need?n("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?n("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[n("v-uni-text",{staticClass:"iconfont icon-shouye1"}),n("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?n("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[n("v-uni-text",{staticClass:"iconfont icon-yonghu"}),n("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?n("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[n("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):n("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[n("v-uni-view",[t._v("快捷")]),n("v-uni-view",[t._v("导航")])],1)],1):t._e()},a=[]}}]);