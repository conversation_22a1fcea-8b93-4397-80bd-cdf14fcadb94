(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-store_notes-note_list"],{"12a1":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={diyIcon:i("a68f").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.tabBarList&&t.tabBarList.list?i("v-uni-view",[i("v-uni-view",{staticClass:"tab-bar",style:{backgroundColor:t.tabBarList.backgroundColor}},[i("v-uni-view",{staticClass:"tabbar-border"}),t._l(t.tabBarList.list,(function(e,n){return i("v-uni-view",{key:e.id,staticClass:"item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.redirectTo(e.link)}}},[i("v-uni-view",{staticClass:"bd"},["/pages/goods/cart"==e.link.wap_url?[1==t.tabBarList.type||2==t.tabBarList.type?i("v-uni-view",{staticClass:"icon",attrs:{animation:t.cartAnimation,id:"tabbarCart"}},[t.verify(e.link)?["img"==e.selected_icon_type?i("v-uni-image",{attrs:{src:t.$util.img(e.selectedIconPath)}}):t._e(),"icon"==e.selected_icon_type?i("diy-icon",{attrs:{icon:e.selectedIconPath,value:e.selected_style?e.selected_style:null}}):t._e()]:["img"==e.icon_type?i("v-uni-image",{attrs:{src:t.$util.img(e.iconPath)}}):t._e(),"icon"==e.icon_type?i("diy-icon",{attrs:{icon:e.iconPath,value:e.style?e.style:null}}):t._e()],t.cartNumber>0?i("v-uni-view",{staticClass:"cart-count-mark font-size-activity-tag",class:{max:"/pages/goods/cart"==e.link.wap_url&&t.cartNumber>99},style:{background:"var(--price-color)"}},[t._v(t._s(t.cartNumber>99?"99+":t.cartNumber))]):t._e()],2):t._e()]:[1==t.tabBarList.type||2==t.tabBarList.type?i("v-uni-view",{staticClass:"icon"},[t.verify(e.link)?["img"==e.selected_icon_type?i("v-uni-image",{attrs:{src:t.$util.img(e.selectedIconPath)}}):t._e(),"icon"==e.selected_icon_type?i("diy-icon",{attrs:{icon:e.selectedIconPath,value:e.selected_style?e.selected_style:null}}):t._e()]:["img"==e.icon_type?i("v-uni-image",{attrs:{src:t.$util.img(e.iconPath)}}):t._e(),"icon"==e.icon_type?i("diy-icon",{attrs:{icon:e.iconPath,value:e.style?e.style:null}}):t._e()]],2):t._e()],1!=t.tabBarList.type&&3!=t.tabBarList.type||"diy"!=t.tabBarList.theme?t._e():i("v-uni-view",{staticClass:"label",style:{color:t.verify(e.link)?t.tabBarList.textHoverColor:t.tabBarList.textColor}},[t._v(t._s(e.text))]),1!=t.tabBarList.type&&3!=t.tabBarList.type||"default"!=t.tabBarList.theme?t._e():i("v-uni-view",{staticClass:"label",style:{color:t.verify(e.link)?"var(--base-color)":"#333333"}},[t._v(t._s(e.text))])],2)],1)}))],2),i("v-uni-view",{staticClass:"tab-bar-placeholder"})],1):t._e()},a=[]},1499:function(t,e,i){"use strict";var n=i("3195"),o=i.n(n);o.a},2532:function(t,e,i){"use strict";i.r(e);var n=i("12a1"),o=i("e926");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("7d44");var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"09d90d92",null,!1,n["a"],void 0);e["default"]=s.exports},"29ac":function(t,e,i){"use strict";i.r(e);var n=i("b791"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},3195:function(t,e,i){var n=i("c50e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("179bf0ea",n,!0,{sourceMap:!1,shadowMode:!1})},3488:function(t,e,i){var n=i("c4cd");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("38184dc6",n,!0,{sourceMap:!1,shadowMode:!1})},"64a3":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47"),i("5ef2");var n={name:"diy-bottom-nav",props:{value:{type:Object},name:{type:String,default:""}},data:function(){return{currentRoute:"",jumpFlag:!0,cartAnimation:{}}},mounted:function(){var t=this,e=getCurrentPages()[getCurrentPages().length-1];e&&e.route&&(this.currentRoute=e.route),this.$nextTick((function(){if(!t.$store.state.cartPosition){var e=uni.createSelectorQuery().in(t);e.select("#tabbarCart").boundingClientRect((function(e){e&&t.$store.commit("setCartPosition",e)})).exec(),e.select(".tab-bar").boundingClientRect((function(e){e&&t.$store.commit("setTabBarHeight",e.height+"px")})).exec()}}))},computed:{cartChange:function(){return this.$store.state.cartChange}},watch:{cartChange:function(t,e){var i=this;if(t>e){var n=uni.createAnimation({duration:200,timingFunction:"ease"});n.scale(1.2).step(),this.cartAnimation=n.export(),setTimeout((function(){n.scale(1).step(),i.cartAnimation=n.export()}),300)}}},methods:{redirectTo:function(t){this.$emit("callback"),this.$util.diyRedirectTo(t)},verify:function(t){if(null==t||""==t||!t.wap_url)return!1;if(this.name)var e=this.currentRoute+"?name="+this.name;else e=this.currentRoute;return"/pages/index/index"==t.wap_url&&"DIY_VIEW_INDEX"==this.name||!(!e||-1==t.wap_url.indexOf(e))}}};e.default=n},"729a":function(t,e,i){"use strict";i.r(e);var n=i("b9c0"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},7854:function(t,e,i){"use strict";i.r(e);var n=i("8ba8"),o=i("f48d");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"7d44":function(t,e,i){"use strict";var n=i("d1d0"),o=i.n(n);o.a},"7e2d":function(t,e,i){"use strict";i.r(e);var n=i("b0e3"),o=i("729a");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("1499");var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"60e68c57",null,!1,n["a"],void 0);e["default"]=s.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},a68f:function(t,e,i){"use strict";i.r(e);var n=i("acc8"),o=i("29ac");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("c225");var r=i("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"1839a53e",null,!1,n["a"],void 0);e["default"]=s.exports},acc8:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"diy-icon",style:this.iconBgStyle},[e("v-uni-text",{staticClass:"js-icon",class:this.iconClass,style:this.iconStyle})],1)},o=[]},b0e3:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={pageMeta:i("7854").default,loadingCover:i("c003").default,nsEmpty:i("52a6").default,diyBottomNav:i("2532").default,nsLogin:i("2910").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",[i("v-uni-view",{staticClass:"notes-nav"},[i("v-uni-scroll-view",{staticClass:"notes-list",attrs:{"scroll-x":"true"}},t._l(t.groupInfo.groupList,(function(e,n){return i("v-uni-text",{key:n,staticClass:"notes-item",class:n==t.groupInfo.currIdent?"color-base-text active color-base-border":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.tabCut(e.group_id,n)}}},[t._v(t._s(e.group_name))])})),1)],1),0!=t.groupInfo.currId?i("mescroll-uni",{ref:"mescroll",attrs:{top:"94"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getNotesList.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[i("v-uni-view",{staticClass:"notes-content"},t._l(t.noteListInfo,(function(e,n){return i("v-uni-view",{key:n,staticClass:"note-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.noteDetail(e.note_id)}}},[i("v-uni-text",{staticClass:"note-title"},[t._v(t._s(e.note_title))]),e.goods_highlights?i("v-uni-view",{staticClass:"notes-highlights-list"},t._l(e.label,(function(e,n){return i("v-uni-text",{key:n,staticClass:"color-base-bg"},[t._v(t._s(e))])})),1):t._e(),i("v-uni-view",{staticClass:"note-desc"},[i("v-uni-text",{staticClass:"color-base-text"},[t._v("#"+t._s("goods_item"==e.note_type?"单品介绍":"掌柜说")+"#")]),t._v(t._s(e.note_abstract))],1),i("v-uni-view",{staticClass:"notes-img-wrap",class:{"notes-img-wrap-list":1==e.cover_type}},[0==e.cover_type?i("v-uni-image",{staticClass:"notes-item-image",attrs:{src:t.$util.img(e.cover_img),mode:"aspectFill"}}):t._l(e.img,(function(e,n){return i("v-uni-image",{key:n,staticClass:"notes-item-image-li",attrs:{src:t.$util.img(e),mode:"aspectFit"}})}))],2),i("v-uni-view",{staticClass:"rest-info"},[1==e.is_show_release_time?i("v-uni-view",{staticClass:"time"},[t._v(t._s(t.$util.timeStampTurnTime(e.create_time,"Y-m-d")))]):t._e(),i("v-uni-view",{staticClass:"read-info"},[1==e.is_show_read_num?i("v-uni-text",[t._v("阅读"),i("v-uni-text",[t._v(t._s(e.initial_read_num+e.read_num))])],1):t._e(),1==e.is_show_dianzan_num?i("v-uni-text",{on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.giveLike(e.note_id,n)}}},[1==e.is_dianzan?i("v-uni-text",{staticClass:"iconfont icon-likefill color-base-text"}):i("v-uni-text",{staticClass:"iconfont icon-gz"}),i("v-uni-text",[t._v(t._s(e.initial_dianzan_num+e.dianzan_num))])],1):t._e()],1)],1)],1)})),1)],1),i("loading-cover",{ref:"loadingCover"})],2):t._e(),t.showEmpty&&0==t.noteListInfo.length?i("v-uni-view",{staticClass:"empty-box"},[i("ns-empty",{attrs:{isIndex:!1,text:"暂无店铺笔记"}})],1):t._e(),i("v-uni-view",{staticClass:"page-bottom"},[i("diy-bottom-nav")],1),i("ns-login",{ref:"login"})],1)],1)},a=[]},b791:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"diy-icon",props:{icon:{type:String,default:""},value:{type:Object,default:function(){return null}}},computed:{iconClass:function(){var t=" "+this.icon;return this.value&&this.value.iconColor.length>1&&(t+=" gradient"),t},iconBgStyle:function(){if(!this.value)return{};var t={"border-radius":this.value.bgRadius+"%",background:""};return this.value.iconBgImg&&(t["background"]+="url("+this.$util.img(this.value.iconBgImg)+") no-repeat bottom / contain"),this.value.iconBgColor.length&&(t.background&&(t.background+=","),1==this.value.iconBgColor.length?t.background+=this.value.iconBgColor[0]:t["background"]+="linear-gradient("+this.value.iconBgColorDeg+"deg, "+this.value.iconBgColor.join(",")+")"),this.$util.objToStyle(t)},iconStyle:function(){if(!this.value)return{};var t={"font-size":this.value.fontSize+"%"};return 1==this.value.iconColor.length?t.color=this.value.iconColor[0]:t["background"]="linear-gradient("+this.value.iconColorDeg+"deg, "+this.value.iconColor.join(",")+")",this.$util.objToStyle(t)}}};e.default=n},b9c0:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("c223");var n={data:function(){return{groupInfo:{groupList:[],currIdent:0,currId:0},noteListInfo:[],giveLikeFlag:!1,showEmpty:!1,mpShareData:null}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.notes||(e.$util.showToast({title:"商家未开启店铺笔记",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member")),this.getGroup(),this.$refs.mescroll&&this.$refs.mescroll.refresh()},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},methods:{getGroup:function(){var t=this;this.showEmpty=!1,this.$api.sendRequest({url:"/notes/api/notes/group",success:function(e){t.showEmpty=!0,0==e.code?0!=e.data.length&&(t.groupInfo.groupList=e.data,t.groupInfo.currId=e.data[0].group_id):t.$util.showToast({title:e.message})},fail:function(e){mescroll.endErr(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},tabCut:function(t,e){this.groupInfo.currIdent=e,this.groupInfo.currId=t,this.$refs.mescroll.refresh(),this.$refs.loadingCover&&this.$refs.loadingCover.show()},getNotesList:function(t){var e=this;this.showEmpty=!1,this.$api.sendRequest({url:"/notes/api/notes/page",data:{page:t.num,page_size:t.size,group_id:this.groupInfo.currId},success:function(i){e.showEmpty=!0;var n=[];0==i.code?n=i.data.list:e.$util.showToast({title:i.message}),t.endSuccess(n.length),1==t.num&&(e.noteListInfo=[]),e.noteListInfo=e.noteListInfo.concat(n),e.noteListInfo.forEach((function(t,e){t.img=t.cover_img.split(","),t.label=t.goods_highlights.split(",")})),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(i){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},noteDetail:function(t){this.$util.redirectTo("/pages_tool/store_notes/note_detail",{note_id:t})},giveLike:function(t,e){var i=this;if(this.storeToken){if(this.giveLikeFlag)return!1;this.giveLikeFlag=!0;var n=1==this.noteListInfo[e].is_dianzan?"/notes/api/record/delete":"/notes/api/record/add";this.$api.sendRequest({url:n,data:{note_id:t},success:function(t){i.giveLikeFlag=!1,0==t.code&&t.data>0?(i.noteType,i.noteListInfo[e].dianzan_num=1==i.noteListInfo[e].is_dianzan?i.noteListInfo[e].dianzan_num-1:i.noteListInfo[e].dianzan_num+1,i.noteListInfo[e].is_dianzan=1==i.noteListInfo[e].is_dianzan?0:1):i.$util.showToast({title:t.message})}})}else this.$refs.login.open("/pages_tool/store_notes/note_list")}}};e.default=n},c225:function(t,e,i){"use strict";var n=i("3488"),o=i.n(n);o.a},c4cd:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.diy-icon[data-v-1839a53e]{width:100%;height:100%;font-size:100%;color:#000;display:flex;align-items:center;justify-content:center}.diy-icon .js-icon[data-v-1839a53e]{font-size:50%;line-height:1;padding:%?1?%}.diy-icon .js-icon.gradient[data-v-1839a53e]{-webkit-background-clip:text!important;-webkit-text-fill-color:transparent}',""]),t.exports=e},c50e:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.notes-nav[data-v-60e68c57]{overflow:hidden;padding:0 %?20?%;background-color:#fff}.notes-nav .notes-list[data-v-60e68c57]{display:flex;align-items:center;width:100%;height:%?90?%;white-space:nowrap}.notes-nav .notes-list .notes-item[data-v-60e68c57]{display:inline-block;margin:0 %?20?%;height:%?86?%;text-align:center;line-height:%?90?%}.notes-nav .notes-list .notes-item.active[data-v-60e68c57]{border-bottom:%?4?% solid}.notes-content[data-v-60e68c57]{height:100%;width:100%}.note-item[data-v-60e68c57]{margin:%?30?% %?20?%;padding:%?20?%;background-color:#fff;border-radius:%?6?%;overflow:hidden;position:relative}.note-item .note-title[data-v-60e68c57]{font-size:%?30?%;font-weight:600;line-height:%?44?%;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.note-item .notes-highlights-list uni-text[data-v-60e68c57]{display:inline-block;color:#fff;font-size:%?24?%;line-height:%?36?%;padding:0 %?10?%;border-radius:%?4?%;margin:0 %?5?%}.note-item .note-desc[data-v-60e68c57]{margin:%?4?% 0 %?8?%;line-height:%?40?%;overflow:hidden}.note-item .note-desc uni-text[data-v-60e68c57]{float:left;margin-right:%?16?%}.note-item .notes-img-wrap[data-v-60e68c57]{position:relative;height:%?300?%}.note-item .notes-img-wrap .notes-item-image[data-v-60e68c57]{width:100%;height:%?300?%;object-fit:cover}.note-item .notes-img-wrap-list[data-v-60e68c57]{display:flex;justify-content:space-between;flex-wrap:wrap;height:auto}.note-item .notes-img-wrap-list uni-image[data-v-60e68c57]{width:calc((100% - %?40?%) / 3);height:%?210?%;margin-top:%?20?%}.note-item .notes-img-wrap-list uni-image[data-v-60e68c57]:nth-child(-n + 3){margin-top:0}.note-item .notes-img-wrap-list[data-v-60e68c57]:after{content:"";width:calc((100% - %?40?%) / 3)}.note-item .note-img-one[data-v-60e68c57]{display:flex;justify-content:center;align-items:center;width:100%;height:%?420?%}.note-item .note-img-one uni-image[data-v-60e68c57]{width:100%;height:%?420?%}.note-item .note-img-more[data-v-60e68c57]{width:100%;white-space:nowrap}.note-item .note-img-more .more-item[data-v-60e68c57]{overflow:hidden;display:inline-block;width:%?200?%;height:%?200?%;text-align:center;line-height:%?200?%;border-radius:%?10?%}.note-item .note-img-more .more-item ~ .more-item[data-v-60e68c57]{margin-left:%?16?%}.note-item .note-img-more .more-item uni-image[data-v-60e68c57]{width:%?200?%;height:%?200?%}.note-item .notes-img-wrap[data-v-60e68c57]{position:relative;height:%?300?%}.note-item .notes-img-wrap .notes-item-image[data-v-60e68c57]{width:100%;height:%?300?%;object-fit:cover}.note-item .notes-img-wrap-list[data-v-60e68c57]{display:flex;justify-content:space-between;flex-wrap:wrap;height:auto}.note-item .notes-img-wrap-list uni-image[data-v-60e68c57]{width:calc((100% - %?40?%) / 3);height:%?210?%;margin-top:%?20?%}.note-item .notes-img-wrap-list uni-image[data-v-60e68c57]:nth-child(-n + 3){margin-top:0}.note-item .notes-img-wrap-list[data-v-60e68c57]:after{content:"";width:calc((100% - %?40?%) / 3)}.note-item .rest-info[data-v-60e68c57]{display:flex;justify-content:space-between;align-items:center;margin-top:%?20?%}.note-item .rest-info .time[data-v-60e68c57],\r\n.note-item .rest-info .read-info[data-v-60e68c57]{color:#b6b6b6;font-size:%?24?%}.note-item .rest-info uni-text[data-v-60e68c57]{margin-left:%?10?%}.note-item .rest-info .read-info > uni-text[data-v-60e68c57]{margin-left:%?30?%}.empty-box[data-v-60e68c57]{margin-top:%?200?%}',""]),t.exports=e},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(o){o.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=o},d1d0:function(t,e,i){var n=i("d644");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("088dfa75",n,!0,{sourceMap:!1,shadowMode:!1})},d644:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.placeholder[data-v-09d90d92]{height:%?112?%}.placeholder.bluge[data-v-09d90d92]{height:%?180?%}.safe-area[data-v-09d90d92]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.tab-bar[data-v-09d90d92]{background-color:#fff;box-sizing:border-box;position:fixed;left:0;bottom:0;width:100%;z-index:998;display:flex;border-top:%?2?% solid #f5f5f5;padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.tab-bar .tabbar-border[data-v-09d90d92]{background-color:hsla(0,0%,100%,.329412);position:absolute;left:0;top:0;width:100%;height:%?2?%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.tab-bar .item[data-v-09d90d92]{display:flex;align-items:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;flex:1;flex-direction:column;padding-bottom:%?10?%;box-sizing:border-box}.tab-bar .item .bd[data-v-09d90d92]{position:relative;height:%?100?%;flex-direction:column;text-align:center;display:flex;justify-content:center;align-items:center}.tab-bar .item .bd .icon[data-v-09d90d92]{position:relative;display:inline-block;margin-top:%?10?%;width:%?40?%;height:%?40?%;font-size:%?40?%;line-height:%?40?%}.tab-bar .item .bd .icon uni-image[data-v-09d90d92]{width:100%;height:100%}.tab-bar .item .bd .icon > uni-view[data-v-09d90d92]{height:inherit;display:flex;align-items:center}.tab-bar .item .bd .icon .bar-icon[data-v-09d90d92]{font-size:%?42?%}.tab-bar .item .bd .label[data-v-09d90d92]{position:relative;text-align:center;font-size:%?24?%;line-height:1;margin-top:%?12?%}.tab-bar .item.bulge .bd[data-v-09d90d92]{position:relative;height:%?100?%;flex-direction:column;text-align:center}.tab-bar .item.bulge .bd .icon[data-v-09d90d92]{margin-top:%?-60?%;margin-bottom:%?4?%;border-radius:50%;width:%?100?%;height:%?102?%;padding:%?10?%;border-top:%?2?% solid #f5f5f5;background-color:#fff;box-sizing:border-box}.tab-bar .item.bulge .bd .icon uni-image[data-v-09d90d92]{width:100%;height:100%;border-radius:50%}.tab-bar .item.bulge .bd .label[data-v-09d90d92]{position:relative;text-align:center;font-size:%?24?%;height:%?40?%;line-height:%?40?%}.tab-bar .item .cart-count-mark[data-v-09d90d92]{position:absolute;top:%?-8?%;right:%?-18?%;width:%?24?%;height:%?24?%!important;display:flex;justify-content:center;align-items:center;color:#fff;padding:%?6?%;border-radius:50%;z-index:99}.tab-bar .item .cart-count-mark.max[data-v-09d90d92]{width:%?40?%;border-radius:%?24?%;right:%?-28?%}.tab-bar-placeholder[data-v-09d90d92]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?112?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?112?%)}',""]),t.exports=e},e926:function(t,e,i){"use strict";i.r(e);var n=i("64a3"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},f48d:function(t,e,i){"use strict";i.r(e);var n=i("cc1b"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a}}]);