(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-goods-search"],{"0041":function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("dc69"),e("8f71"),e("bf0f"),e("0c26"),e("5c47"),e("af8f"),e("aa9c");var o={data:function(){return{inputValue:"",historyList:[],searchList:[],alikeList:[],isIndex:!1,searchWords:"",hotList:[],isAllHistory:!1}},onLoad:function(t){t.keyword&&(this.inputValue=t.keyword),!uni.getStorageSync("search")&&uni.setStorageSync("search",[])},onShow:function(){this.findHistoryList(),this.defaultSearch(),this.findHotList(),this.$nextTick((function(){this.getHistoryHeight()}))},methods:{findHistoryList:function(){this.historyList=uni.getStorageSync("search").reverse()},deleteHistoryList:function(){var t=this;uni.showModal({title:"提示",content:"确认删除全部历史记录？",success:function(i){i.confirm&&(uni.setStorageSync("search",[]),t.findHistoryList())}})},deleteItem:function(t){var i=this;uni.showModal({title:"提示",content:"确认删除该条历史记录？",success:function(e){if(e.confirm){var o=uni.getStorageSync("search"),n=o.filter((function(i){return i!=t}));uni.setStorageSync("search",n),i.findHistoryList()}}})},defaultSearch:function(){var t=this;this.$api.sendRequest({url:"/api/goods/defaultSearchWords",success:function(i){t.searchWords=i.data.words}})},findHotList:function(){var t=this;this.$api.sendRequest({url:"/api/goods/hotSearchWords",success:function(i){""!=i.data.words&&(t.hotList=i.data.words.split(","))}})},inputFocus:function(t){""!=this.inputValue.trim()&&(this.dataList=[])},otherSearch:function(t){this.inputValue=t,this.search()},search:function(){var t=this;if(""!=this.inputValue.trim()){var i=uni.getStorageSync("search"),e=[];i.length?(e=i.filter((function(i){return i!=t.inputValue.trim()})),e.push(this.inputValue.trim())):e.push(this.inputValue.trim()),uni.setStorageSync("search",e),this.$util.redirectTo("/pages/goods/list",{keyword:this.inputValue.trim()})}else""==this.searchWords?this.$util.showToast({title:"搜索内容不能为空哦"}):this.$util.redirectTo("/pages/goods/list",{keyword:this.searchWords})},getHistoryHeight:function(){var t=this,i=uni.createSelectorQuery().in(this);i.select("#history-list").boundingClientRect((function(i){i&&i.height>2*uni.upx2px(70)+2*uni.upx2px(35)&&(t.isAllHistory=!0)})).exec()}}};i.default=o},"1a0c":function(t,i,e){"use strict";e.r(i);var o=e("0041"),n=e.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(s);i["default"]=n.a},2665:function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){return o}));var o={pageMeta:e("7854").default},n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":t.themeColor}}),e("v-uni-view",[e("v-uni-view",{staticClass:"content"},[e("v-uni-view",{staticClass:"cate-search"},[e("v-uni-view",{staticClass:"search-box"},[e("v-uni-input",{staticClass:"uni-input",attrs:{maxlength:"50","confirm-type":"search",focus:!0,placeholder:t.searchWords?t.searchWords:t.$lang("inputPlaceholder")},on:{focus:function(i){arguments[0]=i=t.$handleEvent(i),t.inputFocus.apply(void 0,arguments)},confirm:function(i){arguments[0]=i=t.$handleEvent(i),t.search()}},model:{value:t.inputValue,callback:function(i){t.inputValue=i},expression:"inputValue"}}),e("v-uni-text",{staticClass:"iconfont icon-sousuo3",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.search()}}})],1)],1),e("v-uni-view",{staticClass:"search-content"},[t.historyList.length?e("v-uni-view",{staticClass:"history"},[e("v-uni-view",{staticClass:"history-box"},[e("v-uni-view",{staticClass:"history-top"},[e("v-uni-view",{staticClass:"title"},[t._v(t._s(t.$lang("history")))]),e("v-uni-view",{staticClass:"icon iconfont icon-icon7",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.deleteHistoryList.apply(void 0,arguments)}}})],1),e("v-uni-view",{staticClass:"history-bottom ",style:{maxHeight:t.isAllHistory?"168rpx":"100%"},attrs:{id:"history-list"}},[t._l(t.historyList,(function(i,o){return e("v-uni-view",{key:o,staticClass:"history-li",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.otherSearch(i)}}},[e("v-uni-view",[t._v(t._s(i))])],1)})),t.isAllHistory?e("v-uni-view",{staticClass:"history-li history_more",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.isAllHistory=!1}}},[e("v-uni-view",[e("v-uni-text",{staticClass:"iconfont icon-iconangledown"})],1)],1):t._e()],2)],1)],1):t._e(),t.hotList.length?e("v-uni-view",{staticClass:"history"},[e("v-uni-view",{staticClass:"history-box"},[e("v-uni-view",{staticClass:"history-top"},[e("v-uni-view",{staticClass:"title"},[t._v(t._s(t.$lang("hot")))])],1),e("v-uni-view",{staticClass:"history-bottom"},t._l(t.hotList,(function(i,o){return e("v-uni-view",{key:o,staticClass:"history-li",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.otherSearch(i)},longpress:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteItem(i)}}},[e("v-uni-view",[t._v(t._s(i))])],1)})),1)],1)],1):t._e()],1)],1)],1)],1)},s=[]},"5a88":function(t,i,e){var o=e("6dc8");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("ade8dcf8",o,!0,{sourceMap:!1,shadowMode:!1})},"6dc8":function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-dfd2dc98] .fixed{position:relative;top:0}[data-v-dfd2dc98] .empty{margin-top:0!important}.cart-empty[data-v-dfd2dc98]{padding-top:54px}.content[data-v-dfd2dc98]{width:100vw;height:calc(100vh - env(safe-area-inset-bottom) - 0px);background:#fff}.cate-search[data-v-dfd2dc98]{width:100%;background:#fff;padding:%?10?% %?30?%;box-sizing:border-box;padding-top:%?30?%}.cate-search uni-input[data-v-dfd2dc98]{font-size:%?28?%;height:100%;padding:0 %?25?% 0 %?30?%;width:calc(100% - %?120?%)}.cate-search uni-text[data-v-dfd2dc98]{font-size:%?32?%;color:#909399;width:%?120?%;text-align:center}.cate-search .search-box[data-v-dfd2dc98]{width:100%;height:%?64?%;background:#f8f8f8;display:flex;justify-content:center;align-items:center;border-radius:%?40?%}.search-content[data-v-dfd2dc98]{box-sizing:border-box;background:#fff}.history[data-v-dfd2dc98]{width:100%;box-sizing:border-box}.history .history-box[data-v-dfd2dc98]{width:100%;height:100%;background:#fff;padding:%?30?% %?30?% %?0?% %?30?%;box-sizing:border-box;overflow:hidden}.history .history-box .history-top[data-v-dfd2dc98]{width:100%;height:%?60?%;display:flex;justify-content:space-between;align-items:center;font-size:%?32?%}.history .history-box .history-top .title[data-v-dfd2dc98]{font-weight:500;font-size:%?32?%}.history .history-box .history-top .iconfont[data-v-dfd2dc98]{color:#909399;font-size:%?28?%}.history .history-box .history-bottom[data-v-dfd2dc98]{width:100%;padding-top:%?20?%;position:relative}.history .history-box .history-bottom .history-li[data-v-dfd2dc98]{display:inline-block;margin-right:%?20?%;margin-bottom:%?15?%;max-width:100%}.history .history-box .history-bottom .history-li uni-view[data-v-dfd2dc98]{line-height:%?66?%;background:#f8f8f8!important;height:%?66?%;color:#303133!important;margin:0 %?0?% %?4?% 0!important;padding:0 %?20?%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;border-radius:%?20?%}.history .history-box .history-bottom .history-li.history_more[data-v-dfd2dc98]{margin-right:0;position:absolute;bottom:0;right:0}.history .hidden-show[data-v-dfd2dc98]{width:100%;height:%?70?%;text-align:center;line-height:%?70?%}.search-alike[data-v-dfd2dc98]{width:100%;height:calc(100vh - %?100?%);padding:0 %?20?%;box-sizing:border-box}.search-alike .alike-box[data-v-dfd2dc98]{width:100%;height:100%;background:#fff;border-radius:%?20?%;overflow:hidden}',""]),t.exports=i},7854:function(t,i,e){"use strict";e.r(i);var o=e("8ba8"),n=e("f48d");for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(s);var a=e("828b"),r=Object(a["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);i["default"]=r.exports},"8ba8":function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){}));var o=function(){var t=this.$createElement,i=this._self._c||t;return i("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"9c6c":function(t,i,e){"use strict";e.r(i);var o=e("2665"),n=e("1a0c");for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(s);e("e922");var a=e("828b"),r=Object(a["a"])(n["default"],o["b"],o["c"],!1,null,"dfd2dc98",null,!1,o["a"],void 0);i["default"]=r.exports},cc1b:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("5ef2"),e("64aa"),e("5c47"),e("a1c1"),e("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,i=getCurrentPages()[0];this.$pageVm=i.$vm||i,uni.onWindowResize((function(i){t.$emit("resize",i)})),this.$pageVm.$on("hook:onPageScroll",(function(i){t.$emit("scroll",i)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,i){t.setStyle({pullToRefresh:{support:i,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,i=String(this.scrollTop);if(-1!==i.indexOf("rpx")&&(i=uni.upx2px(i.replace("rpx",""))),i=parseFloat(i),!isNaN(i)){var e=function e(n){n.scrollTop===i&&(t.$pageVm.$off("hook:onPageScroll",e),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:i,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",e)}})}}}};i.default=n},e922:function(t,i,e){"use strict";var o=e("5a88"),n=e.n(o);n.a},f48d:function(t,i,e){"use strict";e.r(i);var o=e("cc1b"),n=e.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(s);i["default"]=n.a}}]);