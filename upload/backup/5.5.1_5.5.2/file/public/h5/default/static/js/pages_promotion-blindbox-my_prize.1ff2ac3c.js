(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-blindbox-my_prize"],{"42d5":function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("c223"),e("c9b5"),e("bf0f"),e("ab80");var o={data:function(){return{myprizes:!0,participant:!1,blindbox:[],diff:"1",blindgoods:{},blindboxId:"",mescroll:null,typeShow:!0}},onLoad:function(t){this.blindboxId=t.blindbox_id,this.getGoods()},methods:{getGoods:function(){var t=this;this.$api.sendRequest({url:"/blindbox/api/blindbox/blindboxInfo",data:{blindbox_id:this.blindboxId},success:function(i){i.code>=0?t.blindgoods=i.data:t.$util.showToast({title:i.message})}})},getMyPrize:function(t){var i=this;this.mescroll=t,this.$api.sendRequest({url:"/blindbox/api/blindbox/box",data:{blindbox_id:this.blindboxId,diff:this.diff,page:t.num,page_size:t.size},success:function(e){i.typeShow=!0;var o=[];0==e.code&&e.data?o=e.data.list:i.$util.showToast({title:e.message}),t.endSuccess(o.length),1==t.num&&(i.blindbox=[]),i.blindbox=i.blindbox.concat(o),i.$refs.loadingCover&&i.$refs.loadingCover.hide()}})},myPrize:function(t){this.blindbox=[],this.diff=t,this.typeShow=!1,this.mescroll.resetUpScroll(!1)},errorImg:function(){this.blindgoods.blindbox_images=this.$util.getDefaultImage().goods,this.$forceUpdate()},errorSkuImg:function(){this.blindbox.sku_image=this.$util.getDefaultImage().goods},errorSkuHeadImg:function(){this.blindbox.headimg=this.$util.getDefaultImage().head},deliver:function(t){var i=this;uni.setStorage({key:"blindOrderCreateData",data:{sku_id:t.sku_id.toString(),num:1,blindbox_goods_id:t.blindbox_goods_id,out_trade_no:t.out_trade_no},success:function(){i.$util.redirectTo("/pages_promotion/blindbox/fill_address")}})},look:function(t){this.$util.redirectTo("/pages/order/list",{order_id:t.order_id})}}};i.default=o},5460:function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){return o}));var o={pageMeta:e("7854").default,loadingCover:e("c003").default},n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":t.themeColor}}),e("v-uni-view",{staticClass:"prize-box"},[e("v-uni-view",{staticClass:"my_prize",style:{backgroundImage:"url("+t.$util.img("public/uniapp/blindbox/index_bg.png")+")"}},[e("v-uni-view",{staticClass:"top"},[e("v-uni-view",{staticClass:"top-img"},[""!=t.blindgoods.blindbox_images?e("v-uni-image",{attrs:{src:t.$util.img(t.blindgoods.blindbox_images),mode:"aspectFit"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.errorImg()}}}):e("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/blindbox/default.png")}})],1),e("v-uni-view",{staticClass:"right"},[e("v-uni-view",{staticClass:"blind-title font-size-toolbar"},[t._v(t._s(t.blindgoods.blindbox_name))]),1==t.blindgoods.blindbox_status?e("v-uni-view",{staticClass:"statused font-size-tag"},[t._v("进行中")]):t._e(),-1==t.blindgoods.blindbox_status?e("v-uni-view",{staticClass:"statused font-size-tag"},[t._v("已关闭")]):t._e(),2==t.blindgoods.blindbox_status?e("v-uni-view",{staticClass:"statused font-size-tag"},[t._v("已结束")]):t._e(),e("v-uni-view",{staticClass:"font-size-tag ul"},[e("v-uni-text",{staticClass:"li"}),e("v-uni-text",[t._v("盲盒次数：")]),e("v-uni-text",[t._v("可拆"+t._s(t.blindgoods.blindbox_count)+"次，剩余"),e("v-uni-text",{staticClass:"chi"},[t._v(t._s(t.blindgoods.blindbox_inventory))]),t._v("次")],1)],1),e("v-uni-view",{staticClass:"font-size-tag ul"},[e("v-uni-text",{staticClass:"li"}),e("v-uni-text",[t._v("我的次数：")]),e("v-uni-text",[t._v("已拆"),e("v-uni-text",{staticClass:"chi"},[t._v(t._s(t.blindgoods.buy_num))]),t._v("次")],1)],1)],1)],1),e("v-uni-view",{staticClass:"content"},[e("v-uni-view",{staticClass:"tabs"},[e("v-uni-view",{class:1==t.diff?"tab tab_left act":"tab tab_left",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.myPrize("1")}}},[t._v("我的奖品")]),e("v-uni-view",{class:2==t.diff?"tab tab_right act":"tab tab_right",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.myPrize("2")}}},[t._v("参与人")])],1),e("mescroll-uni",{ref:"mescroll",attrs:{size:10},on:{getData:function(i){arguments[0]=i=t.$handleEvent(i),t.getMyPrize.apply(void 0,arguments)}}},[e("template",{attrs:{slot:"list"},slot:"list"},[e("v-uni-view",{staticClass:"big"},[t.typeShow?e("v-uni-view",{staticClass:"list-tab"},[t.blindbox.length>0?e("v-uni-view",{staticClass:"goods-list"},t._l(t.blindbox,(function(i,o){return e("v-uni-view",{key:o,staticClass:"list-body"},[1==t.diff?e("v-uni-view",{staticClass:"list-body-img"},[""!=i.sku_image?e("v-uni-image",{attrs:{src:t.$util.img(i.sku_image),mode:"aspectFit"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.errorSkuImg()}}}):e("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/blindbox/default.png")}})],1):t._e(),2==t.diff?e("v-uni-view",{staticClass:"list-body-img-right"},[i.headimg?e("v-uni-image",{attrs:{src:t.$util.img(i.headimg),mode:"aspectFit"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.errorSkuHeadImg()}}}):e("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/common/default_headimg.png")}})],1):t._e(),1==t.diff?e("v-uni-view",{staticClass:"shop-content"},[e("v-uni-view",{staticClass:"shop-title"},[t._v(t._s(i.sku_name))]),e("v-uni-view",{staticClass:"shop-time"},[e("v-uni-text",{staticClass:"color-tip font-size-tag"},[t._v(t._s(t.$util.timeStampTurnTime(i.create_time)))]),0==i.is_dispatch?e("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deliver(i)}}},[t._v("发货")]):t._e(),1==i.is_dispatch?e("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.look(i)}}},[t._v("查看状态")]):t._e()],1)],1):t._e(),2==t.diff?e("v-uni-view",{staticClass:"shop-content-right"},[e("v-uni-view",{staticClass:"shop-name-right"},[e("v-uni-text",[t._v(t._s(i.nickname))]),e("v-uni-text",{staticClass:"color-tip font-size-tag"},[t._v(t._s(t.$util.timeStampTurnTime(i.create_time)))])],1),e("v-uni-view",{staticClass:"shop-title-right color-sub font-size-tag"},[t._v("获得"+t._s(i.sku_name))])],1):t._e()],1)})),1):e("v-uni-view",{staticClass:"prize-null"},[1==t.diff?e("v-uni-view",{staticClass:"prize-image"},[e("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/blindbox/prize_null.png"),mode:"aspectFill"}})],1):t._e(),1==t.diff?e("v-uni-view",{staticClass:"prize-null-title"},[t._v("暂无奖品~")]):t._e(),2==t.diff?e("v-uni-view",{staticClass:"prize-image"},[e("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/blindbox/people_null.png"),mode:"aspectFill"}})],1):t._e(),2==t.diff?e("v-uni-view",{staticClass:"prize-null-title"},[t._v("暂无参与人~")]):t._e()],1)],1):e("v-uni-view",[e("v-uni-view",{staticClass:"jiazai-box ",staticStyle:{background:"#fff"}},[e("v-uni-view",{staticClass:"jiazai"},[t._v("加载中...")])],1)],1)],1)],1)],2)],1)],1),e("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},7854:function(t,i,e){"use strict";e.r(i);var o=e("8ba8"),n=e("f48d");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);var r=e("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);i["default"]=s.exports},"7efb":function(t,i,e){"use strict";e.r(i);var o=e("5460"),n=e("bff1");for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);e("ae38"),e("ccd5");var r=e("828b"),s=Object(r["a"])(n["default"],o["b"],o["c"],!1,null,"2343c5c3",null,!1,o["a"],void 0);i["default"]=s.exports},"8ba8":function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){}));var o=function(){var t=this.$createElement,i=this._self._c||t;return i("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"968b":function(t,i,e){var o=e("c1ce");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("cdaee210",o,!0,{sourceMap:!1,shadowMode:!1})},ae38:function(t,i,e){"use strict";var o=e("ec37"),n=e.n(o);n.a},af05:function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.prize-box[data-v-2343c5c3]{width:100%;height:100%;position:fixed;top:0;left:0}.prize-box .my_prize[data-v-2343c5c3]{height:100vh;background-size:100% 100%;background-repeat:no-repeat;display:flex;flex-direction:column}.prize-box .my_prize .top[data-v-2343c5c3]{display:flex;justify-content:space-between;margin-left:%?30?%;padding-top:%?60?%}.prize-box .my_prize .top .top-img[data-v-2343c5c3]{background-color:#fff;width:%?210?%;height:%?210?%;margin-right:%?20?%}.prize-box .my_prize .top .top-img uni-image[data-v-2343c5c3]{width:100%;height:100%}.prize-box .my_prize .top .right[data-v-2343c5c3]{flex:1}.prize-box .my_prize .top .right uni-view[data-v-2343c5c3]{color:#fff}.prize-box .my_prize .top .right .blind-title[data-v-2343c5c3]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;word-break:break-word;height:%?80?%;line-height:1.3}.prize-box .my_prize .top .right .statused[data-v-2343c5c3]{background-color:#ffbe01;border-radius:%?6?%;padding:%?6?% 0;line-height:1;width:%?104?%;text-align:center;color:#fff;margin:%?6?% 0}.prize-box .my_prize .top .right .ul[data-v-2343c5c3]{display:flex;align-items:center}.prize-box .my_prize .top .right .ul uni-text[data-v-2343c5c3]{color:#fff}.prize-box .my_prize .top .right .ul .chi[data-v-2343c5c3]{color:#ffbe01}.prize-box .my_prize .top .right .ul .li[data-v-2343c5c3]{display:inline-block;background-color:#fff;width:%?6?%;height:%?6?%;border-radius:50%;margin-right:%?10?%}.prize-box .my_prize .content[data-v-2343c5c3]{margin:%?60?% %?30?%;border:%?2?% solid #d1beff;border-radius:%?20?%;background-color:#8a6de1;flex:1}.prize-box .my_prize .content .tabs[data-v-2343c5c3]{display:flex;box-sizing:border-box}.prize-box .my_prize .content .tabs .tab[data-v-2343c5c3]{width:50%;text-align:center;height:%?80?%;line-height:%?80?%;background-color:#d1beff}.prize-box .my_prize .content .tabs .tab_left[data-v-2343c5c3]{border-radius:%?18?% 0 %?20?% 0;color:#1f066c;font-weight:700}.prize-box .my_prize .content .tabs .tab_right[data-v-2343c5c3]{border-radius:0 %?18?% 0 %?20?%;color:#1f066c;font-weight:700}.prize-box .my_prize .content .tabs .act[data-v-2343c5c3]{background:#8a6de1!important;margin:0 %?-2?%;margin-top:%?-20?%;line-height:%?80?%;height:%?80?%;border-top:%?2?% solid #d1beff;border-left:%?2?% solid #d1beff;border-right:%?2?% solid #d1beff;border-radius:%?20?% %?20?% 0 0;color:#fff}.prize-box .my_prize .content .list-tab .goods-list[data-v-2343c5c3]{background-color:#fff;border-radius:%?10?%;padding:%?30?%}.prize-box .my_prize .content .list-tab .goods-list .list-body[data-v-2343c5c3]{display:flex;justify-content:space-between;align-items:center;border-bottom:%?2?% solid #eee;padding-bottom:%?30?%;margin-bottom:%?30?%}.prize-box .my_prize .content .list-tab .goods-list .list-body[data-v-2343c5c3]:last-child{border-bottom:none;padding-bottom:0;margin-bottom:0}.prize-box .my_prize .content .list-tab .goods-list .list-body .list-body-img[data-v-2343c5c3]{display:flex;align-items:center;justify-content:center;width:%?150?%;height:%?150?%;margin-right:%?20?%;background-color:#fff}.prize-box .my_prize .content .list-tab .goods-list .list-body .list-body-img uni-image[data-v-2343c5c3]{width:100%;height:100%}.prize-box .my_prize .content .list-tab .goods-list .list-body .shop-content[data-v-2343c5c3]{display:flex;flex-direction:column;justify-content:space-between;flex:1}.prize-box .my_prize .content .list-tab .goods-list .list-body .shop-content .shop-title[data-v-2343c5c3]{margin-top:%?-8?%;height:%?84?%;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;word-break:break-word;line-height:%?42?%;color:#303133}.prize-box .my_prize .content .list-tab .goods-list .list-body .shop-content .shop-time[data-v-2343c5c3]{display:flex;justify-content:space-between;align-items:flex-end;margin-top:%?20?%}.prize-box .my_prize .content .list-tab .goods-list .list-body .list-body-img-right[data-v-2343c5c3]{display:flex;align-items:center;justify-content:center;width:%?80?%;height:%?80?%;background-color:#fff;margin-right:%?20?%}.prize-box .my_prize .content .list-tab .goods-list .list-body .list-body-img-right uni-image[data-v-2343c5c3]{width:100%;height:100%;border-radius:50%}.prize-box .my_prize .content .list-tab .goods-list .list-body .shop-content-right[data-v-2343c5c3]{display:flex;flex-direction:column;justify-content:space-between;flex:1}.prize-box .my_prize .content .list-tab .goods-list .list-body .shop-content-right .shop-name-right[data-v-2343c5c3]{display:flex;align-items:center;justify-content:space-between}.prize-box .my_prize .content .list-tab .goods-list .list-body .shop-content-right .shop-title-right[data-v-2343c5c3]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;color:#303133;margin-top:%?10?%}.prize-box .my_prize .content .list-tab .prize-null[data-v-2343c5c3]{background-color:#fff;border-radius:%?10?%;display:flex;flex-direction:column;text-align:center;align-items:center;padding:%?220?%}.prize-box .my_prize .content .list-tab .prize-null .prize-image[data-v-2343c5c3]{width:%?468?%;height:%?314?%}.prize-box .my_prize .content .list-tab .prize-null .prize-image uni-image[data-v-2343c5c3]{width:100%;height:100%}.jiazai-box[data-v-2343c5c3]{background-color:#fff;border-radius:%?10?%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;text-align:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center;padding-bottom:%?535?%;padding-top:%?100?%}.jiazai-box .jiazai[data-v-2343c5c3]{text-align:center}',""]),t.exports=i},bff1:function(t,i,e){"use strict";e.r(i);var o=e("42d5"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a},c1ce:function(t,i,e){var o=e("c86c");i=o(!1),i.push([t.i,"[data-v-2343c5c3] .mescroll-uni-fixed{background-color:#8a6de1;margin:0 %?30?%;padding:%?30?% %?10?% %?20?%!important;border:%?2?% solid #d1beff;border-top:none;top:%?408?%!important;bottom:%?60?%!important;\r\n\t/* height: 860rpx !important; */border-radius:0 0 %?20?% %?20?%}\r\n/* /deep/ .list-tab .mescroll-uni-fixed{\r\n\t\tbackground-color: #8A6DE1;\r\n\t\tmargin: 0 30rpx;\r\n\t\tpadding: 30rpx 10rpx 20rpx !important;\r\n\t\tborder: 2rpx solid #D1BEFF;\r\n\t\tborder-top: none;\r\n\t\ttop: 410rpx !important;\r\n\t\theight: 740rpx !important;\r\n\t\tborder-radius: 0 0 20rpx 20rpx;\r\n\t}\r\n\t/deep/ .list-tab-right .mescroll-uni-fixed{\r\n\t\tbackground-color: #8A6DE1;\r\n\t\tmargin: 0 30rpx;\r\n\t\tpadding: 30rpx 10rpx 20rpx !important;\r\n\t\tborder: 2rpx solid #D1BEFF;\r\n\t\tborder-top: none;\r\n\t\ttop: 410rpx !important;\r\n\t\theight: 860rpx !important;\r\n\t\tborder-radius: 0 0 20rpx 20rpx;\r\n\t} */[data-v-2343c5c3] .mescroll-upwarp{min-height:0!important;padding:0!important;text-align:center;clear:both;margin-bottom:0!important;height:0!important}",""]),t.exports=i},cc1b:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("5ef2"),e("64aa"),e("5c47"),e("a1c1"),e("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,i=getCurrentPages()[0];this.$pageVm=i.$vm||i,uni.onWindowResize((function(i){t.$emit("resize",i)})),this.$pageVm.$on("hook:onPageScroll",(function(i){t.$emit("scroll",i)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,i){t.setStyle({pullToRefresh:{support:i,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,i=String(this.scrollTop);if(-1!==i.indexOf("rpx")&&(i=uni.upx2px(i.replace("rpx",""))),i=parseFloat(i),!isNaN(i)){var e=function e(n){n.scrollTop===i&&(t.$pageVm.$off("hook:onPageScroll",e),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:i,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",e)}})}}}};i.default=n},ccd5:function(t,i,e){"use strict";var o=e("968b"),n=e.n(o);n.a},ec37:function(t,i,e){var o=e("af05");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("7175cdbb",o,!0,{sourceMap:!1,shadowMode:!1})},f48d:function(t,i,e){"use strict";e.r(i);var o=e("cc1b"),n=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);i["default"]=n.a}}]);