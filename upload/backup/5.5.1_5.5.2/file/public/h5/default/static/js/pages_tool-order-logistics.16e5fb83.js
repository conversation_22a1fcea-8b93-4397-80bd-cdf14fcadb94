(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-order-logistics"],{"2ba2":function(t,e,o){"use strict";o.r(e);var i=o("4de7"),a=o.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(n);e["default"]=a.a},3715:function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){return i}));var i={pageMeta:o("7854").default,loadingCover:o("c003").default},a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",[o("page-meta",{attrs:{"page-style":t.themeColor}}),o("v-uni-view",[o("v-uni-scroll-view",{staticClass:"order-nav",attrs:{"scroll-x":!0,"show-scrollbar":!1}},t._l(t.packageList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"uni-tab-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.ontabtap(i)}}},[o("v-uni-text",{staticClass:"uni-tab-item-title",class:i==t.currIndex?"uni-tab-item-title-active color-base-border  color-base-text":""},[t._v(t._s(e.package_name))])],1)})),1),t._l(t.packageList,(function(e,i){return o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:i==t.currIndex,expression:"packageIndex == currIndex"}],key:i,staticClass:"swiper-item"},[o("v-uni-view",{staticClass:"container"},[o("v-uni-view",{staticClass:"goods-wrap"},[o("v-uni-view",{staticClass:"body"},t._l(e.goods_list,(function(e,a){return o("v-uni-view",{key:a,staticClass:"goods"},[o("v-uni-view",{staticClass:"goods-img",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toGoodsDetail(e.sku_id)}}},[o("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i,a)}}})],1),o("v-uni-view",{staticClass:"goods-info"},[o("v-uni-view",{staticClass:"goods-name",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toGoodsDetail(e.sku_id)}}},[t._v(t._s(e.sku_name))]),o("v-uni-view",{staticClass:"goods-sub-section"},[o("v-uni-view",[o("v-uni-text",[o("v-uni-text",{staticClass:"iconfont icon-close"}),t._v(t._s(e.num))],1)],1)],1)],1)],1)})),1)],1),1==e.delivery_type?o("v-uni-view",{staticClass:"express-company-wrap"},[o("v-uni-view",{staticClass:"company-logo"},[o("v-uni-image",{attrs:{src:t.$util.img(e.express_company_image)}})],1),o("v-uni-view",{staticClass:"info"},[o("v-uni-view",{staticClass:"company"},[o("v-uni-text",[t._v("承运公司： "+t._s(e.express_company_name))])],1),o("v-uni-view",{staticClass:"no"},[o("v-uni-text",[t._v("运单号："),o("v-uni-text",{staticClass:"color-tip"},[t._v(t._s(e.delivery_no))])],1),o("v-uni-text",{staticClass:"iconfont icon-fuzhi",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.copyDeliveryNo(e.delivery_no)}}})],1)],1)],1):t._e(),1==e.delivery_type?o("v-uni-view",{staticClass:"track-wrap"},[e.trace.success&&0!=e.trace.list.length?t._l(e.trace.list,(function(e,i){return o("v-uni-view",{key:i,staticClass:"track-item",class:0==i?"active":""},[o("v-uni-view",{staticClass:"dot",class:0==i?"color-base-bg":""}),o("v-uni-view",{staticClass:"msg"},[o("v-uni-view",{staticClass:"text",class:0==i?"color-base-text":""},[t._v(t._s(e.remark))]),o("v-uni-view",{staticClass:"time",class:0==i?"color-base-text":""},[t._v(t._s(e.datetime))])],1)],1)})):(e.trace.success&&e.trace.list.length,[o("v-uni-view",{staticClass:"fail-wrap font-size-base"},[t._v(t._s(e.trace.reason))])])],2):t._e()],1)],1)})),o("loading-cover",{ref:"loadingCover"})],2)],1)},n=[]},"4de7":function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("bf0f"),o("2797"),o("dc69");var i={data:function(){return{orderId:"",packageList:[],isIphoneX:!1,currIndex:0,status:0}},onLoad:function(t){t.order_id&&(this.orderId=t.order_id)},onShow:function(){this.storeToken?this.getPackageInfo():this.$util.redirectTo("/pages_tool/login/index"),this.isIphoneX=this.$util.uniappIsIPhoneX()},methods:{ontabtap:function(t){this.currIndex=t},getPackageInfo:function(){var t=this;this.$api.sendRequest({url:"/api/order/package",data:{order_id:this.orderId},success:function(e){e.code>=0?(t.packageList=e.data,t.packageList.forEach((function(e){e.trace.list&&(e.trace.list=e.trace.list.reverse()),e.status=t.status++})),t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:"未获取到订单信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/order/list")}),1500))},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},toGoodsDetail:function(t){this.$util.redirectTo("/pages/goods/detail",{sku_id:t})},imageError:function(t,e){this.packageList[t].goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},copyDeliveryNo:function(t){this.$util.copy(t)}}};e.default=i},"5ef9":function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.swiper-item[data-v-5f3d4042]{padding-top:%?94?%;height:100%;padding-bottom:%?30?%;padding-bottom:constant(safe-area-inset-bottom);\r\n  /*兼容 IOS<11.2*/padding-bottom:env(safe-area-inset-bottom)\r\n  /*兼容 IOS>11.2*/}.swiper-item .container[data-v-5f3d4042]{height:calc(100vh - %?124?%);overflow-y:scroll;box-sizing:border-box;display:flex;flex-direction:column;align-items:center;padding-bottom:%?30?%}.swiper-item .container.safearea[data-v-5f3d4042]{padding:%?68?%}.order-nav[data-v-5f3d4042]{width:100vw;flex-direction:row;white-space:nowrap;background:#fff;display:flex;position:fixed;left:0;z-index:998}.order-nav .uni-tab-item[data-v-5f3d4042]{display:inline-block;padding:%?30?% %?24?% 0}.order-nav .uni-tab-item-title[data-v-5f3d4042]{color:#555;font-size:%?32?%;display:block;line-height:1;padding:0 %?10?% %?30?%;flex-wrap:nowrap;white-space:nowrap;text-align:center}.order-nav .uni-tab-item-title-active[data-v-5f3d4042]{display:block;border-bottom:%?2?% solid #fff;padding:0 %?10?% %?30?%}.order-nav[data-v-5f3d4042] ::-webkit-scrollbar{width:0;height:0;color:transparent}.goods-wrap[data-v-5f3d4042]{padding:%?30?%;border-radius:%?10?%;background:#fff;position:relative;width:calc(100% - %?60?%);box-sizing:border-box;margin-top:%?20?%;padding:%?30?%}.goods-wrap .goods[data-v-5f3d4042]{display:flex;position:relative;margin-bottom:%?20?%}.goods-wrap .goods[data-v-5f3d4042]:last-of-type{margin-bottom:0}.goods-wrap .goods .goods-img[data-v-5f3d4042]{width:%?180?%;height:%?180?%;margin-right:%?20?%}.goods-wrap .goods .goods-img uni-image[data-v-5f3d4042]{width:100%;height:100%}.goods-wrap .goods .goods-info[data-v-5f3d4042]{flex:1;display:flex;flex-direction:column;justify-content:space-between;position:relative;max-width:calc(100% - %?140?%)}.goods-wrap .goods .goods-info .goods-name[data-v-5f3d4042]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;font-weight:500}.goods-wrap .goods .goods-info .goods-sub-section[data-v-5f3d4042]{width:100%;line-height:1.3;display:flex}.goods-wrap .goods .goods-info .goods-sub-section .goods-price[data-v-5f3d4042]{font-weight:700;font-size:%?20?%}.goods-wrap .goods .goods-info .goods-sub-section .unit[data-v-5f3d4042]{font-weight:400;font-size:%?24?%;margin-right:%?2?%}.goods-wrap .goods .goods-info .goods-sub-section uni-view[data-v-5f3d4042]{flex:1;line-height:1.3}.goods-wrap .goods .goods-info .goods-sub-section uni-view[data-v-5f3d4042]:last-of-type{text-align:left}.goods-wrap .goods .goods-info .goods-sub-section uni-view:last-of-type .iconfont[data-v-5f3d4042]{line-height:1;font-size:%?24?%}.express-company-wrap[data-v-5f3d4042]{padding:%?30?%;border-radius:%?10?%;background:#fff;position:relative;width:calc(100% - %?60?%);box-sizing:border-box;margin-top:%?20?%}.express-company-wrap .company-logo[data-v-5f3d4042]{width:%?120?%;height:%?120?%;margin-right:%?20?%;float:left}.express-company-wrap .company-logo uni-image[data-v-5f3d4042]{width:100%;height:100%}.express-company-wrap .info[data-v-5f3d4042]{flex:1}.express-company-wrap .info .company[data-v-5f3d4042]{line-height:1.5;margin-top:%?16?%}.express-company-wrap .info .no[data-v-5f3d4042]{margin-top:%?10?%;line-height:1.5}.express-company-wrap .info .icon-fuzhi[data-v-5f3d4042]{font-size:%?28?%;line-height:1;margin-left:%?6?%}.track-wrap[data-v-5f3d4042]{padding:%?30?%;border-radius:%?10?%;background:#fff;position:relative;width:calc(100% - %?60?%);box-sizing:border-box;margin-top:%?20?%}.track-wrap .track-item[data-v-5f3d4042]{position:relative;flex-wrap:wrap;overflow:visible;display:flex}.track-wrap .track-item[data-v-5f3d4042]:after{content:"";position:absolute;z-index:1;pointer-events:none;background-color:#e5e5e5;width:%?2?%;height:150%;top:%?56?%;left:%?20?%;bottom:%?-40?%}.track-wrap .track-item .dot[data-v-5f3d4042]{margin:%?34?% %?20?% 0 %?10?%;width:%?20?%;height:%?20?%;border-radius:%?10?%;background-color:#ccc;z-index:9}.track-wrap .track-item .msg[data-v-5f3d4042]{padding:%?20?% 0;flex:1}.track-wrap .track-item .msg .text[data-v-5f3d4042]{line-height:1.5;font-size:%?28?%}.track-wrap .track-item .msg .time[data-v-5f3d4042]{color:#909399;font-size:%?20?%;line-height:1.3;margin-top:%?10?%}.track-wrap .track-item[data-v-5f3d4042]:last-of-type:after{content:unset}[data-v-5f3d4042] .uni-scroll-view ::-webkit-scrollbar{\r\n  /* 隐藏滚动条，但依旧具备可以滚动的功能 */display:none;width:0;height:0;color:transparent;background:transparent}[data-v-5f3d4042] ::-webkit-scrollbar{display:none;width:0;height:0;color:transparent;background:transparent}',""]),t.exports=e},"6c7e":function(t,e,o){var i=o("5ef9");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=o("967d").default;a("379c5acc",i,!0,{sourceMap:!1,shadowMode:!1})},7854:function(t,e,o){"use strict";o.r(e);var i=o("8ba8"),a=o("f48d");for(var n in a)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(n);var s=o("828b"),r=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=r.exports},"8ba8":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},"920a":function(t,e,o){"use strict";o.r(e);var i=o("3715"),a=o("2ba2");for(var n in a)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(n);o("ab61");var s=o("828b"),r=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"5f3d4042",null,!1,i["a"],void 0);e["default"]=r.exports},ab61:function(t,e,o){"use strict";var i=o("6c7e"),a=o.n(i);a.a},cc1b:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("5ef2"),o("64aa"),o("5c47"),o("a1c1"),o("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var o=function o(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",o),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",o)}})}}}};e.default=a},f48d:function(t,e,o){"use strict";o.r(e);var i=o("cc1b"),a=o.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(n);e["default"]=a.a}}]);