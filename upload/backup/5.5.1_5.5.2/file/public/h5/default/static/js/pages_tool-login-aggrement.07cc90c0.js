(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-login-aggrement"],{2284:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.aggrement-info[data-v-1656543e]{padding:%?20?%}',""]),t.exports=e},4971:function(t,e,n){"use strict";n.r(e);var o=n("49a6"),r=n("9c2f");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("b1f95");var i=n("828b"),u=Object(i["a"])(r["default"],o["b"],o["c"],!1,null,"1656543e",null,!1,o["a"],void 0);e["default"]=u.exports},"49a6":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return o}));var o={pageMeta:n("7854").default,nsMpHtml:n("d108").default,loadingCover:n("c003").default},r=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[e("page-meta",{attrs:{"page-style":this.themeColor}}),e("v-uni-view",[this.aggrement&&this.aggrement.content?e("v-uni-view",{staticClass:"aggrement-info"},[e("ns-mp-html",{attrs:{content:this.aggrement.content}})],1):this._e(),e("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},7854:function(t,e,n){"use strict";n.r(e);var o=n("8ba8"),r=n("f48d");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);var i=n("828b"),u=Object(i["a"])(r["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=u.exports},"861f":function(t,e,n){var o=n("2284");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=n("967d").default;r("93a55420",o,!0,{sourceMap:!1,shadowMode:!1})},"8ba8":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},r=[]},"9c2f":function(t,e,n){"use strict";n.r(e);var o=n("a9e2"),r=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=r.a},a9e2:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{aggrement:null,type:"SERVICE"}},onLoad:function(t){this.type=t.type||"",this.getaggrementInfo()},methods:{getaggrementInfo:function(){var t=this;this.$api.sendRequest({url:"/api/register/aggrement",data:{type:this.type},success:function(e){e.code>=0&&(t.aggrement=e.data),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})}}}},b1f95:function(t,e,n){"use strict";var o=n("861f"),r=n.n(o);r.a},cc1b:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("64aa"),n("5c47"),n("a1c1"),n("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},r={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var n=function n(r){r.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",n),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",n)}})}}}};e.default=r},f48d:function(t,e,n){"use strict";n.r(e);var o=n("cc1b"),r=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=r.a}}]);