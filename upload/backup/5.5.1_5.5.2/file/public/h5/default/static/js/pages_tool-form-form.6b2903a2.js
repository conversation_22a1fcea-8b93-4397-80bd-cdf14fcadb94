(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-form-form"],{"04cc":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={pageMeta:i("7854").default,nsForm:i("ae30").default,nsEmpty:i("52a6").default,loadingCover:i("c003").default,nsLogin:i("2910").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",[t.detail?i("v-uni-view",{staticClass:"page"},[i("v-uni-view",{staticClass:"form-banner"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/form/banner.png"),mode:"widthFix"}})],1),i("v-uni-view",{staticClass:"system-form-wrap"},[i("v-uni-view",{staticClass:"form-title"},[t._v("请填写表单所需信息")]),i("ns-form",{ref:"form",attrs:{data:t.detail.json_data}}),i("v-uni-button",{staticClass:"button mini",attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.create()}}},[t._v("提交")])],1)],1):i("ns-empty",{attrs:{text:t.complete?"提交成功":"未获取到表单信息"}}),i("loading-cover",{ref:"loadingCover"}),i("ns-login",{ref:"login"})],1)],1)},r=[]},"6d1e":function(t,e,i){"use strict";i.r(e);var a=i("f077"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"92bf":function(t,e,i){"use strict";i.r(e);var a=i("04cc"),n=i("6d1e");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("dfee");var o=i("828b"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"6d8707e4",null,!1,a["a"],void 0);e["default"]=s.exports},c5e3:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.form-banner[data-v-6d8707e4]{width:100vw;line-height:1}.form-banner uni-image[data-v-6d8707e4]{width:100%;line-height:1}.system-form-wrap[data-v-6d8707e4]{background:#f8f8f8;border-radius:%?32?%;overflow:hidden;margin:0 0 %?60?% 0;padding:0 %?26?%;-webkit-transform:translateY(%?-40?%);transform:translateY(%?-40?%)}.system-form-wrap .form-title[data-v-6d8707e4]{line-height:%?100?%;padding-top:%?20?%}.system-form-wrap .button[data-v-6d8707e4]{height:%?80?%;line-height:%?80?%!important;margin-top:%?30?%!important;width:90%;border-radius:%?80?%}.system-form-wrap[data-v-6d8707e4] .form-wrap{background:#fff;padding:%?30?%;border-radius:%?32?%}',""]),t.exports=e},dfee:function(t,e,i){"use strict";var a=i("e12f"),n=i.n(a);n.a},e12f:function(t,e,i){var a=i("c5e3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("1f7d2615",a,!0,{sourceMap:!1,shadowMode:!1})},f077:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("d4b5");var a={data:function(){return{id:0,detail:null,isRepeat:!1,complete:!1,scroll:!0}},onLoad:function(t){var e=this;if(this.id=t.id||0,t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("id")&&(e.id=t.split("-")[1])}))}this.storeToken?this.getData():this.$nextTick((function(){e.$refs.login.open("/pages_tool/form/form?id="+e.id)}))},watch:{storeToken:function(t,e){t&&this.getData()}},methods:{getData:function(){var t=this;this.$api.sendRequest({url:"/form/api/form/info",data:{form_id:this.id},success:function(e){0==e.code&&e.data&&(t.detail=e.data),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},create:function(){var t=this;if(this.$refs.form.verify()){if(this.isRepeat)return;this.isRepeat=!0,this.$api.sendRequest({url:"/form/api/form/create",data:{form_id:this.id,form_data:JSON.stringify(this.$refs.form.formData)},success:function(e){0==e.code?(t.$util.showToast({title:"提交成功"}),setTimeout((function(){t.complete=!0,t.detail=null}),1500)):(t.isRepeat=!1,t.$util.showToast({title:e.message}))}})}}}};e.default=a}}]);