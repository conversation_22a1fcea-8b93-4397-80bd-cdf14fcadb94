(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-cancellation"],{"0817":function(e,t,n){"use strict";n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("5ef2"),n("5c47"),n("2c10"),n("a1c1"),n("23f4"),n("7d2f"),n("9c4e"),n("ab80"),n("aa9c"),n("473f"),n("bf0f"),n("3efd");var r=a(n("af87")),i=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,o=/^<\/([-A-Za-z0-9_]+)[^>]*>/,c=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,l=p("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),s=p("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),u=p("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),d=p("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),f=p("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),g=p("script,style");function p(e){for(var t={},n=e.split(","),a=0;a<n.length;a++)t[n[a]]=!0;return t}var h=function(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e),e=function(e){return e=e.replace(/<!--[\s\S]*-->/gi,""),e}(e),e=function(e){var t='<img style="width:100% !important;display:block;max-width: '.concat("100%",' !important;"');return e=e.replace(/\\/g,"").replace(/<img/g,t),e=e.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(e,n){return t+' src="'+r.default.img(n)+'"/>'})),e}(e),e=function(e){return e=e.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(e,t){return e=e.replace(/[:](\s?)[\s\S]*/gi,(function(e,t){return e.replace(/"/g,"'")})),e})),e}(e);var t=[],n={node:"root",children:[]};return function(e,t){var n,a,r,p=[],h=e;p.last=function(){return this[this.length-1]};while(e){if(a=!0,p.last()&&g[p.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+p.last()+"[^>]*>"),(function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""})),v("",p.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"),n>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),a=!1)):0==e.indexOf("</")?(r=e.match(o),r&&(e=e.substring(r[0].length),r[0].replace(o,v),a=!1)):0==e.indexOf("<")&&(r=e.match(i),r&&(e=e.substring(r[0].length),r[0].replace(i,m),a=!1)),a){n=e.indexOf("<");var b=n<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(b)}if(e==h)throw"Parse Error: "+e;h=e}function m(e,n,a,r){if(n=n.toLowerCase(),s[n])while(p.last()&&u[p.last()])v("",p.last());if(d[n]&&p.last()==n&&v("",n),r=l[n]||!!r,r||p.push(n),t.start){var i=[];a.replace(c,(function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:f[t]?t:"";i.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(n,i,r)}}function v(e,n){if(n){for(a=p.length-1;a>=0;a--)if(p[a]==n)break}else var a=0;if(a>=0){for(var r=p.length-1;r>=a;r--)t.end&&t.end(p[r]);p.length=a}}v()}(e,{start:function(e,a,r){var i={name:e};if(0!==a.length&&(i.attrs=function(e){return e.reduce((function(e,t){var n=t.value,a=t.name;return e[a]?e[a]=e[a]+" "+n:e[a]=n,e}),{})}(a)),r){var o=t[0]||n;o.children||(o.children=[]),o.children.push(i)}else t.unshift(i)},end:function(e){var a=t.shift();if(a.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)n.children.push(a);else{var r=t[0];r.children||(r.children=[]),r.children.push(a)}},chars:function(e){var a={type:"text",text:e};if(0===t.length)n.children.push(a);else{var r=t[0];r.children||(r.children=[]),r.children.push(a)}},comment:function(e){var n={node:"comment",text:e},a=t[0];a.children||(a.children=[]),a.children.push(n)}}),n.children};t.default=h},"2b6d":function(e,t,n){"use strict";n.r(t);var a=n("bd61"),r=n("d9f7");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("43c3");var o=n("828b"),c=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,"021dd42b",null,!1,a["a"],void 0);t["default"]=c.exports},"430b":function(e,t,n){"use strict";n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;a(n("0817"));t.default={data:function(){return{agreement:{},isSelect:!1}},onLoad:function(e){this.storeToken?this.getCancelAgreement():this.$util.redirectTo("/pages_tool/login/index")},methods:{getCancelAgreement:function(){var e=this;this.$api.sendRequest({url:"/membercancel/api/membercancel/agreement",success:function(t){t.code>=0&&(e.agreement=t.data)}})},changeSelect:function(){this.isSelect=1!=this.isSelect},next:function(){this.isSelect?this.$util.redirectTo("/pages_tool/member/assets"):this.$util.showToast({title:"请先勾选同意协议"})}}}},"43c3":function(e,t,n){"use strict";var a=n("f2bc"),r=n.n(a);r.a},"5f8a":function(e,t,n){var a=n("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-021dd42b] .agreement-content uni-view{font-size:%?24?%;line-height:%?44?%}.agreement-box .align-center[data-v-021dd42b]{text-align:center}.agreement-box .agreement-intro[data-v-021dd42b]{height:calc(100vh - %?210?%);padding-top:%?40?%;padding-left:%?40?%;padding-right:%?40?%;box-sizing:border-box;overflow-y:auto}.agreement-box .agreement-intro .agreement-title[data-v-021dd42b]{font-size:%?32?%;line-height:%?60?%;margin-bottom:%?10?%}.agreement-box .agreement-intro .agreement-content[data-v-021dd42b]{font-size:%?24?%;line-height:%?44?%}.agreement-box .agreement-btn[data-v-021dd42b]{position:fixed;width:100%;height:%?210?%;bottom:0;padding-top:%?16?%;box-sizing:border-box;text-align:center}.agreement-box .agreement-btn .agreement-btn-select[data-v-021dd42b]{display:flex;justify-content:center;align-items:center}.agreement-box .agreement-btn .agreement-btn-select .iconfont[data-v-021dd42b]{color:#838383}.agreement-box .agreement-btn .agreement-text[data-v-021dd42b]{font-size:%?28?%;color:#838383;margin-left:%?10?%}.agreement-box .agreement-btn uni-button[data-v-021dd42b]{display:inline-block;margin-top:%?20?%;color:var(--btn-text-color);font-size:%?28?%;width:%?300?%;height:%?80?%;line-height:%?80?%}',""]),e.exports=t},7854:function(e,t,n){"use strict";n.r(t);var a=n("8ba8"),r=n("f48d");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);var o=n("828b"),c=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=c.exports},"8ba8":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){}));var a=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},r=[]},bd61:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var a={pageMeta:n("7854").default,nsMpHtml:n("d108").default},r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",[n("page-meta",{attrs:{"page-style":e.themeColor}}),n("v-uni-view",{staticClass:"container"},[n("v-uni-view",{staticClass:"agreement-box"},[n("v-uni-view",{staticClass:"agreement-intro"},[n("v-uni-view",{staticClass:"align-center agreement-title"},[e._v(e._s(e.agreement.title))]),n("ns-mp-html",{staticClass:"agreement-content",attrs:{content:e.agreement.content}})],1),n("v-uni-view",{staticClass:"agreement-btn"},[n("v-uni-view",{staticClass:"align-center agreement-btn-select"},[e.isSelect?n("v-uni-text",{staticClass:"iconfont icon-dui color-base-text",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeSelect.apply(void 0,arguments)}}}):n("v-uni-text",{staticClass:"iconfont icon-yuan_checkbox",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeSelect.apply(void 0,arguments)}}}),n("v-uni-text",{staticClass:"agreement-text",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeSelect.apply(void 0,arguments)}}},[e._v("勾选即表示您已阅读并同意本协议")])],1),n("v-uni-button",{staticClass:"btn color-base-bg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.next.apply(void 0,arguments)}}},[e._v("下一步")])],1)],1)],1)],1)},i=[]},cc1b:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("5ef2"),n("64aa"),n("5c47"),n("a1c1"),n("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},r={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(e){return-1!==["dark","light"].indexOf(e)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var e=this,t=getCurrentPages()[0];this.$pageVm=t.$vm||t,uni.onWindowResize((function(t){e.$emit("resize",t)})),this.$pageVm.$on("hook:onPageScroll",(function(t){e.$emit("scroll",t)})),this.$watch("backgroundTextStyle",(function(){e.setBackgroundTextStyle()})),this.$watch((function(){return[e.rootFontSize,e.pageStyle]}),(function(){e.setPageMeta()})),this.$watch((function(){return[e.backgroundColor,e.backgroundColorTop,e.backgroundColorBottom]}),(function(){e.setBackgroundColor()})),this.$watch((function(){return[e.scrollTop,e.scrollDuration]}),(function(){e.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(e,t){e.setStyle({pullToRefresh:{support:t,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var e=this,t=String(this.scrollTop);if(-1!==t.indexOf("rpx")&&(t=uni.upx2px(t.replace("rpx",""))),t=parseFloat(t),!isNaN(t)){var n=function n(r){r.scrollTop===t&&(e.$pageVm.$off("hook:onPageScroll",n),e.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:t,duration:this.scrollDuration,success:function(){e.$pageVm.$on("hook:onPageScroll",n)}})}}}};t.default=r},d9f7:function(e,t,n){"use strict";n.r(t);var a=n("430b"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},f2bc:function(e,t,n){var a=n("5f8a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=n("967d").default;r("8a99f85e",a,!0,{sourceMap:!1,shadowMode:!1})},f48d:function(e,t,n){"use strict";n.r(t);var a=n("cc1b"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a}}]);