(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-game-smash_eggs"],{"015d":function(t,e,a){"use strict";a.r(e);var i=a("0f46"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"07e9":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={pageMeta:a("7854").default,uniPopup:a("d745").default,loadingCover:a("c003").default,nsLogin:a("2910").default,hoverNav:a("c1f1").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-scroll-view",{staticClass:"container",attrs:{"scroll-y":"true"}},[a("v-uni-view",[a("v-uni-view",{staticClass:"head-wrap"},[a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/game/egg_head.png"),mode:"widthFix"}}),a("v-uni-view",{staticClass:"rule-mark",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openRulePopup.apply(void 0,arguments)}}},[t._v("规则")])],1),a("v-uni-view",{staticClass:"status-wrap"},[1==t.gameInfo.status?[t.storeToken?a("v-uni-text",{staticClass:"mark"},[t._v("您还有"),a("v-uni-text",{staticClass:"num"},[t._v(t._s(t.gameInfo.surplus_num))]),t._v("次机会")],1):a("v-uni-text",{staticClass:"mark"},[t._v("请先进行登录")])]:0==t.gameInfo.status?[a("v-uni-text",{staticClass:"mark"},[t._v("活动尚未开始")])]:[a("v-uni-text",{staticClass:"mark"},[t._v("活动已经结束")])]],2),a("v-uni-view",{staticClass:"eggs-wrap"},[a("v-uni-view",{staticClass:"box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.lottery.apply(void 0,arguments)}}},[a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/game/golden_eggs.png"),mode:"widthFix"}})],1),a("v-uni-view",{staticClass:"box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.lottery.apply(void 0,arguments)}}},[a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/game/golden_eggs.png"),mode:"widthFix"}})],1),a("v-uni-view",{staticClass:"box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.lottery.apply(void 0,arguments)}}},[a("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/game/golden_eggs.png"),mode:"widthFix"}})],1)],1),a("v-uni-view",{staticClass:"action-text"},[a("v-uni-view",{staticClass:"point"},[t._v("我的积分："+t._s(t.point))]),a("v-uni-view",{staticClass:"record",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_promotion/game/record",{id:t.id})}}},[t._v("我的中奖记录")])],1),t.gameInfo.is_show_winner&&t.gameInfo.draw_record.length?a("v-uni-view",{staticClass:"record-wrap"},[a("v-uni-view",{staticClass:"body-shade"}),a("v-uni-view",{staticClass:"head"},[t._v("中奖名单")]),a("v-uni-view",{staticClass:"body"},[a("v-uni-view",{staticClass:"wrap",class:{animate:t.animate}},t._l(t.gameInfo.draw_record,(function(e,i){return a("v-uni-view",{key:i},[a("v-uni-view",{staticClass:"tit"},[t._v(t._s(t._f("cover")(e.member_nick_name)))]),a("v-uni-view",{staticClass:"txt"},[a("l-time",{attrs:{text:1e3*e.create_time}}),t._v("获得 "+t._s(e.award_name))],1)],1)})),1)],1)],1):t._e(),a("uni-popup",{ref:"transitionPopup",attrs:{type:"center",maskClick:!1}},[a("v-uni-view",{staticClass:"transition-popup"},[a("v-uni-view",{staticClass:"wrap"},[0==t.eggStatus?a("v-uni-image",{staticClass:"eggs",attrs:{src:t.$util.img("public/uniapp/game/golden_eggs_0.png"),mode:"widthFix"}}):t._e(),1==t.eggStatus?a("v-uni-image",{staticClass:"eggs",attrs:{src:t.$util.img("public/uniapp/game/golden_eggs_1.png"),mode:"widthFix"}}):t._e(),2==t.eggStatus?a("v-uni-image",{staticClass:"eggs",attrs:{src:t.$util.img("public/uniapp/game/golden_eggs_2.png"),mode:"widthFix"}}):t._e(),a("v-uni-image",{staticClass:"hammer",attrs:{src:t.$util.img("public/uniapp/game/hammer.png"),mode:"widthFix"}})],1)],1)],1),a("uni-popup",{ref:"resultPopup",attrs:{type:"center",maskClick:!1}},[a("v-uni-view",{staticClass:"result-wrap"},[t.result.is_winning?[a("v-uni-view",{staticClass:"content-wrap yes"},[1==t.result.award_type?a("v-uni-image",{staticClass:"look",attrs:{src:t.$util.img("public/uniapp/game/point.png"),mode:"widthFix"}}):t._e(),2==t.result.award_type?a("v-uni-image",{staticClass:"look",attrs:{src:t.$util.img("public/uniapp/game/balance.png"),mode:"widthFix"}}):t._e(),3==t.result.award_type?a("v-uni-image",{staticClass:"look",attrs:{src:t.$util.img("public/uniapp/game/coupon.png"),mode:"widthFix"}}):t._e(),4==t.result.award_type?a("v-uni-image",{staticClass:"look",attrs:{src:t.$util.img("public/uniapp/game/gift.png"),mode:"widthFix"}}):t._e(),1==t.result.award_type?a("v-uni-view",{staticClass:"msg"},[t._v("恭喜您抽中"+t._s(t.result.point)+"个积分")]):t._e(),2==t.result.award_type?a("v-uni-view",{staticClass:"msg"},[t._v("恭喜您抽中"+t._s(t.result.balance)+"元红包")]):t._e(),3==t.result.award_type?a("v-uni-view",{staticClass:"msg"},[t._v("恭喜您抽中优惠券“"+t._s(t.result.relate_name)+"”")]):t._e(),a("v-uni-view",{staticClass:"btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}},[t._v("继续抽奖")]),a("v-uni-text",{staticClass:"iconfont icon-round-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}})],1),a("v-uni-image",{staticClass:"bg-img",attrs:{src:t.$util.img("public/uniapp/game/result_yes.png"),mode:"widthFix"}})]:[a("v-uni-view",{staticClass:"content-wrap"},[a("v-uni-image",{staticClass:"look",attrs:{src:t.$util.img("public/uniapp/game/result_look.png"),mode:"widthFix"}}),a("v-uni-view",{staticClass:"msg"},[t._v(t._s(t.gameInfo.no_winning_desc))]),a("v-uni-view",{staticClass:"btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}},[t._v("继续抽奖")]),a("v-uni-text",{staticClass:"iconfont icon-round-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePopup.apply(void 0,arguments)}}})],1),a("v-uni-image",{staticClass:"bg-img",attrs:{src:t.$util.img("public/uniapp/game/result_no.png"),mode:"widthFix"}})]],2)],1),a("uni-popup",{ref:"rulePopup",attrs:{type:"center",maskClick:!1}},[a("v-uni-view",{staticClass:"rule-wrap"},[a("v-uni-view",{staticClass:"content-wrap"},[a("v-uni-image",{staticClass:"rule-head",attrs:{src:t.$util.img("public/uniapp/game/rule_head.png"),mode:"widthFix"}}),a("v-uni-scroll-view",{staticClass:"rule",attrs:{"scroll-y":"true"}},[a("v-uni-view",[a("v-uni-view",{staticClass:"tit"},[t._v("活动时间")]),a("v-uni-view",{staticClass:"text"},[t._v(t._s(t.$util.timeStampTurnTime(t.gameInfo.start_time))+" - "+t._s(t.$util.timeStampTurnTime(t.gameInfo.end_time)))]),a("v-uni-view",{staticClass:"tit"},[t._v("参与规则")]),0==t.gameInfo.join_type?a("v-uni-view",{staticClass:"text"},[t._v("每个用户活动期间共有"+t._s(t.gameInfo.join_frequency)+"次抽奖机会。")]):a("v-uni-view",{staticClass:"text"},[t._v("每个用户活动期间每天都有"+t._s(t.gameInfo.join_frequency)+"次抽奖机会，每天0点更新。")]),a("v-uni-view",{staticClass:"text"},[t._v("每次抽奖需消耗 "+t._s(t.gameInfo.points)+" 积分")]),0!=t.gameInfo.level_id?a("v-uni-view",{staticClass:"text"},[t._v("该活动只有"+t._s(t.gameInfo.level_name)+"等级的会员可参与。")]):t._e(),""!=t.gameInfo.remark?[a("v-uni-view",{staticClass:"tit"},[t._v("活动说明")]),a("v-uni-view",{staticClass:"text"},[t._v(t._s(t.gameInfo.remark))])]:t._e()],2)],1),a("v-uni-text",{staticClass:"iconfont icon-round-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeRulePopup.apply(void 0,arguments)}}})],1)],1)],1),a("loading-cover",{ref:"loadingCover"}),a("ns-login",{ref:"login"}),a("hover-nav")],1)],1)],1)},o=[]},"0f46":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=i},"22ad":function(t,e,a){var i=a("5a9e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("3a16179a",i,!0,{sourceMap:!1,shadowMode:!1})},"353c":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797"),a("5ef2"),a("e966"),a("aa9c");var n=i(a("d745")),o=i(a("7477")),r={components:{uniPopup:n.default,LTime:o.default},data:function(){return{id:0,gameInfo:{no_winning_desc:"",surplus_num:0},result:{is_winning:0,award_type:0,point:0,balance:0,relate_name:""},isClick:!1,eggStatus:0,scrollTimer:null,point:0,animate:!1,shareImg:""}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.egg||(e.$util.showToast({title:"商家未开启砸金蛋",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.id&&(this.id=t.id),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var a=decodeURIComponent(t.scene);a=a.split("&"),a.length&&a.forEach((function(t){-1!=t.indexOf("id")&&(e.id=t.split("-")[1]),-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}this.getGameInfo()},onShow:function(){this.storeToken&&(uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member")),this.getMemberPointInfo())},watch:{storeToken:function(t,e){t&&(this.getMemberPointInfo(),this.getGameInfo())}},onShareAppMessage:function(t){var e=this.gameInfo.game_name,a=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),i=a.path;return{title:e,path:i,imageUrl:"",success:function(t){},fail:function(t){}}},methods:{getMemberPointInfo:function(){var t=this;this.$api.sendRequest({url:"/api/memberaccount/info",data:{account_type:"point"},success:function(e){e.data&&(t.point=parseInt(e.data.point))}})},lottery:function(){var t=this;this.storeToken?1==this.gameInfo.status&&(this.gameInfo.surplus_num<=0?this.$util.showToast({title:"您的抽奖次数已用完"}):this.gameInfo.points>0&&this.point<this.gameInfo.points?this.$util.showToast({title:"积分不足"}):this.isClick||(this.isClick=!0,this.$api.sendRequest({url:"/egg/api/egg/lottery",data:{id:this.id},success:function(e){e.code>=0?(t.result=e.data,t.start(),t.point-=t.gameInfo.points,t.getGameInfo()):t.$util.showToast({title:e.message})}}))):this.$refs.login.open("/pages_promotion/game/smash_eggs?id="+this.id)},getGameInfo:function(){var t=this;this.$api.sendRequest({url:"/egg/api/egg/info",data:{id:this.id},success:function(e){e.code>=0&&e.data?(t.gameInfo=e.data,t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.gameInfo.is_show_winner&&t.gameInfo.draw_record.length>6&&(t.scrollTimer=setInterval(t.scrollRecord,2e3))):(t.$util.showToast({title:"未获取到活动信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))},fail:function(e){t.$util.showToast({title:"未获取到活动信息！"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500)}})},start:function(){var t=this;this.$refs.transitionPopup.open(),setTimeout((function(){t.eggStatus=1}),1e3),setTimeout((function(){t.eggStatus=2}),3e3),setTimeout((function(){t.$refs.transitionPopup.close(),t.$refs.resultPopup.open()}),3300)},scrollRecord:function(){var t=this;this.animate=!0,setTimeout((function(){t.gameInfo.draw_record.push(t.gameInfo.draw_record[0]),t.gameInfo.draw_record.shift(),t.animate=!1}),1e3)},closePopup:function(){this.isClick=!1,this.eggStatus=0,this.$refs.resultPopup.close()},openRulePopup:function(){this.$refs.rulePopup.open()},closeRulePopup:function(){this.$refs.rulePopup.close()}},filters:{cover:function(t){return"string"==typeof t&&t.length>0?t.substr(0,1)+"******"+t.substr(-1):""}},onHide:function(){clearInterval(this.scrollTimer)}};e.default=r},"5a9e":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container[data-v-64b55df8]{width:100vw;height:100vh;background-image:linear-gradient(#f3623f,#f74222)}.head-wrap[data-v-64b55df8]{position:relative}.head-wrap uni-image[data-v-64b55df8]{width:100%;margin:0 auto;display:block}.head-wrap .rule-mark[data-v-64b55df8]{position:absolute;right:0;top:%?60?%;background-color:#ffe854;color:#ff7908;height:%?60?%;line-height:%?60?%;padding:0 %?30?%;border-top-left-radius:%?60?%;border-bottom-left-radius:%?60?%}.status-wrap[data-v-64b55df8]{-webkit-transform:translateY(%?-340?%);transform:translateY(%?-340?%);text-align:center}.status-wrap .mark[data-v-64b55df8]{display:inline-block;line-height:1;padding:%?16?% %?60?%;background-color:#d17e1f;color:#fff;border-radius:%?60?%}.status-wrap .num[data-v-64b55df8]{color:#ffe821;font-size:%?32?%;margin:0 %?4?%;display:inline-block}.eggs-wrap[data-v-64b55df8]{display:flex;width:90%;margin:0 auto;-webkit-transform:translateY(%?-300?%);transform:translateY(%?-300?%)}.eggs-wrap .box[data-v-64b55df8]{flex:1;text-align:center}.eggs-wrap .box uni-image[data-v-64b55df8]{width:90%}.action-text[data-v-64b55df8]{display:flex;-webkit-transform:translateY(%?-300?%);transform:translateY(%?-300?%);margin:%?60?% %?50?% 0 %?50?%}.action-text > uni-view[data-v-64b55df8]{flex:1;line-height:1}.action-text .point[data-v-64b55df8]{color:#fee331}.action-text .record[data-v-64b55df8]{color:#fff;text-align:right}.record-wrap[data-v-64b55df8]{-webkit-transform:translateY(%?-300?%);transform:translateY(%?-300?%);margin:%?80?% %?50?% %?30?% %?50?%;border-radius:%?10?%;background-color:#ff6e43;padding:%?12?%;box-shadow:0 .45em 0 #d92a00;position:relative;height:%?430?%}.record-wrap .body-shade[data-v-64b55df8]{width:calc(100% - %?24?%);height:%?60?%;top:%?12?%;left:%?12?%;background-color:#da2b00;position:absolute;z-index:2;border-radius:%?8?%}.record-wrap .head[data-v-64b55df8]{border:%?6?% solid #ff6e43;box-shadow:inset 0 0 %?10?% 0 #d92a00,0 .45em 0 #d92a00;background-color:#da2b00;position:absolute;z-index:5;text-align:center;width:%?300?%;height:%?70?%;line-height:%?70?%;border-radius:%?70?%;top:0;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);color:#fff;font-weight:600;letter-spacing:%?4?%}.record-wrap .body[data-v-64b55df8]{background-color:#da2b00;border-radius:%?8?%;height:%?340?%;box-shadow:inset 0 0 %?10?% 0 #d92a00;padding:%?60?% %?30?% %?30?% %?30?%;overflow:hidden}.record-wrap .body .wrap > uni-view[data-v-64b55df8]{display:flex}.record-wrap .body .wrap.animate[data-v-64b55df8]{transition:all 1s ease-in-out;-webkit-transform:translateY(%?-60?%);transform:translateY(%?-60?%)}.record-wrap .body .tit[data-v-64b55df8]{line-height:%?60?%;width:%?220?%;color:#fff}.record-wrap .body .txt[data-v-64b55df8]{line-height:%?60?%;flex:1;color:#fee331}.transition-popup .wrap[data-v-64b55df8]{position:relative;padding:0 %?100?%}.transition-popup .wrap .eggs[data-v-64b55df8]{width:%?380?%}.transition-popup .wrap .hammer[data-v-64b55df8]{position:absolute;width:%?80?%;top:%?-60?%;right:%?60?%;-webkit-transform-origin:bottom right;transform-origin:bottom right;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-animation:chuizi-data-v-64b55df8 1.1s ease 3;animation:chuizi-data-v-64b55df8 1.1s ease 3}.result-wrap[data-v-64b55df8]{position:relative}.result-wrap .bg-img[data-v-64b55df8]{width:80vw}.result-wrap .content-wrap[data-v-64b55df8]{position:absolute;z-index:5;width:100%;height:100%;top:0;left:0;text-align:center}.result-wrap .content-wrap .look[data-v-64b55df8]{margin:%?70?% auto 0 auto;display:block;width:%?120?%}.result-wrap .content-wrap .msg[data-v-64b55df8]{margin:%?10?% auto 0 auto;padding:0 %?20?%;color:#da2b00;width:70%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.result-wrap .content-wrap .btn[data-v-64b55df8]{width:50%;height:%?70?%;line-height:%?70?%;border-radius:%?70?%;text-align:center;color:#da2b00;background:linear-gradient(#ffe1ac,#fec965);margin:%?146?% auto 0 auto}.result-wrap .content-wrap .icon-round-close[data-v-64b55df8]{position:absolute;color:#fff;font-size:%?70?%;top:%?-120?%;right:%?0?%}.result-wrap .yes .look[data-v-64b55df8]{width:%?80?%;height:%?80?%;margin-top:%?60?%}.result-wrap .yes .btn[data-v-64b55df8]{margin-top:%?160?%}.rule-wrap[data-v-64b55df8]{border-radius:%?10?%;background-color:#ffd697;width:80vw;padding:%?12?%;box-sizing:border-box}.rule-wrap .content-wrap[data-v-64b55df8]{background-color:#fff2dd;width:100%;border-radius:%?8?%;position:relative}.rule-wrap .content-wrap .rule-head[data-v-64b55df8]{width:100%;position:absolute;-webkit-transform:translateY(-50%);transform:translateY(-50%);left:0;top:0}.rule-wrap .content-wrap .rule[data-v-64b55df8]{max-height:%?880?%;overflow:hidden;padding:%?80?% %?30?% 0 %?30?%;box-sizing:border-box}.rule-wrap .content-wrap .rule .tit[data-v-64b55df8]{font-weight:600;color:#da2b00;margin-top:%?10?%}.rule-wrap .content-wrap .rule .text[data-v-64b55df8]{font-size:%?26?%;color:#da2b00}.rule-wrap .content-wrap .icon-round-close[data-v-64b55df8]{color:#fff;text-align:center;position:absolute;bottom:%?-150?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);font-size:%?70?%}@-webkit-keyframes chuizi-data-v-64b55df8{0%{-moz-transform:rotate(-45);-ms-transform:rotate(-45);-webkit-transform:rotate(-45);transform:rotate(-45deg)}30%{-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(-45);-ms-transform:rotate(-45);-webkit-transform:rotate(-45);transform:rotate(-45)}}@keyframes chuizi-data-v-64b55df8{0%{-moz-transform:rotate(-45);-ms-transform:rotate(-45);-webkit-transform:rotate(-45);transform:rotate(-45deg)}30%{-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(-45);-ms-transform:rotate(-45);-webkit-transform:rotate(-45);transform:rotate(-45)}}',""]),t.exports=e},"5dd4":function(t,e,a){"use strict";a.r(e);var i=a("07e9"),n=a("9e56");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("b838"),a("8517");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"64b55df8",null,!1,i["a"],void 0);e["default"]=s.exports},7477:function(t,e,a){"use strict";a.r(e);var i=a("7dc7"),n=a("aaa6");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"3f50a906",null,!1,i["a"],void 0);e["default"]=s.exports},7854:function(t,e,a){"use strict";a.r(e);var i=a("8ba8"),n=a("f48d");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},"7dc7":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-text",[this._v(this._s(this.temp))])},n=[]},8517:function(t,e,a){"use strict";var i=a("981f"),n=a.n(i);n.a},"8ba8":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},9327:function(t,e,a){"use strict";var i=a("8bdb"),n=a("9f69"),o=a("1ded").f,r=a("c435"),s=a("9e70"),u=a("b6a1"),c=a("862c"),l=a("0931"),d=a("a734"),f=n("".slice),p=Math.min,g=l("endsWith"),v=!d&&!g&&!!function(){var t=o(String.prototype,"endsWith");return t&&!t.writable}();i({target:"String",proto:!0,forced:!v&&!g},{endsWith:function(t){var e=s(c(this));u(t);var a=arguments.length>1?arguments[1]:void 0,i=e.length,n=void 0===a?i:p(r(a),i),o=s(t);return f(e,n-o.length,n)===o}})},"981f":function(t,e,a){var i=a("e9da");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("3b6906ac",i,!0,{sourceMap:!1,shadowMode:!1})},9851:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5c47"),a("0506"),a("a1c1"),a("23f4"),a("7d2f"),a("9c4e"),a("ab80"),a("bf0f"),a("c223"),Function.prototype.asyAfter=function(t){var e=this;return function(){var a=e.apply(this,arguments);return"next"===a?t.apply(this,arguments):a}},Date.prototype.pattern=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours()%12==0?12:this.getHours()%12,"H+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};for(var a in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length))),/(E+)/.test(t)&&(t=t.replace(RegExp.$1,(RegExp.$1.length>1?RegExp.$1.length>2?"星期":"周":"")+{0:"日",1:"一",2:"二",3:"三",4:"四",5:"五",6:"六"}[this.getDay()+""])),e)new RegExp("("+a+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?e[a]:("00"+e[a]).substr((""+e[a]).length)));return t};var i=function(){},n=i.prototype;n.getUnix=function(){return(new Date).getTime()},n.getTodayUnix=function(){var t=new Date,e="".concat(t.getFullYear(),"/").concat(t.getMonth()+1,"/").concat(t.getDate()," 00:00:00");return new Date(e).getTime()},n.getYearUnix=function(){var t=new Date;return t.setMonth(0),t.setDate(1),t.setHours(0),t.setMinutes(0),t.setSeconds(0),t.setMilliseconds(0),t.getTime()},n.getLastDate=function(t){if(t){var e=new Date(t);if(e.pattern)return e.pattern("yyyy-MM-dd");var a=e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1,i=e.getDate()<10?"0"+e.getDate():e.getDate();return e.getFullYear()+"-"+a+"-"+i}};var o=new RegExp("-","g");n.getFormatTime=function(t,e){if(!t)return"";switch(function(t){return/^\[object\s(.*)\]$/.exec(Object.prototype.toString.call(t))[1]}(t)){case"Date":t=t.getTime();break;case"String":t=t.replace(o,"/");break;default:t=new Date(t).getTime();break}var a=this.getUnix(),i=(this.getYearUnix(),(a-t)/1e3);if(t>a&&e)return this.getLastDate(t);return function(t,e){var a=function(t){return t<=0||Math.floor(t/60)<=0?"刚刚":"next"}.asyAfter((function(t){return t<3600?Math.floor(t/60)+"分钟前":"next"})).asyAfter((function(t,e){var a=r.getTodayUnix();return t>=3600&&e-a>=0?Math.floor(t/60/60)+"小时前":"next"})).asyAfter((function(t,e){var a=r.getTodayUnix();return t=(a-e)/1e3,t/86400<=31?Math.ceil(t/86400)+"天前":"next"})).asyAfter((function(t,e){return r.getLastDate(e)}));return a(t,e)}(i,t)};var r=new i,s=r;e.default=s},"9e56":function(t,e,a){"use strict";a.r(e);var i=a("353c"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},a725:function(t,e,a){"use strict";var i=a("ac2a"),n=a.n(i);n.a},aaa6:function(t,e,a){"use strict";a.r(e);var i=a("c2c7"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},ac2a:function(t,e,a){var i=a("f714");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("1a69ffc2",i,!0,{sourceMap:!1,shadowMode:!1})},b838:function(t,e,a){"use strict";var i=a("22ad"),n=a.n(i);n.a},c1f1:function(t,e,a){"use strict";a.r(e);var i=a("fa1d"),n=a("015d");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("a725");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"c1934e78",null,!1,i["a"],void 0);e["default"]=s.exports},c2c7:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("9327");var n=i(a("9851")),o={name:"l-time",props:{text:{type:[String,Number,Date],default:""},maxDate:{type:Boolean,default:!1}},data:function(){return{textVal:this.text}},watch:{text:function(){this.textVal=this.text}},computed:{temp:function(){return this.getText()}},methods:{getText:function(){var t=this,e=n.default.getFormatTime(t.textVal,t.maxDate);return e&&(e.endsWith("刚刚")||e.endsWith("分钟前"))&&setTimeout((function(){var e=t.textVal;t.textVal="",t.textVal=e}),6e4),this.textVal?e:""},onClick:function(){this.$emit("on-tap",this.textVal)}}};e.default=o},cc1b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var a=function a(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",a),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",a)}})}}}};e.default=n},e9da:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,"[data-v-64b55df8] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:none!important}[data-v-64b55df8] .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box{overflow:unset}",""]),t.exports=e},f48d:function(t,e,a){"use strict";a.r(e);var i=a("cc1b"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},f714:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return 1==t.pageCount||t.need?a("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?a("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[a("v-uni-text",{staticClass:"iconfont icon-shouye1"}),a("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?a("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[a("v-uni-text",{staticClass:"iconfont icon-yonghu"}),a("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?a("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[a("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):a("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[a("v-uni-view",[t._v("快捷")]),a("v-uni-view",[t._v("导航")])],1)],1):t._e()},n=[]}}]);