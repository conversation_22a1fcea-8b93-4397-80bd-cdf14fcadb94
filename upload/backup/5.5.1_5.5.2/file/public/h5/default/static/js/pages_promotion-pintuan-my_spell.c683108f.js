(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-pintuan-my_spell"],{"00ba":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?o("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?o("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),o("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),o("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),o("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():o("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},n=[]},"06ac":function(t,e,o){"use strict";o.r(e);var i=o("75b0"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"11ac":function(t,e,o){"use strict";var i=o("9c4f"),n=o.n(i);n.a},2144:function(t,e,o){var i=o("d668");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=o("967d").default;n("099408a4",i,!0,{sourceMap:!1,shadowMode:!1})},"546a":function(t,e,o){"use strict";var i=o("e729"),n=o.n(i);n.a},"62b5":function(t,e,o){"use strict";var i=o("2144"),n=o.n(i);n.a},"75b0":function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("bf0f"),o("2797"),o("c223");var n=i(o("e12a")),a={components:{uniCountDown:n.default},data:function(){return{mescroll:null,dataList:[],pintuanStatusList:[{id:2,name:"拼团中"},{id:3,name:"拼团成功"},{id:1,name:"拼团失败"}],pintuanStatus:"all",pintuanState:[{},{color:"#FF4544",text:"拼团失败"},{color:"#FFA044",text:"拼团中"},{color:"#11BD64",text:"拼团成功"}]}},onShow:function(){var t=this;setTimeout((function(){t.addonIsExist.pintuan||(t.$util.showToast({title:"商家未开启拼团",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.storeToken||this.$nextTick((function(){t.$refs.login.open("/pages_promotion/pintuan/my_spell")}))},methods:{getData:function(t){var e=this;this.mescroll=t,this.$api.sendRequest({url:"/pintuan/api/order/page",data:{page_size:t.size,page:t.num,pintuan_status:this.pintuanStatus},success:function(o){var i=[],n=o.message;0==o.code&&o.data?i=o.data.list:e.$util.showToast({title:n}),t.endSuccess(i.length),1==t.num&&(e.dataList=[]),i.forEach((function(t){t.group_end_time>o.timestamp?t.timeMachine=e.$util.countDown(t.group_end_time-o.timestamp):t.timeMachine=null})),e.dataList=e.dataList.concat(i),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(){t.endErr(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},goPinTuanDetail:function(t){this.$util.redirectTo("/pages_promotion/pintuan/detail",{pintuan_id:t})},goIndex:function(){this.$util.redirectTo("/pages/index/index")},toshare:function(t){this.$util.redirectTo("/pages_promotion/pintuan/share",{id:t})},toOrderDetail:function(t){this.$util.redirectTo("/pages/order/detail",{order_id:t})},categoryChange:function(t){this.pintuanStatus=t,this.mescroll.resetUpScroll()},imageError:function(t){this.dataList[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},memberImageError:function(t,e){this.dataList[t].member_list[e].member_img=this.$util.getDefaultImage().head,this.$forceUpdate()}},watch:{storeToken:function(t,e){t&&this.$refs.mescroll.refresh()}}};e.default=a},7854:function(t,e,o){"use strict";o.r(e);var i=o("8ba8"),n=o("f48d");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);var s=o("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=r.exports},"8ba8":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},"9c4f":function(t,e,o){var i=o("bb37");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=o("967d").default;n("1a51f0e1",i,!0,{sourceMap:!1,shadowMode:!1})},bb37:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-countdown[data-v-45a7f114]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-45a7f114]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?24?%}.uni-countdown__splitor.day[data-v-45a7f114]{line-height:%?50?%}.uni-countdown__number[data-v-45a7f114]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;border:%?2?% solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=e},be29:function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return i}));var i={pageMeta:o("7854").default,uniCountDown:o("e12a").default,nsEmpty:o("52a6").default,nsLogin:o("2910").default,loadingCover:o("c003").default},n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",[o("page-meta",{attrs:{"page-style":t.themeColor}}),o("v-uni-view",[t.storeToken?o("v-uni-view",{staticClass:"my_spell_category"},[o("v-uni-view",{staticClass:"category-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.categoryChange("all")}}},[o("v-uni-view",{staticClass:"item-con",class:"all"==t.pintuanStatus?"active color-base-text color-base-bg-before":""},[t._v("全部")])],1),t._l(t.pintuanStatusList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"category-item",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.categoryChange(e.id)}}},[o("v-uni-view",{staticClass:"item-con",class:e.id==t.pintuanStatus?"active color-base-text color-base-bg-before":""},[t._v(t._s(e.name))])],1)}))],2):t._e(),t.storeToken?o("mescroll-uni",{ref:"mescroll",attrs:{top:"90",size:10},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getData.apply(void 0,arguments)}}},[o("template",{attrs:{slot:"list"},slot:"list"},[t._l(t.dataList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"goods-list"},[o("v-uni-view",{staticClass:"list-header"},[o("v-uni-text",{staticClass:"state-time"},[t._v("发起拼单 "+t._s(t.$util.timeStampTurnTime(e.pay_time)))]),o("v-uni-text",{staticClass:"state-sign",style:{color:t.pintuanState[e.pintuan_status].color}},[t._v(t._s(t.pintuanState[e.pintuan_status].text))])],1),o("v-uni-view",{staticClass:"list-body",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toshare(e.id)}}},[o("v-uni-view",{staticClass:"list-body-img"},[o("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i)}}})],1),o("v-uni-view",{staticClass:"shop-content"},[o("v-uni-view",{staticClass:"shop-title"},[t._v(t._s(e.sku_name))]),o("v-uni-view",{staticClass:"pintuan-num"},[t._v(t._s(e.pintuan_num)+"人拼单")]),o("v-uni-view",{staticClass:"pintuan-price price-style large"},[o("v-uni-text",{staticClass:"pintuan-price-icon price-style small"},[t._v("¥")]),t._v(t._s(parseFloat(e.order_money).toFixed(2).split(".")[0])),o("v-uni-text",{staticClass:"pintuan-price-icon  price-style small"},[t._v("."+t._s(parseFloat(e.order_money).toFixed(2).split(".")[1]))])],1)],1)],1),2==e.pintuan_status?o("v-uni-view",{staticClass:"list-footer"},[e.timeMachine?[o("v-uni-view",{staticClass:"list-footer-time"},[o("v-uni-text",[t._v("还剩")]),o("v-uni-text",{staticClass:"color-base-text"},[t._v(t._s(e.pintuan_num-e.pintuan_count))]),o("v-uni-text",[t._v("人，剩余时间")]),o("v-uni-view",{staticClass:"time-wrap"},[o("uni-count-down",{staticClass:"time",attrs:{day:e.timeMachine.d,hour:e.timeMachine.h,minute:e.timeMachine.i,second:e.timeMachine.s,color:"#909399",splitorColor:"#909399","background-color":"transparent","border-color":"transparent"}})],1)],1),o("v-uni-button",{staticClass:"share-btn",attrs:{type:"primary",size:"mini"},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toshare(e.id)}}},[t._v("邀请好友")])]:[o("v-uni-text",[t._v("拼团失败")])]],2):3==e.pintuan_status?o("v-uni-view",{staticClass:"list-footer"},[o("v-uni-view",{staticClass:"picture-box"},t._l(e.member_list,(function(e,n){return n<4?o("v-uni-view",{key:n,staticClass:"img-box"},[e.member_img?o("v-uni-image",{attrs:{src:t.$util.img(e.member_img),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.memberImageError(i,n)}}}):o("v-uni-image",{attrs:{src:t.$util.img(t.$util.getDefaultImage().head),mode:"aspectFill"}})],1):t._e()})),1),o("v-uni-button",{staticClass:"order-btn mini",attrs:{type:"primary",size:"mini"},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toOrderDetail(e.order_id)}}},[t._v("查看详情")])],1):t._e()],1)})),0==t.dataList.length?o("v-uni-view",{staticStyle:{"padding-top":"0"}},[o("ns-empty",{attrs:{isIndex:!0,emptyBtn:{url:"/pages_promotion/pintuan/list",text:"去逛逛"},text:"暂无拼团订单"}})],1):t._e()],2)],2):t._e(),o("ns-login",{ref:"login"}),o("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},cc1b:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("5ef2"),o("64aa"),o("5c47"),o("a1c1"),o("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var o=function o(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",o),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",o)}})}}}};e.default=n},d668:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-45245bdb] .empty{margin-top:0!important}.my_spell_category[data-v-45245bdb]{width:100%;height:%?88?%;display:flex;justify-content:space-around;background-color:#fff;position:fixed;top:0;z-index:999;box-sizing:border-box}.my_spell_category .category-item[data-v-45245bdb]{width:%?130?%;text-align:center}.my_spell_category .category-item .item-con[data-v-45245bdb]{display:inline-block;height:%?88?%;font-size:%?30?%;position:relative;line-height:%?88?%}.my_spell_category .category-item .item-con.active[data-v-45245bdb]:after{content:"";display:block;width:100%;height:%?4?%;border-radius:%?6?%;position:absolute;left:0;bottom:0}.my_spell_category .category-item[data-v-45245bdb]:last-of-type{margin-right:0}.goods-list[data-v-45245bdb]{margin:%?20?% %?30?% %?20?%;background-color:#fff;border-radius:%?10?%;padding:%?30?%}.list-header[data-v-45245bdb]{display:flex;align-items:center;justify-content:space-between}.list-header .state-time[data-v-45245bdb]{font-size:%?28?%;color:#303133}.list-header .state-sign[data-v-45245bdb]{font-size:%?24?%}.list-body[data-v-45245bdb]{display:flex;justify-content:space-between;margin-top:%?22?%}.list-body .list-body-img[data-v-45245bdb]{display:flex;align-items:center;justify-content:center;width:%?170?%;height:%?170?%;margin-right:%?18?%;border-radius:%?10?%;overflow:hidden}.list-body .list-body-img uni-image[data-v-45245bdb]{width:%?170?%;height:%?170?%}.list-body .shop-content[data-v-45245bdb]{display:flex;flex-direction:column;padding:%?6?% 0;width:%?531?%;width:calc(100% - %?178?%)}.list-body .shop-content .shop-title[data-v-45245bdb]{margin-top:%?-8?%;max-height:%?84?%;font-size:%?28?%;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;line-height:%?42?%;color:#303133;word-wrap:break-word;width:100%}.list-body .shop-content .pintuan-num[data-v-45245bdb]{color:#909399;font-size:%?26?%}.list-body .shop-content .pintuan-price[data-v-45245bdb]{margin-top:auto;line-height:1;font-size:%?32?%;color:var(--price-color)}.list-body .shop-content .pintuan-price .pintuan-price-icon[data-v-45245bdb]{margin-right:%?6?%;font-size:%?24?%}.list-footer[data-v-45245bdb]{display:flex;height:%?80?%;justify-content:space-between;align-items:center;margin-top:%?12?%}.list-footer .time-wrap[data-v-45245bdb]{display:inline-block;margin-left:%?10?%}.list-footer .list-footer-time[data-v-45245bdb]{color:#909399}.list-footer uni-text[data-v-45245bdb]{border-radius:%?60?%;font-size:%?24?%;line-height:%?50?%}.list-footer .picture-box[data-v-45245bdb]{margin-top:%?20?%;width:60%;height:100%;display:flex;align-items:center}.list-footer .img-box uni-image[data-v-45245bdb]{border:%?2?% solid #fff;margin-right:%?-24?%;width:%?50?%;height:%?50?%;border-radius:50%}.list-footer .order-btn[data-v-45245bdb]{background-color:#fff!important;color:#303133;border:%?2?% solid #909399;height:%?56?%;line-height:%?60?%;padding:0 %?30?%;margin:0}.list-footer .share-btn[data-v-45245bdb]{height:%?60?%;line-height:%?60?%;background-color:var(--base-color);color:#fff;padding:0 %?30?%;border:none;margin:0}.empty[data-v-45245bdb]{width:100%;display:flex;flex-direction:column;align-items:center;padding:%?20?%;box-sizing:border-box;margin-top:%?150?%}.empty .iconfont[data-v-45245bdb]{font-size:%?190?%;color:#909399;line-height:1.2}.empty uni-button[data-v-45245bdb]{margin-top:%?20?%;font-size:%?28?%}',""]),t.exports=e},dceb:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,"[data-v-45245bdb] .uni-countdown__number,[data-v-45245bdb] .uni-countdown__splitor{margin:0;padding:0}",""]),t.exports=e},e12a:function(t,e,o){"use strict";o.r(e);var i=o("00ba"),n=o("ea5a");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("11ac");var s=o("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"45a7f114",null,!1,i["a"],void 0);e["default"]=r.exports},e729:function(t,e,o){var i=o("dceb");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=o("967d").default;n("63518d4a",i,!0,{sourceMap:!1,shadowMode:!1})},ea5a:function(t,e,o){"use strict";o.r(e);var i=o("f9fd"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},efdb:function(t,e,o){"use strict";o.r(e);var i=o("be29"),n=o("06ac");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("62b5"),o("546a");var s=o("828b"),r=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"45245bdb",null,!1,i["a"],void 0);e["default"]=r.exports},f48d:function(t,e,o){"use strict";o.r(e);var i=o("cc1b"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},f9fd:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("64aa");var i={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:[Number,String],default:0},hour:{type:[Number,String],default:0},minute:{type:[Number,String],default:0},second:{type:[Number,String],default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},mounted:function(t){var e=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},watch:{day:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},hour:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},minute:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},second:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,e,o,i){return t=Number(t),e=Number(e),o=Number(o),i=Number(i),60*t*60*24+60*e*60+60*o+i},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,o=0,i=0,n=0;t>0?(e=Math.floor(t/86400),o=Math.floor(t/3600)-24*e,i=Math.floor(t/60)-24*e*60-60*o,n=Math.floor(t)-24*e*60*60-60*o*60-60*i):this.timeUp(),e<10&&(e="0"+e),o<10&&(o="0"+o),i<10&&(i="0"+i),n<10&&(n="0"+n),this.d=e,this.h=o,this.i=i,this.s=n}}};e.default=i}}]);