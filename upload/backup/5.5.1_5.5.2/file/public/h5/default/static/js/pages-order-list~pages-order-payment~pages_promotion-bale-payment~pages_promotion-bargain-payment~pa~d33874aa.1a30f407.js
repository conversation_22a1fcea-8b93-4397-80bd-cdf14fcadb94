(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-list~pages-order-payment~pages_promotion-bale-payment~pages_promotion-bargain-payment~pa~d33874aa"],{"0619":function(e,t,a){"use strict";var n=a("36f2"),o=a.n(n);o.a},"171a":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={name:"nsSwitch",props:{checked:{type:Boolean,default:!1}},methods:{change:function(){this.$emit("change")}}};t.default=n},"1f3b":function(e,t,a){"use strict";a.r(t);var n=a("be4e"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},"36f2":function(e,t,a){var n=a("bfb8");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=a("967d").default;o("043c1984",n,!0,{sourceMap:!1,shadowMode:!1})},"4c42":function(e,t,a){"use strict";var n=a("c46e"),o=a.n(n);o.a},"554a":function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,'.weui-switch[data-v-7e2a453e]{display:block;position:relative;width:%?94?%;height:%?45?%;outline:0;border-radius:%?30?%;border:%?2?% solid;border-color:#dfdfdf;transition:background-color .1s,border .1s}.weui-switch .bgview[data-v-7e2a453e]{content:" ";position:absolute;top:0;left:0;width:%?94?%;height:%?45?%;border-radius:%?30?%;transition:-webkit-transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1),-webkit-transform .35s cubic-bezier(.45,1,.4,1)}.weui-switch .spotview[data-v-7e2a453e]{content:" ";position:absolute;top:%?2?%;left:%?4?%;width:%?40?%;height:%?40?%;border-radius:50%;background-color:#fff;box-shadow:0 %?2?% %?6?% rgba(0,0,0,.4);transition:-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35),-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35)}.weui-switch-on[data-v-7e2a453e]{border-color:#6f6f6f}.weui-switch-on .bgview[data-v-7e2a453e]{border-color:#1aad19}.weui-switch-on .spotview[data-v-7e2a453e]{-webkit-transform:translateX(%?48?%);transform:translateX(%?48?%)}',""]),e.exports=t},"5e08":function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={uniPopup:a("d745").default,nsSwitch:a("b0ec").default},o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[e.payInfo?a("uni-popup",{ref:"choosePaymentPopup",attrs:{type:"center","mask-click":!1}},[a("v-uni-view",{staticClass:"choose-payment-popup popup",on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("支付方式")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.close()}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"pay-money"},[a("v-uni-text",{staticClass:"money"},[e._v("支付金额"+e._s(e._f("moneyFormat")(e.payMoney))+"元")])],1),e.balanceDeduct>0&&e.balanceUsable&&1==e.balanceConfig?a("v-uni-view",{staticClass:"payment-item"},[a("v-uni-view",{staticClass:"iconfont icon-yue"}),a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-text",{staticClass:"name"},[e._v("余额抵扣")]),a("v-uni-view",{staticClass:"money"},[e._v("可用¥"+e._s(e._f("moneyFormat")(e.balanceDeduct)))])],1),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==e.isBalance},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.useBalance.apply(void 0,arguments)}}})],1):e._e(),e.payMoney>0?[e.payTypeList.length?[e._l(e.payTypeList,(function(t,n){return[e.offlineShow||"offlinepay"!=t.type?a("v-uni-view",{key:n,staticClass:"payment-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.payIndex=n}}},[a("v-uni-view",{staticClass:"iconfont",class:t.icon}),a("v-uni-text",{staticClass:"name"},[e._v(e._s(t.name))]),a("v-uni-text",{staticClass:"iconfont",class:e.payIndex==n?"icon-yuan_checked color-base-text":"icon-checkboxblank"})],1):e._e()]}))]:[a("v-uni-view",{staticClass:"empty"},[e._v("平台尚未配置支付方式！")])]]:e._e()],2),a("v-uni-view",{staticClass:"popup-footer"},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.confirm()}}},[e._v("确认支付")])],1)],1)],1):e._e()],1)},i=[]},"7e0a":function(e,t,a){"use strict";a.r(t);var n=a("171a"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},b0ec:function(e,t,a){"use strict";a.r(t);var n=a("bf29"),o=a("7e0a");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("4c42");var s=a("828b"),c=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,"7e2a453e",null,!1,n["a"],void 0);t["default"]=c.exports},b6f2:function(e,t,a){"use strict";a.r(t);var n=a("5e08"),o=a("1f3b");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("0619");var s=a("828b"),c=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,"0f26514c",null,!1,n["a"],void 0);t["default"]=c.exports},be4e:function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4626"),a("5ac7"),a("e838"),a("8f71"),a("bf0f"),a("5ef2"),a("5c47"),a("a1c1");var o=n(a("d745")),i=n(a("b0ec")),s=a("edd0"),c={name:"payment",components:{uniPopup:o.default,nsSwitch:i.default},props:{balanceUsable:{type:Boolean,default:!0}},data:function(){return{payIndex:0,payTypeList:[{name:"支付宝支付",icon:"icon-zhifubaozhifu-",type:"alipay"},{name:"微信支付",icon:"icon-weixin1",type:"wechatpay"},{name:"线下支付",icon:"icondiy icon-yuezhifu",type:"offlinepay"}],timer:null,payInfo:null,balanceConfig:0,sale:!0,isBalance:0,balance:0,resetPayComplete:!0,repeatFlag:!1}},created:function(e){this.getPayType(),this.balanceUsable&&this.getBalanceConfig()},computed:{balanceDeduct:function(){var e=0;return this.payInfo&&this.balance&&(e=this.balance>this.payInfo.pay_money?this.payInfo.pay_money:this.balance),e},payMoney:function(){var e=0;return this.payInfo&&(e=this.payInfo.pay_money,this.balanceDeduct&&this.isBalance&&this.balanceUsable&&(e=this.payInfo.pay_money-this.balanceDeduct)),e},offlineShow:function(){var e=getCurrentPages(),t=e[e.length-1],a=t.route;return!!this.$store.state.offlineWhiteList.length&&this.$store.state.offlineWhiteList.includes(a)}},methods:{pageShow:function(){if(this.payInfo){var e=uni.getStorageSync("offlinepay");e&&(uni.removeStorageSync("offlinepay"),this.close())}else uni.removeStorageSync("offlinepay")},close:function(){this.$emit("close"),this.$refs.choosePaymentPopup.close()},useBalance:function(){this.isBalance=this.isBalance?0:1,this.$emit("useBalance",this.isBalance)},confirm:function(){0==this.payTypeList.length&&this.payMoney>0?this.$util.showToast({title:"请选择支付方式！"}):0!=this.resetPayComplete?(uni.showLoading({title:"支付中...",mask:!0}),this.repeatFlag||(this.repeatFlag=!0,this.pay(),uni.setStorageSync("pay_flag",1))):this.$util.showToast({title:"支付取消中，请稍后再试！"})},getPayInfo:function(e,t){var a=this;this.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:e},success:function(e){e.code>=0&&e.data?(a.payInfo=e.data,a.balanceConfig&&a.balanceUsable&&a.getMemberBalance(),setTimeout((function(){a.$refs.choosePaymentPopup.open(),"function"==typeof t&&t()}))):a.$util.showToast({title:"未获取到支付信息！"})}})},getBalanceConfig:function(){var e=this;this.$api.sendRequest({url:"/api/pay/getBalanceConfig",data:{},success:function(t){e.balanceConfig=t.data.balance_show}})},getMemberBalance:function(){var e=this;this.$api.sendRequest({url:"/api/memberaccount/usablebalance",success:function(t){0==t.code&&t.data&&(e.balance=parseFloat(t.data.usable_balance))}})},getPayType:function(){var e=this;this.$api.sendRequest({url:"/api/pay/type",success:function(t){0==t.code&&(""==t.data.pay_type?e.payTypeList=[]:e.payTypeList=e.payTypeList.filter((function(e,a){return-1!=t.data.pay_type.indexOf(e.type)})))}})},pay:function(){var e=this,t=this.payTypeList[this.payIndex],a="";a="BlindboxGoodsOrderPayNotify"==this.payInfo.event?"/pages_promotion/blindbox/index?outTradeNo=":"/pages_tool/pay/result?code=",this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:this.payInfo.out_trade_no,pay_type:t?t.type:"",return_url:encodeURIComponent(this.$config.h5Domain+a+this.payInfo.out_trade_no),is_balance:this.isBalance},success:function(a){if(uni.hideLoading(),a.code>=0){if(a.data.pay_success)return void e.paySuccess();switch(t.type){case"alipay":if(e.$util.isWeiXin()){var n=encodeURIComponent(a.data.data);e.$util.redirectTo("/pages_tool/pay/wx_pay",{wx_alipay:n,out_trade_no:e.payInfo.out_trade_no},"","redirectTo"),e.repeatFlag=!1}else e.repeatFlag=!1,location.href=a.data.data,e.checkPayStatus();break;case"wechatpay":if(e.$util.isWeiXin()){if("ios"==uni.getSystemInfoSync().platform)var o=uni.getStorageSync("initUrl");else o=location.href;e.$api.sendRequest({url:"/wechat/api/wechat/jssdkconfig",data:{url:o},success:function(t){var n=new s.Weixin,o=a.data.data;n.init(t.data),n.pay({timestamp:o.timestamp?o.timestamp:o.timeStamp,nonceStr:o.nonceStr,package:o.package,signType:o.signType,paySign:o.paySign},(function(t){"chooseWXPay:ok"==t.errMsg?(e.paySuccess(),e.repeatFlag=!1):(e.$util.showToast({title:t.errMsg}),setTimeout((function(){e.close(),e.repeatFlag=!1}),1500))}),(function(t){e.$util.showToast({title:"您已取消支付"}),e.resetpay(),e.repeatFlag=!1}))}})}else e.repeatFlag=!1,location.href=a.data.url,e.checkPayStatus();break;case"offlinepay":e.$util.redirectTo("/pages_tool/pay/offlinepay",{outTradeNo:e.payInfo.out_trade_no}),e.repeatFlag=!1;break}}else e.$util.showToast({title:a.message}),e.repeatFlag=!1},fail:function(t){uni.hideLoading(),e.$util.showToast({title:"request:fail"}),e.repeatFlag=!1}})},checkPayStatus:function(){var e=this;this.timer=setInterval((function(){e.$api.sendRequest({url:"/api/pay/status",data:{out_trade_no:e.payInfo.out_trade_no},success:function(t){0==t.code?2==t.data.pay_status&&(clearInterval(e.timer),e.paySuccess()):clearInterval(e.timer)}})}),1e3)},paySuccess:function(){"BlindboxGoodsOrderPayNotify"==this.payInfo.event?this.$util.redirectTo("/pages_promotion/blindbox/index",{outTradeNo:this.payInfo.out_trade_no},"redirectTo"):this.payInfo.return_url?-1!=this.payInfo.return_url.indexOf("http://")||-1!=this.payInfo.return_url.indexOf("https://")?location.replace(this.payInfo.return_url):this.$util.redirectTo(this.payInfo.return_url,{},"redirectTo"):this.$util.redirectTo("/pages_tool/pay/result",{code:this.payInfo.out_trade_no},"redirectTo")},resetpay:function(){var e=this;this.resetPayComplete=!1,this.$api.sendRequest({url:"/api/pay/resetpay",data:{out_trade_no:this.payInfo.out_trade_no},success:function(t){0==t.code?e.getPayInfo(t.data,(function(){e.resetPayComplete=!0})):e.resetPayComplete=!0},fail:function(t){e.resetPayComplete=!0}})}},deactivated:function(){clearInterval(this.timer)}};t.default=c},bf29:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[a("v-uni-view",{staticClass:"weui-switch",class:{"weui-switch-on":e.checked,"color-base-border":e.checked},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.change()}}},[a("v-uni-view",{staticClass:"bgview",class:{"color-base-bg":e.checked}}),a("v-uni-view",{staticClass:"spotview"})],1)],1)},o=[]},bfb8:function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.popup[data-v-0f26514c]{width:75vw;background:#fff;border-top-left-radius:%?10?%;border-top-right-radius:%?10?%}.popup .popup-header[data-v-0f26514c]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-0f26514c]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-0f26514c]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-0f26514c]{height:calc(100% - %?250?%)}.popup .popup-body.safe-area[data-v-0f26514c]{height:calc(100% - %?270?%)}.popup .popup-footer[data-v-0f26514c]{height:%?100?%}.popup .popup-footer .confirm-btn[data-v-0f26514c]{height:%?72?%;line-height:%?72?%;color:#fff;text-align:center;margin:%?20?% %?30?% 0;border-radius:%?10?%}.popup .popup-footer.bottom-safe-area[data-v-0f26514c]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.choose-payment-popup .payment-item[data-v-0f26514c]{display:flex;align-items:center;justify-content:space-between;height:%?90?%;margin:0 %?30?%;border-bottom:%?2?% solid #eee;padding:%?20?% 0}.choose-payment-popup .payment-item[data-v-0f26514c]:nth-child(2){padding-top:0}.choose-payment-popup .payment-item[data-v-0f26514c]:last-child{border-bottom:none}.choose-payment-popup .payment-item .iconfont[data-v-0f26514c]{font-size:%?64?%}.choose-payment-popup .payment-item .icon-yue[data-v-0f26514c]{color:#faa218}.choose-payment-popup .payment-item .icon-weixin1[data-v-0f26514c]{color:#24af41}.choose-payment-popup .payment-item .icon-yuezhifu[data-v-0f26514c]{color:#f9a647}.choose-payment-popup .payment-item .icon-zhifubaozhifu-[data-v-0f26514c]{color:#00a0e9}.choose-payment-popup .payment-item .icon-checkboxblank[data-v-0f26514c]{font-size:%?40?%;color:#eee}.choose-payment-popup .payment-item .icon-yuan_checked[data-v-0f26514c]{font-size:%?40?%}.choose-payment-popup .payment-item .name[data-v-0f26514c]{margin-left:%?20?%;font-size:%?28?%;flex:1}.choose-payment-popup .payment-item .info-wrap[data-v-0f26514c]{flex:1;margin-left:%?20?%}.choose-payment-popup .payment-item .info-wrap .name[data-v-0f26514c]{margin-left:0;font-size:%?28?%;flex:1}.choose-payment-popup .payment-item .info-wrap .money[data-v-0f26514c]{color:#909399;font-size:%?24?%}.choose-payment-popup .payment-item .box[data-v-0f26514c]{flex:1;padding:0 %?10?%;line-height:inherit;text-align:right}.choose-payment-popup .payment-item .box uni-input[data-v-0f26514c]{font-size:%?24?%!important}.choose-payment-popup .payment-item.set-pay-password[data-v-0f26514c]{height:auto}.choose-payment-popup .payment-item.set-pay-password .box[data-v-0f26514c]{font-size:%?24?%!important}.choose-payment-popup .pay-money[data-v-0f26514c]{text-align:center;padding:%?20?% 0 %?40?% 0;background-color:#fff;font-weight:700;margin-top:%?30?%;line-height:1}.choose-payment-popup .pay-money .unit[data-v-0f26514c]{margin-right:%?4?%;font-size:%?24?%}.choose-payment-popup .pay-money .money[data-v-0f26514c]{font-size:%?32?%}.empty[data-v-0f26514c]{width:100%;text-align:center;padding:%?40?% 0;color:#606266;font-size:%?24?%}',""]),e.exports=t},c46e:function(e,t,a){var n=a("554a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var o=a("967d").default;o("95c2f08a",n,!0,{sourceMap:!1,shadowMode:!1})}}]);