(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-pay-cashier"],{"0717":function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.cashier[data-v-5ed2a988]{display:flex;align-items:center;flex-direction:column;padding:%?80?% %?26?%}.cashier .content[data-v-5ed2a988]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:100%;padding:0 %?60?%;text-align:center}.cashier .money-wrap[data-v-5ed2a988]{font-weight:700;margin:%?40?% 0}.cashier .money-wrap .unit[data-v-5ed2a988]{font-size:%?40?%;margin-right:%?6?%}.cashier .money-wrap .money[data-v-5ed2a988]{font-size:%?70?%}.cashier .pay-type[data-v-5ed2a988]{width:100%;background:#fff;border-radius:%?20?%}.cashier .pay-type .payment-item[data-v-5ed2a988]{display:flex;align-items:center;justify-content:space-between;height:%?90?%;border-bottom:%?2?% solid #eee;padding:%?20?% %?30?%}.cashier .pay-type .payment-item[data-v-5ed2a988]:last-of-type{border-bottom:none}.cashier .pay-type .payment-item > uni-view[data-v-5ed2a988]{display:flex;align-items:center}.cashier .pay-type .payment-item > uni-view .name[data-v-5ed2a988]{margin-left:%?20?%}.cashier .pay-type .payment-item .iconfont[data-v-5ed2a988]{font-size:%?64?%}.cashier .pay-type .payment-item .icon-weixin1[data-v-5ed2a988]{color:#24af41}.cashier .pay-type .payment-item .icon-zhifubaozhifu-[data-v-5ed2a988]{color:#00a0e9}.cashier .pay-type .payment-item .icon-yuan_checked[data-v-5ed2a988]{font-size:%?40?%;color:var(--base-color)}.cashier .pay-type .payment-item .icon-checkboxblank[data-v-5ed2a988]{font-size:%?40?%}.cashier uni-button[data-v-5ed2a988]{width:100%;margin-top:%?80?%!important;background:var(--base-color);height:%?90?%;line-height:%?90?%;border-radius:%?90?%}',""]),t.exports=e},"1f36":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797"),a("5ef2"),a("dd2b"),a("5c47"),a("0506"),a("a1c1"),a("e838");var n=a("edd0"),i={data:function(){return{payIndex:0,payTypeList:[{name:"支付宝支付",icon:"icon-zhifubaozhifu-",type:"alipay"},{name:"微信支付",icon:"icon-weixin1",type:"wechatpay"}],timer:null,payInfo:null,outTradeNo:""}},onLoad:function(t){this.getPayType(),this.outTradeNo=t.out_trade_no||"",this.getPayInfo()},methods:{getPayInfo:function(){var t=this;this.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:this.outTradeNo},success:function(e){e.code>=0&&e.data&&(t.payInfo=e.data,0==t.payInfo.pay_status&&setTimeout((function(){t.autoPay()}),500))}})},getPayType:function(){var t=this;this.$api.sendRequest({url:"/api/pay/type",success:function(e){""==e.data.pay_type?t.payTypeList=[]:t.payTypeList.forEach((function(a,n){-1==e.data.pay_type.indexOf(a.type)&&t.payTypeList.splice(n,1)}))}})},autoPay:function(){var t=this;this.payTypeList.length&&(this.$util.isWeiXin()?this.payTypeList.forEach((function(e,a){"wechatpay"==e.type&&(t.payIndex=a,t.confirm())})):/AlipayClient/.test(window.navigator.userAgent)&&this.payTypeList.forEach((function(e,a){"alipay"==e.type&&(t.payIndex=a,t.confirm())})))},confirm:function(){var t=this;this.storeToken?0==this.payTypeList.length&&this.payInfo.pay_money>0?this.$util.showToast({title:"请选择支付方式！"}):(uni.showLoading({title:"支付中...",mask:!0}),this.pay()):this.$nextTick((function(){t.$refs.login.open("/pages_tool/pay/cashier?out_trade_no="+t.outTradeNo)}))},pay:function(){var t=this,e=this.payTypeList[this.payIndex];if(e){var a=encodeURIComponent(this.$config.h5Domain+"/pages_tool/pay/result?code="+this.payInfo.out_trade_no);this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:this.payInfo.out_trade_no,pay_type:e.type,return_url:a},success:function(a){if(uni.hideLoading(),a.code>=0)switch(e.type){case"alipay":if(t.$util.isWeiXin()){var i=encodeURIComponent(a.data.data);t.$util.redirectTo("/pages/pay/wx_pay/wx_pay",{wx_alipay:i,out_trade_no:t.payInfo.out_trade_no},"redirectTo")}else location.href=a.data.data,t.checkPayStatus();break;case"wechatpay":if(t.$util.isWeiXin()){if("ios"==uni.getSystemInfoSync().platform)var o=uni.getStorageSync("initUrl");else o=location.href;t.$api.sendRequest({url:"/wechat/api/wechat/jssdkconfig",data:{url:o},success:function(e){var i=new n.Weixin,o=a.data.data;i.init(e.data),i.pay({timestamp:o.timestamp,nonceStr:o.nonceStr,package:o.package,signType:o.signType,paySign:o.paySign},(function(e){"chooseWXPay:ok"==e.errMsg?t.back?location.replace(t.back+"/pages_tool/pay/result?code="+t.payInfo.out_trade_no):t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"redirectTo"):t.$util.showToast({title:e.errMsg})}))}})}else location.href=a.data.url,t.checkPayStatus();break}else t.$util.showToast({title:a.message})},fail:function(e){uni.hideLoading(),t.$util.showToast({title:"request:fail"})}})}},checkPayStatus:function(){var t=this;this.timer=setInterval((function(){t.$api.sendRequest({url:"/api/pay/status",data:{out_trade_no:t.payInfo.out_trade_no},success:function(e){0==e.code?2==e.data.pay_status&&(clearInterval(t.timer),t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"redirectTo")):clearInterval(t.timer)}})}),1e3)}},watch:{storeToken:function(t,e){t&&this.getPayInfo()}},filters:{moneyFormat:function(t){return parseFloat(t).toFixed(2)}}};e.default=i},"342b2":function(t,e,a){var n=a("0717");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("46732ba4",n,!0,{sourceMap:!1,shadowMode:!1})},6173:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={pageMeta:a("7854").default,nsEmpty:a("52a6").default,nsLogin:a("2910").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-view",{staticClass:"cashier"},[t.payInfo?[0==t.payInfo.pay_status?[a("v-uni-text",{staticClass:"content"},[t._v(t._s(t.payInfo.pay_body))]),a("v-uni-view",{staticClass:"money-wrap"},[a("v-uni-text",{staticClass:"unit price-font"},[t._v("￥")]),a("v-uni-text",{staticClass:"money price-font"},[t._v(t._s(t._f("moneyFormat")(t.payInfo.pay_money)))])],1),t.payTypeList.length>0?[a("v-uni-view",{staticClass:"pay-type"},t._l(t.payTypeList,(function(e,n){return a("v-uni-view",{key:n,staticClass:"payment-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.payIndex=n}}},[a("v-uni-view",[a("v-uni-text",{staticClass:"iconfont",class:e.icon}),a("v-uni-text",{staticClass:"name"},[t._v(t._s(e.name))])],1),a("v-uni-text",{staticClass:"iconfont",class:t.payIndex==n?"icon-yuan_checked color-base-text":"icon-checkboxblank"})],1)})),1),a("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm.apply(void 0,arguments)}}},[t._v("确认支付")])]:a("v-uni-view",{staticClass:"empty"},[t._v("店铺尚未配置支付方式！")])]:a("ns-empty",{attrs:{text:"该支付单据已支付","is-index":!0}})]:a("ns-empty",{attrs:{text:"未获取到支付信息","is-index":!0}}),a("ns-login",{ref:"login"})],2)],1)},o=[]},7854:function(t,e,a){"use strict";a.r(e);var n=a("8ba8"),i=a("f48d");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);var r=a("828b"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"8a00":function(t,e,a){"use strict";a.r(e);var n=a("1f36"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},"8ba8":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},i=[]},"90fd":function(t,e,a){"use strict";a.r(e);var n=a("6173"),i=a("8a00");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("ffdc");var r=a("828b"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"5ed2a988",null,!1,n["a"],void 0);e["default"]=s.exports},cc1b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},i={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var a=function a(i){i.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",a),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",a)}})}}}};e.default=i},f48d:function(t,e,a){"use strict";a.r(e);var n=a("cc1b"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},ffdc:function(t,e,a){"use strict";var n=a("342b2"),i=a.n(n);i.a}}]);