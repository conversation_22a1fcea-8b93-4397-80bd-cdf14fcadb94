(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-game-record"],{6146:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("c223");e.default={data:function(){return{list:[],id:0}},onLoad:function(t){t.id&&(this.id=t.id)},onShow:function(){this.list=[],this.$refs.mescroll&&this.$refs.mescroll.refresh()},methods:{getListData:function(t){var e=this;this.$api.sendRequest({url:"/api/game/recordPage",data:{id:this.id,page:t.num,page_size:t.size},success:function(n){var o=[];0==n.code&&n.data?o=n.data.list:e.$util.showToast({title:n.message}),t.endSuccess(o.length),1==t.num&&(e.list=[]),e.list=e.list.concat(o),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(n){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})}}}},"69a5":function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.mescroll-uni-content[data-v-3cb07def]{overflow:hidden}.record-item[data-v-3cb07def]{display:flex;padding:%?30?%;margin:%?20?% %?30?% 0;background-color:#fff;border-radius:%?10?%;align-items:center}.record-item .image-wrap[data-v-3cb07def]{width:%?80?%;height:%?80?%;display:flex;justify-content:center;align-items:center;overflow:hidden;border-radius:%?10?%}.record-item .image-wrap uni-image[data-v-3cb07def]{width:%?80?%}.record-item .content[data-v-3cb07def]{flex:1;padding:0 %?20?%}.record-item .content .name[data-v-3cb07def]{line-height:1;color:#303133;margin-bottom:%?30?%}.record-item .content .time[data-v-3cb07def],\r\n.record-item .content .cont[data-v-3cb07def]{color:#909399;line-height:1;margin-top:%?20?%}.record-item .btn[data-v-3cb07def]{width:%?128?%;color:#fff;padding:%?6?% 0;text-align:center;border-radius:%?10?%;font-size:%?24?%}',""]),t.exports=e},"6d77":function(t,e,n){"use strict";n.r(e);var o=n("6146"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=i.a},7854:function(t,e,n){"use strict";n.r(e);var o=n("8ba8"),i=n("f48d");for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);var r=n("828b"),s=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=s.exports},"7ee3":function(t,e,n){"use strict";n.r(e);var o=n("ef14"),i=n("6d77");for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);n("9bd6");var r=n("828b"),s=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"3cb07def",null,!1,o["a"],void 0);e["default"]=s.exports},"8ba8":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},i=[]},"9bd6":function(t,e,n){"use strict";var o=n("f1d60"),i=n.n(o);i.a},cc1b:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("64aa"),n("5c47"),n("a1c1"),n("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},i={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var n=function n(i){i.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",n),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",n)}})}}}};e.default=i},ef14:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return o}));var o={pageMeta:n("7854").default,nsEmpty:n("52a6").default,loadingCover:n("c003").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("page-meta",{attrs:{"page-style":t.themeColor}}),n("mescroll-uni",{ref:"mescroll",on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getListData.apply(void 0,arguments)}}},[n("template",{attrs:{slot:"list"},slot:"list"},[t.list.length>0?t._l(t.list,(function(e,o){return n("v-uni-view",{key:o,staticClass:"record-item"},[n("v-uni-view",{staticClass:"image-wrap"},[1==e.award_type?n("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/game/point.png"),mode:"widthFix"}}):t._e(),2==e.award_type?n("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/game/balance.png"),mode:"widthFix"}}):t._e(),3==e.award_type?n("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/game/coupon.png"),mode:"widthFix"}}):t._e()],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-view",{staticClass:"name"},[t._v(t._s(e.award_name))]),1==e.award_type?n("v-uni-view",{staticClass:"cont font-size-goods-tag"},[t._v("奖励详情："+t._s(e.point)+"个积分")]):t._e(),2==e.award_type?n("v-uni-view",{staticClass:"cont font-size-goods-tag"},[t._v("奖励详情："+t._s(e.balance)+"元红包")]):t._e(),3==e.award_type?n("v-uni-view",{staticClass:"cont font-size-goods-tag"},[t._v("奖励详情：优惠券“"+t._s(e.relate_name)+"”")]):t._e(),n("v-uni-view",{staticClass:"time font-size-goods-tag"},[t._v("中奖时间："+t._s(t.$util.timeStampTurnTime(e.create_time)))])],1),1==e.award_type?n("v-uni-view",{staticClass:"btn color-base-bg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_tool/member/point")}}},[t._v("查看")]):t._e(),2==e.award_type?n("v-uni-view",{staticClass:"btn color-base-bg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_tool/member/balance_detail")}}},[t._v("查看")]):t._e(),3==e.award_type?n("v-uni-view",{staticClass:"btn color-base-bg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_tool/member/coupon")}}},[t._v("查看")]):t._e()],1)})):[n("ns-empty",{attrs:{text:"暂无中奖记录",isIndex:!1}})],n("loading-cover",{ref:"loadingCover"})],2)],2)],1)},a=[]},f1d60:function(t,e,n){var o=n("69a5");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=n("967d").default;i("89f15c4e",o,!0,{sourceMap:!1,shadowMode:!1})},f48d:function(t,e,n){"use strict";n.r(e);var o=n("cc1b"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=i.a}}]);