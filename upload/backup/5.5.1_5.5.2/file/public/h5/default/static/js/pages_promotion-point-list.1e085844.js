(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-point-list"],{"015d":function(t,e,i){"use strict";i.r(e);var a=i("0f46"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},"0410":function(t,e,i){"use strict";i.r(e);var a=i("7b93"),o=i("e01c");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("0b85"),i("a14f");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"5ba96dde",null,!1,a["a"],void 0);e["default"]=r.exports},"0b85":function(t,e,i){"use strict";var a=i("a0ab"),o=i.n(a);o.a},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=a},1150:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".ns-adv[data-v-5ba96dde] uni-image{width:100%;border-radius:0}",""]),t.exports=e},7854:function(t,e,i){"use strict";i.r(e);var a=i("8ba8"),o=i("f48d");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=r.exports},"7b93":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){return a}));var a={pageMeta:i("7854").default,uniPopup:i("d745").default,loadingCover:i("c003").default,nsLogin:i("2910").default,hoverNav:i("c1f1").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"conteiner"},[i("v-uni-scroll-view",{staticClass:"point-scroll-view",attrs:{"scroll-y":"true"},on:{scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.getData.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"point-wrap",style:{"background-position-y":-t.menuButtonBounding.bottom+"px"}},[i("v-uni-view",{staticClass:"head-box"},[i("v-uni-view",{staticClass:"account-content"},[i("v-uni-view",{staticClass:"left"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/point/point-icon.png"),mode:"widthFix"}}),i("v-uni-view",[t._v("我的积分")])],1),i("v-uni-view",{staticClass:"right",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirect("/pages_tool/member/point")}}},[i("v-uni-text",{staticClass:"point price-font"},[t._v(t._s(t.point))]),i("v-uni-text",{staticClass:"text"},[t._v("积分")]),i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1),i("v-uni-view",{staticClass:"remark"},[i("v-uni-view",{staticClass:"label"},[t._v("提醒")]),i("v-uni-view",{staticClass:"text"},[t._v("积分兑好礼，每日上新换不停！")])],1)],1),i("v-uni-view",{staticClass:"menu-wrap"},[i("v-uni-view",{staticClass:"menu-list"},[i("v-uni-view",{staticClass:"menu-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openPointPopup()}}},[i("v-uni-image",{staticClass:"menu-img",attrs:{src:t.$util.img("/public/uniapp/point/point-rule.png")}}),i("v-uni-image",{staticClass:"menu-tag",attrs:{src:t.$util.img("/public/uniapp/point/must-see.png")}}),i("v-uni-view",{staticClass:"title"},[t._v("活动规则")])],1),i("v-uni-view",{staticClass:"menu-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirect("/pages_tool/recharge/list")}}},[i("v-uni-image",{staticClass:"menu-img",attrs:{src:t.$util.img("/public/uniapp/point/recharge.png")}}),i("v-uni-image",{staticClass:"menu-tag",attrs:{src:t.$util.img("/public/uniapp/point/high.png")}}),i("v-uni-view",{staticClass:"title"},[t._v("储值赚积分")])],1),i("v-uni-view",{staticClass:"menu-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirect("/pages_promotion/point/order_list")}}},[i("v-uni-image",{staticClass:"menu-img",attrs:{src:t.$util.img("/public/uniapp/point/exchange-record.png")}}),i("v-uni-view",{staticClass:"title"},[t._v("兑换记录")])],1),i("v-uni-view",{staticClass:"menu-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.luckdraw.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"menu-img",attrs:{src:t.$util.img("/public/uniapp/point/luck-draw.png")}}),i("v-uni-view",{staticClass:"title"},[t._v("积分抽奖")])],1),i("v-uni-view",{staticClass:"menu-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirect("/pages_tool/member/point_detail")}}},[i("v-uni-image",{staticClass:"menu-img",attrs:{src:t.$util.img("/public/uniapp/point/point-detail.png")}}),i("v-uni-view",{staticClass:"title"},[t._v("积分明细")])],1)],1)],1),i("v-uni-view",{staticClass:"poster-wrap"},[i("v-uni-view",{staticClass:"poster-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirect("/pages_tool/recharge/list")}}},[i("v-uni-image",{attrs:{src:t.$util.img("/public/uniapp/point/recharge-poster.png"),mode:"widthFix"}})],1),i("v-uni-view",{staticClass:"poster-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.luckdraw.apply(void 0,arguments)}}},[i("v-uni-image",{attrs:{src:t.$util.img("/public/uniapp/point/luck-draw-poster.png"),mode:"widthFix"}})],1)],1),t.rechargeList.length?i("v-uni-view",{staticClass:"recharge-list-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redirect("/pages_tool/recharge/list")}}},t._l(t.rechargeList.slice(0,4),(function(e,a){return i("v-uni-view",{key:a,staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"recharge"},[t._v("储值"+t._s(parseFloat(e.buy_price))+"元")]),i("v-uni-view",{staticClass:"point"},[t._v("可得"+t._s(e.point)+"积分")]),i("v-uni-view",{staticClass:"btn"},[t._v("去储值")])],1)})),1):t._e(),i("v-uni-view",{staticClass:"body-wrap",class:{"no-login":!t.storeToken}},[t.couponList.length>0?i("v-uni-view",{staticClass:"point-exchange-wrap exchange-coupon"},[i("v-uni-view",{staticClass:"card-category-title"},[i("v-uni-text",{staticClass:"before-line"}),i("v-uni-text",[t._v("积分换券")]),i("v-uni-text",{staticClass:"after-line"})],1),i("v-uni-view",{staticClass:"list-wrap"},[i("v-uni-view",{staticClass:"list-wrap-scroll",class:{"single-row":t.couponList.length<3}},t._l(t.couponList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"list-wrap-item coupon-list-wrap-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"img-box"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/point/coupon_"+t.themeStyle.name+"_bg1.png")}})],1),i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{staticClass:"coupon",style:{backgroundImage:"url("+t.$util.img("public/uniapp/point/coupon_theme-blue_bg1.jpg")+")"}},[i("v-uni-view",{staticClass:"coupon_left color-line-border"},[i("v-uni-view",{staticClass:"price price-font"},["reward"==e.coupon_type?[i("v-uni-text",[t._v("￥")]),t._v(t._s(parseFloat(e.money)))]:t._e(),"discount"==e.coupon_type?[t._v(t._s(parseFloat(e.discount))),i("v-uni-text",[t._v("折")])]:t._e()],2),i("v-uni-view",{staticClass:"coupon-info"},[i("v-uni-view",{staticClass:"coupon_condition font-size-activity-tag"},[t._v(t._s(0==e.at_least?"无门槛优惠券":"满"+parseFloat(e.at_least).toFixed(0)+"可用"))]),1==e.goods_type?i("v-uni-view",{staticClass:"coupon_type font-size-activity-tag"},[t._v("全场券")]):2==e.goods_type||3==e.goods_type?i("v-uni-view",{staticClass:"coupon_type font-size-activity-tag"},[t._v("指定券")]):t._e()],1)],1),i("v-uni-view",{staticClass:"coupon_right"},[i("v-uni-view",{staticClass:"coupon_num font-size-tag"},[t._v(t._s(e.point)+"积分")]),i("v-uni-view",{staticClass:"coupon_btn"},[t._v("兑换")])],1)],1)],1)],1)})),1)],1)],1):t._e(),t.hongbaoList.length>0?i("v-uni-view",{staticClass:"point-exchange-wrap exchange-hongbao"},[i("v-uni-view",{staticClass:"card-category-title"},[i("v-uni-text",{staticClass:"before-line"}),i("v-uni-text",[t._v("积分换红包")]),i("v-uni-text",{staticClass:"after-line"})],1),i("v-uni-view",{staticClass:"list-wrap"},t._l(t.hongbaoList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"list-wrap-item hongbao-list-wrap-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"img-box"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/point/hongbao_bg.png")}})],1),i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{staticClass:"coupon hongbao"},[i("v-uni-view",{staticClass:"coupon_left"},[i("v-uni-view",{staticClass:"price price-font"},[i("v-uni-text",[t._v("￥")]),t._v(t._s(parseFloat(e.balance).toFixed(0)))],1)],1),i("v-uni-view",{staticClass:"coupon_right"},[i("v-uni-view",{staticClass:"coupon_num  font-size-tag"},[t._v(t._s(e.point)+"积分")]),i("v-uni-view",{staticClass:"coupon_btn"},[t._v("兑换")])],1)],1)],1)],1)})),1)],1):t._e(),t.goodsList.length>0?i("v-uni-view",{staticClass:"point-exchange-wrap"},[i("v-uni-view",{staticClass:"card-category-title"},[i("v-uni-text",{staticClass:"before-line"}),i("v-uni-text",[t._v("积分换礼品")]),i("v-uni-text",{staticClass:"after-line"})],1),i("v-uni-view",{staticClass:"list-wrap"},[t.goodsList.length?i("v-uni-view",{staticClass:"goods-list double-column"},t._l(t.goodsList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"goods-item "},[i("v-uni-view",{staticClass:"goods-img",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-image",{attrs:{src:t.goodsImg(e),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError(a)}}})],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap"},[i("v-uni-view",{staticClass:"goods-name",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[t._v(t._s(e.name))])],1),i("v-uni-view",{staticClass:"lineheight-clear"},[i("v-uni-view",{staticClass:"discount-price"},[i("v-uni-view",[i("v-uni-text",{staticClass:"unit price-font point"},[t._v(t._s(e.point))]),i("v-uni-text",{staticClass:"unit  font-size-tag "},[t._v("积分")])],1),e.price>0&&e.pay_type>0?[i("v-uni-text",{staticClass:"unit  font-size-tag"},[t._v("+")]),i("v-uni-view",[i("v-uni-text",{staticClass:"font-size-tag"},[t._v(t._s(parseFloat(e.price).toFixed(2).split(".")[0]))]),i("v-uni-text",{staticClass:"unit  font-size-tag"},[t._v("."+t._s(parseFloat(e.price).toFixed(2).split(".")[1])+"元")])],1)]:t._e()],2),i("v-uni-view",{staticClass:"btn",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[t._v("兑换")])],1),e.stock_show||e.sale_show?i("v-uni-view",{staticClass:"pro-info"},[e.stock_show?i("v-uni-view",{staticClass:"font-size-activity-tag color-tip"},[t._v("库存:"+t._s(isNaN(parseInt(e.stock))?0:parseInt(e.stock)))]):t._e(),e.sale_show?i("v-uni-view",{staticClass:"font-size-activity-tag color-tip sale"},[t._v("已兑:"+t._s(isNaN(parseInt(e.sale_num))?0:parseInt(e.sale_num)))]):t._e()],1):t._e()],1)],1)})),1):t._e()],1)],1):t._e()],1)],1)],1),i("v-uni-view",{on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"pointPopup",attrs:{type:"bottom"}},[i("v-uni-view",{staticClass:"tips-layer"},[i("v-uni-view",{staticClass:"head",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closePointPopup()}}},[i("v-uni-view",{staticClass:"title"},[t._v("积分说明")]),i("v-uni-text",{staticClass:"iconfont icon-close"})],1),i("v-uni-view",{staticClass:"body"},[i("v-uni-view",{staticClass:"detail margin-bottom"},[i("v-uni-view",{staticClass:"tip"},[t._v("积分的获取")]),i("v-uni-view",{staticClass:"font-size-base"},[t._v("1、积分可在注册、签到、分享、消费、充值时获得。")]),i("v-uni-view",{staticClass:"font-size-base"},[t._v("2、在购买部分商品时可获得积分。")]),i("v-uni-view",{staticClass:"tip"},[t._v("积分的使用")]),i("v-uni-view",{staticClass:"font-size-base"},[t._v("1、积分可用于兑换积分中心的商品。")]),i("v-uni-view",{staticClass:"font-size-base"},[t._v("2、积分可在参与某些活动时使用。")]),i("v-uni-view",{staticClass:"font-size-base"},[t._v("3、积分不得转让，出售，不设有效期。")]),i("v-uni-view",{staticClass:"tip"},[t._v("积分的查询")]),i("v-uni-view",{staticClass:"font-size-base"},[t._v("1、积分可在会员中心中查询具体数额以及明细。")])],1)],1)],1)],1)],1),i("loading-cover",{ref:"loadingCover"}),i("ns-login",{ref:"login"}),i("hover-nav")],1)],1)},n=[]},"884c":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("c223"),i("e838"),i("aa9c");var o=a(i("d745")),n={components:{uniPopup:o.default},data:function(){return{mescroll:{num:0,total:1,loading:!1},categoryList:[{id:1,name:"积分换好物"},{id:2,name:"积分换券"},{id:3,name:"积分换红包"}],isLogin:!1,goodsList:[],couponList:[],hongbaoList:[],point:0,signState:1,mpShareData:null,menuButtonBounding:{bottom:0},rechargeList:[],newestGame:null}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.pointexchange||(e.$util.showToast({title:"商家未开启积分商城",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}this.getData(),this.getRechargeList(),this.getNewestGame()},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member")),this.storeToken&&this.getAccountInfo(),this.getCouponList(),this.getHongbaoList(),this.getSignState()},methods:{getSignState:function(){var t=this;this.$api.sendRequest({url:"/api/membersignin/getSignStatus",success:function(e){0==e.code&&(t.signState=e.data.is_use)}})},jumpPage:function(t){this.$util.redirectTo(t)},openPointPopup:function(){this.$refs.pointPopup.open()},closePointPopup:function(){this.$refs.pointPopup.close()},getCouponList:function(){var t=this;this.$api.sendRequest({url:"/pointexchange/api/goods/page",data:{page_size:0,type:2},success:function(e){0==e.code&&e.data?t.couponList=e.data.list:t.$util.showToast({title:e.message}),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(){this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},getHongbaoList:function(){var t=this;this.$api.sendRequest({url:"/pointexchange/api/goods/page",data:{page_size:0,type:3},success:function(e){0==e.code&&e.data?t.hongbaoList=e.data.list:t.$util.showToast({title:e.message}),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(){this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},getData:function(){var t=this;this.mescroll.loading||this.mescroll.num>=this.mescroll.total||(this.mescroll.loading=!0,this.$api.sendRequest({url:"/pointexchange/api/goods/page",data:{page:this.mescroll.num+1,page_size:10,type:1},success:function(e){var i=[],a=e.message;0==e.code&&e.data?i=e.data.list:t.$util.showToast({title:a}),t.mescroll.loading=!1,t.mescroll.total=e.data.page_count,t.mescroll.num+=1,1==t.mescroll.num&&(t.goodsList=[]),t.goodsList=t.goodsList.concat(i),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(){this.mescroll.loading=!1,this.$refs.loadingCover&&this.$refs.loadingCover.hide()}}))},toDetail:function(t){this.$util.redirectTo("/pages_promotion/point/detail",{id:t.id})},goGoodsList:function(){this.$util.redirectTo("/pages_promotion/point/goods_list")},getAccountInfo:function(){var t=this;this.$api.sendRequest({url:"/api/memberaccount/info",data:{account_type:"point"},success:function(e){0==e.code&&e.data?isNaN(parseFloat(e.data.point))||(t.point=parseFloat(e.data.point).toFixed(0)):t.$util.showToast({title:e.message})}})},login:function(){this.$refs.login.open("/pages_promotion/point/list")},imgError:function(t){this.goodsList[t].image=this.$util.getDefaultImage().goods,this.$forceUpdate()},goodsImg:function(t){var e="";switch(t.type){case 1:e=this.$util.img(t.image.split(",")[0],{size:"mid"});break;case 2:e=t.image?this.$util.img(t.image):this.$util.img("public/uniapp/point/coupon.png");break;case 3:e=t.image?this.$util.img(t.image):this.$util.img("public/uniapp/point/hongbao.png");break}return e},redirect:function(t){this.storeToken?this.$util.redirectTo(t):this.$refs.login.open(t)},getRechargeList:function(){var t=this;this.$api.sendRequest({url:"/memberrecharge/api/memberrecharge/page",data:{page_size:100,page:1},success:function(e){if(0==e.code&&e.data){var i=[];e.data.list.forEach((function(t){t.point>0&&i.push(t)})),t.rechargeList=i}}})},back:function(){getCurrentPages().length>1?uni.navigateBack({delta:1}):this.$util.redirectTo("/pages/index/index")},getNewestGame:function(){var t=this;this.$api.sendRequest({url:"/api/game/newestgame",success:function(e){0==e.code&&e.data&&(t.newestGame=e.data)}})},luckdraw:function(){if(this.newestGame)switch(this.newestGame.game_type){case"cards":this.$util.redirectTo("/pages_promotion/game/cards",{id:this.newestGame.game_id});break;case"egg":this.$util.redirectTo("/pages_promotion/game/smash_eggs",{id:this.newestGame.game_id});break;case"turntable":this.$util.redirectTo("/pages_promotion/game/turntable",{id:this.newestGame.game_id});break}else this.$util.showToast({title:"暂无相关活动"})}},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine}};e.default=n},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},9085:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.conteiner[data-v-5ba96dde]{width:100vw;height:100vh;display:flex;flex-direction:column}.conteiner .point-scroll-view[data-v-5ba96dde]{flex:1;height:0}.point-navbar[data-v-5ba96dde]{width:100vw;padding-bottom:%?20?%;display:flex;align-items:center;position:fixed;left:0;top:0;z-index:100;background-image:linear-gradient(1turn,#f8f8f8,#e74a32);background-size:100% %?400?%;background-position-y:top;background-repeat:no-repeat}.point-navbar .nav-wrap[data-v-5ba96dde]{height:100%;display:flex;padding:0 %?24?%;box-sizing:border-box}.point-navbar .back[data-v-5ba96dde]{background:hsla(0,0%,100%,.4);display:flex;align-items:center;justify-content:center;border-radius:50%}.point-navbar .back .iconfont[data-v-5ba96dde]{color:#222;font-size:%?36?%;font-weight:700}.point-navbar .search[data-v-5ba96dde]{flex:1;background:#fff;margin:0 %?15?%;border-radius:%?30?%;display:flex;align-items:center;box-sizing:border-box;padding:%?20?%}.point-navbar .search .tips[data-v-5ba96dde]{color:#4a4a4a;font-size:%?24?%;margin-left:%?20?%}.point-navbar .sign uni-image[data-v-5ba96dde]{width:100%;height:100%}.point-navbar-block[data-v-5ba96dde]{padding-bottom:%?20?%}.point-wrap[data-v-5ba96dde]{background-image:linear-gradient(1turn,#f8f8f8,#e74a32);background-size:100% %?380?%;background-repeat:no-repeat;padding-top:%?20?%}\r\n/* 说明弹框 */.tips-layer[data-v-5ba96dde]{background:#fff;z-index:999;height:40%;width:100%}.tips-layer .head[data-v-5ba96dde]{position:relative}.tips-layer .title[data-v-5ba96dde]{height:%?80?%;line-height:%?80?%;text-align:center;font-size:%?32?%;font-weight:700}.tips-layer uni-text[data-v-5ba96dde]{position:absolute;top:%?8?%;right:22px;font-size:%?32?%;font-weight:500}.tips-layer .body[data-v-5ba96dde]{width:100%;height:calc(100% - %?80?%);overflow-y:scroll}.tips-layer .body .detail[data-v-5ba96dde]{padding:%?20?%}.tips-layer .body .detail .font-size-base[data-v-5ba96dde]{margin-bottom:%?10?%}.lineheight-clear[data-v-5ba96dde]{line-height:1!important}.goods-list.double-column[data-v-5ba96dde]{display:flex;flex-wrap:wrap;margin-top:%?20?%}.goods-list.double-column .goods-item[data-v-5ba96dde]{flex:1;position:relative;background-color:#fff;flex-basis:48%;max-width:calc((100% - %?24?%) / 2);margin-right:%?24?%;margin-bottom:%?24?%;border-radius:%?18?%}.goods-list.double-column .goods-item[data-v-5ba96dde]:nth-child(2n){margin-right:0}.goods-list.double-column .goods-item .goods-img[data-v-5ba96dde]{position:relative;overflow:hidden;padding-top:100%;border-top-left-radius:%?18?%;border-top-right-radius:%?18?%}.goods-list.double-column .goods-item .goods-img uni-image[data-v-5ba96dde]{width:100%;position:absolute;top:50%;left:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.goods-list.double-column .goods-item .goods-tag[data-v-5ba96dde]{color:#fff;line-height:1;padding:%?8?% %?16?%;position:absolute;border-bottom-right-radius:%?10?%;top:0;left:0;font-size:%?22?%}.goods-list.double-column .goods-item .goods-tag-img[data-v-5ba96dde]{position:absolute;border-top-left-radius:%?10?%;width:%?80?%;height:%?80?%;top:0;left:0;z-index:5;overflow:hidden}.goods-list.double-column .goods-item .goods-tag-img uni-image[data-v-5ba96dde]{width:100%;height:100%}.goods-list.double-column .goods-item .info-wrap[data-v-5ba96dde]{padding:0 %?20?% %?24?% %?20?%}.goods-list.double-column .goods-item .goods-name[data-v-5ba96dde]{line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-top:%?20?%;font-size:%?26?%;color:#333;font-weight:600}.goods-list.double-column .goods-item .discount-price[data-v-5ba96dde]{display:flex;flex-wrap:wrap;align-items:center;font-weight:700;line-height:1;margin-top:%?16?%;color:var(--price-color);overflow:hidden;flex:1;width:0;margin-right:%?20?%}.goods-list.double-column .goods-item .discount-price uni-view[data-v-5ba96dde]{line-height:1;color:var(--price-color)}.goods-list.double-column .goods-item .discount-price .unit[data-v-5ba96dde]{margin-right:%?6?%}.goods-list.double-column .goods-item .discount-price .point[data-v-5ba96dde]{font-size:%?32?%}.goods-list.double-column .goods-item .pro-info[data-v-5ba96dde]{display:flex;margin-top:%?16?%;justify-content:flex-start}.goods-list.double-column .goods-item .pro-info > uni-view[data-v-5ba96dde]{line-height:1;display:flex;align-items:center}.goods-list.double-column .goods-item .pro-info > uni-view uni-button[data-v-5ba96dde]{padding:0 %?16?%;line-height:2}.goods-list.double-column .goods-item .pro-info > uni-view[data-v-5ba96dde]:nth-child(2):before{content:" ";width:%?2?%;background-color:#d8d8d8;height:%?20?%;margin:0 %?16?%}.goods-list.double-column .goods-item .member-price-tag[data-v-5ba96dde]{display:inline-block;width:%?60?%;line-height:1;margin-left:%?6?%}.goods-list.double-column .goods-item .member-price-tag uni-image[data-v-5ba96dde]{width:100%}.goods-list.double-column .goods-item .lineheight-clear[data-v-5ba96dde]{display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap}.goods-list.double-column .goods-item .lineheight-clear .btn[data-v-5ba96dde]{width:%?96?%;height:%?50?%;background:#ff6c24;border-radius:%?50?%;text-align:center;color:#fff;font-size:%?26?%;margin-top:%?20?%}.head-wrap[data-v-5ba96dde]{width:100vw;line-height:1;position:relative;height:%?270?%}.head-wrap > uni-image[data-v-5ba96dde]{width:100%}.head-wrap .wrap[data-v-5ba96dde]{width:100%;height:100%;position:absolute;z-index:5;top:0;left:0}.head-wrap .member-wrap[data-v-5ba96dde]{height:%?190?%;padding:%?50?% %?30?% %?30?% %?30?%;display:flex;align-items:center;box-sizing:border-box}.head-wrap .member-wrap .headimg[data-v-5ba96dde]{width:%?100?%;height:%?100?%;background:#fff;border:2px solid #fff;border-radius:50%;overflow:hidden}.head-wrap .member-wrap .headimg uni-image[data-v-5ba96dde]{width:100%;height:100%}.head-wrap .member-wrap .point[data-v-5ba96dde]{margin-left:%?30?%;color:var(--btn-text-color);font-size:%?36?%}.head-wrap .member-wrap .point-name[data-v-5ba96dde]{font-size:%?24?%;color:var(--btn-text-color);margin-left:%?4?%;margin-top:%?5?%}.head-wrap .member-wrap .rule[data-v-5ba96dde]{flex:1;text-align:right;color:var(--btn-text-color)}.head-wrap .member-wrap .icon-wenhao[data-v-5ba96dde]{font-size:%?24?%;color:var(--btn-text-color);margin-right:%?6?%}.head-wrap .action-wrap[data-v-5ba96dde]{display:flex;justify-content:space-between;align-items:center;height:%?80?%;background-color:hsla(0,0%,100%,.1)}.head-wrap .action-wrap uni-view[data-v-5ba96dde]{line-height:1;text-align:center;width:calc((100vw - %?1?%) / 2);color:var(--btn-text-color)}.head-wrap .action-wrap uni-view uni-text[data-v-5ba96dde]{font-size:%?24?%;margin-left:%?8?%}.head-wrap .action-wrap uni-view.split[data-v-5ba96dde]{width:%?1?%;height:%?50?%;background-color:hsla(0,0%,93.3%,.3);flex-shrink:0}.head-wrap .action-wrap uni-view uni-image[data-v-5ba96dde]{width:100%}.head-wrap .no-login[data-v-5ba96dde]{display:flex;align-items:center;justify-content:center;text-align:center}.head-wrap .no-login uni-text[data-v-5ba96dde]{color:#fff}.head-wrap .no-login .login-btn[data-v-5ba96dde]{display:inline-block;height:%?70?%;line-height:%?70?%;width:%?200?%;border:1px solid #fff;border-radius:%?10?%;margin-bottom:%?20?%}.ns-adv[data-v-5ba96dde]{margin:0;border-radius:0;overflow:hidden;line-height:1}.ns-adv uni-image[data-v-5ba96dde]{width:100%;border-radius:0!important}.body-wrap[data-v-5ba96dde]{margin-top:%?20?%}.body-wrap.no-login[data-v-5ba96dde]{margin-top:%?20?%}.body-wrap .point-exchange-wrap[data-v-5ba96dde]{padding:0 %?24?%;margin-top:%?30?%}.body-wrap .type-wrap[data-v-5ba96dde]{display:flex;align-items:center}.body-wrap .type-wrap .type-name[data-v-5ba96dde]{font-size:%?30?%;color:#303133;line-height:1}.body-wrap .type-wrap > uni-view[data-v-5ba96dde]{width:%?2?%;height:%?23?%;background-color:#909399;margin:0 %?20?%}.body-wrap .type-wrap .type-sub[data-v-5ba96dde]{font-size:%?24?%;color:#909399;line-height:1}.body-wrap .list-wrap[data-v-5ba96dde]{width:100%}.body-wrap .list-wrap .list-wrap-scroll[data-v-5ba96dde]{width:100%;flex-direction:row;line-height:1}.body-wrap .list-wrap .list-wrap-item[data-v-5ba96dde]{display:inline-block;width:%?330?%;overflow:hidden;margin-right:%?30?%;margin-top:%?20?%;position:relative}.body-wrap .list-wrap .list-wrap-item.hongbao-list-wrap-item[data-v-5ba96dde]{height:%?141?%}.body-wrap .list-wrap .list-wrap-item[data-v-5ba96dde]:nth-child(2n+2){margin-right:0}.body-wrap .list-wrap .list-wrap-item .img-box[data-v-5ba96dde]{width:100%;height:100%;position:absolute;top:0;left:0}.body-wrap .list-wrap .list-wrap-item .img-box uni-image[data-v-5ba96dde]{width:100%;height:100%}.body-wrap .list-wrap .list-wrap-item .content[data-v-5ba96dde]{position:relative;z-index:9}.body-wrap .list-wrap .list-wrap-item .content .coupon[data-v-5ba96dde]{background-size:100% 100%;background-repeat:no-repeat;border-radius:%?10?%;display:flex;padding:%?20?% 0}.body-wrap .list-wrap .list-wrap-item .content .coupon .coupon_right[data-v-5ba96dde]{position:relative;width:%?156?%;max-width:%?156?%}.body-wrap .list-wrap .list-wrap-item .content .coupon .coupon_right .coupon_btn[data-v-5ba96dde]{margin:%?10?% auto 0;width:%?80?%;height:%?40?%;line-height:%?40?%;font-size:%?24?%;text-align:center;border-radius:%?10?%;border-width:1px;border-style:solid;background:#ff3d3d;color:#fff}.body-wrap .list-wrap .list-wrap-item .content .coupon .coupon_right .coupon_num[data-v-5ba96dde]{margin-top:%?10?%;text-align:center;color:#ff3d3d}.body-wrap .list-wrap .list-wrap-item .content .coupon .coupon_right[data-v-5ba96dde]::after{position:absolute;top:50%;margin-left:0;content:"";width:0;height:96%;border-left:2px dashed #fd463e;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.body-wrap .list-wrap .list-wrap-item .content .coupon .coupon_left[data-v-5ba96dde]{flex:1;width:0;text-align:left;padding:0 %?20?%;display:flex;align-items:center}.body-wrap .list-wrap .list-wrap-item .content .coupon .coupon_left .price[data-v-5ba96dde]{margin-top:0!important;padding:0;font-weight:600;flex:1;width:0;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;color:#fd463e;font-size:%?56?%}.body-wrap .list-wrap .list-wrap-item .content .coupon .coupon_left .price uni-text[data-v-5ba96dde]{font-size:%?24?%;color:#fd463e}.body-wrap .list-wrap .list-wrap-item .content .coupon .coupon_left .coupon-info[data-v-5ba96dde]{flex:1;width:0;display:flex;flex-direction:column;align-items:flex-start;justify-content:space-between;height:80%}.body-wrap .list-wrap .list-wrap-item .content .coupon .coupon_left .coupon_condition[data-v-5ba96dde]{line-height:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:%?26?%;font-weight:700;color:#fd463e}.body-wrap .list-wrap .list-wrap-item .content .coupon .coupon_left .coupon_type[data-v-5ba96dde]{color:#fd463e;font-size:%?24?%}.body-wrap .list-wrap .list-wrap-item .content .hongbao .coupon_left .price[data-v-5ba96dde], .body-wrap .list-wrap .list-wrap-item .content .hongbao .coupon_left .coupon_condition[data-v-5ba96dde]{color:#fff}.body-wrap .list-wrap .list-wrap-item .content .hongbao .coupon_right .coupon_num[data-v-5ba96dde]{color:#fff}.body-wrap .list-wrap .list-wrap-item .content .hongbao .coupon_right .coupon_btn[data-v-5ba96dde]{color:#fff;border-color:#fff}.body-wrap .list-wrap .list-wrap-item .content .hongbao .coupon_right[data-v-5ba96dde]::after{position:absolute;top:0;margin-left:0;content:"";width:0;height:100%;border-left:0;opacity:.2}.body-wrap .list-wrap .list-wrap-item .content .coupon-price-wrap[data-v-5ba96dde]{width:100%;height:%?105?%;display:flex;justify-content:space-between}.body-wrap .list-wrap .list-wrap-item .content .coupon-price-wrap .coupon-price[data-v-5ba96dde]{font-size:%?48?%;margin-top:%?10?%;margin-left:%?20?%}.body-wrap .list-wrap .list-wrap-item .content .coupon-price-wrap .coupon-price uni-text[data-v-5ba96dde]{font-size:%?24?%}.body-wrap .list-wrap .list-wrap-item .content .coupon-point .coupon-point-num[data-v-5ba96dde]{width:%?160?%;height:%?44?%;position:relative}.body-wrap .list-wrap .list-wrap-item .content .coupon-point .coupon-point-num uni-image[data-v-5ba96dde]{width:100%;height:100%;position:absolute}.body-wrap .list-wrap .list-wrap-item .content .coupon-point .coupon-point-num uni-text[data-v-5ba96dde]{position:relative;z-index:9;color:#fff;font-size:%?24?%;display:inline-block;width:100%;line-height:%?44?%;text-align:center;vertical-align:top}.body-wrap .list-wrap .list-wrap-item .content .coupon-point .coupon-conditions[data-v-5ba96dde]{font-size:%?20?%;color:#909399;line-height:1;margin-top:%?24?%}.body-wrap .list-wrap .list-wrap-item .content .coupon-name[data-v-5ba96dde]{font-size:%?24?%;color:#303133;margin-top:%?23?%;line-height:1;padding:0 %?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.body-wrap .list-wrap .list-wrap-item .content.hongbao-content[data-v-5ba96dde]{background-color:#fff;border-radius:%?20?%;padding-bottom:%?30?%}.body-wrap .list-wrap .list-wrap-item .content .hongbao-img[data-v-5ba96dde]{height:%?330?%}.body-wrap .list-wrap .list-wrap-item .content .hongbao-img uni-image[data-v-5ba96dde]{width:100%;height:100%}.body-wrap .list-wrap .list-wrap-item .content .price[data-v-5ba96dde]{font-size:%?28?%;color:#303133;line-height:1;padding-left:%?26?%;margin-top:%?20?%}.body-wrap .list-wrap .list-wrap-item .content .point[data-v-5ba96dde]{font-size:%?32?%;padding-left:%?26?%;margin-top:%?17?%;line-height:1}.body-wrap .list-wrap .list-wrap-item .content .point uni-text[data-v-5ba96dde]{font-size:%?24?%}.body-wrap .list-wrap .list-wrap-item .content .stock[data-v-5ba96dde]{font-size:%?20?%;color:#909399;line-height:1;padding-left:%?26?%;margin-top:%?20?%}.exchange-coupon .list-wrap[data-v-5ba96dde]{width:100%;overflow-x:scroll}.exchange-coupon .list-wrap .list-wrap-scroll[data-v-5ba96dde]{flex-direction:column;display:flex;max-height:%?388?%;flex-wrap:wrap}.exchange-coupon .list-wrap .list-wrap-scroll.single-row[data-v-5ba96dde]{max-height:%?192?%}.exchange-coupon .list-wrap .list-wrap-item[data-v-5ba96dde]{width:%?424?%;margin-right:%?16?%;margin-top:%?18?%}.type-wrap-box[data-v-5ba96dde]{display:flex;justify-content:space-between}.type-wrap-box .more[data-v-5ba96dde]{color:#ff5251;cursor:pointer}.card-category-title[data-v-5ba96dde]{display:flex;align-items:center;justify-content:center;font-size:%?30?%;color:#222;padding:0;font-weight:700}.card-category-title .before-line[data-v-5ba96dde], .card-category-title .after-line[data-v-5ba96dde]{width:%?30?%;height:%?4?%;margin:0 %?10?%;background-color:#222}.head-box[data-v-5ba96dde]{display:flex;flex-direction:column;background-color:#fff;border-radius:%?16?%;padding:%?24?%;box-sizing:border-box;width:calc(100% - %?48?%);margin:0 %?24?% %?20?% %?24?%;position:relative}.head-box .account-content[data-v-5ba96dde]{display:flex;justify-content:space-between;align-items:center}.head-box .account-content .left[data-v-5ba96dde]{display:flex;align-items:center}.head-box .account-content .left uni-image[data-v-5ba96dde]{width:%?88?%;height:%?88?%}.head-box .account-content .left uni-view[data-v-5ba96dde]{font-size:%?32?%;font-weight:700;margin-left:%?25?%}.head-box .account-content .right[data-v-5ba96dde]{display:flex;align-items:baseline}.head-box .account-content .right .point[data-v-5ba96dde]{font-weight:700!important;font-size:%?38?%;color:#ff002d}.head-box .account-content .right .text[data-v-5ba96dde]{font-size:%?28?%;font-weight:700;margin:0 %?10?%}.head-box .remark[data-v-5ba96dde]{display:flex;align-items:center;margin-top:%?30?%;padding:%?25?% 0 0;border-top:%?2?% solid #f2f2f2}.head-box .remark .label[data-v-5ba96dde]{border-radius:%?4?%;border:%?2?% solid #fe172e;color:#fe172e;padding:%?2?% %?8?%;font-size:%?22?%;line-height:1;font-weight:700}.head-box .remark .text[data-v-5ba96dde]{font-size:%?24?%;font-weight:700;margin-left:%?30?%;line-height:1}.menu-wrap[data-v-5ba96dde]{background:#fff;border-radius:%?24?%;margin:%?20?% %?24?%;padding:%?40?% 0 %?30?% 0}.menu-wrap .menu-list[data-v-5ba96dde]{display:flex}.menu-wrap .menu-list .menu-item[data-v-5ba96dde]{flex:1;display:flex;flex-direction:column;align-items:center;position:relative}.menu-wrap .menu-list .menu-item .menu-tag[data-v-5ba96dde]{position:absolute;z-index:2;right:0;width:%?58?%;height:%?26?%}.menu-wrap .menu-list .menu-item .menu-img[data-v-5ba96dde]{width:%?86?%;height:%?86?%}.menu-wrap .menu-list .menu-item .title[data-v-5ba96dde]{margin-top:%?16?%;font-size:%?26?%;color:#222}.poster-wrap[data-v-5ba96dde]{margin:%?20?% %?24?%;display:flex}.poster-wrap .poster-item[data-v-5ba96dde]{flex:1;border-radius:%?16?%;overflow:hidden;line-height:1}.poster-wrap .poster-item uni-image[data-v-5ba96dde]{width:100%;height:auto;will-change:transform;line-height:1}.poster-wrap .poster-item[data-v-5ba96dde]:last-child{margin-left:%?20?%}.recharge-list-wrap[data-v-5ba96dde]{margin:%?20?% %?24?%;display:flex}.recharge-list-wrap .item-wrap[data-v-5ba96dde]{overflow:hidden;background:linear-gradient(321deg,#f4402b,#fd7c40);border-radius:%?16?%;position:relative;padding-top:%?40?%;width:calc((100% - %?60?%) / 4);margin-right:%?20?%;text-align:center}.recharge-list-wrap .item-wrap[data-v-5ba96dde]:nth-child(4){margin-right:0}.recharge-list-wrap .item-wrap[data-v-5ba96dde]:before{content:" ";display:block;position:absolute;left:50%;top:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);background:#f8f8f8;width:%?30?%;height:%?30?%;border-radius:50%}.recharge-list-wrap .item-wrap .recharge[data-v-5ba96dde]{font-size:%?26?%;color:#fff20c;white-space:nowrap;padding:0 %?10?%;overflow:hidden}.recharge-list-wrap .item-wrap .point[data-v-5ba96dde]{font-size:%?20?%;color:#fff;white-space:nowrap;padding:0 %?10?%;overflow:hidden}.recharge-list-wrap .item-wrap .btn[data-v-5ba96dde]{margin-top:%?30?%;line-height:%?60?%;font-size:%?24?%;font-weight:600;color:#fff;border-top:%?2?% dashed #fff}.exchange-hongbao .coupon_left .price uni-text[data-v-5ba96dde]{color:#fff!important}.exchange-hongbao .coupon_right[data-v-5ba96dde]{width:%?120?%!important;max-width:%?120?%}',""]),t.exports=e},a0ab:function(t,e,i){var a=i("9085");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("41b408fd",a,!0,{sourceMap:!1,shadowMode:!1})},a14f:function(t,e,i){"use strict";var a=i("be2e9"),o=i.n(a);o.a},a725:function(t,e,i){"use strict";var a=i("ac2a"),o=i.n(a);o.a},ac2a:function(t,e,i){var a=i("f714");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("1a69ffc2",a,!0,{sourceMap:!1,shadowMode:!1})},be2e9:function(t,e,i){var a=i("1150");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("38ce105a",a,!0,{sourceMap:!1,shadowMode:!1})},c1f1:function(t,e,i){"use strict";i.r(e);var a=i("fa1d"),o=i("015d");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("a725");var s=i("828b"),r=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"c1934e78",null,!1,a["a"],void 0);e["default"]=r.exports},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var a={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(o){o.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",a))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=o},e01c:function(t,e,i){"use strict";i.r(e);var a=i("884c"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},f48d:function(t,e,i){"use strict";i.r(e);var a=i("cc1b"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},f714:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},o=[]}}]);