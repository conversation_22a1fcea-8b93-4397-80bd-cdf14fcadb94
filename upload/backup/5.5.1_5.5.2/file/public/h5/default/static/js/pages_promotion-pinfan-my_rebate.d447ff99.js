(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-pinfan-my_rebate"],{"00ba":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?i("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},o=[]},"11ac":function(t,e,i){"use strict";var n=i("9c4f"),o=i.n(n);o.a},"13f3":function(t,e,i){"use strict";i.r(e);var n=i("d4b29"),o=i("9300");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("a0be"),i("d0bc");var s=i("828b"),r=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,"03d80da3",null,!1,n["a"],void 0);e["default"]=r.exports},"1b07":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,"[data-v-03d80da3] .uni-countdown__number,[data-v-03d80da3] .uni-countdown__splitor{margin:0;padding:0}",""]),t.exports=e},7199:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("c223");var o=n(i("e12a")),a={components:{uniCountDown:o.default},data:function(){return{mescroll:null,dataList:[],pintuanStatusList:[{id:2,name:"拼团中"},{id:3,name:"拼团成功"},{id:1,name:"拼团失败"}],pintuanStatus:2,pintuanState:[{},{color:"#FF4544",text:"拼团失败"},{color:"#FFA044",text:"拼团中"},{color:"#11BD64",text:"拼团成功"}]}},onShow:function(){var t=this;setTimeout((function(){t.addonIsExist.pinfan||(t.$util.showToast({title:"商家未开启拼团返利",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.storeToken||this.$nextTick((function(){t.$refs.login.open("/pages_promotion/pinfan/my_rebate")}))},methods:{getData:function(t){var e=this;this.mescroll=t,this.$api.sendRequest({url:"/pinfan/api/order/page",data:{page_size:t.size,page:t.num,pintuan_status:this.pintuanStatus},success:function(i){var n=[],o=i.message;0==i.code&&i.data?n=i.data.list:e.$util.showToast({title:o}),t.endSuccess(n.length),1==t.num&&(e.dataList=[]),n.forEach((function(t){t.group_end_time>i.timestamp?t.timeMachine=e.$util.countDown(t.group_end_time-i.timestamp):t.timeMachine=null})),e.dataList=e.dataList.concat(n),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(){t.endErr(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},goPinTuanDetail:function(t){this.$util.redirectTo("/pages_promotion/pinfan/detail",{pinfan_id:t})},toReward:function(t,e){1==t||2==t?this.$util.redirectTo("/pages_tool/member/balance_detail",{related_id:e,from_type:"pinfan"}):3==t?this.$util.redirectTo("/pages_tool/member/coupon",{related_id:e}):4==t&&this.$util.redirectTo("/pages_tool/member/point_detail",{related_id:e,from_type:"pinfan"})},goIndex:function(){this.$util.redirectTo("/pages/index/index")},toshare:function(t){this.$util.redirectTo("/pages_promotion/pinfan/share",{id:t})},toOrderDetail:function(t,e){this.$util.redirectTo("/pages/order/detail",{order_id:t})},categoryChange:function(t){this.pintuanStatus=t,this.mescroll.resetUpScroll()},imageError:function(t){this.dataList[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},memberImageError:function(t,e){this.dataList[t].member_list[e].member_img=this.$util.getDefaultImage().head,this.$forceUpdate()}},watch:{storeToken:function(t,e){t&&this.$refs.mescroll.refresh()}}};e.default=a},7854:function(t,e,i){"use strict";i.r(e);var n=i("8ba8"),o=i("f48d");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);var s=i("828b"),r=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=r.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},9300:function(t,e,i){"use strict";i.r(e);var n=i("7199"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},"9c4f":function(t,e,i){var n=i("bb37");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("1a51f0e1",n,!0,{sourceMap:!1,shadowMode:!1})},"9fe8":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-03d80da3] .empty{margin-top:0!important}.my_spell_category[data-v-03d80da3]{width:100%;height:%?88?%;display:flex;justify-content:space-around;background-color:#fff;position:fixed;top:0;z-index:999;box-sizing:border-box}.my_spell_category .category-item[data-v-03d80da3]{width:%?130?%;text-align:center}.my_spell_category .category-item .item-con[data-v-03d80da3]{display:inline-block;height:%?88?%;font-size:%?30?%;position:relative;line-height:%?88?%}.my_spell_category .category-item .item-con.active[data-v-03d80da3]:after{content:"";display:block;width:100%;height:%?4?%;border-radius:%?6?%;position:absolute;left:0;bottom:0}.my_spell_category .category-item[data-v-03d80da3]:last-of-type{margin-right:0}.goods-list[data-v-03d80da3]{margin:%?20?% %?30?% %?20?%;background-color:#fff;border-radius:%?10?%;padding:%?30?%}.list-header[data-v-03d80da3]{display:flex;align-items:center;justify-content:space-between}.list-header .state-time[data-v-03d80da3]{font-size:%?28?%;color:#303133}.list-header .state-sign[data-v-03d80da3]{font-size:%?24?%}.list-body[data-v-03d80da3]{display:flex;justify-content:space-between;margin-top:%?32?%}.list-body .list-body-img[data-v-03d80da3]{display:flex;align-items:center;justify-content:center;width:%?160?%;height:%?174?%;margin-right:%?18?%}.list-body .list-body-img uni-image[data-v-03d80da3]{width:%?160?%;height:%?174?%;margin-right:%?10?%}.list-body .shop-content[data-v-03d80da3]{display:flex;flex-direction:column;justify-content:space-between;width:%?531?%}.list-body .shop-content .shop-title[data-v-03d80da3]{margin-top:%?-8?%;height:%?84?%;font-size:%?28?%;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;line-height:%?42?%;color:#303133}.list-body .shop-content .pintuan-num[data-v-03d80da3]{color:#909399;font-size:%?26?%}.list-body .shop-content .status-name[data-v-03d80da3]{display:flex;justify-content:space-between;align-items:center}.list-body .shop-content .status-name .pintuan-price[data-v-03d80da3]{line-height:1;font-size:%?32?%;color:var(--price-color)}.list-body .shop-content .status-name .pintuan-price .pintuan-price-icon[data-v-03d80da3]{margin-right:%?6?%;font-size:%?24?%}.list-footer[data-v-03d80da3]{display:flex;height:%?80?%;justify-content:space-between;align-items:center;margin-top:%?22?%}.list-footer .time-wrap[data-v-03d80da3]{display:inline-block;margin-left:%?10?%}.list-footer .list-footer-time[data-v-03d80da3]{color:#909399}.list-footer uni-text[data-v-03d80da3]{border-radius:%?60?%;font-size:%?24?%;line-height:%?50?%}.list-footer .picture-box[data-v-03d80da3]{margin-top:%?20?%;width:60%;height:100%;display:flex;align-items:center}.list-footer .img-box uni-image[data-v-03d80da3]{border:%?2?% solid #fff;margin-right:%?-24?%;width:%?50?%;height:%?50?%;border-radius:50%}.empty[data-v-03d80da3]{width:100%;display:flex;flex-direction:column;align-items:center;padding:%?20?%;box-sizing:border-box;margin-top:%?150?%}.empty .iconfont[data-v-03d80da3]{font-size:%?190?%;color:#909399;line-height:1.2}.empty uni-button[data-v-03d80da3]{margin-top:%?20?%;font-size:%?28?%}',""]),t.exports=e},a0be:function(t,e,i){"use strict";var n=i("f27a"),o=i.n(n);o.a},bb37:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-countdown[data-v-45a7f114]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-45a7f114]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?24?%}.uni-countdown__splitor.day[data-v-45a7f114]{line-height:%?50?%}.uni-countdown__number[data-v-45a7f114]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;border:%?2?% solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=e},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var n={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(o){o.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",n))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=o},d0bc:function(t,e,i){"use strict";var n=i("ed38"),o=i.n(n);o.a},d4b29:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={pageMeta:i("7854").default,uniCountDown:i("e12a").default,nsEmpty:i("52a6").default,nsLogin:i("2910").default,loadingCover:i("c003").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",[t.storeToken?i("v-uni-view",{staticClass:"my_spell_category"},t._l(t.pintuanStatusList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"category-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.categoryChange(e.id)}}},[i("v-uni-view",{staticClass:"item-con",class:e.id==t.pintuanStatus?"active color-base-text color-base-bg-before":""},[t._v(t._s(e.name))])],1)})),1):t._e(),t.storeToken?i("mescroll-uni",{ref:"mescroll",attrs:{top:"90",size:10},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getData.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[t._l(t.dataList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"goods-list"},[i("v-uni-view",{staticClass:"list-header"},[i("v-uni-text",{staticClass:"state-time"},[t._v("发起拼单 "+t._s(t.$util.timeStampTurnTime(e.pay_time)))]),1!=e.pintuan_status&&3==t.pintuanStatus?i("v-uni-text",{staticClass:"state-sign",style:{color:t.pintuanState[e.pintuan_status].color}},[t._v(t._s(t.pintuanState[e.pintuan_status].text))]):1==e.pintuan_status&&3==t.pintuanStatus?i("v-uni-text",{staticClass:"state-sign",style:{color:t.pintuanState[e.pintuan_status].color}},[t._v("未抽中发货")]):i("v-uni-text",{staticClass:"state-sign",style:{color:t.pintuanState[e.pintuan_status].color}},[t._v(t._s(t.pintuanState[e.pintuan_status].text))])],1),i("v-uni-view",{staticClass:"list-body"},[i("v-uni-view",{staticClass:"list-body-img",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goPinTuanDetail(e.pintuan_id)}}},[i("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"})},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(n)}}})],1),i("v-uni-view",{staticClass:"shop-content"},[i("v-uni-view",{staticClass:"shop-title"},[t._v(t._s(e.sku_name))]),i("v-uni-view",{staticClass:"pintuan-num"},[t._v(t._s(e.pintuan_num)+"人拼单")]),i("v-uni-view",{staticClass:"status-name"},[i("v-uni-view",{staticClass:"pintuan-price price-style large"},[i("v-uni-text",{staticClass:"pintuan-price-icon price-style small"},[t._v("¥")]),t._v(t._s(parseFloat(e.order_money).toFixed(2).split(".")[0])),i("v-uni-text",{staticClass:"pintuan-price-icon price-style small"},[t._v("."+t._s(parseFloat(e.order_money).toFixed(2).split(".")[1]))])],1),1==e.pintuan_status&&null!=e.m_related_id&&null==e.c_related_id?i("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toReward(e.reward_type,e.m_related_id)}}},[t._v("查看返利")]):t._e(),1==e.pintuan_status&&null!=e.c_related_id&&null==e.m_related_id?i("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toReward(e.reward_type,e.c_related_id)}}},[t._v("查看返利")]):t._e(),1==e.pintuan_status&&null==e.c_related_id&&null==e.m_related_id?i("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toOrderDetail(e.order_id,e.delivery_type)}}},[t._v("查看订单详情")]):t._e()],1)],1)],1),2==e.pintuan_status?i("v-uni-view",{staticClass:"list-footer"},[e.timeMachine?[i("v-uni-view",{staticClass:"list-footer-time"},[i("v-uni-text",[t._v("还剩")]),i("v-uni-text",{staticClass:"color-base-text"},[t._v(t._s(e.pintuan_num-e.pintuan_count))]),i("v-uni-text",[t._v("人，剩余时间")]),i("v-uni-view",{staticClass:"time-wrap"},[i("uni-count-down",{staticClass:"time",attrs:{day:e.timeMachine.d,hour:e.timeMachine.h,minute:e.timeMachine.i,second:e.timeMachine.s,color:"#909399",splitorColor:"#909399","background-color":"transparent","border-color":"transparent"}})],1)],1),i("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toshare(e.id)}}},[t._v("邀请好友")])]:[i("v-uni-text",[t._v("拼团失败")])]],2):3==e.pintuan_status?i("v-uni-view",{staticClass:"list-footer"},[i("v-uni-view",{staticClass:"picture-box"},t._l(e.member_list,(function(e,o){return o<4?i("v-uni-view",{key:o,staticClass:"img-box"},[e.member_img?i("v-uni-image",{attrs:{src:t.$util.img(e.member_img),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.memberImageError(n,o)}}}):i("v-uni-image",{attrs:{src:t.$util.img(t.$util.getDefaultImage().head),mode:"aspectFill"}})],1):t._e()})),1),i("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toOrderDetail(e.order_id,e.delivery_type)}}},[t._v("查看订单详情")])],1):t._e()],1)})),0==t.dataList.length?i("v-uni-view",{staticStyle:{"padding-top":"0"}},[i("ns-empty",{attrs:{isIndex:!0,emptyBtn:{url:"/pages_promotion/pinfan/list",text:"去逛逛"},text:"暂无拼团返利订单"}})],1):t._e()],2)],2):t._e(),i("ns-login",{ref:"login"}),i("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},e12a:function(t,e,i){"use strict";i.r(e);var n=i("00ba"),o=i("ea5a");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("11ac");var s=i("828b"),r=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,"45a7f114",null,!1,n["a"],void 0);e["default"]=r.exports},ea5a:function(t,e,i){"use strict";i.r(e);var n=i("f9fd"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},ed38:function(t,e,i){var n=i("1b07");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("6d9a4363",n,!0,{sourceMap:!1,shadowMode:!1})},f27a:function(t,e,i){var n=i("9fe8");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("1240618e",n,!0,{sourceMap:!1,shadowMode:!1})},f48d:function(t,e,i){"use strict";i.r(e);var n=i("cc1b"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},f9fd:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:[Number,String],default:0},hour:{type:[Number,String],default:0},minute:{type:[Number,String],default:0},second:{type:[Number,String],default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},mounted:function(t){var e=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},watch:{day:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},hour:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},minute:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},second:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,e,i,n){return t=Number(t),e=Number(e),i=Number(i),n=Number(n),60*t*60*24+60*e*60+60*i+n},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,i=0,n=0,o=0;t>0?(e=Math.floor(t/86400),i=Math.floor(t/3600)-24*e,n=Math.floor(t/60)-24*e*60-60*i,o=Math.floor(t)-24*e*60*60-60*i*60-60*n):this.timeUp(),e<10&&(e="0"+e),i<10&&(i="0"+i),n<10&&(n="0"+n),o<10&&(o="0"+o),this.d=e,this.h=i,this.i=n,this.s=o}}};e.default=n}}]);