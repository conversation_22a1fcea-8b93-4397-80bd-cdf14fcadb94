(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-fenxiao-order"],{"010d":function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"c",(function(){return r})),o.d(e,"a",(function(){return i}));var i={pageMeta:o("7854").default,nsEmpty:o("52a6").default,nsLogin:o("2910").default,loadingCover:o("c003").default},a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",[o("page-meta",{attrs:{"page-style":t.themeColor}}),o("v-uni-view",[o("v-uni-view",{staticClass:"withdraw-cate"},[t._l(t.category,(function(e,i){return[o("v-uni-view",{key:i+"_0",staticClass:"cate-li",class:{"active color-base-text color-base-bg-before":t.selectId==e.id},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.selectCate(e.id)}}},[t._v(t._s(e.name))])]}))],2),t.storeToken?o("mescroll-uni",{ref:"mescroll",staticClass:"member-point",attrs:{top:"90",size:8},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getData.apply(void 0,arguments)}}},[o("v-uni-view",{staticClass:"goods_list",attrs:{slot:"list"},slot:"list"},[o("v-uni-view",{staticClass:"order-list"},t._l(t.orderList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"order-item",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.toDetail(e.fenxiao_order_id)}}},[o("v-uni-view",{staticClass:"order-header"},[o("v-uni-text",{staticClass:"site-name font-size-base"},[t._v(t._s(e.order_no))]),1==e.is_refund?o("v-uni-text",{staticClass:"status-name color-base-text"},[t._v("已退款")]):1==e.is_settlement?o("v-uni-text",{staticClass:"status-name color-text-green"},[t._v("已结算")]):o("v-uni-text",{staticClass:"status-name color-text-orange"},[t._v("待结算")])],1),o("v-uni-view",{staticClass:"order-body"},[o("v-uni-view",{staticClass:"goods-wrap"},[o("v-uni-view",{staticClass:"goods-img"},[o("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"aspectFill","lazy-load":!0},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i)}}})],1),o("v-uni-view",{staticClass:"goods-info"},[o("v-uni-view",{staticClass:"top-wrap"},[o("v-uni-view",{staticClass:"goods-name font-size-base"},[t._v(t._s(e.sku_name))]),o("v-uni-view",[o("v-uni-text",{staticClass:"color-tip"},[t._v("返"+t._s(t.fenxiaoWords.account))]),o("v-uni-text",{staticClass:"price-color  price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",{staticClass:"price-color price-style large"},[t._v(t._s(parseFloat(e.commission).toFixed(2).split(".")[0]))]),o("v-uni-text",{staticClass:"price-color price-style small"},[t._v("."+t._s(parseFloat(e.commission).toFixed(2).split(".")[1]))])],1)],1),o("v-uni-view",{staticClass:"goods-sub-section"},[o("v-uni-view",{staticClass:"goods-price"},[o("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",{staticClass:"price-color price-style large"},[t._v(t._s(parseFloat(e.price).toFixed(2).split(".")[0]))]),o("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(e.price).toFixed(2).split(".")[1]))])],1),o("v-uni-view",[o("v-uni-text",[o("v-uni-text",{staticClass:"iconfont icon-close"}),t._v(t._s(e.num))],1)],1)],1)],1)],1)],1),o("v-uni-view",{staticClass:"order-footer"},[o("v-uni-view",{staticClass:"order-base-info active"},[o("v-uni-view",{staticClass:"order-type "},[o("v-uni-text",{staticClass:"color-tip"},[t._v(t._s(t.$util.timeStampTurnTime(e.create_time)))])],1),o("v-uni-view",{staticClass:"total"},[o("v-uni-text",[t._v("合计：")]),o("v-uni-text",{staticClass:"price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",{staticClass:"price-color font-size-toolbar"},[t._v(t._s(parseFloat(e.real_goods_money).toFixed(2).split(".")[0]))]),o("v-uni-text",{staticClass:"price-color"},[t._v("."+t._s(parseFloat(e.real_goods_money).toFixed(2).split(".")[1]))])],1)],1)],1)],1)})),1),o("v-uni-view",{staticClass:"cart-empty"},[0==t.selectId&&0==t.orderList.length&&t.emptyShow?o("ns-empty",{attrs:{text:"暂无订单",isIndex:!1}}):t._e(),1==t.selectId&&0==t.orderList.length&&t.emptyShow?o("ns-empty",{attrs:{text:"暂无待结算订单",isIndex:!1}}):t._e(),2==t.selectId&&0==t.orderList.length&&t.emptyShow?o("ns-empty",{attrs:{text:"暂无已结算订单",isIndex:!1}}):t._e(),3==t.selectId&&0==t.orderList.length&&t.emptyShow?o("ns-empty",{attrs:{text:"暂无已退款订单",isIndex:!1}}):t._e()],1)],1)],1):t._e(),o("ns-login",{ref:"login"}),o("loading-cover",{ref:"loadingCover"})],1)],1)},r=[]},"07af":function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("c223");var a=i(o("9fbb")),r={data:function(){return{category:[{id:0,name:"全部",number:2},{id:1,name:"待结算",number:0},{id:2,name:"已结算",number:0},{id:3,name:"已退款",number:0}],selectId:0,orderList:[],emptyShow:!1,fenxiaoId:"",subMemberId:""}},mixins:[a.default],onLoad:function(t){void 0!=t.type&&(this.selectId=t.type)},onShow:function(){var t=this;setTimeout((function(){t.addonIsExist.fenxiao||(t.$util.showToast({title:"商家未开启分销",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),this.fenxiaoWords&&this.fenxiaoWords.concept&&this.$langConfig.title(this.fenxiaoWords.concept+"订单"),this.storeToken||this.$nextTick((function(){t.$refs.login.open("/pages_promotion/fenxiao/order")}))},methods:{getData:function(t){var e=this;this.emptyShow=!1,1==t.num&&(this.orderList=[]),this.$api.sendRequest({url:"/fenxiao/api/order/page",data:{page:t.num,page_size:t.size,is_settlement:this.selectId},success:function(o){e.emptyShow=!0;var i=[];o.message;0==o.code&&o.data&&o.data.list?i=o.data.list:e.$util.showToast({title:o.message}),t.endSuccess(i.length),1==t.num&&(e.orderList=[]),e.orderList=e.orderList.concat(i),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(o){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},selectCate:function(t){this.selectId=t,this.$refs.mescroll.refresh()},toDetail:function(t){this.$util.redirectTo("/pages_promotion/fenxiao/order_detail",{id:t})},imageError:function(t){this.orderList[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}},watch:{storeToken:function(t,e){t&&this.$refs.mescroll.refresh()}}};e.default=r},"1a37":function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-27e9da50] .fixed{position:relative;top:0}[data-v-27e9da50] .empty{margin-top:0!important}.cart-empty[data-v-27e9da50]{padding-top:%?208?%!important}.color-text-green[data-v-27e9da50]{color:#11bd64}.color-text-orange[data-v-27e9da50]{color:#ffa044}.withdraw-cate[data-v-27e9da50]{width:100%;height:%?90?%;display:flex;box-sizing:border-box;background:#fff}.withdraw-cate .cate-li[data-v-27e9da50]{flex:1;display:flex;justify-content:center;align-items:center;height:100%;font-size:%?30?%}.withdraw-cate .cate-li.active[data-v-27e9da50]{box-sizing:border-box;position:relative}.withdraw-cate .cate-li.active[data-v-27e9da50]::after{position:absolute;bottom:0;left:0;content:"";width:100%;height:%?4?%}.goods_list[data-v-27e9da50]{width:100%;height:100%;padding:0 %?24?%;box-sizing:border-box;margin-top:%?18?%}.goods_list .order-item[data-v-27e9da50]{padding:%?30?%;box-sizing:border-box;border-radius:%?10?%;background:#fff;position:relative;margin-bottom:%?18?%}.goods_list .order-item .order-header[data-v-27e9da50]{display:flex;align-items:center;position:relative;padding-bottom:%?24?%;line-height:1;font-size:%?22?%}.goods_list .order-item .order-header .icon-dianpu[data-v-27e9da50]{display:inline-block;line-height:1;margin-right:%?12?%}.goods_list .order-item .order-header .status-name[data-v-27e9da50]{flex:1;text-align:right}.goods_list .order-item .order-body[data-v-27e9da50]{margin-bottom:%?24?%}.goods_list .order-item .order-body .goods-wrap[data-v-27e9da50]{display:flex;position:relative}.goods_list .order-item .order-body .goods-wrap[data-v-27e9da50]:last-of-type{margin-bottom:0}.goods_list .order-item .order-body .goods-wrap .goods-img[data-v-27e9da50]{width:%?170?%;height:%?170?%;padding:%?20?% 0 0 0;margin-right:%?5?%}.goods_list .order-item .order-body .goods-wrap .goods-img uni-image[data-v-27e9da50]{width:100%;height:100%;border-radius:%?10?%}.goods_list .order-item .order-body .goods-wrap .goods-info[data-v-27e9da50]{flex:1;position:relative;padding:%?20?% 0 0 0;max-width:calc(100% - %?200?%);margin-left:%?18?%;display:flex;flex-direction:column}.goods_list .order-item .order-body .goods-wrap .goods-info .top-wrap[data-v-27e9da50]{flex:1}.goods_list .order-item .order-body .goods-wrap .goods-info .goods-name[data-v-27e9da50]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?22?%;color:#000}.goods_list .order-item .order-body .goods-wrap .goods-info .goods-sub-section[data-v-27e9da50]{width:100%;line-height:1.5;display:flex;align-items:center;font-size:%?22?%;margin-top:%?20?%}.goods_list .order-item .order-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-27e9da50]{font-size:%?24?%;margin-right:%?2?%}.goods_list .order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-27e9da50]{flex:1;line-height:1}.goods_list .order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-27e9da50]:last-of-type{text-align:right}.goods_list .order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view:last-of-type .iconfont[data-v-27e9da50]{line-height:1;font-size:%?24?%}.goods_list .order-item .order-body .goods-wrap .goods-info .order-time[data-v-27e9da50]{margin-top:%?12?%;font-size:%?22?%;color:#838383}.goods_list .order-item .order-body .goods-wrap .goods-info .order-time .goods-price[data-v-27e9da50]{font-size:%?22?%;float:right;color:#000}.goods_list .order-item .order-footer[data-v-27e9da50]{padding-top:%?24?%}.goods_list .order-item .order-footer .order-base-info[data-v-27e9da50]{display:flex}.goods_list .order-item .order-footer .order-base-info .total[data-v-27e9da50]{text-align:right;padding-top:%?20?%;flex:1;font-size:%?22?%}.goods_list .order-item .order-footer .order-base-info .total > uni-text[data-v-27e9da50]{line-height:1}.goods_list .order-item .order-footer .order-base-info .order-type[data-v-27e9da50]{font-size:%?22?%}.goods_list .order-item .order-footer .order-base-info .order-type > uni-text[data-v-27e9da50]{line-height:1}.goods_list .order-item .order-footer .order-base-info.active .total[data-v-27e9da50]{padding-top:0}.goods_list .order-item .order-footer .order-base-info.active .order-type[data-v-27e9da50]{padding-top:0}.goods_list .order-item[data-v-27e9da50]:last-child{border:none}.price-color[data-v-27e9da50]{color:var(--price-color)}',""]),t.exports=e},7854:function(t,e,o){"use strict";o.r(e);var i=o("8ba8"),a=o("f48d");for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(r);var s=o("828b"),n=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=n.exports},"857f":function(t,e,o){var i=o("1a37");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=o("967d").default;a("764497f8",i,!0,{sourceMap:!1,shadowMode:!1})},"8ba8":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},a=[]},"9fbb":function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{fenxiaoWords:{}}},methods:{getFenxiaoWrods:function(){var t=this;this.$api.sendRequest({url:"/fenxiao/api/config/words",success:function(e){e.code>=0&&e.data&&(t.fenxiaoWords=e.data,uni.setStorageSync("fenxiaoWords",e.data))}})}},onShow:function(){uni.getStorageSync("fenxiaoWords")&&(this.fenxiaoWords=uni.getStorageSync("fenxiaoWords")),this.getFenxiaoWrods()}};e.default=i},b73a:function(t,e,o){"use strict";o.r(e);var i=o("07af"),a=o.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},c216:function(t,e,o){"use strict";o.r(e);var i=o("010d"),a=o("b73a");for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(r);o("f470");var s=o("828b"),n=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"27e9da50",null,!1,i["a"],void 0);e["default"]=n.exports},cc1b:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("5ef2"),o("64aa"),o("5c47"),o("a1c1"),o("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},a={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var o=function o(a){a.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",o),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",o)}})}}}};e.default=a},f470:function(t,e,o){"use strict";var i=o("857f"),a=o.n(i);a.a},f48d:function(t,e,o){"use strict";o.r(e);var i=o("cc1b"),a=o.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a}}]);