(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-point-order_list"],{"201d":function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-76b4961b] .fixed{position:relative;top:0}[data-v-76b4961b] .empty{padding-top:0!important}.order-container[data-v-76b4961b]{width:100vw;height:100vh}.align-right[data-v-76b4961b]{text-align:right}.order-item[data-v-76b4961b]{margin:%?20?% %?24?%;border-radius:%?12?%;background:#fff;position:relative}.order-item .order-header[data-v-76b4961b]{display:flex;align-items:center;position:relative;padding:%?20?% %?24?% %?26?% %?24?%}.order-item .order-header.waitpay[data-v-76b4961b]{padding-left:%?70?%}.order-item .order-header.waitpay .iconyuan_checked[data-v-76b4961b],\r\n.order-item .order-header.waitpay .iconyuan_checkbox[data-v-76b4961b]{font-size:%?32?%;position:absolute;top:48%;left:%?20?%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.order-item .order-header.waitpay .iconyuan_checkbox[data-v-76b4961b]{color:#909399}.order-item .order-header .icondianpu[data-v-76b4961b]{display:inline-block;line-height:1;margin-right:%?12?%;font-size:%?28?%}.order-item .order-header .order-no[data-v-76b4961b]{font-size:%?26?%}.order-item .order-header .status-name[data-v-76b4961b]{flex:1;text-align:right;font-size:%?26?%;font-weight:600}.order-item .order-body .goods-wrap[data-v-76b4961b]{display:flex;position:relative;padding:0 %?24?% %?30?% %?24?%}.order-item .order-body .goods-wrap[data-v-76b4961b]:last-of-type{margin-bottom:0}.order-item .order-body .goods-wrap .goods-img[data-v-76b4961b]{width:%?160?%;height:%?160?%;margin-right:%?20?%}.order-item .order-body .goods-wrap .goods-img uni-image[data-v-76b4961b]{width:100%;height:100%;border-radius:%?10?%}.order-item .order-body .goods-wrap .goods-info[data-v-76b4961b]{flex:1;position:relative;max-width:calc(100% - %?180?%);display:flex;flex-direction:column}.order-item .order-body .goods-wrap .goods-info .pro-info[data-v-76b4961b]{flex:1}.order-item .order-body .goods-wrap .goods-info .goods-name[data-v-76b4961b]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;color:#303133}.order-item .order-body .goods-wrap .goods-info .goods-sub-section[data-v-76b4961b]{width:100%;line-height:1.3;display:flex;margin-top:%?14?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-76b4961b]{font-size:%?24?%;color:var(--price-color);flex:1;font-weight:700}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-num[data-v-76b4961b]{font-size:%?24?%;color:#909399;flex:1;text-align:right;line-height:1}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-num .iconfont[data-v-76b4961b]{font-size:%?24?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .goods-type[data-v-76b4961b]{font-size:%?24?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-76b4961b]{font-size:%?24?%;margin-right:%?2?%}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-76b4961b]{flex:1;line-height:1.3;display:flex;flex-direction:column}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-76b4961b]:last-of-type{text-align:right}.order-item .order-body .goods-wrap .goods-info .goods-sub-section uni-view:last-of-type .iconfont[data-v-76b4961b]{line-height:1;font-size:%?24?%}.order-item .order-body .goods-wrap .goods-info .goods-action[data-v-76b4961b]{text-align:right}.order-item .order-body .goods-wrap .goods-info .goods-action .action-btn[data-v-76b4961b]{line-height:1;padding:%?14?% %?20?%;color:#303133;display:inline-block;border-radius:%?10?%;background:#fff;border:%?2?% solid #999;font-size:%?24?%;margin-left:%?10?%}.order-item .order-body .multi-order-goods[data-v-76b4961b]{width:calc(100vw - %?96?%);white-space:nowrap;margin:0 %?24?% %?30?% %?24?%!important;position:relative}.order-item .order-body .multi-order-goods .scroll-view[data-v-76b4961b]{width:100%}.order-item .order-body .multi-order-goods .goods-wrap[data-v-76b4961b]{padding:0}.order-item .order-body .multi-order-goods .goods-img[data-v-76b4961b]{min-width:%?160?%}.order-item .order-body .multi-order-goods .shade[data-v-76b4961b]{position:absolute;z-index:5;height:100%;width:%?44?%;right:0;top:0}.order-item .order-body .multi-order-goods .shade uni-image[data-v-76b4961b]{width:100%;height:100%}.order-item .order-footer .order-base-info .total[data-v-76b4961b]{padding:%?20?%;font-size:%?24?%;background:hsla(0,0%,97.3%,.5);display:flex;margin:0 %?24?%}.order-item .order-footer .order-base-info .total > uni-text[data-v-76b4961b]{flex:1;line-height:1;margin-left:%?10?%}.order-item .order-footer .order-base-info .order-type[data-v-76b4961b]{padding-top:%?20?%;flex:0.5}.order-item .order-footer .order-base-info .order-type > uni-text[data-v-76b4961b]{line-height:1}.order-item .order-footer .order-action[data-v-76b4961b]{text-align:right;padding:%?30?% %?24?%;position:relative}.order-item .order-footer .order-action .order-time[data-v-76b4961b]{position:absolute;top:%?35?%;left:%?30?%;display:flex;align-items:center;font-size:10px;color:#b5b6b9}.order-item .order-footer .order-action .order-time uni-image[data-v-76b4961b]{width:%?26?%;height:%?26?%;margin-right:%?6?%}.order-item .order-footer .order-action .action-btn[data-v-76b4961b]{line-height:1;padding:%?20?% %?26?%;color:#333;display:inline-block;border-radius:%?10?%;background:#fff;border:%?2?% solid #999;font-size:%?24?%;margin-left:%?10?%}.empty[data-v-76b4961b]{padding-top:%?200?%;text-align:center}.empty .empty-image[data-v-76b4961b]{width:%?180?%;height:%?180?%}.order-nav[data-v-76b4961b]{width:100vw;height:%?80?%;flex-direction:row;white-space:nowrap;background:#fff;display:flex;position:fixed;left:0;z-index:998;justify-content:space-around;border-radius:0 0 %?24?% %?24?%}.order-nav .uni-tab-item[data-v-76b4961b]{width:%?120?%;text-align:center}.order-nav .uni-tab-item-title[data-v-76b4961b]{display:inline-block;height:%?80?%;line-height:%?80?%;border-bottom:1px solid #fff;flex-wrap:nowrap;white-space:nowrap;text-align:center;font-size:%?30?%;position:relative}.order-nav .uni-tab-item-title-active[data-v-76b4961b]::after{content:" ";display:block;position:absolute;left:0;bottom:0;width:100%;height:%?6?%;background:linear-gradient(270deg,var(--base-color-light-9),var(--base-color))}.order-nav[data-v-76b4961b] ::-webkit-scrollbar{width:0;height:0;color:transparent}',""]),t.exports=e},"449f":function(t,e,o){"use strict";o.r(e);var i=o("d778"),r=o("8479");for(var a in r)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return r[t]}))}(a);o("aea7");var n=o("828b"),s=Object(n["a"])(r["default"],i["b"],i["c"],!1,null,"76b4961b",null,!1,i["a"],void 0);e["default"]=s.exports},7854:function(t,e,o){"use strict";o.r(e);var i=o("8ba8"),r=o("f48d");for(var a in r)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return r[t]}))}(a);var n=o("828b"),s=Object(n["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},7968:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("c223"),o("e838");var i={data:function(){return{orderList:[],showEmpty:!1,outTradeNo:"",payMoney:0,statusList:[{status:"all",id:"all",name:"全部"},{status:0,id:"pay",name:"待支付"},{status:1,id:"complete",name:"已完成"}],orderStatus:"all"}},onLoad:function(){var t=this;setTimeout((function(){t.addonIsExist.pointexchange||(t.$util.showToast({title:"商家未开启积分商城",mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages/index/index",{},"redirectTo")}),2e3))}),1e3),this.storeToken||this.$nextTick((function(){t.$refs.login.open("/pages_promotion/point/order_list")}))},onShow:function(){this.$refs.mescroll&&this.$refs.mescroll.refresh()},watch:{storeToken:function(t,e){t&&this.$refs.mescroll.refresh()}},methods:{ontabtap:function(t){var e=t.target.dataset.current||t.currentTarget.dataset.current;this.orderStatus=this.statusList[e].status,this.$refs.loadingCover.show(),this.$refs.mescroll.refresh()},getListData:function(t){var e=this;this.showEmpty=!1,this.$api.sendRequest({url:"/pointexchange/api/order/page",data:{page:t.num,page_size:t.size,order_status:this.orderStatus},success:function(o){e.showEmpty=!0;var i=[],r=o.message;0==o.code&&o.data?i=o.data.list:e.$util.showToast({title:r}),t.endSuccess(i.length),1==t.num&&(e.orderList=[]),e.orderList=e.orderList.concat(i),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(o){t.endErr(),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}})},orderClose:function(t,e){var o=this;uni.showModal({title:"提示",content:"确定关闭此次兑换？",success:function(i){i.confirm&&o.$api.sendRequest({url:"/pointexchange/api/order/close",data:{order_id:t},success:function(t){t.code>=0&&(o.orderList[e].order_status=-1,o.$util.showToast({title:"关闭成功"}),o.$forceUpdate())}})}})},openChoosePayment:function(t,e){this.outTradeNo=t,this.payMoney=parseFloat(e),this.$refs.choosePaymentPopup.open()},orderPay:function(){this.$refs.choosePaymentPopup.getPayInfo(this.outTradeNo)},detail:function(t){1==t.type&&t.relate_order_id?this.$util.redirectTo("/pages/order/detail",{order_id:t.relate_order_id}):this.$util.redirectTo("/pages/order/detail_point",{order_id:t.order_id})},imageError:function(t){this.orderList[t].exchange_image=this.$util.getDefaultImage().goods,this.$forceUpdate()}}};e.default=i},"7f9b":function(t,e,o){var i=o("201d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=o("967d").default;r("5b678c08",i,!0,{sourceMap:!1,shadowMode:!1})},8479:function(t,e,o){"use strict";o.r(e);var i=o("7968"),r=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},"8ba8":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return r})),o.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},r=[]},aea7:function(t,e,o){"use strict";var i=o("7f9b"),r=o.n(i);r.a},cc1b:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("5ef2"),o("64aa"),o("5c47"),o("a1c1"),o("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},r={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var o=function o(r){r.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",o),t.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",o)}})}}}};e.default=r},d778:function(t,e,o){"use strict";o.d(e,"b",(function(){return r})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){return i}));var i={pageMeta:o("7854").default,nsEmpty:o("52a6").default,nsPayment:o("7aec").default,loadingCover:o("c003").default,nsLogin:o("2910").default},r=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",[o("page-meta",{attrs:{"page-style":t.themeColor}}),o("v-uni-view",{staticClass:"order-container"},[t.storeToken?o("v-uni-view",{staticClass:"order-nav"},t._l(t.statusList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"uni-tab-item",attrs:{id:e.id,"data-current":i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.ontabtap.apply(void 0,arguments)}}},[o("v-uni-text",{staticClass:"uni-tab-item-title",class:e.status==t.orderStatus?"uni-tab-item-title-active color-base-text":""},[t._v(t._s(e.name))])],1)})),1):t._e(),o("mescroll-uni",{ref:"mescroll",attrs:{top:"80rpx"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getListData.apply(void 0,arguments)}}},[o("template",{attrs:{slot:"list"},slot:"list"},[t.orderList.length?t._l(t.orderList,(function(e,i){return o("v-uni-view",{key:i,staticClass:"order-item",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.detail(e)}}},[o("v-uni-view",{staticClass:"order-header"},[o("v-uni-text",{staticClass:"order-no"},[t._v("订单号："+t._s(e.order_no))]),o("v-uni-text",{staticClass:"status-name"},[t._v(t._s(0==e.order_status?"待支付":1==e.order_status?"已完成":-1==e.order_status?"已关闭":""))])],1),o("v-uni-view",{staticClass:"order-body"},[o("v-uni-view",{staticClass:"goods-wrap"},[o("v-uni-view",{staticClass:"goods-img"},[2==e.type?[o("v-uni-image",{attrs:{src:t.$util.img(e.exchange_image)?t.$util.img(e.exchange_image):t.$util.img("public/uniapp/point/coupon.png"),mode:"aspectFill","lazy-load":!0},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i)}}})]:3==e.type?[o("v-uni-image",{attrs:{src:t.$util.img(e.exchange_image)?t.$util.img(e.exchange_image):t.$util.img("public/uniapp/point/hongbao.png"),mode:"aspectFill","lazy-load":!0},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i)}}})]:[o("v-uni-image",{attrs:{src:t.$util.img(e.exchange_image),mode:"aspectFill","lazy-load":!0},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError(i)}}})]],2),o("v-uni-view",{staticClass:"goods-info"},[o("v-uni-view",{staticClass:"pro-info"},[o("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.exchange_name))]),o("v-uni-view",{staticClass:"goods-sub-section"},[o("v-uni-text",{staticClass:"goods-price"},[o("v-uni-text",{staticClass:"price-style large"},[t._v(t._s(e.point))]),o("v-uni-text",{staticClass:"unit price-style small"},[t._v("积分")]),e.price>0?[t._v("+"),o("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),o("v-uni-text",{staticClass:"price-style large"},[t._v(t._s(parseFloat(e.price).toFixed(2).split(".")[0]))]),o("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(e.price).toFixed(2).split(".")[1]))])]:t._e()],2),o("v-uni-text",{staticClass:"goods-num"},[o("v-uni-text",{staticClass:"iconfont icon-close"}),t._v(t._s(e.num))],1)],1)],1)],1)],1)],1),o("v-uni-view",{staticClass:"order-footer"},[0==e.order_status&&1==e.type?o("v-uni-view",{staticClass:"order-action"},[o("v-uni-view",{staticClass:"order-box-btn font-size-tag",on:{click:function(o){o.stopPropagation(),arguments[0]=o=t.$handleEvent(o),t.orderClose(e.order_id,i)}}},[t._v("关闭")]),o("v-uni-view",{staticClass:"order-box-btn color-base-bg color-base-border",on:{click:function(o){o.stopPropagation(),arguments[0]=o=t.$handleEvent(o),t.openChoosePayment(e.out_trade_no,e.price)}}},[t._v("支付")])],1):o("v-uni-view",{staticClass:"order-action"},[o("v-uni-view",{staticClass:"order-box-btn font-size-tag"},[t._v("查看详情")])],1)],1)],1)})):t._e(),t.showEmpty&&!t.orderList.length?[o("v-uni-view",{staticClass:"cart-empty"},[o("ns-empty",{attrs:{isIndex:!0,emptyBtn:{url:"/pages_promotion/point/list",text:"去逛逛"},text:"暂无积分兑换订单"}})],1)]:t._e()],2)],2),o("ns-payment",{ref:"choosePaymentPopup",attrs:{payMoney:t.payMoney},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.orderPay.apply(void 0,arguments)}}}),o("loading-cover",{ref:"loadingCover"}),o("ns-login",{ref:"login"})],1)],1)},a=[]},f48d:function(t,e,o){"use strict";o.r(e);var i=o("cc1b"),r=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a}}]);