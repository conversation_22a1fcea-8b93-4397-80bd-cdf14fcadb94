(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-giftcard-member_give_info"],{"38b1":function(t,e,a){var o=a("a89f");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=a("967d").default;i("1a2638aa",o,!0,{sourceMap:!1,shadowMode:!1})},"40ad":function(t,e,a){"use strict";a.r(e);var o=a("ad02"),i=a.n(o);for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);e["default"]=i.a},4365:function(t,e,a){"use strict";var o=a("38b1"),i=a.n(o);i.a},"4ec3":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return o}));var o={pageMeta:a("7854").default,nsLogin:a("2910").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),t.cardInfo?a("v-uni-view",{staticClass:"page"},[a("v-uni-view",{staticClass:"bg-box",style:{backgroundImage:"url("+t.$util.img("public/uniapp/giftcard/give_bg_1.png")+")"}},[a("v-uni-view",{staticClass:"card-img"},[a("v-uni-image",{attrs:{src:t.$util.img(t.cardimg()),mode:"widthFix"}})],1),a("v-uni-view",{staticClass:"card-box"},[a("v-uni-view",{staticClass:"member-info"},[a("v-uni-view",{staticClass:"headimg"},[a("v-uni-image",{attrs:{src:t.$util.img(t.cardInfo.to_member_headimg?t.cardInfo.to_member_headimg:t.$util.getDefaultImage().head),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.cardInfo.to_member_headimg=t.$util.getDefaultImage().head}}})],1),a("v-uni-view",{staticClass:"member"},[a("v-uni-text",{staticClass:"member-name"},[t._v(t._s(t.cardInfo.to_member_nickname))])],1)],1),a("v-uni-view",{staticClass:"desc"},[a("v-uni-view",{staticClass:"title"},[t._v("领取了您的「"+t._s(t.cardInfo.card_name)+"」")]),a("v-uni-view",{staticClass:"content"},[t._v(t._s(t.cardInfo.blessing))])],1)],1)],1),a("v-uni-image",{staticClass:"bg-img",attrs:{src:t.$util.img("public/uniapp/giftcard/give_bg_2.png"),mode:"widthFix"}}),a("v-uni-button",{staticClass:"btn",attrs:{type:"primary"}},[t._v("已领取")]),a("ns-login",{ref:"login"})],1):t._e()],1)},n=[]},7854:function(t,e,a){"use strict";a.r(e);var o=a("8ba8"),i=a("f48d");for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);var r=a("828b"),d=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=d.exports},"8ba8":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},i=[]},a89f:function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.page[data-v-63aa7068]{background:#fff;min-height:100vh;box-sizing:border-box}.bg-box[data-v-63aa7068]{background-color:#f6f9ff;background-repeat:no-repeat;background-size:100%;padding:%?30?%}.bg-img[data-v-63aa7068]{background-color:#f6f9ff;width:100%;max-height:%?100?%}.member-box[data-v-63aa7068]{display:flex;justify-content:center;align-items:center;margin-top:%?20?%}.member-box uni-image[data-v-63aa7068]{width:%?44?%;height:%?44?%;border-radius:%?6?%}.member-box uni-view[data-v-63aa7068]{font-size:%?30?%;margin-left:%?10?%}.card-status[data-v-63aa7068]{margin:%?15?% auto;font-size:%?36?%;text-align:center;display:flex;align-items:center;justify-content:center}.card-status uni-image[data-v-63aa7068]{width:%?44?%;height:%?44?%;margin-right:%?15?%}.card-img[data-v-63aa7068]{margin:%?20?% auto;width:96%;overflow:hidden}.card-img uni-image[data-v-63aa7068]{width:100%;border-radius:%?18?%;margin:0}.card-title[data-v-63aa7068]{font-size:%?30?%;color:#444;display:flex;justify-content:center}.card-title > uni-text[data-v-63aa7068]{color:var(--price-color)}.card-box[data-v-63aa7068]{display:flex;padding-top:%?40?%;margin-top:%?40?%;flex-direction:column}.card-box .member-info[data-v-63aa7068]{display:flex;flex-direction:column;align-items:center}.card-box .member-info .headimg[data-v-63aa7068]{width:%?110?%;height:%?110?%;display:flex;align-items:center;justify-content:center}.card-box .member-info .headimg uni-image[data-v-63aa7068]{width:100%;max-height:%?110?%;border-radius:50%;border:%?2?% solid #fff}.card-box .member-info .member[data-v-63aa7068]{width:100%;overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;justify-content:center;text-align:center;margin-top:%?10?%}.card-box .member-info .member .member-name[data-v-63aa7068]{font-size:%?32?%;color:#666}.card-box .desc[data-v-63aa7068]{margin-top:%?20?%;display:flex;flex-direction:column;justify-content:center}.card-box .desc .title[data-v-63aa7068]{font-size:%?30?%;font-weight:700;text-align:center}.card-box .desc .content[data-v-63aa7068]{text-align:center;font-size:%?30?%;color:#888}.goods-list[data-v-63aa7068]{border-top:%?0?% solid #f0f0f0;padding:%?30?% 0;display:flex}.goods-list .goods-left[data-v-63aa7068]{display:flex;width:calc(100% - %?108?%);overflow:hidden;white-space:nowrap;position:relative;align-items:center}.goods-list .goods-left uni-image[data-v-63aa7068]{width:%?108?%;max-height:%?108?%;margin-right:%?22?%;flex-shrink:0;border-radius:%?16?%}.goods-list .goods-left[data-v-63aa7068]:after{content:" ";box-shadow:%?-4?% 0 %?24?% rgba(0,0,0,.8);width:%?1?%;height:%?80?%;right:%?-1?%;top:%?14?%;position:absolute;background:hsla(0,0%,100%,0)}.goods-list .goods-more[data-v-63aa7068]{width:%?108?%;height:%?108?%;display:flex;align-items:center;justify-content:center;font-size:%?26?%;position:relative}.goods-list .goods-more uni-text[data-v-63aa7068]{font-size:%?28?%;line-height:1}.goods-list.goodsOpen[data-v-63aa7068]{flex-direction:column;position:relative}.goods-list.goodsOpen .btn[data-v-63aa7068]{position:absolute;right:%?20?%;top:%?50?%;font-size:%?26?%;display:flex;align-items:baseline;background-color:var(--giftcard-promotion-color)}.goods-list.goodsOpen .btn uni-text[data-v-63aa7068]{line-height:1.1;font-size:%?24?%;margin-left:%?8?%}.goods-list .goods-item[data-v-63aa7068]{display:flex;margin-bottom:%?20?%}.goods-list .goods-item .goods-image[data-v-63aa7068]{width:%?108?%;height:%?108?%;overflow:hidden;border-radius:%?18?%}.goods-list .goods-item .goods-image uni-image[data-v-63aa7068]{width:%?108?%;height:%?108?%;max-height:%?108?%}.goods-list .goods-item .goods-info[data-v-63aa7068]{width:calc(100% - %?268?%)}.goods-list .goods-item .goods-info .goods-name[data-v-63aa7068]{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;line-height:1.5;font-size:%?28?%}.goods-list .goods-item .goods-info .goods-num[data-v-63aa7068]{margin-top:%?20?%}.goods-list .goods-item .goods-info .goods-num uni-text[data-v-63aa7068]{color:#666;font-size:%?24?%}.goods-list .goods-item .goods-info .goods-num uni-text[data-v-63aa7068]:last-child{margin-left:%?50?%}.btn[data-v-63aa7068]{width:50%;margin:%?40?% auto;background-color:#f6f6f6!important;color:#999!important;font-weight:700}.card-popup-layer[data-v-63aa7068]{width:%?500?%;height:%?400?%;position:relative;padding:%?20?%;box-sizing:border-box}.card-popup-layer .head-wrap[data-v-63aa7068]{width:100%;text-align:right}.card-popup-layer .head-wrap uni-text[data-v-63aa7068]{font-size:%?34?%}.card-popup-layer .content[data-v-63aa7068]{display:flex;flex-direction:column;align-items:center;font-size:%?32?%;font-weight:700;padding:%?40?% 0}.card-popup-layer .button-box[data-v-63aa7068]{margin-top:%?10?%}.card-popup-layer .button-box uni-button[data-v-63aa7068]{background-color:var(--giftcard-promotion-color)}',""]),t.exports=e},ad02:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797"),a("5ef2");var o={data:function(){return{memberCardId:0,cardInfo:null,goodsOpen:!1}},onLoad:function(t){var e=this;if(t.source_member&&uni.setStorageSync("source_member",t.source_member),t.member_card_id&&(this.memberCardId=t.member_card_id),t.scene){var a=decodeURIComponent(t.scene);a=a.split("&"),a.length&&a.forEach((function(t){-1!=t.indexOf("member_card_id")&&(e.memberCardId=t.split("-")[1])}))}},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member")),this.getData()},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},methods:{cardimg:function(){return this.cardInfo&&-1!=this.cardInfo.card_cover.indexOf(",")?this.cardInfo.card_cover:this.cardInfo?this.cardInfo.card_cover.split(",")[0]:""},getData:function(){var t=this;this.$api.sendRequest({url:"/giftcard/api/membercard/detail",data:{member_card_id:this.memberCardId},success:function(e){e.code>=0?(t.cardInfo=e.data,t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:e.message,mask:!0,duration:2e3}),setTimeout((function(){t.$util.redirectTo("/pages_promotion/giftcard/member")}),1500))},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})}}};e.default=o},cc1b:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},i={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var a=function a(i){i.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",a),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",a)}})}}}};e.default=i},f48d:function(t,e,a){"use strict";a.r(e);var o=a("cc1b"),i=a.n(o);for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);e["default"]=i.a},ff17:function(t,e,a){"use strict";a.r(e);var o=a("4ec3"),i=a("40ad");for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);a("4365");var r=a("828b"),d=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"63aa7068",null,!1,o["a"],void 0);e["default"]=d.exports}}]);