(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-bale-detail"],{"00ba":function(t,o,i){"use strict";i.d(o,"b",(function(){return e})),i.d(o,"c",(function(){return s})),i.d(o,"a",(function(){}));var e=function(){var t=this,o=t.$createElement,i=t._self._c||o;return i("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?i("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},s=[]},"015d":function(t,o,i){"use strict";i.r(o);var e=i("0f46"),s=i.n(e);for(var a in e)["default"].indexOf(a)<0&&function(t){i.d(o,t,(function(){return e[t]}))}(a);o["default"]=s.a},"04ea":function(t,o,i){"use strict";var e=i("3693"),s=i.n(e);s.a},"0f46":function(t,o,i){"use strict";i("6a54"),Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var e={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};o.default=e},"11ac":function(t,o,i){"use strict";var e=i("9c4f"),s=i.n(e);s.a},"33da":function(t,o,i){"use strict";i("6a54"),Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("dc8a"),i("aa9c"),i("d4b5"),i("e838"),i("5c47"),i("a1c1");var e={data:function(){return{info:null,timeMachine:null,page:1,goodsList:[],cart:{},totalPrice:0,totalNum:0,goodsSkuDetail:null,skuList:[],cartShow:!1,isSub:!1}},onLoad:function(t){var o=this;if(t.id&&(this.id=t.id),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("id")&&(o.id=t.split("-")[1])}))}if(t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(o.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}this.getBaleInfo()},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member"))},methods:{getBaleInfo:function(){var t=this;this.$api.sendRequest({url:"/bale/api/bale/detail",data:{bale_id:this.id},success:function(o){0==o.code&&o.data?(t.info=o.data,1==t.info.status?(t.timeMachine=t.$util.countDown(t.info.end_time-o.timestamp),t.goodsList=t.info.sku_list,t.$refs.loadingCover&&t.$refs.loadingCover.hide()):2==t.info.status&&(t.$util.showToast({title:"该活动已关闭"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))):(t.$util.showToast({title:"未找到活动"}),setTimeout((function(){t.$util.redirectTo("/pages/index/index")}),1500))},fail:function(o){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},imageError:function(t){this.goodsList[t].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},singleSkuPlus:function(t){if(void 0!=this.cart["goods_"+t.goods_id]){var o=this.cart["goods_"+t.goods_id].num;if(o+1>t.stock)return void this.$util.showToast({title:"库存不足"});this.cart["goods_"+t.goods_id].num+=1,this.cart["goods_"+t.goods_id]["sku_"+t.sku_id]?this.cart["goods_"+t.goods_id]["sku_"+t.sku_id].num+=1:(this.cart["goods_"+t.goods_id]["sku_"+t.sku_id]=t,this.cart["goods_"+t.goods_id]["sku_"+t.sku_id].num=1)}else this.cart["goods_"+t.goods_id]={num:1},this.cart["goods_"+t.goods_id]["sku_"+t.sku_id]=t,this.cart["goods_"+t.goods_id]["sku_"+t.sku_id].num=1;this.totalNum+=1,this.cart=Object.assign({},this.cart)},singleSkuReduce:function(t){if(void 0!=this.cart["goods_"+t.goods_id]){var o=this.cart["goods_"+t.goods_id]["sku_"+t.sku_id].num;this.cart["goods_"+t.goods_id].num-=1,this.cart["goods_"+t.goods_id]["sku_"+t.sku_id].num-=1,this.totalNum-=1,o-1==0&&(delete this.cart["goods_"+t.goods_id]["sku_"+t.sku_id],1==Object.keys(this.cart["goods_"+t.goods_id]).length&&delete this.cart["goods_"+t.goods_id]),this.cart=Object.assign({},this.cart)}},manySkuSelect:function(t){var o=this;"string"==typeof t.sku_spec_format&&t.sku_spec_format&&(t.sku_spec_format=JSON.parse(t.sku_spec_format)),"string"==typeof t.goods_sku_spec_format&&t.goods_sku_spec_format&&(t.goods_spec_format=JSON.parse(t.goods_sku_spec_format)),t.bale_id=this.id,t.activity_sku_ids=this.info.sku_ids.split(","),this.goodsSkuDetail=t,setTimeout((function(){o.$refs.goodsSku.show("bale")}),100)},refreshGoodsSkuDetail:function(t){Object.assign(this.goodsSkuDetail,t)},joinCart:function(t){void 0!=this.cart["goods_"+t.goods_id]?void 0!=this.cart["goods_"+t.goods_id]["sku_"+t.sku_id]?(this.cart["goods_"+t.goods_id].num+=t.num-this.cart["goods_"+t.goods_id]["sku_"+t.sku_id].num,this.totalNum+=t.num-this.cart["goods_"+t.goods_id]["sku_"+t.sku_id].num,this.cart["goods_"+t.goods_id]["sku_"+t.sku_id].num=t.num):(this.cart["goods_"+t.goods_id]["sku_"+t.sku_id]=t.detail,this.cart["goods_"+t.goods_id]["sku_"+t.sku_id].num=t.num,this.cart["goods_"+t.goods_id].num+=t.num,this.totalNum+=t.num):(this.cart["goods_"+t.goods_id]={num:t.num},this.cart["goods_"+t.goods_id]["sku_"+t.sku_id]=t.detail,this.cart["goods_"+t.goods_id]["sku_"+t.sku_id].num=t.num,this.totalNum+=t.num),this.cart=Object.assign({},this.cart),this.$refs.goodsSku.hide()},openCartPopup:function(){this.skuList.length&&(this.cartShow=!this.cartShow)},closeCartPopup:function(){this.cartShow=!1},clearCart:function(){this.cart={},this.totalNum=0,this.closeCartPopup()},submit:function(){var t=this;if(this.storeToken){if(this.isSub)return;this.isSub=!0;var o=[];this.skuList.forEach((function(t){o.push({sku_id:t.sku_id,num:t.num})})),uni.setStorage({key:"baleOrderCreateData",data:{bale_id:this.id,sku_list_json:JSON.stringify(o)},success:function(){t.isSub=!1,t.$util.redirectTo("/pages_promotion/bale/payment")}})}else this.$nextTick((function(){t.$refs.login.open("/pages_promotion/bale/detail?id="+t.id)}))}},filters:{moneyFormat:function(t){return parseFloat(t).toFixed(2)},sku:function(t){var o="";return t&&(o=t.sku_name.replace(t.goods_name,"")),o}},watch:{cart:{deep:!0,handler:function(t,o){var i=[],e=1;for(var s in this.cart){var a=this.cart[s];for(var n in a)if(-1==n.indexOf("num")){var r=a[n];r.start=e,r.end=e+r.num,i.push(a[n]),e+=r.num}}this.skuList=i}},totalNum:function(t){if(t>0)if(t%this.info.num==0)this.totalPrice=this.info.price*(t/this.info.num);else{for(var o=Math.floor(t/this.info.num),i=parseFloat(o*this.info.price),e=o*this.info.num,s=0;s<t%this.info.num;s++){e+=1;for(var a=0;a<this.skuList.length;a++){var n=this.skuList[a];if(e>=n.start&&e<n.end){i+=parseFloat(n.price);break}}}this.totalPrice=i}else this.totalPrice=0}},onShareAppMessage:function(){var t=this.$util.getCurrentShareRoute(this.memberInfo?this.memberInfo.member_id:0),o=t.path;return{title:"这些商品"+this.info.price+"元任选"+this.info.num+"件",imageUrl:"",path:o,success:function(t){},fail:function(t){}}}};o.default=e},"33ed":function(t,o,i){var e=i("c86c");o=e(!1),o.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container[data-v-26f768fd]{width:100vw;height:100vh}.activity-head[data-v-26f768fd]{padding:0 %?40?% %?40?%;padding-top:%?40?%;height:%?160?%}.activity-head .activity-text[data-v-26f768fd]{color:#fff;line-height:1}.activity-head .time[data-v-26f768fd]{color:#fff;font-size:%?24?%;margin-top:%?6?%}.activity-head .no-start[data-v-26f768fd]{color:#fff;font-size:%?30?%;margin-top:%?16?%}.goods-wrap[data-v-26f768fd]{background-color:#fff;margin:%?-80?% %?20?% %?40?%;border-radius:%?8?%}.goods-wrap .goods-item[data-v-26f768fd]{padding:%?30?% 0;display:flex;margin:0 %?30?%;border-bottom:1px solid #f8f8f8}.goods-wrap .goods-item[data-v-26f768fd]:last-child{border-bottom:0}.goods-wrap .goods-item .goods-image[data-v-26f768fd]{width:%?200?%;height:%?200?%;overflow:hidden;margin-right:%?20?%;border-radius:%?10?%}.goods-wrap .goods-item .goods-image uni-image[data-v-26f768fd]{width:inherit}.goods-wrap .goods-item .goods-info[data-v-26f768fd]{flex:1;position:relative}.goods-wrap .goods-item .goods-info .name[data-v-26f768fd]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;font-size:%?28?%;line-height:%?36?%;font-weight:600}.goods-wrap .goods-item .goods-info .spec-name[data-v-26f768fd]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;font-size:%?28?%}.goods-wrap .goods-item .goods-info .introduction[data-v-26f768fd]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;font-size:%?28?%;color:#999;line-height:%?28?%;margin-top:%?10?%;height:%?28?%}.goods-wrap .goods-item .goods-bottom[data-v-26f768fd]{display:flex;position:absolute;bottom:0;left:0;width:100%}.goods-wrap .goods-item .goods-bottom > uni-view[data-v-26f768fd]{flex:1}.goods-wrap .goods-item .goods-bottom .price[data-v-26f768fd]{line-height:1;display:flex;align-items:baseline;font-weight:700}.goods-wrap .goods-item .goods-bottom .price .unit[data-v-26f768fd]{font-size:%?24?%}.goods-wrap .goods-item .goods-bottom .num[data-v-26f768fd]{line-height:1}.goods-wrap .goods-item .goods-bottom .num uni-view[data-v-26f768fd]{line-height:1}.goods-wrap .goods-item .goods-bottom .num .num-wrap[data-v-26f768fd]{text-align:right}.goods-wrap .goods-item .goods-bottom .num .goods-num[data-v-26f768fd]{width:%?60?%;padding:0 %?10?%;display:inline-block;text-align:center;-webkit-transform:translateY(%?-4?%);transform:translateY(%?-4?%)}.goods-wrap .goods-item .goods-bottom .num .icon-jianshao[data-v-26f768fd]{font-size:%?40?%;color:#ccc}.goods-wrap .goods-item .goods-bottom .num .icon-add-fill[data-v-26f768fd]{font-size:%?40?%}.goods-wrap .goods-item .goods-bottom .num .select[data-v-26f768fd]{color:#fff;font-size:%?24?%;padding:0 %?20?%;height:%?40?%;line-height:%?40?%;border-radius:%?36?%;display:inline-block}.footer-wrap-fill[data-v-26f768fd]{width:100vw;height:%?120?%}.footer-wrap[data-v-26f768fd]{position:fixed;width:100vw;bottom:0;padding:%?20?%;background:#fff;box-shadow:0 -2px 10px 0 rgba(125,126,128,.16);display:flex;box-sizing:border-box;z-index:10}.footer-wrap > uni-view[data-v-26f768fd]{flex:1}.footer-wrap .left[data-v-26f768fd]{display:flex}.footer-wrap .left .cart-wrap[data-v-26f768fd]{width:%?80?%;height:%?80?%;border-radius:50%;background-color:#ddd;display:flex;align-items:center;justify-content:center;position:relative;margin-right:%?30?%}.footer-wrap .left .cart-wrap .iconfont[data-v-26f768fd]{font-size:%?40?%;color:#999}.footer-wrap .left .cart-wrap .num[data-v-26f768fd]{position:absolute;right:0;top:0;font-size:%?20?%;-webkit-transform:translateX(%?16?%);transform:translateX(%?16?%);padding:%?4?% %?10?%;line-height:1;color:#fff;border-radius:%?20?%}.footer-wrap .left .data[data-v-26f768fd]{flex:1}.footer-wrap .left .data .price[data-v-26f768fd]{line-height:1;display:flex;align-items:baseline;font-weight:600;font-size:%?32?%;margin-top:%?10?%}.footer-wrap .left .data .price .unit[data-v-26f768fd]{font-size:%?24?%}.footer-wrap .left .data .desc[data-v-26f768fd]{margin-top:%?10?%;font-size:%?28?%;color:#999;line-height:1}.footer-wrap .right[data-v-26f768fd]{text-align:right;display:flex;align-items:center;justify-content:flex-end}.footer-wrap .right .sub-btn[data-v-26f768fd]{height:%?70?%;line-height:%?70?%;color:#fff;text-align:center;width:%?200?%;border-radius:%?50?%;font-size:%?28?%}.footer-wrap .right .sub-btn.disabled[data-v-26f768fd]{background-color:#ccc;color:#eee}.cart-shade[data-v-26f768fd]{position:fixed;width:100vw;height:100vh;z-index:4;left:0;top:0}.cart-popup[data-v-26f768fd]{position:fixed;width:100vw;height:0;background-color:#fff;bottom:%?120?%;box-shadow:0 -2px 10px 0 rgba(125,126,128,.16);box-sizing:initial;z-index:5;transition:all .3s}.cart-popup.show[data-v-26f768fd]{height:45vh}.cart-popup .header[data-v-26f768fd]{width:100%;line-height:1;display:flex;height:%?80?%;background-color:#eee;align-items:center}.cart-popup .header > uni-view[data-v-26f768fd]{flex:1;padding:0 %?30?%;box-sizing:border-box;font-size:%?24?%}.cart-popup .header .right[data-v-26f768fd]{text-align:right}.cart-popup .header .right .iconfont[data-v-26f768fd]{font-size:%?24?%;margin-right:%?6?%}.cart-popup .cart-goods-wrap[data-v-26f768fd]{width:100%;height:calc(100% - %?80?%);overflow-y:scroll}.cart-popup .cart-goods-wrap .goods-item[data-v-26f768fd]{padding:%?20?% 0;margin:0 %?30?%;border-bottom:1px solid #eee;display:flex;align-items:center}.cart-popup .cart-goods-wrap .goods-item[data-v-26f768fd]:last-child{border-bottom:none}.cart-popup .cart-goods-wrap .goods-item .info[data-v-26f768fd]{flex:1}.cart-popup .cart-goods-wrap .goods-item .info .goods-name[data-v-26f768fd]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;font-size:%?28?%;line-height:%?36?%;font-weight:600}.cart-popup .cart-goods-wrap .goods-item .info .sku-name[data-v-26f768fd]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;font-size:%?28?%;line-height:%?36?%;color:#999}.cart-popup .cart-goods-wrap .goods-item .price[data-v-26f768fd]{font-weight:700;padding:0 %?40?%}.cart-popup .cart-goods-wrap .goods-item .price .unit[data-v-26f768fd]{font-size:%?24?%}.cart-popup .cart-goods-wrap .goods-item .num .goods-num[data-v-26f768fd]{width:%?60?%;padding:0 %?10?%;display:inline-block;text-align:center;-webkit-transform:translateY(%?-4?%);transform:translateY(%?-4?%)}.cart-popup .cart-goods-wrap .goods-item .num .icon-jianshao[data-v-26f768fd]{font-size:%?40?%;color:#ccc}.cart-popup .cart-goods-wrap .goods-item .num .icon-add-fill[data-v-26f768fd]{font-size:%?40?%}',""]),t.exports=o},3693:function(t,o,i){var e=i("33ed");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var s=i("967d").default;s("6474ffaa",e,!0,{sourceMap:!1,shadowMode:!1})},"4c2f":function(t,o,i){var e=i("c86c");o=e(!1),o.push([t.i,".activity-head[data-v-26f768fd] .uni-countdown__number{line-height:%?36?%;height:%?36?%;padding:0 %?6?%;font-size:%?24?%}.activity-head[data-v-26f768fd] .uni-countdown__splitor{line-height:%?40?%}[data-v-26f768fd] .uni-popup__wrapper.uni-center{background:rgba(0,0,0,.6)}[data-v-26f768fd] .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box{border-radius:10px;background:rgba($color:#000000,$alpha:0)}[data-v-26f768fd] .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box{overflow-y:visible;background:unset}[data-v-26f768fd] .sku-layer .uni-popup__wrapper-box{overflow-y:initial!important;max-height:none!important}",""]),t.exports=o},"68ff":function(t,o,i){"use strict";i.r(o);var e=i("83d6"),s=i("d97c");for(var a in s)["default"].indexOf(a)<0&&function(t){i.d(o,t,(function(){return s[t]}))}(a);i("04ea"),i("8430");var n=i("828b"),r=Object(n["a"])(s["default"],e["b"],e["c"],!1,null,"26f768fd",null,!1,e["a"],void 0);o["default"]=r.exports},"6c95":function(t,o,i){"use strict";i("6a54");var e=i("f5bd").default;Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var s=e(i("d745")),a=e(i("33da")),n=e(i("132d")),r={components:{uniPopup:s.default,nsGoodsSku:n.default},mixins:[a.default]};o.default=r},"83d6":function(t,o,i){"use strict";i.d(o,"b",(function(){return s})),i.d(o,"c",(function(){return a})),i.d(o,"a",(function(){return e}));var e={pageMeta:i("7854").default,uniCountDown:i("e12a").default,nsGoodsSku:i("132d").default,nsLogin:i("2910").default,hoverNav:i("c1f1").default,loadingCover:i("c003").default},s=function(){var t=this,o=t.$createElement,i=t._self._c||o;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",{staticClass:"container"},[t.info?[i("v-uni-view",{staticClass:"activity-head color-base-bg"},[i("v-uni-view",{staticClass:"activity-text font-size-toolbar"},[t._v("以下商品"+t._s(t.info.price)+"元任选"+t._s(t.info.num)+"件")]),0==t.info.status?i("v-uni-view",{staticClass:"no-start"},[t._v("活动未开始")]):t._e(),t.timeMachine?i("v-uni-view",{staticClass:"time"},[t._v("距离结束还剩"),i("uni-count-down",{attrs:{day:t.timeMachine.d,hour:t.timeMachine.h,minute:t.timeMachine.i,second:t.timeMachine.s,color:"#fff",splitorColor:"#fff !important",backgroundColor:"none","border-color":"transparent"}})],1):t._e()],1),i("v-uni-view",{staticClass:"goods-wrap"},t._l(t.goodsList,(function(o,e){return i("v-uni-view",{key:e,staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-image",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/pages/goods/detail",{sku_id:o.sku_id})}}},[i("v-uni-image",{attrs:{src:t.$util.img(o.sku_image,{size:"mid"}),mode:"widthFix"},on:{error:function(o){arguments[0]=o=t.$handleEvent(o),t.imageError(e)}}})],1),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"name",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/pages/goods/detail",{sku_id:o.sku_id})}}},[t._v(t._s(o.goods_name))]),i("v-uni-view",{staticClass:"spec-name"},[t._v(t._s(o.spec_name))]),i("v-uni-view",{staticClass:"introduction"},[t._v(t._s(o.introduction))]),i("v-uni-view",{staticClass:"goods-bottom"},[i("v-uni-view",{staticClass:"price  price-style large"},[i("v-uni-text",{staticClass:"unit  price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(parseFloat(o.price).toFixed(2).split(".")[0])),i("v-uni-text",{staticClass:"unit   price-style small"},[t._v("."+t._s(parseFloat(o.price).toFixed(2).split(".")[1]))])],1),i("v-uni-view",{staticClass:"num"},[void 0!=t.cart["goods_"+o.goods_id]&&t.cart["goods_"+o.goods_id]["sku_"+o.sku_id]?t.cart["goods_"+o.goods_id]["sku_"+o.sku_id]?[i("v-uni-view",{staticClass:"num-wrap"},[i("v-uni-text",{staticClass:"iconfont icon-jianshao",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.singleSkuReduce(o)}}}),i("v-uni-text",{staticClass:"goods-num"},[t._v(t._s(t.cart["goods_"+o.goods_id]["sku_"+o.sku_id].num))]),i("v-uni-text",{staticClass:"iconfont icon-add-fill color-base-text",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.singleSkuPlus(o)}}})],1)]:t._e():[i("v-uni-view",{staticClass:"num-wrap"},[o.stock>0?i("v-uni-text",{staticClass:"iconfont icon-add-fill color-base-text",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.singleSkuPlus(o)}}}):i("v-uni-text",{staticClass:"color-sub"},[t._v("库存不足")])],1)]],2)],1)],1)],1)})),1),i("v-uni-view",{staticClass:"footer-wrap-fill"}),i("v-uni-view",{staticClass:"footer-wrap"},[i("v-uni-view",{staticClass:"left"},[i("v-uni-view",{staticClass:"cart-wrap",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.openCartPopup.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"iconfont icon-cart-on"}),t.totalNum?i("v-uni-text",{staticClass:"num color-base-bg"},[t._v(t._s(t.totalNum))]):t._e()],1),i("v-uni-view",{staticClass:"data"},[i("v-uni-view",{staticClass:"price price-color"},[i("v-uni-text",{staticClass:"unit"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(parseFloat(t.totalPrice).toFixed(2).split(".")[0])),i("v-uni-text",{staticClass:"unit "},[t._v("."+t._s(parseFloat(t.totalPrice).toFixed(2).split(".")[1]))])],1),i("v-uni-view",{staticClass:"desc"},[t._v(t._s(t.info.price)+"元任选"+t._s(t.info.num)+"件")])],1)],1),i("v-uni-view",{staticClass:"right"},[t.totalNum>0&&t.totalNum%t.info.num==0&&0!=t.info.status?i("v-uni-view",{staticClass:"sub-btn color-base-bg",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.submit.apply(void 0,arguments)}}},[t._v("立即下单")]):i("v-uni-view",{staticClass:"sub-btn disabled"},[t._v("立即下单")])],1)],1),t.cartShow?i("v-uni-view",{staticClass:"cart-shade",staticStyle:{"background-color":"rgba(0,0,0,.4)"},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.closeCartPopup.apply(void 0,arguments)}}}):t._e(),t.skuList.length?i("v-uni-view",{staticClass:"cart-popup",class:{show:t.cartShow}},[i("v-uni-view",{staticClass:"header"},[i("v-uni-view",{staticClass:"left"},[i("v-uni-text",[t._v("购物车")])],1),i("v-uni-view",{staticClass:"right",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.clearCart.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"iconfont icon-icon7"}),i("v-uni-text",[t._v("清空购物车")])],1)],1),i("v-uni-scroll-view",{staticClass:"cart-goods-wrap",attrs:{"scroll-y":!0}},t._l(t.skuList,(function(o,e){return i("v-uni-view",{key:e,staticClass:"goods-item"},[i("v-uni-view",{staticClass:"info"},[i("v-uni-text",{staticClass:"goods-name"},[t._v(t._s(o.goods_name))]),o.goods_name!=o.sku_name?i("v-uni-text",{staticClass:"sku-name"},[t._v(t._s(t._f("sku")(o)))]):t._e()],1),i("v-uni-view",{staticClass:"price price-style large"},[i("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(parseFloat(o.price).toFixed(2).split(".")[0])),i("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(o.price).toFixed(2).split(".")[1]))])],1),i("v-uni-view",{staticClass:"num"},[i("v-uni-text",{staticClass:"iconfont icon-jianshao",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.singleSkuReduce(o)}}}),i("v-uni-text",{staticClass:"goods-num"},[t._v(t._s(o.num))]),i("v-uni-text",{staticClass:"iconfont icon-add-fill color-base-text",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.singleSkuPlus(o)}}})],1)],1)})),1)],1):t._e()]:t._e(),t.goodsSkuDetail?i("ns-goods-sku",{ref:"goodsSku",attrs:{goodsId:t.goodsSkuDetail.goods_id,"goods-detail":t.goodsSkuDetail},on:{refresh:function(o){arguments[0]=o=t.$handleEvent(o),t.refreshGoodsSkuDetail.apply(void 0,arguments)},confirm:function(o){arguments[0]=o=t.$handleEvent(o),t.joinCart.apply(void 0,arguments)}}}):t._e(),i("ns-login",{ref:"login"}),i("hover-nav"),i("loading-cover",{ref:"loadingCover"})],2)],1)},a=[]},8430:function(t,o,i){"use strict";var e=i("eee5"),s=i.n(e);s.a},"9c4f":function(t,o,i){var e=i("bb37");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var s=i("967d").default;s("1a51f0e1",e,!0,{sourceMap:!1,shadowMode:!1})},a725:function(t,o,i){"use strict";var e=i("ac2a"),s=i.n(e);s.a},ac2a:function(t,o,i){var e=i("f714");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var s=i("967d").default;s("1a69ffc2",e,!0,{sourceMap:!1,shadowMode:!1})},bb37:function(t,o,i){var e=i("c86c");o=e(!1),o.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-countdown[data-v-45a7f114]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-45a7f114]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?24?%}.uni-countdown__splitor.day[data-v-45a7f114]{line-height:%?50?%}.uni-countdown__number[data-v-45a7f114]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;border:%?2?% solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=o},c1f1:function(t,o,i){"use strict";i.r(o);var e=i("fa1d"),s=i("015d");for(var a in s)["default"].indexOf(a)<0&&function(t){i.d(o,t,(function(){return s[t]}))}(a);i("a725");var n=i("828b"),r=Object(n["a"])(s["default"],e["b"],e["c"],!1,null,"c1934e78",null,!1,e["a"],void 0);o["default"]=r.exports},d97c:function(t,o,i){"use strict";i.r(o);var e=i("6c95"),s=i.n(e);for(var a in e)["default"].indexOf(a)<0&&function(t){i.d(o,t,(function(){return e[t]}))}(a);o["default"]=s.a},e12a:function(t,o,i){"use strict";i.r(o);var e=i("00ba"),s=i("ea5a");for(var a in s)["default"].indexOf(a)<0&&function(t){i.d(o,t,(function(){return s[t]}))}(a);i("11ac");var n=i("828b"),r=Object(n["a"])(s["default"],e["b"],e["c"],!1,null,"45a7f114",null,!1,e["a"],void 0);o["default"]=r.exports},ea5a:function(t,o,i){"use strict";i.r(o);var e=i("f9fd"),s=i.n(e);for(var a in e)["default"].indexOf(a)<0&&function(t){i.d(o,t,(function(){return e[t]}))}(a);o["default"]=s.a},eee5:function(t,o,i){var e=i("4c2f");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var s=i("967d").default;s("301e7aee",e,!0,{sourceMap:!1,shadowMode:!1})},f714:function(t,o,i){var e=i("c86c");o=e(!1),o.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=o},f9fd:function(t,o,i){"use strict";i("6a54"),Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0,i("64aa");var e={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:[Number,String],default:0},hour:{type:[Number,String],default:0},minute:{type:[Number,String],default:0},second:{type:[Number,String],default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},mounted:function(t){var o=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){o.seconds--,o.seconds<0?o.timeUp():o.countDown()}),1e3)},watch:{day:function(t){var o=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){o.seconds--,o.seconds<0?o.timeUp():o.countDown()}),1e3)},hour:function(t){var o=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){o.seconds--,o.seconds<0?o.timeUp():o.countDown()}),1e3)},minute:function(t){var o=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){o.seconds--,o.seconds<0?o.timeUp():o.countDown()}),1e3)},second:function(t){var o=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){o.seconds--,o.seconds<0?o.timeUp():o.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,o,i,e){return t=Number(t),o=Number(o),i=Number(i),e=Number(e),60*t*60*24+60*o*60+60*i+e},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,o=0,i=0,e=0,s=0;t>0?(o=Math.floor(t/86400),i=Math.floor(t/3600)-24*o,e=Math.floor(t/60)-24*o*60-60*i,s=Math.floor(t)-24*o*60*60-60*i*60-60*e):this.timeUp(),o<10&&(o="0"+o),i<10&&(i="0"+i),e<10&&(e="0"+e),s<10&&(s="0"+s),this.d=o,this.h=i,this.i=e,this.s=s}}};o.default=e},fa1d:function(t,o,i){"use strict";i.d(o,"b",(function(){return e})),i.d(o,"c",(function(){return s})),i.d(o,"a",(function(){}));var e=function(){var t=this,o=t.$createElement,i=t._self._c||o;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},s=[]}}]);