<link rel="stylesheet" href="__STATIC__/ext/video/video.css">
<link rel="stylesheet" type="text/css" href="__STATIC__/ext/searchable_select/searchable_select.css" />
<link rel="stylesheet" type="text/css" href="__STATIC__/ext/layui/extend/cascader/cascader.css"/>
<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css" />

<div class="layui-form">
	<div class="layui-tab layui-tab-brief" lay-filter="goods_tab">
		<ul class="layui-tab-title">
			<li class="layu1i-this" lay-id="basic">基础设置</li>
			<li lay-id="price-stock">价格库存</li>
			<li lay-id="detail">项目详情</li>
			<li lay-id="senior">高级设置</li>
		</ul>
		<div class="layui-tab-content">
			<!-- 基础设置 -->
			<div class="layui-tab-item layui-show">
				<div class="layui-card card-common card-brief head">
					<div class="layui-card-header">
						<span class="card-title">基础信息</span>
					</div>

					<div class="layui-card-body">
						<div class="layui-form-item">
							<label class="layui-form-label"><span class="required">*</span>项目名称：</label>
							<div class="layui-input-inline">
								<input name="goods_name" type="text" value="{$goods_info['goods_name']}" placeholder="请输入项目名称，不能超过60个字符" maxlength="60" autocomplete="off" lay-verify="goods_name" class="layui-input len-long">
							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">促销语：</label>
							<div class="layui-input-inline">
								<textarea class="layui-textarea len-long" name="introduction" maxlength="100" lay-verify="introduction" placeholder="请输入促销语，不能超过100个字符">{$goods_info['introduction']}</textarea>
							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">关键词：</label>
							<div class="layui-input-block">
								<input type="text" name="keywords" value="{$goods_info['keywords']}" placeholder="项目关键词用于SEO搜索，不能超过100个字符" maxlength="100" autocomplete="off" class="layui-input len-long">
							</div>
						</div>

						<div class="layui-form-item goods-image-wrap">
							<label class="layui-form-label"><span class="required">*</span>项目主图：</label>
							<div class="layui-input-block">
								<!--项目主图项-->
								<div class="js-goods-image"></div>
							</div>
							<div class="word-aux">第一张图片将作为项目主图,支持同时上传多张图片,多张图片之间可随意调整位置；</div>
							<div class="word-aux">支持jpg、gif、png格式上传或从图片空间中选择，建议使用尺寸800x800像素以上、大小不超过1M的正方形图片；</div>
							<div class="word-aux">上传后的图片将会自动保存在图片空间的默认分类中，最多上传10张（至少1张）</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">项目视频：</label>
							<div class="layui-input-block">
								<div class="video-thumb">
									<video id="goods_video" class="video-js vjs-big-play-centered" controls="" poster="SHOP_IMG/goods_video_preview.png" preload="auto"></video>
								</div>
								<div id="videoUpload2" class="up-video " title="项目视频" >
									<span class="delete-video hide" onclick="deleteVideo()"><img class="del-img" src="SHOP_IMG/delete.png">删除</span>
									<span  class=" replace-video hide js-add-goods-video" ><img class="up-img" src="SHOP_IMG/upload.png">上传视频</span>
								</div>

							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label"></label>
							<div class="layui-input-block">
								<input type="text" name="video_url" placeholder="在此输入外链视频地址" value="{$goods_info['video_url']}" autocomplete="off" class="layui-input len-long">
							</div>
							<div class="file-title word-aux">
								<div>注意事项：</div>
								<ul>
									<li>1、检查upload文件夹是否有读写权限。</li>
									<li>2、PHP默认上传限制为2MB，需要在php.ini配置文件中修改“post_max_size”和“upload_max_filesize”的大小。</li>
									<li>3、视频支持手动输入外链视频地址或者上传本地视频文件</li>
									<li>4、必须上传.mp4视频格式</li>
									<li>5、视频文件大小不能超过500MB</li>
								</ul>
							</div>
						</div>

						{notempty name="$service_list"}
						<div class="layui-form-item">
							<label class="layui-form-label">项目服务：</label>
							<div class="layui-input-block">
								{foreach name="$service_list" item="vo"}
								<input type="checkbox" name="goods_service_ids" value="{$vo.id}" title="{$vo.service_name}" lay-skin="primary" {if strpos($goods_info['goods_service_ids'],(string)$vo['id'])>-1}checked{/if}>
								{/foreach}
							</div>
						</div>
						{/notempty}

						<div class="layui-form-item goods-category-wrap">
							<label class="layui-form-label"><span class="required">*</span>项目分类：</label>
							<div class="layui-input-block" id="category_select_box">
							</div>
							<input type="hidden" lay-verify="category_id"/>
							<input type="hidden" id="category_data" value='{:json_encode($goods_info.goods_category_data)}'>
							<div class="word-aux">项目可以属于多个分类，最多10个</div>
						</div>

						<div class="layui-form-item goods_state">
							<label class="layui-form-label"><span class="required">*</span>是否上架：</label>
							<div class="layui-input-block">
								<input type="radio" name="goods_state" value="1" title="立刻上架" lay-filter="goods_state" {if $goods_info['goods_state'] == 1 }checked{/if}>
								<input type="radio" name="goods_state" value="0" title="放入仓库" lay-filter="goods_state" {if $goods_info['goods_state'] == 0 }checked{/if}>
							</div>
						</div>

						{if $goods_info['goods_state'] == 0}
							<div class="layui-form-item timer_on">
								<label class="layui-form-label">定时上架：</label>
								<div class="layui-input-block">
									<input type="radio" name="timer_on_status" class="timer_on_status_true" value="1" title="启用" lay-filter="timer_on" {if $goods_info['timer_on'] > 0} checked {/if}>
									<input type="radio" name="timer_on_status" value="2" title="不启用" lay-filter="timer_on" {if $goods_info['timer_on'] == 0} checked {/if}>
								</div>
								<div class="word-aux">启用定时上架后，到达设定时间，此项目将自动上架。</div>
							</div>
							{if $goods_info['timer_on'] > 0}
							<div class="layui-form-item timer_on_time">
								<label class="layui-form-label"></label>
								<div class="layui-input-inline">
									<input type="text" id="timer_on" name="timer_on" value="{:date('Y-m-d H:i:s',$goods_info['timer_on'])}" lay-verify="required" class="layui-input len-mid" autocomplete="off" readonly>
									<i class=" iconrili iconfont calendar"></i>
								</div>
							</div>
							{else/}
							<div class="layui-form-item timer_on_time layui-hide">
								<label class="layui-form-label"></label>
								<div class="layui-input-inline">
									<input type="text" id="timer_on" name="timer_on" lay-verify="" class="layui-input len-mid" autocomplete="off" readonly>
									<i class=" iconrili iconfont calendar"></i>
								</div>
							</div>
							{/if}
						{/if}

						<div class="layui-form-item">
							<label class="layui-form-label">定时下架：</label>
							<div class="layui-input-block">
								<input type="radio" name="timer_off_status" value="1" title="启用" lay-filter="timer_off" {if $goods_info['timer_off'] > 0} checked {/if}>
								<input type="radio" name="timer_off_status" value="2" title="不启用" lay-filter="timer_off" {if $goods_info['timer_off'] == 0} checked {/if}>
							</div>
							<div class="word-aux">启用定时下架后，到达设定时间，此项目将自动下架。</div>
						</div>
						{if $goods_info['timer_off'] > 0}
						<div class="layui-form-item timer_off">
							<label class="layui-form-label"></label>
							<div class="layui-input-inline">
								<input type="text" id="timer_off" name="timer_off" value="{:date('Y-m-d H:i:s',$goods_info['timer_off'])}" lay-verify="required" class="layui-input len-mid" autocomplete="off" readonly>
								<i class=" iconrili iconfont calendar"></i>
							</div>
						</div>
						{else/}
						<div class="layui-form-item timer_off" style="display:none">
							<label class="layui-form-label"></label>
							<div class="layui-input-inline">
								<input type="text" id="timer_off" name="timer_off" lay-verify="" class="layui-input len-mid" autocomplete="off" readonly>
								<i class=" iconrili iconfont calendar"></i>
							</div>
						</div>
						{/if}


						<div class="layui-form-item">
							<label class="layui-form-label">是否需要预约：</label>
							<div class="layui-input-block">
								<input type="radio" name="is_reserve" value="1" title="是" {if $goods_info['is_reserve'] == 1 }checked{/if}>
								<input type="radio" name="is_reserve" value="0" title="否" {if $goods_info['is_reserve'] == 0 }checked{/if}>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">服务模式：</label>
							<div class="layui-input-block">
								<input type="radio" name="service_mode" value="onsite" title="上门服务" lay-filter="service_mode" {if $goods_info['service_mode'] == 'onsite' }checked{/if}>
								<input type="radio" name="service_mode" value="in_store" title="到店服务" lay-filter="service_mode" {if $goods_info['service_mode'] == 'in_store' }checked{/if}>
							</div>
						</div>

						<div class="layui-form-item onsite-price" {if $goods_info['service_mode'] == 'in_store' }style="display: none"{/if}>
							<label class="layui-form-label">费用形式：</label>
							<div class="layui-input-block">
								<input type="radio" name="service_price_way" value="reserve_price" title="预约定金" {if $goods_info['service_price_way'] == 'reserve_price' }checked{/if}>
								<input type="radio" name="service_price_way" value="fixed_price" title="一口价格" {if $goods_info['service_price_way'] == 'fixed_price' }checked{/if}>
							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label"><span class="required">*</span>销售价：</label>
							<div class="layui-input-block">
								<input type="text" name="price" value="{$goods_info['price']}" placeholder="0.00" lay-verify="service_price" class="layui-input len-short" autocomplete="off">
								<div class="layui-form-mid">元</div>
							</div>
						</div>


						{if addon_is_exit('cashier') == 1}
						<div class="layui-form-item">
							<label class="layui-form-label">销售渠道：</label>
							<div class="layui-input-block">
								<input type="radio" name="sale_channel" value="all" title="线上线下销售" {if $goods_info.sale_channel eq 'all'}checked{/if}>
								<input type="radio" name="sale_channel" value="online" title="线上销售" {if $goods_info.sale_channel eq 'online'}checked{/if}>
								<input type="radio" name="sale_channel" value="offline" title="线下销售" {if $goods_info.sale_channel eq 'offline'}checked{/if}>
							</div>
						</div>
						{/if}

						{if $store_is_exit}
						<div class="layui-form-item">
							<label class="layui-form-label">适用门店：</label>
							<div class="layui-input-block">
								<input type="radio" name="sale_store" value="all" title="全部门店" {if $goods_info.sale_store eq 'all'}checked{/if} lay-filter="sale_store">
								<input type="radio" name="sale_store" value="" title="部分门店" {if $goods_info.sale_store neq 'all'}checked{/if} lay-filter="sale_store">
							</div>
						</div>

						<div class="layui-form-item sale-store-select" {if $goods_info.sale_store eq 'all'}style="display: none"{/if} lay-verify="sale_store">
							<label class="layui-form-label"></label>
							<div class="layui-input-block">
								<button class="layui-btn select-store">选择门店</button>
								<div style="width: 700px">
									<table class="layui-table" lay-skin="nob">
										<colgroup>
											<col width="30%">
											<col width="60%">
											<col width="10%">
										</colgroup>
										<tr>
											<th>门店名称</th>
											<th>门店地址</th>
											<th>操作</th>
										</tr>
										<tbody class="sale-store">
										{if isset($store_list) && !empty($store_list)}
										{foreach name="$store_list" item="vo"}
										<tr data-store="{$vo.store_id}">
											<td>{$vo.store_name}</td>
											<td>{$vo.full_address}{$vo.address}</td>
											<td><a href="javascript:;" class="del">删除</a></td>
										</tr>
										{/foreach}
										{/if}
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="need-verify">
							<div class="layui-form-item">
								<label class="layui-form-label">核销有效期：</label>
								<div class="layui-input-block">
									<input type="radio" name="verify_validity_type" value="0" title="永久" lay-filter="verify_validity_type"  {if $goods_info['verify_validity_type'] eq 0}checked{/if}>
									<input type="radio" name="verify_validity_type" value="1" title="购买后几日有效" lay-filter="verify_validity_type"  {if $goods_info['verify_validity_type'] eq 1}checked{/if}>
									<input type="radio" name="verify_validity_type" value="2" title="指定过期日期" lay-filter="verify_validity_type"  {if $goods_info['verify_validity_type'] eq 2}checked{/if}>
								</div>
							</div>

							<div class="layui-form-item validity-type validity-type-1 {if $goods_info['verify_validity_type'] neq 1}layui-hide{/if}">
								<label class="layui-form-label"><span class="required">*</span>有效期：</label>
								<div class="layui-input-inline">
									<input type="text" name="virtual_indate" placeholder="0" class="layui-input len-short" lay-verify="virtual_indate" autocomplete="off" {if $goods_info['verify_validity_type'] eq 1}value="{$goods_info['virtual_indate']}"{/if}>
								</div>
								<div class="layui-form-mid layui-word-aux">天</div>
							</div>

							<div class="layui-form-item validity-type validity-type-2 {if $goods_info['verify_validity_type'] neq 2}layui-hide{/if}">
								<label class="layui-form-label"><span class="required">*</span>有效期：</label>
								<div class="layui-input-inline">
									<input type="text" id="virtual_time" name="virtual_time" class="layui-input len-mid" lay-verify="virtual_time" autocomplete="off" readonly {if $goods_info['verify_validity_type'] eq 2}value="{:time_to_date($goods_info['virtual_indate'])}"{/if}>
									<i class=" iconrili iconfont calendar"></i>
								</div>
								<div class="word-aux" style="clear:both;top: 5px;position: relative;">无论何时购买此商品，到达指定时间后都将过期，无法核销。</div>
							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">是否统一售价：</label>
							<div class="layui-input-block">
								<input type="radio" name="is_unify_price" value="1" title="是" {if $goods_info.is_unify_price eq '1'}checked{/if} >
								<input type="radio" name="is_unify_price" value="0" title="否" {if $goods_info.is_unify_price eq '0'}checked{/if}>
							</div>
							<div class="word-aux">价格设置之后门店不能修改价格，门店按照平台设置的价格售卖</div>
						</div>

						{/if}

						</div>
					</div>

				</div>


			<!-- 价格库存 -->
			<div class="layui-tab-item">
				<div class="layui-form-item layui-hide">
					<label class="layui-form-label">启用多规格：</label>
					<div class="layui-input-inline">
						<input type="checkbox" value="1" lay-skin="switch" name="spec_type" lay-filter="spec_type" lay-verify="spec_type" {notempty name="$goods_info['goods_spec_format']" }checked{/notempty}>
						<input type="hidden" id="spec_type_status" {if empty($goods_info['goods_spec_format'])} value="0" {else/} value="1" {/if}>
					</div>
				</div>

				<!-- 单规格 -->
				<div class="js-single-spec" {notempty name="$goods_info['goods_spec_format']" }style="display:none;"{/notempty}>

					<div class="layui-form-item">
						<label class="layui-form-label">划线价：</label>
						<div class="layui-input-block">
							<input type="text" name="market_price" value="{$goods_info['market_price']}" placeholder="0.00" lay-verify="market_price" class="layui-input len-short" autocomplete="off">
							<div class="layui-form-mid">元</div>
						</div>
						<div class="word-aux">项目没有优惠活动显示的划线价格，如果项目有折扣等优惠活动划线价显示销售价</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">成本价：</label>
						<div class="layui-input-block">
							<input type="text" name="cost_price" value="{$goods_info['cost_price']}" placeholder="0.00" class="layui-input len-short" lay-verify="cost_price" autocomplete="off">
							<div class="layui-form-mid">元</div>
						</div>
						<div class="word-aux">成本价将不会对前台会员展示，用于商家统计使用</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">项目编码：</label>
						<div class="layui-input-inline">
							<input type="text" name="sku_no" value="{$goods_info['sku_list'][0]['sku_no']}" placeholder="请输入项目编码" maxlength="50" class="layui-input len-long" autocomplete="off">
						</div>
					</div>

				</div>

				<!-- 多规格 -->
				<div class="js-more-spec" {notempty name="$goods_info['goods_spec_format']" }style="display:block;"{/notempty}>

					<!--规格项/规格值-->
					<div class="spec-edit-list"></div>

					<div class="layui-form-item js-add-spec">
						<label class="layui-form-label"></label>
						<div class="layui-input-inline">
							<button class="layui-btn" type="button">添加规格</button>
						</div>
					</div>

					<div class="layui-form-item batch-operation-sku">
						<label class="layui-form-label">批量操作：</label>
						<div class="layui-input-inline">
							<span class="text-color" data-field="spec_name">副标题</span>
							<span class="text-color" data-field="price" data-verify="price">销售价</span>
							<span class="text-color" data-field="market_price" data-verify="market_price">划线价</span>
							<span class="text-color" data-field="cost_price" data-verify="cost_price">成本价</span>
							<span class="text-color" data-field="stock" data-verify="stock">库存</span>
							<span class="text-color" data-field="stock_alarm" data-verify="stock_alarm">库存预警</span>
							<span class="text-color" data-field="sku_no" data-verify="">项目编码</span>
							<input type="text" class="layui-input len-short" name="batch_operation_sku" autocomplete="off" />
							<button class="layui-btn confirm" type="button">确定</button>
							<button class="layui-btn layui-btn-primary cancel" type="button">取消</button>
						</div>
					</div>

					<!--编辑时用到的SKU列表-->
					<div class="js-edit-sku-list">
						{foreach name="$goods_info['sku_list']" item="vo" key="k"}
						<div data-index="{$k}">
							<input type="hidden" name="edit_sku_id" value="{$vo['sku_id']}" />
							<input type="hidden" name="edit_spec_name" value="{$vo['spec_name']}" />
							<input type="hidden" name="edit_sku_no" value="{$vo['sku_no']}" />
							<input type="hidden" name="edit_sku_spec_format" value="{$vo['sku_spec_format']}" />
							<input type="hidden" name="edit_price" value="{$vo['price']}" />
							<input type="hidden" name="edit_market_price" value="{$vo['market_price']}" />
							<input type="hidden" name="edit_cost_price" value="{$vo['cost_price']}" />
							<input type="hidden" name="edit_stock" value="{$vo['stock']}" />
							<input type="hidden" name="edit_stock_alarm" value="{$vo['stock_alarm']}" />
							<input type="hidden" name="edit_sku_image" value="{$vo['sku_image']}" />
							<input type="hidden" name="edit_sku_images" value="{$vo['sku_images']}" />
							<input type="hidden" name="edit_is_default" value="{$vo['is_default']}" />
							<input type="hidden" name="edit_service_length" value="{$vo['service_length']}" />
						</div>
						{/foreach}
					</div>

					<!--sku列表-->
					<div class="layui-form-item sku-table">
						<label class="layui-form-label"></label>
						<div class="layui-input-block"></div>
					</div>

				</div>

				<div class="layui-form-item js-goods-stock-wrap" {notempty name="$goods_info['goods_spec_format']" }style="display:none;"{/notempty}>
					<label class="layui-form-label"><span class="required">*</span>库存：</label>
					<div class="layui-input-block">
						<input type="number" name="goods_stock" value="{$goods_info['goods_stock']}" placeholder="0" lay-verify="goods_stock" class="layui-input len-short" autocomplete="off" {notempty name="$goods_info['goods_spec_format']" }disabled{/notempty}>
						<div class="layui-form-mid">件</div>
					</div>
				</div>

				<div class="layui-form-item js-goods-stock-wrap" {notempty name="$goods_info['goods_spec_format']" }style="display:none;"{/notempty}>
					<label class="layui-form-label">库存预警：</label>
					<div class="layui-input-block">
						<input type="number" name="goods_stock_alarm" value="{$goods_info['goods_stock_alarm']}" placeholder="0" lay-verify="goods_stock_alarm" class="layui-input len-short" autocomplete="off">
						<div class="layui-form-mid">件</div>
					</div>
					<div class="word-aux">项目库存少于预警数量，项目列表库存数量标红显示，0为不预警。</div>
				</div>

				<div class="layui-form-item js-goods-stock-wrap">
					<label class="layui-form-label">服务时长：</label>
					<div class="layui-input-block">
						<input type="number" name="service_length" value="{$goods_info['sku_list'][0]['service_length']}" placeholder="请输入服务时长" autocomplete="off" class="layui-input len-short" lay-verify="service_length">
						<div class="layui-form-mid">分钟</div>
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">虚拟销量：</label>
					<div class="layui-input-block">
						<input type="number" name="virtual_sale" placeholder="0" value="{$goods_info['virtual_sale']}" lay-verify="virtual_sale" class="layui-input len-short" autocomplete="off">
						<div class="layui-form-mid">件</div>
					</div>
					<div class="word-aux">该设置不计入项目统计数据</div>
				</div>

				<div class="layui-form-item is_limit">
					<label class="layui-form-label">是否限购：</label>
					<div class="layui-input-block">
						<input type="radio" name="is_limit" value="0" title="否" lay-filter="is_limit" {if $goods_info['is_limit'] == 0} checked {/if}>
						<input type="radio" name="is_limit" value="1" title="是" lay-filter="is_limit" {if $goods_info['is_limit'] == 1} checked {/if}>
					</div>
					<div class="word-aux">启用限购后，购买项目时，会对该项目购买量做限制判断。</div>
				</div>

				{if $goods_info['is_limit'] == 1}
				<div class="layui-form-item limit_type" >
					<label class="layui-form-label">限购类型：</label>
					<div class="layui-input-block">
						<input type="radio" name="limit_type" class="limit_type" value="1" title="单次限购" lay-filter="limit_type" {if $goods_info['limit_type'] == 1 } checked {/if}>
						<input type="radio" name="limit_type" class="limit_type" value="2" title="长期限购" lay-filter="limit_type" {if $goods_info['limit_type'] == 2} checked {/if}>
						<input type="number" name="max_buy" placeholder="" lay-verify="max_buy" value="{$goods_info['max_buy']}" class="layui-input len-short" autocomplete="off">
						<div class="layui-form-mid">&nbsp件</div>
					</div>
					<div class="word-aux">单次限购是针对于每次下单不能超过限购数量，长期限购是针对于会员账号购买这个项目的总数不能超过限购数量。</div>
				</div>
				{/if}

				<div class="layui-form-item">
					<label class="layui-form-label">起售：</label>
					<div class="layui-input-block">
						<input type="number" name="min_buy" placeholder="" lay-verify="min_buy" value="{$goods_info['min_buy']}" class="layui-input len-short" autocomplete="off">
						<div class="layui-form-mid">件</div>
					</div>
					<div class="word-aux">起售数量超出项目库存时，买家无法购买该项目</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">会员等级折扣：</label>
					<div class="layui-input-block">
						<div class="layui-input-inline">
							<input type="radio" name="is_consume_discount" value="1" title="参与" {if $goods_info['is_consume_discount'] > 0} checked {/if}>
							<input type="radio" name="is_consume_discount" value="0" title="不参与" {if $goods_info['is_consume_discount'] == 0} checked {/if}>
						</div>
					</div>
					<div class="word-aux">如果该项目未单独配置过优惠规则，则按照默认会员等级折扣优惠</div>
				</div>
			</div>

			<!-- 项目详情 -->
			<div class="layui-tab-item">
				<div class="layui-form-item">
					<label class="layui-form-label sm"></label>
					<div class="layui-input-inline special-length">
						<input type="hidden" name="goods_content" value="{$goods_info['goods_content']}" />
						<script id="editor" type="text/plain" style="width:100%;height:500px;"></script>
					</div>
				</div>
				<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.config.js"></script>
				<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.all.js"> </script>
				<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/lang/zh-cn/zh-cn.js"></script>
			</div>

			<div class="layui-tab-item layui-card card-common card-brief head">
				<div class="layui-card-header">
					<span class="card-title">高级设置</span>
				</div>

				<div class="layui-card-body">
					<div class="layui-form-item">
						<label class="layui-form-label">排序：</label>
						<div class="layui-input-block">
							<input type="number" name="sort" value="{$goods_info['sort']}" class="layui-input len-short" placeholder="0" autocomplete="off">
						</div>
						<div class="word-aux">项目默认排序号为0，数字越大，排序越靠前，数字重复，则最新添加的靠前。</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">项目详情显示库存：</label>
						<div class="layui-input-block">
							<input type="radio" name="stock_show" value="1" title="显示"  {if $goods_info['stock_show'] > 0} checked {/if}>
							<input type="radio" name="stock_show" value="0" title="隐藏"  {if $goods_info['stock_show'] == 0} checked {/if}>
						</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">划线价显示：</label>
						<div class="layui-input-block">
							<input type="radio" name="market_price_show" value="1" title="显示"  {if $goods_info['market_price_show'] > 0} checked {/if}>
							<input type="radio" name="market_price_show" value="0" title="隐藏"  {if $goods_info['market_price_show'] == 0} checked {/if}>
						</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">项目详情显示弹幕：</label>
						<div class="layui-input-block">
							<input type="radio" name="barrage_show" value="1" title="显示"  {if $goods_info['barrage_show'] > 0} checked {/if}>
							<input type="radio" name="barrage_show" value="0" title="隐藏"  {if $goods_info['barrage_show'] == 0} checked {/if}>
						</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">项目详情显示销量：</label>
						<div class="layui-input-block">
							<input type="radio" name="sale_show" value="1" title="显示"  {if $goods_info['sale_show'] > 0} checked {/if}>
							<input type="radio" name="sale_show" value="0" title="隐藏"  {if $goods_info['sale_show'] == 0} checked {/if}>
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">项目海报：</label>
						<div class="layui-input-inline">
							<select name="template_id" lay-search="" lay-verify="">
								<option value="">请选择项目海报</option>
								{foreach name="$poster_list" item="vo"}
								<option value="{$vo['template_id']}" {if $goods_info.template_id == $vo.template_id} selected {/if}>{$vo['poster_name']}</option>
								{/foreach}
							</select>
						</div>
					</div>

					{if $form_is_exit}
					<div class="layui-form-item">
						<label class="layui-form-label">项目表单：</label>
						<div class="layui-input-block len-mid">
							<select name="form_id">
								<option value="0">请选择项目表单</option>
								{foreach name="$form_list" item="vo"}
								<option value="{$vo.id}" {if $goods_info.form_id == $vo.id} selected {/if}>{$vo.form_name}</option>
								{/foreach}
							</select>
						</div>
						<div class="word-aux">
							<a href="{:href_url('form://shop/form/addform?form_type=goods')}" class="text-color" target="_blank">创建项目表单</a>
							<a href="javascript:;" onclick="refreshFormList()" class="text-color">刷新</a>
						</div>
					</div>
					{/if}
				</div>

			</div>

		</div>
	</div>

	<input type="hidden" name="goods_id" value="{$goods_info['goods_id']}" />
	<input type="hidden" name="goods_spec_format" value="{$goods_info['goods_spec_format']}" />
	<input type="hidden" name="goods_image" value="{$goods_info['goods_image']}" />
	<input type="hidden" name="goods_attr_format" value="{$goods_info['goods_attr_format']}" />

	<div class="fixed-btn">
		<button class="layui-btn layui-btn-primary border-color text-color js-prev" lay-filter="prev">上一步</button>
		<button class="layui-btn js-save" lay-submit="" lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary border-color text-color js-next" lay-submit="" lay-filter="next">下一步</button>
	</div>
</div>

<!--规格项模板-->
<script type="text/html" id="specTemplate">

	{{# for(var i=0;i<d.list.length;i++){ }}
	<div class="spec-item" data-index="{{i}}">
		<div class="layui-form-item spec">
			<label class="layui-form-label">规格项{{i+1}}：</label>
			<div class="layui-input-inline">
				<select name="spec_item">
					<option value="0"></option>
					{{# if(d.list[i].spec_name != ''){ }}
					<option value="{{d.list[i].spec_id}}" data-attr-name="{{d.list[i].spec_name}}" selected>{{d.list[i].spec_name}}</option>
					{{# }else{ }}
					{{# } }}
				</select>
				<i class="layui-icon layui-icon-close" data-index="{{i}}"></i>
			</div>

			{{# if(i==0){ }}
			<div class="layui-input-inline">
				{{# if(d.add_spec_img){ }}
				<input type="checkbox" name="add_spec_img" title="添加规格图片" lay-skin="primary" lay-filter="add_spec_img" checked>
				{{# }else{ }}
				<input type="checkbox" name="add_spec_img" title="添加规格图片" lay-skin="primary" lay-filter="add_spec_img">
				{{# } }}
			</div>
			{{# } }}
		</div>

		{{# if(d.list[i].spec_name != ''){ }}
		<div class="layui-form-item spec-value">
		{{# }else{ }}
		<div class="layui-form-item spec-value" style="display:none;">
		{{# } }}
			<label class="layui-form-label"></label>
			<div class="layui-input-block spec-value">
				{{# if(d.list[i].value.length){ }}
				<ul>
					{{# for(var j=0;j<d.list[i].value.length;j++){ }}
					<li data-index="{{j}}" data-parent-index="{{i}}" >
						{{# if(i==0 && d.add_spec_img){ }}
						<div class="img-wrap">
							{{# if(d.list[i].value[j].image){ }}
							<img src="{{ns.img(d.list[i].value[j].image)}}" alt="">
							{{# }else{ }}
							<img src="SHOP_IMG/goods_spec_value_empty.png" alt="">
							{{# } }}
						</div>
						{{# } }}

						<span title="双击可编辑规格值" ondblclick="$(this).attr('contenteditable',true);$(this).focus()" class="spec-txt" data-spec_value_name="{{d.list[i].value[j].spec_value_name}}"  data-parent-index="{{i}}" data-index="{{j}}">{{d.list[i].value[j].spec_value_name}}</span>
						<!--{{1# if(d.list[i].value[j].is_delete === undefined){ }}-->
						<i class="layui-icon layui-icon-close" data-parent-index="{{i}}" data-index="{{j}}"></i>
						<!--{{1# } }}-->
					</li>
					{{# } }}
				</ul>
				{{# } }}

				<a class="text-color" href="javascript:;" data-index="{{i}}">+添加规格值</a>

				<div class="add-spec-value-popup" data-index="{{i}}">
					<select name="spec_value_item"></select>
					<button class="layui-btn layui-btn-primary border-color text-color js-cancel-spec-value">取消</button>
				</div>

			</div>
		</div>

	</div>
	{{# } }}
</script>

<!--SKU列表模板-->
<script type="text/html" id="skuTableTemplate">

	{{# if(d.skuList.length){ }}
	<table class="layui-table">
		<colgroup></colgroup>
		<thead>
			<tr>
				{{# if(d.showSpecName){ }}
				<th colspan="{{d.colSpan}}" style="min-width: 60px;">项目规格</th>
				{{# } }}
				<th rowspan="{{d.rowSpan}}">SKU图片</th>
				<th rowspan="{{d.rowSpan}}">副标题</th>
				<th rowspan="{{d.rowSpan}}"><span class="required">*</span>销售价</th>
				<th rowspan="{{d.rowSpan}}">划线价</th>
				<th rowspan="{{d.rowSpan}}">成本价</th>
				<th rowspan="{{d.rowSpan}}"><span class="required">*</span>库存</th>
				<th rowspan="{{d.rowSpan}}">库存预警</th>
				<th rowspan="{{d.rowSpan}}">服务时长</th>
				<th rowspan="{{d.rowSpan}}">商品编码（多个编码以英文逗号分割）</th>
				<th rowspan="{{d.rowSpan}}" style="white-space: nowrap;">默认展示</th>
			</tr>
			{{# if(d.colSpan>1){ }}
			<tr>
				{{# for(var i=0;i<d.specList.length;i++){ }}
				{{# if(d.specList[i].spec_name && d.specList[i].value.length> 0){ }}
				<th>{{d.specList[i].spec_name}}</th>
				{{# } }}
				{{# } }}
			</tr>
			{{# } }}
		</thead>
		<tbody>
			{{# for(var i=0;i<d.skuList.length;i++){ }}
			<tr>
				<td id="sku_img_{{i}}">
					{{# for(var j=0;j<d.skuList[i].sku_images_arr.length;j++){ }}
					<div class="img-wrap" data-index="{{j}}" data-parent-index="{{i}}">
						<a href="javascript:void(0)">
							<img src="{{ns.img(d.skuList[i].sku_images_arr[j])}}" layer-src />
						</a>
						<div class="operation">
							<i title="图片预览" class="iconfont iconreview js-preview"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
					</div>
					{{# } }}
					{{# if(d.skuList[i].sku_images_arr.length<d.goods_sku_max){ }}
						<div class="upload-sku-img" data-index="{{i}}"><i class="layui-icon layui-icon-add-1"></i></div>
					{{# } }}
				</td>
				<td>
					<input type="text" name="spec_name" placeholder="副标题" maxlength="100" value="{{d.skuList[i].spec_name}}" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="text" name="price" placeholder="销售价" lay-verify="sku_price" value="{{d.skuList[i].price}}" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="text" name="market_price" placeholder="划线价" value="{{d.skuList[i].market_price}}" lay-verify="sku_market_price" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="text" name="cost_price" placeholder="成本价" value="{{d.skuList[i].cost_price}}" lay-verify="sku_cost_price" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="text" name="stock" placeholder="库存" value="{{d.skuList[i].stock}}" lay-verify="sku_stock" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="text" name="stock_alarm" placeholder="库存预警" value="{{d.skuList[i].stock_alarm}}" lay-verify="sku_stock_alarm" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="number" name="service_length" placeholder="服务时长" value="{{d.skuList[i].service_length}}" maxlength="50" class="layui-input" autocomplete="off" data-index="{{i}}" lay-verify="verify_num">
				</td>
				<td>
					<input type="text" name="sku_no" placeholder="商品编码" value="{{d.skuList[i].sku_no}}" maxlength="50" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td style="min-width: 40px;">
					{{# if(d.skuList[i].is_default == 1) { }}
					<input type="checkbox" data-index="{{i}}" name="is_default" lay-filter="is_default" lay-skin="switch" checked>
					{{# }else { }}
					<input type="checkbox" data-index="{{i}}" name="is_default" lay-filter="is_default" lay-skin="switch">
					{{# } }}
				</td>
			</tr>
			{{# } }}

		</tbody>
	</table>
	{{# } }}
	<div class="word-aux text-color" style="margin: 10px 0 0 0;">默认展示，是多规格项目在客户访问项目时，默认显示的项目规格</div>
</script>

<!--项目主图列表-->
<script type="text/html" id="goodsImage">
	{{# if(d.list.length){ }}
		{{# for(var i=0;i<d.list.length;i++){ }}
			<div class="item upload_img_square_item" data-index="{{i}}">
				<div class="img-wrap">
					<img src="{{ns.img(d.list[i],'small')}}" layer-src="{{ns.img(d.list[i],'big')}}">
				</div>
				<div class="operation">
					<i title="图片预览" class="iconfont iconreview js-preview"></i>
					<i title="删除图片" class="layui-icon layui-icon-delete js-delete" data-index="{{i}}"></i>
					<div class="replace_img" data-index="{{i}}">点击替换</div>
				</div>
			</div>
		{{# } }}
		{{# if(d.list.length < d.max){ }}
			<div class="item js-add-goods-image upload_img_square">+</div>
		{{# } }}
	{{# }else{ }}
		<div class="item js-add-goods-image upload_img_square">+</div>
	{{# } }}
</script>
<script src="__STATIC__/ext/drag-arrange.js"></script>
<script src="__STATIC__/ext/video/videojs-ie8.min.js"></script>
<script src="__STATIC__/ext/video/video.min.js"></script>
<script src="__STATIC__/ext/searchable_select/searchable_select.js"></script>
<script src="SHOP_JS/category_select.js?time=20240821"></script>
<script src="SHOP_JS/goods_edit_common.js?time=20250428"></script>
<script src="ADDON_CARDSERVICE_JS/service_goods_edit.js?v=1"></script>

