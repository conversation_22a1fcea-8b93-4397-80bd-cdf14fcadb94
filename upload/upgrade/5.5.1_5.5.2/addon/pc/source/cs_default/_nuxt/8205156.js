(window.webpackJsonp=window.webpackJsonp||[]).push([[72],{536:function(t,e,o){},539:function(t,e,o){"use strict";o(536)},544:function(t,e,o){"use strict";o.r(e);o(24),o(25),o(23),o(7),o(29),o(18),o(30);var n=o(10),r=(o(535),o(12)),l=o(206);function c(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var d={name:"goods_recommend",props:{page:{type:[Number,String],default:1},pageSize:{type:[Number,String],default:5}},data:function(){return{loading:!0,list:[]}},created:function(){this.getGoodsRecommend()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?c(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):c(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(r.b)(["defaultGoodsImage"])),methods:{getGoodsRecommend:function(){var t=this;Object(l.e)({page:this.page,page_size:this.pageSize}).then((function(e){0==e.code&&(t.list=e.data.list),t.loading=!1})).catch((function(e){t.loading=!1}))},imageError:function(t){this.list[t].sku_image=this.defaultGoodsImage}}},_=d,m=(o(539),o(6)),component=Object(m.a)(_,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"goods-recommend"},[e("h4",[t._v("商品精选")]),t._v(" "),t.list.length?e("ul",t._l(t.list,(function(o,n){return e("li",{key:n,on:{click:function(e){return t.$util.pushToTab({path:"/sku/"+o.sku_id})}}},[e("div",{staticClass:"img-wrap"},[e("img",{attrs:{src:t.$img(o.sku_image,{size:"mid"})},on:{error:function(e){return t.imageError(n)}}})]),t._v(" "),e("div",{staticClass:"price"},[t._v("￥"+t._s(o.discount_price))]),t._v(" "),e("p",{staticClass:"sku-name"},[t._v(t._s(o.goods_name))]),t._v(" "),e("div",{staticClass:"info-wrap"})])})),0):t._e()])}),[],!1,null,"a68a46cc",null);e.default=component.exports},555:function(t,e,o){"use strict";o.d(e,"c",(function(){return r})),o.d(e,"a",(function(){return l})),o.d(e,"b",(function(){return c}));var n=o(1);function r(t){return Object(n.a)({url:"/api/goodscollect/iscollect",data:t,forceLogin:!0})}function l(t){return Object(n.a)({url:"/api/goodscollect/add",data:t,forceLogin:!0})}function c(t){return Object(n.a)({url:"/api/goodscollect/delete",data:t,forceLogin:!0})}},556:function(t,e,o){"use strict";o.d(e,"b",(function(){return r})),o.d(e,"a",(function(){return l}));var n=o(1);function r(t){return Object(n.a)({url:"/api/goodsevaluate/page",data:t})}function l(t){return Object(n.a)({url:"/api/goodsevaluate/getgoodsevaluate",data:t})}},564:function(t,e,o){"use strict";o.d(e,"b",(function(){return r})),o.d(e,"a",(function(){return l}));var n=o(1);function r(t){return Object(n.a)({url:"/coupon/api/coupon/typepagelists",data:t})}function l(t){return Object(n.a)({url:"/coupon/api/coupon/receive",data:t,forceLogin:!0})}},566:function(t,e,o){"use strict";var n=o(3),r=o(5),l=o(47),c=o(44),d=o(58),_=o(323),m=o(19),v=o(4),h=o(322),f=o(212),k=o(567),y=o(568),S=o(114),C=o(569),D=[],w=r(D.sort),O=r(D.push),j=v((function(){D.sort(void 0)})),A=v((function(){D.sort(null)})),T=f("sort"),x=!v((function(){if(S)return S<70;if(!(k&&k>3)){if(y)return!0;if(C)return C<603;var code,t,e,o,n="";for(code=65;code<76;code++){switch(t=String.fromCharCode(code),code){case 66:case 69:case 70:case 72:e=3;break;case 68:case 71:e=4;break;default:e=2}for(o=0;o<47;o++)D.push({k:t+o,v:e})}for(D.sort((function(a,b){return b.v-a.v})),o=0;o<D.length;o++)t=D[o].k.charAt(0),n.charAt(n.length-1)!==t&&(n+=t);return"DGBEFHACIJK"!==n}}));n({target:"Array",proto:!0,forced:j||!A||!T||!x},{sort:function(t){void 0!==t&&l(t);var e=c(this);if(x)return void 0===t?w(e):w(e,t);var o,n,r=[],v=d(e);for(n=0;n<v;n++)n in e&&O(r,e[n]);for(h(r,function(t){return function(e,o){return void 0===o?-1:void 0===e?1:void 0!==t?+t(e,o)||0:m(e)>m(o)?1:-1}}(t)),o=d(r),n=0;n<o;)e[n]=r[n++];for(;n<v;)_(e,n++);return e}})},567:function(t,e,o){var n=o(94).match(/firefox\/(\d+)/i);t.exports=!!n&&+n[1]},568:function(t,e,o){var n=o(94);t.exports=/MSIE|Trident/.test(n)},569:function(t,e,o){var n=o(94).match(/AppleWebKit\/(\d+)\./);t.exports=!!n&&+n[1]},652:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABcAAAAXCAYAAADgKtSgAAACEUlEQVRIS7WVsXEcMQxFQaZ24MC5FNoznpE7kK4KhXIJ6EB3FRwyp3IJrkC6Dk6hM5egQE6XnscBOFzerhx4jskOl+Tnx8cHmOSMI50RWyq4qt6IyOPKRQczY302VLWsETOzijuC/xCR392hb8wDXFWZX/j6VkQOIvLU7YfE9Rr4BrCU0sV+vz+oaj0IONGVUm5TSp8d7FpEdiynlK5KKc+IICL3b4Fze90wgCPDxszqhS4L4MyRFGLt7JIslbmIXALi4FcicnSWfC9FZO9AlbmIfPU9bzJ/WNCcvPCfEV+0v3fWo+Y3oyzcDAMGzEgayWIczUyJopTyPaX0y8yOHhV7PzjrltcwwInPVRUXtKT0dutz4LqzF6YnVm2ar/nVwXAFjokEt/nCxbO18Dnh3flmioZkbqdpes05f2GtA2dvaI/eGOBumqY/Oef3ToSIFit0Z2ZbtxruYTyuWHNmxbBpRDTT3GV4AnyUKi7rATw/VfNRwp45bsG7fF8GO8Y9O6/QTx76RlWrWzr3INFDEBh7CyUMeAwuCzsGuygYogPsxcsfK//sox7BW3m71VpvGecL5c+Wmq+Z5l3LnfWHBV/3jSxabmvJY42cMPdbSRKOAYxqRccdLXeapo8553fhNLcrc9o1Oahnl6z4r95Sws8B0EXdzkJkzeejC2vY3csze5mWXrFZ41or///9f9YH+i/YqE4ncfpLqwAAAABJRU5ErkJggg=="},653:function(t,e,o){},745:function(t,e,o){"use strict";o(653)},760:function(t,e,o){"use strict";o.r(e);o(56);var n=o(577),r=(o(25),o(29),o(30),o(10)),l=(o(23),o(7),o(317),o(92),o(18),o(566),o(24),o(315),o(31),o(64),o(73),o(206)),c=o(152),d=o(555),_=o(564),m=o(556),v=o(12),h=o(538),f=o.n(h),address=o(208),k=(o(27),o(22));function y(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var S={data:function(){return{id:0,skuId:0,loading:!0,picZoomUrl:"",thumbPosition:0,moveThumbLeft:!1,moveThumbRight:!1,goodsSkuDetail:{video_url:""},evaluate_show:!1,discountText:"距离结束仅剩",discountTimeMachine:{currentTime:0,startTime:0,endTime:0},qrcode:"",specDisabled:!1,specBtnRepeat:!1,btnSwitch:!1,whetherCollection:0,couponList:[],couponBtnRepeat:!1,manjian:{},currentPage:1,pageSize:10,total:0,evaluaType:0,evaluteCount:{},goodsEvaluateList:[],bundling:[{bundling_goods:{bl_name:"",sku_image:""}}],service:null,service_is_display:"",number:1,tabName:"detail",tabBundling:"",playerOptions:{playbackRates:[.5,1,1.5,2,3],autoplay:!1,muted:!1,loop:!1,preload:"auto",language:"zh-CN",aspectRatio:"16:9",fluid:!0,sources:[{type:"video/mp4",src:""}],poster:"",notSupportedMessage:"此视频暂无法播放，请稍后再试",controlBar:{timeDivider:!0,durationDisplay:!0,remainingTimeDisplay:!0,fullscreenToggle:!0}},switchMedia:"img",hasFollow:!1,kefuConfig:{system:"",open_pc:"",open_url:""},provinceArr:{},cityArr:{},districtArr:{},currTabAddres:"province",hideRegion:!1,selectedAddress:{},service_list:[],cartListNum:0,listNum:!1,shopNum:"",seoInfo:{title:"",keywords:""},categoryNameArr:[]}},components:{CountDown:f.a},head:function(){return{title:this.seoInfo.title,meta:[{name:"description",content:this.seoInfo.title},{name:"keyword",content:this.seoInfo.keywords}]}},asyncData:function(t){var e=t.params;return Object(l.g)({sku_id:e.id}).then((function(t){if(0==t.code&&t.data){var data=t.data;return{seoInfo:{title:data.goods_name,keywords:data.keywords}}}})).catch((function(t){}))},created:function(){this.skuId=this.$route.params.id,this.getGoodsSkuDetail()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?y(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):y(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(v.b)(["token","siteInfo","defaultHeadImage","defaultShopImage","defaultGoodsImage","addonIsExit","locationRegion"])),watch:{$route:function(t){this.skuId=t.params.id,this.getGoodsSkuDetail()}},methods:{shopServiceOpen:function(){},service_link:function(){this.token?this.$refs.servicerMessage.show():this.$message({message:"您还未登录",type:"warning"})},tabChange:function(t,e){},bundlingChange:function(t,e){},getGoodsSkuDetail:function(){var t=this;Object(l.f)({sku_id:this.skuId}).then((function(e){var data=e.data;if(null!=data.goods_sku_detail){var o=data.goods_sku_detail.category_id.split(",");if(o=o.filter((function(t){return t&&t.trim()})),t.categorySearch(o[o.length-1]),t.goodsSkuDetail=data.goods_sku_detail,t.service_list=data.goods_sku_detail.goods_service,t.goodsSkuDetail.min_buy>0&&(t.number=t.goodsSkuDetail.min_buy),t.id=t.goodsSkuDetail.goods_id,0==t.skuId&&(t.skuId=t.goodsSkuDetail.sku_id),t.goodsSkuDetail.sku_images?t.goodsSkuDetail.sku_images=t.goodsSkuDetail.sku_images.split(","):t.goodsSkuDetail.sku_images=[],t.goodsSkuDetail.goods_spec_format&&t.goodsSkuDetail.goods_image&&(t.goodsSkuDetail.goods_image=t.goodsSkuDetail.goods_image.split(","),t.master_img=t.goodsSkuDetail.goods_image,t.goodsSkuDetail.sku_images=t.goodsSkuDetail.goods_image.concat(t.goodsSkuDetail.sku_images)),t.goodsSkuDetail.video_url&&(t.switchMedia="video",t.playerOptions.poster=t.$img(t.goodsSkuDetail.sku_images[0]),t.playerOptions.sources[0].src=t.$img(t.goodsSkuDetail.video_url)),t.picZoomUrl=t.goodsSkuDetail.sku_images[0],t.goodsSkuDetail.unit=t.goodsSkuDetail.unit||"件",t.goodsSkuDetail.sku_spec_format&&(t.goodsSkuDetail.sku_spec_format=JSON.parse(t.goodsSkuDetail.sku_spec_format)),t.goodsSkuDetail.goods_attr_format){var n=JSON.parse(t.goodsSkuDetail.goods_attr_format);t.goodsSkuDetail.goods_attr_format=t.$util.unique(n,"attr_id");for(var i=0;i<t.goodsSkuDetail.goods_attr_format.length;i++)for(var r=0;r<n.length;r++)t.goodsSkuDetail.goods_attr_format[i].attr_id==n[r].attr_id&&t.goodsSkuDetail.goods_attr_format[i].attr_value_id!=n[r].attr_value_id&&(t.goodsSkuDetail.goods_attr_format[i].attr_value_name+="、"+n[r].attr_value_name)}t.goodsSkuDetail.goods_spec_format&&(t.goodsSkuDetail.goods_spec_format=JSON.parse(t.goodsSkuDetail.goods_spec_format)),window.document.title="".concat(t.goodsSkuDetail.sku_name," - ").concat(t.siteInfo.site_name),1==t.goodsSkuDetail.promotion_type&&t.addonIsExit.discount&&(t.goodsSkuDetail.end_time-e.timestamp>0?t.discountTimeMachine={currentTime:e.timestamp,startTime:e.timestamp,endTime:t.goodsSkuDetail.end_time}:t.goodsSkuDetail.promotion_type=0),t.goodsSkuDetail.manjian&&t.getManjian(t.goodsSkuDetail.manjian),t.goodsSkuDetail.coupon_list&&(t.couponList=t.goodsSkuDetail.coupon_list,t.couponList.forEach((function(t){t.count==t.lead_count?t.useState=2:0!=t.max_fetch&&t.member_coupon_num&&t.member_coupon_num>=t.max_fetch?t.useState=1:t.useState=0})),t.couponList=t.couponList.sort(t.sortBy("useState"))),t.goodsSkuDetail.bundling_list&&t.handleBundlingData(t.goodsSkuDetail.bundling_list),t.shopServiceOpen(),t.evaluateConfig=t.goodsSkuDetail.evaluate_config,1==t.evaluateConfig.evaluate_show&&(t.evaluate_show=!0,t.goodsEvaluCount(),t.getGoodsEvaluate()),t.loading=!1}else t.$router.push("/")})).then((function(e){""!=t.token&&t.getWhetherCollection(),t.getAftersale(),t.modifyGoodsInfo(),t.getGoodsQrcode(),t.getAddress("province",null,!0),t.locationRegion||t.$store.commit("app/SET_LOCATION_REGION",{level_1:{id:11e4,pid:0,name:"北京市",shortname:"北京",longitude:"116.40529",latitude:"39.904987",level:1,sort:1,status:1,default_data:1},level_2:{id:110100,pid:11e4,name:"北京市",shortname:"北京",longitude:"116.40529",latitude:"39.904987",level:2,sort:1,status:1,default_data:1},level_3:{id:110101,pid:110100,name:"东城区",shortname:"东城",longitude:"116.418755",latitude:"39.917545",level:3,sort:3,status:1,default_data:1},master_img:[]}),t.selectedAddress=t.locationRegion,t.provinceId=t.selectedAddress.level_1.id,t.getAddress("city",null,!0,(function(){t.cityId=t.selectedAddress.level_2.id,t.cityId&&t.getAddress("district",null,!0)}))})).catch((function(e){t.loading=!1,t.$router.push("/")}))},categorySearch:function(t){var e=this;Object(c.b)({category_id:t}).then((function(t){if(0==t.code&&t.data){e.categoryNameArr=[];try{t.data.category_full_name.split("$_SPLIT_$").forEach((function(o,n){var r={};r.name=o,r.category_id=t.data["category_id_"+(n+1)],e.categoryNameArr.push(r)}))}catch(t){e.categoryNameArr=[]}}})).catch((function(t){}))},sortBy:function(t){return function(e,o){return e[t]-o[t]}},changeThumbImg:function(t){if(!(this.goodsSkuDetail.sku_images.length<4)){var e=this.goodsSkuDetail.sku_images.length%4;if(0==e)e=this.goodsSkuDetail.sku_images.length-4;else if(0!=e&&1!=e&&e<2)return;"prev"==t?0!=this.thumbPosition&&94!=Math.round(this.thumbPosition,2)&&(this.thumbPosition+=94):"next"==t&&Math.round(this.thumbPosition,2)!=-Math.round(94*e,2)&&(this.thumbPosition-=94)}},getWhetherCollection:function(){var t=this;Object(d.c)({goods_id:this.goodsSkuDetail.goods_id}).then((function(e){t.whetherCollection=e.data}))},editCollection:function(){var t=this;0==this.whetherCollection?Object(d.a)({sku_id:this.skuId,goods_id:this.goodsSkuDetail.goods_id}).then((function(e){e.data>0&&(t.whetherCollection=1,t.goodsSkuDetail.collect_num++)})).catch((function(e){"您尚未登录，请先进行登录"==e.message&&t.$router.push("/auth/login"),t.btnSwitch=!1})):Object(d.b)({goods_id:this.goodsSkuDetail.goods_id}).then((function(e){e.data>0&&(t.whetherCollection=0,t.goodsSkuDetail.collect_num--)})).catch((function(e){"您尚未登录，请先进行登录"==e.message&&t.$router.push("/auth/login"),t.btnSwitch=!1}))},getManjian:function(data){var t=this;this.manjian=data;var e=0==data.type?"元":"件";Object.keys(data.rule_json).forEach((function(o){var n=data.rule_json[o];if(n.coupon_data)for(var i=0;i<n.coupon_data.length;i++)n.coupon_data[i].coupon_num=n.coupon_num[i];if(n.limit=0==data.type?parseFloat(n.limit).toFixed(2):parseInt(n.limit),null!=n.discount_money&&(null==t.manjian.manjian?t.manjian.manjian="满"+n.limit+e+"减"+n.discount_money+"元":t.manjian.manjian+="；满"+n.limit+e+"减"+n.discount_money+"元"),null!=n.point||null!=n.coupon){var text="";null!=n.point&&(text="送"+n.point+"积分"),null!=n.coupon&&null!=n.coupon_data&&n.coupon_data.forEach((function(t,e){"discount"==t.type?""==text?text="送"+n.coupon_num[e]+"张"+parseFloat(t.discount)+"折优惠券":text+="、送"+n.coupon_num[e]+"张"+parseFloat(t.discount)+"折优惠券":""==text?text="送"+n.coupon_num[e]+"张"+parseFloat(t.money)+"元优惠券":text+="、送"+n.coupon_num[e]+"张"+parseFloat(t.money)+"元优惠券"})),null==t.manjian.mansong?t.manjian.mansong="满"+n.limit+e+text:t.manjian.mansong+="；满"+n.limit+e+text}null!=n.free_shipping&&(null==t.manjian.free_shipping?t.manjian.free_shipping="满"+n.limit+e+"包邮":t.manjian.free_shipping+="；满"+n.limit+e+"包邮")}))},receiveCoupon:function(t){var e=this;Object(k.a)()?this.couponBtnRepeat||(this.couponBtnRepeat=!0,Object(_.a)({coupon_type_id:t,site_id:this.goodsSkuDetail.site_id,get_type:2}).then((function(t){t.data;var o=t.message;0==t.code&&(o="领取成功"),e.$message({message:o,type:"success"}),e.couponBtnRepeat=!1})).catch((function(t){e.$message({message:t.message,type:"error"}),e.couponBtnRepeat=!1}))):this.$router.push("/auth/login")},handleBundlingData:function(data){if(this.bundling=data,this.bundling.length){this.tabBundling="bundling_"+this.bundling[0].bl_id;for(var i=0;i<this.bundling.length;i++){for(var t=[],e=0;e<this.bundling[i].bundling_goods.length;e++)this.bundling[i].bundling_goods[e].goods_id==this.goodsSkuDetail.goods_id?t.unshift(this.bundling[i].bundling_goods[e]):t.push(this.bundling[i].bundling_goods[e]);this.bundling[i].bundling_goods=t}}},evaluationType:function(t){this.evaluaType=t,this.getGoodsEvaluate()},getAftersale:function(){var t=this;Object(l.b)({}).then((function(e){if(0==e.code&&e.data){t.service_is_display=e.data;e.data.content;e.data.content&&(t.service=e.data)}}))},modifyGoodsInfo:function(){Object(l.i)({sku_id:this.skuId,site_id:this.goodsSkuDetail.site_id}),Object(l.a)({sku_id:this.skuId,goods_id:this.goodsSkuDetail.goods_id})},getGoodsQrcode:function(){var t=this;Object(l.d)({sku_id:this.skuId}).then((function(e){var data=e.data;data.path.h5.img&&(t.qrcode=t.$img(data.path.h5.img))}))},goodsEvaluCount:function(){var t=this;Object(m.a)({goods_id:this.goodsSkuDetail.goods_id}).then((function(e){0==e.code&&e.data&&(t.evaluteCount=e.data)}))},getGoodsEvaluate:function(){var t=this;Object(m.b)({page:this.currentPage,page_size:this.pageSize,goods_id:this.id,explain_type:0==this.evaluaType?"":this.evaluaType}).then((function(e){var o=[];0==e.code&&e.data&&(o=e.data.list,t.total=e.data.count);for(var i=0;i<o.length;i++){if(1==o[i].explain_type?o[i].star=5:2==o[i].explain_type?o[i].star=3:3==o[i].explain_type&&(o[i].star=1),o[i].images){o[i].images=o[i].images.split(","),o[i].imagesFormat=[];for(var n=0;n<o[i].images.length;n++)o[i].imagesFormat.push(t.$img(o[i].images[n]))}if(o[i].again_images){o[i].again_images=o[i].again_images.split(","),o[i].againImagesFormat=[];for(var r=0;r<o[i].again_images.length;r++)o[i].againImagesFormat.push(t.$img(o[i].again_images[r]))}1==o[i].is_anonymous&&(o[i].member_name=o[i].member_name.replace(o[i].member_name.substring(1,o[i].member_name.length-1),"***"))}t.goodsEvaluateList=o}))},imageErrorEvaluate:function(t){this.goodsEvaluateList[t].member_headimg=this.defaultHeadImage},handlePageSizeChange:function(t){this.pageSize=t,this.getGoodsEvaluate()},handleCurrentPageChange:function(t){this.currentPage=t,this.getGoodsEvaluate()},changeSpec:function(t,e){var o=this;if(!this.specDisabled){this.specBtnRepeat=!1,this.skuId=t;for(var i=0;i<this.goodsSkuDetail.goods_spec_format.length;i++)for(var n=this.goodsSkuDetail.goods_spec_format[i],r=0;r<n.value.length;r++)e==this.goodsSkuDetail.goods_spec_format[i].value[r].spec_id&&(this.goodsSkuDetail.goods_spec_format[i].value[r].selected=!1);Object(l.g)({sku_id:this.skuId}).then((function(t){var data=t.data;null!=data?(data.sku_images=data.sku_images.split(","),o.picZoomUrl=data.sku_images[0],o.playerOptions.poster=o.$img(data.sku_image),""==data.sku_images?(data.sku_images=o.master_img,o.picZoomUrl=data.sku_images[0],o.playerOptions.poster=o.$img(data.sku_image)):data.sku_images=data.sku_images.concat(o.master_img),data.sku_spec_format&&(data.sku_spec_format=JSON.parse(data.sku_spec_format)),data.goods_spec_format&&(data.goods_spec_format=JSON.parse(data.goods_spec_format)),data.goods_attr_format&&(data.goods_attr_format=JSON.parse(data.goods_attr_format)),o.keyInput(),1==data.promotion_type&&(o.discountTimeMachine={currentTime:t.timestamp,startTime:t.timestamp,endTime:data.end_time}),o.specBtnRepeat=!1,Object.assign(o.goodsSkuDetail,data)):o.$router.push("/")}))}},changeNum:function(t){if(0!=this.goodsSkuDetail.stock){var e=this.goodsSkuDetail.stock;if("+"==t){if(!(this.number<e))return;this.number++}else if("-"==t){if(!(this.number>1))return;this.number-=1}this.purchase()}},purchase:function(){if(this.goodsSkuDetail.min_buy>0&&this.number<this.goodsSkuDetail.min_buy&&(this.$message({message:"该商品起购"+this.goodsSkuDetail.min_buy+"件",type:"warning"}),this.number=this.goodsSkuDetail.min_buy),1==this.goodsSkuDetail.is_limit)if(1==this.goodsSkuDetail.limit_type)this.number>this.goodsSkuDetail.max_buy&&(this.$message({message:"该商品最多购买"+this.goodsSkuDetail.max_buy+"件",type:"warning"}),this.number=this.goodsSkuDetail.max_buy);else if(2==this.goodsSkuDetail.limit_type){this.shopNum=this.goodsSkuDetail.max_buy-this.goodsSkuDetail.purchased_num;var title="";0==this.goodsSkuDetail.purchased_num?title="该商品每人限购"+this.goodsSkuDetail.max_buy+"件":this.goodsSkuDetail.purchased_num>0&&(title="该商品每人限购"+this.goodsSkuDetail.max_buy+"件，您已购买"+this.goodsSkuDetail.purchased_num+"件"),this.number>this.shopNum&&(this.$message({message:title,type:"warning"}),this.number=this.shopNum)}},keyInput:function(){var t=this.goodsSkuDetail.stock;if(0!=this.goodsSkuDetail.stock){0!=this.number&&""!=this.number||(this.number=1);/^\d+$/.test(parseInt(this.number))?(this.number>t&&(this.number=t),this.number=parseInt(this.number)):this.number=1,this.purchase()}else this.number=0},onPlayerPlay:function(t){},onPlayerPause:function(t){},onPlayerEnded:function(t){},onPlayerWaiting:function(t){},onPlayerPlaying:function(t){},onPlayerLoadeddata:function(t){},onPlayerTimeupdate:function(t){},onPlayerCanplay:function(t){},onPlayerCanplaythrough:function(t){},playerStateChanged:function(t){},playerReadied:function(t){},joinCart:function(){var t=this;0!=this.goodsSkuDetail.stock?0!=this.number.length&&0!=this.number?this.btnSwitch||(this.btnSwitch=!0,this.$store.dispatch("cart/add_to_cart",{site_id:this.goodsSkuDetail.site_id,sku_id:this.goodsSkuDetail.sku_id,num:this.number}).then((function(e){e.data>0&&t.$message({message:"加入购物车成功",type:"success"}),t.btnSwitch=!1})).catch((function(e){"您尚未登录，请先进行登录"==e.message?t.$router.push("/auth/login"):t.$message.error(e.message),t.btnSwitch=!1}))):this.$message({message:"购买数量不能为0",type:"warning"}):this.$message({message:"商品已售罄",type:"warning"})},buyNow:function(){if(0!=this.goodsSkuDetail.stock)if(0!=this.number.length&&0!=this.number)if(parseInt(this.number)+parseInt(this.goodsSkuDetail.purchased_num)>this.goodsSkuDetail.max_buy&&0!=this.goodsSkuDetail.max_buy){var t=parseInt(this.goodsSkuDetail.max_buy)-parseInt(this.goodsSkuDetail.purchased_num);this.$message({message:"商品限购"+this.goodsSkuDetail.max_buy+"件，现在已经购买"+this.goodsSkuDetail.purchased_num+"件,还能购买"+t+"件",type:"warning"})}else{var data={sku_id:this.skuId,num:this.number};this.$store.dispatch("order/setOrderCreateData",data),this.$router.push({path:"/order/payment"})}else this.$message({message:"购买数量不能为0",type:"warning"});else this.$message({message:"商品已售罄",type:"warning"})},countDownS_cb:function(){},countDownE_cb:function(){this.discountText="活动已结束"},imageErrorSpec:function(t){this.goodsSkuDetail.sku_images[t]=this.defaultGoodsImage,this.picZoomUrl=this.defaultGoodsImage},getAddress:function(t,e,o,n){var r=this,l=0;switch(t){case"province":l=0;break;case"city":e&&(this.provinceId=e.id),l=this.provinceId,this.cityArr={},this.districtArr={};break;case"district":e&&(this.cityId=e.id),l=this.cityId,this.districtArr={}}if(e){if(e.level<=2)for(var i=e.level;i<=3;i++)delete this.selectedAddress["level_"+i];this.selectedAddress["level_"+e.level]=e}if(o||this.$store.commit("app/SET_LOCATION_REGION",this.selectedAddress),this.$forceUpdate(),"community"==t)return this.hideRegion=!0,void setTimeout((function(){r.hideRegion=!1}),10);Object(address.a)({pid:l}).then((function(e){e.code;var data=e.data;if(data){switch(t){case"province":r.provinceArr=data;break;case"city":r.cityArr=data;break;case"district":r.districtArr=data}r.currTabAddres=t,n&&n()}})).catch((function(t){}))}}},C=o(544),D={name:"detail",components:{PicZoom:n.a,GoodsRecommend:C.default},mixins:[S]},w=(o(745),o(6)),component=Object(w.a)(D,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"goods-detail-wrap"},[t.categoryNameArr?e("div",{staticClass:"detail-nav-wrap"},[t.categoryNameArr.length?e("div",{staticClass:"detail-nav"},[t._l(t.categoryNameArr,(function(o,n){return[e("router-link",{attrs:{to:{path:"/goods/list",query:{category_id:o.category_id,level:n}}}},[t._v(t._s(o.name)+"\n        ")]),t._v(" "),e("span",{staticClass:"iconfont icon-arrow-right"})]})),t._v(" "),e("span",{staticClass:"goods-name"},[t._v(t._s(t.goodsSkuDetail.goods_name))])],2):t._e()]):t._e(),t._v(" "),e("div",{staticClass:"detail-main"},[e("div",{staticClass:"goods-detail"},[e("div",{staticClass:"preview-wrap"},[""!=t.goodsSkuDetail.video_url?e("div",{staticClass:"video-player-wrap",class:{show:"video"==t.switchMedia}},[e("client-only",[""!=t.goodsSkuDetail.video_url?e("video-player",{ref:"videoPlayer",attrs:{playsinline:!0,options:t.playerOptions},on:{play:function(e){return t.onPlayerPlay(e)},pause:function(e){return t.onPlayerPause(e)},ended:function(e){return t.onPlayerEnded(e)},waiting:function(e){return t.onPlayerWaiting(e)},playing:function(e){return t.onPlayerPlaying(e)},loadeddata:function(e){return t.onPlayerLoadeddata(e)},timeupdate:function(e){return t.onPlayerTimeupdate(e)},canplay:function(e){return t.onPlayerCanplay(e)},canplaythrough:function(e){return t.onPlayerCanplaythrough(e)},statechanged:function(e){return t.playerStateChanged(e)},ready:t.playerReadied}}):t._e()],1),t._v(" "),""!=t.goodsSkuDetail.video_url?e("div",{staticClass:"media-mode"},[e("span",{class:{"ns-bg-color":"video"==t.switchMedia},on:{click:function(e){t.switchMedia="video"}}},[t._v("视频")]),t._v(" "),e("span",{class:{"ns-bg-color":"img"==t.switchMedia},on:{click:function(e){t.switchMedia="img"}}},[t._v("图片")])]):t._e()],1):t._e(),t._v(" "),e("div",{staticClass:"magnifier-wrap"},[e("pic-zoom",{ref:"PicZoom",attrs:{url:t.$img(t.picZoomUrl),scale:2}})],1),t._v(" "),e("div",{staticClass:"spec-items"},[e("span",{staticClass:"left-btn iconfont icon-weibiaoti35",class:{move:t.moveThumbLeft},on:{click:function(e){return t.changeThumbImg("prev")}}}),t._v(" "),e("span",{staticClass:"right-btn iconfont icon-weibiaoti35",class:{move:t.moveThumbRight},on:{click:function(e){return t.changeThumbImg("next")}}}),t._v(" "),e("ul",{style:{top:42+t.thumbPosition+"px"}},t._l(t.goodsSkuDetail.sku_images,(function(o,n){return e("li",{key:n,class:{selected:t.picZoomUrl==o},on:{mousemove:function(e){t.picZoomUrl=o}}},[e("img",{attrs:{src:t.$img(o,{size:"small"})},on:{error:function(e){return t.imageErrorSpec(n)}}})])})),0)])]),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"basic-info-wrap"},[e("h1",[t._v(t._s(t.goodsSkuDetail.goods_name))]),t._v(" "),t.goodsSkuDetail.introduction?e("p",{staticClass:"desc"},[t._v(t._s(t.goodsSkuDetail.introduction))]):t._e(),t._v(" "),1==t.goodsSkuDetail.promotion_type&&t.discountTimeMachine.currentTime&&t.addonIsExit.discount?e("div",{staticClass:"discount-banner ns-bg-color"},[t._m(0),t._v(" "),e("div",{staticClass:"surplus-time"},[e("span",[t._v(t._s(t.discountText))]),t._v(" "),e("count-down",{staticClass:"count-down",attrs:{currentTime:t.discountTimeMachine.currentTime,startTime:t.discountTimeMachine.startTime,endTime:t.discountTimeMachine.endTime,dayTxt:"天",hourTxt:"小时",minutesTxt:"分钟",secondsTxt:"秒"},on:{start_callback:function(e){return t.countDownS_cb()},end_callback:function(e){return t.countDownE_cb()}}})],1)]):t._e(),t._v(" "),e("div",{staticClass:"item-block"},[e("div",{staticClass:"promotion-price"},[e("dl",{staticClass:"item-line"},[e("dt",{staticClass:"ns-text-color-gray"},[t._v("销售价")]),t._v(" "),e("dd",[e("em",{staticClass:"yuan ns-text-color"},[t._v("¥")]),t._v(" "),e("span",{staticClass:"price ns-text-color"},[t._v(t._s(t.goodsSkuDetail.discount_price))])])]),t._v(" "),1==t.goodsSkuDetail.promotion_type&&t.discountTimeMachine.currentTime?e("dl",{staticClass:"item-line"},[e("dt",{staticClass:"ns-text-color-gray"},[t._v("原价")]),t._v(" "),e("dd",[e("em",{staticClass:"market-yuan"},[t._v("¥")]),t._v(" "),e("span",{staticClass:"market-price"},[t._v(t._s(t.goodsSkuDetail.price))])])]):t._e(),t._v(" "),t.goodsSkuDetail.member_price>0?e("dl",{staticClass:"item-line"},[e("dt",{staticClass:"ns-text-color-gray"},[t._v("会员价")]),t._v(" "),e("dd",[e("em",{staticClass:"market-yuan"},[t._v("¥")]),t._v(" "),e("span",{staticClass:"member_price"},[t._v(t._s(t.goodsSkuDetail.member_price))])])]):t._e(),t._v(" "),t.goodsSkuDetail.market_price>0?e("dl",{staticClass:"item-line"},[e("dt",{staticClass:"ns-text-color-gray"},[t._v("市场价")]),t._v(" "),e("dd",[e("em",{staticClass:"market-yuan"},[t._v("¥")]),t._v(" "),e("span",{staticClass:"market-price"},[t._v(t._s(t.goodsSkuDetail.market_price))])])]):t._e(),t._v(" "),t.addonIsExit.coupon&&t.couponList.length?e("dl",{staticClass:"item-line coupon-list"},[e("dt",{staticClass:"ns-text-color-gray"},[t._v("优惠券")]),t._v(" "),e("div",[e("dd",t._l(t.couponList,(function(o,n){return e("p",{key:n,staticClass:"ns-text-color",on:{click:function(e){return t.receiveCoupon(o.coupon_type_id)}}},["discount"==o.type?e("span",{staticClass:"ns-border-color"},[t._v(t._s(o.discount)+"折")]):t._e(),t._v(" "),"reward"==o.type?e("span",{staticClass:"ns-border-color"},[t._v("￥"+t._s(o.money))]):t._e()])})),0)])]):t._e(),t._v(" "),t.manjian.manjian?e("dl",{staticClass:"item-line manjian",staticStyle:{"align-items":"top"}},[e("dt",[t._v("满减")]),t._v(" "),e("dd",[e("i",{staticClass:"i-activity-flag ns-text-color ns-border-color"},[t._v(t._s(t.manjian.manjian_name))]),t._v(" "),e("span",[t._v(t._s(t.manjian.manjian))])])]):t._e(),t._v(" "),null!=t.manjian.mansong?e("dl",{staticClass:"item-line mansong"},[e("dt",[t._v("满送")]),t._v(" "),e("dd",[e("i",{staticClass:"i-activity-flag ns-text-color ns-border-color",staticStyle:{height:"14px","line-height":"14px","margin-top":"5px"}},[t._v(t._s(t.manjian.manjian_name))]),t._v(" "),e("span",[t._v(t._s(t.manjian.mansong))])])]):t._e(),t._v(" "),null!=t.manjian.free_shipping?e("dl",{staticClass:"item-line"},[e("dt",[t._v("包邮")]),t._v(" "),e("dd",[e("i",{staticClass:"i-activity-flag ns-text-color ns-border-color"},[t._v(t._s(t.manjian.free_shipping))])])]):t._e()])]),t._v(" "),0==t.goodsSkuDetail.is_virtual?e("dl",{staticClass:"item-line delivery"},[e("dt",[t._v("配送至")]),t._v(" "),e("dd",[e("div",{staticClass:"region-selected ns-border-color-gray"},[e("span",[t.selectedAddress.level_1?[t._l(t.selectedAddress,(function(e){return[t._v("\n                    "+t._s(e.name)+"\n                  ")]}))]:[t._v("\n                  请选择配送地址\n                ")]],2),t._v(" "),e("i",{staticClass:"el-icon-arrow-down"})]),t._v(" "),e("div",{staticClass:"region-list ns-border-color-gray",class:{hide:t.hideRegion}},[e("ul",{staticClass:"nav-tabs"},[e("li",{class:{active:"province"==t.currTabAddres},on:{click:function(e){t.currTabAddres="province"}}},[e("div",[e("span",[t._v(t._s(t.selectedAddress.level_1?t.selectedAddress.level_1.name:"请选择省"))]),t._v(" "),e("i",{staticClass:"el-icon-arrow-down"})])]),t._v(" "),e("li",{class:{active:"city"==t.currTabAddres},on:{click:function(e){t.currTabAddres="city"}}},[e("div",[e("span",[t._v(t._s(t.selectedAddress.level_2?t.selectedAddress.level_2.name:"请选择市"))]),t._v(" "),e("i",{staticClass:"el-icon-arrow-down"})])]),t._v(" "),e("li",{class:{active:"district"==t.currTabAddres},on:{click:function(e){t.currTabAddres="district"}}},[e("div",[e("span",[t._v(t._s(t.selectedAddress.level_3?t.selectedAddress.level_3.name:"请选择区/县"))]),t._v(" "),e("i",{staticClass:"el-icon-arrow-down"})])])]),t._v(" "),e("div",{staticClass:"tab-content"},[e("div",{staticClass:"tab-pane",class:{active:"province"==t.currTabAddres}},[e("ul",{staticClass:"province"},t._l(t.provinceArr,(function(o,n){return e("li",{key:n,class:{selected:t.selectedAddress["level_"+o.level]&&t.selectedAddress["level_"+o.level].id==o.id}},[e("span",{on:{click:function(e){return t.getAddress("city",o)}}},[t._v(t._s(o.name))])])})),0)]),t._v(" "),e("div",{staticClass:"tab-pane",class:{active:"city"==t.currTabAddres}},[e("ul",{staticClass:"city"},t._l(t.cityArr,(function(o,n){return e("li",{key:n,class:{selected:t.selectedAddress["level_"+o.level]&&t.selectedAddress["level_"+o.level].id==o.id}},[e("span",{on:{click:function(e){return t.getAddress("district",o)}}},[t._v(t._s(o.name))])])})),0)]),t._v(" "),e("div",{staticClass:"tab-pane",class:{active:"district"==t.currTabAddres}},[e("ul",{staticClass:"district"},t._l(t.districtArr,(function(o,n){return e("li",{key:n,class:{selected:t.selectedAddress["level_"+o.level]&&t.selectedAddress["level_"+o.level].id==o.id}},[e("span",{on:{click:function(e){return t.getAddress("community",o)}}},[t._v(t._s(o.name))])])})),0)])])])])]):t._e(),t._v(" "),e("dl",{staticClass:"item-line service"},[e("dt",[t._v("服务")]),t._v(" "),e("dd",[e("span",[t._v("\n              由\n              "),e("span",{staticClass:"ns-text-color"},[t._v(t._s(t.siteInfo.site_name))]),t._v("\n              发货并提供售后服务\n            ")])])]),t._v(" "),e("hr",{staticClass:"divider"}),t._v(" "),t.goodsSkuDetail.goods_spec_format?e("div",{staticClass:"sku-list"},t._l(t.goodsSkuDetail.goods_spec_format,(function(o,n){return e("dl",{key:n,staticClass:"item-line"},[e("dt",[t._v(t._s(o.spec_name))]),t._v(" "),e("dd",[e("ul",t._l(o.value,(function(o,n){return e("li",{key:n},[e("div",{class:{"selected ns-border-color":o.selected||t.skuId==o.sku_id,disabled:o.disabled||!o.selected&&t.specDisabled},on:{click:function(e){return t.changeSpec(o.sku_id,o.spec_id)}}},[o.image?e("img",{attrs:{src:t.$img(o.image,{size:"small"})}}):t._e(),t._v(" "),e("span",[t._v(t._s(o.spec_value_name))])])])})),0)])])})),0):t._e(),t._v(" "),e("div",{staticClass:"buy-number"},[e("dl",{staticClass:"item-line"},[e("dt",[t._v("数量")]),t._v(" "),e("dd",[e("div",{staticClass:"num-wrap"},[e("div",{staticClass:"operation"},[e("span",{staticClass:"decrease el-icon-minus",on:{click:function(e){return t.changeNum("-")}}}),t._v(" "),e("el-input",{attrs:{placeholder:"0"},on:{input:function(e){return t.keyInput()}},model:{value:t.number,callback:function(e){t.number=e},expression:"number"}}),t._v(" "),e("span",{staticClass:"increase el-icon-plus",on:{click:function(e){return t.changeNum("+")}}})],1)]),t._v(" "),e("span",{staticClass:"unit"},[t._v(t._s(t.goodsSkuDetail.unit))]),t._v(" "),t.goodsSkuDetail.stock_show?e("span",{staticClass:"inventory"},[t._v("库存"+t._s(t.goodsSkuDetail.stock)+t._s(t.goodsSkuDetail.unit))]):t._e(),t._v(" "),1==t.goodsSkuDetail.is_limit&&t.goodsSkuDetail.max_buy>0||t.goodsSkuDetail.min_buy>1?e("em",{staticClass:"restrictions"},[1==t.goodsSkuDetail.is_limit&&t.goodsSkuDetail.max_buy>0&&t.goodsSkuDetail.min_buy>1?e("span",[t._v("("+t._s(t.goodsSkuDetail.min_buy)+t._s(t.goodsSkuDetail.unit)+"起售，限购"+t._s(t.goodsSkuDetail.max_buy)+t._s(t.goodsSkuDetail.unit)+")")]):1==t.goodsSkuDetail.is_limit&&t.goodsSkuDetail.max_buy>0?e("span",[t._v("(限购"+t._s(t.goodsSkuDetail.max_buy)+t._s(t.goodsSkuDetail.unit)+")")]):t.goodsSkuDetail.min_buy>1?e("span",[t._v("("+t._s(t.goodsSkuDetail.min_buy)+t._s(t.goodsSkuDetail.unit)+"起售)")]):t._e()]):t._e()])])]),t._v(" "),e("dl",{staticClass:"item-line buy-btn"},[e("dt"),t._v(" "),1==t.goodsSkuDetail.goods_state?e("dd",[0==t.goodsSkuDetail.stock?[e("el-button",{attrs:{type:"info",plain:"",disabled:""}},[t._v("库存不足")])]:0!=t.goodsSkuDetail.max_buy&&t.goodsSkuDetail.purchased_num>=t.goodsSkuDetail.max_buy?[e("el-button",{attrs:{type:"info",plain:"",disabled:""}},[t._v("已达最大限购数量")])]:[e("el-button",{attrs:{type:"primary",plain:""},on:{click:t.buyNow}},[t._v("立即购买")]),t._v(" "),0==t.goodsSkuDetail.is_virtual?e("el-button",{attrs:{type:"primary",icon:"el-icon-shopping-cart-2"},on:{click:t.joinCart}},[t._v("加入购物车")]):t._e()],t._v(" "),e("div",{staticClass:"go-phone icon-item",on:{click:t.editCollection}},[e("span",{class:["iconfont",1==t.whetherCollection?"icon-_shouzang2 selected":"icon-shouzang"]}),t._v(" "),e("span",[t._v("收藏")])]),t._v(" "),e("div",{staticClass:"go-phone",attrs:{href:"javascript:;"}},[e("img",{attrs:{src:o(652)}}),t._v(" "),e("span",[t._v("二维码")]),t._v(" "),e("div",{staticClass:"qrcode-wrap"},[e("img",{attrs:{src:t.qrcode,alt:"二维码图片"}})])])],2):e("dd",[[e("el-button",{attrs:{type:"info",plain:"",disabled:""}},[t._v("该商品已下架")])],t._v(" "),e("div",{staticClass:"go-phone icon-item",on:{click:t.editCollection}},[e("span",{class:["iconfont",1==t.whetherCollection?"icon-_shouzang2 selected":"icon-shouzang"]}),t._v(" "),e("span",[t._v("收藏")])]),t._v(" "),e("div",{staticClass:"go-phone",attrs:{href:"javascript:;"}},[e("img",{attrs:{src:o(652)}}),t._v(" "),e("span",[t._v("二维码")]),t._v(" "),e("div",{staticClass:"qrcode-wrap"},[e("img",{attrs:{src:t.qrcode,alt:"二维码图片"}})])])],2)]),t._v(" "),e("dl",{directives:[{name:"show",rawName:"v-show",value:t.service_list.length,expression:"service_list.length"}],staticClass:"item-line merchant-service"},[e("dt",[t._v("商品服务")]),t._v(" "),e("div",t._l(t.service_list,(function(o){return e("dd",[e("i",{staticClass:"el-icon-success"}),t._v(" "),e("span",{staticClass:"ns-text-color-gray",attrs:{title:o.service_name}},[t._v(t._s(o.service_name))])])})),0)])]),t._v(" "),t.addonIsExit.bundling&&t.bundling.length&&t.bundling[0].bl_name?e("el-tabs",{staticClass:"bundling-wrap",on:{"tab-click":t.bundlingChange},model:{value:t.tabBundling,callback:function(e){t.tabBundling=e},expression:"tabBundling"}},t._l(t.bundling,(function(o,n){return e("el-tab-pane",{key:n,attrs:{label:o.bl_name,name:"bundling_"+o.bl_id}},[e("div",{staticClass:"operation"},[e("div",{staticClass:"price-wrap"},[e("span",[t._v("组合套餐价")]),t._v(" "),e("strong",{staticClass:"bl-price ns-text-color"},[t._v("￥"+t._s(o.bl_price))])]),t._v(" "),e("el-button",{attrs:{type:"primary",size:"medium"},on:{click:function(e){return t.$router.push("/promotion/combo/"+o.bl_id)}}},[t._v("立即购买")]),t._v(" "),e("i",{staticClass:"equal"},[t._v("=")])],1),t._v(" "),e("div",{staticClass:"suits"},[e("ul",t._l(o.bundling_goods,(function(o,n){return e("li",{key:n,on:{click:function(e){return t.$util.pushToTab({path:"/sku/"+o.sku_id})}}},[e("div",{staticClass:"sku-img"},[e("img",{attrs:{src:t.$img(o.sku_image,{size:"mid"})}})]),t._v(" "),e("div",{staticClass:"sku-name"},[t._v(t._s(o.sku_name))]),t._v(" "),e("div",{staticClass:"sku-price ns-text-color"},[t._v("￥"+t._s(o.price))])])})),0)])])})),1):t._e(),t._v(" "),e("div",{staticClass:"detail-wrap"},[e("el-tabs",{staticClass:"goods-tab",attrs:{type:"card"},on:{"tab-click":t.tabChange},model:{value:t.tabName,callback:function(e){t.tabName=e},expression:"tabName"}},[e("el-tab-pane",{attrs:{label:"商品详情",name:"detail"}},[e("div",{domProps:{innerHTML:t._s(t.goodsSkuDetail.goods_content)}})]),t._v(" "),e("el-tab-pane",{attrs:{label:"商品属性",name:"attr"}},[e("ul",{staticClass:"attr-list"},[t.goodsSkuDetail.goods_attr_format&&t.goodsSkuDetail.goods_attr_format.length>0?t._l(t.goodsSkuDetail.goods_attr_format,(function(o,n){return e("li",{key:n},[t._v(t._s(o.attr_name)+"："+t._s(o.attr_value_name))])})):t._e()],2)]),t._v(" "),t.evaluate_show?e("el-tab-pane",{staticClass:"evaluate",attrs:{label:t.evaluteCount.total?"商品评价("+t.evaluteCount.total+")":"商品评价",name:"evaluate"}},[t.evaluteCount.total?[e("nav",[e("li",{class:0==t.evaluaType?"selected":"",on:{click:function(e){return t.evaluationType(0)}}},[t._v("全部评价("+t._s(t.evaluteCount.total)+")")]),t._v(" "),e("li",{class:1==t.evaluaType?"selected":"",on:{click:function(e){return t.evaluationType(1)}}},[t._v("好评("+t._s(t.evaluteCount.haoping)+")")]),t._v(" "),e("li",{class:2==t.evaluaType?"selected":"",on:{click:function(e){return t.evaluationType(2)}}},[t._v("中评("+t._s(t.evaluteCount.zhongping)+")")]),t._v(" "),e("li",{class:3==t.evaluaType?"selected":"",on:{click:function(e){return t.evaluationType(3)}}},[t._v("差评("+t._s(t.evaluteCount.chaping)+")")])]),t._v(" "),e("ul",{staticClass:"list"},t._l(t.goodsEvaluateList,(function(o,n){return e("li",{key:n},[e("div",{staticClass:"member-info"},[e("img",{staticClass:"avatar",attrs:{src:t.$img(o.member_headimg)},on:{error:function(e){return t.imageErrorEvaluate(n)}}}),t._v(" "),e("span",[t._v(t._s(o.member_name))])]),t._v(" "),e("div",{staticClass:"info-wrap"},[e("el-rate",{attrs:{disabled:""},model:{value:o.star,callback:function(e){t.$set(o,"star",e)},expression:"item.star"}}),t._v(" "),e("p",{staticClass:"content"},[t._v(t._s(o.content))]),t._v(" "),o.images?e("div",{staticClass:"img-list"},t._l(o.images,(function(img,n){return e("el-image",{key:n,attrs:{src:t.$img(img),"preview-src-list":o.imagesFormat}})})),1):t._e(),t._v(" "),e("div",{staticClass:"sku-info"},[e("span",[t._v(t._s(o.sku_name))]),t._v(" "),e("span",{staticClass:"create-time"},[t._v(t._s(t.$util.timeStampTurnTime(o.create_time)))])]),t._v(" "),""!=o.explain_first?e("div",{staticClass:"evaluation-reply"},[t._v("店家回复："+t._s(o.explain_first))]):t._e(),t._v(" "),1==o.again_is_audit?[e("div",{staticClass:"review-evaluation"},[e("span",[t._v("追加评价")]),t._v(" "),e("span",{staticClass:"review-time"},[t._v(t._s(t.$util.timeStampTurnTime(o.again_time)))])]),t._v(" "),e("p",{staticClass:"content"},[t._v(t._s(o.again_content))]),t._v(" "),e("div",{staticClass:"img-list"},t._l(o.again_images,(function(n,r){return e("el-image",{key:r,attrs:{src:t.$img(n),"preview-src-list":o.againImagesFormat}})})),1),t._v(" "),""!=o.again_explain?e("div",{staticClass:"evaluation-reply"},[t._v("店家回复："+t._s(o.again_explain)+"\n                      ")]):t._e()]:t._e()],2)])})),0),t._v(" "),e("div",{staticClass:"pager"},[e("el-pagination",{attrs:{background:"","pager-count":5,total:t.total,"prev-text":"上一页","next-text":"下一页","current-page":t.currentPage,"page-size":t.pageSize,"hide-on-single-page":""},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handlePageSizeChange,"current-change":t.handleCurrentPageChange}})],1)]:e("div",{staticClass:"empty"},[t._v("该商品暂无评价哦")])],2):t._e(),t._v(" "),t.service?[1==t.service_is_display.is_display?e("el-tab-pane",{staticClass:"after-sale",attrs:{label:t.service.title,name:"after_sale"}},[e("div",{domProps:{innerHTML:t._s(t.service.content)}})]):t._e()]:t._e()],2)],1)],1)])])}),[function(){var t=this,e=t._self._c;return e("div",{staticClass:"activity-name"},[e("i",{staticClass:"discount-icon iconfont icon-icon_naozhong"}),t._v(" "),e("span",[t._v("限时折扣")])])}],!1,null,null,null);e.default=component.exports}}]);