(window.webpackJsonp=window.webpackJsonp||[]).push([[57],{540:function(e,t,r){"use strict";r.d(t,"h",(function(){return o})),r.d(t,"i",(function(){return d})),r.d(t,"g",(function(){return c})),r.d(t,"f",(function(){return l})),r.d(t,"e",(function(){return f})),r.d(t,"a",(function(){return m})),r.d(t,"d",(function(){return v})),r.d(t,"b",(function(){return h})),r.d(t,"c",(function(){return _})),r.d(t,"j",(function(){return y}));var n=r(1);function o(e){return Object(n.a)({url:"/api/orderrefund/refundData",data:e,forceLogin:!0})}function d(e){return Object(n.a)({url:"/api/orderrefund/refundDataBatch",data:e,forceLogin:!0})}function c(e){return Object(n.a)({url:"/api/orderrefund/refund",data:e,forceLogin:!0})}function l(e){return Object(n.a)({url:"/api/orderrefund/detail",data:e,forceLogin:!0})}function f(e){return Object(n.a)({url:"/api/orderrefund/delivery",data:e,forceLogin:!0})}function m(e){return Object(n.a)({url:"/api/orderrefund/cancel",data:e,forceLogin:!0})}function v(e){return Object(n.a)({url:"/api/ordercomplain/detail",data:e,forceLogin:!0})}function h(e){return Object(n.a)({url:"/api/ordercomplain/complain",data:e,forceLogin:!0})}function _(e){return Object(n.a)({url:"/api/ordercomplain/cancel",data:e,forceLogin:!0})}function y(e){return Object(n.a)({url:"/api/orderrefund/lists",data:e,forceLogin:!0})}},627:function(e,t,r){},628:function(e,t,r){},720:function(e,t,r){"use strict";r(627)},721:function(e,t,r){"use strict";r(628)},796:function(e,t,r){"use strict";r.r(t);r(24),r(25),r(23),r(29),r(18),r(30);var n=r(10),o=(r(73),r(210),r(92),r(7),r(74),r(540)),d=r(12),c=r(33);function l(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}var f={name:"refund",components:{},data:function(){return{orderGoodsId:"",orderId:"",refundType:1,refundReason:"",refundRemark:"",isIphoneX:!1,refundData:{refund_type:[],order_goods_info:{sku_image:""}},isSub:!1,show_type:0,detail:{refund_action:[]},loading:!0,yes:!0,dialogVisible:!1,hide:!1,uploadActionUrl:c.a.baseUrl+"/api/upload/refundimg",uploadData:{app_type:"pc",app_type_name:"PC"},imgList:[],dialogImageUrl:""}},created:function(){this.$route.query.order_goods_id&&(this.orderGoodsId=this.$route.query.order_goods_id),this.$route.query.order_id&&(this.orderId=this.$route.query.order_id),this.getRefundData()},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(d.b)(["defaultGoodsImage"])),layout:"member",mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},methods:{handleRemove:function(e,t){var i=this.$util.inArray(e.response.data.pic_path,this.imgList);this.imgList.splice(i,1),this.imgList.length<5&&(this.hide=!1)},handleSuccess:function(e,t){var r=this.imgList;r=r.concat(e.data.pic_path),this.imgList=r,this.imgList.length>=5&&(this.hide=!0)},handleExceed:function(e,t){this.$message.warning("上传图片最大数量为5张")},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},selectRefundType:function(e){this.refundType=e},getRefundData:function(){var e=this;Object(o.h)({order_goods_id:this.orderGoodsId}).then((function(t){var code=t.code,data=(t.message,t.data);code>=0?e.refundData=data:e.$message({message:"未获取到该订单项退款信息！",type:"warning",duration:2e3,onClose:function(){e.$router.push({path:"/member/activist"})}}),e.loading=!1})).catch((function(t){e.loading=!1,e.$message.error({message:t.message,duration:2e3,onClose:function(){e.$router.push({path:"/member/activist"})}})}))},submit:function(){var e=this;if(this.verify()){if(this.isSub)return;this.isSub=!0;var t={order_goods_ids:this.orderGoodsId,refund_type:this.refundType,refund_reason:this.refundReason,refund_remark:this.refundRemark,refund_images:this.imgList.toString()};Object(o.g)(t).then((function(t){var code=t.code,r=t.message;t.data;code>=0?e.$message({message:"申请成功！",type:"success",duration:2e3,onClose:function(){e.$router.push({path:"/member/activist"})}}):(e.isSub=!1,e.$message({message:r,type:"warning"}))})).catch((function(t){e.$message.error({message:t.message,duration:2e3,onClose:function(){e.$router.push({path:"/member/activist"})}})}))}},verify:function(){return""!=this.refundReason||(this.$message({message:"请选择退款原因",type:"warning"}),!1)}}},m=f,v=(r(720),r(721),r(6)),component=Object(v.a)(m,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t("el-card",{staticClass:"box-card order-list"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("el-breadcrumb",{attrs:{separator:"/"}},[t("el-breadcrumb-item",{attrs:{to:{path:"/member/order_list"}}},[e._v("我的订单")]),e._v(" "),t("el-breadcrumb-item",{attrs:{to:{path:"/member/order_detail?order_id="+e.orderId}}},[e._v("订单详情")]),e._v(" "),t("el-breadcrumb-item",[e._v("退款")])],1)],1),e._v(" "),t("div",{staticClass:"goods-list"},[t("table",[t("tr",[t("td",{attrs:{width:"62.5%"}},[e._v("商品")]),e._v(" "),t("td",{attrs:{width:"12.5%"}},[e._v("数量")]),e._v(" "),t("td",{attrs:{width:"12.5%"}},[e._v("金额")])])])]),e._v(" "),t("div",{staticClass:"goods-list"},[t("table",[t("tr",[t("td",{attrs:{width:"62.5%"}},[t("div",{staticClass:"goods-info"},[t("div",{staticClass:"goods-info-left"},[t("router-link",{attrs:{to:{path:"/sku/"+e.refundData.order_goods_info.sku_id},target:"_blank"}},[t("img",{staticClass:"goods-img",attrs:{src:e.$img(e.refundData.order_goods_info.sku_image,{size:"mid"})},on:{error:function(t){e.refundData.order_goods_info.sku_image=e.defaultGoodsImage}}})])],1),e._v(" "),t("div",{staticClass:"goods-info-right"},[t("router-link",{attrs:{to:{path:"/sku/"+e.refundData.order_goods_info.sku_id},target:"_blank"}},[t("div",{staticClass:"goods-name"},[e._v(e._s(e.refundData.order_goods_info.sku_name))])])],1)])]),e._v(" "),t("td",{staticClass:"goods-num",attrs:{width:"12.5%"}},[e._v(e._s(e.refundData.order_goods_info.num))]),e._v(" "),t("td",{staticClass:"goods-money",attrs:{width:"12.5%"}},[e._v("￥"+e._s(e.refundData.order_goods_info.goods_money))])])])])]),e._v(" "),t("div",{staticClass:"item-block"},[t("div",{staticClass:"block-text"},[e._v("退款类型")]),e._v(" "),t("div",{staticClass:"pay-type-list"},[t("div",{staticClass:"pay-type-item",class:1==e.refundType?"active":"",on:{click:function(t){return e.selectRefundType(1)}}},[e._v("退款无需退货")]),e._v(" "),2==e.refundData.refund_type.length?t("div",{staticClass:"pay-type-item",class:2==e.refundType?"active":"",on:{click:function(t){return e.selectRefundType(2)}}},[e._v("退货退款")]):e._e(),e._v(" "),t("div",{staticClass:"clear"})])]),e._v(" "),t("div",{staticClass:"item-block"},[t("div",{staticClass:"block-text"}),e._v(" "),t("el-form",{ref:"form",staticClass:"refund-form",attrs:{"label-width":"80px"}},[t("el-form-item",{attrs:{label:"退款金额"}},[t("el-input",{attrs:{disabled:"",value:e.refundData.refund_money}})],1),e._v(" "),t("el-form-item",{attrs:{label:"退款原因"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.refundReason,callback:function(t){e.refundReason=t},expression:"refundReason"}},e._l(e.refundData.refund_reason_type,(function(e,r){return t("el-option",{key:r,attrs:{label:e,value:e}})})),1)],1),e._v(" "),t("el-form-item",{attrs:{label:"退款说明"}},[t("el-input",{attrs:{maxlength:"140","show-word-limit":"",resize:"none",rows:"5",placeholder:"请输入退款说明（选填）",type:"textarea"},model:{value:e.refundRemark,callback:function(t){e.refundRemark=t},expression:"refundRemark"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"退款图片"}},[t("div",{staticClass:"upload-wrap"},[t("el-upload",{ref:"upload",class:{ishide:e.hide},attrs:{action:e.uploadActionUrl,data:e.uploadData,"list-type":"picture-card","on-success":function(t,r){return e.handleSuccess(t,r)},"on-preview":e.handlePictureCardPreview,"on-remove":function(t,r){return e.handleRemove(t,r)},"on-exceed":e.handleExceed,multiple:"",drag:"",limit:5}},[t("i",{staticClass:"el-icon-plus"})]),e._v(" "),t("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})]),e._v(" "),t("div",{staticClass:"tips"},[e._v("共5张，还能上传"+e._s(e.imgList.length?5-e.imgList.length:5)+"张")])],1)])],1)],1),e._v(" "),t("div",{staticClass:"item-block"},[t("div",{staticClass:"order-submit"},[t("el-button",{staticClass:"el-button--primary",attrs:{type:"primary"},on:{click:e.submit}},[e._v("提交")])],1),e._v(" "),t("div",{staticClass:"clear"})])],1)])}),[],!1,null,"01a24785",null);t.default=component.exports}}]);