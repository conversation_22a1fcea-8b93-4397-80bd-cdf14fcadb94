(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-list"],{"03ec":function(t,e,i){"use strict";i.r(e);var a=i("fc38"),r=i("37e5");for(var s in r)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(s);i("c7cb");var n=i("828b"),o=Object(n["a"])(r["default"],a["b"],a["c"],!1,null,"c121b056",null,!1,a["a"],void 0);e["default"]=o.exports},"0573":function(t,e,i){"use strict";var a=i("6194"),r=i.n(a);r.a},1241:function(t,e,i){"use strict";i.r(e);var a=i("9f5f"),r=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},1641:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uniTable:i("03ec").default,uniPopup:i("2166").default,selectLay:i("3523").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"userlist"},[a("v-uni-view",{staticClass:"userlist-box"},[a("v-uni-view",{staticClass:"userlist-left"},[a("v-uni-view",{staticClass:"user-title"},[t._v("员工"),a("v-uni-text",{staticClass:"iconfont icongengduo1"})],1),a("v-uni-view",{staticClass:"user-search"},[a("v-uni-view",{staticClass:"search"},[a("v-uni-text",{staticClass:"iconfont icon31sousuo"}),a("v-uni-input",{attrs:{type:"text",placeholder:"请输入员工名称/手机号"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)}},model:{value:t.search_text,callback:function(e){t.search_text=e},expression:"search_text"}})],1)],1),a("v-uni-view",{staticClass:"user-list-wrap"},[t.list.length>0?[a("v-uni-scroll-view",{staticClass:"user-list-scroll all-scroll",attrs:{"scroll-top":t.scrollTop,"scroll-y":"true"},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.scroll.apply(void 0,arguments)},scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.getUserListFn.apply(void 0,arguments)}}},t._l(t.list,(function(e,i){return a("v-uni-view",{key:i,staticClass:"item",class:i==t.selectUserKeys?"itemhover":"",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.userSelect(e,i)}}},[a("v-uni-image",{attrs:{src:t.$util.img(t.defaultImg.head),mode:"aspectFit"}}),a("v-uni-view",{staticClass:"item-right"},[a("v-uni-view",[a("v-uni-view",{staticClass:"user-name"},[t._v(t._s(e.username))]),a("v-uni-view",{staticClass:"user-money"},[t._v(t._s(e.group_name))])],1),a("v-uni-view",[a("v-uni-view",{staticClass:"user-status"},[t._v(t._s(e.status?"正常":"锁定"))]),a("v-uni-view",{staticClass:"login-time"},[t._v(t._s(e.login_time?t.$util.timeFormat(e.login_time):"--"))])],1)],1)],1)})),1)]:t.one_judge||0!=t.list.length?t._e():a("v-uni-view",{staticClass:"notYet"},[t._v("暂无员工")])],2),a("v-uni-view",{staticClass:"add-user"},[a("v-uni-button",{staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addUser.apply(void 0,arguments)}}},[t._v("添加员工")])],1)],1),a("v-uni-view",{staticClass:"userlist-right"},[a("v-uni-view",{staticClass:"user-title"},[t._v("员工详情")]),a("v-uni-view",{staticClass:"user-information"},["{}"!=JSON.stringify(t.detail)?[a("v-uni-view",{staticClass:"title"},[t._v("基本信息")]),a("v-uni-view",{staticClass:"information-box"},[a("v-uni-view",{staticClass:"box-left"},[a("v-uni-view",{staticClass:"information"},[a("v-uni-view",[t._v("员工名称：")]),a("v-uni-view",[t._v(t._s(t.detail.username))])],1),a("v-uni-view",{staticClass:"information"},[a("v-uni-view",[t._v("员工角色：")]),a("v-uni-view",[t._v(t._s(t.detail.group_name))])],1),a("v-uni-view",{staticClass:"information"},[a("v-uni-view",[t._v("员工状态：")]),a("v-uni-view",[t._v(t._s(t.detail.status?"正常":"锁定"))])],1),a("v-uni-view",{staticClass:"information"},[a("v-uni-view",[t._v("最后登录IP：")]),a("v-uni-view",[t._v(t._s(t.detail.login_ip?t.detail.login_ip:"--"))])],1),a("v-uni-view",{staticClass:"information"},[a("v-uni-view",[t._v("最后登录时间：")]),a("v-uni-view",[t._v(t._s(t.detail.login_time?t.$util.timeFormat(t.detail.login_time):"--"))])],1)],1),a("v-uni-image",{staticClass:"user-img",attrs:{src:t.$util.img(t.defaultImg.head),mode:"widthFix"}})],1),a("v-uni-view",{staticClass:"title"},[t._v("操作日志")]),a("v-uni-view",[a("uni-table",{attrs:{url:"/cashier/storeapi/user/userlog",cols:t.logCols,option:{uid:t.detail.uid},pagesize:7}})],1)]:[a("v-uni-image",{staticClass:"cart-empty",attrs:{src:i("4dd3"),mode:"widthFix"}})]],2),!t.detail||0!=t.detail.is_admin&&0!=t.detail.is_system?t._e():a("v-uni-view",{staticClass:"button-box flex justify-end"},[a("v-uni-button",{staticClass:"default-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.deletePop.open()}}},[t._v("删除")]),a("v-uni-button",{staticClass:"default-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editUserAction(t.detail.uid)}}},[t._v("修改")])],1)],1),a("uni-popup",{ref:"addUserPop"},[a("v-uni-view",{staticClass:"pop-box"},[a("v-uni-view",{staticClass:"pop-header"},[t._v(t._s(parseInt(t.formData.uid)>0?"修改":"添加")+"员工"),a("v-uni-view",{staticClass:"pop-header-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancelAddUser()}}},[a("v-uni-text",{staticClass:"iconguanbi1 iconfont"})],1)],1),a("v-uni-view",{staticClass:"common-scrollbar pop-content"},[a("v-uni-view",{staticClass:"form-content"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("用户名：")],1),a("v-uni-view",{staticClass:"form-inline search-wrap"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",disabled:parseInt(t.formData.uid)>0,placeholder:"请输入用户名"},model:{value:t.formData.username,callback:function(e){t.$set(t.formData,"username",e)},expression:"formData.username"}})],1)],1),parseInt(t.formData.uid)?a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"}),t._v("状态：")],1),a("v-uni-view",{staticClass:"form-inline search-wrap"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.statusChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==t.formData.status}}),t._v("正常")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"0",checked:0==t.formData.status}}),t._v("锁定")],1)],1)],1)],1):a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"}),t._v("密码：")],1),a("v-uni-view",{staticClass:"form-inline search-wrap"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入密码"},model:{value:t.formData.password,callback:function(e){t.$set(t.formData,"password",e)},expression:"formData.password"}})],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-view",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"}),t._v("员工角色：")],1),a("v-uni-view",{staticClass:"form-inline"},[a("select-lay",{attrs:{zindex:10,value:t.formData.group_id.toString(),name:"names",placeholder:"请选择员工角色",options:t.userGroup},on:{selectitem:function(e){arguments[0]=e=t.$handleEvent(e),t.selectUserGroup.apply(void 0,arguments)}}})],1)],1)],1)],1),a("v-uni-view",{staticClass:"pop-bottom"},[a("v-uni-button",{staticClass:"primary-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.save.apply(void 0,arguments)}}},[t._v(t._s(parseInt(t.formData.uid)>0?"修改":"添加")+"员工")])],1)],1)],1)],1)],1),a("unipopup",{ref:"deletePop",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"confirm-pop"},[a("v-uni-view",{staticClass:"title"},[t._v("确定要删除该员工数据吗？")]),a("v-uni-view",{staticClass:"btn"},[a("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.deletePop.close()}}},[t._v("取消")]),a("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteUserFn(t.detail.uid)}}},[t._v("确定")])],1)],1)],1)],1)},s=[]},"19d1":function(t,e,i){var a=i("ef1b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=i("967d").default;r("2818a6a2",a,!0,{sourceMap:!1,shadowMode:!1})},"1bf0":function(t,e,i){"use strict";var a=i("39fd"),r=i.n(a);r.a},"327c":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-5a401bf0]{display:none}\r\n/* 收银台相关 */uni-text[data-v-5a401bf0],\r\nuni-view[data-v-5a401bf0]{font-size:.14rem}body[data-v-5a401bf0]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-5a401bf0]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-5a401bf0]::-webkit-scrollbar-button{display:none}body[data-v-5a401bf0]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-5a401bf0]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-5a401bf0]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-5a401bf0]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-5a401bf0]{color:var(--primary-color)!important}.userlist[data-v-5a401bf0]{width:100%;height:100%;display:flex;align-items:center;justify-content:space-between;box-sizing:border-box}.userlist .userlist-box[data-v-5a401bf0]{width:100%;height:100%;background:#fff;display:flex}.userlist .userlist-box .userlist-left[data-v-5a401bf0]{width:5rem;height:100%;border-right:.01rem solid #e6e6e6;box-sizing:border-box;display:flex;flex-direction:column}.userlist .userlist-box .userlist-left .notYet[data-v-5a401bf0]{color:#e6e6e6;font-size:.4rem;margin-top:3rem;text-align:center}.userlist .userlist-box .userlist-left .add-user[data-v-5a401bf0]{padding:.24rem .2rem;background:#fff}.userlist .userlist-box .userlist-left .add-user uni-button[data-v-5a401bf0]{height:.4rem;line-height:.4rem}.userlist .userlist-box .userlist-left .user-title[data-v-5a401bf0]{text-align:center;line-height:.6rem;font-size:.18rem;font-weight:500;height:.6rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.userlist .userlist-box .userlist-left .user-title .icongengduo1[data-v-5a401bf0]{position:absolute;top:50%;right:.2rem;-webkit-transform:translateY(-50%);transform:translateY(-50%);font-size:.3rem;color:var(--primary-color)}.userlist .userlist-box .userlist-left .user-search[data-v-5a401bf0]{width:100%;height:.6rem;border-bottom:.01rem solid #e6e6e6;display:flex;align-items:center;justify-content:center;padding:0 .2rem;box-sizing:border-box}.userlist .userlist-box .userlist-left .user-search .search[data-v-5a401bf0]{width:5.6rem;height:.4rem;border-radius:.04rem;background:#f5f5f5;display:flex;align-items:center;padding:0 .2rem;box-sizing:border-box}.userlist .userlist-box .userlist-left .user-search .search .iconfont[data-v-5a401bf0]{font-size:.16rem;color:#909399;margin-right:.11rem}.userlist .userlist-box .userlist-left .user-search .search uni-input[data-v-5a401bf0]{width:80%;height:60%;border:none;font-size:.14rem}.userlist .userlist-box .userlist-left .user-list-wrap[data-v-5a401bf0]{flex:1;height:0}.userlist .userlist-box .userlist-left .user-list-scroll[data-v-5a401bf0]{width:100%;height:100%}.userlist .userlist-box .userlist-left .user-list-scroll .itemhover[data-v-5a401bf0]{background:var(--primary-color-light-9)}.userlist .userlist-box .userlist-left .user-list-scroll .item[data-v-5a401bf0]{width:100%;display:flex;align-items:center;padding:.2rem;box-sizing:border-box;border-bottom:.01rem solid #e6e6e6}.userlist .userlist-box .userlist-left .user-list-scroll .item uni-image[data-v-5a401bf0]{width:.7rem;height:.7rem;margin-right:.1rem}.userlist .userlist-box .userlist-left .user-list-scroll .item .item-right[data-v-5a401bf0]{display:flex;flex-direction:row;justify-content:space-between;align-items:center;height:.6rem;width:100%}.userlist .userlist-box .userlist-left .user-list-scroll .item .item-right .user-name[data-v-5a401bf0]{font-size:.16rem}.userlist .userlist-box .userlist-left .user-list-scroll .item .item-right .user-money[data-v-5a401bf0]{font-size:.14rem;margin-top:.1rem}.userlist .userlist-box .userlist-left .user-list-scroll .item .item-right .login-time[data-v-5a401bf0]{margin-top:.1rem}.userlist .userlist-box .userlist-left .user-list-scroll .item .item-right .user-status[data-v-5a401bf0]{text-align:right;color:var(--primary-color);font-size:.16rem}.userlist .userlist-box .userlist-right[data-v-5a401bf0]{width:0;flex:1;height:100%;border-right:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.userlist .userlist-box .userlist-right .user-title[data-v-5a401bf0]{text-align:center;line-height:.6rem;font-size:.18rem;font-weight:500;height:.6rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.userlist .userlist-box .userlist-right .user-title .icongengduo1[data-v-5a401bf0], .userlist .userlist-box .userlist-right .user-title .iconguanbi1[data-v-5a401bf0]{position:absolute;top:50%;right:.2rem;-webkit-transform:translateY(-50%);transform:translateY(-50%);font-size:.3rem;color:var(--primary-color);cursor:pointer}.userlist .userlist-box .userlist-right .user-information[data-v-5a401bf0]{width:100%;padding:.2rem .2rem .2rem .2rem;box-sizing:border-box;height:calc(100% - 1.38rem);overflow-y:auto;position:relative}.userlist .userlist-box .userlist-right .user-information .title[data-v-5a401bf0]{font-size:.18rem;margin-bottom:.32rem}.userlist .userlist-box .userlist-right .user-information .title2[data-v-5a401bf0]{margin-bottom:.35rem}.userlist .userlist-box .userlist-right .user-information .information-box[data-v-5a401bf0]{display:flex;justify-content:space-between}.userlist .userlist-box .userlist-right .user-information .information-box .box-left[data-v-5a401bf0]{width:5rem}.userlist .userlist-box .userlist-right .user-information .information-box .box-left .information[data-v-5a401bf0]{width:100%;padding-left:.1rem;box-sizing:border-box;display:flex;align-items:center;margin-bottom:.15rem}.userlist .userlist-box .userlist-right .user-information .information-box .box-left .information uni-view[data-v-5a401bf0]{color:#303133;font-size:.14rem}.userlist .userlist-box .userlist-right .user-information .information-box .box-left .information uni-view[data-v-5a401bf0]:nth-child(1){width:1.3rem;margin-right:.16rem;text-align:right}.userlist .userlist-box .userlist-right .user-information .information-box .box-left .information uni-view[data-v-5a401bf0]:nth-child(2){width:74%;margin-right:.23rem;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.userlist .userlist-box .userlist-right .user-information .information-box .box-left .information[data-v-5a401bf0]:last-child{margin-bottom:.35rem}.userlist .userlist-box .userlist-right .user-information .information-box .user-img[data-v-5a401bf0]{width:1.5rem;height:1.5rem}.userlist .userlist-box .userlist-right .user-information .table[data-v-5a401bf0]{width:100%;height:2.6rem;box-sizing:border-box}.userlist .userlist-box .userlist-right .user-information .table .table-all[data-v-5a401bf0]{width:100%;display:flex;align-items:center;justify-content:space-between;padding:0 .38rem;box-sizing:border-box}.userlist .userlist-box .userlist-right .user-information .table .table-all .table-td[data-v-5a401bf0]{font-size:.14rem;text-align:left;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.userlist .userlist-box .userlist-right .user-information .table .table-th[data-v-5a401bf0]{height:.56rem;background:#f7f8fa}.userlist .userlist-box .userlist-right .user-information .table .table-tb[data-v-5a401bf0]{width:100%;height:calc(100% - .56rem)}.userlist .userlist-box .userlist-right .user-information .table .table-tb .table-tr[data-v-5a401bf0]{height:.7rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box}.userlist .userlist-box .userlist-right .user-information .table .table-tb .table-tr .table-td[data-v-5a401bf0]{text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}.userlist .userlist-box .userlist-right .user-information .table .table-tb .table-tr .table-td uni-image[data-v-5a401bf0]{width:.5rem;height:.5rem}.userlist .userlist-box .userlist-right .button-box[data-v-5a401bf0]{width:100%;position:absolute;bottom:0;left:0;box-sizing:border-box;padding:.24rem .2rem}.userlist .userlist-box .userlist-right .button-box uni-button[data-v-5a401bf0]{width:1rem;height:.4rem;line-height:.4rem;margin-left:.1rem;margin-right:0}uni-view[data-v-5a401bf0]{color:#303133}[data-v-5a401bf0] .uni-scroll-view::-webkit-scrollbar{width:.05rem;height:.3rem}[data-v-5a401bf0] .uni-scroll-view::-webkit-scrollbar-thumb{border-radius:.1rem;box-shadow:inset 0 0 .05rem rgba(0,0,0,.2);background:#c1c1c1}.user-information[data-v-5a401bf0]::-webkit-scrollbar{width:.05rem;height:.3rem}.user-information[data-v-5a401bf0]::-webkit-scrollbar-thumb{border-radius:.1rem;box-shadow:inset 0 0 .05rem rgba(0,0,0,.2);background:#c1c1c1}.cart-empty[data-v-5a401bf0]{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:2.1rem}.form-content[data-v-5a401bf0]{margin-top:.2rem}.form-content .form-item[data-v-5a401bf0]{margin-bottom:.1rem;display:flex}.form-content .form-item .form-label[data-v-5a401bf0]{width:1.3rem;text-align:right;padding-right:.1rem;box-sizing:border-box;height:.32rem;line-height:.32rem}.form-content .form-item .form-label .required[data-v-5a401bf0]{color:red;margin-right:.03rem}.form-content .form-item .form-inline[data-v-5a401bf0]{width:2.4rem;line-height:.32rem;margin-right:.1rem;box-sizing:border-box}.form-content .form-item .form-inline .form-input[data-v-5a401bf0]{border-width:.01rem;border-style:solid;background-color:#fff;color:rgba(0,0,0,.85);border-radius:.02rem;padding-left:.1rem;height:.32rem;line-height:.32rem;font-size:.14rem;border-color:#e6e6e6}.form-content .form-item .form-inline uni-button[data-v-5a401bf0]{width:calc(50% - .05rem);display:inline-block;margin-right:.1rem}.form-content .form-item .form-inline uni-button[data-v-5a401bf0]:nth-child(2){margin-right:0}.form-content .form-item .search-wrap[data-v-5a401bf0]{position:relative}.form-radio-group[data-v-5a401bf0]{display:flex;align-items:center}.form-checkbox-item[data-v-5a401bf0], .form-radio-item[data-v-5a401bf0]{margin-right:%?26?%;display:flex;align-items:center}[data-v-5a401bf0] .uni-radio-input, .uni-checkbox-input[data-v-5a401bf0]{width:.18rem;height:.18rem}.pop-box[data-v-5a401bf0]{background:#fff;width:4.2rem;height:3.38rem}.pop-box .pop-header[data-v-5a401bf0]{padding:0 .15rem 0 .2rem;height:.5rem;line-height:.5rem;border-bottom:.01rem solid #f0f0f0;font-size:.14rem;color:#333;overflow:hidden;border-radius:.02rem .2rem 0 0;box-sizing:border-box;display:flex;justify-content:space-between}.pop-box .pop-header .pop-header-close[data-v-5a401bf0]{cursor:pointer}.pop-box .pop-header .pop-header-close uni-text[data-v-5a401bf0]{font-size:.18rem}.pop-box .pop-content[data-v-5a401bf0]{height:calc(100% - 1.05rem);padding:.2rem;box-sizing:border-box}.pop-box .form-content[data-v-5a401bf0]{margin-top:0;padding-top:.2rem;display:flex;flex-direction:column;align-items:center}.pop-box .form-content .form-label[data-v-5a401bf0]{width:.9rem}.pop-box .pop-bottom[data-v-5a401bf0]{padding:.1rem;border-top:.01rem solid #eee}.pop-box .pop-bottom uni-button[data-v-5a401bf0]{width:95%}',""]),t.exports=e},3523:function(t,e,i){"use strict";i.r(e);var a=i("6e0b"),r=i("53f4");for(var s in r)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(s);i("1bf0");var n=i("828b"),o=Object(n["a"])(r["default"],a["b"],a["c"],!1,null,"3387bb78",null,!1,a["a"],void 0);e["default"]=o.exports},"37e5":function(t,e,i){"use strict";i.r(e);var a=i("69c0"),r=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},"39fd":function(t,e,i){var a=i("48fc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=i("967d").default;r("2cfa7e01",a,!0,{sourceMap:!1,shadowMode:!1})},"48fc":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-3387bb78]{display:none}\r\n/* 收银台相关 */uni-text[data-v-3387bb78],\r\nuni-view[data-v-3387bb78]{font-size:.14rem}body[data-v-3387bb78]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-3387bb78]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-3387bb78]::-webkit-scrollbar-button{display:none}body[data-v-3387bb78]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-3387bb78]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-3387bb78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-3387bb78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-3387bb78]{color:var(--primary-color)!important}.uni-select-lay[data-v-3387bb78]{position:relative;z-index:999;box-sizing:border-box}.uni-select-lay .uni-select-input[data-v-3387bb78]{opacity:0;position:absolute;z-index:-111}.uni-select-lay .uni-select-lay-select[data-v-3387bb78]{-webkit-user-select:none;user-select:none;position:relative;z-index:3;height:.32rem;padding:0 .3rem 0 .1rem;box-sizing:border-box;border-radius:.02rem;border:.01rem solid #e5e5e5;display:flex;align-items:center;font-size:.14rem;color:#999}.uni-select-lay .uni-select-lay-select .uni-disabled[data-v-3387bb78]{position:absolute;left:0;width:100%;height:100%;z-index:19;cursor:no-drop;background:hsla(0,0%,100%,.5)}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close[data-v-3387bb78]{position:absolute;right:.35rem;top:0;height:100%;width:.15rem;display:flex;align-items:center;justify-content:center;z-index:3;cursor:pointer}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]{position:relative;background:#fff;width:.13rem;height:.13rem;border-radius:50%;border:.01rem solid #bbb}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::before, .uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::after{content:"";position:absolute;left:20%;top:50%;height:.01rem;width:60%;-webkit-transform:rotate(45deg);transform:rotate(45deg);background-color:#bbb}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::after{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.uni-select-lay .uni-select-lay-select .uni-select-lay-input[data-v-3387bb78]{font-size:.14rem;color:#999;display:block;width:98%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:.3rem;box-sizing:border-box}.uni-select-lay .uni-select-lay-select .uni-select-lay-input.active[data-v-3387bb78]{color:#333}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon[data-v-3387bb78]{cursor:pointer;position:absolute;right:0;top:0;height:100%;width:.3rem;display:flex;align-items:center;justify-content:center}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon[data-v-3387bb78]::before{content:"";width:.01rem;height:100%;position:absolute;left:0;top:0;background-color:#e5e5e5}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon uni-text[data-v-3387bb78]{display:block;width:0;height:0;border-width:.07rem .07rem 0;border-style:solid;border-color:#bbb transparent transparent;transition:.3s}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled[data-v-3387bb78]{cursor:no-drop}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled uni-text[data-v-3387bb78]{width:.2rem;height:.2rem;border:.02rem solid red;border-radius:50%;transition:.3s;position:relative;z-index:999}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled uni-text[data-v-3387bb78]::after{content:"";position:absolute;top:50%;left:0;width:100%;height:.02rem;margin-top:-.01rem;background-color:red;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.uni-select-lay .uni-select-lay-select.active .uni-select-lay-icon uni-text[data-v-3387bb78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.uni-select-lay .uni-select-lay-options[data-v-3387bb78]{-webkit-user-select:none;user-select:none;position:absolute;top:calc(100% + .05rem);left:0;width:100%;max-height:2.5rem;border-radius:.02rem;border:1px solid #e5e5e5;background:#fff;padding:.05rem 0;box-sizing:border-box;z-index:9}.uni-select-lay .uni-select-lay-options .uni-select-lay-item[data-v-3387bb78]{padding:0 .1rem;box-sizing:border-box;cursor:pointer;line-height:2.5;transition:.3s;font-size:.14rem}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.active[data-v-3387bb78]{background:var(--primary-color);color:#fff}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.active[data-v-3387bb78]:hover{background:var(--primary-color);color:#fff}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.disabled[data-v-3387bb78]{color:#999;cursor:not-allowed}.uni-select-lay .uni-select-lay-options .uni-select-lay-item[data-v-3387bb78]:hover{background-color:#f5f5f5}.uni-select-lay .uni-select-lay-options .nosearch[data-v-3387bb78]{font-size:.16rem;line-height:3;text-align:center;color:#666}.uni-date-mask[data-v-3387bb78]{position:fixed;bottom:0;top:0;left:0;right:0;background-color:transparent;transition-duration:.3s;z-index:8}',""]),t.exports=e},"4dd3":function(t,e,i){t.exports=i.p+"static/cashier/cart_empty.png"},"53f4":function(t,e,i){"use strict";i.r(e);var a=i("6c5f"),r=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},6194:function(t,e,i){var a=i("327c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=i("967d").default;r("71751018",a,!0,{sourceMap:!1,shadowMode:!1})},"69c0":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("aa77"),i("aa9c"),i("5ef2"),i("c223"),i("bd06"),i("8f71"),i("dd2b");var a={name:"uniTable",options:{virtualHost:!0},emits:["selection-change"],props:{data:{type:Array,default:function(){return[]}},border:{type:Boolean,default:!1},stripe:{type:Boolean,default:!1},type:{type:String,default:""},emptyText:{type:String,default:"没有更多数据"},loading:{type:Boolean,default:!1},rowKey:{type:String,default:""}},data:function(){return{noData:!0,minWidth:0,multiTableHeads:[]}},watch:{loading:function(t){},data:function(t){this.theadChildren;this.theadChildren&&this.theadChildren.rowspan,this.noData=!1}},created:function(){this.trChildren=[],this.thChildren=[],this.theadChildren=null,this.backData=[],this.backIndexData=[]},methods:{isNodata:function(){this.theadChildren;var t=1;this.theadChildren&&(t=this.theadChildren.rowspan),this.noData=this.trChildren.length-t<=0},selectionAll:function(){var t=this,e=1,i=this.theadChildren;this.theadChildren?e=i.rowspan-1:i=this.trChildren[0];var a=this.data&&this.data.length.length>0;i.checked=!0,i.indeterminate=!1,this.trChildren.forEach((function(i,r){if(!i.disabled){if(i.checked=!0,a&&i.keyValue){var s=t.data.find((function(e){return e[t.rowKey]===i.keyValue}));t.backData.find((function(e){return e[t.rowKey]===s[t.rowKey]}))||t.backData.push(s)}r>e-1&&-1===t.backIndexData.indexOf(r-e)&&t.backIndexData.push(r-e)}})),this.$emit("selection-change",{detail:{value:this.backData,index:this.backIndexData}})},toggleRowSelection:function(t,e){var i=this;t=[].concat(t),this.trChildren.forEach((function(a,r){var s=t.findIndex((function(t){return"number"===typeof t?t===r-1:t[i.rowKey]===a.keyValue})),n=a.checked;-1!==s&&(a.checked="boolean"===typeof e?e:!a.checked,n!==a.checked&&i.check(a.rowData||a,a.checked,a.rowData?a.keyValue:null,!0))})),this.$emit("selection-change",{detail:{value:this.backData,index:this.backIndexData}})},clearSelection:function(){var t=this.theadChildren;this.theadChildren||(t=this.trChildren[0]),t.checked=!1,t.indeterminate=!1,this.trChildren.forEach((function(t){t.checked=!1})),this.backData=[],this.backIndexData=[],this.$emit("selection-change",{detail:{value:[],index:[]}})},toggleAllSelection:function(){var t=[],e=1,i=this.theadChildren;this.theadChildren?e=i.rowspan-1:i=this.trChildren[0],this.trChildren.forEach((function(i,a){i.disabled||a>e-1&&t.push(a-e)})),this.toggleRowSelection(t)},check:function(t,e,i,a){var r=this,s=this.theadChildren;this.theadChildren||(s=this.trChildren[0]);var n=this.trChildren.findIndex((function(e,i){return t===e}));n<0&&(n=this.data.findIndex((function(t){return t[r.rowKey]===i}))+1);this.trChildren.filter((function(t){return!t.disabled&&t.keyValue})).length;if(0!==n){if(e)i&&this.backData.push(t),this.backIndexData.push(n-1);else{var o=this.backData.findIndex((function(t){return t[r.rowKey]===i})),l=this.backIndexData.findIndex((function(t){return t===n-1}));i&&this.backData.splice(o,1),this.backIndexData.splice(l,1)}var u=this.trChildren.find((function(t,e){return e>0&&!t.checked&&!t.disabled}));u?(s.indeterminate=!0,s.checked=!1):(s.indeterminate=!1,s.checked=!0),0===this.backIndexData.length&&(s.indeterminate=!1),a||this.$emit("selection-change",{detail:{value:this.backData,index:this.backIndexData}})}else e?this.selectionAll():this.clearSelection()}}};e.default=a},"6c5f":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("bf0f"),i("2797"),i("8f71"),i("4626"),i("5ac7");var a={name:"select-lay",props:{disabled:{type:Boolean,default:!1},zindex:{type:Number,default:999},options:{type:Array,default:function(){return[]}},name:{type:String,default:""},value:{type:[String,Number],default:""},placeholder:{type:String,default:"请选择"},showplaceholder:{type:Boolean,default:!0},slabel:{type:String,default:"label"},svalue:{type:String,default:"value"}},data:function(){return{active:!1,isfocus:!1,isremove:!1,ismove:!1,changevalue:"",oldvalue:"",changes:!1,changesValue:"",vlist:[],settimer:null}},mounted:function(){this.itemcheck()},watch:{value:function(){this.itemcheck()},options:function(){this.itemcheck()}},methods:{itemcheck:function(){var t=this;""!=this.value?this.options.length>0&&this.options.forEach((function(e){t.value!=e[t.svalue]||(t.oldvalue=t.changevalue=e[t.slabel])})):this.oldvalue=this.changevalue=""},select:function(){this.disabled||(this.active=!this.active,this.active?this.changes=!1:this.changevalue=this.oldvalue)},unifocus:function(){this.disabled||(this.active=!0,this.changes=!1,this.isfocus=!0)},uniblur:function(){var t=this;this.isfocus=!1,setTimeout((function(){t.isremove||t.ismove?(t.isremove=!1,t.ismove=!1):(t.changevalue=t.oldvalue,t.isremove=!1,t.active=!1)}),153)},movetouch:function(){var t=this;setTimeout((function(){t.isfocus?t.ismove=!1:t.ismove||(t.ismove=!0)}),100)},selectmove:function(){var t=this;setTimeout((function(){t.isfocus?t.ismove=!1:t.ismove||(t.ismove=!0)}),100)},removevalue:function(){this.isremove=!0,this.changes=!1,this.changevalue=""},intchange:function(){var t=this;""!=this.changevalue?this.oldvalue!=this.changevalue&&(this.vlist=[],this.changes=!0,this.changesValue="正在搜索...",this.settimer&&clearTimeout(this.settimer),this.settimer=setTimeout((function(){t.vlist=t.options.filter((function(e){return e[t.slabel].includes(t.changevalue)})),0===t.vlist.length&&(t.changesValue="暂无匹配内容！")}),600)):this.changes=!1},selectitem:function(t,e){if(e&&e.disabled)return!1;this.changevalue=this.oldvalue,this.active=!1,this.$emit("selectitem",t,e)}}};e.default=a},"6e0b":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"uni-select-lay",style:{"z-index":t.zindex}},[i("v-uni-input",{staticClass:"uni-select-input",attrs:{type:"text",name:t.name,readonly:!0},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}),i("v-uni-view",{staticClass:"uni-select-lay-select",class:{active:t.active}},[t.disabled?i("v-uni-view",{staticClass:"uni-disabled"}):t._e(),""!=t.changevalue&&this.active?i("v-uni-view",{staticClass:"uni-select-lay-input-close"},[i("v-uni-text",{on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.removevalue.apply(void 0,arguments)}}})],1):t._e(),i("v-uni-input",{staticClass:"uni-select-lay-input",class:{active:""!=t.changevalue&&t.changevalue!=t.placeholder},attrs:{type:"text",readonly:!0,disabled:"true",placeholder:t.placeholder},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.unifocus.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.intchange.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.uniblur.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}},model:{value:t.changevalue,callback:function(e){t.changevalue=e},expression:"changevalue"}}),i("v-uni-view",{staticClass:"uni-select-lay-icon",class:{disabled:t.disabled},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}}},[i("v-uni-text")],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.active,expression:"active"}],staticClass:"uni-date-mask",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}}}),i("v-uni-scroll-view",{directives:[{name:"show",rawName:"v-show",value:t.active,expression:"active"}],staticClass:"uni-select-lay-options",attrs:{"scroll-y":!0},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.selectmove.apply(void 0,arguments)},touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.movetouch.apply(void 0,arguments)}}},[t.changes?[t.vlist.length>0?t._l(t.vlist,(function(e,a){return i("v-uni-view",{key:a,staticClass:"uni-select-lay-item",class:{active:t.value==e[t.svalue]},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.selectitem(a,e)}}},[t._v(t._s(e[t.slabel]))])})):[i("v-uni-view",{staticClass:"nosearch"},[t._v(t._s(t.changesValue))])]]:[t.showplaceholder?i("v-uni-view",{staticClass:"uni-select-lay-item",class:{active:""==t.value},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.selectitem(-1,null)}}},[t._v(t._s(t.placeholder))]):t._e(),t._l(t.options,(function(e,a){return i("v-uni-view",{key:a,staticClass:"uni-select-lay-item",class:{active:t.value==e[t.svalue],disabled:e.disabled},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.selectitem(a,e)}}},[t._v(t._s(e[t.slabel]))])}))]],2)],1)},r=[]},"9f5f":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("c223"),i("fd3c"),i("e966");var r=i("b875"),s=a(i("2166")),n={components:{unipopup:s.default},data:function(){var t=this;return{selectUserKeys:0,search_text:"",page:1,page_size:8,list:[],one_judge:!0,listLock:!0,scrollTop:0,detail:{},logCols:[{width:60,title:"操作记录",align:"left",field:"action_name"},{width:20,title:"操作IP地址",align:"left",field:"ip"},{width:20,title:"操作时间",align:"right",templet:function(e){return t.$util.timeFormat(e.create_time)}}],formData:{username:"",password:"",group_id:""},userGroup:[],isRepeat:!1}},onLoad:function(){this.getUserListFn(),this.getUserGroup()},methods:{userSelect:function(t,e){this.selectUserKeys=e,this.getUserDetailFn(t.uid),this.one_judge=!0,this.isRepeat=!1,this.formData={username:"",password:"",group_id:""}},statusChange:function(t){this.formData.status=t.detail.value},search:function(){this.page=1,this.list=[],this.one_judge=!0,this.listLock=!0,this.getUserListFn()},getUserListFn:function(){var t=this;if(!this.listLock)return!1;(0,r.getUserList)({page:this.page,page_size:this.page_size,username:this.search_text}).then((function(e){0==e.data.list.length&&t.one_judge&&(t.detail={},t.one_judge=!1),e.code>=0&&0!=e.data.list.length&&(0==t.list.length?t.list=e.data.list:t.list=t.list.concat(e.data.list),t.one_judge&&t.getUserDetailFn(t.list[0].uid)),1==t.page&&(t.scrollTop=0),e.data.list.length<t.page_size?t.listLock=!1:t.page++}))},scroll:function(t){this.scrollTop=t.detail.scrollTop},getUserDetailFn:function(t){var e=this;(0,r.getUserDetail)(t).then((function(t){0==t.code&&(e.detail=t.data,e.one_judge=!1)}))},getUserGroup:function(){var t=this;(0,r.getAllGroups)().then((function(e){0==e.code&&e.data&&(t.userGroup=e.data.map((function(t){return{label:t.group_name,value:t.group_id,create_uid:t.create_uid,store_id:t.store_id}})))}))},editUserAction:function(t){var e=this;(0,r.getUserDetail)(t).then((function(t){0==t.code&&t.data.create_user_info&&(e.formData={username:t.data.username,group_id:t.data.group_id,uid:t.data.uid,status:t.data.status},e.$refs.addUserPop.open())}))},deleteUserFn:function(t){var e=this;if(this.isRepeat)return!1;this.isRepeat=!0,(0,r.deleteUser)(t).then((function(t){t.code>=0?(e.page=1,e.list=[],e.one_judge=!0,e.listLock=!0,e.getUserListFn(),e.$refs.deletePop.close()):e.$util.showToast({title:t.message}),e.isRepeat=!1}))},addUser:function(){this.$refs.addUserPop.open()},cancelAddUser:function(){this.formData={username:"",password:"",group_id:""},this.$refs.addUserPop.close()},selectUserGroup:function(t,e){this.formData.group_id=t>=0?parseInt(e.value):0},save:function(){var t=this;if(this.verify()&&!this.isRepeat){this.isRepeat=!0;var e="";e=parseInt(this.formData.uid)>0?(0,r.editUser)(this.formData):(0,r.addUser)(this.formData),e.then((function(e){e.code>=0?(t.$util.showToast({title:"操作成功"}),t.page=1,t.list=[],t.one_judge=!0,t.listLock=!0,t.cancelAddUser(),t.getUserListFn(),t.isRepeat=!1,t.formData={username:"",password:"",group_id:""}):(t.isRepeat=!1,t.$util.showToast({title:e.message}))}))}},verify:function(){return this.formData.username?0!=parseInt(this.formData.uid)||this.formData.password?!!this.formData.group_id||(this.$util.showToast({title:"请选择员工角色"}),!1):(this.$util.showToast({title:"请输入密码"}),!1):(this.$util.showToast({title:"请输入用户名"}),!1)}}};e.default=n},c7cb:function(t,e,i){"use strict";var a=i("19d1"),r=i.n(a);r.a},ef1b:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-c121b056]{display:none}\r\n/* 收银台相关 */uni-text[data-v-c121b056],\r\nuni-view[data-v-c121b056]{font-size:.14rem}body[data-v-c121b056]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-c121b056]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-c121b056]::-webkit-scrollbar-button{display:none}body[data-v-c121b056]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-c121b056]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-c121b056]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-c121b056]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-c121b056]{color:var(--primary-color)!important}.uni-table-scroll[data-v-c121b056]{width:100%;overflow-x:auto}.uni-table[data-v-c121b056]{position:relative;width:100%;border-radius:5px;background-color:#fff;box-sizing:border-box;display:table;overflow-x:auto}.uni-table[data-v-c121b056]  .uni-table-tr:nth-child(n + 2):hover{background-color:#f5f7fa}.uni-table[data-v-c121b056]  .uni-table-thead .uni-table-tr:hover{background-color:#fafafa}.table--border[data-v-c121b056]{border:1px #ebeef5 solid;border-right:none}.border-none[data-v-c121b056]{border-bottom:none}.table--stripe[data-v-c121b056]  .uni-table-tr:nth-child(2n + 3){background-color:#fafafa}\r\n/* 表格加载、无数据样式 */.uni-table-loading[data-v-c121b056]{position:relative;display:table-row;height:50px;line-height:50px;overflow:hidden;box-sizing:border-box}.empty-border[data-v-c121b056]{border-right:1px #ebeef5 solid}.uni-table-text[data-v-c121b056]{position:absolute;right:0;left:0;text-align:center;font-size:14px;color:#999}.uni-table-mask[data-v-c121b056]{position:absolute;top:0;bottom:0;left:0;right:0;background-color:hsla(0,0%,100%,.8);z-index:99;display:flex;margin:auto;transition:all .5s;justify-content:center;align-items:center}.uni-table--loader[data-v-c121b056]{width:30px;height:30px;border:2px solid #aaa;border-radius:50%;-webkit-animation:2s uni-table--loader-data-v-c121b056 linear infinite;animation:2s uni-table--loader-data-v-c121b056 linear infinite;position:relative}@-webkit-keyframes uni-table--loader-data-v-c121b056{0%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}10%{border-left-color:transparent}20%{border-bottom-color:transparent}30%{border-right-color:transparent}40%{border-top-color:transparent}50%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}60%{border-top-color:transparent}70%{border-left-color:transparent}80%{border-bottom-color:transparent}90%{border-right-color:transparent}100%{-webkit-transform:rotate(-1turn);transform:rotate(-1turn)}}@keyframes uni-table--loader-data-v-c121b056{0%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}10%{border-left-color:transparent}20%{border-bottom-color:transparent}30%{border-right-color:transparent}40%{border-top-color:transparent}50%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}60%{border-top-color:transparent}70%{border-left-color:transparent}80%{border-bottom-color:transparent}90%{border-right-color:transparent}100%{-webkit-transform:rotate(-1turn);transform:rotate(-1turn)}}',""]),t.exports=e},f04e:function(t,e,i){"use strict";i.r(e);var a=i("1641"),r=i("1241");for(var s in r)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(s);i("0573");var n=i("828b"),o=Object(n["a"])(r["default"],a["b"],a["c"],!1,null,"5a401bf0",null,!1,a["a"],void 0);e["default"]=o.exports},fc38:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"uni-table-scroll",class:{"table--border":t.border,"border-none":!t.noData}},[i("table",{staticClass:"uni-table",class:{"table--stripe":t.stripe},style:{"min-width":t.minWidth+"px"},attrs:{border:"0",cellpadding:"0",cellspacing:"0"}},[t._t("default"),t.noData?i("v-uni-view",{staticClass:"uni-table-loading"},[i("v-uni-view",{staticClass:"uni-table-text",class:{"empty-border":t.border}},[t._v(t._s(t.emptyText))])],1):t._e(),t.loading?i("v-uni-view",{staticClass:"uni-table-mask",class:{"empty-border":t.border}},[i("div",{staticClass:"uni-table--loader"})]):t._e()],2)])},r=[]}}]);