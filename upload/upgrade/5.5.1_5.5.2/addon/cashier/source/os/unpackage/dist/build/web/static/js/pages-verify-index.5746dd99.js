(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-verify-index"],{1116:function(t,e,n){"use strict";n.r(e);var i=n("423c"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"1f3b":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getVerifyInfo=function(t){return a.default.post("/cashier/storeapi/verify/info",{data:{code:t}})},e.getVerifyRecordDetail=function(t){return a.default.post("/cashier/storeapi/verify/recordsdetail",{data:{id:t}})},e.getVerifyRecordList=function(t){return a.default.post("/cashier/storeapi/verify/recordlists",{data:t})},e.verifyCode=function(t){return a.default.post("/cashier/storeapi/verify/verify",{data:{verify_code:t}})};var a=i(n("4e01"))},"29bc":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-9f38bc4c]{display:none}\r\n/* 收银台相关 */uni-text[data-v-9f38bc4c],\r\nuni-view[data-v-9f38bc4c]{font-size:.14rem}body[data-v-9f38bc4c]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-9f38bc4c]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-9f38bc4c]::-webkit-scrollbar-button{display:none}body[data-v-9f38bc4c]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-9f38bc4c]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-9f38bc4c]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-9f38bc4c]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-9f38bc4c]{color:var(--primary-color)!important}.container[data-v-9f38bc4c]{display:flex;align-items:center;justify-content:center;flex-direction:column}.search-title[data-v-9f38bc4c]{font-size:.18rem;color:#303133}.search-wrap[data-v-9f38bc4c]{display:flex;margin-top:.3rem}.search-wrap uni-button[data-v-9f38bc4c]{width:1rem;text-align:center;box-sizing:border-box;line-height:.5rem}.search-desc[data-v-9f38bc4c]{color:#909399;font-size:.14rem;margin-top:.3rem}.input-wrap[data-v-9f38bc4c]{width:4.5rem;height:.5rem;border:.01rem solid #ccc;display:flex;border-radius:.02rem;align-items:center}.input-wrap uni-input[data-v-9f38bc4c]{flex:1;padding:0 .15rem;font-size:.16rem}.input-wrap .placeholder[data-v-9f38bc4c]{flex:1;height:.58rem;line-height:.58rem;font-size:.16rem;font-weight:400;color:#909399}.input-wrap .iconfont[data-v-9f38bc4c]{font-size:.18rem;padding:0 .15rem;font-weight:700}.record[data-v-9f38bc4c]{text-align:center;margin-top:.2rem}.record uni-text[data-v-9f38bc4c]{color:var(--primary-color);font-size:.14rem;cursor:pointer}.content-box[data-v-9f38bc4c]{padding:.15rem .15rem .15rem .15rem}.content-box .input-wrap[data-v-9f38bc4c]{width:6rem;height:.4rem;border:.01rem solid #ccc;display:flex;border-radius:.02rem}.content-box .input-wrap uni-input[data-v-9f38bc4c]{flex:1;padding:0 .15rem;height:.38rem;line-height:.38rem;font-size:.16rem;font-weight:400}.content-box .input-wrap .placeholder[data-v-9f38bc4c]{font-weight:400;color:#909399;font-size:.18rem}.content-box .input-wrap .search[data-v-9f38bc4c]{border-radius:0;width:1rem;line-height:.38rem;font-size:.16rem;font-family:Source Han Sans CN}.content-box .input-wrap .search[data-v-9f38bc4c]::after{border:none}.content-box .content-data[data-v-9f38bc4c]{border:.01rem solid #eee;margin-top:.2rem;padding:.15rem}.content-box .content-data .verify-item[data-v-9f38bc4c]{display:flex;padding:.15rem 0}.content-box .content-data .verify-item .container-image[data-v-9f38bc4c]{width:1rem;height:1rem}.content-box .content-data .verify-item .container-image uni-image[data-v-9f38bc4c]{width:100%;height:100%}.content-box .content-data .verify-item .container-box[data-v-9f38bc4c]{display:flex;flex-direction:column;justify-content:space-between;margin-left:.15rem;width:0;flex:1}.content-box .content-data .verify-item .container-box .content-name[data-v-9f38bc4c]{font-size:.15rem;margin-top:.05rem}.content-box .content-data .verify-item .container-box .content-desc[data-v-9f38bc4c]{display:flex;margin-top:.15rem;color:#999;font-size:.13rem}.content-box .content-data .verify-item .container-box .content-desc .time[data-v-9f38bc4c]{margin-left:.5rem}.content-box .content-data .verify-action[data-v-9f38bc4c]{display:flex;justify-content:flex-end;border-top:.01rem solid #eee;padding-top:.15rem}.content-box .content-data .verify-action uni-button[data-v-9f38bc4c]{width:1rem;height:.36rem;margin:0 0 0 .15rem}.content-box .content-data .content-bottom[data-v-9f38bc4c]{padding:.15rem 0;border-top:.01rem solid #eee}.content-box .content-data .content-bottom .bottom-item[data-v-9f38bc4c]{color:#999;display:flex;margin-top:.15rem;width:5rem;justify-content:space-between}.content-box .content-data .content-bottom .bottom-item uni-view[data-v-9f38bc4c]{margin-right:.5rem}',""]),t.exports=e},"32d2":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("base-page",[n("v-uni-view",{staticClass:"uni-flex uni-row height-all"},["search"==t.step?n("v-uni-view",{staticClass:"container common-wrap",staticStyle:{"-webkit-flex":"1",flex:"1"}},[n("v-uni-view",{staticClass:"search-title"},[t._v("查询核销码核销")]),n("v-uni-view",{staticClass:"search-wrap"},[n("v-uni-view",{staticClass:"input-wrap"},[n("v-uni-input",{attrs:{type:"text",value:"",placeholder:"请输入核销码或扫描核销码","placeholder-class":"placeholder",focus:t.inputFocus},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.inputFocus=!0},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.codeInputBlur.apply(void 0,arguments)}},model:{value:t.code,callback:function(e){t.code=e},expression:"code"}})],1),n("v-uni-button",{staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)}}},[t._v("查询")])],1),n("v-uni-view",{staticClass:"search-desc"},[t._v("使用扫码枪扫码时需注意光标需要停留在输入框中")]),n("v-uni-view",{staticClass:"record",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/verify/list")}}},[n("v-uni-text",[t._v("核销记录")])],1)],1):t._e(),"verify"==t.step?n("v-uni-view",{staticClass:"content-box common-wrap",staticStyle:{"-webkit-flex":"1",flex:"1"}},[n("v-uni-view",{staticClass:"input-wrap"},[n("v-uni-input",{attrs:{placeholder:"请输入核销码"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)}},model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}),n("v-uni-button",{staticClass:"primary-btn search",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.search()}}},[t._v("查询")])],1),n("v-uni-view",{staticClass:"content-data"},[n("v-uni-view",{staticClass:"content-top"},t._l(t.verifyInfo.data.item_array,(function(e,i){return n("v-uni-view",{key:i,staticClass:"verify-item"},[n("v-uni-view",{staticClass:"container-image"},[n("v-uni-image",{attrs:{src:t.$util.img(e.img.split(",")[0],{size:"small"}),mode:"aspectFit"}})],1),n("v-uni-view",{staticClass:"container-box"},[n("v-uni-view",{staticClass:"content-name"},[t._v(t._s(e.name))]),n("v-uni-view",{staticClass:"content-name"},[t._v("x"+t._s(e.num))])],1)],1)})),1),n("v-uni-view",{staticClass:"content-bottom"},[n("v-uni-view",{staticClass:"bottom-item"},[n("v-uni-view",[t._v("核销状态："+t._s(0==t.verifyInfo.is_verify?"待核销":"已核销"))])],1),n("v-uni-view",{staticClass:"bottom-item"},[n("v-uni-view",[t._v("核销类型："+t._s(t.verifyInfo.verify_type_name)+"核销")])],1),n("v-uni-view",{staticClass:"bottom-item"},[n("v-uni-view",[t._v("总次数/已使用："+t._s(t.verifyInfo.verify_total_count?t.verifyInfo.verify_total_count:"不限")+"次/"+t._s(t.verifyInfo.verify_use_num)+"次")])],1),n("v-uni-view",{staticClass:"bottom-item"},[n("v-uni-view",[t._v("有效期："+t._s(t.verifyInfo.expire_time?t.$util.timeFormat(t.verifyInfo.expire_time,"Y-m-d H:i"):"永久"))])],1)],1),n("v-uni-view",{staticClass:"verify-action"},[n("v-uni-button",{staticClass:"default-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.step="search"}}},[t._v("取消")]),n("v-uni-button",{directives:[{name:"show",rawName:"v-show",value:0==t.verifyInfo.is_verify,expression:"verifyInfo.is_verify == 0"}],staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.verify()}}},[t._v("立即核销")])],1)],1)],1):t._e()],1)],1)},a=[]},"3dfb":function(t,e,n){"use strict";var i=n("a6e9"),a=n.n(i);a.a},"423c":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("bf0f"),n("18f7"),n("de6c"),n("2425"),n("0c26"),n("5c47"),n("af8f");var i=n("1f3b"),a={data:function(){return{step:"search",code:"",verifyInfo:null,isRepeat:!1,inputFocus:!1}},onLoad:function(){var t=this;uni.hideTabBar(),this.$nextTick((function(){t.inputFocus=!0}))},methods:{codeInputBlur:function(){var t=this;this.inputFocus=!1,this.verifyInfo||this.$nextTick((function(){t.inputFocus=!0}))},deleteCode:function(){this.code=this.code.substr(0,this.code.length-1)},search:function(){var t=this;this.code?setTimeout((function(){t.code=new URLSearchParams(t.code.split("?")[1]).get("code"),(0,i.getVerifyInfo)(t.code.trim()).then((function(e){t.code="",e.code>=0?(t.verifyInfo=e.data,t.step="verify"):t.$util.showToast({title:e.message})}))}),200):this.$util.showToast({title:"请输入核销码"})},verify:function(){var t=this;this.verifyInfo?this.isRepeat||(this.isRepeat=!0,(0,i.verifyCode)(this.verifyInfo.verify_code).then((function(e){t.isRepeat=!1,e.code>=0&&(t.step="search",t.verifyInfo=null,t.code=""),t.$util.showToast({title:e.message})}))):this.$util.showToast({title:"请先查询核销码信息"})},scancode:function(){var t=this;uni.scanCode({scanType:["qrCode","barCode"],success:function(e){t.code=e.result,t.search()},fail:function(e){t.$util.showToast({title:"扫码失败"})}})}}};e.default=a},"6ea5":function(t,e,n){"use strict";n.r(e);var i=n("32d2"),a=n("1116");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("3dfb");var r=n("828b"),c=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"9f38bc4c",null,!1,i["a"],void 0);e["default"]=c.exports},a6e9:function(t,e,n){var i=n("29bc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("530db657",i,!0,{sourceMap:!1,shadowMode:!1})}}]);