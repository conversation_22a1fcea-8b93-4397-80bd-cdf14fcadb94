(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-billing-index~pages-buycard-index~pages-marketing-coupon_list~pages-marketing-edit_coupon~page~af242e5f"],{"0e57":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={appid:"__UNI__0B0A569"}},"15ab":function(e,t,n){"use strict";var r=n("7658"),a=n("57e7");r("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),a)},"1ea2":function(e,t,n){"use strict";var r=n("af9e"),a=n("1c06"),i=n("ada5"),o=n("5d6e"),s=Object.isExtensible,u=r((function(){s(1)}));e.exports=u||o?function(e){return!!a(e)&&((!o||"ArrayBuffer"!==i(e))&&(!s||s(e)))}:s},"20f3":function(e,t,n){"use strict";var r=n("8bdb"),a=n("5145");r({target:"Array",proto:!0,forced:a!==[].lastIndexOf},{lastIndexOf:a})},3471:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=(0,r.default)(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var a=0,i=function(){};return{s:i,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){u=!0,o=e},f:function(){try{s||null==n["return"]||n["return"]()}finally{if(u)throw o}}}},n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("7a76"),n("c9b5");var r=function(e){return e&&e.__esModule?e:{default:e}}(n("5d6b"))},4085:function(e,t,n){"use strict";var r=n("8bdb"),a=n("85c1");r({global:!0,forced:a.globalThis!==a},{globalThis:a})},"49b7":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pages:[{path:"pages/billing/index",style:{navigationStyle:"custom",navigationBarTitleText:"开单"}},{path:"pages/reserve/index",style:{navigationStyle:"custom",navigationBarTitleText:"预约"}},{path:"pages/buycard/index",style:{navigationStyle:"custom",navigationBarTitleText:"售卡"}},{path:"pages/recharge/index",style:{navigationStyle:"custom",navigationBarTitleText:"充值"}},{path:"pages/verify/index",style:{navigationStyle:"custom",navigationBarTitleText:"核销"}},{path:"pages/verify/list",style:{navigationStyle:"custom",navigationBarTitleText:"核销记录"}},{path:"pages/index/loading",style:{navigationStyle:"custom",navigationBarTitleText:"加载页",leftWindow:!1,topWindow:!1}},{path:"pages/index/no_permission",style:{navigationStyle:"custom",navigationBarTitleText:"对不起，您没有权限"}},{path:"pages/index/change_shifts",style:{navigationStyle:"custom",navigationBarTitleText:"交班",leftWindow:!1,topWindow:!1}},{path:"pages/index/change_shiftsrecord",style:{navigationStyle:"custom",navigationBarTitleText:"交班记录"}},{path:"pages/login/login",style:{navigationStyle:"custom",navigationBarTitleText:"登录",leftWindow:!1,topWindow:!1}},{path:"pages/member/list",style:{navigationStyle:"custom",navigationBarTitleText:"会员"}},{path:"pages/order/orderrefund",style:{navigationStyle:"custom",navigationBarTitleText:"退款维权"}},{path:"pages/order/rechargeorder",style:{navigationStyle:"custom",navigationBarTitleText:"充值订单"}},{path:"pages/stat/index",style:{navigationStyle:"custom",navigationBarTitleText:"营业数据"}},{path:"pages/order/orderlist",style:{navigationStyle:"custom",navigationBarTitleText:"订单列表"}},{path:"pages/goods/goodslist",style:{navigationStyle:"custom",navigationBarTitleText:"商品管理"}},{path:"pages/goods/print_price_tag",style:{navigationStyle:"custom",navigationBarTitleText:"打印价格标签"}},{path:"pages/index/change_shiftssalelist",style:{navigationStyle:"custom",navigationBarTitleText:"商品销售"}},{path:"pages/refund/list",style:{navigationStyle:"custom",navigationBarTitleText:"退款记录"}},{path:"pages/user/list",style:{navigationStyle:"custom",navigationBarTitleText:"员工管理"}},{path:"pages/stock/wastage",style:{navigationStyle:"custom",navigationBarTitleText:"出库单"}},{path:"pages/stock/stockout",style:{navigationStyle:"custom",navigationBarTitleText:"编辑出库单"}},{path:"pages/stock/storage",style:{navigationStyle:"custom",navigationBarTitleText:"入库单"}},{path:"pages/stock/stockin",style:{navigationStyle:"custom",navigationBarTitleText:"编辑入库单"}},{path:"pages/stock/allocate",style:{navigationStyle:"custom",navigationBarTitleText:"调拨单"}},{path:"pages/stock/edit_allocate",style:{navigationStyle:"custom",navigationBarTitleText:"编辑调拨单"}},{path:"pages/stock/check",style:{navigationStyle:"custom",navigationBarTitleText:"库存盘点"}},{path:"pages/stock/edit_check",style:{navigationStyle:"custom",navigationBarTitleText:"编辑库存盘点单"}},{path:"pages/stock/manage",style:{navigationStyle:"custom",navigationBarTitleText:"库存管理"}},{path:"pages/stock/records",style:{navigationStyle:"custom",navigationBarTitleText:"库存流水"}},{path:"pages/reserve/config",style:{navigationStyle:"custom",navigationBarTitleText:"预约设置"}},{path:"pages/store/config",style:{navigationStyle:"custom",navigationBarTitleText:"门店设置"}},{path:"pages/store/index",style:{navigationStyle:"custom",navigationBarTitleText:"门店信息"}},{path:"pages/store/operate",style:{navigationStyle:"custom",navigationBarTitleText:"运营设置"}},{path:"pages/store/close",style:{navigationStyle:"custom",navigationBarTitleText:"门店停业"}},{path:"pages/store/settlement",style:{navigationStyle:"custom",navigationBarTitleText:"门店结算"}},{path:"pages/store/settlement_record",style:{navigationStyle:"custom",navigationBarTitleText:"结算记录"}},{path:"pages/store/acccount_record",style:{navigationStyle:"custom",navigationBarTitleText:"账户记录"}},{path:"pages/printer/list",style:{navigationStyle:"custom",navigationBarTitleText:"小票打印"}},{path:"pages/printer/add",style:{navigationStyle:"custom",navigationBarTitleText:"打印机添加"}},{path:"pages/store/deliver",style:{navigationStyle:"custom",navigationBarTitleText:"配送员"}},{path:"pages/collectmoney/config",style:{navigationStyle:"custom",navigationBarTitleText:"收款设置"}},{path:"pages/local/config",style:{navigationStyle:"custom",navigationBarTitleText:"本机设置"}},{path:"pages/scale/list",style:{navigationStyle:"custom",navigationBarTitleText:"电子秤"}},{path:"pages/scale/add",style:{navigationStyle:"custom",navigationBarTitleText:"添加电子秤"}},{path:"pages/marketing/coupon_list",style:{navigationStyle:"custom",navigationBarTitleText:"优惠券"}},{path:"pages/marketing/edit_coupon",style:{navigationStyle:"custom",navigationBarTitleText:"编辑优惠券"}},{path:"pages/marketing/coupon_detail",style:{navigationStyle:"custom",navigationBarTitleText:"优惠券详情"}}],globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#EFF0F4",backgroundColor:"#EFF0F4",background:"#22243a",pageOrientation:"landscape",animationType:"none",style:{"app-plus":{background:"#EFF0F4",bounceBackground:"#EFF0F4"}}},tabBar:{custom:!0,color:"#303133",selectedColor:"#fa5d14",backgroundColor:"#fff",borderStyle:"white",list:[{pagePath:"pages/billing/index",text:"开单"},{pagePath:"pages/reserve/index",text:"预约"},{pagePath:"pages/buycard/index",text:"售卡"},{pagePath:"pages/verify/index",text:"核销"},{pagePath:"pages/recharge/index",text:"充值"}]},easycom:{autoscan:!0,custom:{"layout-(.*)":"@/layout/layout-$1/base-page.vue","uni-(.*)":"@/components/uni-$1/uni-$1.vue","nc-(.*)":"@/components/nc-$1/nc-$1.vue"}},leftWindow:{path:"layout/layout-default/components/layout-aside.vue"}}},5075:function(e,t,n){"use strict";var r=n("ae5c"),a=n("71e9"),i=n("e7e3"),o=n("52df"),s=n("81a7"),u=n("1fc1"),c=n("1297"),l=n("d67c"),f=n("5112"),d=n("7e91"),h=TypeError,p=function(e,t){this.stopped=e,this.result=t},g=p.prototype;e.exports=function(e,t,n){var v,m,y,k,_,w,b,x=n&&n.that,T=!(!n||!n.AS_ENTRIES),S=!(!n||!n.IS_RECORD),I=!(!n||!n.IS_ITERATOR),P=!(!n||!n.INTERRUPTED),O=r(t,x),A=function(e){return v&&d(v,"normal",e),new p(!0,e)},E=function(e){return T?(i(e),P?O(e[0],e[1],A):O(e[0],e[1])):P?O(e,A):O(e)};if(S)v=e.iterator;else if(I)v=e;else{if(m=f(e),!m)throw new h(o(e)+" is not iterable");if(s(m)){for(y=0,k=u(e);k>y;y++)if(_=E(e[y]),_&&c(g,_))return _;return new p(!1)}v=l(e,m)}w=S?e.next:v.next;while(!(b=a(w,v)).done){try{_=E(b.value)}catch(C){d(v,"throw",C)}if("object"==typeof _&&_&&c(g,_))return _}return new p(!1)}},"57e7":function(e,t,n){"use strict";var r=n("e37c"),a=n("e4ca"),i=n("a74c"),o=n("ae5c"),s=n("b720"),u=n("1eb8"),c=n("5075"),l=n("0cc2"),f=n("97ed"),d=n("437f"),h=n("ab4a"),p=n("d0b1").fastKey,g=n("235c"),v=g.set,m=g.getterFor;e.exports={getConstructor:function(e,t,n,l){var f=e((function(e,a){s(e,d),v(e,{type:t,index:r(null),first:void 0,last:void 0,size:0}),h||(e.size=0),u(a)||c(a,e[l],{that:e,AS_ENTRIES:n})})),d=f.prototype,g=m(t),y=function(e,t,n){var r,a,i=g(e),o=k(e,t);return o?o.value=n:(i.last=o={index:a=p(t,!0),key:t,value:n,previous:r=i.last,next:void 0,removed:!1},i.first||(i.first=o),r&&(r.next=o),h?i.size++:e.size++,"F"!==a&&(i.index[a]=o)),e},k=function(e,t){var n,r=g(e),a=p(t);if("F"!==a)return r.index[a];for(n=r.first;n;n=n.next)if(n.key===t)return n};return i(d,{clear:function(){var e=g(this),t=e.first;while(t)t.removed=!0,t.previous&&(t.previous=t.previous.next=void 0),t=t.next;e.first=e.last=void 0,e.index=r(null),h?e.size=0:this.size=0},delete:function(e){var t=g(this),n=k(this,e);if(n){var r=n.next,a=n.previous;delete t.index[n.index],n.removed=!0,a&&(a.next=r),r&&(r.previous=a),t.first===n&&(t.first=r),t.last===n&&(t.last=a),h?t.size--:this.size--}return!!n},forEach:function(e){var t,n=g(this),r=o(e,arguments.length>1?arguments[1]:void 0);while(t=t?t.next:n.first){r(t.value,t.key,this);while(t&&t.removed)t=t.previous}},has:function(e){return!!k(this,e)}}),i(d,n?{get:function(e){var t=k(this,e);return t&&t.value},set:function(e,t){return y(this,0===e?0:e,t)}}:{add:function(e){return y(this,e=0===e?0:e,e)}}),h&&a(d,"size",{configurable:!0,get:function(){return g(this).size}}),f},setStrong:function(e,t,n){var r=t+" Iterator",a=m(t),i=m(r);l(e,t,(function(e,t){v(this,{type:r,target:e,state:a(e),kind:t,last:void 0})}),(function(){var e=i(this),t=e.kind,n=e.last;while(n&&n.removed)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?f("keys"===t?n.key:"values"===t?n.value:[n.key,n.value],!1):(e.target=void 0,f(void 0,!0))}),n?"entries":"values",!n,!0),d(t)}}},"5d6e":function(e,t,n){"use strict";var r=n("af9e");e.exports=r((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}}))},"62b0":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t&&("object"===(0,r.default)(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return(0,a.default)(e)},n("7a76"),n("c9b5");var r=i(n("fcf3")),a=i(n("f478"));function i(e){return e&&e.__esModule?e:{default:e}}},6730:function(e,t,n){"use strict";var r=n("8bdb"),a=n("71e9");r({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return a(URL.prototype.toString,this)}})},"68ef":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=u,n("c1a3"),n("bf0f"),n("18f7"),n("de6c"),n("7a76"),n("c9b5");var r=s(n("f1f8")),a=s(n("e668")),i=s(n("d441")),o=s(n("d2c4"));function s(e){return e&&e.__esModule?e:{default:e}}function u(e){var n="function"===typeof Map?new Map:void 0;return t.default=u=function(e){if(null===e||!(0,i.default)(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return(0,o.default)(e,arguments,(0,r.default)(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),(0,a.default)(t,e)},u(e)}},"6c31":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}},n("bf0f"),n("7996"),n("6a88")},7658:function(e,t,n){"use strict";var r=n("8bdb"),a=n("85c1"),i=n("bb80"),o=n("8466"),s=n("81a9"),u=n("d0b1"),c=n("5075"),l=n("b720"),f=n("474f"),d=n("1eb8"),h=n("1c06"),p=n("af9e"),g=n("29ba"),v=n("181d"),m=n("dcda");e.exports=function(e,t,n){var y=-1!==e.indexOf("Map"),k=-1!==e.indexOf("Weak"),_=y?"set":"add",w=a[e],b=w&&w.prototype,x=w,T={},S=function(e){var t=i(b[e]);s(b,e,"add"===e?function(e){return t(this,0===e?0:e),this}:"delete"===e?function(e){return!(k&&!h(e))&&t(this,0===e?0:e)}:"get"===e?function(e){return k&&!h(e)?void 0:t(this,0===e?0:e)}:"has"===e?function(e){return!(k&&!h(e))&&t(this,0===e?0:e)}:function(e,n){return t(this,0===e?0:e,n),this})},I=o(e,!f(w)||!(k||b.forEach&&!p((function(){(new w).entries().next()}))));if(I)x=n.getConstructor(t,e,y,_),u.enable();else if(o(e,!0)){var P=new x,O=P[_](k?{}:-0,1)!==P,A=p((function(){P.has(1)})),E=g((function(e){new w(e)})),C=!k&&p((function(){var e=new w,t=5;while(t--)e[_](t,t);return!e.has(-0)}));E||(x=t((function(e,t){l(e,b);var n=m(new w,e,x);return d(t)||c(t,n[_],{that:n,AS_ENTRIES:y}),n})),x.prototype=b,b.constructor=x),(A||C)&&(S("delete"),S("has"),y&&S("get")),(C||O)&&S(_),k&&b.clear&&delete b.clear}return T[e]=x,r({global:!0,constructor:!0,forced:x!==w},T),v(x,e),k||n.setStrong(x,e,y),x}},"861b":function(e,t,n){"use strict";(function(e){var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uniCloud=t.default=t.UniCloudError=void 0;var a=r(n("f478")),i=r(n("5de6")),o=r(n("fcf3")),s=r(n("b7c7")),u=r(n("3471")),c=r(n("2634")),l=r(n("2fdc")),f=r(n("9b1b")),d=r(n("acb1")),h=r(n("cad9")),p=r(n("68ef")),g=r(n("80b1")),v=r(n("efe5"));n("4085"),n("7a76"),n("c9b5"),n("bf0f"),n("ab80"),n("f7a5"),n("aa9c"),n("e966"),n("c223"),n("dd2b"),n("5ef2"),n("2797"),n("dc8a"),n("473f"),n("4626"),n("5ac7"),n("4100"),n("5c47"),n("d4b5"),n("0c26"),n("0506"),n("fd3c"),n("6a54"),n("a1c1"),n("de6c"),n("c1a3"),n("18f7"),n("af8f"),n("64aa"),n("8f71"),n("23f4"),n("7d2f"),n("9c4e"),n("4db2"),n("c976"),n("4d8f"),n("7b97"),n("668a"),n("c5b7"),n("8ff5"),n("2378"),n("641a"),n("64e0"),n("cce3"),n("efba"),n("d009"),n("bd7d"),n("7edd"),n("d798"),n("f547"),n("5e54"),n("b60a"),n("8c18"),n("12973"),n("f991"),n("198e"),n("8557"),n("63b1"),n("1954"),n("1cf1"),n("01a2"),n("e39c"),n("e062"),n("aa77"),n("2c10"),n("f555"),n("dc69"),n("9370"),n("6730"),n("08eb"),n("15d1"),n("d5c6"),n("5a56"),n("f074"),n("20f3");var m=r(n("49b7"));function y(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof e||"undefined"!=typeof self&&self;var k=y((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),r={},a=r.lib={},i=a.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},o=a.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,a=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<a;i++){var o=n[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=o<<24-(r+i)%4*8}else for(i=0;i<a;i+=4)t[r+i>>>2]=n[i>>>2];return this.sigBytes+=a,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,r=[],a=function(t){var n=987654321,r=4294967295;return function(){var a=((n=36969*(65535&n)+(n>>16)&r)<<16)+(t=18e3*(65535&t)+(t>>16)&r)&r;return a/=4294967296,(a+=.5)*(e.random()>.5?1:-1)}},i=0;i<t;i+=4){var s=a(4294967296*(n||e.random()));n=987654071*s(),r.push(4294967296*s()|0)}return new o.init(r,t)}}),s=r.enc={},u=s.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],a=0;a<n;a++){var i=t[a>>>2]>>>24-a%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new o.init(n,t/2)}},c=s.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],a=0;a<n;a++){var i=t[a>>>2]>>>24-a%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new o.init(n,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},f=a.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new o.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,a=n.sigBytes,i=this.blockSize,s=a/(4*i),u=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*i,c=e.min(4*u,a);if(u){for(var l=0;l<u;l+=i)this._doProcessBlock(r,l);var f=r.splice(0,u);n.sigBytes-=c}return new o.init(f,c)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});a.Hasher=f.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new d.HMAC.init(e,n).finalize(t)}}});var d=r.algo={};return r}(Math),n)})),_=k,w=(y((function(e,t){var n;e.exports=(n=_,function(e){var t=n,r=t.lib,a=r.WordArray,i=r.Hasher,o=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var u=o.MD5=i.extend({_doReset:function(){this._hash=new a.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,a=e[r];e[r]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}var i=this._hash.words,o=e[t+0],u=e[t+1],h=e[t+2],p=e[t+3],g=e[t+4],v=e[t+5],m=e[t+6],y=e[t+7],k=e[t+8],_=e[t+9],w=e[t+10],b=e[t+11],x=e[t+12],T=e[t+13],S=e[t+14],I=e[t+15],P=i[0],O=i[1],A=i[2],E=i[3];P=c(P,O,A,E,o,7,s[0]),E=c(E,P,O,A,u,12,s[1]),A=c(A,E,P,O,h,17,s[2]),O=c(O,A,E,P,p,22,s[3]),P=c(P,O,A,E,g,7,s[4]),E=c(E,P,O,A,v,12,s[5]),A=c(A,E,P,O,m,17,s[6]),O=c(O,A,E,P,y,22,s[7]),P=c(P,O,A,E,k,7,s[8]),E=c(E,P,O,A,_,12,s[9]),A=c(A,E,P,O,w,17,s[10]),O=c(O,A,E,P,b,22,s[11]),P=c(P,O,A,E,x,7,s[12]),E=c(E,P,O,A,T,12,s[13]),A=c(A,E,P,O,S,17,s[14]),P=l(P,O=c(O,A,E,P,I,22,s[15]),A,E,u,5,s[16]),E=l(E,P,O,A,m,9,s[17]),A=l(A,E,P,O,b,14,s[18]),O=l(O,A,E,P,o,20,s[19]),P=l(P,O,A,E,v,5,s[20]),E=l(E,P,O,A,w,9,s[21]),A=l(A,E,P,O,I,14,s[22]),O=l(O,A,E,P,g,20,s[23]),P=l(P,O,A,E,_,5,s[24]),E=l(E,P,O,A,S,9,s[25]),A=l(A,E,P,O,p,14,s[26]),O=l(O,A,E,P,k,20,s[27]),P=l(P,O,A,E,T,5,s[28]),E=l(E,P,O,A,h,9,s[29]),A=l(A,E,P,O,y,14,s[30]),P=f(P,O=l(O,A,E,P,x,20,s[31]),A,E,v,4,s[32]),E=f(E,P,O,A,k,11,s[33]),A=f(A,E,P,O,b,16,s[34]),O=f(O,A,E,P,S,23,s[35]),P=f(P,O,A,E,u,4,s[36]),E=f(E,P,O,A,g,11,s[37]),A=f(A,E,P,O,y,16,s[38]),O=f(O,A,E,P,w,23,s[39]),P=f(P,O,A,E,T,4,s[40]),E=f(E,P,O,A,o,11,s[41]),A=f(A,E,P,O,p,16,s[42]),O=f(O,A,E,P,m,23,s[43]),P=f(P,O,A,E,_,4,s[44]),E=f(E,P,O,A,x,11,s[45]),A=f(A,E,P,O,I,16,s[46]),P=d(P,O=f(O,A,E,P,h,23,s[47]),A,E,o,6,s[48]),E=d(E,P,O,A,y,10,s[49]),A=d(A,E,P,O,S,15,s[50]),O=d(O,A,E,P,v,21,s[51]),P=d(P,O,A,E,x,6,s[52]),E=d(E,P,O,A,p,10,s[53]),A=d(A,E,P,O,w,15,s[54]),O=d(O,A,E,P,u,21,s[55]),P=d(P,O,A,E,k,6,s[56]),E=d(E,P,O,A,I,10,s[57]),A=d(A,E,P,O,m,15,s[58]),O=d(O,A,E,P,T,21,s[59]),P=d(P,O,A,E,g,6,s[60]),E=d(E,P,O,A,b,10,s[61]),A=d(A,E,P,O,h,15,s[62]),O=d(O,A,E,P,_,21,s[63]),i[0]=i[0]+P|0,i[1]=i[1]+O|0,i[2]=i[2]+A|0,i[3]=i[3]+E|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,a=8*t.sigBytes;n[a>>>5]|=128<<24-a%32;var i=e.floor(r/4294967296),o=r;n[15+(a+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(a+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,u=s.words,c=0;c<4;c++){var l=u[c];u[c]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,r,a,i,o){var s=e+(t&n|~t&r)+a+o;return(s<<i|s>>>32-i)+t}function l(e,t,n,r,a,i,o){var s=e+(t&r|n&~r)+a+o;return(s<<i|s>>>32-i)+t}function f(e,t,n,r,a,i,o){var s=e+(t^n^r)+a+o;return(s<<i|s>>>32-i)+t}function d(e,t,n,r,a,i,o){var s=e+(n^(t|~r))+a+o;return(s<<i|s>>>32-i)+t}t.MD5=i._createHelper(u),t.HmacMD5=i._createHmacHelper(u)}(Math),n.MD5)})),y((function(e,t){var n;e.exports=(n=_,void function(){var e=n,t=e.lib.Base,r=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,a=4*n;t.sigBytes>a&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),o=this._iKey=t.clone(),s=i.words,u=o.words,c=0;c<n;c++)s[c]^=1549556828,u[c]^=909522486;i.sigBytes=o.sigBytes=a,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())})),y((function(e,t){e.exports=_.HmacMD5}))),b=y((function(e,t){e.exports=_.enc.Utf8})),x=y((function(e,t){var n;e.exports=(n=_,function(){var e=n,t=e.lib.WordArray;function r(e,n,r){for(var a=[],i=0,o=0;o<n;o++)if(o%4){var s=r[e.charCodeAt(o-1)]<<o%4*2,u=r[e.charCodeAt(o)]>>>6-o%4*2;a[i>>>2]|=(s|u)<<24-i%4*8,i++}return t.create(a,i)}e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var a=[],i=0;i<n;i+=3)for(var o=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<n;s++)a.push(r.charAt(o>>>6*(3-s)&63));var u=r.charAt(64);if(u)for(;a.length%4;)a.push(u);return a.join("")},parse:function(e){var t=e.length,n=this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var i=0;i<n.length;i++)a[n.charCodeAt(i)]=i}var o=n.charAt(64);if(o){var s=e.indexOf(o);-1!==s&&(t=s)}return r(e,t,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),n.enc.Base64)})),T="uni_id_token",S="uni_id_token_expired",I={DEFAULT:"FUNCTION",FUNCTION:"FUNCTION",OBJECT:"OBJECT",CLIENT_DB:"CLIENT_DB"},P="pending",O="fulfilled",A="rejected";function E(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function C(e){return"object"===E(e)}function L(e){return"function"==typeof e}function N(e){return function(){try{return e.apply(e,arguments)}catch(e){console.error(e)}}}var R="REJECTED",U="NOT_PENDING",D=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.createPromise,r=t.retryRule,a=void 0===r?R:r;(0,g.default)(this,e),this.createPromise=n,this.status=null,this.promise=null,this.retryRule=a}return(0,v.default)(e,[{key:"needRetry",get:function(){if(!this.status)return!0;switch(this.retryRule){case R:return this.status===A;case U:return this.status!==P}}},{key:"exec",value:function(){var e=this;return this.needRetry?(this.status=P,this.promise=this.createPromise().then((function(t){return e.status=O,Promise.resolve(t)}),(function(t){return e.status=A,Promise.reject(t)})),this.promise):this.promise}}]),e}(),M=function(){function e(){(0,g.default)(this,e),this._callback={}}return(0,v.default)(e,[{key:"addListener",value:function(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}},{key:"on",value:function(e,t){return this.addListener(e,t)}},{key:"removeListener",value:function(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');var n=this._callback[e];if(n){var r=function(e,t){for(var n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(r,1)}}},{key:"off",value:function(e,t){return this.removeListener(e,t)}},{key:"removeAllListener",value:function(e){delete this._callback[e]}},{key:"emit",value:function(e){for(var t=this._callback[e],n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];if(t)for(var i=0;i<t.length;i++)t[i].apply(t,r)}}]),e}();function F(e){return e&&"string"==typeof e?JSON.parse(e):e}var j=F([]),q="web",B=(F(void 0),F([])||[]);try{(n("0e57").default||n("0e57")).appid}catch(yr){}var K,J={};function W(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=J,n=e,Object.prototype.hasOwnProperty.call(t,n)||(J[e]=r),J[e]}"app"===q&&(J=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={});var H=["invoke","success","fail","complete"],z=W("_globalUniCloudInterceptor");function V(e,t){z[e]||(z[e]={}),C(t)&&Object.keys(t).forEach((function(n){H.indexOf(n)>-1&&function(e,t,n){var r=z[e][t];r||(r=z[e][t]=[]),-1===r.indexOf(n)&&L(n)&&r.push(n)}(e,n,t[n])}))}function $(e,t){z[e]||(z[e]={}),C(t)?Object.keys(t).forEach((function(n){H.indexOf(n)>-1&&function(e,t,n){var r=z[e][t];if(r){var a=r.indexOf(n);a>-1&&r.splice(a,1)}}(e,n,t[n])})):delete z[e]}function G(e,t){return e&&0!==e.length?e.reduce((function(e,n){return e.then((function(){return n(t)}))}),Promise.resolve()):Promise.resolve()}function Q(e,t){return z[e]&&z[e][t]||[]}function Y(e){V("callObject",e)}var X=W("_globalUniCloudListener"),Z={RESPONSE:"response",NEED_LOGIN:"needLogin",REFRESH_TOKEN:"refreshToken"},ee={CLIENT_DB:"clientdb",CLOUD_FUNCTION:"cloudfunction",CLOUD_OBJECT:"cloudobject"};function te(e){return X[e]||(X[e]=[]),X[e]}function ne(e,t){var n=te(e);n.includes(t)||n.push(t)}function re(e,t){var n=te(e),r=n.indexOf(t);-1!==r&&n.splice(r,1)}function ae(e,t){for(var n=te(e),r=0;r<n.length;r++)(0,n[r])(t)}var ie,oe=!1;function se(){return ie||(ie=new Promise((function(e){oe&&e(),function t(){if("function"==typeof getCurrentPages){var n=getCurrentPages();n&&n[0]&&(oe=!0,e())}oe||setTimeout((function(){t()}),30)}()})),ie)}function ue(e){var t={};for(var n in e){var r=e[n];L(r)&&(t[n]=N(r))}return t}var ce=function(e){(0,d.default)(n,e);var t=(0,h.default)(n);function n(e){var r;return(0,g.default)(this,n),r=t.call(this,e.message),r.errMsg=e.message||e.errMsg||"unknown system error",r.code=r.errCode=e.code||e.errCode||"SYSTEM_ERROR",r.errSubject=r.subject=e.subject||e.errSubject,r.cause=e.cause,r.requestId=e.requestId,r}return(0,v.default)(n,[{key:"toJson",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}]),n}((0,p.default)(Error));t.UniCloudError=ce;var le,fe,de={request:function(e){return uni.request(e)},uploadFile:function(e){return uni.uploadFile(e)},setStorageSync:function(e,t){return uni.setStorageSync(e,t)},getStorageSync:function(e){return uni.getStorageSync(e)},removeStorageSync:function(e){return uni.removeStorageSync(e)},clearStorageSync:function(){return uni.clearStorageSync()},connectSocket:function(e){return uni.connectSocket(e)}};function he(){return{token:de.getStorageSync(T)||de.getStorageSync("uniIdToken"),tokenExpired:de.getStorageSync(S)}}function pe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.token,n=e.tokenExpired;t&&de.setStorageSync(T,t),n&&de.setStorageSync(S,n)}function ge(){return le||(le=uni.getSystemInfoSync()),le}var ve={};function me(){var e=uni.getLocale&&uni.getLocale()||"en";if(fe)return(0,f.default)((0,f.default)((0,f.default)({},ve),fe),{},{locale:e,LOCALE:e});var t=ge(),n=t.deviceId,r=t.osName,a=t.uniPlatform,i=t.appId,o=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(var s in t)Object.hasOwnProperty.call(t,s)&&-1===o.indexOf(s)&&delete t[s];return fe=(0,f.default)((0,f.default)({PLATFORM:a,OS:r,APPID:i,DEVICEID:n},function(){var e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;var n=uni.getLaunchOptionsSync(),r=n.scene,a=n.channel;e=a,t=r}}catch(e){}return{channel:e,scene:t}}()),t),(0,f.default)((0,f.default)((0,f.default)({},ve),fe),{},{locale:e,LOCALE:e})}var ye,ke={sign:function(e,t){var n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),w(n,t).toString()},wrappedRequest:function(e,t){return new Promise((function(n,r){t(Object.assign(e,{complete:function(e){e||(e={});var t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){var a=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",i=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return r(new ce({code:a,message:i,requestId:t}))}var o=e.data;if(o.error)return r(new ce({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,n(o)}}))}))},toBase64:function(e){return x.stringify(b.parse(e))}},_e=function(){function e(t){var n=this;(0,g.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),this.config=Object.assign({},{endpoint:0===t.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},t),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=de,this._getAccessTokenPromiseHub=new D({createPromise:function(){return n.requestAuth(n.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((function(e){if(!e.result||!e.result.accessToken)throw new ce({code:"AUTH_FAILED",message:"获取accessToken失败"});n.setAccessToken(e.result.accessToken)}))},retryRule:U})}return(0,v.default)(e,[{key:"hasAccessToken",get:function(){return!!this.accessToken}},{key:"setAccessToken",value:function(e){this.accessToken=e}},{key:"requestWrapped",value:function(e){return ke.wrappedRequest(e,this.adapter.request)}},{key:"requestAuth",value:function(e){return this.requestWrapped(e)}},{key:"request",value:function(e,t){var n=this;return Promise.resolve().then((function(){return n.hasAccessToken?t?n.requestWrapped(e):n.requestWrapped(e).catch((function(t){return new Promise((function(e,n){!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((function(){return n.getAccessToken()})).then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))})):n.getAccessToken().then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))}))}},{key:"rebuildRequest",value:function(e){var t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=ke.sign(t.data,this.config.clientSecret),t}},{key:"setupRequest",value:function(e,t){var n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),r={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,r["x-basement-token"]=this.accessToken),r["x-serverless-sign"]=ke.sign(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:r}}},{key:"getAccessToken",value:function(){return this._getAccessTokenPromiseHub.exec()}},{key:"authorize",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getAccessToken();case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request((0,f.default)((0,f.default)({},this.setupRequest(t)),{},{timeout:e.timeout}))}},{key:"getOSSUploadOptionsFromPath",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFileToOSS",value:function(e){var t=this,n=e.url,r=e.formData,a=e.name,i=e.filePath,o=e.fileType,s=e.onUploadProgress;return new Promise((function(e,u){var c=t.adapter.uploadFile({url:n,formData:r,name:a,filePath:i,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success:function(t){t&&t.statusCode<400?e(t):u(new ce({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){u(new ce({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"reportOSSUpload",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFile",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r,a,i,o,s,u,l,f,d,h,p,g,v,m,y,k,_,w,b,x,T;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,a=t.fileType,i=void 0===a?"image":a,o=t.cloudPathAsRealPath,s=void 0!==o&&o,u=t.onUploadProgress,l=t.config,"string"===E(r)){e.next=3;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(r=r.trim()){e.next=5;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(r)){e.next=7;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:if(f=l&&l.envType||this.config.envType,!(s&&("/"!==r[0]&&(r="/"+r),r.indexOf("\\")>-1))){e.next=10;break}throw new ce({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});case 10:return e.next=12,this.getOSSUploadOptionsFromPath({env:f,filename:s?r.split("/").pop():r,fileId:s?r:void 0});case 12:return d=e.sent.result,h="https://"+d.cdnDomain+"/"+d.ossPath,p=d.securityToken,g=d.accessKeyId,v=d.signature,m=d.host,y=d.ossPath,k=d.id,_=d.policy,w=d.ossCallbackUrl,b={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:g,Signature:v,host:m,id:k,key:y,policy:_,success_action_status:200},p&&(b["x-oss-security-token"]=p),w&&(x=JSON.stringify({callbackUrl:w,callbackBody:JSON.stringify({fileId:k,spaceId:this.config.spaceId}),callbackBodyType:"application/json"}),b.callback=ke.toBase64(x)),T={url:"https://"+d.host,formData:b,fileName:"file",name:"file",filePath:n,fileType:i},e.next=27,this.uploadFileToOSS(Object.assign({},T,{onUploadProgress:u}));case 27:if(!w){e.next=29;break}return e.abrupt("return",{success:!0,filePath:n,fileID:h});case 29:return e.next=31,this.reportOSSUpload({id:k});case 31:if(!e.sent.success){e.next=33;break}return e.abrupt("return",{success:!0,filePath:n,fileID:h});case 33:throw new ce({code:"UPLOAD_FAILED",message:"文件上传失败"});case 34:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList;return new Promise((function(e,n){Array.isArray(t)&&0!==t.length||n(new ce({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),e({fileList:t.map((function(e){return{fileID:e,tempFileURL:e}}))})}))}},{key:"getFileInfo",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t,n,r,a=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a.length>0&&void 0!==a[0]?a[0]:{},n=t.fileList,Array.isArray(n)&&0!==n.length){e.next=3;break}throw new ce({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});case 3:return r={method:"serverless.file.resource.info",params:JSON.stringify({id:n.map((function(e){return e.split("?")[0]})).join(",")})},e.next=6,this.request(this.setupRequest(r));case 6:return e.t0=e.sent.result,e.abrupt("return",{fileList:e.t0});case 8:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),we={init:function(e){var t=new _e(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},be="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";!function(e){e.local="local",e.none="none",e.session="session"}(ye||(ye={}));var xe,Te=function(){},Se=y((function(e,t){var n;e.exports=(n=_,function(e){var t=n,r=t.lib,a=r.WordArray,i=r.Hasher,o=t.algo,s=[],u=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,a=0;a<64;)t(r)&&(a<8&&(s[a]=n(e.pow(r,.5))),u[a]=n(e.pow(r,1/3)),a++),r++}();var c=[],l=o.SHA256=i.extend({_doReset:function(){this._hash=new a.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],a=n[1],i=n[2],o=n[3],s=n[4],l=n[5],f=n[6],d=n[7],h=0;h<64;h++){if(h<16)c[h]=0|e[t+h];else{var p=c[h-15],g=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,v=c[h-2],m=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;c[h]=g+c[h-7]+m+c[h-16]}var y=r&a^r&i^a&i,k=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),_=d+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&f)+u[h]+c[h];d=f,f=l,l=s,s=o+_|0,o=i,i=a,a=r,r=_+(k+y)|0}n[0]=n[0]+r|0,n[1]=n[1]+a|0,n[2]=n[2]+i|0,n[3]=n[3]+o|0,n[4]=n[4]+s|0,n[5]=n[5]+l|0,n[6]=n[6]+f|0,n[7]=n[7]+d|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,a=8*t.sigBytes;return n[a>>>5]|=128<<24-a%32,n[14+(a+64>>>9<<4)]=e.floor(r/4294967296),n[15+(a+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(l),t.HmacSHA256=i._createHmacHelper(l)}(Math),n.SHA256)})),Ie=Se,Pe=y((function(e,t){e.exports=_.HmacSHA256})),Oe=function(){var e;if(!Promise){e=function(){},e.promise={};var t=function(){throw new ce({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var n=new Promise((function(t,n){e=function(e,r){return e?n(e):t(r)}}));return e.promise=n,e};function Ae(e){return void 0===e}function Ee(e){return"[object Null]"===Object.prototype.toString.call(e)}function Ce(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function Le(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:32,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n="",r=0;r<e;r++)n+=t.charAt(Math.floor(62*Math.random()));return n}!function(e){e.WEB="web",e.WX_MP="wx_mp"}(xe||(xe={}));var Ne={adapter:null,runtime:void 0},Re=["anonymousUuidKey"],Ue=function(e){(0,d.default)(n,e);var t=(0,h.default)(n);function n(){var e;return(0,g.default)(this,n),e=t.call(this),Ne.adapter.root.tcbObject||(Ne.adapter.root.tcbObject={}),e}return(0,v.default)(n,[{key:"setItem",value:function(e,t){Ne.adapter.root.tcbObject[e]=t}},{key:"getItem",value:function(e){return Ne.adapter.root.tcbObject[e]}},{key:"removeItem",value:function(e){delete Ne.adapter.root.tcbObject[e]}},{key:"clear",value:function(){delete Ne.adapter.root.tcbObject}}]),n}(Te);function De(e,t){switch(e){case"local":return t.localStorage||new Ue;case"none":return new Ue;default:return t.sessionStorage||new Ue}}var Me=function(){function e(t){if((0,g.default)(this,e),!this._storage){this._persistence=Ne.adapter.primaryStorage||t.persistence,this._storage=De(this._persistence,Ne.adapter);var n="access_token_".concat(t.env),r="access_token_expire_".concat(t.env),a="refresh_token_".concat(t.env),i="anonymous_uuid_".concat(t.env),o="login_type_".concat(t.env),s="token_type_".concat(t.env),u="user_info_".concat(t.env);this.keys={accessTokenKey:n,accessTokenExpireKey:r,refreshTokenKey:a,anonymousUuidKey:i,loginTypeKey:o,userInfoKey:u,deviceIdKey:"device_id",tokenTypeKey:s}}}return(0,v.default)(e,[{key:"updatePersistence",value:function(e){if(e!==this._persistence){var t="local"===this._persistence;this._persistence=e;var n=De(e,Ne.adapter);for(var r in this.keys){var a=this.keys[r];if(!t||!Re.includes(r)){var i=this._storage.getItem(a);Ae(i)||Ee(i)||(n.setItem(a,i),this._storage.removeItem(a))}}this._storage=n}}},{key:"setStore",value:function(e,t,n){if(this._storage){var r={version:n||"localCachev1",content:t},a=JSON.stringify(r);try{this._storage.setItem(e,a)}catch(e){throw e}}}},{key:"getStore",value:function(e,t){try{if(!this._storage)return}catch(e){return""}t=t||"localCachev1";var n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}},{key:"removeStore",value:function(e){this._storage.removeItem(e)}}]),e}(),Fe={},je={};function qe(e){return Fe[e]}var Be=(0,v.default)((function e(t,n){(0,g.default)(this,e),this.data=n||null,this.name=t})),Ke=function(e){(0,d.default)(n,e);var t=(0,h.default)(n);function n(e,r){var a;return(0,g.default)(this,n),a=t.call(this,"error",{error:e,data:r}),a.error=e,a}return(0,v.default)(n)}(Be),Je=new(function(){function e(){(0,g.default)(this,e),this._listeners={}}return(0,v.default)(e,[{key:"on",value:function(e,t){return function(e,t,n){n[e]=n[e]||[],n[e].push(t)}(e,t,this._listeners),this}},{key:"off",value:function(e,t){return function(e,t,n){if(n&&n[e]){var r=n[e].indexOf(t);-1!==r&&n[e].splice(r,1)}}(e,t,this._listeners),this}},{key:"fire",value:function(e,t){if(e instanceof Ke)return console.error(e.error),this;var n="string"==typeof e?new Be(e,t||{}):e,r=n.name;if(this._listens(r)){n.target=this;var a,i=this._listeners[r]?(0,s.default)(this._listeners[r]):[],o=(0,u.default)(i);try{for(o.s();!(a=o.n()).done;){var c=a.value;c.call(this,n)}}catch(l){o.e(l)}finally{o.f()}}return this}},{key:"_listens",value:function(e){return this._listeners[e]&&this._listeners[e].length>0}}]),e}());function We(e,t){Je.on(e,t)}function He(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Je.fire(e,t)}function ze(e,t){Je.off(e,t)}var Ve,$e="loginStateChanged",Ge="loginStateExpire",Qe="loginTypeChanged",Ye="anonymousConverted",Xe="refreshAccessToken";!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(Ve||(Ve={}));var Ze=function(){function e(){(0,g.default)(this,e),this._fnPromiseMap=new Map}return(0,v.default)(e,[{key:"run",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t,n){var r,a=this;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=this._fnPromiseMap.get(t),e.abrupt("return",(r||(r=new Promise(function(){var e=(0,l.default)((0,c.default)().mark((function e(r,i){var o;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a._runIdlePromise();case 3:return o=n(),e.t0=r,e.next=7,o;case 7:e.t1=e.sent,(0,e.t0)(e.t1),e.next=14;break;case 11:e.prev=11,e.t2=e["catch"](0),i(e.t2);case 14:return e.prev=14,a._fnPromiseMap.delete(t),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,11,14,17]])})));return function(t,n){return e.apply(this,arguments)}}()),this._fnPromiseMap.set(t,r)),r));case 2:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_runIdlePromise",value:function(){return Promise.resolve()}}]),e}(),et=function(){function e(t){(0,g.default)(this,e),this._singlePromise=new Ze,this._cache=qe(t.env),this._baseURL="https://".concat(t.env,".ap-shanghai.tcb-api.tencentcloudapi.com"),this._reqClass=new Ne.adapter.reqClass({timeout:t.timeout,timeoutMsg:"请求在".concat(t.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]})}return(0,v.default)(e,[{key:"_getDeviceId",value:function(){if(this._deviceID)return this._deviceID;var e=this._cache.keys.deviceIdKey,t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=Le(),this._cache.setStore(e,t)),this._deviceID=t,t}},{key:"_request",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t,n){var r,a,i,o,s,u=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=u.length>2&&void 0!==u[2]?u[2]:{},a={"x-request-id":Le(),"x-device-id":this._getDeviceId()},!r.withAccessToken){e.next=9;break}return i=this._cache.keys.tokenTypeKey,e.next=6,this.getAccessToken();case 6:o=e.sent,s=this._cache.getStore(i),a.authorization="".concat(s," ").concat(o);case 9:return e.abrupt("return",this._reqClass["get"===r.method?"get":"post"]({url:"".concat(this._baseURL).concat(t),data:n,headers:a}));case 10:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_fetchAccessToken",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t,n,r,a,i,o,s,u,f,d,h=this;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.loginTypeKey,r=t.accessTokenKey,a=t.accessTokenExpireKey,i=t.tokenTypeKey,o=this._cache.getStore(n),!o||o===Ve.ANONYMOUS){e.next=3;break}throw new ce({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});case 3:return e.next=5,this._singlePromise.run("fetchAccessToken",(0,l.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,h._request("/auth/v1/signin/anonymously",{},{method:"post"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)}))));case 5:return s=e.sent,u=s.access_token,f=s.expires_in,d=s.token_type,e.abrupt("return",(this._cache.setStore(i,d),this._cache.setStore(r,u),this._cache.setStore(a,Date.now()+1e3*f),u));case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"isAccessTokenExpired",value:function(e,t){var n=!0;return e&&t&&(n=t<Date.now()),n}},{key:"getAccessToken",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t,n,r,a,i;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=this._cache.getStore(n),i=this._cache.getStore(r),e.abrupt("return",this.isAccessTokenExpired(a,i)?this._fetchAccessToken():a);case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t,n,r,a;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.loginTypeKey,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,Ve.ANONYMOUS),this.getAccessToken()));case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getUserInfo",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t=this;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._singlePromise.run("getUserInfo",(0,l.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)})))));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),tt=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],nt={"X-SDK-Version":"1.3.5"};function rt(e,t,n){var r=e[t];e[t]=function(t){var a={},i={};n.forEach((function(n){var r=n.call(e,t),o=r.data,s=r.headers;Object.assign(a,o),Object.assign(i,s)}));var o=t.data;return o&&function(){var e;if(e=o,"[object FormData]"!==Object.prototype.toString.call(e))t.data=(0,f.default)((0,f.default)({},o),a);else for(var n in a)o.append(n,a[n])}(),t.headers=(0,f.default)((0,f.default)({},t.headers||{}),i),r.call(e,t)}}function at(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:(0,f.default)((0,f.default)({},nt),{},{"x-seqid":e})}}var it=function(){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,g.default)(this,e),this.config=n,this._reqClass=new Ne.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:"请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]}),this._cache=qe(this.config.env),this._localCache=(t=this.config.env,je[t]),this.oauth=new et(this.config),rt(this._reqClass,"post",[at]),rt(this._reqClass,"upload",[at]),rt(this._reqClass,"download",[at])}return(0,v.default)(e,[{key:"post",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.post(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"upload",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.upload(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"download",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.download(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t,n;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken()),e.prev=1,e.next=4,this._refreshAccessTokenPromise;case 4:t=e.sent,e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](1),n=e.t0;case 10:if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,!n){e.next=12;break}throw n;case 12:return e.abrupt("return",t);case 13:case"end":return e.stop()}}),e,this,[[1,7]])})));return function(){return e.apply(this,arguments)}}()},{key:"_refreshAccessToken",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t,n,r,a,i,o,s,u,l,f,d,h,p;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey,i=t.loginTypeKey,o=t.anonymousUuidKey,this._cache.removeStore(n),this._cache.removeStore(r),s=this._cache.getStore(a),s){e.next=5;break}throw new ce({message:"未登录CloudBase"});case 5:return u={refresh_token:s},e.next=8,this.request("auth.fetchAccessTokenWithRefreshToken",u);case 8:if(l=e.sent,!l.data.code){e.next=21;break}if(f=l.data.code,"SIGN_PARAM_INVALID"!==f&&"REFRESH_TOKEN_EXPIRED"!==f&&"INVALID_REFRESH_TOKEN"!==f){e.next=20;break}if(this._cache.getStore(i)!==Ve.ANONYMOUS||"INVALID_REFRESH_TOKEN"!==f){e.next=19;break}return d=this._cache.getStore(o),h=this._cache.getStore(a),e.next=17,this.send("auth.signInAnonymously",{anonymous_uuid:d,refresh_token:h});case 17:return p=e.sent,e.abrupt("return",(this.setRefreshToken(p.refresh_token),this._refreshAccessToken()));case 19:He(Ge),this._cache.removeStore(a);case 20:throw new ce({code:l.data.code,message:"刷新access token失败：".concat(l.data.code)});case 21:if(!l.data.access_token){e.next=23;break}return e.abrupt("return",(He(Xe),this._cache.setStore(n,l.data.access_token),this._cache.setStore(r,l.data.access_token_expire+Date.now()),{accessToken:l.data.access_token,accessTokenExpire:l.data.access_token_expire}));case 23:l.data.refresh_token&&(this._cache.removeStore(a),this._cache.setStore(a,l.data.refresh_token),this._refreshAccessToken());case 24:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getAccessToken",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t,n,r,a,i,o,s;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey,this._cache.getStore(a)){e.next=3;break}throw new ce({message:"refresh token不存在，登录状态异常"});case 3:if(i=this._cache.getStore(n),o=this._cache.getStore(r),s=!0,e.t0=this._shouldRefreshAccessTokenHook,!e.t0){e.next=9;break}return e.next=8,this._shouldRefreshAccessTokenHook(i,o);case 8:e.t0=!e.sent;case 9:if(e.t1=e.t0,!e.t1){e.next=12;break}s=!1;case 12:return e.abrupt("return",(!i||!o||o<Date.now())&&s?this.refreshAccessToken():{accessToken:i,accessTokenExpire:o});case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"request",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t,n,r){var a,i,o,s,u,l,d,h,p,g,v,m,y,k,_;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a="x-tcb-trace_".concat(this.config.env),i="application/x-www-form-urlencoded",o=(0,f.default)({action:t,env:this.config.env,dataVersion:"2019-08-16"},n),e.t0=-1===tt.indexOf(t),!e.t0){e.next=9;break}return this._cache.keys,e.next=8,this.oauth.getAccessToken();case 8:o.access_token=e.sent;case 9:if("storage.uploadFile"!==t){e.next=15;break}for(u in s=new FormData,s)s.hasOwnProperty(u)&&void 0!==s[u]&&s.append(u,o[u]);i="multipart/form-data",e.next=17;break;case 15:for(l in i="application/json",s={},o)void 0!==o[l]&&(s[l]=o[l]);case 17:return d={headers:{"content-type":i}},r&&r.timeout&&(d.timeout=r.timeout),r&&r.onUploadProgress&&(d.onUploadProgress=r.onUploadProgress),h=this._localCache.getStore(a),h&&(d.headers["X-TCB-Trace"]=h),p=n.parse,g=n.inQuery,v=n.search,m={env:this.config.env},p&&(m.parse=!0),g&&(m=(0,f.default)((0,f.default)({},g),m)),y=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=/\?/.test(t),a="";for(var i in n)""===a?!r&&(t+="?"):a+="&",a+="".concat(i,"=").concat(encodeURIComponent(n[i]));return/^http(s)?\:\/\//.test(t+=a)?t:"".concat(e).concat(t)}(be,"//tcb-api.tencentcloudapi.com/web",m),v&&(y+=v),e.next=28,this.post((0,f.default)({url:y,data:s},d));case 28:if(k=e.sent,_=k.header&&k.header["x-tcb-trace"],_&&this._localCache.setStore(a,_),(200===Number(k.status)||200===Number(k.statusCode))&&k.data){e.next=32;break}throw new ce({code:"NETWORK_ERROR",message:"network request error"});case 32:return e.abrupt("return",k);case 33:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}()},{key:"send",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r,a,i,o=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.next=4,this.request(t,n,(0,f.default)((0,f.default)({},r),{},{onUploadProgress:n.onUploadProgress}));case 4:if(a=e.sent,"ACCESS_TOKEN_DISABLED"!==a.data.code&&"ACCESS_TOKEN_EXPIRED"!==a.data.code||-1!==tt.indexOf(t)){e.next=14;break}return e.next=8,this.oauth.refreshAccessToken();case 8:return e.next=10,this.request(t,n,(0,f.default)((0,f.default)({},r),{},{onUploadProgress:n.onUploadProgress}));case 10:if(i=e.sent,!i.data.code){e.next=13;break}throw new ce({code:i.data.code,message:Ce(i.data.message)});case 13:return e.abrupt("return",i.data);case 14:if(!a.data.code){e.next=16;break}throw new ce({code:a.data.code,message:Ce(a.data.message)});case 16:return e.abrupt("return",a.data);case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,e)}}]),e}(),ot={};function st(e){return ot[e]}var ut=function(){function e(t){(0,g.default)(this,e),this.config=t,this._cache=qe(t.env),this._request=st(t.env)}return(0,v.default)(e,[{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,e)}},{key:"setAccessToken",value:function(e,t){var n=this._cache.keys,r=n.accessTokenKey,a=n.accessTokenExpireKey;this._cache.setStore(r,e),this._cache.setStore(a,t)}},{key:"refreshUserInfo",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t,n;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getUserInfo",{});case 2:return t=e.sent,n=t.data,e.abrupt("return",(this.setLocalUserInfo(n),n));case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e)}}]),e}(),ct=function(){function e(t){if((0,g.default)(this,e),!t)throw new ce({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=t,this._cache=qe(this._envId),this._request=st(this._envId),this.setUserInfo()}return(0,v.default)(e,[{key:"linkWithTicket",value:function(e){if("string"!=typeof e)throw new ce({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}},{key:"linkWithRedirect",value:function(e){e.signInWithRedirect()}},{key:"updatePassword",value:function(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}},{key:"updateEmail",value:function(e){return this._request.send("auth.updateEmail",{newEmail:e})}},{key:"updateUsername",value:function(e){if("string"!=typeof e)throw new ce({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}},{key:"getLinkedUidList",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t,n,r,a;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getLinkedUidList",{});case 2:return t=e.sent,n=t.data,r=!1,a=n.users,e.abrupt("return",(a.forEach((function(e){e.wxOpenId&&e.wxPublicId&&(r=!0)})),{users:a,hasPrimaryUid:r}));case 7:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setPrimaryUid",value:function(e){return this._request.send("auth.setPrimaryUid",{uid:e})}},{key:"unlink",value:function(e){return this._request.send("auth.unlink",{platform:e})}},{key:"update",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r,a,i,o,s,u,l;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.nickName,r=t.gender,a=t.avatarUrl,i=t.province,o=t.country,s=t.city,e.next=8,this._request.send("auth.updateUserInfo",{nickName:n,gender:r,avatarUrl:a,province:i,country:o,city:s});case 8:u=e.sent,l=u.data,this.setLocalUserInfo(l);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refresh",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.oauth.getUserInfo();case 2:return t=e.sent,e.abrupt("return",(this.setLocalUserInfo(t),t));case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setUserInfo",value:function(){var e=this,t=this._cache.keys.userInfoKey,n=this._cache.getStore(t);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((function(t){e[t]=n[t]})),this.location={country:n.country,province:n.province,city:n.city}}},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e),this.setUserInfo()}}]),e}(),lt=function(){function e(t){if((0,g.default)(this,e),!t)throw new ce({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=qe(t);var n=this._cache.keys,r=n.refreshTokenKey,a=n.accessTokenKey,i=n.accessTokenExpireKey,o=this._cache.getStore(r),s=this._cache.getStore(a),u=this._cache.getStore(i);this.credential={refreshToken:o,accessToken:s,accessTokenExpire:u},this.user=new ct(t)}return(0,v.default)(e,[{key:"isAnonymousAuth",get:function(){return this.loginType===Ve.ANONYMOUS}},{key:"isCustomAuth",get:function(){return this.loginType===Ve.CUSTOM}},{key:"isWeixinAuth",get:function(){return this.loginType===Ve.WECHAT||this.loginType===Ve.WECHAT_OPEN||this.loginType===Ve.WECHAT_PUBLIC}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}]),e}(),ft=function(e){(0,d.default)(n,e);var t=(0,h.default)(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,v.default)(n,[{key:"signIn",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._cache.updatePersistence("local"),e.next=3,this._request.oauth.getAccessToken();case 3:return He($e),He(Qe,{env:this.config.env,loginType:Ve.ANONYMOUS,persistence:"local"}),t=new lt(this.config.env),e.next=8,t.user.refresh();case 8:return e.abrupt("return",t);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r,a,i,o,s;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=this._cache.keys,r=n.anonymousUuidKey,a=n.refreshTokenKey,i=this._cache.getStore(r),o=this._cache.getStore(a),e.next=7,this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:i,refresh_token:o,ticket:t});case 7:if(s=e.sent,!s.refresh_token){e.next=16;break}return this._clearAnonymousUUID(),this.setRefreshToken(s.refresh_token),e.next=13,this._request.refreshAccessToken();case 13:return He(Ye,{env:this.config.env}),He(Qe,{loginType:Ve.CUSTOM,persistence:"local"}),e.abrupt("return",{credential:{refreshToken:s.refresh_token}});case 16:throw new ce({message:"匿名转化失败"});case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"_setAnonymousUUID",value:function(e){var t=this._cache.keys,n=t.anonymousUuidKey,r=t.loginTypeKey;this._cache.removeStore(n),this._cache.setStore(n,e),this._cache.setStore(r,Ve.ANONYMOUS)}},{key:"_clearAnonymousUUID",value:function(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}]),n}(ut),dt=function(e){(0,d.default)(n,e);var t=(0,h.default)(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,v.default)(n,[{key:"signIn",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ce({code:"PARAM_ERROR",message:"ticket must be a string"});case 2:return n=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signInWithTicket",{ticket:t,refresh_token:this._cache.getStore(n)||""});case 5:if(r=e.sent,!r.refresh_token){e.next=15;break}return this.setRefreshToken(r.refresh_token),e.next=10,this._request.refreshAccessToken();case 10:return He($e),He(Qe,{env:this.config.env,loginType:Ve.CUSTOM,persistence:this.config.persistence}),e.next=14,this.refreshUserInfo();case 14:return e.abrupt("return",new lt(this.config.env));case 15:throw new ce({message:"自定义登录失败"});case 16:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),n}(ut),ht=function(e){(0,d.default)(n,e);var t=(0,h.default)(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,v.default)(n,[{key:"signIn",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t,n){var r,a,i,o,s;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ce({code:"PARAM_ERROR",message:"email must be a string"});case 2:return r=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signIn",{loginType:"EMAIL",email:t,password:n,refresh_token:this._cache.getStore(r)||""});case 5:if(a=e.sent,i=a.refresh_token,o=a.access_token,s=a.access_token_expire,!i){e.next=22;break}if(this.setRefreshToken(i),!o||!s){e.next=15;break}this.setAccessToken(o,s),e.next=17;break;case 15:return e.next=17,this._request.refreshAccessToken();case 17:return e.next=19,this.refreshUserInfo();case 19:return He($e),He(Qe,{env:this.config.env,loginType:Ve.EMAIL,persistence:this.config.persistence}),e.abrupt("return",new lt(this.config.env));case 22:throw a.code?new ce({code:a.code,message:"邮箱登录失败: ".concat(a.message)}):new ce({message:"邮箱登录失败"});case 23:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"activate",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.activateEndUserMail",{token:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"resetPasswordWithToken",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t,n){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.resetPasswordWithToken",{token:t,newPassword:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(ut),pt=function(e){(0,d.default)(n,e);var t=(0,h.default)(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,v.default)(n,[{key:"signIn",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t,n){var r,a,i,o,s;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ce({code:"PARAM_ERROR",message:"username must be a string"});case 2:return"string"!=typeof n&&(n="",console.warn("password is empty")),r=this._cache.keys.refreshTokenKey,e.next=6,this._request.send("auth.signIn",{loginType:Ve.USERNAME,username:t,password:n,refresh_token:this._cache.getStore(r)||""});case 6:if(a=e.sent,i=a.refresh_token,o=a.access_token_expire,s=a.access_token,!i){e.next=23;break}if(this.setRefreshToken(i),!s||!o){e.next=16;break}this.setAccessToken(s,o),e.next=18;break;case 16:return e.next=18,this._request.refreshAccessToken();case 18:return e.next=20,this.refreshUserInfo();case 20:return He($e),He(Qe,{env:this.config.env,loginType:Ve.USERNAME,persistence:this.config.persistence}),e.abrupt("return",new lt(this.config.env));case 23:throw a.code?new ce({code:a.code,message:"用户名密码登录失败: ".concat(a.message)}):new ce({message:"用户名密码登录失败"});case 24:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(ut),gt=function(){function e(t){(0,g.default)(this,e),this.config=t,this._cache=qe(t.env),this._request=st(t.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),We(Qe,this._onLoginTypeChanged)}return(0,v.default)(e,[{key:"currentUser",get:function(){var e=this.hasLoginState();return e&&e.user||null}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}},{key:"anonymousAuthProvider",value:function(){return new ft(this.config)}},{key:"customAuthProvider",value:function(){return new dt(this.config)}},{key:"emailAuthProvider",value:function(){return new ht(this.config)}},{key:"usernameAuthProvider",value:function(){return new pt(this.config)}},{key:"signInAnonymously",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new ft(this.config).signIn());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signInWithEmailAndPassword",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t,n){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new ht(this.config).signIn(t,n));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"signInWithUsernameAndPassword",value:function(e,t){return new pt(this.config).signIn(e,t)}},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._anonymousAuthProvider||(this._anonymousAuthProvider=new ft(this.config)),We(Ye,this._onAnonymousConverted),e.next=3,this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"signOut",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){var t,n,r,a,i,o;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loginType!==Ve.ANONYMOUS){e.next=2;break}throw new ce({message:"匿名用户不支持登出操作"});case 2:if(t=this._cache.keys,n=t.refreshTokenKey,r=t.accessTokenKey,a=t.accessTokenExpireKey,i=this._cache.getStore(n),i){e.next=5;break}return e.abrupt("return");case 5:return e.next=7,this._request.send("auth.logout",{refresh_token:i});case 7:return o=e.sent,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.removeStore(a),He($e),He(Qe,{env:this.config.env,loginType:Ve.NULL,persistence:this.config.persistence}),o));case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signUpWithEmailAndPassword",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t,n){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.signUpWithEmailAndPassword",{email:t,password:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"sendPasswordResetEmail",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.sendPasswordResetEmail",{email:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"onLoginStateChanged",value:function(e){var t=this;We($e,(function(){var n=t.hasLoginState();e.call(t,n)}));var n=this.hasLoginState();e.call(this,n)}},{key:"onLoginStateExpired",value:function(e){We(Ge,e.bind(this))}},{key:"onAccessTokenRefreshed",value:function(e){We(Xe,e.bind(this))}},{key:"onAnonymousConverted",value:function(e){We(Ye,e.bind(this))}},{key:"onLoginTypeChanged",value:function(e){var t=this;We(Qe,(function(){var n=t.hasLoginState();e.call(t,n)}))}},{key:"getAccessToken",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.getAccessToken();case 2:return e.t0=e.sent.accessToken,e.t1=this.config.env,e.abrupt("return",{accessToken:e.t0,env:e.t1});case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"hasLoginState",value:function(){var e=this._cache.keys,t=e.accessTokenKey,n=e.accessTokenExpireKey,r=this._cache.getStore(t),a=this._cache.getStore(n);return this._request.oauth.isAccessTokenExpired(r,a)?null:new lt(this.config.env)}},{key:"isUsernameRegistered",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new ce({code:"PARAM_ERROR",message:"username must be a string"});case 2:return e.next=4,this._request.send("auth.isUsernameRegistered",{username:t});case 4:return n=e.sent,r=n.data,e.abrupt("return",r&&r.isRegistered);case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getLoginState",value:function(){return Promise.resolve(this.hasLoginState())}},{key:"signInWithTicket",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new dt(this.config).signIn(t));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"shouldRefreshAccessToken",value:function(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}},{key:"getUserInfo",value:function(){return this._request.send("auth.getUserInfo",{}).then((function(e){return e.code?e:(0,f.default)((0,f.default)({},e.data),{},{requestId:e.seqId})}))}},{key:"getAuthHeader",value:function(){var e=this._cache.keys,t=e.refreshTokenKey,n=e.accessTokenKey,r=this._cache.getStore(t);return{"x-cloudbase-credentials":this._cache.getStore(n)+"/@@/"+r}}},{key:"_onAnonymousConverted",value:function(e){var t=e.data.env;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}},{key:"_onLoginTypeChanged",value:function(e){var t=e.data,n=t.loginType,r=t.persistence,a=t.env;a===this.config.env&&(this._cache.updatePersistence(r),this._cache.setStore(this._cache.keys.loginTypeKey,n))}}]),e}(),vt=function(e,t){t=t||Oe();var n=st(this.config.env),r=e.cloudPath,a=e.filePath,i=e.onUploadProgress,o=e.fileType,s=void 0===o?"image":o;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){var o=e.data,u=o.url,c=o.authorization,l=o.token,f=o.fileId,d=o.cosFileId,h=e.requestId,p={key:r,signature:c,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":l};n.upload({url:u,data:p,file:a,name:r,fileType:s,onUploadProgress:i}).then((function(e){201===e.statusCode?t(null,{fileID:f,requestId:h}):t(new ce({code:"STORAGE_REQUEST_FAIL",message:"STORAGE_REQUEST_FAIL: ".concat(e.data)}))})).catch((function(e){t(e)}))})).catch((function(e){t(e)})),t.promise},mt=function(e,t){t=t||Oe();var n=st(this.config.env),r=e.cloudPath;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){t(null,e)})).catch((function(e){t(e)})),t.promise},yt=function(e,t){var n=e.fileList;if(t=t||Oe(),!n||!Array.isArray(n))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};var r,a=(0,u.default)(n);try{for(a.s();!(r=a.n()).done;){var i=r.value;if(!i||"string"!=typeof i)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"}}}catch(s){a.e(s)}finally{a.f()}var o={fileid_list:n};return st(this.config.env).send("storage.batchDeleteFile",o).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},kt=function(e,t){var n=e.fileList;t=t||Oe(),n&&Array.isArray(n)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});var r,a=[],i=(0,u.default)(n);try{for(i.s();!(r=i.n()).done;){var s=r.value;"object"==(0,o.default)(s)?(s.hasOwnProperty("fileID")&&s.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),a.push({fileid:s.fileID,max_age:s.maxAge})):"string"==typeof s?a.push({fileid:s}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"})}}catch(l){i.e(l)}finally{i.f()}var c={file_list:a};return st(this.config.env).send("storage.batchGetDownloadUrl",c).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},_t=function(){var e=(0,l.default)((0,c.default)().mark((function e(t,n){var r,a,i,o;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=t.fileID,e.next=3,kt.call(this,{fileList:[{fileID:r,maxAge:600}]});case 3:if(a=e.sent.fileList[0],"SUCCESS"===a.code){e.next=6;break}return e.abrupt("return",n?n(a):new Promise((function(e){e(a)})));case 6:if(i=st(this.config.env),o=a.download_url,o=encodeURI(o),n){e.next=10;break}return e.abrupt("return",i.download({url:o}));case 10:return e.t0=n,e.next=13,i.download({url:o});case 13:e.t1=e.sent,(0,e.t0)(e.t1);case 15:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),wt=function(e,t){var n,r=e.name,a=e.data,i=e.query,o=e.parse,s=e.search,u=e.timeout,c=t||Oe();try{n=a?JSON.stringify(a):""}catch(r){return Promise.reject(r)}if(!r)return Promise.reject(new ce({code:"PARAM_ERROR",message:"函数名不能为空"}));var l={inQuery:i,parse:o,search:s,function_name:r,request_data:n};return st(this.config.env).send("functions.invokeFunction",l,{timeout:u}).then((function(e){if(e.code)c(null,e);else{var t=e.data.response_data;if(o)c(null,{result:t,requestId:e.requestId});else try{t=JSON.parse(e.data.response_data),c(null,{result:t,requestId:e.requestId})}catch(e){c(new ce({message:"response data must be json"}))}}return c.promise})).catch((function(e){c(e)})),c.promise},bt={timeout:15e3,persistence:"session"},xt={},Tt=function(){function e(t){(0,g.default)(this,e),this.config=t||this.config,this.authObj=void 0}return(0,v.default)(e,[{key:"init",value:function(t){switch(Ne.adapter||(this.requestClient=new Ne.adapter.reqClass({timeout:t.timeout||5e3,timeoutMsg:"请求在".concat((t.timeout||5e3)/1e3,"s内未完成，已中断")})),this.config=(0,f.default)((0,f.default)({},bt),t),!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new e(this.config)}},{key:"auth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.persistence;if(this.authObj)return this.authObj;var n,r=t||Ne.adapter.primaryStorage||bt.persistence;return r!==this.config.persistence&&(this.config.persistence=r),function(e){var t=e.env;Fe[t]=new Me(e),je[t]=new Me((0,f.default)((0,f.default)({},e),{},{persistence:"local"}))}(this.config),n=this.config,ot[n.env]=new it(n),this.authObj=new gt(this.config),this.authObj}},{key:"on",value:function(e,t){return We.apply(this,[e,t])}},{key:"off",value:function(e,t){return ze.apply(this,[e,t])}},{key:"callFunction",value:function(e,t){return wt.apply(this,[e,t])}},{key:"deleteFile",value:function(e,t){return yt.apply(this,[e,t])}},{key:"getTempFileURL",value:function(e,t){return kt.apply(this,[e,t])}},{key:"downloadFile",value:function(e,t){return _t.apply(this,[e,t])}},{key:"uploadFile",value:function(e,t){return vt.apply(this,[e,t])}},{key:"getUploadMetadata",value:function(e,t){return mt.apply(this,[e,t])}},{key:"registerExtension",value:function(e){xt[e.name]=e}},{key:"invokeExtension",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t,n){var r;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=xt[t],r){e.next=3;break}throw new ce({message:"扩展".concat(t," 必须先注册")});case 3:return e.next=5,r.invoke(n,this);case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"useAdapters",value:function(e){var t=function(e){var t,n,r=(t=e,"[object Array]"===Object.prototype.toString.call(t)?e:[e]),a=(0,u.default)(r);try{for(a.s();!(n=a.n()).done;){var i=n.value,o=i.isMatch,s=i.genAdapter,c=i.runtime;if(o())return{adapter:s(),runtime:c}}}catch(l){a.e(l)}finally{a.f()}}(e)||{},n=t.adapter,r=t.runtime;n&&(Ne.adapter=n),r&&(Ne.runtime=r)}}]),e}(),St=new Tt;function It(e,t,n){void 0===n&&(n={});var r=/\?/.test(t),a="";for(var i in n)""===a?!r&&(t+="?"):a+="&",a+=i+"="+encodeURIComponent(n[i]);return/^http(s)?:\/\//.test(t+=a)?t:""+e+t}var Pt=function(){function e(){(0,g.default)(this,e)}return(0,v.default)(e,[{key:"get",value:function(e){var t=e.url,n=e.data,r=e.headers,a=e.timeout;return new Promise((function(e,i){de.request({url:It("https:",t),data:n,method:"GET",header:r,timeout:a,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"post",value:function(e){var t=e.url,n=e.data,r=e.headers,a=e.timeout;return new Promise((function(e,i){de.request({url:It("https:",t),data:n,method:"POST",header:r,timeout:a,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"upload",value:function(e){return new Promise((function(t,n){var r=e.url,a=e.file,i=e.data,o=e.headers,s=e.fileType,u=de.uploadFile({url:It("https:",r),name:"file",formData:Object.assign({},i),filePath:a,fileType:s,header:o,success:function(e){var n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(n.statusCode=parseInt(i.success_action_status,10)),t(n)},fail:function(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&u&&"function"==typeof u.onProgressUpdate&&u.onProgressUpdate((function(t){e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}]),e}(),Ot={setItem:function(e,t){de.setStorageSync(e,t)},getItem:function(e){return de.getStorageSync(e)},removeItem:function(e){de.removeStorageSync(e)},clear:function(){de.clearStorageSync()}},At={genAdapter:function(){return{root:{},reqClass:Pt,localStorage:Ot,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};St.useAdapters(At);var Et=St,Ct=Et.init;Et.init=function(e){e.env=e.spaceId;var t=Ct.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;var n=t.auth;return t.auth=function(e){var t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((function(e){var n;t[e]=(n=t[e],function(e){e=e||{};var t=ue(e),r=t.success,a=t.fail,i=t.complete;if(!(r||a||i))return n.call(this,e);n.call(this,e).then((function(e){r&&r(e),i&&i(e)}),(function(e){a&&a(e),i&&i(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Lt=Et;function Nt(e,t){return Rt.apply(this,arguments)}function Rt(){return Rt=(0,l.default)((0,c.default)().mark((function e(t,n){var r,a,i;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r="http://".concat(t,":").concat(n,"/system/ping"),e.prev=1,e.next=4,i={url:r,timeout:500},new Promise((function(e,t){de.request((0,f.default)((0,f.default)({},i),{},{success:function(t){e(t)},fail:function(e){t(e)}}))}));case 4:return a=e.sent,e.abrupt("return",!(!a.data||0!==a.data.code));case 8:return e.prev=8,e.t0=e["catch"](1),e.abrupt("return",!1);case 11:case"end":return e.stop()}}),e,null,[[1,8]])}))),Rt.apply(this,arguments)}function Ut(e,t){return Dt.apply(this,arguments)}function Dt(){return Dt=(0,l.default)((0,c.default)().mark((function e(t,n){var r,a,i;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=0;case 1:if(!(a<t.length)){e.next=11;break}return i=t[a],e.next=5,Nt(i,n);case 5:if(!e.sent){e.next=8;break}return r=i,e.abrupt("break",11);case 8:a++,e.next=1;break;case 11:return e.abrupt("return",{address:r,port:n});case 12:case"end":return e.stop()}}),e)}))),Dt.apply(this,arguments)}var Mt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"},Ft=function(){function e(t){if((0,g.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),!t.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},t),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=de}return(0,v.default)(e,[{key:"request",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r=this,a=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(a.length>1&&void 0!==a[1])||a[1],n=!1,!n){e.next=8;break}return e.next=5,this.setupLocalRequest(t);case 5:e.t0=e.sent,e.next=9;break;case 8:e.t0=this.setupRequest(t);case 9:return t=e.t0,e.abrupt("return",Promise.resolve().then((function(){return n?r.requestLocal(t):ke.wrappedRequest(t,r.adapter.request)})));case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"requestLocal",value:function(e){var t=this;return new Promise((function(n,r){t.adapter.request(Object.assign(e,{complete:function(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){var t=e.data&&e.data.code||"SYS_ERR",a=e.data&&e.data.message||"request:fail";return r(new ce({code:t,message:a}))}n({success:!0,result:e.data})}}))}))}},{key:"setupRequest",value:function(e){var t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=ke.sign(t,this.config.clientSecret);var r=me();n["x-client-info"]=encodeURIComponent(JSON.stringify(r));var a=he(),i=a.token;return n["x-client-token"]=i,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}},{key:"setupLocalRequest",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r,a,i,o,s,u,l,f;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=me(),r=he(),a=r.token,i=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:n,token:a}),o=this.__dev__&&this.__dev__.debugInfo||{},s=o.address,u=o.servePort,e.next=9,Ut(s,u);case 9:return l=e.sent,f=l.address,e.abrupt("return",{url:"http://".concat(f,":").concat(u,"/").concat(Mt[t.method]),method:"POST",data:i,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))});case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}},{key:"getUploadFileOptions",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}},{key:"reportUploadFile",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}},{key:"uploadFile",value:function(e){var t,n=this,r=e.filePath,a=e.cloudPath,i=e.fileType,o=void 0===i?"image":i,s=e.onUploadProgress;if(!a)throw new ce({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});return this.getUploadFileOptions({cloudPath:a}).then((function(e){var a=e.result,i=a.url,u=a.formData,c=a.name;return t=e.result.fileUrl,new Promise((function(e,t){var a=n.adapter.uploadFile({url:i,formData:u,name:c,filePath:r,fileType:o,success:function(n){n&&n.statusCode<400?e(n):t(new ce({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){t(new ce({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&a&&"function"==typeof a.onProgressUpdate&&a.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((function(){return n.reportUploadFile({cloudPath:a})})).then((function(e){return new Promise((function(n,a){e.success?n({success:!0,filePath:r,fileID:t}):a(new ce({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))}))}},{key:"deleteFile",value:function(e){var t=e.fileList,n={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:t})};return this.request(n).then((function(e){if(e.success)return e.result;throw new ce({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList,n=e.maxAge;if(!Array.isArray(t)||0===t.length)throw new ce({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});var r={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:t,maxAge:n})};return this.request(r).then((function(e){if(e.success)return{fileList:e.result.fileList.map((function(e){return{fileID:e.fileID,tempFileURL:e.tempFileURL}}))};throw new ce({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}]),e}(),jt={init:function(e){var t=new Ft(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},qt=y((function(e,t){e.exports=_.enc.Hex}));function Bt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Kt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.data,r=t.functionName,a=t.method,o=t.headers,s=t.signHeaderKeys,u=void 0===s?[]:s,c=t.config,l=String(Date.now()),f=Bt(),d=Object.assign({},o,{"x-from-app-id":c.spaceAppId,"x-from-env-id":c.spaceId,"x-to-env-id":c.spaceId,"x-from-instance-id":l,"x-from-function-name":r,"x-client-timestamp":l,"x-alipay-source":"client","x-request-id":f,"x-alipay-callid":f,"x-trace-id":f}),h=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(u),p=e.split("?")||[],g=(0,i.default)(p,2),v=g[0],m=void 0===v?"":v,y=g[1],k=void 0===y?"":y,_=function(e){var t="HMAC-SHA256",n=e.signedHeaders.join(";"),r=e.signedHeaders.map((function(t){return"".concat(t.toLowerCase(),":").concat(e.headers[t],"\n")})).join(""),a=Ie(e.body).toString(qt),i="".concat(e.method.toUpperCase(),"\n").concat(e.path,"\n").concat(e.query,"\n").concat(r,"\n").concat(n,"\n").concat(a,"\n"),o=Ie(i).toString(qt),s="".concat(t,"\n").concat(e.timestamp,"\n").concat(o,"\n"),u=Pe(s,e.secretKey).toString(qt);return"".concat(t," Credential=").concat(e.secretId,", SignedHeaders=").concat(n,", Signature=").concat(u)}({path:m,query:k,method:a,headers:d,timestamp:l,body:JSON.stringify(n),secretId:c.accessKey,secretKey:c.secretKey,signedHeaders:h.sort()});return{url:"".concat(c.endpoint).concat(e),headers:Object.assign({},d,{Authorization:_})}}function Jt(e){var t=e.url,n=e.data,r=e.method,a=void 0===r?"POST":r,i=e.headers,s=void 0===i?{}:i,u=e.timeout;return new Promise((function(e,r){de.request({url:t,method:a,data:"object"==(0,o.default)(n)?JSON.stringify(n):n,header:s,dataType:"json",timeout:u,complete:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=s["x-trace-id"]||"";if(!t.statusCode||t.statusCode>=400){var a=t.data||{},i=a.message,o=a.errMsg,u=a.trace_id;return r(new ce({code:"SYS_ERR",message:i||o||"request:fail",requestId:u||n}))}e({status:t.statusCode,data:t.data,headers:t.header,requestId:n})}})}))}function Wt(e,t){var n=e.path,r=e.data,a=e.method,i=void 0===a?"GET":a,o=Kt(n,{functionName:"",data:r,method:i,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t}),s=o.url,u=o.headers;return Jt({url:s,data:r,method:i,headers:u}).then((function(e){var t=e.data||{};if(!t.success)throw new ce({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((function(e){throw new ce({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Ht(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new ce({code:"INVALID_PARAM",message:"fileID不合法"});var r=t.substring(0,n),a=t.substring(n+1);return r!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),a}function zt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}var Vt=function(){function e(t){(0,g.default)(this,e),this.config=t}return(0,v.default)(e,[{key:"signedURL",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="/ws/function/".concat(e),r=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),a=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Bt(),timestamp:""+Date.now()}),i=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return a[e]?"".concat(e,"=").concat(a[e]):null})).filter(Boolean).join("&"),"host:".concat(r)].join("\n"),o=["HMAC-SHA256",Ie(i).toString(qt)].join("\n"),s=Pe(o,this.config.secretKey).toString(qt),u=Object.keys(a).map((function(e){return"".concat(e,"=").concat(encodeURIComponent(a[e]))})).join("&");return"".concat(this.config.wsEndpoint).concat(n,"?").concat(u,"&signature=").concat(s)}}]),e}(),$t=function(){function e(t){if((0,g.default)(this,e),["spaceId","spaceAppId","accessKey","secretKey"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),t.endpoint){if("string"!=typeof t.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(t.endpoint))throw new Error("endpoint must start with https://");t.endpoint=t.endpoint.replace(/\/$/,"")}this.config=Object.assign({},t,{endpoint:t.endpoint||"https://".concat(t.spaceId,".api-hz.cloudbasefunction.cn"),wsEndpoint:t.wsEndpoint||"wss://".concat(t.spaceId,".api-hz.cloudbasefunction.cn")}),this._websocket=new Vt(this.config)}return(0,v.default)(e,[{key:"callFunction",value:function(e){return function(e,t){var n=e.name,r=e.data,a=e.async,i=void 0!==a&&a,o=e.timeout,s="POST",u={"x-to-function-name":n};i&&(u["x-function-invoke-type"]="async");var c=Kt("/functions/invokeFunction",{functionName:n,data:r,method:s,headers:u,signHeaderKeys:["x-to-function-name"],config:t}),l=c.url,f=c.headers;return Jt({url:l,data:r,method:s,headers:f,timeout:o}).then((function(e){var t=0;if(i){var n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new ce({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((function(e){throw new ce({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}},{key:"uploadFileToOSS",value:function(e){var t=e.url,n=e.filePath,r=e.fileType,a=e.formData,i=e.onUploadProgress;return new Promise((function(e,o){var s=de.uploadFile({url:t,filePath:n,fileType:r,formData:a,name:"file",success:function(t){t&&t.statusCode<400?e(t):o(new ce({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){o(new ce({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&s&&"function"==typeof s.onProgressUpdate&&s.onProgressUpdate((function(e){i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"uploadFile",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r,a,i,o,s,u,l,f,d,h;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,a=void 0===r?"":r,i=t.fileType,o=void 0===i?"image":i,s=t.onUploadProgress,"string"===E(a)){e.next=3;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(a=a.trim()){e.next=5;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(a)){e.next=7;break}throw new ce({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:return e.next=9,Wt({path:"/".concat(a.replace(/^\//,""),"?post_url")},this.config);case 9:return u=e.sent,l=u.file_id,f=u.upload_url,d=u.form_data,h=d&&d.reduce((function(e,t){return e[t.key]=t.value,e}),{}),e.abrupt("return",this.uploadFileToOSS({url:f,filePath:n,fileType:o,formData:h,onUploadProgress:s}).then((function(){return{fileID:l}})));case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r=this;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.fileList,e.abrupt("return",new Promise((function(e,t){(!n||n.length<0)&&e({code:"INVALID_PARAM",message:"fileList不能为空数组"}),n.length>50&&e({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});var a,i=[],o=(0,u.default)(n);try{for(o.s();!(a=o.n()).done;){var s=a.value,c=void 0;"string"!==E(s)&&e({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{c=Ht.call(r,s)}catch(e){console.warn(e.errCode,e.errMsg),c=s}i.push({file_id:c,expire:600})}}catch(l){o.e(l)}finally{o.f()}Wt({path:"/?download_url",data:{file_list:i},method:"POST"},r.config).then((function(t){var n=t.file_list,a=void 0===n?[]:n;e({fileList:a.map((function(e){return{fileID:zt.call(r,e.file_id),tempFileURL:e.download_url}}))})})).catch((function(e){return t(e)}))})));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"connectWebSocket",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(t){var n,r;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.name,r=t.query,e.abrupt("return",de.connectSocket({url:this._websocket.signedURL(n,r),complete:function(){}}));case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),Gt={init:function(e){e.provider="alipay";var t=new $t(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function Qt(e){var t,n=e.data;t=me();var r=JSON.parse(JSON.stringify(n||{}));if(Object.assign(r,{clientInfo:t}),!r.uniIdToken){var a=he(),i=a.token;i&&(r.uniIdToken=i)}return r}var Yt=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}],Xt=/[\\^$.*+?()[\]{}|]/g,Zt=RegExp(Xt.source);function en(e,t,n){return e.replace(new RegExp((r=t)&&Zt.test(r)?r.replace(Xt,"\\$&"):r,"g"),n);var r}var tn={NONE:"none",REQUEST:"request",RESPONSE:"response",BOTH:"both"},nn="_globalUniCloudStatus",rn="_globalUniCloudSecureNetworkCache__{spaceId}";var an;an="0123456789abcdef";var on="uni-secure-network",sn={SYSTEM_ERROR:{code:2e4,message:"System error"},APP_INFO_INVALID:{code:20101,message:"Invalid client"},GET_ENCRYPT_KEY_FAILED:{code:20102,message:"Get encrypt key failed"}};function un(e){var t=e||{},n=t.errSubject,r=t.subject,a=t.errCode,i=t.errMsg,o=t.code,s=t.message,u=t.cause;return new ce({subject:n||r||on,code:a||o||sn.SYSTEM_ERROR.code,message:i||s,cause:u})}var cn;function ln(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.secretType;return t===tn.REQUEST||t===tn.RESPONSE||t===tn.BOTH}function fn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name,n=e.data,r=void 0===n?{}:n;return"app"===q&&"DCloud-clientDB"===t&&"encryption"===r.redirectTo&&"getAppClientKey"===r.action}function dn(e){e.functionName,e.result,e.logPvd}function hn(e){var t=e.callFunction,n=function(n){var r=this,a=n.name;n.data=Qt.call(e,{data:n.data});var i={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],o=ln(n),s=fn(n),u=o||s;return t.call(this,n).then((function(e){return e.errCode=0,!u&&dn.call(r,{functionName:a,result:e,logPvd:i}),Promise.resolve(e)}),(function(e){return!u&&dn.call(r,{functionName:a,result:e,logPvd:i}),e&&e.message&&(e.message=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.message,n=void 0===t?"":t,r=e.extraInfo,a=void 0===r?{}:r,i=e.formatter,o=void 0===i?[]:i,s=0;s<o.length;s++){var u=o[s],c=u.rule,l=u.content,f=u.mode,d=n.match(c);if(d){for(var h=l,p=1;p<d.length;p++)h=en(h,"{$".concat(p,"}"),d[p]);for(var g in a)h=en(h,"{".concat(g,"}"),a[g]);return"replace"===f?h:n+h}}return n}({message:"[".concat(n.name,"]: ").concat(e.message),formatter:Yt,extraInfo:{functionName:a}})),Promise.reject(e)}))};e.callFunction=function(t){var r,a,i=e.config,o=i.provider,s=i.spaceId,u=t.name;return t.data=t.data||{},r=n,r=r.bind(e),a=fn(t)?n.call(e,t):function(e){var t=e.name,n=e.data,r=void 0===n?{}:n;return"mp-weixin"===q&&"uni-id-co"===t&&"secureNetworkHandshakeByWeixin"===r.method}(t)?r.call(e,t):ln(t)?new cn({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,r=e.functionName,a=ge(),i=a.appId,o=a.uniPlatform,s=a.osName,u=o;"app"===o&&(u=s);var c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,r=j;if(!r)return{};t=function(e){return"tencent"===e?"tcb":e}(t);var a=r.find((function(e){return e.provider===t&&e.spaceId===n}));return a&&a.config}({provider:t,spaceId:n});if(!c||!c.accessControl||!c.accessControl.enable)return!1;var l=c.accessControl.function||{},f=Object.keys(l);if(0===f.length)return!0;var d=function(e,t){for(var n,r,a,i=0;i<e.length;i++){var o=e[i];o!==t?"*"!==o?o.split(",").map((function(e){return e.trim()})).indexOf(t)>-1&&(r=o):a=o:n=o}return n||r||a}(f,r);if(!d)return!1;if((l[d]||[]).find((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.appId===i&&(e.platform||"").toLowerCase()===u.toLowerCase()})))return!0;throw console.error("此应用[appId: ".concat(i,", platform: ").concat(u,"]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client")),un(sn.APP_INFO_INVALID)}({provider:o,spaceId:s,functionName:u})?new cn({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):r(t),Object.defineProperty(a,"result",{get:function(){return console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{}}}),a.then((function(e){return"undefined"!=typeof UTSJSONObject&&(e.result=new UTSJSONObject(e.result)),e}))}}cn="mp-weixin"!==q&&"app"!==q?function(){return(0,v.default)((function e(){throw(0,g.default)(this,e),un({message:"Platform ".concat(q," is not supported by secure network")})}))}():function(){return(0,v.default)((function e(){throw(0,g.default)(this,e),un({message:"Platform ".concat(q," is not enabled, please check whether secure network module is enabled in your manifest.json")})}))}();var pn=Symbol("CLIENT_DB_INTERNAL");function gn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=pn,e.inspect=null,e.__ob__=void 0,new Proxy(e,{get:function(e,n,r){if("_uniClient"===n)return null;if("symbol"==(0,o.default)(n))return e[n];if(n in e||"string"!=typeof n){var a=e[n];return"function"==typeof a?a.bind(e):a}return t.get(e,n,r)}})}function vn(e){return{on:function(t,n){e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:function(t,n){e[t]=e[t]||[];var r=e[t].indexOf(n);-1!==r&&e[t].splice(r,1)}}}var mn=["db.Geo","db.command","command.aggregate"];function yn(e,t){return mn.indexOf("".concat(e,".").concat(t))>-1}function kn(e){switch(E(e)){case"array":return e.map((function(e){return kn(e)}));case"object":return e._internalType===pn||Object.keys(e).forEach((function(t){e[t]=kn(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function _n(e){return e&&e.content&&e.content.$method}var wn=function(){function e(t,n,r){(0,g.default)(this,e),this.content=t,this.prevStage=n||null,this.udb=null,this._database=r}return(0,v.default)(e,[{key:"toJSON",value:function(){for(var e=this,t=[e.content];e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((function(e){return{$method:e.$method,$param:kn(e.$param)}}))}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}},{key:"getAction",value:function(){var e=this.toJSON().$db.find((function(e){return"action"===e.$method}));return e&&e.$param&&e.$param[0]}},{key:"getCommand",value:function(){return{$db:this.toJSON().$db.filter((function(e){return"action"!==e.$method}))}}},{key:"isAggregate",get:function(){for(var e=this;e;){var t=_n(e),n=_n(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}},{key:"isCommand",get:function(){for(var e=this;e;){if("command"===_n(e))return!0;e=e.prevStage}return!1}},{key:"isAggregateCommand",get:function(){for(var e=this;e;){var t=_n(e),n=_n(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}},{key:"getNextStageFn",value:function(e){var t=this;return function(){return bn({$method:e,$param:kn(Array.from(arguments))},t,t._database)}}},{key:"count",get:function(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}},{key:"remove",get:function(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}},{key:"get",value:function(){return this._send("get",Array.from(arguments))}},{key:"add",get:function(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}},{key:"update",value:function(){return this._send("update",Array.from(arguments))}},{key:"end",value:function(){return this._send("end",Array.from(arguments))}},{key:"set",get:function(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}},{key:"_send",value:function(e,t){var n=this.getAction(),r=this.getCommand();return r.$db.push({$method:e,$param:kn(t)}),this._database._callCloudFunction({action:n,command:r})}}]),e}();function bn(e,t,n){return gn(new wn(e,t,n),{get:function(e,t){var r="db";return e&&e.content&&(r=e.content.$method),yn(r,t)?bn({$method:t},e,n):function(){return bn({$method:t,$param:kn(Array.from(arguments))},e,n)}}})}function xn(e){var t=e.path,n=e.method;return function(){function e(){(0,g.default)(this,e),this.param=Array.from(arguments)}return(0,v.default)(e,[{key:"toJSON",value:function(){return{$newDb:[].concat((0,s.default)(t.map((function(e){return{$method:e}}))),[{$method:n,$param:this.param}])}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}}]),e}()}var Tn=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.uniClient,r=void 0===n?{}:n,a=t.isJQL,i=void 0!==a&&a;(0,g.default)(this,e),this._uniClient=r,this._authCallBacks={},this._dbCallBacks={},r._isDefault&&(this._dbCallBacks=W("_globalUniCloudDatabaseCallback")),i||(this.auth=vn(this._authCallBacks)),this._isJQL=i,Object.assign(this,vn(this._dbCallBacks)),this.env=gn({},{get:function(e,t){return{$env:t}}}),this.Geo=gn({},{get:function(e,t){return xn({path:["Geo"],method:t})}}),this.serverDate=xn({path:[],method:"serverDate"}),this.RegExp=xn({path:[],method:"RegExp"})}return(0,v.default)(e,[{key:"getCloudEnv",value:function(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}},{key:"_callback",value:function(e,t){var n=this._dbCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,s.default)(t))}))}},{key:"_callbackAuth",value:function(e,t){var n=this._authCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,s.default)(t))}))}},{key:"multiSend",value:function(){var e=Array.from(arguments),t=e.map((function(e){var t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}]),e}();function Sn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return gn(new e(t),{get:function(e,t){return yn("db",t)?bn({$method:t},null,e):function(){return bn({$method:t,$param:kn(Array.from(arguments))},null,e)}}})}var In=function(e){(0,d.default)(n,e);var t=(0,h.default)(n);function n(){return(0,g.default)(this,n),t.apply(this,arguments)}return(0,v.default)(n,[{key:"_parseResult",value:function(e){return this._isJQL?e.result:e}},{key:"_callCloudFunction",value:function(e){var t=this,n=e.action,r=e.command,a=e.multiCommand,i=e.queryList;function o(e,t){if(a&&i)for(var n=0;n<i.length;n++){var r=i[n];r.udb&&"function"==typeof r.udb.setResult&&(t?r.udb.setResult(t):r.udb.setResult(e.result.dataList[n]))}}var s=this,u=this._isJQL?"databaseForJQL":"database";function c(e){return s._callback("error",[e]),G(Q(u,"fail"),e).then((function(){return G(Q(u,"complete"),e)})).then((function(){return o(null,e),ae(Z.RESPONSE,{type:ee.CLIENT_DB,content:e}),Promise.reject(e)}))}var l=G(Q(u,"invoke")),f=this._uniClient;return l.then((function(){return f.callFunction({name:"DCloud-clientDB",type:I.CLIENT_DB,data:{action:n,command:r,multiCommand:a}})})).then((function(e){var n=e.result,r=n.code,a=n.message,i=n.token,l=n.tokenExpired,f=n.systemInfo,d=void 0===f?[]:f;if(d)for(var h=0;h<d.length;h++){var p=d[h],g=p.level,v=p.message,m=p.detail,y="[System Info]"+v;m&&(y="".concat(y,"\n详细信息：").concat(m)),(console["app"===q&&"warn"===g?"error":g]||console.log)(y)}if(r)return c(new ce({code:r,message:a,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,i&&l&&(pe({token:i,tokenExpired:l}),t._callbackAuth("refreshToken",[{token:i,tokenExpired:l}]),t._callback("refreshToken",[{token:i,tokenExpired:l}]),ae(Z.REFRESH_TOKEN,{token:i,tokenExpired:l}));for(var k=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}],_=function(t){var n=k[t],r=n.prop,a=n.tips;if(r in e.result){var i=e.result[r];Object.defineProperty(e.result,r,{get:function(){return console.warn(a),i}})}},w=0;w<k.length;w++)_(w);return function(e){return G(Q(u,"success"),e).then((function(){return G(Q(u,"complete"),e)})).then((function(){o(e,null);var t=s._parseResult(e);return ae(Z.RESPONSE,{type:ee.CLIENT_DB,content:t}),Promise.resolve(t)}))}(e)}),(function(e){return/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),c(new ce({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId}))}))}}]),n}(Tn),Pn="token无效，跳转登录页面",On="token过期，跳转登录页面",An={TOKEN_INVALID_TOKEN_EXPIRED:On,TOKEN_INVALID_INVALID_CLIENTID:Pn,TOKEN_INVALID:Pn,TOKEN_INVALID_WRONG_TOKEN:Pn,TOKEN_INVALID_ANONYMOUS_USER:Pn},En={"uni-id-token-expired":On,"uni-id-check-token-failed":Pn,"uni-id-token-not-exist":Pn,"uni-id-check-device-feature-failed":Pn};function Cn(e,t){var n="";return n=e?"".concat(e,"/").concat(t):t,n.replace(/^\//,"")}function Ln(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=[],r=[];return e.forEach((function(e){!0===e.needLogin?n.push(Cn(t,e.path)):!1===e.needLogin&&r.push(Cn(t,e.path))})),{needLoginPage:n,notNeedLoginPage:r}}function Nn(e){return e.split("?")[0].replace(/^\//,"")}function Rn(){return function(e){var t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){var e=getCurrentPages();return e[e.length-1]}())}function Un(){return Nn(Rn())}function Dn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;var n=t.list,r=Nn(e);return n.some((function(e){return e.pagePath===r}))}var Mn,Fn=!!m.default.uniIdRouter,jn=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m.default,t=e.pages,n=void 0===t?[]:t,r=e.subPackages,a=void 0===r?[]:r,i=e.uniIdRouter,o=void 0===i?{}:i,u=e.tabBar,c=void 0===u?{}:u,l=o.loginPage,f=o.needLogin,d=void 0===f?[]:f,h=o.resToLogin,p=void 0===h||h,g=Ln(n),v=g.needLoginPage,y=g.notNeedLoginPage,k=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],n=[];return e.forEach((function(e){var r=e.root,a=e.pages,i=void 0===a?[]:a,o=Ln(i,r),u=o.needLoginPage,c=o.notNeedLoginPage;t.push.apply(t,(0,s.default)(u)),n.push.apply(n,(0,s.default)(c))})),{needLoginPage:t,notNeedLoginPage:n}}(a),_=k.needLoginPage,w=k.notNeedLoginPage;return{loginPage:l,routerNeedLogin:d,resToLogin:p,needLoginPage:[].concat((0,s.default)(v),(0,s.default)(_)),notNeedLoginPage:[].concat((0,s.default)(y),(0,s.default)(w)),loginPageInTabBar:Dn(l,c)}}(),qn=jn.loginPage,Bn=jn.routerNeedLogin,Kn=jn.resToLogin,Jn=jn.needLoginPage,Wn=jn.notNeedLoginPage,Hn=jn.loginPageInTabBar;if(Jn.indexOf(qn)>-1)throw new Error("Login page [".concat(qn,'] should not be "needLogin", please check your pages.json'));function zn(e){var t=Un();if("/"===e.charAt(0))return e;var n=e.split("?"),r=(0,i.default)(n,2),a=r[0],o=r[1],s=a.replace(/^\//,"").split("/"),u=t.split("/");u.pop();for(var c=0;c<s.length;c++){var l=s[c];".."===l?u.pop():"."!==l&&u.push(l)}return""===u[0]&&u.shift(),"/"+u.join("/")+(o?"?"+o:"")}function Vn(e){var t=Nn(zn(e));return!(Wn.indexOf(t)>-1)&&(Jn.indexOf(t)>-1||Bn.some((function(t){return function(e,t){return new RegExp(t).test(e)}(e,t)})))}function $n(e){var t=e.redirect,n=Nn(t),r=Nn(qn);return Un()!==r&&n!==r}function Gn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.api,n=e.redirect;if(n&&$n({redirect:n})){var r=function(e,t){return"/"!==e.charAt(0)&&(e="/"+e),t?e.indexOf("?")>-1?e+"&uniIdRedirectUrl=".concat(encodeURIComponent(t)):e+"?uniIdRedirectUrl=".concat(encodeURIComponent(t)):e}(qn,n);Hn?"navigateTo"!==t&&"redirectTo"!==t||(t="switchTab"):"switchTab"===t&&(t="navigateTo");var a={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((function(){a[t]({url:r})}),0)}}function Qn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.url,n={abortLoginPageJump:!1,autoToLoginPage:!1},r=function(){var e,t=he(),n=t.token,r=t.tokenExpired;if(n){if(r<Date.now()){var a="uni-id-token-expired";e={errCode:a,errMsg:En[a]}}}else{var i="uni-id-check-token-failed";e={errCode:i,errMsg:En[i]}}return e}();if(Vn(t)&&r){if(r.uniIdRedirectUrl=t,te(Z.NEED_LOGIN).length>0)return setTimeout((function(){ae(Z.NEED_LOGIN,r)}),0),n.abortLoginPageJump=!0,n;n.autoToLoginPage=!0}return n}function Yn(){!function(){var e=Rn(),t=Qn({url:e}),n=t.abortLoginPageJump,r=t.autoToLoginPage;n||r&&Gn({api:"redirectTo",redirect:e})}();for(var e=["navigateTo","redirectTo","reLaunch","switchTab"],t=function(t){var n=e[t];uni.addInterceptor(n,{invoke:function(e){var t=Qn({url:e.url}),r=t.abortLoginPageJump,a=t.autoToLoginPage;return r?e:a?(Gn({api:n,redirect:zn(e.url)}),!1):e}})},n=0;n<e.length;n++)t(n)}function Xn(){this.onResponse((function(e){var t=e.type,n=e.content,r=!1;switch(t){case"cloudobject":r=function(e){if("object"!=(0,o.default)(e))return!1;var t=e||{},n=t.errCode;return n in En}(n);break;case"clientdb":r=function(e){if("object"!=(0,o.default)(e))return!1;var t=e||{},n=t.errCode;return n in An}(n)}r&&function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=te(Z.NEED_LOGIN);se().then((function(){var n=Rn();if(n&&$n({redirect:n}))return t.length>0?ae(Z.NEED_LOGIN,Object.assign({uniIdRedirectUrl:n},e)):void(qn&&Gn({api:"navigateTo",redirect:n}))}))}(n)}))}function Zn(e){!function(e){e.onResponse=function(e){ne(Z.RESPONSE,e)},e.offResponse=function(e){re(Z.RESPONSE,e)}}(e),function(e){e.onNeedLogin=function(e){ne(Z.NEED_LOGIN,e)},e.offNeedLogin=function(e){re(Z.NEED_LOGIN,e)},Fn&&(W(nn).needLoginInit||(W(nn).needLoginInit=!0,se().then((function(){Yn.call(e)})),Kn&&Xn.call(e)))}(e),function(e){e.onRefreshToken=function(e){ne(Z.REFRESH_TOKEN,e)},e.offRefreshToken=function(e){re(Z.REFRESH_TOKEN,e)}}(e)}var er="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",tr=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function nr(){var e,t,n=he().token||"",r=n.split(".");if(!n||3!==r.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{e=JSON.parse((t=r[1],decodeURIComponent(Mn(t).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(n){throw new Error("获取当前用户信息出错，详细错误信息为："+n.message)}return e.tokenExpired=1e3*e.exp,delete e.exp,delete e.iat,e}Mn="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!tr.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,a="",i=0;i<e.length;)t=er.indexOf(e.charAt(i++))<<18|er.indexOf(e.charAt(i++))<<12|(n=er.indexOf(e.charAt(i++)))<<6|(r=er.indexOf(e.charAt(i++))),a+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return a}:atob;var rr=y((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n="chooseAndUploadFile:ok",r="chooseAndUploadFile:fail";function a(e,t){return e.tempFiles.forEach((function(e,n){e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((function(e){return e.path}))),e}function i(e,t,r){var a=r.onChooseFile,i=r.onUploadProgress;return t.then((function(e){if(a){var t=a(e);if(void 0!==t)return Promise.resolve(t).then((function(t){return void 0===t?e:t}))}return e})).then((function(t){return!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,a=arguments.length>3?arguments[3]:void 0;(t=Object.assign({},t)).errMsg=n;var i=t.tempFiles,o=i.length,s=0;return new Promise((function(n){for(;s<r;)u();function u(){var r=s++;if(r>=o)!i.find((function(e){return!e.url&&!e.errMsg}))&&n(t);else{var c=i[r];e.uploadFile({provider:c.provider,filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,cloudPathAsRealPath:c.cloudPathAsRealPath,onUploadProgress:function(e){e.index=r,e.tempFile=c,e.tempFilePath=c.path,a&&a(e)}}).then((function(e){c.url=e.fileID,r<o&&u()})).catch((function(e){c.errMsg=e.errMsg||e.message,r<o&&u()}))}}}))}(e,t,5,i)}))}t.initChooseAndUploadFile=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"all"};return"image"===t.type?i(e,function(e){var t=e.count,n=e.sizeType,i=e.sourceType,o=void 0===i?["album","camera"]:i,s=e.extension;return new Promise((function(e,i){uni.chooseImage({count:t,sizeType:n,sourceType:o,extension:s,success:function(t){e(a(t,"image"))},fail:function(e){i({errMsg:e.errMsg.replace("chooseImage:fail",r)})}})}))}(t),t):"video"===t.type?i(e,function(e){var t=e.camera,n=e.compressed,i=e.maxDuration,o=e.sourceType,s=void 0===o?["album","camera"]:o,u=e.extension;return new Promise((function(e,o){uni.chooseVideo({camera:t,compressed:n,maxDuration:i,sourceType:s,extension:u,success:function(t){var n=t.tempFilePath,r=t.duration,i=t.size,o=t.height,s=t.width;e(a({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:i,type:t.tempFile&&t.tempFile.type||"",width:s,height:o,duration:r,fileType:"video",cloudPath:""}]},"video"))},fail:function(e){o({errMsg:e.errMsg.replace("chooseVideo:fail",r)})}})}))}(t),t):i(e,function(e){var t=e.count,n=e.extension;return new Promise((function(e,i){var o=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(o=wx.chooseMessageFile),"function"!=typeof o)return i({errMsg:r+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});o({type:"all",count:t,extension:n,success:function(t){e(a(t))},fail:function(e){i({errMsg:e.errMsg.replace("chooseFile:fail",r)})}})}))}(t),t)}}})),ar=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(rr),ir={auto:"auto",onready:"onready",manual:"manual"};function or(e){return{props:{localdata:{type:Array,default:function(){return[]}},options:{type:[Object,Array],default:function(){return{}}},spaceInfo:{type:Object,default:function(){return{}}},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:function(){return{mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}},created:function(){var e=this;this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((function(){var t=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((function(n){t.push(e[n])})),t}),(function(t,n){if(e.loadtime!==ir.manual){for(var r=!1,a=[],i=2;i<t.length;i++)t[i]!==n[i]&&(a.push(t[i]),r=!0);t[0]!==n[0]&&(e.mixinDatacomPage.current=e.pageCurrent),e.mixinDatacomPage.size=e.pageSize,e.onMixinDatacomPropsChange(r,a)}}))},methods:{onMixinDatacomPropsChange:function(e,t){},mixinDatacomEasyGet:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.getone,r=void 0!==n&&n,a=t.success,i=t.fail;this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((function(t){e.mixinDatacomLoading=!1;var n=t.result,i=n.data,o=n.count;e.getcount&&(e.mixinDatacomPage.count=o),e.mixinDatacomHasMore=i.length<e.pageSize;var s=r?i.length?i[0]:void 0:i;e.mixinDatacomResData=s,a&&a(s)})).catch((function(t){e.mixinDatacomLoading=!1,e.mixinDatacomErrorMessage=t,e.mixinDatacomError=t,i&&i(t)})))},mixinDatacomGet:function(){var t,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r=r||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);var a=r.action||this.action;a&&(n=n.action(a));var i=r.collection||this.collection;n=Array.isArray(i)?(t=n).collection.apply(t,(0,s.default)(i)):n.collection(i);var o=r.where||this.where;o&&Object.keys(o).length&&(n=n.where(o));var u=r.field||this.field;u&&(n=n.field(u));var c=r.foreignKey||this.foreignKey;c&&(n=n.foreignKey(c));var l=r.groupby||this.groupby;l&&(n=n.groupBy(l));var f=r.groupField||this.groupField;f&&(n=n.groupField(f)),!0===(void 0!==r.distinct?r.distinct:this.distinct)&&(n=n.distinct());var d=r.orderby||this.orderby;d&&(n=n.orderBy(d));var h=void 0!==r.pageCurrent?r.pageCurrent:this.mixinDatacomPage.current,p=void 0!==r.pageSize?r.pageSize:this.mixinDatacomPage.size,g=void 0!==r.getcount?r.getcount:this.getcount,v=void 0!==r.gettree?r.gettree:this.gettree,m=void 0!==r.gettreepath?r.gettreepath:this.gettreepath,y={getCount:g},k={limitLevel:void 0!==r.limitlevel?r.limitlevel:this.limitlevel,startWith:void 0!==r.startwith?r.startwith:this.startwith};return v&&(y.getTree=k),m&&(y.getTreePath=k),n=n.skip(p*(h-1)).limit(p).get(y),n}}}}function sr(e){return W(rn.replace("{spaceId}",e.config.spaceId))}function ur(){return cr.apply(this,arguments)}function cr(){return cr=(0,l.default)((0,c.default)().mark((function e(){var t,n,r,a,i,o,s,u=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=u.length>0&&void 0!==u[0]?u[0]:{},n=t.openid,r=t.callLoginByWeixin,a=void 0!==r&&r,i=sr(this),"mp-weixin"===q){e.next=4;break}throw new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `".concat(q,"`"));case 4:if(!n||!a){e.next=6;break}throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");case 6:if(!n){e.next=8;break}return e.abrupt("return",(i.mpWeixinOpenid=n,{}));case 8:return e.next=10,new Promise((function(e,t){uni.login({success:function(t){e(t.code)},fail:function(e){t(new Error(e.errMsg))}})}));case 10:return o=e.sent,s=this.importObject("uni-id-co",{customUI:!0}),e.next=14,s.secureNetworkHandshakeByWeixin({code:o,callLoginByWeixin:a});case 14:return i.mpWeixinCode=o,e.abrupt("return",{code:o});case 16:case"end":return e.stop()}}),e,this)}))),cr.apply(this,arguments)}function lr(e){return fr.apply(this,arguments)}function fr(){return fr=(0,l.default)((0,c.default)().mark((function e(t){var n;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=sr(this),e.abrupt("return",(n.initPromise||(n.initPromise=ur.call(this,t).then((function(e){return e})).catch((function(e){throw delete n.initPromise,e}))),n.initPromise));case 2:case"end":return e.stop()}}),e,this)}))),fr.apply(this,arguments)}function dr(e){!function(e){ve=e}(e)}function hr(e){var t={getSystemInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(n){return new Promise((function(r,a){t[e]((0,f.default)((0,f.default)({},n),{},{success:function(e){r(e)},fail:function(e){a(e)}}))}))}}var pr=function(e){(0,d.default)(n,e);var t=(0,h.default)(n);function n(){var e;return(0,g.default)(this,n),e=t.call(this),e._uniPushMessageCallback=e._receivePushMessage.bind((0,a.default)(e)),e._currentMessageId=-1,e._payloadQueue=[],e}return(0,v.default)(n,[{key:"init",value:function(){var e=this;return Promise.all([hr("getSystemInfo")(),hr("getPushClientId")()]).then((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=(0,i.default)(t,2),r=n[0];r=void 0===r?{}:r;var a=r.appId,o=n[1];o=void 0===o?{}:o;var s=o.cid;if(!a)throw new Error("Invalid appId, please check the manifest.json file");if(!s)throw new Error("Invalid push client id");e._appId=a,e._pushClientId=s,e._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),e.emit("open"),e._initMessageListener()}),(function(t){throw e.emit("error",t),e.close(),t}))}},{key:"open",value:function(){var e=(0,l.default)((0,c.default)().mark((function e(){return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.init());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_isUniCloudSSE",value:function(e){if("receive"!==e.type)return!1;var t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}},{key:"_receivePushMessage",value:function(e){if(this._isUniCloudSSE(e)){var t=e&&e.data&&e.data.payload,n=t.action,r=t.messageId,a=t.message;this._payloadQueue.push({action:n,messageId:r,message:a}),this._consumMessage()}}},{key:"_consumMessage",value:function(){for(var e=this;;){var t=this._payloadQueue.find((function(t){return t.messageId===e._currentMessageId+1}));if(!t)break;this._currentMessageId++,this._parseMessagePayload(t)}}},{key:"_parseMessagePayload",value:function(e){var t=e.action,n=e.messageId,r=e.message;"end"===t?this._end({messageId:n,message:r}):"message"===t&&this._appendMessage({messageId:n,message:r})}},{key:"_appendMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("message",t)}},{key:"_end",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("end",t),this.close()}},{key:"_initMessageListener",value:function(){uni.onPushMessage(this._uniPushMessageCallback)}},{key:"_destroy",value:function(){uni.offPushMessage(this._uniPushMessageCallback)}},{key:"toJSON",value:function(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}},{key:"close",value:function(){this._destroy(),this.emit("close")}}]),n}(M);var gr={tcb:Lt,tencent:Lt,aliyun:we,private:jt,dcloud:jt,alipay:Gt},vr=new(function(){function e(){(0,g.default)(this,e)}return(0,v.default)(e,[{key:"init",value:function(e){var t={},n=gr[e.provider];if(!n)throw new Error("未提供正确的provider参数");return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new D({createPromise:function(){var t=Promise.resolve();t=new Promise((function(e){setTimeout((function(){e()}),1)}));var n=e.auth();return t.then((function(){return n.getLoginState()})).then((function(e){return e?Promise.resolve():n.signInAnonymously()}))}}))}(t),hn(t),function(e){var t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),function(e){e.database=function(t){if(t&&Object.keys(t).length>0)return e.init(t).database();if(this._database)return this._database;var n=Sn(In,{uniClient:e});return this._database=n,n},e.databaseForJQL=function(t){if(t&&Object.keys(t).length>0)return e.init(t).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;var n=Sn(In,{uniClient:e,isJQL:!0});return this._databaseForJQL=n,n}}(t),function(e){e.getCurrentUserInfo=nr,e.chooseAndUploadFile=ar.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return or(e)}}),e.SSEChannel=pr,e.initSecureNetworkByWeixin=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.openid,r=t.callLoginByWeixin,a=void 0!==r&&r;return lr.call(e,{openid:n,callLoginByWeixin:a})}}(e),e.setCustomClientInfo=dr,e.importObject=function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==(0,o.default)(t.secretMethods)&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},n);var r=n,a=r.customUI,i=r.loadingOptions,s=r.errorOptions,u=r.parseSystemError,d=!a;return new Proxy({},{get:function(r,a){switch(a){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fn,n=e.interceptorName,r=e.getCallbackArgs;return(0,l.default)((0,c.default)().mark((function e(){var a,i,o,s,u,l,d=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(a=d.length,i=new Array(a),o=0;o<a;o++)i[o]=d[o];return s=r?r({params:i}):{},e.prev=2,e.next=5,G(Q(n,"invoke"),(0,f.default)({},s));case 5:return e.next=7,t.apply(void 0,i);case 7:return u=e.sent,e.next=10,G(Q(n,"success"),(0,f.default)((0,f.default)({},s),{},{result:u}));case 10:return e.abrupt("return",u);case 13:return e.prev=13,e.t0=e["catch"](2),l=e.t0,e.next=18,G(Q(n,"fail"),(0,f.default)((0,f.default)({},s),{},{error:l}));case 18:throw l;case 19:return e.prev=19,e.next=22,G(Q(n,"complete"),l?(0,f.default)((0,f.default)({},s),{},{error:l}):(0,f.default)((0,f.default)({},s),{},{result:u}));case 22:return e.finish(19);case 23:case"end":return e.stop()}}),e,null,[[2,13,19,23]])})))}({fn:function(){var r=(0,l.default)((0,c.default)().mark((function r(){var p,g,v,m,y,k,_,w,b,x,T,S,P,O,A,E=arguments;return(0,c.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:for(d&&uni.showLoading({title:i.title,mask:i.mask}),g=E.length,v=new Array(g),m=0;m<g;m++)v[m]=E[m];return y={name:t,type:I.OBJECT,data:{method:a,params:v}},"object"==(0,o.default)(n.secretMethods)&&function(e,t){var n=t.data.method,r=e.secretMethods||{},a=r[n]||r["*"];a&&(t.secretType=a)}(n,y),k=!1,r.prev=5,r.next=8,e.callFunction(y);case 8:p=r.sent,r.next=14;break;case 11:r.prev=11,r.t0=r["catch"](5),k=!0,p={result:new ce(r.t0)};case 14:if(_=p.result||{},w=_.errSubject,b=_.errCode,x=_.errMsg,T=_.newToken,d&&uni.hideLoading(),T&&T.token&&T.tokenExpired&&(pe(T),ae(Z.REFRESH_TOKEN,(0,f.default)({},T))),!b){r.next=39;break}if(S=x,!k||!u){r.next=24;break}return r.next=20,u({objectName:t,methodName:a,params:v,errSubject:w,errCode:b,errMsg:x});case 20:if(r.t1=r.sent.errMsg,r.t1){r.next=23;break}r.t1=x;case 23:S=r.t1;case 24:if(!d){r.next=37;break}if("toast"!==s.type){r.next=29;break}uni.showToast({title:S,icon:"none"}),r.next=37;break;case 29:if("modal"===s.type){r.next=31;break}throw new Error("Invalid errorOptions.type: ".concat(s.type));case 31:return r.next=33,(0,l.default)((0,c.default)().mark((function e(){var t,n,r,a,i,o,s=arguments;return(0,c.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=s.length>0&&void 0!==s[0]?s[0]:{},n=t.title,r=t.content,a=t.showCancel,i=t.cancelText,o=t.confirmText,e.abrupt("return",new Promise((function(e,t){uni.showModal({title:n,content:r,showCancel:a,cancelText:i,confirmText:o,success:function(t){e(t)},fail:function(){e({confirm:!1,cancel:!0})}})})));case 2:case"end":return e.stop()}}),e)})))({title:"提示",content:S,showCancel:s.retry,cancelText:"取消",confirmText:s.retry?"重试":"确定"});case 33:if(P=r.sent,O=P.confirm,!s.retry||!O){r.next=37;break}return r.abrupt("return",h.apply(void 0,v));case 37:throw A=new ce({subject:w,code:b,message:x,requestId:p.requestId}),A.detail=p.result,ae(Z.RESPONSE,{type:ee.CLOUD_OBJECT,content:A}),A;case 39:return r.abrupt("return",(ae(Z.RESPONSE,{type:ee.CLOUD_OBJECT,content:p.result}),p.result));case 40:case"end":return r.stop()}}),r,null,[[5,11]])})));function h(){return r.apply(this,arguments)}return h}(),interceptorName:"callObject",getCallbackArgs:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.params;return{objectName:t,methodName:a,params:n}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((function(e){if(t[e]){var n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){var r=this,a=!1;if("callFunction"===t){var i=n&&n.type||I.DEFAULT;a=i!==I.DEFAULT}var o="callFunction"===t&&!a,s=this._initPromiseHub.exec();n=n||{};var u=ue(n),c=u.success,l=u.fail,f=u.complete,d=s.then((function(){return a?Promise.resolve():G(Q(t,"invoke"),n)})).then((function(){return e.call(r,n)})).then((function(e){return a?Promise.resolve(e):G(Q(t,"success"),e).then((function(){return G(Q(t,"complete"),e)})).then((function(){return o&&ae(Z.RESPONSE,{type:ee.CLOUD_FUNCTION,content:e}),Promise.resolve(e)}))}),(function(e){return a?Promise.reject(e):G(Q(t,"fail"),e).then((function(){return G(Q(t,"complete"),e)})).then((function(){return ae(Z.RESPONSE,{type:ee.CLOUD_FUNCTION,content:e}),Promise.reject(e)}))}));if(!(c||l||f))return d;d.then((function(e){c&&c(e),f&&f(e),o&&ae(Z.RESPONSE,{type:ee.CLOUD_FUNCTION,content:e})}),(function(e){l&&l(e),f&&f(e),o&&ae(Z.RESPONSE,{type:ee.CLOUD_FUNCTION,content:e})}))}}(t[e],e).bind(t)}})),t.init=this.init,t}}]),e}());t.uniCloud=vr,function(){var e=B,n={};if(e&&1===e.length)n=e[0],t.uniCloud=vr=vr.init(n),vr._isDefault=!0;else{var r;r=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"].forEach((function(e){vr[e]=function(){return console.error(r),Promise.reject(new ce({code:"SYS_ERR",message:r}))}}))}if(Object.assign(vr,{get mixinDatacom(){return or(vr)}}),Zn(vr),vr.addInterceptor=V,vr.removeInterceptor=$,vr.interceptObject=Y,"app"===q&&(uni.__uniCloud=vr),"app"===q||"web"===q){var a=function(){return K||(K=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),K)}();a.uniCloud=vr,a.UniCloudError=ce}}();var mr=vr;t.default=mr}).call(this,n("0ee4"))},9370:function(e,t,n){"use strict";var r=n("8bdb"),a=n("af9e"),i=n("1099"),o=n("c215"),s=a((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})}));r({target:"Date",proto:!0,arity:1,forced:s},{toJSON:function(e){var t=i(this),n=o(t,"number");return"number"!=typeof n||isFinite(n)?t.toISOString():null}})},acb1:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,r.default)(e,t)},n("7a76"),n("c9b5"),n("6a54");var r=function(e){return e&&e.__esModule?e:{default:e}}(n("e668"))},c1a3:function(e,t,n){"use strict";n("15ab")},c238:function(e,t,n){"use strict";var r=n("af9e");e.exports=!r((function(){return Object.isExtensible(Object.preventExtensions({}))}))},cad9:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,a.default)();return function(){var n,a=(0,r.default)(e);if(t){var o=(0,r.default)(this).constructor;n=Reflect.construct(a,arguments,o)}else n=a.apply(this,arguments);return(0,i.default)(this,n)}},n("6a88"),n("bf0f"),n("7996");var r=o(n("f1f8")),a=o(n("6c31")),i=o(n("62b0"));function o(e){return e&&e.__esModule?e:{default:e}}},d0b1:function(e,t,n){"use strict";var r=n("8bdb"),a=n("bb80"),i=n("11bf"),o=n("1c06"),s=n("338c"),u=n("d6b1").f,c=n("80bb"),l=n("8449"),f=n("1ea2"),d=n("d7b4"),h=n("c238"),p=!1,g=d("meta"),v=0,m=function(e){u(e,g,{value:{objectID:"O"+v++,weakData:{}}})},y=e.exports={enable:function(){y.enable=function(){},p=!0;var e=c.f,t=a([].splice),n={};n[g]=1,e(n).length&&(c.f=function(n){for(var r=e(n),a=0,i=r.length;a<i;a++)if(r[a]===g){t(r,a,1);break}return r},r({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:l.f}))},fastKey:function(e,t){if(!o(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!s(e,g)){if(!f(e))return"F";if(!t)return"E";m(e)}return e[g].objectID},getWeakData:function(e,t){if(!s(e,g)){if(!f(e))return!0;if(!t)return!1;m(e)}return e[g].weakData},onFreeze:function(e){return h&&p&&f(e)&&!s(e,g)&&m(e),e}};i[g]=!0},d2c4:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=o,n("6a88"),n("bf0f"),n("7996"),n("aa9c");var r=i(n("e668")),a=i(n("6c31"));function i(e){return e&&e.__esModule?e:{default:e}}function o(e,n,i){return(0,a.default)()?t.default=o=Reflect.construct.bind():t.default=o=function(e,t,n){var a=[null];a.push.apply(a,t);var i=Function.bind.apply(e,a),o=new i;return n&&(0,r.default)(o,n.prototype),o},o.apply(null,arguments)}},d441:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return-1!==Function.toString.call(e).indexOf("[native code]")},n("5ef2"),n("c9b5"),n("bf0f"),n("ab80")},e062:function(e,t,n){"use strict";var r=n("8bdb");r({target:"Number",stat:!0,nonConfigurable:!0,nonWritable:!0},{MAX_SAFE_INTEGER:9007199254740991})},e668:function(e,t,n){"use strict";function r(e,n){return t.default=r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,n)}n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,n("8a8d")},f1f8:function(e,t,n){"use strict";function r(e){return t.default=r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},r(e)}n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,n("8a8d"),n("926e")},f478:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},n("7a76"),n("c9b5")},f555:function(e,t,n){"use strict";var r=n("85c1"),a=n("ab4a"),i=n("e4ca"),o=n("471d"),s=n("af9e"),u=r.RegExp,c=u.prototype,l=a&&s((function(){var e=!0;try{u(".","d")}catch(l){e=!1}var t={},n="",r=e?"dgimsy":"gimsy",a=function(e,r){Object.defineProperty(t,e,{get:function(){return n+=r,!0}})},i={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var o in e&&(i.hasIndices="d"),i)a(o,i[o]);var s=Object.getOwnPropertyDescriptor(c,"flags").get.call(t);return s!==r||n!==r}));l&&i(c,"flags",{configurable:!0,get:o})}}]);