(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-stock-manage"],{"02fc":function(t,e,a){"use strict";a.r(e);var i=a("500d"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"0548":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c223"),a("5c47"),a("af8f"),a("fd3c");var n=a("54b6"),o=i(a("151b")),r=i(a("43ca")),s={components:{uniDataPicker:o.default,uniDataTable:r.default},data:function(){var t=this;return{classifyData:{data:[]},table:{loading:!1,data:[]},paging:{pageSize:9,pageCurrent:1,total:0},option:{search:"",category_id:"",page_size:10},cols:[{field:"account_data",width:20,title:"产品名称",align:"left",templet:function(e){var a=t.$util.img(e.sku_image,{size:"small"}),i='\n\t\t\t\t\t\t\t<view class="goods-content">\n\t\t\t\t\t\t\t\t<image class="goods-img" src="'.concat(a,'" mode="aspectFill" />\n\t\t\t\t\t\t\t\t<text class="goods-name multi-hidden">').concat(e.goods_name,"</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t");return i}},{width:19,title:"规格",align:"center",field:"spec_name"},{width:13,title:"编码",align:"center",field:"sku_no"},{field:"real_stock",width:10,title:"库存",align:"center"},{field:"cost_price",width:12,title:"成本",align:"center"},{width:20,title:"添加时间",templet:function(e){return t.$util.timeFormat(e.create_time)}},{width:15,title:"操作",align:"right",action:!0}]}},onLoad:function(t){this.getCategory()},methods:{searchFn:function(){this.$refs.goodsListTable.load({page:1})},resetFn:function(){this.option.search="",this.option.category_id="",this.$refs.goodsListTable.load({page:1})},getCategory:function(){var t=this;(0,n.getGoodsCategory)({level:3}).then((function(e){var a=e.data;0==e.code&&a.length?(t.classifyData.data=t.analyzeCategory(a),t.$forceUpdate()):t.$util.showToast({title:e.message})}))},analyzeCategory:function(t){var e=this,a=t.map((function(t,a){var i={};return i.text=t.category_name,i.value=t.category_id,t.child_list&&t.child_list.length&&(i.children=e.analyzeCategory(t.child_list)),i}));return a},toDetail:function(){this.$util.redirectTo("/pages/stock/records")}}};e.default=s},"079e":function(t,e,a){"use strict";var i=a("1db5"),n=a.n(i);n.a},"151b":function(t,e,a){"use strict";a.r(e);var i=a("f38ae"),n=a("02fc");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("f828");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"57986d64",null,!1,i["a"],void 0);e["default"]=s.exports},"1db5":function(t,e,a){var i=a("ba75");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("5fc75c7b",i,!0,{sourceMap:!1,shadowMode:!1})},3047:function(t,e,a){"use strict";(function(t){a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("fcf3"));a("64aa"),a("aa9c"),a("bf0f"),a("dc8a"),a("c223"),a("0c26"),a("fd3c"),a("8f71");var o={props:{localdata:{type:[Array,Object],default:function(){return[]}},spaceInfo:{type:Object,default:function(){return{}}},collection:{type:String,default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},getone:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},manual:{type:Boolean,default:!1},value:{type:[Array,String,Number],default:function(){return[]}},modelValue:{type:[Array,String,Number],default:function(){return[]}},preload:{type:Boolean,default:!1},stepSearh:{type:Boolean,default:!0},selfField:{type:String,default:""},parentField:{type:String,default:""},multiple:{type:Boolean,default:!1},map:{type:Object,default:function(){return{text:"text",value:"value"}}}},data:function(){return{loading:!1,errorMessage:"",loadMore:{contentdown:"",contentrefresh:"",contentnomore:""},dataList:[],selected:[],selectedIndex:0,page:{current:this.pageCurrent,size:this.pageSize,count:0}}},computed:{isLocaldata:function(){return!this.collection.length},postField:function(){var t=[this.field];return this.parentField&&t.push("".concat(this.parentField," as parent_value")),t.join(",")},dataValue:function(){var t=Array.isArray(this.modelValue)?this.modelValue.length>0:null!==this.modelValue||void 0!==this.modelValue;return t?this.modelValue:this.value},hasValue:function(){return"number"===typeof this.dataValue||null!=this.dataValue&&this.dataValue.length>0}},created:function(){var t=this;this.$watch((function(){var e=[];return["pageCurrent","pageSize","spaceInfo","value","modelValue","localdata","collection","action","field","orderby","where","getont","getcount","gettree"].forEach((function(a){e.push(t[a])})),e}),(function(e,a){for(var i=2;i<e.length;i++)if(e[i]!=a[i]){!0;break}e[0]!=a[0]&&(t.page.current=t.pageCurrent),t.page.size=t.pageSize,t.onPropsChange()})),this._treeData=[]},methods:{onPropsChange:function(){this._treeData=[]},getCommand:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.database(this.spaceInfo),i=e.action||this.action;i&&(a=a.action(i));var n=e.collection||this.collection;a=a.collection(n);var o=e.where||this.where;o&&Object.keys(o).length&&(a=a.where(o));var r=e.field||this.field;r&&(a=a.field(r));var s=e.orderby||this.orderby;s&&(a=a.orderBy(s));var d=void 0!==e.pageCurrent?e.pageCurrent:this.page.current,l=void 0!==e.pageSize?e.pageSize:this.page.size,c=void 0!==e.getcount?e.getcount:this.getcount,u=void 0!==e.gettree?e.gettree:this.gettree,f={getCount:c,getTree:u};return e.getTreePath&&(f.getTreePath=e.getTreePath),a=a.skip(l*(d-1)).limit(l).get(f),a},getNodeData:function(t){var e=this;this.loading||(this.loading=!0,this.getCommand({field:this.postField,where:this._pathWhere()}).then((function(a){e.loading=!1,e.selected=a.result.data,t&&t()})).catch((function(t){e.loading=!1,e.errorMessage=t})))},getTreePath:function(t){var e=this;this.loading||(this.loading=!0,this.getCommand({field:this.postField,getTreePath:{startWith:"".concat(this.selfField,"=='").concat(this.dataValue,"'")}}).then((function(a){e.loading=!1;var i=[];e._extractTreePath(a.result.data,i),e.selected=i,t&&t()})).catch((function(t){e.loading=!1,e.errorMessage=t})))},loadData:function(){var t=this;this.isLocaldata?this._processLocalData():null==this.dataValue?this.stepSearh?this._loadNodeData((function(e){t._treeData=e,t._updateBindData()})):this._loadAllData((function(e){t._treeData=[],t._extractTree(e,t._treeData,null),t._updateBindData()})):this._loadNodeData((function(e){t._treeData=e,t._updateBindData(),t._updateSelected()}))},_loadAllData:function(t){var e=this;this.loading||(this.loading=!0,this.getCommand({field:this.postField,gettree:!0,startwith:"".concat(this.selfField,"=='").concat(this.dataValue,"'")}).then((function(a){e.loading=!1,t(a.result.data),e.onDataChange()})).catch((function(t){e.loading=!1,e.errorMessage=t})))},_loadNodeData:function(t,e){var a=this;this.loading||(this.loading=!0,this.getCommand({field:this.postField,where:e||this._postWhere(),pageSize:500}).then((function(e){a.loading=!1,t(e.result.data),a.onDataChange()})).catch((function(t){a.loading=!1,a.errorMessage=t})))},_pathWhere:function(){var t=[],e=this._getParentNameByField();return e&&t.push("".concat(e," == '").concat(this.dataValue,"'")),this.where?"(".concat(this.where,") && (").concat(t.join(" || "),")"):t.join(" || ")},_postWhere:function(){var t=[],e=this.selected,a=this.parentField;if(a&&t.push("".concat(a," == null || ").concat(a,' == ""')),e.length)for(var i=0;i<e.length-1;i++)t.push("".concat(a," == '").concat(e[i].value,"'"));var n=[];return this.where&&n.push("(".concat(this.where,")")),t.length&&n.push("(".concat(t.join(" || "),")")),n.join(" && ")},_nodeWhere:function(){var t=[],e=this.selected;return e.length&&t.push("".concat(this.parentField," == '").concat(e[e.length-1].value,"'")),this.where?"(".concat(this.where,") && (").concat(t.join(" || "),")"):t.join(" || ")},_getParentNameByField:function(){for(var t=this.field.split(","),e=null,a=0;a<t.length;a++){var i=t[a].split("as");if(!(i.length<2)&&"value"===i[1].trim()){e=i[0].trim();break}}return e},_isTreeView:function(){return this.parentField&&this.selfField},_updateSelected:function(){for(var t=this.dataList,e=this.selected,a=this.map.text,i=this.map.value,n=0;n<e.length;n++)for(var o=e[n].value,r=t[n],s=0;s<r.length;s++){var d=r[s];if(d[i]===o){e[n].text=d[a];break}}},_updateBindData:function(t){var e=this._filterData(this._treeData,this.selected),a=e.dataList,i=e.hasNodes,n=!1===this._stepSearh&&!i;return t&&(t.isleaf=n),this.dataList=a,this.selectedIndex=a.length-1,!n&&this.selected.length<a.length&&this.selected.push({value:null,text:"请选择"}),{isleaf:n,hasNodes:i}},_filterData:function(t,e){var a=[],i=!0;a.push(t.filter((function(t){return null===t.parent_value||void 0===t.parent_value||""===t.parent_value})));for(var n=0;n<e.length;n++){var o=e[n].value,r=t.filter((function(t){return t.parent_value===o}));r.length?a.push(r):i=!1}return{dataList:a,hasNodes:i}},_extractTree:function(t,e,a){for(var i=this.map.value,n=0;n<t.length;n++){var o=t[n],r={};for(var s in o)"children"!==s&&(r[s]=o[s]);null!==a&&void 0!==a&&""!==a&&(r.parent_value=a),e.push(r);var d=o.children;d&&this._extractTree(d,e,o[i])}},_extractTreePath:function(t,e){for(var a=0;a<t.length;a++){var i=t[a],n={};for(var o in i)"children"!==o&&(n[o]=i[o]);e.push(n);var r=i.children;r&&this._extractTreePath(r,e)}},_findNodePath:function(t,e){for(var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=this.map.text,n=this.map.value,o=0;o<e.length;o++){var r=e[o],s=r.children,d=r[i],l=r[n];if(a.push({value:l,text:d}),l===t)return a;if(s){var c=this._findNodePath(t,s,a);if(c.length)return c}a.pop()}return[]},_processLocalData:function(){this._treeData=[],this._extractTree(this.localdata,this._treeData);var t=this.dataValue;void 0!==t&&(Array.isArray(t)&&(t=t[t.length-1],"object"===(0,n.default)(t)&&t[this.map.value]&&(t=t[this.map.value])),this.selected=this._findNodePath(t,this.localdata))}}};e.default=o}).call(this,a("861b")["uniCloud"])},"401b":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-38f0f5a8]{display:none}\r\n/* 收银台相关 */uni-text[data-v-38f0f5a8],\r\nuni-view[data-v-38f0f5a8]{font-size:.14rem}body[data-v-38f0f5a8]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-38f0f5a8]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-38f0f5a8]::-webkit-scrollbar-button{display:none}body[data-v-38f0f5a8]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-38f0f5a8]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-38f0f5a8]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-38f0f5a8]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-38f0f5a8]{color:var(--primary-color)!important}.uni-data-pickerview[data-v-38f0f5a8]{flex:1;display:flex;flex-direction:column;overflow:hidden;height:100%}.error-text[data-v-38f0f5a8]{color:#dd524d}.loading-cover[data-v-38f0f5a8]{position:absolute;left:0;top:0;right:0;bottom:0;background-color:hsla(0,0%,100%,.5);display:flex;flex-direction:column;align-items:center;z-index:1001}.load-more[data-v-38f0f5a8]{margin:auto}.error-message[data-v-38f0f5a8]{background-color:#fff;position:absolute;left:0;top:0;right:0;bottom:0;padding:15px;opacity:.9;z-index:102}.selected-list[data-v-38f0f5a8]{display:flex;flex-direction:row;flex-wrap:nowrap;padding:0 5px;border-bottom:1px solid #f8f8f8}.selected-item[data-v-38f0f5a8]{margin-left:10px;margin-right:10px;padding:12px 0;text-align:center;white-space:nowrap}.selected-item-text-overflow[data-v-38f0f5a8]{width:168px;\r\n  /* fix nvue */overflow:hidden;width:6em;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis}.selected-item-active[data-v-38f0f5a8]{border-bottom:2px solid var(--primary-color)}.selected-item-text[data-v-38f0f5a8]{color:var(--primary-color)}.tab-c[data-v-38f0f5a8]{position:relative;flex:1;display:flex;flex-direction:row;overflow:hidden}.list[data-v-38f0f5a8]{flex:1}.item[data-v-38f0f5a8]{padding:12px 15px;\r\n  /* border-bottom: 1px solid #f0f0f0; */display:flex;flex-direction:row;justify-content:space-between}.is-disabled[data-v-38f0f5a8]{opacity:.5}.item-text[data-v-38f0f5a8]{\r\n  /* flex: 1; */color:#333}.item-text-overflow[data-v-38f0f5a8]{width:280px;\r\n  /* fix nvue */overflow:hidden;width:20em;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis}.check[data-v-38f0f5a8]{margin-right:5px;border:2px solid var(--primary-color);border-left:0;border-top:0;height:12px;width:6px;-webkit-transform-origin:center;transform-origin:center;transition:all .3s;-webkit-transform:rotate(45deg);transform:rotate(45deg)}',""]),t.exports=e},"4e90":function(t,e,a){"use strict";var i=a("c32f"),n=a.n(i);n.a},"500d":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("fcf3"));a("aa9c"),a("dd2b"),a("f7a5"),a("bd06"),a("aa77"),a("bf0f");var o=i(a("3047")),r=i(a("7773")),s=i(a("a9b8")),d={name:"UniDataPicker",emits:["popupopened","popupclosed","nodeclick","input","change","update:modelValue"],mixins:[o.default],components:{DataPickerView:r.default,uniLoadMore:s.default},props:{options:{type:[Object,Array],default:function(){return{}}},popupTitle:{type:String,default:"请选择"},placeholder:{type:String,default:"请选择"},heightMobile:{type:String,default:""},readonly:{type:Boolean,default:!1},clearIcon:{type:Boolean,default:!0},border:{type:Boolean,default:!0},split:{type:String,default:"/"},ellipsis:{type:Boolean,default:!0}},data:function(){return{isOpened:!1,inputSelected:[]}},created:function(){var t=this;this.form=this.getForm("uniForms"),this.formItem=this.getForm("uniFormsItem"),this.formItem&&this.formItem.name&&(this.rename=this.formItem.name,this.form.inputChildrens.push(this)),this.$nextTick((function(){t.load()}))},methods:{clear:function(){this.inputSelected.splice(0),this._dispatchEvent([])},onPropsChange:function(){this._treeData=[],this.selectedIndex=0,this.load()},load:function(){var t=this;this.readonly?this._processReadonly(this.localdata,this.dataValue):this.isLocaldata?(this.loadData(),this.inputSelected=this.selected.slice(0)):this.parentField||this.selfField||!this.hasValue?this.hasValue&&this.getTreePath((function(){t.inputSelected=t.selected.slice(0)})):this.getNodeData((function(){t.inputSelected=t.selected.slice(0)}))},getForm:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"uniForms",e=this.$parent,a=e.$options.name;while(a!==t){if(e=e.$parent,!e)return!1;a=e.$options.name}return e},show:function(){var t=this;this.isOpened=!0,setTimeout((function(){t.$refs.pickerView.updateData({treeData:t._treeData,selected:t.selected,selectedIndex:t.selectedIndex})}),200),this.$emit("popupopened")},hide:function(){this.isOpened=!1,this.$emit("popupclosed")},handleInput:function(){this.readonly||this.show()},handleClose:function(t){this.hide()},onnodeclick:function(t){this.$emit("nodeclick",t)},ondatachange:function(t){this._treeData=this.$refs.pickerView._treeData},onchange:function(t){var e=this;this.hide(),this.$nextTick((function(){e.inputSelected=t})),this._dispatchEvent(t)},_processReadonly:function(t,e){var a,i=t.findIndex((function(t){return t.children}));if(i>-1)return Array.isArray(e)?(a=e[e.length-1],"object"===(0,n.default)(a)&&a.value&&(a=a.value)):a=e,void(this.inputSelected=this._findNodePath(a,this.localdata));if(this.hasValue){for(var o=[],r=0;r<e.length;r++){var s=e[r],d=t.find((function(t){return t.value==s}));d&&o.push(d)}o.length&&(this.inputSelected=o)}else this.inputSelected=[]},_filterForArray:function(t,e){for(var a=[],i=0;i<e.length;i++){var n=e[i],o=t.find((function(t){return t.value==n}));o&&a.push(o)}return a},_dispatchEvent:function(t){var e={};if(t.length){for(var a=new Array(t.length),i=0;i<t.length;i++)a[i]=t[i].value;e=t[t.length-1]}else e.value="";this.formItem&&this.formItem.setValue(e.value),this.$emit("input",e.value),this.$emit("update:modelValue",e.value),this.$emit("change",{detail:{value:t}})}}};e.default=d},5430:function(t){t.exports=JSON.parse('{"uni-load-more.contentdown":"上拉顯示更多","uni-load-more.contentrefresh":"正在加載...","uni-load-more.contentnomore":"沒有更多數據了"}')},"54b6":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.editGoods=function(t){return n.default.post("/cashier/storeapi/goods/editgoods",{data:t})},e.exportPrintPriceTagData=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.default.post("/cashier/storeapi/goods/exportPrintPriceTagData",{data:t})},e.getElectronicScaleInformation=function(){return n.default.post("/scale/storeapi/scale/cashierscale")},e.getGoodsCategory=function(t){return n.default.post("/cashier/storeapi/goods/category",{data:t})},e.getGoodsDetail=function(t){return n.default.post("/cashier/storeapi/goods/detail",{data:{goods_id:t}})},e.getGoodsInfoByCode=function(t){return n.default.post("/cashier/storeapi/goods/skuinfo",{data:{sku_no:t}})},e.getGoodsList=function(t){return n.default.post("/cashier/storeapi/goods/page",{data:t})},e.getGoodsSceen=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.default.post("/cashier/storeapi/goods/screen",{data:t})},e.getGoodsSkuList=function(t){return n.default.post("/cashier/storeapi/goods/skulist",{data:{goods_id:t}})},e.getManageGoodsCategory=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.default.post("/stock/storeapi/manage/getGoodsCategory",{data:t})},e.getServiceCategory=function(t){return n.default.post("/cashier/storeapi/service/category",{data:t})},e.getServiceList=function(t){return n.default.post("/cashier/storeapi/service/page",{data:t})},e.getSkuListBySelect=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.default.post("/cashier/storeapi/goods/getSkuListBySelect",{data:t})},e.setGoodsLocalRestrictions=function(t){return n.default.post("/cashier/storeapi/goods/setGoodsLocalRestrictions",{data:t})},e.setGoodsStatus=function(t){return n.default.post("/cashier/storeapi/goods/setstatus",{data:t})};var n=i(a("4e01"))},"654c":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("b7c7"));a("fd3c"),a("dd2b"),a("aa9c"),a("f7a5");var o=i(a("3047")),r={name:"UniDataPickerView",emits:["nodeclick","change","datachange","update:modelValue"],mixins:[o.default],props:{managedMode:{type:Boolean,default:!1},ellipsis:{type:Boolean,default:!0}},data:function(){return{}},created:function(){var t=this;this.managedMode||this.$nextTick((function(){t.load()}))},methods:{onPropsChange:function(){this._treeData=[],this.selectedIndex=0,this.load()},load:function(){var t=this;this.isLocaldata?this.loadData():this.dataValue.length&&this.getTreePath((function(e){t.loadData()}))},handleSelect:function(t){this.selectedIndex=t},handleNodeClick:function(t,e,a){var i=this;if(!t.disable){var o=this.dataList[e][a],r=o[this.map.text],s=o[this.map.value];if(e<this.selected.length-1?(this.selected.splice(e,this.selected.length-e),this.selected.push({text:r,value:s})):e===this.selected.length-1&&this.selected.splice(e,1,{text:r,value:s}),o.isleaf)this.onSelectedChange(o,o.isleaf);else{var d=this._updateBindData(),l=d.isleaf,c=d.hasNodes;(this._isTreeView()||c)&&(!this.isLocaldata||c&&!l)?l||c?this.onSelectedChange(o,!1):this._loadNodeData((function(t){var e;t.length?((e=i._treeData).push.apply(e,(0,n.default)(t)),i._updateBindData(o)):o.isleaf=!0;i.onSelectedChange(o,o.isleaf)}),this._nodeWhere()):this.onSelectedChange(o,!0)}}},updateData:function(t){this._treeData=t.treeData,this.selected=t.selected,this._treeData.length?this._updateBindData():this.loadData()},onDataChange:function(){this.$emit("datachange")},onSelectedChange:function(t,e){e&&this._dispatchEvent(),t&&this.$emit("nodeclick",t)},_dispatchEvent:function(){this.$emit("change",this.selected.slice(0))}}};e.default=r},6929:function(t,e,a){"use strict";a.r(e);var i=a("0548"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"6fcc":function(t,e,a){var i=a("b685");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("50f0be16",i,!0,{sourceMap:!1,shadowMode:!1})},7773:function(t,e,a){"use strict";a.r(e);var i=a("f3df"),n=a("bcec");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("7b92");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"38f0f5a8",null,!1,i["a"],void 0);e["default"]=s.exports},"7b92":function(t,e,a){"use strict";var i=a("9e22"),n=a.n(i);n.a},"955f":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa");var n,o=a("d3b4"),r=i(a("ac0d"));setTimeout((function(){n=uni.getSystemInfoSync().platform}),16);var s=(0,o.initVueI18n)(r.default),d=s.t,l={name:"UniLoadMore",emits:["clickLoadMore"],props:{status:{type:String,default:"more"},showIcon:{type:Boolean,default:!0},iconType:{type:String,default:"auto"},iconSize:{type:Number,default:24},color:{type:String,default:"#777777"},contentText:{type:Object,default:function(){return{contentdown:"",contentrefresh:"",contentnomore:""}}},showText:{type:Boolean,default:!0}},data:function(){return{webviewHide:!1,platform:n,imgBase64:"data:image/png;base64,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"}},computed:{iconSnowWidth:function(){return 2*(Math.floor(this.iconSize/24)||1)},contentdownText:function(){return this.contentText.contentdown||d("uni-load-more.contentdown")},contentrefreshText:function(){return this.contentText.contentrefresh||d("uni-load-more.contentrefresh")},contentnomoreText:function(){return this.contentText.contentnomore||d("uni-load-more.contentnomore")}},mounted:function(){},methods:{onClick:function(){this.$emit("clickLoadMore",{detail:{status:this.status}})}}};e.default=l},"9e22":function(t,e,a){var i=a("401b");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("6e91206e",i,!0,{sourceMap:!1,shadowMode:!1})},a0da:function(t){t.exports=JSON.parse('{"uni-load-more.contentdown":"上拉显示更多","uni-load-more.contentrefresh":"正在加载...","uni-load-more.contentnomore":"没有更多数据了"}')},a531:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-169fab2c]{display:none}\r\n/* 收银台相关 */uni-text[data-v-169fab2c],\r\nuni-view[data-v-169fab2c]{font-size:.14rem}body[data-v-169fab2c]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-169fab2c]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-169fab2c]::-webkit-scrollbar-button{display:none}body[data-v-169fab2c]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-169fab2c]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-169fab2c]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-169fab2c]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-169fab2c]{color:var(--primary-color)!important}.manage[data-v-169fab2c]{position:relative;background-color:#fff;padding:.15rem;min-height:100vh;box-sizing:border-box}.screen-warp[data-v-169fab2c]{padding:.15rem;background-color:#f2f3f5;margin-bottom:.15rem}.screen-warp .common-form-item .form-label[data-v-169fab2c]{width:1.2rem}.screen-warp .common-btn-wrap[data-v-169fab2c]{margin-left:1.2rem}.screen-warp .goods-category .form-input-inline[data-v-169fab2c]{width:2.8rem}.screen-warp .goods-category .form-input-inline[data-v-169fab2c] .input-value-border{border-width:0}.screen-warp .common-form-item[data-v-169fab2c]{margin-bottom:0}[data-v-169fab2c] .goods-content{display:flex}[data-v-169fab2c] .goods-content .goods-img{margin-right:.1rem;width:.5rem;height:.5rem}[data-v-169fab2c] .goods-content .goods-name{flex:1;white-space:pre-wrap;align-self:baseline}.action-btn uni-text[data-v-169fab2c]{color:var(--primary-color)}',""]),t.exports=e},a794:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-load-more",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[!t.webviewHide&&("circle"===t.iconType||"auto"===t.iconType&&"android"===t.platform)&&"loading"===t.status&&t.showIcon?a("svg",{staticClass:"uni-load-more__img uni-load-more__img--android-H5",style:{width:t.iconSize+"px",height:t.iconSize+"px"},attrs:{width:"24",height:"24",viewBox:"25 25 50 50"}},[a("circle",{style:{color:t.color},attrs:{cx:"50",cy:"50",r:"20",fill:"none","stroke-width":3}})]):!t.webviewHide&&"loading"===t.status&&t.showIcon?a("v-uni-view",{staticClass:"uni-load-more__img uni-load-more__img--ios-H5",style:{width:t.iconSize+"px",height:t.iconSize+"px"}},[a("v-uni-image",{attrs:{src:t.imgBase64,mode:"widthFix"}})],1):t._e(),t.showText?a("v-uni-text",{staticClass:"uni-load-more__text",style:{color:t.color}},[t._v(t._s("more"===t.status?t.contentdownText:"loading"===t.status?t.contentrefreshText:t.contentnomoreText))]):t._e()],1)},n=[]},a9b8:function(t,e,a){"use strict";a.r(e);var i=a("a794"),n=a("f6db");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("079e");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"ae5f2568",null,!1,i["a"],void 0);e["default"]=s.exports},ac0d:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("ee44")),o=i(a("a0da")),r=i(a("5430")),s={en:n.default,"zh-Hans":o.default,"zh-Hant":r.default};e.default=s},b685:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.uni-data-tree[data-v-57986d64]{flex:1;position:relative;font-size:14px}.error-text[data-v-57986d64]{color:#dd524d}.input-value[data-v-57986d64]{\ndisplay:flex;\nflex-direction:row;align-items:center;flex-wrap:nowrap;font-size:14px;\n\t/* line-height: 35px; */padding:0 10px;padding-right:5px;overflow:hidden;height:35px;\nbox-sizing:border-box\n}.input-value-border[data-v-57986d64]{border:1px solid #e5e5e5;border-radius:5px}.selected-area[data-v-57986d64]{flex:1;overflow:hidden;\ndisplay:flex;\nflex-direction:row}.load-more[data-v-57986d64]{\nmargin-right:auto;\n}.selected-list[data-v-57986d64]{\ndisplay:flex;\nflex-direction:row;flex-wrap:nowrap\n\t/* padding: 0 5px; */}.selected-item[data-v-57986d64]{flex-direction:row;\n\t/* padding: 0 1px; */\nwhite-space:nowrap\n}.text-color[data-v-57986d64]{color:#333}.placeholder[data-v-57986d64]{color:grey;font-size:12px}.input-split-line[data-v-57986d64]{opacity:.5}.arrow-area[data-v-57986d64]{position:relative;width:20px;\nmargin-bottom:5px;margin-left:auto;display:flex;\njustify-content:center;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transform-origin:center;transform-origin:center}.input-arrow[data-v-57986d64]{width:7px;height:7px;border-left:1px solid #999;border-bottom:1px solid #999}.uni-data-tree-cover[data-v-57986d64]{position:fixed;left:0;top:0;right:0;bottom:0;background-color:rgba(0,0,0,.4);\ndisplay:flex;\nflex-direction:column;z-index:100}.uni-data-tree-dialog[data-v-57986d64]{position:fixed;left:0;top:20%;right:0;bottom:0;background-color:#fff;border-top-left-radius:10px;border-top-right-radius:10px;\ndisplay:flex;\nflex-direction:column;z-index:102;overflow:hidden;\n}.dialog-caption[data-v-57986d64]{position:relative;\ndisplay:flex;\nflex-direction:row\n\t/* border-bottom: 1px solid #f0f0f0; */}.title-area[data-v-57986d64]{\ndisplay:flex;\nalign-items:center;\nmargin:auto;\npadding:0 10px}.dialog-title[data-v-57986d64]{\n\t/* font-weight: bold; */line-height:44px}.dialog-close[data-v-57986d64]{position:absolute;top:0;right:0;bottom:0;\ndisplay:flex;\nflex-direction:row;align-items:center;padding:0 15px}.dialog-close-plus[data-v-57986d64]{width:16px;height:2px;background-color:#666;border-radius:2px;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.dialog-close-rotate[data-v-57986d64]{position:absolute;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.picker-view[data-v-57986d64]{flex:1;overflow:hidden}.icon-clear[data-v-57986d64]{display:flex;align-items:center}\n@media (min-width:768px){.uni-data-tree-cover[data-v-57986d64]{background-color:initial}.uni-data-tree-dialog[data-v-57986d64]{position:absolute;top:55px;height:auto;min-height:400px;max-height:50vh;background-color:#fff;border:1px solid #ebeef5;box-shadow:0 2px 12px 0 rgba(0,0,0,.1);border-radius:4px;overflow:unset}.dialog-caption[data-v-57986d64]{display:none}.icon-clear[data-v-57986d64]{\n\t\t/* margin-right: 5px; */}}\n\n\n\n/* picker 弹出层通用的指示小三角, todo：扩展至上下左右方向定位 */\n.uni-popper__arrow[data-v-57986d64],\n.uni-popper__arrow[data-v-57986d64]::after{position:absolute;display:block;width:0;height:0;border-color:transparent;border-style:solid;border-width:6px}.uni-popper__arrow[data-v-57986d64]{-webkit-filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));top:-6px;left:10%;margin-right:3px;border-top-width:0;border-bottom-color:#ebeef5}.uni-popper__arrow[data-v-57986d64]::after{content:" ";top:1px;margin-left:-6px;border-top-width:0;border-bottom-color:#fff}\n\n',""]),t.exports=e},ba75:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-ae5f2568]{display:none}\r\n/* 收银台相关 */uni-text[data-v-ae5f2568],\r\nuni-view[data-v-ae5f2568]{font-size:.14rem}body[data-v-ae5f2568]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-ae5f2568]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-ae5f2568]::-webkit-scrollbar-button{display:none}body[data-v-ae5f2568]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-ae5f2568]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-ae5f2568]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-ae5f2568]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-ae5f2568]{color:var(--primary-color)!important}.uni-load-more[data-v-ae5f2568]{display:flex;flex-direction:row;height:40px;align-items:center;justify-content:center}.uni-load-more__text[data-v-ae5f2568]{font-size:14px;margin-left:8px}.uni-load-more__img[data-v-ae5f2568]{width:24px;height:24px}.uni-load-more__img--nvue[data-v-ae5f2568]{color:#666}.uni-load-more__img--android[data-v-ae5f2568],\r\n.uni-load-more__img--ios[data-v-ae5f2568]{width:24px;height:24px;-webkit-transform:rotate(0deg);transform:rotate(0deg)}.uni-load-more__img--android[data-v-ae5f2568]{-webkit-animation:loading-ios 1s 0s linear infinite;animation:loading-ios 1s 0s linear infinite}@-webkit-keyframes loading-android-data-v-ae5f2568{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-android-data-v-ae5f2568{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.uni-load-more__img--ios-H5[data-v-ae5f2568]{position:relative;-webkit-animation:loading-ios-H5-data-v-ae5f2568 1s 0s step-end infinite;animation:loading-ios-H5-data-v-ae5f2568 1s 0s step-end infinite}.uni-load-more__img--ios-H5 uni-image[data-v-ae5f2568]{position:absolute;width:100%;height:100%;left:0;top:0}@-webkit-keyframes loading-ios-H5-data-v-ae5f2568{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}8%{-webkit-transform:rotate(30deg);transform:rotate(30deg)}16%{-webkit-transform:rotate(60deg);transform:rotate(60deg)}24%{-webkit-transform:rotate(90deg);transform:rotate(90deg)}32%{-webkit-transform:rotate(120deg);transform:rotate(120deg)}40%{-webkit-transform:rotate(150deg);transform:rotate(150deg)}48%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}56%{-webkit-transform:rotate(210deg);transform:rotate(210deg)}64%{-webkit-transform:rotate(240deg);transform:rotate(240deg)}73%{-webkit-transform:rotate(270deg);transform:rotate(270deg)}82%{-webkit-transform:rotate(300deg);transform:rotate(300deg)}91%{-webkit-transform:rotate(330deg);transform:rotate(330deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-ios-H5-data-v-ae5f2568{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}8%{-webkit-transform:rotate(30deg);transform:rotate(30deg)}16%{-webkit-transform:rotate(60deg);transform:rotate(60deg)}24%{-webkit-transform:rotate(90deg);transform:rotate(90deg)}32%{-webkit-transform:rotate(120deg);transform:rotate(120deg)}40%{-webkit-transform:rotate(150deg);transform:rotate(150deg)}48%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}56%{-webkit-transform:rotate(210deg);transform:rotate(210deg)}64%{-webkit-transform:rotate(240deg);transform:rotate(240deg)}73%{-webkit-transform:rotate(270deg);transform:rotate(270deg)}82%{-webkit-transform:rotate(300deg);transform:rotate(300deg)}91%{-webkit-transform:rotate(330deg);transform:rotate(330deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.uni-load-more__img--android-H5[data-v-ae5f2568]{-webkit-animation:loading-android-H5-rotate-data-v-ae5f2568 2s linear infinite;animation:loading-android-H5-rotate-data-v-ae5f2568 2s linear infinite;-webkit-transform-origin:center center;transform-origin:center center}.uni-load-more__img--android-H5 circle[data-v-ae5f2568]{display:inline-block;-webkit-animation:loading-android-H5-dash-data-v-ae5f2568 1.5s ease-in-out infinite;animation:loading-android-H5-dash-data-v-ae5f2568 1.5s ease-in-out infinite;stroke:currentColor;stroke-linecap:round}@-webkit-keyframes loading-android-H5-rotate-data-v-ae5f2568{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-android-H5-rotate-data-v-ae5f2568{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes loading-android-H5-dash-data-v-ae5f2568{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-40}100%{stroke-dasharray:90,150;stroke-dashoffset:-120}}@keyframes loading-android-H5-dash-data-v-ae5f2568{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-40}100%{stroke-dasharray:90,150;stroke-dashoffset:-120}}',""]),t.exports=e},bcec:function(t,e,a){"use strict";a.r(e);var i=a("654c"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},c32f:function(t,e,a){var i=a("a531");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("74650662",i,!0,{sourceMap:!1,shadowMode:!1})},c8b8:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uniDataPicker:a("151b").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"manage"},[a("v-uni-view",{staticClass:"screen-warp common-form"},[a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-label",{staticClass:"form-label"},[t._v("商品名称/编码")]),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入商品名称/编码"},model:{value:t.option.search,callback:function(e){t.$set(t.option,"search",e)},expression:"option.search"}})],1)],1),a("v-uni-view",{staticClass:"form-inline goods-category"},[a("v-uni-label",{staticClass:"form-label"},[t._v("商品分类")]),a("v-uni-view",{staticClass:"form-input-inline"},[a("uni-data-picker",{attrs:{localdata:t.classifyData.data,"popup-title":"请选择商品分类"},model:{value:t.option.category_id,callback:function(e){t.$set(t.option,"category_id",e)},expression:"option.category_id"}})],1)],1),a("v-uni-view",{staticClass:"form-inline common-btn-wrap"},[a("v-uni-button",{staticClass:"screen-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.searchFn()}}},[t._v("筛选")]),a("v-uni-button",{attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.resetFn()}}},[t._v("重置")])],1)],1)],1),a("v-uni-view",{staticClass:"manage-table"},[a("uniDataTable",{ref:"goodsListTable",attrs:{url:"/stock/storeapi/manage/lists",option:t.option,cols:t.cols},scopedSlots:t._u([{key:"action",fn:function(e){return[a("v-uni-view",{staticClass:"action-btn"},[a("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toDetail()}}},[t._v("查看流水")])],1)]}}])})],1)],1)],1)},o=[]},def2:function(t,e,a){"use strict";a.r(e);var i=a("c8b8"),n=a("6929");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("4e90");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"169fab2c",null,!1,i["a"],void 0);e["default"]=s.exports},ee44:function(t){t.exports=JSON.parse('{"uni-load-more.contentdown":"Pull up to show more","uni-load-more.contentrefresh":"loading...","uni-load-more.contentnomore":"No more data"}')},f38ae:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uniLoadMore:a("a9b8").default,uniIcons:a("c370").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-data-tree"},[a("v-uni-view",{staticClass:"uni-data-tree-input",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleInput.apply(void 0,arguments)}}},[t._t("default",[a("v-uni-view",{staticClass:"input-value",class:{"input-value-border":t.border}},[t.errorMessage?a("v-uni-text",{staticClass:"selected-area error-text"},[t._v(t._s(t.errorMessage))]):t.loading&&!t.isOpened?a("v-uni-view",{staticClass:"selected-area"},[a("uni-load-more",{staticClass:"load-more",attrs:{contentText:t.loadMore,status:"loading"}})],1):t.inputSelected.length?a("v-uni-scroll-view",{staticClass:"selected-area",attrs:{"scroll-x":"true"}},[a("v-uni-view",{staticClass:"selected-list"},t._l(t.inputSelected,(function(e,i){return a("v-uni-view",{key:i,staticClass:"selected-item"},[a("v-uni-text",{staticClass:"text-color"},[t._v(t._s(e.text))]),i<t.inputSelected.length-1?a("v-uni-text",{staticClass:"input-split-line"},[t._v(t._s(t.split))]):t._e()],1)})),1)],1):a("v-uni-text",{staticClass:"selected-area placeholder"},[t._v(t._s(t.placeholder))]),t.clearIcon&&!t.readonly&&t.inputSelected.length?a("v-uni-view",{staticClass:"icon-clear",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"clear",color:"#c0c4cc",size:"24"}})],1):t._e(),t.clearIcon&&t.inputSelected.length||t.readonly?t._e():a("v-uni-view",{staticClass:"arrow-area"},[a("v-uni-view",{staticClass:"input-arrow"})],1)],1)],{options:t.options,data:t.inputSelected,error:t.errorMessage})],2),t.isOpened?a("v-uni-view",{staticClass:"uni-data-tree-cover",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleClose.apply(void 0,arguments)}}}):t._e(),t.isOpened?a("v-uni-view",{staticClass:"uni-data-tree-dialog"},[a("v-uni-view",{staticClass:"uni-popper__arrow"}),a("v-uni-view",{staticClass:"dialog-caption"},[a("v-uni-view",{staticClass:"title-area"},[a("v-uni-text",{staticClass:"dialog-title"},[t._v(t._s(t.popupTitle))])],1),a("v-uni-view",{staticClass:"dialog-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleClose.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"dialog-close-plus",attrs:{"data-id":"close"}}),a("v-uni-view",{staticClass:"dialog-close-plus dialog-close-rotate",attrs:{"data-id":"close"}})],1)],1),a("data-picker-view",{ref:"pickerView",staticClass:"picker-view",attrs:{localdata:t.localdata,preload:t.preload,collection:t.collection,field:t.field,orderby:t.orderby,where:t.where,"step-searh":t.stepSearh,"self-field":t.selfField,"parent-field":t.parentField,"managed-mode":!0,map:t.map,ellipsis:t.ellipsis},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onchange.apply(void 0,arguments)},datachange:function(e){arguments[0]=e=t.$handleEvent(e),t.ondatachange.apply(void 0,arguments)},nodeclick:function(e){arguments[0]=e=t.$handleEvent(e),t.onnodeclick.apply(void 0,arguments)}},model:{value:t.dataValue,callback:function(e){t.dataValue=e},expression:"dataValue"}})],1):t._e()],1)},o=[]},f3df:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uniLoadMore:a("a9b8").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-data-pickerview"},[a("v-uni-scroll-view",{staticClass:"selected-area",attrs:{"scroll-x":"true","scroll-y":"false","show-scrollbar":!1}},[a("v-uni-view",{staticClass:"selected-list"},[t._l(t.selected,(function(e,i){return[e.text?a("v-uni-view",{staticClass:"selected-item",class:{"selected-item-active":i==t.selectedIndex,"selected-item-text-overflow":t.ellipsis},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSelect(i)}}},[a("v-uni-text",[t._v(t._s(e.text))])],1):t._e()]}))],2)],1),a("v-uni-view",{staticClass:"tab-c"},[t._l(t.dataList,(function(e,i){return[i==t.selectedIndex?a("v-uni-scroll-view",{key:i,staticClass:"list",attrs:{"scroll-y":!0}},t._l(e,(function(e,n){return a("v-uni-view",{staticClass:"item",class:{"is-disabled":!!e.disable},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handleNodeClick(e,i,n)}}},[a("v-uni-text",{staticClass:"item-text item-text-overflow"},[t._v(t._s(e[t.map.text]))]),t.selected.length>i&&e[t.map.value]==t.selected[i].value?a("v-uni-view",{staticClass:"check"}):t._e()],1)})),1):t._e()]})),t.loading?a("v-uni-view",{staticClass:"loading-cover"},[a("uni-load-more",{staticClass:"load-more",attrs:{contentText:t.loadMore,status:"loading"}})],1):t._e(),t.errorMessage?a("v-uni-view",{staticClass:"error-message"},[a("v-uni-text",{staticClass:"error-text"},[t._v(t._s(t.errorMessage))])],1):t._e()],2)],1)},o=[]},f6db:function(t,e,a){"use strict";a.r(e);var i=a("955f"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},f828:function(t,e,a){"use strict";var i=a("6fcc"),n=a.n(i);n.a}}]);