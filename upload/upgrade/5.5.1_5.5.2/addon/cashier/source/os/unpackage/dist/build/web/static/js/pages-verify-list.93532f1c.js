(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-verify-list"],{"1f3b":function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getVerifyInfo=function(e){return n.default.post("/cashier/storeapi/verify/info",{data:{code:e}})},t.getVerifyRecordDetail=function(e){return n.default.post("/cashier/storeapi/verify/recordsdetail",{data:{id:e}})},t.getVerifyRecordList=function(e){return n.default.post("/cashier/storeapi/verify/recordlists",{data:e})},t.verifyCode=function(e){return n.default.post("/cashier/storeapi/verify/verify",{data:{verify_code:e}})};var n=a(i("4e01"))},"27b7":function(e,t,i){"use strict";i.r(t);var a=i("e23e"),n=i("626b");for(var c in n)["default"].indexOf(c)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(c);i("2d5c");var r=i("828b"),o=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"02eb6649",null,!1,a["a"],void 0);t["default"]=o.exports},"2d5c":function(e,t,i){"use strict";var a=i("3c2f"),n=i.n(a);n.a},"3c2f":function(e,t,i){var a=i("6789");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("967d").default;n("4d5c7272",a,!0,{sourceMap:!1,shadowMode:!1})},"626b":function(e,t,i){"use strict";i.r(t);var a=i("774b"),n=i.n(a);for(var c in a)["default"].indexOf(c)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(c);t["default"]=n.a},6789:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-02eb6649]{display:none}\r\n/* 收银台相关 */uni-text[data-v-02eb6649],\r\nuni-view[data-v-02eb6649]{font-size:.14rem}body[data-v-02eb6649]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-02eb6649]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-02eb6649]::-webkit-scrollbar-button{display:none}body[data-v-02eb6649]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-02eb6649]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-02eb6649]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-02eb6649]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-02eb6649]{color:var(--primary-color)!important}.check-wrap[data-v-02eb6649]{flex-direction:column;background-color:#fff;min-height:100%}.check-wrap .check-head[data-v-02eb6649]{display:flex;justify-content:space-around;line-height:.6rem;font-weight:500;height:.6rem;border-top:.01rem solid #e6e6e6;border-bottom:.01rem solid #e6e6e6}.check-wrap .check-head uni-text[data-v-02eb6649]{width:5rem;text-align:center;font-size:.18rem;border-left:.01rem solid #e6e6e6;box-sizing:border-box}.check-wrap .check-head uni-text[data-v-02eb6649]:nth-child(2){flex:1;width:0}.check-wrap .check-content[data-v-02eb6649]{display:flex;min-height:calc(100vh - 1rem)}.check-wrap .check-content > uni-view[data-v-02eb6649]{padding:.2rem;box-sizing:border-box}.check-wrap .check-content .left-wrap-content[data-v-02eb6649]{display:flex;flex-direction:column;height:calc(100vh - 1rem);padding:0}.check-wrap .check-content .left-wrap-content .wrap-search-box[data-v-02eb6649]{height:.35rem;border-bottom:.01rem solid #e6e6e6;padding:.1rem .15rem}.check-wrap .check-content .left-wrap-content .wrap-search-box .wrap-search[data-v-02eb6649]{background:#f5f5f5;display:flex;position:relative;padding:.05rem .15rem .05rem .4rem}.check-wrap .check-content .left-wrap-content .wrap-search-box .wrap-search uni-input[data-v-02eb6649]{width:100%}.check-wrap .check-content .left-wrap-content .wrap-search-box .wrap-search .iconfont[data-v-02eb6649]{position:absolute;left:.15rem;top:.08rem;cursor:pointer}.check-wrap .check-content .not-record[data-v-02eb6649]{color:#e6e6e6;font-size:.4rem;margin-top:3rem;text-align:center;width:5rem}.check-wrap .check-content .check-list[data-v-02eb6649]{width:5rem;height:calc(100% - .5rem);padding:0}.check-wrap .check-content .check-list .item-hover[data-v-02eb6649]{background:var(--primary-color-light-9)}.check-wrap .check-content .check-list .item[data-v-02eb6649]{position:relative;padding:.2rem;border-bottom:.01rem solid #e6e6e6;cursor:pointer}.check-wrap .check-content .check-list .item .name[data-v-02eb6649]{font-size:.16rem;padding-bottom:.07rem}.check-wrap .check-content .check-list .item .item-box .head[data-v-02eb6649]{display:flex;justify-content:space-between}.check-wrap .check-content .check-list .item .item-box .body[data-v-02eb6649]{margin-top:.15rem;display:flex;justify-content:space-between}.check-wrap .check-content .check-list .item .item-box .time[data-v-02eb6649]{text-align:right}.check-wrap .check-content .check-list .item .time[data-v-02eb6649],\r\n.check-wrap .check-content .check-list .item .nick-name[data-v-02eb6649]{line-height:1;width:50%;font-size:.16rem}.check-wrap .check-content .check-list .item .type[data-v-02eb6649]{position:absolute;right:.25rem;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.check-wrap .check-content .check-detail[data-v-02eb6649]{flex:1;width:0;border-left:.01rem solid #e6e6e6;position:relative}.check-wrap .check-content .check-detail .detail-empty[data-v-02eb6649]{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:2.1rem}.check-wrap .check-content .check-detail .form-content[data-v-02eb6649]{margin-top:.2rem}.check-wrap .check-content .check-detail .form-content .form-item[data-v-02eb6649]{margin-bottom:.1rem;display:flex}.check-wrap .check-content .check-detail .form-content .form-item .form-label[data-v-02eb6649]{width:1.6rem;text-align:right;padding-right:.1rem;box-sizing:border-box;height:.32rem;line-height:.32rem}.check-wrap .check-content .check-detail .form-content .form-item .form-inline[data-v-02eb6649]{line-height:.32rem;margin-right:.1rem;box-sizing:border-box}.check-wrap .check-content .check-detail .form-content .verify-item[data-v-02eb6649]{display:flex;padding:.15rem;background:#f5f5f5;margin-bottom:.1rem}.check-wrap .check-content .check-detail .form-content .verify-item .item-img[data-v-02eb6649]{width:.8rem;height:.8rem;display:flex;align-items:center;justify-content:center}.check-wrap .check-content .check-detail .form-content .verify-item .item-img uni-image[data-v-02eb6649]{width:100%}.check-wrap .check-content .check-detail .form-content .verify-item .item-info[data-v-02eb6649]{flex:1;width:0;margin-left:.15rem}',""]),e.exports=t},"774b":function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("c223");var a=i("1f3b"),n={data:function(){return{detailInfo:null,page:1,page_size:20,list:[],current_id:0,searchText:""}},onLoad:function(){this.getRecordList()},methods:{search:function(){this.page=1,this.list=[],this.getRecordList()},getRecordList:function(){var e=this;(0,a.getVerifyRecordList)({page:this.page,page_size:this.page_size,search_text:this.searchText}).then((function(t){0==t.data.list.length&&(e.detailInfo={},e.$forceUpdate()),t.code>=0&&0!=t.data.list.length&&(e.page+=1,e.list=e.list.concat(t.data.list)),e.list.length&&e.tableDataFn(e.list[0])}))},tableDataFn:function(e){this.current_id=e.id,this.getInfo(e.id)},getInfo:function(e){var t=this;(0,a.getVerifyRecordDetail)(e).then((function(e){e.code>=0?(t.detailInfo=null,t.detailInfo=e.data):t.$util.showToast({title:e.message})}))}}};t.default=n},e23e:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("base-page",[i("v-uni-view",{staticClass:"uni-flex uni-row check-wrap"},[i("v-uni-view",{staticClass:"check-head"},[i("v-uni-text",[e._v("核销列表")]),i("v-uni-text",[e._v("核销详情")])],1),i("v-uni-view",{staticClass:"check-content"},[i("v-uni-view",{staticClass:"left-wrap-content"},[i("v-uni-view",{staticClass:"wrap-search-box"},[i("v-uni-view",{staticClass:"wrap-search"},[i("v-uni-input",{attrs:{placeholder:"输入核销码","placeholder-style":"font-size:0.14rem"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.search()}},model:{value:e.searchText,callback:function(t){e.searchText=t},expression:"searchText"}}),i("v-uni-text",{staticClass:"iconfont icon31sousuo",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.search()}}})],1)],1),e.list.length>0?[i("v-uni-scroll-view",{staticClass:"check-list all-scroll",attrs:{"scroll-y":"true"},on:{scrolltolower:function(t){arguments[0]=t=e.$handleEvent(t),e.getRecordList.apply(void 0,arguments)}}},e._l(e.list,(function(t,a){return i("v-uni-view",{key:a,staticClass:"item",class:e.current_id==t.id?"item-hover":"",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.tableDataFn(t)}}},[i("v-uni-view",{staticClass:"item-box"},[i("v-uni-view",{staticClass:"head"},[i("v-uni-view",{staticClass:"nick-name"},[e._v("核销码："+e._s(t.verify_code))]),i("v-uni-view",{staticClass:"time"},[e._v("核销时间："+e._s(e.$util.timeFormat(t.verify_time,"Y-m-d H:i")))])],1),i("v-uni-view",{staticClass:"body"},[i("v-uni-text",[e._v(e._s(t.verifier_name))])],1)],1)],1)})),1)]:i("v-uni-view",{staticClass:"not-record"},[e._v("暂无数据")])],2),i("v-uni-view",{staticClass:"check-detail text"},[e.detailInfo&&Object.keys(e.detailInfo).length?i("v-uni-view",{staticClass:"form-content"},[e._l(e.detailInfo.verify_content_json.item_array,(function(t,a){return i("v-uni-view",{key:a,staticClass:"verify-item"},[i("v-uni-view",{staticClass:"item-img"},[i("v-uni-image",{attrs:{src:e.$util.img(t.img.split(",")[0],{size:"small"}),mode:"aspectFit"}})],1),i("v-uni-view",{staticClass:"item-info"},[i("v-uni-view",[e._v(e._s(t.name))])],1)],1)})),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[e._v("核销码：")]),i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-text",[e._v(e._s(e.detailInfo.verify_code))])],1)],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[e._v("核销类型：")]),i("v-uni-view",{staticClass:"form-inline"},[e._v(e._s(e.detailInfo.verify_type_name)+"核销")])],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[e._v("核销员：")]),i("v-uni-view",{staticClass:"form-inline"},[e._v(e._s(e.detailInfo.verifier_name))])],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[e._v("核销次数：")]),i("v-uni-view",{staticClass:"form-inline"},[e._v(e._s(e.detailInfo.verify_num))])],1),i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[e._v("核销时间：")]),i("v-uni-view",{staticClass:"form-inline"},[e._v(e._s(e.$util.timeFormat(e.detailInfo.verify_time,"Y-m-d H:i")))])],1),e.detailInfo.member_id>0?i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[e._v("所属会员：")]),i("v-uni-view",{staticClass:"form-inline"},[e._v(e._s(e.detailInfo.nickname))])],1):e._e(),e.detailInfo.member_id>0?i("v-uni-view",{staticClass:"form-item"},[i("v-uni-view",{staticClass:"form-label"},[e._v("手机号：")]),i("v-uni-view",{staticClass:"form-inline"},[e._v(e._s(e.detailInfo.mobile?e.detailInfo.mobile:"--"))])],1):e._e()],2):[i("v-uni-image",{staticClass:"detail-empty",attrs:{src:e.$util.img("@/static/goods/goods_empty.png"),mode:"widthFix"}})]],2)],1)],1)],1)},n=[]}}]);