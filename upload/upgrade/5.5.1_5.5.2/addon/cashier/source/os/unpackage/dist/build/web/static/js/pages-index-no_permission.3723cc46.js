(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-no_permission"],{"304c":function(n,t,r){"use strict";r.r(t);var a=r("8435"),e=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(n){r.d(t,n,(function(){return a[n]}))}(i);t["default"]=e.a},"3ca4":function(n,t,r){"use strict";var a=r("ba71"),e=r.n(a);e.a},8435:function(n,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={data:function(){return{}},onLoad:function(){uni.hideTabBar({})},methods:{}};t.default=a},8517:function(n,t,r){"use strict";r.r(t);var a=r("9116"),e=r("304c");for(var i in e)["default"].indexOf(i)<0&&function(n){r.d(t,n,(function(){return e[n]}))}(i);r("3ca4");var o=r("828b"),d=Object(o["a"])(e["default"],a["b"],a["c"],!1,null,"49db9202",null,!1,a["a"],void 0);t["default"]=d.exports},9116:function(n,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return e})),r.d(t,"a",(function(){}));var a=function(){var n=this.$createElement,t=this._self._c||n;return t("base-page",[t("v-uni-view",{staticClass:"uni-flex uni-row height-all"},[t("v-uni-view",{staticClass:"container common-wrap",staticStyle:{"-webkit-flex":"1",flex:"1"}},[t("v-uni-view",{staticClass:"msg"},[this._v("对不起，您没有权限访问该页面")])],1)],1)],1)},e=[]},ba71:function(n,t,r){var a=r("f943");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[n.i,a,""]]),a.locals&&(n.exports=a.locals);var e=r("967d").default;e("4b66b4b4",a,!0,{sourceMap:!1,shadowMode:!1})},f943:function(n,t,r){var a=r("c86c");t=a(!1),t.push([n.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-49db9202]{display:none}\r\n/* 收银台相关 */uni-text[data-v-49db9202],\r\nuni-view[data-v-49db9202]{font-size:.14rem}body[data-v-49db9202]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-49db9202]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-49db9202]::-webkit-scrollbar-button{display:none}body[data-v-49db9202]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-49db9202]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-49db9202]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-49db9202]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-49db9202]{color:var(--primary-color)!important}.container[data-v-49db9202]{display:flex;align-items:center;justify-content:center;flex-direction:column;background:#fff}.container .msg[data-v-49db9202]{color:#333;font-size:.16rem}',""]),n.exports=t}}]);