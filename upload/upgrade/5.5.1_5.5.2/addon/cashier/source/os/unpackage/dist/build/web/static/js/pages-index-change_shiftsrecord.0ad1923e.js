(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-change_shiftsrecord"],{"00fb":function(t,e,a){var n=a("ae36");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("7ca7fbe7",n,!0,{sourceMap:!1,shadowMode:!1})},"11a9":function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.addPrinter=function(t){return i.default.post("/printer/storeapi/printer/add",{data:t})},e.deletePrinter=function(t){return i.default.post("/printer/storeapi/printer/deleteprinter",{data:{printer_id:t}})},e.editPrinter=function(t){return i.default.post("/printer/storeapi/printer/edit",{data:t})},e.getOrderType=function(){return i.default.post("/printer/storeapi/printer/getordertype")},e.getPrinterInfo=function(t){return i.default.post("/printer/storeapi/printer/info",{data:{printer_id:t}})},e.getPrinterList=function(t){return i.default.post("/printer/storeapi/printer/lists",{data:t})},e.getTemplate=function(){return i.default.post("/printer/storeapi/printer/template")},e.printTicket=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=s.default.getLocalConfig();return t.printer_ids="all"==e.printerSelectType?"all":e.printerSelectIds.toString(),i.default.post("/cashier/storeapi/cashier/printticket",{data:t})},a("c9b5"),a("bf0f"),a("ab80");var i=n(a("4e01")),s=n(a("a07f"))},"1bf0":function(t,e,a){"use strict";var n=a("39fd"),i=a.n(n);i.a},3523:function(t,e,a){"use strict";a.r(e);var n=a("6e0b"),i=a("53f4");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("1bf0");var o=a("828b"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"3387bb78",null,!1,n["a"],void 0);e["default"]=r.exports},"39fd":function(t,e,a){var n=a("48fc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("2cfa7e01",n,!0,{sourceMap:!1,shadowMode:!1})},"48fc":function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-3387bb78]{display:none}\r\n/* 收银台相关 */uni-text[data-v-3387bb78],\r\nuni-view[data-v-3387bb78]{font-size:.14rem}body[data-v-3387bb78]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-3387bb78]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-3387bb78]::-webkit-scrollbar-button{display:none}body[data-v-3387bb78]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-3387bb78]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-3387bb78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-3387bb78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-3387bb78]{color:var(--primary-color)!important}.uni-select-lay[data-v-3387bb78]{position:relative;z-index:999;box-sizing:border-box}.uni-select-lay .uni-select-input[data-v-3387bb78]{opacity:0;position:absolute;z-index:-111}.uni-select-lay .uni-select-lay-select[data-v-3387bb78]{-webkit-user-select:none;user-select:none;position:relative;z-index:3;height:.32rem;padding:0 .3rem 0 .1rem;box-sizing:border-box;border-radius:.02rem;border:.01rem solid #e5e5e5;display:flex;align-items:center;font-size:.14rem;color:#999}.uni-select-lay .uni-select-lay-select .uni-disabled[data-v-3387bb78]{position:absolute;left:0;width:100%;height:100%;z-index:19;cursor:no-drop;background:hsla(0,0%,100%,.5)}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close[data-v-3387bb78]{position:absolute;right:.35rem;top:0;height:100%;width:.15rem;display:flex;align-items:center;justify-content:center;z-index:3;cursor:pointer}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]{position:relative;background:#fff;width:.13rem;height:.13rem;border-radius:50%;border:.01rem solid #bbb}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::before, .uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::after{content:"";position:absolute;left:20%;top:50%;height:.01rem;width:60%;-webkit-transform:rotate(45deg);transform:rotate(45deg);background-color:#bbb}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::after{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.uni-select-lay .uni-select-lay-select .uni-select-lay-input[data-v-3387bb78]{font-size:.14rem;color:#999;display:block;width:98%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:.3rem;box-sizing:border-box}.uni-select-lay .uni-select-lay-select .uni-select-lay-input.active[data-v-3387bb78]{color:#333}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon[data-v-3387bb78]{cursor:pointer;position:absolute;right:0;top:0;height:100%;width:.3rem;display:flex;align-items:center;justify-content:center}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon[data-v-3387bb78]::before{content:"";width:.01rem;height:100%;position:absolute;left:0;top:0;background-color:#e5e5e5}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon uni-text[data-v-3387bb78]{display:block;width:0;height:0;border-width:.07rem .07rem 0;border-style:solid;border-color:#bbb transparent transparent;transition:.3s}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled[data-v-3387bb78]{cursor:no-drop}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled uni-text[data-v-3387bb78]{width:.2rem;height:.2rem;border:.02rem solid red;border-radius:50%;transition:.3s;position:relative;z-index:999}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled uni-text[data-v-3387bb78]::after{content:"";position:absolute;top:50%;left:0;width:100%;height:.02rem;margin-top:-.01rem;background-color:red;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.uni-select-lay .uni-select-lay-select.active .uni-select-lay-icon uni-text[data-v-3387bb78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.uni-select-lay .uni-select-lay-options[data-v-3387bb78]{-webkit-user-select:none;user-select:none;position:absolute;top:calc(100% + .05rem);left:0;width:100%;max-height:2.5rem;border-radius:.02rem;border:1px solid #e5e5e5;background:#fff;padding:.05rem 0;box-sizing:border-box;z-index:9}.uni-select-lay .uni-select-lay-options .uni-select-lay-item[data-v-3387bb78]{padding:0 .1rem;box-sizing:border-box;cursor:pointer;line-height:2.5;transition:.3s;font-size:.14rem}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.active[data-v-3387bb78]{background:var(--primary-color);color:#fff}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.active[data-v-3387bb78]:hover{background:var(--primary-color);color:#fff}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.disabled[data-v-3387bb78]{color:#999;cursor:not-allowed}.uni-select-lay .uni-select-lay-options .uni-select-lay-item[data-v-3387bb78]:hover{background-color:#f5f5f5}.uni-select-lay .uni-select-lay-options .nosearch[data-v-3387bb78]{font-size:.16rem;line-height:3;text-align:center;color:#666}.uni-date-mask[data-v-3387bb78]{position:fixed;bottom:0;top:0;left:0;right:0;background-color:transparent;transition-duration:.3s;z-index:8}',""]),t.exports=e},"4ce4":function(t,e,a){"use strict";a.r(e);var n=a("5594"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},"53f4":function(t,e,a){"use strict";a.r(e);var n=a("6c5f"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},5594:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("e838"),a("e966"),a("fd3c"),a("22b6"),a("bf0f"),a("2797"),a("aa9c"),a("d4b5");var n=a("11a9"),i=a("b875"),s={data:function(){var t=this;return{shiftsData:null,screen:{page:1,start_time:"",end_time:"",uid:0},userList:[],cols:[{width:20,title:"员工",field:"username",align:"left"},{width:20,title:"开始时间",align:"center",return:function(e){return e.start_time?t.$util.timeFormat(e.start_time):""}},{width:20,title:"结束时间",align:"center",return:function(e){return t.$util.timeFormat(e.end_time)}},{width:15,title:"总销售",align:"right",return:function(e){return t.$util.moneyFormat(parseFloat(e.billing_money)+parseFloat(e.buycard_money))}},{width:15,title:"会员充值",align:"right",return:function(e){return t.$util.moneyFormat(e.recharge_money)}},{width:15,title:"应收金额",align:"right",return:function(e){return t.$util.moneyFormat(parseFloat(e.billing_money)+parseFloat(e.buycard_money)+parseFloat(e.recharge_money)-parseFloat(e.refund_money))}},{width:15,title:"支付统计",align:"right",return:function(e){return t.$util.moneyFormat(parseFloat(e.cash)+parseFloat(e.alipay)+parseFloat(e.wechatpay)+parseFloat(e.own_wechatpay)+parseFloat(e.own_alipay)+parseFloat(e.own_pos))}},{width:16,title:"商品销售",align:"right",return:function(t){return t.sale_goods_count.class_num+"种 "+t.sale_goods_count.num+"件"}},{width:15,title:"操作",action:!0,align:"right"}]}},onLoad:function(){this.getUserListFn()},methods:{switchStoreAfter:function(){this.screen={page:1,start_time:"",end_time:"",uid:0},this.$refs.table.load(),this.getUserListFn()},saleGoods:function(t){this.$util.redirectTo("/pages/index/change_shiftssalelist",{id:t.value.id})},detail:function(t){var e=this.$util.deepClone(t.value);e.total_sale=parseFloat(e.billing_money)+parseFloat(e.buycard_money),e.total_sale_count=parseInt(e.billing_count)+parseInt(e.buycard_count),e.total_count=e.total_sale_count+parseInt(e.recharge_count)+parseInt(e.refund_count),e.total_money=e.total_sale+parseFloat(e.recharge_money)-parseFloat(e.refund_money),e.total_pay_money=parseFloat(e.cash)+parseFloat(e.alipay)+parseFloat(e.wechatpay)+parseFloat(e.own_wechatpay)+parseFloat(e.own_alipay)+parseFloat(e.own_pos),e.total_pay_count=parseInt(e.cash_count)+parseInt(e.alipay_count)+parseInt(e.wechatpay_count)+parseInt(e.own_wechatpay_count)+parseInt(e.own_alipay_count)+parseInt(e.own_pos_count),this.shiftsData=e,this.$refs.shiftslistPop.open("center")},getUserListFn:function(){var t=this;(0,i.getUserList)({page:1,page_size:0}).then((function(e){e.code>=0&&0!=e.data.list.length&&(t.userList=e.data.list.map((function(t){return{label:t.username,value:t.uid}})))}))},reset:function(){this.screen={page:1,start_time:"",end_time:"",uid:0},this.$refs.table.load(this.screen)},selectUser:function(t,e){this.screen.uid=t>=0?parseInt(e.value):0},search:function(){this.$refs.table.load(this.screen)},printTicketFn:function(){var t=this,e={record_id:this.shiftsData.id};(0,n.printTicket)(e).then((function(e){if(0==e.code)if(Object.values(e.data).length){var a=Object.values(e.data);try{var n={printer:[]};a.forEach((function(t){n.printer.push({printer_type:t.printer_info.printer_type,host:t.printer_info.host,ip:t.printer_info.ip,port:t.printer_info.port,content:t.content,print_width:t.printer_info.print_width})})),t.$pos.send("Print",JSON.stringify(n))}catch(i){console.log("err",i,e.data)}}else t.$util.showToast({title:"未开启交接班小票打印"});else t.$util.showToast({title:e.message?e.message:"小票打印失败"})}))}}};e.default=s,window.POS_PRINT_CALLBACK=function(t){uni.showToast({title:t,icon:"none"})}},"6c5f":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("bf0f"),a("2797"),a("8f71"),a("4626"),a("5ac7");var n={name:"select-lay",props:{disabled:{type:Boolean,default:!1},zindex:{type:Number,default:999},options:{type:Array,default:function(){return[]}},name:{type:String,default:""},value:{type:[String,Number],default:""},placeholder:{type:String,default:"请选择"},showplaceholder:{type:Boolean,default:!0},slabel:{type:String,default:"label"},svalue:{type:String,default:"value"}},data:function(){return{active:!1,isfocus:!1,isremove:!1,ismove:!1,changevalue:"",oldvalue:"",changes:!1,changesValue:"",vlist:[],settimer:null}},mounted:function(){this.itemcheck()},watch:{value:function(){this.itemcheck()},options:function(){this.itemcheck()}},methods:{itemcheck:function(){var t=this;""!=this.value?this.options.length>0&&this.options.forEach((function(e){t.value!=e[t.svalue]||(t.oldvalue=t.changevalue=e[t.slabel])})):this.oldvalue=this.changevalue=""},select:function(){this.disabled||(this.active=!this.active,this.active?this.changes=!1:this.changevalue=this.oldvalue)},unifocus:function(){this.disabled||(this.active=!0,this.changes=!1,this.isfocus=!0)},uniblur:function(){var t=this;this.isfocus=!1,setTimeout((function(){t.isremove||t.ismove?(t.isremove=!1,t.ismove=!1):(t.changevalue=t.oldvalue,t.isremove=!1,t.active=!1)}),153)},movetouch:function(){var t=this;setTimeout((function(){t.isfocus?t.ismove=!1:t.ismove||(t.ismove=!0)}),100)},selectmove:function(){var t=this;setTimeout((function(){t.isfocus?t.ismove=!1:t.ismove||(t.ismove=!0)}),100)},removevalue:function(){this.isremove=!0,this.changes=!1,this.changevalue=""},intchange:function(){var t=this;""!=this.changevalue?this.oldvalue!=this.changevalue&&(this.vlist=[],this.changes=!0,this.changesValue="正在搜索...",this.settimer&&clearTimeout(this.settimer),this.settimer=setTimeout((function(){t.vlist=t.options.filter((function(e){return e[t.slabel].includes(t.changevalue)})),0===t.vlist.length&&(t.changesValue="暂无匹配内容！")}),600)):this.changes=!1},selectitem:function(t,e){if(e&&e.disabled)return!1;this.changevalue=this.oldvalue,this.active=!1,this.$emit("selectitem",t,e)}}};e.default=n},"6e0b":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-select-lay",style:{"z-index":t.zindex}},[a("v-uni-input",{staticClass:"uni-select-input",attrs:{type:"text",name:t.name,readonly:!0},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}),a("v-uni-view",{staticClass:"uni-select-lay-select",class:{active:t.active}},[t.disabled?a("v-uni-view",{staticClass:"uni-disabled"}):t._e(),""!=t.changevalue&&this.active?a("v-uni-view",{staticClass:"uni-select-lay-input-close"},[a("v-uni-text",{on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.removevalue.apply(void 0,arguments)}}})],1):t._e(),a("v-uni-input",{staticClass:"uni-select-lay-input",class:{active:""!=t.changevalue&&t.changevalue!=t.placeholder},attrs:{type:"text",readonly:!0,disabled:"true",placeholder:t.placeholder},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.unifocus.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.intchange.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.uniblur.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}},model:{value:t.changevalue,callback:function(e){t.changevalue=e},expression:"changevalue"}}),a("v-uni-view",{staticClass:"uni-select-lay-icon",class:{disabled:t.disabled},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}}},[a("v-uni-text")],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.active,expression:"active"}],staticClass:"uni-date-mask",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}}}),a("v-uni-scroll-view",{directives:[{name:"show",rawName:"v-show",value:t.active,expression:"active"}],staticClass:"uni-select-lay-options",attrs:{"scroll-y":!0},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.selectmove.apply(void 0,arguments)},touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.movetouch.apply(void 0,arguments)}}},[t.changes?[t.vlist.length>0?t._l(t.vlist,(function(e,n){return a("v-uni-view",{key:n,staticClass:"uni-select-lay-item",class:{active:t.value==e[t.svalue]},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.selectitem(n,e)}}},[t._v(t._s(e[t.slabel]))])})):[a("v-uni-view",{staticClass:"nosearch"},[t._v(t._s(t.changesValue))])]]:[t.showplaceholder?a("v-uni-view",{staticClass:"uni-select-lay-item",class:{active:""==t.value},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.selectitem(-1,null)}}},[t._v(t._s(t.placeholder))]):t._e(),t._l(t.options,(function(e,n){return a("v-uni-view",{key:n,staticClass:"uni-select-lay-item",class:{active:t.value==e[t.svalue],disabled:e.disabled},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.selectitem(n,e)}}},[t._v(t._s(e[t.slabel]))])}))]],2)],1)},i=[]},"722b":function(t,e,a){"use strict";var n=a("00fb"),i=a.n(n);i.a},"99f4":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return n}));var n={uniDatetimePicker:a("ea9b").default,selectLay:a("3523").default,uniDataTable:a("43ca").default,uniPopup:a("2166").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"manage"},[a("v-uni-view",{staticClass:"screen-warp  common-form"},[a("uni-datetime-picker",{attrs:{type:"datetime",placeholder:"请选择开始时间",clearIcon:!1},model:{value:t.screen.start_time,callback:function(e){t.$set(t.screen,"start_time",e)},expression:"screen.start_time"}}),a("uni-datetime-picker",{attrs:{type:"datetime",placeholder:"请选择结束时间",clearIcon:!1},model:{value:t.screen.end_time,callback:function(e){t.$set(t.screen,"end_time",e)},expression:"screen.end_time"}}),a("v-uni-view",[a("select-lay",{attrs:{zindex:10,value:t.screen.uid,name:"names",placeholder:"请选择员工",options:t.userList},on:{selectitem:function(e){arguments[0]=e=t.$handleEvent(e),t.selectUser.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-view",{staticClass:"form-inline common-btn-wrap"},[a("v-uni-button",{staticClass:"screen-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)}}},[t._v("搜索")]),a("v-uni-button",{attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.reset()}}},[t._v("重置")])],1)],1)],1),a("uni-data-table",{ref:"table",attrs:{url:"/cashier/storeapi/cashier/changeShiftsRecord",option:t.screen,cols:t.cols},scopedSlots:t._u([{key:"action",fn:function(e){return[a("v-uni-view",{staticClass:"common-table-action"},[a("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.detail(e)}}},[t._v("查看详情")])],1),a("v-uni-view",{staticClass:"common-table-action"},[a("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.saleGoods(e)}}},[t._v("商品销售")])],1)]}}])})],1),a("uni-popup",{ref:"shiftslistPop"},[a("v-uni-view",{staticClass:"pop-box shiftsslistPop"},[a("v-uni-view",{staticClass:"pop-header"},[a("v-uni-view",{staticClass:"pop-header-text"},[t._v("交班详情")]),a("v-uni-view",{staticClass:"pop-header-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.shiftslistPop.close()}}},[a("v-uni-text",{staticClass:"iconguanbi1 iconfont"})],1)],1),t.shiftsData?a("v-uni-view",{staticClass:"pop-content common-scrollbar"},[a("v-uni-view",{staticClass:"pop-content-item"},[a("v-uni-view",{staticClass:"pop-content-text"},[t._v("总销售（"+t._s(t._f("moneyFormat")(t.shiftsData.total_sale))+"元 "+t._s(t.shiftsData.total_sale_count)+"笔）")]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("开单销售（"+t._s(t._f("moneyFormat")(t.shiftsData.billing_money))+"元 "+t._s(t.shiftsData.billing_count)+"笔）")]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("售卡销售（"+t._s(t._f("moneyFormat")(t.shiftsData.buycard_money))+"元 "+t._s(t.shiftsData.buycard_count)+"笔）")])],1),a("v-uni-view",{staticClass:"pop-content-item"},[a("v-uni-view",{staticClass:"pop-content-text"},[t._v("会员充值（"+t._s(t._f("moneyFormat")(t.shiftsData.recharge_money))+"元 "+t._s(t.shiftsData.recharge_count)+"笔）")])],1),a("v-uni-view",{staticClass:"pop-content-item"},[a("v-uni-view",{staticClass:"pop-content-text"},[t._v("应收金额（"+t._s(t._f("moneyFormat")(t.shiftsData.total_money))+"元 "+t._s(t.shiftsData.total_count)+"笔）")]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("开单销售（"+t._s(t._f("moneyFormat")(t.shiftsData.billing_money))+"元 "+t._s(t.shiftsData.billing_count)+"笔）")]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("售卡销售（"+t._s(t._f("moneyFormat")(t.shiftsData.buycard_money))+"元 "+t._s(t.shiftsData.buycard_count)+"笔）")]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("会员充值（"+t._s(t._f("moneyFormat")(t.shiftsData.recharge_money))+"元 "+t._s(t.shiftsData.recharge_count)+"笔）")]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("订单退款（"+t._s(t._f("moneyFormat")(t.shiftsData.refund_money))+"元 "+t._s(t.shiftsData.refund_count)+"笔）")])],1),a("v-uni-view",{staticClass:"pop-content-item"},[a("v-uni-view",{staticClass:"pop-content-text"},[t._v("支付统计（"+t._s(t._f("moneyFormat")(t.shiftsData.total_pay_money))+"元 "+t._s(t.shiftsData.total_pay_count)+"笔）")]),t.shiftsData.cash>0?a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("现金收款（"+t._s(t._f("moneyFormat")(t.shiftsData.cash))+"元 "+t._s(t.shiftsData.cash_count)+"笔）")]):t._e(),t.shiftsData.wechatpay>0?a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("微信收款（"+t._s(t._f("moneyFormat")(t.shiftsData.wechatpay))+"元 "+t._s(t.shiftsData.wechatpay_count)+"笔）")]):t._e(),t.shiftsData.alipay>0?a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("支付宝收款（"+t._s(t._f("moneyFormat")(t.shiftsData.alipay))+"元 "+t._s(t.shiftsData.alipay_count)+"笔）")]):t._e(),t.shiftsData.own_wechatpay>0?a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("个人微信收款（"+t._s(t._f("moneyFormat")(t.shiftsData.own_wechatpay))+"元 "+t._s(t.shiftsData.own_wechatpay_count)+"笔）")]):t._e(),t.shiftsData.own_alipay>0?a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("个人支付宝收款（"+t._s(t._f("moneyFormat")(t.shiftsData.own_alipay))+"元 "+t._s(t.shiftsData.own_alipay_count)+"笔）")]):t._e(),t.shiftsData.own_pos>0?a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("个人POS收款（"+t._s(t._f("moneyFormat")(t.shiftsData.own_pos))+"元 "+t._s(t.shiftsData.own_pos_count)+"笔）")]):t._e()],1),a("v-uni-view",{staticClass:"pop-content-item"},[a("v-uni-view",{staticClass:"pop-content-text"},[t._v("商品销售（"+t._s(t.shiftsData.sale_goods_count.class_num)+"种 "+t._s(t.shiftsData.sale_goods_count.num)+"件）")]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("线上销售（"+t._s(t.shiftsData.sale_goods_count.online_class_num)+"种 "+t._s(t.shiftsData.sale_goods_count.online_num)+"件）")]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("线下销售（"+t._s(t.shiftsData.sale_goods_count.offline_class_num)+"种 "+t._s(t.shiftsData.sale_goods_count.offline_num)+"件）")])],1)],1):t._e(),a("v-uni-view",{staticClass:"pop-content-footer"},[a("v-uni-button",{staticClass:"primary-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.printTicketFn.apply(void 0,arguments)}}},[t._v("打印小票")])],1)],1)],1)],1)},s=[]},ae36:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-2778c9be]{display:none}\r\n/* 收银台相关 */uni-text[data-v-2778c9be],\r\nuni-view[data-v-2778c9be]{font-size:.14rem}body[data-v-2778c9be]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-2778c9be]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-2778c9be]::-webkit-scrollbar-button{display:none}body[data-v-2778c9be]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-2778c9be]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-2778c9be]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-2778c9be]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-2778c9be]{color:var(--primary-color)!important}.manage[data-v-2778c9be]{position:relative;background-color:#fff;padding:.15rem;height:100vh;box-sizing:border-box}.screen-warp[data-v-2778c9be]{padding:.15rem;background-color:#f2f3f5;margin-bottom:.15rem;display:flex;justify-content:start}.screen-warp[data-v-2778c9be] .uni-date-x{height:.35rem}.screen-warp[data-v-2778c9be] .uni-select-lay{background:#fff}.screen-warp[data-v-2778c9be] .uni-select-lay .uni-select-lay-select{height:.37rem}.screen-warp .primary-btn[data-v-2778c9be]{margin-left:0}.screen-warp > *[data-v-2778c9be]{margin-right:.15rem}.screen-warp .common-btn-wrap[data-v-2778c9be]{margin-left:0}.pop-box[data-v-2778c9be]{background:#fff;width:4rem;height:60vh;display:flex;flex-direction:column}.pop-box .pop-header[data-v-2778c9be]{width:100%;padding:0 .15rem 0 .2rem;height:.5rem;margin:0 auto;line-height:.5rem;border-bottom:.01rem solid #f0f0f0;font-size:.14rem;color:#333;overflow:hidden;border-radius:.02rem .2rem 0 0;box-sizing:border-box;display:flex;justify-content:space-between}.pop-box .pop-header .pop-header-close[data-v-2778c9be]{cursor:pointer}.pop-box .pop-header .pop-header-close uni-text[data-v-2778c9be]{font-size:.18rem}.pop-box .pop-content[data-v-2778c9be]{flex:1;height:0;padding:.1rem .2rem;box-sizing:border-box;font-weight:900;overflow-y:scroll}.pop-box .pop-contents[data-v-2778c9be]{margin-top:.3rem;width:3rem;height:.8rem;padding:.1rem .2rem;box-sizing:border-box;font-weight:900;display:flex;flex-direction:column;flex-wrap:wrap;justify-content:space-between}.pop-box .pop-content-item[data-v-2778c9be]{margin-left:.3rem}.pop-box .pop-content-items[data-v-2778c9be]{margin-left:.3rem}.pop-box .pop-content-text[data-v-2778c9be]{padding:.1rem}.pop-box .pop-contents-text[data-v-2778c9be]{margin-left:.4rem;font-weight:400;padding:.1rem}.pop-box .pop-content-footer[data-v-2778c9be]{display:flex;padding:.1rem .2rem;border-top:.01rem solid #e6e6e6;justify-content:center}.pop-box .pop-content-footer uni-button[data-v-2778c9be]{width:1rem;margin:0}',""]),t.exports=e},aee8:function(t,e,a){"use strict";a.r(e);var n=a("99f4"),i=a("4ce4");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("722b");var o=a("828b"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"2778c9be",null,!1,n["a"],void 0);e["default"]=r.exports}}]);