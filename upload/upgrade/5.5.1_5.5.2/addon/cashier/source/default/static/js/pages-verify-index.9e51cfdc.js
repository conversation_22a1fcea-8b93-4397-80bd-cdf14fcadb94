(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-verify-index"],{"05ac":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("0c26"),n("5c47"),n("af8f");var i=n("a35c"),a={data:function(){return{step:"search",code:"",verifyInfo:null,isRepeat:!1,inputFocus:!1}},onLoad:function(){var e=this;uni.hideTabBar(),this.$nextTick((function(){e.inputFocus=!0}))},methods:{codeInputBlur:function(){var e=this;this.inputFocus=!1,this.verifyInfo||this.$nextTick((function(){e.inputFocus=!0}))},deleteCode:function(){this.code=this.code.substr(0,this.code.length-1)},search:function(){var e=this;this.code?setTimeout((function(){(0,i.getVerifyInfo)(e.code.trim()).then((function(t){e.code="",t.code>=0?(e.verifyInfo=t.data,e.step="verify"):e.$util.showToast({title:t.message})}))}),200):this.$util.showToast({title:"请输入核销码"})},verify:function(){var e=this;this.verifyInfo?this.isRepeat||(this.isRepeat=!0,(0,i.verifyCode)(this.verifyInfo.verify_code).then((function(t){e.isRepeat=!1,t.code>=0&&(e.step="search",e.verifyInfo=null,e.code=""),e.$util.showToast({title:t.message})}))):this.$util.showToast({title:"请先查询核销码信息"})},scancode:function(){var e=this;uni.scanCode({scanType:["qrCode","barCode"],success:function(t){e.code=t.result,e.search()},fail:function(t){e.$util.showToast({title:"扫码失败"})}})}}};t.default=a},"24a7":function(e,t,n){"use strict";n.r(t);var i=n("05ac"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=a.a},3959:function(e,t,n){var i=n("dcf1");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("967d").default;a("13fbcb5b",i,!0,{sourceMap:!1,shadowMode:!1})},"61e2":function(e,t,n){"use strict";var i=n("3959"),a=n.n(i);a.a},6757:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("base-page",[n("v-uni-view",{staticClass:"uni-flex uni-row height-all"},["search"==e.step?n("v-uni-view",{staticClass:"container common-wrap",staticStyle:{"-webkit-flex":"1",flex:"1"}},[n("v-uni-view",{staticClass:"search-title"},[e._v("查询核销码核销")]),n("v-uni-view",{staticClass:"search-wrap"},[n("v-uni-view",{staticClass:"input-wrap"},[n("v-uni-input",{attrs:{type:"text",value:"",placeholder:"请输入核销码或扫描核销码","placeholder-class":"placeholder",focus:e.inputFocus},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.search.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.inputFocus=!0},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.codeInputBlur.apply(void 0,arguments)}},model:{value:e.code,callback:function(t){e.code=t},expression:"code"}})],1),n("v-uni-button",{staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.search.apply(void 0,arguments)}}},[e._v("查询")])],1),n("v-uni-view",{staticClass:"search-desc"},[e._v("使用扫码枪扫码时需注意光标需要停留在输入框中")]),n("v-uni-view",{staticClass:"record",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.redirectTo("/pages/verify/list")}}},[n("v-uni-text",[e._v("核销记录")])],1)],1):e._e(),"verify"==e.step?n("v-uni-view",{staticClass:"content-box common-wrap",staticStyle:{"-webkit-flex":"1",flex:"1"}},[n("v-uni-view",{staticClass:"input-wrap"},[n("v-uni-input",{attrs:{placeholder:"请输入核销码"},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.search.apply(void 0,arguments)}},model:{value:e.code,callback:function(t){e.code=t},expression:"code"}}),n("v-uni-button",{staticClass:"primary-btn search",attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.search()}}},[e._v("查询")])],1),n("v-uni-view",{staticClass:"content-data"},[n("v-uni-view",{staticClass:"content-top"},e._l(e.verifyInfo.data.item_array,(function(t,i){return n("v-uni-view",{key:i,staticClass:"verify-item"},[n("v-uni-view",{staticClass:"container-image"},[n("v-uni-image",{attrs:{src:e.$util.img(t.img.split(",")[0],{size:"small"}),mode:"aspectFit"}})],1),n("v-uni-view",{staticClass:"container-box"},[n("v-uni-view",{staticClass:"content-name"},[e._v(e._s(t.name))]),n("v-uni-view",{staticClass:"content-name"},[e._v("x"+e._s(t.num))])],1)],1)})),1),n("v-uni-view",{staticClass:"content-bottom"},[n("v-uni-view",{staticClass:"bottom-item"},[n("v-uni-view",[e._v("核销状态："+e._s(0==e.verifyInfo.is_verify?"待核销":"已核销"))])],1),n("v-uni-view",{staticClass:"bottom-item"},[n("v-uni-view",[e._v("核销类型："+e._s(e.verifyInfo.verify_type_name)+"核销")])],1),n("v-uni-view",{staticClass:"bottom-item"},[n("v-uni-view",[e._v("总次数/已使用："+e._s(e.verifyInfo.verify_total_count?e.verifyInfo.verify_total_count:"不限")+"次/"+e._s(e.verifyInfo.verify_use_num)+"次")])],1),n("v-uni-view",{staticClass:"bottom-item"},[n("v-uni-view",[e._v("有效期："+e._s(e.verifyInfo.expire_time?e.$util.timeFormat(e.verifyInfo.expire_time,"Y-m-d H:i"):"永久"))])],1)],1),n("v-uni-view",{staticClass:"verify-action"},[n("v-uni-button",{staticClass:"default-btn",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.step="search"}}},[e._v("取消")]),n("v-uni-button",{directives:[{name:"show",rawName:"v-show",value:0==e.verifyInfo.is_verify,expression:"verifyInfo.is_verify == 0"}],staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.verify()}}},[e._v("立即核销")])],1)],1)],1):e._e()],1)],1)},a=[]},"8ccb":function(e,t,n){"use strict";n.r(t);var i=n("6757"),a=n("24a7");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("61e2");var r=n("828b"),c=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"526d694e",null,!1,i["a"],void 0);t["default"]=c.exports},a35c:function(e,t,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getVerifyInfo=function(e){return a.default.post("/cashier/storeapi/verify/info",{data:{code:e}})},t.getVerifyRecordDetail=function(e){return a.default.post("/cashier/storeapi/verify/recordsdetail",{data:{id:e}})},t.getVerifyRecordList=function(e){return a.default.post("/cashier/storeapi/verify/recordlists",{data:e})},t.verifyCode=function(e){return a.default.post("/cashier/storeapi/verify/verify",{data:{verify_code:e}})};var a=i(n("a3b5"))},dcf1:function(e,t,n){var i=n("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-526d694e]{display:none}\r\n/* 收银台相关 */uni-text[data-v-526d694e],\r\nuni-view[data-v-526d694e]{font-size:.14rem}body[data-v-526d694e]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-526d694e]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-526d694e]::-webkit-scrollbar-button{display:none}body[data-v-526d694e]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-526d694e]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-526d694e]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-526d694e]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-526d694e]{color:var(--primary-color)!important}.container[data-v-526d694e]{display:flex;align-items:center;justify-content:center;flex-direction:column}.search-title[data-v-526d694e]{font-size:.18rem;color:#303133}.search-wrap[data-v-526d694e]{display:flex;margin-top:.3rem}.search-wrap uni-button[data-v-526d694e]{width:1rem;text-align:center;box-sizing:border-box;line-height:.5rem}.search-desc[data-v-526d694e]{color:#909399;font-size:.14rem;margin-top:.3rem}.input-wrap[data-v-526d694e]{width:4.5rem;height:.5rem;border:.01rem solid #ccc;display:flex;border-radius:.02rem;align-items:center}.input-wrap uni-input[data-v-526d694e]{flex:1;padding:0 .15rem;font-size:.16rem}.input-wrap .placeholder[data-v-526d694e]{flex:1;height:.58rem;line-height:.58rem;font-size:.16rem;font-weight:400;color:#909399}.input-wrap .iconfont[data-v-526d694e]{font-size:.18rem;padding:0 .15rem;font-weight:700}.record[data-v-526d694e]{text-align:center;margin-top:.2rem}.record uni-text[data-v-526d694e]{color:var(--primary-color);font-size:.14rem;cursor:pointer}.content-box[data-v-526d694e]{padding:.15rem .15rem .15rem .15rem}.content-box .input-wrap[data-v-526d694e]{width:6rem;height:.4rem;border:.01rem solid #ccc;display:flex;border-radius:.02rem}.content-box .input-wrap uni-input[data-v-526d694e]{flex:1;padding:0 .15rem;height:.38rem;line-height:.38rem;font-size:.16rem;font-weight:400}.content-box .input-wrap .placeholder[data-v-526d694e]{font-weight:400;color:#909399;font-size:.18rem}.content-box .input-wrap .search[data-v-526d694e]{border-radius:0;width:1rem;line-height:.38rem;font-size:.16rem;font-family:Source Han Sans CN}.content-box .input-wrap .search[data-v-526d694e]::after{border:none}.content-box .content-data[data-v-526d694e]{border:.01rem solid #eee;margin-top:.2rem;padding:.15rem}.content-box .content-data .verify-item[data-v-526d694e]{display:flex;padding:.15rem 0}.content-box .content-data .verify-item .container-image[data-v-526d694e]{width:1rem;height:1rem}.content-box .content-data .verify-item .container-image uni-image[data-v-526d694e]{width:100%;height:100%}.content-box .content-data .verify-item .container-box[data-v-526d694e]{display:flex;flex-direction:column;justify-content:space-between;margin-left:.15rem;width:0;flex:1}.content-box .content-data .verify-item .container-box .content-name[data-v-526d694e]{font-size:.15rem;margin-top:.05rem}.content-box .content-data .verify-item .container-box .content-desc[data-v-526d694e]{display:flex;margin-top:.15rem;color:#999;font-size:.13rem}.content-box .content-data .verify-item .container-box .content-desc .time[data-v-526d694e]{margin-left:.5rem}.content-box .content-data .verify-action[data-v-526d694e]{display:flex;justify-content:flex-end;border-top:.01rem solid #eee;padding-top:.15rem}.content-box .content-data .verify-action uni-button[data-v-526d694e]{width:1rem;height:.36rem;margin:0 0 0 .15rem}.content-box .content-data .content-bottom[data-v-526d694e]{padding:.15rem 0;border-top:.01rem solid #eee}.content-box .content-data .content-bottom .bottom-item[data-v-526d694e]{color:#999;display:flex;margin-top:.15rem;width:5rem;justify-content:space-between}.content-box .content-data .content-bottom .bottom-item uni-view[data-v-526d694e]{margin-right:.5rem}',""]),e.exports=t}}]);