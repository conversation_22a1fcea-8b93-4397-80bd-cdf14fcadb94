(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-loading"],{"0228":function(a,t,n){var r=n("df93");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[a.i,r,""]]),r.locals&&(a.exports=r.locals);var e=n("967d").default;e("3e2d4790",r,!0,{sourceMap:!1,shadowMode:!1})},"2d05":function(a,t,n){"use strict";n.r(t);var r=n("9ab4"),e=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(a){n.d(t,a,(function(){return r[a]}))}(o);t["default"]=e.a},3406:function(a,t,n){a.exports=n.p+"static/cashier/start_logo.png"},"94d0":function(a,t,n){"use strict";n.d(t,"b",(function(){return e})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return r}));var r={pageMeta:n("7854").default},e=function(){var a=this.$createElement,t=this._self._c||a;return t("v-uni-view",[t("page-meta",{attrs:{"root-font-size":this.rootSize}}),t("v-uni-view",{staticClass:"container",style:this.themeColor},[t("v-uni-image",{attrs:{src:n("3406"),mode:"heightFix"}})],1)],1)},o=[]},"9ab4":function(a,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{}},onShow:function(){this.loadThemeColor()}}},ddf6:function(a,t,n){"use strict";n.r(t);var r=n("94d0"),e=n("2d05");for(var o in e)["default"].indexOf(o)<0&&function(a){n.d(t,a,(function(){return e[a]}))}(o);n("f688");var i=n("828b"),d=Object(i["a"])(e["default"],r["b"],r["c"],!1,null,"a1a4a8a0",null,!1,r["a"],void 0);t["default"]=d.exports},df93:function(a,t,n){var r=n("c86c");t=r(!1),t.push([a.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-a1a4a8a0]{display:none}\r\n/* 收银台相关 */uni-text[data-v-a1a4a8a0],\r\nuni-view[data-v-a1a4a8a0]{font-size:.14rem}body[data-v-a1a4a8a0]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-a1a4a8a0]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-a1a4a8a0]::-webkit-scrollbar-button{display:none}body[data-v-a1a4a8a0]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-a1a4a8a0]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-a1a4a8a0]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-a1a4a8a0]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-a1a4a8a0]{color:var(--primary-color)!important}.container[data-v-a1a4a8a0]{width:100vw;height:100vh;background:#fff;display:flex;flex-direction:column;align-items:center;justify-content:center}.container uni-image[data-v-a1a4a8a0]{height:80%}',""]),a.exports=t},f688:function(a,t,n){"use strict";var r=n("0228"),e=n.n(r);e.a}}]);