(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-scale-add"],{"001e":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("fd3c"),a("dc8a"),a("4626"),a("5ac7"),a("bf0f"),a("2797"),a("aa9c"),a("d4b5");var n=i(a("2634")),r=i(a("2fdc")),o=i(a("9056")),s=a("8b8b"),l={components:{uniDataSelect:o.default},data:function(){return{brandList:[],modelList:[],formData:{type:"barcode",name:"",brand:"",model:"",config:{ip:"",port:"",serialport:"",baudrate:""},network_type:"tcp"},flag:!1,scaleId:0,scaleBrand:{}}},onLoad:function(t){var e=this;return(0,r.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.getScaleBrandFn();case 2:t.scale_id&&(e.scaleId=t.scale_id,e.getDetailFn());case 3:case"end":return a.stop()}}),a)})))()},onShow:function(){},watch:{"formData.brand":{handler:function(t){if(t){var e=this.scaleBrand[t].model_list;this.modelList=Object.keys(e).map((function(t){return{value:t,text:e[t].model_name}})),this.formData.model&&!Object.keys(e).includes(this.formData.model)&&(this.formData.model="")}else this.formData.model="",this.modelList=[]},immediate:!0}},methods:{getScaleBrandFn:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var a,i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.addon.includes("scale")){e.next=3;break}return t.$util.showToast({title:"未安装电子秤插件"}),e.abrupt("return");case 3:return e.next=5,(0,s.getScaleBrand)();case 5:a=e.sent,0==a.code&&(t.scaleBrand=a.data,i=[],Object.keys(t.scaleBrand).forEach((function(e){i.push({text:t.scaleBrand[e].brand_name,value:e})})),t.brandList=i);case 7:case"end":return e.stop()}}),e)})))()},bradnChange:function(t){this.formData.model=t.detail.value},selectBrand:function(t){this.formData.brand=t},saveFn:function(){var t=this;if(this.addon.includes("scale")){if(this.check()){if(this.flag)return!1;this.flag=!0;var e=this.$util.deepClone(this.formData);e.config=JSON.stringify(this.formData.config),e.scale_id=this.scaleId;var a="";a=this.scaleId?(0,s.editScale)(e):(0,s.addScale)(e),a.then((function(e){t.flag=!1,t.$util.showToast({title:e.message}),e.code>=0&&setTimeout((function(){t.$util.redirectTo("/pages/scale/list")}),1500)}))}}else this.$util.showToast({title:"未安装电子秤插件"})},back:function(){this.$util.redirectTo("/pages/scale/list")},check:function(){if(!this.formData.name)return this.$util.showToast({title:"请输入电子秤名称"}),!1;if(!this.formData.model)return this.$util.showToast({title:"请选择电子秤型号"}),!1;if("tcp"==this.formData.network_type){if(!this.formData.config.ip)return this.$util.showToast({title:"请输入设备IP地址"}),!1;if(!this.formData.config.port)return this.$util.showToast({title:"请输入设备端口号"}),!1}if("serialport"==this.formData.network_type){if(!this.formData.config.serialport)return this.$util.showToast({title:"请输入串口名称"}),!1;if(!this.formData.config.baudrate)return this.$util.showToast({title:"请输入串口波特率"}),!1}return!0},getDetailFn:function(){var t=this;this.addon.includes("scale")?(0,s.getScaleDetail)({scale_id:this.scaleId}).then((function(e){e.code>=0&&e.data&&(t.formData=e.data)})):this.$util.showToast({title:"未安装电子秤插件"})},networkTypeChange:function(t){this.formData.network_type=t.detail.value}}};e.default=l},"0d8b":function(t,e,a){"use strict";a.r(e);var i=a("ee2f"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"19ee":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={uniDataSelect:a("9056").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"common-wrap common-form body-overhide"},[a("v-uni-view",{staticClass:"common-title"},[t._v("电子秤设置")]),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("电子秤名称")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),a("v-uni-text",{staticClass:"form-word-aux"})],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("秤类型")],1),a("v-uni-view",{staticClass:"form-input-inline border-none"},[a("v-uni-view",{staticClass:"scale-type"},[a("v-uni-view",{class:{active:"barcode"==t.formData.type},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.formData.type="barcode"}}},[t._v("条码秤")]),a("v-uni-view",{class:{active:"cashier"==t.formData.type},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.formData.type="cashier"}}},[t._v("收银秤")])],1)],1)],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("电子秤品牌")],1),a("v-uni-view",{staticClass:"form-input-inline border-none"},[a("uni-data-select",{attrs:{localdata:t.brandList,label:""},model:{value:t.formData.brand,callback:function(e){t.$set(t.formData,"brand",e)},expression:"formData.brand"}})],1)],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("电子秤型号")],1),a("v-uni-view",{staticClass:"form-input-inline border-none"},[a("uni-data-select",{attrs:{localdata:t.modelList},model:{value:t.formData.model,callback:function(e){t.$set(t.formData,"model",e)},expression:"formData.model"}})],1)],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("通讯方式")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.networkTypeChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"tcp",checked:"tcp"==t.formData.network_type}}),t._v("TCP")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"serialport",checked:"serialport"==t.formData.network_type}}),t._v("串口")],1)],1)],1)],1),"dahua"==t.formData.brand&&"TM"==t.formData.model?a("v-uni-view",{staticClass:"scale-tips"},[a("v-uni-view",[t._v("使用大华电子秤前需先配置一下电子秤“条码格式”，配置方式为：")]),a("v-uni-view",[t._v("使用大华电子秤厂官方提供的大华电子秤上位机软件TMA4.0，连接到设备后，打开基础设置 -> 系统参数，设置条码格式为 “FFWWWWWNNNNNC” 或\n\t\t\t\t“FFWWWWWNNNNNEEEEEC”，设置好之后点击下载，下载成功之后即配置完成")])],1):t._e(),"aclas"==t.formData.brand&&"LS"==t.formData.model?a("v-uni-view",{staticClass:"scale-tips"},[a("v-uni-view",[t._v("使用顶尖电子秤需将电子秤默认条码类型配置为“7” 或者 “87”")])],1):t._e(),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"tcp"==t.formData.network_type,expression:"formData.network_type == 'tcp'"}]},[a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("设备ip地址")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:t.formData.config.ip,callback:function(e){t.$set(t.formData.config,"ip",e)},expression:"formData.config.ip"}})],1),a("v-uni-text",{staticClass:"form-word-aux"})],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("设备端口号")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:t.formData.config.port,callback:function(e){t.$set(t.formData.config,"port",e)},expression:"formData.config.port"}})],1),a("v-uni-text",{staticClass:"form-word-aux"})],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"serialport"==t.formData.network_type,expression:"formData.network_type == 'serialport'"}]},[a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("串口名称")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:t.formData.config.serialport,callback:function(e){t.$set(t.formData.config,"serialport",e)},expression:"formData.config.serialport"}})],1),a("v-uni-text",{staticClass:"form-word-aux"})],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("串口波特率")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:t.formData.config.baudrate,callback:function(e){t.$set(t.formData.config,"baudrate",e)},expression:"formData.config.baudrate"}})],1),a("v-uni-text",{staticClass:"form-word-aux"})],1)],1),a("v-uni-view",{staticClass:"common-btn-wrap"},[a("v-uni-button",{staticClass:"screen-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveFn.apply(void 0,arguments)}}},[t._v("保存")]),a("v-uni-button",{staticClass:"screen-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.back.apply(void 0,arguments)}}},[t._v("返回")])],1)],1)],1)},r=[]},"2e5b":function(t,e,a){"use strict";a.r(e);var i=a("001e"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"2f52":function(t,e,a){"use strict";a.r(e);var i=a("19ee"),n=a("2e5b");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("4aeb");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"32e2c969",null,!1,i["a"],void 0);e["default"]=s.exports},3973:function(t,e,a){var i=a("464e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("9e13b238",i,!0,{sourceMap:!1,shadowMode:!1})},"464e":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-32e2c969]{display:none}\r\n/* 收银台相关 */uni-text[data-v-32e2c969],\r\nuni-view[data-v-32e2c969]{font-size:.14rem}body[data-v-32e2c969]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-32e2c969]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-32e2c969]::-webkit-scrollbar-button{display:none}body[data-v-32e2c969]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-32e2c969]::-webkit-scrollbar-track{background-color:initial}.common-wrap[data-v-32e2c969]{height:100%;overflow:auto}.common-wrap[data-v-32e2c969]::-webkit-scrollbar{width:.06rem;height:.06rem}.common-wrap[data-v-32e2c969]::-webkit-scrollbar-button{display:none}.common-wrap[data-v-32e2c969]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}.common-wrap[data-v-32e2c969]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-32e2c969]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-32e2c969]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-32e2c969]{color:var(--primary-color)!important}.common-wrap[data-v-32e2c969]{padding:%?30?%;background-color:#fff}.common-title[data-v-32e2c969]{font-size:.18rem;margin-bottom:.2rem}.scale-type[data-v-32e2c969]{display:flex;align-items:center}.scale-type uni-view[data-v-32e2c969]{width:1rem;height:.35rem;line-height:.35rem;text-align:center;font-size:.14rem;border:.01rem solid #e6e6e6;border-left-width:0;transition:all .3s;cursor:pointer}.scale-type uni-view[data-v-32e2c969]:hover, .scale-type uni-view.active[data-v-32e2c969]{border-color:var(--primary-color);color:var(--primary-color);background-color:var(--primary-color-light-9);box-shadow:-.01rem 0 0 0 var(--primary-color)}.scale-type uni-view[data-v-32e2c969]:first-child{border-left-width:.01rem;box-shadow:none}.border-none[data-v-32e2c969]{border:none!important}[data-v-32e2c969] .uni-select{border-radius:0}.scale-tips[data-v-32e2c969]{display:inline-block;padding:.1rem;border-radius:.05rem;color:var(--primary-color)!important;border:.01rem solid var(--primary-color)!important;background-color:var(--primary-color-light-9)!important;margin-left:1.1rem;margin-bottom:.1rem}',""]),t.exports=e},"4aeb":function(t,e,a){"use strict";var i=a("3973"),n=a.n(i);n.a},"55c1":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={uniIcons:a("6f18").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-stat__select"},[t.label?a("span",{staticClass:"uni-label-text hide-on-phone"},[t._v(t._s(t.label+"："))]):t._e(),a("v-uni-view",{staticClass:"uni-stat-box",class:{"uni-stat__actived":t.current}},[a("v-uni-view",{staticClass:"uni-select",class:{"uni-select--disabled":t.disabled}},[a("v-uni-view",{staticClass:"uni-select__input-box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleSelector.apply(void 0,arguments)}}},[t.current?a("v-uni-view",{staticClass:"uni-select__input-text"},[t._v(t._s(t.current))]):a("v-uni-view",{staticClass:"uni-select__input-text uni-select__input-placeholder"},[t._v(t._s(t.typePlaceholder))]),t.current&&t.clear?a("uni-icons",{attrs:{type:"clear",color:"#c0c4cc",size:"24"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clearVal.apply(void 0,arguments)}}}):a("uni-icons",{attrs:{type:t.showSelector?"top":"bottom",size:"14",color:"#999"}})],1),t.showSelector?a("v-uni-view",{staticClass:"uni-select--mask",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleSelector.apply(void 0,arguments)}}}):t._e(),t.showSelector?a("v-uni-view",{staticClass:"uni-select__selector"},[a("v-uni-view",{staticClass:"uni-popper__arrow"}),a("v-uni-scroll-view",{staticClass:"uni-select__selector-scroll",attrs:{"scroll-y":"true"}},[0===t.mixinDatacomResData.length?a("v-uni-view",{staticClass:"uni-select__selector-empty"},[a("v-uni-text",[t._v(t._s(t.emptyTips))])],1):t._l(t.mixinDatacomResData,(function(e,i){return a("v-uni-view",{key:i,staticClass:"uni-select__selector-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.change(e)}}},[a("v-uni-text",{class:{"uni-select__selector__disabled":e.disable}},[t._v(t._s(t.formatItemName(e)))])],1)}))],2)],1):t._e()],1)],1)],1)},r=[]},"6f40":function(t,e,a){"use strict";var i=a("fb12"),n=a.n(i);n.a},"8b8b":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.addScale=function(t){return n.default.post("/scale/storeapi/scale/add",{data:t})},e.deleteScale=function(t){return n.default.post("/scale/storeapi/scale/delete",{data:t})},e.editScale=function(t){return n.default.post("/scale/storeapi/scale/edit",{data:t})},e.getScaleBrand=function(){return n.default.post("/scale/storeapi/scale/scaleBrand")},e.getScaleDetail=function(t){return n.default.post("/scale/storeapi/scale/detail",{data:t})},e.getScaleList=function(t){return n.default.post("/scale/storeapi/scale/page",{data:t})};var n=i(a("a3b5"))},9056:function(t,e,a){"use strict";a.r(e);var i=a("55c1"),n=a("0d8b");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("6f40");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"191ab648",null,!1,i["a"],void 0);e["default"]=s.exports},ee2f:function(t,e,a){"use strict";(function(t){a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("aa77"),a("bf0f"),a("2797"),a("5ef2"),a("c223");var i={name:"uni-stat-select",mixins:[t.mixinDatacom||{}],data:function(){return{showSelector:!1,current:"",mixinDatacomResData:[],apps:[],channels:[]}},props:{localdata:{type:Array,default:function(){return[]}},value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"请选择"},emptyTips:{type:String,default:"无选项"},clear:{type:Boolean,default:!0},defItem:{type:Number,default:0},disabled:{type:Boolean,default:!1}},created:function(){this.last="".concat(this.collection,"_last_selected_option_value"),this.collection&&!this.localdata.length&&this.mixinDatacomEasyGet()},computed:{typePlaceholder:function(){var t=this.placeholder,e={"opendb-stat-app-versions":"版本","opendb-app-channels":"渠道","opendb-app-list":"应用"}[this.collection];return e?t+e:t}},watch:{localdata:{immediate:!0,handler:function(t,e){Array.isArray(t)&&e!==t&&(this.mixinDatacomResData=t)}},value:function(){this.initDefVal()},mixinDatacomResData:{immediate:!0,handler:function(t){t.length&&this.initDefVal()}}},methods:{initDefVal:function(){var t="";if(!this.value&&0!==this.value||this.isDisabled(this.value))if(!this.modelValue&&0!==this.modelValue||this.isDisabled(this.modelValue)){var e;if(this.collection&&(e=uni.getStorageSync(this.last)),e||0===e)t=e;else{var a="";this.defItem>0&&this.defItem<this.mixinDatacomResData.length&&(a=this.mixinDatacomResData[this.defItem-1].value),t=a}this.emit(t)}else t=this.modelValue;else t=this.value;var i=this.mixinDatacomResData.find((function(e){return e.value===t}));this.current=i?this.formatItemName(i):""},isDisabled:function(t){var e=!1;return this.mixinDatacomResData.forEach((function(a){a.value===t&&(e=a.disable)})),e},clearVal:function(){this.emit(""),this.collection&&uni.removeStorageSync(this.last)},change:function(t){t.disable||(this.showSelector=!1,this.current=this.formatItemName(t),this.emit(t.value))},emit:function(t){this.$emit("change",t),this.$emit("input",t),this.$emit("update:modelValue",t),this.collection&&uni.setStorageSync(this.last,t)},toggleSelector:function(){this.disabled||(this.showSelector=!this.showSelector)},formatItemName:function(t){var e=t.text,a=t.value,i=t.channel_code;return i=i?"(".concat(i,")"):"",this.collection.indexOf("app-list")>0?"".concat(e,"(").concat(a,")"):e||"未命名".concat(i)}}};e.default=i}).call(this,a("861b")["uniCloud"])},eed8:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-191ab648]{display:none}\r\n/* 收银台相关 */uni-text[data-v-191ab648],\r\nuni-view[data-v-191ab648]{font-size:.14rem}body[data-v-191ab648]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-191ab648]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-191ab648]::-webkit-scrollbar-button{display:none}body[data-v-191ab648]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-191ab648]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-191ab648]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-191ab648]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-191ab648]{color:var(--primary-color)!important}@media screen and (max-width:500px){.hide-on-phone[data-v-191ab648]{display:none}}.uni-stat__select[data-v-191ab648]{display:flex;align-items:center;cursor:pointer;width:100%;flex:1;box-sizing:border-box}.uni-stat-box[data-v-191ab648]{width:100%;flex:1}.uni-stat__actived[data-v-191ab648]{width:100%;flex:1}.uni-label-text[data-v-191ab648]{font-size:14px;font-weight:700;color:#6a6a6a;margin:auto 0;margin-right:5px}.uni-select[data-v-191ab648]{font-size:14px;border:1px solid #e5e5e5;box-sizing:border-box;border-radius:4px;padding:0 5px;padding-left:10px;position:relative;display:flex;-webkit-user-select:none;user-select:none;flex-direction:row;align-items:center;border-bottom:solid 1px #e5e5e5;width:100%;flex:1;height:35px}.uni-select--disabled[data-v-191ab648]{background-color:#f5f7fa;cursor:not-allowed}.uni-select__label[data-v-191ab648]{font-size:16px;height:35px;padding-right:10px;color:#909399}.uni-select__input-box[data-v-191ab648]{position:relative;display:flex;flex:1;flex-direction:row;align-items:center}.uni-select__input[data-v-191ab648]{flex:1;font-size:14px;height:22px;line-height:22px}.uni-select__input-plac[data-v-191ab648]{font-size:14px;color:#909399}.uni-select__selector[data-v-191ab648]{box-sizing:border-box;position:absolute;top:calc(100% + 12px);left:0;width:100%;background-color:#fff;border:1px solid #ebeef5;border-radius:6px;box-shadow:0 2px 12px 0 rgba(0,0,0,.1);z-index:2;padding:4px 0}.uni-select__selector-scroll[data-v-191ab648]{max-height:200px;box-sizing:border-box}.uni-select__selector-empty[data-v-191ab648],\r\n.uni-select__selector-item[data-v-191ab648]{display:flex;cursor:pointer;line-height:35px;font-size:14px;text-align:center;\r\n  /* border-bottom: solid 1px $uni-border-3; */padding:0 10px}.uni-select__selector-item[data-v-191ab648]:hover{background-color:#f9f9f9}.uni-select__selector-empty[data-v-191ab648]:last-child,\r\n.uni-select__selector-item[data-v-191ab648]:last-child{border-bottom:none}.uni-select__selector__disabled[data-v-191ab648]{opacity:.4;cursor:default}\r\n/* picker 弹出层通用的指示小三角 */.uni-popper__arrow[data-v-191ab648],\r\n.uni-popper__arrow[data-v-191ab648]::after{position:absolute;display:block;width:0;height:0;border-color:transparent;border-style:solid;border-width:6px}.uni-popper__arrow[data-v-191ab648]{-webkit-filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));top:-6px;left:10%;margin-right:3px;border-top-width:0;border-bottom-color:#ebeef5}.uni-popper__arrow[data-v-191ab648]::after{content:" ";top:1px;margin-left:-6px;border-top-width:0;border-bottom-color:#fff}.uni-select__input-text[data-v-191ab648]{width:100%;color:#333;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;overflow:hidden}.uni-select__input-placeholder[data-v-191ab648]{color:#6a6a6a;font-size:12px}.uni-select--mask[data-v-191ab648]{position:fixed;top:0;bottom:0;right:0;left:0}',""]),t.exports=e},fb12:function(t,e,a){var i=a("eed8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("15e6434f",i,!0,{sourceMap:!1,shadowMode:!1})}}]);