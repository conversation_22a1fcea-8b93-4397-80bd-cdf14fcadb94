(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-printer-add"],{"0d8b":function(e,t,a){"use strict";a.r(t);var i=a("ee2f"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},"32a9":function(e,t,a){"use strict";var i=a("a62a"),n=a.n(i);n.a},"55c1":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i}));var i={uniIcons:a("6f18").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"uni-stat__select"},[e.label?a("span",{staticClass:"uni-label-text hide-on-phone"},[e._v(e._s(e.label+"："))]):e._e(),a("v-uni-view",{staticClass:"uni-stat-box",class:{"uni-stat__actived":e.current}},[a("v-uni-view",{staticClass:"uni-select",class:{"uni-select--disabled":e.disabled}},[a("v-uni-view",{staticClass:"uni-select__input-box",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSelector.apply(void 0,arguments)}}},[e.current?a("v-uni-view",{staticClass:"uni-select__input-text"},[e._v(e._s(e.current))]):a("v-uni-view",{staticClass:"uni-select__input-text uni-select__input-placeholder"},[e._v(e._s(e.typePlaceholder))]),e.current&&e.clear?a("uni-icons",{attrs:{type:"clear",color:"#c0c4cc",size:"24"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clearVal.apply(void 0,arguments)}}}):a("uni-icons",{attrs:{type:e.showSelector?"top":"bottom",size:"14",color:"#999"}})],1),e.showSelector?a("v-uni-view",{staticClass:"uni-select--mask",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSelector.apply(void 0,arguments)}}}):e._e(),e.showSelector?a("v-uni-view",{staticClass:"uni-select__selector"},[a("v-uni-view",{staticClass:"uni-popper__arrow"}),a("v-uni-scroll-view",{staticClass:"uni-select__selector-scroll",attrs:{"scroll-y":"true"}},[0===e.mixinDatacomResData.length?a("v-uni-view",{staticClass:"uni-select__selector-empty"},[a("v-uni-text",[e._v(e._s(e.emptyTips))])],1):e._l(e.mixinDatacomResData,(function(t,i){return a("v-uni-view",{key:i,staticClass:"uni-select__selector-item",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.change(t)}}},[a("v-uni-text",{class:{"uni-select__selector__disabled":t.disable}},[e._v(e._s(e.formatItemName(t)))])],1)}))],2)],1):e._e()],1)],1)],1)},r=[]},"6f40":function(e,t,a){"use strict";var i=a("fb12"),n=a.n(i);n.a},"8e12":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-56419d78]{display:none}\r\n/* 收银台相关 */uni-text[data-v-56419d78],\r\nuni-view[data-v-56419d78]{font-size:.14rem}body[data-v-56419d78]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-56419d78]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-56419d78]::-webkit-scrollbar-button{display:none}body[data-v-56419d78]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-56419d78]::-webkit-scrollbar-track{background-color:initial}.printer .common-wrap[data-v-56419d78]{height:100%;overflow:auto}.printer .common-wrap[data-v-56419d78]::-webkit-scrollbar{width:.06rem;height:.06rem}.printer .common-wrap[data-v-56419d78]::-webkit-scrollbar-button{display:none}.printer .common-wrap[data-v-56419d78]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}.printer .common-wrap[data-v-56419d78]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-56419d78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-56419d78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-56419d78]{color:var(--primary-color)!important}.common-title[data-v-56419d78]{font-size:.18rem;margin-bottom:.2rem}[data-v-56419d78] .uni-select{border-width:0;border-radius:0}.printer[data-v-56419d78]{position:relative;height:100%}.printer .common-wrap[data-v-56419d78]{background-color:#fff;padding:.2rem .2rem .88rem .2rem}.printer .common-btn-wrap[data-v-56419d78]{width:100%;position:absolute;left:0;bottom:0;padding:.24rem .2rem;display:flex;justify-content:space-between;margin:0;box-sizing:border-box;background-color:#fff}.printer .common-btn-wrap uni-button[data-v-56419d78]{line-height:.4rem;height:.4rem;margin:0;flex:1}.printer .common-btn-wrap uni-button.primary-btn[data-v-56419d78]{margin-right:.1rem}',""]),e.exports=t},9056:function(e,t,a){"use strict";a.r(t);var i=a("55c1"),n=a("0d8b");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("6f40");var s=a("828b"),o=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"191ab648",null,!1,i["a"],void 0);t["default"]=o.exports},a62a:function(e,t,a){var i=a("8e12");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("5dfd63b9",i,!0,{sourceMap:!1,shadowMode:!1})},b860:function(e,t,a){"use strict";a.r(t);var i=a("c2b4"),n=a("f1f2");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("32a9");var s=a("828b"),o=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"56419d78",null,!1,i["a"],void 0);t["default"]=o.exports},bf41:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.addPrinter=function(e){return n.default.post("/printer/storeapi/printer/add",{data:e})},t.deletePrinter=function(e){return n.default.post("/printer/storeapi/printer/deleteprinter",{data:{printer_id:e}})},t.editPrinter=function(e){return n.default.post("/printer/storeapi/printer/edit",{data:e})},t.getOrderType=function(){return n.default.post("/printer/storeapi/printer/getordertype")},t.getPrinterInfo=function(e){return n.default.post("/printer/storeapi/printer/info",{data:{printer_id:e}})},t.getPrinterList=function(e){return n.default.post("/printer/storeapi/printer/lists",{data:e})},t.getTemplate=function(){return n.default.post("/printer/storeapi/printer/template")},t.printTicket=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=r.default.getLocalConfig();return e.printer_ids="all"==t.printerSelectType?"all":t.printerSelectIds.toString(),n.default.post("/cashier/storeapi/cashier/printticket",{data:e})},a("c9b5"),a("bf0f"),a("ab80");var n=i(a("a3b5")),r=i(a("3885"))},c2b4:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i}));var i={uniDataSelect:a("9056").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("base-page",[a("v-uni-view",{staticClass:"printer "},[a("v-uni-view",{staticClass:"common-wrap common-form body-overhide"},[a("v-uni-view",{staticClass:"common-title"},[e._v("打印机设置")]),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("打印机名称")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:e.savedata.printer_name,callback:function(t){e.$set(e.savedata,"printer_name",t)},expression:"savedata.printer_name"}})],1),a("v-uni-text",{staticClass:"form-word-aux"})],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("打印机类型")],1),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.printerTypeChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"cloud",checked:"cloud"==e.savedata.printer_type}}),e._v("云打印机")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"local",checked:"local"==e.savedata.printer_type}}),e._v("本地打印机")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"network",checked:"network"==e.savedata.printer_type}}),e._v("网络打印机")],1)],1)],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"cloud"==e.savedata.printer_type,expression:"savedata.printer_type == 'cloud'"}]},[a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("打印机品牌")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-view",{staticClass:"form-input"},[e._v("易联云")])],1),a("v-uni-text",{staticClass:"form-word-aux"})],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("打印机编号")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:e.savedata.printer_code,callback:function(t){e.$set(e.savedata,"printer_code",t)},expression:"savedata.printer_code"}})],1),a("v-uni-text",{staticClass:"form-word-aux"})],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("打印机密钥")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:e.savedata.printer_key,callback:function(t){e.$set(e.savedata,"printer_key",t)},expression:"savedata.printer_key"}})],1),a("v-uni-text",{staticClass:"form-word-aux"})],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("应用id")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:e.savedata.open_id,callback:function(t){e.$set(e.savedata,"open_id",t)},expression:"savedata.open_id"}})],1),a("v-uni-text",{staticClass:"form-word-aux-line"},[e._v("应用id（易联云-开发者中心后台应用中心里获取）")])],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("apiKey")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:e.savedata.apikey,callback:function(t){e.$set(e.savedata,"apikey",t)},expression:"savedata.apikey"}})],1),a("v-uni-text",{staticClass:"form-word-aux-line"},[e._v("apiKey（易联云-开发者中心后台应用中心里获取）")])],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"local"==e.savedata.printer_type,expression:"savedata.printer_type == 'local'"}]},[a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("打印机端口")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-view",{staticClass:"form-input"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:e.savedata.host,callback:function(t){e.$set(e.savedata,"host",t)},expression:"savedata.host"}})],1)],1),a("v-uni-text",{staticClass:"form-word-aux"},[e._v("打印机端口 (可以填写打印机端口号：如LPT1 或 本地网络共享打印机：如\\\\*************\\POS_NAME)")])],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"network"==e.savedata.printer_type,expression:"savedata.printer_type == 'network'"}]},[a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("打印机IP")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-view",{staticClass:"form-input"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:e.savedata.ip,callback:function(t){e.$set(e.savedata,"ip",t)},expression:"savedata.ip"}})],1)],1),a("v-uni-text",{staticClass:"form-word-aux"})],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("打印机端口")],1),a("v-uni-view",{staticClass:"form-input-inline"},[a("v-uni-view",{staticClass:"form-input"},[a("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:e.savedata.port,callback:function(t){e.$set(e.savedata,"port",t)},expression:"savedata.port"}})],1)],1),a("v-uni-text",{staticClass:"form-word-aux"})],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"local"==e.savedata.printer_type||"network"==e.savedata.printer_type,expression:"savedata.printer_type == 'local' || savedata.printer_type == 'network'"}]},[a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[e._v("*")]),e._v("打印宽度")],1),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.printWidthChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"58mm",checked:"58mm"==e.savedata.print_width}}),e._v("58mm")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"80mm",checked:"80mm"==e.savedata.print_width}}),e._v("80mm")],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"common-title"},[e._v("支付打印")]),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("支付打印")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.orderPayChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==e.savedata.order_pay_open}}),e._v("开启")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"0",checked:0==e.savedata.order_pay_open}}),e._v("关闭")],1)],1)],1)],1),1==e.savedata.order_pay_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("打印模板")]),e.template.goodsorder&&e.template.goodsorder.length?a("v-uni-view",{staticClass:"form-input-inline "},[a("uni-data-select",{attrs:{localdata:e.template.goodsorder},model:{value:e.orderPayTempIndex,callback:function(t){e.orderPayTempIndex=t},expression:"orderPayTempIndex"}})],1):e._e()],1):e._e(),1==e.savedata.order_pay_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("打印联数")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.orderPayNumChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==e.savedata.order_pay_print_num}}),e._v("1")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"2",checked:2==e.savedata.order_pay_print_num}}),e._v("2")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"3",checked:3==e.savedata.order_pay_print_num}}),e._v("3")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"4",checked:4==e.savedata.order_pay_print_num}}),e._v("4")],1)],1)],1)],1):e._e(),1==e.savedata.order_pay_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("订单类型")]),a("v-uni-view",{staticClass:"form-block"},[a("v-uni-checkbox-group",{staticClass:"form-checkbox-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.orderPayTypeChange.apply(void 0,arguments)}}},e._l(e.orderType,(function(t,i){return a("v-uni-label",{staticClass:"form-checkbox-item"},[a("v-uni-checkbox",{attrs:{value:t.type.toString(),checked:e.savedata.order_pay_order_type.includes(t.type.toString())||e.savedata.order_pay_order_type.includes(parseInt(t.type))}}),e._v(e._s(t.name))],1)})),1)],1)],1):e._e(),a("v-uni-view",{staticClass:"common-title"},[e._v("收货打印")]),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("收货打印")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.takeDeliveryChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==e.savedata.take_delivery_open}}),e._v("开启")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"0",checked:0==e.savedata.take_delivery_open}}),e._v("关闭")],1)],1)],1)],1),1==e.savedata.take_delivery_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("打印模板")]),e.template.goodsorder&&e.template.goodsorder.length?a("v-uni-view",{staticClass:"form-input-inline"},[a("uni-data-select",{attrs:{localdata:e.template.goodsorder},model:{value:e.takeDeliveryTempIndex,callback:function(t){e.takeDeliveryTempIndex=t},expression:"takeDeliveryTempIndex"}})],1):e._e()],1):e._e(),1==e.savedata.take_delivery_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("打印联数")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.takeDeliveryNumChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==e.savedata.take_delivery_print_num}}),e._v("1")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"2",checked:2==e.savedata.take_delivery_print_num}}),e._v("2")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"3",checked:3==e.savedata.take_delivery_print_num}}),e._v("3")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"4",checked:4==e.savedata.take_delivery_print_num}}),e._v("4")],1)],1)],1)],1):e._e(),1==e.savedata.take_delivery_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("订单类型")]),a("v-uni-view",{staticClass:"form-block"},[a("v-uni-checkbox-group",{staticClass:"form-checkbox-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.takeDeliveryTypeChange.apply(void 0,arguments)}}},e._l(e.orderType,(function(t,i){return a("v-uni-label",{staticClass:"form-checkbox-item"},[a("v-uni-checkbox",{attrs:{value:t.type.toString(),checked:e.savedata.take_delivery_order_type.includes(t.type.toString())||e.savedata.take_delivery_order_type.includes(parseInt(t.type))}}),e._v(e._s(t.name))],1)})),1)],1)],1):e._e(),a("v-uni-view",{staticClass:"common-title"},[e._v("手动打印")]),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("手动打印")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.manualChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==e.savedata.manual_open}}),e._v("开启")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"0",checked:0==e.savedata.manual_open}}),e._v("关闭")],1)],1)],1)],1),1==e.savedata.manual_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("打印模板")]),e.template.goodsorder&&e.template.goodsorder.length?a("v-uni-view",{staticClass:"form-input-inline"},[a("uni-data-select",{attrs:{localdata:e.template.goodsorder},model:{value:e.manualTempIndex,callback:function(t){e.manualTempIndex=t},expression:"manualTempIndex"}})],1):e._e()],1):e._e(),1==e.savedata.manual_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("打印联数")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.manualNumChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==e.savedata.print_num}}),e._v("1")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"2",checked:2==e.savedata.print_num}}),e._v("2")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"3",checked:3==e.savedata.print_num}}),e._v("3")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"4",checked:4==e.savedata.print_num}}),e._v("4")],1)],1)],1)],1):e._e(),a("v-uni-view",{staticClass:"common-title"},[e._v("充值打印")]),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("充值打印")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.rechargeChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==e.savedata.recharge_open}}),e._v("开启")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"0",checked:0==e.savedata.recharge_open}}),e._v("关闭")],1)],1)],1)],1),1==e.savedata.recharge_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("打印模板")]),e.template.recharge&&e.template.recharge.length?a("v-uni-view",{staticClass:"form-input-inline"},[a("uni-data-select",{attrs:{localdata:e.template.recharge},model:{value:e.rechargeTempIndex,callback:function(t){e.rechargeTempIndex=t},expression:"rechargeTempIndex"}})],1):e._e()],1):e._e(),1==e.savedata.recharge_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("打印联数")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.rechargeNumChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==e.savedata.recharge_print_num}}),e._v("1")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"2",checked:2==e.savedata.recharge_print_num}}),e._v("2")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"3",checked:3==e.savedata.recharge_print_num}}),e._v("3")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"4",checked:4==e.savedata.recharge_print_num}}),e._v("4")],1)],1)],1)],1):e._e(),a("v-uni-view",{staticClass:"common-title"},[e._v("收银交班打印")]),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("收银交班打印")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.changeShiftsChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==e.savedata.change_shifts_open}}),e._v("开启")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"0",checked:0==e.savedata.change_shifts_open}}),e._v("关闭")],1)],1)],1)],1),1==e.savedata.change_shifts_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("打印模板")]),e.template.change_shifts&&e.template.change_shifts.length?a("v-uni-view",{staticClass:"form-input-inline"},[a("uni-data-select",{attrs:{localdata:e.template.change_shifts},model:{value:e.changeShiftsTempIndex,callback:function(t){e.changeShiftsTempIndex=t},expression:"changeShiftsTempIndex"}})],1):e._e()],1):e._e(),1==e.savedata.change_shifts_open?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[e._v("打印联数")]),a("v-uni-view",{staticClass:"form-inline"},[a("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.changeShiftsNumChange.apply(void 0,arguments)}}},[a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"1",checked:1==e.savedata.change_shifts_print_num}}),e._v("1")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"2",checked:2==e.savedata.change_shifts_print_num}}),e._v("2")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"3",checked:3==e.savedata.change_shifts_print_num}}),e._v("3")],1),a("v-uni-label",{staticClass:"radio form-radio-item"},[a("v-uni-radio",{attrs:{value:"4",checked:4==e.savedata.change_shifts_print_num}}),e._v("4")],1)],1)],1)],1):e._e()],1),a("v-uni-view",{staticClass:"common-btn-wrap"},[a("v-uni-button",{staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveFn.apply(void 0,arguments)}}},[e._v("保存")]),a("v-uni-button",{staticClass:"default-btn",attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.back.apply(void 0,arguments)}}},[e._v("返回")])],1)],1)],1)},r=[]},e341:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("fd3c"),a("aa9c"),a("c9b5"),a("bf0f"),a("ab80");var n=i(a("9056")),r=a("bf41"),s={components:{uniDataSelect:n.default},data:function(){return{printer_id:0,savedata:{printer_name:"",brand:"yilianyun",printer_code:"",printer_key:"",open_id:"",apikey:"",printer_type:"cloud",order_pay_open:0,order_pay_template_id:0,order_pay_print_num:1,order_pay_order_type:[],take_delivery_open:0,take_delivery_template_id:0,take_delivery_print_num:1,take_delivery_order_type:[],manual_open:0,template_id:0,print_num:1,recharge_open:0,recharge_template_id:0,recharge_print_num:1,change_shifts_open:0,change_shifts_template_id:0,change_shifts_print_num:1,host:"",ip:"",port:"",print_width:"58mm"},time:{start:"08:30",end:"23:30"},interval:30,advance:"",max:"",week:[],flag:!1,template:{},orderPayTempIndex:0,takeDeliveryTempIndex:0,manualTempIndex:0,rechargeTempIndex:0,changeShiftsTempIndex:0,orderType:[]}},onLoad:function(e){e.printer_id&&(this.printer_id=e.printer_id)},onShow:function(){uni.setLocale("zh-Hans"),this.getTemplate(),this.getOrderTypeFn()},methods:{getData:function(){var e=this;(0,r.getPrinterInfo)(this.printer_id).then((function(t){t.code>=0?(e.savedata=t.data,e.orderPayTempIndex=e.savedata.order_pay_template_id,e.takeDeliveryTempIndex=e.savedata.take_delivery_template_id,e.manualTempIndex=e.savedata.template_id,e.rechargeTempIndex=e.savedata.recharge_template_id,e.changeShiftsTempIndex=e.savedata.change_shifts_template_id):e.$util.showToast({title:t.message})}))},back:function(){this.$util.redirectTo("/pages/printer/list")},getTemplate:function(){var e=this;(0,r.getTemplate)().then((function(t){if(0==t.code){var a={};t.data.map((function(e,t){a[e.type]||(a[e.type]=[]);var i={};i.text=e.template_name,i.value=e.template_id,a[e.type].push(i)})),e.template=a,e.printer_id&&e.getData()}}))},getOrderTypeFn:function(){var e=this;(0,r.getOrderType)().then((function(t){0==t.code&&(e.orderType=t.data)}))},printerTypeChange:function(e){this.savedata.printer_type=e.detail.value},printWidthChange:function(e){this.savedata.print_width=e.detail.value},orderPayChange:function(e){this.savedata.order_pay_open=e.detail.value},orderPayNumChange:function(e){this.savedata.order_pay_print_num=e.detail.value},orderPayTypeChange:function(e){this.savedata.order_pay_order_type=e.detail.value},takeDeliveryChange:function(e){this.savedata.take_delivery_open=e.detail.value},takeDeliveryNumChange:function(e){this.savedata.take_delivery_print_num=e.detail.value},takeDeliveryTypeChange:function(e){this.savedata.take_delivery_order_type=e.detail.value},manualChange:function(e){this.savedata.manual_open=e.detail.value},manualNumChange:function(e){this.savedata.print_num=e.detail.value},rechargeChange:function(e){this.savedata.recharge_open=e.detail.value},rechargeNumChange:function(e){this.savedata.recharge_print_num=e.detail.value},changeShiftsChange:function(e){this.savedata.change_shifts_open=e.detail.value},changeShiftsNumChange:function(e){this.savedata.change_shifts_print_num=e.detail.value},check:function(){var e=Object.assign({},this.savedata);if(!e.printer_name)return this.$util.showToast({title:"请输入打印机名称"}),!1;if("cloud"==e.printer_type){if(!e.printer_code)return this.$util.showToast({title:"请输入打印机编号"}),!1;if(!e.printer_key)return this.$util.showToast({title:"请输入打印机密钥"}),!1;if(!e.open_id)return this.$util.showToast({title:"请输入应用id"}),!1;if(!e.apikey)return this.$util.showToast({title:"请输入apikey"}),!1}if("local"==e.printer_type&&!e.host)return this.$util.showToast({title:"请输入打印机打印机端口"}),!1;if("network"==e.printer_type){if(!e.ip)return this.$util.showToast({title:"请输入打印机打印机地址"}),!1;if(!e.port)return this.$util.showToast({title:"请输入打印机打印机端口"}),!1}return!0},saveFn:function(){var e=this;if(this.check()){var t=this.savedata;t.take_delivery_order_type=t.take_delivery_order_type.toString(),t.order_pay_order_type=t.order_pay_order_type.toString(),t.order_pay_template_id=this.orderPayTempIndex,t.take_delivery_template_id=this.takeDeliveryTempIndex,t.template_id=this.manualTempIndex,t.recharge_template_id=this.rechargeTempIndex,t.change_shifts_template_id=this.changeShiftsTempIndex;var a="";if(this.printer_id>0?(t.printer_id=this.printer_id,a=(0,r.editPrinter)(t)):a=(0,r.addPrinter)(t),this.flag)return!1;this.flag=!0,a.then((function(t){e.flag=!1,e.$util.showToast({title:t.message}),t.code>=0&&setTimeout((function(){e.$util.redirectTo("/pages/printer/list")}),1500)}))}},timeTurnTimeStamp:function(e){var t=e.split(":");return 3600*t[0]+60*t[1]},timeFormat:function(e){var t=e/3600,a=e%3600/60;return t=t<10?"0"+t:t,a=a<10?"0"+a:a,t+":"+a}}};t.default=s},ee2f:function(e,t,a){"use strict";(function(e){a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa"),a("aa77"),a("bf0f"),a("2797"),a("5ef2"),a("c223");var i={name:"uni-stat-select",mixins:[e.mixinDatacom||{}],data:function(){return{showSelector:!1,current:"",mixinDatacomResData:[],apps:[],channels:[]}},props:{localdata:{type:Array,default:function(){return[]}},value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"请选择"},emptyTips:{type:String,default:"无选项"},clear:{type:Boolean,default:!0},defItem:{type:Number,default:0},disabled:{type:Boolean,default:!1}},created:function(){this.last="".concat(this.collection,"_last_selected_option_value"),this.collection&&!this.localdata.length&&this.mixinDatacomEasyGet()},computed:{typePlaceholder:function(){var e=this.placeholder,t={"opendb-stat-app-versions":"版本","opendb-app-channels":"渠道","opendb-app-list":"应用"}[this.collection];return t?e+t:e}},watch:{localdata:{immediate:!0,handler:function(e,t){Array.isArray(e)&&t!==e&&(this.mixinDatacomResData=e)}},value:function(){this.initDefVal()},mixinDatacomResData:{immediate:!0,handler:function(e){e.length&&this.initDefVal()}}},methods:{initDefVal:function(){var e="";if(!this.value&&0!==this.value||this.isDisabled(this.value))if(!this.modelValue&&0!==this.modelValue||this.isDisabled(this.modelValue)){var t;if(this.collection&&(t=uni.getStorageSync(this.last)),t||0===t)e=t;else{var a="";this.defItem>0&&this.defItem<this.mixinDatacomResData.length&&(a=this.mixinDatacomResData[this.defItem-1].value),e=a}this.emit(e)}else e=this.modelValue;else e=this.value;var i=this.mixinDatacomResData.find((function(t){return t.value===e}));this.current=i?this.formatItemName(i):""},isDisabled:function(e){var t=!1;return this.mixinDatacomResData.forEach((function(a){a.value===e&&(t=a.disable)})),t},clearVal:function(){this.emit(""),this.collection&&uni.removeStorageSync(this.last)},change:function(e){e.disable||(this.showSelector=!1,this.current=this.formatItemName(e),this.emit(e.value))},emit:function(e){this.$emit("change",e),this.$emit("input",e),this.$emit("update:modelValue",e),this.collection&&uni.setStorageSync(this.last,e)},toggleSelector:function(){this.disabled||(this.showSelector=!this.showSelector)},formatItemName:function(e){var t=e.text,a=e.value,i=e.channel_code;return i=i?"(".concat(i,")"):"",this.collection.indexOf("app-list")>0?"".concat(t,"(").concat(a,")"):t||"未命名".concat(i)}}};t.default=i}).call(this,a("861b")["uniCloud"])},eed8:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-191ab648]{display:none}\r\n/* 收银台相关 */uni-text[data-v-191ab648],\r\nuni-view[data-v-191ab648]{font-size:.14rem}body[data-v-191ab648]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-191ab648]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-191ab648]::-webkit-scrollbar-button{display:none}body[data-v-191ab648]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-191ab648]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-191ab648]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-191ab648]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-191ab648]{color:var(--primary-color)!important}@media screen and (max-width:500px){.hide-on-phone[data-v-191ab648]{display:none}}.uni-stat__select[data-v-191ab648]{display:flex;align-items:center;cursor:pointer;width:100%;flex:1;box-sizing:border-box}.uni-stat-box[data-v-191ab648]{width:100%;flex:1}.uni-stat__actived[data-v-191ab648]{width:100%;flex:1}.uni-label-text[data-v-191ab648]{font-size:14px;font-weight:700;color:#6a6a6a;margin:auto 0;margin-right:5px}.uni-select[data-v-191ab648]{font-size:14px;border:1px solid #e5e5e5;box-sizing:border-box;border-radius:4px;padding:0 5px;padding-left:10px;position:relative;display:flex;-webkit-user-select:none;user-select:none;flex-direction:row;align-items:center;border-bottom:solid 1px #e5e5e5;width:100%;flex:1;height:35px}.uni-select--disabled[data-v-191ab648]{background-color:#f5f7fa;cursor:not-allowed}.uni-select__label[data-v-191ab648]{font-size:16px;height:35px;padding-right:10px;color:#909399}.uni-select__input-box[data-v-191ab648]{position:relative;display:flex;flex:1;flex-direction:row;align-items:center}.uni-select__input[data-v-191ab648]{flex:1;font-size:14px;height:22px;line-height:22px}.uni-select__input-plac[data-v-191ab648]{font-size:14px;color:#909399}.uni-select__selector[data-v-191ab648]{box-sizing:border-box;position:absolute;top:calc(100% + 12px);left:0;width:100%;background-color:#fff;border:1px solid #ebeef5;border-radius:6px;box-shadow:0 2px 12px 0 rgba(0,0,0,.1);z-index:2;padding:4px 0}.uni-select__selector-scroll[data-v-191ab648]{max-height:200px;box-sizing:border-box}.uni-select__selector-empty[data-v-191ab648],\r\n.uni-select__selector-item[data-v-191ab648]{display:flex;cursor:pointer;line-height:35px;font-size:14px;text-align:center;\r\n  /* border-bottom: solid 1px $uni-border-3; */padding:0 10px}.uni-select__selector-item[data-v-191ab648]:hover{background-color:#f9f9f9}.uni-select__selector-empty[data-v-191ab648]:last-child,\r\n.uni-select__selector-item[data-v-191ab648]:last-child{border-bottom:none}.uni-select__selector__disabled[data-v-191ab648]{opacity:.4;cursor:default}\r\n/* picker 弹出层通用的指示小三角 */.uni-popper__arrow[data-v-191ab648],\r\n.uni-popper__arrow[data-v-191ab648]::after{position:absolute;display:block;width:0;height:0;border-color:transparent;border-style:solid;border-width:6px}.uni-popper__arrow[data-v-191ab648]{-webkit-filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));top:-6px;left:10%;margin-right:3px;border-top-width:0;border-bottom-color:#ebeef5}.uni-popper__arrow[data-v-191ab648]::after{content:" ";top:1px;margin-left:-6px;border-top-width:0;border-bottom-color:#fff}.uni-select__input-text[data-v-191ab648]{width:100%;color:#333;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;overflow:hidden}.uni-select__input-placeholder[data-v-191ab648]{color:#6a6a6a;font-size:12px}.uni-select--mask[data-v-191ab648]{position:fixed;top:0;bottom:0;right:0;left:0}',""]),e.exports=t},f1f2:function(e,t,a){"use strict";a.r(t);var i=a("e341"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},fb12:function(e,t,a){var i=a("eed8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("15e6434f",i,!0,{sourceMap:!1,shadowMode:!1})}}]);