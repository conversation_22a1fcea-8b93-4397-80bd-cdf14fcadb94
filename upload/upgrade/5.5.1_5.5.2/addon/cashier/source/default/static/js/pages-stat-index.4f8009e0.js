(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-stat-index"],{"0288":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".container[data-v-2ebba7d9]{width:30px;height:30px;position:relative}.container.loading3[data-v-2ebba7d9]{-webkit-animation:rotation 1s infinite;animation:rotation 1s infinite}.container.loading3 .shape1[data-v-2ebba7d9]{border-top-left-radius:10px}.container.loading3 .shape2[data-v-2ebba7d9]{border-top-right-radius:10px}.container.loading3 .shape3[data-v-2ebba7d9]{border-bottom-left-radius:10px}.container.loading3 .shape4[data-v-2ebba7d9]{border-bottom-right-radius:10px}.container .shape[data-v-2ebba7d9]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-2ebba7d9]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-2ebba7d9]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-2ebba7d9]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-2ebba7d9]{bottom:0;right:0;background-color:#e66}.loading3 .shape1[data-v-2ebba7d9]{-webkit-animation:animation3shape1-data-v-2ebba7d9 .5s ease 0s infinite alternate;animation:animation3shape1-data-v-2ebba7d9 .5s ease 0s infinite alternate}@-webkit-keyframes animation3shape1-data-v-2ebba7d9{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(5px,5px);transform:translate(5px,5px)}}@keyframes animation3shape1-data-v-2ebba7d9{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(5px,5px);transform:translate(5px,5px)}}.loading3 .shape2[data-v-2ebba7d9]{-webkit-animation:animation3shape2-data-v-2ebba7d9 .5s ease 0s infinite alternate;animation:animation3shape2-data-v-2ebba7d9 .5s ease 0s infinite alternate}@-webkit-keyframes animation3shape2-data-v-2ebba7d9{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-5px,5px);transform:translate(-5px,5px)}}@keyframes animation3shape2-data-v-2ebba7d9{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-5px,5px);transform:translate(-5px,5px)}}.loading3 .shape3[data-v-2ebba7d9]{-webkit-animation:animation3shape3-data-v-2ebba7d9 .5s ease 0s infinite alternate;animation:animation3shape3-data-v-2ebba7d9 .5s ease 0s infinite alternate}@-webkit-keyframes animation3shape3-data-v-2ebba7d9{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(5px,-5px);transform:translate(5px,-5px)}}@keyframes animation3shape3-data-v-2ebba7d9{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(5px,-5px);transform:translate(5px,-5px)}}.loading3 .shape4[data-v-2ebba7d9]{-webkit-animation:animation3shape4-data-v-2ebba7d9 .5s ease 0s infinite alternate;animation:animation3shape4-data-v-2ebba7d9 .5s ease 0s infinite alternate}@-webkit-keyframes animation3shape4-data-v-2ebba7d9{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-5px,-5px);transform:translate(-5px,-5px)}}@keyframes animation3shape4-data-v-2ebba7d9{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-5px,-5px);transform:translate(-5px,-5px)}}",""]),t.exports=e},"05ba":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={qiunLoading:a("3cbb").default,qiunError:a("3628").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"chartsview",attrs:{id:"ChartBoxId"+t.cid}},[t.mixinDatacomLoading?a("v-uni-view",[a("qiun-loading",{attrs:{loadingType:t.loadingType}})],1):t._e(),t.mixinDatacomErrorMessage&&t.errorShow?a("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.reloading.apply(void 0,arguments)}}},[a("qiun-error",{attrs:{errorMessage:t.errorMessage}})],1):t._e(),t.echarts?[a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showchart,expression:"showchart"}],wxsProps:{"change:resize":"echartsResize","change:prop":"echartsOpts"},staticStyle:{width:"100%",height:"100%"},style:{background:t.background},attrs:{"data-directory":t.directory,id:"EC"+t.cid,prop:t.echartsOpts,"change:prop":t.rdcharts.ecinit,resize:t.echartsResize,"change:resize":t.rdcharts.ecresize}})]:[a("v-uni-view",{wxsProps:{"change:prop":"uchartsOpts"},attrs:{id:"UC"+t.cid,prop:t.uchartsOpts,"change:prop":t.rdcharts.ucinit},on:{mousemove:function(e){e=t.$handleWxsEvent(e),t.rdcharts.mouseMove(e,t.$getComponentDescriptor())},mousedown:function(e){e=t.$handleWxsEvent(e),t.rdcharts.mouseDown(e,t.$getComponentDescriptor())},mouseup:function(e){e=t.$handleWxsEvent(e),t.rdcharts.mouseUp(e,t.$getComponentDescriptor())},touchstart:function(e){e=t.$handleWxsEvent(e),t.rdcharts.touchStart(e,t.$getComponentDescriptor())},touchmove:function(e){e=t.$handleWxsEvent(e),t.rdcharts.touchMove(e,t.$getComponentDescriptor())},touchend:function(e){e=t.$handleWxsEvent(e),t.rdcharts.touchEnd(e,t.$getComponentDescriptor())},click:function(e){e=t.$handleWxsEvent(e),t.rdcharts.tap(e,t.$getComponentDescriptor())}}},[t.showchart?a("v-uni-canvas",{style:{width:t.cWidth+"px",height:t.cHeight+"px",background:t.background},attrs:{id:t.cid,canvasId:t.cid,"disable-scroll":t.disableScroll},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t._error.apply(void 0,arguments)}}}):t._e()],1)]],2)},r=[]},1543:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa");var n=i(a("74a5")),r=i(a("a738")),o=i(a("2c0f")),s=i(a("c4f4")),l=i(a("394f")),c={components:{Loading1:n.default,Loading2:r.default,Loading3:o.default,Loading4:s.default,Loading5:l.default},name:"qiun-loading",props:{loadingType:{type:Number,default:2}},data:function(){return{}}};e.default=c},"17bf":function(t,e,a){a("01a2"),a("e39c"),a("bf0f"),a("844d"),a("18f7"),a("de6c"),a("aa9c"),t.exports=function(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var i,n,r,o,s=[],l=!0,c=!1;try{if(r=(a=a.call(t)).next,0===e){if(Object(a)!==a)return;l=!1}else for(;!(l=(i=r.call(a)).done)&&(s.push(i.value),s.length!==e);l=!0);}catch(d){c=!0,n=d}finally{try{if(!l&&null!=a["return"]&&(o=a["return"](),Object(o)!==o))return}finally{if(c)throw n}}return s}},t.exports.__esModule=!0,t.exports["default"]=t.exports},1851:function(t,e,a){"use strict";var i=a("8bdb"),n=a("84d6"),r=a("1cb5");i({target:"Array",proto:!0},{fill:n}),r("fill")},"1b02":function(t,e,a){var i=a("526b");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("41a79d39",i,!0,{sourceMap:!1,shadowMode:!1})},"1bc3":function(t,e,a){"use strict";a.r(e);var i=a("cfd1"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"20a5":function(t,e,a){"use strict";var i=a("5b42"),n=a.n(i);n.a},"21fb":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"loading3",data:function(){return{}}}},"2b6a":function(t,e,a){var i=a("c561");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("0d11a608",i,!0,{sourceMap:!1,shadowMode:!1})},"2c0f":function(t,e,a){"use strict";a.r(e);var i=a("7ba9"),n=a("69e5");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("20a5");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"2ebba7d9",null,!1,i["a"],void 0);e["default"]=s.exports},"2e1f":function(t,e,a){"use strict";a.r(e);var i=a("c0ee"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},3103:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"loading5",data:function(){return{}}}},3628:function(t,e,a){"use strict";a.r(e);var i=a("d85d"),n=a("8ec9");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("d395");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"307d175c",null,!1,i["a"],void 0);e["default"]=s.exports},"394f":function(t,e,a){"use strict";a.r(e);var i=a("3b23"),n=a("1bc3");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("3f4b");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"11617f41",null,!1,i["a"],void 0);e["default"]=s.exports},3951:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".container[data-v-be0a6fc8]{width:30px;height:30px;position:relative}.container.loading5 .shape[data-v-be0a6fc8]{width:15px;height:15px}.container .shape[data-v-be0a6fc8]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-be0a6fc8]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-be0a6fc8]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-be0a6fc8]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-be0a6fc8]{bottom:0;right:0;background-color:#e66}.loading5 .shape1[data-v-be0a6fc8]{animation:animation5shape1-data-v-be0a6fc8 2s ease 0s infinite reverse}@-webkit-keyframes animation5shape1-data-v-be0a6fc8{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(15px);transform:translateY(15px)}50%{-webkit-transform:translate(15px,15px);transform:translate(15px,15px)}75%{-webkit-transform:translate(15px);transform:translate(15px)}}@keyframes animation5shape1-data-v-be0a6fc8{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(15px);transform:translateY(15px)}50%{-webkit-transform:translate(15px,15px);transform:translate(15px,15px)}75%{-webkit-transform:translate(15px);transform:translate(15px)}}.loading5 .shape2[data-v-be0a6fc8]{animation:animation5shape2-data-v-be0a6fc8 2s ease 0s infinite reverse}@-webkit-keyframes animation5shape2-data-v-be0a6fc8{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(-15px);transform:translate(-15px)}50%{-webkit-transform:translate(-15px,15px);transform:translate(-15px,15px)}75%{-webkit-transform:translateY(15px);transform:translateY(15px)}}@keyframes animation5shape2-data-v-be0a6fc8{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(-15px);transform:translate(-15px)}50%{-webkit-transform:translate(-15px,15px);transform:translate(-15px,15px)}75%{-webkit-transform:translateY(15px);transform:translateY(15px)}}.loading5 .shape3[data-v-be0a6fc8]{animation:animation5shape3-data-v-be0a6fc8 2s ease 0s infinite reverse}@-webkit-keyframes animation5shape3-data-v-be0a6fc8{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(15px);transform:translate(15px)}50%{-webkit-transform:translate(15px,-15px);transform:translate(15px,-15px)}75%{-webkit-transform:translateY(-15px);transform:translateY(-15px)}}@keyframes animation5shape3-data-v-be0a6fc8{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(15px);transform:translate(15px)}50%{-webkit-transform:translate(15px,-15px);transform:translate(15px,-15px)}75%{-webkit-transform:translateY(-15px);transform:translateY(-15px)}}.loading5 .shape4[data-v-be0a6fc8]{animation:animation5shape4-data-v-be0a6fc8 2s ease 0s infinite reverse}@-webkit-keyframes animation5shape4-data-v-be0a6fc8{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(-15px);transform:translateY(-15px)}50%{-webkit-transform:translate(-15px,-15px);transform:translate(-15px,-15px)}75%{-webkit-transform:translate(-15px);transform:translate(-15px)}}@keyframes animation5shape4-data-v-be0a6fc8{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(-15px);transform:translateY(-15px)}50%{-webkit-transform:translate(-15px,-15px);transform:translate(-15px,-15px)}75%{-webkit-transform:translate(-15px);transform:translate(-15px)}}",""]),t.exports=e},"3b23":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"container loading6"},[e("v-uni-view",{staticClass:"shape shape1"}),e("v-uni-view",{staticClass:"shape shape2"}),e("v-uni-view",{staticClass:"shape shape3"}),e("v-uni-view",{staticClass:"shape shape4"})],1)},n=[]},"3cb1":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getStatDay=function(t){return n.default.post("/cashier/storeapi/stat/dayStatData",{data:t})},e.getStatHour=function(t){return n.default.post("/cashier/storeapi/stat/hourStatData",{data:t})},e.getStatTotal=function(t){return n.default.post("/cashier/storeapi/stat/statTotal",{data:t})};var n=i(a("a3b5"))},"3cbb":function(t,e,a){"use strict";a.r(e);var i=a("634e"),n=a("622d");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"1c9129ec",null,!1,i["a"],void 0);e["default"]=s.exports},"3d98":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"qiun-error",props:{errorMessage:{type:String,default:null}},data:function(){return{}}};e.default=i},"3e11":function(t,e,a){var i=a("a927");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("26838122",i,!0,{sourceMap:!1,shadowMode:!1})},"3f4b":function(t,e,a){"use strict";var i=a("667f"),n=a.n(i);n.a},"457c":function(t,e,a){var i=a("d993");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("e7e7ec22",i,!0,{sourceMap:!1,shadowMode:!1})},"51de":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"container loading5"},[e("v-uni-view",{staticClass:"shape shape1"}),e("v-uni-view",{staticClass:"shape shape2"}),e("v-uni-view",{staticClass:"shape shape3"}),e("v-uni-view",{staticClass:"shape shape4"})],1)},n=[]},"526b":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".pop-content[data-v-3353c590] .uni-icons{line-height:.32rem}",""]),t.exports=e},"5b42":function(t,e,a){var i=a("0288");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("25b53614",i,!0,{sourceMap:!1,shadowMode:!1})},6178:function(t,e){t.exports=function(t){if(Array.isArray(t))return t},t.exports.__esModule=!0,t.exports["default"]=t.exports},"622d":function(t,e,a){"use strict";a.r(e);var i=a("1543"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"634e":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[1==t.loadingType?a("Loading1"):t._e(),2==t.loadingType?a("Loading2"):t._e(),3==t.loadingType?a("Loading3"):t._e(),4==t.loadingType?a("Loading4"):t._e(),5==t.loadingType?a("Loading5"):t._e()],1)},n=[]},"657c":function(t,e,a){var i=a("b8ee");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("22c3aab0",i,!0,{sourceMap:!1,shadowMode:!1})},"667f":function(t,e,a){var i=a("6847");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("005e643d",i,!0,{sourceMap:!1,shadowMode:!1})},6791:function(t,e,a){"use strict";(function(t){var e=a("dda4").default,i=a("bdbb").default;a("7a76"),a("c9b5"),a("bf0f"),a("ab80"),a("2797"),a("aa9c"),a("5c47"),a("a1c1"),a("e966"),a("5ef2"),a("0506"),a("473f"),a("c223"),a("fd3c"),a("8f71"),a("f7a5"),a("64aa"),a("4100"),a("dc69"),a("1851"),a("d4b5");var n={version:"v2.0.0-20210419",yAxisWidth:15,yAxisSplit:5,xAxisHeight:22,xAxisLineHeight:22,legendHeight:15,yAxisTitleWidth:15,padding:[10,10,10,10],pixelRatio:1,rotate:!1,columePadding:3,fontSize:13,fontColor:"#666666",dataPointShape:["circle","circle","circle","circle"],color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],linearColor:["#0EE2F8","#2BDCA8","#FA7D8D","#EB88E2","#2AE3A0","#0EE2F8","#EB88E2","#6773E3","#F78A85"],pieChartLinePadding:15,pieChartTextPadding:5,xAxisTextPadding:3,titleColor:"#333333",titleFontSize:20,subtitleColor:"#999999",subtitleFontSize:15,toolTipPadding:3,toolTipBackground:"#000000",toolTipOpacity:.7,toolTipLineHeight:20,radarLabelTextMargin:13,gaugeLabelTextMargin:13},r=function(t){for(var e=arguments.length,a=new Array(e>1?e-1:0),i=1;i<e;i++)a[i-1]=arguments[i];if(null==t)throw new TypeError("[uCharts] Cannot convert undefined or null to object");if(!a||a.length<=0)return t;function n(t,e){for(var a in e)t[a]=t[a]&&"[object Object]"===t[a].toString()?n(t[a],e[a]):t[a]=e[a];return t}return a.forEach((function(e){t=n(t,e)})),t},o={toFixed:function(t,e){return e=e||2,this.isFloat(t)&&(t=t.toFixed(e)),t},isFloat:function(t){return t%1!==0},approximatelyEqual:function(t,e){return Math.abs(t-e)<1e-10},isSameSign:function(t,e){return Math.abs(t)===t&&Math.abs(e)===e||Math.abs(t)!==t&&Math.abs(e)!==e},isSameXCoordinateArea:function(t,e){return this.isSameSign(t.x,e.x)},isCollision:function(t,e){t.end={},t.end.x=t.start.x+t.width,t.end.y=t.start.y-t.height,e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height;var a=e.start.x>t.end.x||e.end.x<t.start.x||e.end.y>t.start.y||e.start.y<t.end.y;return!a}};function s(t,e){var a=Array(2),i=20037508.34*t/180,n=Math.log(Math.tan((90+e)*Math.PI/360))/(Math.PI/180);return n=20037508.34*n/180,a[0]=i,a[1]=n,a}function l(t,e){var a=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(t,e,a,i){return e+e+a+a+i+i})),i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(a),n=parseInt(i[1],16),r=parseInt(i[2],16),o=parseInt(i[3],16);return"rgba("+n+","+r+","+o+","+e+")"}function c(t,e,a){if(isNaN(t))throw new Error("[uCharts] series数据需为Number格式");a=a||10,e=e||"upper";var i=1;while(a<1)a*=10,i*=10;t="upper"===e?Math.ceil(t*i):Math.floor(t*i);while(t%a!==0)"upper"===e?t++:t--;return t/i}function d(t,e,a){function i(t){while(t<0)t+=2*Math.PI;while(t>2*Math.PI)t-=2*Math.PI;return t}return t=i(t),e=i(e),a=i(a),e>a&&(a+=2*Math.PI,t<e&&(t+=2*Math.PI)),t>=e&&t<=a}function h(t,e){function a(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].y>=Math.max(t[e-1].y,t[e+1].y)||t[e].y<=Math.min(t[e-1].y,t[e+1].y))}function i(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].x>=Math.max(t[e-1].x,t[e+1].x)||t[e].x<=Math.min(t[e-1].x,t[e+1].x))}var n=.2,r=.2,o=null,s=null,l=null,c=null;if(e<1?(o=t[0].x+(t[1].x-t[0].x)*n,s=t[0].y+(t[1].y-t[0].y)*n):(o=t[e].x+(t[e+1].x-t[e-1].x)*n,s=t[e].y+(t[e+1].y-t[e-1].y)*n),e>t.length-3){var d=t.length-1;l=t[d].x-(t[d].x-t[d-1].x)*r,c=t[d].y-(t[d].y-t[d-1].y)*r}else l=t[e+1].x-(t[e+2].x-t[e].x)*r,c=t[e+1].y-(t[e+2].y-t[e].y)*r;return a(t,e+1)&&(c=t[e+1].y),a(t,e)&&(s=t[e].y),i(t,e+1)&&(l=t[e+1].x),i(t,e)&&(o=t[e].x),(s>=Math.max(t[e].y,t[e+1].y)||s<=Math.min(t[e].y,t[e+1].y))&&(s=t[e].y),(c>=Math.max(t[e].y,t[e+1].y)||c<=Math.min(t[e].y,t[e+1].y))&&(c=t[e+1].y),(o>=Math.max(t[e].x,t[e+1].x)||o<=Math.min(t[e].x,t[e+1].x))&&(o=t[e].x),(l>=Math.max(t[e].x,t[e+1].x)||l<=Math.min(t[e].x,t[e+1].x))&&(l=t[e+1].x),{ctrA:{x:o,y:s},ctrB:{x:l,y:c}}}function p(t,e,a){return{x:a.x+t,y:a.y-e}}function f(t,e){if(e)while(o.isCollision(t,e))t.start.x>0?t.start.y--:t.start.x<0||t.start.y>0?t.start.y++:t.start.y--;return t}function u(t,e,a){for(var i=0,n=0;n<t.length;n++){var r=t[n];if(r.color||(r.color=a.color[i],i=(i+1)%a.color.length),r.linearIndex||(r.linearIndex=n),r.index||(r.index=0),r.type||(r.type=e.type),"undefined"==typeof r.show&&(r.show=!0),r.type||(r.type=e.type),r.pointShape||(r.pointShape="circle"),!r.legendShape)switch(r.type){case"line":r.legendShape="line";break;case"column":r.legendShape="rect";break;case"area":r.legendShape="triangle";break;default:r.legendShape="circle"}}return t}function x(t,e,a,i){var n=e||[];if("custom"==t&&0==n.length&&(n=i.linearColor),"custom"==t&&n.length<a.length)for(var r=a.length-n.length,o=0;o<r;o++)n.push(i.linearColor[(o+1)%i.linearColor.length]);return n}function m(t,e){var a=0,i=e-t;return a=i>=1e4?1e3:i>=1e3?100:i>=100?10:i>=10?5:i>=1?1:i>=.1?.1:i>=.01?.01:i>=.001?.001:i>=1e-4?1e-4:i>=1e-5?1e-5:1e-6,{minRange:c(t,"lower",a),maxRange:c(e,"upper",a)}}function g(t,e,a){var i=0;if(t=String(t),!1!==a&&void 0!==a&&a.setFontSize&&a.measureText)return a.setFontSize(e),a.measureText(t).width;t=t.split("");for(var n=0;n<t.length;n++){var r=t[n];/[a-zA-Z]/.test(r)?i+=7:/[0-9]/.test(r)?i+=5.5:/\./.test(r)?i+=2.7:/-/.test(r)?i+=3.25:/:/.test(r)?i+=2.5:/[\u4e00-\u9fa5]/.test(r)?i+=10:/\(|\)/.test(r)?i+=3.73:/\s/.test(r)?i+=2.5:/%/.test(r)?i+=8:i+=10}return i*e/10}function v(t){return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data)}),[])}function y(t,e){for(var a=new Array(e),i=0;i<a.length;i++)a[i]=0;for(var n=0;n<t.length;n++)for(i=0;i<a.length;i++)a[i]+=t[n].data[i];return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data).concat(a)}),[])}function b(t,e,a){var i,n;return t.clientX?e.rotate?(n=e.height-t.clientX*e.pix,i=(t.pageY-a.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):(i=t.clientX*e.pix,n=(t.pageY-a.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):e.rotate?(n=e.height-t.x*e.pix,i=t.y*e.pix):(i=t.x*e.pix,n=t.y*e.pix),{x:i,y:n}}function w(t,e){for(var a=[],i=0;i<t.length;i++){var n=t[i];if(null!==n.data[e]&&"undefined"!==typeof n.data[e]&&n.show){var r={};r.color=n.color,r.type=n.type,r.style=n.style,r.pointShape=n.pointShape,r.disableLegend=n.disableLegend,r.name=n.name,r.show=n.show,r.data=n.formatter?n.formatter(n.data[e]):n.data[e],a.push(r)}}return a}function A(t,e,a){var i=t.map((function(t){return g(t,e,a)}));return Math.max.apply(null,i)}function C(t){for(var e=2*Math.PI/t,a=[],i=0;i<t;i++)a.push(e*i);return a.map((function(t){return-1*t+Math.PI/2}))}function S(t,e,a,i){for(var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},r=e.chartData.calPoints?e.chartData.calPoints:[],o=t.map((function(t){var r=[];return r=i||t.data,{text:n.formatter?n.formatter(t,r[a],a,e):t.name+": "+t.data,color:t.color}})),s=[],l={x:0,y:0},c=0;c<r.length;c++){var d=r[c];"undefined"!==typeof d[a]&&null!==d[a]&&s.push(d[a])}for(var h=0;h<s.length;h++){var p=s[h];l.x=Math.round(p.x),l.y+=p.y}return l.y/=s.length,{textList:o,offset:l}}function T(t,e,a,i){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},r=e.chartData.calPoints,o=t.map((function(t){return{text:n.formatter?n.formatter(t,i[a],a,e):t.name+": "+t.data,color:t.color,disableLegend:!!t.disableLegend}}));o=o.filter((function(t){if(!0!==t.disableLegend)return t}));for(var s=[],l={x:0,y:0},c=0;c<r.length;c++){var d=r[c];"undefined"!==typeof d[a]&&null!==d[a]&&s.push(d[a])}for(var h=0;h<s.length;h++){var p=s[h];l.x=Math.round(p.x),l.y+=p.y}return l.y/=s.length,{textList:o,offset:l}}function k(t,e,a,i,n,r){var o=a.chartData.calPoints,s=r.color.upFill,l=r.color.downFill,c=[s,s,l,s],d=[],h={text:n[i],color:null};d.push(h),e.map((function(e){0==i?e.data[1]-e.data[0]<0?c[1]=l:c[1]=s:(e.data[0]<t[i-1][1]&&(c[0]=l),e.data[1]<e.data[0]&&(c[1]=l),e.data[2]>t[i-1][1]&&(c[2]=s),e.data[3]<t[i-1][1]&&(c[3]=l));var a={text:"开盘："+e.data[0],color:c[0]},n={text:"收盘："+e.data[1],color:c[1]},r={text:"最低："+e.data[2],color:c[2]},o={text:"最高："+e.data[3],color:c[3]};d.push(a,n,r,o)}));for(var p=[],f={x:0,y:0},u=0;u<o.length;u++){var x=o[u];"undefined"!==typeof x[i]&&null!==x[i]&&p.push(x[i])}return f.x=Math.round(p[0][0].x),{textList:d,offset:f}}function D(t,e,a){return t.x<=e.width-e.area[1]+10&&t.x>=e.area[3]-10&&t.y>=e.area[0]&&t.y<=e.height-e.area[2]}function P(t,e,a){return Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)<=Math.pow(a,2)}function _(t,e){var a=[],i=[];return t.forEach((function(t,n){e.connectNulls?null!==t&&i.push(t):null!==t?i.push(t):(i.length&&a.push(i),i=[])})),i.length&&a.push(i),a}function M(t,e,a,i,n){var r={angle:0,xAxisHeight:a.xAxisHeight},o=t.map((function(t){return g(t,e.xAxis.fontSize||a.fontSize,n)})),s=Math.max.apply(this,o);return 1==e.xAxis.rotateLabel&&s+2*a.xAxisTextPadding>i&&(r.angle=45*Math.PI/180,r.xAxisHeight=2*a.xAxisTextPadding+s*Math.sin(r.angle)),r}function L(t,e,a,n){var r={angle:0,xAxisHeight:a.xAxisHeight};r.ranges=function(t,e,a){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1,r=v(t),o=[];r=r.filter((function(t){return"object"===i(t)&&null!==t?t.constructor.toString().indexOf("Array")>-1?null!==t:null!==t.value:null!==t})),r.map((function(t){"object"===i(t)?t.constructor.toString().indexOf("Array")>-1?"candle"==e.type?t.map((function(t){o.push(t)})):o.push(t[0]):o.push(t.value):o.push(t)}));var s=0,l=0;if(o.length>0&&(s=Math.min.apply(this,o),l=Math.max.apply(this,o)),n>-1?("number"===typeof e.xAxis.data[n].min&&(s=Math.min(e.xAxis.data[n].min,s)),"number"===typeof e.xAxis.data[n].max&&(l=Math.max(e.xAxis.data[n].max,l))):("number"===typeof e.xAxis.min&&(s=Math.min(e.xAxis.min,s)),"number"===typeof e.xAxis.max&&(l=Math.max(e.xAxis.max,l))),s===l){var c=l||10;l+=c}for(var d=s,h=l,p=[],f=(h-d)/e.xAxis.splitNumber,u=0;u<=e.xAxis.splitNumber;u++)p.push(d+f*u);return p}(t,e,a),r.rangesFormat=r.ranges.map((function(t){return t=e.xAxis.formatter?e.xAxis.formatter(t):o.toFixed(t,2),t}));var s=r.ranges.map((function(t){return t=o.toFixed(t,2),t=e.xAxis.formatter?e.xAxis.formatter(Number(t)):t,t}));r=Object.assign(r,j(s,e,a));var l=r.eachSpacing,c=s.map((function(t){return g(t,a.fontSize,n)})),d=Math.max.apply(this,c);return d+2*a.xAxisTextPadding>l&&(r.angle=45*Math.PI/180,r.xAxisHeight=2*a.xAxisTextPadding+d*Math.sin(r.angle)),!0===e.xAxis.disabled&&(r.xAxisHeight=0),r}function F(t,e,a,i,n){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,o=n.extra.radar||{};o.max=o.max||0;for(var s=Math.max(o.max,Math.max.apply(null,v(i))),l=[],c=function(n){var o=i[n],c={};c.color=o.color,c.legendShape=o.legendShape,c.pointShape=o.pointShape,c.data=[],o.data.forEach((function(i,n){var o={};o.angle=t[n],o.proportion=i/s,o.position=p(a*o.proportion*r*Math.cos(o.angle),a*o.proportion*r*Math.sin(o.angle),e),c.data.push(o)})),l.push(c)},d=0;d<i.length;d++)c(d);return l}function O(t,e){for(var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,i=0,n=0,r=0;r<t.length;r++){var o=t[r];o.data=null===o.data?0:o.data,i+=o.data}for(var s=0;s<t.length;s++){var l=t[s];l.data=null===l.data?0:l.data,l._proportion_=0===i?1/t.length*a:l.data/i*a,l._radius_=e}for(var c=0;c<t.length;c++){var d=t[c];d._start_=n,n+=2*d._proportion_*Math.PI}return t}function E(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t=t.sort((function(t,e){return parseInt(e.data)-parseInt(t.data)}));for(var i=0;i<t.length;i++)t[i].radius=t[i].data/t[0].data*e*a,t[i]._proportion_=t[i].data/t[0].data;return t.reverse()}function I(t,e,a,i){for(var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=0,o=0,s=[],l=0;l<t.length;l++){var c=t[l];c.data=null===c.data?0:c.data,r+=c.data,s.push(c.data)}for(var d=Math.min.apply(null,s),h=Math.max.apply(null,s),p=i-a,f=0;f<t.length;f++){var u=t[f];u.data=null===u.data?0:u.data,0===r||"area"==e?(u._proportion_=u.data/r*n,u._rose_proportion_=1/t.length*n):(u._proportion_=u.data/r*n,u._rose_proportion_=u.data/r*n),u._radius_=a+p*((u.data-d)/(h-d))}for(var x=0;x<t.length;x++){var m=t[x];m._start_=o,o+=2*m._rose_proportion_*Math.PI}return t}function z(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==a&&(a=.999999);for(var i=0;i<t.length;i++){var n=t[i];n.data=null===n.data?0:n.data;var r=void 0;r="circle"==e.type?2:e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle,n._proportion_=r*n.data*a+e.startAngle,n._proportion_>=2&&(n._proportion_=n._proportion_%2)}return t}function B(t,e,a){for(var i=e-a+1,n=e,r=0;r<t.length;r++)t[r].value=null===t[r].value?0:t[r].value,t[r]._startAngle_=n,t[r]._endAngle_=i*t[r].value+e,t[r]._endAngle_>=2&&(t[r]._endAngle_=t[r]._endAngle_%2),n=t[r]._endAngle_;return t}function W(t,e,a){for(var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,n=0;n<t.length;n++){var r=t[n];if(r.data=null===r.data?0:r.data,"auto"==a.pointer.color){for(var o=0;o<e.length;o++)if(r.data<=e[o].value){r.color=e[o].color;break}}else r.color=a.pointer.color;var s=a.startAngle-a.endAngle+1;r._endAngle_=s*r.data+a.startAngle,r._oldAngle_=a.oldAngle,a.oldAngle<a.endAngle&&(r._oldAngle_+=2),r.data>=a.oldData?r._proportion_=(r._endAngle_-r._oldAngle_)*i+a.oldAngle:r._proportion_=r._oldAngle_-(r._oldAngle_-r._endAngle_)*i,r._proportion_>=2&&(r._proportion_=r._proportion_%2)}return t}function R(t,e,a,i,n,r){return t.map((function(t){if(null===t)return null;var o=0;return o="mix"==r.type?r.extra.mix.column.seriesGap*r.pix||0:r.extra.column.seriesGap*r.pix||0,t.width=Math.ceil((e-2*n.columePadding-o*(a-1))/a),r.extra.mix&&r.extra.mix.column.width&&+r.extra.mix.column.width>0&&(t.width=Math.min(t.width,+r.extra.mix.column.width*r.pix)),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),t.width<=0&&(t.width=1),t.x+=(i+.5-a/2)*(t.width+o),t}))}function N(t,e,a,i,n,r,o){return t.map((function(t){return null===t?null:(t.width=Math.ceil((e-2*n.columePadding)/2),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),i>0&&(t.width-=2*o),t)}))}function Y(t,e,a,i,n,r,o){return t.map((function(t,a){return null===t?null:(t.width=Math.ceil((e-2*n.columePadding)/2),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(t.width=Math.min(t.width,+r.extra.column.width*r.pix)),t)}))}function j(t,e,a){var i=e.width-e.area[1]-e.area[3],n=e.enableScroll?Math.min(e.xAxis.itemCount,t.length):t.length;("line"==e.type||"area"==e.type)&&n>1&&"justify"==e.xAxis.boundaryGap&&(n-=1);var r=i/n,o=[],s=e.area[3],l=e.width-e.area[1];return t.forEach((function(t,e){o.push(s+e*r)})),"justify"!==e.xAxis.boundaryGap&&(!0===e.enableScroll?o.push(s+t.length*r):o.push(l)),{xAxisPoints:o,startX:s,endX:l,eachSpacing:r}}function G(t,e,a,i,n,r,o){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,l=[],c=r.height-r.area[0]-r.area[2];return t.forEach((function(t,o){if(null===t)l.push(null);else{var d=[];t.forEach((function(t,l){var h={};h.x=i[o]+Math.round(n/2);var p=t.value||t,f=c*(p-e)/(a-e);f*=s,h.y=r.height-Math.round(f)-r.area[2],d.push(h)})),l.push(d)}})),l}function H(t,e,a,n,r,o,s){var l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,c="center";"line"!=o.type&&"area"!=o.type||(c=o.xAxis.boundaryGap);var d=[],h=o.height-o.area[0]-o.area[2],p=o.width-o.area[1]-o.area[3];return t.forEach((function(t,s){if(null===t)d.push(null);else{var f={};f.color=t.color,f.x=n[s];var u,x,m,g=t;if("object"===i(t)&&null!==t)if(t.constructor.toString().indexOf("Array")>-1)u=[].concat(o.chartData.xAxisData.ranges),x=u.shift(),m=u.pop(),g=t[1],f.x=o.area[3]+p*(t[0]-x)/(m-x);else g=t.value;"center"==c&&(f.x+=Math.round(r/2));var v=h*(g-e)/(a-e);v*=l,f.y=o.height-Math.round(v)-o.area[2],d.push(f)}})),d}function J(t,e,a,i,n,r,o,s,l){var c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,d=[],h=r.height-r.area[0]-r.area[2];return t.forEach((function(t,o){if(null===t)d.push(null);else{var p={};if(p.color=t.color,p.x=i[o]+Math.round(n/2),s>0){for(var f=0,u=0;u<=s;u++)f+=l[u].data[o];var x=f-t,m=h*(f-e)/(a-e),g=h*(x-e)/(a-e)}else f=t,m=h*(f-e)/(a-e),g=0;var v=g;m*=c,v*=c,p.y=r.height-Math.round(m)-r.area[2],p.y0=r.height-Math.round(v)-r.area[2],d.push(p)}})),d}function X(t,e,a,n){var r,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;r="stack"==n?y(t,e.categories.length):v(t);var s=[];r=r.filter((function(t){return"object"===i(t)&&null!==t?t.constructor.toString().indexOf("Array")>-1?null!==t:null!==t.value:null!==t})),r.map((function(t){"object"===i(t)?t.constructor.toString().indexOf("Array")>-1?"candle"==e.type?t.map((function(t){s.push(t)})):s.push(t[1]):s.push(t.value):s.push(t)}));var l=0,c=0;if(s.length>0&&(l=Math.min.apply(this,s),c=Math.max.apply(this,s)),o>-1?("number"===typeof e.yAxis.data[o].min&&(l=Math.min(e.yAxis.data[o].min,l)),"number"===typeof e.yAxis.data[o].max&&(c=Math.max(e.yAxis.data[o].max,c))):("number"===typeof e.yAxis.min&&(l=Math.min(e.yAxis.min,l)),"number"===typeof e.yAxis.max&&(c=Math.max(e.yAxis.max,c))),l===c){var d=c||10;c+=d}for(var h=m(l,c),p=h.minRange,f=h.maxRange,u=[],x=(f-p)/e.yAxis.splitNumber,g=0;g<=e.yAxis.splitNumber;g++)u.push(p+x*g);return u.reverse()}function q(t,e,a,i){var n=r({},{type:""},e.extra.column),o=e.yAxis.data.length,s=new Array(o);if(o>0){for(var l=0;l<o;l++){s[l]=[];for(var c=0;c<t.length;c++)t[c].index==l&&s[l].push(t[c])}for(var d=new Array(o),h=new Array(o),p=new Array(o),f=function(t){var r=e.yAxis.data[t];1==e.yAxis.disabled&&(r.disabled=!0),r.formatter||(r.formatter=function(t){return t.toFixed(r.tofix)+(r.unit||"")}),d[t]=X(s[t],e,a,n.type,t);var o=r.fontSize*e.pix||a.fontSize;p[t]={position:r.position?r.position:"left",width:0},h[t]=d[t].map((function(e){return e=r.formatter(Number(e)),p[t].width=Math.max(p[t].width,g(e,o,i)+5),e}));var l=r.calibration?4*e.pix:0;p[t].width+=l+3*e.pix,!0===r.disabled&&(p[t].width=0)},u=0;u<o;u++)f(u)}else{d=new Array(1),h=new Array(1),p=new Array(1);e.yAxis.formatter||(e.yAxis.formatter=function(t){return t.toFixed(e.yAxis.tofix)+(e.yAxis.unit||"")}),d[0]=X(t,e,a,n.type),p[0]={position:"left",width:0};var x=e.yAxis.fontSize*e.pix||a.fontSize;h[0]=d[0].map((function(t){return t=e.yAxis.formatter(Number(t)),p[0].width=Math.max(p[0].width,g(t,x,i)+5),t})),p[0].width+=3*e.pix,!0===e.yAxis.disabled?(p[0]={position:"left",width:0},e.yAxis.data[0]={disabled:!0}):e.yAxis.data[0]={disabled:!1,position:"left",max:e.yAxis.max,min:e.yAxis.min,formatter:e.yAxis.formatter}}return{rangesFormat:h,ranges:d,yAxisWidth:p}}function Q(t,e){!0!==e.rotateLock?(t.translate(e.height,0),t.rotate(90*Math.PI/180)):!0!==e._rotate_&&(t.translate(e.height,0),t.rotate(90*Math.PI/180),e._rotate_=!0)}function U(t,e,a,i,n){if(i.beginPath(),"hollow"==n.dataPointShapeType?(i.setStrokeStyle(e),i.setFillStyle(n.background),i.setLineWidth(2*n.pix)):(i.setStrokeStyle("#ffffff"),i.setFillStyle(e),i.setLineWidth(1*n.pix)),"diamond"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y),i.lineTo(t.x,t.y****),i.lineTo(t.x****,t.y),i.lineTo(t.x,t.y-4.5))}));else if("circle"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x*****n.pix,t.y),i.arc(t.x,t.y,3*n.pix,0,2*Math.PI,!1))}));else if("square"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x-3.5,t.y-3.5),i.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===a)t.forEach((function(t,e){null!==t&&(i.moveTo(t.x,t.y-4.5),i.lineTo(t.x-4.5,t.y****),i.lineTo(t.x****,t.y****),i.lineTo(t.x,t.y-4.5))}));else if("triangle"===a)return;i.closePath(),i.fill(),i.stroke()}function Z(t,e,a,i){var n=t.title.fontSize||e.titleFontSize,r=t.subtitle.fontSize||e.subtitleFontSize,o=t.title.name||"",s=t.subtitle.name||"",l=t.title.color||t.fontColor,c=t.subtitle.color||t.fontColor,d=o?n:0,h=s?r:0;if(s){var p=g(s,r*t.pix,a),f=i.x-p/2+(t.subtitle.offsetX||0)*t.pix,u=i.y+r*t.pix/2+(t.subtitle.offsetY||0)*t.pix;o&&(u+=(d*t.pix+5)/2),a.beginPath(),a.setFontSize(r*t.pix),a.setFillStyle(c),a.fillText(s,f,u),a.closePath(),a.stroke()}if(o){var x=g(o,n*t.pix,a),m=i.x-x/2+(t.title.offsetX||0),v=i.y+n*t.pix/2+(t.title.offsetY||0)*t.pix;s&&(v-=(h*t.pix+5)/2),a.beginPath(),a.setFontSize(n*t.pix),a.setFillStyle(l),a.fillText(o,m,v),a.closePath(),a.stroke()}}function V(t,e,a,n,r){var o=e.data,s=e.textOffset?e.textOffset:0;t.forEach((function(t,l){if(null!==t){n.beginPath(),n.setFontSize(e.textSize||a.fontSize),n.setFillStyle(e.textColor||a.fontColor);var c=o[l];"object"===i(o[l])&&null!==o[l]&&(c=o[l].constructor.toString().indexOf("Array")>-1?o[l][1]:o[l].value);var d=e.formatter?e.formatter(c):c;n.setTextAlign("center"),n.fillText(String(d),t.x,t.y-4+s*r.pix),n.closePath(),n.stroke(),n.setTextAlign("left")}}))}function K(t,e,a,i,n,r){e-=t.width/2+n.gaugeLabelTextMargin;for(var o=t.startAngle-t.endAngle+1,s=o/t.splitLine.splitNumber,l=t.endNumber-t.startNumber,c=l/t.splitLine.splitNumber,d=t.startAngle,h=t.startNumber,p=0;p<t.splitLine.splitNumber+1;p++){var f={x:e*Math.cos(d*Math.PI),y:e*Math.sin(d*Math.PI)},u=t.formatter?t.formatter(h):h;f.x+=a.x-g(u,n.fontSize,r)/2,f.y+=a.y;var x=f.x,m=f.y;r.beginPath(),r.setFontSize(n.fontSize),r.setFillStyle(t.labelColor||i.fontColor),r.fillText(u,x,m+n.fontSize/2),r.closePath(),r.stroke(),d+=s,d>=2&&(d%=2),h+=c}}function $(t,e,a,i,n,r){var s=i.extra.radar||{};e+=n.radarLabelTextMargin*i.pix,t.forEach((function(t,l){var c={x:e*Math.cos(t),y:e*Math.sin(t)},d=p(c.x,c.y,a),h=d.x,f=d.y;o.approximatelyEqual(c.x,0)?h-=g(i.categories[l]||"",n.fontSize,r)/2:c.x<0&&(h-=g(i.categories[l]||"",n.fontSize,r)),r.beginPath(),r.setFontSize(n.fontSize),r.setFillStyle(s.labelColor||i.fontColor),r.fillText(i.categories[l]||"",h,f+n.fontSize/2),r.closePath(),r.stroke()}))}function tt(t,e,a,i,n,r){for(var s=a.pieChartLinePadding,l=[],c=null,d=t.map((function(t,e,a){var i=t.formatter?t.formatter(t,e,a):o.toFixed(100*t._proportion_.toFixed(4))+"%";t._rose_proportion_&&(t._proportion_=t._rose_proportion_);var n=2*Math.PI-(t._start_+2*Math.PI*t._proportion_/2),r=t.color,s=t._radius_;return{arc:n,text:i,color:r,radius:s,textColor:t.textColor,textSize:t.textSize}})),h=0;h<d.length;h++){var u=d[h],x=Math.cos(u.arc)*(u.radius+s),m=Math.sin(u.arc)*(u.radius+s),v=Math.cos(u.arc)*u.radius,y=Math.sin(u.arc)*u.radius,b=x>=0?x+a.pieChartTextPadding:x-a.pieChartTextPadding,w=m,A=g(u.text,u.textSize||a.fontSize,i),C=w;c&&o.isSameXCoordinateArea(c.start,{x:b})&&(C=b>0?Math.min(w,c.start.y):x<0||w>0?Math.max(w,c.start.y):Math.min(w,c.start.y)),b<0&&(b-=A);var S={lineStart:{x:v,y:y},lineEnd:{x:x,y:m},start:{x:b,y:C},width:A,height:a.fontSize,text:u.text,color:u.color,textColor:u.textColor,textSize:u.textSize};c=f(S,c),l.push(c)}for(var T=0;T<l.length;T++){var k=l[T],D=p(k.lineStart.x,k.lineStart.y,r),P=p(k.lineEnd.x,k.lineEnd.y,r),_=p(k.start.x,k.start.y,r);i.setLineWidth(1*e.pix),i.setFontSize(a.fontSize),i.beginPath(),i.setStrokeStyle(k.color),i.setFillStyle(k.color),i.moveTo(D.x,D.y);var M=k.start.x<0?_.x+k.width:_.x,L=k.start.x<0?_.x-5:_.x+5;i.quadraticCurveTo(P.x,P.y,M,_.y),i.moveTo(D.x,D.y),i.stroke(),i.closePath(),i.beginPath(),i.moveTo(_.x+k.width,_.y),i.arc(M,_.y,2,0,2*Math.PI),i.closePath(),i.fill(),i.beginPath(),i.setFontSize(k.textSize||a.fontSize),i.setFillStyle(k.textColor||e.fontColor),i.fillText(k.text,L,_.y+3),i.closePath(),i.stroke(),i.closePath()}}function et(t,e,a){for(var i=r({},{type:"solid",dashLength:4,data:[]},t.extra.markLine),n=t.area[3],o=t.width-t.area[1],s=function(t,e){for(var a,i,n=e.height-e.area[0]-e.area[2],r=0;r<t.length;r++){t[r].yAxisIndex=t[r].yAxisIndex?t[r].yAxisIndex:0;var o=[].concat(e.chartData.yAxisData.ranges[t[r].yAxisIndex]);a=o.pop(),i=o.shift();var s=n*(t[r].value-a)/(i-a);t[r].y=e.height-Math.round(s)-e.area[2]}return t}(i.data,t),c=0;c<s.length;c++){var d=r({},{lineColor:"#DE4A42",showLabel:!1,labelFontColor:"#666666",labelBgColor:"#DFE8FF",labelBgOpacity:.8,yAxisIndex:0},s[c]);if("dash"==i.type&&a.setLineDash([i.dashLength,i.dashLength]),a.setStrokeStyle(d.lineColor),a.setLineWidth(1*t.pix),a.beginPath(),a.moveTo(n,d.y),a.lineTo(o,d.y),a.stroke(),a.setLineDash([]),d.showLabel){var h=t.yAxis.formatter?t.yAxis.formatter(Number(d.value)):d.value;a.setFontSize(e.fontSize);var p=g(h,e.fontSize,a),f=(t.chartData.yAxisData.yAxisWidth[0].width,t.area[3]-p-2*e.toolTipPadding),u=t.area[3],x=u-f,m=u-e.toolTipPadding,v=d.y;a.setFillStyle(l(d.labelBgColor,d.labelBgOpacity)),a.setStrokeStyle(d.labelBgColor),a.setLineWidth(1*t.pix),a.beginPath(),a.rect(f,v-.5*e.fontSize-e.toolTipPadding,x,e.fontSize+2*e.toolTipPadding),a.closePath(),a.stroke(),a.fill(),a.setFontSize(e.fontSize),a.setTextAlign("right"),a.setFillStyle(d.labelFontColor),a.fillText(String(h),m,v+.5*e.fontSize),a.stroke(),a.setTextAlign("left")}}}function at(t,e,a,i,n){var o=r({},{gridType:"solid",dashLength:4},t.extra.tooltip),s=t.area[3],c=t.width-t.area[1];if("dash"==o.gridType&&a.setLineDash([o.dashLength,o.dashLength]),a.setStrokeStyle(o.gridColor||"#cccccc"),a.setLineWidth(1*t.pix),a.beginPath(),a.moveTo(s,t.tooltip.offset.y),a.lineTo(c,t.tooltip.offset.y),a.stroke(),a.setLineDash([]),o.yAxisLabel)for(var d=function(t,e,a,i,n){for(var r=[].concat(a.chartData.yAxisData.ranges),o=a.height-a.area[0]-a.area[2],s=a.area[0],l=[],c=0;c<r.length;c++){var d=r[c].shift(),h=r[c].pop(),p=d-(d-h)*(t-s)/o;p=a.yAxis.data[c].formatter?a.yAxis.data[c].formatter(Number(p)):p.toFixed(0),l.push(String(p))}return l}(t.tooltip.offset.y,t.series,t),h=t.chartData.yAxisData.yAxisWidth,p=t.area[3],f=t.width-t.area[1],u=0;u<d.length;u++){a.setFontSize(e.fontSize);var x,m=g(d[u],e.fontSize,a),v=void 0,y=void 0;"left"==h[u].position?(v=p-h[u].width,y=Math.max(v,v+m+2*e.toolTipPadding)):(v=f,y=Math.max(v+h[u].width,v+m+2*e.toolTipPadding)),x=y-v;var b=v+(x-m)/2,w=t.tooltip.offset.y;a.beginPath(),a.setFillStyle(l(o.labelBgColor||e.toolTipBackground,o.labelBgOpacity||e.toolTipOpacity)),a.setStrokeStyle(o.labelBgColor||e.toolTipBackground),a.setLineWidth(1*t.pix),a.rect(v,w-.5*e.fontSize-e.toolTipPadding,x,e.fontSize+2*e.toolTipPadding),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(e.fontSize),a.setFillStyle(o.labelFontColor||t.fontColor),a.fillText(d[u],b,w+.5*e.fontSize),a.closePath(),a.stroke(),"left"==h[u].position?p-=h[u].width+t.yAxis.padding*t.pix:f+=h[u].width+t.yAxis.padding*t.pix}}function it(t,e,a,i,n){var o=r({},{activeBgColor:"#000000",activeBgOpacity:.08},e.extra.column),s=e.area[0],c=e.height-e.area[2];i.beginPath(),i.setFillStyle(l(o.activeBgColor,o.activeBgOpacity)),i.rect(t-n/2,s,n,c-s),i.closePath(),i.fill(),i.setFillStyle("#FFFFFF")}function nt(t,e,a,i,n,o,s){var c=r({},{showBox:!0,showArrow:!0,bgColor:"#000000",bgOpacity:.7,borderColor:"#000000",borderWidth:0,borderRadius:0,borderOpacity:.7,fontColor:"#FFFFFF",splitLine:!0},a.extra.tooltip),d=4*a.pix,h=5*a.pix,p=c.showArrow?8*a.pix:0,f=!1;"line"!=a.type&&"area"!=a.type&&"candle"!=a.type&&"mix"!=a.type||1==c.splitLine&&function(t,e,a,i){var n=e.extra.tooltip||{};n.gridType=void 0==n.gridType?"solid":n.gridType,n.dashLength=void 0==n.dashLength?4:n.dashLength;var r=e.area[0],o=e.height-e.area[2];if("dash"==n.gridType&&i.setLineDash([n.dashLength,n.dashLength]),i.setStrokeStyle(n.gridColor||"#cccccc"),i.setLineWidth(1*e.pix),i.beginPath(),i.moveTo(t,r),i.lineTo(t,o),i.stroke(),i.setLineDash([]),n.xAxisLabel){var s=e.categories[e.tooltip.index];i.setFontSize(a.fontSize);var c=g(s,a.fontSize,i),d=t-.5*c,h=o;i.beginPath(),i.setFillStyle(l(n.labelBgColor||a.toolTipBackground,n.labelBgOpacity||a.toolTipOpacity)),i.setStrokeStyle(n.labelBgColor||a.toolTipBackground),i.setLineWidth(1*e.pix),i.rect(d-a.toolTipPadding,h,c+2*a.toolTipPadding,a.fontSize+2*a.toolTipPadding),i.closePath(),i.stroke(),i.fill(),i.beginPath(),i.setFontSize(a.fontSize),i.setFillStyle(n.labelFontColor||e.fontColor),i.fillText(String(s),d,h+a.toolTipPadding+a.fontSize),i.closePath(),i.stroke()}}(a.tooltip.offset.x,a,i,n),e=r({x:0,y:0},e),e.y-=8*a.pix;var u=t.map((function(t){return g(t.text,i.fontSize,n)})),x=d+h+4*i.toolTipPadding+Math.max.apply(null,u),m=2*i.toolTipPadding+t.length*i.toolTipLineHeight;if(0!=c.showBox){e.x-Math.abs(a._scrollDistance_||0)+p+x>a.width&&(f=!0),m+e.y>a.height&&(e.y=a.height-m),n.beginPath(),n.setFillStyle(l(c.bgColor||i.toolTipBackground,c.bgOpacity||i.toolTipOpacity)),n.setLineWidth(c.borderWidth*a.pix),n.setStrokeStyle(l(c.borderColor,c.borderOpacity));var v=c.borderRadius;f?(c.showArrow&&(n.moveTo(e.x,e.y+10*a.pix),n.lineTo(e.x-p,e.y+10*a.pix+5*a.pix)),n.arc(e.x-p-v,e.y+m-v,v,0,Math.PI/2,!1),n.arc(e.x-p-Math.round(x)+v,e.y+m-v,v,Math.PI/2,Math.PI,!1),n.arc(e.x-p-Math.round(x)+v,e.y+v,v,-Math.PI,-Math.PI/2,!1),n.arc(e.x-p-v,e.y+v,v,-Math.PI/2,0,!1),c.showArrow&&(n.lineTo(e.x-p,e.y+10*a.pix-5*a.pix),n.lineTo(e.x,e.y+10*a.pix))):(c.showArrow&&(n.moveTo(e.x,e.y+10*a.pix),n.lineTo(e.x+p,e.y+10*a.pix-5*a.pix)),n.arc(e.x+p+v,e.y+v,v,-Math.PI,-Math.PI/2,!1),n.arc(e.x+p+Math.round(x)-v,e.y+v,v,-Math.PI/2,0,!1),n.arc(e.x+p+Math.round(x)-v,e.y+m-v,v,0,Math.PI/2,!1),n.arc(e.x+p+v,e.y+m-v,v,Math.PI/2,Math.PI,!1),c.showArrow&&(n.lineTo(e.x+p,e.y+10*a.pix+5*a.pix),n.lineTo(e.x,e.y+10*a.pix))),n.closePath(),n.fill(),c.borderWidth>0&&n.stroke(),t.forEach((function(t,a){if(null!==t.color){n.beginPath(),n.setFillStyle(t.color);var r=e.x+p+2*i.toolTipPadding,o=e.y+(i.toolTipLineHeight-i.fontSize)/2+i.toolTipLineHeight*a+i.toolTipPadding+1;f&&(r=e.x-x-p+2*i.toolTipPadding),n.fillRect(r,o,d,i.fontSize),n.closePath()}})),t.forEach((function(t,a){var r=e.x+p+2*i.toolTipPadding+d+h;f&&(r=e.x-x-p+2*i.toolTipPadding+ +d+h);var o=e.y+(i.toolTipLineHeight-i.fontSize)/2+i.toolTipLineHeight*a+i.toolTipPadding;n.beginPath(),n.setFontSize(i.fontSize),n.setFillStyle(c.fontColor),n.fillText(t.text,r,o+i.fontSize),n.closePath(),n.stroke()}))}}function rt(t,e,a,i,n,r){var o=t.extra.tooltip||{};o.horizentalLine&&t.tooltip&&1===i&&("line"==t.type||"area"==t.type||"column"==t.type||"candle"==t.type||"mix"==t.type)&&at(t,e,a),a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&a.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===i&&nt(t.tooltip.textList,t.tooltip.offset,t,e,a),a.restore()}function ot(t,e,a,i){var n=e.chartData.xAxisData,r=n.xAxisPoints,o=n.startX,s=n.endX,l=n.eachSpacing,c="center";"line"!=e.type&&"area"!=e.type||(c=e.xAxis.boundaryGap);var d=e.height-e.area[2],h=e.area[0];if(e.enableScroll&&e.xAxis.scrollShow){var p=e.height-e.area[2]+a.xAxisHeight,f=s-o,u=l*(r.length-1),x=f*f/u,m=0;e._scrollDistance_&&(m=-e._scrollDistance_*f/u),i.beginPath(),i.setLineCap("round"),i.setLineWidth(6*e.pix),i.setStrokeStyle(e.xAxis.scrollBackgroundColor||"#EFEBEF"),i.moveTo(o,p),i.lineTo(s,p),i.stroke(),i.closePath(),i.beginPath(),i.setLineCap("round"),i.setLineWidth(6*e.pix),i.setStrokeStyle(e.xAxis.scrollColor||"#A6A6A6"),i.moveTo(o+m,p),i.lineTo(o+m+x,p),i.stroke(),i.closePath(),i.setLineCap("butt")}if(i.save(),e._scrollDistance_&&0!==e._scrollDistance_&&i.translate(e._scrollDistance_,0),!0===e.xAxis.calibration&&(i.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),i.setLineCap("butt"),i.setLineWidth(1*e.pix),r.forEach((function(t,a){a>0&&(i.beginPath(),i.moveTo(t-l/2,d),i.lineTo(t-l/2,d+3*e.pix),i.closePath(),i.stroke())}))),!0!==e.xAxis.disableGrid&&(i.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),i.setLineCap("butt"),i.setLineWidth(1*e.pix),"dash"==e.xAxis.gridType&&i.setLineDash([e.xAxis.dashLength*e.pix,e.xAxis.dashLength*e.pix]),e.xAxis.gridEval=e.xAxis.gridEval||1,r.forEach((function(t,a){a%e.xAxis.gridEval==0&&(i.beginPath(),i.moveTo(t,d),i.lineTo(t,h),i.stroke())})),i.setLineDash([])),!0!==e.xAxis.disabled){var v=t.length;e.xAxis.labelCount&&(v=e.xAxis.itemCount?Math.ceil(t.length/e.xAxis.itemCount*e.xAxis.labelCount):e.xAxis.labelCount,v-=1);for(var y=Math.ceil(t.length/v),b=[],w=t.length,A=0;A<w;A++)A%y!==0?b.push(""):b.push(t[A]);b[w-1]=t[w-1];var C=e.xAxis.fontSize*e.pix||a.fontSize;0===a._xAxisTextAngle_?b.forEach((function(t,n){var o=-g(String(t),C,i)/2;"center"==c&&(o+=l/2);var s=0;e.xAxis.scrollShow&&(s=6*e.pix),i.beginPath(),i.setFontSize(C),i.setFillStyle(e.xAxis.fontColor||e.fontColor),i.fillText(String(t),r[n]+o,d+C+(a.xAxisHeight-s-C)/2),i.closePath(),i.stroke()})):b.forEach((function(t,n){i.save(),i.beginPath(),i.setFontSize(C),i.setFillStyle(e.xAxis.fontColor||e.fontColor);var o=g(String(t),C,i),s=-o;"center"==c&&(s+=l/2);var h=function(t,e,a){var i=t,n=a-e,r=i+(a-n-i)/Math.sqrt(2);r*=-1;var o=(a-n)*(Math.sqrt(2)-1)-(a-n-i)/Math.sqrt(2);return{transX:r,transY:o}}(r[n]+l/2,d+C/2+5,e.height),p=h.transX,f=h.transY;i.rotate(-1*a._xAxisTextAngle_),i.translate(p,f),i.fillText(String(t),r[n]+s,d+C+5),i.closePath(),i.stroke(),i.restore()}))}i.restore(),e.xAxis.axisLine&&(i.beginPath(),i.setStrokeStyle(e.xAxis.axisLineColor),i.setLineWidth(1*e.pix),i.moveTo(o,e.height-e.area[2]),i.lineTo(s,e.height-e.area[2]),i.stroke())}function st(t,e,a,i){if(!0!==e.yAxis.disableGrid){var n=e.height-e.area[0]-e.area[2],r=n/e.yAxis.splitNumber,o=e.area[3],s=e.chartData.xAxisData.xAxisPoints,l=e.chartData.xAxisData.eachSpacing,c=l*(s.length-1),d=o+c,h=[],p=1;!1===e.xAxis.axisLine&&(p=0);for(var f=p;f<e.yAxis.splitNumber+1;f++)h.push(e.height-e.area[2]-r*f);i.save(),e._scrollDistance_&&0!==e._scrollDistance_&&i.translate(e._scrollDistance_,0),"dash"==e.yAxis.gridType&&i.setLineDash([e.yAxis.dashLength*e.pix,e.yAxis.dashLength*e.pix]),i.setStrokeStyle(e.yAxis.gridColor),i.setLineWidth(1*e.pix),h.forEach((function(t,e){i.beginPath(),i.moveTo(o,t),i.lineTo(d,t),i.stroke()})),i.setLineDash([]),i.restore()}}function lt(t,e,a,i){if(!0!==e.yAxis.disabled){var n=e.height-e.area[0]-e.area[2],r=n/e.yAxis.splitNumber,o=e.area[3],s=e.width-e.area[1],l=e.height-e.area[2],c=l+a.xAxisHeight;e.xAxis.scrollShow&&(c-=3*e.pix),e.xAxis.rotateLabel&&(c=e.height-e.area[2]+e.fontSize*e.pix/2),i.beginPath(),i.setFillStyle(e.background),1==e.enableScroll&&e.xAxis.scrollPosition&&"left"!==e.xAxis.scrollPosition&&i.fillRect(0,0,o,c),1==e.enableScroll&&e.xAxis.scrollPosition&&"right"!==e.xAxis.scrollPosition&&i.fillRect(s,0,e.width,c),i.closePath(),i.stroke();for(var d=[],h=0;h<=e.yAxis.splitNumber;h++)d.push(e.area[0]+r*h);var p=e.area[3],f=e.width-e.area[1];if(e.yAxis.data)for(var u=function(t){var n=e.yAxis.data[t];if(!0!==n.disabled){var r=e.chartData.yAxisData.rangesFormat[t],o=n.fontSize*e.pix||a.fontSize,s=e.chartData.yAxisData.yAxisWidth[t],c=n.textAlign||"right";if(r.forEach((function(t,a){var r=d[a]?d[a]:l;i.beginPath(),i.setFontSize(o),i.setLineWidth(1*e.pix),i.setStrokeStyle(n.axisLineColor||"#cccccc"),i.setFillStyle(n.fontColor||e.fontColor);var h=0,u=4*e.pix;if("left"==s.position){switch(1==n.calibration&&(i.moveTo(p,r),i.lineTo(p-3*e.pix,r),u+=3*e.pix),c){case"left":i.setTextAlign("left"),h=p-s.width;break;case"right":i.setTextAlign("right"),h=p-u;break;default:i.setTextAlign("center"),h=p-s.width/2}i.fillText(String(t),h,r+o/2-3*e.pix)}else{switch(1==n.calibration&&(i.moveTo(f,r),i.lineTo(f+3*e.pix,r),u+=3*e.pix),c){case"left":i.setTextAlign("left"),h=f+u;break;case"right":i.setTextAlign("right"),h=f+s.width;break;default:i.setTextAlign("center"),h=f+s.width/2}i.fillText(String(t),h,r+o/2-3*e.pix)}i.closePath(),i.stroke(),i.setTextAlign("left")})),!1!==n.axisLine&&(i.beginPath(),i.setStrokeStyle(n.axisLineColor||"#cccccc"),i.setLineWidth(1*e.pix),"left"==s.position?(i.moveTo(p,e.height-e.area[2]),i.lineTo(p,e.area[0])):(i.moveTo(f,e.height-e.area[2]),i.lineTo(f,e.area[0])),i.stroke()),e.yAxis.showTitle){var h=n.titleFontSize||a.fontSize,u=n.title;i.beginPath(),i.setFontSize(h),i.setFillStyle(n.titleFontColor||e.fontColor),"left"==s.position?i.fillText(u,p-g(u,h,i)/2,e.area[0]-10*e.pix):i.fillText(u,f-g(u,h,i)/2,e.area[0]-10*e.pix),i.closePath(),i.stroke()}"left"==s.position?p-=s.width+e.yAxis.padding*e.pix:f+=s.width+e.yAxis.padding*e.pix}},x=0;x<e.yAxis.data.length;x++)u(x)}}function ct(t,e,a,i,n){if(!1!==e.legend.show){var r=n.legendData,o=r.points,s=r.area,l=e.legend.padding*e.pix,c=e.legend.fontSize*e.pix,d=15*e.pix,h=5*e.pix,p=e.legend.itemGap*e.pix,f=Math.max(e.legend.lineHeight*e.pix,c);i.beginPath(),i.setLineWidth(e.legend.borderWidth*e.pix),i.setStrokeStyle(e.legend.borderColor),i.setFillStyle(e.legend.backgroundColor),i.moveTo(s.start.x,s.start.y),i.rect(s.start.x,s.start.y,s.width,s.height),i.closePath(),i.fill(),i.stroke(),o.forEach((function(t,n){var o,u=0;u=r.widthArr[n],o=r.heightArr[n];var x=0,m=0;"top"==e.legend.position||"bottom"==e.legend.position?(x=s.start.x+(s.width-u)/2,m=s.start.y+l+n*f):(u=0==n?0:r.widthArr[n-1],x=s.start.x+l+u,m=s.start.y+l+(s.height-o)/2),i.setFontSize(a.fontSize);for(var v=0;v<t.length;v++){var y=t[v];switch(y.area=[0,0,0,0],y.area[0]=x,y.area[1]=m,y.area[3]=m+f,i.beginPath(),i.setLineWidth(1*e.pix),i.setStrokeStyle(y.show?y.color:e.legend.hiddenColor),i.setFillStyle(y.show?y.color:e.legend.hiddenColor),y.legendShape){case"line":i.moveTo(x,m+.5*f-2*e.pix),i.fillRect(x,m+.5*f-2*e.pix,15*e.pix,4*e.pix);break;case"triangle":i.moveTo(x+7.5*e.pix,m+.5*f-5*e.pix),i.lineTo(x*****e.pix,m+.5*f+5*e.pix),i.lineTo(x+12.5*e.pix,m+.5*f+5*e.pix),i.lineTo(x+7.5*e.pix,m+.5*f-5*e.pix);break;case"diamond":i.moveTo(x+7.5*e.pix,m+.5*f-5*e.pix),i.lineTo(x*****e.pix,m+.5*f),i.lineTo(x+7.5*e.pix,m+.5*f+5*e.pix),i.lineTo(x+12.5*e.pix,m+.5*f),i.lineTo(x+7.5*e.pix,m+.5*f-5*e.pix);break;case"circle":i.moveTo(x+7.5*e.pix,m+.5*f),i.arc(x+7.5*e.pix,m+.5*f,5*e.pix,0,2*Math.PI);break;case"rect":i.moveTo(x,m+.5*f-5*e.pix),i.fillRect(x,m+.5*f-5*e.pix,15*e.pix,10*e.pix);break;case"square":i.moveTo(x+5*e.pix,m+.5*f-5*e.pix),i.fillRect(x+5*e.pix,m+.5*f-5*e.pix,10*e.pix,10*e.pix);break;case"none":break;default:i.moveTo(x,m+.5*f-5*e.pix),i.fillRect(x,m+.5*f-5*e.pix,15*e.pix,10*e.pix)}i.closePath(),i.fill(),i.stroke(),x+=d+h;var b=.5*f+.5*c-2;i.beginPath(),i.setFontSize(c),i.setFillStyle(y.show?e.legend.fontColor:e.legend.hiddenColor),i.fillText(y.name,x,m+b),i.closePath(),i.stroke(),"top"==e.legend.position||"bottom"==e.legend.position?(x+=g(y.name,c,i)+p,y.area[2]=x):(y.area[2]=x+g(y.name,c,i)+p,x-=d+h,m+=f)}}))}}function dt(t,e,a){a=0==a?1:a;for(var i=[],n=0;n<a;n++)i[n]=Math.random();return Math.floor(i.reduce((function(t,e){return t+e}))/a*(e-t))+t}function ht(t,e,a,i){for(var n=!1,r=0;r<e.length;r++)if(e[r].area){if(!(t[3]<e[r].area[1]||t[0]>e[r].area[2]||t[1]>e[r].area[3]||t[2]<e[r].area[0])){n=!0;break}if(t[0]<0||t[1]<0||t[2]>a||t[3]>i){n=!0;break}n=!1}return n}function pt(t,e,a,i,n,r){return{x:(e-a.xMin)*i+n,y:(a.yMax-t)*i+r}}function ft(t,e,a){if(e[1]==a[1])return!1;if(e[1]>t[1]&&a[1]>t[1])return!1;if(e[1]<t[1]&&a[1]<t[1])return!1;if(e[1]==t[1]&&a[1]>t[1])return!1;if(a[1]==t[1]&&e[1]>t[1])return!1;if(e[0]<t[0]&&a[1]<t[1])return!1;var i=a[0]-(a[0]-e[0])*(a[1]-t[1])/(a[1]-e[1]);return!(i<t[0])}function ut(t,e,a){for(var i=0,n=0;n<e.length;n++){var r=e[n][0];1==e.length&&(r=e[n][0]);for(var o=0;o<r.length-1;o++){var l=r[o],c=r[o+1];a&&(l=s(r[o][0],r[o][1]),c=s(r[o+1][0],r[o+1][1])),ft(t,l,c)&&(i+=1)}}return i%2==1}function xt(t,e,a){var i=t.series.sort((function(t,e){return parseInt(e.textSize)-parseInt(t.textSize)}));switch(e){case"normal":for(var n=0;n<i.length;n++){var r=i[n].name,o=i[n].textSize*t.pix,s=g(r,o,a),l=void 0,c=void 0,d=void 0,h=0;while(1){h++,l=dt(-t.width/2,t.width/2,5)-s/2,c=dt(-t.height/2,t.height/2,5)+o/2,d=[l-5+t.width/2,c-5-o+t.height/2,l+s+5+t.width/2,c+5+t.height/2];var p=ht(d,i,t.width,t.height);if(!p)break;if(1e3==h){d=[-100,-100,-100,-100];break}}i[n].area=d}break;case"vertical":for(var f=function(){return Math.random()>.7},u=0;u<i.length;u++){var x=i[u].name,m=i[u].textSize*t.pix,v=g(x,m,a),y=f(),b=void 0,w=void 0,A=void 0,C=void 0,S=0;while(1){S++;var T=void 0;if(y?(b=dt(-t.width/2,t.width/2,5)-v/2,w=dt(-t.height/2,t.height/2,5)+m/2,A=[w-5-v+t.width/2,-b-5+t.height/2,w+5+t.width/2,-b+m+5+t.height/2],C=[t.width-(t.width/2-t.height/2)-(-b+m+5+t.height/2)-5,t.height/2-t.width/2+(w-5-v+t.width/2)-5,t.width-(t.width/2-t.height/2)-(-b+m+5+t.height/2)+m,t.height/2-t.width/2+(w-5-v+t.width/2)+v+5],T=ht(C,i,t.height,t.width)):(b=dt(-t.width/2,t.width/2,5)-v/2,w=dt(-t.height/2,t.height/2,5)+m/2,A=[b-5+t.width/2,w-5-m+t.height/2,b+v+5+t.width/2,w+5+t.height/2],T=ht(A,i,t.width,t.height)),!T)break;if(1e3==S){A=[-1e3,-1e3,-1e3,-1e3];break}}y?(i[u].area=C,i[u].areav=A):i[u].area=A,i[u].rotate=y}break}return i}function mt(t,e,a,i,n,r,s){for(var l=0;l<t.length;l++){var c=t[l],d=void 0,h=void 0,p=void 0,f=void 0,u=c.formatter?c.formatter(c,l,t):o.toFixed(100*c._proportion_)+"%";"right"==n?(d=0==l?(c.funnelArea[2]+s.x)/2:(c.funnelArea[2]+t[l-1].funnelArea[2])/2,h=d+2*r,p=c.funnelArea[1]+i/2,f=c.textSize*e.pix||e.fontSize*e.pix,a.setLineWidth(1*e.pix),a.setStrokeStyle(c.color),a.setFillStyle(c.color),a.beginPath(),a.moveTo(d,p),a.lineTo(h,p),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(h,p),a.arc(h,p,2,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(f),a.setFillStyle(c.textColor||e.fontColor),a.fillText(u,h+5,p+f/2-2),a.closePath(),a.stroke(),a.closePath()):(d=0==l?(c.funnelArea[0]+s.x)/2:(c.funnelArea[0]+t[l-1].funnelArea[0])/2,h=d-2*r,p=c.funnelArea[1]+i/2,f=c.textSize*e.pix||e.fontSize*e.pix,a.setLineWidth(1*e.pix),a.setStrokeStyle(c.color),a.setFillStyle(c.color),a.beginPath(),a.moveTo(d,p),a.lineTo(h,p),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(h,p),a.arc(h,p,2,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(f),a.setFillStyle(c.textColor||e.fontColor),a.fillText(u,h-5-g(u,f,a),p+f/2-2),a.closePath(),a.stroke(),a.closePath())}}function gt(t,e){e.draw()}var vt={easeIn:function(t){return Math.pow(t,3)},easeOut:function(t){return Math.pow(t-1,3)+1},easeInOut:function(t){return(t/=.5)<1?.5*Math.pow(t,3):.5*(Math.pow(t-2,3)+2)},linear:function(t){return t}};function yt(t){this.isStop=!1,t.duration="undefined"===typeof t.duration?1e3:t.duration,t.timing=t.timing||"easeInOut";var e=function(){return"undefined"!==typeof setTimeout?function(t,e){setTimeout((function(){var e=+new Date;t(e)}),e)}:"undefined"!==typeof requestAnimationFrame?requestAnimationFrame:function(t){t(null)}}(),a=null,i=function(n){if(null===n||!0===this.isStop)return t.onProcess&&t.onProcess(1),void(t.onAnimationFinish&&t.onAnimationFinish());if(null===a&&(a=n),n-a<t.duration){var r=(n-a)/t.duration,o=vt[t.timing];r=o(r),t.onProcess&&t.onProcess(r),e(i,17)}else t.onProcess&&t.onProcess(1),t.onAnimationFinish&&t.onAnimationFinish()};i=i.bind(this),e(i,17)}function bt(t,a,i,n){var c=this,d=a.series;"pie"!==t&&"ring"!==t&&"rose"!==t&&"funnel"!==t||(d=function(t,e,a){var i=[];if(t[0].data.constructor.toString().indexOf("Array")>-1){e._pieSeries_=t;for(var n=t[0].data,r=0;r<n.length;r++)n[r].formatter=t[0].formatter,n[r].data=n[r].value,i.push(n[r]);e.series=i}else i=t;return i}(d,a));var f=a.categories;d=u(d,a,i);var m=a.animation?a.duration:0;c.animationInstance&&c.animationInstance.stop();var v=null;if("candle"==t){var y=r({},a.extra.candle.average);y.show?(v=function(t,e,a,i){for(var n=[],r=0;r<t.length;r++){for(var o={data:[],name:e[r],color:a[r]},s=0,l=i.length;s<l;s++)if(s<t[r])o.data.push(null);else{for(var c=0,d=0;d<t[r];d++)c+=i[s-d][1];o.data.push(+(c/t[r]).toFixed(3))}n.push(o)}return n}(y.day,y.name,y.color,d[0].data),v=u(v,a,i),a.seriesMA=v):v=a.seriesMA?a.seriesMA=u(a.seriesMA,a,i):d}else v=d;a._series_=d=function(t){for(var e=[],a=0;a<t.length;a++)1==t[a].show&&e.push(t[a]);return e}(d),a.area=new Array(4);for(var b=0;b<4;b++)a.area[b]=a.padding[b]*a.pix;var w=function(t,e,a,i,n){var r={area:{start:{x:0,y:0},end:{x:0,y:0},width:0,height:0,wholeWidth:0,wholeHeight:0},points:[],widthArr:[],heightArr:[]};if(!1===e.legend.show)return i.legendData=r,r;var o=e.legend.padding*e.pix,s=e.legend.margin*e.pix,l=e.legend.fontSize*e.pix,c=15*e.pix,d=5*e.pix,h=Math.max(e.legend.lineHeight*e.pix,l);if("top"==e.legend.position||"bottom"==e.legend.position){for(var p=[],f=0,u=[],x=[],m=0;m<t.length;m++){var v=t[m],y=c+d+g(v.name||"undefined",l,n)+e.legend.itemGap*e.pix;f+y>e.width-e.area[1]-e.area[3]?(p.push(x),u.push(f-e.legend.itemGap*e.pix),f=y,x=[v]):(f+=y,x.push(v))}if(x.length){p.push(x),u.push(f-e.legend.itemGap*e.pix),r.widthArr=u;var b=Math.max.apply(null,u);switch(e.legend.float){case"left":r.area.start.x=e.area[3],r.area.end.x=e.area[3]+b+2*o;break;case"right":r.area.start.x=e.width-e.area[1]-b-2*o,r.area.end.x=e.width-e.area[1];break;default:r.area.start.x=(e.width-b)/2-o,r.area.end.x=(e.width+b)/2+o}r.area.width=b+2*o,r.area.wholeWidth=b+2*o,r.area.height=p.length*h+2*o,r.area.wholeHeight=p.length*h+2*o+2*s,r.points=p}}else{var w=t.length,A=e.height-e.area[0]-e.area[2]-2*s-2*o,C=Math.min(Math.floor(A/h),w);switch(r.area.height=C*h+2*o,r.area.wholeHeight=C*h+2*o,e.legend.float){case"top":r.area.start.y=e.area[0]+s,r.area.end.y=e.area[0]+s+r.area.height;break;case"bottom":r.area.start.y=e.height-e.area[2]-s-r.area.height,r.area.end.y=e.height-e.area[2]-s;break;default:r.area.start.y=(e.height-r.area.height)/2,r.area.end.y=(e.height+r.area.height)/2}for(var S=w%C===0?w/C:Math.floor(w/C+1),T=[],k=0;k<S;k++){var D=t.slice(k*C,k*C+C);T.push(D)}if(r.points=T,T.length){for(var P=0;P<T.length;P++){for(var _=T[P],M=0,L=0;L<_.length;L++){var F=c+d+g(_[L].name||"undefined",l,n)+e.legend.itemGap*e.pix;F>M&&(M=F)}r.widthArr.push(M),r.heightArr.push(_.length*h+2*o)}for(var O=0,E=0;E<r.widthArr.length;E++)O+=r.widthArr[E];r.area.width=O-e.legend.itemGap*e.pix+2*o,r.area.wholeWidth=r.area.width+o}}switch(e.legend.position){case"top":r.area.start.y=e.area[0]+s,r.area.end.y=e.area[0]+s+r.area.height;break;case"bottom":r.area.start.y=e.height-e.area[2]-r.area.height-s,r.area.end.y=e.height-e.area[2]-s;break;case"left":r.area.start.x=e.area[3],r.area.end.x=e.area[3]+r.area.width;break;case"right":r.area.start.x=e.width-e.area[1]-r.area.width,r.area.end.x=e.width-e.area[1];break}return i.legendData=r,r}(v,a,0,a.chartData,n),S=w.area.wholeHeight,T=w.area.wholeWidth;switch(a.legend.position){case"top":a.area[0]+=S;break;case"bottom":a.area[2]+=S;break;case"left":a.area[3]+=T;break;case"right":a.area[1]+=T;break}var k={},D=0;if("line"===a.type||"column"===a.type||"area"===a.type||"mix"===a.type||"candle"===a.type){if(k=q(d,a,i,n),D=k.yAxisWidth,a.yAxis.showTitle){for(var P=0,X=0;X<a.yAxis.data.length;X++)P=Math.max(P,a.yAxis.data[X].titleFontSize?a.yAxis.data[X].titleFontSize:i.fontSize);a.area[0]+=(P+6)*a.pix}for(var at=0,nt=0,dt=0;dt<D.length;dt++)"left"==D[dt].position?(a.area[3]+=nt>0?D[dt].width+a.yAxis.padding*a.pix:D[dt].width,nt+=1):(a.area[1]+=at>0?D[dt].width+a.yAxis.padding*a.pix:D[dt].width,at+=1)}else i.yAxisWidth=D;if(a.chartData.yAxisData=k,a.categories&&a.categories.length&&"radar"!==a.type&&"gauge"!==a.type){a.chartData.xAxisData=j(a.categories,a);var ht=M(a.categories,a,i,a.chartData.xAxisData.eachSpacing,n),ft=ht.xAxisHeight,ut=ht.angle;i.xAxisHeight=ft,i._xAxisTextAngle_=ut,a.area[2]+=ft,a.chartData.categoriesData=ht}else if("line"===a.type||"area"===a.type||"points"===a.type){a.chartData.xAxisData=L(d,a,i,n),f=a.chartData.xAxisData.rangesFormat;var vt=M(f,a,i,a.chartData.xAxisData.eachSpacing,n),bt=vt.xAxisHeight,wt=vt.angle;i.xAxisHeight=bt,i._xAxisTextAngle_=wt,a.area[2]+=bt,a.chartData.categoriesData=vt}else a.chartData.xAxisData={xAxisPoints:[]};if(a.enableScroll&&"right"==a.xAxis.scrollAlign&&void 0===a._scrollDistance_){var At,Ct=a.chartData.xAxisData.xAxisPoints,St=a.chartData.xAxisData.startX,Tt=a.chartData.xAxisData.endX,kt=a.chartData.xAxisData.eachSpacing,Dt=kt*(Ct.length-1),Pt=Tt-St;At=Pt-Dt,c.scrollOption={currentOffset:At,startTouchX:At,distance:0,lastMoveTime:0},a._scrollDistance_=At}switch("pie"!==t&&"ring"!==t&&"rose"!==t||(i._pieTextMaxLength_=!1===a.dataLabel?0:function(t,e,a){t=O(t);for(var i=0,n=0;n<t.length;n++){var r=t[n],s=r.formatter?r.formatter(+r._proportion_.toFixed(2)):o.toFixed(100*r._proportion_)+"%";i=Math.max(i,g(s,e.fontSize,a))}return i}(v,i,n)),t){case"word":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),function(t,e,a,i){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=r({},{type:"normal",autoColors:!0},e.extra.word);e.chartData.wordCloudData||(e.chartData.wordCloudData=xt(e,o.type,i)),i.beginPath(),i.setFillStyle(e.background),i.rect(0,0,e.width,e.height),i.fill(),i.save();var s=e.chartData.wordCloudData;i.translate(e.width/2,e.height/2);for(var l=0;l<s.length;l++){i.save(),s[l].rotate&&i.rotate(90*Math.PI/180);var c=s[l].name,d=s[l].textSize*e.pix,h=g(c,d,i);i.beginPath(),i.setStrokeStyle(s[l].color),i.setFillStyle(s[l].color),i.setFontSize(d),s[l].rotate?s[l].areav[0]>0&&(e.tooltip&&e.tooltip.index==l?i.strokeText(c,(s[l].areav[0]+5-e.width/2)*n-h*(1-n)/2,(s[l].areav[1]+5+d-e.height/2)*n):i.fillText(c,(s[l].areav[0]+5-e.width/2)*n-h*(1-n)/2,(s[l].areav[1]+5+d-e.height/2)*n)):s[l].area[0]>0&&(e.tooltip&&e.tooltip.index==l?i.strokeText(c,(s[l].area[0]+5-e.width/2)*n-h*(1-n)/2,(s[l].area[1]+5+d-e.height/2)*n):i.fillText(c,(s[l].area[0]+5-e.width/2)*n-h*(1-n)/2,(s[l].area[1]+5+d-e.height/2)*n)),i.stroke(),i.restore()}i.restore()}(d,a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"map":n.clearRect(0,0,a.width,a.height),function(t,e,a,i){var n,o,c=r({},{border:!0,mercator:!1,borderWidth:1,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#f04864",activeFillColor:"#facc14",activeFillOpacity:1},e.extra.map),d=t,h=function(t){for(var e,a={xMin:180,xMax:0,yMin:90,yMax:0},i=0;i<t.length;i++)for(var n=t[i].geometry.coordinates,r=0;r<n.length;r++){e=n[r],1==e.length&&(e=e[0]);for(var o=0;o<e.length;o++){var s=e[o][0],l=e[o][1],c={x:s,y:l};a.xMin=a.xMin<c.x?a.xMin:c.x,a.xMax=a.xMax>c.x?a.xMax:c.x,a.yMin=a.yMin<c.y?a.yMin:c.y,a.yMax=a.yMax>c.y?a.yMax:c.y}}return a}(d);if(c.mercator){var p=s(h.xMax,h.yMax),f=s(h.xMin,h.yMin);h.xMax=p[0],h.yMax=p[1],h.xMin=f[0],h.yMin=f[1]}for(var u=e.width/Math.abs(h.xMax-h.xMin),x=e.height/Math.abs(h.yMax-h.yMin),m=u<x?u:x,v=e.width/2-Math.abs(h.xMax-h.xMin)/2*m,y=e.height/2-Math.abs(h.yMax-h.yMin)/2*m,b=0;b<d.length;b++){i.beginPath(),i.setLineWidth(c.borderWidth*e.pix),i.setStrokeStyle(c.borderColor),i.setFillStyle(l(t[b].color,c.fillOpacity)),e.tooltip&&e.tooltip.index==b&&(i.setStrokeStyle(c.activeBorderColor),i.setFillStyle(l(c.activeFillColor,c.activeFillOpacity)));for(var w=d[b].geometry.coordinates,A=0;A<w.length;A++){n=w[A],1==n.length&&(n=n[0]);for(var C=0;C<n.length;C++){var S=Array(2);S=c.mercator?s(n[C][0],n[C][1]):n[C],o=pt(S[1],S[0],h,m,v,y),0===C?(i.beginPath(),i.moveTo(o.x,o.y)):i.lineTo(o.x,o.y)}i.fill(),1==c.border&&i.stroke()}if(1==e.dataLabel){var T=d[b].properties.centroid;if(T){c.mercator&&(T=s(d[b].properties.centroid[0],d[b].properties.centroid[1])),o=pt(T[1],T[0],h,m,v,y);var k=d[b].textSize||a.fontSize,D=d[b].properties.name;i.beginPath(),i.setFontSize(k),i.setFillStyle(d[b].textColor||e.fontColor),i.fillText(D,o.x-g(D,k,i)/2,o.y+k/2),i.closePath(),i.stroke()}}}e.chartData.mapData={bounds:h,scale:m,xoffset:v,yoffset:y,mercator:c.mercator},rt(e,a,i,1),i.draw()}(d,a,i,n);break;case"funnel":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),a.chartData.funnelData=function(t,e,a,i){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=r({},{activeWidth:10,activeOpacity:.3,border:!1,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,labelAlign:"right",linearType:"none",customColor:[]},e.extra.funnel),s=(e.height-e.area[0]-e.area[2])/t.length,c={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.height-e.area[2]},d=o.activeWidth*e.pix,h=Math.min((e.width-e.area[1]-e.area[3])/2-d,(e.height-e.area[0]-e.area[2])/2-d);t=E(t,h,n),i.save(),i.translate(c.x,c.y),o.customColor=x(o.linearType,o.customColor,t,a);for(var p=0;p<t.length;p++){if(0==p){e.tooltip&&e.tooltip.index==p&&(i.beginPath(),i.setFillStyle(l(t[p].color,o.activeOpacity)),i.moveTo(-d,0),i.lineTo(-t[p].radius-d,-s),i.lineTo(t[p].radius+d,-s),i.lineTo(d,0),i.lineTo(-d,0),i.closePath(),i.fill()),t[p].funnelArea=[c.x-t[p].radius,c.y-s,c.x+t[p].radius,c.y],i.beginPath(),i.setLineWidth(o.borderWidth*e.pix),i.setStrokeStyle(o.borderColor);var f=l(t[p].color,o.fillOpacity);if("custom"==o.linearType){var u=i.createLinearGradient(t[p].radius,-s,-t[p].radius,-s);u.addColorStop(0,l(t[p].color,o.fillOpacity)),u.addColorStop(.5,l(o.customColor[t[p].linearIndex],o.fillOpacity)),u.addColorStop(1,l(t[p].color,o.fillOpacity)),f=u}i.setFillStyle(f),i.moveTo(0,0),i.lineTo(-t[p].radius,-s),i.lineTo(t[p].radius,-s),i.lineTo(0,0),i.closePath(),i.fill(),1==o.border&&i.stroke()}else{e.tooltip&&e.tooltip.index==p&&(i.beginPath(),i.setFillStyle(l(t[p].color,o.activeOpacity)),i.moveTo(0,0),i.lineTo(-t[p-1].radius-d,0),i.lineTo(-t[p].radius-d,-s),i.lineTo(t[p].radius+d,-s),i.lineTo(t[p-1].radius+d,0),i.lineTo(0,0),i.closePath(),i.fill()),t[p].funnelArea=[c.x-t[p].radius,c.y-s*(p+1),c.x+t[p].radius,c.y-s*p],i.beginPath(),i.setLineWidth(o.borderWidth*e.pix),i.setStrokeStyle(o.borderColor);f=l(t[p].color,o.fillOpacity);if("custom"==o.linearType){u=i.createLinearGradient(t[p].radius,-s,-t[p].radius,-s);u.addColorStop(0,l(t[p].color,o.fillOpacity)),u.addColorStop(.5,l(o.customColor[t[p].linearIndex],o.fillOpacity)),u.addColorStop(1,l(t[p].color,o.fillOpacity)),f=u}i.setFillStyle(f),i.moveTo(0,0),i.lineTo(-t[p-1].radius,0),i.lineTo(-t[p].radius,-s),i.lineTo(t[p].radius,-s),i.lineTo(t[p-1].radius,0),i.lineTo(0,0),i.closePath(),i.fill(),1==o.border&&i.stroke()}i.translate(0,-s)}return i.restore(),!1!==e.dataLabel&&1===n&&mt(t,e,i,s,o.labelAlign,d,c),{center:c,radius:h,series:t}}(d,a,i,n,t),ct(a.series,a,i,n,a.chartData),rt(a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"line":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),st(0,a,0,n),ot(f,a,i,n);var e=function(t,e,a,i){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=r({},{type:"straight",width:2},e.extra.line);o.width*=e.pix;var s=e.chartData.xAxisData,l=s.xAxisPoints,c=s.eachSpacing,d=[];i.save();var p=0,f=e.width+c;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),p=-e._scrollDistance_-2*c+e.area[3],f=p+(e.xAxis.itemCount+4)*c),t.forEach((function(t,r){var s,u,x;s=[].concat(e.chartData.yAxisData.ranges[t.index]),u=s.pop(),x=s.shift();var m=t.data,g=H(m,u,x,l,c,e,a,n);d.push(g);var v=_(g,t);if("dash"==t.lineType){var y=t.dashLength?t.dashLength:8;y*=e.pix,i.setLineDash([y,y])}i.beginPath(),i.setStrokeStyle(t.color),i.setLineWidth(o.width),v.forEach((function(t,e){if(1===t.length)i.moveTo(t[0].x,t[0].y),i.arc(t[0].x,t[0].y,1,0,2*Math.PI);else{i.moveTo(t[0].x,t[0].y);var a=0;if("curve"===o.type)for(var n=0;n<t.length;n++){var r=t[n];if(0==a&&r.x>p&&(i.moveTo(r.x,r.y),a=1),n>0&&r.x>p&&r.x<f){var s=h(t,n-1);i.bezierCurveTo(s.ctrA.x,s.ctrA.y,s.ctrB.x,s.ctrB.y,r.x,r.y)}}if("straight"===o.type)for(var l=0;l<t.length;l++){var c=t[l];0==a&&c.x>p&&(i.moveTo(c.x,c.y),a=1),l>0&&c.x>p&&c.x<f&&i.lineTo(c.x,c.y)}if("step"===o.type)for(var d=0;d<t.length;d++){var u=t[d];0==a&&u.x>p&&(i.moveTo(u.x,u.y),a=1),d>0&&u.x>p&&u.x<f&&(i.lineTo(u.x,t[d-1].y),i.lineTo(u.x,u.y))}i.moveTo(t[0].x,t[0].y)}})),i.stroke(),i.setLineDash([]),!1!==e.dataPointShape&&U(g,t.color,t.pointShape,i,e)})),!1!==e.dataLabel&&1===n&&t.forEach((function(t,r){var o,s,d;o=[].concat(e.chartData.yAxisData.ranges[t.index]),s=o.pop(),d=o.shift();var h=t.data,p=H(h,s,d,l,c,e,a,n);V(p,t,a,i,e)})),i.restore(),{xAxisPoints:l,calPoints:d,eachSpacing:c}}(d,a,i,n,t),o=e.xAxisPoints,s=e.calPoints,l=e.eachSpacing;a.chartData.xAxisPoints=o,a.chartData.calPoints=s,a.chartData.eachSpacing=l,lt(0,a,i,n),!1!==a.enableMarkLine&&1===t&&et(a,i,n),ct(a.series,a,i,n,a.chartData),rt(a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"mix":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),st(0,a,0,n),ot(f,a,i,n);var o=function(t,a,i,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,s=r({},{width:p/2,barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},a.extra.mix.column),c=a.chartData.xAxisData,d=c.xAxisPoints,p=c.eachSpacing,f=a.height-a.area[2],u=[],m=0,g=0;t.forEach((function(t,e){"column"==t.type&&(g+=1)})),n.save();var v=-2,y=d.length+2,b=0,w=a.width+p;if(a._scrollDistance_&&0!==a._scrollDistance_&&!0===a.enableScroll&&(n.translate(a._scrollDistance_,0),v=Math.floor(-a._scrollDistance_/p)-2,y=v+a.xAxis.itemCount+4,b=-a._scrollDistance_-2*p+a.area[3],w=b+(a.xAxis.itemCount+4)*p),s.customColor=x(s.linearType,s.customColor,t,i),t.forEach((function(t,r){var c,x,A;c=[].concat(a.chartData.yAxisData.ranges[t.index]),x=c.pop(),A=c.shift();var C=t.data,S=H(C,x,A,d,p,a,i,o);if(u.push(S),"column"==t.type){S=R(S,p,g,m,i,a);for(var T=0;T<S.length;T++){var k=S[T];if(null!==k&&T>v&&T<y){var D=k.x-k.width/2;a.height,k.y,a.area[2];n.beginPath();var P=k.color||t.color,M=k.color||t.color;if("none"!==s.linearType){var L=n.createLinearGradient(D,k.y,D,a.height-a.area[2]);"opacity"==s.linearType?(L.addColorStop(0,l(P,s.linearOpacity)),L.addColorStop(1,l(P,1))):(L.addColorStop(0,l(s.customColor[t.linearIndex],s.linearOpacity)),L.addColorStop(s.colorStop,l(s.customColor[t.linearIndex],s.linearOpacity)),L.addColorStop(1,l(P,1))),P=L}if(s.barBorderRadius&&4===s.barBorderRadius.length||s.barBorderCircle){var F=D,O=k.y,E=k.width,I=a.height-a.area[2]-k.y;s.barBorderCircle&&(s.barBorderRadius=[E/2,E/2,0,0]);var z=e(s.barBorderRadius,4),B=z[0],W=z[1],N=z[2],Y=z[3];B+N>I&&(B=I,N=0,W=I,Y=0),B+N>E/2&&(B=E/2,N=0,W=E/2,Y=0),B=B<0?0:B,W=W<0?0:W,N=N<0?0:N,Y=Y<0?0:Y,n.arc(F+B,O+B,B,-Math.PI,-Math.PI/2),n.arc(F+E-W,O+W,W,-Math.PI/2,0),n.arc(F+E-N,O+I-N,N,0,Math.PI/2),n.arc(F+Y,O+I-Y,Y,Math.PI/2,Math.PI)}else n.moveTo(D,k.y),n.lineTo(D+k.width-2,k.y),n.lineTo(D+k.width-2,a.height-a.area[2]),n.lineTo(D,a.height-a.area[2]),n.lineTo(D,k.y),n.setLineWidth(1),n.setStrokeStyle(M);n.setFillStyle(P),n.closePath(),n.fill()}}m+=1}if("area"==t.type)for(var j=_(S,t),G=0;G<j.length;G++){var J=j[G];if(n.beginPath(),n.setStrokeStyle(t.color),n.setFillStyle(l(t.color,.2)),n.setLineWidth(2*a.pix),J.length>1){var X=J[0],q=J[J.length-1];n.moveTo(X.x,X.y);var Q=0;if("curve"===t.style)for(var Z=0;Z<J.length;Z++){var V=J[Z];if(0==Q&&V.x>b&&(n.moveTo(V.x,V.y),Q=1),Z>0&&V.x>b&&V.x<w){var K=h(J,Z-1);n.bezierCurveTo(K.ctrA.x,K.ctrA.y,K.ctrB.x,K.ctrB.y,V.x,V.y)}}else for(var $=0;$<J.length;$++){var tt=J[$];0==Q&&tt.x>b&&(n.moveTo(tt.x,tt.y),Q=1),$>0&&tt.x>b&&tt.x<w&&n.lineTo(tt.x,tt.y)}n.lineTo(q.x,f),n.lineTo(X.x,f),n.lineTo(X.x,X.y)}else{var et=J[0];n.moveTo(et.x-p/2,et.y),n.lineTo(et.x+p/2,et.y),n.lineTo(et.x+p/2,f),n.lineTo(et.x-p/2,f),n.moveTo(et.x-p/2,et.y)}n.closePath(),n.fill()}if("line"==t.type){var at=_(S,t);at.forEach((function(e,i){if("dash"==t.lineType){var r=t.dashLength?t.dashLength:8;r*=a.pix,n.setLineDash([r,r])}if(n.beginPath(),n.setStrokeStyle(t.color),n.setLineWidth(2*a.pix),1===e.length)n.moveTo(e[0].x,e[0].y),n.arc(e[0].x,e[0].y,1,0,2*Math.PI);else{n.moveTo(e[0].x,e[0].y);var o=0;if("curve"==t.style)for(var s=0;s<e.length;s++){var l=e[s];if(0==o&&l.x>b&&(n.moveTo(l.x,l.y),o=1),s>0&&l.x>b&&l.x<w){var c=h(e,s-1);n.bezierCurveTo(c.ctrA.x,c.ctrA.y,c.ctrB.x,c.ctrB.y,l.x,l.y)}}else for(var d=0;d<e.length;d++){var p=e[d];0==o&&p.x>b&&(n.moveTo(p.x,p.y),o=1),d>0&&p.x>b&&p.x<w&&n.lineTo(p.x,p.y)}n.moveTo(e[0].x,e[0].y)}n.stroke(),n.setLineDash([])}))}"point"==t.type&&(t.addPoint=!0),1==t.addPoint&&"column"!==t.type&&U(S,t.color,t.pointShape,n,a)})),!1!==a.dataLabel&&1===o){m=0;t.forEach((function(t,e){var r,s,l;r=[].concat(a.chartData.yAxisData.ranges[t.index]),s=r.pop(),l=r.shift();var c=t.data,h=H(c,s,l,d,p,a,i,o);"column"!==t.type?V(h,t,i,n,a):(h=R(h,p,g,m,i,a),V(h,t,i,n,a),m+=1)}))}return n.restore(),{xAxisPoints:d,calPoints:u,eachSpacing:p}}(d,a,i,n,t),s=o.xAxisPoints,c=o.calPoints,p=o.eachSpacing;a.chartData.xAxisPoints=s,a.chartData.calPoints=c,a.chartData.eachSpacing=p,lt(0,a,i,n),!1!==a.enableMarkLine&&1===t&&et(a,i,n),ct(a.series,a,i,n,a.chartData),rt(a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"column":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),st(0,a,0,n),ot(f,a,i,n);var o=function(t,a,i,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,s=a.chartData.xAxisData,c=s.xAxisPoints,d=s.eachSpacing,h=r({},{type:"group",width:d/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},a.extra.column),p=[];n.save();var f=-2,u=c.length+2;return a._scrollDistance_&&0!==a._scrollDistance_&&!0===a.enableScroll&&(n.translate(a._scrollDistance_,0),f=Math.floor(-a._scrollDistance_/d)-2,u=f+a.xAxis.itemCount+4),a.tooltip&&a.tooltip.textList&&a.tooltip.textList.length&&1===o&&it(a.tooltip.offset.x,a,0,n,d),h.customColor=x(h.linearType,h.customColor,t,i),t.forEach((function(r,s){var x,m,g;x=[].concat(a.chartData.yAxisData.ranges[r.index]),m=x.pop(),g=x.shift();var v=r.data;switch(h.type){case"group":var y=H(v,m,g,c,d,a,i,o),b=J(v,m,g,c,d,a,i,s,t,o);p.push(b),y=R(y,d,t.length,s,i,a);for(var w=0;w<y.length;w++){var A=y[w];if(null!==A&&w>f&&w<u){var C=A.x-A.width/2,S=a.height-A.y-a.area[2];n.beginPath();var T=A.color||r.color,k=A.color||r.color;if("none"!==h.linearType){var D=n.createLinearGradient(C,A.y,C,a.height-a.area[2]);"opacity"==h.linearType?(D.addColorStop(0,l(T,h.linearOpacity)),D.addColorStop(1,l(T,1))):(D.addColorStop(0,l(h.customColor[r.linearIndex],h.linearOpacity)),D.addColorStop(h.colorStop,l(h.customColor[r.linearIndex],h.linearOpacity)),D.addColorStop(1,l(T,1))),T=D}if(h.barBorderRadius&&4===h.barBorderRadius.length||!0===h.barBorderCircle){var P=C,_=A.y,M=A.width,L=a.height-a.area[2]-A.y;h.barBorderCircle&&(h.barBorderRadius=[M/2,M/2,0,0]);var F=e(h.barBorderRadius,4),O=F[0],E=F[1],I=F[2],z=F[3];O+I>L&&(O=L,I=0,E=L,z=0),O+I>M/2&&(O=M/2,I=0,E=M/2,z=0),O=O<0?0:O,E=E<0?0:E,I=I<0?0:I,z=z<0?0:z,n.arc(P+O,_+O,O,-Math.PI,-Math.PI/2),n.arc(P+M-E,_+E,E,-Math.PI/2,0),n.arc(P+M-I,_+L-I,I,0,Math.PI/2),n.arc(P+z,_+L-z,z,Math.PI/2,Math.PI)}else n.moveTo(C,A.y),n.lineTo(C+A.width-2,A.y),n.lineTo(C+A.width-2,a.height-a.area[2]),n.lineTo(C,a.height-a.area[2]),n.lineTo(C,A.y),n.setLineWidth(1),n.setStrokeStyle(k);n.setFillStyle(T),n.closePath(),n.fill()}}break;case"stack":y=J(v,m,g,c,d,a,i,s,t,o);p.push(y),y=Y(y,d,t.length,0,i,a);for(var B=0;B<y.length;B++){var W=y[B];if(null!==W&&B>f&&B<u){n.beginPath();T=W.color||r.color,C=W.x-W.width/2+1,S=a.height-W.y-a.area[2];var j=a.height-W.y0-a.area[2];s>0&&(S-=j),n.setFillStyle(T),n.moveTo(C,W.y),n.fillRect(C,W.y,W.width-2,S),n.closePath(),n.fill()}}break;case"meter":y=H(v,m,g,c,d,a,i,o);if(p.push(y),y=N(y,d,t.length,s,i,a,h.meterBorder),0==s)for(var G=0;G<y.length;G++){var X=y[G];if(null!==X&&G>f&&G<u){n.beginPath(),n.setFillStyle(h.meterFillColor);C=X.x-X.width/2,S=a.height-X.y-a.area[2];n.moveTo(C,X.y),n.fillRect(C,X.y,X.width,S),n.closePath(),n.fill(),h.meterBorder>0&&(n.beginPath(),n.setStrokeStyle(r.color),n.setLineWidth(h.meterBorder*a.pix),n.moveTo(C+.5*h.meterBorder,X.y+S),n.lineTo(C+.5*h.meterBorder,X.y+.5*h.meterBorder),n.lineTo(C+X.width-.5*h.meterBorder,X.y+.5*h.meterBorder),n.lineTo(C+X.width-.5*h.meterBorder,X.y+S),n.stroke())}}else for(var q=0;q<y.length;q++){var Q=y[q];if(null!==Q&&q>f&&q<u){n.beginPath(),n.setFillStyle(Q.color||r.color);C=Q.x-Q.width/2,S=a.height-Q.y-a.area[2];n.moveTo(C,Q.y),n.fillRect(C,Q.y,Q.width,S),n.closePath(),n.fill()}}break}})),!1!==a.dataLabel&&1===o&&t.forEach((function(e,r){var s,l,p;s=[].concat(a.chartData.yAxisData.ranges[e.index]),l=s.pop(),p=s.shift();var f=e.data;switch(h.type){case"group":var u=H(f,l,p,c,d,a,i,o);u=R(u,d,t.length,r,i,a),V(u,e,i,n,a);break;case"stack":u=J(f,l,p,c,d,a,i,r,t,o);V(u,e,i,n,a);break;case"meter":u=H(f,l,p,c,d,a,i,o);V(u,e,i,n,a);break}})),n.restore(),{xAxisPoints:c,calPoints:p,eachSpacing:d}}(d,a,i,n,t),s=o.xAxisPoints,c=o.calPoints,h=o.eachSpacing;a.chartData.xAxisPoints=s,a.chartData.calPoints=c,a.chartData.eachSpacing=h,lt(0,a,i,n),!1!==a.enableMarkLine&&1===t&&et(a,i,n),ct(a.series,a,i,n,a.chartData),rt(a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"area":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),st(0,a,0,n),ot(f,a,i,n);var e=function(t,e,a,i){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=r({},{type:"straight",opacity:.2,addLine:!1,width:2,gradient:!1},e.extra.area),s=e.chartData.xAxisData,c=s.xAxisPoints,d=s.eachSpacing,p=e.height-e.area[2],f=[];i.save();var u=0,x=e.width+d;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(i.translate(e._scrollDistance_,0),u=-e._scrollDistance_-2*d+e.area[3],x=u+(e.xAxis.itemCount+4)*d),t.forEach((function(t,r){var s,m,g;s=[].concat(e.chartData.yAxisData.ranges[t.index]),m=s.pop(),g=s.shift();var v=t.data,y=H(v,m,g,c,d,e,a,n);f.push(y);for(var b=_(y,t),w=0;w<b.length;w++){var A=b[w];if(i.beginPath(),i.setStrokeStyle(l(t.color,o.opacity)),o.gradient){var C=i.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);C.addColorStop("0",l(t.color,o.opacity)),C.addColorStop("1.0",l("#FFFFFF",.1)),i.setFillStyle(C)}else i.setFillStyle(l(t.color,o.opacity));if(i.setLineWidth(o.width*e.pix),A.length>1){var S=A[0],T=A[A.length-1];i.moveTo(S.x,S.y);var k=0;if("curve"===o.type)for(var D=0;D<A.length;D++){var P=A[D];if(0==k&&P.x>u&&(i.moveTo(P.x,P.y),k=1),D>0&&P.x>u&&P.x<x){var M=h(A,D-1);i.bezierCurveTo(M.ctrA.x,M.ctrA.y,M.ctrB.x,M.ctrB.y,P.x,P.y)}}if("straight"===o.type)for(var L=0;L<A.length;L++){var F=A[L];0==k&&F.x>u&&(i.moveTo(F.x,F.y),k=1),L>0&&F.x>u&&F.x<x&&i.lineTo(F.x,F.y)}if("step"===o.type)for(var O=0;O<A.length;O++){var E=A[O];0==k&&E.x>u&&(i.moveTo(E.x,E.y),k=1),O>0&&E.x>u&&E.x<x&&(i.lineTo(E.x,A[O-1].y),i.lineTo(E.x,E.y))}i.lineTo(T.x,p),i.lineTo(S.x,p),i.lineTo(S.x,S.y)}else{var I=A[0];i.moveTo(I.x-d/2,I.y),i.lineTo(I.x+d/2,I.y),i.lineTo(I.x+d/2,p),i.lineTo(I.x-d/2,p),i.moveTo(I.x-d/2,I.y)}if(i.closePath(),i.fill(),o.addLine){if("dash"==t.lineType){var z=t.dashLength?t.dashLength:8;z*=e.pix,i.setLineDash([z,z])}if(i.beginPath(),i.setStrokeStyle(t.color),i.setLineWidth(o.width*e.pix),1===A.length)i.moveTo(A[0].x,A[0].y),i.arc(A[0].x,A[0].y,1,0,2*Math.PI);else{i.moveTo(A[0].x,A[0].y);var B=0;if("curve"===o.type)for(var W=0;W<A.length;W++){var R=A[W];if(0==B&&R.x>u&&(i.moveTo(R.x,R.y),B=1),W>0&&R.x>u&&R.x<x){var N=h(A,W-1);i.bezierCurveTo(N.ctrA.x,N.ctrA.y,N.ctrB.x,N.ctrB.y,R.x,R.y)}}if("straight"===o.type)for(var Y=0;Y<A.length;Y++){var j=A[Y];0==B&&j.x>u&&(i.moveTo(j.x,j.y),B=1),Y>0&&j.x>u&&j.x<x&&i.lineTo(j.x,j.y)}if("step"===o.type)for(var G=0;G<A.length;G++){var J=A[G];0==B&&J.x>u&&(i.moveTo(J.x,J.y),B=1),G>0&&J.x>u&&J.x<x&&(i.lineTo(J.x,A[G-1].y),i.lineTo(J.x,J.y))}i.moveTo(A[0].x,A[0].y)}i.stroke(),i.setLineDash([])}}!1!==e.dataPointShape&&U(y,t.color,t.pointShape,i,e)})),!1!==e.dataLabel&&1===n&&t.forEach((function(t,r){var o,s,l;o=[].concat(e.chartData.yAxisData.ranges[t.index]),s=o.pop(),l=o.shift();var h=t.data,p=H(h,s,l,c,d,e,a,n);V(p,t,a,i,e)})),i.restore(),{xAxisPoints:c,calPoints:f,eachSpacing:d}}(d,a,i,n,t),o=e.xAxisPoints,s=e.calPoints,c=e.eachSpacing;a.chartData.xAxisPoints=o,a.chartData.calPoints=s,a.chartData.eachSpacing=c,lt(0,a,i,n),!1!==a.enableMarkLine&&1===t&&et(a,i,n),ct(a.series,a,i,n,a.chartData),rt(a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"ring":case"pie":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),a.chartData.pieData=function(t,e,a,i){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=r({},{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,ringWidth:30,customRadius:0,border:!1,borderWidth:2,borderColor:"#FFFFFF",centerColor:"#FFFFFF",linearType:"none",customColor:[]},"pie"==e.type?e.extra.pie:e.extra.ring),s={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2};0==a.pieChartLinePadding&&(a.pieChartLinePadding=o.activeRadius*e.pix);var c=Math.min((e.width-e.area[1]-e.area[3])/2-a.pieChartLinePadding-a.pieChartTextPadding-a._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-a.pieChartLinePadding-a.pieChartTextPadding);o.customRadius>0&&(c=o.customRadius*e.pix),t=O(t,c,n);var d=o.activeRadius*e.pix;if(o.customColor=x(o.linearType,o.customColor,t,a),t=t.map((function(t){return t._start_+=o.offsetAngle*Math.PI/180,t})),t.forEach((function(t,a){e.tooltip&&e.tooltip.index==a&&(i.beginPath(),i.setFillStyle(l(t.color,o.activeOpacity||.5)),i.moveTo(s.x,s.y),i.arc(s.x,s.y,t._radius_+d,t._start_,t._start_+2*t._proportion_*Math.PI),i.closePath(),i.fill()),i.beginPath(),i.setLineWidth(o.borderWidth*e.pix),i.lineJoin="round",i.setStrokeStyle(o.borderColor);var n,r=t.color;"custom"==o.linearType&&(n=i.createCircularGradient?i.createCircularGradient(s.x,s.y,t._radius_):i.createRadialGradient(s.x,s.y,0,s.x,s.y,t._radius_),n.addColorStop(0,l(o.customColor[t.linearIndex],1)),n.addColorStop(1,l(t.color,1)),r=n);i.setFillStyle(r),i.moveTo(s.x,s.y),i.arc(s.x,s.y,t._radius_,t._start_,t._start_+2*t._proportion_*Math.PI),i.closePath(),i.fill(),1==o.border&&i.stroke()})),"ring"===e.type){var h=.6*c;"number"===typeof o.ringWidth&&o.ringWidth>0&&(h=Math.max(0,c-o.ringWidth*e.pix)),i.beginPath(),i.setFillStyle(o.centerColor),i.moveTo(s.x,s.y),i.arc(s.x,s.y,h,0,2*Math.PI),i.closePath(),i.fill()}if(!1!==e.dataLabel&&1===n){for(var p=!1,f=0,u=t.length;f<u;f++)if(t[f].data>0){p=!0;break}p&&tt(t,e,a,i,0,s)}return 1===n&&"ring"===e.type&&Z(e,a,i,s),{center:s,radius:c,series:t}}(d,a,i,n,t),ct(a.series,a,i,n,a.chartData),rt(a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"rose":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),a.chartData.pieData=function(t,e,a,i){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=r({},{type:"area",activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF",linearType:"none",customColor:[]},e.extra.rose);0==a.pieChartLinePadding&&(a.pieChartLinePadding=o.activeRadius*e.pix);var s={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},c=Math.min((e.width-e.area[1]-e.area[3])/2-a.pieChartLinePadding-a.pieChartTextPadding-a._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-a.pieChartLinePadding-a.pieChartTextPadding),d=o.minRadius||.5*c;t=I(t,o.type,d,c,n);var h=o.activeRadius*e.pix;if(o.customColor=x(o.linearType,o.customColor,t,a),t=t.map((function(t){return t._start_+=(o.offsetAngle||0)*Math.PI/180,t})),t.forEach((function(t,a){e.tooltip&&e.tooltip.index==a&&(i.beginPath(),i.setFillStyle(l(t.color,o.activeOpacity||.5)),i.moveTo(s.x,s.y),i.arc(s.x,s.y,h+t._radius_,t._start_,t._start_+2*t._rose_proportion_*Math.PI),i.closePath(),i.fill()),i.beginPath(),i.setLineWidth(o.borderWidth*e.pix),i.lineJoin="round",i.setStrokeStyle(o.borderColor);var n,r=t.color;"custom"==o.linearType&&(n=i.createCircularGradient?i.createCircularGradient(s.x,s.y,t._radius_):i.createRadialGradient(s.x,s.y,0,s.x,s.y,t._radius_),n.addColorStop(0,l(o.customColor[t.linearIndex],1)),n.addColorStop(1,l(t.color,1)),r=n);i.setFillStyle(r),i.moveTo(s.x,s.y),i.arc(s.x,s.y,t._radius_,t._start_,t._start_+2*t._rose_proportion_*Math.PI),i.closePath(),i.fill(),1==o.border&&i.stroke()})),!1!==e.dataLabel&&1===n){for(var p=!1,f=0,u=t.length;f<u;f++)if(t[f].data>0){p=!0;break}p&&tt(t,e,a,i,0,s)}return{center:s,radius:c,series:t}}(d,a,i,n,t),ct(a.series,a,i,n,a.chartData),rt(a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"radar":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),a.chartData.radarData=function(t,e,a,i){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=r({},{gridColor:"#cccccc",gridType:"radar",labelColor:"#666666",opacity:.2,gridCount:3,border:!1,borderWidth:2},e.extra.radar),s=C(e.categories.length),c={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},d=(e.width-e.area[1]-e.area[3])/2,h=(e.height-e.area[0]-e.area[2])/2,f=Math.min(d-(A(e.categories,a.fontSize,i)+a.radarLabelTextMargin),h-a.radarLabelTextMargin);f-=a.radarLabelTextMargin*e.pix,i.beginPath(),i.setLineWidth(1*e.pix),i.setStrokeStyle(o.gridColor),s.forEach((function(t){var e=p(f*Math.cos(t),f*Math.sin(t),c);i.moveTo(c.x,c.y),i.lineTo(e.x,e.y)})),i.stroke(),i.closePath();for(var u=function(t){var a={};if(i.beginPath(),i.setLineWidth(1*e.pix),i.setStrokeStyle(o.gridColor),"radar"==o.gridType)s.forEach((function(e,n){var r=p(f/o.gridCount*t*Math.cos(e),f/o.gridCount*t*Math.sin(e),c);0===n?(a=r,i.moveTo(r.x,r.y)):i.lineTo(r.x,r.y)})),i.lineTo(a.x,a.y);else{var n=p(f/o.gridCount*t*Math.cos(1.5),f/o.gridCount*t*Math.sin(1.5),c);i.arc(c.x,c.y,c.y-n.y,0,2*Math.PI,!1)}i.stroke(),i.closePath()},x=1;x<=o.gridCount;x++)u(x);var m=F(s,c,f,t,e,n);return m.forEach((function(t,a){if(i.beginPath(),i.setLineWidth(o.borderWidth*e.pix),i.setStrokeStyle(t.color),i.setFillStyle(l(t.color,o.opacity)),t.data.forEach((function(t,e){0===e?i.moveTo(t.position.x,t.position.y):i.lineTo(t.position.x,t.position.y)})),i.closePath(),i.fill(),!0===o.border&&i.stroke(),i.closePath(),!1!==e.dataPointShape){var n=t.data.map((function(t){return t.position}));U(n,t.color,t.pointShape,i,e)}})),$(s,f,c,e,a,i),{center:c,radius:f,angleList:s}}(d,a,i,n,t),ct(a.series,a,i,n,a.chartData),rt(a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"arcbar":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),a.chartData.arcbarData=function(t,e,a,i){var n,o,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,c=r({},{startAngle:.75,endAngle:.25,type:"default",width:12,gap:2,linearType:"none",customColor:[]},e.extra.arcbar);t=z(t,c,s),n=c.centerX||c.centerY?{x:c.centerX?c.centerX:e.width/2,y:c.centerY?c.centerY:e.height/2}:{x:e.width/2,y:e.height/2},c.radius?o=c.radius:(o=Math.min(n.x,n.y),o-=5*e.pix,o-=c.width/2),c.customColor=x(c.linearType,c.customColor,t,a);for(var d=0;d<t.length;d++){var h=t[d];i.setLineWidth(c.width*e.pix),i.setStrokeStyle(c.backgroundColor||"#E9E9E9"),i.setLineCap("round"),i.beginPath(),"default"==c.type?i.arc(n.x,n.y,o-(c.width*e.pix+c.gap*e.pix)*d,c.startAngle*Math.PI,c.endAngle*Math.PI,!1):i.arc(n.x,n.y,o-(c.width*e.pix+c.gap*e.pix)*d,0,2*Math.PI,!1),i.stroke();var p=h.color;if("custom"==c.linearType){var f=i.createLinearGradient(n.x-o,n.y,n.x+o,n.y);f.addColorStop(1,l(c.customColor[h.linearIndex],1)),f.addColorStop(0,l(h.color,1)),p=f}i.setLineWidth(c.width*e.pix),i.setStrokeStyle(p),i.setLineCap("round"),i.beginPath(),i.arc(n.x,n.y,o-(c.width*e.pix+c.gap*e.pix)*d,c.startAngle*Math.PI,h._proportion_*Math.PI,!1),i.stroke()}return Z(e,a,i,n),{center:n,radius:o,series:t}}(d,a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"gauge":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),a.chartData.gaugeData=function(t,e,a,i,n){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,s=r({},{type:"default",startAngle:.75,endAngle:.25,width:15,splitLine:{fixRadius:0,splitNumber:10,width:15,color:"#FFFFFF",childNumber:5,childWidth:5},pointer:{width:15,color:"auto"}},a.extra.gauge);void 0==s.oldAngle&&(s.oldAngle=s.startAngle),void 0==s.oldData&&(s.oldData=0),t=B(t,s.startAngle,s.endAngle);var c={x:a.width/2,y:a.height/2},d=Math.min(c.x,c.y);d-=5*a.pix,d-=s.width/2;var h=d-s.width,p=0;if("progress"==s.type){var f=d-3*s.width;n.beginPath();var u=n.createLinearGradient(c.x,c.y-f,c.x,c.y+f);u.addColorStop("0",l(e[0].color,.3)),u.addColorStop("1.0",l("#FFFFFF",.1)),n.setFillStyle(u),n.arc(c.x,c.y,f,0,2*Math.PI,!1),n.fill(),n.setLineWidth(s.width),n.setStrokeStyle(l(e[0].color,.3)),n.setLineCap("round"),n.beginPath(),n.arc(c.x,c.y,h,s.startAngle*Math.PI,s.endAngle*Math.PI,!1),n.stroke(),p=s.startAngle-s.endAngle+1;s.splitLine.splitNumber;var x=p/s.splitLine.splitNumber/s.splitLine.childNumber,m=-d-.5*s.width-s.splitLine.fixRadius,g=-d-s.width-s.splitLine.fixRadius+s.splitLine.width;n.save(),n.translate(c.x,c.y),n.rotate((s.startAngle-1)*Math.PI);for(var v=s.splitLine.splitNumber*s.splitLine.childNumber+1,y=e[0].data*o,b=0;b<v;b++)n.beginPath(),y>b/v?n.setStrokeStyle(l(e[0].color,1)):n.setStrokeStyle(l(e[0].color,.3)),n.setLineWidth(3*a.pix),n.moveTo(m,0),n.lineTo(g,0),n.stroke(),n.rotate(x*Math.PI);n.restore(),e=z(e,s,o),n.setLineWidth(s.width),n.setStrokeStyle(e[0].color),n.setLineCap("round"),n.beginPath(),n.arc(c.x,c.y,h,s.startAngle*Math.PI,e[0]._proportion_*Math.PI,!1),n.stroke();var w=d-2.5*s.width;n.save(),n.translate(c.x,c.y),n.rotate((e[0]._proportion_-1)*Math.PI),n.beginPath(),n.setLineWidth(s.width/3);var A=n.createLinearGradient(0,.6*-w,0,.6*w);A.addColorStop("0",l("#FFFFFF",0)),A.addColorStop("0.5",l(e[0].color,1)),A.addColorStop("1.0",l("#FFFFFF",0)),n.setStrokeStyle(A),n.arc(0,0,w,.85*Math.PI,1.15*Math.PI,!1),n.stroke(),n.beginPath(),n.setLineWidth(1),n.setStrokeStyle(e[0].color),n.setFillStyle(e[0].color),n.moveTo(-w-s.width/3/2,-4),n.lineTo(-w-s.width/3/2-4,0),n.lineTo(-w-s.width/3/2,4),n.lineTo(-w-s.width/3/2,-4),n.stroke(),n.fill(),n.restore()}else{n.setLineWidth(s.width),n.setLineCap("butt");for(var C=0;C<t.length;C++){var S=t[C];n.beginPath(),n.setStrokeStyle(S.color),n.arc(c.x,c.y,d,S._startAngle_*Math.PI,S._endAngle_*Math.PI,!1),n.stroke()}n.save(),p=s.startAngle-s.endAngle+1;var T=p/s.splitLine.splitNumber,k=p/s.splitLine.splitNumber/s.splitLine.childNumber,D=-d-.5*s.width-s.splitLine.fixRadius,P=-d-.5*s.width-s.splitLine.fixRadius+s.splitLine.width,_=-d-.5*s.width-s.splitLine.fixRadius+s.splitLine.childWidth;n.translate(c.x,c.y),n.rotate((s.startAngle-1)*Math.PI);for(var M=0;M<s.splitLine.splitNumber+1;M++)n.beginPath(),n.setStrokeStyle(s.splitLine.color),n.setLineWidth(2*a.pix),n.moveTo(D,0),n.lineTo(P,0),n.stroke(),n.rotate(T*Math.PI);n.restore(),n.save(),n.translate(c.x,c.y),n.rotate((s.startAngle-1)*Math.PI);for(var L=0;L<s.splitLine.splitNumber*s.splitLine.childNumber+1;L++)n.beginPath(),n.setStrokeStyle(s.splitLine.color),n.setLineWidth(1*a.pix),n.moveTo(D,0),n.lineTo(_,0),n.stroke(),n.rotate(k*Math.PI);n.restore(),e=W(e,t,s,o);for(var F=0;F<e.length;F++){var O=e[F];n.save(),n.translate(c.x,c.y),n.rotate((O._proportion_-1)*Math.PI),n.beginPath(),n.setFillStyle(O.color),n.moveTo(s.pointer.width,0),n.lineTo(0,-s.pointer.width/2),n.lineTo(-h,0),n.lineTo(0,s.pointer.width/2),n.lineTo(s.pointer.width,0),n.closePath(),n.fill(),n.beginPath(),n.setFillStyle("#FFFFFF"),n.arc(0,0,s.pointer.width/6,0,2*Math.PI,!1),n.fill(),n.restore()}!1!==a.dataLabel&&K(s,d,c,a,i,n)}return Z(a,i,n,c),1===o&&"gauge"===a.type&&(a.extra.gauge.oldAngle=e[0]._proportion_,a.extra.gauge.oldData=e[0].data),{center:c,radius:d,innerRadius:h,categories:t,totalAngle:p}}(f,d,a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break;case"candle":this.animationInstance=new yt({timing:a.timing,duration:m,onProcess:function(t){n.clearRect(0,0,a.width,a.height),a.rotate&&Q(n,a),st(0,a,0,n),ot(f,a,i,n);var e=function(t,e,a,i,n){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,s=r({},{color:{},average:{}},a.extra.candle);s.color=r({},{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},s.color),s.average=r({},{show:!1,name:[],day:[],color:i.color},s.average),a.extra.candle=s;var l=a.chartData.xAxisData,c=l.xAxisPoints,d=l.eachSpacing,p=[];n.save();var f=-2,u=c.length+2,x=0,m=a.width+d;return a._scrollDistance_&&0!==a._scrollDistance_&&!0===a.enableScroll&&(n.translate(a._scrollDistance_,0),f=Math.floor(-a._scrollDistance_/d)-2,u=f+a.xAxis.itemCount+4,x=-a._scrollDistance_-2*d+a.area[3],m=x+(a.xAxis.itemCount+4)*d),(s.average.show||e)&&e.forEach((function(t,e){var r,s,l;r=[].concat(a.chartData.yAxisData.ranges[t.index]),s=r.pop(),l=r.shift();for(var p=t.data,f=H(p,s,l,c,d,a,i,o),u=_(f,t),g=0;g<u.length;g++){var v=u[g];if(n.beginPath(),n.setStrokeStyle(t.color),n.setLineWidth(1),1===v.length)n.moveTo(v[0].x,v[0].y),n.arc(v[0].x,v[0].y,1,0,2*Math.PI);else{n.moveTo(v[0].x,v[0].y);for(var y=0,b=0;b<v.length;b++){var w=v[b];if(0==y&&w.x>x&&(n.moveTo(w.x,w.y),y=1),b>0&&w.x>x&&w.x<m){var A=h(v,b-1);n.bezierCurveTo(A.ctrA.x,A.ctrA.y,A.ctrB.x,A.ctrB.y,w.x,w.y)}}n.moveTo(v[0].x,v[0].y)}n.closePath(),n.stroke()}})),t.forEach((function(t,e){var r,l,h;r=[].concat(a.chartData.yAxisData.ranges[t.index]),l=r.pop(),h=r.shift();var x=t.data,m=G(x,l,h,c,d,a,i,o);p.push(m);for(var g=_(m,t),v=0;v<g[0].length;v++)if(v>f&&v<u){var y=g[0][v];n.beginPath(),x[v][1]-x[v][0]>0?(n.setStrokeStyle(s.color.upLine),n.setFillStyle(s.color.upFill),n.setLineWidth(1*a.pix),n.moveTo(y[3].x,y[3].y),n.lineTo(y[1].x,y[1].y),n.lineTo(y[1].x-d/4,y[1].y),n.lineTo(y[0].x-d/4,y[0].y),n.lineTo(y[0].x,y[0].y),n.lineTo(y[2].x,y[2].y),n.lineTo(y[0].x,y[0].y),n.lineTo(y[0].x+d/4,y[0].y),n.lineTo(y[1].x+d/4,y[1].y),n.lineTo(y[1].x,y[1].y),n.moveTo(y[3].x,y[3].y)):(n.setStrokeStyle(s.color.downLine),n.setFillStyle(s.color.downFill),n.setLineWidth(1*a.pix),n.moveTo(y[3].x,y[3].y),n.lineTo(y[0].x,y[0].y),n.lineTo(y[0].x-d/4,y[0].y),n.lineTo(y[1].x-d/4,y[1].y),n.lineTo(y[1].x,y[1].y),n.lineTo(y[2].x,y[2].y),n.lineTo(y[1].x,y[1].y),n.lineTo(y[1].x+d/4,y[1].y),n.lineTo(y[0].x+d/4,y[0].y),n.lineTo(y[0].x,y[0].y),n.moveTo(y[3].x,y[3].y)),n.closePath(),n.fill(),n.stroke()}})),n.restore(),{xAxisPoints:c,calPoints:p,eachSpacing:d}}(d,v,a,i,n,t),o=e.xAxisPoints,s=e.calPoints,l=e.eachSpacing;a.chartData.xAxisPoints=o,a.chartData.calPoints=s,a.chartData.eachSpacing=l,lt(0,a,i,n),!1!==a.enableMarkLine&&1===t&&et(a,i,n),ct(v?0:a.series,a,i,n,a.chartData),rt(a,i,n,t),gt(0,n)},onAnimationFinish:function(){c.uevent.trigger("renderComplete")}});break}}function wt(){this.events={}}yt.prototype.stop=function(){this.isStop=!0},wt.prototype.addEventListener=function(t,e){this.events[t]=this.events[t]||[],this.events[t].push(e)},wt.prototype.delEventListener=function(t){this.events[t]=[]},wt.prototype.trigger=function(){for(var t=arguments.length,e=Array(t),a=0;a<t;a++)e[a]=arguments[a];var i=e[0],n=e.slice(1);this.events[i]&&this.events[i].forEach((function(t){try{t.apply(null,n)}catch(e){}}))};var At=function(t){t.pix=t.pixelRatio?t.pixelRatio:1,t.fontSize=t.fontSize?t.fontSize:13,t.fontColor=t.fontColor?t.fontColor:n.fontColor,""!=t.background&&"none"!=t.background||(t.background="#FFFFFF"),t.title=r({},t.title),t.subtitle=r({},t.subtitle),t.duration=t.duration?t.duration:1e3,t.yAxis=r({},{data:[],showTitle:!1,disabled:!1,disableGrid:!1,splitNumber:5,gridType:"solid",dashLength:4*t.pix,gridColor:"#cccccc",padding:10,fontColor:"#666666"},t.yAxis),t.xAxis=r({},{rotateLabel:!1,type:"calibration",gridType:"solid",dashLength:4,scrollAlign:"left",boundaryGap:"center",axisLine:!0,axisLineColor:"#cccccc"},t.xAxis),t.xAxis.scrollPosition=t.xAxis.scrollAlign,t.legend=r({},{show:!0,position:"bottom",float:"center",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",borderWidth:0,padding:5,margin:5,itemGap:10,fontSize:t.fontSize,lineHeight:t.fontSize,fontColor:"#333333",formatter:{},hiddenColor:"#CECECE"},t.legend),t.extra=r({},t.extra),t.rotate=!!t.rotate,t.animation=!!t.animation,t.rotate=!!t.rotate,t.canvas2d=!!t.canvas2d;var e=JSON.parse(JSON.stringify(n));if(e.color=t.color?t.color:e.color,e.yAxisTitleWidth=!0!==t.yAxis.disabled&&t.yAxis.title?e.yAxisTitleWidth:0,"pie"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.pie.labelWidth*t.pix||e.pieChartLinePadding*t.pix),"ring"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.ring.labelWidth*t.pix||e.pieChartLinePadding*t.pix),"rose"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.rose.labelWidth*t.pix||e.pieChartLinePadding*t.pix),e.pieChartTextPadding=!1===t.dataLabel?0:e.pieChartTextPadding*t.pix,e.yAxisSplit=t.yAxis.splitNumber?t.yAxis.splitNumber:n.yAxisSplit,e.rotate=t.rotate,t.rotate){var a=t.width,i=t.height;t.width=i,t.height=a}if(t.padding=t.padding?t.padding:e.padding,e.yAxisWidth=n.yAxisWidth*t.pix,e.xAxisHeight=n.xAxisHeight*t.pix,t.enableScroll&&t.xAxis.scrollShow&&(e.xAxisHeight+=6*t.pix),e.xAxisLineHeight=n.xAxisLineHeight*t.pix,e.fontSize=t.fontSize*t.pix,e.titleFontSize=n.titleFontSize*t.pix,e.subtitleFontSize=n.subtitleFontSize*t.pix,e.toolTipPadding=n.toolTipPadding*t.pix,e.toolTipLineHeight=n.toolTipLineHeight*t.pix,e.columePadding=n.columePadding*t.pix,!t.context)throw new Error("[uCharts] 未获取到context！注意：v2.0版本后，需要自行获取canvas的绘图上下文并传入opts.context！");this.context=t.context,this.context.setTextAlign||(this.context.setStrokeStyle=function(t){return this.strokeStyle=t},this.context.setLineWidth=function(t){return this.lineWidth=t},this.context.setLineCap=function(t){return this.lineCap=t},this.context.setFontSize=function(t){return this.font=t+"px sans-serif"},this.context.setFillStyle=function(t){return this.fillStyle=t},this.context.setTextAlign=function(t){return this.textAlign=t},this.context.draw=function(){}),t.chartData={},this.uevent=new wt,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},this.opts=t,this.config=e,bt.call(this,t.type,t,e,this.context)};At.prototype.updateData=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.opts=r({},this.opts,t),this.opts.updateData=!0;var e=t.scrollPosition||"current";switch(e){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":var a=q(this.opts.series,this.opts,this.config,this.context),i=a.yAxisWidth;this.config.yAxisWidth=i;var n=0,o=j(this.opts.categories,this.opts,this.config),s=o.xAxisPoints,l=o.startX,c=o.endX,d=o.eachSpacing,h=d*(s.length-1),p=c-l;n=p-h,this.scrollOption={currentOffset:n,startTouchX:n,distance:0,lastMoveTime:0},this.opts._scrollDistance_=n;break}bt.call(this,this.opts.type,this.opts,this.config,this.context)},At.prototype.zoom=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.opts.xAxis.itemCount;if(!0===this.opts.enableScroll){var e=Math.round(Math.abs(this.scrollOption.currentOffset)/this.opts.chartData.eachSpacing)+Math.round(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=t.itemCount;var a=q(this.opts.series,this.opts,this.config,this.context),i=a.yAxisWidth;this.config.yAxisWidth=i;var n=0,r=j(this.opts.categories,this.opts,this.config),o=r.xAxisPoints,s=r.startX,l=r.endX,c=r.eachSpacing,d=c*e,h=l-s,p=h-c*(o.length-1);n=h/2-d,n>0&&(n=0),n<p&&(n=p),this.scrollOption={currentOffset:n,startTouchX:n,distance:0,lastMoveTime:0},this.opts._scrollDistance_=n,bt.call(this,this.opts.type,this.opts,this.config,this.context)}else console.log("[uCharts] 请启用滚动条后使用")},At.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()},At.prototype.addEventListener=function(t,e){this.uevent.addEventListener(t,e)},At.prototype.delEventListener=function(t){this.uevent.delEventListener(t)},At.prototype.getCurrentDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],e){var a=b(e,this.opts,t);return"pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type?function(t,e){var a=-1;if(e&&e.center&&P(t,e.center,e.radius)){var i=Math.atan2(e.center.y-t.y,t.x-e.center.x);i=-i;for(var n=0,r=e.series.length;n<r;n++){var o=e.series[n];if(d(i,o._start_,o._start_+2*o._proportion_*Math.PI)){a=n;break}}}return a}({x:a.x,y:a.y},this.opts.chartData.pieData):"radar"===this.opts.type?function(t,e,a){var i=2*Math.PI/a,n=-1;if(P(t,e.center,e.radius)){var r=function(t){return t<0&&(t+=2*Math.PI),t>2*Math.PI&&(t-=2*Math.PI),t},o=Math.atan2(e.center.y-t.y,t.x-e.center.x);o*=-1,o<0&&(o+=2*Math.PI);var s=e.angleList.map((function(t){return t=r(-1*t),t}));s.forEach((function(t,e){var a=r(t-i/2),s=r(t+i/2);s<a&&(s+=2*Math.PI),(o>=a&&o<=s||o+2*Math.PI>=a&&o+2*Math.PI<=s)&&(n=e)}))}return n}({x:a.x,y:a.y},this.opts.chartData.radarData,this.opts.categories.length):"funnel"===this.opts.type?function(t,e){for(var a=-1,i=0,n=e.series.length;i<n;i++){var r=e.series[i];if(t.x>r.funnelArea[0]&&t.x<r.funnelArea[2]&&t.y>r.funnelArea[1]&&t.y<r.funnelArea[3]){a=i;break}}return a}({x:a.x,y:a.y},this.opts.chartData.funnelData):"map"===this.opts.type?function(t,e){for(var a=-1,i=e.chartData.mapData,n=e.series,r=function(t,e,a,i,n,r){return{x:(e-n)/i+a.xMin,y:a.yMax-(t-r)/i}}(t.y,t.x,i.bounds,i.scale,i.xoffset,i.yoffset),o=[r.x,r.y],s=0,l=n.length;s<l;s++){var c=n[s].geometry.coordinates;if(ut(o,c,e.chartData.mapData.mercator)){a=s;break}}return a}({x:a.x,y:a.y},this.opts):"word"===this.opts.type?function(t,e){for(var a=-1,i=0,n=e.length;i<n;i++){var r=e[i];if(t.x>r.area[0]&&t.x<r.area[2]&&t.y>r.area[1]&&t.y<r.area[3]){a=i;break}}return a}({x:a.x,y:a.y},this.opts.chartData.wordCloudData):function(t,e,a,i){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,r=-1,o=a.chartData.eachSpacing/2,s=[];if(e&&e.length>0){for(var l=1;l<a.chartData.xAxisPoints.length;l++)s.push(a.chartData.xAxisPoints[l]-o);"line"!=a.type&&"area"!=a.type||"justify"!=a.xAxis.boundaryGap||(s=a.chartData.xAxisPoints),a.categories||(o=0),D(t,a,i)&&s.forEach((function(e,a){t.x+n+o>e&&(r=a)}))}return r}({x:a.x,y:a.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1},At.prototype.getLegendDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],e){var a=b(e,this.opts,t);return function(t,e,a){var i=-1;if(function(t,e){return t.x>e.start.x&&t.x<e.end.x&&t.y>e.start.y&&t.y<e.end.y}(t,e.area)){for(var n=e.points,r=-1,o=0,s=n.length;o<s;o++)for(var l=n[o],c=0;c<l.length;c++){r+=1;var d=l[c]["area"];if(d&&t.x>d[0]-0&&t.x<d[2]+0&&t.y>d[1]-0&&t.y<d[3]+0){i=r;break}}return i}return i}({x:a.x,y:a.y},this.opts.chartData.legendData)}return-1},At.prototype.touchLegend=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=null;if(a=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],a){b(a,this.opts,t);var i=this.getLegendDataIndex(t);i>=0&&("candle"==this.opts.type?this.opts.seriesMA[i].show=!this.opts.seriesMA[i].show:this.opts.series[i].show=!this.opts.series[i].show,this.opts.animation=!!e.animation,this.opts._scrollDistance_=this.scrollOption.currentOffset,bt.call(this,this.opts.type,this.opts,this.config,this.context))}},At.prototype.showToolTip=function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=null;i=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],i||console.log("[uCharts] 未获取到event坐标信息");var n=b(i,this.opts,t),o=this.scrollOption.currentOffset,s=r({},this.opts,{_scrollDistance_:o,animation:!1});if("line"===this.opts.type||"area"===this.opts.type||"column"===this.opts.type){var l=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(l>-1){var c=w(this.opts.series,l);if(0!==c.length){var d=S(c,this.opts,l,this.opts.categories,a),h=d.textList,p=d.offset;p.y=n.y,s.tooltip={textList:void 0!==a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:p,option:a,index:l}}}bt.call(this,s.type,s,this.config,this.context)}if("mix"===this.opts.type){l=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(l>-1){o=this.scrollOption.currentOffset,s=r({},this.opts,{_scrollDistance_:o,animation:!1}),c=w(this.opts.series,l);if(0!==c.length){var f=T(c,this.opts,l,this.opts.categories,a);h=f.textList,p=f.offset;p.y=n.y,s.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:p,option:a,index:l}}}bt.call(this,s.type,s,this.config,this.context)}if("candle"===this.opts.type){l=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(l>-1){o=this.scrollOption.currentOffset,s=r({},this.opts,{_scrollDistance_:o,animation:!1}),c=w(this.opts.series,l);if(0!==c.length){d=k(this.opts.series[0].data,c,this.opts,l,this.opts.categories,this.opts.extra.candle,a),h=d.textList,p=d.offset;p.y=n.y,s.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:p,option:a,index:l}}}bt.call(this,s.type,s,this.config,this.context)}if("pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type||"funnel"===this.opts.type){l=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(l>-1){s=r({},this.opts,{animation:!1}),c=this.opts._series_[l],h=[{text:a.formatter?a.formatter(c,void 0,l,s):c.name+": "+c.data,color:c.color}],p={x:n.x,y:n.y};s.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:p,option:a,index:l}}bt.call(this,s.type,s,this.config,this.context)}if("map"===this.opts.type){l=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(l>-1){s=r({},this.opts,{animation:!1}),c=this.opts._series_[l];c.name=c.properties.name;h=[{text:a.formatter?a.formatter(c,void 0,l,this.opts):c.name,color:c.color}],p={x:n.x,y:n.y};s.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:p,option:a,index:l}}s.updateData=!1,bt.call(this,s.type,s,this.config,this.context)}if("word"===this.opts.type){l=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(l>-1){s=r({},this.opts,{animation:!1}),c=this.opts._series_[l],h=[{text:a.formatter?a.formatter(c,void 0,l,this.opts):c.name,color:c.color}],p={x:n.x,y:n.y};s.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:p,option:a,index:l}}s.updateData=!1,bt.call(this,s.type,s,this.config,this.context)}if("radar"===this.opts.type){l=void 0==a.index?this.getCurrentDataIndex(t):a.index;if(l>-1){s=r({},this.opts,{animation:!1}),c=w(this.opts.series,l);if(0!==c.length){h=c.map((function(t){return{text:a.formatter?a.formatter(t,e.opts.categories[l],l,e.opts):t.name+": "+t.data,color:t.color}})),p={x:n.x,y:n.y};s.tooltip={textList:a.textList?a.textList:h,offset:void 0!==a.offset?a.offset:p,option:a,index:l}}}bt.call(this,s.type,s,this.config,this.context)}},At.prototype.translate=function(t){this.scrollOption={currentOffset:t,startTouchX:t,distance:0,lastMoveTime:0};var e=r({},this.opts,{_scrollDistance_:t,animation:!1});bt.call(this,this.opts.type,e,this.config,this.context)},At.prototype.scrollStart=function(t){var e=null;e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0];var a=b(e,this.opts,t);e&&!0===this.opts.enableScroll&&(this.scrollOption.startTouchX=a.x)},At.prototype.scroll=function(t){0===this.scrollOption.lastMoveTime&&(this.scrollOption.lastMoveTime=Date.now());var e=this.opts.touchMoveLimit||60,a=Date.now(),i=a-this.scrollOption.lastMoveTime;if(!(i<Math.floor(1e3/e))){this.scrollOption.lastMoveTime=a;var n=null;if(n=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],n&&!0===this.opts.enableScroll){var o,s=b(n,this.opts,t);o=s.x-this.scrollOption.startTouchX;var l=this.scrollOption.currentOffset,c=function(t,e,a,i,n){var r=n.width-n.area[1]-n.area[3],o=a.eachSpacing*(n.chartData.xAxisData.xAxisPoints.length-1),s=e;return e>=0?(s=0,t.uevent.trigger("scrollLeft"),t.scrollOption.position="left",n.xAxis.scrollPosition="left"):Math.abs(e)>=o-r?(s=r-o,t.uevent.trigger("scrollRight"),t.scrollOption.position="right",n.xAxis.scrollPosition="right"):(t.scrollOption.position=e,n.xAxis.scrollPosition=e),s}(this,l+o,this.opts.chartData,this.config,this.opts);this.scrollOption.distance=o=c-l;var d=r({},this.opts,{_scrollDistance_:l+o,animation:!1});return bt.call(this,d.type,d,this.config,this.context),l+o}}},At.prototype.scrollEnd=function(t){if(!0===this.opts.enableScroll){var e=this.scrollOption,a=e.currentOffset,i=e.distance;this.scrollOption.currentOffset=a+i,this.scrollOption.distance=0}},"object"===i(t)&&"object"===i(t.exports)&&(t.exports=At)}).call(this,a("dc84")(t))},6847:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".container[data-v-11617f41]{width:30px;height:30px;position:relative}.container.loading6[data-v-11617f41]{-webkit-animation:rotation 1s infinite;animation:rotation 1s infinite}.container.loading6 .shape[data-v-11617f41]{width:12px;height:12px;border-radius:2px}.container .shape[data-v-11617f41]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-11617f41]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-11617f41]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-11617f41]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-11617f41]{bottom:0;right:0;background-color:#e66}.loading6 .shape1[data-v-11617f41]{-webkit-animation:animation6shape1-data-v-11617f41 2s linear 0s infinite normal;animation:animation6shape1-data-v-11617f41 2s linear 0s infinite normal}@-webkit-keyframes animation6shape1-data-v-11617f41{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(18px);transform:translateY(18px)}50%{-webkit-transform:translate(18px,18px);transform:translate(18px,18px)}75%{-webkit-transform:translate(18px);transform:translate(18px)}}@keyframes animation6shape1-data-v-11617f41{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(18px);transform:translateY(18px)}50%{-webkit-transform:translate(18px,18px);transform:translate(18px,18px)}75%{-webkit-transform:translate(18px);transform:translate(18px)}}.loading6 .shape2[data-v-11617f41]{-webkit-animation:animation6shape2-data-v-11617f41 2s linear 0s infinite normal;animation:animation6shape2-data-v-11617f41 2s linear 0s infinite normal}@-webkit-keyframes animation6shape2-data-v-11617f41{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(-18px);transform:translate(-18px)}50%{-webkit-transform:translate(-18px,18px);transform:translate(-18px,18px)}75%{-webkit-transform:translateY(18px);transform:translateY(18px)}}@keyframes animation6shape2-data-v-11617f41{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(-18px);transform:translate(-18px)}50%{-webkit-transform:translate(-18px,18px);transform:translate(-18px,18px)}75%{-webkit-transform:translateY(18px);transform:translateY(18px)}}.loading6 .shape3[data-v-11617f41]{-webkit-animation:animation6shape3-data-v-11617f41 2s linear 0s infinite normal;animation:animation6shape3-data-v-11617f41 2s linear 0s infinite normal}@-webkit-keyframes animation6shape3-data-v-11617f41{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(18px);transform:translate(18px)}50%{-webkit-transform:translate(18px,-18px);transform:translate(18px,-18px)}75%{-webkit-transform:translateY(-18px);transform:translateY(-18px)}}@keyframes animation6shape3-data-v-11617f41{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translate(18px);transform:translate(18px)}50%{-webkit-transform:translate(18px,-18px);transform:translate(18px,-18px)}75%{-webkit-transform:translateY(-18px);transform:translateY(-18px)}}.loading6 .shape4[data-v-11617f41]{-webkit-animation:animation6shape4-data-v-11617f41 2s linear 0s infinite normal;animation:animation6shape4-data-v-11617f41 2s linear 0s infinite normal}@-webkit-keyframes animation6shape4-data-v-11617f41{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(-18px);transform:translateY(-18px)}50%{-webkit-transform:translate(-18px,-18px);transform:translate(-18px,-18px)}75%{-webkit-transform:translate(-18px);transform:translate(-18px)}}@keyframes animation6shape4-data-v-11617f41{0%{-webkit-transform:translate(0);transform:translate(0)}25%{-webkit-transform:translateY(-18px);transform:translateY(-18px)}50%{-webkit-transform:translate(-18px,-18px);transform:translate(-18px,-18px)}75%{-webkit-transform:translate(-18px);transform:translate(-18px)}}",""]),t.exports=e},"69e5":function(t,e,a){"use strict";a.r(e);var i=a("21fb"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"6da7":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"loading1",data:function(){return{}}}},"70bf":function(t,e,a){"use strict";a.r(e);var i=a("8b04"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"70d4":function(t,e,a){"use strict";a.r(e);var i=a("cb8b"),n=a("2e1f");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("82eb"),a("f156");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"3353c590",null,!1,i["a"],void 0);e["default"]=s.exports},"74a5":function(t,e,a){"use strict";a.r(e);var i=a("f811"),n=a("7b4c");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("a7c9");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"5f50525e",null,!1,i["a"],void 0);e["default"]=s.exports},"77a1":function(t,e,a){"use strict";a.r(e);var i=a("05ba"),n=a("70bf");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);var o=a("fcc7");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("ad03");var s=a("828b");n["default"].__module="rdcharts";var l=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"de9bfb5e",null,!1,i["a"],n["default"]);e["default"]=l.exports},"77a4":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"container loading2"},[e("v-uni-view",{staticClass:"shape shape1"}),e("v-uni-view",{staticClass:"shape shape2"}),e("v-uni-view",{staticClass:"shape shape3"}),e("v-uni-view",{staticClass:"shape shape4"})],1)},n=[]},"79b7":function(t,e,a){a("f7a5"),a("bf0f"),a("08eb"),a("18f7"),a("5c47"),a("0506");var i=a("e476");t.exports=function(t,e){if(t){if("string"===typeof t)return i(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?i(t,e):void 0}},t.exports.__esModule=!0,t.exports["default"]=t.exports},"7b4c":function(t,e,a){"use strict";a.r(e);var i=a("6da7"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"7ba9":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"container loading3"},[e("v-uni-view",{staticClass:"shape shape1"}),e("v-uni-view",{staticClass:"shape shape2"}),e("v-uni-view",{staticClass:"shape shape3"}),e("v-uni-view",{staticClass:"shape shape4"})],1)},n=[]},"80d5":function(t,e,a){"use strict";a.r(e);var i=a("f08d"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"82eb":function(t,e,a){"use strict";var i=a("1b02"),n=a.n(i);n.a},"882e":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-3353c590]{display:none}\r\n/* 收银台相关 */uni-text[data-v-3353c590],\r\nuni-view[data-v-3353c590]{font-size:.14rem}body[data-v-3353c590]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-3353c590]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-3353c590]::-webkit-scrollbar-button{display:none}body[data-v-3353c590]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-3353c590]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-3353c590]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-3353c590]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-3353c590]{color:var(--primary-color)!important}.common-wrap[data-v-3353c590]{padding:.2rem .2rem .6rem;height:100vh;box-sizing:border-box;overflow-y:auto}.title[data-v-3353c590]{display:flex;margin-bottom:.2rem;font-size:.16rem;font-family:Source Han Sans CN;font-weight:700;line-height:.2rem}.title-port[data-v-3353c590]{margin-bottom:.2rem;margin-left:.1rem;font-size:.16rem;font-family:Source Han Sans CN;font-weight:700;line-height:.2rem}.choice-day[data-v-3353c590]{display:flex;margin-bottom:.2rem}.choice-time[data-v-3353c590]{margin-top:.09rem;font-size:.12rem;font-family:Source Han Sans CN;font-weight:400;line-height:.36rem}.report[data-v-3353c590]{display:flex;justify-content:flex-end;margin-right:.2rem;font-size:.14rem;font-family:Source Han Sans CN;font-weight:400;line-height:.36rem;cursor:pointer}.move[data-v-3353c590]{margin-right:.06rem}.money[data-v-3353c590]{display:flex;flex-wrap:wrap;margin-top:.2rem}.money .estimate[data-v-3353c590]{width:calc((100% - .85rem) / 5);margin:0 .08rem .2rem;padding:.2rem;background:#fff;border:.01rem solid #eee;border-radius:.02rem;cursor:pointer;position:relative;box-sizing:border-box}.money .estimate .income[data-v-3353c590]{display:flex;flex-direction:row;box-sizing:border-box;line-height:.2rem}.money .estimate .income .income-name[data-v-3353c590]{font-size:.16rem}.money .estimate .num-money .last_income[data-v-3353c590]{display:block;margin:.15rem 0;font-size:.24rem;font-weight:500;line-height:.2rem}.money .estimate .num-money .detail[data-v-3353c590]{display:block;text-align:right;color:var(--primary-color);font-size:.12rem;position:relative;bottom:-.05rem}.money .estimate[data-v-3353c590]:last-child{margin-right:0}.yesterday[data-v-3353c590]{display:flex;flex-wrap:wrap;font-size:.12rem}.top-num[data-v-3353c590]{display:flex;margin-left:.05rem;font-size:.12rem;font-weight:400}.date-btn[data-v-3353c590]{height:.42rem;line-height:.42rem;font-size:.14rem;padding:0 .3rem;box-sizing:border-box;border:.01rem solid #d2d2d2;cursor:pointer;border-right:none;border-left:none;position:relative}.date-btn[data-v-3353c590]:nth-child(6)::after{border-radius:0 .02rem .02rem 0}.date-btn[data-v-3353c590]:first-child::after{border-radius:.02rem 0 0 .02rem}.date-btn[data-v-3353c590]:first-child{border-radius:.02rem 0 0 .02rem}.date-btn[data-v-3353c590]::after{content:"";position:absolute;top:-.01rem;left:0;bottom:-.01rem;right:-.01rem;border-right:.01rem solid #d2d2d2;border-left:.01rem solid #d2d2d2}.select[data-v-3353c590]{color:#fff;background-color:var(--primary-color);border-color:var(--primary-color)}.select[data-v-3353c590]::after{z-index:2;border-color:var(--primary-color)}.select[data-v-3353c590]:first-child{border-radius:.02rem 0 0 .02rem}.seleced[data-v-3353c590]:nth-child(6){border-radius:0 .02rem .02rem 0}.c-datepicker-picker[data-v-3353c590]{z-index:99999999!important}.yesterday[data-v-3353c590]{display:none}.dropdown-content-box[data-v-3353c590]{padding:.05rem 0;margin-top:.05rem;background-color:#fff;border:.01rem solid #ebeef5;border-radius:.04rem;box-shadow:0 .01rem .12rem 0 rgba(0,0,0,.1);position:relative}.dropdown-content-box .arrow[data-v-3353c590]{position:absolute;top:-.06rem;right:.06rem;width:0;height:0;border-left:.06rem solid transparent;border-right:.06rem solid transparent;border-bottom:.06rem solid #fff}.dropdown-content-box .text[data-v-3353c590]{display:flex;align-items:center;justify-content:center;margin:0;padding:0 .1rem;transition:all .3s;font-size:.12rem;width:1.5rem;box-sizing:border-box;text-align:left;line-height:1.5}.js-prompt-top[data-v-3353c590]{color:#c8c9cc;font-size:.14rem;z-index:999;margin-left:.05rem;cursor:pointer}.pop-box[data-v-3353c590]{background:#fff;width:6rem;height:3.38rem}.pop-box .pop-header[data-v-3353c590]{padding:0 .15rem 0 .2rem;height:.5rem;line-height:.5rem;border-bottom:.01rem solid #f0f0f0;font-size:.14rem;color:#333;overflow:hidden;border-radius:.02rem .2rem 0 0;box-sizing:border-box;display:flex;justify-content:space-between}.pop-box .pop-header .pop-header-close[data-v-3353c590]{cursor:pointer}.pop-box .pop-header .pop-header-close uni-text[data-v-3353c590]{font-size:.18rem}.pop-box .pop-content[data-v-3353c590]{height:calc(100% - 1.05rem);padding:.2rem;box-sizing:border-box}.pop-box .pop-bottom[data-v-3353c590]{padding:.1rem .2rem;border-top:.01rem solid #eee}.pop-box .pop-bottom uni-button[data-v-3353c590]{width:100%;line-height:.35rem;height:.35rem}.charts-pop[data-v-3353c590]{width:9.5rem;height:5.4rem;background-color:#fff}.charts-pop .pop-content[data-v-3353c590]{width:100%;height:calc(100% - 1rem)}',""]),t.exports=e},"8b04":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d4b5"),a("3efd");var n=i(a("fcf3")),r=i(a("6791")),o=i(a("b6cc")),s=i(a("b767")),l={},c=null;function d(t,e){for(var a in t)"object"===(0,n.default)(t[a])?d(t[a],e):"format"===a&&"string"===typeof t[a]&&(t["formatter"]=e[t[a]]?e[t[a]]:void 0);return t}var h={data:function(){return{rid:null}},mounted:function(){var t=this;c={top:0,left:0};var e=document.querySelectorAll("uni-main")[0];void 0===e&&(e=document.querySelectorAll("uni-page-wrapper")[0]),c={top:e.offsetTop,left:e.offsetLeft},setTimeout((function(){null===t.rid&&t.$ownerInstance.callMethod("getRenderType")}),200)},destroyed:function(){delete o.default.option[this.rid],delete o.default.instance[this.rid],delete s.default.option[this.rid],delete s.default.instance[this.rid]},methods:{ecinit:function(t,e,a,i){var r=JSON.parse(JSON.stringify(t.id));if(this.rid=r,l[r]=this.$ownerInstance,s.default.option[r]=JSON.parse(JSON.stringify(t)),"object"===(0,n.default)(window.echarts))this.newEChart();else{var o=document.createElement("script"),c=window.location.origin,d=i.getDataset().directory;o.src=c+d+"static/h5/echarts.min.js",o.onload=this.newEChart,document.head.appendChild(o)}},ecresize:function(t,e,a,i){s.default.instance[this.rid]&&s.default.instance[this.rid].resize()},newEChart:function(){var t=this.rid;void 0===s.default.instance[t]?(s.default.instance[t]=echarts.init(l[t].$el.children[0]),!0===s.default.option[t].ontap&&s.default.instance[t].on("click",(function(e){var a=JSON.parse(JSON.stringify({x:e.event.offsetX,y:e.event.offsetY}));l[t].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:a,currentIndex:e.dataIndex,value:e.data,seriesName:e.seriesName,id:t}})})),this.updataEChart(t,s.default.option[t])):this.updataEChart(t,s.default.option[t])},updataEChart:function(t,e){if(e=d(e,s.default.formatter),e.tooltip&&(e.tooltip.show=!!e.tooltipShow,e.tooltip.position=this.tooltipPosition(),"string"===typeof e.tooltipFormat&&s.default.formatter[e.tooltipFormat]&&(e.tooltip.formatter=e.tooltip.formatter?e.tooltip.formatter:s.default.formatter[e.tooltipFormat])),e.series)for(var a in e.series){var i=e.series[a].linearGradient;i&&(e.series[a].color=new echarts.graphic.LinearGradient(i[0],i[1],i[2],i[3],i[4]))}s.default.instance[t].setOption(e,e.notMerge),s.default.instance[t].on("finished",(function(){l[t].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:t}}),s.default.instance[t]&&s.default.instance[t].off("finished")}))},tooltipPosition:function(){return function(t,e,a,i,n){var r=t[0],o=t[1],s=n.viewSize[0],l=n.viewSize[1],c=n.contentSize[0],d=n.contentSize[1],h=r+30,p=o+30;return h+c>s&&(h=r-c-30),p+d>l&&(p=o-d-30),[h,p]}},ucinit:function(t,e,a,i){var n=JSON.parse(JSON.stringify(t.canvasId));this.rid=n,l[n]=this.$ownerInstance,o.default.option[n]=JSON.parse(JSON.stringify(t)),o.default.option[n]=d(o.default.option[n],o.default.formatter);var r=document.getElementById(n);r&&r.children[0]&&(o.default.option[n].context=r.children[0].getContext("2d"),this.newUChart())},newUChart:function(){var t=this.rid;o.default.instance[t]=new r.default(o.default.option[t]),o.default.instance[t].addEventListener("renderComplete",(function(){l[t].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:t}}),o.default.instance[t].delEventListener("renderComplete")})),o.default.instance[t].addEventListener("scrollLeft",(function(){l[t].callMethod("emitMsg",{name:"scrollLeft",params:{type:"scrollLeft",scrollLeft:!0,id:t}})})),o.default.instance[t].addEventListener("scrollRight",(function(){l[t].callMethod("emitMsg",{name:"scrollRight",params:{type:"scrollRight",scrollRight:!0,id:t}})}))},tooltipDefault:function(t,e,a,i){if(e){var r=t.data;return"object"===(0,n.default)(t.data)&&(r=t.data.value),e+" "+t.name+":"+r}return void 0!==t.properties?t.properties.name:t.name+":"+t.data},showTooltip:function(t,e){var a=this,i=o.default.option[e].tooltipCustom;if(i&&void 0!==i&&null!==i){var n=void 0;i.x>=0&&i.y>=0&&(n={x:i.x,y:i.y+10}),o.default.instance[e].showToolTip(t,{index:i.index,offset:n,textList:i.textList,formatter:function(t,i,n,r){return"string"===typeof o.default.option[e].tooltipFormat&&o.default.formatter[o.default.option[e].tooltipFormat]?o.default.formatter[o.default.option[e].tooltipFormat](t,i,n,r):a.tooltipDefault(t,i,n,r)}})}else o.default.instance[e].showToolTip(t,{formatter:function(t,i,n,r){return"string"===typeof o.default.option[e].tooltipFormat&&o.default.formatter[o.default.option[e].tooltipFormat]?o.default.formatter[o.default.option[e].tooltipFormat](t,i,n,r):a.tooltipDefault(t,i,n,r)}})},tap:function(t){var e=this.rid,a=o.default.option[e].ontap,i=o.default.option[e].tooltipShow;if(0!=a){var n,r,s=document.getElementById("UC"+e).getBoundingClientRect(),d={};d=t.detail.x?{x:t.detail.x-s.left,y:t.detail.y-s.top+c.top}:{x:t.clientX-s.left,y:t.clientY-s.top+c.top},t.changedTouches.unshift(d),n=o.default.instance[e].getCurrentDataIndex(t),r=o.default.instance[e].getLegendDataIndex(t),o.default.instance[e].touchLegend(t),1==i&&this.showTooltip(t,e),l[e].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:d,currentIndex:n,legendIndex:r,id:e}})}},touchStart:function(t){var e=this.rid,a=o.default.option[e].ontouch;0!=a&&(o.default.instance[e].scrollStart(t),l[e].callMethod("emitMsg",{name:"getTouchStart",params:{type:"touchStart",event:t.changedTouches[0],id:e}}))},touchMove:function(t){var e=this.rid,a=o.default.option[e].ontouch;if(0!=a&&(o.default.instance[e].scroll(t),l[e].callMethod("emitMsg",{name:"getTouchMove",params:{type:"touchMove",event:t.changedTouches[0],id:e}}),!0===o.default.option[e].ontap&&!1===o.default.option[e].enableScroll&&!0===o.default.option[e].onmovetip)){var i=document.getElementById("UC"+e).getBoundingClientRect(),n={x:t.changedTouches[0].clientX-i.left,y:t.changedTouches[0].clientY-i.top+c.top};t.changedTouches.unshift(n),!0===o.default.option[e].tooltipShow&&this.showTooltip(t,e)}},touchEnd:function(t){var e=this.rid,a=o.default.option[e].ontouch;0!=a&&(o.default.instance[e].scrollEnd(t),l[e].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"touchEnd",event:t.changedTouches[0],id:e}}))},mouseDown:function(t){var e=this.rid,a=o.default.option[e].onmouse;if(0!=a){var i,n=document.getElementById("UC"+e).getBoundingClientRect();i={x:t.clientX-n.left,y:t.clientY-n.top+c.top},t.changedTouches.unshift(i),o.default.instance[e].scrollStart(t),o.default.option[e].mousedown=!0,l[e].callMethod("emitMsg",{name:"getTouchStart",params:{type:"mouseDown",event:i,id:e}})}},mouseMove:function(t){var e=this.rid,a=o.default.option[e].onmouse,i=o.default.option[e].tooltipShow;if(0!=a){var n,r=document.getElementById("UC"+e).getBoundingClientRect();n={x:t.clientX-r.left,y:t.clientY-r.top+c.top},t.changedTouches.unshift(n),o.default.option[e].mousedown?(o.default.instance[e].scroll(t),l[e].callMethod("emitMsg",{name:"getTouchMove",params:{type:"mouseMove",event:n,id:e}})):o.default.instance[e]&&1==i&&this.showTooltip(t,e)}},mouseUp:function(t){var e=this.rid,a=o.default.option[e].onmouse;if(0!=a){var i,n=document.getElementById("UC"+e).getBoundingClientRect();i={x:t.clientX-n.left,y:t.clientY-n.top+c.top},t.changedTouches.unshift(i),o.default.instance[e].scrollEnd(t),o.default.option[e].mousedown=!1,l[e].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"mouseUp",event:i,id:e}})}}}};e.default=h},"8e4d":function(t,e,a){a("7a76"),a("c9b5"),t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports["default"]=t.exports},"8ec9":function(t,e,a){"use strict";a.r(e);var i=a("3d98"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},a738:function(t,e,a){"use strict";a.r(e);var i=a("77a4"),n=a("80d5");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("a838");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"7ad6b4f1",null,!1,i["a"],void 0);e["default"]=s.exports},a7c9:function(t,e,a){"use strict";var i=a("457c"),n=a.n(i);n.a},a838:function(t,e,a){"use strict";var i=a("3e11"),n=a.n(i);n.a},a927:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".container[data-v-7ad6b4f1]{width:30px;height:30px;position:relative}.container.loading2[data-v-7ad6b4f1]{-webkit-transform:rotate(10deg);transform:rotate(10deg)}.container.loading2 .shape[data-v-7ad6b4f1]{border-radius:5px}.container.loading2[data-v-7ad6b4f1]{-webkit-animation:rotation 1s infinite;animation:rotation 1s infinite}.container .shape[data-v-7ad6b4f1]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-7ad6b4f1]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-7ad6b4f1]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-7ad6b4f1]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-7ad6b4f1]{bottom:0;right:0;background-color:#e66}.loading2 .shape1[data-v-7ad6b4f1]{-webkit-animation:animation2shape1-data-v-7ad6b4f1 .5s ease 0s infinite alternate;animation:animation2shape1-data-v-7ad6b4f1 .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape1-data-v-7ad6b4f1{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,20px);transform:translate(20px,20px)}}@keyframes animation2shape1-data-v-7ad6b4f1{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,20px);transform:translate(20px,20px)}}.loading2 .shape2[data-v-7ad6b4f1]{-webkit-animation:animation2shape2-data-v-7ad6b4f1 .5s ease 0s infinite alternate;animation:animation2shape2-data-v-7ad6b4f1 .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape2-data-v-7ad6b4f1{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,20px);transform:translate(-20px,20px)}}@keyframes animation2shape2-data-v-7ad6b4f1{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,20px);transform:translate(-20px,20px)}}.loading2 .shape3[data-v-7ad6b4f1]{-webkit-animation:animation2shape3-data-v-7ad6b4f1 .5s ease 0s infinite alternate;animation:animation2shape3-data-v-7ad6b4f1 .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape3-data-v-7ad6b4f1{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,-20px);transform:translate(20px,-20px)}}@keyframes animation2shape3-data-v-7ad6b4f1{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(20px,-20px);transform:translate(20px,-20px)}}.loading2 .shape4[data-v-7ad6b4f1]{-webkit-animation:animation2shape4-data-v-7ad6b4f1 .5s ease 0s infinite alternate;animation:animation2shape4-data-v-7ad6b4f1 .5s ease 0s infinite alternate}@-webkit-keyframes animation2shape4-data-v-7ad6b4f1{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,-20px);transform:translate(-20px,-20px)}}@keyframes animation2shape4-data-v-7ad6b4f1{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-20px,-20px);transform:translate(-20px,-20px)}}",""]),t.exports=e},a982:function(t,e,a){"use strict";(function(t){a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("d4b5"),a("4626"),a("5ac7"),a("aa9c"),a("fd3c"),a("5c47");var n=i(a("fcf3")),r=(i(a("6791")),i(a("b6cc"))),o=i(a("b767"));function s(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length,a=new Array(e>1?e-1:0),i=1;i<e;i++)a[i-1]=arguments[i];for(var r in a)for(var o in a[r])a[r].hasOwnProperty(o)&&(t[o]=a[r][o]&&"object"===(0,n.default)(a[r][o])?s(Array.isArray(a[r][o])?[]:{},t[o],a[r][o]):a[r][o]);return t}function l(t){var e=t.getFullYear(),a=t.getMonth()+1,i=t.getDate();a>=1&&a<=9&&(a="0"+a),i>=0&&i<=9&&(i="0"+i);var n=e+"-"+a+"-"+i;return n}var c={name:"qiun-data-charts",mixins:[t.mixinDatacom],props:{type:{type:String,default:null},canvasId:{type:String,default:"uchartsid"},canvas2d:{type:Boolean,default:!1},background:{type:String,default:"none"},animation:{type:Boolean,default:!0},chartData:{type:Object,default:function(){return{categories:[],series:[]}}},opts:{type:Object,default:function(){return{}}},eopts:{type:Object,default:function(){return{}}},loadingType:{type:Number,default:2},errorShow:{type:Boolean,default:!0},errorMessage:{type:String,default:null},inScrollView:{type:Boolean,default:!1},reshow:{type:Boolean,default:!1},reload:{type:Boolean,default:!1},disableScroll:{type:Boolean,default:!1},ontap:{type:Boolean,default:!0},ontouch:{type:Boolean,default:!1},onmouse:{type:Boolean,default:!0},onmovetip:{type:Boolean,default:!1},echartsH5:{type:Boolean,default:!1},echartsApp:{type:Boolean,default:!1},tooltipShow:{type:Boolean,default:!0},tooltipFormat:{type:String,default:void 0},tooltipCustom:{default:void 0},startDate:{type:String,default:void 0},endDate:{type:String,default:void 0},textEnum:{type:Array,default:function(){return[]}},groupEnum:{type:Array,default:function(){return[]}},pageScrollTop:{type:Number,default:0},directory:{type:String,default:"/"}},data:function(){return{cid:"uchartsid",inWx:!1,inAli:!1,inTt:!1,inBd:!1,inH5:!1,inApp:!1,type2d:!0,disScroll:!1,pixel:1,cWidth:375,cHeight:250,showchart:!1,echarts:!1,echartsResize:!1,uchartsOpts:{canvasId:"ngWYfF8K5Nr4cB760dzmDRw2QLF6tvHm"},echartsOpts:{},drawData:{},lastDrawTime:null}},created:function(){if(this.cid=this.canvasId,"uchartsid"==this.canvasId||""==this.canvasId){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",e=t.length,a="",i=0;i<32;i++)a+=t.charAt(Math.floor(Math.random()*e));this.cid=a}this.type2d=!1,this.disScroll=this.disableScroll},mounted:function(){var t=this;this.inH5=!0,!0===this.echartsH5&&(this.echarts=!0),this.$nextTick((function(){t.beforeInit()})),uni.onWindowResize((function(e){if(1!=t.mixinDatacomLoading){var a=t.mixinDatacomErrorMessage;null!==a&&"null"!==a&&""!==a||setTimeout((function(){t.echarts?t.echartsResize=!t.echartsResize:t.resizeHandler()}),200)}}))},destroyed:function(){!0===this.echarts?(delete o.default.option[this.cid],delete o.default.instance[this.cid]):(delete r.default.option[this.cid],delete r.default.instance[this.cid]),uni.offWindowResize((function(){}))},watch:{chartDataProps:{handler:function(t,e){"object"===(0,n.default)(t)?JSON.stringify(t)!==JSON.stringify(e)&&(t.series&&t.series.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this.showchart=!1,this.mixinDatacomErrorMessage=null)):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：chartData数据类型错误")},immediate:!1,deep:!0},localdata:{handler:function(t,e){JSON.stringify(t)!==JSON.stringify(e)&&(t.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this.showchart=!1,this.mixinDatacomErrorMessage=null))},immediate:!1,deep:!0},optsProps:{handler:function(t,e){"object"===(0,n.default)(t)?JSON.stringify(t)!==JSON.stringify(e)&&!1===this.echarts&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：opts数据类型错误")},immediate:!1,deep:!0},eoptsProps:{handler:function(t,e){"object"===(0,n.default)(t)?JSON.stringify(t)!==JSON.stringify(e)&&!0===this.echarts&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：eopts数据类型错误")},immediate:!1,deep:!0},reshow:function(t,e){var a=this;!0===t&&!1===this.mixinDatacomLoading&&setTimeout((function(){a.showchart=!0,a.mixinDatacomErrorMessage=null,a.echartsResize=!a.echartsResize,a.checkData(a.drawData)}),200)},reload:function(t,e){!0===t&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())},mixinDatacomErrorMessage:function(t,e){t&&(this.emitMsg({name:"error",params:{type:"error",errorShow:this.errorShow,msg:t,id:this.cid}}),this.errorShow&&console.log("[秋云图表组件]"+t))},errorMessage:function(t,e){t&&this.errorShow&&null!==t&&"null"!==t&&""!==t?(this.showchart=!1,this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=t):(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())}},computed:{optsProps:function(){return JSON.parse(JSON.stringify(this.opts))},eoptsProps:function(){return JSON.parse(JSON.stringify(this.eopts))},chartDataProps:function(){return JSON.parse(JSON.stringify(this.chartData))}},methods:{beforeInit:function(){this.mixinDatacomErrorMessage=null,"object"===(0,n.default)(this.chartData)&&null!=this.chartData&&void 0!==this.chartData.series&&this.chartData.series.length>0?(this.mixinDatacomLoading=!0,this.drawData=s({},this.chartData),this.checkData(this.chartData)):this.localdata.length>0?(this.mixinDatacomLoading=!0,this.localdataInit(this.localdata)):""!==this.collection?(this.mixinDatacomLoading=!1,this.getCloudData()):this.mixinDatacomLoading=!0},localdataInit:function(t){if(this.groupEnum.length>0)for(var e=0;e<t.length;e++)for(var a=0;a<this.groupEnum.length;a++)t[e].group===this.groupEnum[a].value&&(t[e].group=this.groupEnum[a].text);if(this.textEnum.length>0)for(var i=0;i<t.length;i++)for(var n=0;n<this.textEnum.length;n++)t[i].text===this.textEnum[n].value&&(t[i].text=this.textEnum[n].text);var c=!1,d={categories:[],series:[]},h=[],p=[];if(c=!0===this.echarts?o.default.categories.includes(this.type):r.default.categories.includes(this.type),!0===c){if(this.chartData&&this.chartData.categories&&this.chartData.categories.length>0)h=this.chartData.categories;else if(this.startDate&&this.endDate){var f=new Date(this.startDate),u=new Date(this.endDate);while(f<=u)h.push(l(f)),f=f.setDate(f.getDate()+1),f=new Date(f)}else{var x={};t.map((function(t,e){void 0==t.text||x[t.text]||(h.push(t.text),x[t.text]=!0)}))}d.categories=h}var m={};if(t.map((function(t,e){void 0==t.group||m[t.group]||(p.push({name:t.group,data:[]}),m[t.group]=!0)})),0==p.length)if(p=[{name:"默认分组",data:[]}],!0===c)for(var g=0;g<h.length;g++){for(var v=0,y=0;y<t.length;y++)t[y].text==h[g]&&(v=t[y].value);p[0].data.push(v)}else for(var b=0;b<t.length;b++)p[0].data.push({name:t[b].text,value:t[b].value});else for(var w=0;w<p.length;w++)if(h.length>0)for(var A=0;A<h.length;A++){for(var C=0,S=0;S<t.length;S++)p[w].name==t[S].group&&t[S].text==h[A]&&(C=t[S].value);p[w].data.push(C)}else for(var T=0;T<t.length;T++)p[w].name==t[T].group&&p[w].data.push(t[T].value);d.series=p,this.drawData=s({},d),this.checkData(d)},reloading:function(){this.showchart=!1,this.mixinDatacomErrorMessage=null,""!==this.collection?(this.mixinDatacomLoading=!1,this.onMixinDatacomPropsChange(!0)):this.beforeInit()},checkData:function(t){var e=this,a=this.cid;!0===this.echarts?(this.type&&o.default.type.includes(this.type)?o.default.option[a]=s({},o.default[this.type],this.eopts):o.default.option[a]=s({},this.eopts),o.default.option[a].id=a):this.type&&r.default.type.includes(this.type)?(r.default.option[a]=s({},r.default[this.type],this.opts),r.default.option[a].canvasId=a):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：props参数中type类型不正确");var i=s({},t);if(void 0!==i.series&&i.series.length>0)if(this.mixinDatacomErrorMessage=null,!0===this.echarts){o.default.option[a].xAxis&&o.default.option[a].xAxis.type&&"category"===o.default.option[a].xAxis.type&&(o.default.option[a].xAxis.data=i.categories),o.default.option[a].yAxis&&o.default.option[a].yAxis.type&&"category"===o.default.option[a].yAxis.type&&(o.default.option[a].yAxis.data=i.categories),o.default.option[a].series=[];for(var n=0;n<i.series.length;n++){o.default.option[a].seriesTemplate=o.default.option[a].seriesTemplate?o.default.option[a].seriesTemplate:{};var l=s({},o.default.option[a].seriesTemplate,i.series[n]);o.default.option[a].series.push(l)}this.$nextTick((function(){e.init()}))}else r.default.option[a].categories=i.categories,r.default.option[a].series=i.series,this.$nextTick((function(){e.init()}))},resizeHandler:function(){var t=this,e=Date.now(),a=this.lastDrawTime?this.lastDrawTime:e-3e3,i=e-a;if(!(i<1e3))uni.createSelectorQuery().in(this).select("#ChartBoxId"+this.cid).boundingClientRect((function(e){t.showchart=!0,e.width>0&&e.height>0&&(e.width===t.cWidth&&e.height===t.cHeight||t.checkData(t.drawData))})).exec()},getCloudData:function(){var t=this;1!=this.mixinDatacomLoading&&(this.mixinDatacomLoading=!0,this.mixinDatacomGet().then((function(e){t.mixinDatacomResData=e.result.data,t.localdataInit(t.mixinDatacomResData)})).catch((function(e){t.mixinDatacomLoading=!1,t.showchart=!1,t.mixinDatacomErrorMessage="请求错误："+e})))},onMixinDatacomPropsChange:function(t,e){1==t&&""!==this.collection&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this._clearChart(),this.getCloudData())},_clearChart:function(){var t=this.cid;if(!0!==this.echrts){var e=uni.createCanvasContext(t,this);e.clearRect(0,0,this.cWidth,this.cHeight),e.draw()}},init:function(){var t=this,e=this.cid;uni.createSelectorQuery().in(this).select("#ChartBoxId"+e).boundingClientRect((function(a){a.width>0&&a.height>0?(t.lastDrawTime=Date.now(),t.cWidth=a.width,t.cHeight=a.height,!0!==t.echarts&&(r.default.option[e].background="none"==t.background?"#FFFFFF":t.background,r.default.option[e].canvas2d=t.type2d,r.default.option[e].pixelRatio=t.pixel,r.default.option[e].animation=t.animation,r.default.option[e].width=a.width*t.pixel,r.default.option[e].height=a.height*t.pixel,r.default.option[e].ontap=t.ontap,r.default.option[e].ontouch=t.ontouch,r.default.option[e].onmouse=t.onmouse,r.default.option[e].onmovetip=t.onmovetip,r.default.option[e].tooltipShow=t.tooltipShow,r.default.option[e].tooltipFormat=t.tooltipFormat,r.default.option[e].tooltipCustom=t.tooltipCustom,r.default.option[e].inScrollView=t.inScrollView,r.default.option[e].lastDrawTime=t.lastDrawTime),t.inH5||t.inApp?1==t.echarts?(t.mixinDatacomLoading=!1,t.showchart=!0,o.default.option[e].ontap=t.ontap,o.default.option[e].onmouse=t.onmouse,o.default.option[e].tooltipShow=t.tooltipShow,o.default.option[e].tooltipFormat=t.tooltipFormat,o.default.option[e].tooltipCustom=t.tooltipCustom,o.default.option[e].lastDrawTime=t.lastDrawTime,o.default.option[e].rotateLock=o.default.option[e].rotate,t.echartsOpts=s({},o.default.option[e])):(r.default.option[e].rotateLock=r.default.option[e].rotate,t.mixinDatacomLoading=!1,t.showchart=!0,t.uchartsOpts=s({},r.default.option[e])):(r.default.option[e]=function t(e,a){for(var i in e)"object"===(0,n.default)(e[i])?t(e[i],a):"format"===i&&"string"===typeof e[i]&&(e["formatter"]=a[e[i]]?a[e[i]]:void 0);return e}(r.default.option[e],r.default.formatter),t.mixinDatacomErrorMessage=null,t.mixinDatacomLoading=!1,t.showchart=!0,t.$nextTick((function(){if(!0===t.type2d){var i=uni.createSelectorQuery().in(t);i.select("#"+e).fields({node:!0,size:!0}).exec((function(i){if(i[0]){var n=i[0].node,o=n.getContext("2d");r.default.option[e].context=o,n.width=a.width*t.pixel,n.height=a.height*t.pixel,n._width=a.width*t.pixel,n._height=a.height*t.pixel,r.default.option[e].rotateLock=r.default.option[e].rotate,t._newChart(e)}else t.showchart=!1,t.mixinDatacomErrorMessage="参数错误：开启2d模式后，未获取到dom节点，canvas-id:"+e}))}else t.inAli&&(r.default.option[e].rotateLock=r.default.option[e].rotate),r.default.option[e].context=uni.createCanvasContext(e,t),t._newChart(e)})))):(t.mixinDatacomLoading=!1,t.showchart=!1,1==t.reshow&&(t.mixinDatacomErrorMessage="布局错误：未获取到父元素宽高尺寸！canvas-id:"+e))})).exec()},saveImage:function(){var t=this;uni.canvasToTempFilePath({canvasId:this.cid,success:function(e){var a=document.createElement("a");a.href=e.tempFilePath,a.download=t.cid,a.target="_blank",a.click()}},this)},_error:function(t){this.mixinDatacomErrorMessage=t.detail.errMsg},emitMsg:function(t){this.$emit(t.name,t.params)},getRenderType:function(){!0===this.echarts&&!1===this.mixinDatacomLoading&&this.beforeInit()},toJSON:function(){return this}}};e.default=c}).call(this,a("861b")["uniCloud"])},ad03:function(t,e,a){"use strict";var i=a("657c"),n=a.n(i);n.a},ae5a:function(t,e,a){"use strict";var i=a("af72"),n=a.n(i);n.a},af72:function(t,e,a){var i=a("3951");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("6c58130f",i,!0,{sourceMap:!1,shadowMode:!1})},b6cc:function(t,e){var a=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"];t.exports={type:["pie","ring","rose","word","funnel","map","arcbar","line","column","area","radar","gauge","candle","mix","demotype"],range:["饼状图","圆环图","玫瑰图","词云图","漏斗图","地图","圆弧进度条","折线图","柱状图","区域图","雷达图","仪表盘","K线图","混合图","自定义类型"],categories:["line","column","area","radar","gauge","candle","mix","demotype"],instance:{},option:{},formatter:{yAxisDemo1:function(t){return t+"元"},yAxisDemo2:function(t){return t.toFixed(2)},seriesDemo1:function(t){return t+"元"},tooltipDemo1:function(t,e,a,i){return 0==a?"随便用"+t.data+"年":"其他我没改"+t.data+"天"},pieDemo:function(t,e,a){if(void 0!==e)return a[e].name+"："+a[e].data+"元"}},demotype:{type:"line",color:a,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"curve",width:2}}},pie:{type:"pie",color:a,padding:[5,5,5,5],extra:{pie:{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},ring:{type:"ring",color:a,padding:[5,5,5,5],rotate:!1,dataLabel:!0,legend:{show:!0,position:"right",lineHeight:25},title:{name:"收益率",fontSize:15,color:"#666666"},subtitle:{name:"70%",fontSize:25,color:"#7cb5ec"},extra:{ring:{ringWidth:30,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},rose:{type:"rose",color:a,padding:[5,5,5,5],legend:{show:!0,position:"left",lineHeight:25},extra:{rose:{type:"area",minRadius:50,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF"}}},word:{type:"word",color:a,extra:{word:{type:"normal",autoColors:!1}}},funnel:{type:"funnel",color:a,padding:[15,15,0,15],extra:{funnel:{activeOpacity:.3,activeWidth:10,border:!0,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,labelAlign:"right"}}},map:{type:"map",color:a,padding:[0,0,0,0],dataLabel:!0,extra:{map:{border:!0,borderWidth:1,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#F04864",activeFillColor:"#FACC14",activeFillOpacity:1}}},arcbar:{type:"arcbar",color:a,title:{name:"百分比",fontSize:25,color:"#00FF00"},subtitle:{name:"默认标题",fontSize:15,color:"#666666"},extra:{arcbar:{type:"default",width:12,backgroundColor:"#E9E9E9",startAngle:.75,endAngle:.25,gap:2}}},line:{type:"line",color:a,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"straight",width:2}}},column:{type:"column",color:a,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{},legend:{},extra:{column:{type:"group",width:30,meterBorde:1,meterFillColor:"#FFFFFF",activeBgColor:"#000000",activeBgOpacity:.08}}},area:{type:"area",color:a,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{area:{type:"straight",opacity:.2,addLine:!0,width:2,gradient:!1}}},radar:{type:"radar",color:a,padding:[5,5,5,5],legend:{show:!0,position:"right",lineHeight:25},extra:{radar:{gridType:"radar",gridColor:"#CCCCCC",gridCount:3,opacity:.2,labelColor:"#666666",max:200}}},gauge:{type:"gauge",color:a,title:{name:"66Km/H",fontSize:25,color:"#2fc25b",offsetY:50},subtitle:{name:"实时速度",fontSize:15,color:"#1890ff",offsetY:-50},extra:{gauge:{type:"default",width:30,labelColor:"#666666",startAngle:.75,endAngle:.25,startNumber:0,endNumber:100,labelFormat:"",splitLine:{fixRadius:0,splitNumber:10,width:30,color:"#FFFFFF",childNumber:5,childWidth:12},pointer:{width:24,color:"auto"}}}},candle:{type:"candle",color:a,padding:[15,15,0,15],enableScroll:!0,enableMarkLine:!0,dataLabel:!1,xAxis:{labelCount:4,itemCount:40,disableGrid:!0,gridColor:"#CCCCCC",gridType:"solid",dashLength:4,scrollShow:!0,scrollAlign:"left",scrollColor:"#A6A6A6",scrollBackgroundColor:"#EFEBEF"},yAxis:{},legend:{},extra:{candle:{color:{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},average:{show:!0,name:["MA5","MA10","MA30"],day:[5,10,20],color:["#1890ff","#2fc25b","#facc14"]}},markLine:{type:"dash",dashLength:5,data:[{value:2150,lineColor:"#f04864",showLabel:!0},{value:2350,lineColor:"#f04864",showLabel:!0}]}}},mix:{type:"mix",color:a,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{disabled:!1,disableGrid:!1,splitNumber:5,gridType:"dash",dashLength:4,gridColor:"#CCCCCC",padding:10,showTitle:!0,data:[]},legend:{},extra:{mix:{column:{width:20}}}},point:{type:"point",color:a,padding:[15,15,0,15]},bubble:{type:"bubble",color:a,padding:[15,15,0,15]}}},b767:function(t,e){var a=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"];t.exports={type:["pie","ring","rose","funnel","line","column","area","radar","gauge","candle","demotype"],categories:["line","column","area","radar","gauge","candle","demotype"],instance:{},option:{},formatter:{tooltipDemo1:function(t){var e="";for(var a in t){0==a&&(e+=t[a].axisValueLabel+"年销售额");var i="--";null!==t[a].data&&(i=t[a].data),e+="\n"+t[a].seriesName+"："+i+" 万元"}return e},legendFormat:function(t){return"自定义图例+"+t},yAxisFormatDemo:function(t,e){return t+"元"},seriesFormatDemo:function(t){return t.name+"年"+t.value+"元"}},demotype:{color:a},column:{color:a,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"bar",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},line:{color:a,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},area:{color:a,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],areaStyle:{},label:{show:!0,color:"#666666",position:"top"}}},pie:{color:a,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:"50%",label:{show:!0,color:"#666666",position:"top"}}},ring:{color:a,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,color:"#666666",position:"top"},labelLine:{show:!0}}},rose:{color:a,title:{text:""},tooltip:{trigger:"item"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"pie",data:[],radius:"55%",center:["50%","50%"],rosetype:"area"}},funnel:{color:a,title:{text:""},tooltip:{trigger:"item",formatter:"{b} : {c}%"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:100,minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{bordercolor:"#fff",borderwidth:1},emphasis:{label:{fontSize:20}},data:[]}},gauge:{color:a,tooltip:{formatter:"{a} <br/>{b} : {c}%"},seriesTemplate:{name:"业务指标",type:"gauge",detail:{formatter:"{value}%"},data:[{value:50,name:"完成率"}]}},candle:{xAxis:{data:[]},yAxis:{},color:a,title:{text:""},dataZoom:[{type:"inside",xAxisIndex:[0,1],start:10,end:100},{show:!0,xAxisIndex:[0,1],type:"slider",bottom:10,start:10,end:100}],seriesTemplate:{name:"",type:"k",data:[]}}}},b8ee:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".chartsview[data-v-de9bfb5e]{width:100%;height:100%;display:flex;flex:1;justify-content:center;align-items:center}",""]),t.exports=e},c0ee:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("aa9c"),a("e966");var i=a("3cb1"),n={data:function(){return{statType:"expected_earnings_total_money",statTypeArr:{expected_earnings_total_money:"预计收入",billing_money:"开单金额数",billing_count:"开单数量",buycard_money:"办卡金额数",buycard_count:"办卡数",recharge_money:"会员充值金额",recharge_count:"会员充值数量",refund_money:"会员退款金额",refund_count:"会员退款数量",order_member_count:"门店下单会员数",balance_money:"会员余额消费金额",online_pay_money:"商城订单",online_refund_money:"退款维权"},dateType:"today",timeObj:{today:[],yesterday:[],week:[],month:[],custom:[]},chartData:{categories:[],series:[]},businessData:null,chartsOpts:{enableScroll:!0,xAxis:{scrollShow:!0,itemCount:24,disableGrid:!0}}}},onLoad:function(){this.setDate(),this.getStatData()},onShow:function(){var t=new Date,e=t.getFullYear(),a=t.getMonth()+1,i=t.getDate();this.endDate=e+"-"+a+"-"+i+" 23:59:59"},methods:{resetChartData:function(){this.chartData.categories=[],this.chartData.series=[]},setDate:function(){var t=this.$util.timeTurnTimeStamp(this.$util.timeFormat(Date.now()/1e3,"Y-m-d"));this.timeObj.today=[t,t+86399],this.timeObj.yesterday=[t-86400,t-1],this.timeObj.week=[t-604800,t],this.timeObj.month=[t-2592e3,t]},switchDateType:function(t){if(this.dateType=t,"custom"==t)return this.$refs.customTime.open(),!1;this.chartsOpts.xAxis.itemCount="month"==t?10:24,this.getStatData()},switchStatType:function(t){var e=this;this.statType=t,this.$refs.chartsPop.open(),this.resetChartData(),this.getStatData(),setTimeout((function(){e.getChartData()}),500)},changeTime:function(t){this.timeObj.custom=t,this.chartsOpts.xAxis.itemCount=10},getStatData:function(){"custom"==this.dateType&&(this.$refs.customTime.close(),this.timeObj.custom[0]=1e3*this.$util.timeTurnTimeStamp(this.timeObj.custom[0]),this.timeObj.custom[1]=1e3*this.$util.timeTurnTimeStamp(this.timeObj.custom[1])),this.getBusinessData()},getChartData:function(){var t=this,e={};e.start_time=this.timeObj[this.dateType][0];var a="";"today"==this.dateType||"yesterday"==this.dateType?a=(0,i.getStatHour)(e):(e.end_time=this.timeObj[this.dateType][1],a=(0,i.getStatDay)(e)),a.then((function(e){e.code>=0&&(t.chartData.series=[],t.chartData.series.push({data:e.data[t.statType],name:t.statTypeArr[t.statType]}),t.chartData.categories=e.data.time)}))},getBusinessData:function(){var t=this,e={};e.start_time="custom"==this.dateType?parseInt(this.timeObj[this.dateType][0]/1e3):parseInt(this.timeObj[this.dateType][0]),e.end_time="custom"==this.dateType?parseInt(this.timeObj[this.dateType][1]/1e3):parseInt(this.timeObj[this.dateType][1]),(0,i.getStatTotal)(e).then((function(e){e.code>=0&&(t.businessData=e.data)}))}}};e.default=n},c4f4:function(t,e,a){"use strict";a.r(e);var i=a("51de"),n=a("d766");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("ae5a");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"be0a6fc8",null,!1,i["a"],void 0);e["default"]=s.exports},c561:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.chartsview[data-v-307d175c]{width:100%;height:100%;display:flex;flex-direction:column;flex:1;justify-content:center;align-items:center}.charts-font[data-v-307d175c]{font-size:14px;color:#ccc;margin-top:10px}.charts-error[data-v-307d175c]{width:128px;height:128px;background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAUz0lEQVR4Xu1de3Bc1X3+zmp3jYVWfkPAgCWwY8zLEglNQCSW0yT9o2SQaDKdNulUykwfM+k09p+J3ImYIPJXi9yZzDSZZiwyaZuZBCwnNG1DMogEmUAAy7xs/MAyNhCMjWWtsK1deU/n23OvtLu6j3Pv3t29d71nxjOSde455/5+3/m9z7kCjRY4BR7eK7fkcmhrasLT37hbTAY+QYADigDHagwFYGhc7gZwHMAUgG4hMPzNe8RoWInTAECAnHl4r+yREt0DXWIbhx3cJ5fHP8TYjntFR4DTBDqUIwBmMrJDCtyPHDoQw0Q8jkeXinCLtECp43Gwh56R22IxTBbu+KFxOTbQJbo9DlW17rYASGdlNySeKl2JADpbkmKiait0mWhoXHZkmzE52CkocmvavvOsbMvl8MhAl+jlQrg2CQzu6BI9NV2Yw+T2AJiVo+DuL2kSeLQ1KfrC8kLcYbkYBv/pbjEWhjUNjUvSpk9KSAicm2tGXxjAaUcbewBkJAm6xeLBp1PJ2os06ttcDl8H0CEEaGnvGegSg2EAQZTW4B0AEntSS2ov0mhgJc5jmwT6IDEWi2E0zNZ2WEFhC4CZjCRxH7GwAfpbkmIkLC9EFQBg20BXeOySsNBGZx2OXkB6Vg5CgAbMZgD7BTDSkhTDOgM3+kSDAr7iANNz8n4hQRdxojUu9kTjVRurtKKAJwBIKZfPZPOuYWFgY6wlgV4hau+GNVjsnQKeAJDOSIp/Wt6lbSKeQG8jSOSdAbV+wisA7FxDSGAqBmwNS5DIiGIucyNwKiGedutTz3/3BgCb4JBJoGqDIJ2VW4REmxRog0S3lGgT/NlfY3RzCgJjQmJSCkxeDuDwBgCb8HAhvQkCIdCbSgQfmSPDmWGDzHsm1UqwjBEUAMbqERCeAEBGz2RknwR2uW0yAZQdL6DR+WEW90syXLmjtW8So0Jg9MoE9tSD4esZANUAAd1M5NAjGOULaaOkAzCKGEaj7Ar7AoBHEGxrSYqdOnxMZ+W3ZA59ZehxnWkC7yMlJkUMIy1x7IyaVPANAAMEHTlgTACO1rYERlqTot+J8nbp58C5VcEBDftnOEpAKAsAgYPAPgNZQbYFP3QeCAybJ/Bg2CVC2QDwCoJUAtudiJKuExCQLoZbPKirAoOHovuIThVByuXii2jE/C9I2TaXBYsfmThyahMtCWy1A4ERbj7rvvRI9aCa3F7pINm3n5XdXgtjFgHAYCQrW4v8bBo6MYFep5cwmEefuSwQpDNSRoq9+osdrqRaGBqXMhfDVi8gWASAdEbuswuyGCKNSLatBygXBHUqAQohMmHESAKrqzSro4TIS2yOq10dVQQAuyKQUoC7BXnIxHQWwwL4ay/qIM/8DHaFJuijv7M99QzaNmAx6hzQFsvhKSmxvakJo7oHUooA4MUA0wHBTDYfQnVUB6bFnLc1JHqiFgPwxPnSzhKjLUn0B+UpsDoqFkOfLvO5HN8AMN5lOJUU2+2IMD0ne0QOtCcq0k7OANe1VGToag7qaBRXeiFFAJjOyBENsV20Jqcgj2FQHgvyJWYvAQfPAJuvAv7198ADm4DMHJBKAmuag5ypemPpGNiVWk2pDcCDDDQCPTU7EOgmjrxMRgA8dgBYmwJOXwBuWgH87m3gz26OLgDy6q9G9RSLvIAymFZUGsaCjJzE7qB1+vvngXRGQebG5QB/P30eaF2iQBHllk8wxdDfGq/eYVLLQJBfEOQNOpk3/Bg86hbA8iAZwt2/a78asX8zsKRJ/fzYQeDttFIHUbcJqi0JnM4FaOX9g2Sw7lgHTgPTs0DHRxTjT5wDtqzTfTr8/aoJArfTwX055P1519q6apGV4v8/XlU6nzv/vo8CvzwK3L0W2LS6Wquo/DzVAoFrMiivyzVSvpUnycIMVAUU///2kgIC9b+pDqq5jirMVXEX0RUAfMkwgoDrMoFQBUbUcoqxVFJsrdQCtAAQZhBUijChGlfiwdSSypx81gaAAQLLA6OhIlaNF2MGqriMm1cFqJoEtlai0lobAIzqZbPYF7RrV2N+BT79L99c8Eh+dzI474RGYSqB9qDyBuaLawMgnZE8Exjau24C56TPAZ8+vsD0594GPrHW50DWjwVuD2gBwDgm/q1AX6VOB/v5YeDWNerlXnsf+MKGYF/ULQvrdTZXAFQioeN1kVHqTxuAgSo2xiWCdk+DVgWuAEjPyt31XqARJYAZa92ZSqq7CMttjgCoh1r9cgkU1ueDuq7PEQDTs/JY0Nm8sBI0gusKxCCMZDIogsyqzJIDiA3YAqCx+yvDs4BHLVsKBFoPEPDLNYbToUCZUsASAFHY/SfTCxWtq5qBK4ziEB2a1VmfsqTAIgBUupLXL/Evshj0NNCaBM7NAr89sTDSp64HOj/id+ToP1eOR7AYAD4qg6tBwufeARhabV8BfPJaFWxZEgeSMWDTmstaApD8vuMCRQAI47Gs37wFrG4GrksBR882mG212VhW3rpEtPvZiL6OhvmZyOszFPm7D6qqX+76L6xfGIH/l7mkVMGNKy773Z8njIyh1081cfHBkJAEfgqZv2kV8Cmj4HP/H4ADZ1RBqNmYbfvEtV7hVX/9/X7HYR4AYUr6PPmmYjSZ/7kblRT47yOK8Uyu8DxA6xXqHADVw2XsARQhuSWBFV7rBeYBoHsyuNJ7582zwBNHgNVLgb+8TTH/8YOq/o+7ffPVCwynpAiC+W+cUfOcnwOuvlIdO4ti86MG5gGQdrkFtFoE2fUykJ4F+u4AknHgx6+pnf/ZduCW1QD9f3oDPA/A5tcFpA3x8ingxXcVuPJ61Dgt27MRuKG1Wm8c6DyevYE8AMJk/f/nq8BNK5VeN10/k8mvnwZ+ZRw1pfjnYdCOq1VZuJd2/Bzwv0cXGL9hpdr1PGNIgMUF8Oe3AquWehk1FH0nUknR6WUleQCENe37vZeUFOi/AyhUDbQL/JwE5q4ngI4YNxBxl7NiZ5kBoCePAb9/R4mBq64EejcGX9DhhTl++sYTaPdya7sCgPoySM1LvujzM8Dzd3cuMNy08ikZeBqYqoE7njv1t28p3c3f/+hapSLs2jszAI1Luo40JMn4j64s7v3PzympQNeSgLtnLfDxiHkYXkvGFAA0r2Yjsf3sPF0km/r/H+9aEP8P8ORPEhh5ecErIPNpGLJxF7/3oWLcfesV80rboQ+UyDf7f3HTYuPx+XeAX00Ct68BPn0D8O8TasyvbgZakrpvEIp+nuyAPACmM/KsW7k3b+N4/ABA5lSq8cIH0/o39f9f3KoYQYab0uDnR4BjZ4Gv3LYAgB+9qiQB+xe2X0+q4kw22hL8V9roTXz3BTXP1z6mVAJzDfxHqUIDNELN02f9hI7/T6v7zSklbml8MfFCXzzoRp1Pv57n/Wj5M/RrJnn2/QG4rlVJIPM4+DfvWVjB9/YpFfFJIzBEZv7iMHAirUT+59qBO2zcu7zuf1ftfrOKtxAUVElBF3cGTTtzPBaNtiaFhRy0nlHoGIDc/a+fUlez0Dq/ZY26kiXoRoCR0G5WvRkoMnf0/xwFXnwPiAFINgE3LAOmLwKnjPEoKejfW7Xj0+q0Mef92seLVQNLvF95H/iTm4CNJfZC0O8e5HheAkJCNwBkHsv++zuDXKq/sSgd/uu1xf47gfnWOR5mVeNe1Qx8adOClW8l+n8woQzDL9682Cik7fDTg8D1KaD3Zn9rrdRTlHA0mmmvLJJOHopERFg8AK+EIgiomo6dAy7OKdXwNx3AiRngRy8DOQl0XgN8Zp19tJDMJZMLRX/pOh7eqwhMNRCmZtpkVreiePEECADLj0SH6WWd1kIgPP6G2sVty4Av36pE/+43gDMX1K6+b8NiEJhGHqXEV263Bwm9AY731Q6gJVF7qnDnT7ynbCS6zDx8QpXJgNi8JPBwmljouoC1f3X7FdDHH30DmMsBG1YosU+m0Vg8exG4pkV5B2begCFg5htIMCf7gDOadsCXbgGusbEjqkkbMp+iv7QR5POG+eUGADL/rWmgSQBzEui4CvjT9cBUBnjikPqbCYJzF4EfGBdNMW5g5xmYBKY3cOgMsGEVcClXTVY7z+WkArxUCIkoFIA6keLXx4DXTiuDb2ubihdkcsCnDZ+fDPzZIRX+pYvJRBPF6GfbVPRQt124FE4AULJZBOe0YwFUAZG9mv2Fd4G9J4tdOEb0fnNCVQxtuQHoug4gCH5yADgxrdjtZPRZeQqvnFISgEmiMDV6ZjbueP0DgBm9PYes9Tizhr84mr98Ol9Qwps66OszZOyF+WS2GSQKiw2gCcD6BkBMABT9DNLY6XFKgicnVWCIuQR6BMwb0Ojz0hhipg3x5dsilR6ubwA0xwGCgDvaLsJHJtPPP/yBKvQg862SQG5gYByArZI5ELc1+Pi7PgCmM5JfuArNRZBuL0uGMkdg5dsXPktwcPfS4PMq9s1xzDAxS9KZlYxQ0wdAlOIAFOPU5Wxm1s6KKfTzqbvLYT7HNWMANCZZixihVp8AYHSPlrxdWpcMMiN8/Pmua1QW0E+LajbQeNf6A4BZ1MGw5z98bDFLybAnDqvYPptOkMcJGBGuB+BraReFRCYU/OPXgVMfWjOW+p4Gn1nu5RbedZMIHOe7L6pevJa+EqlvtzWU9XdPoeCQ1AM6vbCp+0t3P3c96wJZzMHG6l4WdJR7VuCR54ELc6q4xEu0sCymBfiwp2ygbj1AgOvzPBQjcU8dL9bpNPQops1dT7sgCGYxScSxGURa2+rubXh+mWo84KkeICu7IcFbQEPb6Pqx6mdpXBV9HvlgoRiktLS7nJcw9X4ipuIM9CJWXqHSxVFqXkrDRZgOhdgROZsDvv8ScMnMWgiV9uWOXxfACR6qErp8BJpVY7KF2UW3UrUwgEQC51qTQrtiU1UFz8pJIRDqj67MzAGPva5EPsO7zPkHwXyO98NX1IeomE4myFgnQHAxysiQMkPBLIZlsWoEmrYLyHfxdC6g1i/PxfJkD9O/bOWIf/OaGep7Nur8eEwBoNCFpHQw6wb/9s7yDcwq0FDbBVwAQAQ8gULCUVSPn1y4J4BlX+uWqfIv85iXFaHN4g7uaJ4INr84wtLzrAReeFs99Ve3Fx8O/ZfnVd3hXdeqbxOFuXnxAOYBENaLoZwITWnA3ct6APN0L/tTbFOEXxFXP3OnT11UI5HxhY2HPnjYhCqFpeY8+8BWGGnk89/fB9AOYWPRCXMLYW1eDMB5APCHqBaGEAisDWCI+PDZ4ttDSplEYKxfody79uUKKOapI55J4Glg2gIsJqF6IYAIMvZh/2MGQG5bA3ymLXgI0IATAj3ml0G8VmxLieOtS4SnlYXufoByydoUUwdHeUhkdk6dLqbzwCNndO8KG5nO8DGZblYHs2bwJweLgWQGmFgbyDgBG08OXx+AB1K0IAv/3ctHPP1cExO6G0LKBYDu82Q+q4a52ynSWTlUGEFkKpht+ZJiu4L/T8/g8+1Ac6JY/ejObdcvlRSWRWc8vSUlRt3S9mXdEKJzRrDcFwzL86ynZ7qYzW+tAA3Knx5Qhaa0GcotrHQ706f16T6J0ZYk+r3cE1SEuHRGTgDYHBZGVWIdhcz3WhlcuJ7CdDGDUn/crozJcprbDtYCAeDpY5PFAIiYO+iV2Nz15udcyk0Xc+7CqiPeKPLAxvJAoPO5WOOr7KMugTttEBQBoJ7VQCHzGS9wqiX0AiyCwKxBqBYIjPA9v9LuJK0n4gn0ul0XE5m7gr0wpbSveYqmnDF0nw3qWhm3gI4OCHQkyiIA6NwXoEuMsPSjH8/IX2ausiuiIcjawXJtAXOVgYBAYjKVRKedYWj3vYDQJ4cqy8rwjO4GAq502uWGdyfjsvHFkPDw2n4lAoOphHjQaalOIHACkSUAqF/SWUy6BR6iQLt6WaMERlqTot8rCBheTiTQYWcM2h539BKCrBcih/09dEBAvuWAYW5eMh8x9JnXyBu5hS359xQYa4ljp+N51ygUioSdaUGvjyBIJbDdLdpHKV7YxyqxxNyBIwDq0SMImiE1Gk870GOuz+oEGN1E1xPvUTo6ViNm1GpaTyCwC/O7AoChRwnsq9VbNuZ1pIA2CCy9BIk9rgDg9OmMHAbw9QYzQkkBLRAYkcNRAMoIBPYLoE8LAAYI6j5TGEr26i3KUyFo4ZDaANBMReott9ErUAq41RI4TaYNAA4ShWNkgVI2QoPZVRO5vYItAIbGZYcQWJZZiv2DncIoh8x/XCLSN4u6ESSqfw8UAEPjeaOvj1+kBLCuqQmd37hb8Gfz+0Juueio0jGS6/ZTDGq+6CIJMLhPLk+cx9lYDO1k+kPPyBEhMDnQJQbNh1g4ks1iopErqDhe9scT6JnLYBgC91vN5hbrd1vhIgB851nZlsvhWLYZKyj6DWmAgS6xrXCwhlHoRtry/s4a/1QSHWY4l6FcKbCtcNOxT0ygpyUp6KH5apY2gLHrt1AFCIHOWAwdpgoonKURKvZFc9eHuKtjQHcpY/Mle3NoQw7dsgkTfr4VXDq5rRH48F7Zk8uhrakJo1bMNwdqZA1d+empgx3zPQ3iobMnN9Bu3AYIPFDcoWu1mc+lBAIADsQDpshhpGEY+gNDLZgfCADoNSQvqPLkTSuxfuNq7IwJhODTCv4YUYunsjkc3f8+Bo7M4v8KYy5cixmPKVxXaWymnDWXLQGGxiVjAmaCoZy1XPbPSon9O+4VHSYhaIdJid2lhJESUzvu1f80nBNhywYA3cY5oOhI8uqlaLk+hfWXPUc1CHAijSOnL2CGXePAZKHBzd0PgEG50jZR6pZrTGXZpWwA+J248Vw4KNAAQDj4ULNVBA6Abz8ru8Ul9SVyITCVbUY/DRsjxLxLSmhdYSYEHh3oEiMch2rm0iXssqHSxI57xfZKU/ChZ+QjAOb1c6XnM+g3T4NKzRc4AIbGZR8TSeaCYzH0Ua8ZIeY8QzXbSBEAcvlSZyvwBKYPndb10LjkBQ1a4NV8P51u8zTQ6eynT+AA8LOIxjO1o0ADALWjfShmjhwACgNPoaCgsnWOO+VLwrJOq3VEDgCGLrbMjdeK0EEGZqr9DpEDgFXgqdpEK50vlsPUQJf/nHwt1///89bqDjCPWPAAAAAASUVORK5CYII=");background-position:50%}',""]),t.exports=e},cb8b:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={uniPopup:a("cea0").default,uniDatetimePicker:a("da34").default,qiunDataCharts:a("77a1").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"common-wrap"},[a("v-uni-view",{staticClass:"title"},[t._v("营业数据")]),a("v-uni-view",{staticClass:"choice-day"},[a("v-uni-view",{staticClass:"date-btn",class:"today"==t.dateType?"select":"",attrs:{value:"today"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchDateType("today")}}},[t._v("今日")]),a("v-uni-view",{staticClass:"date-btn",class:"yesterday"==t.dateType?"select":"",attrs:{value:"yesterday"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchDateType("yesterday")}}},[t._v("昨日")]),a("v-uni-view",{staticClass:"date-btn",class:"week"==t.dateType?"select":"",attrs:{value:"week"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchDateType("week")}}},[t._v("7日内")]),a("v-uni-view",{staticClass:"date-btn",class:"month"==t.dateType?"select":"",attrs:{value:"month"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchDateType("month")}}},[t._v("30日内")]),a("v-uni-view",{staticClass:"date-btn",class:"custom"==t.dateType?"select":"",attrs:{value:"custom"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchDateType("custom")}}},[t._v("自定义")]),a("v-uni-view",{staticClass:"report text-color"},[a("v-uni-text",{staticClass:"move iconfont iconicon-test"}),a("v-uni-text")],1)],1),t.businessData?[a("v-uni-view",{staticClass:"title-port"},[t._v("线下收银")]),a("v-uni-view",{staticClass:"money"},[a("v-uni-view",{staticClass:"estimate",class:"expected_earnings_total_money"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("预计收入(元)")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.expected_earnings_total_money||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("expected_earnings_total_money")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"billing_money"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("开单金额数(元)")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.billing_money||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("billing_money")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"billing_count"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("开单数量")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.billing_count||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("billing_count")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"buycard_money"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("办卡金额数(元)")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.buycard_money||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("buycard_money")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"buycard_count"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("办卡数")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.buycard_count||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("buycard_count")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"recharge_money"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("会员充值金额(元)")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.recharge_money||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("recharge_money")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"recharge_count"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("会员充值数量")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.recharge_count||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("recharge_count")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"refund_money"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("会员退款金额(元)")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.refund_money||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("refund_money")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"refund_count"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("会员退款数量")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.refund_count||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("refund_count")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"order_member_count"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("门店下单会员数")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.order_member_count||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("order_member_count")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"balance_money"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("会员余额消费金额")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.balance_money||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("balance_money")}}},[t._v("查看详情")])],1)],1)],1)]:t._e(),t.businessData?[a("v-uni-view",{staticClass:"title-port"},[t._v("线上商城")]),a("v-uni-view",{staticClass:"money"},[a("v-uni-view",{staticClass:"estimate",class:"online_pay_money"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("商城订单(元)")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.online_pay_money||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("online_pay_money")}}},[t._v("查看详情")])],1)],1),a("v-uni-view",{staticClass:"estimate",class:"online_refund_money"==t.statType?"estimate-active":""},[a("v-uni-view",{staticClass:"income"},[a("v-uni-text",{staticClass:"income-name"},[t._v("退款维权(元)")])],1),a("v-uni-view",{staticClass:"num-money"},[a("v-uni-text",{staticClass:"last_income"},[t._v(t._s(t.businessData.online_refund_money||0))]),a("v-uni-text",{staticClass:"detail",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchStatType("online_refund_money")}}},[t._v("查看详情")])],1)],1)],1)]:t._e()],2),a("uni-popup",{ref:"customTime"},[a("v-uni-view",{staticClass:"pop-box"},[a("v-uni-view",{staticClass:"pop-header"},[a("v-uni-view",{staticClass:"pop-header-text"},[t._v("自定义时间选择")]),a("v-uni-view",{staticClass:"pop-header-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.customTime.close()}}},[a("v-uni-text",{staticClass:"iconguanbi1 iconfont"})],1)],1),a("v-uni-view",{staticClass:"pop-content "},[a("uni-datetime-picker",{attrs:{end:t.endDate,clearIcon:!1,type:"datetimerange",rangeSeparator:"至"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTime.apply(void 0,arguments)}},model:{value:t.timeObj.custom,callback:function(e){t.$set(t.timeObj,"custom",e)},expression:"timeObj.custom"}})],1),a("v-uni-view",{staticClass:"pop-bottom"},[a("v-uni-button",{staticClass:"primary-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getStatData()}}},[t._v("确定")])],1)],1)],1),a("uni-popup",{ref:"chartsPop"},[a("v-uni-view",{staticClass:"pop-box charts-pop"},[a("v-uni-view",{staticClass:"pop-header"},[a("v-uni-view",{staticClass:"pop-header-text"},[t._v("运营数据图表展示")]),a("v-uni-view",{staticClass:"pop-header-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.chartsPop.close()}}},[a("v-uni-text",{staticClass:"iconguanbi1 iconfont"})],1)],1),a("v-uni-view",{staticClass:"pop-content"},[a("qiun-data-charts",{attrs:{type:"line",chartData:t.chartData,eopts:{seriesTemplate:{smooth:!0}},ontouch:!0,opts:t.chartsOpts}})],1)],1)],1)],1)},r=[]},cfd1:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"loading6",data:function(){return{}}}},d395:function(t,e,a){"use strict";var i=a("2b6a"),n=a.n(i);n.a},d766:function(t,e,a){"use strict";a.r(e);var i=a("3103"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},d85d:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"chartsview"},[e("v-uni-view",{staticClass:"charts-error"}),e("v-uni-view",{staticClass:"charts-font"},[this._v(this._s(null==this.errorMessage?"请点击重试":this.errorMessage))])],1)},n=[]},d993:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".container[data-v-5f50525e]{width:30px;height:30px;position:relative}.container.loading1[data-v-5f50525e]{-webkit-transform:rotate(45deg);transform:rotate(45deg)}.container .shape[data-v-5f50525e]{position:absolute;width:10px;height:10px;border-radius:1px}.container .shape.shape1[data-v-5f50525e]{left:0;background-color:#1890ff}.container .shape.shape2[data-v-5f50525e]{right:0;background-color:#91cb74}.container .shape.shape3[data-v-5f50525e]{bottom:0;background-color:#fac858}.container .shape.shape4[data-v-5f50525e]{bottom:0;right:0;background-color:#e66}.loading1 .shape1[data-v-5f50525e]{-webkit-animation:animation1shape1-data-v-5f50525e .5s ease 0s infinite alternate;animation:animation1shape1-data-v-5f50525e .5s ease 0s infinite alternate}@-webkit-keyframes animation1shape1-data-v-5f50525e{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(16px,16px);transform:translate(16px,16px)}}@keyframes animation1shape1-data-v-5f50525e{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(16px,16px);transform:translate(16px,16px)}}.loading1 .shape2[data-v-5f50525e]{-webkit-animation:animation1shape2-data-v-5f50525e .5s ease 0s infinite alternate;animation:animation1shape2-data-v-5f50525e .5s ease 0s infinite alternate}@-webkit-keyframes animation1shape2-data-v-5f50525e{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-16px,16px);transform:translate(-16px,16px)}}@keyframes animation1shape2-data-v-5f50525e{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-16px,16px);transform:translate(-16px,16px)}}.loading1 .shape3[data-v-5f50525e]{-webkit-animation:animation1shape3-data-v-5f50525e .5s ease 0s infinite alternate;animation:animation1shape3-data-v-5f50525e .5s ease 0s infinite alternate}@-webkit-keyframes animation1shape3-data-v-5f50525e{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(16px,-16px);transform:translate(16px,-16px)}}@keyframes animation1shape3-data-v-5f50525e{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(16px,-16px);transform:translate(16px,-16px)}}.loading1 .shape4[data-v-5f50525e]{-webkit-animation:animation1shape4-data-v-5f50525e .5s ease 0s infinite alternate;animation:animation1shape4-data-v-5f50525e .5s ease 0s infinite alternate}@-webkit-keyframes animation1shape4-data-v-5f50525e{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-16px,-16px);transform:translate(-16px,-16px)}}@keyframes animation1shape4-data-v-5f50525e{from{-webkit-transform:translate(0);transform:translate(0)}to{-webkit-transform:translate(-16px,-16px);transform:translate(-16px,-16px)}}",""]),t.exports=e},dda4:function(t,e,a){var i=a("6178"),n=a("17bf"),r=a("79b7"),o=a("8e4d");t.exports=function(t,e){return i(t)||n(t,e)||r(t,e)||o()},t.exports.__esModule=!0,t.exports["default"]=t.exports},e476:function(t,e){t.exports=function(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=new Array(e);a<e;a++)i[a]=t[a];return i},t.exports.__esModule=!0,t.exports["default"]=t.exports},f08d:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"loading2",data:function(){return{}}}},f156:function(t,e,a){"use strict";var i=a("fc40"),n=a.n(i);n.a},f811:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"container loading1"},[e("v-uni-view",{staticClass:"shape shape1"}),e("v-uni-view",{staticClass:"shape shape2"}),e("v-uni-view",{staticClass:"shape shape3"}),e("v-uni-view",{staticClass:"shape shape4"})],1)},n=[]},fc40:function(t,e,a){var i=a("882e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("e56ba3e6",i,!0,{sourceMap:!1,shadowMode:!1})},fcc7:function(t,e,a){"use strict";a.r(e);var i=a("a982"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a}}]);