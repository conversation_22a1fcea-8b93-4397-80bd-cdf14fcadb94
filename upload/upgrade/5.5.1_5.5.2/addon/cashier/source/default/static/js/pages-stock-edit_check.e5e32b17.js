(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-stock-edit_check"],{"0aa0":function(t,e,a){"use strict";a.r(e);var o=a("d1e3"),n=a("d2c2");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("ff29");var s=a("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"164bfcdc",null,!1,o["a"],void 0);e["default"]=r.exports},"28b4":function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.addAllocate=function(t){return n.default.post("/stock/storeapi/allocate/addallocate",{data:t})},e.addInventory=function(t){return n.default.post("/stock/storeapi/check/add",{data:t})},e.allocateAgree=function(t){return n.default.post("/stock/storeapi/allocate/agree",{data:{allot_id:t}})},e.allocateDelete=function(t){return n.default.post("/stock/storeapi/allocate/delete",{data:{allot_id:t}})},e.allocateRefuse=function(t){return n.default.post("/stock/storeapi/allocate/refuse",{data:t})},e.editAllocate=function(t){return n.default.post("/stock/storeapi/allocate/editAllocate",{data:t})},e.editInventory=function(t){return n.default.post("/stock/storeapi/check/edit",{data:t})},e.editStorage=function(t){return n.default.post("/stock/storeapi/storage/stockin",{data:t})},e.editWastage=function(t){return n.default.post("/stock/storeapi/wastage/stockout",{data:t})},e.getAllocateDetail=function(t){return n.default.post("/stock/storeapi/allocate/detail",{data:{allot_id:t}})},e.getAllocateDetailInEdit=function(t){return n.default.post("/stock/storeapi/allocate/editData",{data:{allot_id:t}})},e.getAllocateList=function(t){return n.default.post("/stock/storeapi/allocate/lists",{data:t})},e.getAllotNo=function(){return n.default.post("/stock/storeapi/allocate/getAllotNo")},e.getDocumentType=function(){return n.default.post("/stock/storeapi/manage/getDocumentType")},e.getInventoryDetail=function(t){return n.default.post("/stock/storeapi/check/detail",{data:{inventory_id:t}})},e.getInventoryDetailInEdit=function(t){return n.default.post("/stock/storeapi/check/editData",{data:{inventory_id:t}})},e.getInventoryList=function(t){return n.default.post("/stock/storeapi/check/lists",{data:t})},e.getInventoryNo=function(){return n.default.post("/stock/storeapi/Check/getInventoryNo")},e.getSkuListForStock=function(t){return n.default.post("/stock/storeapi/manage/getskulist",{data:t})},e.getStockGoodsList=function(t){return n.default.post("/stock/storeapi/manage/lists",{data:t})},e.getStockGoodsRecords=function(t){return n.default.post("/stock/storeapi/manage/records",{data:t})},e.getStorageDetail=function(t){return n.default.post("/stock/storeapi/storage/detail",{data:{document_id:t}})},e.getStorageDetailInEdit=function(t){return n.default.post("/stock/storeapi/storage/editData",{data:{document_id:t}})},e.getStorageDocumentNo=function(){return n.default.post("/stock/storeapi/storage/getDocumentNo")},e.getStorageLists=function(t){return n.default.post("/stock/storeapi/storage/lists",{data:t})},e.getStoreLists=function(){return n.default.post("/stock/storeapi/store/lists")},e.getWastageDetail=function(t){return n.default.post("/stock/storeapi/wastage/detail",{data:{document_id:t}})},e.getWastageDetailInEdit=function(t){return n.default.post("/stock/storeapi/wastage/editData",{data:{document_id:t}})},e.getWastageDocumentNo=function(){return n.default.post("/stock/storeapi/wastage/getDocumentNo")},e.getWastageLists=function(t){return n.default.post("/stock/storeapi/wastage/lists",{data:t})},e.inventoryAgree=function(t){return n.default.post("/stock/storeapi/check/agree",{data:{inventory_id:t}})},e.inventoryDelete=function(t){return n.default.post("stock/storeapi/check/delete",{data:{inventory_id:t}})},e.inventoryRefuse=function(t){return n.default.post("/stock/storeapi/check/refuse",{data:t})},e.storageAgree=function(t){return n.default.post("/stock/storeapi/storage/agree",{data:{document_id:t}})},e.storageDelete=function(t){return n.default.post("/stock/storeapi/storage/delete",{data:{document_id:t}})},e.storageRefuse=function(t){return n.default.post("/stock/storeapi/storage/refuse",{data:t})};var n=o(a("a3b5"))},"3efd1":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d4b5"),a("aa9c"),a("e966"),a("bf0f"),a("2797"),a("4626"),a("5ac7"),a("5ef2"),a("e838"),a("dd2b"),a("7a76"),a("c9b5");var o=a("28b4"),n={data:function(){return{params:{search_text:""},goodsList:[],goodsIdArr:[],goodsShow:!1,totalData:{kindsNum:0,upNum:0,downNum:0,sameNum:0},screen:{inventory_id:"",inventory_no:"",remark:"",stock_json:"",time:""},remark:"",isSubmit:!1,inputIndex:-1,dialogVisible:!1,info:null}},onLoad:function(t){this.screen.inventory_id=t.inventory_id||"",this.screen.time=this.$util.timeFormat(Date.parse(new Date)/1e3),this.screen.inventory_id?this.getEditData():this.getDocumentNo()},watch:{goodsIdArr:function(t){this.calcTotalData()}},methods:{getDocumentNo:function(){var t=this;(0,o.getInventoryNo)().then((function(e){e.code>=0?t.screen.inventory_no=e.data:t.$util.showToast({title:e.message})}))},getEditData:function(){var t=this;(0,o.getInventoryDetailInEdit)(this.screen.inventory_id).then((function(e){if(e.code>=0&&e.data)for(var a in t.info=e.data,t.screen.inventory_no=t.info.inventory_no,t.screen.time=t.$util.timeFormat(t.info.action_time),t.remark=JSON.parse(JSON.stringify(t.info.remark)),t.info.goods_list)t.info.goods_list[a].title=t.info.goods_list[a].sku_name,t.goodsIdArr.push(parseInt(a)),t.goodsList.push(t.info.goods_list[a])}))},getGoodsData:function(t,e){var a=this,n=t.detail;this.inputIndex=e,n&&n.value?(0,o.getSkuListForStock)({search:n?n.value:""}).then((function(t){t.code>=0&&1==t.data.length?a.selectGoods(t.data):t.code>=0?(a.params.search_text=n?n.value:"",a.dialogVisible=!0):a.$util.showToast({title:t.message})})):(this.params.search_text=n?n.value:"",this.dialogVisible=!0)},selectGoods:function(t){var e=this;t.forEach((function(t,a){if(t.goods_num=1,t.goods_price=0,t.title=t.sku_name+"",e.goodsIdArr.includes(t.sku_id)){var o=e.goodsIdArr.indexOf(t.sku_id);e.params.search_text&&(e.goodsList[o].goods_num=parseFloat(e.goodsList[o].goods_num)+1)}else console.log(111),e.goodsIdArr.push(t.sku_id),e.goodsList.push(t)})),this.goodsShow=!1,this.params.search_text="",this.$forceUpdate()},delGoods:function(t){this.goodsList.splice(this.goodsIdArr.indexOf(t),1),this.goodsIdArr.splice(this.goodsIdArr.indexOf(t),1)},stockOutFn:function(){return this.screen.inventory_no?this.screen.time?this.goodsIdArr.length?void(this.globalStoreInfo.stock_config&&1==this.globalStoreInfo.stock_config.is_audit?this.$refs.tipsPop.open():this.save()):(this.$util.showToast({title:"请选择盘点数据"}),!1):(this.$util.showToast({title:"请选择盘点时间"}),!1):(this.$util.showToast({title:"请输入盘点单号"}),!1)},save:function(){var t=this,e=!1,a=[];try{this.goodsList.forEach((function(o,n){if(t.goodsIdArr.includes(o.sku_id)){if(!parseFloat(o.goods_num||0)){e=!0;var i="请输入"+o.sku_name+"的盘点数量";throw t.$util.showToast({title:i}),new Error("end")}var s={};s.goods_num=o.goods_num,s.goods_sku_id=o.sku_id,a.push(s)}}))}catch(i){if("end"!=i.message)throw i}if(e)return!1;if(this.isSubmit)return!1;this.isSubmit=!0,this.screen.stock_json=JSON.stringify(a);var n=this.screen.inventory_id?o.editInventory:o.addInventory;n(this.screen).then((function(e){t.isSubmit=!1,t.$util.showToast({title:e.message}),e.code>=0&&(t.$refs.tipsPop&&t.$refs.tipsPop.close(),setTimeout((function(){t.backFn()}),500),t.resetFn())}))},backFn:function(){this.$util.redirectTo("/pages/stock/check")},calcTotalData:function(){var t=this;this.totalData.kindsNum=0,this.totalData.upNum=0,this.totalData.downNum=0,this.totalData.sameNum=0,this.goodsList.forEach((function(e,a){t.goodsIdArr.includes(e.sku_id)&&e.goods_num&&(e.goods_num-e.stock==0?t.totalData.sameNum++:e.goods_num-e.stock>0?t.totalData.upNum++:e.goods_num-e.stock<0&&t.totalData.downNum++)})),this.totalData.kindsNum=this.goodsIdArr.length},resetFn:function(){this.goodsIdArr=[],this.goodsShow=!1,this.totalData.kindsNum=0,this.totalData.countNum=0,this.totalData.price=0},changeTime:function(t){this.screen.time=t},remarkConfirm:function(){this.screen.remark=JSON.parse(JSON.stringify(this.remark)),this.$refs.remarkPopup.close()}}};e.default=n},"4a26":function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=o(a("3efd1")),i=o(a("cea0")),s=o(a("e420")),r={components:{unipopup:i.default,stockGoodsDialog:s.default},mixins:[n.default]};e.default=r},"65b0":function(t,e,a){var o=a("6d4b");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=a("967d").default;n("3884c7ec",o,!0,{sourceMap:!1,shadowMode:!1})},"6d4b":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-164bfcdc]{display:none}\r\n/* 收银台相关 */uni-text[data-v-164bfcdc],\r\nuni-view[data-v-164bfcdc]{font-size:.14rem}body[data-v-164bfcdc]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-164bfcdc]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-164bfcdc]::-webkit-scrollbar-button{display:none}body[data-v-164bfcdc]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-164bfcdc]::-webkit-scrollbar-track{background-color:initial}.stock-body .content-wrap[data-v-164bfcdc], .stock-body .content-wrap .table-wrap .table-body[data-v-164bfcdc]{height:100%;overflow:auto}.stock-body .content-wrap[data-v-164bfcdc]::-webkit-scrollbar, .stock-body .content-wrap .table-wrap .table-body[data-v-164bfcdc]::-webkit-scrollbar{width:.06rem;height:.06rem}.stock-body .content-wrap[data-v-164bfcdc]::-webkit-scrollbar-button, .stock-body .content-wrap .table-wrap .table-body[data-v-164bfcdc]::-webkit-scrollbar-button{display:none}.stock-body .content-wrap[data-v-164bfcdc]::-webkit-scrollbar-thumb, .stock-body .content-wrap .table-wrap .table-body[data-v-164bfcdc]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}.stock-body .content-wrap[data-v-164bfcdc]::-webkit-scrollbar-track, .stock-body .content-wrap .table-wrap .table-body[data-v-164bfcdc]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-164bfcdc]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-164bfcdc]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-164bfcdc]{color:var(--primary-color)!important}.form-content[data-v-164bfcdc]{display:flex;flex-wrap:wrap;margin-top:.2rem}.form-content .store-info .form-inline[data-v-164bfcdc]{padding-left:.05rem}.form-content .form-item[data-v-164bfcdc]{margin-bottom:.1rem;display:flex}.form-content .form-item .form-label[data-v-164bfcdc]{width:1.3rem;text-align:right;padding-right:.1rem;box-sizing:border-box;height:.32rem;line-height:.32rem}.form-content .form-item .form-label .required[data-v-164bfcdc]{color:red;margin-right:.03rem}.form-content .form-item .form-inline[data-v-164bfcdc]{width:2.4rem;line-height:.32rem;margin-right:.1rem;box-sizing:border-box}.form-content .form-item .form-inline.input uni-input[data-v-164bfcdc]{padding:0 .1rem}.form-content .form-item .form-inline .form-input[data-v-164bfcdc]{border-width:.01rem;border-style:solid;background-color:#fff;color:rgba(0,0,0,.85);border-radius:.02rem;padding-left:.1rem;height:.32rem;line-height:.32rem;font-size:.14rem;border-color:#e6e6e6}.stock-body[data-v-164bfcdc]{position:relative;height:100%}.stock-body .content-wrap[data-v-164bfcdc]{padding:.15rem;background-color:#fff;box-sizing:border-box}.stock-body .content-wrap .title[data-v-164bfcdc]{font-size:.18rem;margin-bottom:.2rem;text-align:center}.stock-body .content-wrap .table-wrap[data-v-164bfcdc]{position:relative;margin-top:%?40?%;border:%?1?% solid #dcdfe6}.stock-body .content-wrap .table-wrap .table-head[data-v-164bfcdc]{background-color:#f7f7f7}.stock-body .content-wrap .table-wrap .table-body[data-v-164bfcdc]{max-height:6rem}.stock-body .content-wrap .table-wrap .table-body .table-tr[data-v-164bfcdc]:nth-child(1){position:absolute;left:0;right:0;background:#fff;z-index:3}.stock-body .content-wrap .table-wrap .table-body .table-tr[data-v-164bfcdc]:nth-child(2){margin-top:.49rem}.stock-body .content-wrap .table-wrap .table-body .table-tr:last-of-type .table-td[data-v-164bfcdc]{border-bottom:0}.stock-body .content-wrap .table-wrap .table-tr[data-v-164bfcdc]{display:flex}.stock-body .content-wrap .table-wrap .table-th[data-v-164bfcdc],\r\n.stock-body .content-wrap .table-wrap .table-td[data-v-164bfcdc]{display:flex;align-items:center;justify-content:center;padding:.07rem .3rem;border-bottom:.01rem solid #dcdfe6;border-right:.01rem solid #dcdfe6;text-align:center}.stock-body .content-wrap .table-wrap .table-th[data-v-164bfcdc]:last-of-type,\r\n.stock-body .content-wrap .table-wrap .table-td[data-v-164bfcdc]:last-of-type{border-right:0;justify-content:flex-end}.stock-body .content-wrap .table-wrap .table-th.goods-name[data-v-164bfcdc],\r\n.stock-body .content-wrap .table-wrap .table-td.goods-name[data-v-164bfcdc]{justify-content:flex-start}.stock-body .content-wrap .table-wrap .table-th.goods-name uni-image[data-v-164bfcdc],\r\n.stock-body .content-wrap .table-wrap .table-td.goods-name uni-image[data-v-164bfcdc]{width:.45rem;height:.45rem;flex-shrink:0}.stock-body .content-wrap .table-wrap .table-th.goods-name .name[data-v-164bfcdc],\r\n.stock-body .content-wrap .table-wrap .table-td.goods-name .name[data-v-164bfcdc]{margin-left:.1rem}.stock-body .content-wrap .table-wrap .delete[data-v-164bfcdc]{margin:0;font-size:.14rem;background-color:var(--primary-color);color:#fff;line-height:.32rem;height:.32rem}.stock-body .content-wrap .table-wrap .delete[data-v-164bfcdc]::after{border-width:0}.stock-body .content-wrap .table-wrap .table-empty[data-v-164bfcdc]{justify-content:center;padding:.3rem;color:#999}.stock-body .content-wrap .select-goods-input[data-v-164bfcdc],\r\n.stock-body .content-wrap .goods-name[data-v-164bfcdc]{position:relative}.stock-body .content-wrap .select-goods-input uni-input[data-v-164bfcdc],\r\n.stock-body .content-wrap .goods-name uni-input[data-v-164bfcdc]{flex:1;padding:0 .2rem}.stock-body .content-wrap .select-goods-input .icontuodong[data-v-164bfcdc],\r\n.stock-body .content-wrap .goods-name .icontuodong[data-v-164bfcdc]{font-size:.16rem;position:absolute;top:.17rem;right:.34rem;z-index:2;cursor:pointer}.stock-body .content-wrap uni-input[data-v-164bfcdc]{font-size:.14rem!important;border:.01rem solid #e6e6e6!important;height:.32rem}.stock-body .action-wrap[data-v-164bfcdc]{position:absolute;bottom:0;left:0;right:0;display:flex;justify-content:space-between;padding:.24rem .2rem;align-items:center;background-color:#fff;z-index:10}.stock-body .action-wrap .btn-wrap[data-v-164bfcdc]{display:flex;align-items:center;justify-content:center}.stock-body .action-wrap .btn-wrap uni-button[data-v-164bfcdc]{margin:0;min-width:2.75rem;height:.4rem;line-height:.4rem;font-size:.14rem}.stock-body .action-wrap .btn-wrap uni-button.stockout-btn[data-v-164bfcdc]{margin-right:.15rem;background-color:var(--primary-color);color:#fff}.stock-body .action-wrap .btn-wrap uni-button.stockout-btn[data-v-164bfcdc]::after{border-width:0}.stock-body .action-wrap .btn-wrap uni-button.remark[data-v-164bfcdc]{margin-right:.15rem;min-width:1.2rem}.remark-wrap[data-v-164bfcdc]{width:6rem;background-color:#fff;border-radius:.04rem;box-shadow:0 .01rem .12rem 0 rgba(0,0,0,.1)}.remark-wrap .header[data-v-164bfcdc]{display:flex;justify-content:space-between;align-items:center;padding:0 .15rem;height:.45rem;line-height:.45rem;border-bottom:.01rem solid #e8eaec}.remark-wrap .header .iconfont[data-v-164bfcdc]{font-size:.16rem}.remark-wrap .body[data-v-164bfcdc]{padding:.15rem .15rem .1rem}.remark-wrap .body uni-textarea[data-v-164bfcdc]{border:.01rem solid #e6e6e6;width:100%;padding:.1rem;box-sizing:border-box;font-size:.14rem}.remark-wrap .body .placeholder-class[data-v-164bfcdc]{font-size:.14rem}.remark-wrap .footer[data-v-164bfcdc]{height:.5rem;padding-bottom:.05rem;display:flex;align-items:center;justify-content:center}.remark-wrap .footer uni-button.default[data-v-164bfcdc]{width:95%}',""]),t.exports=e},d1e3:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return o}));var o={uniDatetimePicker:a("da34").default,stockGoodsDialog:a("e420").default,uniPopup:a("cea0").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"stock-body"},[a("v-uni-view",{staticClass:"content-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goodsShow=!1}}},[""!=t.screen.inventory_id?a("v-uni-view",{staticClass:"title"},[t._v("编辑盘点单")]):a("v-uni-view",{staticClass:"title"},[t._v("添加盘点单")]),a("v-uni-view",{staticClass:"screen-warp form-content"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("盘点单号")],1),a("v-uni-view",{staticClass:"form-inline input"},[a("v-uni-input",{attrs:{type:"text",disabled:""!=t.screen.inventory_id,placeholder:"请输入入库单号"},model:{value:t.screen.inventory_no,callback:function(e){t.$set(t.screen,"inventory_no",e)},expression:"screen.inventory_no"}})],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("盘点时间")],1),a("v-uni-view",{staticClass:"form-inline"},[a("uni-datetime-picker",{attrs:{type:"timestamp",clearIcon:!1},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTime.apply(void 0,arguments)}},model:{value:t.screen.time,callback:function(e){t.$set(t.screen,"time",e)},expression:"screen.time"}})],1)],1),a("v-uni-view",{staticClass:"form-item store-info"},[a("v-uni-view",{staticClass:"form-label"},[t._v("当前门店：")]),a("v-uni-view",{staticClass:"form-inline"},[t._v(t._s(t.globalStoreInfo.store_name))])],1),a("v-uni-view",{staticClass:"form-item store-info"},[a("v-uni-view",{staticClass:"form-label"},[t._v("当前操作人：")]),a("v-uni-view",{staticClass:"form-inline"},[t._v(t._s(t.userInfo?t.userInfo.username:""))])],1)],1),t.globalStoreInfo.stock_config&&1==t.globalStoreInfo.stock_config.is_audit?a("v-uni-view",{staticClass:"tips text-color"},[t._v("说明：待审核状态下只有经办人允许修改，只有变为已审核状态后才会使库存发生变化，已审核状态的单据不允许再修改。")]):t._e(),a("v-uni-view",{staticClass:"table-wrap"},[a("v-uni-view",{staticClass:"table-head"},[a("v-uni-view",{staticClass:"table-tr"},[a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"3"}},[t._v("产品名称/规格/编码")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("当前库存")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("销售库存")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("单位")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"2"}},[t._v("实盘数量")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("盈亏数量")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("操作")])],1)],1),a("v-uni-view",{staticClass:"table-body"},[a("v-uni-view",{staticClass:"table-tr"},[a("v-uni-view",{staticClass:"table-td select-goods-input",staticStyle:{flex:"3"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.goodsShow=!0}}},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入产品名称/规格/编码"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsData(e,-1)}},model:{value:t.params.search_text,callback:function(e){t.$set(t.params,"search_text",e)},expression:"params.search_text"}}),a("v-uni-text",{staticClass:"iconfont icontuodong",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsData({detail:null},-1)}}})],1),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}}),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}}),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}}),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"2"}}),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}}),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}})],1),t._l(t.goodsList,(function(e,o){return[t.goodsIdArr.includes(e.sku_id)?a("v-uni-view",{key:o+"_0",staticClass:"table-tr"},[a("v-uni-view",{staticClass:"table-td goods-name",staticStyle:{flex:"3"}},[t._v(t._s(e.title))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(e.real_stock||0))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(e.stock||0))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(e.unit||"件"))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"2"}},[a("v-uni-input",{attrs:{type:"number",placeholder:"请输入数量"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.calcTotalData.apply(void 0,arguments)}},model:{value:e.goods_num,callback:function(a){t.$set(e,"goods_num",a)},expression:"item.goods_num"}})],1),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(parseFloat(e.goods_num-e.stock)||0))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[a("v-uni-button",{staticClass:"delete",attrs:{type:"default"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.delGoods(e.sku_id)}}},[t._v("删除")])],1)],1):t._e()]})),t.goodsIdArr.length?t._e():a("v-uni-view",{staticClass:"table-tr table-empty"},[t._v("暂无数据，请选择商品数据")])],2)],1)],1),a("v-uni-view",{staticClass:"action-wrap"},[a("v-uni-view",{staticClass:"table-total"},[t._v("合计：共"+t._s(t.totalData.kindsNum)+"种商品，盘盈："+t._s(t.totalData.upNum)+"种，盘亏："+t._s(t.totalData.downNum)+"种，持平："+t._s(t.totalData.sameNum)+"种")]),a("v-uni-view",{staticClass:"btn-wrap"},[a("v-uni-button",{staticClass:"remark default",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.remarkPopup.open()}}},[t._v("备注")]),a("v-uni-button",{staticClass:"stockout-btn",attrs:{type:"default",loading:t.isSubmit},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.stockOutFn.apply(void 0,arguments)}}},[t._v("盘点")]),a("v-uni-button",{staticClass:"default",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.backFn.apply(void 0,arguments)}}},[t._v("返回")])],1)],1)],1),a("stock-goods-dialog",{attrs:{params:t.params},on:{selectGoods:function(e){arguments[0]=e=t.$handleEvent(e),t.selectGoods.apply(void 0,arguments)}},model:{value:t.dialogVisible,callback:function(e){t.dialogVisible=e},expression:"dialogVisible"}}),a("unipopup",{ref:"tipsPop",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"confirm-pop"},[a("v-uni-view",{staticClass:"title"},[t._v('单据保存后将处于"待审核"状态，只有经办人可以编辑或删除等操作！是否确认保存？')]),a("v-uni-view",{staticClass:"btn"},[a("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.tipsPop.close()}}},[t._v("取消")]),a("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.save.apply(void 0,arguments)}}},[t._v("确定")])],1)],1)],1),a("uni-popup",{ref:"remarkPopup",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"remark-wrap"},[a("v-uni-view",{staticClass:"header"},[a("v-uni-text",{staticClass:"title"},[t._v("备注")]),a("v-uni-text",{staticClass:"iconfont iconguanbi1",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.remarkPopup.close()}}})],1),a("v-uni-view",{staticClass:"body"},[a("v-uni-textarea",{attrs:{placeholder:"填写备注信息","placeholder-class":"placeholder-class"},model:{value:t.remark,callback:function(e){t.remark=e},expression:"remark"}})],1),a("v-uni-view",{staticClass:"footer"},[a("v-uni-button",{staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.remarkConfirm.apply(void 0,arguments)}}},[t._v("确认")])],1)],1)],1)],1)},i=[]},d2c2:function(t,e,a){"use strict";a.r(e);var o=a("4a26"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=n.a},ff29:function(t,e,a){"use strict";var o=a("65b0"),n=a.n(o);n.a}}]);