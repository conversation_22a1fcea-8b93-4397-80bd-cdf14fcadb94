(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-store-config"],{"0652":function(t,e,a){var i=a("5847");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=a("967d").default;r("21eb42ea",i,!0,{sourceMap:!1,shadowMode:!1})},"0d8b":function(t,e,a){"use strict";a.r(e);var i=a("ee2f"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=r.a},"13b2":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"pick-regions"},[a("v-uni-picker",{attrs:{mode:"multiSelector",value:t.multiIndex,range:t.multiArray},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.handleValueChange.apply(void 0,arguments)},columnchange:function(e){arguments[0]=e=t.$handleEvent(e),t.handleColumnChange.apply(void 0,arguments)}}},[t._t("default")],2)],1)},r=[]},"18b3":function(t,e,a){"use strict";a.r(e);var i=a("e3ae"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=r.a},"18c6":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(a("5de6")),n=i(a("2634")),o=i(a("2fdc"));a("fd3c"),a("bf0f"),a("2797"),a("aa9c");var s=a("7f58"),l={props:{defaultRegions:{type:Array},selectArr:{type:String}},data:function(){return{pickerValueArray:[],cityArr:[],districtArr:[],multiIndex:[0,0,0],isInitMultiArray:!1,isLoadDefaultAreas:!1}},watch:{defaultRegions:{handler:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];t.length==this.selectArr&&t.join("")!==e.join("")&&this.handleDefaultRegions()},immediate:!0}},computed:{multiArray:function(){if(this.isLoadDefaultAreas){var t=this.pickedArr.map((function(t){return t.map((function(t){return t.label}))}));return t}},pickedArr:function(){return this.isInitMultiArray?"2"==this.selectArr?[this.pickerValueArray[0],this.pickerValueArray[1]]:[this.pickerValueArray[0],this.pickerValueArray[1],this.pickerValueArray[2]]:"2"==this.selectArr?[this.pickerValueArray[0],this.cityArr]:[this.pickerValueArray[0],this.cityArr,this.districtArr]}},created:function(){this.getDefaultAreas(0,{level:0})},methods:{handleColumnChange:function(t){var e=this;return(0,o.default)((0,n.default)().mark((function a(){var i,r;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e.isInitMultiArray=!1,i=t.detail.column,r=t.detail.value,e.multiIndex[i]=r,a.t0=i,a.next=0===a.t0?7:1===a.t0?14:2===a.t0?18:27;break;case 7:return a.next=9,e.getAreasAsync(e.pickerValueArray[0][e.multiIndex[i]].value);case 9:return e.cityArr=a.sent,a.next=12,e.getAreasAsync(e.cityArr[0].value);case 12:return e.districtArr=a.sent,a.abrupt("break",27);case 14:return a.next=16,e.getAreasAsync(e.cityArr[e.multiIndex[i]].value);case 16:return e.districtArr=a.sent,a.abrupt("break",27);case 18:if(e.cityArr.length){a.next=22;break}return a.next=21,e.getAreasAsync(e.pickerValueArray[0][0].value);case 21:e.cityArr=a.sent;case 22:if(e.districtArr.length){a.next=26;break}return a.next=25,e.getAreasAsync(e.cityArr[0].value);case 25:e.districtArr=a.sent;case 26:return a.abrupt("break",27);case 27:case"end":return a.stop()}}),a)})))()},handleValueChange:function(t){var e=(0,r.default)(t.detail.value,3),a=e[0],i=e[1],n=e[2],o=(0,r.default)(this.pickedArr,3),s=o[0],l=o[1],c=o[2],d="";d="2"==this.selectArr?[s[a],l[i]]:[s[a],l[i],c[n]],this.$emit("getRegions",d)},handleDefaultRegions:function(){var t=this,e=setInterval((function(){if(t.isLoadDefaultAreas){t.isInitMultiArray=!1;for(var a=0;a<t.defaultRegions.length;a++)for(var i=function(e){t.defaultRegions[a]!=t.pickerValueArray[a][e].value&&t.defaultRegions[a]!=t.pickerValueArray[a][e].label||1!=t.pickerValueArray[a][e].level||(t.$set(t.multiIndex,a,e),t.getAreas(t.pickerValueArray[a][e].value,(function(a){t.cityArr=a;for(var i=function(a){if(t.defaultRegions[1]==t.cityArr[a].value||t.defaultRegions[1]==t.cityArr[a].label)return t.$set(t.multiIndex,1,a),t.getAreas(t.cityArr[a].value,(function(i){t.districtArr=i;for(var r=0;r<t.districtArr.length;r++)if(t.defaultRegions[2]==t.districtArr[r].value||t.defaultRegions[2]==t.districtArr[r].label){t.$set(t.multiIndex,2,r),t.handleValueChange({detail:{value:[e,a,r]}});break}})),"break"},r=0;r<t.cityArr.length;r++){var n=i(r);if("break"===n)break}})))},r=0;r<t.pickerValueArray[a].length;r++)i(r);t.isLoadDefaultAreas&&clearInterval(e)}}),100)},getDefaultAreas:function(t,e){var a=this;(0,s.getAreaList)({pid:t}).then((function(t){if(0==t.code){var i=[],r=void 0;t.data.forEach((function(t,a){void 0!=e&&(0==e.level&&void 0!=e.province_id?r=e.province_id:1==e.level&&void 0!=e.city_id?r=e.city_id:2==e.level&&void 0!=e.district_id&&(r=e.district_id)),void 0==r&&0==a&&(r=t.id),i.push({value:t.id,label:t.name,level:t.level})})),a.pickerValueArray[e.level]=i,e.level+1<3?(e.level++,a.getDefaultAreas(r,e)):(a.isInitMultiArray=!0,a.isLoadDefaultAreas=!0)}}))},getAreasAsync:function(t){return(0,o.default)((0,n.default)().mark((function e(){var a,i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,s.getAreaList)({pid:t});case 2:if(a=e.sent,0!=a.code){e.next=7;break}return i=[],a.data.forEach((function(t,e){i.push({value:t.id,label:t.name,level:t.level})})),e.abrupt("return",i);case 7:case"end":return e.stop()}}),e)})))()},getAreas:function(t,e){(0,s.getAreaList)({pid:t}).then((function(t){if(0==t.code){var a=[];t.data.forEach((function(t,e){a.push({value:t.id,label:t.name,level:t.level})})),e&&e(a)}}))}}};e.default=l},"55c1":function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={uniIcons:a("6f18").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-stat__select"},[t.label?a("span",{staticClass:"uni-label-text hide-on-phone"},[t._v(t._s(t.label+"："))]):t._e(),a("v-uni-view",{staticClass:"uni-stat-box",class:{"uni-stat__actived":t.current}},[a("v-uni-view",{staticClass:"uni-select",class:{"uni-select--disabled":t.disabled}},[a("v-uni-view",{staticClass:"uni-select__input-box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleSelector.apply(void 0,arguments)}}},[t.current?a("v-uni-view",{staticClass:"uni-select__input-text"},[t._v(t._s(t.current))]):a("v-uni-view",{staticClass:"uni-select__input-text uni-select__input-placeholder"},[t._v(t._s(t.typePlaceholder))]),t.current&&t.clear?a("uni-icons",{attrs:{type:"clear",color:"#c0c4cc",size:"24"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clearVal.apply(void 0,arguments)}}}):a("uni-icons",{attrs:{type:t.showSelector?"top":"bottom",size:"14",color:"#999"}})],1),t.showSelector?a("v-uni-view",{staticClass:"uni-select--mask",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleSelector.apply(void 0,arguments)}}}):t._e(),t.showSelector?a("v-uni-view",{staticClass:"uni-select__selector"},[a("v-uni-view",{staticClass:"uni-popper__arrow"}),a("v-uni-scroll-view",{staticClass:"uni-select__selector-scroll",attrs:{"scroll-y":"true"}},[0===t.mixinDatacomResData.length?a("v-uni-view",{staticClass:"uni-select__selector-empty"},[a("v-uni-text",[t._v(t._s(t.emptyTips))])],1):t._l(t.mixinDatacomResData,(function(e,i){return a("v-uni-view",{key:i,staticClass:"uni-select__selector-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.change(e)}}},[a("v-uni-text",{class:{"uni-select__selector__disabled":e.disable}},[t._v(t._s(t.formatItemName(e)))])],1)}))],2)],1):t._e()],1)],1)],1)},n=[]},"560b":function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={uniDataSelect:a("9056").default,pickRegions:a("dbf4").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("base-page",[i("v-uni-view",{staticClass:"store-config"},[i("v-uni-view",{staticClass:"common-wrap common-form fixd common-scrollbar"},[i("v-uni-view",{staticClass:"common-title"},[t._v("门店设置")]),i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[t._v("门店名称")]),i("v-uni-view",{staticClass:"form-input-inline"},[i("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:t.storeData.store_name,callback:function(e){t.$set(t.storeData,"store_name",e)},expression:"storeData.store_name"}})],1),i("v-uni-text",{staticClass:"form-word-aux-line"},[t._v("门店的名称（招牌）")])],1),i("v-uni-view",{staticClass:"common-form-item store-img"},[i("v-uni-label",{staticClass:"form-label"},[t._v("门店图片")]),i("v-uni-view",{staticClass:"form-input-inline upload-box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addImg.apply(void 0,arguments)}}},[t.storeData.store_image?i("v-uni-view",{staticClass:"upload"},[i("v-uni-image",{attrs:{src:t.$util.img(t.storeData.store_image),mode:"heightFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.img(t.defaultImg.store)}}})],1):i("v-uni-view",{staticClass:"upload"},[i("v-uni-text",{staticClass:"iconfont iconyunshangchuan"}),i("v-uni-view",[t._v("点击上传")])],1)],1),i("v-uni-text",{staticClass:"form-word-aux-line"},[t._v("门店图片在PC及移动端对应页面及列表作为门店标志出现。建议图片尺寸：100 * 100像素，图片格式：jpg、png、jpeg。")])],1),i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[t._v("门店类型")]),i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.storeTypeChange.apply(void 0,arguments)}}},[i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"directsale",checked:"directsale"==t.storeData.store_type}}),t._v("直营店")],1),i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"franchise",checked:"franchise"==t.storeData.store_type}}),t._v("加盟店")],1)],1)],1)],1),t.category.status?i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[t._v("门店分类")]),i("v-uni-view",{staticClass:"form-input-inline"},[i("uni-data-select",{attrs:{localdata:t.category.list},model:{value:t.storeData.category_id,callback:function(e){t.$set(t.storeData,"category_id",e)},expression:"storeData.category_id"}})],1)],1):t._e(),t.label.length?i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[t._v("门店标签")]),i("v-uni-view",{staticClass:"form-block"},[i("v-uni-checkbox-group",{staticClass:"form-checkbox-group",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.labelChange.apply(void 0,arguments)}}},t._l(t.label,(function(e,a){return i("v-uni-label",{staticClass:"form-checkbox-item"},[i("v-uni-checkbox",{attrs:{value:e.label_id.toString(),checked:t.labelChecked(e)}}),t._v(t._s(e.label_name))],1)})),1)],1)],1):t._e(),i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[t._v("门店电话")]),i("v-uni-view",{staticClass:"form-input-inline"},[i("v-uni-input",{staticClass:"form-input",attrs:{type:"number"},model:{value:t.storeData.telphone,callback:function(e){t.$set(t.storeData,"telphone",e)},expression:"storeData.telphone"}})],1)],1),i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[t._v("门店地址")]),i("v-uni-view",{staticClass:"form-inline"},[i("pick-regions",{ref:"selectArea",attrs:{"default-regions":t.defaultRegions},on:{getRegions:function(e){arguments[0]=e=t.$handleEvent(e),t.handleGetRegions.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"form-input-inline long"},[i("v-uni-view",{staticClass:"form-input"},[t._v(t._s(t.storeData.full_address))])],1)],1)],1)],1),i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"}),i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-view",{staticClass:"form-input-inline long"},[i("v-uni-input",{staticClass:"form-input",attrs:{type:"text"},model:{value:t.storeData.address,callback:function(e){t.$set(t.storeData,"address",e)},expression:"storeData.address"}})],1),i("v-uni-view",{staticClass:"form-input-inline short btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getLatLng()}}},[t._v("查找")])],1)],1),i("v-uni-view",{staticClass:"common-form-item store-img"},[i("v-uni-label",{staticClass:"form-label"},[t._v("地图定位")]),i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-view",{staticClass:"map-box"},[i("v-uni-image",{staticClass:"map-icon",attrs:{src:a("c7ce")}}),i("v-uni-map",{staticStyle:{width:"100%",height:"100%"},attrs:{latitude:t.storeData.latitude,longitude:t.storeData.longitude,markers:t.covers},on:{markertap:function(e){arguments[0]=e=t.$handleEvent(e),t.markertap.apply(void 0,arguments)},regionchange:function(e){arguments[0]=e=t.$handleEvent(e),t.tap.apply(void 0,arguments)}}})],1)],1)],1),i("v-uni-view",{staticClass:"common-btn-wrap"},[i("v-uni-button",{staticClass:"screen-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveFn.apply(void 0,arguments)}}},[t._v("保存")]),i("v-uni-button",{attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/store/index")}}},[t._v("返回")])],1)],1)],1)],1)},n=[]},5847:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-2ac6ea26]{display:none}\r\n/* 收银台相关 */uni-text[data-v-2ac6ea26],\r\nuni-view[data-v-2ac6ea26]{font-size:.14rem}body[data-v-2ac6ea26]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-2ac6ea26]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-2ac6ea26]::-webkit-scrollbar-button{display:none}body[data-v-2ac6ea26]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-2ac6ea26]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-2ac6ea26]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-2ac6ea26]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-2ac6ea26]{color:var(--primary-color)!important}.store-config[data-v-2ac6ea26]{position:relative}.store-config .common-wrap.fixd[data-v-2ac6ea26]{padding:%?30?%;height:calc(100vh - .4rem);overflow-y:auto;box-sizing:border-box}.store-config .common-wrap.fixd .form-label[data-v-2ac6ea26]{width:1.5rem!important}.store-config .common-wrap.fixd .form-input-inline[data-v-2ac6ea26] .uni-select{border:none}.store-config .common-wrap.fixd .common-btn-wrap[data-v-2ac6ea26]{position:absolute;left:0;bottom:0;right:0;padding:.24rem .2rem}.store-config .common-wrap.fixd .form-word-aux-line[data-v-2ac6ea26]{margin-left:1.5rem!important}.store-config .upload-box[data-v-2ac6ea26]{border:.01rem dashed #e6e6e6!important;width:2.5rem!important;height:1.2rem!important;display:flex;align-items:center;justify-content:center}.store-config .upload-box .upload[data-v-2ac6ea26]{text-align:center;color:#5a5a5a}.store-config .upload-box .upload .iconfont[data-v-2ac6ea26]{font-size:.3rem}.store-config .upload-box .upload uni-image[data-v-2ac6ea26]{max-width:100%;height:1.2rem!important}.store-config .store-img[data-v-2ac6ea26]{align-items:flex-start!important}.store-config .map-box[data-v-2ac6ea26]{width:6.5rem;height:5rem;position:relative}.store-config .map-box .map-icon[data-v-2ac6ea26]{position:absolute;top:calc(50% - .36rem);left:calc(50% - .18rem);width:.36rem;height:.36rem;z-index:100}.store-config .form-input[data-v-2ac6ea26]{font-size:.16rem}.store-config .form-input-inline.btn[data-v-2ac6ea26]{height:.37rem;line-height:.35rem;box-sizing:border-box;border:.01rem solid #e6e6e6;text-align:center;cursor:pointer}.store-config .common-title[data-v-2ac6ea26]{font-size:.18rem;margin-bottom:.2rem}',""]),t.exports=e},"6f40":function(t,e,a){"use strict";var i=a("fb12"),r=a.n(i);r.a},"7f58":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getAddressByName=function(t){return r.default.post("/cashier/storeapi/address/getaddressbyname",{data:{address:t}})},e.getAreaList=function(t){return r.default.post("/cashier/storeapi/address/arealist",{data:t})},e.getTranAddressInfo=function(t){return r.default.post("/cashier/storeapi/address/tranaddressinfo",{data:{latlng:t}})};var r=i(a("a3b5"))},"8df8":function(t,e,a){"use strict";a.r(e);var i=a("18c6"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=r.a},9056:function(t,e,a){"use strict";a.r(e);var i=a("55c1"),r=a("0d8b");for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(n);a("6f40");var o=a("828b"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"191ab648",null,!1,i["a"],void 0);e["default"]=s.exports},9901:function(t,e,a){"use strict";var i=a("0652"),r=a.n(i);r.a},c7ce:function(t,e,a){t.exports=a.p+"static/location.png"},dbf4:function(t,e,a){"use strict";a.r(e);var i=a("13b2"),r=a("8df8");for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(n);var o=a("828b"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},dfb8:function(t,e,a){"use strict";a.r(e);var i=a("560b"),r=a("18b3");for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(n);a("9901");var o=a("828b"),s=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,"2ac6ea26",null,!1,i["a"],void 0);e["default"]=s.exports},e3ae:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797"),a("fd3c"),a("c9b5"),a("ab80"),a("aa9c"),a("4626"),a("5ac7");var i=a("5c0d"),r=a("7f58"),n={data:function(){return{storeData:{},covers:[{latitude:39.909,longitude:116.39742,iconPath:"/static/location.png"}],defaultRegions:[],category:{status:0,list:[]},label:[],labelData:{}}},onLoad:function(){this.getLabel(),this.getCategory(),this.getData()},methods:{getData:function(){var t=this;this.storeData=this.$util.deepClone(this.globalStoreInfo),this.storeData.start_time=this.timeFormat(this.storeData.start_time),this.storeData.end_time=this.timeFormat(this.storeData.end_time),this.defaultRegions=[this.storeData.province_id,this.storeData.city_id,this.storeData.district_id],this.$nextTick((function(){t.$refs.selectArea.handleDefaultRegions()}))},getLabel:function(){var t=this;(0,i.getAllStoreLabel)().then((function(e){if(0==e.code){t.label=e.data;var a={};e.data.forEach((function(t){a[t.label_id]=t.label_name})),t.labelData=a}}))},getCategory:function(){var t=this;(0,i.getAllStoreCategory)().then((function(e){0==e.code&&(t.category.status=e.data.status,t.category.list=e.data.list.map((function(t){return{value:t.category_id,text:t.category_name}})))}))},addImg:function(){var t=this;this.$util.upload(1,{path:"image"},(function(e){e.length>0&&(t.storeData.store_image=e[0],t.$forceUpdate())}))},tap:function(t){t.detail&&t.detail.centerLocation&&(this.storeData.latitude=t.detail.centerLocation.latitude,this.storeData.longitude=t.detail.centerLocation.longitude,this.covers=[{latitude:this.storeData.latitude,longitude:this.storeData.longitude,iconPath:"/static/location.png"}],this.getAddress(),this.$forceUpdate())},handleGetRegions:function(t){this.storeData.full_address="",this.storeData.full_address+=void 0!=t[0]?t[0].label:"",this.storeData.full_address+=void 0!=t[1]?"-"+t[1].label:"",this.storeData.full_address+=void 0!=t[2]?"-"+t[2].label:"",this.storeData.province_id=void 0!=t[0]?t[0].value:"",this.storeData.city_id=void 0!=t[1]?t[1].value:"",this.storeData.district_id=void 0!=t[2]?t[2].value:"",this.defaultRegions=[this.storeData.province_id,this.storeData.city_id,this.storeData.district_id],this.$forceUpdate(),this.getLatLng()},getAddress:function(){var t=this,e=this.storeData.latitude+","+this.storeData.longitude;(0,r.getTranAddressInfo)(e).then((function(e){0==e.code&&(t.storeData.full_address="",t.storeData.full_address+=void 0!=e.data.province?e.data.province:"",t.storeData.full_address+=void 0!=e.data.city?"-"+e.data.city:"",t.storeData.full_address+=void 0!=e.data.district?"-"+e.data.district:"",t.storeData.address=void 0!=e.data.address?e.data.address:"",t.storeData.province_id=void 0!=e.data.province_id?e.data.province_id:"",t.storeData.city_id=void 0!=e.data.city_id?e.data.city_id:"",t.storeData.district_id=void 0!=e.data.district_id?e.data.district_id:"",t.defaultRegions=[t.storeData.province_id,t.storeData.city_id,t.storeData.district_id],t.$forceUpdate())}))},getLatLng:function(){var t=this,e=this.storeData.full_address+this.storeData.address;(0,r.getAddressByName)(e).then((function(e){0==e.code&&(t.storeData.latitude=e.data.latitude,t.storeData.longitude=e.data.longitude)}))},storeTypeChange:function(t){this.storeData.store_type=t.detail.value},getSaveData:function(){var t=Object.assign({},this.storeData);return t.start_time=this.timeTurnTimeStamp(t.start_time),t.end_time=this.timeTurnTimeStamp(t.end_time),t.time_week=this.storeData.time_week.toString(),t},checkData:function(t){return""==t.store_name?(this.$util.showToast({title:"请输入门店名称"}),!1):!(!t.district_id||""==t.address)||(this.$util.showToast({title:"请选择门店地址"}),!1)},saveFn:function(){var t=this,e=this.getSaveData();if(this.checkData(e)){if(this.flag)return!1;this.flag=!0,(0,i.editStore)(e).then((function(e){t.flag=!1,t.$util.showToast({title:e.message}),e.code>=0&&t.$store.dispatch("app/getStoreInfoFn",{callback:function(){t.getData()}})}))}},timeTurnTimeStamp:function(t){var e=t.split(":");return 3600*e[0]+60*e[1]},timeFormat:function(t){var e=t/3600,a=t%3600/60;return e=e<10?"0"+e:e,a=a<10?"0"+a:a,e+":"+a},labelChange:function(t){var e=this;if(t.detail.value.length){this.storeData.label_id=","+t.detail.value.toString()+",";var a=[];t.detail.value.forEach((function(t){a.push(e.labelData[t])})),this.storeData.label_name=","+a.toString()+","}else this.storeData.label_id="",this.storeData.label_name=""},labelChecked:function(t){var e=[];return!!this.storeData.label_id&&("string"==typeof this.storeData.label_id&&(e=this.storeData.label_id.split(",")),e.includes(t.label_id.toString()))}}};e.default=n},ee2f:function(t,e,a){"use strict";(function(t){a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("aa77"),a("bf0f"),a("2797"),a("5ef2"),a("c223");var i={name:"uni-stat-select",mixins:[t.mixinDatacom||{}],data:function(){return{showSelector:!1,current:"",mixinDatacomResData:[],apps:[],channels:[]}},props:{localdata:{type:Array,default:function(){return[]}},value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"请选择"},emptyTips:{type:String,default:"无选项"},clear:{type:Boolean,default:!0},defItem:{type:Number,default:0},disabled:{type:Boolean,default:!1}},created:function(){this.last="".concat(this.collection,"_last_selected_option_value"),this.collection&&!this.localdata.length&&this.mixinDatacomEasyGet()},computed:{typePlaceholder:function(){var t=this.placeholder,e={"opendb-stat-app-versions":"版本","opendb-app-channels":"渠道","opendb-app-list":"应用"}[this.collection];return e?t+e:t}},watch:{localdata:{immediate:!0,handler:function(t,e){Array.isArray(t)&&e!==t&&(this.mixinDatacomResData=t)}},value:function(){this.initDefVal()},mixinDatacomResData:{immediate:!0,handler:function(t){t.length&&this.initDefVal()}}},methods:{initDefVal:function(){var t="";if(!this.value&&0!==this.value||this.isDisabled(this.value))if(!this.modelValue&&0!==this.modelValue||this.isDisabled(this.modelValue)){var e;if(this.collection&&(e=uni.getStorageSync(this.last)),e||0===e)t=e;else{var a="";this.defItem>0&&this.defItem<this.mixinDatacomResData.length&&(a=this.mixinDatacomResData[this.defItem-1].value),t=a}this.emit(t)}else t=this.modelValue;else t=this.value;var i=this.mixinDatacomResData.find((function(e){return e.value===t}));this.current=i?this.formatItemName(i):""},isDisabled:function(t){var e=!1;return this.mixinDatacomResData.forEach((function(a){a.value===t&&(e=a.disable)})),e},clearVal:function(){this.emit(""),this.collection&&uni.removeStorageSync(this.last)},change:function(t){t.disable||(this.showSelector=!1,this.current=this.formatItemName(t),this.emit(t.value))},emit:function(t){this.$emit("change",t),this.$emit("input",t),this.$emit("update:modelValue",t),this.collection&&uni.setStorageSync(this.last,t)},toggleSelector:function(){this.disabled||(this.showSelector=!this.showSelector)},formatItemName:function(t){var e=t.text,a=t.value,i=t.channel_code;return i=i?"(".concat(i,")"):"",this.collection.indexOf("app-list")>0?"".concat(e,"(").concat(a,")"):e||"未命名".concat(i)}}};e.default=i}).call(this,a("861b")["uniCloud"])},eed8:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-191ab648]{display:none}\r\n/* 收银台相关 */uni-text[data-v-191ab648],\r\nuni-view[data-v-191ab648]{font-size:.14rem}body[data-v-191ab648]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-191ab648]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-191ab648]::-webkit-scrollbar-button{display:none}body[data-v-191ab648]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-191ab648]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-191ab648]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-191ab648]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-191ab648]{color:var(--primary-color)!important}@media screen and (max-width:500px){.hide-on-phone[data-v-191ab648]{display:none}}.uni-stat__select[data-v-191ab648]{display:flex;align-items:center;cursor:pointer;width:100%;flex:1;box-sizing:border-box}.uni-stat-box[data-v-191ab648]{width:100%;flex:1}.uni-stat__actived[data-v-191ab648]{width:100%;flex:1}.uni-label-text[data-v-191ab648]{font-size:14px;font-weight:700;color:#6a6a6a;margin:auto 0;margin-right:5px}.uni-select[data-v-191ab648]{font-size:14px;border:1px solid #e5e5e5;box-sizing:border-box;border-radius:4px;padding:0 5px;padding-left:10px;position:relative;display:flex;-webkit-user-select:none;user-select:none;flex-direction:row;align-items:center;border-bottom:solid 1px #e5e5e5;width:100%;flex:1;height:35px}.uni-select--disabled[data-v-191ab648]{background-color:#f5f7fa;cursor:not-allowed}.uni-select__label[data-v-191ab648]{font-size:16px;height:35px;padding-right:10px;color:#909399}.uni-select__input-box[data-v-191ab648]{position:relative;display:flex;flex:1;flex-direction:row;align-items:center}.uni-select__input[data-v-191ab648]{flex:1;font-size:14px;height:22px;line-height:22px}.uni-select__input-plac[data-v-191ab648]{font-size:14px;color:#909399}.uni-select__selector[data-v-191ab648]{box-sizing:border-box;position:absolute;top:calc(100% + 12px);left:0;width:100%;background-color:#fff;border:1px solid #ebeef5;border-radius:6px;box-shadow:0 2px 12px 0 rgba(0,0,0,.1);z-index:2;padding:4px 0}.uni-select__selector-scroll[data-v-191ab648]{max-height:200px;box-sizing:border-box}.uni-select__selector-empty[data-v-191ab648],\r\n.uni-select__selector-item[data-v-191ab648]{display:flex;cursor:pointer;line-height:35px;font-size:14px;text-align:center;\r\n  /* border-bottom: solid 1px $uni-border-3; */padding:0 10px}.uni-select__selector-item[data-v-191ab648]:hover{background-color:#f9f9f9}.uni-select__selector-empty[data-v-191ab648]:last-child,\r\n.uni-select__selector-item[data-v-191ab648]:last-child{border-bottom:none}.uni-select__selector__disabled[data-v-191ab648]{opacity:.4;cursor:default}\r\n/* picker 弹出层通用的指示小三角 */.uni-popper__arrow[data-v-191ab648],\r\n.uni-popper__arrow[data-v-191ab648]::after{position:absolute;display:block;width:0;height:0;border-color:transparent;border-style:solid;border-width:6px}.uni-popper__arrow[data-v-191ab648]{-webkit-filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));top:-6px;left:10%;margin-right:3px;border-top-width:0;border-bottom-color:#ebeef5}.uni-popper__arrow[data-v-191ab648]::after{content:" ";top:1px;margin-left:-6px;border-top-width:0;border-bottom-color:#fff}.uni-select__input-text[data-v-191ab648]{width:100%;color:#333;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;overflow:hidden}.uni-select__input-placeholder[data-v-191ab648]{color:#6a6a6a;font-size:12px}.uni-select--mask[data-v-191ab648]{position:fixed;top:0;bottom:0;right:0;left:0}',""]),t.exports=e},fb12:function(t,e,a){var i=a("eed8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=a("967d").default;r("15e6434f",i,!0,{sourceMap:!1,shadowMode:!1})}}]);