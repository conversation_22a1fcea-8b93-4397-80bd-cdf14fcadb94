(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-payment~pages_promotion-bale-payment~pages_promotion-bargain-payment~pages_promotion-bun~6f10579c"],{"049e":function(e,t,a){"use strict";var i=a("621b"),o=a.n(i);o.a},"0817":function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2"),a("5c47"),a("2c10"),a("a1c1"),a("23f4"),a("7d2f"),a("9c4e"),a("ab80"),a("aa9c"),a("473f"),a("bf0f"),a("3efd");var o=i(a("af87")),s=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,r=/^<\/([-A-Za-z0-9_]+)[^>]*>/,n=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,l=f("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),d=f("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),c=f("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),p=f("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),u=f("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),v=f("script,style");function f(e){for(var t={},a=e.split(","),i=0;i<a.length;i++)t[a[i]]=!0;return t}var m=function(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e),e=function(e){return e=e.replace(/<!--[\s\S]*-->/gi,""),e}(e),e=function(e){var t='<img style="width:100% !important;display:block;max-width: '.concat("100%",' !important;"');return e=e.replace(/\\/g,"").replace(/<img/g,t),e=e.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(e,a){return t+' src="'+o.default.img(a)+'"/>'})),e}(e),e=function(e){return e=e.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(e,t){return e=e.replace(/[:](\s?)[\s\S]*/gi,(function(e,t){return e.replace(/"/g,"'")})),e})),e}(e);var t=[],a={node:"root",children:[]};return function(e,t){var a,i,o,f=[],m=e;f.last=function(){return this[this.length-1]};while(e){if(i=!0,f.last()&&v[f.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+f.last()+"[^>]*>"),(function(e,a){return a=a.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(a),""})),g("",f.last());else if(0==e.indexOf("\x3c!--")?(a=e.indexOf("--\x3e"),a>=0&&(t.comment&&t.comment(e.substring(4,a)),e=e.substring(a+3),i=!1)):0==e.indexOf("</")?(o=e.match(r),o&&(e=e.substring(o[0].length),o[0].replace(r,g),i=!1)):0==e.indexOf("<")&&(o=e.match(s),o&&(e=e.substring(o[0].length),o[0].replace(s,h),i=!1)),i){a=e.indexOf("<");var b=a<0?e:e.substring(0,a);e=a<0?"":e.substring(a),t.chars&&t.chars(b)}if(e==m)throw"Parse Error: "+e;m=e}function h(e,a,i,o){if(a=a.toLowerCase(),d[a])while(f.last()&&c[f.last()])g("",f.last());if(p[a]&&f.last()==a&&g("",a),o=l[a]||!!o,o||f.push(a),t.start){var s=[];i.replace(n,(function(e,t){var a=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:u[t]?t:"";s.push({name:t,value:a,escaped:a.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(a,s,o)}}function g(e,a){if(a){for(i=f.length-1;i>=0;i--)if(f[i]==a)break}else var i=0;if(i>=0){for(var o=f.length-1;o>=i;o--)t.end&&t.end(f[o]);f.length=i}}g()}(e,{start:function(e,i,o){var s={name:e};if(0!==i.length&&(s.attrs=function(e){return e.reduce((function(e,t){var a=t.value,i=t.name;return e[i]?e[i]=e[i]+" "+a:e[i]=a,e}),{})}(i)),o){var r=t[0]||a;r.children||(r.children=[]),r.children.push(s)}else t.unshift(s)},end:function(e){var i=t.shift();if(i.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)a.children.push(i);else{var o=t[0];o.children||(o.children=[]),o.children.push(i)}},chars:function(e){var i={type:"text",text:e};if(0===t.length)a.children.push(i);else{var o=t[0];o.children||(o.children=[]),o.children.push(i)}},comment:function(e){var a={node:"comment",text:e},i=t[0];i.children||(i.children=[]),i.children.push(a)}}),a.children};t.default=m},"0ddd":function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(a("c0030")),s={name:"common-payment",data:function(){return{}},props:{api:Object,createDataKey:String},mixins:[o.default]};t.default=s},3672:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("bf0f"),a("de6c"),a("5c47"),a("a1c1"),a("64aa"),a("e966"),a("5ef2"),a("c9b5"),a("ab80"),a("aa9c"),a("d4b5"),a("2797");var o=i(a("d745")),s={name:"nsSelectTime",components:{uniPopup:o.default},data:function(){return{key:0,keys:0,obj:{},dayData:[],timeData:[],judge:!1,keyJudge:0,dayTime:0}},methods:{refresh:function(){this.key=0,this.keys=0,this.keyJudge=0},open:function(e,t){this.dayData=[],this.timeData=[],this.obj=e,this.toDay(e.dataTime.time_type,e.dataTime.time_week),this.judge&&("no"==t?this.selectTime("","",t):this.$refs.selectTime.open())},selectTime:function(e,t,a){if("days"==e)this.keyJudge=t,this.toTime();else if("time"==e){this.keys=t,this.key=this.keyJudge;var i=this.dayData[this.key];i.time=this.timeData[this.keys];var o=i.time.replace("立即配送（","").replace("）",""),s=new Date,r=o.split("-"),n=r[0].split(":"),l=r[1].split(":"),d=i.month.split("月"),c=d[0],p=d[1].split("日")[0];s.setHours(n[0],n[1],0,0),i.start_time=s.getTime()/1e3,i.start_date=s.getFullYear()+"-"+c+"-"+p+" "+r[0],s.setHours(l[0],l[1],0,0),i.end_time=s.getTime()/1e3,i.end_date=s.getFullYear()+"-"+c+"-"+p+" "+r[1],this.$emit("selectTime",{data:i,type:a}),this.$refs.selectTime.close()}if("no"==a){this.toTime(a);var u=this.dayData[0];u.time=this.timeData[0];var v=new Date,f=u.time.replace("立即配送（","").replace("）","").split("-"),m=f[0].split(":"),b=f[1].split(":"),h=u.month.split("月"),g=h[0],_=h[1].split("日")[0];v.setHours(m[0],m[1],0,0),u.start_time=v.getTime()/1e3,u.start_date=v.getFullYear()+"-"+g+"-"+_+" "+f[0],v.setHours(b[0],b[1],0,0),u.end_time=v.getTime()/1e3,u.end_date=v.getFullYear()+"-"+g+"-"+_+" "+f[1],this.$emit("selectTime",{data:u,type:a})}this.$forceUpdate()},close:function(){this.$refs.selectTime.close()},toDay:function(e,t){var a=new Date;this.obj.dataTime.advance_day&&(a=new Date(a.getTime()+864e5*this.obj.dataTime.advance_day));var i=a.getFullYear(),o=a.getMonth()+1,s=a.getDate(),r=a.getDay(),n=new Date(i,o,0).getDate(),l=a.getHours(),d=a.getMinutes();this.dayTime=this.obj.dataTime.advance_day?0:3600*Number(l)+60*Number(d);var c=!1,p=1,u=this.obj.dataTime.most_day?this.obj.dataTime.most_day+1:1,v=parseInt(a.getTime()/1e3),f=["周日","周一","周二","周三","周四","周五","周六"];t.time_week&&7==t.time_week.length&&(c=!0);for(var m=0;m<u;m++){var b={},h=f[r];if(this.obj.dataTime.most_day>0&&v+86400*p>v+86400*this.obj.dataTime.most_day){this.judge=!0;break}if(0==e||c||-1!=t.indexOf(r.toString())){var g=this.obj.dataTime.delivery_time[this.obj.dataTime.delivery_time.length-1].end_time;switch(g-=60*this.obj.dataTime.time_interval,p){case 1:0==m&&(g<this.dayTime?m-=1:(b={title:0==this.obj.dataTime.advance_day?"今天":"",type:"special",month:o+"月"+s+"日",Day:"("+h+")"},this.dayData.push(b)));break;case 2:0!=m&&1!=m||(b={title:0==this.obj.dataTime.advance_day?"明天":"",month:o+"月"+s+"日",Day:"("+h+")"},this.dayData.push(b));break;default:b={title:"",month:o+"月"+s+"日",Day:"("+h+")"},this.dayData.push(b)}}else m-=1;s!=n?s+=1:(12!=o?o+=1:o=1,s=1),6!=r?r+=1:r=0,p+=1,0==this.obj.dataTime.most_day&&0==m&&(this.judge=!0)}this.toTime()},toTime:function(e){var t=this;"no"==e&&(this.key=0,this.keys=0,this.keyJudge=0);var a=[];this.obj.dataTime.delivery_time||(this.obj.dataTime.delivery_time=[{start_time:this.obj.dataTime.start_time,end_time:this.obj.dataTime.end_time}]);var i=JSON.parse(JSON.stringify(this.dayTime)),o=!1;this.dayData[this.keyJudge]&&this.dayData[this.keyJudge].type&&i>this.obj.dataTime.start_time&&(o=!0);var s=this.obj.dataTime.time_interval?60*this.obj.dataTime.time_interval:1200;this.obj.dataTime.delivery_time.forEach((function(e){e.end_time=e.end_time?e.end_time:86400;for(var r=parseInt((parseInt(e.end_time)-parseInt(e.start_time))/s),n=o?parseInt(i):parseInt(e.start_time),l=0;l<r;l++){if(parseInt(n)+parseInt(s)>e.end_time)break;if(o){if(n>=i)if(t.obj.dataTime.time_interval){if(n<=e.end_time){var d="";d="local"==t.obj.delivery.delivery_type&&0==l?"立即配送（"+t.$util.getTimeStr(n)+"-"+t.$util.getTimeStr(n+s)+"）":t.$util.getTimeStr(n)+"-"+t.$util.getTimeStr(n+s),a.push(d)}}else a.push(t.$util.getTimeStr(n))}else t.obj.dataTime.time_interval?n<=e.end_time&&a.push(t.$util.getTimeStr(n)+"-"+t.$util.getTimeStr(n+s)):a.push(t.$util.getTimeStr(n));n=parseInt(n)+s}})),this.timeData=a,this.$forceUpdate()}}};t.default=s},"47f2":function(e,t,a){"use strict";a.r(t);var i=a("86cd"),o=a("9fc9");for(var s in o)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(s);a("049e");var r=a("828b"),n=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"6f865b62",null,!1,i["a"],void 0);t["default"]=n.exports},6064:function(e,t,a){"use strict";var i=a("b42f"),o=a.n(i);o.a},"621b":function(e,t,a){var i=a("b417");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("66f6c4ac",i,!0,{sourceMap:!1,shadowMode:!1})},"86cd":function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return i}));var i={nsForm:a("ae30").default,nsSwitch:a("b0ec").default,payment:a("b6f2").default,uniPopup:a("d745").default,nsEmpty:a("52a6").default,nsMpHtml:a("d108").default,nsSelectTime:a("a523").default,nsLogin:a("2910").default,loadingCover:a("c003").default},o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"order-container",class:{"safe-area":e.isIphoneX}},[a("v-uni-scroll-view",{staticClass:"order-scroll-container",attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"payment-navbar-block"}),e.paymentData?[e.paymentData.is_virtual?[a("v-uni-view",{staticClass:"mobile-wrap"},[a("v-uni-view",{staticClass:"tips color-base-text"},[a("v-uni-text",{staticClass:"iconfont icongantanhao"}),e._v("购买虚拟类商品需填写手机号，方便商家与您联系")],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"icon"},[a("v-uni-image",{attrs:{src:e.$util.img("public/uniapp/order/icon-mobile.png"),mode:"widthFix"}})],1),a("v-uni-text",{staticClass:"text"},[e._v("手机号码")]),a("v-uni-input",{staticClass:"input",attrs:{type:"number",maxlength:"11",placeholder:"请输入您的手机号码","placeholder-class":"color-tip placeholder"},model:{value:e.orderCreateData.member_address.mobile,callback:function(t){e.$set(e.orderCreateData.member_address,"mobile",t)},expression:"orderCreateData.member_address.mobile"}})],1)],1)]:[e.goodsData.delivery.express_type.length>1?a("v-uni-view",{staticClass:"delivery-mode"},[a("v-uni-view",{staticClass:"action"},e._l(e.goodsData.delivery.express_type,(function(t,i){return a("v-uni-view",{key:i,class:{active:t.name==e.orderCreateData.delivery.delivery_type},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectDeliveryType(t)}}},[e._v(e._s(t.title)),a("v-uni-view",{staticClass:"out-radio"})],1)})),1)],1):e._e(),"express"==e.orderCreateData.delivery.delivery_type?a("v-uni-view",{staticClass:"address-box",class:{"not-delivery-type":e.goodsData.delivery.express_type.length<=1}},[e.memberAddress?a("v-uni-view",{staticClass:"info-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"content"},[a("v-uni-text",{staticClass:"name"},[e._v(e._s(e.memberAddress.name?e.memberAddress.name:""))]),a("v-uni-text",{staticClass:"mobile"},[e._v(e._s(e.memberAddress.mobile?e.memberAddress.mobile:""))]),a("v-uni-view",{staticClass:"desc-wrap"},[e._v(e._s(e.memberAddress.full_address?e.memberAddress.full_address:"")+"\n\t\t\t\t\t\t\t\t"+e._s(e.memberAddress.address?e.memberAddress.address:""))])],1),a("v-uni-text",{staticClass:"cell-more iconfont icon-right"})],1):a("v-uni-view",{staticClass:"empty-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"info"},[e._v("请设置收货地址")]),a("v-uni-view",{staticClass:"cell-more"},[a("v-uni-view",{staticClass:"iconfont icon-right"})],1)],1),a("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],1):e._e(),"local"==e.orderCreateData.delivery.delivery_type?a("v-uni-view",{staticClass:"address-box",class:{"not-delivery-type":e.goodsData.delivery.express_type.length<=1}},[e.localMemberAddress?a("v-uni-view",[e.storeList&&Object.keys(e.storeList).length>0?[a("v-uni-view",{staticClass:"local-delivery-store"},[e.storeInfo?a("v-uni-view",{staticClass:"info"},[e._v("由"),a("v-uni-text",{staticClass:"store-name"},[e._v(e._s(e.storeInfo.store_name))]),e._v("提供配送"),a("v-uni-view",[e._v("营业时间："+e._s(e.storeInfo.open_date))])],1):a("v-uni-view",{staticClass:"info"},[a("v-uni-text",{staticClass:"store-name"},[e._v("超出配送范围，请选择其他门店")])],1),Object.keys(e.storeList).length>1?a("v-uni-view",{staticClass:"cell-more",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("deliveryPopup")}}},[a("v-uni-text",[e._v("点击切换")]),a("v-uni-text",{staticClass:"iconfont icon-right"})],1):e._e()],1)]:e._e(),a("v-uni-view",{staticClass:"info-wrap local",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"content"},[a("v-uni-text",{staticClass:"name"},[e._v(e._s(e.localMemberAddress.name?e.localMemberAddress.name:""))]),a("v-uni-text",{staticClass:"mobile"},[e._v(e._s(e.localMemberAddress.mobile?e.localMemberAddress.mobile:""))]),a("v-uni-view",{staticClass:"desc-wrap"},[e._v(e._s(e.localMemberAddress.full_address?e.localMemberAddress.full_address:"")+"\n\t\t\t\t\t\t\t\t\t"+e._s(e.localMemberAddress.address?e.localMemberAddress.address:""))])],1),a("v-uni-text",{staticClass:"cell-more iconfont icon-right"})],1),e.calculateGoodsData.config.local&&1==e.calculateGoodsData.delivery.local.info.time_is_open?a("v-uni-view",{staticClass:"local-box"},[a("v-uni-view",{staticClass:"pick-block",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.localtime("")}}},[a("v-uni-view",{staticClass:"title font-size-base"},[e._v("送达时间")]),a("v-uni-view",{staticClass:"time-picker"},[a("v-uni-text",{class:{"color-tip":!e.deliveryTime}},[e._v(e._s(e.deliveryTime?e.deliveryTime:"请选择送达时间"))]),a("v-uni-text",{staticClass:"iconfont icon-right cell-more"})],1)],1)],1):e._e()],2):a("v-uni-view",{staticClass:"empty-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAddress.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"info"},[e._v("请设置收货地址")]),a("v-uni-view",{staticClass:"cell-more"},[a("v-uni-view",{staticClass:"iconfont icon-right"})],1)],1),a("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],1):e._e(),"store"==e.orderCreateData.delivery.delivery_type?a("v-uni-view",{staticClass:"store-box",class:{"not-delivery-type":e.goodsData.delivery.express_type.length<=1}},[e.storeInfo?[a("v-uni-view",{staticClass:"store-info",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("deliveryPopup")}}},[a("v-uni-view",{staticClass:"store-address-info"},[a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-view",{staticClass:"title"},[a("v-uni-text",[e._v(e._s(e.storeInfo.store_name))])],1),a("v-uni-view",{staticClass:"store-detail"},[0==e.storeInfo.status&&e.storeInfo.close_desc?a("v-uni-view",{staticClass:"close-desc"},[e._v(e._s(e.storeInfo.close_desc))]):e._e(),e.storeInfo.open_date?a("v-uni-view",[e._v("营业时间："+e._s(e.storeInfo.open_date))]):e._e(),a("v-uni-view",{staticClass:"address"},[e._v(e._s(e.storeInfo.full_address)+" "+e._s(e.storeInfo.address))])],1)],1),e.storeList&&Object.keys(e.storeList).length>1?a("v-uni-view",{staticClass:"cell-more iconfont icon-right"}):e._e()],1)],1),e.orderCreateData.member_address?a("v-uni-view",{staticClass:"mobile-wrap store-mobile"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"text"},[e._v("姓名")]),a("v-uni-input",{staticClass:"input",attrs:{type:"text","placeholder-class":"color-tip placeholder",disabled:!0},model:{value:e.orderCreateData.member_address.name,callback:function(t){e.$set(e.orderCreateData.member_address,"name",t)},expression:"orderCreateData.member_address.name"}})],1)],1):e._e(),e.orderCreateData.member_address?a("v-uni-view",{staticClass:"mobile-wrap store-mobile"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-text",{staticClass:"text"},[e._v("预留手机")]),a("v-uni-input",{staticClass:"input",attrs:{type:"number",maxlength:"11",placeholder:"请输入您的手机号码","placeholder-class":"color-tip placeholder"},model:{value:e.orderCreateData.member_address.mobile,callback:function(t){e.$set(e.orderCreateData.member_address,"mobile",t)},expression:"orderCreateData.member_address.mobile"}})],1)],1):e._e(),e.goodsData.jielong_id?a("v-uni-view",{staticClass:"store-time"},[a("v-uni-view",{staticClass:"left"},[e._v("提货时间")]),a("v-uni-view",{staticClass:"right"},[e._v(e._s(e.$util.timeStampTurnTime(e.goodsData.jielong_info.take_start_time,"Y/m/d"))+" ~ "+e._s(e.$util.timeStampTurnTime(e.goodsData.jielong_info.take_end_time,"Y/m/d")))])],1):a("v-uni-view",{staticClass:"store-time",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.storetime("")}}},[a("v-uni-view",{staticClass:"left"},[e._v("提货时间")]),a("v-uni-view",{staticClass:"right"},[e._v(e._s(e.deliveryTime)),a("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1)]:a("v-uni-view",{staticClass:"empty"},[e._v("当前无自提门店，请选择其它配送方式")]),a("v-uni-image",{staticClass:"address-line",attrs:{src:e.$util.img("public/uniapp/order/address-line.png")}})],2):e._e()],e.calculateGoodsData?a("v-uni-view",{staticClass:"site-wrap order-goods"},[a("v-uni-view",{staticClass:"site-body"},e._l(e.calculateGoodsData.goods_list,(function(t,i){return a("v-uni-view",{key:i,staticClass:"goods-item"},[a("v-uni-view",{staticClass:"goods-wrap"},[a("v-uni-view",{staticClass:"goods-img",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})}}},[a("v-uni-image",{attrs:{src:e.$util.img(t.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.imageError(i)}}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-view",{staticClass:"top-wrap"},[a("v-uni-view",{staticClass:"goods-name",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})}}},[e._v(e._s(t.sku_name))]),t.sku_spec_format?a("v-uni-view",{staticClass:"sku"},[a("v-uni-view",{staticClass:"goods-spec"},[e._l(t.sku_spec_format,(function(t,i){return[a("v-uni-view",[e._v(e._s(t.spec_value_name))])]}))],2)],1):e._e(),0==t.is_virtual?[e.orderCreateData.delivery&&e.orderCreateData.delivery.delivery_type&&t.support_trade_type&&-1==t.support_trade_type.indexOf(e.orderCreateData.delivery.delivery_type)?a("v-uni-view",{staticClass:"error-tips"},[a("v-uni-text",{staticClass:"iconfont icon-gantanhao"}),a("v-uni-text",[e._v("该商品不支持"+e._s(e.orderCreateData.delivery.delivery_type_name))])],1):e._e()]:e._e(),t.error&&t.error.message?a("v-uni-view",{staticClass:"error-tips"},[a("v-uni-text",{staticClass:"iconfont icon-gantanhao"}),a("v-uni-text",[e._v(e._s(t.error.message))])],1):e._e()],2),a("v-uni-view",{staticClass:"goods-sub-section"},[a("v-uni-view",{staticClass:"color-base-text"},[a("v-uni-text",{staticClass:"unit price-style small"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"goods-price price-style large"},[e._v(e._s(parseFloat(t.price).toFixed(2).split(".")[0]))]),a("v-uni-text",{staticClass:"unit price-style small"},[e._v("."+e._s(parseFloat(t.price).toFixed(2).split(".")[1]))])],1),a("v-uni-view",[a("v-uni-text",{staticClass:"font-size-tag"},[e._v("x")]),a("v-uni-text",{staticClass:"font-size-base"},[e._v(e._s(t.num))])],1)],1)],1)],1),e.calculateGoodsData.goods_list[i].member_card_list?a("v-uni-view",{staticClass:"member-goods-card order-cell",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectMemberGoodsCard(i)}}},[a("v-uni-text",{staticClass:"tit"},[e._v("次卡抵扣")]),a("v-uni-view",{staticClass:"box text-overflow"},[e.calculateGoodsData.goods_list[i].card_promotion_money?[a("v-uni-text",{staticClass:"text"},[e._v("次卡抵扣"+e._s(e.calculateGoodsData.goods_list[i].card_use_num)+"张/"+e._s(e.calculateGoodsData.goods_list[i].card_use_num)+"次")]),a("v-uni-text",{staticClass:"price-font"},[e._v("-￥"+e._s(e._f("moneyFormat")(e.calculateGoodsData.goods_list[i].card_promotion_money)))])]:a("v-uni-text",{staticClass:"color-tip"},[e._v("请选择次卡")])],2),a("v-uni-text",{staticClass:"iconfont icon-right"})],1):e._e(),e.goodsData.goods_list[i].goods_form?a("v-uni-view",{staticClass:"goods-form",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.editForm(i)}}},[a("ns-form",{ref:"goodsForm",refInFor:!0,attrs:{data:e.goodsData.goods_list[i].goods_form.json_data,"custom-attr":{sku_id:t.sku_id,form_id:e.goodsData.goods_list[i].goods_form.id}}}),a("v-uni-text",{staticClass:"cell-more iconfont icon-right"}),a("v-uni-view",{staticClass:"shade"})],1):e._e()],1)})),1)],1):e._e(),a("v-uni-view",{staticClass:"site-wrap buyer-message"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("买家留言")]),a("v-uni-view",{staticClass:"box text-overflow ",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("buyerMessagePopup")}}},[e.orderCreateData.buyer_message?a("v-uni-text",[e._v(e._s(e.orderCreateData.buyer_message))]):a("v-uni-text",{staticClass:"color-sub"},[e._v("无留言")])],1),a("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1),e.paymentData.system_form?a("v-uni-view",{staticClass:"system-form-wrap"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v(e._s(e.paymentData.system_form.form_name))])],1),a("ns-form",{ref:"form",attrs:{data:e.paymentData.system_form.json_data}})],1):e._e(),e.calculateGoodsData||e.promotionInfo||e.calculateGoodsData&&e.calculateGoodsData.max_usable_point>0||e.goodsData.invoice?a("v-uni-view",{staticClass:"site-wrap"},[a("v-uni-view",{staticClass:"site-footer"},[-1!=e.modules.indexOf("coupon")?a("v-uni-view",{staticClass:"order-cell coupon"},[a("v-uni-text",{staticClass:"tit"},[e._v("优惠券")]),a("v-uni-view",{staticClass:"box text-overflow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("couponPopup")}}},[e.orderCreateData.coupon&&e.orderCreateData.coupon.coupon_id?[a("v-uni-text",[e._v("已使用优惠券，优惠")]),a("v-uni-text",{staticClass:"unit price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money price-font"},[e._v(e._s(e._f("moneyFormat")(e.calculateData&&e.calculateData.coupon_money?e.calculateData.coupon_money:0)))])]:a("v-uni-text",[e._v("不使用优惠券")])],2),a("v-uni-text",{staticClass:"iconfont icon-right"})],1):e._e(),e.promotionInfo?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("活动优惠")]),a("v-uni-view",{staticClass:"box text-overflow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("promotionPopup")}}},[a("v-uni-text",[e._v(e._s(e.promotionInfo.title))])],1),a("v-uni-text",{staticClass:"iconfont icon-right"})],1):e._e(),e.calculateGoodsData&&e.calculateGoodsData.max_usable_point>0?a("v-uni-view",{staticClass:"order-cell point"},[a("v-uni-text",{staticClass:"tit"},[a("v-uni-text",[e._v("使用"+e._s(parseInt(e.calculateGoodsData.max_usable_point))+"积分可抵扣")]),a("v-uni-text",{staticClass:"unit price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money price-font"},[e._v(e._s(e._f("moneyFormat")(e.calculateData.point_money)))])],1),a("v-uni-view",{staticClass:"box"}),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==e.orderCreateData.is_point},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.usePoint.apply(void 0,arguments)}}})],1):e._e(),1==e.goodsData.invoice.invoice_status?a("v-uni-view",{staticClass:"order-cell order-invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票")]),a("v-uni-view",{staticClass:"box text-overflow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openPopup("invoicePopup")}}},[1==e.orderCreateData.is_invoice?a("v-uni-text",[e._v(e._s(1==e.orderCreateData.invoice_type?"纸质":"电子")+"发票("+e._s(e.orderCreateData.invoice_content)+")")]):a("v-uni-text",[e._v("无需发票")])],1),a("v-uni-text",{staticClass:"iconfont icon-right"})],1):e._e()],1)],1):e._e(),e.paymentData.recommend_member_card&&Object.keys(e.paymentData.recommend_member_card).length>0?a("v-uni-view",{staticClass:"site-wrap box member-card-wrap"},[a("v-uni-view",{staticClass:"head",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectMemberCard.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"iconfont icon-huiyuan"}),a("v-uni-view",{staticClass:"info"},[e._v("开通"+e._s(e.paymentData.recommend_member_card.level_name)),a("v-uni-text",[e._v("本单预计可省")]),a("v-uni-text",{staticClass:"price-color"},[e._v(e._s(e._f("moneyFormat")(e.paymentData.recommend_member_card.discount_money)))]),a("v-uni-text",[e._v("元")])],1),a("v-uni-text",{staticClass:"iconfont",class:1==e.orderCreateData.is_open_card?"icon-yuan_checked color-base-text":"icon-yuan_checkbox"})],1),e.orderCreateData.is_open_card?a("v-uni-view",{staticClass:"body"},e._l(e.cardChargeType,(function(t,i){return a("v-uni-view",{key:i,staticClass:"item",class:{"active color-base-border":t.key==e.orderCreateData.member_card_unit},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectMemberCardUnit(t.key)}}},[a("v-uni-view",{staticClass:"title"},[e._v(e._s(t.title))]),a("v-uni-view",{staticClass:"price price-font"},[e._v(e._s(e.$lang("common.currencySymbol"))+e._s(parseFloat(t.value))+"/"+e._s(t.unit))]),t.key==e.orderCreateData.member_card_unit?a("v-uni-text",{staticClass:"iconfont icon-icon color-base-text price-font identify"}):e._e()],1)})),1):e._e()],1):e._e(),e.calculateData?[a("v-uni-view",{staticClass:"order-money"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("商品金额")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"unit color-title price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money color-title price-font"},[e._v(e._s(e._f("moneyFormat")(e.calculateData.goods_money)))])],1)],1),0==e.calculateData.is_virtual&&e.calculateData.delivery_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("运费")]),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("+")]),a("v-uni-text",{staticClass:"unit price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money price-font"},[e._v(e._s(e._f("moneyFormat")(e.calculateData.delivery_money)))])],1)],1):e._e(),e.orderCreateData.is_invoice&&e.calculateData.invoice_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[a("v-uni-text",[e._v("税费")]),a("v-uni-text",{staticClass:"color-base-text font-bold price-font"},[e._v("("+e._s(e.goodsData.invoice.invoice_rate)+"%)")])],1),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("+")]),a("v-uni-text",{staticClass:"unit price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money price-font"},[e._v(e._s(e._f("moneyFormat")(e.calculateData.invoice_money)))])],1)],1):e._e(),e.orderCreateData.is_invoice&&e.calculateData.invoice_delivery_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票邮寄费")]),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("+")]),a("v-uni-text",{staticClass:"unit price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money price-font"},[e._v(e._s(e._f("moneyFormat")(e.calculateData.invoice_delivery_money)))])],1)],1):e._e(),e.calculateData.promotion_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("优惠")]),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("-")]),a("v-uni-text",{staticClass:"unit price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money price-font"},[e._v(e._s(e._f("moneyFormat")(e.calculateData.promotion_money)))])],1)],1):e._e(),e.calculateData.coupon_money?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("优惠券")]),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("-")]),a("v-uni-text",{staticClass:"unit price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money price-font"},[e._v(e._s(e._f("moneyFormat")(e.calculateData.coupon_money)))])],1)],1):e._e(),e.calculateData.point_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("积分抵扣")]),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("-")]),a("v-uni-text",{staticClass:"unit price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money price-font"},[e._v(e._s(e._f("moneyFormat")(e.calculateData.point_money)))])],1)],1):e._e(),e.calculateData.member_card_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("会员卡")]),a("v-uni-view",{staticClass:"box color-base-text"},[a("v-uni-text",{staticClass:"operator"},[e._v("+")]),a("v-uni-text",{staticClass:"unit price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money price-font"},[e._v(e._s(e._f("moneyFormat")(e.calculateData.member_card_money)))])],1)],1):e._e()],1),e.transactionAgreement.title&&e.transactionAgreement.content?a("v-uni-view",{staticClass:"agreement"},[e._v("购买前请先阅读"),a("v-uni-text",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.agreementPopup.open()}}},[e._v("《"+e._s(e.transactionAgreement.title)+"》")]),e._v("，下单即代表同意该协议")],1):e._e(),a("v-uni-view",{staticClass:"order-submit bottom-safe-area"},[a("v-uni-view",{staticClass:"order-settlement-info"},[a("v-uni-text",{staticClass:"font-size-base color-tip margin-right"},[e._v("共"+e._s(e.calculateData.goods_num)+"件")]),a("v-uni-text",{staticClass:"font-size-base"},[e._v("合计：")]),a("v-uni-text",{staticClass:" unit price-font"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:" money price-font"},[e._v(e._s(parseFloat(e.calculateData.pay_money).toFixed(2).split(".")[0]))]),a("v-uni-text",{staticClass:" unit price-font"},[e._v("."+e._s(parseFloat(e.calculateData.pay_money).toFixed(2).split(".")[1]))])],1),a("v-uni-view",{staticClass:"submit-btn"},[e.surplusStartMoney()?a("v-uni-button",{staticClass:"no-submit mini",attrs:{size:"mini"}},[e._v("差"+e._s(e._f("moneyFormat")(e.surplusStartMoney()))+"起送")]):a("v-uni-button",{staticClass:"mini",attrs:{type:"primary",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.create()}}},[e._v("提交订单")])],1)],1),a("v-uni-view",{staticClass:"order-submit-block"}),e.calculateData?a("payment",{ref:"choosePaymentPopup",on:{close:function(t){arguments[0]=t=e.$handleEvent(t),e.payClose.apply(void 0,arguments)}}}):e._e()]:e._e(),a("uni-popup",{ref:"invoicePopup",attrs:{type:"bottom","mask-click":!1}},[a("v-uni-view",{staticClass:"invoice-popup popup",style:1==e.orderCreateData.is_invoice?"height: 83vh;":"height: 48vh;",on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("invoicePopup")}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[a("v-uni-view",[e.goodsData.invoice?a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("需要发票")]),a("v-uni-view",{staticClass:"option-grpup"},[a("v-uni-view",{staticClass:"option-item",class:{"color-base-bg active":0==e.orderCreateData.is_invoice},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeIsInvoice.apply(void 0,arguments)}}},[e._v("不需要")]),a("v-uni-view",{staticClass:"option-item",class:{"color-base-bg active":1==e.orderCreateData.is_invoice},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeIsInvoice.apply(void 0,arguments)}}},[e._v("需要")])],1)],1):e._e(),1==e.orderCreateData.is_invoice?[a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票类型")]),a("v-uni-view",{staticClass:"option-grpup"},e._l(e.goodsData.invoice.invoice_type.split(","),(function(t,i){return a("v-uni-view",{key:i,staticClass:"option-item",class:{"color-base-bg active":e.orderCreateData.invoice_type==t},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.changeInvoiceType(t)}}},[e._v(e._s(1==t?"纸质":"电子"))])})),1)],1),a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("抬头类型")]),a("v-uni-view",{staticClass:"option-grpup"},[a("v-uni-view",{staticClass:"option-item",class:{"color-base-bg active":1==e.orderCreateData.invoice_title_type},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeInvoiceTitleType(1)}}},[e._v("个人")]),a("v-uni-view",{staticClass:"option-item",class:{"color-base-bg active":2==e.orderCreateData.invoice_title_type},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeInvoiceTitleType(2)}}},[e._v("企业")])],1)],1),a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票信息")]),a("v-uni-view",{staticClass:"invoice-form-group"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请填写抬头名称"},model:{value:e.orderCreateData.invoice_title,callback:function(t){e.$set(e.orderCreateData,"invoice_title","string"===typeof t?t.trim():t)},expression:"orderCreateData.invoice_title"}}),2==e.orderCreateData.invoice_title_type?a("v-uni-input",{attrs:{type:"text",placeholder:"请填写纳税人识别号"},model:{value:e.orderCreateData.taxpayer_number,callback:function(t){e.$set(e.orderCreateData,"taxpayer_number","string"===typeof t?t.trim():t)},expression:"orderCreateData.taxpayer_number"}}):e._e(),1==e.orderCreateData.invoice_type?a("v-uni-input",{attrs:{type:"text",placeholder:"请填写邮寄地址"},model:{value:e.orderCreateData.invoice_full_address,callback:function(t){e.$set(e.orderCreateData,"invoice_full_address","string"===typeof t?t.trim():t)},expression:"orderCreateData.invoice_full_address"}}):e._e(),2==e.orderCreateData.invoice_type?a("v-uni-input",{attrs:{type:"text",placeholder:"请填写邮箱"},model:{value:e.orderCreateData.invoice_email,callback:function(t){e.$set(e.orderCreateData,"invoice_email","string"===typeof t?t.trim():t)},expression:"orderCreateData.invoice_email"}}):e._e()],1)],1),a("v-uni-view",{staticClass:"invoice-cell"},[a("v-uni-text",{staticClass:"tit"},[e._v("发票内容")]),a("v-uni-view",{staticClass:"option-grpup"},e._l(e.goodsData.invoice.invoice_content_array,(function(t,i){return a("v-uni-view",{key:i,staticClass:"option-item content",class:{"color-base-bg active":t==e.orderCreateData.invoice_content},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.changeInvoiceContent(t)}}},[e._v(e._s(t))])})),1)],1)]:e._e(),a("v-uni-view",{staticClass:"invoice-tops"},[e._v("发票内容将以根据税法调整，具体请以展示为准，发票内容显示详细商品名 称及价格信息")])],2)],1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveInvoice.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg"},[e._v("确定")])],1)],1)],1),e.promotionInfo?a("uni-popup",{ref:"promotionPopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"promotion-popup popup"},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("活动优惠")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("promotionPopup")}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"order-cell",staticStyle:{"align-items":"baseline"}},[a("v-uni-view",{staticClass:"tit"},[a("v-uni-text",{staticClass:"promotion-mark ns-gradient-promotionpages-payment"},[e._v(e._s(e.promotionInfo.title))])],1),a("v-uni-view",{staticClass:"promotion-content"},[a("v-uni-view",{staticClass:"tit tit-content",staticStyle:{"white-space":"pre-line"},domProps:{innerHTML:e._s(e.promotionInfo.content)}})],1)],1)],1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("promotionPopup")}}},[e._v("确定")])],1)],1)],1):e._e(),a("uni-popup",{ref:"deliveryPopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"delivery-popup popup"},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("已为您甄选出附近所有相关门店")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("deliveryPopup")}}})],1),a("v-uni-view",{staticClass:"popup-body store-popup",class:{"safe-area":e.isIphoneX}},[a("mescroll-uni",{ref:"mescroll",attrs:{top:"50px"},on:{getData:function(t){arguments[0]=t=e.$handleEvent(t),e.getStore.apply(void 0,arguments)}}},[a("template",{attrs:{slot:"list"},slot:"list"},[a("v-uni-view",{staticClass:"delivery-content"},[e.storeData?e._l(e.storeData,(function(t,i){return a("v-uni-view",{key:i,staticClass:"item-wrap",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectPickupPoint(t)}}},[a("v-uni-view",{staticClass:"detail"},[a("v-uni-view",{staticClass:"name",class:t.store_id==e.orderCreateData.delivery.store_id?"color-base-text":""},[a("v-uni-text",[e._v(e._s(t.store_name))]),t.distance?a("v-uni-text",[e._v("("+e._s(t.distance)+"km)")]):e._e()],1),a("v-uni-view",{staticClass:"info"},[0==t.status&&t.close_desc?a("v-uni-view",{staticClass:"close-desc"},[e._v(e._s(t.close_desc))]):e._e(),a("v-uni-view",{staticClass:"font-size-goods-tag",class:t.store_id==e.orderCreateData.delivery.store_id?"color-base-text":""},[e._v("营业时间："+e._s(t.open_date))]),a("v-uni-view",{staticClass:"font-size-goods-tag",class:t.store_id==e.orderCreateData.delivery.store_id?"color-base-text":""},[e._v("地址："+e._s(t.full_address)+e._s(t.address))])],1)],1),t.store_id==e.orderCreateData.delivery.store_id?a("v-uni-view",{staticClass:"icon"},[a("v-uni-text",{staticClass:"iconfont icon-yuan_checked color-base-text"})],1):e._e()],1)})):a("v-uni-view",{staticClass:"empty-wrap"},[a("ns-empty",{attrs:{text:"所选择收货地址附近没有可以自提的门店",isIndex:!1}})],1)],2)],1)],2)],1)],1)],1),a("uni-popup",{ref:"buyerMessagePopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"buyermessag-popup popup",staticStyle:{height:"auto"},on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("买家留言")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("buyerMessagePopup")}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[a("v-uni-view",[a("v-uni-view",{staticClass:"buyermessag-cell"},[a("v-uni-view",{staticClass:"buyermessag-form-group"},[a("v-uni-textarea",{attrs:{type:"text",maxlength:"100",placeholder:"留言前建议先与商家协调一致","placeholder-class":"color-tip"},model:{value:e.orderCreateData.buyer_message,callback:function(t){e.$set(e.orderCreateData,"buyer_message",t)},expression:"orderCreateData.buyer_message"}})],1)],1)],1)],1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveBuyerMessage.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg"},[e._v("确定")])],1)],1)],1),e.calculateGoodsData?a("uni-popup",{ref:"couponPopup",attrs:{type:"bottom","mask-click":!1}},[a("v-uni-view",{staticClass:"coupon-popup popup",on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("优惠券")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup("couponPopup")}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[e.coupon_list.length>0?a("v-uni-view",e._l(e.coupon_list,(function(t,i){return a("v-uni-view",{key:i,staticClass:"coupon-item",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectCoupon(t)}}},[a("v-uni-view",{staticClass:"coupon-info",style:{backgroundColor:"var(--main-color-shallow)"}},[a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-image",{staticClass:"coupon-line",attrs:{mode:"heightFix",src:e.$util.img("public/uniapp/coupon/coupon_line.png")}}),a("v-uni-view",{staticClass:"coupon-money"},["divideticket"==t.type||"reward"==t.type?[a("v-uni-text",{staticClass:"unit"},[e._v(e._s(e.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"money"},[e._v(e._s(parseFloat(t.money)))])]:"discount"==t.type?[a("v-uni-text",{staticClass:"money"},[e._v(e._s(parseFloat(t.discount)))]),a("v-uni-text",{staticClass:"unit"},[e._v("折")])]:e._e(),a("v-uni-view",{staticClass:"at-least"},[t.at_least>0?[e._v("满"+e._s(t.at_least)+"可用")]:[e._v("无门槛")]],2)],2)],1),a("v-uni-view",{staticClass:"desc-wrap"},[a("v-uni-view",{staticClass:"coupon-name"},[e._v(e._s(t.coupon_name))]),"discount"==t.type&&t.discount_limit>0?a("v-uni-view",{staticClass:"limit"},[e._v("最多可抵￥"+e._s(t.discount_limit))]):e._e(),a("v-uni-view",{staticClass:"time font-size-goods-tag"},[e._v("有效期："+e._s(t.end_time?e.$util.timeStampTurnTime(t.end_time):"长期有效"))])],1),a("v-uni-view",{staticClass:"iconfont",class:e.orderCreateData.coupon.coupon_id==t.coupon_id?"icon-yuan_checked color-base-text":"icon-yuan_checkbox"})],1)],1)})),1):a("v-uni-view",{staticClass:"coupon-empty"},[e._v("暂无可用的优惠券")])],1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.useCoupon.apply(void 0,arguments)}}},[e._v("确定")])],1)],1)],1):e._e(),a("v-uni-view",{on:{touchmove:function(t){t.preventDefault(),arguments[0]=t=e.$handleEvent(t)}}},[a("uni-popup",{ref:"agreementPopup",attrs:{type:"center",maskClick:!1}},[a("v-uni-view",{staticClass:"agreement-conten-box"},[a("v-uni-view",{staticClass:"close"},[a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.agreementPopup.close()}}})],1),a("v-uni-view",{staticClass:"title"},[e._v(e._s(e.transactionAgreement.title))]),a("v-uni-view",{staticClass:"con"},[a("v-uni-scroll-view",{staticClass:"con",attrs:{"scroll-y":"true"}},[a("ns-mp-html",{attrs:{content:e.transactionAgreement.content}})],1)],1)],1)],1)],1),a("uni-popup",{ref:"editFormPopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"form-popup popup",staticStyle:{height:"auto"},on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("买家信息")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.editFormPopup.close()}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},[e.tempFormData?a("ns-form",{ref:"tempForm",attrs:{data:e.tempFormData.json_data}}):e._e()],1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveForm.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg"},[e._v("确定")])],1)],1)],1),a("uni-popup",{ref:"memberGoodsCardPopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"member-card-popup popup",on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[e._v("选择次卡")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.memberGoodsCardPopup.close()}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":e.isIphoneX},attrs:{"scroll-y":"true"}},e._l(e.selectGoodsCard.cardList,(function(t,i){return a("v-uni-view",{staticClass:"card-item",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectGoodsCard.click(t.item_id)}}},[a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"title"},[e._v(e._s(t.goods_name))]),a("v-uni-view",{staticClass:"info"},["timecard"==t.card_type?a("v-uni-text",[e._v("不限次数")]):e._e(),"oncecard"==t.card_type?a("v-uni-text",[e._v("剩余"+e._s(t.num-t.use_num)+"次")]):e._e(),"commoncard"==t.card_type?a("v-uni-text",[e._v("剩余"+e._s(t.total_num-t.total_use_num)+"次")]):e._e(),a("v-uni-text",[e._v("|")]),a("v-uni-text",[e._v(e._s(t.end_time?e.$util.timeStampTurnTime(t.end_time):"长期有效"))])],1)],1),a("v-uni-view",{staticClass:"iconfont",class:e.selectGoodsCard.itemId==t.item_id?"icon-yuan_checked color-base-text":"icon-yuan_checkbox"})],1)})),1),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":e.isIphoneX},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveMemberGoodsCard.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg"},[e._v("确定")])],1)],1)],1)]:e._e()],2),a("ns-select-time",{ref:"timePopup",on:{selectTime:function(t){arguments[0]=t=e.$handleEvent(t),e.selectPickupTime.apply(void 0,arguments)}}}),a("ns-login",{ref:"login"}),a("loading-cover",{ref:"loadingCover"})],1)},s=[]},"9fc9":function(e,t,a){"use strict";a.r(t);var i=a("0ddd"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(s);t["default"]=o.a},a523:function(e,t,a){"use strict";a.r(t);var i=a("d68f"),o=a("c4af");for(var s in o)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(s);a("6064");var r=a("828b"),n=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"8e6fd976",null,!1,i["a"],void 0);t["default"]=n.exports},b417:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-6f865b62] uni-input,[data-v-6f865b62] uni-view{font-size:%?24?%}.font-bold[data-v-6f865b62]{font-weight:700}.order-container[data-v-6f865b62]{width:100vw;height:100vh;display:flex;flex-direction:column;background:linear-gradient(180deg,var(--base-color) 10%,#f8f8f8);background-size:100% %?260?%;background-repeat:no-repeat}.order-container .order-scroll-container[data-v-6f865b62]{width:100%;height:0;flex:1}.order-container .payment-navbar-block[data-v-6f865b62]{height:%?60?%}.payment-navbar[data-v-6f865b62]{width:100vw;padding-bottom:%?20?%;position:fixed;left:0;top:0;z-index:100;background:linear-gradient(180deg,var(--base-color) 10%,#f8f8f8);background-size:100% %?260?%}.payment-navbar .nav-wrap[data-v-6f865b62]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;box-sizing:border-box;position:relative}.payment-navbar .navbar-title[data-v-6f865b62]{color:#fff;font-size:%?32?%}.payment-navbar .icon-back_light[data-v-6f865b62]{color:#fff;position:absolute;left:%?24?%;font-size:%?40?%}.payment-navbar-block[data-v-6f865b62]{padding-bottom:%?20?%}.mobile-wrap[data-v-6f865b62]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%}.mobile-wrap .tips[data-v-6f865b62]{font-size:%?22?%;margin-bottom:%?30?%;background:var(--main-color-shallow);border-radius:%?10?%;padding:%?20?% %?30?%;line-height:1;display:flex;align-items:center}.mobile-wrap .tips .iconfont[data-v-6f865b62]{margin-right:%?5?%}.mobile-wrap.local-mobile[data-v-6f865b62]{border-bottom:%?2?% solid #f4f4f6;margin:0}.mobile-wrap.store-mobile[data-v-6f865b62]{border-top:%?2?% solid #f4f4f6;margin:%?20?% 0 0 0;padding:%?20?% 0;border-radius:0}.mobile-wrap .form-group[data-v-6f865b62]{display:flex;align-items:center;width:100%}.mobile-wrap .form-group .iconfont[data-v-6f865b62]{margin-right:%?26?%;font-size:%?32?%}.mobile-wrap .form-group .text[data-v-6f865b62]{display:inline-block;line-height:%?50?%;padding-right:%?10?%;font-size:%?28?%;font-weight:700}.mobile-wrap .form-group .placeholder[data-v-6f865b62]{line-height:%?50?%}.mobile-wrap .form-group .input[data-v-6f865b62]{flex:1;height:%?50?%;line-height:%?50?%;text-align:right;font-size:%?28?%}.order-cell[data-v-6f865b62]{display:flex;margin:0 0 %?30?% 0;align-items:center;background:#fff;line-height:%?40?%;position:relative}.order-cell.clear-flex[data-v-6f865b62]{display:block}.order-cell.textarea-box[data-v-6f865b62]{display:flex;align-items:baseline;font-size:%?28?%}.order-cell uni-text[data-v-6f865b62]{font-size:%?28?%}.order-cell .tit[data-v-6f865b62]{text-align:left;font-size:%?28?%;min-width:%?160?%;color:#000;font-weight:700}.order-cell .tit uni-text[data-v-6f865b62]{font-size:%?28?%}.order-cell .tit .tit-content[data-v-6f865b62]{max-width:%?540?%;font-size:%?24?%;line-height:%?35?%;margin-bottom:%?10?%}.order-cell .box[data-v-6f865b62]{flex:1;padding:0 %?10?%;line-height:inherit;text-align:right}.order-cell .box.text-overflow[data-v-6f865b62]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.order-cell .box.text-overflow .money[data-v-6f865b62]{overflow:hidden;max-width:40%;display:inline-block;text-overflow:ellipsis;vertical-align:top}.order-cell .box .icon-right[data-v-6f865b62]{color:#303133;margin-left:%?20?%}.order-cell .box .operator[data-v-6f865b62]{font-size:%?24?%;margin-right:%?6?%;font-weight:700;color:var(--price-color)}.order-cell .box uni-textarea[data-v-6f865b62]{width:auto;height:%?88?%;font-size:%?28?%}.order-cell .iconfont[data-v-6f865b62]{color:#909399;line-height:normal;font-size:%?24?%}.order-cell .unit[data-v-6f865b62]{margin-right:%?4?%;font-weight:700;font-size:%?28?%!important;margin-left:%?4?%;color:var(--price-color)}.order-cell .money[data-v-6f865b62]{font-size:%?28?%!important;font-weight:700;color:var(--price-color)}.site-wrap[data-v-6f865b62]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;padding:%?40?% 0}.site-wrap.order-goods[data-v-6f865b62]{padding:0}.site-wrap .site-body[data-v-6f865b62]{margin:0 %?24?%}.site-wrap .site-body .goods-item[data-v-6f865b62]{border-bottom:%?2?% solid #f4f4f6}.site-wrap .site-body .goods-item[data-v-6f865b62]:last-child{border-bottom:0}.site-wrap .site-body .goods-item .error-tips[data-v-6f865b62]{color:#ff443f;padding:%?10?% %?20?%;display:inline-flex;align-items:center;line-height:1;background:#ffecec;margin-top:%?20?%;border-radius:%?12?%;width:auto}.site-wrap .site-body .goods-item .error-tips .iconfont[data-v-6f865b62]{margin-right:%?10?%}.site-wrap .site-body .goods-wrap[data-v-6f865b62]{display:flex;position:relative;padding:%?30?% 0}.site-wrap .site-body .goods-wrap .goods-img[data-v-6f865b62]{width:%?180?%;height:%?180?%;margin-right:%?20?%;border-radius:%?10?%;overflow:hidden}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-6f865b62]{width:100%;height:100%;border-radius:%?10?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-6f865b62]{flex:1;position:relative;width:0;margin-top:%?-4?%;display:flex;flex-direction:column;justify-content:space-between}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-6f865b62]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;font-weight:700}.site-wrap .site-body .goods-wrap .goods-info .sku[data-v-6f865b62]{display:flex;line-height:1;margin-top:%?8?%}.site-wrap .site-body .goods-wrap .goods-info .sku .goods-spec[data-v-6f865b62]{color:#909399;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1;display:flex}.site-wrap .site-body .goods-wrap .goods-info .sku .goods-spec uni-view[data-v-6f865b62]{background-color:#f4f4f4;color:#666;padding:%?6?% %?10?%;margin-right:%?12?%;line-height:1}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-6f865b62]{font-size:%?24?%;margin-right:%?4?%;font-weight:700;color:var(--price-color)}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-6f865b62]{font-weight:700;font-size:%?32?%;color:var(--price-color)}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-6f865b62]:first-of-type{width:80%;overflow:hidden;text-overflow:ellipsis}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-6f865b62]:last-of-type{text-align:right;position:absolute;right:0;bottom:0;font-weight:700}.site-wrap .site-footer[data-v-6f865b62]{margin:0 %?24?% 0}.site-wrap .site-footer .order-cell[data-v-6f865b62]:last-of-type{margin-bottom:0}[data-v-6f865b62] .goods-form{display:flex;align-items:center;position:relative}[data-v-6f865b62] .goods-form ns-form{display:flex;width:100%}[data-v-6f865b62] .goods-form .shade{position:absolute;left:0;top:0;width:100%;height:100%;z-index:5}[data-v-6f865b62] .goods-form .cell-more{margin-left:%?50?%;float:right;color:#909399;font-size:%?24?%}[data-v-6f865b62] .goods-form .form-wrap{flex:1;width:0}[data-v-6f865b62] .goods-form .form-wrap .icon-right{display:none}[data-v-6f865b62] .goods-form .form-wrap > uni-view,[data-v-6f865b62] .goods-form .form-wrap > uni-picker{display:none}[data-v-6f865b62] .goods-form .form-wrap > uni-view:first-child,[data-v-6f865b62] .goods-form .form-wrap > uni-picker:first-child{display:block;border-bottom:none}[data-v-6f865b62] .goods-form .form-wrap > uni-view:first-child .required,[data-v-6f865b62] .goods-form .form-wrap > uni-picker:first-child .required{display:none}[data-v-6f865b62] .goods-form .order-cell .name{width:auto}[data-v-6f865b62] .goods-form .order-cell .tit{font-weight:700}[data-v-6f865b62] .goods-form .order-cell .tit:after{content:"："}.member-goods-card[data-v-6f865b62]{margin-bottom:0;padding-bottom:%?30?%}.member-goods-card .text[data-v-6f865b62]{margin-right:%?10?%;color:#999}.member-goods-card .price-font[data-v-6f865b62]{color:var(--price-color)}.order-money[data-v-6f865b62]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%}.order-money .order-cell[data-v-6f865b62]:last-child{margin-bottom:0}.error-message[data-v-6f865b62]{position:fixed;z-index:5;left:0;bottom:%?100?%;width:100vw;background:#f6f6cb;text-align:left;padding:%?10?% %?20?%;color:red}.order-submit[data-v-6f865b62]{position:fixed;z-index:5;left:0;bottom:0;width:100vw;height:%?100?%;background:#fff;text-align:right;display:flex;align-items:center;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.order-submit .order-settlement-info[data-v-6f865b62]{flex:1;height:%?100?%;line-height:%?100?%;display:flex;padding-left:%?30?%;align-items:baseline}.order-submit .order-settlement-info .unit[data-v-6f865b62]{font-weight:700;font-size:%?24?%;margin-right:%?4?%;color:var(--price-color)}.order-submit .order-settlement-info .money[data-v-6f865b62]{font-weight:700;font-size:%?32?%;color:var(--price-color)}.order-submit .submit-btn[data-v-6f865b62]{height:%?80?%;margin:0 %?30?%;display:flex;justify-content:center;align-items:center}.order-submit .submit-btn uni-button[data-v-6f865b62]{line-height:%?70?%;width:%?180?%;height:%?70?%;padding:0;font-size:%?28?%;font-weight:700}.order-submit .submit-btn .no-submit[data-v-6f865b62]{width:unset;background-color:#ccc;color:#fff;padding:0 %?20?%;font-size:%?28?%}.order-submit-block[data-v-6f865b62]{height:%?120?%;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.popup[data-v-6f865b62]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-6f865b62]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-6f865b62]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-6f865b62]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-6f865b62]{height:calc(100% - %?250?%)}.popup .popup-body.store-popup[data-v-6f865b62]{height:calc(100% - %?120?%)}.popup .popup-body.safe-area[data-v-6f865b62]{height:calc(100% - %?270?%)}.popup .popup-body.store-popup.safe-area[data-v-6f865b62]{height:calc(100% - %?140?%)}.popup .popup-footer[data-v-6f865b62]{height:%?120?%}.popup .popup-footer .confirm-btn[data-v-6f865b62]{height:%?80?%;line-height:%?80?%;color:#fff;text-align:center;margin:%?20?% %?32?% %?40?%;border-radius:%?10?%;font-size:%?28?%}.popup .popup-footer .confirm-btn.color-base-bg[data-v-6f865b62]{color:var(--btn-text-color)}.popup .popup-footer.bottom-safe-area[data-v-6f865b62]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.invoice-popup[data-v-6f865b62]{height:83vh;padding:%?18?% 0;box-sizing:border-box;position:relative}.invoice-popup .invoice-close[data-v-6f865b62]{position:absolute;line-height:1;top:%?48?%;right:%?48?%;font-size:%?32?%;z-index:9}.invoice-popup .popup-body .invoice-cell[data-v-6f865b62]{padding:%?30?% 0;border-top:%?2?% solid #eee;margin:0 %?48?%}.invoice-popup .popup-body .invoice-cell[data-v-6f865b62]:first-of-type{border-top:none}.invoice-popup .popup-body .invoice-cell .tit[data-v-6f865b62]{font-size:%?28?%}.invoice-popup .popup-body .invoice-cell .option-grpup[data-v-6f865b62]{padding-top:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item[data-v-6f865b62]{height:%?54?%;line-height:%?54?%;display:inline-block;font-size:%?22?%;padding:0 %?36?%;background:#f8f8f8;border:%?2?% solid #eee;border-radius:%?10?%;margin-right:%?30?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.active[data-v-6f865b62]{color:var(--btn-text-color)}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.content[data-v-6f865b62]{margin-bottom:%?20?%}.invoice-popup .popup-body .invoice-cell .option-grpup .option-item.content[data-v-6f865b62]:last-child{margin-bottom:0}.invoice-popup .popup-body .invoice-cell .invoice-form-group uni-input[data-v-6f865b62]{background:#f8f8f8;border-radius:%?10?%;height:%?66?%;margin-top:%?22?%;padding:0 %?32?%;font-size:%?24?%}.invoice-popup .popup-body .invoice-tops[data-v-6f865b62]{font-size:%?20?%;margin:0 %?48?%;color:#909399}.buyermessag-popup[data-v-6f865b62]{box-sizing:border-box;position:relative}.buyermessag-popup .buyermessag-close[data-v-6f865b62]{position:absolute;line-height:1;top:%?48?%;right:%?48?%;font-size:%?32?%;z-index:9}.buyermessag-popup .popup-body .buyermessag-cell[data-v-6f865b62]{padding:%?30?% 0;border-top:%?2?% solid #eee;margin:0 %?32?%}.buyermessag-popup .popup-body .buyermessag-cell[data-v-6f865b62]:first-of-type{border-top:none}.buyermessag-popup .popup-body .buyermessag-cell .buyermessag-form-group uni-textarea[data-v-6f865b62]{display:flex;align-items:baseline;font-size:%?28?%;width:100%;background-color:#f8f8f8;padding:%?20?%;box-sizing:border-box;border-radius:%?10?%}.coupon-popup[data-v-6f865b62]{height:65vh}.coupon-popup .popup-body[data-v-6f865b62]{background:#fff}.coupon-popup .coupon-empty[data-v-6f865b62]{display:flex;align-items:center;justify-content:center;height:100%;color:#909399!important}.coupon-popup .coupon-item[data-v-6f865b62]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;margin:%?20?% %?32?% 0;padding:0;position:relative;background-color:#fff2f0}.coupon-popup .coupon-item[data-v-6f865b62]:before, .coupon-popup .coupon-item[data-v-6f865b62]:after{position:absolute;content:"";background-color:#fff;top:50%;width:%?30?%;height:%?30?%;border-radius:50%;z-index:5}.coupon-popup .coupon-item[data-v-6f865b62]:before{left:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.coupon-popup .coupon-item[data-v-6f865b62]:after{right:0;-webkit-transform:translate(50%,-50%);transform:translate(50%,-50%)}.coupon-popup .coupon-item .coupon-info[data-v-6f865b62]{height:%?190?%;display:flex;width:100%;position:relative}.coupon-popup .coupon-item .coupon-info .info-wrap[data-v-6f865b62]{width:%?220?%;height:%?190?%;display:flex;justify-content:center;align-items:center;margin-right:%?20?%;background-repeat:no-repeat;background-size:100% 100%;position:relative;background:linear-gradient(270deg,var(--bg-color),var(--bg-color-shallow))}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-line[data-v-6f865b62]{position:absolute;right:0;top:0;height:100%}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money[data-v-6f865b62]{color:#fff;text-align:center;line-height:1}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money .unit[data-v-6f865b62]{font-size:%?30?%}.coupon-popup .coupon-item .coupon-info .info-wrap .coupon-money .money[data-v-6f865b62]{font-size:%?60?%}.coupon-popup .coupon-item .coupon-info .info-wrap .at-least[data-v-6f865b62]{font-size:%?24?%;color:#fff;text-align:center;margin-top:%?20?%}.coupon-popup .coupon-item .coupon-info .desc-wrap[data-v-6f865b62]{flex:1;max-width:calc(100% - %?360?%)}.coupon-popup .coupon-item .coupon-info .desc-wrap uni-view[data-v-6f865b62]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.coupon-popup .coupon-item .coupon-info .desc-wrap .coupon-name[data-v-6f865b62]{margin-top:%?10?%;margin-bottom:%?4?%;font-size:%?28?%}.coupon-popup .coupon-item .coupon-info .desc-wrap .limit[data-v-6f865b62]{font-size:%?20?%}.coupon-popup .coupon-item .coupon-info .desc-wrap .time[data-v-6f865b62]{border-top:%?2?% dashed #ccc;position:absolute;bottom:%?30?%;color:#909399;padding-top:%?10?%;line-height:1.5;font-size:%?20?%}.coupon-popup .coupon-item .coupon-info .iconfont[data-v-6f865b62]{font-size:%?44?%;position:absolute;top:50%;right:%?20?%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.coupon-popup .coupon-item .coupon-info .icon-yuan_checkbox[data-v-6f865b62]{color:#909399}.promotion-popup[data-v-6f865b62]{height:40vh}.promotion-popup .order-cell[data-v-6f865b62]{margin:%?30?% %?30?%}.promotion-popup .order-cell .tit[data-v-6f865b62]{width:auto;min-width:unset}.promotion-popup .order-cell .promotion-mark[data-v-6f865b62]{padding:%?4?% %?10?%;line-height:1;border-radius:%?10?%;font-size:%?24?%;margin-right:%?10?%;color:var(--main-color);background-color:var(--main-color-shallow)}.delivery-popup[data-v-6f865b62]{height:80vh;box-sizing:border-box}.delivery-popup .delivery-content[data-v-6f865b62]{height:100%;overflow-y:scroll;padding:%?30?% 0;box-sizing:border-box}.delivery-popup .delivery-content .item-wrap[data-v-6f865b62]{padding:%?20?% 0;box-sizing:border-box;border-top:%?2?% solid #eee;display:flex;justify-content:space-between;align-items:center;margin:0 %?48?%}.delivery-popup .delivery-content .item-wrap .detail[data-v-6f865b62]{width:90%}.delivery-popup .delivery-content .item-wrap .detail .name[data-v-6f865b62]{display:flex}.delivery-popup .delivery-content .item-wrap .detail .name uni-text[data-v-6f865b62]{font-size:%?28?%}.delivery-popup .delivery-content .item-wrap .detail .info[data-v-6f865b62]{line-height:1.2}.delivery-popup .delivery-content .item-wrap .detail .info uni-view[data-v-6f865b62]{font-size:%?24?%}.delivery-popup .delivery-content .item-wrap .detail .info .close-desc[data-v-6f865b62]{color:red}.delivery-popup .delivery-content .item-wrap .icon[data-v-6f865b62]{flex:1;text-align:right;max-height:%?50?%}.delivery-popup .delivery-content .item-wrap .icon .iconfont[data-v-6f865b62]{line-height:1;font-size:%?44?%}.delivery-popup .delivery-content .item-wrap[data-v-6f865b62]:first-of-type{padding-top:0;border-top:none}.delivery-popup .delivery-content .empty[data-v-6f865b62]{text-align:center;font-size:%?24?%}.balance-switch[data-v-6f865b62]{-webkit-transform:scale(.8);transform:scale(.8)}.address-box[data-v-6f865b62]{margin:0 %?24?% 0;background-color:#fff;position:relative;overflow:hidden;border-bottom-left-radius:%?16?%;border-bottom-right-radius:%?16?%;padding:%?30?% %?24?%}.address-box.not-delivery-type[data-v-6f865b62]{border-radius:%?16?%}.address-box .address-line[data-v-6f865b62]{position:absolute;bottom:%?0?%;left:0;width:100%;height:%?6?%}.address-box .info-wrap[data-v-6f865b62]{display:flex;align-items:center}.address-box .info-wrap.local[data-v-6f865b62]{padding-bottom:%?20?%}.address-box .info-wrap .content[data-v-6f865b62]{flex:1}.address-box .info-wrap .content .name[data-v-6f865b62]{margin-right:%?10?%;font-weight:700;font-size:%?28?%}.address-box .info-wrap .content .mobile[data-v-6f865b62]{font-weight:700;font-size:%?28?%}.address-box .info-wrap .desc-wrap[data-v-6f865b62]{word-break:break-word;font-size:%?26?%;color:#666}.address-box .icon-wrap[data-v-6f865b62]{width:%?24?%;height:%?42?%;position:relative;margin-right:%?26?%;align-self:flex-start;padding-top:%?6?%}.address-box .icon-wrap.empty[data-v-6f865b62]{padding-top:0}.address-box .icon-wrap .iconfont[data-v-6f865b62]{font-size:%?32?%;display:inline-block;vertical-align:middle}.address-box .empty-wrap[data-v-6f865b62]{height:%?80?%;line-height:%?80?%;display:flex;align-items:center}.address-box .empty-wrap .info[data-v-6f865b62]{flex:1;font-size:%?28?%}.address-box .cell-more[data-v-6f865b62]{margin-left:%?50?%;float:right;color:#909399;font-size:%?24?%}.address-box .cell-more .iconfont[data-v-6f865b62]{color:#909399}.address-box .local-delivery-store[data-v-6f865b62]{display:flex;align-items:center;padding-bottom:%?20?%;margin-bottom:%?20?%;border-bottom:%?2?% solid #eee}.address-box .local-delivery-store .info[data-v-6f865b62]{flex:1;width:0;font-size:%?28?%}.address-box .local-delivery-store .store-name[data-v-6f865b62]{color:var(--base-color);margin:0 %?10?%}.address-box .local-delivery-store .cell-more[data-v-6f865b62]{font-size:%?28?%;display:flex;align-items:center}.address-box .local-delivery-store .icon-right[data-v-6f865b62]{float:right;color:#909399;font-size:%?24?%}.local-box[data-v-6f865b62]{border-top:%?2?% solid #eee}.local-box .order-cell[data-v-6f865b62]{padding-top:%?30?%;margin-bottom:0}.local-box .order-cell .box[data-v-6f865b62]{padding:0}.local-box .pick-block[data-v-6f865b62]{padding-top:%?20?%;display:flex;align-items:center}.local-box .pick-block > uni-view[data-v-6f865b62]{flex:1}.local-box .pick-block .title[data-v-6f865b62]{font-weight:700}.local-box .pick-block .time-picker[data-v-6f865b62]{display:flex;align-items:center;justify-content:flex-end}.local-box .pick-block .time-picker .cell-more[data-v-6f865b62]{float:right;margin-left:%?10?%;color:#909399;font-size:%?24?%}.local-box .pick-block .time-picker .cell-more .iconfont[data-v-6f865b62]{color:#909399}.local-box .pick-block .time-picker uni-text[data-v-6f865b62]{white-space:nowrap}.empty-local[data-v-6f865b62]{color:#ff443f}.delivery-mode[data-v-6f865b62]{margin:0 %?24?%;overflow:hidden;border-top-left-radius:%?16?%;border-top-right-radius:%?16?%;background-color:var(--base-color)}.delivery-mode .action[data-v-6f865b62]{display:flex;background:var(--base-color-light-7)}.delivery-mode .action > uni-view[data-v-6f865b62]{flex:1;text-align:center;height:%?76?%;line-height:%?76?%;font-size:%?30?%;color:#000;position:relative}.delivery-mode .action > uni-view:nth-child(2).active[data-v-6f865b62], .delivery-mode .action > uni-view:nth-child(3).active[data-v-6f865b62]{border-top-left-radius:%?16?%}.delivery-mode .action > uni-view .out-radio[data-v-6f865b62]:after, .delivery-mode .action > uni-view .out-radio[data-v-6f865b62]:before{position:absolute;content:"";width:%?20?%;height:%?20?%;background-color:#fff;bottom:0;display:none}.delivery-mode .action > uni-view .out-radio[data-v-6f865b62]:after{-webkit-transform:translateX(100%);transform:translateX(100%);right:0}.delivery-mode .action > uni-view .out-radio[data-v-6f865b62]:before{left:0;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.delivery-mode .action .active[data-v-6f865b62]{background:#fff;color:var(--base-color);border-top-right-radius:%?16?%}.delivery-mode .action .active[data-v-6f865b62]:after, .delivery-mode .action .active[data-v-6f865b62]:before{position:absolute;content:"";width:%?40?%;height:%?40?%;background-color:var(--base-color-light-7);bottom:0;-webkit-transform:translateX(100%);transform:translateX(100%);border-radius:50%;z-index:5}.delivery-mode .action .active[data-v-6f865b62]:after{-webkit-transform:translateX(100%);transform:translateX(100%);right:0}.delivery-mode .action .active[data-v-6f865b62]:before{left:0;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.delivery-mode .action .active .out-radio[data-v-6f865b62]:after, .delivery-mode .action .active .out-radio[data-v-6f865b62]:before{display:block}.store-box[data-v-6f865b62]{position:relative;padding:%?30?% %?24?%;margin:0 %?24?% 0;background-color:#fff;border-bottom-left-radius:%?16?%;border-bottom-right-radius:%?16?%;overflow:hidden}.store-box.not-delivery-type[data-v-6f865b62]{border-radius:%?16?%}.store-box .address-line[data-v-6f865b62]{position:absolute;bottom:0;left:0;width:100%;height:%?6?%}.store-box .store-info[data-v-6f865b62]{display:flex;align-items:baseline}.store-box .store-info .icon[data-v-6f865b62]{position:relative;margin-right:%?12?%;align-self:flex-start;margin-top:%?-2?%}.store-box .store-info .icon.img[data-v-6f865b62]{background-color:unset;margin-right:%?8?%;width:%?46?%;height:%?46?%;border-radius:50%;margin-top:%?12?%}.store-box .store-info .icon.img uni-image[data-v-6f865b62]{width:100%;height:100%}.store-box .store-info .icon .iconfont[data-v-6f865b62]{font-size:%?32?%}.store-box .store-info .store-address-info[data-v-6f865b62]{width:100%;display:flex;align-items:center}.store-box .store-info .store-address-info .info-wrap[data-v-6f865b62]{flex:1;width:0}.store-box .store-info .store-address-info .info-wrap .title[data-v-6f865b62]{margin-bottom:%?10?%;font-size:%?28?%;font-weight:700}.store-box .store-info .store-address-info .info-wrap .title .cell-more[data-v-6f865b62]{float:right;margin-left:%?50?%;color:#909399;font-size:%?24?%;font-weight:500}.store-box .store-info .store-address-info .info-wrap .store-detail uni-view[data-v-6f865b62]{word-break:break-word;font-size:%?26?%}.store-box .store-info .store-address-info .info-wrap .store-detail .close-desc[data-v-6f865b62]{color:red}.store-box .store-info .store-address-info .info-wrap .store-detail .address[data-v-6f865b62]{color:#606266;width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis}.store-box .store-info .store-address-info .cell-more[data-v-6f865b62]{color:#909399}.store-box .empty[data-v-6f865b62]{text-align:center}.store-box .store-time[data-v-6f865b62]{border-top:%?2?% solid #f4f4f6;display:flex;align-items:center;justify-content:space-between;padding:%?20?% 0 0;box-sizing:border-box}.store-box .store-time uni-view[data-v-6f865b62]{font-size:%?28?%}.store-box .store-time .left[data-v-6f865b62]{font-weight:700}.store-box .store-time .right[data-v-6f865b62]{display:flex;align-items:center;line-height:1;font-size:%?24?%}.store-box .store-time .right .iconfont[data-v-6f865b62]{font-size:%?24?%;margin-left:%?14?%;color:#909399}.buyer-message[data-v-6f865b62]{padding:%?30?% %?24?%}.buyer-message .order-cell[data-v-6f865b62]{margin-bottom:0}.member-card-wrap[data-v-6f865b62]{background-color:#fffbf4;padding:0 %?30?%!important}.member-card-wrap .head[data-v-6f865b62]{display:flex;align-items:center;height:%?80?%}.member-card-wrap .icon-yuan_checked[data-v-6f865b62], .member-card-wrap .icon-yuan_checkbox[data-v-6f865b62]{font-size:%?32?%}.member-card-wrap .icon-huiyuan[data-v-6f865b62]{margin-right:%?10?%;line-height:1;font-size:%?36?%;background-image:linear-gradient(156deg,#814635,#3a221b);-webkit-background-clip:text;-webkit-text-fill-color:transparent}.member-card-wrap .info[data-v-6f865b62]{text-align:left;flex:1;color:#e5ce75;font-size:%?24?%;color:#333}.member-card-wrap .body[data-v-6f865b62]{display:flex;overflow-x:scroll;padding:%?10?% 0 %?20?% 0}.member-card-wrap .body .item[data-v-6f865b62]{padding:%?20?% 0 %?30?% 0;width:calc((100% - %?60?%) / 4);text-align:center;background:#fff;margin-right:%?20?%;border:%?4?% solid #fff;border-radius:%?10?%;position:relative;overflow:hidden}.member-card-wrap .body .item .icon-icon[data-v-6f865b62]{position:absolute;right:0;bottom:0;font-size:%?32?%;display:none;line-height:1}.member-card-wrap .body .item[data-v-6f865b62]:last-child{margin-right:0}.member-card-wrap .body .item .title[data-v-6f865b62]{margin-top:%?20?%;font-weight:700}.member-card-wrap .body .item .price[data-v-6f865b62]{margin-top:%?10?%}.member-card-wrap .body .active .icon-icon[data-v-6f865b62]{display:block}.system-form-wrap[data-v-6f865b62]{margin:%?20?% %?24?% 0;background:#fff;padding:%?30?% %?24?%;border-radius:%?16?%;padding:0;overflow:hidden}.system-form-wrap .order-cell[data-v-6f865b62]{padding:%?30?% %?24?%;margin-bottom:0;border-bottom:%?2?% solid #f4f4f6}.system-form-wrap[data-v-6f865b62] .form-wrap{margin:0 %?24?%}.system-form-wrap[data-v-6f865b62] .form-wrap .icon-right{color:#909399;font-size:%?24?%}.agreement[data-v-6f865b62]{margin:%?20?% %?24?% 0}.agreement uni-text[data-v-6f865b62]{color:var(--base-color)}.agreement-conten-box[data-v-6f865b62]{background:#fff;padding:%?30?% %?30?%}.agreement-conten-box .title[data-v-6f865b62]{text-align:center;margin-bottom:%?20?%;font-weight:bolder}.agreement-conten-box .close[data-v-6f865b62]{position:absolute;right:%?30?%;top:%?10?%}.agreement-conten-box .con[data-v-6f865b62]{height:60vh}.icon[data-v-6f865b62]{line-height:1;margin-right:%?14?%;max-height:%?50?%}.icon uni-image[data-v-6f865b62]{width:%?38?%;margin:%?-6?% auto;max-height:%?50?%}.form-popup[data-v-6f865b62]{height:60vh!important}.form-popup .popup-body[data-v-6f865b62]{padding:%?20?% %?30?%;box-sizing:border-box}.member-card-popup[data-v-6f865b62]{height:60vh}.member-card-popup .popup-body .card-item[data-v-6f865b62]{display:flex;padding:%?30?%;background:var(--base-color-light-9);margin:%?24?% %?20?%;border-radius:%?18?%}.member-card-popup .popup-body .card-item .content[data-v-6f865b62]{flex:1;width:0;margin-right:%?30?%}.member-card-popup .popup-body .card-item .content .title[data-v-6f865b62]{line-height:%?40?%;font-size:%?28?%;font-weight:600}.member-card-popup .popup-body .card-item .content .info uni-text[data-v-6f865b62]{line-height:1;font-size:%?24?%;color:#666;margin-top:%?20?%;margin-right:%?8?%;display:inline-block}.member-card-popup .popup-body .card-item .iconfont[data-v-6f865b62]{font-size:%?44?%}.member-card-popup .popup-body .card-item .icon-yuan_checkbox[data-v-6f865b62]{color:#909399}.order-cell .promotion-content[data-v-6f865b62]{flex:1}',""]),e.exports=t},b42f:function(e,t,a){var i=a("f7d5");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("599afa74",i,!0,{sourceMap:!1,shadowMode:!1})},c0030:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(a("39d8")),s=i(a("9b1b")),r=i(a("fcf3"));a("bf0f"),a("2797"),a("e838"),a("dc8a"),a("aa9c"),a("5ef2"),a("fd3c"),a("d4b5"),a("5c47"),a("0506"),a("473f"),a("c223");i(a("0817"));var n={options:{styleIsolation:"shared"},data:function(){return{outTradeNo:"",isIphoneX:!1,orderCreateData:{is_balance:0,is_point:1,is_invoice:0,invoice_type:0,invoice_title_type:1,is_tax_invoice:0,coupon:{coupon_id:0},delivery:{},member_goods_card:{},order_key:"",buyer_message:""},paymentData:null,calculateData:null,tempData:null,storeId:0,deliveryTime:"",memberAddress:null,localMemberAddress:null,isRepeat:!1,promotionInfo:null,transactionAgreement:{},tempFormData:null,menuButtonBounding:{},storeConfig:null,localConfig:null,selectGoodsCard:{skuId:0,itemId:0,cardList:{}},storeData:[],latitude:"",longitude:"",coupon_list:[],modules:[]}},inject:["promotion"],created:function(){var e=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?(Object.assign(this.orderCreateData,uni.getStorageSync(this.createDataKey)),this.location&&(this.orderCreateData.latitude=this.location.latitude,this.orderCreateData.longitude=this.location.longitude,this.latitude=this.location.latitude,this.longitude=this.location.longitude),this.payment()):this.$nextTick((function(){e.$refs.loadingCover.hide(),e.$refs.login.open(e.$util.getCurrentRoute().path)})),this.getTransactionAgreement()},computed:{goodsData:function(){if(this.paymentData)return this.paymentData.goods_list.forEach((function(e){e.sku_spec_format&&"string"==typeof e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format))})),this.paymentData},calculateGoodsData:function(){if(this.calculateData)return this.calculateData.goods_list.forEach((function(e){e.sku_spec_format&&"string"==typeof e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format))})),this.calculateData},balanceDeduct:function(){if(this.calculateData)return this.calculateData.member_account&&this.calculateData.member_account.balance_total<=parseFloat(this.calculateData.order_money).toFixed(2)?parseFloat(this.calculateData.member_account.balance_total).toFixed(2):parseFloat(this.calculateData.order_money).toFixed(2)},storeList:function(){return this.getStoreList()},storeInfo:function(){var e=this.getStoreList();return e&&this.orderCreateData.delivery&&"express"!=this.orderCreateData.delivery.delivery_type&&this.storeId?e[this.orderCreateData.delivery.store_id]:null},cardChargeType:function(){if(this.paymentData&&this.paymentData.recommend_member_card&&Object.keys(this.paymentData.recommend_member_card).length>0){var e=[],t=this.paymentData.recommend_member_card.charge_rule;return Object.keys(t).forEach((function(a,i){switch(a){case"week":e.push({key:a,value:t[a],title:"周卡",unit:"周"});break;case"month":e.push({key:a,value:t[a],title:"月卡",unit:"月"});break;case"quarter":e.push({key:a,value:t[a],title:"季卡",unit:"季"});break;case"year":e.push({key:a,value:t[a],title:"年卡",unit:"年"});break}})),e}}},watch:{storeToken:function(e,t){this.payment()},deliveryTime:function(e){e||this.$refs.timePopup.refresh()},location:function(e){e&&(this.orderCreateData.latitude=e.latitude,this.orderCreateData.longitude=e.longitude,this.latitude=e.latitude,this.longitude=e.longitude,this.payment())},calculateGoodsData:function(e){e&&e.config.local&&e.delivery.local.info.time_is_open&&!this.deliveryTime&&this.localtime("no")}},methods:{pageShow:function(){uni.getStorageSync("addressBack")&&(uni.removeStorageSync("addressBack"),this.payment()),this.$refs.choosePaymentPopup&&this.$refs.choosePaymentPopup.pageShow()},payment:function(){var e=this,t=this.handleCreateData();this.$api.sendRequest({url:this.api.payment,data:t,success:function(a){if(0==a.code&&a.data){var i=a.data;if(i&&i.delivery.express_type&&i.delivery.express_type.length){var o=uni.getStorageSync("delivery"),s=i.delivery.express_type[0];i.delivery.express_type.forEach((function(t){o&&t.name==o.delivery_type&&(s=t),"local"==t.name&&(e.localConfig=t),"store"==t.name&&(e.storeConfig=t)})),e.selectDeliveryType(s,!1,i.member_account)}if(i.is_virtual&&(e.orderCreateData.member_address={mobile:i.member_account.mobile?i.member_account.mobile:""}),e.orderCreateData.order_key=i.order_key,e.modules=i.modules,i=e.handleGoodsFormData(i),e.promotionInfo=e.promotion(i),e.paymentData=i,e.$refs.form){console.log(e.paymentData.system_form.json_data,JSON.parse(t.form_data).form_data);var r=JSON.parse(t.form_data).form_data,n=e.$util.deepClone(e.paymentData.system_form.json_data);r.forEach((function(e){n.forEach((function(t){e.id===t.id&&(t.value.default=e.val)}))})),e.paymentData.system_form.json_data=n}e.$forceUpdate(),e.getCouponList((function(){e.calculate()}))}else e.$util.showToast({title:a.message}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1e3)}})},getCouponList:function(e){var t=this;if(-1!=this.modules.indexOf("coupon")){var a=this.handleCreateData();this.orderCreateData.coupon.coupon_id=0,this.$api.sendRequest({url:"/api/ordercreate/getcouponlist",data:a,success:function(a){if(0==a.code&&a.data){var i=a.data;t.coupon_list=i,t.coupon_list.length>0&&(t.orderCreateData.coupon.coupon_id=t.coupon_list[0].coupon_id)}else t.$util.showToast({title:a.message});"function"==typeof e&&e()}})}else"function"==typeof e&&e()},handleGoodsFormData:function(e){var t=uni.getStorageSync("goodFormData");return this.$refs.goodsForm?e.goods_list=this.$util.deepClone(this.paymentData.goods_list):e.goods_list.forEach((function(e){if(e.goods_form){var a={};e.form_data?e.form_data.map((function(e){a[e.id]=e})):t&&t.goods_id==e.goods_id&&t.form_data.map((function(e){a[e.id]=e})),Object.keys(a).length&&e.goods_form.json_data.forEach((function(e){a[e.id]&&(e.val=a[e.id].val)}))}})),e},calculate:function(){var e=this;this.$api.sendRequest({url:this.api.calculate,data:this.handleCreateData(),success:function(t){e.$refs.loadingCover&&e.$refs.loadingCover.isShow&&e.$refs.loadingCover.hide(),0==t.code&&t.data?(e.calculateData=e.handleGoodsFormData(t.data),e.calculateData.coupon_list=e.coupon_list,t.data.delivery&&("express"==t.data.delivery.delivery_type&&(e.memberAddress=t.data.delivery.member_address),"local"==t.data.delivery.delivery_type&&(e.localMemberAddress=t.data.delivery.member_address)),t.data.goods_list.forEach((function(t){if(t.member_card_list){if(e.orderCreateData.member_goods_card[t.sku_id]){var a=e.orderCreateData.member_goods_card[t.sku_id];t.member_card_list[a]||delete e.orderCreateData.member_goods_card[t.sku_id]}}else e.orderCreateData.member_goods_card[t.sku_id]&&delete e.orderCreateData.member_goods_card[t.sku_id]})),t.data.coupon_id?e.orderCreateData.coupon.coupon_id=t.data.coupon_id:e.orderCreateData.coupon.coupon_id=0,e.$forceUpdate()):e.$util.showToast({title:t.message})}})},create:function(){var e=this;this.verify()&&!this.isRepeat&&(this.isRepeat=!0,uni.showLoading({title:""}),this.$api.sendRequest({url:this.api.create,data:this.handleCreateData(),success:function(t){uni.hideLoading(),0==t.code?(e.outTradeNo=t.data,uni.removeStorageSync("deliveryTime"),uni.removeStorageSync("goodFormData"),uni.setStorageSync("paySource",""),0==e.calculateData.pay_money?e.$util.redirectTo("/pages_tool/pay/result",{code:t.data},"redirectTo"):e.openChoosePayment(),e.$store.dispatch("getCartNumber")):(e.$util.showToast({title:t.message}),e.isRepeat=!1)}}))},handleCreateData:function(){var e=this,t=this.$util.deepClone(this.orderCreateData);return this.$refs.form&&(t.form_data={form_id:this.paymentData.system_form.id,form_data:this.$util.deepClone(this.$refs.form.formData)}),this.$refs.goodsForm&&(t.form_data||(t.form_data={}),t.form_data.goods_form={},this.$refs.goodsForm.forEach((function(a){t.form_data.goods_form[a._props.customAttr.sku_id]={form_id:a._props.customAttr.form_id,form_data:e.$util.deepClone(a.formData)}}))),Object.keys(t).forEach((function(e){var a=t[e];"object"==(0,r.default)(a)&&(t[e]=JSON.stringify(a))})),this.paymentData&&0==this.orderCreateData.is_virtual&&t.member_address&&this.orderCreateData.delivery&&"store"!=this.orderCreateData.delivery.delivery_type&&delete t.member_address,t},openChoosePayment:function(){this.$refs.choosePaymentPopup.getPayInfo(this.outTradeNo)},verify:function(){if(1==this.paymentData.is_virtual){if(!this.orderCreateData.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号"}),!1}else{if(!this.orderCreateData.delivery||!this.orderCreateData.delivery.delivery_type)return this.$util.showToast({title:"商家未设置配送方式"}),!1;if("express"==this.orderCreateData.delivery.delivery_type&&!this.memberAddress||"local"==this.orderCreateData.delivery.delivery_type&&!this.localMemberAddress)return this.$util.showToast({title:"请先选择您的收货地址"}),!1;if("store"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可提货的门店,请选择其他配送方式"}),!1;if(!this.orderCreateData.member_address.mobile)return this.$util.showToast({title:"请输入预留手机"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$util.showToast({title:"请输入正确的手机号"}),!1;if(!this.deliveryTime)return this.$util.showToast({title:"请选择提货时间"}),!1}if("local"==this.orderCreateData.delivery.delivery_type){if(!this.orderCreateData.delivery.store_id)return this.$util.showToast({title:"没有可配送的门店,请选择其他配送方式"}),!1;if(this.calculateGoodsData.config.local.is_use&&1==this.calculateGoodsData.delivery.local.info.time_is_open&&!this.deliveryTime)return this.$util.showToast({title:"请选择送达时间"}),!1}}if(this.$refs.goodsForm){for(var e=!0,t=0;t<this.$refs.goodsForm.length;t++){var a=this.$refs.goodsForm[t];if(e=a.verify(),!e)break}if(!e)return!1}if(this.paymentData.system_form){var i=this.$refs.form.verify();if(!i)return!1}return!0},selectAddress:function(){var e={back:this.$util.getCurrentRoute().path,local:0,type:1};"local"==this.orderCreateData.delivery.delivery_type&&(e.local=1,e.type=2),this.$util.redirectTo("/pages_tool/member/address",e)},selectDeliveryType:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!this.orderCreateData.delivery||this.orderCreateData.delivery.delivery_type!=e.name){this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.deliveryTime="";var i={delivery_type:e.name,delivery_type_name:e.title};"store"!=e.name&&"local"!=e.name||(e.store_list[0]&&(i.store_id=e.store_list[0].store_id),this.storeId=i.store_id?i.store_id:0,this.orderCreateData.member_address||(this.paymentData?this.orderCreateData.member_address={name:this.paymentData.member_account.nickname,mobile:this.paymentData.member_account.mobile}:a&&(this.orderCreateData.member_address={name:a.nickname,mobile:a.mobile}))),this.$set(this.orderCreateData,"delivery",i),uni.setStorageSync("delivery",i),"express"==this.orderCreateData.delivery.delivery_type||this.location||this.$util.getLocation(),t&&this.payment(),"store"==e.name&&this.storetime("no"),"local"==e.name&&this.localtime("no")}},imageError:function(e){this.paymentData.goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.calculateData.goods_list[e].sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},selectPickupPoint:function(e){if(e.store_id!=this.storeId){this.storeId=e.store_id,this.orderCreateData.delivery.store_id=e.store_id,this.payment(),this.resetDeliveryTime();var t=uni.getStorageSync("delivery");t.store_id=e.store_id,uni.setStorageSync("delivery",t)}this.$refs.deliveryPopup.close()},resetDeliveryTime:function(){this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:"",end_date:""},this.deliveryTime="",uni.removeStorageSync("deliveryTime")},storetime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.storeInfo){var t=this.$util.deepClone(this.storeInfo);t.delivery_time="string"==typeof t.delivery_time&&t.delivery_time?JSON.parse(t.delivery_time):t.delivery_time,t.delivery_time&&(void 0!=t.delivery_time.length||t.delivery_time.length)||(t.delivery_time=[{start_time:t.start_time,end_time:t.end_time}]);var a={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(a,e),this.$forceUpdate()}},selectPickupTime:function(e){this.deliveryTime=e.data.month+"("+e.data.time+")",this.orderCreateData.delivery.buyer_ask_delivery_time={start_date:e.data.start_date,end_date:e.data.end_date},uni.setStorageSync("deliveryTime",{deliveryTime:this.deliveryTime,buyer_ask_delivery_time:this.orderCreateData.delivery.buyer_ask_delivery_time,delivery_type:this.orderCreateData.delivery.delivery_type})},storeImgError:function(){this.storeInfo.store_image=this.$util.getDefaultImage().store},openPopup:function(e){"deliveryPopup"==e&&(!this.storeList||Object.keys(this.storeList).length<=1)||(this.tempData=this.$util.deepClone(this.orderCreateData),this.$refs[e].open())},closePopup:function(e){this.orderCreateData=this.$util.deepClone(this.tempData),this.$refs[e].close(),this.tempData=null},changeIsInvoice:function(){0==this.orderCreateData.is_invoice?(this.orderCreateData.is_invoice=1,this.orderCreateData.invoice_type||(this.orderCreateData.invoice_type=this.goodsData.invoice.invoice_type.split(",")[0])):this.orderCreateData.is_invoice=0},changeInvoiceType:function(e){this.orderCreateData.invoice_type=e},changeInvoiceTitleType:function(e){this.orderCreateData.invoice_title_type=e},changeIsTaxInvoice:function(){0==this.orderCreateData.is_tax_invoice?this.orderCreateData.is_tax_invoice=1:this.orderCreateData.is_tax_invoice=0,this.$forceUpdate()},changeInvoiceContent:function(e){this.orderCreateData.invoice_content=e,this.$forceUpdate()},invoiceVerify:function(){if(!this.orderCreateData.invoice_title)return this.$util.showToast({title:"请填写发票抬头"}),!1;if(!this.orderCreateData.taxpayer_number&&2==this.orderCreateData.invoice_title_type)return this.$util.showToast({title:"请填写纳税人识别号"}),!1;if(1==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_full_address&&1==this.paymentData.is_virtual)return this.$util.showToast({title:"请填写发票邮寄地址"}),!1;if(2==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_email)return this.$util.showToast({title:"请填写邮箱"}),!1;if(2==this.orderCreateData.invoice_type){if(!/^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/.test(this.orderCreateData.invoice_email))return this.$util.showToast({title:"请填写正确的邮箱"}),!1}return!!this.orderCreateData.invoice_content||(this.$util.showToast({title:"请选择发票内容"}),!1)},saveInvoice:function(){(1!=this.orderCreateData.is_invoice||this.invoiceVerify())&&(this.calculate(),this.$refs.invoicePopup.close())},saveBuyerMessage:function(){this.$refs.buyerMessagePopup.close()},selectMemberCard:function(){this.orderCreateData.is_open_card=this.orderCreateData.is_open_card?0:1,this.orderCreateData.member_card_unit||(this.orderCreateData.member_card_unit=this.cardChargeType[0].key),this.payment()},selectMemberCardUnit:function(e){this.orderCreateData.member_card_unit=e,this.calculate()},usePoint:function(){this.orderCreateData.is_point=this.orderCreateData.is_point?0:1,this.calculate()},payClose:function(){this.$store.dispatch("getCartNumber"),this.$util.redirectTo("/pages/order/detail",{order_id:this.$refs.choosePaymentPopup.payInfo.order_id},"redirectTo")},selectCoupon:function(e){this.orderCreateData.coupon.coupon_id==e.coupon_id?this.orderCreateData.coupon={coupon_id:0}:this.orderCreateData.coupon={coupon_id:e.coupon_id}},useCoupon:function(){this.$refs.couponPopup.close(),this.calculate()},localtime:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.calculateGoodsData&&this.calculateGoodsData.config.local){var t=this.$util.deepClone(this.calculateGoodsData.delivery.local.info);if(Object.keys(t).length){t.delivery_time&&(t.end_time=t.delivery_time[t.delivery_time.length-1].end_time);var a={delivery:this.orderCreateData.delivery,dataTime:t};this.$refs.timePopup.open(a,e)}}},surplusStartMoney:function(){var e=0;if(this.calculateData&&this.calculateData.delivery&&"local"==this.calculateData.delivery.delivery_type){var t,a=null!==(t=this.calculateGoodsData.delivery.start_money)&&void 0!==t?t:0;e=parseFloat(a)-parseFloat(this.calculateData.goods_money),e=e<0?0:e}return e},getTransactionAgreement:function(){var e=this;this.$api.sendRequest({url:"/api/order/transactionagreement",success:function(t){t.data&&(e.transactionAgreement=t.data)}})},editForm:function(e){this.tempFormData={index:e,json_data:this.$util.deepClone(this.goodsData.goods_list[e].goods_form.json_data)},this.$refs.editFormPopup.open()},saveForm:function(){this.$refs.tempForm.verify()&&(this.$set(this.paymentData.goods_list[this.tempFormData.index].goods_form,"json_data",this.$refs.tempForm.formData),this.$refs.editFormPopup.close())},selectMemberGoodsCard:function(e){var t=this,a=this.goodsData.goods_list[e].sku_id;this.selectGoodsCard={skuId:a,itemId:this.orderCreateData.member_goods_card[a]?this.orderCreateData.member_goods_card[a]:0,cardList:this.$util.deepClone(this.calculateGoodsData.goods_list[e].member_card_list),click:function(e){t.selectGoodsCard.itemId=t.selectGoodsCard.itemId==e?0:e}},this.$refs.memberGoodsCardPopup.open()},saveMemberGoodsCard:function(){this.orderCreateData.member_goods_card[this.selectGoodsCard.skuId]=this.selectGoodsCard.itemId||0,this.$refs.memberGoodsCardPopup.close(),this.payment()},back:function(){uni.navigateBack({delta:1})},getStoreList:function(){var e=null;return this.orderCreateData.delivery&&("local"==this.orderCreateData.delivery.delivery_type&&this.localConfig&&(e=this.localConfig.store_list,e=e.reduce((function(e,t){return(0,s.default)((0,s.default)({},e),{},(0,o.default)({},t.store_id,t))}),{})),"store"==this.orderCreateData.delivery.delivery_type&&this.storeConfig&&(e=this.storeConfig.store_list,e=e.reduce((function(e,t){return(0,s.default)((0,s.default)({},e),{},(0,o.default)({},t.store_id,t))}),{}))),e},getStore:function(e){var t,a,i=this;this.$api.sendRequest({url:"/api/store/getStorePage",data:{page_size:e.size,page:e.num,latitude:null!==(t=this.latitude)&&void 0!==t?t:"",longitude:null!==(a=this.longitude)&&void 0!==a?a:"",type:this.orderCreateData.delivery.delivery_type,store_ids:this.paymentData.available_store_ids},success:function(t){var a=[];t.message;0==t.code&&t.data&&(a=t.data.list),e.endSuccess(a.length),1==e.num&&(i.storeData=[]),i.storeData=i.storeData.concat(a)},fail:function(t){e.endErr()}})}},filters:{moneyFormat:function(e){return parseFloat(e).toFixed(2)}}};t.default=n},c4af:function(e,t,a){"use strict";a.r(t);var i=a("3672"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(s);t["default"]=o.a},d68f:function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return i}));var i={uniPopup:a("d745").default},o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"ns-time"},[a("uni-popup",{ref:"selectTime",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"box"},[a("v-uni-view",{staticClass:"title"},[e.obj.delivery&&"local"==e.obj.delivery.delivery_type?[e._v("选择送达时间")]:e._e(),e.obj.delivery&&"store"==e.obj.delivery.delivery_type?[e._v("选择自提时间")]:e._e(),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.close.apply(void 0,arguments)}}})],2),a("v-uni-view",{staticClass:"body"},[a("v-uni-scroll-view",{staticClass:"left",attrs:{"scroll-y":!0}},e._l(e.dayData,(function(t,i){return a("v-uni-view",{key:i,staticClass:"item",class:i==e.keyJudge?"itemDay":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectTime("days",i,"yes")}}},[t.title?[e._v(e._s(t.title))]:[e._v(e._s(t.month))],a("v-uni-text",{staticClass:"itemtext"},[e._v(e._s(t.Day))])],2)})),1),a("v-uni-scroll-view",{staticClass:"right",attrs:{"scroll-y":!0}},e._l(e.timeData,(function(t,i){return a("v-uni-view",{key:i,staticClass:"item",class:e.key==e.keyJudge&&i==e.keys?"itemTime":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectTime("time",i,"yes")}}},[e._v(e._s(t)),e.key==e.keyJudge&&i==e.keys?a("v-uni-text",{staticClass:"iconfont icon-yuan_checked color-base-text"}):e._e()],1)})),1)],1)],1)],1)],1)},s=[]},f7d5:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.box[data-v-8e6fd976]{height:%?728?%}.box .title[data-v-8e6fd976]{padding:0 %?30?%;box-sizing:border-box;text-align:center;font-size:%?28?%;font-weight:700;position:relative;height:%?90?%;line-height:%?90?%;border-bottom:%?1?% solid #f7f4f4}.box .title .icon-close[data-v-8e6fd976]{font-size:%?26?%;color:#909399;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.box .body[data-v-8e6fd976]{width:100%;height:calc(100% - %?90?%);display:flex;align-items:center}.box .body .left[data-v-8e6fd976]{width:%?230?%;background:#f8f8f8;height:100%}.box .body .left .item[data-v-8e6fd976]{width:100%;padding:%?16?% %?30?%;box-sizing:border-box;text-align:center;font-size:%?24?%;display:flex;align-items:center}.box .body .left .itemDay[data-v-8e6fd976]{background:#fff}.box .body .right[data-v-8e6fd976]{width:calc(100% - %?230?%);height:100%;padding:0 %?30?%;box-sizing:border-box}.box .body .right .item[data-v-8e6fd976]{width:100%;font-size:%?24?%;border-bottom:%?1?% solid #eee;display:flex;align-items:center;justify-content:space-between;height:%?72?%}.box .body .right .item .icon-yuan_checked[data-v-8e6fd976]{font-size:%?38?%;margin-right:%?30?%}.box .body .right .itemTime[data-v-8e6fd976]{color:var(--main-color)}',""]),e.exports=t}}]);