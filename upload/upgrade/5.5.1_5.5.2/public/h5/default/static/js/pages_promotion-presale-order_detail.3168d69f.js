(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_promotion-presale-order_detail","pages_promotion-blindbox-index~pages_promotion-giftcard-order_detail~pages_promotion-giftcard-order_~f811c135"],{"00dc":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={uniPopup:a("d745").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"contact-wrap"},[t._t("default"),a("v-uni-button",{staticClass:"contact-button",attrs:{type:"default","hover-class":"none","open-type":t.openType,"send-message-title":t.sendMessageTitle,"send-message-path":t.sendMessagePath,"send-message-img":t.sendMessageImg,"show-message-card":!0},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.contactServicer.apply(void 0,arguments)}}}),a("uni-popup",{ref:"servicePopup",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"service-popup-wrap"},[a("v-uni-view",{staticClass:"head-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.servicePopup.close()}}},[a("v-uni-text",[t._v("联系客服")]),a("v-uni-text",{staticClass:"iconfont icon-close"})],1),a("v-uni-view",{staticClass:"body-wrap"},[t._v(t._s(t.siteInfo.site_tel?"请联系客服，客服电话是"+t.siteInfo.site_tel:"抱歉，商家暂无客服，请线下联系"))])],1)],1)],2)},s=[]},1309:function(t,e,a){"use strict";a.r(e);var i=a("98db"),o=a("ec80");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("df86"),a("64ec");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"9ce749c0",null,!1,i["a"],void 0);e["default"]=r.exports},"171a":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"nsSwitch",props:{checked:{type:Boolean,default:!1}},methods:{change:function(){this.$emit("change")}}};e.default=i},"1dc6":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"ns-contact",props:{niushop:{type:Object,default:function(){return{}}},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""}},data:function(){return{config:null,openType:""}},created:function(){this.servicerConfig&&(this.config=this.servicerConfig.h5)},methods:{contactServicer:function(){if("none"==this.config.type&&this.$refs.servicePopup.open(),"contact"!=this.openType)switch(this.config.type){case"wxwork":location.href=this.config.wxwork_url;break;case"third":location.href=this.config.third_url;break;case"niushop":this.$util.redirectTo("/pages_tool/chat/room",this.niushop);break;default:this.makePhoneCall()}},makePhoneCall:function(){this.$api.sendRequest({url:"/api/site/shopcontact",success:function(t){0==t.code&&t.data.mobile&&uni.makePhoneCall({phoneNumber:t.data.mobile})}})}}};e.default=i},"1dd9":function(t,e,a){var i=a("2a42");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("8c18b280",i,!0,{sourceMap:!1,shadowMode:!1})},2523:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.contact-wrap[data-v-142659c1]{width:100%;height:100%;position:relative}.contact-wrap .contact-button[data-v-142659c1]{width:100%;height:100%;position:absolute;left:0;top:0;z-index:5;padding:0;margin:0;opacity:0;overflow:hidden}.service-popup-wrap[data-v-142659c1]{width:%?600?%}.service-popup-wrap .head-wrap[data-v-142659c1]{display:flex;justify-content:space-between;align-items:center;padding:0 %?30?%;height:%?90?%}.service-popup-wrap .body-wrap[data-v-142659c1]{text-align:center;padding:%?30?%;height:%?100?%}',""]),t.exports=e},"2a42":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.popup[data-v-4252f8a9]{width:75vw;background:#fff;border-top-left-radius:%?10?%;border-top-right-radius:%?10?%}.popup .popup-header[data-v-4252f8a9]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-4252f8a9]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-4252f8a9]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-4252f8a9]{height:calc(100% - %?250?%)}.popup .popup-body.safe-area[data-v-4252f8a9]{height:calc(100% - %?270?%)}.popup .popup-footer[data-v-4252f8a9]{height:%?100?%}.popup .popup-footer .confirm-btn[data-v-4252f8a9]{height:%?72?%;line-height:%?72?%;color:#fff;text-align:center;margin:%?20?% %?30?% 0;border-radius:%?10?%;font-size:%?28?%}.popup .popup-footer.bottom-safe-area[data-v-4252f8a9]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.choose-payment-popup .payment-item[data-v-4252f8a9]{display:flex;align-items:center;justify-content:space-between;height:%?90?%;margin:0 %?30?%;border-bottom:%?2?% solid #eee;padding:%?20?% 0}.choose-payment-popup .payment-item[data-v-4252f8a9]:nth-child(2){padding-top:0}.choose-payment-popup .payment-item[data-v-4252f8a9]:last-child{border-bottom:none}.choose-payment-popup .payment-item .iconfont[data-v-4252f8a9]{font-size:%?64?%}.choose-payment-popup .payment-item .icon-yue[data-v-4252f8a9]{color:#faa218}.choose-payment-popup .payment-item .icon-weixin1[data-v-4252f8a9]{color:#24af41}.choose-payment-popup .payment-item .icon-zhifubaozhifu-[data-v-4252f8a9]{color:#00a0e9}.choose-payment-popup .payment-item .icon-checkboxblank[data-v-4252f8a9]{font-size:%?40?%;color:#eee}.choose-payment-popup .payment-item .icon-yuan_checked[data-v-4252f8a9]{font-size:%?40?%}.choose-payment-popup .payment-item .name[data-v-4252f8a9]{margin-left:%?20?%;font-size:%?28?%;flex:1}.choose-payment-popup .payment-item .info-wrap[data-v-4252f8a9]{flex:1;margin-left:%?20?%}.choose-payment-popup .payment-item .info-wrap .name[data-v-4252f8a9]{margin-left:0;font-size:%?28?%;flex:1}.choose-payment-popup .payment-item .info-wrap .money[data-v-4252f8a9]{color:#909399;font-size:%?24?%}.choose-payment-popup .payment-item .box[data-v-4252f8a9]{flex:1;padding:0 %?10?%;line-height:inherit;text-align:right}.choose-payment-popup .payment-item .box uni-input[data-v-4252f8a9]{font-size:%?24?%!important}.choose-payment-popup .payment-item.set-pay-password[data-v-4252f8a9]{height:auto}.choose-payment-popup .payment-item.set-pay-password .box[data-v-4252f8a9]{font-size:%?24?%!important}.choose-payment-popup .pay-money[data-v-4252f8a9]{text-align:center;padding:%?20?% 0 %?40?% 0;background-color:#fff;font-weight:700;margin-top:%?30?%;line-height:1}.choose-payment-popup .pay-money .unit[data-v-4252f8a9]{margin-right:%?4?%;font-size:%?24?%}.choose-payment-popup .pay-money .money[data-v-4252f8a9]{font-size:%?32?%}.empty[data-v-4252f8a9]{width:100%;text-align:center;padding:%?40?% 0;color:#606266;font-size:%?24?%}',""]),t.exports=e},"2a87":function(t,e,a){"use strict";a.r(e);var i=a("a985"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},"483ef":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,"[data-v-9ce749c0] .sku-layer .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{max-height:unset!important}",""]),t.exports=e},"4c42":function(t,e,a){"use strict";var i=a("c46e"),o=a.n(i);o.a},5036:function(t,e,a){"use strict";a.r(e);var i=a("00dc"),o=a("5323");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("bb68");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"142659c1",null,!1,i["a"],void 0);e["default"]=r.exports},5323:function(t,e,a){"use strict";a.r(e);var i=a("1dc6"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},"554a":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.weui-switch[data-v-7e2a453e]{display:block;position:relative;width:%?94?%;height:%?45?%;outline:0;border-radius:%?30?%;border:%?2?% solid;border-color:#dfdfdf;transition:background-color .1s,border .1s}.weui-switch .bgview[data-v-7e2a453e]{content:" ";position:absolute;top:0;left:0;width:%?94?%;height:%?45?%;border-radius:%?30?%;transition:-webkit-transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1),-webkit-transform .35s cubic-bezier(.45,1,.4,1)}.weui-switch .spotview[data-v-7e2a453e]{content:" ";position:absolute;top:%?2?%;left:%?4?%;width:%?40?%;height:%?40?%;border-radius:50%;background-color:#fff;box-shadow:0 %?2?% %?6?% rgba(0,0,0,.4);transition:-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35),-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35)}.weui-switch-on[data-v-7e2a453e]{border-color:#6f6f6f}.weui-switch-on .bgview[data-v-7e2a453e]{border-color:#1aad19}.weui-switch-on .spotview[data-v-7e2a453e]{-webkit-transform:translateX(%?48?%);transform:translateX(%?48?%)}',""]),t.exports=e},"60eb":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("e838");var o=i(a("870a")),s=i(a("7254")),n=i(a("5036")),r={components:{nsGoodsRecommend:s.default,nsContact:n.default},data:function(){return{isIphoneX:!1,orderId:0,orderData:{action:[]},timestamp:0,payMoney:0,payType:"",memberBalance:0,isBalance:0,isSub:!1}},mixins:[o.default],onLoad:function(t){t.order_id&&(this.orderId=t.order_id)},computed:{balanceMoney:function(){return this.orderData.balance_deposit_money+this.orderData.balance_final_money},balanceDeduct:function(){if(1==this.orderData.order_status&&""==this.orderData.final_out_trade_no&&this.memberBalance>0){var t=this.orderData.order_money-this.orderData.presale_deposit_money;return(this.memberBalance>t?t:this.memberBalance).toFixed(2)}return 0},presaleDiscount:function(){return(parseFloat(this.orderData.presale_money)-parseFloat(this.orderData.presale_deposit_money)).toFixed(2)}},onShow:function(){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.storeToken?this.getOrderData():this.$util.redirectTo("/pages_tool/login/index",{back:"/pages/order/detail?order_id="+this.orderId}),this.getMemberBalance()},methods:{getMemberBalance:function(){var t=this;this.$api.sendRequest({url:"/api/memberaccount/info",data:{account_type:"balance,balance_money"},success:function(e){e.data&&(t.memberBalance=parseFloat(e.data.balance)+parseFloat(e.data.balance_money))}})},goDetail:function(){this.$util.redirectTo("/pages_promotion/presale/detail",{id:this.orderData.presale_id})},getOrderData:function(){var t=this;this.$api.sendRequest({url:"/presale/api/order/detail",data:{order_id:this.orderId},success:function(e){t.timestamp=e.timestamp,uni.stopPullDownRefresh(),e.code>=0?(t.orderData=e.data,t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:"未获取到订单信息！"}),setTimeout((function(){t.$util.redirectTo("/pages_promotion/presale/list")}),1500))},fail:function(e){uni.stopPullDownRefresh(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},onPullDownRefresh:function(){this.getOrderData()},operation:function(t){var e=this;switch(t){case"deleteOrder":this.deleteOrder(this.orderData.id,(function(){e.getOrderData()}));break;case"orderClose":this.orderClose(this.orderData.id,(function(){e.getOrderData()}));break;case"orderPayDeposit":this.openPaymentPopup(this.orderData,"presale_deposit_money");break;case"refundDeposit":this.refundDeposit(this.orderData.id,(function(){e.getOrderData()}));break;case"orderPayFinal":this.openPaymentPopup(this.orderData,"final_money");break}},imageError:function(){this.orderData.sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},pay:function(){var t=this;this.isSub||(this.isSub=!0,"final_money"==this.payType?this.orderPayFinal(this.orderData,(function(){t.getOrderData()})):"presale_deposit_money"==this.payType&&this.orderPayDeposit(this.orderData,(function(){t.getOrderData()})))},useBalance:function(){this.isBalance?(this.isBalance=0,this.payMoney+=parseFloat(this.balanceDeduct)):(this.isBalance=1,this.payMoney-=parseFloat(this.balanceDeduct))}},filters:{abs:function(t){return Math.abs(parseFloat(t)).toFixed(2)}}};e.default=r},"64ec":function(t,e,a){"use strict";var i=a("835c"),o=a.n(i);o.a},"67dd":function(t,e,a){"use strict";var i=a("1dd9"),o=a.n(i);o.a},7258:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={uniPopup:a("d745").default,nsSwitch:a("b0ec").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("uni-popup",{ref:"choosePaymentPopup",attrs:{type:"center","mask-click":!1}},[a("v-uni-view",{staticClass:"choose-payment-popup popup",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[t._v("支付方式")]),a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close()}}})],1),a("v-uni-scroll-view",{staticClass:"popup-body",class:{"safe-area":t.isIphoneX},attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"pay-money"},[a("v-uni-text",{staticClass:"money"},[t._v("支付金额"+t._s(t._f("moneyFormat")(t.payMoney))+"元")])],1),t.balanceDeduct>0&&1==t.balanceConfig&&t.sale?a("v-uni-view",{staticClass:"payment-item"},[a("v-uni-view",{staticClass:"iconfont icon-yue"}),a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-text",{staticClass:"name"},[t._v("余额抵扣")]),a("v-uni-view",{staticClass:"money"},[t._v("可用¥"+t._s(t.balanceDeduct))])],1),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==t.isBalance},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.useBalance.apply(void 0,arguments)}}})],1):t._e(),t.payMoney>0?[t.payTypeList.length?t._l(t.payTypeList,(function(e,i){return a("v-uni-view",{key:i,staticClass:"payment-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.payIndex=i}}},[a("v-uni-view",{staticClass:"iconfont",class:e.icon}),a("v-uni-text",{staticClass:"name"},[t._v(t._s(e.name))]),a("v-uni-text",{staticClass:"iconfont",class:t.payIndex==i?"icon-yuan_checked color-base-text":"icon-checkboxblank"})],1)})):[a("v-uni-view",{staticClass:"empty"},[t._v("平台尚未配置支付方式！")])]]:t._e()],2),a("v-uni-view",{staticClass:"popup-footer",class:{"bottom-safe-area":t.isIphoneX}},[a("v-uni-view",{staticClass:"confirm-btn color-base-bg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm()}}},[t._v("确认支付")])],1)],1)],1)],1)},s=[]},"7aec":function(t,e,a){"use strict";a.r(e);var i=a("7258"),o=a("2a87");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("67dd");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"4252f8a9",null,!1,i["a"],void 0);e["default"]=r.exports},"7c21":function(t,e,a){var i=a("2523");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("1ca7a40c",i,!0,{sourceMap:!1,shadowMode:!1})},"7e0a":function(t,e,a){"use strict";a.r(e);var i=a("171a"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},"835c":function(t,e,a){var i=a("483ef");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("1e0399e4",i,!0,{sourceMap:!1,shadowMode:!1})},"870a":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={methods:{orderClose:function(t,e){var a=this;uni.showModal({title:"提示",content:"您确定要关闭该订单吗？",success:function(i){i.confirm&&a.$api.sendRequest({url:"/presale/api/order/close",data:{order_id:t},success:function(t){0==t.code?"function"==typeof e&&e():a.$util.showToast({title:t.message,duration:2e3})}})}})},deleteOrder:function(t,e){var a=this;uni.showModal({title:"提示",content:"您确定要删除该订单吗？",success:function(i){i.confirm&&a.$api.sendRequest({url:"/presale/api/order/delete",data:{order_id:t},success:function(t){0==t.code?"function"==typeof e&&e():a.$util.showToast({title:t.message,duration:2e3})}})}})},refundDeposit:function(t,e){var a=this;uni.showModal({title:"提示",content:"您确定要退定金吗？",success:function(i){i.confirm&&a.$api.sendRequest({url:"/presale/api/refund/applyRefund",data:{order_id:t},success:function(t){0==t.code?"function"==typeof e&&e():a.$util.showToast({title:t.message,duration:2e3})}})}})},orderPayFinal:function(t,e){var a=this,i="/presale/api/order/pay",o={id:t.id};""==t.final_out_trade_no&&(i="/presale/api/ordercreate/finalCreate",o.is_balance=this.isBalance),this.$api.sendRequest({url:i,data:o,success:function(t){a.isSub=!1,t.code>=0?0==a.payMoney?a.$util.redirectTo("/pages_tool/pay/result",{code:t.data},"redirectTo"):a.$refs.choosePaymentPopup.getPayInfo(t.data):a.$util.showToast({title:t.message}),a.isBalance=0},fail:function(t){a.isSub=!1}})},orderPayDeposit:function(t,e){var a=this;this.$api.sendRequest({url:"/presale/api/order/pay",data:{id:t.id},success:function(t){a.isSub=!1,t.code>=0?a.$refs.choosePaymentPopup.getPayInfo(t.data):a.$util.showToast({title:t.message})},fail:function(t){a.isSub=!1}})},openPaymentPopup:function(t,e){this.payType=e,"final_money"==e?""==t.final_out_trade_no&&(this.payMoney=t.order_money-t.presale_deposit_money):"presale_deposit_money"==e&&(this.payMoney=t.pay_deposit_money),uni.setStorageSync("paySource","presale"),this.$refs.choosePaymentPopup.open()}}};e.default=i},9498:function(t,e,a){var i=a("ecc8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("ce94eec6",i,!0,{sourceMap:!1,shadowMode:!1})},"98db":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={pageMeta:a("7854").default,nsContact:a("5036").default,nsGoodsRecommend:a("7254").default,nsPayment:a("7aec").default,loadingCover:a("c003").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-view",{staticClass:"detail-container",class:{"safe-area":t.isIphoneX}},[a("v-uni-view",{staticClass:"status-wrap color-base-bg",style:{backgroundImage:"url("+t.$util.img("public/uniapp/order/status-wrap-bg.png")+")"}},[a("v-uni-view",{staticClass:"order-status-left"},[a("v-uni-view",{staticClass:"status-name"},[1==t.orderData.refund_status?[a("v-uni-view",{staticClass:"name"},[t._v("退款中")])]:[a("v-uni-view",{staticClass:"name"},[t._v(t._s(t.orderData.order_status_name))]),1==t.orderData.order_status?[t.orderData.pay_start_time>t.timestamp?a("v-uni-view",{staticClass:"desc"},[t._v("尾款开始支付时间："+t._s(t.$util.timeStampTurnTime(t.orderData.pay_start_time)))]):a("v-uni-view",{staticClass:"desc"},[t._v("尾款支付结束时间："+t._s(t.$util.timeStampTurnTime(t.orderData.pay_end_time)))])]:t._e()]],2)],1)],1),a("v-uni-view",{staticClass:"site-wrap"},[a("v-uni-view",{staticClass:"site-body"},[a("v-uni-view",{staticClass:"goods-wrap"},[a("v-uni-view",{staticClass:"goods-img",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goDetail.apply(void 0,arguments)}}},[a("v-uni-image",{attrs:{src:t.$util.img(t.orderData.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError()}}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-view",{staticClass:"goods-name",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goDetail.apply(void 0,arguments)}}},[t._v(t._s(t.orderData.sku_name))]),a("v-uni-view",{staticClass:"goods-sub-section"},[a("v-uni-view",[a("v-uni-text",{staticClass:"goods-price "},[a("v-uni-text",{staticClass:"unit price-style small"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"font-size-base price-style large"},[t._v(t._s(parseFloat(t.orderData.price).toFixed(2).split(".")[0]))]),a("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.orderData.price).toFixed(2).split(".")[1]))])],1)],1),a("v-uni-view",[a("v-uni-text",{staticClass:"font-size-base"},[a("v-uni-text",{staticClass:"iconfont icon-close"}),t._v(t._s(t.orderData.num))],1)],1)],1)],1)],1)],1)],1),a("v-uni-view",{staticClass:"order-summary"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("订单编号：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.order_no))]),a("v-uni-view",{staticClass:"copy",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.orderData.order_no)}}},[t._v("复制")])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("创建时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.create_time)))])],1)],1),t.orderData.close_time>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("关闭时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.close_time)))])],1)],1):t._e(),t.orderData.pay_deposit_time>0?[a("v-uni-view",{staticClass:"hr"}),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("定金支付方式：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.deposit_pay_type_name))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("定金支付时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.pay_deposit_time)))])],1)],1)]:t._e(),t.orderData.pay_final_time>0?[a("v-uni-view",{staticClass:"hr"}),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("尾款支付方式：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.final_pay_type_name))])],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("尾款支付时间：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderData.pay_final_time)))])],1)],1)]:t._e(),t.orderData.delivery_type_name?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("配送方式：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.delivery_type_name))])],1)],1):t._e(),""!=t.orderData.buyer_message?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("买家留言：")]),a("v-uni-view",{staticClass:"box"},[a("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderData.buyer_message))])],1)],1):t._e(),a("ns-contact",{attrs:{niushop:{order_id:t.orderId}}},[a("v-uni-view",{staticClass:"kefu"},[a("v-uni-view",[a("v-uni-text",{staticClass:"iconfont icon-ziyuan"}),a("v-uni-text",[t._v("联系客服")])],1)],1)],1)],2),a("v-uni-view",{staticClass:"order-money presale-process"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-view",{staticClass:"tit",class:{"color-base-text":0==t.orderData.pay_deposit_time}},[a("v-uni-text",{staticClass:"font-size-base"},[t._v("阶段一：付定金")]),t.orderData.pay_deposit_time>0?a("v-uni-text",{staticClass:"font-size-base"},[t._v("（已付）")]):t._e()],1),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{class:{"color-base-text":0==t.orderData.pay_deposit_time}},[a("v-uni-text",{staticClass:"font-size-tag price-color price-font"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"font-size-base price-color price-font"},[t._v(t._s(t.orderData.presale_deposit_money))])],1)],1)],1),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-view",{staticClass:"tit",class:{"color-base-text":t.orderData.pay_deposit_time>0&&t.orderData.pay_final_time>=0}},[a("v-uni-text",{staticClass:"font-size-base"},[t._v("阶段二：付尾款")]),t.orderData.pay_final_time>0?a("v-uni-text",{staticClass:"font-size-base"},[t._v("（已付）")]):t._e()],1),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{class:{"color-base-text":t.orderData.pay_deposit_time>0&&t.orderData.pay_final_time>=0}},[a("v-uni-text",{staticClass:"font-size-tag price-color price-font"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"font-size-base price-color price-font"},[t._v(t._s(t.orderData.final_money))])],1)],1)],1)],1),a("v-uni-view",{staticClass:"order-money"},[a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("商品金额")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-title price-font"},[a("v-uni-text",{staticClass:"font-size-goods-tag "},[t._v(t._s(t.$lang("common.currencySymbol")))]),t._v(t._s(t.orderData.goods_money))],1)],1)],1),t.presaleDiscount>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("定金膨胀")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator price-color"},[t._v("-")]),a("v-uni-text",{staticClass:"font-size-goods-tag price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price-color"},[t._v(t._s(t.presaleDiscount))])],1)],1)],1):t._e(),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("运费")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"font-size-goods-tag price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price-color"},[t._v(t._s(t.orderData.delivery_money))])],1)],1)],1),t.orderData.invoice_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("税费"),a("v-uni-text",{staticClass:"color-base-text price-font"},[t._v("("+t._s(t.orderData.invoice_rate)+"%)")])],1),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator price-color"},[t._v("+")]),a("v-uni-text",{staticClass:"font-size-goods-tag price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price-color"},[t._v(t._s(t.orderData.invoice_money))])],1)],1)],1):t._e(),t.orderData.invoice_delivery_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("发票邮寄费")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator price-color"},[t._v("+")]),a("v-uni-text",{staticClass:"font-size-goods-tag price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price-color"},[t._v(t._s(t.orderData.invoice_delivery_money))])],1)],1)],1):t._e(),t.orderData.promotion_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("优惠")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator price-color"},[t._v("-")]),a("v-uni-text",{staticClass:"font-size-goods-tag price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price-color"},[t._v(t._s(t.orderData.promotion_money))])],1)],1)],1):t._e(),t.orderData.coupon_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("优惠券")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator price-color"},[t._v("-")]),a("v-uni-text",{staticClass:"font-size-goods-tag price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price-color"},[t._v(t._s(t.orderData.coupon_money))])],1)],1)],1):t._e(),t.balanceMoney>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("使用余额")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator price-color"},[t._v("-")]),a("v-uni-text",{staticClass:"font-size-goods-tag price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price-color"},[t._v(t._s(t.balanceMoney))])],1)],1)],1):t._e(),t.orderData.point_money>0?a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-text",{staticClass:"tit"},[t._v("积分抵扣")]),a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"operator price-color"},[t._v("-")]),a("v-uni-text",{staticClass:"font-size-goods-tag price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"price-color"},[t._v(t._s(t.orderData.point_money))])],1)],1)],1):t._e(),a("v-uni-view",{staticClass:"order-cell"},[a("v-uni-view",{staticClass:"box align-right"},[a("v-uni-text",[t._v("合计：")]),a("v-uni-text",{staticClass:"color-base-text price-font"},[a("v-uni-text",{staticClass:"font-size-goods-tag price-color"},[t._v(t._s(t.$lang("common.currencySymbol")))]),a("v-uni-text",{staticClass:"font-size-base price-color"},[t._v(t._s(t.orderData.order_money))])],1)],1)],1),t.orderData.action.length>0?a("v-uni-view",{staticClass:"order-action",class:{"bottom-safe-area":t.isIphoneX}},[t._l(t.orderData.action,(function(e,i){return["orderPayFinal"==e.action?[0!=t.orderData.refund_status&&-1!=t.orderData.refund_status||-1==t.orderData.order_status?a("v-uni-view",{staticClass:"order-box-btn disabled"},[t._v(t._s(e.title))]):[t.orderData.pay_start_time<t.timestamp&&t.orderData.pay_end_time>t.timestamp?a("v-uni-view",{staticClass:"order-box-btn color-base-border color-base-text",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.operation(e.action)}}},[t._v(t._s(e.title))]):a("v-uni-view",{staticClass:"order-box-btn disabled"},[t._v(t._s(e.title))])]]:"refundDeposit"==e.action?[0==t.orderData.refund_status&&0==t.orderData.is_deposit_back||-1==t.orderData.refund_status&&0==t.orderData.is_deposit_back?a("v-uni-view",{staticClass:"order-box-btn",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.operation(e.action)}}},[t._v(t._s(e.title))]):t._e()]:a("v-uni-view",{staticClass:"order-box-btn",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.operation(e.action)}}},[t._v(t._s(e.title))])]}))],2):t._e()],1),a("ns-goods-recommend",{ref:"goodrecommend",attrs:{route:"order_detail"}}),a("ns-payment",{ref:"choosePaymentPopup",attrs:{payMoney:t.payMoney,balanceDeduct:t.balanceDeduct,isBalance:t.isBalance},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.pay.apply(void 0,arguments)},useBalance:function(e){arguments[0]=e=t.$handleEvent(e),t.useBalance.apply(void 0,arguments)}}}),a("loading-cover",{ref:"loadingCover"})],1)],1)},s=[]},a985:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("5c47"),a("2c10"),a("bf0f"),a("2797"),a("5ef2"),a("dd2b");var o=i(a("d745")),s=i(a("b0ec")),n=a("edd0"),r={name:"ns-payment",components:{uniPopup:o.default,nsSwitch:s.default},props:{balanceDeduct:{type:[Number,String],default:""},isBalance:{type:Number,default:0},payMoney:{type:[Number,String],default:0}},data:function(){return{isIphoneX:!1,payIndex:0,payTypeList:[{name:"支付宝支付",icon:"icon-zhifubaozhifu-",type:"alipay"},{name:"微信支付",icon:"icon-weixin1",type:"wechatpay"}],timer:null,payInfo:{},balanceConfig:0,sale:!0}},created:function(t){var e=window.location.href,a=e.match("presale/order_list/order_list"),i=e.match("presale/order_detail/order_detail");(a||i)&&(this.sale=!1),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getPayType(),this.getBalanceConfig()},methods:{open:function(){this.$refs.choosePaymentPopup.open()},close:function(){this.$refs.choosePaymentPopup.close()},useBalance:function(){this.$emit("useBalance")},confirm:function(){0==this.payTypeList.length&&this.payMoney>0?this.$util.showToast({title:"请选择支付方式！"}):(uni.showLoading({title:"支付中...",mask:!0}),this.$refs.choosePaymentPopup.close(),this.$emit("confirm"),uni.setStorageSync("pay_flag",1))},getPayInfo:function(t){var e=this;this.$api.sendRequest({url:"/api/pay/info",data:{out_trade_no:t},success:function(t){t.code>=0&&t.data?(e.payInfo=t.data,2==e.payInfo["pay_status"]?e.$util.redirectTo("/pages_tool/pay/result",{code:e.payInfo.out_trade_no},"","redirectTo"):e.pay()):(e.$util.showToast({title:"未获取到支付信息！"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1500))}})},getBalanceConfig:function(){var t=this;this.$api.sendRequest({url:"/api/pay/getBalanceConfig",data:{},success:function(e){t.balanceConfig=e.data.balance_show}})},getPayType:function(){var t=this;this.$api.sendRequest({url:"/api/pay/type",success:function(e){0==e.code&&(""==e.data.pay_type?t.payTypeList=[]:t.payTypeList.forEach((function(a,i){-1==e.data.pay_type.indexOf(a.type)&&t.payTypeList.splice(i,1)})))}})},pay:function(){var t=this,e=this.payTypeList[this.payIndex];if(e){var a="";a="BlindboxGoodsOrderPayNotify"==this.payInfo.event?"/pages_promotion/blindbox/index?outTradeNo=":"/pages_tool/pay/result?code=",this.$api.sendRequest({url:"/api/pay/pay",data:{out_trade_no:this.payInfo.out_trade_no,pay_type:e.type,return_url:encodeURIComponent(this.$config.h5Domain+a+this.payInfo.out_trade_no)},success:function(a){if(uni.hideLoading(),a.code>=0)switch(e.type){case"alipay":if(t.$util.isWeiXin()){var i=encodeURIComponent(a.data.data);t.$util.redirectTo("/pages_tool/pay/wx_pay",{wx_alipay:i,out_trade_no:t.payInfo.out_trade_no},"","redirectTo")}else location.href=a.data.data,t.checkPayStatus();break;case"wechatpay":if(t.$util.isWeiXin()){if("ios"==uni.getSystemInfoSync().platform)var o=uni.getStorageSync("initUrl");else o=location.href;t.$api.sendRequest({url:"/wechat/api/wechat/jssdkconfig",data:{url:o},success:function(e){var i=new n.Weixin,o=a.data.data;i.init(e.data),i.pay({timestamp:o.timestamp?o.timestamp:o.timeStamp,nonceStr:o.nonceStr,package:o.package,signType:o.signType,paySign:o.paySign},(function(e){"chooseWXPay:ok"==e.errMsg?"BlindboxGoodsOrderPayNotify"==t.payInfo.event?t.$util.redirectTo("/pages_promotion/blindbox/index",{outTradeNo:t.payInfo.out_trade_no},"","redirectTo"):t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo"):t.$util.showToast({title:e.errMsg})}),(function(e){t.$util.showToast({title:"您已取消支付"}),setTimeout((function(){t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"redirectTo")}),2e3)}))}})}else location.href=a.data.url,t.checkPayStatus();break}else t.$util.showToast({title:a.message})},fail:function(e){uni.hideLoading(),t.$util.showToast({title:"request:fail"})}})}},checkPayStatus:function(){var t=this;this.timer=setInterval((function(){t.$api.sendRequest({url:"/api/pay/status",data:{out_trade_no:t.payInfo.out_trade_no},success:function(e){0==e.code?2==e.data.pay_status&&(clearInterval(t.timer),t.$util.redirectTo("/pages_tool/pay/result",{code:t.payInfo.out_trade_no},"","redirectTo")):clearInterval(t.timer)}})}),1e3)}},deactivated:function(){clearInterval(this.timer)}};e.default=r},b0ec:function(t,e,a){"use strict";a.r(e);var i=a("bf29"),o=a("7e0a");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);a("4c42");var n=a("828b"),r=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"7e2a453e",null,!1,i["a"],void 0);e["default"]=r.exports},bb68:function(t,e,a){"use strict";var i=a("7c21"),o=a.n(i);o.a},bf29:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("v-uni-view",{staticClass:"weui-switch",class:{"weui-switch-on":t.checked,"color-base-border":t.checked},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.change()}}},[a("v-uni-view",{staticClass:"bgview",class:{"color-base-bg":t.checked}}),a("v-uni-view",{staticClass:"spotview"})],1)],1)},o=[]},c46e:function(t,e,a){var i=a("554a");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("95c2f08a",i,!0,{sourceMap:!1,shadowMode:!1})},df86:function(t,e,a){"use strict";var i=a("9498"),o=a.n(i);o.a},ec80:function(t,e,a){"use strict";a.r(e);var i=a("60eb"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},ecc8:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */uni-text[data-v-9ce749c0],\r\nuni-view[data-v-9ce749c0]{font-size:%?24?%}.align-right[data-v-9ce749c0]{text-align:right}.color-text-white[data-v-9ce749c0]{color:#fff}.detail-container[data-v-9ce749c0]{height:100vh}.detail-container .height-box[data-v-9ce749c0]{display:block;padding-bottom:%?100?%}.detail-container.safe-area .height-box[data-v-9ce749c0]{display:block;padding-bottom:%?168?%}.status-wrap[data-v-9ce749c0]{background-size:100% 100%;padding:%?40?% 0;height:%?180?%}.status-wrap uni-image[data-v-9ce749c0]{width:%?104?%;height:%?86?%;margin-right:%?20?%;margin-top:%?20?%}.status-wrap .order-status-left[data-v-9ce749c0]{display:flex;margin:0 %?40?%}.status-wrap > uni-view[data-v-9ce749c0]{text-align:center;color:#fff}.status-wrap .desc[data-v-9ce749c0]{margin-left:%?20?%}.status-wrap .price[data-v-9ce749c0]{font-weight:600}.status-wrap .action-group[data-v-9ce749c0]{text-align:center;padding-top:%?20?%}.status-wrap .action-group .action-btn[data-v-9ce749c0]{line-height:1;padding:%?16?% %?50?%;display:inline-block;border-radius:%?32?%;background:#fff;box-shadow:0 0 %?14?% hsla(0,0%,62%,.6)}.site-wrap[data-v-9ce749c0]{margin:%?20?% %?30?%;padding:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative;margin-top:%?-69?%}.site-wrap .site-header[data-v-9ce749c0]{display:flex;align-items:center}.site-wrap .site-header .icon-dianpu[data-v-9ce749c0]{display:inline-block;line-height:1;margin-right:%?12?%;font-size:%?28?%}.site-wrap .site-body .goods-wrap[data-v-9ce749c0]{margin-bottom:%?20?%;display:flex;position:relative}.site-wrap .site-body .goods-wrap[data-v-9ce749c0]:last-of-type{margin-bottom:0}.site-wrap .site-body .goods-wrap .goods-img[data-v-9ce749c0]{width:%?160?%;height:%?160?%;margin-right:%?20?%}.site-wrap .site-body .goods-wrap .goods-img uni-image[data-v-9ce749c0]{width:100%;height:100%;border-radius:%?10?%}.site-wrap .site-body .goods-wrap .goods-info[data-v-9ce749c0]{flex:1;position:relative;display:flex;flex-direction:column;justify-content:space-between}.site-wrap .site-body .goods-wrap .goods-info .goods-name[data-v-9ce749c0]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section[data-v-9ce749c0]{width:100%;line-height:1.3;display:flex;margin-top:%?20?%}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .goods-price[data-v-9ce749c0]{font-weight:700;font-size:%?20?%;color:var(--price-color)}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section .unit[data-v-9ce749c0]{font-size:%?24?%;margin-right:%?2?%;font-weight:700}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-9ce749c0]{flex:1;line-height:1.3}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view[data-v-9ce749c0]:last-of-type{text-align:right}.site-wrap .site-body .goods-wrap .goods-info .goods-sub-section uni-view:last-of-type .iconfont[data-v-9ce749c0]{line-height:1;font-size:%?24?%}.site-wrap .site-body .goods-wrap .goods-info .goods-action[data-v-9ce749c0]{text-align:right;padding-top:%?20?%}.site-wrap .site-body .goods-wrap .goods-info .goods-action uni-navigator[data-v-9ce749c0]{display:inline-block}.site-wrap .site-body .goods-wrap .goods-info .goods-action .order-box-btn[data-v-9ce749c0]{height:%?48?%!important;line-height:%?48?%!important;font-size:%?24?%!important;display:inline-block;background:#fff;border:.5px solid #999;margin-left:%?10?%;box-sizing:initial}.order-cell[data-v-9ce749c0]{display:flex;margin:%?20?% 0;align-items:center;background:#fff;line-height:%?40?%}.order-cell .tit[data-v-9ce749c0]{text-align:left}.order-cell .box[data-v-9ce749c0]{flex:1;padding:0 %?20?%;line-height:inherit}.order-cell .box .textarea[data-v-9ce749c0]{height:%?40?%}.order-cell .iconfont[data-v-9ce749c0]{color:#bbb;font-size:%?28?%}.order-cell .order-pay[data-v-9ce749c0]{padding:0}.order-cell .order-pay uni-text[data-v-9ce749c0]{display:inline-block;margin-left:%?6?%}.order-summary[data-v-9ce749c0]{margin:%?20?% %?30?%;padding:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative}.order-summary .order-cell[data-v-9ce749c0]:first-child{margin-top:0}.order-summary .order-cell .tit[data-v-9ce749c0]{font-size:%?28?%;width:%?196?%}.order-summary .order-cell .box[data-v-9ce749c0]{display:flex;align-items:center}.order-summary .order-cell .box uni-text[data-v-9ce749c0]{font-size:%?28?%}.order-summary .order-cell .copy[data-v-9ce749c0]{font-size:%?20?%;display:inline-block;background:#f7f7f7;line-height:1;padding:%?6?% %?10?%;margin-left:%?10?%;border-radius:%?18?%;border:%?2?% solid #d2d2d2}.order-summary .hr[data-v-9ce749c0]{width:100%;height:%?2?%;background:#f7f7f7;margin-bottom:%?20?%}.order-money[data-v-9ce749c0]{margin:%?20?% %?30?%;padding:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative}.order-money .order-cell .tit[data-v-9ce749c0]{font-size:%?28?%}.order-money .order-cell .box[data-v-9ce749c0]{font-weight:600;padding:0}.order-money .order-cell .box uni-text[data-v-9ce749c0]{font-size:%?28?%;font-weight:700}.order-money .order-cell .box .operator[data-v-9ce749c0]{font-size:%?24?%;margin-right:%?6?%}.kefu[data-v-9ce749c0]{margin:%?20?% %?30?%;padding:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative;margin:%?30?% 0 %?10?%;border-top:%?2?% solid #f7f7f7;padding-bottom:0;padding-top:%?30?%}.kefu > uni-view[data-v-9ce749c0]{display:flex;justify-content:center;align-items:center}.kefu > uni-view .iconfont[data-v-9ce749c0]{font-weight:700;margin-right:%?10?%;font-size:%?28?%;line-height:1}.kefu uni-button[data-v-9ce749c0]{width:100%;border:none;z-index:1;padding:0;margin:0;background:none;height:%?50?%;line-height:%?50?%;display:flex;justify-content:center}.kefu uni-button[data-v-9ce749c0]::after{border:none!important}.kefu uni-button .iconfont[data-v-9ce749c0]{margin-right:%?10?%}.order-action[data-v-9ce749c0]{text-align:right}.order-action .order-box-btn[data-v-9ce749c0]{margin-right:%?30?%;margin-left:0;font-size:%?24?%;height:%?60?%;line-height:%?60?%;box-sizing:initial;min-width:%?60?%;text-align:center}.order-action .order-box-btn.color-base-bg[data-v-9ce749c0]{color:#fff}.order-action .order-box-btn[data-v-9ce749c0]:last-child{margin-right:0}.order-action .order-box-btn.disabled[data-v-9ce749c0]{background:#eee;border-color:#e5e5e5;color:#999}.status-name uni-view[data-v-9ce749c0]{font-size:%?32?%;color:#fff;line-height:1;text-align:left}.status-name .name[data-v-9ce749c0]{margin-top:%?40?%}.status-name .desc[data-v-9ce749c0]{font-size:%?24?%;margin:%?20?% 0 0 0}.head-nav[data-v-9ce749c0]{width:100%;height:0}.head-nav.active[data-v-9ce749c0]{padding-top:%?40?%}.head-return[data-v-9ce749c0]{height:%?90?%;line-height:%?90?%;color:#fff;font-weight:600;font-size:%?32?%;position:relative;text-align:center}.head-return uni-text[data-v-9ce749c0]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);left:%?20?%;display:inline-block;margin-right:%?10?%;font-size:%?32?%}.store-detail uni-view[data-v-9ce749c0]{font-size:%?20?%}.store-wrap[data-v-9ce749c0]{margin:%?20?% %?30?%;padding:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative;margin-top:%?-76?%}.store-wrap .store-info[data-v-9ce749c0]{display:flex;align-items:center;padding-left:%?50?%;position:relative}.store-wrap .store-info .icon[data-v-9ce749c0]{left:0;position:absolute;top:%?4?%}.store-wrap .store-info .icon .iconfont[data-v-9ce749c0]{line-height:%?50?%;font-size:%?28?%}.store-wrap .store-info .icon .icon-mendian[data-v-9ce749c0]{font-size:%?32?%}.store-wrap .store-info .store-name[data-v-9ce749c0]{display:flex}.store-wrap .store-info .store-name .name[data-v-9ce749c0]{flex:1}.store-wrap .store-info .store-info-detail[data-v-9ce749c0]{flex:1}.store-wrap .store-info .store-info-detail .store-detail uni-view[data-v-9ce749c0]{font-size:%?24?%}.store-wrap .store-info .store-info-detail > uni-view[data-v-9ce749c0]:first-of-type{font-size:%?26?%}.store-wrap .store-info .cell-more[data-v-9ce749c0]{margin-left:%?50?%}.pick-block[data-v-9ce749c0]{display:flex;align-items:center;margin-top:%?20?%;padding-top:%?20?%}.pick-block.first-pick-block[data-v-9ce749c0]{border-top:%?2?% solid #f1f1f1}.pick-block uni-input[data-v-9ce749c0],\r\n.pick-block .last-child[data-v-9ce749c0]{flex:1;text-align:right;font-size:%?24?%}.sku[data-v-9ce749c0]{display:flex;line-height:1;margin-top:%?10?%;margin-bottom:%?10?%}.goods-spec[data-v-9ce749c0]{color:#838383;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.presale-process .order-cell[data-v-9ce749c0]{margin:%?10?% 0}',""]),t.exports=e}}]);