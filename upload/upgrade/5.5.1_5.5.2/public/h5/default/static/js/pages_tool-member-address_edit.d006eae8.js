(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-member-address_edit"],{"34f8":function(t,e,a){"use strict";a.d(e,"b",(function(){return s})),a.d(e,"c",(function(){return d})),a.d(e,"a",(function(){return i}));var i={pageMeta:a("7854").default,pickRegions:a("04c1").default,loadingCover:a("c003").default},s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":t.themeColor}}),a("v-uni-view",{staticClass:"address-edit-content"},[a("v-uni-view",{staticClass:"edit-wrap"},[a("v-uni-view",{staticClass:"tip"},[t._v("地址信息")]),a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("consignee"))),a("v-uni-text",[t._v("*")])],1),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"text","placeholder-class":"placeholder-class",placeholder:t.$lang("consigneePlaceholder"),maxlength:"30",name:"name"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("mobile"))),a("v-uni-text",[t._v("*")])],1),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"number","placeholder-class":"placeholder-class",placeholder:t.$lang("mobilePlaceholder"),maxlength:"11"},model:{value:t.formData.mobile,callback:function(e){t.$set(t.formData,"mobile",e)},expression:"formData.mobile"}})],1),a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("telephone")))]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"text","placeholder-class":"placeholder-class",placeholder:t.$lang("telephonePlaceholder"),maxlength:"20"},model:{value:t.formData.telephone,callback:function(e){t.$set(t.formData,"telephone",e)},expression:"formData.telephone"}})],1),2==t.localType?[a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("receivingCity"))),a("v-uni-text",[t._v("*")])],1),a("v-uni-view",{staticClass:"text_inp",class:{empty:!t.formData.full_address,"color-tip":!t.formData.full_address},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAddress.apply(void 0,arguments)}}},[t._v(t._s(t.formData.full_address?t.formData.full_address:"请选择省市区县"))]),a("v-uni-text",{staticClass:"padding-left iconfont icon-location",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAddress.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("address"))),a("v-uni-text",[t._v("*")])],1),a("v-uni-text",{staticClass:"select-address",class:{empty:!t.formData.address,"color-tip":!t.formData.address},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAddress.apply(void 0,arguments)}}},[t._v(t._s(t.formData.address?t.formData.address:t.$lang("addressPlaceholder")))])],1),t.isEdit?a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("house"))),a("v-uni-text",[t._v("*")])],1),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"text","placeholder-class":"placeholder-class",placeholder:t.$lang("housePlaceholder"),maxlength:"50"},model:{value:t.formData.house,callback:function(e){t.$set(t.formData,"house",e)},expression:"formData.house"}})],1):t._e()]:[a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("receivingCity"))),a("v-uni-text",[t._v("*")])],1),a("pick-regions",{attrs:{"default-regions":t.defaultRegions},on:{getRegions:function(e){arguments[0]=e=t.$handleEvent(e),t.handleGetRegions.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"select-address ",class:{empty:!t.formData.full_address,"color-tip":!t.formData.full_address}},[t._v(t._s(t.formData.full_address?t.formData.full_address:"请选择省市区县"))])],1)],1),a("v-uni-view",{staticClass:"edit-item"},[a("v-uni-text",{staticClass:"tit"},[t._v(t._s(t.$lang("address"))),a("v-uni-text",[t._v("*")])],1),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"text","placeholder-class":"placeholder-class",placeholder:t.$lang("addressPlaceholder"),maxlength:"50"},model:{value:t.formData.address,callback:function(e){t.$set(t.formData,"address",e)},expression:"formData.address"}})],1)]],2),a("v-uni-view",{staticClass:"identify-area"},[a("v-uni-view",{staticClass:"tip"},[t._v("智能识别地址")]),a("v-uni-view",{staticClass:"paste-address"},[a("v-uni-view",{staticClass:"sample-area"},[t._v("示例：山西省太原市小店区**路**号")]),a("v-uni-view",{staticClass:"intelligent-identify"},[a("v-uni-textarea",{staticClass:"input-addr",attrs:{placeholder:"粘贴或输入文本，智能识别姓名、电话和地址",name:"",id:"",cols:"30",rows:"10"},model:{value:t.pasteAddress,callback:function(e){t.pasteAddress=e},expression:"pasteAddress"}}),a("v-uni-view",{staticClass:"action-area"},[a("v-uni-view",{staticClass:"identify-btn color-base-bg",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.identifyAddr()}}},[t._v("识别")])],1)],1)],1)],1),a("v-uni-view",{staticClass:"btn"},[a("v-uni-button",{staticClass:"add",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveAddress.apply(void 0,arguments)}}},[t._v(t._s(t.$lang("save")))])],1),a("loading-cover",{ref:"loadingCover"})],1)],1)},d=[]},4005:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5c47"),a("af8f"),a("0c26"),a("aa9c");var s=i(a("04c1")),d=i(a("fe8d")),r=i(a("2f8f")),n={components:{pickRegions:s.default},data:function(){return{formData:{id:0,name:"",mobile:"",telephone:"",province_id:"",city_id:"",district_id:"",community_id:"",address:"",full_address:"",house:"",latitude:0,longitude:0,is_default:1},pasteAddress:"",address:"",addressValue:"",back:"",redirect:"redirectTo",flag:!1,defaultRegions:[],localType:1,isEdit:!1,webSign:!1}},onLoad:function(t){if(t.back&&(this.back=t.back),t.redirect&&(this.redirect=t.redirect),t.type&&(this.localType=t.type),t.id&&!t.name)this.formData.id=t.id,this.getAddressDetail();else if(t.name){this.isEdit=!0,this.webSign=!0,uni.getStorageSync("addressInfo")&&(this.formData=uni.getStorageSync("addressInfo")),this.formData.address=t.name,this.localType=2,this.getAddress(t.latng);var e=this.getQueryVariable("latng").split(",");this.formData.latitude=e[0],this.formData.longitude=e[1],this.formData.house=""}else this.$refs.loadingCover&&this.$refs.loadingCover.hide()},onBackPress:function(){uni.setStorageSync("addressInfo","")},onShow:function(){this.formData.id?uni.setNavigationBarTitle({title:"编辑收货地址"}):uni.setNavigationBarTitle({title:"新增收货地址"})},onReady:function(){this.$refs.loadingCover.hide()},onHide:function(){this.flag=!1},methods:{identifyAddr:function(){var t=this;this.pasteAddress?this.$api.sendRequest({url:"/api/address/analysesAddress",data:{address:this.pasteAddress},success:function(e){e.code>=0?(t.formData.address=e.data.detail,t.formData.full_address=e.data.province_name+"-"+e.data.district_name+"-"+e.data.city_name,t.addressValue=e.data.province_id+"-"+e.data.district_id+"-"+e.data.city_id):t.$util.showToast({title:e.message})},fail:function(e){t.$util.showToast({title:e.message})}}):this.$util.showToast({title:"请粘贴或输入文本信息"})},getAddressDetail:function(){var t=this;this.$api.sendRequest({url:"/api/memberaddress/info",data:{id:this.formData.id},success:function(e){var a=e.data;null!=a&&(t.formData.name=a.name,t.formData.mobile=a.mobile,t.formData.telephone=a.telephone,t.formData.address=a.address,t.formData.full_address=a.full_address,t.formData.latitude=a.latitude,t.formData.longitude=a.longitude,t.formData.is_default=a.is_default,t.localType=a.type,t.defaultRegions=[a.province_id,a.city_id,a.district_id],t.addressValue+=void 0!=a.province_id?a.province_id:"",t.addressValue+=void 0!=a.city_id?"-"+a.city_id:"",t.addressValue+=void 0!=a.district_id?"-"+a.district_id:""),t.$refs.loadingCover&&t.$refs.loadingCover.hide()},fail:function(e){t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},getAddress:function(t){var e=this;this.$api.sendRequest({url:"/api/memberaddress/tranAddressInfo",data:{latlng:t},success:function(t){0==t.code?(e.formData.full_address="",e.formData.full_address+=void 0!=t.data.province?t.data.province:"",e.formData.full_address+=void 0!=t.data.city?"-"+t.data.city:"",e.formData.full_address+=void 0!=t.data.district?"-"+t.data.district:"",e.addressValue="",e.addressValue+=void 0!=t.data.province_id?t.data.province_id:"",e.addressValue+=void 0!=t.data.city_id?"-"+t.data.city_id:"",e.addressValue+=void 0!=t.data.district_id?"-"+t.data.district_id:""):e.showToast({title:"数据有误"})}})},handleGetRegions:function(t){this.formData.full_address="",this.formData.full_address+=void 0!=t[0]?t[0].label:"",this.formData.full_address+=void 0!=t[1]?"-"+t[1].label:"",this.formData.full_address+=void 0!=t[2]?"-"+t[2].label:"",this.addressValue="",this.addressValue+=void 0!=t[0]?t[0].value:"",this.addressValue+=void 0!=t[1]?"-"+t[1].value:"",this.addressValue+=void 0!=t[2]?"-"+t[2].value:""},selectAddress:function(){var t=this.formData;uni.setStorageSync("addressInfo",t);var e=r.default.h5Domain+"/pages_tool/member/address_edit?type="+this.localType;this.formData.id&&(e+="&id="+this.formData.id),this.back&&(e+="&back="+this.back),window.location.href="https://apis.map.qq.com/tools/locpicker?search=1&type=0&backurl="+encodeURIComponent(e)+"&key="+r.default.mpKey+"&referer=myapp"},getQueryVariable:function(t){for(var e=window.location.search.substring(1),a=e.split("&"),i=0;i<a.length;i++){var s=a[i].split("=");if(s[0]==t)return s[1]}return!1},vertify:function(){this.formData.name=this.formData.name.trim(),this.formData.mobile=this.formData.mobile.trim(),this.formData.address=this.formData.address.trim();var t=[{name:"name",checkType:"required",errorMsg:"请输入姓名"},{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"},{name:"full_address",checkType:"required",errorMsg:"请选择省市区县"},{name:"address",checkType:"required",errorMsg:"详细地址不能为空"}];this.isEdit&&t.push({name:"house",checkType:"required",errorMsg:"门牌不能为空"});var e=d.default.check(this.formData,t);return!!e||(this.$util.showToast({title:d.default.error}),this.flag=!1,!1)},saveAddress:function(){var t=this;if(!this.flag&&(this.flag=!0,this.vertify())){var e=this.addressValue.split("-"),a={},i="";a={name:this.formData.name,mobile:this.formData.mobile,telephone:this.formData.telephone,province_id:e[0],city_id:e[1],district_id:e[2]?e[2]:"",community_id:0,address:this.isEdit?this.formData.address+this.formData.house:this.formData.address,full_address:this.formData.full_address,latitude:this.formData.latitude,longitude:this.formData.longitude,is_default:this.formData.is_default,type:this.localType},i="add",this.formData.id&&(i="edit",a.id=this.formData.id,""!=this.back&&(a.is_default=1)),this.$api.sendRequest({url:"/api/memberaddress/"+i,data:a,success:function(e){if(t.flag=!1,0==e.code){if(""!=t.back){if(console.log(t.webSign),t.webSign)return void window.history.go(-3);uni.navigateBack({delta:2})}else t.$util.showToast({title:e.message}),uni.navigateBack({delta:1});uni.removeStorageSync("addressInfo")}else t.$util.showToast({title:e.message})},fail:function(e){t.flag=!1}})}}}};e.default=n},"466ab":function(t,e,a){"use strict";a.r(e);var i=a("34f8"),s=a("a921");for(var d in s)["default"].indexOf(d)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(d);a("f242");var r=a("828b"),n=Object(r["a"])(s["default"],i["b"],i["c"],!1,null,"85fbcb14",null,!1,i["a"],void 0);e["default"]=n.exports},"75d4":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-85fbcb14] pick-regions,\r\n.pick-regions[data-v-85fbcb14]{flex:1}.identify-area .tip[data-v-85fbcb14]{padding:%?20?% %?30?% %?10?%;background-color:#f8f8f8;color:#909399}.identify-area .paste-address[data-v-85fbcb14]{margin:0 %?30?%}.identify-area .paste-address .sample-area[data-v-85fbcb14]{background-color:#f8ecc5;color:#7d5329;font-size:%?24?%;line-height:%?32?%;padding:%?16?% %?30?% %?60?%;border-radius:%?30?% %?30?% 0 0}.identify-area .paste-address .intelligent-identify[data-v-85fbcb14]{margin-top:%?-44?%;background-color:#fff;border-radius:%?30?%;padding:%?30?%}.identify-area .paste-address .intelligent-identify .action-area[data-v-85fbcb14]{display:flex;align-items:center;justify-content:flex-end}.identify-area .paste-address .intelligent-identify .action-area .identify-btn[data-v-85fbcb14]{color:#fff;font-size:%?26?%;line-height:%?30?%;border-radius:%?30?%;padding:%?12?% %?20?%}.identify-area .paste-address .intelligent-identify .input-addr[data-v-85fbcb14]{width:100%;height:%?200?%;color:#333;font-size:%?24?%}.edit-wrap[data-v-85fbcb14]{background:#fff;overflow:hidden}.edit-wrap .tip[data-v-85fbcb14]{padding:%?20?% %?30?% %?10?%;background-color:#f8f8f8;color:#909399}.edit-item[data-v-85fbcb14]{display:flex;align-items:center;margin:0 %?30?%;min-height:%?100?%;background-color:#fff}.edit-item .text_inp[data-v-85fbcb14]{margin-left:%?20?%;flex:1}.edit-item .tit[data-v-85fbcb14]{width:%?148?%}.edit-item .tit uni-text[data-v-85fbcb14]{margin-left:%?10?%;color:#ff4544}.edit-item .tit.margin_tit[data-v-85fbcb14]{align-self:flex-start;margin-top:%?24?%}.edit-item .icon-location[data-v-85fbcb14]{color:#606266;align-self:flex-start;margin-top:%?20?%}.edit-item .select-address[data-v-85fbcb14]{display:block;margin-left:%?10?%}.edit-item .select-address.empty[data-v-85fbcb14]{color:grey}.edit-item uni-textarea[data-v-85fbcb14],\r\n.edit-item uni-input[data-v-85fbcb14]{flex:1;font-size:%?28?%;margin-left:%?20?%;padding:0}.edit-item uni-textarea[data-v-85fbcb14]{margin-top:%?6?%;height:%?100?%;padding-bottom:%?20?%;padding-top:%?20?%;line-height:%?50?%}.edit-wrap > .edit-item + .edit-item[data-v-85fbcb14]{border-top:%?2?% solid #ebedf0}.add[data-v-85fbcb14]{margin-top:%?60?%;height:%?80?%;line-height:%?80?%!important;border-radius:%?80?%;font-weight:500;width:calc(100% - %?60?%);margin-left:%?30?%;font-size:%?32?%}.btn[data-v-85fbcb14]{position:fixed;width:100%;bottom:%?30?%;height:auto;padding-bottom:constant(safe-area-inset-bottom);\r\n  /*兼容 IOS<11.2*/padding-bottom:env(safe-area-inset-bottom)\r\n  /*兼容 IOS>11.2*/}',""]),t.exports=e},"9f16":function(t,e,a){var i=a("75d4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var s=a("967d").default;s("4ee3df92",i,!0,{sourceMap:!1,shadowMode:!1})},a921:function(t,e,a){"use strict";a.r(e);var i=a("4005"),s=a.n(i);for(var d in i)["default"].indexOf(d)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(d);e["default"]=s.a},f242:function(t,e,a){"use strict";var i=a("9f16"),s=a.n(i);s.a}}]);