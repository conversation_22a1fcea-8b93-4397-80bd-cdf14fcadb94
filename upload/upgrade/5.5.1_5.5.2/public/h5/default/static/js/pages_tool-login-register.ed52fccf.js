(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-login-register"],{"0817":function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2"),a("5c47"),a("2c10"),a("a1c1"),a("23f4"),a("7d2f"),a("9c4e"),a("ab80"),a("aa9c"),a("473f"),a("bf0f"),a("3efd");var o=i(a("af87")),n=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,r=/^<\/([-A-Za-z0-9_]+)[^>]*>/,s=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,c=f("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),l=f("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),d=f("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),u=f("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),p=f("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),h=f("script,style");function f(e){for(var t={},a=e.split(","),i=0;i<a.length;i++)t[a[i]]=!0;return t}var g=function(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e),e=function(e){return e=e.replace(/<!--[\s\S]*-->/gi,""),e}(e),e=function(e){var t='<img style="width:100% !important;display:block;max-width: '.concat("100%",' !important;"');return e=e.replace(/\\/g,"").replace(/<img/g,t),e=e.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(e,a){return t+' src="'+o.default.img(a)+'"/>'})),e}(e),e=function(e){return e=e.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(e,t){return e=e.replace(/[:](\s?)[\s\S]*/gi,(function(e,t){return e.replace(/"/g,"'")})),e})),e}(e);var t=[],a={node:"root",children:[]};return function(e,t){var a,i,o,f=[],g=e;f.last=function(){return this[this.length-1]};while(e){if(i=!0,f.last()&&h[f.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+f.last()+"[^>]*>"),(function(e,a){return a=a.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(a),""})),b("",f.last());else if(0==e.indexOf("\x3c!--")?(a=e.indexOf("--\x3e"),a>=0&&(t.comment&&t.comment(e.substring(4,a)),e=e.substring(a+3),i=!1)):0==e.indexOf("</")?(o=e.match(r),o&&(e=e.substring(o[0].length),o[0].replace(r,b),i=!1)):0==e.indexOf("<")&&(o=e.match(n),o&&(e=e.substring(o[0].length),o[0].replace(n,m),i=!1)),i){a=e.indexOf("<");var v=a<0?e:e.substring(0,a);e=a<0?"":e.substring(a),t.chars&&t.chars(v)}if(e==g)throw"Parse Error: "+e;g=e}function m(e,a,i,o){if(a=a.toLowerCase(),l[a])while(f.last()&&d[f.last()])b("",f.last());if(u[a]&&f.last()==a&&b("",a),o=c[a]||!!o,o||f.push(a),t.start){var n=[];i.replace(s,(function(e,t){var a=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:p[t]?t:"";n.push({name:t,value:a,escaped:a.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(a,n,o)}}function b(e,a){if(a){for(i=f.length-1;i>=0;i--)if(f[i]==a)break}else var i=0;if(i>=0){for(var o=f.length-1;o>=i;o--)t.end&&t.end(f[o]);f.length=i}}b()}(e,{start:function(e,i,o){var n={name:e};if(0!==i.length&&(n.attrs=function(e){return e.reduce((function(e,t){var a=t.value,i=t.name;return e[i]?e[i]=e[i]+" "+a:e[i]=a,e}),{})}(i)),o){var r=t[0]||a;r.children||(r.children=[]),r.children.push(n)}else t.unshift(n)},end:function(e){var i=t.shift();if(i.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)a.children.push(i);else{var o=t[0];o.children||(o.children=[]),o.children.push(i)}},chars:function(e){var i={type:"text",text:e};if(0===t.length)a.children.push(i);else{var o=t[0];o.children||(o.children=[]),o.children.push(i)}},comment:function(e){var a={node:"comment",text:e},i=t[0];i.children||(i.children=[]),i.children.push(a)}}),a.children};t.default=g},2646:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.conten-box[data-v-5427b115]{padding:0 %?30?% %?30?%}.conten-box .title[data-v-5427b115]{text-align:center;margin-top:%?50?%;margin-bottom:%?10?%}.conten-box .close[data-v-5427b115]{position:absolute;right:%?30?%;top:%?10?%}.conten-box .con[data-v-5427b115]{height:%?500?%}[data-v-5427b115] .reward-popup .uni-popup__wrapper-box{background:none!important;max-width:unset!important;max-height:unset!important;overflow:unset!important}',""]),e.exports=t},"2d00":function(e,t,a){"use strict";a.r(t);var i=a("4501"),o=a("f946");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("b48a"),a("c0d7");var r=a("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"5427b115",null,!1,i["a"],void 0);t["default"]=s.exports},4501:function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return i}));var i={pageMeta:a("7854").default,uniPopup:a("d745").default,nsMpHtml:a("d108").default,loadingCover:a("c003").default,registerReward:a("349c").default},o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[a("page-meta",{attrs:{"page-style":e.themeColor}}),a("v-uni-scroll-view",{staticClass:"container",attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"header-wrap"},[a("v-uni-view",{staticClass:"title"},[e._v("注册")]),a("v-uni-view",{staticClass:"regisiter-agreement"},[a("v-uni-text",{staticClass:"color-tip"},[e._v("已有账号,")]),a("v-uni-text",{staticClass:"color-base-text",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toLogin.apply(void 0,arguments)}}},[e._v("立即登录")])],1)],1),a("v-uni-view",{staticClass:"body-wrap"},[a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"mobile"==e.registerMode,expression:"registerMode == 'mobile'"}],staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"area-code"},[e._v("+86")]),a("v-uni-input",{staticClass:"input",attrs:{type:"number",placeholder:"仅限中国大陆手机号注册","placeholder-class":"input-placeholder",maxlength:"11"},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}})],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"account"==e.registerMode,expression:"registerMode == 'account'"}],staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-input",{staticClass:"input",attrs:{type:"text",placeholder:"请输入账号","placeholder-class":"input-placeholder"},model:{value:e.formData.account,callback:function(t){e.$set(e.formData,"account",t)},expression:"formData.account"}})],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"account"==e.registerMode,expression:"registerMode == 'account'"}],staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-input",{staticClass:"input",attrs:{type:"password",placeholder:"请输入密码","placeholder-class":"input-placeholder"},model:{value:e.formData.password,callback:function(t){e.$set(e.formData,"password",t)},expression:"formData.password"}})],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"account"==e.registerMode,expression:"registerMode == 'account'"}],staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-input",{staticClass:"input",attrs:{type:"password",placeholder:"请确认密码","placeholder-class":"input-placeholder"},model:{value:e.formData.rePassword,callback:function(t){e.$set(e.formData,"rePassword",t)},expression:"formData.rePassword"}})],1)],1),e.isOpenCaptcha?a("v-uni-view",{staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-input",{staticClass:"input",attrs:{type:"text",placeholder:"请输入验证码","placeholder-class":"input-placeholder"},model:{value:e.formData.vercode,callback:function(t){e.$set(e.formData,"vercode",t)},expression:"formData.vercode"}}),a("v-uni-image",{staticClass:"captcha",attrs:{src:e.captcha.img},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getCaptcha.apply(void 0,arguments)}}})],1)],1):e._e(),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"mobile"==e.registerMode,expression:"registerMode == 'mobile'"}],staticClass:"input-wrap"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-input",{staticClass:"input",attrs:{type:"text",placeholder:"请输入动态码","placeholder-class":"input-placeholder"},model:{value:e.formData.dynacode,callback:function(t){e.$set(e.formData,"dynacode",t)},expression:"formData.dynacode"}}),a("v-uni-view",{staticClass:"dynacode",class:120==e.dynacodeData.seconds?"color-base-text":"color-tip",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendMobileCode.apply(void 0,arguments)}}},[e._v(e._s(e.dynacodeData.codeText))])],1)],1)],1),a("v-uni-view",{staticClass:"login-mode-box"},[a("v-uni-text",{directives:[{name:"show",rawName:"v-show",value:"mobile"==e.registerMode&&-1!=e.registerConfig.register.indexOf("username"),expression:"registerMode == 'mobile' && registerConfig.register.indexOf('username') != -1"}],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.switchRegisterMode.apply(void 0,arguments)}}},[e._v("使用用户名注册")]),a("v-uni-text",{directives:[{name:"show",rawName:"v-show",value:"account"==e.registerMode&&-1!=e.registerConfig.register.indexOf("mobile"),expression:"registerMode == 'account' && registerConfig.register.indexOf('mobile') != -1"}],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.switchRegisterMode.apply(void 0,arguments)}}},[e._v("使用手机号注册")])],1),a("v-uni-view",{staticClass:"btn_view"},[a("v-uni-button",{staticClass:"login-btn color-base-border color-base-bg",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.register.apply(void 0,arguments)}}},[e._v("注册")])],1),e.registerConfig.agreement_show?a("v-uni-view",{staticClass:"regisiter-agreement"},[a("v-uni-text",{staticClass:"iconfont is-agree",class:e.isAgree?"icon-yuan_checked color-base-text":"icon-yuan_checkbox",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isAgree=!e.isAgree}}}),a("v-uni-text",{staticClass:"tips"},[e._v("请阅读并同意")]),a("v-uni-text",{staticClass:"color-base-text",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toAggrement("PRIVACY")}}},[e._v("《隐私协议》")]),a("v-uni-text",{staticClass:"tips"},[e._v("和")]),a("v-uni-text",{staticClass:"color-base-text",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toAggrement("SERVICE")}}},[e._v("《用户协议》")])],1):e._e()],1),a("v-uni-view",{on:{touchmove:function(t){t.preventDefault(),arguments[0]=t=e.$handleEvent(t)}}},[a("uni-popup",{ref:"registerPopup",attrs:{type:"center",maskClick:!1}},[a("v-uni-view",{staticClass:"conten-box"},[a("v-uni-view",{staticClass:"close"},[a("v-uni-text",{staticClass:"iconfont icon-close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toClose.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"title"},[e._v(e._s(e.agreement.title))]),a("v-uni-view",{staticClass:"con"},[a("v-uni-scroll-view",{staticClass:"con",attrs:{"scroll-y":"true"}},[a("ns-mp-html",{attrs:{content:e.agreement.content}})],1)],1)],1)],1)],1),a("loading-cover",{ref:"loadingCover"}),a("register-reward",{ref:"registerReward"})],1)],1)},n=[]},7854:function(e,t,a){"use strict";a.r(t);var i=a("8ba8"),o=a("f48d");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);var r=a("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=s.exports},"8ba8":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){}));var i=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},o=[]},"97aa7":function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2"),a("5c47"),a("a1c1"),a("aa9c"),a("0506");var o=i(a("d745")),n=i(a("fe8d")),r=i(a("349c")),s=(i(a("0817")),{components:{uniPopup:o.default,registerReward:r.default},data:function(){return{allowRegister:!0,registerMode:"account",formData:{mobile:"",account:"",password:"",rePassword:"",vercode:"",dynacode:"",key:""},agreement:{title:"",content:""},captcha:{id:"",img:""},isSub:!1,back:"",dynacodeData:{seconds:120,timer:null,codeText:"获取动态码",isSend:!1},registerConfig:{register:""},authInfo:null,isAgree:!1,isOpenCaptcha:0}},onLoad:function(e){e.back&&(this.back=e.back),this.getCaptchaConfig(),this.getRegisiterAggrement(),this.getRegisterConfig(),this.authInfo=uni.getStorageSync("authInfo")},onShow:function(){},onReady:function(){this.$refs.loadingCover&&this.$refs.loadingCover.hide()},methods:{toAggrement:function(e){this.$util.redirectTo("/pages_tool/login/aggrement",{type:e})},switchRegisterMode:function(){this.registerMode="mobile"==this.registerMode?"account":"mobile"},openPopup:function(){this.$refs.registerPopup.open()},toClose:function(){this.$refs.registerPopup.close()},getRegisiterAggrement:function(){var e=this;this.$api.sendRequest({url:"/api/register/aggrement",success:function(t){t.code>=0&&(e.agreement=t.data)}})},getRegisterConfig:function(){var e=this;this.$api.sendRequest({url:"/api/register/config",success:function(t){t.code>=0&&(e.registerConfig=t.data.value,""==e.registerConfig.register?(e.$util.showToast({title:"平台未启用注册!"}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),1e3)):-1!=e.registerConfig.register.indexOf("username")?e.registerMode="account":e.registerMode="mobile",e.$refs.loadingCover&&e.$refs.loadingCover.hide())}})},getCaptchaConfig:function(){var e=this;this.$api.sendRequest({url:"/api/config/getCaptchaConfig",success:function(t){t.code>=0&&(e.isOpenCaptcha=t.data.shop_reception_register,1==e.isOpenCaptcha&&e.getCaptcha())}})},getCaptcha:function(){var e=this;0!=this.isOpenCaptcha&&this.$api.sendRequest({url:"/api/captcha/captcha",data:{captcha_id:this.captcha.id},success:function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))}})},register:function(){var e=this;if("account"==this.registerMode){var t="/api/register/username";a={username:this.formData.account,password:this.formData.password}}else{t="/api/register/mobile";var a={mobile:this.formData.mobile,key:this.formData.key,code:this.formData.dynacode}}if(1==this.isOpenCaptcha&&""!=this.captcha.id&&(a.captcha_id=this.captcha.id,a.captcha_code=this.formData.vercode),this.authInfo&&(Object.assign(a,this.authInfo),this.authInfo.nickName&&(a.nickname=this.authInfo.nickName),this.authInfo.avatarUrl&&(a.headimg=this.authInfo.avatarUrl)),uni.getStorageSync("source_member")&&(a.source_member=uni.getStorageSync("source_member")),this.verify(a)){if(this.isSub)return;this.isSub=!0,this.$api.sendRequest({url:t,data:a,success:function(t){t.code>=0?(e.$store.commit("setToken",t.data.token),e.$store.dispatch("getCartNumber"),e.getMemberInfo((function(){e.$util.showToast({title:"注册成功"});var t=e.back?e.back:"/pages/member/index";e.$store.commit("setCanReceiveRegistergiftInfo",{status:!0,path:e.$util.openRegisterRewardPath(t)}),e.$util.loginComplete(t,"redirectTo")}))):(e.isSub=!1,e.getCaptcha(),e.$util.showToast({title:t.message}))},fail:function(t){e.isSub=!1,e.getCaptcha()}})}},verify:function(e){if(this.registerConfig.agreement_show&&!this.isAgree)return this.$util.showToast({title:"请先阅读并同意协议"}),!1;if("mobile"==this.registerMode){var t=[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}];1==this.isOpenCaptcha&&""!=this.captcha.id&&t.push({name:"captcha_code",checkType:"required",errorMsg:this.$lang("captchaPlaceholder")}),t.push({name:"code",checkType:"required",errorMsg:this.$lang("dynacodePlaceholder")})}if("account"==this.registerMode){t=[{name:"username",checkType:"required",errorMsg:"请输入账号"},{name:"password",checkType:"required",errorMsg:"请输入密码"}];var a=this.registerConfig;if(!/^[A-Za-z0-9]+$/.test(e.username))return void this.$util.showToast({title:"用户名只能输入数字跟英文"});if(a.pwd_len>0&&t.push({name:"password",checkType:"lengthMin",checkRule:a.pwd_len,errorMsg:"密码长度不能小于"+a.pwd_len+"位"}),""!=a.pwd_complexity){var i="密码需包含",o="";-1!=a.pwd_complexity.indexOf("number")&&(o+="(?=.*?[0-9])",i+="数字"),-1!=a.pwd_complexity.indexOf("letter")&&(o+="(?=.*?[a-z])",i+="、小写字母"),-1!=a.pwd_complexity.indexOf("upper_case")&&(o+="(?=.*?[A-Z])",i+="、大写字母"),-1!=a.pwd_complexity.indexOf("symbol")&&(o+="(?=.*?[#?!@$%^&*-])",i+="、特殊字符"),t.push({name:"password",checkType:"reg",checkRule:o,errorMsg:i})}if(this.formData.password!=this.formData.rePassword)return this.$util.showToast({title:"两次密码不一致"}),!1;1==this.isOpenCaptcha&&""!=this.captcha.id&&t.push({name:"captcha_code",checkType:"required",errorMsg:this.$lang("captchaPlaceholder")})}var r=n.default.check(e,t);return!!r||(this.$util.showToast({title:n.default.error}),!1)},toLogin:function(){this.back?this.$util.redirectTo("/pages_tool/login/index",{back:encodeURIComponent(this.back)}):this.$util.redirectTo("/pages_tool/login/index")},sendMobileCode:function(){var e=this;if(120==this.dynacodeData.seconds&&!this.dynacodeData.isSend){var t={mobile:this.formData.mobile};1==this.isOpenCaptcha&&""!=this.captcha.id&&(t.captcha_id=this.captcha.id,t.captcha_code=this.formData.vercode);var a=[{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}];1==this.isOpenCaptcha&&""!=this.captcha.id&&a.push({name:"captcha_code",checkType:"required",errorMsg:"请输入验证码"});var i=n.default.check(t,a);i?(this.dynacodeData.isSend=!0,this.dynacodeData.timer=setInterval((function(){e.dynacodeData.seconds--,e.dynacodeData.codeText=e.dynacodeData.seconds+"s后可重新获取"}),1e3),this.$api.sendRequest({url:"/api/register/mobileCode",data:t,success:function(t){t.code>=0?e.formData.key=t.data.key:(e.$util.showToast({title:t.message}),e.refreshDynacodeData())},fail:function(){e.$util.showToast({title:"request:fail"}),e.refreshDynacodeData()}})):this.$util.showToast({title:n.default.error})}},refreshDynacodeData:function(){this.getCaptcha(),clearInterval(this.dynacodeData.timer),this.dynacodeData={seconds:120,timer:null,codeText:"获取动态码",isSend:!1}},getMemberInfo:function(e){var t=this;this.$api.sendRequest({url:"/api/member/info",success:function(a){a.code>=0&&(t.$store.commit("setMemberInfo",a.data),e&&e())}})}},watch:{"dynacodeData.seconds":{handler:function(e,t){0==e&&this.refreshDynacodeData()},immediate:!0,deep:!0}}});t.default=s},"9e31":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-5427b115] .uni-scroll-view{background-color:#fff}[data-v-5427b115] .uni-scroll-view::-webkit-scrollbar{\r\n  /* 隐藏滚动条，但依旧具备可以滚动的功能 */display:none}uni-page-body[data-v-5427b115]{width:100%;background:#fff!important}body.?%PAGE?%[data-v-5427b115]{background:#fff!important}.align-right[data-v-5427b115]{color:#838383}.container[data-v-5427b115]{width:100vw;height:100vh}.header-wrap[data-v-5427b115]{width:80%;margin:calc(%?120?% + 44px) auto 0;background-repeat:no-repeat;background-size:contain;background-position:bottom;position:relative}.header-wrap .title[data-v-5427b115]{font-size:%?60?%;font-weight:700}.body-wrap[data-v-5427b115]{margin-top:%?100?%;padding-bottom:%?100?%}.body-wrap .form-wrap[data-v-5427b115]{width:80%;margin:0 auto}.body-wrap .form-wrap .input-wrap[data-v-5427b115]{position:relative;width:100%;box-sizing:border-box;height:%?60?%;margin-top:%?60?%}.body-wrap .form-wrap .input-wrap .iconfont[data-v-5427b115]{width:%?60?%;height:%?60?%;position:absolute;left:0;right:0;line-height:%?60?%;font-size:%?32?%;color:#303133;font-weight:600}.body-wrap .form-wrap .input-wrap .content[data-v-5427b115]{display:flex;height:%?60?%;border-bottom:%?2?% solid #eee;align-items:center}.body-wrap .form-wrap .input-wrap .content .input[data-v-5427b115]{flex:1;height:%?60?%;line-height:%?60?%;font-size:%?28?%}.body-wrap .form-wrap .input-wrap .content .input-placeholder[data-v-5427b115]{font-size:%?28?%;color:#bfbfbf;line-height:%?60?%}.body-wrap .form-wrap .input-wrap .content .captcha[data-v-5427b115]{margin:%?4?%;height:%?52?%;width:%?140?%}.body-wrap .form-wrap .input-wrap .content .dynacode[data-v-5427b115]{line-height:%?60?%;font-size:%?24?%}.body-wrap .form-wrap .input-wrap .content .area-code[data-v-5427b115]{line-height:%?60?%;margin-right:%?20?%;font-size:%?28?%}.body-wrap .forget-section[data-v-5427b115]{display:flex;width:80%;margin:%?40?% auto}.body-wrap .forget-section uni-view[data-v-5427b115]{flex:1;font-size:%?24?%;line-height:1}.body-wrap .btn_view[data-v-5427b115]{width:100%;margin:%?94?% auto auto;padding:0 %?30?%;box-sizing:border-box}.body-wrap .login-btn[data-v-5427b115]{height:%?90?%;line-height:%?90?%;border-radius:%?90?%;text-align:center;border:%?2?% solid;width:100%;margin:0}.body-wrap .auth-login[data-v-5427b115]{margin-top:%?20?%;width:calc(100% - %?4?%);height:%?90?%;line-height:%?90?%;border-radius:%?10?%;border:%?2?% solid;color:#fff;text-align:center;margin-left:0;background-color:#fff}.body-wrap .auth-login uni-text[data-v-5427b115]{color:#d0d0d0}.body-wrap .auth-login .iconfont[data-v-5427b115]{font-size:%?70?%}.body-wrap .auth-login .icon-weixin[data-v-5427b115]{color:#1aad19}.body-wrap .regisiter-agreement[data-v-5427b115]{text-align:center;margin-top:%?30?%;color:#838383;line-height:%?60?%;font-size:%?24?%}.body-wrap .regisiter-agreement .tips[data-v-5427b115]{margin:0 %?10?%}.body-wrap .regisiter-agreement .is-agree[data-v-5427b115]{font-size:%?26?%}.login-btn-box[data-v-5427b115]{margin-top:%?50?%}.login-btn-box.active[data-v-5427b115]{margin:%?30?% 0 %?50?%}.back-btn[data-v-5427b115]{font-size:%?52?%;position:fixed;left:%?24?%;top:%?72?%;z-index:9;color:#000}.login-mode-box[data-v-5427b115]{display:flex;justify-content:flex-end;color:#909399;margin:auto;margin-top:%?44?%;font-size:%?26?%;width:80%}.auth-index[data-v-5427b115]{width:100vw;height:100vh;box-sizing:border-box;padding:0 %?44?%}.auth-index .website-logo[data-v-5427b115]{padding-top:%?154?%;display:flex;justify-content:center}.auth-index .website-logo .logo[data-v-5427b115]{width:%?300?%;height:%?90?%;display:block}.auth-index .login-desc[data-v-5427b115]{color:#333;font-size:%?28?%;text-align:center;line-height:%?34?%;min-height:%?34?%;margin-top:%?40?%}.auth-index .login-area[data-v-5427b115]{margin-top:%?181?%;display:flex;flex-direction:column;align-items:center}.auth-index .login-area .btn[data-v-5427b115]{background-color:#fff;border:%?2?% solid var(--base-color);color:var(--base-color);box-sizing:border-box;width:%?630?%;height:%?88?%;font-size:%?26?%;border-radius:%?44?%;line-height:%?86?%;font-weight:500;text-align:center;margin-bottom:%?40?%}.auth-index .login-area .btn.quick-login[data-v-5427b115]{color:#fff;background-color:var(--base-color)}.auth-index .login-area .agreement[data-v-5427b115]{display:flex;align-items:center;justify-content:center;width:100%;margin-top:%?28?%;padding:%?10?% 0;font-size:%?24?%;line-height:1}.auth-index .login-area .agreement .agree[data-v-5427b115]{color:#c8c9cc;font-size:%?26?%;line-height:%?22?%;margin-right:%?12?%}.auth-index .login-area .agreement .tips-text[data-v-5427b115]{display:flex;align-items:center;line-height:%?28?%;font-size:%?24?%}.auth-index .login-area .agreement .tips-text .tips[data-v-5427b115]{color:#666}.auth-index .login-area .footer[data-v-5427b115]{margin-top:%?200?%;width:100%;box-sizing:border-box;display:flex;flex-direction:column;align-items:center}.auth-index .login-area .footer .text[data-v-5427b115]{font-size:%?26?%;line-height:%?36?%;color:#333;text-align:center;margin-bottom:%?30?%;font-weight:400}.auth-index .login-area .footer .mine[data-v-5427b115]{width:%?80?%;height:%?80?%;line-height:%?78?%;border:%?2?% solid #ddd;border-radius:50%;font-size:%?46?%;display:flex;align-items:center;justify-content:center;color:var(--base-color)}.auth-index .login-area .footer .mode-name[data-v-5427b115]{font-size:%?24?%;line-height:%?36?%;color:#999;font-weight:400;margin-top:%?30?%}',""]),e.exports=t},b48a:function(e,t,a){"use strict";var i=a("e735"),o=a.n(i);o.a},c0d7:function(e,t,a){"use strict";var i=a("faa3"),o=a.n(i);o.a},cc1b:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("5ef2"),a("64aa"),a("5c47"),a("a1c1"),a("e838");var i={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},o={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(e){return-1!==["dark","light"].indexOf(e)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var e=this,t=getCurrentPages()[0];this.$pageVm=t.$vm||t,uni.onWindowResize((function(t){e.$emit("resize",t)})),this.$pageVm.$on("hook:onPageScroll",(function(t){e.$emit("scroll",t)})),this.$watch("backgroundTextStyle",(function(){e.setBackgroundTextStyle()})),this.$watch((function(){return[e.rootFontSize,e.pageStyle]}),(function(){e.setPageMeta()})),this.$watch((function(){return[e.backgroundColor,e.backgroundColorTop,e.backgroundColorBottom]}),(function(){e.setBackgroundColor()})),this.$watch((function(){return[e.scrollTop,e.scrollDuration]}),(function(){e.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(e,t){e.setStyle({pullToRefresh:{support:t,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var e=this,t=String(this.scrollTop);if(-1!==t.indexOf("rpx")&&(t=uni.upx2px(t.replace("rpx",""))),t=parseFloat(t),!isNaN(t)){var a=function a(o){o.scrollTop===t&&(e.$pageVm.$off("hook:onPageScroll",a),e.$emit("scrolldone",i))};uni.pageScrollTo({scrollTop:t,duration:this.scrollDuration,success:function(){e.$pageVm.$on("hook:onPageScroll",a)}})}}}};t.default=o},e735:function(e,t,a){var i=a("9e31");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("566d6f04",i,!0,{sourceMap:!1,shadowMode:!1})},f48d:function(e,t,a){"use strict";a.r(t);var i=a("cc1b"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},f946:function(e,t,a){"use strict";a.r(t);var i=a("97aa7"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},faa3:function(e,t,a){var i=a("2646");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("967d").default;o("eb1c1546",i,!0,{sourceMap:!1,shadowMode:!1})}}]);