(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-category"],{"029e":function(t,e,i){var a=i("2356");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("4f6c6235",a,!0,{sourceMap:!1,shadowMode:!1})},"0379":function(t,e,i){"use strict";i.r(e);var a=i("d82a"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},"12a1":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={diyIcon:i("a68f").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.tabBarList&&t.tabBarList.list?i("v-uni-view",[i("v-uni-view",{staticClass:"tab-bar",style:{backgroundColor:t.tabBarList.backgroundColor}},[i("v-uni-view",{staticClass:"tabbar-border"}),t._l(t.tabBarList.list,(function(e,a){return i("v-uni-view",{key:e.id,staticClass:"item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.redirectTo(e.link)}}},[i("v-uni-view",{staticClass:"bd"},["/pages/goods/cart"==e.link.wap_url?[1==t.tabBarList.type||2==t.tabBarList.type?i("v-uni-view",{staticClass:"icon",attrs:{animation:t.cartAnimation,id:"tabbarCart"}},[t.verify(e.link)?["img"==e.selected_icon_type?i("v-uni-image",{attrs:{src:t.$util.img(e.selectedIconPath)}}):t._e(),"icon"==e.selected_icon_type?i("diy-icon",{attrs:{icon:e.selectedIconPath,value:e.selected_style?e.selected_style:null}}):t._e()]:["img"==e.icon_type?i("v-uni-image",{attrs:{src:t.$util.img(e.iconPath)}}):t._e(),"icon"==e.icon_type?i("diy-icon",{attrs:{icon:e.iconPath,value:e.style?e.style:null}}):t._e()],t.cartNumber>0?i("v-uni-view",{staticClass:"cart-count-mark font-size-activity-tag",class:{max:"/pages/goods/cart"==e.link.wap_url&&t.cartNumber>99},style:{background:"var(--price-color)"}},[t._v(t._s(t.cartNumber>99?"99+":t.cartNumber))]):t._e()],2):t._e()]:[1==t.tabBarList.type||2==t.tabBarList.type?i("v-uni-view",{staticClass:"icon"},[t.verify(e.link)?["img"==e.selected_icon_type?i("v-uni-image",{attrs:{src:t.$util.img(e.selectedIconPath)}}):t._e(),"icon"==e.selected_icon_type?i("diy-icon",{attrs:{icon:e.selectedIconPath,value:e.selected_style?e.selected_style:null}}):t._e()]:["img"==e.icon_type?i("v-uni-image",{attrs:{src:t.$util.img(e.iconPath)}}):t._e(),"icon"==e.icon_type?i("diy-icon",{attrs:{icon:e.iconPath,value:e.style?e.style:null}}):t._e()]],2):t._e()],1!=t.tabBarList.type&&3!=t.tabBarList.type||"diy"!=t.tabBarList.theme?t._e():i("v-uni-view",{staticClass:"label",style:{color:t.verify(e.link)?t.tabBarList.textHoverColor:t.tabBarList.textColor}},[t._v(t._s(e.text))]),1!=t.tabBarList.type&&3!=t.tabBarList.type||"default"!=t.tabBarList.theme?t._e():i("v-uni-view",{staticClass:"label",style:{color:t.verify(e.link)?"var(--base-color)":"#333333"}},[t._v(t._s(e.text))])],2)],1)}))],2),i("v-uni-view",{staticClass:"tab-bar-placeholder"})],1):t._e()},s=[]},2356:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-ff073ca8] .uni-popup__wrapper.uni-center{background:rgba(0,0,0,.6)}[data-v-ff073ca8] .uni-popup__wrapper-box{border-radius:0!important}[data-v-ff073ca8] .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box{overflow-y:visible}[data-v-ff073ca8] .loading-layer{background:#fff!important}[data-v-ff073ca8] .category-template-4 .template-four .uni-popup__wrapper-box{border-radius:0 0 14px 14px!important;overflow:hidden}[data-v-ff073ca8] .category-template-4 .content-wrap .categoty-goods-wrap .goods-list{margin-top:%?30?%}[data-v-ff073ca8] .category-template-4 .content-wrap .goods-list .goods-item .footer-wrap .right-wrap .num-action{width:%?46?%;height:%?46?%}[data-v-ff073ca8] .uni-page-refresh-inner .uni-page-refresh__path{stroke:#010101!important}',""]),t.exports=e},2532:function(t,e,i){"use strict";i.r(e);var a=i("12a1"),o=i("e926");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("7d44");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"09d90d92",null,!1,a["a"],void 0);e["default"]=r.exports},2728:function(t,e,i){var a=i("b7ca");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("904d7006",a,!0,{sourceMap:!1,shadowMode:!1})},"29ac":function(t,e,i){"use strict";i.r(e);var a=i("b791"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},"2c08":function(t,e,i){"use strict";i.r(e);var a=i("9c6c3"),o=i("2ef4");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("d5db");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"3d5ca5bf",null,!1,a["a"],void 0);e["default"]=r.exports},"2cea":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(i("fcf3"));i("5c47"),i("e838"),i("af8f"),i("bf0f"),i("2797"),i("aa9c"),i("d4b5"),i("c223"),i("64aa"),i("c9b5"),i("ab80");var s,n,r,c=a(i("5f0e")),l=(uni.getSystemInfoSync(),{}),d={components:{nsGoodsSkuCategory:c.default},name:"diy-category",props:{value:{type:Object,default:function(){return{}}}},data:function(){return{oneCategorySelect:0,select:0,categoryId:"category-0",categoryTree:null,scrollLock:!1,triggered:!0,heightArea:[],isSub:!1,carIconList:{},endTips:0,cartAnimation:{},loadType:"",templateFourData:[],isIphoneX:!1}},created:function(){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.getCategoryTree(),this.loadType=1==this.value.goodsLevel&&"all"==this.value.loadType?"all":"part"},mounted:function(){var t=this;n=uni.createSelectorQuery().in(this),n.select(".content-wrap").boundingClientRect((function(t){t&&(s=t.height)})).exec(),setTimeout((function(){n.select(".end-tips").boundingClientRect((function(e){e&&e.top>s&&(t.endTips=1)})).exec(),n.select(".cart-icon").boundingClientRect((function(t){t&&(r=t)})).exec(),1==t.value.template&&t.getHeightArea(-1)}),500)},watch:{componentRefresh:function(t){}},computed:{cartTotalMoney:function(){var t=parseFloat(this.cartMoney).toFixed(2);return t.split(".")},navbarHeight:function(){return 44},navbarInnerStyle:function(){var t="";return(4!=this.value.template||4==this.value.template&&!this.value.search)&&(t+="height:"+2*l.height+"rpx;",t+="padding-top:"+this.navbarHeight+"px;",t+="text-align: center;",t+="line-height:"+2*l.height+"rpx;",t+="font-size: 16px;",t+="padding-bottom: 10rpx;"),t},wxSearchHeight:function(){return""},uniPopupTop:function(){return"100rpx","100rpx"}},methods:{pageShow:function(){this.$store.dispatch("getCartNumber"),this.heightArea.length||this.getHeightArea(-1),this.dealCategoryData()},dealCategoryData:function(){var t=this;uni.getStorageSync("tabBarParams")&&(4!=this.value.template?this.categoryTree.forEach((function(e,i){e.category_id==uni.getStorageSync("tabBarParams").split("=")[1]&&(t.select=i,t.categoryId="category-"+i)})):this.templateFourData.forEach((function(e,i){e.category_id==uni.getStorageSync("tabBarParams").split("=")[1]&&(t.oneCategorySelect=i,t.categoryId="category-"+i,t.categoryTree=t.templateFourData[i].child_list||[],t.select=0)})),uni.removeStorageSync("tabBarParams"))},getHeightArea:function(t){var e=[];n.selectAll(".content-wrap .child-category").boundingClientRect((function(t){t&&t.length&&t.forEach((function(t,i){0==i?e.push([0,t.height]):e.push([e[i-1][1],e[i-1][1]+t.height])}))})).exec(),this.heightArea=e,-1!=t&&t<this.categoryTree.length-1&&this.$refs.categoryItem[t+1].getGoodsList(),this.refreshData()},getCategoryTree:function(){var t=this;this.$api.sendRequest({url:"/api/goodscategory/tree",data:{level:3},success:function(e){0==e.code&&(t.categoryTree=e.data,4==t.value.template&&(t.templateFourData=JSON.parse(JSON.stringify(t.categoryTree)),t.categoryTree=t.templateFourData[0].child_list),t.dealCategoryData())}})},switchOneCategory:function(t){t>=this.categoryTree.length||(this.select=t,this.categoryId="category-"+t,this.scrollLock=!0)},touchStart:function(){this.scrollLock=!1},listenScroll:function(t){if(!this.scrollLock){var e=t.detail.scrollTop;if(this.heightArea.length){for(var i=0;i<this.heightArea.length;i++)if(e>=this.heightArea[i][0]&&e<=this.heightArea[i][1]){this.select=i;break}1!=this.value.template&&"all"==this.value.loadType&&this.heightArea[this.select][1]-e-s<300&&this.$refs.categoryItem[this.select].getGoodsList()}}},onRefresh:function(){this.triggered=!1},onRestore:function(){this.triggered="restore"},toLogin:function(){this.$emit("tologin")},selectSku:function(t,e){var i=this;this.$api.sendRequest({url:"/api/goodssku/getInfoForCategory",data:{sku_id:t.sku_id},success:function(t){if(t.code>=0){var e=t.data;e.unit=e.unit||"件",e.sku_images?e.sku_images=e.sku_images.split(","):e.sku_images=[],e.goods_spec_format&&e.goods_image&&(e.goods_image=e.goods_image.split(","),e.sku_images=e.goods_image.concat(e.sku_images)),e.sku_spec_format&&(e.sku_spec_format=JSON.parse(e.sku_spec_format)),e.goods_spec_format&&(e.goods_spec_format=JSON.parse(e.goods_spec_format)),1==e.promotion_type&&(e.discountTimeMachine=i.$util.countDown(e.end_time-t.timestamp)),1==e.promotion_type&&e.discountTimeMachine?e.member_price>0&&Number(e.member_price)<=Number(e.discount_price)?e.show_price=e.member_price:e.show_price=e.discount_price:e.member_price>0?e.show_price=e.member_price:e.show_price=e.price,i.$refs.skuSelect.show(e)}}})},settlement:function(){var t=this,e=!1;for(var i in this.cartList){var a=this.cartList[i];for(var s in a){if(a.max_buy&&a.num>a.max_buy){e=!0,this.$util.showToast({title:"商品"+a.goods_name+"商品最多可购买"+a.max_buy+"件"});break}if("object"==(0,o.default)(a[s])){if(a[s].num>a[s].stock){e=!0,this.$util.showToast({title:"商品"+a.goods_name+"库存不足"});break}if(a[s].min_buy&&a[s].num<a[s].min_buy){e=!0,this.$util.showToast({title:"商品"+a.goods_name+"商品最少要购买"+a[s].min_buy+"件"});break}}}}e||this.cartIds.length&&!this.isSub&&(this.isSub=!0,uni.removeStorageSync("delivery"),uni.setStorage({key:"orderCreateData",data:{cart_ids:this.cartIds.toString()},success:function(){t.$util.redirectTo("/pages/order/payment"),t.isSub=!1}}))},addCartPoint:function(t,e){if(2==this.value.template||this.value.quickBuy){var i=(new Date).getTime();this.$set(this.carIconList,i,{left:t,top:e,index:0,bezierPos:this.$util.bezier([{x:t,y:e},{x:t-200,y:t-120},{x:r.left+10,y:r.top}],6).bezier_points,timer:null}),this.startAnimation(i)}},startAnimation:function(t){var e=this,i=this.carIconList[t].bezierPos,a=this.carIconList[t].index;this.carIconList[t].timer=setInterval((function(){if(a<6)e.carIconList[t].left=i[a].x,e.carIconList[t].top=i[a].y,a++;else{clearInterval(e.carIconList[t].timer),delete e.carIconList[t],e.$forceUpdate(),setTimeout((function(){e.$store.commit("setCartChange")}),100);var o=uni.createAnimation({duration:200,timingFunction:"ease"});o.scale(1.2).step(),e.cartAnimation=o.export(),setTimeout((function(){o.scale(1).step(),e.cartAnimation=o.export()}),300)}}),50)},templateFourOneFn:function(t){this.categoryTree=this.templateFourData[t].child_list||[],this.oneCategorySelect=t,this.select=0},refreshData:function(){this.$refs.categoryItem&&this.$refs.categoryItem[this.select].loadGoodsCartNum(!0)}}};e.default=d},"2ef4":function(t,e,i){"use strict";i.r(e);var a=i("edee"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},3488:function(t,e,i){var a=i("c4cd");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("38184dc6",a,!0,{sourceMap:!1,shadowMode:!1})},3592:function(t,e,i){var a=i("835a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("aa0a55da",a,!0,{sourceMap:!1,shadowMode:!1})},"3deb":function(t,e,i){"use strict";var a=i("afd9"),o=i.n(a);o.a},"42a9":function(t,e,i){"use strict";i.r(e);var a=i("2cea"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},"4fbd":function(t,e,i){"use strict";i.r(e);var a=i("eac5"),o=i("42a9");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("d5dc");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"9f70ffe2",null,!1,a["a"],void 0);e["default"]=r.exports},"519c":function(t,e,i){"use strict";var a=i("029e"),o=i.n(a);o.a},"5f0e":function(t,e,i){"use strict";i.r(e);var a=i("fb48"),o=i("0379");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("3deb");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"1dfbbcd6",null,!1,a["a"],void 0);e["default"]=r.exports},6013:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={components:{},data:function(){return{diyData:null,mpShareData:null}},onLoad:function(){uni.hideTabBar(),this.getDiyInfo()},onShow:function(){this.$refs.category&&this.$refs.category[0].pageShow()},onUnload:function(){!this.storeToken&&this.$refs.login&&this.$refs.login.cancelCompleteInfo()},methods:{getDiyInfo:function(){var t=this;this.$api.sendRequest({url:"/api/diyview/info",data:{name:"DIY_VIEW_GOODS_CATEGORY"},success:function(e){0==e.code&&e.data&&(t.diyData=e.data,t.diyData.value&&(t.diyData=JSON.parse(t.diyData.value),t.setPublicShare(),t.setMpShare(),t.$refs.loadingCover&&t.$refs.loadingCover.hide()),uni.stopPullDownRefresh())}})},setMpShare:function(){},setPublicShare:function(){var t=this.$config.h5Domain+"/pages/goods/category",e=this.$store.state.globalStoreInfo;e&&(t+="?store_id="+e.store_id),this.$util.setPublicShare({title:this.diyData.global.wechatShareTitle||this.diyData.global.title,desc:this.diyData.global.wechatShareDesc,link:t,imgUrl:this.diyData.global.weappShareImage?this.$util.img(this.diyData.global.weappShareImage):this.$util.img(this.siteInfo.logo_square)})},toLogin:function(){this.$refs.login.open("/pages/goods/category")}},onPullDownRefresh:function(){uni.hideTabBar(),this.getDiyInfo()},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine}};e.default=a},"64a3":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47"),i("5ef2");var a={name:"diy-bottom-nav",props:{value:{type:Object},name:{type:String,default:""}},data:function(){return{currentRoute:"",jumpFlag:!0,cartAnimation:{}}},mounted:function(){var t=this,e=getCurrentPages()[getCurrentPages().length-1];e&&e.route&&(this.currentRoute=e.route),this.$nextTick((function(){if(!t.$store.state.cartPosition){var e=uni.createSelectorQuery().in(t);e.select("#tabbarCart").boundingClientRect((function(e){e&&t.$store.commit("setCartPosition",e)})).exec(),e.select(".tab-bar").boundingClientRect((function(e){e&&t.$store.commit("setTabBarHeight",e.height+"px")})).exec()}}))},computed:{cartChange:function(){return this.$store.state.cartChange}},watch:{cartChange:function(t,e){var i=this;if(t>e){var a=uni.createAnimation({duration:200,timingFunction:"ease"});a.scale(1.2).step(),this.cartAnimation=a.export(),setTimeout((function(){a.scale(1).step(),i.cartAnimation=a.export()}),300)}}},methods:{redirectTo:function(t){this.$emit("callback"),this.$util.diyRedirectTo(t)},verify:function(t){if(null==t||""==t||!t.wap_url)return!1;if(this.name)var e=this.currentRoute+"?name="+this.name;else e=this.currentRoute;return"/pages/index/index"==t.wap_url&&"DIY_VIEW_INDEX"==this.name||!(!e||-1==t.wap_url.indexOf(e))}}};e.default=a},"7d44":function(t,e,i){"use strict";var a=i("d1d0"),o=i.n(a);o.a},"81bb":function(t,e,i){"use strict";i.r(e);var a=i("b902"),o=i("e150");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("e659");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"897a28d6",null,!1,a["a"],void 0);e["default"]=r.exports},"835a":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".uni-popup[data-v-3d5ca5bf]{position:fixed;top:0;bottom:0;left:0;right:0;z-index:999;overflow:hidden}.uni-popup__mask[data-v-3d5ca5bf]{position:absolute;top:0;bottom:0;left:0;right:0;z-index:998;background:rgba(0,0,0,.4);opacity:0}.uni-popup__mask.ani[data-v-3d5ca5bf]{transition:all .3s}.uni-popup__mask.uni-bottom[data-v-3d5ca5bf],\r\n.uni-popup__mask.uni-center[data-v-3d5ca5bf],\r\n.uni-popup__mask.uni-right[data-v-3d5ca5bf],\r\n.uni-popup__mask.uni-left[data-v-3d5ca5bf],\r\n.uni-popup__mask.uni-top[data-v-3d5ca5bf]{opacity:1}.uni-popup__wrapper[data-v-3d5ca5bf]{position:absolute;z-index:999;box-sizing:border-box\r\n\t/*background: #ffffff;\r\n */}.uni-popup__wrapper.ani[data-v-3d5ca5bf]{transition:all .3s}.uni-popup__wrapper.top[data-v-3d5ca5bf]{top:0;left:0;width:100%;-webkit-transform:translateY(-100%);transform:translateY(-100%)}.uni-popup__wrapper.bottom[data-v-3d5ca5bf]{bottom:0;left:0;width:100%;-webkit-transform:translateY(100%);transform:translateY(100%)}.uni-popup__wrapper.right[data-v-3d5ca5bf]{bottom:0;left:0;width:100%;-webkit-transform:translateX(100%);transform:translateX(100%)}.uni-popup__wrapper.left[data-v-3d5ca5bf]{bottom:0;left:0;width:100%;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.uni-popup__wrapper.center[data-v-3d5ca5bf]{width:100%;height:100%;display:flex;justify-content:center;align-items:center;-webkit-transform:scale(1.2);transform:scale(1.2);opacity:0}.uni-popup__wrapper-box[data-v-3d5ca5bf]{position:relative;box-sizing:border-box}.uni-popup__wrapper.uni-custom .uni-popup__wrapper-box[data-v-3d5ca5bf]{\r\n\t/*background: #fff;\r\n */}.uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box[data-v-3d5ca5bf]{position:relative;max-width:80%;max-height:80%;overflow-y:scroll}.uni-popup__wrapper.uni-custom.bottom .uni-popup__wrapper-box[data-v-3d5ca5bf],\r\n.uni-popup__wrapper.uni-custom.top .uni-popup__wrapper-box[data-v-3d5ca5bf]{width:100%;max-height:500px;overflow-y:scroll}.uni-popup__wrapper.uni-bottom[data-v-3d5ca5bf],\r\n.uni-popup__wrapper.uni-top[data-v-3d5ca5bf]{-webkit-transform:translateY(0);transform:translateY(0)}.uni-popup__wrapper.uni-left[data-v-3d5ca5bf],\r\n.uni-popup__wrapper.uni-right[data-v-3d5ca5bf]{-webkit-transform:translateX(0);transform:translateX(0)}.uni-popup__wrapper.uni-center[data-v-3d5ca5bf]{-webkit-transform:scale(1);transform:scale(1);opacity:1}\r\n\r\n/* isIphoneX系列手机底部安全距离 */.bottom.safe-area[data-v-3d5ca5bf]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}",""]),t.exports=e},8790:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.item-wrap[data-v-897a28d6]{width:100%;box-sizing:border-box;padding:0 %?24?%}.item-wrap.goods[data-v-897a28d6]{height:100%}.category-title[data-v-897a28d6]{padding:%?20?% 0;font-size:%?28?%;font-weight:700;color:#222;position:relative;text-align:center}.category-title[data-v-897a28d6]::after, .category-title[data-v-897a28d6]::before{content:" ";width:%?46?%;border-top:%?2?% solid #dfdfdf;position:absolute;top:50%}.category-title[data-v-897a28d6]::after{margin-left:%?40?%}.category-title[data-v-897a28d6]::before{margin-left:%?-80?%}.category-adv[data-v-897a28d6]{width:100%;overflow:hidden;line-height:1;padding:%?20?% 0}.category-adv uni-image[data-v-897a28d6]{width:100%;border-radius:%?8?%}.category-list[data-v-897a28d6]{display:flex;flex-wrap:wrap}.category-list .category-item[data-v-897a28d6]{width:33.33%;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?16?%;box-sizing:border-box}.category-list .category-item .img-box[data-v-897a28d6]{width:80%;padding-top:80%;margin:0 auto;overflow:hidden;margin-bottom:%?20?%;position:relative}.category-list .category-item .img-box uni-image[data-v-897a28d6]{position:absolute;width:100%;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.category-list .category-item .name[data-v-897a28d6]{width:100%;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;text-align:center;line-height:1;font-size:%?24?%}.goods-list .goods-item[data-v-897a28d6]{padding:0 0 %?26?% 0;background:#fff;display:flex;position:relative}.goods-list .goods-item .goods-img[data-v-897a28d6]{width:%?180?%;height:%?180?%;overflow:hidden;border-radius:%?10?%;margin-right:%?14?%}.goods-list .goods-item .goods-img uni-image[data-v-897a28d6]{width:100%;height:100%}.goods-list .goods-item .goods-tag[data-v-897a28d6]{color:#fff;line-height:1;padding:%?8?% %?12?%;position:absolute;border-top-left-radius:%?10?%;border-bottom-right-radius:%?10?%;top:0;left:0;font-size:%?22?%}.goods-list .goods-item .goods-tag-img[data-v-897a28d6]{position:absolute;border-top-left-radius:%?10?%;width:%?80?%;height:%?80?%;top:%?26?%;left:%?26?%;z-index:5;overflow:hidden}.goods-list .goods-item .goods-tag-img uni-image[data-v-897a28d6]{width:100%;height:100%}.goods-list .goods-item .sell-out[data-v-897a28d6]{position:absolute;z-index:1;width:%?180?%;height:%?180?%;top:0;left:0;display:flex;align-items:center;justify-content:center;background:rgba(0,0,0,.5);border-radius:%?10?%}.goods-list .goods-item .sell-out uni-text[data-v-897a28d6]{color:#fff;font-size:%?150?%;position:absolute;left:50%;top:50%;-webkit-transform:translateX(-50%) translateY(-50%);transform:translateX(-50%) translateY(-50%)}.goods-list .goods-item .info-wrap[data-v-897a28d6]{flex:1;display:flex;flex-direction:column;width:0}.goods-list .goods-item .name-wrap[data-v-897a28d6]{flex:1}.goods-list .goods-item .goods-name[data-v-897a28d6]{font-size:%?28?%;line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.goods-list .goods-item .introduction[data-v-897a28d6]{line-height:1;margin-top:%?10?%}.goods-list .goods-item .discount-price[data-v-897a28d6]{display:inline-block;font-weight:700;line-height:1;margin-top:%?16?%;color:var(--price-color)}.goods-list .goods-item .discount-price .unit[data-v-897a28d6]{margin-right:%?6?%}.goods-list .goods-item .pro-info[data-v-897a28d6]{display:flex;margin-top:%?16?%}.goods-list .goods-item .pro-info .delete-price[data-v-897a28d6]{text-decoration:line-through;flex:1}.goods-list .goods-item .pro-info .delete-price .unit[data-v-897a28d6]{margin-right:%?0?%}.goods-list .goods-item .pro-info > uni-view[data-v-897a28d6]{line-height:1}.goods-list .goods-item .pro-info > uni-view[data-v-897a28d6]:nth-child(2){text-align:right}.goods-list .goods-item .member-price-tag[data-v-897a28d6]{display:inline-block;width:%?60?%;line-height:1;margin-left:%?6?%;height:%?28?%}.goods-list .goods-item .member-price-tag uni-image[data-v-897a28d6]{width:100%;height:100%}.goods-list .goods-item .footer-wrap[data-v-897a28d6]{display:flex;justify-content:space-between}.goods-list .goods-item .footer-wrap .right-wrap[data-v-897a28d6]{display:flex;align-items:center;justify-content:end}.goods-list .goods-item .footer-wrap .right-wrap .num[data-v-897a28d6]{width:auto;padding:0 %?20?%;line-height:1}.goods-list .goods-item .footer-wrap .right-wrap .num-action[data-v-897a28d6]{display:flex;align-items:center;justify-content:center;width:%?40?%;height:%?40?%;background:var(--base-color);border-radius:50%;position:relative}.goods-list .goods-item .footer-wrap .right-wrap .num-action .click-event[data-v-897a28d6]{position:absolute;width:%?2?%;height:%?2?%;left:0;top:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);z-index:5}.goods-list .goods-item .footer-wrap .right-wrap .num-action.reduce[data-v-897a28d6]{width:%?38?%;height:%?38?%;background-color:initial;border:%?2?% solid var(--base-color);box-sizing:border-box}.goods-list .goods-item .footer-wrap .right-wrap .num-action.reduce .icon-jian[data-v-897a28d6]{color:var(--base-color)}.goods-list .goods-item .footer-wrap .right-wrap .icon-jian[data-v-897a28d6],\r\n.goods-list .goods-item .footer-wrap .right-wrap .icon-jia[data-v-897a28d6]{color:var(--btn-text-color);font-weight:700;font-size:%?26?%;line-height:1}.goods-list .goods-item .footer-wrap .right-wrap .select-sku[data-v-897a28d6]{font-weight:700;color:var(--btn-text-color);font-size:%?24?%;padding:%?16?% %?24?%;position:relative;line-height:1;text-align:center;border-radius:%?50?%}.goods-list .goods-item .footer-wrap .right-wrap .select-sku .num-tag[data-v-897a28d6]{position:absolute;top:0;right:0;-webkit-transform:translateY(-50%);transform:translateY(-50%);display:inline-block;box-sizing:border-box;color:#fff;line-height:1.2;text-align:center;font-size:%?24?%;padding:0 %?6?%;min-width:%?32?%;border-radius:%?16?%;background-color:var(--base-color);border:%?2?% solid #fff}.goods-list[data-template="3"] .goods-item[data-v-897a28d6]{flex-direction:column}.goods-list[data-template="3"] .goods-item .info-wrap[data-v-897a28d6]{width:100%;margin-top:%?12?%}.goods-list[data-template="3"] .goods-item .goods-img[data-v-897a28d6]{width:100%;height:auto;margin-right:0;line-height:1;position:relative}.goods-list[data-template="3"] .goods-item .goods-img uni-image[data-v-897a28d6]{border-radius:%?8?%}.goods-list[data-template="3"] .goods-item .goods-img .sell-out[data-v-897a28d6]{position:absolute;z-index:1;width:100%;height:auto;top:0;left:0;bottom:0;display:flex;align-items:center;justify-content:center;background:rgba(0,0,0,.5);border-radius:%?10?%}.goods-list[data-template="3"] .goods-item .goods-img .sell-out uni-text[data-v-897a28d6]{color:#fff;font-size:%?240?%}.goods-list[data-template="3"] .goods-item .select-sku[data-v-897a28d6]{font-weight:700;width:%?128?%;height:%?52?%!important;line-height:%?52?%!important}.categoty-goods-wrap[data-v-897a28d6]{display:flex;flex-direction:column;height:100%}.categoty-goods-wrap .scroll-goods-view[data-v-897a28d6]{flex:1;height:0;-webkit-transform:translateX(0);transform:translateX(0)}.screen-category-wrap[data-v-897a28d6]{display:flex;padding-top:%?20?%}.screen-category-wrap .icon-unfold[data-v-897a28d6]{font-size:%?24?%;color:#999;padding:0 0 0 %?20?%}.screen-category[data-v-897a28d6]{flex:1;width:0;padding:0 0 %?20?% 0;white-space:nowrap;height:%?60?%}.screen-category .item[data-v-897a28d6]{display:inline-block;padding:%?4?% %?24?%;background:#f5f5f5;color:#666;margin-right:%?20?%;border-radius:%?40?%}.screen-category .item.selected[data-v-897a28d6]{background-color:var(--base-color);color:var(--btn-text-color)}[data-v-897a28d6] .uni-popup__wrapper-box{border-radius:0}.screen-category-popup[data-v-897a28d6]{display:flex}.screen-category-popup .screen-category[data-v-897a28d6]{white-space:break-spaces;padding:%?20?%;height:auto}.screen-category-popup .title[data-v-897a28d6]{line-height:1;margin-bottom:%?20?%;font-weight:700}.screen-category-popup .item[data-v-897a28d6]{margin-bottom:%?20?%}.end-tips[data-v-897a28d6]{text-align:center;color:#999;font-size:%?24?%;padding:%?30?% 0 %?30?% 0;display:flex;align-items:center;justify-content:center}[data-v-897a28d6] .loading-layer{background:#fff!important}.category-empty[data-v-897a28d6]{flex:1;display:flex;align-items:center;justify-content:center;flex-direction:column;padding-top:%?100?%}.category-empty uni-image[data-v-897a28d6]{width:%?280?%;height:%?252?%}.category-empty .tips[data-v-897a28d6]{font-size:%?26?%;font-weight:500;color:#999;margin-top:%?50?%}.screen-category-4 .item[data-v-897a28d6]{background-color:#f2f2f2!important;padding:%?10?% %?24?%;line-height:1;font-size:%?24?%}.screen-category-4 .item.selected[data-v-897a28d6]{background-color:var(--main-color-shallow)!important;color:var(--main-color)}',""]),t.exports=e},"9c49":function(t,e,i){var a=i("8790");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("a4898046",a,!0,{sourceMap:!1,shadowMode:!1})},"9c6c3":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.showPopup?i("v-uni-view",{staticClass:"uni-popup"},[i("v-uni-view",{staticClass:"uni-popup__mask",class:[t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close(!0)}}}),t.isIphoneX?i("v-uni-view",{staticClass:"uni-popup__wrapper goodslist-uni-popup safe-area",class:[t.type,t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close(!0)}}},[i("v-uni-view",{staticClass:"uni-popup__wrapper-box goodslist-uni-popup-box",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[t._t("default")],2)],1):i("v-uni-view",{staticClass:"uni-popup__wrapper goodslist-uni-popup",class:[t.type,t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close(!0)}}},[i("v-uni-view",{staticClass:"uni-popup__wrapper-box goodslist-uni-popup-box",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[t._t("default")],2)],1)],1):t._e()},o=[]},a68f:function(t,e,i){"use strict";i.r(e);var a=i("acc8"),o=i("29ac");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("c225");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"1839a53e",null,!1,a["a"],void 0);e["default"]=r.exports},acc8:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"diy-icon",style:this.iconBgStyle},[e("v-uni-text",{staticClass:"js-icon",class:this.iconClass,style:this.iconStyle})],1)},o=[]},afd9:function(t,e,i){var a=i("fcae");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("59afc8f5",a,!0,{sourceMap:!1,shadowMode:!1})},b791:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"diy-icon",props:{icon:{type:String,default:""},value:{type:Object,default:function(){return null}}},computed:{iconClass:function(){var t=" "+this.icon;return this.value&&this.value.iconColor.length>1&&(t+=" gradient"),t},iconBgStyle:function(){if(!this.value)return{};var t={"border-radius":this.value.bgRadius+"%",background:""};return this.value.iconBgImg&&(t["background"]+="url("+this.$util.img(this.value.iconBgImg)+") no-repeat bottom / contain"),this.value.iconBgColor.length&&(t.background&&(t.background+=","),1==this.value.iconBgColor.length?t.background+=this.value.iconBgColor[0]:t["background"]+="linear-gradient("+this.value.iconBgColorDeg+"deg, "+this.value.iconBgColor.join(",")+")"),this.$util.objToStyle(t)},iconStyle:function(){if(!this.value)return{};var t={"font-size":this.value.fontSize+"%"};return 1==this.value.iconColor.length?t.color=this.value.iconColor[0]:t["background"]="linear-gradient("+this.value.iconColorDeg+"deg, "+this.value.iconColor.join(",")+")",this.$util.objToStyle(t)}}};e.default=a},b7ca:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.category-page-wrap[data-v-9f70ffe2]{width:100vw;height:100vh;box-sizing:border-box;display:flex;flex-direction:column;background-color:#fff}.content-box[data-v-9f70ffe2]{flex:1;height:0;display:flex}.content-box .tree-wrap[data-v-9f70ffe2]{width:%?170?%;height:100%;background-color:#f5f5f5}.content-box .right-flex-wrap[data-v-9f70ffe2]{flex:1;width:0;height:100%;background:#fff;display:flex;flex-direction:column;-webkit-transform:translateX(0);transform:translateX(0)}.content-box .right-flex-wrap .content-wrap[data-v-9f70ffe2]{display:flex;flex:1;height:0;width:100%}.content-box .right-flex-wrap .child-category-wrap[data-v-9f70ffe2]{width:100%;height:100%}.tree-wrap .category-item-wrap[data-v-9f70ffe2]{height:auto;background-color:#fff}.tree-wrap .category-item[data-v-9f70ffe2]{line-height:1.5;padding:%?26?% %?28?%;box-sizing:border-box;position:relative;background-color:#f5f5f5}.tree-wrap .category-item uni-view[data-v-9f70ffe2]{color:#222;width:100%;line-height:1.3;overflow:hidden;text-align:center;word-break:break-all;max-height:%?100?%}.tree-wrap .category-item.border-top[data-v-9f70ffe2]{border-bottom-right-radius:%?12?%}.tree-wrap .category-item.border-bottom[data-v-9f70ffe2]{border-top-right-radius:%?12?%}.tree-wrap .category-item.select[data-v-9f70ffe2]{background:#fff}.tree-wrap .category-item.select uni-view[data-v-9f70ffe2]{color:#333;font-weight:700}.tree-wrap .category-item.select[data-v-9f70ffe2]::before{content:" ";width:%?8?%;height:%?34?%;background:var(--base-color);display:block;position:absolute;left:0;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.search-box[data-v-9f70ffe2]{position:relative;padding:%?20?% %?30?%;display:flex;align-items:center;background:#fff}.search-box .search-content[data-v-9f70ffe2]{position:relative;height:%?64?%;border-radius:%?40?%;flex:1;background-color:#f5f5f5}.search-box .search-content uni-input[data-v-9f70ffe2]{box-sizing:border-box;display:block;height:100%;width:100%;padding:0 %?20?% 0 %?40?%;background:#f5f5f5;color:#333;border-radius:%?40?%}.search-box .search-content .iconfont[data-v-9f70ffe2]{position:absolute;top:50%;right:%?10?%;-webkit-transform:translateY(-50%);transform:translateY(-50%);font-size:%?32?%;z-index:10;color:#89899a;width:%?80?%;text-align:center}.cart-box[data-v-9f70ffe2]{height:%?100?%;width:100%;position:fixed;left:0;bottom:var(--tab-bar-height,0);background:#fff;border-top:1px solid #f5f5f5;box-sizing:border-box;padding:0 %?30?%;display:flex;align-items:center;justify-content:space-between}.cart-box .left-wrap[data-v-9f70ffe2]{display:flex;align-items:center}.cart-box .cart-icon[data-v-9f70ffe2]{width:%?70?%;height:%?70?%;position:relative}.cart-box .cart-icon .iconfont[data-v-9f70ffe2]{color:var(--btn-text-color);width:inherit;height:inherit;background-color:var(--base-color);border-radius:50%;display:flex;align-items:center;justify-content:center}.cart-box .cart-icon .num[data-v-9f70ffe2]{position:absolute;top:0;right:0;-webkit-transform:translate(60%);transform:translate(60%);display:inline-block;box-sizing:border-box;color:#fff;line-height:1.2;text-align:center;font-size:%?24?%;padding:0 %?6?%;min-width:%?30?%;border-radius:%?16?%;background-color:var(--price-color);border:%?2?% solid #fff}.cart-box .price[data-v-9f70ffe2]{margin-left:%?30?%}.cart-box .price .title[data-v-9f70ffe2]{color:#333}.cart-box .price .money[data-v-9f70ffe2],\r\n.cart-box .price .unit[data-v-9f70ffe2]{font-weight:700;color:var(--price-color)}.cart-box .settlement-btn[data-v-9f70ffe2]{margin:0 0 0 %?20?%;width:%?200?%;font-weight:700;border-radius:%?50?%;height:%?70?%;line-height:%?70?%}.cart-box.active[data-v-9f70ffe2]{bottom:calc(constant(safe-area-inset-bottom) + %?110?%)!important;bottom:calc(env(safe-area-inset-bottom) + %?110?%)!important}.cart-point[data-v-9f70ffe2]{width:%?26?%;height:%?26?%;position:fixed;z-index:1000;background:red;border-radius:50%;transition:all .05s}.category-empty[data-v-9f70ffe2]{flex:1;display:flex;align-items:center;justify-content:center;flex-direction:column}.category-empty uni-image[data-v-9f70ffe2]{width:%?380?%}.category-empty .tips[data-v-9f70ffe2]{font-size:%?26?%;font-weight:500;color:#999;margin-top:%?50?%}.end-tips[data-v-9f70ffe2]{text-align:center;color:#999;font-size:%?24?%;padding:%?20?% 0;opacity:0}.category-template-4 .search-box .search-content uni-input[data-v-9f70ffe2]{background-color:#f1f1f1}.category-template-4 .cart-box[data-v-9f70ffe2]{z-index:2}.category-template-4[data-v-9f70ffe2] .template-four{position:relative;z-index:1}.category-template-4[data-v-9f70ffe2] .template-four .template-four-wrap{position:relative;z-index:1;padding-left:%?20?%;padding-right:%?80?%;padding-bottom:%?10?%;display:flex;height:%?172?%;align-items:baseline;box-sizing:border-box;box-shadow:0 %?4?% %?4?% hsla(0,0%,48.2%,.1)}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup{display:flex;flex-direction:column;overflow:hidden}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .title{line-height:1;margin-bottom:%?20?%;font-weight:700}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .template-four-scroll{display:flex;flex-wrap:wrap;align-items:baseline;align-content:baseline;padding:%?20?%;white-space:nowrap;height:%?380?%;box-sizing:border-box}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .template-four-scroll .uni-scroll-view-content{flex-wrap:wrap;align-items:baseline;align-content:baseline}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .template-four-scroll .item{display:flex;flex-direction:column;align-items:center;padding:%?4?% 0;color:#666;margin-right:%?16?%;border-radius:%?40?%;margin-bottom:%?10?%;width:calc((100% - %?64?%) / 5)}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .template-four-scroll .item:nth-child(5n + 5){margin-right:0}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .template-four-scroll .item .image-warp{margin-bottom:%?6?%;padding:%?4?%;display:flex;align-items:center;justify-content:center;border-radius:%?42?%;border:%?4?% solid transparent}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .template-four-scroll .item uni-image{width:%?84?%;height:%?84?%;border-radius:%?32?%}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .template-four-scroll .item .text{padding:%?2?% 0;border-radius:%?40?%;font-size:%?24?%;box-sizing:border-box;width:100%;box-sizing:border-box;text-align:center;overflow:hidden}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .template-four-scroll .item .ellipsis{display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .template-four-scroll .item.selected .text{background-color:var(--base-color);color:var(--btn-text-color);line-height:1.3;border:%?4?% solid transparent;border-color:var(--base-color)}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .pack-up{font-size:%?24?%;color:#888;height:%?74?%;display:flex;align-items:center;justify-content:center;border-top:%?2?% solid #f2f2f2}.category-template-4[data-v-9f70ffe2] .template-four .template-four-popup .pack-up .iconfont{font-size:%?40?%;margin-left:%?-4?%}.category-template-4[data-v-9f70ffe2] .template-four .category-item-all{position:absolute;bottom:0;z-index:1;right:0;top:0;width:%?72?%;line-height:1;background-color:#fff}.category-template-4[data-v-9f70ffe2] .template-four .category-item-all .category-item-all-wrap{position:absolute;bottom:0;right:0;top:0;left:0;display:flex;flex-direction:column;justify-content:center;align-items:center;z-index:2}.category-template-4[data-v-9f70ffe2] .template-four .category-item-all .text{-webkit-writing-mode:tb-rl;writing-mode:tb-rl;margin-bottom:%?6?%;letter-spacing:%?4?%;font-size:%?24?%;font-weight:700}.category-template-4[data-v-9f70ffe2] .template-four .category-item-all .img{width:%?20?%;height:%?20?%}.category-template-4[data-v-9f70ffe2] .template-four .category-item-all::after{content:"";position:absolute;left:0;width:%?10?%;top:20%;bottom:20%}.category-template-4[data-v-9f70ffe2] .template-four .uni-scroll-view-content{display:flex}.category-template-4[data-v-9f70ffe2] .template-four .category-item{display:flex;flex-direction:column;align-items:center;justify-content:center;min-width:%?130?%;flex-shrink:0;margin-right:%?20?%;padding:%?4?% 0}.category-template-4[data-v-9f70ffe2] .template-four .category-item:last-of-type{margin-right:0}.category-template-4[data-v-9f70ffe2] .template-four .category-item .image-warp{margin-bottom:%?6?%;padding:%?4?%;display:flex;align-items:center;justify-content:center;border-radius:%?42?%;border:%?4?% solid transparent}.category-template-4[data-v-9f70ffe2] .template-four .category-item uni-image{width:%?84?%;height:%?84?%;border-radius:%?32?%}.category-template-4[data-v-9f70ffe2] .template-four .category-item .text{font-size:%?24?%}.category-template-4[data-v-9f70ffe2] .template-four .select .text{padding:%?8?% %?16?%;border-radius:%?40?%;color:#fff;font-size:%?24?%;line-height:1}.category-template-4 .content-wrap .categoty-goods-wrap .goods-list[data-v-9f70ffe2]{margin-top:%?30?%}.category-template-4 .tree-wrap .category-item.select[data-v-9f70ffe2]::before{border-top-right-radius:%?8?%;border-bottom-right-radius:%?8?%}.cart-bottom-block[data-v-9f70ffe2]{height:%?100?%}',""]),t.exports=e},b902:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uniPopup:i("d745").default,loadingCover:i("c003").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"item-wrap",class:t.type},["category"==t.type&&t.category.child_list&&t.category.child_list.length?[t.category.image_adv?i("v-uni-view",{staticClass:"category-adv",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.diyRedirectTo(t.category.link_url)}}},[i("v-uni-image",{attrs:{src:t.$util.img(t.category.image_adv),mode:"widthFix"}})],1):t._e(),2==t.value.level?[i("v-uni-view",{staticClass:"category-title"},[t._v(t._s(t.category.category_name))]),i("v-uni-view",{staticClass:"category-list"},t._l(t.category.child_list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"category-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/pages/goods/list",{category_id:e.category_id})}}},[i("v-uni-view",{staticClass:"img-box"},[i("v-uni-image",{attrs:{src:t.$util.img(e.image),mode:"widthFix"}})],1),i("v-uni-view",{staticClass:"name"},[t._v(t._s(e.category_name))])],1)})),1)]:t._e(),3==t.value.level?[t._l(t.category.child_list,(function(e,a){return[i("v-uni-view",{key:a+"_0",staticClass:"category-title"},[t._v(t._s(e.category_name))]),i("v-uni-view",{key:a+"_1",staticClass:"category-list"},t._l(e.child_list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"category-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/pages/goods/list",{category_id:e.category_id})}}},[i("v-uni-view",{staticClass:"img-box"},[i("v-uni-image",{attrs:{src:t.$util.img(e.image),mode:"widthFix","lazy-load":!0}})],1),i("v-uni-view",{staticClass:"name"},[t._v(t._s(e.category_name))])],1)})),1)]}))]:t._e()]:t._e(),"goods"==t.type?["part"==t.loadType?i("v-uni-view",{staticClass:"categoty-goods-wrap",style:"padding-top:"+(t.value.search?0:"20rpx")},[t.category.child_list&&2==t.value.goodsLevel?[i("v-uni-view",{staticClass:"screen-category-wrap"},[i("v-uni-scroll-view",{staticClass:"screen-category",class:{"screen-category-4":4==t.value.template},attrs:{"scroll-x":"true","scroll-with-animation":!0,"scroll-into-view":t.scrollIntoView}},[i("v-uni-view",{staticClass:"item",class:{selected:-1==t.categoryId},attrs:{id:"category-2--1"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectCategory(-1)}}},[t._v("全部")]),t._l(t.category.child_list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"item",class:{selected:t.categoryId==a},attrs:{id:"category-2-"+a},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectCategory(a)}}},[t._v(t._s(e.category_name))])}))],2),i("v-uni-view",{staticClass:"iconfont icon-unfold",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.screenCategoryPopup.open()}}})],1),i("uni-popup",{ref:"screenCategoryPopup",attrs:{type:"top"}},[i("v-uni-view",{staticClass:"screen-category-popup",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.screenCategoryPopup.close()}}},[i("v-uni-scroll-view",{staticClass:"screen-category",class:{"screen-category-4":4==t.value.template},attrs:{"scroll-y":"true"}},[i("v-uni-view",{staticClass:"title"},[t._v("全部")]),t._l(t.category.child_list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"item",class:{selected:t.categoryId==a},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectCategory(a)}}},[t._v(t._s(e.category_name))])}))],2)],1)],1)]:t._e(),i("v-uni-scroll-view",{staticClass:"scroll-goods-view",attrs:{"scroll-y":"true","lower-threshold":"300","scroll-top":t.scrollTop},on:{scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.scrolltolower.apply(void 0,arguments)},touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchstart.apply(void 0,arguments)},touchend:function(e){arguments[0]=e=t.$handleEvent(e),t.touchend.apply(void 0,arguments)},scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.listenScroll.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"goods-list",attrs:{"data-template":t.value.template}},[t.goodsList.length?t._l(t.goodsList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-img",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-image",{attrs:{src:t.goodsImg(e.goods_image),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError(a)}}}),e.label_name?i("v-uni-view",{staticClass:"color-base-bg goods-tag"},[t._v(t._s(e.label_name))]):t._e(),e.goods_stock<=0?i("v-uni-view",{staticClass:"sell-out"},[i("v-uni-text",{staticClass:"iconfont icon-shuqing"})],1):t._e()],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.goods_name))])],1),i("v-uni-view",{staticClass:"price-wrap"},[i("v-uni-view",{staticClass:"discount-price"},[i("v-uni-text",{staticClass:"unit  price-style small"},[t._v("￥")]),i("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.showPrice(e)).toFixed(2).split(".")[0]))]),i("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.showPrice(e)).toFixed(2).split(".")[1]))])],1),e.member_price&&e.member_price==t.showPrice(e)?i("v-uni-view",{staticClass:"member-price-tag"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/VIP.png"),mode:"widthFix"}})],1):1==e.promotion_type?i("v-uni-view",{staticClass:"member-price-tag"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/discount.png"),mode:"widthFix"}})],1):t._e()],1),i("v-uni-view",{staticClass:"footer-wrap"},[i("v-uni-view",{staticClass:"pro-info"},[t.showMarketPrice(e)?i("v-uni-view",{staticClass:"delete-price font-size-activity-tag color-tip price-font"},[i("v-uni-text",{staticClass:"unit"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.showMarketPrice(e)))])],1):t._e()],1),2==t.value.template||4==t.value.template?i("v-uni-view",{staticClass:"right-wrap"},[e.is_virtual?[i("v-uni-view",{staticClass:"color-base-bg select-sku",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[t._v("立即购买")])]:[e.goods_spec_format?i("v-uni-view",{staticClass:"color-base-bg select-sku",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.selectSku(e)}}},[i("v-uni-text",[t._v("选规格")]),e.num?i("v-uni-text",{staticClass:"num-tag"},[t._v(t._s(e.num))]):t._e()],1):[t.cartList["goods_"+e.goods_id]&&t.cartList["goods_"+e.goods_id]["sku_"+e.sku_id]?[i("v-uni-view",{staticClass:"num-action reduce",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.reduce(e)}}},[i("v-uni-text",{staticClass:"iconfont icon-jian"})],1),i("v-uni-view",{staticClass:"num"},[t._v(t._s(t.cartList["goods_"+e.goods_id]["sku_"+e.sku_id].num))]),i("v-uni-view",{staticClass:"num-action",attrs:{id:"cart-num-"+a},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.increase(i,e)}}},[i("v-uni-text",{staticClass:"iconfont icon-jia"}),i("v-uni-view",{staticClass:"click-event"})],1)]:i("v-uni-view",{staticClass:"num-action",attrs:{id:"cart-num-"+a},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.increase(i,e,0)}}},[i("v-uni-text",{staticClass:"iconfont icon-jia"}),i("v-uni-view",{staticClass:"click-event"})],1)]]],2):t._e(),3==t.value.template?i("v-uni-view",{staticClass:"right-wrap"},[i("v-uni-view",{staticClass:"color-base-bg select-sku",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[t._v("立即购买")])],1):t._e()],1)],1)],1)})):i("v-uni-view",{staticClass:"category-empty"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/category/empty.png"),mode:"widthFix"}})],1),t.last&&(-1==t.categoryId||!t.category.child_list||t.category.child_list&&t.categoryId==t.category.child_list.length-1)?i("v-uni-view",{ref:"endTips",staticClass:"end-tips"},[t._v("已经到底了~")]):i("v-uni-view",{ref:"endTips",staticClass:"end-tips",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchCategory("next")}}},[i("v-uni-text",{staticClass:"iconfont icon-xiangshangzhanhang"}),t._v("上滑查看下一分类")],1)],2),i("loading-cover",{ref:"loadingCover",attrs:{"init-show":t.loading}})],1)],2):t._e(),"all"==t.loadType&&t.goodsList.length?[i("v-uni-view",{staticClass:"goods-list",attrs:{"data-template":t.value.template}},t._l(t.goodsList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-img",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-image",{attrs:{src:t.goodsImg(e.goods_image),mode:"widthFix","lazy-load":!0},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError(a)}}}),i("v-uni-view",{staticClass:"color-base-bg goods-tag"},[t._v(t._s(e.label_name))]),e.goods_stock<=0?i("v-uni-view",{staticClass:"sell-out"},[i("v-uni-text",{staticClass:"iconfont icon-shuqing"})],1):t._e()],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.goods_name))])],1),i("v-uni-view",{staticClass:"price-wrap"},[i("v-uni-view",{staticClass:"discount-price"},[i("v-uni-text",{staticClass:"unit  price-style small"},[t._v("￥")]),i("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.showPrice(e)).toFixed(2).split(".")[0]))]),i("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.showPrice(e)).toFixed(2).split(".")[1]))])],1),e.member_price&&e.member_price==t.showPrice(e)?i("v-uni-view",{staticClass:"member-price-tag"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/VIP.png"),mode:"widthFix"}})],1):1==e.promotion_type?i("v-uni-view",{staticClass:"member-price-tag"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/index/discount.png"),mode:"widthFix"}})],1):t._e()],1),i("v-uni-view",{staticClass:"footer-wrap"},[i("v-uni-view",{staticClass:"pro-info"},[t.showMarketPrice(e)?i("v-uni-view",{staticClass:"delete-price font-size-activity-tag color-tip price-font"},[i("v-uni-text",{staticClass:"unit"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.showMarketPrice(e)))])],1):t._e()],1),2==t.value.template?i("v-uni-view",{staticClass:"right-wrap"},[e.is_virtual?[i("v-uni-view",{staticClass:"color-base-bg select-sku",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[t._v("立即购买")])]:[e.goods_spec_format?i("v-uni-view",{staticClass:"color-base-bg select-sku",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.selectSku(e)}}},[i("v-uni-text",[t._v("选规格")]),e.num?i("v-uni-text",{staticClass:"num-tag"},[t._v(t._s(e.num))]):t._e()],1):[t.cartList["goods_"+e.goods_id]&&t.cartList["goods_"+e.goods_id]["sku_"+e.sku_id]?[i("v-uni-view",{staticClass:"num-action reduce",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.reduce(e)}}},[i("v-uni-text",{staticClass:"iconfont icon-jian"})],1),i("v-uni-view",{staticClass:"num"},[t._v(t._s(t.cartList["goods_"+e.goods_id]["sku_"+e.sku_id].num))]),i("v-uni-view",{staticClass:"num-action",attrs:{id:"cart-num-"+a},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.increase(i,e)}}},[i("v-uni-text",{staticClass:"iconfont icon-jia"}),i("v-uni-view",{staticClass:"click-event"})],1)]:i("v-uni-view",{staticClass:"num-action",attrs:{id:"cart-num-"+a},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.increase(i,e,0)}}},[i("v-uni-text",{staticClass:"iconfont icon-jia"}),i("v-uni-view",{staticClass:"click-event"})],1)]]],2):t._e(),3==t.value.template?i("v-uni-view",{staticClass:"right-wrap"},[i("v-uni-view",{staticClass:"color-base-bg select-sku",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[t._v("立即购买")])],1):t._e()],1)],1)],1)})),1)]:t._e()]:t._e()],2)},s=[]},c225:function(t,e,i){"use strict";var a=i("3488"),o=i.n(a);o.a},c4cd:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.diy-icon[data-v-1839a53e]{width:100%;height:100%;font-size:100%;color:#000;display:flex;align-items:center;justify-content:center}.diy-icon .js-icon[data-v-1839a53e]{font-size:50%;line-height:1;padding:%?1?%}.diy-icon .js-icon.gradient[data-v-1839a53e]{-webkit-background-clip:text!important;-webkit-text-fill-color:transparent}',""]),t.exports=e},d1d0:function(t,e,i){var a=i("d644");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("088dfa75",a,!0,{sourceMap:!1,shadowMode:!1})},d5db:function(t,e,i){"use strict";var a=i("3592"),o=i.n(a);o.a},d5dc:function(t,e,i){"use strict";var a=i("2728"),o=i.n(a);o.a},d644:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.placeholder[data-v-09d90d92]{height:%?112?%}.placeholder.bluge[data-v-09d90d92]{height:%?180?%}.safe-area[data-v-09d90d92]{padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.tab-bar[data-v-09d90d92]{background-color:#fff;box-sizing:border-box;position:fixed;left:0;bottom:0;width:100%;z-index:998;display:flex;border-top:%?2?% solid #f5f5f5;padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.tab-bar .tabbar-border[data-v-09d90d92]{background-color:hsla(0,0%,100%,.329412);position:absolute;left:0;top:0;width:100%;height:%?2?%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.tab-bar .item[data-v-09d90d92]{display:flex;align-items:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;flex:1;flex-direction:column;padding-bottom:%?10?%;box-sizing:border-box}.tab-bar .item .bd[data-v-09d90d92]{position:relative;height:%?100?%;flex-direction:column;text-align:center;display:flex;justify-content:center;align-items:center}.tab-bar .item .bd .icon[data-v-09d90d92]{position:relative;display:inline-block;margin-top:%?10?%;width:%?40?%;height:%?40?%;font-size:%?40?%;line-height:%?40?%}.tab-bar .item .bd .icon uni-image[data-v-09d90d92]{width:100%;height:100%}.tab-bar .item .bd .icon > uni-view[data-v-09d90d92]{height:inherit;display:flex;align-items:center}.tab-bar .item .bd .icon .bar-icon[data-v-09d90d92]{font-size:%?42?%}.tab-bar .item .bd .label[data-v-09d90d92]{position:relative;text-align:center;font-size:%?24?%;line-height:1;margin-top:%?12?%}.tab-bar .item.bulge .bd[data-v-09d90d92]{position:relative;height:%?100?%;flex-direction:column;text-align:center}.tab-bar .item.bulge .bd .icon[data-v-09d90d92]{margin-top:%?-60?%;margin-bottom:%?4?%;border-radius:50%;width:%?100?%;height:%?102?%;padding:%?10?%;border-top:%?2?% solid #f5f5f5;background-color:#fff;box-sizing:border-box}.tab-bar .item.bulge .bd .icon uni-image[data-v-09d90d92]{width:100%;height:100%;border-radius:50%}.tab-bar .item.bulge .bd .label[data-v-09d90d92]{position:relative;text-align:center;font-size:%?24?%;height:%?40?%;line-height:%?40?%}.tab-bar .item .cart-count-mark[data-v-09d90d92]{position:absolute;top:%?-8?%;right:%?-18?%;width:%?24?%;height:%?24?%!important;display:flex;justify-content:center;align-items:center;color:#fff;padding:%?6?%;border-radius:50%;z-index:99}.tab-bar .item .cart-count-mark.max[data-v-09d90d92]{width:%?40?%;border-radius:%?24?%;right:%?-28?%}.tab-bar-placeholder[data-v-09d90d92]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?112?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?112?%)}',""]),t.exports=e},d82a:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("bf0f"),i("2797"),i("aa9c"),i("5c47"),i("e966"),i("dc8a");var o=a(i("2c08")),s={name:"ns-goods-sku-category",components:{uniPopup:o.default},props:{disabled:{type:Boolean,default:!1},pointLimit:{type:[Number,String]},popupType:{type:String,default:"center"}},data:function(){return{systemInfo:{},number:0,btnSwitch:!1,goodsId:0,skuId:0,limitNumber:0,goodsDetail:{},goodsSkuList:{},maxBuy:0,minBuy:0,isLoad:!1,timeout:null,goodsForm:null,skuList:[]}},created:function(){this.systemInfo=uni.getSystemInfoSync()},watch:{pointLimit:function(t,e){this.limitNumber=Number(t)}},computed:{cartInputLock:function(){return!this.isLoad||this.number>0}},methods:{calcSkuStock:function(){var t=this;this.goodsDetail.goods_spec_format&&this.goodsDetail.goods_spec_format.length&&this.goodsDetail.goods_spec_format.forEach((function(e){e.value.forEach((function(e){t.skuList.forEach((function(i){e.sku_id==i.sku_id&&t.$set(e,"stock",i.stock)}))}))}))},show:function(t){this.number=0,this.isLoad=!0,this.goodsDetail=t,this.goodsId=this.goodsDetail.goods_id,this.skuId=this.goodsDetail.sku_id,this.maxBuy=this.goodsDetail.max_buy,this.minBuy=this.goodsDetail.min_buy,this.goodsForm=null,this.getGoodsForm(),this.getGoodsSkuList(this.goodsId),this.keyInput(!1),this.getCurrentCart()&&(this.number=this.getCurrentCart().num),this.$refs.skuPopup.open()},getGoodsForm:function(){var t=this;this.$api.sendRequest({url:"/form/api/form/goodsform",data:{goods_id:this.goodsDetail.goods_id},success:function(e){0==e.code&&e.data&&(t.goodsForm=e.data)}})},hide:function(){this.$refs.skuPopup&&this.$refs.skuPopup.close()},change:function(t,e){if(!this.disabled){this.btnSwitch=!1,this.skuId=t;for(var i=0;i<this.goodsDetail.goods_spec_format.length;i++)for(var a=this.goodsDetail.goods_spec_format[i],o=0;o<a.value.length;o++)e==this.goodsDetail.goods_spec_format[i].value[o].spec_id&&(this.goodsDetail.goods_spec_format[i].value[o].selected=!1);this.isLoad=!0,this.goodsSkuList["sku_"+t]&&(this.goodsDetail=Object.assign({},this.goodsDetail,this.goodsSkuList["sku_"+t])),this.getCurrentCart()?this.number=this.getCurrentCart().num:this.number=0,this.calcSkuStock()}},previewMedia:function(){var t=[];t.push(this.$util.img(this.goodsDetail.sku_image,{size:"big"})),uni.previewImage({current:1,urls:t})},getGoodsSkuList:function(t){var e=this;this.$api.sendRequest({url:"/api/goodssku/goodsSkuByCategory",data:{goods_id:t},success:function(t){if(t.code>=0){t.data;var i={};t.data.forEach((function(a,o){a.sku_spec_format&&(a.sku_spec_format=JSON.parse(a.sku_spec_format)),a.goods_spec_format&&(a.goods_spec_format=JSON.parse(a.goods_spec_format)),1==a.promotion_type&&(a.discountTimeMachine=e.$util.countDown(a.end_time-t.timestamp)),1==a.promotion_type&&a.discountTimeMachine?a.member_price>0&&Number(a.member_price)<=Number(a.discount_price)?a.show_price=a.member_price:a.show_price=a.discount_price:a.member_price>0?a.show_price=a.member_price:a.show_price=a.price,i["sku_"+a.sku_id]=a})),e.goodsSkuList=i,e.skuList=t.data,e.calcSkuStock()}}})},changeNum:function(t,e){var i=this;if(0!=this.goodsDetail.stock&&!this.btnSwitch){var a=this.goodsDetail.stock,o=1;if(1==this.goodsDetail.is_limit&&this.maxBuy>0&&this.maxBuy<a&&(a=this.maxBuy),1==this.goodsDetail.is_limit&&2==this.goodsDetail.limit_type&&this.maxBuy>0&&this.goodsDetail.purchased_num>0){var s=this.maxBuy-this.goodsDetail.purchased_num;a=s<this.goodsDetail.stock?s:this.goodsDetail.stock}if(this.minBuy>1&&(o=this.minBuy),"+"==t){if(this.number<a)this.number++;else{if(this.number>=this.goodsDetail.stock)return void this.$util.showToast({title:"库存不足"});if(1==this.goodsDetail.is_limit&&this.maxBuy>0){if(1==this.goodsDetail.limit_type)return void this.$util.showToast({title:"该商品每次最多购买"+this.maxBuy+this.goodsDetail.unit});if(2==this.goodsDetail.limit_type){var n="该商品每人限购"+this.maxBuy+this.goodsDetail.unit;return n+=this.goodsDetail.purchased_num>0?"，您已购买了"+this.goodsDetail.purchased_num+this.goodsDetail.unit:"",void this.$util.showToast({title:n})}}}var r=uni.createSelectorQuery().in(this);r.select("#"+e.currentTarget.id+" .click-event").boundingClientRect((function(t){t&&i.$emit("addCart",t.left,t.top)})).exec()}else"-"==t&&(this.number>o?this.number-=1:this.number=0);this.number>this.limitNumber&&this.limitNumber&&(this.number=this.limitNumber),this.number?this.cartNumChange(this.number):this.deleteCart()}},blur:function(){var t=this;if(this.number||(this.number=1),this.number>this.limitNumber&&this.limitNumber&&(this.number=this.limitNumber),this.goodsDetail.is_limit&&this.maxBuy>0){var e=this.maxBuy-this.goodsDetail.purchased_num;this.number>e&&(this.number=e)}this.number<this.minBuy&&this.minBuy>0&&(this.number=this.minBuy),this.number<=0&&(this.number=1);var i=parseInt(this.number);this.isLoad=!0,setTimeout((function(){t.number=i,t.cartNumChange(t.number)}),0)},keyInput:function(t,e,i){var a=this;i&&(this.isLoad=!1),setTimeout((function(){var i=a.goodsDetail.stock;0!=a.goodsDetail.stock?(t&&0==a.number.length&&(a.number=1),t&&(a.number<=0||isNaN(a.number))&&(a.number=1),a.number>i&&(a.number=i),t&&a.minBuy>1&&a.number<a.minBuy&&(a.number=a.minBuy),t&&(a.number=parseInt(a.number)),e&&e()):a.number=0}),0)},confirm:function(t){var e=this;if(this.storeToken)if(0!=this.goodsDetail.goods_state){if(this.$refs.form){if(!this.$refs.form.verify())return;uni.setStorageSync("goodFormData",{goods_id:this.goodsDetail.goods_id,form_data:this.$refs.form.formData})}this.number=1,this.keyInput(!0,(function(){if(0!=e.goodsDetail.stock)if(e.number>e.goodsDetail.stock)e.$util.showToast({title:"库存不足"});else if(1==e.goodsDetail.is_limit&&1==e.goodsDetail.limit_type&&e.maxBuy>0&&e.number>e.maxBuy)e.$util.showToast({title:"该商品每次最多购买"+e.maxBuy+e.goodsDetail.unit});else{if(1==e.goodsDetail.is_limit&&2==e.goodsDetail.limit_type&&e.maxBuy>0&&e.number+e.goodsDetail.purchased_num>e.maxBuy){var i="该商品每人限购"+e.maxBuy+e.goodsDetail.unit;return i+=e.goodsDetail.purchased_num>0?"，您已购买了"+e.goodsDetail.purchased_num+e.goodsDetail.unit:"",void e.$util.showToast({title:i})}e.$emit("addCart",t.detail.x,t.detail.y),e.btnSwitch||(e.btnSwitch=!0,e.$api.sendRequest({url:"/api/cart/add",data:{sku_id:e.skuId,num:e.number},success:function(t){var i=t.data;if(i>0){if(e.getCurrentCart())e.cartList["goods_"+e.goodsId]["sku_"+e.skuId].num=e.number;else{e.cartList["goods_"+e.goodsId]||(e.cartList["goods_"+e.goodsId]={});var a=e.goodsDetail.discount_price;e.goodsDetail.member_price>0&&Number(e.goodsDetail.member_price)<=Number(e.goodsDetail.discount_price)&&(a=e.goodsDetail.member_price),e.cartList["goods_"+e.goodsId]["sku_"+e.skuId]={cart_id:i,goods_id:e.goodsId,sku_id:e.skuId,num:e.number,discount_price:a}}e.$store.dispatch("cartCalculate"),e.$emit("refresh"),e.$util.showToast({title:"加入购物车成功"})}e.btnSwitch=!1},fail:function(t){e.$refs.skuPopup&&e.$refs.skuPopup.close(),e.btnSwitch=!1}}))}else e.$util.showToast({title:"商品已售罄"})}))}else this.$util.showToast({title:"商品已下架"});else this.$refs.login.open()},closeSkuPopup:function(){this.$refs.skuPopup&&this.$refs.skuPopup.close()},imageError:function(){this.goodsDetail.sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},valueImageError:function(t,e){this.goodsDetail.goods_spec_format[t].value[e].image=this.$util.getDefaultImage().goods,this.$forceUpdate()},cartNumChange:function(t){var e=this;t<1&&(t=1),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout((function(){e.$api.sendRequest({url:"/api/cart/edit",data:{num:t,cart_id:e.getCurrentCart().cart_id},success:function(i){i.code>=0&&(e.cartList["goods_"+e.goodsId]["sku_"+e.skuId].num=t,e.$store.dispatch("cartCalculate"),e.$emit("refresh"))}})}),800)},deleteCart:function(){var t=this;this.timeout&&clearTimeout(this.timeout),this.$api.sendRequest({url:"/api/cart/delete",data:{cart_id:this.getCurrentCart().cart_id},success:function(e){e.code>=0&&(delete t.cartList["goods_"+t.goodsId]["sku_"+t.skuId],2==Object.keys(t.cartList["goods_"+t.goodsId]).length&&delete t.cartList["goods_"+t.goodsId],t.$store.dispatch("cartCalculate"),t.$emit("refresh"))}})},getCurrentCart:function(){var t=this.cartList["goods_"+this.goodsId],e=null;return t&&t["sku_"+this.skuId]&&(e=t["sku_"+this.skuId]),e}}};e.default=s},da01:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("5c47"),i("c223"),i("e838"),i("e966"),i("dc8a"),i("bf0f"),i("2797");var a={name:"diy-category-item",props:{category:{type:Object},value:{type:Object,default:function(){return{}}},index:{type:Number,default:0},select:{type:Number,default:0},oneCategorySelect:{type:Number,default:0},last:{type:Boolean,default:!1}},data:function(){return{type:"goods",level:3,categoryId:-1,loading:!1,goodsList:[],pageIndex:0,pageSize:10,totalPage:1,touchstartPosition:0,scrollType:"top",contentWrapHeight:0,scrollIntoView:"category-2--1",scrollTop:0,loadType:"",timeout:{},cartEditRepeat:!1}},created:function(){this.type=1==this.value.template?"category":"goods",this.index==this.select&&0==this.pageIndex&&this.getGoodsList(),this.loadType=1==this.value.goodsLevel&&"all"==this.value.loadType?"all":"part"},mounted:function(){var t=this;setTimeout((function(){try{var e=uni.createSelectorQuery().in(t);e.select(".scroll-goods-view").boundingClientRect((function(e){e&&(t.contentWrapHeight=e.height)})).exec()}catch(i){}}),1e3)},watch:{oneCategorySelect:function(){this.scrollTop=-1,this.goodsList=[],this.selectCategory(-1)},select:function(){var t=this;if(this.index==this.select){if(this.scrollTop=0,0==this.pageIndex?this.getGoodsList():this.$refs.loadingCover&&"part"==this.loadType&&(this.$refs.loadingCover.show(),setTimeout((function(){t.$refs.loadingCover.hide()}),300)),!this.contentWrapHeight){var e=uni.createSelectorQuery().in(this);e.select(".scroll-goods-view").boundingClientRect((function(e){e&&(t.contentWrapHeight=e.height)})).exec()}}else"part"==this.loadType&&(this.scrollTop=-1)},"globalStoreInfo.store_id":{handler:function(t,e){t!=e&&(this.index!=this.select&&"all"!=this.loadType||(this.pageIndex=0,this.categoryId=-1,this.totalPage=1,this.getGoodsList()))},deep:!0}},methods:{getGoodsList:function(){var t=this;if(!("goods"!=this.type||this.loading||this.pageIndex>=this.totalPage)){this.loading=!0,this.pageIndex++,this.$refs.loadingCover&&1==this.pageIndex&&"part"==this.loadType&&setTimeout((function(){t.$refs.loadingCover.show()}));var e="",i="";this.value.sortWay&&("default"==this.value.sortWay?(e="",i=""):"sales"==this.value.sortWay?(e="sale_num",i="desc"):"price"==this.value.sortWay?(e="discount_price",i="desc"):"news"==this.value.sortWay&&(e="create_time",i="desc")),this.$api.sendRequest({url:"/api/goodssku/pageByCategory",data:{page:this.pageIndex,page_size:this.pageSize,category_id:-1!=this.categoryId?this.category.child_list[this.categoryId].category_id:this.category.category_id,order:e,sort:i},success:function(e){0==e.code&&e.data&&(1==t.pageIndex&&(t.goodsList=[]),t.goodsList=t.goodsList.concat(e.data.list),t.loadGoodsCartNum(),t.totalPage=e.data.page_count,t.loading=!1,t.$refs.loadingCover&&"part"==t.loadType&&t.$refs.loadingCover.hide(),setTimeout((function(){"all"==t.loadType&&t.$emit("loadfinish",1==t.pageIndex?t.index:-1);var e=uni.createSelectorQuery().in(t);e.select(".goods-list").boundingClientRect((function(e){e&&e.height<t.contentWrapHeight&&(t.scrollType="none")})).exec()}),500))}})}},goodsImg:function(t){var e=t.split(",");return e[0]?this.$util.img(e[0],{size:3==this.value?"big":"mid"}):this.$util.getDefaultImage().goods},imgError:function(t){this.goodsList[t].goods_image=this.$util.getDefaultImage().goods},showPrice:function(t){var e=t.discount_price;return t.member_price&&parseFloat(t.member_price)<parseFloat(e)&&(e=t.member_price),e},showMarketPrice:function(t){if(t.market_price_show){var e=this.showPrice(t);if(t.market_price>0)return t.market_price;if(parseFloat(t.price)>parseFloat(e))return t.price}return""},toDetail:function(t){this.$util.redirectTo("/pages/goods/detail",{goods_id:t.goods_id})},increase:function(t,e){var i=this;if(this.storeToken){if(!this.cartEditRepeat){var a=this.cartList["goods_"+e.goods_id]&&this.cartList["goods_"+e.goods_id]["sku_"+e.sku_id]?this.cartList["goods_"+e.goods_id]["sku_"+e.sku_id]:null,o=a?a.num:0,s=a&&a.cart_id?"/api/cart/edit":"/api/cart/add",n=e.min_buy>0?e.min_buy-1:0,r=o>=e.min_buy?o:n,c=r+1,l=a?a.cart_id:0;if(c>parseInt(e.stock))this.$util.showToast({title:"商品库存不足"});else if(e.is_limit&&e.max_buy&&c>parseInt(e.max_buy))this.$util.showToast({title:"该商品每人限购".concat(e.max_buy).concat(e.unit||"件")});else{var d=uni.createSelectorQuery().in(this);if(d.select("#"+t.currentTarget.id+" .click-event").boundingClientRect((function(t){t&&i.$emit("addCart",t.left,t.top)})).exec(),this.timeout[e.sku_id]&&clearTimeout(this.timeout[e.sku_id]),a)this.cartList["goods_"+e.goods_id]["sku_"+e.sku_id].num=c;else{this.cartList["goods_"+e.goods_id]||(this.cartList["goods_"+e.goods_id]={});var u=e.discount_price;e.member_price>0&&Number(e.member_price)<=Number(e.discount_price)&&(u=e.member_price),this.cartList["goods_"+e.goods_id]["sku_"+e.sku_id]={cart_id:l,goods_id:e.goods_id,sku_id:e.sku_id,num:c,discount_price:u}}this.$forceUpdate(),this.timeout[e.sku_id]=setTimeout((function(){i.cartEditRepeat=!0,i.$api.sendRequest({url:s,data:{cart_id:l,sku_id:e.sku_id,num:c},success:function(t){i.cartEditRepeat=!1,0==t.code?(0==l&&(i.cartList["goods_"+e.goods_id]["sku_"+e.sku_id].cart_id=t.data),i.$store.dispatch("cartCalculate")):i.$util.showToast({title:t.message})}})}),800)}}}else this.$emit("tologin")},reduce:function(t){var e=this;if(!this.cartEditRepeat){var i=this.cartList["goods_"+t.goods_id]&&this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id]?this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id]:null,a=i.num>t.min_buy?i.num:1,o=a>1?"/api/cart/edit":"/api/cart/delete",s=a-1;this.timeout[t.sku_id]&&clearTimeout(this.timeout[t.sku_id]),s?this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id].num=s:(delete this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id],2==Object.keys(this.cartList["goods_"+t.goods_id]).length&&delete this.cartList["goods_"+t.goods_id]),this.$forceUpdate(),i&&0==i.cart_id||(this.timeout[t.sku_id]=setTimeout((function(){e.cartEditRepeat=!0,e.$api.sendRequest({url:o,data:{cart_id:i.cart_id,sku_id:t.sku_id,num:s},success:function(t){e.cartEditRepeat=!1,0==t.code?e.$store.dispatch("cartCalculate"):e.$util.showToast({title:t.message})}})}),800))}},selectSku:function(t){this.storeToken?this.$emit("selectsku",t):this.$emit("tologin")},selectCategory:function(t){var e=this;this.categoryId=t,this.pageIndex=0,this.totalPage=1,this.getGoodsList(),setTimeout((function(){e.scrollIntoView="category-2-"+t}))},scrolltolower:function(){this.getGoodsList()},listenScroll:function(t){t.detail.scrollTop<=10?this.scrollType="top":parseInt(t.detail.scrollTop+this.contentWrapHeight+30)>=t.detail.scrollHeight?this.scrollType="bottom":this.scrollType=""},touchstart:function(t){this.touchstartPosition=t.changedTouches[0].clientY},touchend:function(t){var e=t.changedTouches[0].clientY;("top"==this.scrollType||"none"==this.scrollType)&&e-this.touchstartPosition>100?this.switchCategory("prev"):("bottom"==this.scrollType||"none"==this.scrollType)&&this.touchstartPosition-e>100&&this.switchCategory("next")},switchCategory:function(t){if("prev"==t)if(-1==this.categoryId){var e=this.index-1;if(-1==e)return;this.$emit("switch",e)}else{var i=this.categoryId-1;this.selectCategory(i)}else if(-1==this.categoryId||this.category.child_list&&this.categoryId==this.category.child_list.length-1){var a=this.index+1;this.$emit("switch",a)}else{var o=this.categoryId+1;this.selectCategory(o)}},diyRedirectTo:function(t){t&&this.$util.diyRedirectTo(JSON.parse(t))},loadGoodsCartNum:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.goodsList.forEach((function(e){e.num=t.cartList["goods_"+e.goods_id]?t.cartList["goods_"+e.goods_id].num:0})),e&&this.$forceUpdate()}}};e.default=a},e150:function(t,e,i){"use strict";i.r(e);var a=i("da01"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},e659:function(t,e,i){"use strict";var a=i("9c49"),o=i.n(a);o.a},e926:function(t,e,i){"use strict";i.r(e);var a=i("64a3"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},eac5:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uniPopup:i("d745").default,diyCategoryItem:i("81bb").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{class:["category-page-wrap","category-template-"+t.value.template],style:{height:"calc(100vh - "+t.tabBarHeight+")"}},[t.value.search?i("v-uni-view",{staticClass:"search-box",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages_tool/goods/search")}}},[i("v-uni-view",{staticClass:"search-content"},[i("v-uni-input",{staticClass:"uni-input",attrs:{type:"text",maxlength:"50",placeholder:"商品搜索","confirm-type":"search",readonly:"true"}}),i("v-uni-text",{staticClass:"iconfont icon-sousuo3"})],1)],1):t._e(),4==t.value.template?i("v-uni-view",{staticClass:"template-four wx"},[i("v-uni-scroll-view",{staticClass:"template-four-wrap",attrs:{"scroll-x":"true","scroll-with-animation":!0,"scroll-into-view":"category-one-"+t.oneCategorySelect,"enable-flex":"true"}},t._l(t.templateFourData,(function(e,a){return i("v-uni-view",{key:a,staticClass:"category-item",class:{select:t.oneCategorySelect==a},attrs:{id:"category-one-"+a},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.templateFourOneFn(a)}}},[i("v-uni-view",{staticClass:"image-warp",class:[{"color-base-border":t.oneCategorySelect==a}]},[i("v-uni-image",{attrs:{src:t.$util.img(e.image),mode:"aspectFill"}})],1),i("v-uni-view",{class:["text",{"color-base-bg":t.oneCategorySelect==a}]},[t._v(t._s(e.category_name))])],1)})),1),i("v-uni-view",{staticClass:"category-item-all",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.templateFourPopup.open()}}},[i("v-uni-view",{staticClass:"category-item-all-wrap"},[i("v-uni-text",{staticClass:"text"},[t._v("展开")]),i("v-uni-image",{staticClass:"img",attrs:{src:t.$util.img("/public/uniapp/category/unfold.png"),mode:"aspectFill"}})],1)],1),i("uni-popup",{ref:"templateFourPopup",attrs:{type:"top",top:t.uniPopupTop}},[i("v-uni-view",{staticClass:"template-four-popup"},[i("v-uni-scroll-view",{staticClass:"template-four-scroll",attrs:{"scroll-y":"true","enable-flex":"true"}},t._l(t.templateFourData,(function(e,a){return i("v-uni-view",{key:a,staticClass:"item",class:{selected:t.oneCategorySelect==a},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.templateFourOneFn(a)}}},[i("v-uni-view",{staticClass:"image-warp",class:[{"color-base-border":t.oneCategorySelect==a}]},[i("v-uni-image",{attrs:{src:t.$util.img(e.image),mode:"aspectFill"}})],1),i("v-uni-view",{class:["text",{"color-base-bg":t.oneCategorySelect==a}]},[t._v(t._s(e.category_name))])],1)})),1),i("v-uni-view",{staticClass:"pack-up",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.templateFourPopup.close()}}},[i("v-uni-text",[t._v("点击收起")]),i("v-uni-text",{staticClass:"iconfont icon-iconangledown-copy"})],1)],1)],1)],1):t._e(),t.categoryTree?i("v-uni-view",{staticClass:"content-box"},[t.categoryTree.length?[i("v-uni-scroll-view",{staticClass:"tree-wrap",attrs:{"scroll-y":"true"}},[i("v-uni-view",{staticClass:"category-item-wrap"},t._l(t.categoryTree,(function(e,a){return i("v-uni-view",{key:a,staticClass:"category-item",class:[{select:t.select==a},{"border-bottom":4==t.value.template&&t.select+1===a},{"border-top":4==t.value.template&&t.select-1===a}],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.switchOneCategory(a)}}},[i("v-uni-view",[t._v(t._s(e.category_name))])],1)})),1)],1),i("v-uni-view",{staticClass:"right-flex-wrap"},[1==t.value.template||"all"==t.loadType?i("v-uni-scroll-view",{ref:"contentWrap",staticClass:"content-wrap",attrs:{"scroll-y":"true","scroll-into-view":t.categoryId,"scroll-with-animation":!0,"refresher-enabled":!0,"refresher-default-style":"none","refresher-triggered":t.triggered},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.listenScroll.apply(void 0,arguments)},touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},refresherrefresh:function(e){arguments[0]=e=t.$handleEvent(e),t.onRefresh.apply(void 0,arguments)},refresherrestore:function(e){arguments[0]=e=t.$handleEvent(e),t.onRestore.apply(void 0,arguments)}}},[t._l(t.categoryTree,(function(e,a){return i("v-uni-view",{key:a,staticClass:"child-category",attrs:{id:"category-"+a}},[i("diy-category-item",{ref:"categoryItem",refInFor:!0,attrs:{category:e,value:t.value,index:a,select:t.select,oneCategorySelect:t.oneCategorySelect},on:{tologin:function(e){arguments[0]=e=t.$handleEvent(e),t.toLogin.apply(void 0,arguments)},selectsku:function(e){arguments[0]=e=t.$handleEvent(e),t.selectSku(e,a)},addCart:function(e){arguments[0]=e=t.$handleEvent(e),t.addCartPoint.apply(void 0,arguments)},loadfinish:function(e){arguments[0]=e=t.$handleEvent(e),t.getHeightArea.apply(void 0,arguments)}}})],1)})),i("v-uni-view",{ref:"endTips",staticClass:"end-tips",style:{opacity:t.endTips}},[t._v("已经到底了~")])],2):t._e(),2!=t.value.template&&3!=t.value.template&&4!=t.value.template||"part"!=t.loadType?t._e():i("v-uni-view",{staticClass:"content-wrap"},t._l(t.categoryTree,(function(e,a){return i("v-uni-view",{key:a,staticClass:"child-category-wrap",style:{display:t.select==a?"block":"none"},attrs:{id:"category-"+a}},[i("diy-category-item",{ref:"categoryItem",refInFor:!0,attrs:{category:e,value:t.value,index:a,last:a==t.categoryTree.length-1,select:t.select,oneCategorySelect:t.oneCategorySelect},on:{tologin:function(e){arguments[0]=e=t.$handleEvent(e),t.toLogin.apply(void 0,arguments)},selectsku:function(e){arguments[0]=e=t.$handleEvent(e),t.selectSku(e,a)},addCart:function(e){arguments[0]=e=t.$handleEvent(e),t.addCartPoint.apply(void 0,arguments)},switch:function(e){arguments[0]=e=t.$handleEvent(e),t.switchOneCategory.apply(void 0,arguments)}}})],1)})),1)],1)]:i("v-uni-view",{staticClass:"category-empty"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/category/empty.png"),mode:"widthFix"}}),i("v-uni-view",{staticClass:"tips"},[t._v("暂时没有分类哦！")])],1)],2):i("v-uni-view",{staticClass:"category-empty"},[i("v-uni-image",{attrs:{src:t.$util.img("public/uniapp/category/empty.png"),mode:"widthFix"}}),i("v-uni-view",{staticClass:"tips"},[t._v("暂时没有分类哦！")])],1),(2==t.value.template||4==t.value.template)&&t.value.quickBuy&&t.storeToken&&t.categoryTree&&t.categoryTree.length?i("v-uni-view",{staticClass:"cart-bottom-block"}):t._e(),(2==t.value.template||4==t.value.template)&&t.value.quickBuy&&t.storeToken&&t.categoryTree&&t.categoryTree.length?i("v-uni-view",{staticClass:"cart-box",class:{active:t.isIphoneX},style:{bottom:t.tabBarHeight}},[i("v-uni-view",{staticClass:"left-wrap"},[i("v-uni-view",{ref:"cartIcon",staticClass:"cart-icon",attrs:{animation:t.cartAnimation},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/goods/cart")}}},[i("v-uni-text",{staticClass:"iconfont icon-ziyuan1"}),t.cartNumber?i("v-uni-view",{staticClass:"num"},[t._v(t._s(t.cartNumber<99?t.cartNumber:"99+"))]):t._e()],1),i("v-uni-view",{staticClass:"price"},[i("v-uni-text",{staticClass:"title"},[t._v("总计：")]),i("v-uni-text",{staticClass:"unit font-size-tag price-font"},[t._v("￥")]),i("v-uni-text",{staticClass:"money font-size-toolbar price-font"},[t._v(t._s(t.cartTotalMoney[0]))]),i("v-uni-text",{staticClass:"unit font-size-tag price-font"},[t._v("."+t._s(t.cartTotalMoney[1]?t.cartTotalMoney[1]:"00"))])],1)],1),i("v-uni-view",{staticClass:"right-wrap"},[i("v-uni-button",{staticClass:"settlement-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.settlement.apply(void 0,arguments)}}},[t._v("去结算")])],1)],1):t._e(),t._l(t.carIconList,(function(t,e){return i("v-uni-view",{key:e,staticClass:"cart-point",style:{left:t.left+"px",top:t.top+"px"}})})),i("ns-goods-sku-category",{ref:"skuSelect",on:{refresh:function(e){arguments[0]=e=t.$handleEvent(e),t.refreshData.apply(void 0,arguments)},addCart:function(e){arguments[0]=e=t.$handleEvent(e),t.addCartPoint.apply(void 0,arguments)}}})],2)},s=[]},edee:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"UniPopup",props:{animation:{type:Boolean,default:!0},type:{type:String,default:"center"},custom:{type:Boolean,default:!1},maskClick:{type:Boolean,default:!0},show:{type:Boolean,default:!0}},data:function(){return{ani:"",showPopup:!1,callback:null,isIphoneX:!1}},watch:{show:function(t){t?this.open():this.close()}},created:function(){this.isIphoneX=this.$util.uniappIsIPhoneX()},methods:{clear:function(){},open:function(t){var e=this;t&&(this.callback=t),this.$emit("change",{show:!0}),this.showPopup=!0,this.$nextTick((function(){setTimeout((function(){e.ani="uni-"+e.type}),30)}))},close:function(t,e){var i=this;!this.maskClick&&t||(this.$emit("change",{show:!1}),this.ani="",this.$nextTick((function(){setTimeout((function(){i.showPopup=!1}),300)})),e&&e(),this.callback&&this.callback.call(this))}}};e.default=a},f049:function(t,e,i){"use strict";i.r(e);var a=i("6013"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},f8962:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={pageMeta:i("7854").default,diyCategory:i("4fbd").default,nsLogin:i("2910").default,loadingCover:i("c003").default,diyBottomNav:i("2532").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",[t.diyData?[t._l(t.diyData.value,(function(e,a){return["GoodsCategory"==e.componentName?i("v-uni-view",[i("diy-category",{ref:"category",refInFor:!0,attrs:{value:e},on:{tologin:function(e){arguments[0]=e=t.$handleEvent(e),t.toLogin.apply(void 0,arguments)}}})],1):t._e()]}))]:t._e(),i("ns-login",{ref:"login"}),i("loading-cover",{ref:"loadingCover"}),i("v-uni-view",{attrs:{id:"tab-bar"}},[i("diy-bottom-nav")],1)],2)],1)},s=[]},fb48:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uniPopup:i("d745").default,nsForm:i("ae30").default,nsLogin:i("2910").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-sku",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("uni-popup",{ref:"skuPopup",attrs:{type:t.popupType}},[i("v-uni-view",{staticClass:"sku-layer"},[i("v-uni-view",{staticClass:"sku-content"},[i("v-uni-view",{staticClass:"sku-info",style:{height:2*t.systemInfo.windowHeight+"rpx"}},[i("v-uni-view",{staticClass:"header"},[i("v-uni-view",{staticClass:"img-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewMedia()}}},[i("v-uni-image",{attrs:{src:t.$util.img(t.goodsDetail.sku_image,{size:"mid"})},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imageError()}}})],1),i("v-uni-view",{staticClass:"main"},[i("v-uni-view",{staticClass:"goodname"},[t._v(t._s(t.goodsDetail.goods_name))]),i("v-uni-view",{staticClass:"other-info"},[t.goodsDetail.stock_show?i("v-uni-view",{staticClass:"stock color-tip"},[t._v("库存"+t._s(t.goodsDetail.stock)+t._s(t.goodsDetail.unit))]):t._e(),parseFloat(t.goodsDetail.min_buy)?i("v-uni-view",{staticClass:"starting-num"},[t._v("起售"+t._s(t.goodsDetail.min_buy)+"件")]):t._e()],1)],1)],1),i("v-uni-view",{staticClass:"body-item"},[i("v-uni-scroll-view",{staticClass:"wrap",attrs:{"scroll-y":!0}},[t._l(t.goodsDetail.goods_spec_format,(function(e,a){return i("v-uni-view",{key:a,staticClass:"sku-list-wrap"},[i("v-uni-text",{staticClass:"title font-size-tag"},[t._v(t._s(e.spec_name))]),i("v-uni-view",{staticClass:"sku-list_item"},t._l(e.value,(function(e,o){return i("v-uni-view",{key:o,staticClass:"items color-line-border font-size-tag",class:{selected:e["selected"]||t.skuId==e.sku_id,disabled:e["disabled"]||!e["selected"]&&t.disabled},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.change(e.sku_id,e.spec_id)}}},[e.image?i("v-uni-image",{attrs:{src:t.$util.img(e.image,{size:"small"})},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.valueImageError(a,o)}}}):t._e(),i("v-uni-text",[t._v(t._s(e.spec_value_name))]),0==e.stock?i("v-uni-view",{staticClass:"empty-stock"},[t._v("缺货")]):t._e()],1)})),1)],1)})),t.goodsForm?i("ns-form",{ref:"form",attrs:{data:t.goodsForm}}):t._e()],2)],1),i("v-uni-view",{staticClass:"footer"},[i("v-uni-view",{staticClass:"sku-name font-size-goods-tag"},[t.goodsDetail.sku_spec_format?[t._v("已选择："),t._l(t.goodsDetail.sku_spec_format,(function(e,a){return i("v-uni-text",{key:a,staticClass:"color-tip"},[t._v(t._s(e.spec_value_name))])}))]:t._e()],2),i("v-uni-view",{staticClass:"footer-bottom"},[i("v-uni-view",{staticClass:"footer-left"},[i("v-uni-view",{staticClass:"price-wrap"},[i("v-uni-text",{staticClass:"price price-font"},[t._v("￥"+t._s(t.goodsDetail.show_price))])],1)],1),i("v-uni-view",{staticClass:"footer-right"},[t.cartInputLock?i("v-uni-view",{staticClass:"change_num"},[i("v-uni-view",{staticClass:"num-action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeNum("-")}}},[i("v-uni-text",{staticClass:"desc iconfont icon-jianshao color-base-text"}),i("v-uni-view",{staticClass:"click-event"})],1),i("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.blur.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.keyInput(!1,null,!0)}},model:{value:t.number,callback:function(e){t.number=e},expression:"number"}}),i("v-uni-view",{staticClass:"num-action",attrs:{id:"select-sku-num-"+t.goodsDetail.goods_id},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeNum("+",e)}}},[i("v-uni-text",{staticClass:"add iconfont icon-add-fill color-base-text change_hover"}),i("v-uni-view",{staticClass:"click-event"})],1)],1):0==t.number&&t.isLoad?i("v-uni-view",[i("v-uni-view",{staticClass:"num-action"},[t.goodsDetail.stock&&0!=t.goodsDetail.stock?i("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm(e)}}},[t._v("加入购物车")]):i("v-uni-button",{attrs:{type:"primary",disabled:"true"}},[t._v("确定")]),i("v-uni-view",{staticClass:"click-event"})],1)],1):t._e()],1)],1)],1)],1)],1)],1),i("v-uni-view",{staticClass:"sku-close iconfont icon-close-guanbi",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSkuPopup()}}})],1),i("ns-login",{ref:"login"})],1)},s=[]},fcae:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.sku-content[data-v-1dfbbcd6]{background:#fff;border-radius:%?20?%}.sku-close[data-v-1dfbbcd6]{color:#fff;width:%?50?%;text-align:center;font-size:%?60?%;border-radius:50%;margin:%?40?% auto 0}.sku-layer .sku-info[data-v-1dfbbcd6]{width:%?600?%;height:60vh!important;position:relative;z-index:999}.sku-layer .sku-info .header[data-v-1dfbbcd6]{padding:%?30?%;display:flex;position:relative;border-bottom:%?2?% solid rgba(0,0,0,.1);z-index:2}.sku-layer .sku-info .header .img-wrap[data-v-1dfbbcd6]{width:%?114?%;height:%?114?%;margin-right:%?20?%;border-radius:%?8?%;overflow:hidden;border:%?2?% solid rgba(0,0,0,.1);padding:%?2?%;background-color:#fff;line-height:%?208?%}.sku-layer .sku-info .header .img-wrap uni-image[data-v-1dfbbcd6]{width:100%;height:100%}.sku-layer .sku-info .main[data-v-1dfbbcd6]{font-size:%?24?%;line-height:%?40?%;flex:1;width:0}.sku-layer .sku-info .main .goodname[data-v-1dfbbcd6]{word-wrap:break-word;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;line-height:1.3}.sku-layer .sku-info .main .other-info[data-v-1dfbbcd6]{margin-top:%?20?%;display:flex;align-items:center;justify-content:space-between}.sku-layer .sku-info .main .starting-num[data-v-1dfbbcd6]{color:#909399;font-size:%?24?%}.footer-left .price[data-v-1dfbbcd6]{word-wrap:break-word;font-size:%?32?%;color:var(--price-color)}.sku-layer .sku-info .main .stock[data-v-1dfbbcd6]{line-height:1;font-size:%?24?%}.sku-layer .sku-info .main .sku-name[data-v-1dfbbcd6]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;height:%?90?%;overflow:hidden}.sku-layer .sku-info .main .sku-name uni-text[data-v-1dfbbcd6]{margin-right:%?10?%}.sku-layer .sku-info .sku-close[data-v-1dfbbcd6]{position:absolute;top:%?20?%;right:%?20?%;width:%?40?%;height:%?80?%;font-size:%?50?%}.sku-layer .body-item[data-v-1dfbbcd6]{padding:0 %?30?% %?30?% %?30?%;height:calc(100% - %?282?%);box-sizing:border-box;overflow:scroll}.sku-layer .body-item .wrap[data-v-1dfbbcd6]{height:calc(100% - %?116?%)}.sku-layer .body-item .sku-list-wrap .title[data-v-1dfbbcd6]{font-weight:400;padding:%?20?% 0;margin:0;display:block}.sku-layer .body-item .sku-list-wrap .sku-list_item[data-v-1dfbbcd6]{display:flex;flex-wrap:wrap}.body-item .sku-list-wrap .empty-stock[data-v-1dfbbcd6]{font-size:%?18?%;line-height:%?22?%;position:absolute;right:0;top:0;border-radius:%?4?%;-webkit-transform:translateY(-50%);transform:translateY(-50%);padding:0 %?2?%;color:#989898;background-color:#f0f1f2}.sku-layer .body-item .sku-list-wrap .items[data-v-1dfbbcd6]{text-align:center;position:relative;display:inline-block;border:%?2?% solid #eee;padding:%?4?% %?30?%;border-radius:%?8?%;margin:0 %?20?% %?20?% 0;background-color:#fff!important}.sku-layer .body-item .sku-list-wrap .items.disabled[data-v-1dfbbcd6]{border:%?2?% dashed}.sku-layer .body-item .sku-list-wrap .items uni-image[data-v-1dfbbcd6]{height:%?48?%;width:%?48?%;border-radius:%?4?%;margin-right:%?10?%;display:inline-block;vertical-align:middle}.sku-layer .body-item .number-wrap .number-line[data-v-1dfbbcd6]{padding:%?20?% 0;line-height:%?72?%}.sku-layer .body-item .number-wrap .title[data-v-1dfbbcd6]{font-weight:400}.sku-layer .body-item .number-wrap .number[data-v-1dfbbcd6]{height:%?72?%;border-radius:%?6?%;float:right}.sku-layer .body-item .number-wrap .number uni-button[data-v-1dfbbcd6]{display:inline-block;line-height:%?64?%;height:%?68?%;width:%?60?%;font-size:%?48?%;box-sizing:initial;border:%?2?% solid;padding:0;margin:0;border-radius:0}.sku-layer .body-item .number-wrap .number uni-button.decrease[data-v-1dfbbcd6]{border-right:%?2?% solid #fff!important}.sku-layer .body-item .number-wrap .number uni-button.increase[data-v-1dfbbcd6]{border-left:%?2?% solid #fff!important}.sku-layer .body-item .number-wrap .number uni-button[data-v-1dfbbcd6]:after{border-radius:0;border:none}.sku-layer .body-item .number-wrap .number uni-input[data-v-1dfbbcd6]{display:inline-block;line-height:%?64?%;height:%?68?%;width:%?72?%;text-align:center;font-weight:700;border:%?2?% solid;margin:0;padding:0;vertical-align:top}.sku-layer .footer[data-v-1dfbbcd6]{width:calc(100% - %?60?%);position:absolute;bottom:%?26?%;color:#fff;z-index:1;border-top:%?1?% solid #eee;padding:%?30?% %?30?% 0;background-color:#fff}.sku-layer .footer .sku-name uni-text[data-v-1dfbbcd6]{margin-right:%?10?%}.sku-layer .footer .footer-bottom[data-v-1dfbbcd6]{margin-top:%?24?%;display:flex;justify-content:be;align-items:center}.sku-layer .footer .footer-bottom .footer-left[data-v-1dfbbcd6]{flex:1}.sku-layer .footer .footer-bottom .footer-right[data-v-1dfbbcd6]{width:%?238?%;text-align:right}.sku-layer .footer .footer-bottom .footer-right uni-button[data-v-1dfbbcd6]{margin-right:0;width:%?210?%;height:%?60?%;line-height:%?60?%;font-size:%?30?%}.position-bottom[data-v-1dfbbcd6]{bottom:%?98?%!important}.change_num[data-v-1dfbbcd6]{display:flex;align-items:center;justify-content:flex-end}.change_num > uni-text[data-v-1dfbbcd6],\r\n.change_num .iconfont[data-v-1dfbbcd6]{font-size:%?48?%}.change_num uni-input[data-v-1dfbbcd6]{width:%?70?%;height:%?36?%;line-height:%?36?%;text-align:center}.change_num .num-action[data-v-1dfbbcd6]{position:relative}.change_num .num-action .click-event[data-v-1dfbbcd6]{position:absolute;width:%?2?%;height:%?2?%;left:0;top:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);z-index:5}[data-v-1dfbbcd6] .uni-popup__wrapper{background:transparent!important}[data-v-1dfbbcd6] .sku-layer .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:transparent!important}[data-v-1dfbbcd6] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background:transparent!important}',""]),t.exports=e},fd7f:function(t,e,i){"use strict";i.r(e);var a=i("f8962"),o=i("f049");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("519c");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"ff073ca8",null,!1,a["a"],void 0);e["default"]=r.exports}}]);