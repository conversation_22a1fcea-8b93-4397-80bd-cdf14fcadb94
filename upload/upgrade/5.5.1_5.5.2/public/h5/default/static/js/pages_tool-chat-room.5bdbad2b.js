(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-chat-room"],{"0817":function(e,t,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("5ef2"),i("5c47"),i("2c10"),i("a1c1"),i("23f4"),i("7d2f"),i("9c4e"),i("ab80"),i("aa9c"),i("473f"),i("bf0f"),i("3efd");var s=o(i("af87")),a=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,n=/^<\/([-A-Za-z0-9_]+)[^>]*>/,r=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,d=m("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),c=m("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),u=m("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),l=m("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),g=m("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),f=m("script,style");function m(e){for(var t={},i=e.split(","),o=0;o<i.length;o++)t[i[o]]=!0;return t}var p=function(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e),e=function(e){return e=e.replace(/<!--[\s\S]*-->/gi,""),e}(e),e=function(e){var t='<img style="width:100% !important;display:block;max-width: '.concat("100%",' !important;"');return e=e.replace(/\\/g,"").replace(/<img/g,t),e=e.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(e,i){return t+' src="'+s.default.img(i)+'"/>'})),e}(e),e=function(e){return e=e.replace(/style\s*=\s*["][^>]*;[^"]?/gi,(function(e,t){return e=e.replace(/[:](\s?)[\s\S]*/gi,(function(e,t){return e.replace(/"/g,"'")})),e})),e}(e);var t=[],i={node:"root",children:[]};return function(e,t){var i,o,s,m=[],p=e;m.last=function(){return this[this.length-1]};while(e){if(o=!0,m.last()&&f[m.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+m.last()+"[^>]*>"),(function(e,i){return i=i.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(i),""})),b("",m.last());else if(0==e.indexOf("\x3c!--")?(i=e.indexOf("--\x3e"),i>=0&&(t.comment&&t.comment(e.substring(4,i)),e=e.substring(i+3),o=!1)):0==e.indexOf("</")?(s=e.match(n),s&&(e=e.substring(s[0].length),s[0].replace(n,b),o=!1)):0==e.indexOf("<")&&(s=e.match(a),s&&(e=e.substring(s[0].length),s[0].replace(a,v),o=!1)),o){i=e.indexOf("<");var h=i<0?e:e.substring(0,i);e=i<0?"":e.substring(i),t.chars&&t.chars(h)}if(e==p)throw"Parse Error: "+e;p=e}function v(e,i,o,s){if(i=i.toLowerCase(),c[i])while(m.last()&&u[m.last()])b("",m.last());if(l[i]&&m.last()==i&&b("",i),s=d[i]||!!s,s||m.push(i),t.start){var a=[];o.replace(r,(function(e,t){var i=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:g[t]?t:"";a.push({name:t,value:i,escaped:i.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(i,a,s)}}function b(e,i){if(i){for(o=m.length-1;o>=0;o--)if(m[o]==i)break}else var o=0;if(o>=0){for(var s=m.length-1;s>=o;s--)t.end&&t.end(m[s]);m.length=o}}b()}(e,{start:function(e,o,s){var a={name:e};if(0!==o.length&&(a.attrs=function(e){return e.reduce((function(e,t){var i=t.value,o=t.name;return e[o]?e[o]=e[o]+" "+i:e[o]=i,e}),{})}(o)),s){var n=t[0]||i;n.children||(n.children=[]),n.children.push(a)}else t.unshift(a)},end:function(e){var o=t.shift();if(o.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)i.children.push(o);else{var s=t[0];s.children||(s.children=[]),s.children.push(o)}},chars:function(e){var o={type:"text",text:e};if(0===t.length)i.children.push(o);else{var s=t[0];s.children||(s.children=[]),s.children.push(o)}},comment:function(e){var i={node:"comment",text:e},o=t[0];o.children||(o.children=[]),o.children.push(i)}}),i.children};t.default=p},"099f":function(e,t,i){var o=i("c3979");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var s=i("967d").default;s("f895d6fa",o,!0,{sourceMap:!1,shadowMode:!1})},"0a00":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){}));var o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"goods"},[i("v-uni-view",{staticClass:"goods-msg"},[i("v-uni-image",{attrs:{src:e.$util.img(e.goodsInfo.sku_image),mode:"aspectFill"}}),i("v-uni-view",{staticClass:"goods-item"},[i("v-uni-view",{staticClass:"title"},[e._v(e._s(e.goodsInfo.goods_name))]),i("v-uni-view",{staticClass:"goods-sku"},[e._v("库存:"+e._s(e.goodsInfo.stock)),i("v-uni-text",[e._v("销量:"+e._s(e.goodsInfo.sale_num))])],1),i("v-uni-view",{staticClass:"goods-price"},[i("v-uni-view",{staticClass:"price color-base-text"},[i("v-uni-text",{staticClass:"price-util"},[e._v("￥")]),i("v-uni-text",{staticClass:"price-num"},[e._v(e._s(e.goodsInfo.price))])],1),i("v-uni-view",{staticClass:"see-shop color-base-text",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.go_shop()}}},[e._v("查看商品"),i("v-uni-text",{staticClass:"iconfont icon-right"})],1)],1)],1)],1)],1)},s=[]},"0c7a":function(e,t,i){"use strict";var o=i("2de5"),s=i.n(o);s.a},"1f90":function(e,t,i){"use strict";var o=i("52bd"),s=i.n(o);s.a},"2de5":function(e,t,i){var o=i("bec7");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var s=i("967d").default;s("4f689de1",o,!0,{sourceMap:!1,shadowMode:!1})},"2ffc":function(e,t,i){var o=i("513d");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var s=i("967d").default;s("4c589286",o,!0,{sourceMap:!1,shadowMode:!1})},3291:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={emjoyList:{"[emjoy_01]":"public/static/img/emjoy/emjoy_01.gif","[emjoy_02]":"public/static/img/emjoy/emjoy_02.gif","[emjoy_03]":"public/static/img/emjoy/emjoy_03.gif","[emjoy_04]":"public/static/img/emjoy/emjoy_04.gif","[emjoy_05]":"public/static/img/emjoy/emjoy_05.gif","[emjoy_06]":"public/static/img/emjoy/emjoy_06.gif","[emjoy_07]":"public/static/img/emjoy/emjoy_07.gif","[emjoy_08]":"public/static/img/emjoy/emjoy_08.gif","[emjoy_09]":"public/static/img/emjoy/emjoy_09.gif","[emjoy_10]":"public/static/img/emjoy/emjoy_10.gif","[emjoy_11]":"public/static/img/emjoy/emjoy_11.gif","[emjoy_12]":"public/static/img/emjoy/emjoy_12.gif","[emjoy_13]":"public/static/img/emjoy/emjoy_13.gif","[emjoy_14]":"public/static/img/emjoy/emjoy_14.gif","[emjoy_15]":"public/static/img/emjoy/emjoy_15.gif","[emjoy_16]":"public/static/img/emjoy/emjoy_16.gif","[emjoy_17]":"public/static/img/emjoy/emjoy_17.gif","[emjoy_18]":"public/static/img/emjoy/emjoy_18.gif","[emjoy_19]":"public/static/img/emjoy/emjoy_19.gif","[emjoy_20]":"public/static/img/emjoy/emjoy_20.gif","[emjoy_21]":"public/static/img/emjoy/emjoy_21.gif","[emjoy_22]":"public/static/img/emjoy/emjoy_22.gif","[emjoy_23]":"public/static/img/emjoy/emjoy_23.gif","[emjoy_24]":"public/static/img/emjoy/emjoy_24.gif","[emjoy_25]":"public/static/img/emjoy/emjoy_25.gif","[emjoy_26]":"public/static/img/emjoy/emjoy_26.gif","[emjoy_27]":"public/static/img/emjoy/emjoy_27.gif","[emjoy_28]":"public/static/img/emjoy/emjoy_28.gif","[emjoy_29]":"public/static/img/emjoy/emjoy_29.gif","[emjoy_30]":"public/static/img/emjoy/emjoy_30.gif","[emjoy_31]":"public/static/img/emjoy/emjoy_31.gif","[emjoy_32]":"public/static/img/emjoy/emjoy_32.gif","[emjoy_33]":"public/static/img/emjoy/emjoy_33.gif","[emjoy_34]":"public/static/img/emjoy/emjoy_34.gif","[emjoy_35]":"public/static/img/emjoy/emjoy_35.gif","[emjoy_36]":"public/static/img/emjoy/emjoy_36.gif","[emjoy_37]":"public/static/img/emjoy/emjoy_37.gif","[emjoy_38]":"public/static/img/emjoy/emjoy_38.gif","[emjoy_39]":"public/static/img/emjoy/emjoy_39.gif","[emjoy_40]":"public/static/img/emjoy/emjoy_40.gif","[emjoy_41]":"public/static/img/emjoy/emjoy_41.gif","[emjoy_42]":"public/static/img/emjoy/emjoy_42.gif","[emjoy_43]":"public/static/img/emjoy/emjoy_43.gif","[emjoy_44]":"public/static/img/emjoy/emjoy_44.gif","[emjoy_45]":"public/static/img/emjoy/emjoy_45.gif","[emjoy_46]":"public/static/img/emjoy/emjoy_46.gif","[emjoy_47]":"public/static/img/emjoy/emjoy_47.gif"}}},3866:function(e,t,i){"use strict";i.r(t);var o=i("7747"),s=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(a);t["default"]=s.a},"3d90":function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa");var o={name:"ns-chat-goods",props:{skuId:{type:[Number,String]},goodsDetail:{type:[Object]}},data:function(){return{goodsInfo:{}}},mounted:function(){this.getGoodsInfo()},methods:{getGoodsInfo:function(){var e=this;this.$api.sendRequest({url:"/api/goodssku/detail",data:{sku_id:this.skuId},success:function(t){t.code>=0&&(e.goodsInfo=t.data.goods_sku_detail)}})},sendMsg:function(){this.$emit("sendMsg","goods")}}};t.default=o},"465b":function(e,t,i){var o=i("a6b2");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var s=i("967d").default;s("ef967a9e",o,!0,{sourceMap:!1,shadowMode:!1})},"481a":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){}));var o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"message"},[e.goodsInfo&&e.goodsInfo.goods_name?i("v-uni-view",{staticClass:"goods-item"},[i("v-uni-image",{attrs:{src:e.$util.img(e.goodsInfo.sku_image),mode:"aspectFill"}}),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"goods-name"},[e._v(e._s(e.goodsInfo.sku_name?e.goodsInfo.sku_name:e.goodsInfo.goods_name))]),i("v-uni-view",{staticClass:"goods-bottom"},[i("v-uni-view",{staticClass:"goods-price"},[i("v-uni-text",{staticClass:"goods-price-sign color-base-text"},[e._v("￥")]),i("v-uni-text",{staticClass:"color-base-text"},[e._v(e._s(e.goodsInfo.price))])],1),i("v-uni-view",{staticClass:"goods-option font-size-goods-tag disabled"},[e._v("已发送")])],1)],1)],1):e.goodsDetail?i("v-uni-view",{staticClass:"goods-item"},[i("v-uni-image",{attrs:{src:e.$util.img(e.goodsDetail.sku_image),mode:"aspectFill"}}),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"goods-name"},[e._v(e._s(e.goodsDetail.sku_name?e.goodsDetail.sku_name:e.goodsDetail.goods_name))]),i("v-uni-view",{staticClass:"goods-bottom"},[i("v-uni-view",{staticClass:"goods-price"},[i("v-uni-text",{staticClass:"goods-price-sign color-base-text"},[e._v("￥")]),i("v-uni-text",{staticClass:"color-base-text"},[e._v(e._s(e.goodsDetail.price))])],1),i("v-uni-view",{staticClass:"goods-option font-size-goods-tag color-base-bg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendMsg("goods")}}},[e._v("发送")])],1)],1)],1):e._e()],1)},s=[]},4883:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa");var o={name:"ns-chat-receiveGoods",props:{skuId:{type:[Number,String]}},data:function(){return{goodsInfo:{}}},mounted:function(){this.getInfo()},methods:{getInfo:function(){var e=this;this.$api.sendRequest({url:"/api/goodssku/detail",data:{sku_id:this.skuId},success:function(t){t.code>=0&&(e.goodsInfo=t.data.goods_sku_detail,e.$emit("upDOM"))}})},go_shop:function(){this.$util.redirectTo("/pages/goods/detail?goods_id="+this.goodsInfo.goods_id)}}};t.default=o},"499d4":function(e,t,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("23f4"),i("7d2f"),i("5c47"),i("9c4e"),i("ab80"),i("0506"),i("a1c1"),i("bf0f"),i("2797");var s=o(i("e7296")),a=o(i("8cf6")),n=o(i("be18")),r=o(i("0817")),d=o(i("d745")),c=o(i("3291")),u={name:"chat-message",props:{message:{type:Object},send:{type:Boolean}},data:function(){return{avatar:"",defaultAvatar:this.$util.getDefaultImage().store,myHeadImg:"",defaultHead:this.$util.getDefaultImage().head,emjoyList:c.default.emjoyList,currImg:""}},components:{nsChatGoods:s.default,nsChatOrder:a.default,uniPopup:d.default,nsChatReceiveGoods:n.default},mounted:function(){this.avatar=this.$util.img(this.siteInfo.logo_square),this.myHeadImg=this.$util.img(this.memberInfo.headimg)},methods:{previewMedia:function(e){var t=[e];uni.previewImage({current:0,urls:t})},sendGood:function(){this.$emit("sendGood","goods")},sendOrder:function(){this.$emit("sendOrder","order")},myHeadImgError:function(){this.myHeadImg=this.defaultHead},stringToEmjoy:function(e){var t=this;if(e){var i=RegExp(/\[/);if(i.test(e)){var o=e,s=new RegExp("\\[emjoy_(.+?)\\]","g"),a=o.replace(s,(function(e){var i="";for(var o in t.emjoyList)if(e==o){var s=t.$util.img(t.emjoyList[o]);i="<img class='message-img' src='"+s+"'/>";break}return i||e})),n=(0,r.default)(a);return n.forEach((function(e){"img"==e.name&&(e.attrs.style="display: inline-block;width: 32rpx !important;height: 32rpx !important;padding:0 2rpx;")})),n}var d=e;return d}}}};t.default=u},"4aff":function(e,t,i){"use strict";var o=i("099f"),s=i.n(o);s.a},"513d":function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.more_send[data-v-567ae779]{display:flex;border-top:%?2?% solid #e5e5e5;height:%?390?%;flex-wrap:wrap}.more_send > uni-view[data-v-567ae779]{padding-top:%?16?%;width:25%;height:%?156?%;text-align:center}.more_send > uni-view .iconfont[data-v-567ae779]{display:inline;font-size:%?60?%;color:#333;background-color:#fff;border-radius:%?20?%;padding:%?20?%}.more_send > uni-view > uni-view[data-v-567ae779]{color:#909399;font-size:%?22?%}.chat_send[data-v-567ae779]{display:flex;height:%?90?%;justify-content:space-between;position:relative}.chat_send .send[data-v-567ae779]{position:absolute;right:%?30?%;bottom:%?30?%;color:#fff;align-self:flex-end;font-size:%?28?%;padding:%?10?% %?20?%;border-radius:%?8?%}.chat_send .emjoy_list[data-v-567ae779]{flex:1}\r\n/* 加载数据提示 */.tips[data-v-567ae779]{position:fixed;left:0;top:var(--window-top);width:100%;z-index:9;background-color:transparent;height:%?72?%;-webkit-transform:translateY(%?-80?%);transform:translateY(%?-80?%);transition:-webkit-transform .3s ease-in-out 0s;transition:transform .3s ease-in-out 0s;transition:transform .3s ease-in-out 0s,-webkit-transform .3s ease-in-out 0s;font-size:%?24?%;text-align:center;line-height:%?72?%}.tips.show[data-v-567ae779]{-webkit-transform:translateY(0);transform:translateY(0)}.room[data-v-567ae779]{width:100%;min-height:calc(100vh - %?48?%);padding-bottom:calc(%?110?% + %?88?%);padding-bottom:calc(%?110?% + %?88?% + constant(safe-area-inset-bottom));padding-bottom:calc(%?110?% + %?88?% + env(safe-area-inset-bottom));box-sizing:border-box}.room .room-content-box[data-v-567ae779]{width:100%;min-height:calc(100% + %?20?%);padding-top:%?20?%;box-sizing:border-box}.paddingbottom[data-v-567ae779]{height:calc(%?280?% + %?88?%)}.emjoy-box[data-v-567ae779]{width:100%;height:%?300?%;padding:%?20?% %?25?%;box-sizing:border-box;background:#f4f4f4;border-top:%?1?% solid #e5e5e5}.emjoy-box .emjoy-content[data-v-567ae779]{width:100%;height:100%}.emjoy-box .emjoy-content .emjoy-item[data-v-567ae779]{display:inline-block;width:%?100?%;height:%?70?%;text-align:center;line-height:%?70?%}.emjoy-box .emjoy-content .emjoy-item uni-image[data-v-567ae779]{display:inline-block;width:%?40?%;height:%?40?%}.input-content[data-v-567ae779]{background:#f4f4f4;width:100%;min-height:%?110?%;position:fixed;left:0;bottom:0;padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.input-content.active[data-v-567ae779]{min-height:%?180?%!important}.input-content .keyWords[data-v-567ae779]{height:%?60?%;line-height:%?60?%;padding:%?20?% 0 0;overflow-x:scroll;display:flex;margin-left:%?20?%;margin-right:%?20?%}.input-content .keyWords uni-text[data-v-567ae779]{font-size:%?24?%;margin-right:%?20?%;height:%?28?%;line-height:%?28?%;padding:%?10?% %?16?%;background-color:#fff;border-radius:%?10?%;white-space:nowrap}.input-content .input-box[data-v-567ae779]{width:100%;height:auto;display:flex;justify-content:space-between;align-items:center;padding:%?10?% %?20?%;box-sizing:border-box}.input-content .input-box .iconfont[data-v-567ae779]{line-height:1;font-size:%?52?%;padding:%?15?%}.input-content .input-box .send_btn[data-v-567ae779]{font-size:%?24?%;min-width:%?80?%;text-align:center;background-color:#e8e8e8;color:#fff;border-radius:%?8?%;height:%?50?%;line-height:%?50?%}.input-content .input-box .message-edit[data-v-567ae779]{padding:%?4?% %?15?%;line-height:1.5;background-color:#fff;min-height:%?60?%!important;height:auto!important;border-radius:%?30?%;font-size:%?28?%}',""]),e.exports=t},"52bd":function(e,t,i){var o=i("e112");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var s=i("967d").default;s("19c27141",o,!0,{sourceMap:!1,shadowMode:!1})},"648c":function(e,t,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("aa9c");var s=o(i("2f8f")),a={data:function(){return{timeoutObj:null,servicer_id:null,pingInterval:s.default.pingInterval}},onLoad:function(){uni.closeSocket(),this.checkOpenSocket()},methods:{checkOpenSocket:function(){console.log("判断是否已连接");var e=this;uni.sendSocketMessage({data:"ping",success:function(e){console.log("连接成功,检查")},fail:function(t){console.log("连接失败"),e.openConnection()}})},openConnection:function(){var e=this;console.log("打开连接"),uni.connectSocket({url:s.default.webSocket,method:"POST",success:function(e){console.log("连接成功 connectSocket=",e)},fail:function(e){console.log("连接失败 connectSocket=",e)}}),uni.onSocketError((function(t){console.error("WebSocket 连接失败:",t.errMsg),e.chatListInit(),e.getChatList()})),this.onSocketMessage()},onSocketMessage:function(){var e=this;console.log("开始监听");var t=this;this.pingInterval=s.default.pingInterval,this.timeoutObj&&clearInterval(this.timeoutObj),this.timeoutObj=null,uni.onSocketMessage((function(i){var o=JSON.parse(i.data);if(console.log("监听该服务器消息",i),"close"==o.type)return clearInterval(t.timeoutObj),t.timeoutObj=null,void uni.closeSocket();e.reset(),e.getSocketMsg(i.data)}))},getSocketMsg:function(e){var t=this,i=JSON.parse(e),o={isItMe:!1};if(o.contentType=i.type,"init"==i.type)t.$api.sendRequest({url:"/servicer/api/chat/bind",data:{client_id:i.data.client_id,site_id:t.siteId},success:function(e){0==e.code?t.servicer_id=e.data.servicer_id:t.servicer_id=0,t.chatListInit(),t.getChatList()}});else{if("connect"==i.type)return!1;"string"==i.type?o.content=i.data.servicer_say:"image"==i.type?o.image=i.data.servicer_say:"order"==i.type?o.order_id=i.data.order_id:"goodssku"==i.type&&(o.sku_id=i.data.goods_sku_id)}"init"!=i.type&&(t.messageList.push(o),t.$nextTick((function(){t.setPageScrollTo()})))},reset:function(){console.log("检测心跳"),clearInterval(this.timeoutObj),this.start()},start:function(){console.log("启动心跳");var e=this;this.timeoutObj=setInterval((function(){uni.sendSocketMessage({data:"ping",success:function(e){console.log("连接中....")},fail:function(t){console.log("连接失败重新连接...."),e.openConnection()}})}),this.pingInterval)}},onUnload:function(){clearInterval(this.timeoutObj),this.timeoutObj=null,this.$api.sendRequest({url:"/servicer/api/chat/bye",data:{servicer_id:this.servicer_id,site_id:this.siteId},success:function(e){uni.closeSocket()},fail:function(e){uni.closeSocket()}})}};t.default=a},"6ba1":function(e,t,i){"use strict";var o=i("465b"),s=i.n(o);s.a},7534:function(e,t,i){"use strict";i.r(t);var o=i("499d4"),s=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(a);t["default"]=s.a},7747:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa");var o={name:"ns-chat-order",props:{orderId:{type:[Number,String]},isCanSend:Boolean,orderdetails:{type:[Object]}},data:function(){return{orderInfo:{}}},mounted:function(){this.getGoodsInfo()},methods:{getGoodsInfo:function(){var e=this;this.orderId&&this.$api.sendRequest({url:"/api/order/detail",data:{order_id:this.orderId},success:function(t){t.code>=0&&(e.orderInfo=t.data)}})},sendMsg:function(){this.$emit("sendMsg","order")}}};t.default=o},7854:function(e,t,i){"use strict";i.r(t);var o=i("8ba8"),s=i("f48d");for(var a in s)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(a);var n=i("828b"),r=Object(n["a"])(s["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=r.exports},"7f02":function(e,t,i){"use strict";i.r(t);var o=i("cb33"),s=i("d8c8");for(var a in s)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(a);i("c0ed");var n=i("828b"),r=Object(n["a"])(s["default"],o["b"],o["c"],!1,null,"567ae779",null,!1,o["a"],void 0);t["default"]=r.exports},"8a35":function(e,t,i){"use strict";i.d(t,"b",(function(){return s})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){return o}));var o={uniPopup:i("d745").default},s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"chat-message"},["sendGood"==e.message.contentType?[i("ns-chat-goods",{attrs:{skuId:e.message.sku_id,goodsDetail:e.message.goodsDetail},on:{sendMsg:function(t){arguments[0]=t=e.$handleEvent(t),e.sendGood.apply(void 0,arguments)}}})]:e._e(),"sendOrder"==e.message.contentType?[i("ns-chat-order",{attrs:{orderId:e.message.order_id,orderdetails:e.message.orderDetail},on:{sendMsg:function(t){arguments[0]=t=e.$handleEvent(t),e.sendOrder.apply(void 0,arguments)}}})]:e._e(),"goodssku"==e.message.contentType?[i("ns-chat-receiveGoods",{attrs:{skuId:e.message.sku_id}})]:e._e(),"string"==e.message.contentType?i("v-uni-view",{staticClass:"message"},[i("v-uni-view",{staticClass:"message-item ",class:e.message.isItMe?"right":"left"},[e.message.isItMe?i("v-uni-view",{staticClass:"head_img"},[e.myHeadImg?i("v-uni-image",{staticClass:"img",attrs:{src:e.myHeadImg,mode:"aspectFit"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.myHeadImgError.apply(void 0,arguments)}}}):i("v-uni-image",{staticClass:"img",attrs:{src:e.defaultHead,mode:"aspectFit"}})],1):i("v-uni-view",{staticClass:"head_img"},[e.avatar?i("v-uni-image",{staticClass:"img",attrs:{src:e.avatar,mode:"aspectFit"}}):i("v-uni-image",{staticClass:"img",attrs:{src:e.defaultHead,mode:"aspectFit"}})],1),i("v-uni-view",{staticClass:"chat_text"},[e.message.isItMe&&!e.message.sendStatus?i("v-uni-text",{staticClass:"iconfont icon-warn margin-right color-base-text"}):e._e(),i("v-uni-view",{staticClass:"content"},[i("v-uni-rich-text",{attrs:{nodes:e.stringToEmjoy(e.message.content)}})],1)],1)],1)],1):e._e(),"image"==e.message.contentType?i("v-uni-view",{staticClass:"message"},[i("v-uni-view",{staticClass:"message-item ",class:e.message.isItMe?"right":"left"},[e.message.isItMe?i("v-uni-view",{staticClass:"head_img"},[e.myHeadImg?i("v-uni-image",{staticClass:"img",attrs:{src:e.myHeadImg,mode:"aspectFit"}}):i("v-uni-image",{staticClass:"img",attrs:{src:e.defaultHead,mode:"aspectFit"}})],1):i("v-uni-view",{staticClass:"head_img"},[e.avatar?i("v-uni-image",{staticClass:"img",attrs:{src:e.avatar,mode:"aspectFit"}}):i("v-uni-image",{staticClass:"img",attrs:{src:e.defaultHead,mode:"aspectFit"}})],1),i("v-uni-view",{staticClass:"chat_img"},[e.message.isItMe&&!e.message.sendStatus?i("v-uni-text",{staticClass:"iconfont icon-warn margin-right color-base-text"}):e._e(),i("v-uni-view",{staticClass:"content_img",style:{backgroundImage:"url("+e.$util.img(e.message.image)+")"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.previewMedia(e.$util.img(e.message.image))}}})],1)],1)],1):"goods"==e.message.contentType?i("v-uni-view",[i("ns-chat-goods",{attrs:{isCanSend:!1,skuId:e.message.sku_id}})],1):"order"==e.message.contentType?i("v-uni-view",[i("ns-chat-order",{attrs:{isCanSend:!1,orderId:e.message.order_id}})],1):e._e(),"noline"==e.message.contentType?i("v-uni-view",{staticClass:"no-connect-box"},[i("v-uni-view",{staticClass:"no-connect"},[e._v("客服不在线")])],1):e._e(),"online"==e.message.contentType?i("v-uni-view",{staticClass:"no-connect-box"},[i("v-uni-view",{staticClass:"no-connect"},[e._v("客服在线")])],1):e._e(),i("uni-popup",{ref:"imgPopup",attrs:{type:"center"}},[i("v-uni-view",{staticClass:"imagePop"},[i("v-uni-image",{attrs:{src:e.$util.img(e.currImg),mode:"aspectFit"}})],1)],1)],2)},a=[]},"8ba8":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){}));var o=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},s=[]},"8be5":function(e,t,i){"use strict";i.r(t);var o=i("8a35"),s=i("7534");for(var a in s)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(a);i("0c7a");var n=i("828b"),r=Object(n["a"])(s["default"],o["b"],o["c"],!1,null,"1dcb4068",null,!1,o["a"],void 0);t["default"]=r.exports},"8cf6":function(e,t,i){"use strict";i.r(t);var o=i("9e11"),s=i("3866");for(var a in s)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(a);i("1f90");var n=i("828b"),r=Object(n["a"])(s["default"],o["b"],o["c"],!1,null,"07831da6",null,!1,o["a"],void 0);t["default"]=r.exports},"9e11":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){}));var o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"message"},[e.orderdetails?i("v-uni-view",{staticClass:"goods-item"},[i("v-uni-image",{attrs:{src:e.$util.img(e.orderdetails.order_goods?e.orderdetails.order_goods[0].sku_image:""),mode:"aspectFill"}}),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"goods-name"},[e._v(e._s(e.orderdetails.order_goods?e.orderdetails.order_goods[0].sku_name:""))]),i("v-uni-view",{staticClass:"font-size-goods-tag"},[e._v("订单状态:"+e._s(e.orderdetails.order_status_name))]),i("v-uni-view",{staticClass:"font-size-goods-tag"},[e._v("配送方式:"+e._s(e.orderdetails.delivery_type_name))]),i("v-uni-view",{staticClass:"goods-bottom"},[i("v-uni-view",{staticClass:"goods-price color-base-text"},[i("v-uni-text",{staticClass:"goods-price-sign"},[e._v("￥")]),i("v-uni-text",[e._v(e._s(e.orderdetails.order_goods?e.orderdetails.order_goods[0].price:""))])],1),i("v-uni-view",{staticClass:"goods-option font-size-goods-tag color-base-bg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendMsg("order")}}},[e._v("发送")])],1)],1)],1):e.orderInfo?i("v-uni-view",{staticClass:"goods-item"},[i("v-uni-image",{attrs:{src:e.$util.img(e.orderInfo.order_goods?e.orderInfo.order_goods[0].sku_image:""),mode:"aspectFill"}}),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"goods-name"},[e._v(e._s(e.orderInfo.order_goods?e.orderInfo.order_goods[0].sku_name:""))]),i("v-uni-view",{staticClass:"font-size-goods-tag"},[e._v("订单状态:"+e._s(e.orderInfo.order_status_name))]),i("v-uni-view",{staticClass:"font-size-goods-tag"},[e._v("配送方式:"+e._s(e.orderInfo.delivery_type_name))]),i("v-uni-view",{staticClass:"goods-bottom"},[i("v-uni-view",{staticClass:"goods-price color-base-text"},[i("v-uni-text",{staticClass:"goods-price-sign"},[e._v("￥")]),i("v-uni-text",[e._v(e._s(e.orderInfo.order_goods?e.orderInfo.order_goods[0].price:""))])],1),i("v-uni-view",{staticClass:"goods-option font-size-goods-tag disabled"},[e._v("已发送")])],1)],1)],1):e._e()],1)},s=[]},a2b2:function(e,t,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=o(i("2634")),a=o(i("2fdc"));i("5c47"),i("aa9c"),i("dc8a"),i("c223"),i("dd2b"),i("d4b5"),i("0c26"),i("a1c1");var n=o(i("648c")),r=o(i("8be5")),d=o(i("3291")),c={components:{chatMessage:r.default},data:function(){return{emjoyList:d.default.emjoyList,emjoyShow:!1,chatMore:!1,formData:{content:"",goods_id:0,order_id:0,image:""},isNetWork:!1,send:!1,messageList:[],page:1,isAll:!1,isLoading:!1,showLoading:!1,jumpBottom:null,scrollTop:0,scrollPosition:0,siteId:0,skuId:0,orderId:0,siteName:"",orderdetail:{},style:{},inputFirst:0,inputShow:!1,inputOffsetBottom:0,goodsDetails:{},startId:0,goods_type:{promotion_name:"",promotion_id:""},keyWordsConfig:{is_open:0}}},mixins:[n.default],computed:{chatBottom:function(){return this.emjoyShow||this.chatMore||this.inputShow}},onLoad:function(e){var t=this;e.sku_id&&(this.skuId=e.sku_id,this.getGoodsDetails(e.sku_id)),e.order_id&&(this.orderId=e.order_id,this.getOrderInfo(e.order_id)),(this.skuId||this.orderId)&&(this.send=!0),this.$on("upDOM",(function(){t.setPageScrollTo()})),e.type?this.goods_type.promotion_name=e.type:this.goods_type.promotion_name="",e.type_id?this.goods_type.promotion_id=e.type_id:this.goods_type.promotion_id="",this.storeToken?(this.inputFirst=0,this.inputShow=!1,this.inputOffsetBottom=0,this.getKeyWordsConfig(),uni.setNavigationBarTitle({title:"商家客服"})):this.$util.redirectTo("/pages_tool/login/index")},onReady:function(){var e=this;uni.onKeyboardHeightChange((function(t){e.inputOffsetBottom=t.height,0===t.height&&(e.focus=!1)}))},methods:{onEditorReady:function(){uni.createSelectorQuery().select("#editor").context((function(e){})).exec()},onEditorinput:function(){var e=this;uni.createSelectorQuery().select("#editor").context((function(t){e.editorCtx=t.context,e.editorCtx.getContents({success:function(t){e.formData.content=t.html}})})).exec()},openChatMore:function(){var e=this;this.$util.isAndroid&&(this.inputShow=!1,this.inputFirst=1),this.chatMore=!this.chatMore,this.emjoyShow=!1,this.$nextTick((function(){e.setPageScrollTo()}))},getKeyWordsConfig:function(){var e=this;this.$api.sendRequest({url:"/servicer/api/chat/keyword",success:function(t){t.code>=0&&t.data&&(e.keyWordsConfig=t.data)}})},getGoodsDetails:function(e){var t=this;this.$api.sendRequest({url:"/api/goodssku/detail",data:{sku_id:e},success:function(e){e.code>=0&&(t.goodsDetails=e.data.goods_sku_detail)}})},getOrderInfo:function(e){var t=this;this.$api.sendRequest({url:"/api/order/detail",data:{order_id:e},success:function(e){e.code>=0&&(t.orderdetail=e.data)}})},chatListInit:function(){this.isAll=!1,this.isLoading=!1,this.page=1},getChatList:function(){if(this.isAll)this.isLoading=!1;else if(!this.isLoading){this.isLoading=!0,1==this.page&&(this.messageList=[]);var e;e=this.messageList.length;var t=this;this.$api.sendRequest({url:"/servicer/api/chat/dialogs",data:{servicer_id:this.servicer_id,page:this.page,limit:15,site_id:t.siteId},success:function(i){if(i.code>=0&&i.data){t.page+=1,i.data.list&&i.data.list.length<15&&(t.isAll=!0);var o=[],s=i.data.list;if(s.length)for(var a=0;a<s.length;a++){var n={};0==s[a].content_type?(n.id=t.startId,n.content=0==s[a].type?s[a].consumer_say:s[a].servicer_say,n.isItMe=0==s[a].type,n.contentType="string",n.sendStatus=!0):1==s[a].content_type?(n.id=t.startId,n.isItMe=0==s[a].type,n.sku_id=s[a].goods_sku_id,n.sendStatus=!0,0==s[a].type?n.contentType="sendGood":n.contentType="goodssku"):2==s[a].content_type?(n.id=t.startId,n.isItMe=0==s[a].type,n.contentType="order",n.order_id=s[a].order_id,n.sendStatus=!0):3==s[a].content_type&&(n.id=t.startId,n.isItMe=0==s[a].type,n.contentType="image",n.image=0==s[a].type?s[a].consumer_say:s[a].servicer_say,n.sendStatus=!0),o.push(n),t.startId+=1}setTimeout((function(){if(t.page-1==1){if(t.skuId&&Object.keys(t.goodsDetails).length>0){var i={id:t.startId,isItMe:!0,contentType:"sendGood",goodsDetail:t.goodsDetails};o.push(i),t.startId+=1}if(t.orderId&&Object.keys(t.orderdetail).length>0){var s={id:t.startId,isItMe:!0,contentType:"sendOrder",orderDetail:t.orderdetail};o.push(s),t.startId+=1}}o.length&&(t.messageList=o.concat(t.messageList),t.$nextTick((function(){t.page-1==1?(setTimeout((function(){t.setPageScrollTo()}),1e3),t.isLoading=!1):(t.setPageScrollTo("#chat".concat(t.messageList.length-e)),t.isLoading=!1)})))}),500)}else this.$util.showToast({title:i.message});t.isLoading=!1,t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})}},setPageScrollTo:function(e){if(e){var t=uni.createSelectorQuery().in(this).select(e);t.boundingClientRect((function(e){uni.pageScrollTo({scrollTop:e.top-30,duration:0})})).exec()}else{var i=uni.createSelectorQuery().in(this);i.select(".room").boundingClientRect((function(e){uni.pageScrollTo({scrollTop:e.height-30,duration:0})})).exec(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}},sendGood:function(e,t){this.sendMsg("goods"),this.messageList.splice(t,1)},sendOrder:function(e,t){this.sendMsg("order"),this.messageList.splice(t,1)},sendMsg:function(e){if(!this.isNetWork){this.isNetWork=!0;var t=this;if("goods"==e){var i={};""!=this.goods_type.promotion_id&&(i.promotion_id=this.goods_type.promotion_id,i.promotion_name=this.goods_type.promotion_name),this.$api.sendRequest({url:"/servicer/api/chat/say",data:{goods_id:this.skuId,servicer_id:this.servicer_id,content_type:1,site_id:this.siteId,relate_data:JSON.stringify(i)},success:function(e){t.send=!1,t.joinData("send","goods")},complete:function(){t.isNetWork=!1}})}else if("order"==e)this.$api.sendRequest({url:"/servicer/api/chat/say",data:{order_id:this.orderId,servicer_id:this.servicer_id,site_id:this.siteId,content_type:2},success:function(e){t.send=!1,t.joinData("send","order")},complete:function(){t.isNetWork=!1}});else if("image"==e)t.joinData("send","image"),this.$api.sendRequest({url:"/servicer/api/chat/say",data:{message:this.formData.image.trim(),servicer_id:this.servicer_id,site_id:this.siteId,content_type:3},success:function(e){console.log(e,"图片上传成功")},error:function(){var e=this;t.messageList[t.messageList.length-1].sendStatus=!1,uni.createSelectorQuery().select("#editor").context((function(t){e.editorCtx=t.context,e.editorCtx.clear()})).exec()},complete:function(){t.isNetWork=!1}});else{var o=this.formData.content,s=o.replace(/<p>/,""),a=s.replace(/<\/p>/,""),n=a.replace(/<br>/,"");if("<p></p>"==o||"<p><br></p>"==o||!n.trim())return this.$util.showToast({title:"发送内容不能为空"}),uni.createSelectorQuery().select("#editor").context((function(e){t.editorCtx=e.context,t.editorCtx.clear()})).exec(),void(t.isNetWork=!1);t.joinData("send","string"),this.$api.sendRequest({url:"/servicer/api/chat/say",data:{message:this.formData.content,servicer_id:this.servicer_id,content_type:0},success:function(e){var i=this;0==e.code&&(console.log(e,"文字上传成功"),t.formData.content="<p></p>",uni.createSelectorQuery().select("#editor").context((function(e){i.editorCtx=e.context,i.editorCtx.clear()})).exec())},error:function(){var e=this;t.messageList[t.messageList.length-1].sendStatus=!1,uni.createSelectorQuery().select("#editor").context((function(t){e.editorCtx=t.context,e.editorCtx.clear()})).exec()},complete:function(){t.isNetWork=!1}})}}},joinData:function(e,t){var i=this;return(0,a.default)((0,s.default)().mark((function o(){var a;return(0,s.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:"send"==e&&(a={isItMe:!0,contentType:t,sendStatus:!0},"string"==t?a.content=i.formData.content:"order"==t?a.order_id=i.orderId:"goods"==t?a.sku_id=i.skuId:"image"==t&&(a.image=i.formData.image),i.messageList.push(a),i.$nextTick((function(){setTimeout((function(){i.setPageScrollTo()}),500)})));case 1:case"end":return o.stop()}}),o)})))()},hideLoadTips:function(e){var t=this;e?(this.ajax.loadText="消息获取成功",setTimeout((function(){t.ajax.loading=!1}),300)):(this.ajax.loading=!0,this.ajax.loadText="正在获取消息")},onPageScroll:function(e){0==e.scrollTop&&this.getChatList()},inputFocus:function(e){this.$util.isAndroid()&&this.inputFirst&&(this.inputShow=!0),this.chatMore=!1},closePopWindow:function(){this.inputFirst=0,this.chatMore=!1,this.inputShow=!1},openEmjoy:function(){var e=this;console.log(this.emjoyShow,"this.emjoyShow"),this.chatMore=!1,this.emjoyShow=!this.emjoyShow,this.$nextTick((function(){e.setPageScrollTo()}))},addEmjoy:function(e,t){var i=this;uni.createSelectorQuery().select("#editor").context((function(t){i.editorCtx=t.context,i.editorCtx.getContents({success:function(t){"<p><br></p>"==t.html&&(t.html="<p></p>");var o=i.$util.img(e),s='<img src="'+o+'" style="height=20px; width=20px; margin-left=20px;">',a=t.html.replace(/<\/p>$/,s+"</p>");i.editorCtx.setContents({html:a}),i.formData.content=a,i.emjoyShow=!1}})})).exec()},addImg:function(){var e=this;this.$util.upload(1,{path:"chatimg"},(function(t){if(!t[0])return e.$util.showToast({title:"上传失败！"}),!1;e.formData.image=t[0],e.sendMsg("image")}),"/servicer/api/chat/chatimg")}},beforeDestroy:function(){clearInterval(this.timeoutObj),this.timeoutObj=null,this.$api.sendRequest({url:"/servicer/api/chat/bye",data:{servicer_id:this.servicer_id,site_id:this.siteId},success:function(e){uni.closeSocket()}})}};t.default=c},a6b2:function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.goods[data-v-d18032ec]{padding:%?13?% %?20?%;box-sizing:border-box;width:100vw;position:relative}.goods .goods-msg[data-v-d18032ec]{width:100%;height:%?220?%;background:#fff;position:relative;display:flex;align-items:center;border-radius:%?20?%;margin:0 auto;padding:%?20?%;box-sizing:border-box}.goods .goods-msg uni-image[data-v-d18032ec]{width:%?180?%;height:%?180?%;min-width:%?180?%;border-radius:%?10?%}.goods .goods-msg .goods-item[data-v-d18032ec]{width:100%;height:%?180?%;display:flex;flex-direction:column;justify-content:space-between;padding-left:%?20?%;box-sizing:border-box}.goods .goods-msg .goods-item .title[data-v-d18032ec]{width:100%;line-height:1.4;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.goods .goods-msg .goods-item .goods-sku[data-v-d18032ec]{color:#606266}.goods .goods-msg .goods-item .goods-sku uni-text[data-v-d18032ec]{padding-left:%?20?%}.goods .goods-msg .goods-item .goods-price[data-v-d18032ec]{display:flex;align-items:center;justify-content:space-between}.goods .goods-msg .goods-item .goods-price .price .price-util[data-v-d18032ec]{font-size:%?20?%}.goods .goods-msg .goods-item .goods-price .see-shop[data-v-d18032ec]{display:flex;align-items:center}.goods .goods-msg .goods-item .goods-price .see-shop uni-text[data-v-d18032ec]{padding-top:%?4?%;padding-left:%?4?%;font-size:%?26?%}',""]),e.exports=t},be18:function(e,t,i){"use strict";i.r(t);var o=i("0a00"),s=i("fee7");for(var a in s)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(a);i("6ba1");var n=i("828b"),r=Object(n["a"])(s["default"],o["b"],o["c"],!1,null,"d18032ec",null,!1,o["a"],void 0);t["default"]=r.exports},bec7:function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */[data-v-1dcb4068] .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{background-color:#000}[data-v-1dcb4068] .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box{max-width:100%;width:100%}.imagePop[data-v-1dcb4068]{height:50vh;width:100vw;text-align:center}.imagePop uni-image[data-v-1dcb4068]{width:100%;height:100%}.chat-message[data-v-1dcb4068]{width:100%;height:100%}.chat-message .message[data-v-1dcb4068]{padding:%?13?% %?20?%;position:relative}.chat-message .left .content[data-v-1dcb4068]{padding:%?20?%;max-width:%?450?%;border-radius:%?10?%;font-size:%?30?%}.chat-message .right .content[data-v-1dcb4068]{padding:%?20?%;max-width:%?450?%;border-radius:%?10?%;font-size:%?30?%}.chat-message .content_img[data-v-1dcb4068]{height:%?200?%;width:100%;overflow:hidden;text-align:right;margin-left:%?28?%;background-position:100%;background-repeat:no-repeat;background-size:contain}.chat-message .content_img uni-image[data-v-1dcb4068]{min-height:%?80?%;min-width:%?80?%;height:100%;width:100%}.chat-message .right .content_img[data-v-1dcb4068]{margin-right:%?28?%;margin-left:0}.chat-message .message-item[data-v-1dcb4068]{display:flex;justify-content:flex-start;align-items:flex-start;align-content:flex-start;flex-wrap:nowrap;flex-direction:row}.chat-message .message-item .head_img[data-v-1dcb4068]{width:%?80?%;height:%?80?%;border-radius:50%;overflow:hidden;position:relative}.chat-message .message-item .head_img .img[data-v-1dcb4068]{width:100%;height:100%}.chat-message .message-item .contentType3[data-v-1dcb4068]{padding:0;border-radius:%?2?%;background-color:initial!important}.chat-message .message-item .contentType3 .img[data-v-1dcb4068]{width:%?200?%;height:auto;max-width:%?300?%;max-height:%?400?%}.chat-message .message-item .contentType3[data-v-1dcb4068]::after{border:none!important;display:none!important}.chat-message .message-item .content-type-right[data-v-1dcb4068]{flex-direction:row-reverse}.chat-message .message-item.right[data-v-1dcb4068]{flex-direction:row-reverse}.chat-message .message-item.right .content[data-v-1dcb4068]{background-color:#4cd964;margin-right:%?28?%;word-break:break-all;line-height:%?36?%;position:relative}.chat-message .message-item.left .content[data-v-1dcb4068]{background-color:#fff;margin-left:%?28?%;word-break:break-all;line-height:%?36?%;position:relative}.chat-message .next[data-v-1dcb4068]{width:100%;height:%?20?%}.no-connect-box[data-v-1dcb4068]{width:100%;text-align:center;margin:%?20?% 0 %?50?%}.no-connect-box .no-connect[data-v-1dcb4068]{display:inline-block;padding:0 %?20?%;height:%?40?%;background:red;margin:0 auto;background:rgba(0,0,0,.5);border-radius:%?9?%;text-align:center;line-height:%?40?%;font-size:%?22?%;color:#fff}.chat_text[data-v-1dcb4068],\r\n.chat_img[data-v-1dcb4068]{display:flex;align-items:center}.chat_text .iconfont[data-v-1dcb4068],\r\n.chat_img .iconfont[data-v-1dcb4068]{font-size:%?36?%}.chat_img[data-v-1dcb4068]{width:30%;height:%?200?%}',""]),e.exports=t},c0ed:function(e,t,i){"use strict";var o=i("2ffc"),s=i.n(o);s.a},c3979:function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.message[data-v-0d57b30a]{padding:%?13?% %?20?%;box-sizing:border-box;width:100vw;position:relative}.message .goods-item[data-v-0d57b30a]{width:100%;height:%?220?%;background:#fff;position:relative;display:flex;align-items:center;border-radius:%?20?%;margin:0 auto;padding:%?20?%;box-sizing:border-box}.message .goods-item uni-image[data-v-0d57b30a]{width:%?180?%;height:%?180?%;min-width:%?180?%}.message .goods-item .goods-info[data-v-0d57b30a]{width:100%;height:%?180?%;display:flex;flex-direction:column;justify-content:space-between;padding-left:%?20?%;box-sizing:border-box}.message .goods-item .goods-info .goods-name[data-v-0d57b30a]{width:100%;line-height:1.4;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.message .goods-item .goods-info .goods-bottom[data-v-0d57b30a]{display:flex;justify-content:space-between;align-items:flex-end}.message .goods-item .goods-info .goods-bottom uni-text[data-v-0d57b30a]{line-height:1}.message .goods-item .goods-info .goods-bottom .goods-price[data-v-0d57b30a]{display:flex;align-items:flex-end;padding-bottom:%?10?%;font-weight:500}.message .goods-item .goods-info .goods-bottom .goods-price .goods-price-sign[data-v-0d57b30a]{font-size:%?20?%}.message .goods-item .goods-info .goods-bottom .goods-option[data-v-0d57b30a]{width:%?150?%;height:%?50?%;line-height:%?50?%;text-align:center;border-radius:%?10?%;color:#fff}.message .goods-item .disabled[data-v-0d57b30a]{background:#e5e5e5}',""]),e.exports=t},c41e:function(e,t,i){"use strict";i.r(t);var o=i("3d90"),s=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(a);t["default"]=s.a},cb33:function(e,t,i){"use strict";i.d(t,"b",(function(){return s})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){return o}));var o={pageMeta:i("7854").default,loadingCover:i("c003").default},s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":e.themeColor}}),i("v-uni-view",{staticClass:"container",attrs:{id:"container"}},[i("v-uni-view",{staticClass:"room",class:1==e.keyWordsConfig.is_open?"active":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopWindow.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"tips",class:{show:e.isLoading}},[e._v("正在获取消息")]),i("v-uni-view",{staticClass:"room-content-box"},e._l(e.messageList,(function(t,o){return i("v-uni-view",{key:o,attrs:{id:"chat"+o}},[i("chat-message",{attrs:{message:t,send:e.send},on:{sendGood:function(t){arguments[0]=t=e.$handleEvent(t),e.sendGood(t,o)},sendOrder:function(t){arguments[0]=t=e.$handleEvent(t),e.sendOrder(t,o)}}})],1)})),1),e.chatBottom?i("v-uni-view",{staticClass:"paddingbottom",attrs:{id:"paddingbottom"}}):e._e()],1),i("v-uni-view",{staticClass:"input-content",class:1==e.keyWordsConfig.is_open?"active":""},[1==e.keyWordsConfig.is_open?i("v-uni-view",{staticClass:"keyWords"},e._l(e.keyWordsConfig.keyword_list,(function(t,o){return i("v-uni-text",{key:o,on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.formData.content=t.keyword,e.sendMsg("message")}}},[e._v(e._s(t.keyword))])})),1):e._e(),i("v-uni-view",{staticClass:"input-box"},[i("v-uni-view",{staticClass:"iconfont icon-biaoqing padding-right ",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openEmjoy()}}}),i("v-uni-editor",{staticClass:"message-edit",attrs:{id:"editor"},on:{ready:function(t){arguments[0]=t=e.$handleEvent(t),e.onEditorReady.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.inputFocus.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),e.onEditorinput()},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopWindow()}},model:{value:e.formData.content,callback:function(t){e.$set(e.formData,"content",t)},expression:"formData.content"}}),i("v-uni-view",{staticClass:"iconfont icon-jiahao01 padding-right",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openChatMore()}}}),i("v-uni-view",{staticClass:"send_btn color-base-bg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendMsg("message")}}},[e._v("发送")])],1),e.inputShow&&e.inputFirst?i("v-uni-view",{staticClass:"inputShow"}):e._e(),e.emjoyShow?i("v-uni-view",{on:{touchmove:function(t){t.preventDefault(),t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[i("v-uni-view",{staticClass:"emjoy-box"},[i("v-uni-scroll-view",{staticClass:"emjoy-content",attrs:{"scroll-y":"true"}},e._l(e.emjoyList,(function(t,o){return i("v-uni-view",{key:o,staticClass:"emjoy-item",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.addEmjoy(t,o)}}},[i("v-uni-image",{attrs:{src:e.$util.img(t)}})],1)})),1)],1)],1):e._e(),e.chatMore?i("v-uni-view",{staticClass:"more_send"},[i("v-uni-view",{staticClass:"more_send-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.addImg()}}},[i("v-uni-text",{staticClass:"iconfont icon-tupian "}),i("v-uni-view",[e._v("图片")])],1)],1):e._e()],1),i("loading-cover",{ref:"loadingCover"})],1)],1)},a=[]},cc1b:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},s={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(e){return-1!==["dark","light"].indexOf(e)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var e=this,t=getCurrentPages()[0];this.$pageVm=t.$vm||t,uni.onWindowResize((function(t){e.$emit("resize",t)})),this.$pageVm.$on("hook:onPageScroll",(function(t){e.$emit("scroll",t)})),this.$watch("backgroundTextStyle",(function(){e.setBackgroundTextStyle()})),this.$watch((function(){return[e.rootFontSize,e.pageStyle]}),(function(){e.setPageMeta()})),this.$watch((function(){return[e.backgroundColor,e.backgroundColorTop,e.backgroundColorBottom]}),(function(){e.setBackgroundColor()})),this.$watch((function(){return[e.scrollTop,e.scrollDuration]}),(function(){e.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(e,t){e.setStyle({pullToRefresh:{support:t,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var e=this,t=String(this.scrollTop);if(-1!==t.indexOf("rpx")&&(t=uni.upx2px(t.replace("rpx",""))),t=parseFloat(t),!isNaN(t)){var i=function i(s){s.scrollTop===t&&(e.$pageVm.$off("hook:onPageScroll",i),e.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:t,duration:this.scrollDuration,success:function(){e.$pageVm.$on("hook:onPageScroll",i)}})}}}};t.default=s},d8c8:function(e,t,i){"use strict";i.r(t);var o=i("a2b2"),s=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(a);t["default"]=s.a},e112:function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.message[data-v-07831da6]{padding:%?13?% %?20?%;box-sizing:border-box;width:100vw;position:relative}.message .goods-item[data-v-07831da6]{width:100%;height:%?220?%;background:#fff;position:relative;display:flex;align-items:center;border-radius:%?20?%;margin:0 auto;padding:%?20?%;box-sizing:border-box}.message .goods-item uni-image[data-v-07831da6]{width:%?180?%;height:%?180?%}.message .goods-item .goods-info[data-v-07831da6]{width:100%;height:%?180?%;display:flex;flex-direction:column;justify-content:space-between;padding-left:%?20?%;box-sizing:border-box}.message .goods-item .goods-info .goods-name[data-v-07831da6]{width:100%;line-height:1.4;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;margin-bottom:%?10?%}.message .goods-item .goods-info .goods-bottom[data-v-07831da6]{display:flex;justify-content:space-between;align-items:flex-end}.message .goods-item .goods-info .goods-bottom uni-text[data-v-07831da6]{line-height:1}.message .goods-item .goods-info .goods-bottom .goods-price[data-v-07831da6]{display:flex;align-items:flex-end;padding-bottom:%?10?%;font-weight:500}.message .goods-item .goods-info .goods-bottom .goods-price .goods-price-sign[data-v-07831da6]{font-size:%?20?%}.message .goods-item .goods-info .goods-bottom .goods-option[data-v-07831da6]{width:%?150?%;height:%?50?%;line-height:%?50?%;text-align:center;border-radius:%?10?%;color:#fff}.message .goods-item .disabled[data-v-07831da6]{background:#e5e5e5}',""]),e.exports=t},e7296:function(e,t,i){"use strict";i.r(t);var o=i("481a"),s=i("c41e");for(var a in s)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(a);i("4aff");var n=i("828b"),r=Object(n["a"])(s["default"],o["b"],o["c"],!1,null,"0d57b30a",null,!1,o["a"],void 0);t["default"]=r.exports},f48d:function(e,t,i){"use strict";i.r(t);var o=i("cc1b"),s=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(a);t["default"]=s.a},fee7:function(e,t,i){"use strict";i.r(t);var o=i("4883"),s=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(a);t["default"]=s.a}}]);