(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_tool-goods-coupon"],{"015d":function(t,e,i){"use strict";i.r(e);var o=i("0f46"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},"0859":function(t,e,i){var o=i("6565");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("6eb611ea",o,!0,{sourceMap:!1,shadowMode:!1})},"0eed":function(t,e,i){"use strict";i.r(e);var o=i("a5234"),n=i("afc0");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("c3b8");var s=i("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"9a2c457a",null,!1,o["a"],void 0);e["default"]=r.exports},"0f46":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"hover-nav",props:{need:{type:Boolean,default:!1}},data:function(){return{pageCount:0,fixBtnShow:!1}},created:function(){this.pageCount=getCurrentPages().length},methods:{}};e.default=o},"44fa":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("5ef2"),i("4100"),i("c223");var o={data:function(){return{list:[],sort:1,types:"",couponBtnSwitch:!1,mpShareData:null}},onLoad:function(t){var e=this;if(setTimeout((function(){e.addonIsExist.coupon||(e.$util.showToast({title:"商家未开启优惠券",mask:!0,duration:2e3}),setTimeout((function(){e.$util.redirectTo("/pages/index/index")}),2e3))}),1e3),t.source_member&&uni.setStorageSync("source_member",t.source_member),t.scene){var i=decodeURIComponent(t.scene);i=i.split("&"),i.length&&i.forEach((function(t){-1!=t.indexOf("sku_id")&&(e.skuId=t.split("-")[1]),-1!=t.indexOf("m")&&uni.setStorageSync("source_member",t.split("-")[1]),-1!=t.indexOf("is_test")&&uni.setStorageSync("is_test",1)}))}},onShow:function(){this.storeToken&&uni.getStorageSync("source_member")&&this.$util.onSourceMember(uni.getStorageSync("source_member"))},onShareAppMessage:function(){return this.mpShareData.appMessage},onShareTimeline:function(){return this.mpShareData.timeLine},methods:{changeSort:function(t,e){this.list=[],this.sort=t,this.types=e,this.$refs.mescroll.refresh(!1)},liClick:function(t,e){if(2==t.useState)return!1;0==t.useState?this.receiveCoupon(t,e):3==t.useState||4==t.useState?this.$util.redirectTo("/pages_tool/member/coupon",{state:4==t.useState?2:t.useState}):this.toGoodsList(t,e)},receiveCoupon:function(t,e){var i=this;this.couponBtnSwitch||(this.couponBtnSwitch=!0,this.storeToken?this.$api.sendRequest({url:"/coupon/api/coupon/receive",data:{coupon_type_id:t.coupon_type_id,get_type:2},success:function(e){i.couponBtnSwitch=!1;var o="领取成功，快去使用吧",n=i.list;if(e.code<0&&(o=e.message),1==e.data.is_exist)for(var a=0;a<n.length;a++)n[a].coupon_type_id==t.coupon_type_id&&(n[a].useState=1);else for(var s=0;s<n.length;s++)n[s].coupon_type_id==t.coupon_type_id&&(n[s].received_type=e.data.type,n[s].useState=2);i.$util.showToast({title:o})},fail:function(t){i.couponBtnSwitch=!1}}):(this.couponBtnSwitch=!1,this.$refs.login.open("/pages_tool/goods/coupon")))},getMemberCouponList:function(t){var e=this;this.$api.sendRequest({url:"/coupon/api/coupon/typepagelists",data:{page:t.num,page_size:t.size,sort:this.sort,type:this.types},success:function(i){var o=[],n=i.message;0==i.code&&i.data?o=i.data.list:e.$util.showToast({title:n}),t.endSuccess(o.length),o.length&&o.forEach((function(t){t.count==t.lead_count?t.useState=2:0==t.max_fetch||0!=t.max_fetch&&!t.member_coupon_num||0!=t.max_fetch&&t.member_coupon_num&&t.max_fetch>t.member_coupon_num?t.useState=0:t.wait_coupon_num?t.useState=1:t.lose_coupon_num?t.useState=3:t.use_coupon_num&&(t.useState=4)})),1==t.num&&(e.list=[]),e.list=e.list.concat(o),e.$refs.loadingCover&&e.$refs.loadingCover.hide()},fail:function(){t.endErr(),this.$refs.loadingCover&&this.$refs.loadingCover.hide()}})},imageError:function(t){this.list[t].logo=this.$util.getDefaultImage().goods,this.$forceUpdate()},couponImageError:function(t){this.list[t].image=this.$util.img("public/uniapp/goods/coupon.png"),this.$forceUpdate()},toGoodsList:function(t){1!=t.goods_type?this.$util.redirectTo("/pages/goods/list",{coupon:t.coupon_type_id}):this.$util.redirectTo("/pages/goods/list",{})}}};e.default=o},6565:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.coupon-head[data-v-9a2c457a]{padding:%?20?% %?50?%;display:flex;background:#fff}.coupon-head .sort[data-v-9a2c457a]{border:%?2?% solid #c5c5c5;padding:%?1?% %?20?%;border-radius:%?10?%;cursor:pointer;margin-right:%?15?%}.coupon-listone[data-v-9a2c457a]{padding:0 %?30?%}.coupon-listone .item[data-v-9a2c457a]{display:flex;background-color:#fff2f0;background-size:100% 100%;border-radius:%?20?%;align-items:stretch;margin-top:%?20?%;overflow:hidden}.coupon-listone .item .item-base[data-v-9a2c457a]{position:relative;width:%?197?%;min-width:%?197?%;text-align:center;background:linear-gradient(270deg,var(--bg-color),var(--bg-color-shallow));background-repeat:no-repeat;background-size:100% 100%;padding:%?38?% %?10?% %?38?% %?18?%}.coupon-listone .item .item-base.disabled[data-v-9a2c457a]{background:#dedede}.coupon-listone .item .item-base .coupon-line[data-v-9a2c457a]{position:absolute;right:0;top:0;height:100%}.coupon-listone .item .item-base > uni-view[data-v-9a2c457a]{height:auto;position:relative;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.coupon-listone .item .item-base .use_price[data-v-9a2c457a]{font-size:%?60?%;line-height:1;color:#fff}.coupon-listone .item .item-base .use_price uni-text[data-v-9a2c457a]{font-size:%?32?%}.coupon-listone .item .item-base .use_price.disabled[data-v-9a2c457a]{color:#909399}.coupon-listone .item .item-base .use_condition[data-v-9a2c457a]{color:#fff;margin-top:%?20?%}.coupon-listone .item .item-base .use_condition.margin_top_none[data-v-9a2c457a]{margin-top:0}.coupon-listone .item .item-base .use_condition.disabled[data-v-9a2c457a]{color:#909399}.coupon-listone .item .item-base[data-v-9a2c457a]::after{position:absolute;content:"";background-color:#f8f8f8;left:0;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);height:%?30?%;width:%?15?%;border-radius:0 %?30?% %?30?% 0}.coupon-listone .item .item-btn[data-v-9a2c457a]{width:%?160?%;min-width:%?160?%;align-self:center;position:relative}.coupon-listone .item .item-btn uni-view[data-v-9a2c457a]{width:%?100?%;height:%?50?%;border-radius:%?10?%;line-height:%?50?%;margin:auto;text-align:center;background-image:linear-gradient(90deg,var(--bg-color),var(--bg-color-shallow));color:var(--btn-text-color);font-size:%?24?%}.coupon-listone .item .item-btn uni-view.disabled[data-v-9a2c457a]{background:#dedede!important;color:#909399!important}.coupon-listone .item .item-btn uni-view.to-use[data-v-9a2c457a]{border:%?2?% solid var(--bg-color);background:transparent;color:var(--bg-color)}.coupon-listone .item .item-btn[data-v-9a2c457a]::after{position:absolute;content:"";background-color:#f8f8f8;right:0;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);height:%?30?%;width:%?15?%;border-radius:%?30?% 0 0 %?30?%}.coupon-listone .item .item-info[data-v-9a2c457a]{flex:1;display:flex;flex-direction:column;justify-content:space-between;margin-left:%?20?%;overflow:hidden;background-repeat-x:no-repeat;background-repeat-y:repeat}.coupon-listone .item .item-info .use_time[data-v-9a2c457a]{padding:%?20?% 0;border-top:%?2?% dashed #ccc;font-size:%?20?%;color:#909399}.coupon-listone .item .item-info .use_title[data-v-9a2c457a]{font-size:%?28?%;font-weight:500;padding:%?20?% 0}.coupon-listone .item .item-info .use_title .title[data-v-9a2c457a]{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.coupon-listone .item .item-info .use_title .max_price[data-v-9a2c457a]{font-weight:400;font-size:%?24?%}.empty[data-v-9a2c457a]{margin-top:%?200?%}.cf-container[data-v-9a2c457a]{background:#fff;overflow:hidden}.tab[data-v-9a2c457a]{display:flex;justify-content:space-between;height:%?86?%}.tab > uni-view[data-v-9a2c457a]{text-align:center;width:33%;height:%?86?%}.tab > uni-view uni-text[data-v-9a2c457a]{display:inline-block;line-height:%?86?%;height:%?80?%;font-size:%?30?%}.active[data-v-9a2c457a]{border-bottom:%?4?% solid}.truncate[data-v-9a2c457a]{overflow:hidden;-o-text-overflow:ellipsis;text-overflow:ellipsis;white-space:nowrap}',""]),t.exports=e},7854:function(t,e,i){"use strict";i.r(e);var o=i("8ba8"),n=i("f48d");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);var s=i("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=r.exports},"8ba8":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticStyle:{display:"none"}},[this._t("default")],2)},n=[]},a5234:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return o}));var o={pageMeta:i("7854").default,nsEmpty:i("52a6").default,loadingCover:i("c003").default,nsLogin:i("2910").default,hoverNav:i("c1f1").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("page-meta",{attrs:{"page-style":t.themeColor}}),i("v-uni-view",[i("v-uni-view",{staticClass:"cf-container color-line-border"},[i("v-uni-view",{staticClass:"tab"},[i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSort(1)}}},[i("v-uni-text",{class:1==t.sort?"color-base-text active color-base-border-bottom":""},[t._v("全部")])],1),i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSort(2,"reward")}}},[i("v-uni-text",{class:2==t.sort?"color-base-text active color-base-border-bottom":""},[t._v("满减券")])],1),i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSort(3,"discount")}}},[i("v-uni-text",{class:3==t.sort?"color-base-text active color-base-border-bottom":""},[t._v("折扣券")])],1),i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeSort(4,"no_threshold")}}},[i("v-uni-text",{class:4==t.sort?"color-base-text active color-base-border-bottom":""},[t._v("无门槛券")])],1)],1)],1),i("mescroll-uni",{ref:"mescroll",attrs:{top:"100"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getMemberCouponList.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[i("v-uni-view",{staticClass:"coupon-listone"},t._l(t.list,(function(e,o){return i("v-uni-view",{key:o,staticClass:"item",style:{backgroundColor:2==e.useState?"#F2F2F2":"var(--main-color-shallow)"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.liClick(e,o)}}},[i("v-uni-view",{staticClass:"item-base",class:{disabled:2==e.useState}},[i("v-uni-image",{staticClass:"coupon-line",attrs:{mode:"heightFix",src:t.$util.img("public/uniapp/coupon/coupon_line.png")}}),i("v-uni-view",["reward"==e.type?i("v-uni-view",{staticClass:"use_price price-font",class:{disabled:2==e.useState}},[i("v-uni-text",[t._v("￥")]),t._v(t._s(parseFloat(e.money)))],1):"discount"==e.type?i("v-uni-view",{staticClass:"use_price price-font",class:{disabled:2==e.useState}},[t._v(t._s(parseFloat(e.discount))),i("v-uni-text",[t._v("折")])],1):t._e(),e.at_least>0?i("v-uni-view",{staticClass:"use_condition font-size-tag",class:{disabled:2==e.useState}},[t._v("满"+t._s(e.at_least)+"元可用")]):i("v-uni-view",{staticClass:"use_condition font-size-tag",class:{disabled:2==e.useState}},[t._v("无门槛优惠券")])],1)],1),i("v-uni-view",{staticClass:"item-info"},[i("v-uni-view",{staticClass:"use_title"},[i("v-uni-view",{staticClass:"title"},[t._v(t._s(e.coupon_name))]),i("v-uni-view",{staticClass:"max_price",class:{disabled:2==e.useState}},[t._v(t._s(e.goods_type_name))]),"0.00"!=e.discount_limit?i("v-uni-view",{staticClass:"max_price"},[t._v("(最大优惠"+t._s(e.discount_limit)+"元)")]):t._e(),i("v-uni-view",{staticClass:"max_price",class:{disabled:2==e.useState}},[t._v(t._s(e.use_channel_name))]),"online"!=e.use_channel?i("v-uni-view",{staticClass:"max_price  truncate",class:{disabled:2==e.useState}},[t._v(t._s("all"===e.use_store?"适用门店：全部门店":"适用门店："+e.use_store_name))]):t._e()],1),0==e.validity_type?i("v-uni-view",{staticClass:"use_time"},[t._v("有效期："+t._s(t.$util.timeStampTurnTime(e.end_time)))]):1==e.validity_type?i("v-uni-view",{staticClass:"use_time"},[t._v("有效期：领取之日起"+t._s(e.fixed_term)+"日内有效")]):i("v-uni-view",{staticClass:"use_time"},[t._v("有效期：长期有效")])],1),i("v-uni-view",{staticClass:"item-btn"},[0==e.useState?i("v-uni-view",{on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.receiveCoupon(e,o)}}},[t._v("去领取")]):t._e(),1==e.useState?i("v-uni-view",{staticClass:"to-use",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.toGoodsList(e,o)}}},[t._v("去使用")]):t._e(),2==e.useState?i("v-uni-view",{staticClass:"disabled"},[t._v("已抢光")]):t._e(),3==e.useState?i("v-uni-view",{staticClass:"disabled"},[t._v("已失效")]):t._e(),4==e.useState?i("v-uni-view",{staticClass:"disabled"},[t._v("已使用")]):t._e()],1)],1)})),1),0==t.list.length?i("v-uni-view",[i("ns-empty",{attrs:{text:"暂无可领取的优惠券",isIndex:!1}})],1):t._e()],1)],2),i("loading-cover",{ref:"loadingCover"}),i("ns-login",{ref:"login"}),i("hover-nav")],1)],1)},a=[]},a725:function(t,e,i){"use strict";var o=i("ac2a"),n=i.n(o);n.a},ac2a:function(t,e,i){var o=i("f714");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("967d").default;n("1a69ffc2",o,!0,{sourceMap:!1,shadowMode:!1})},afc0:function(t,e,i){"use strict";i.r(e);var o=i("44fa"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},c1f1:function(t,e,i){"use strict";i.r(e);var o=i("fa1d"),n=i("015d");for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);i("a725");var s=i("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"c1934e78",null,!1,o["a"],void 0);e["default"]=r.exports},c3b8:function(t,e,i){"use strict";var o=i("0859"),n=i.n(o);n.a},cc1b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5ef2"),i("64aa"),i("5c47"),i("a1c1"),i("e838");var o={type:"scrolldone",target:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},currentTarget:{id:"",offsetLeft:0,offsetTop:0,dataset:{}},detail:{}},n={props:{backgroundTextStyle:{type:String,default:"dark",validator:function(t){return-1!==["dark","light"].indexOf(t)}},backgroundColor:{type:String,default:"#ffffff"},backgroundColorTop:{type:String,default:"#ffffff"},backgroundColorBottom:{type:String,default:"#ffffff"},scrollTop:{type:String,default:""},scrollDuration:{type:Number,default:300},pageStyle:{type:String,default:""},enablePullDownRefresh:{type:[Boolean,String],default:!1},rootFontSize:{type:String,default:""}},created:function(){var t=this,e=getCurrentPages()[0];this.$pageVm=e.$vm||e,uni.onWindowResize((function(e){t.$emit("resize",e)})),this.$pageVm.$on("hook:onPageScroll",(function(e){t.$emit("scroll",e)})),this.$watch("backgroundTextStyle",(function(){t.setBackgroundTextStyle()})),this.$watch((function(){return[t.rootFontSize,t.pageStyle]}),(function(){t.setPageMeta()})),this.$watch((function(){return[t.backgroundColor,t.backgroundColorTop,t.backgroundColorBottom]}),(function(){t.setBackgroundColor()})),this.$watch((function(){return[t.scrollTop,t.scrollDuration]}),(function(){t.pageScrollTo()}))},beforeMount:function(){this.setBackgroundColor(),(this.rootFontSize||this.pageStyle)&&this.setPageMeta(),this.backgroundTextStyle&&this.setBackgroundTextStyle()},mounted:function(){this.scrollTop&&this.pageScrollTo()},methods:{setPullDownRefresh:function(t,e){t.setStyle({pullToRefresh:{support:e,style:"Android"===plus.os.name?"circle":"default"}})},setPageMeta:function(){uni.setPageMeta({pageStyle:this.pageStyle,rootFontSize:this.rootFontSize})},setBackgroundTextStyle:function(){},setBackgroundColor:function(){},pageScrollTo:function(){var t=this,e=String(this.scrollTop);if(-1!==e.indexOf("rpx")&&(e=uni.upx2px(e.replace("rpx",""))),e=parseFloat(e),!isNaN(e)){var i=function i(n){n.scrollTop===e&&(t.$pageVm.$off("hook:onPageScroll",i),t.$emit("scrolldone",o))};uni.pageScrollTo({scrollTop:e,duration:this.scrollDuration,success:function(){t.$pageVm.$on("hook:onPageScroll",i)}})}}}};e.default=n},f48d:function(t,e,i){"use strict";i.r(e);var o=i("cc1b"),n=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);e["default"]=n.a},f714:function(t,e,i){var o=i("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.container-box[data-v-c1934e78]{width:100%}.container-box .item-wrap[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap .image-box[data-v-c1934e78]{border-radius:%?10?%}.container-box .item-wrap uni-image[data-v-c1934e78]{width:100%;height:auto;border-radius:%?10?%;will-change:transform}.fixed-box[data-v-c1934e78]{position:fixed;right:%?20?%;bottom:%?300?%;z-index:10;background:#fff;box-shadow:%?2?% %?2?% %?22?% rgba(0,0,0,.3);border-radius:%?120?%;padding:%?20?% 0;display:flex;justify-content:center;flex-direction:column;width:%?120?%;box-sizing:border-box;transition:.3s;overflow:hidden}.fixed-box .btn-item[data-v-c1934e78]{display:flex;justify-content:center;text-align:center;flex-direction:column;line-height:1;margin:%?14?% 0;transition:.1s}.fixed-box .btn-item uni-text[data-v-c1934e78]{font-size:%?44?%;font-weight:700}.fixed-box .btn-item uni-view[data-v-c1934e78]{font-size:%?26?%;font-weight:700}.fixed-box .btn-item.show[data-v-c1934e78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fixed-box .btn-item.icon-xiala[data-v-c1934e78]{margin:0;margin-top:%?0.1?%}',""]),t.exports=e},fa1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return 1==t.pageCount||t.need?i("v-uni-view",{staticClass:"fixed-box",style:{height:t.fixBtnShow?"330rpx":"120rpx"}},[t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/index/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-shouye1"}),i("v-uni-view",[t._v("首页")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/index")}}},[i("v-uni-text",{staticClass:"iconfont icon-yonghu"}),i("v-uni-view",[t._v("我的")])],1):t._e(),t.fixBtnShow?i("v-uni-view",{staticClass:"btn-item icon-xiala",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-text",{staticClass:"iconfont icon-unfold"})],1):i("v-uni-view",{staticClass:"btn-item switch",class:{show:t.fixBtnShow},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.fixBtnShow?t.fixBtnShow=!1:t.fixBtnShow=!0}}},[i("v-uni-view",[t._v("快捷")]),i("v-uni-view",[t._v("导航")])],1)],1):t._e()},n=[]}}]);