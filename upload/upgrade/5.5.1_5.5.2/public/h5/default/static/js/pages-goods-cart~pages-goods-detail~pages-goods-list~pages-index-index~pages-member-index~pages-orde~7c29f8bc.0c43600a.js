(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-cart~pages-goods-detail~pages-goods-list~pages-index-index~pages-member-index~pages-orde~7c29f8bc"],{"02ae":function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){return s}));var s={uniPopup:e("d745").default,nsForm:e("ae30").default,nsLogin:e("2910").default},o=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{staticClass:"goods-sku",on:{touchmove:function(i){i.preventDefault(),i.stopPropagation(),arguments[0]=i=t.$handleEvent(i)}}},[e("uni-popup",{ref:"skuPopup",staticClass:"sku-layer",attrs:{type:"bottom"},on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.popclose.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"sku-info",style:{height:t.skuHeight}},[e("v-uni-view",{staticClass:"header"},["point"==t.type&&t.goodsDetail.type&&1!=t.goodsDetail.type?[2==t.goodsDetail.type?e("v-uni-view",{staticClass:"img-wrap"},[e("v-uni-image",{attrs:{src:t.goodsDetail.image?t.$util.img(t.goodsDetail.image):t.$util.img("public/uniapp/point/coupon.png"),mode:"aspectFit"}})],1):t._e(),3==t.goodsDetail.type?e("v-uni-view",{staticClass:"img-wrap"},[e("v-uni-image",{attrs:{src:t.goodsDetail.image?t.$util.img(t.goodsDetail.image):t.$util.img("public/uniapp/point/hongbao.png"),mode:"aspectFit"}})],1):t._e()]:e("v-uni-view",{staticClass:"img-wrap",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.previewMedia()}}},[e("v-uni-image",{attrs:{src:t.$util.img(t.goodsDetail.sku_image,{size:"mid"}),mode:"aspectFit"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.imageError()}}})],1),e("v-uni-view",{staticClass:"main"},["point"==t.type?[1==t.goodsDetail.type?[e("v-uni-view",{staticClass:"price-wrap"},[e("v-uni-text",{staticClass:"price price-color font-size-toolbar"},[t._v(t._s(t.goodsDetail.point)+"积分")]),"0.00"!=t.goodsDetail.exchange_price&&t.goodsDetail.pay_type>0?[e("v-uni-text",{staticClass:"unit price-color font-size-tag"},[t._v("+")]),e("v-uni-text",{staticClass:"price price-color font-size-toolbar price-font"},[t._v(t._s(t.$lang("common.currencySymbol"))+t._s(t.goodsDetail.exchange_price))])]:t._e()],2),t.goodsDetail.stock_show?e("v-uni-view",{staticClass:"stock"},[t._v("库存"+t._s(t.goodsDetail.stock)+t._s(t.goodsDetail.unit))]):t._e()]:[e("v-uni-view",{staticClass:"price-wrap"},[e("v-uni-view",{staticClass:"price font-size-toolbar"},[t._v(t._s(t.goodsDetail.name))])],1),e("v-uni-view",{staticClass:"stock",staticStyle:{height:"auto"}},[t._v("积分："),e("v-uni-text",{staticClass:"price-color"},[t._v(t._s(t.goodsDetail.point))])],1),e("v-uni-view",{staticClass:"stock"},[t._v("库存："),e("v-uni-text",{staticClass:"color-base-text"},[t._v(t._s(t.goodsDetail.stock)+t._s(t.goodsDetail.unit))])],1)]]:"presale"==t.type?[e("v-uni-view",{staticClass:"price-wrap"},[e("v-uni-text",{staticClass:"unit price-style small"},[t._v("￥")]),e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.presale_deposit).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.presale_deposit).toFixed(2).split(".")[1]))]),t.balance>0?e("v-uni-view",{staticClass:"balance"},[e("v-uni-text",[t._v("尾款")]),e("v-uni-text",{staticClass:"unit price-color font-size-tag price-font"},[t._v("￥")]),e("v-uni-text",{staticClass:"price-color price-font"},[t._v(t._s(t.balance))])],1):t._e()],1),e("v-uni-view",{staticClass:"stock presale-stock"},[t.goodsDetail.stock_show?[t._v("库存"+t._s(t.goodsDetail.stock)+t._s(t.goodsDetail.unit))]:t._e()],2)]:["pintuan"==t.type?e("v-uni-view",{staticClass:"price-wrap"},["ordinary"==t.goodsDetail.pintuan_type?[t.goodsDetail.group_id&&t.goodsDetail.group_id>0?[e("v-uni-text",{staticClass:"unit price-style small"},[t._v("￥")]),e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.pintuanPrice(t.goodsDetail)).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.pintuanPrice(t.goodsDetail)).toFixed(2).split(".")[1]))])]:[e("v-uni-text",{staticClass:"unit price-style small"},[t._v("￥")]),e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.promotion_price).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.promotion_price).toFixed(2).split(".")[1]))])]]:t._e(),"ladder"==t.goodsDetail.pintuan_type?[e("v-uni-text",{staticClass:"unit price-style small"},[t._v("￥")]),e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.pintuanPrice(t.goodsDetail)).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.pintuanPrice(t.goodsDetail)).toFixed(2).split(".")[1]))])]:t._e()],2):"groupbuy"==t.type?e("v-uni-view",{staticClass:"price-wrap"},[e("v-uni-text",{staticClass:"unit price-style small"},[t._v("￥")]),e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.groupbuy_price).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.groupbuy_price).toFixed(2).split(".")[1]))])],1):"topic"==t.type?e("v-uni-view",{staticClass:"price-wrap"},[e("v-uni-text",{staticClass:"unit price-style small"},[t._v("￥")]),e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.topic_price).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.topic_price).toFixed(2).split(".")[1]))])],1):"seckill"==t.type?e("v-uni-view",{staticClass:"price-wrap"},[e("v-uni-text",{staticClass:"unit price-style small"},[t._v("￥")]),e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.seckill_price).toFixed(2).split(".")[0]))]),e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.seckill_price).toFixed(2).split(".")[1]))])],1):e("v-uni-view",{staticClass:"price-wrap"},[e("v-uni-text",{staticClass:"unit price-style small"},[t._v("￥")]),t.goodsDetail.discount_price>0?[t.goodsDetail.member_price?[t.showPrice(t.goodsDetail.discount_price)<t.showPrice(t.goodsDetail.member_price)?e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.discount_price).toFixed(2).split(".")[0]))]):e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.member_price).toFixed(2).split(".")[0]))]),t.showPrice(t.goodsDetail.discount_price)<t.showPrice(t.goodsDetail.member_price)?e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.discount_price).toFixed(2).split(".")[1]))]):e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.member_price).toFixed(2).split(".")[1]))])]:[t.showPrice(t.goodsDetail.discount_price)<t.showPrice(t.goodsDetail.price)?e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.discount_price).toFixed(2).split(".")[0]))]):e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.price).toFixed(2).split(".")[0]))]),t.showPrice(t.goodsDetail.discount_price)<t.showPrice(t.goodsDetail.price)?e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.discount_price).toFixed(2).split(".")[1]))]):e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.price).toFixed(2).split(".")[1]))])]]:[t.goodsDetail.member_price?e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.member_price).toFixed(2).split(".")[0]))]):e("v-uni-text",{staticClass:"price price-style large"},[t._v(t._s(parseFloat(t.goodsDetail.price).toFixed(2).split(".")[0]))]),t.goodsDetail.member_price?e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.member_price).toFixed(2).split(".")[1]))]):e("v-uni-text",{staticClass:"unit price-style small"},[t._v("."+t._s(parseFloat(t.goodsDetail.price).toFixed(2).split(".")[1]))])]],2),t.goodsDetail.stock_show?e("v-uni-view",{staticClass:"stock"},[t._v("库存"+t._s(t.goodsDetail.stock)+t._s(t.goodsDetail.unit))]):t._e()],t.goodsDetail.sku_spec_format?e("v-uni-view",{staticClass:"sku-name font-size-tag"},[e("v-uni-text",{staticClass:"color-tip"},[t._v("已选规格：")]),t._l(t.goodsDetail.sku_spec_format,(function(i,s){return e("v-uni-text",{key:s,staticClass:"spec-value"},[t._v(t._s(i.spec_value_name))])}))],2):t._e()],2),e("v-uni-view",{staticClass:"sku-close iconfont icon-close",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.closeSkuPopup()}}})],2),e("v-uni-view",{staticClass:"body-item"},[e("v-uni-scroll-view",{staticClass:"wrap",attrs:{"scroll-y":!0}},["pintuan"!=t.type||"ladder"!=t.goodsDetail.pintuan_type||t.goodsDetail.group_id?t._e():e("v-uni-view",{staticClass:"sku-list-wrap"},[e("v-uni-text",{staticClass:"title font-size-base"},[t._v("")]),e("v-uni-view",{staticClass:"items",class:{selected:"pintuan_num"==t.pintuan_num_field},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.pintuanChange("pintuan_num")}}},[e("v-uni-text",[t._v(t._s(t.goodsDetail.pintuan_num)+"人团")])],1),t.goodsDetail.pintuan_num_2>0?e("v-uni-view",{staticClass:"items",class:{selected:"pintuan_num_2"==t.pintuan_num_field},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.pintuanChange("pintuan_num_2")}}},[e("v-uni-text",[t._v(t._s(t.goodsDetail.pintuan_num_2)+"人团")])],1):t._e(),t.goodsDetail.pintuan_num_3>0?e("v-uni-view",{staticClass:"items",class:{selected:"pintuan_num_3"==t.pintuan_num_field},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.pintuanChange("pintuan_num_3")}}},[e("v-uni-text",[t._v(t._s(t.goodsDetail.pintuan_num_3)+"人团")])],1):t._e()],1),t._l(t.goodsDetail.goods_spec_format,(function(i,s){return e("v-uni-view",{key:s,staticClass:"sku-list-wrap"},[e("v-uni-text",{staticClass:"title font-size-base"},[t._v(t._s(i.spec_name))]),t._l(i.value,(function(i,o){return e("v-uni-view",{key:o,staticClass:"items",class:{selected:i["selected"]||t.skuId==i.sku_id,disabled:i["disabled"]||!i["selected"]&&t.disabled},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.change(i.sku_id,i.spec_id)}}},[i.image?e("v-uni-image",{attrs:{src:t.$util.img(i.image,{size:"small"})},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.valueImageError(s,o)}}}):t._e(),e("v-uni-text",[t._v(t._s(i.spec_value_name))]),0==i.stock?e("v-uni-view",{staticClass:"empty-stock"},[t._v("缺货")]):t._e()],1)}))],2)})),e("v-uni-view",{staticClass:"number-wrap"},[e("v-uni-view",{staticClass:"number-line"},["point"==t.type?e("v-uni-text",{staticClass:"title font-size-base"},[t._v("兑换数量")]):e("v-uni-text",{staticClass:"title font-size-base"},[t._v("购买数量")]),t.limitNumber>0?e("v-uni-text",{staticClass:"limit-txt color-base-text"},[t._v("(每人限购"+t._s(t.limitNumber)+t._s(t.goodsDetail.unit)+")")]):t._e(),t.maxBuy>0&&t.minBuy>1?e("v-uni-text",{staticClass:"limit-txt color-base-text"},[t._v("("+t._s(t.minBuy)+t._s(t.goodsDetail.unit)+"起售，限购"+t._s(t.maxBuy)+t._s(t.goodsDetail.unit)+")")]):t.maxBuy>0?e("v-uni-text",{staticClass:"limit-txt color-base-text"},[t._v("(限购"+t._s(t.maxBuy)+t._s(t.goodsDetail.unit)+")")]):t.minBuy>1?e("v-uni-text",{staticClass:"limit-txt color-base-text"},[t._v("("+t._s(t.minBuy)+t._s(t.goodsDetail.unit)+"起售)")]):t._e(),e("v-uni-view",{staticClass:"number"},[e("v-uni-button",{staticClass:"decrease color-line-border",class:{disabled:t.decreaseDisabled},attrs:{type:"default"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changeNum("-")}}},[t._v("-")]),e("v-uni-input",{staticClass:"uni-input color-line-border font-size-goods-tag",attrs:{type:"number",placeholder:"0"},on:{blur:function(i){arguments[0]=i=t.$handleEvent(i),t.blur.apply(void 0,arguments)},input:function(i){arguments[0]=i=t.$handleEvent(i),t.keyInput(!1)}},model:{value:t.number,callback:function(i){t.number=i},expression:"number"}}),e("v-uni-button",{staticClass:"increase color-line-border",class:{disabled:t.increaseDisabled},attrs:{type:"default"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changeNum("+")}}},[t._v("+")])],1)],1)],1),t.goodsForm?e("ns-form",{ref:"form",attrs:{data:t.goodsForm},on:{changeFormVal:function(i){arguments[0]=i=t.$handleEvent(i),t.changeFormVal.apply(void 0,arguments)}}}):t._e()],2)],1),e("v-uni-view",{staticClass:"footer",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.confirm()}}},[t.goodsDetail.stock&&0!=t.goodsDetail.stock&&0==t.goodsDetail.is_virtual&&"buy_now"==t.type?e("v-uni-button",{style:{background:t.themeStyle.goods_detail.goods_btn_color_shallow,color:"#fff"},attrs:{type:"primary"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.confirm(!0)}}},[t._v("加入购物车")]):t._e(),"point"==t.type?[1==t.goodsDetail.type?[t.goodsDetail.point*t.number>t.memberPoint?e("v-uni-button",{attrs:{disabled:"true",type:"primary"}},[t._v("积分不足")]):t.goodsDetail.stock&&0!=t.goodsDetail.stock?e("v-uni-button",{attrs:{type:"primary"}},[t._v("兑换")]):e("v-uni-button",{attrs:{type:"primary",disabled:"true"}},[t._v("库存不足")])]:2==t.goodsDetail.type||3==t.goodsDetail.type?[e("v-uni-button",{attrs:{type:"primary"}},[t._v("兑换")])]:t._e()]:1==t.goodsDetail.goods_state?[t.goodsDetail.stock&&0!=t.goodsDetail.stock&&"join_cart"==t.type?e("v-uni-button",{attrs:{type:"primary"}},[t._v("加入购物车")]):t.goodsDetail.stock&&0!=t.goodsDetail.stock&&"buy_now"==t.type?e("v-uni-button",{attrs:{type:"primary"}},[t._v("立即购买")]):t.goodsDetail.stock&&0!=t.goodsDetail.stock&&"confirm"==t.type?e("v-uni-button",{attrs:{type:"primary"}},[t._v("确认")]):t.goodsDetail.stock&&0!=t.goodsDetail.stock?["pintuan"!=t.type&&"pinfan"!=t.type&&t.goodsDetail.buy_num?[t.goodsDetail.buy_num<=t.goodsDetail.stock?e("v-uni-button",{attrs:{type:"primary"}},[t._v("立即抢购")]):e("v-uni-button",{attrs:{type:"primary",disabled:"true"}},[t._v("库存不足")])]:[e("v-uni-button",{attrs:{type:"primary"}},[t._v("立即抢购")])]]:e("v-uni-button",{attrs:{type:"primary",disabled:"true"}},[t._v("库存不足")])]:e("v-uni-button",{attrs:{type:"primary",disabled:"true"}},[t._v("该商品已下架")])],2)],1)],1),e("ns-login",{ref:"login"})],1)},a=[]},"132d":function(t,i,e){"use strict";e.r(i);var s=e("02ae"),o=e("560b");for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);e("fbf2");var n=e("828b"),u=Object(n["a"])(o["default"],s["b"],s["c"],!1,null,"d78ce1c0",null,!1,s["a"],void 0);i["default"]=u.exports},"1a52":function(t,i,e){var s=e("8d20");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[t.i,s,""]]),s.locals&&(t.exports=s.locals);var o=e("967d").default;o("2f81132c",s,!0,{sourceMap:!1,shadowMode:!1})},"293b":function(t,i,e){var s=e("c86c");i=s(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.sku-info[data-v-d78ce1c0]{max-height:75vh;height:45vh;position:relative;z-index:999}.sku-info .header[data-v-d78ce1c0]{padding:%?30?% %?30?% %?20?% %?210?%;position:relative;border-bottom:%?2?% solid #eee;min-height:%?170?%}.sku-info .header .img-wrap[data-v-d78ce1c0]{width:%?160?%;height:%?160?%;position:absolute;left:%?20?%;border-radius:%?10?%;overflow:hidden;padding:%?2?%;background-color:#fff;line-height:%?208?%}.sku-info .header .img-wrap uni-image[data-v-d78ce1c0]{width:inherit;height:inherit}.sku-info .main[data-v-d78ce1c0]{font-size:%?24?%;line-height:%?40?%;padding-right:%?40?%}.sku-info .main .price-wrap[data-v-d78ce1c0]{font-weight:700}.sku-info .main .price-wrap .unit[data-v-d78ce1c0]{margin-right:%?4?%}.sku-info .main .stock[data-v-d78ce1c0]{font-size:%?24?%;color:#909399;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;height:%?70?%;overflow:hidden}.sku-info .main .price[data-v-d78ce1c0]{word-wrap:break-word}.sku-info .main .sku-name[data-v-d78ce1c0]{display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;overflow:hidden;height:%?42?%}.sku-info .main .sku-name .spec-value[data-v-d78ce1c0]::after{content:"/"}.sku-info .main .sku-name .spec-value[data-v-d78ce1c0]:last-child::after{content:""}.sku-info .sku-close[data-v-d78ce1c0]{position:absolute;top:%?20?%;right:%?40?%;width:%?40?%;height:%?80?%;font-size:%?40?%}.body-item[data-v-d78ce1c0]{padding:0 %?30?%;height:calc(100% - %?360?%);box-sizing:border-box;overflow:scroll}.body-item .wrap[data-v-d78ce1c0]{height:calc(100% - %?60?%)}.body-item .sku-list-wrap[data-v-d78ce1c0]{padding-bottom:%?0?%;position:relative}.body-item .sku-list-wrap .title[data-v-d78ce1c0]{padding:%?20?% 0;display:block}.body-item .sku-list-wrap .empty-stock[data-v-d78ce1c0]{font-size:%?18?%;line-height:%?22?%;position:absolute;right:0;top:0;border-radius:%?4?%;-webkit-transform:translateY(-50%);transform:translateY(-50%);padding:0 %?2?%;color:#989898;background-color:#f0f1f2}.body-item .sku-list-wrap .items[data-v-d78ce1c0]{position:relative;display:inline-block;border:%?2?% solid #eee;padding:%?4?% %?30?%;border-radius:%?8?%;margin:0 %?20?% %?20?% 0;background-color:#fff;font-size:%?24?%}.body-item .sku-list-wrap .selected[data-v-d78ce1c0]{color:var(--base-color);border-color:var(--base-color)}.body-item .sku-list-wrap .items.disabled[data-v-d78ce1c0]{border:%?2?% dashed}.body-item .sku-list-wrap .items uni-image[data-v-d78ce1c0]{height:%?44?%;width:%?44?%;border-radius:%?10?%;margin-right:%?10?%;display:inline-block;vertical-align:middle}.body-item .number-wrap .number-line[data-v-d78ce1c0]{padding:%?20?% 0;line-height:%?72?%;display:flex}.body-item .number-wrap .title[data-v-d78ce1c0]{font-weight:400}.body-item .number-wrap .number[data-v-d78ce1c0]{display:flex;height:%?72?%;border-radius:%?6?%;float:right;justify-content:flex-end;flex:1}.body-item .number-wrap .number uni-button[data-v-d78ce1c0]{display:inline-block;line-height:%?64?%;height:%?68?%;width:%?60?%;font-size:%?48?%;box-sizing:initial;border:%?2?% solid #eee;padding:0;margin:0;border-radius:0;background-color:#fff!important}.body-item .number-wrap .number uni-button.disabled[data-v-d78ce1c0]{background:#f7f7f7!important}.body-item .number-wrap .number uni-button.decrease[data-v-d78ce1c0]{border-right-width:0!important}.body-item .number-wrap .number uni-button.increase[data-v-d78ce1c0]{border-left:0!important}.body-item .number-wrap .number uni-button[data-v-d78ce1c0]:after{border-radius:0;border:none}.body-item .number-wrap .number uni-input[data-v-d78ce1c0]{display:inline-block;line-height:%?64?%;height:%?68?%;min-width:%?90?%;text-align:center;font-weight:700;border:%?2?% solid;margin:0;vertical-align:top;background-color:#f8f8f8!important;flex:0;padding:0 5px}.footer[data-v-d78ce1c0]{height:%?100?%;width:100%;position:absolute;bottom:%?30?%;bottom:calc(%?30?% + constant(safe-area-inset-bottom));bottom:calc(%?30?% + env(safe-area-inset-bottom));color:#fff;z-index:1;display:flex;justify-content:center;align-items:flex-start}.footer uni-button[data-v-d78ce1c0]{width:100%;height:%?80?%;background-color:var(--goods-btn-color);font-weight:700;border:none}.presale-stock[data-v-d78ce1c0]{height:auto!important}.balance[data-v-d78ce1c0]{margin-left:%?10?%;display:inline-block;line-height:1.3;font-size:%?24?%}.balance uni-text[data-v-d78ce1c0]{font-weight:400}.balance .unit[data-v-d78ce1c0]{margin-right:0!important}',""]),t.exports=i},"333d":function(t,i,e){"use strict";var s=e("1a52"),o=e.n(s);o.a},"42cb":function(t,i,e){"use strict";e("6a54");var s=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("64aa"),e("e838"),e("bf0f"),e("2797"),e("c223"),e("aa9c"),e("e966"),e("d4b5");var o=s(e("cd14")),a={name:"ns-goods-sku",components:{uniPopup:o.default},props:{goodsId:{type:[Number,String],default:0},memberPoint:{type:[Number,String],default:0},goodsDetail:{type:Object,default:null},disabled:{type:Boolean,default:!1},pointLimit:{type:[Number,String]},maxBuy:{type:Number,default:0},minBuy:{type:Number,default:0},goodsFormVal:{type:Array,default:function(){return[]}}},data:function(){return{isIphoneX:!1,systemInfo:{},number:1,btnSwitch:!1,type:"",callback:null,skuId:0,pintuanId:0,limitNumber:0,minNumber:0,preview:0,cartNum:0,goodsSkuDetail:{},pintuan_num_field:"pintuan_num",goodsSkuInfo:null,goodsForm:null,isLoad:!1,skuList:[]}},created:function(){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.systemInfo=uni.getSystemInfoSync(),this.isLoad=!0,this.goodsId&&this.goodsDetail.goods_spec_format?(this.skuId=this.goodsDetail.sku_id,this.goodsDetail.pintuan_id?this.getPintuanGoodsSkuList():this.goodsDetail.pinfan_id?this.getPinfanGoodsSkuList():this.goodsDetail.groupbuy_id?this.getGroupbuyGoodsSkuList():this.goodsDetail.bargain_id?this.getBargainGoodsSkuList():this.goodsDetail.seckill_id?this.getSeckillGoodsSkuList():this.goodsDetail.topic_id?this.getTopicsGoodsSkuList():this.goodsDetail.exchange_id?this.getPointGoodsSkuList():this.goodsDetail.presale_id?this.getPresaleGoodsSkuList():this.goodsDetail.bale_id?this.getBaleGoodsSkuList():this.getGeneralGoodsSkuList()):this.skuId=this.goodsDetail.sku_id},watch:{pointLimit:function(t,i){this.limitNumber=Number(t)},goodsDetail:{handler:function(t,i){this.skuId=t.sku_id,"pintuan"==this.type&&"ladder"==t.pintuan_type&&(this.pintuan_num_field=t.pintuan_num_field),this.goodsDetail.goods_form&&!this.goodsForm&&(this.goodsForm=this.goodsDetail.goods_form),this.calcSkuStock(),t.goods_id!=i.goods_id&&(this.goodsDetail.pintuan_id?this.getPintuanGoodsSkuList():this.goodsDetail.pinfan_id?this.getPinfanGoodsSkuList():this.goodsDetail.groupbuy_id?this.getGroupbuyGoodsSkuList():this.goodsDetail.bargain_id?this.getBargainGoodsSkuList():this.goodsDetail.seckill_id?this.getSeckillGoodsSkuList():this.goodsDetail.topic_id?this.getTopicsGoodsSkuList():this.goodsDetail.exchange_id?this.getPointGoodsSkuList():this.goodsDetail.presale_id?this.getPresaleGoodsSkuList():this.goodsDetail.bale_id?this.getBaleGoodsSkuList():this.getGeneralGoodsSkuList())},deep:!0},minBuy:function(t,i){this.minBuy>1&&(this.number=Number(this.minBuy))}},computed:{balance:function(){var t=this.goodsDetail.member_price&&this.goodsDetail.member_price>0?this.goodsDetail.member_price:this.goodsDetail.price,i=parseFloat(t)-parseFloat(this.goodsDetail.presale_price);return i=i<0?0:i,i.toFixed(2)},decreaseDisabled:function(){var t=this.minBuy>0?this.minBuy:1;return this.number<=t},increaseDisabled:function(){var t=this.maxBuy>0&&this.maxBuy<this.goodsDetail.stock?this.maxBuy:this.goodsDetail.stock;return this.number>=t},skuHeight:function(){var t=48;return this.goodsDetail&&(this.goodsDetail.goods_spec_format&&this.goodsDetail.goods_spec_format.length&&(t=51+9.5*this.goodsDetail.goods_spec_format.length),this.goodsForm&&this.goodsForm.length&&(t+=5*this.goodsForm.length)),t+="vh",t}},methods:{calcSkuStock:function(){var t=this;this.goodsDetail.goods_spec_format&&this.goodsDetail.goods_spec_format.length&&this.goodsDetail.goods_spec_format.forEach((function(i){i.value.forEach((function(i){t.skuList.forEach((function(e){i.sku_id==e.sku_id&&t.$set(i,"stock",e.stock)}))}))}))},changeFormVal:function(t){this.$emit("detailChangeVal",t)},getGoodsForm:function(){var t=this;this.$api.sendRequest({url:"/form/api/form/goodsform",data:{goods_id:this.goodsDetail.goods_id},success:function(i){0==i.code&&i.data&&t.$set(t.goodsDetail,"goods_form",i.data),t.goodsFormVal.length&&Object.assign(t.goodsDetail.goods_form,t.goodsFormVal)}})},getGeneralGoodsSkuList:function(t){var i=this;this.$api.sendRequest({url:"/api/goodssku/goodsSku",data:{goods_id:this.goodsId},success:function(e){if(e.code>=0){var s={};e.data.forEach((function(t,o){t=i.handleData(t),1==t.promotion_type&&(t.discountTimeMachine=i.$util.countDown(t.end_time-e.timestamp)),1==t.promotion_type&&t.discountTimeMachine?t.member_price>0&&Number(t.member_price)<=Number(t.discount_price)?t.show_price=t.member_price:t.show_price=t.discount_price:t.member_price>0?t.show_price=t.member_price:t.show_price=t.price,s["sku_"+t.sku_id]=t})),0==i.skuId&&(i.skuId=e.data[0].sku_id),i.goodsSkuInfo=s,i.isLoad=!1,i.skuList=e.data,i.calcSkuStock(),t&&t()}else i.$util.redirectTo("/pages/index/index")}})},getPintuanGoodsSkuList:function(){var t=this;this.$api.sendRequest({url:"/pintuan/api/goods/goodsSku",data:{goods_id:this.goodsId,pintuan_id:this.goodsDetail.pintuan_id,pintuan_num:this.goodsDetail[this.goodsDetail["pintuan_num_field"]]},success:function(i){if(i.code>=0){var e={};i.data.forEach((function(i,s){i=t.handleData(i),i.show_price=0==t.goodsDetail.group_id?i.promotion_price:i.pintuan_price,i.save_price=i.price-i.show_price>0?(i.price-i.show_price).toFixed(2):0,i["pintuan_num_field"]=t.goodsDetail["pintuan_num_field"],e["sku_"+i.sku_id]=i})),t.skuId=i.data[0].sku_id,t.goodsSkuInfo=e}else t.$util.redirectTo("/pages/index/index")}})},getPinfanGoodsSkuList:function(){var t=this;this.$api.sendRequest({url:"/pinfan/api/goods/goodsSku",data:{goods_id:this.goodsId,pintuan_id:this.goodsDetail.pinfan_id},success:function(i){if(i.code>=0){var e={};i.data.forEach((function(i,s){i=t.handleData(i),i.show_price=0==t.goodsDetail.group_id?i.promotion_price:i.pintuan_price,i.save_price=i.price-i.show_price>0?(i.price-i.show_price).toFixed(2):0,e["sku_"+i.sku_id]=i})),t.skuId=i.data[0].sku_id,t.goodsSkuInfo=e}else t.$util.redirectTo("/pages/index/index")}})},getGroupbuyGoodsSkuList:function(){var t=this;this.$api.sendRequest({url:"/groupbuy/api/goods/goodsSku",data:{goods_id:this.goodsId,groupbuy_id:this.goodsDetail.groupbuy_id},success:function(i){if(i.code>=0){var e=i.data,s={};i.data.forEach((function(i,o){i=t.handleData(i),i.show_price=t.goodsDetail.groupbuy_price,i.save_price=i.price-i.show_price>0?(i.price-i.show_price).toFixed(2):0,e.stock>e.buy_num&&(t.number=t.goodsDetail.buy_num,t.minNumber=t.goodsDetail.buy_num),s["sku_"+i.sku_id]=i})),t.skuId=i.data[0].sku_id,t.goodsSkuInfo=s}else t.$util.redirectTo("/pages/index/index")}})},getBargainGoodsSkuList:function(){var t=this;this.$api.sendRequest({url:"/bargain/api/goods/goodsSku",data:{goods_id:this.goodsId,bargain_id:this.goodsDetail.bargain_id},success:function(i){if(i.code>=0){var e={};i.data.forEach((function(i,s){i=t.handleData(i),i.show_price=t.goodsDetail.bargain_id>0?i.floor_price:i.price,i.stock=i.bargain_stock,i.save_price=i.price-i.show_price>0?(i.price-i.show_price).toFixed(2):0,e["sku_"+i.sku_id]=i})),t.skuId=i.data[0].sku_id,t.goodsSkuInfo=e}else t.$util.redirectTo("/pages/index/index")}})},getSeckillGoodsSkuList:function(){var t=this;this.$api.sendRequest({url:"/seckill/api/seckillgoods/goodsSku",data:{goods_id:this.goodsId,seckill_id:this.goodsDetail.seckill_id},success:function(i){if(i.code>=0){var e={};i.data.forEach((function(i,s){i=t.handleData(i),i.show_price=i.seckill_price,i.price-i.show_price>0&&(i.price-i.show_price).toFixed(2),e["sku_"+i.sku_id]=i})),t.skuId=i.data[0].sku_id,t.goodsSkuInfo=e}else t.$util.redirectTo("/pages/index/index")}})},getTopicsGoodsSkuList:function(){var t=this;this.$api.sendRequest({url:"/topic/api/topicgoods/goodsSku",data:{goods_id:this.goodsId,topic_id:this.goodsDetail.topic_id},success:function(i){if(i.code>=0){var e={};i.data.forEach((function(i,s){i=t.handleData(i),i.show_price=t.goodsDetail.topic_id>0?i.topic_price:i.price,i.save_price=i.price-i.show_price>0?(i.price-i.show_price).toFixed(2):0,e["sku_"+i.sku_id]=i})),t.skuId=i.data[0].sku_id,t.goodsSkuInfo=e}else t.$util.redirectTo("/pages/index/index")}})},getPointGoodsSkuList:function(){var t=this;1==this.goodsDetail.type&&this.$api.sendRequest({url:"/pointexchange/api/goods/goodsSku",data:{goods_id:this.goodsId,exchange_id:this.goodsDetail.exchange_id,type:this.goodsDetail.type},success:function(i){if(i.code>=0){var e={};i.data.forEach((function(i,s){i=t.handleData(i),e["sku_"+i.sku_id]=i})),t.skuId=i.data[0].sku_id,t.goodsSkuInfo=e}else t.$util.redirectTo("/pages/index/index")}})},getPresaleGoodsSkuList:function(){var t=this;this.$api.sendRequest({url:"/presale/api/goods/goodsSku",data:{goods_id:this.goodsId,presale_id:this.goodsDetail.presale_id},success:function(i){if(i.code>=0){var e={};i.data.forEach((function(i,s){i=t.handleData(i),e["sku_"+i.sku_id]=i})),t.skuId=i.data[0].sku_id,t.goodsSkuInfo=e}else t.$util.redirectTo("/pages/index/index")}})},getBaleGoodsSkuList:function(){var t=this,i={};this.goodsDetail.sku_list.forEach((function(e,s){e=t.handleData(e),i["sku_"+e.sku_id]=e})),this.skuId=this.goodsDetail.sku_list[0].sku_id,this.goodsSkuInfo=i},handleData:function(t){return t.sku_images?t.sku_images=t.sku_images.split(","):t.sku_images=[],t.goods_spec_format&&t.goods_image&&(t.goods_image=t.goods_image.split(","),t.sku_images=t.goods_image.concat(t.sku_images)),t.sku_spec_format&&(t.sku_spec_format=JSON.parse(t.sku_spec_format)),t.goods_spec_format&&(t.goods_spec_format=JSON.parse(t.goods_spec_format)),t},show:function(t,i){var e=this,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.callback=i,this.number=1,"confirm"!=this.type&&this.$refs.skuPopup.open(),this.type=t,this.skuId=this.goodsDetail.sku_id,this.preview=this.goodsDetail.preview||0,"pintuan"==this.type&&this.goodsDetail.pintuan_id)this.limitNumber=this.goodsDetail.buy_num;else if("pinfan"==this.type&&this.goodsDetail.pinfan_id)this.limitNumber=this.goodsDetail.buy_num;else if("groupbuy"==this.type&&this.goodsDetail.groupbuy_id)this.goodsDetail.stock>this.goodsDetail.buy_num&&(this.number=this.goodsDetail.buy_num,this.minNumber=this.goodsDetail.buy_num);else if("bargain"==this.type&&this.goodsDetail.bargain_id)this.number=1,this.minNumber=1;else if("confirm"==this.type){if(this.isLoad)return;this.goodsId=s.goods_id,this.skuId=s.sku_id,this.getGeneralGoodsSkuList((function(){e.$refs.skuPopup.open()}))}"join_cart"==this.type&&this.getCartData(),"point"==this.type&&-1==this.goodsDetail.stock&&(this.goodsDetail.pointCoupon=1,this.goodsDetail.stock=99999),"bale"==this.type&&(this.number=1,-1==this.$util.inArray(this.skuId+"",this.goodsDetail.activity_sku_ids)?(this.confirmDisabled=!0,this.$util.showToast({title:"该规格未参与打包一口价活动"})):this.confirmDisabled=!1),this.minBuy>1&&(this.number=Number(this.minBuy)),this.$forceUpdate()},hide:function(){this.$emit("hideSkuPop"),this.$refs.skuPopup&&this.$refs.skuPopup.close()},popclose:function(){this.$refs.skuPopup.showPopup?this.$emit("hideSkuPop"):(this.goodsForm=null,this.getGoodsForm())},previewMedia:function(){var t=[];t.push(this.$util.img(this.goodsDetail.sku_image,{size:"big"})),uni.previewImage({current:1,urls:t})},pintuanChange:function(t){this.goodsDetail["pintuan_num_field"]=t,this.pintuan_num_field=t},change:function(t,i){if(null!=this.goodsSkuInfo&&!this.disabled){this.btnSwitch=!1,this.skuId=t;for(var e=0;e<this.goodsDetail.goods_spec_format.length;e++)for(var s=this.goodsDetail.goods_spec_format[e],o=0;o<s.value.length;o++)i==this.goodsDetail.goods_spec_format[e].value[o].spec_id&&(this.goodsDetail.goods_spec_format[e].value[o].selected=!1);this.goodsSkuDetail=this.goodsSkuInfo["sku_"+this.skuId],this.$emit("refresh",this.goodsSkuDetail),this.$emit("getSkuId",this.skuId),this.goodsDetail.bale_id&&(-1==this.$util.inArray(t+"",this.goodsDetail.activity_sku_ids)?(this.confirmDisabled=!0,this.$util.showToast({title:"该规格未参与打包一口价活动"})):this.confirmDisabled=!1),this.keyInput(!0)}},showPrice:function(t){return parseFloat(t)},pintuanPrice:function(t){return"ordinary"==t.pintuan_type||"pintuan_num"==t.pintuan_num_field?t.pintuan_price:"pintuan_num_2"==t.pintuan_num_field?t.pintuan_price_2:"pintuan_num_3"==t.pintuan_num_field?t.pintuan_price_3:void 0},changeNum:function(t){if(0!=this.goodsDetail.stock){var i=1,e=this.goodsDetail.stock;if(1==this.maxBuy&&(e=1),1==this.goodsDetail.is_limit&&this.maxBuy>0&&this.maxBuy<e&&(e=this.maxBuy),1==this.goodsDetail.is_limit&&2==this.goodsDetail.limit_type&&this.maxBuy>0&&this.goodsDetail.purchased_num>0){var s=this.maxBuy-this.goodsDetail.purchased_num;e=s<this.goodsDetail.stock?s:this.goodsDetail.stock}if(this.minBuy>1&&(i=this.minBuy),"pintuan"==this.type&&this.goodsDetail.pintuan_id?e=this.goodsDetail.buy_num>this.goodsDetail.stock?this.goodsDetail.stock:this.goodsDetail.buy_num:"groupbuy"==this.type&&this.goodsDetail.groupbuy_id?(e=(this.goodsDetail.buy_num,this.goodsDetail.stock,this.goodsDetail.stock),i=this.goodsDetail.buy_num):"seckill"==this.type&&this.goodsDetail.seckill_id&&this.goodsDetail.num>0&&this.goodsDetail.num<this.goodsDetail.stock?e=this.goodsDetail.num:"bargain"==this.type&&this.goodsDetail.bargain_id?e=1:"presale"==this.type&&this.goodsDetail.presale_id&&this.goodsDetail.presale_num>0&&(e=this.goodsDetail.presale_num>this.goodsDetail.stock?this.goodsDetail.stock:this.goodsDetail.presale_num),"+"==t)if(this.number<e)this.number++;else{if(this.number>=this.goodsDetail.stock)return void this.$util.showToast({title:"库存不足"});if(1==this.goodsDetail.is_limit&&this.maxBuy>0){if(1==this.goodsDetail.limit_type)return void this.$util.showToast({title:"该商品每次最多购买"+this.maxBuy+this.goodsDetail.unit});if(2==this.goodsDetail.limit_type){var o="该商品每人限购"+this.maxBuy+this.goodsDetail.unit;return o+=this.goodsDetail.purchased_num>0?"，您已购买了"+this.goodsDetail.purchased_num+this.goodsDetail.unit:"",void this.$util.showToast({title:o})}}if("seckill"==this.type&&this.goodsDetail.seckill_id&&this.goodsDetail.num>0)return void this.$util.showToast({title:"该商品每人限购"+this.goodsDetail.num+this.goodsDetail.unit});if("presale"==this.type&&this.goodsDetail.presale_id&&this.goodsDetail.presale_num>0)return void this.$util.showToast({title:"该商品每人限购"+this.goodsDetail.presale_num+this.goodsDetail.unit})}else if("-"==t){if(!(this.number>i))return void(this.minBuy>1&&this.$util.showToast({title:"该商品"+this.minBuy+this.goodsDetail.unit+"起售"}));this.number-=1}this.number>this.limitNumber&&this.limitNumber&&(this.number=this.limitNumber)}},blur:function(){if(this.number||(this.number=0),this.number>this.limitNumber&&this.limitNumber&&(this.number=this.limitNumber),this.number<this.minNumber&&this.minNumber&&(this.number=this.minNumber),1==this.goodsDetail.is_limit&&this.maxBuy>0&&this.number>this.maxBuy&&(this.number=this.maxBuy),1==this.goodsDetail.is_limit&&this.maxBuy>0&&this.goodsDetail.purchased_num>0){var t=this.maxBuy-this.goodsDetail.purchased_num;this.number>t&&(this.number=t)}this.number<this.minBuy&&this.minBuy>0&&(this.number=this.minBuy),this.number<=0&&(this.number=1)},keyInput:function(t,i){var e=this;setTimeout((function(){var s=e.goodsDetail.stock;0!=e.goodsDetail.stock?(t&&0==e.number.length&&(e.number=1),t&&(e.number<=0||isNaN(e.number))&&(e.number=1),"pintuan"==e.type&&e.goodsDetail.pintuan_id&&e.number>e.goodsDetail.buy_num?e.number=e.goodsDetail.buy_num:"groupbuy"==e.type&&e.goodsDetail.groupbuy_id&&e.number<e.goodsDetail.buy_num?e.number=1:"seckill"==e.type&&e.goodsDetail.seckill_id&&e.goodsDetail.max_buy>0&&e.number>e.goodsDetail.max_buy?e.number=e.goodsDetail.max_buy:"bargain"==e.type&&e.goodsDetail.bargain_id?e.number=1:"presale"==e.type&&e.goodsDetail.presale_id&&e.goodsDetail.presale_num>0&&e.number>e.goodsDetail.presale_num?e.number=e.goodsDetail.presale_num:e.number>s&&(e.number=s),e.minBuy>1&&e.number<e.minBuy&&(e.number=e.minBuy),t&&(e.number=parseInt(e.number)),i&&i()):e.number=0}),0)},confirm:function(){var t=this,i=arguments.length>0&&void 0!==arguments[0]&&arguments[0];uni.removeStorageSync("delivery"),this.preview?this.$util.showToast({title:"预览商品无法购买"}):0!=this.goodsDetail.goods_state&&(this.storeToken?this.keyInput(!0,(function(){if(0!=t.goodsDetail.stock)if(0==t.number.length||t.number<=0)t.$util.showToast({title:"购买数量不能小于等于0"});else if("pintuan"!=t.type&&"pinfan"!=t.type&&t.goodsDetail.buy_num>t.goodsDetail.stock)t.$util.showToast({title:"库存小于最低购买数量"});else if(t.number>t.goodsDetail.stock)t.$util.showToast({title:"库存不足"});else if(1==t.goodsDetail.is_limit&&1==t.goodsDetail.limit_type&&t.maxBuy>0&&t.number>t.maxBuy)t.$util.showToast({title:"该商品每次最多购买"+t.maxBuy+t.goodsDetail.unit});else{if(1==t.goodsDetail.is_limit&&2==t.goodsDetail.limit_type&&t.maxBuy>0&&t.number+t.goodsDetail.purchased_num>t.maxBuy){var e="该商品每人限购"+t.maxBuy+t.goodsDetail.unit;return e+=t.goodsDetail.purchased_num>0?"，您已购买了"+t.goodsDetail.purchased_num+t.goodsDetail.unit:"",void t.$util.showToast({title:e})}if(("join_cart"==t.type||i)&&1==t.goodsDetail.is_limit&&t.maxBuy>0&&t.cartNum+t.number>t.maxBuy)t.$util.showToast({title:"该商品每人限购"+t.maxBuy+t.goodsDetail.unit});else{if(t.$refs.form){if(!t.$refs.form.verify())return;uni.setStorageSync("goodFormData",{goods_id:t.goodsDetail.goods_id,form_data:t.$refs.form.formData})}if(!t.btnSwitch)if(t.btnSwitch=!0,"join_cart"==t.type||i)t.$api.sendRequest({url:"/api/cart/add",data:{sku_id:t.skuId,num:t.number,form_data:t.$refs.form?JSON.stringify(t.$refs.form.formData):""},success:function(i){var e=i.data;if(e>0){t.$util.showToast({title:"加入购物车成功"}),t.cartNum+=t.number,t.$store.dispatch("getCartNumber");var s=t.goodsDetail.discount_price;t.goodsDetail.member_price>0&&Number(t.goodsDetail.member_price)<=Number(t.goodsDetail.discount_price)&&(s=t.goodsDetail.member_price),t.callback&&t.callback({cart_id:e,goods_id:t.goodsDetail.goods_id,sku_id:t.skuId,num:t.cartNum,discount_price:s})}t.$refs.skuPopup&&t.$refs.skuPopup.close(),t.btnSwitch=!1},fail:function(i){t.$refs.skuPopup&&t.$refs.skuPopup.close(),t.btnSwitch=!1}});else if("buy_now"==t.type){var s={sku_id:t.skuId,num:t.number};uni.setStorage({key:"orderCreateData",data:s,success:function(){t.$util.redirectTo("/pages/order/payment"),t.btnSwitch=!1}})}else if("seckill"==t.type){s={seckill_id:t.goodsDetail.seckill_id,num:t.number,sku_id:t.skuId};uni.setStorage({key:"seckillOrderCreateData",data:s,success:function(){t.$util.redirectTo("/pages_promotion/seckill/payment"),t.btnSwitch=!1}})}else if("pintuan"==t.type){s={pintuan_goods_id:t.goodsDetail.id,group_id:t.goodsDetail.group_id,num:t.number};"ladder"!=t.goodsDetail.pintuan_type||s.group_id||(s.extend=JSON.stringify({pintuan_num:t.goodsDetail[t.goodsDetail["pintuan_num_field"]]})),uni.setStorage({key:"pintuanOrderCreateData",data:s,success:function(){t.$util.redirectTo("/pages_promotion/pintuan/payment"),t.btnSwitch=!1}})}else if("pinfan"==t.type){s={pintuan_goods_id:t.goodsDetail.id,group_id:t.goodsDetail.group_id,num:t.number};uni.setStorage({key:"pinfanOrderCreateData",data:s,success:function(){t.$util.redirectTo("/pages_promotion/pinfan/payment"),t.btnSwitch=!1}})}else if("topic"==t.type){s={topic_goods_id:t.goodsDetail.id,num:t.number};uni.setStorage({key:"topicOrderCreateData",data:s,success:function(){t.$util.redirectTo("/pages_promotion/topics/payment"),t.btnSwitch=!1}})}else if("groupbuy"==t.type){s={groupbuy_id:t.goodsDetail.groupbuy_id,sku_id:t.skuId,num:t.number};uni.setStorage({key:"groupbuyOrderCreateData",data:s,success:function(){t.$util.redirectTo("/pages_promotion/groupbuy/payment"),t.btnSwitch=!1}})}else if("presale"==t.type){s={presale_id:t.goodsDetail.presale_id,sku_id:t.skuId,num:t.number};uni.setStorage({key:"presaleOrderCreateData",data:s,success:function(){t.$util.redirectTo("/pages_promotion/presale/payment"),t.btnSwitch=!1}})}else if("bargain"==t.type)t.callback?t.callback():t.$api.sendRequest({url:"/bargain/api/bargain/launch",data:{id:t.goodsDetail.id},success:function(i){t.btnSwitch=!1,0==i.code?t.$util.redirectTo("/pages_promotion/bargain/detail",{b_id:t.goodsDetail.bargain_id,l_id:i.data},"redirectTo"):t.$util.showToast({title:i.message})}});else if("point"==t.type){s={id:t.goodsDetail.exchange_id,sku_id:t.skuId,num:t.number};uni.setStorage({key:"exchangeOrderCreateData",data:s,success:function(){t.$util.redirectTo("/pages_promotion/point/payment"),t.btnSwitch=!1}})}else"bale"==t.type?t.confirmDisabled||(t.btnSwitch=!1,t.$emit("confirm",{detail:t.goodsDetail,goods_id:t.goodsDetail.goods_id,sku_id:t.goodsDetail.sku_id,num:t.number})):"confirm"==t.type&&(t.btnSwitch=!1,t.$refs.skuPopup&&t.$refs.skuPopup.close(),t.callback&&t.callback(t.skuId,t.number))}}else t.$util.showToast({title:"商品已售罄"})})):this.$refs.login.open())},closeSkuPopup:function(){this.$emit("hideSkuPop"),this.$refs.skuPopup&&this.$refs.skuPopup.close()},imageError:function(){this.goodsDetail.sku_image=this.$util.getDefaultImage().goods,this.$forceUpdate()},valueImageError:function(t,i){this.goodsDetail.goods_spec_format[t].value[i].image=this.$util.getDefaultImage().goods,this.$forceUpdate()},getCartData:function(){var t=this;this.$api.sendRequest({url:"/api/cart/goodsnum",data:{goods_id:this.goodsDetail.goods_id},success:function(i){i.code>=0&&(t.cartNum=i.data)}})}}};i.default=a},"560b":function(t,i,e){"use strict";e.r(i);var s=e("42cb"),o=e.n(s);for(var a in s)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(a);i["default"]=o.a},"74b3":function(t,i,e){var s=e("293b");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[t.i,s,""]]),s.locals&&(t.exports=s.locals);var o=e("967d").default;o("3bd967dc",s,!0,{sourceMap:!1,shadowMode:!1})},8837:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var s={name:"UniPopup",props:{animation:{type:Boolean,default:!0},type:{type:String,default:"center"},custom:{type:Boolean,default:!1},maskClick:{type:Boolean,default:!0},show:{type:Boolean,default:!0}},data:function(){return{ani:"",showPopup:!1,callback:null,isIphoneX:!1}},watch:{show:function(t){t?this.open():this.close()}},created:function(){this.isIphoneX=this.$util.uniappIsIPhoneX()},methods:{clear:function(){},open:function(t){var i=this;t&&(this.callback=t),this.$emit("change",{show:!0}),this.showPopup=!0,this.$nextTick((function(){setTimeout((function(){i.ani="uni-"+i.type}),30)}))},close:function(t,i){var e=this;!this.maskClick&&t||(this.$emit("change",{show:!1}),this.ani="",this.$nextTick((function(){setTimeout((function(){e.showPopup=!1}),300)})),i&&i(),this.callback&&this.callback.call(this))}}};i.default=s},"8d20":function(t,i,e){var s=e("c86c");i=s(!1),i.push([t.i,".uni-popup[data-v-14c0a5c7]{position:fixed;top:0;bottom:0;left:0;right:0;z-index:999;overflow:hidden}.uni-popup__mask[data-v-14c0a5c7]{position:absolute;top:0;bottom:0;left:0;right:0;z-index:998;background:rgba(0,0,0,.4);opacity:0}.uni-popup__mask.ani[data-v-14c0a5c7]{transition:all .3s}.uni-popup__mask.uni-bottom[data-v-14c0a5c7],\r\n.uni-popup__mask.uni-center[data-v-14c0a5c7],\r\n.uni-popup__mask.uni-right[data-v-14c0a5c7],\r\n.uni-popup__mask.uni-left[data-v-14c0a5c7],\r\n.uni-popup__mask.uni-top[data-v-14c0a5c7]{opacity:1}.uni-popup__wrapper[data-v-14c0a5c7]{position:absolute;z-index:999;box-sizing:border-box;background:#fff}.uni-popup__wrapper.ani[data-v-14c0a5c7]{transition:all .3s}.uni-popup__wrapper.top[data-v-14c0a5c7]{top:0;left:0;width:100%;-webkit-transform:translateY(-100%);transform:translateY(-100%)}.uni-popup__wrapper.bottom[data-v-14c0a5c7]{bottom:0;left:0;width:100%;-webkit-transform:translateY(100%);transform:translateY(100%)}.uni-popup__wrapper.right[data-v-14c0a5c7]{bottom:0;left:0;width:100%;-webkit-transform:translateX(100%);transform:translateX(100%)}.uni-popup__wrapper.left[data-v-14c0a5c7]{bottom:0;left:0;width:100%;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.uni-popup__wrapper.center[data-v-14c0a5c7]{width:100%;height:100%;display:flex;justify-content:center;align-items:center;-webkit-transform:scale(1.2);transform:scale(1.2);opacity:0}.uni-popup__wrapper-box[data-v-14c0a5c7]{position:relative;box-sizing:border-box}.uni-popup__wrapper.uni-custom .uni-popup__wrapper-box[data-v-14c0a5c7]{background:#fff}.uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box[data-v-14c0a5c7]{position:relative;max-width:80%;max-height:80%;overflow-y:scroll}.uni-popup__wrapper.uni-custom.bottom .uni-popup__wrapper-box[data-v-14c0a5c7],\r\n.uni-popup__wrapper.uni-custom.top .uni-popup__wrapper-box[data-v-14c0a5c7]{width:100%;max-height:500px;overflow-y:scroll}.uni-popup__wrapper.uni-bottom[data-v-14c0a5c7],\r\n.uni-popup__wrapper.uni-top[data-v-14c0a5c7]{-webkit-transform:translateY(0);transform:translateY(0)}.uni-popup__wrapper.uni-left[data-v-14c0a5c7],\r\n.uni-popup__wrapper.uni-right[data-v-14c0a5c7]{-webkit-transform:translateX(0);transform:translateX(0)}.uni-popup__wrapper.uni-center[data-v-14c0a5c7]{-webkit-transform:scale(1);transform:scale(1);opacity:1}\r\n\r\n/* isIphoneX系列手机底部安全距离 */.bottom.safe-area[data-v-14c0a5c7]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}",""]),t.exports=i},c889:function(t,i,e){"use strict";e.d(i,"b",(function(){return s})),e.d(i,"c",(function(){return o})),e.d(i,"a",(function(){}));var s=function(){var t=this,i=t.$createElement,e=t._self._c||i;return t.showPopup?e("v-uni-view",{staticClass:"uni-popup"},[e("v-uni-view",{staticClass:"uni-popup__mask",class:[t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.close(!0)}}}),t.isIphoneX?e("v-uni-view",{staticClass:"uni-popup__wrapper safe-area",class:[t.type,t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.close(!0)}}},[e("v-uni-view",{staticClass:"uni-popup__wrapper-box",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.clear.apply(void 0,arguments)}}},[t._t("default")],2)],1):e("v-uni-view",{staticClass:"uni-popup__wrapper",class:[t.type,t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.close(!0)}}},[e("v-uni-view",{staticClass:"uni-popup__wrapper-box",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.clear.apply(void 0,arguments)}}},[t._t("default")],2)],1)],1):t._e()},o=[]},cd14:function(t,i,e){"use strict";e.r(i);var s=e("c889"),o=e("d0b3");for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);e("333d");var n=e("828b"),u=Object(n["a"])(o["default"],s["b"],s["c"],!1,null,"14c0a5c7",null,!1,s["a"],void 0);i["default"]=u.exports},d0b3:function(t,i,e){"use strict";e.r(i);var s=e("8837"),o=e.n(s);for(var a in s)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(a);i["default"]=o.a},fbf2:function(t,i,e){"use strict";var s=e("74b3"),o=e.n(s);o.a}}]);