<!DOCTYPE html>
<html>
<head>
	<meta name="renderer" content="webkit" />
	<meta http-equiv="X-UA-COMPATIBLE" content="IE=edge,chrome=1" />
	<title>{$shop_info['site_name']|default=""}</title>
	<meta name="keywords" content="{$shop_info['seo_keywords'] ?? '' }">
	<meta name="description" content="{$shop_info['seo_description'] ?? '' }">
	<link rel="icon" type="image/x-icon" href="__STATIC__/img/shop_bitbug_favicon.ico" />
	{notempty name="$load_diy_icon_url" }
		<!-- 加载自定义图标库 -->
		{:implode('',$load_diy_icon_url)}
	{/notempty}
	<link rel="stylesheet" type="text/css" href="STATIC_CSS/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="__STATIC__/ext/layui/css/layui.css" />
	<link rel="stylesheet" type="text/css" href="SHOP_CSS/template/{$theme_config['url']}" />
	<link rel="stylesheet" type="text/css" href="SHOP_CSS/common.css?time=20250528" />
	<script src="__STATIC__/js/jquery-3.1.1.js"></script>
	<script src="__STATIC__/js/jquery.cookie.js"></script>
	<script src="__STATIC__/ext/layui/layui.js"></script>
	<script>
		layui.use(['layer', 'upload', 'element'], function() {});
		//全局定义一次, 加载formSelects
		layui.extend({
			formSelects: 'STATIC_EXT/layui/extend/formSelects-v4',
			layCascader: '__STATIC__/ext/layui/extend/cascader/cascader',
			dropdown: '__STATIC__/ext/layui/extend/dropdown/dropdown'
		});
		window.ns_url = {
			baseUrl: "ROOT_URL/",
			route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
			appModule: '{$app_module ?? ""}',
			siteId: '{:request()->siteid()}',
			shopImg: 'SHOP_IMG',
			staticImg: 'STATIC_IMG',
			staticExt: 'STATIC_EXT',
			uploadMaxFileSize: '{$upload_max_filesize ?? 0}',
			siteName : "{$shop_info['site_name']}",
		};
		window.regexp_config = {:json_encode(config('regexp'))};
	</script>
	<script src="__STATIC__/js/common.js?v=20250310"></script>
	<script src="SHOP_JS/common.js?time=20241114"></script>
</head>
<body>
{__CONTENT__}
<script type="text/html" id="reset_pass">
	<!-- 重置密码弹框html -->
	<div class="layui-form">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>原密码</label>
			<div class="layui-input-block">
				<input type="password" id="old_pass" name="old_pass" required class="layui-input len-mid" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>新密码</label>
			<div class="layui-input-block">
				<input type="password" id="new_pass" name="new_pass" required class="layui-input len-mid" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>确认新密码</label>
			<div class="layui-input-block">
				<input type="password" id="repeat_pass" name="repeat_pass" required class="layui-input len-mid" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			</div>
		</div>

		<div class="form-row mid">
			<button class="layui-btn" onclick="repass()">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="layer.closeAll()">返回</button>
		</div>
	</div>
</script>
<script type="text/html" id="patch_alert">
	<table class="layui-table">
		<colgroup>
			<col width="30%">
			<col width="70%">
		</colgroup>
		<thead>
		<tr>
			<th>补丁名称</th>
			<th>补丁说明</th>
		</tr>
		</thead>
		<tbody>
			{{# d.forEach((item)=>{ }}
			<tr>
				<td>{{item.patch_name}}</td>
				<td>{{item.patch_desc}}</td>
			</tr>
			{{# }) }}
		</tbody>
	</table>
</script>
</body>
</html>