.member-level {
	width: 100%;
	min-height: 100vh;
	position: relative;
}
.level-top {
	width: 100%;
	position: relative;
	image {
		width: 100%;
		height: 460rpx;
		position: absolute;
	}
}
.banner-container {
	width: 100vw;
	position: relative;
	left: 0;
	top: 0;

	.memberInfo {
		width: 100%;
		height: 140rpx;
		padding: 40rpx 40rpx 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
		image {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			border: 4rpx solid #ffffff;
			box-sizing: border-box;
		}
		.growth-rules {
			position: absolute;
			display: flex;
			align-items: center;
			color: #fff;
			right: 40rpx;
			font-size: 24rpx;
			z-index: 10;
			.iconfont{
				margin-right: 10rpx;
			}
		}
		.member-desc {
			width: calc(100% - 20rpx - 100rpx);
			height: 100%;
			padding: 16rpx 0;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			justify-content: center;
			
			view {
				font-weight: bold;
				line-height: 1;
				color: #ffffff;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			
			.expire-time {
				color: #ccc;
				font-weight: normal;
				margin-top: 10rpx;
			}
		}
	}

	.demand {
		width: 100%;
		padding: 0 $padding;
		box-sizing: border-box;
		.demand-title {
			font-size: $font-size-toolbar;
			font-weight: bold;
			line-height: 1;
			display: flex;
			align-items: center;
			image {
				width: 39rpx;
				height: 35rpx;
				margin-right: 10rpx;
			}
		}
		.demand-info {
			padding: 10rpx 24rpx;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			margin-top: 27rpx;
			border-radius: 10rpx;
			justify-content: space-between;
			height: 150rpx;
			background: #ffffff;
			.info-title {
				display: flex;
				justify-content: space-between;
				align-items: center;
				text {
					&:nth-child(1) {
						color: #000;
						font-size: $font-size-tag;
					}
					&:nth-child(2) {
						color: #959595;
					}
				}
			}
			progress {
				margin-top: 39rpx;
			}
			.info-size {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: $font-size-tag;
				color: #959595;
			}
		}
	}

	.uni-swiper-dots {
		bottom: 30rpx !important;
	}

	.image-container {
		box-sizing: border-box;
		width: 100%;
		height: 100%;
		display: flex;
		image {
			width: 100%;
			height: 100%;
		}
		.slide-image {
			width: 535rpx;
			height: 300rpx;
			z-index: 200;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-radius: 20rpx;
			overflow: hidden;
			position: relative;
			.bg-border{
				width:  calc(100% - 40rpx);
				height:  calc(100% - 40rpx);
				position: absolute;
				top: 18rpx;
				left: 20rpx;
				border: 2rpx solid rgba(255, 255, 255, .2);
				z-index: 10;
				border-radius: 10rpx;
				opacity: .5;
				
			}
			.growth-rules{
				position: absolute;
				right: 40rpx;
				top: 40rpx;
				z-index: 10;
			}
			.info {
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				position: absolute;
				left: 0;
				bottom: 0;
				padding: 30rpx 40rpx;
				box-sizing: border-box;
				.level-detail {
					font-size: 52rpx;
					display: flex;
					align-items: center;
					margin-top: 26rpx;
				}
				.growr-name {
					font-size: 24rpx;
					margin-top: 50rpx;
					opacity: 0.8;
				}
				.growr-value {
					font-size: 24rpx;
					margin-top: 10rpx;
					opacity: 0.8;
				}
				.progress {
					margin-top: 30rpx;
				}
				.residue-growr-value {
					text-align: right;
					font-size: 24rpx;
					margin-top: 10rpx;
				}
				view {
					color: #ffffff;
					line-height: 1.3;
				}
			}
			.now_growth {
				margin-top: 20rpx;
			}
			.pic {
				display: flex;
				justify-content: center;
				align-items: center;

				image {
					width: 160rpx;
				}
			}
			.isnow {
				font-size: 20rpx;
				color: #fff;
				padding: 2rpx;
				line-height: 1;
				margin-left: 10rpx;
			}
		}
	}

	.item-left {
		justify-content: flex-end;
		padding: 56rpx 26rpx 0 0;
	}

	.image-container-box .item-left {
		justify-content: center;
		padding: 56rpx 0 0 0;
	}

	.item-right {
		justify-content: flex-start;
		padding: 56rpx 0 0 26rpx;
	}

	.item-center {
		justify-content: center;
		padding: 56rpx 0 0 0;
	}

	.card-content {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx 30rpx 20rpx;
		// padding: 20rpx 30rpx;
		margin:$margin-updown $margin-both;
		.gift-title {
			font-size: 30rpx;
		}
		.equity-itme {
			display: flex;
			align-items: center;
			image {
				width: 60rpx;
				height: 60rpx;
				margin-right: 30rpx;
			}
			.equity-content {
				padding: 20rpx 0;
				line-height: 1;
				&.active {
					border-bottom: 2rpx solid #e5e5e5;
				}
				flex: 1;
				display: flex;
				flex-direction: column;
				.equity-desc {
					font-size: $font-size-activity-tag;
					margin-top: 16rpx;
					color: $color-tip;
				}
			}
		}
	}
	
	.card-privilege-list{
		 width: 100%;
		 flex-wrap: wrap;
		 display: flex;
		 justify-content: center;
		 
		 .card-privilege-item{
			width: 33%;
			display: inline-block;
			margin-top: 0;
			text-align: center;
			.card-privilege-icon{
				width: 60rpx;
				height: 60rpx;
				text-align: center;
				margin: 0 auto;
				line-height: 1;
			}
			.card-privilege-name{
				color: $color-title;
				font-size: $font-size-sub;
				padding-top: 20rpx;
			}
			.card-privilege-text{
				color: $color-tip;
				font-size: $font-size-goods-tag;
				padding: 0 20rpx;
			}
			.iconfont {
				font-size: 60rpx;
				background-image:-webkit-linear-gradient(top,#E3B66B,#F7DAA5); 
				-webkit-background-clip:text; 
				-webkit-text-fill-color:transparent; 
			}
			.icon-zhekou,.icon-hongbao{
				font-size: 54rpx;
			}
			
		}
	}

	.member-gift {
		background-color: #fff;
		margin: $margin-updown $margin-both;
		padding: 20rpx 30rpx;
		border-radius: 10rpx;
		.gift-title {
			font-size: 30rpx;
		}
		.gift-itme {
			display: flex;
			align-items: center;
			image {
				width: 60rpx;
				height: 60rpx;
				margin-right: 30rpx;
			}
			.gift-content {
				&.active {
					border-bottom: 2rpx solid #e5e5e5;
				}
				padding: 20rpx 0;
				line-height: 1;
				flex: 1;
				display: flex;
				flex-direction: column;
				.gift-desc {
					font-size: 24rpx;
					margin-top: 16rpx;
					color: #999;
				}
			}
		}
	}

	.desc-wrap {
		box-sizing: border-box;
		width: 100%;
		height: 98rpx;
		padding: 24rpx 66rpx 0;

		.title {
			width: 100%;
			height: 42rpx;
			line-height: 42rpx;
			color: #222222;
			font-size: $font-size-base;
			font-family: 'PingFangTC-Regular';
			font-weight: 600;
			text-align: left;
		}

		.desc {
			margin-top: 4rpx;
			width: 100%;
			height: 34rpx;
			line-height: 34rpx;
			color: #999999;
			font-size: $font-size-tag;
			font-family: 'PingFangTC-Regular';
			text-align: left;
		}
	}

	@keyframes descAnimation {
		0% {
			opacity: 1;
		}

		25% {
			opacity: 0.5;
		}

		50% {
			opacity: 0;
		}

		75% {
			opacity: 0.5;
		}

		100% {
			opacity: 1;
		}
	}

	@-webkit-keyframes descAnimation {
		0% {
			opacity: 1;
		}

		25% {
			opacity: 0.5;
		}

		50% {
			opacity: 0;
		}

		75% {
			opacity: 0.5;
		}

		100% {
			opacity: 1;
		}
	}
}
.coupon-popup-box {
	background-color: #f7f7f7;
	.coupon-popup-title {
		text-align: center;
		font-size: 32rpx;
		line-height: 90rpx;
		height: 90rpx;
		display: block;
		font-weight: bold;
		position: relative;
		border-bottom: 1rpx solid #eeeeee;
	}
	.iconfont {
		position: absolute;
		float: right;
		right: 44rpx;
		font-size: 40rpx;
		font-weight: 500;
	}
	.coupon-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 4rpx;
		.coupon-name {
			flex: 1;
			display: flex;
			flex-direction: column;
			.desc {
				margin-top: 20rpx;
				font-size: $font-size-tag;
				color: #ababab;
			}
		}
		.coupon-price {
			color: red;
			text {
				font-size: 70rpx;
			}
		}
	}
	.coupon-popup-content {
		max-height: 390rpx;
		padding: 20rpx;
		box-sizing: border-box;
	}
}
.card-content-head{
	text-align: center;
	color: $color-title;
	margin: 20rpx 0;
	.line-box{
		float: left;
		text-align: center;
		width: 35%;
		margin-top: 26rpx;
		.line{
			background-color: $color-title;
			width: 60rpx;
			height: 2rpx;
		}
	}
	.card-content-title{
		float: left;
		text-align: center;
		width: 30%;
		font-size: $font-size-base;
		color: $color-title;
	}
}

.right{
	float: right;
}
.clear{
	clear: both;
}
.card-time-list{
	margin: -7.5rpx;
	white-space: nowrap;
	overflow-x: scroll;
	height: 256rpx;
		
	.card-item-box{
		padding: 15rpx;
		display: inline-block;
		width: 33.3333%;
		box-sizing: border-box;
		
		&.small {
			width: 32.3%;
		}
		
		.card-time-item{
			border: 2rpx solid #cccccc;
			border-radius: $border-radius;
			text-align: center;
			padding: 25rpx 0 20rpx;
			
			image{
				width: 60rpx;
			}
			
			.time-name {
				line-height: 1.3;
			}
		}
		.card-time-item.active{
			border-color: #E3B66B;
			background: rgba(227, 182, 107, .3);
		}
		.time-price{
			font-size: $font-size-tag;
			text{
				font-size: $font-size-toolbar;
			}
			
			.price {
				font-weight: bolder;
			}
		}
	}
}
.action-wrap{
	height: 140rpx;
	
	&.have-agreement{
		height: 190rpx;
	}
	
	&.bottom-safe-area {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
}
.action {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 140rpx;
	background: #fff;
	box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
	text-align: right;
	line-height: 100rpx;
	padding: 0 40rpx;
	box-sizing: border-box;
	
	&.have-agreement{
		height: 190rpx;
	}
	
	.agreement {
		text-align: center;
		font-size: $font-size-tag;
		line-height: 1;
		margin-top: 20rpx;
		
		text {
			color: #E3B66B;
		}
	}

	&.bottom-safe-area {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.action-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		color: #7C5711;
		padding: 0 40rpx;
		display: inline-block;
		text-align: center;
		margin: 30rpx 0 0 0;
		border-radius: 10rpx;
		border: none;
		background-image:linear-gradient(to top,#F7DAA5,#E3B66B);
		box-sizing: border-box;
	}
	
	.title{
		margin-right: 6rpx;
	}
	
	.bold{
		font-weight: bold;
	}
}

/* 说明弹框 */
.tips-layer {
	background: #fff;
	z-index: 999;
	height: 40%;
	width: 100%;
	
	.head {
		position: relative;
	}
	
	.title {
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		font-size: $font-size-toolbar;
		font-weight: 700;
	}
	
	text {
		position: absolute;
		top: 8rpx;
		right: 44rpx;
		font-size: $font-size-toolbar;
		font-weight: 500;
	}
	
	.body {
		width: 100%;
		height: calc(100% - 80rpx);
		overflow-y: scroll;
		
		.detail {
			padding: 20rpx;
			
			.font-size-base {
				margin-bottom: 10rpx;
			}
		}
		
	}
}