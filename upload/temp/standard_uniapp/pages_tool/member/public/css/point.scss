.custom-navbar {
	width: 100vw;
	padding-bottom: 20rpx;
	position: fixed;
	left: 0;
	top: 0;
	z-index: 100;
	background: unset;
	
	// #ifdef MP-WEIXIN
	background-size: 100% 380rpx;
	// #endif
	
	.navbar-wrap {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
	}
	
	.navbar-title {
		color: #fff;
		font-size: 32rpx;
		font-weight: 600;
	}
	
	.back {
		position: absolute;
		color: #fff;
		left: 30rpx;
		font-size: 40rpx;
	}
}

.custom-navbar-block {
	padding-bottom: 20rpx;
}


.head-wrap {
	width: 100vw;
	background-size: 100%;
	padding: 60rpx 68rpx 140rpx 68rpx; 
	box-sizing: border-box;
	border-radius: 0 0 100% 100%/0 0 70rpx 70rpx;
	overflow: hidden;
	
	// #ifdef MP-WEIXIN
	padding-top: 160rpx;
	// #endif
	
	.title {
		text-align: left;
		line-height: 1;
		color: #F6F6F6;
	}
	
	.point {
		color: var(--btn-text-color);
		text-align: left;
		line-height: 1;
		margin-bottom: 20rpx;
		font-size: 64rpx;
	}
	
	.flex-box {
		display: flex;
		margin-top: 56rpx;
		
		.flex-item {
			flex: 1;
			
			.num {
				font-size: 34rpx;
				margin-bottom: 20rpx;
				color: #fff;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
			
			view {
				text-align: left;
				color: #F6F6F6;
				line-height: 1;
			}
		}
	}
}

.menu-wrap {
	border-radius: 20rpx;
	margin: 0 24rpx;
	padding: 30rpx;
	background: #fff;
	display: flex;
	transform: translateY(-90rpx);
	
	.menu-item {
		flex: 1;
		text-align: left;
		display: flex;
		align-items: center;
		
		.icon {
			width: 88rpx;
			height: 88rpx;
			background: #F3F3F3;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			margin-right: 24rpx;
			
			image {
				width: 70%;
			}
		}
		
		.title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
		}
	}
}

.task-wrap {
	background-color: #fff;
	margin: 30rpx 24rpx;
	border-radius: 18rpx;
	padding: 32rpx;
	transform: translateY(-90rpx);
	
	.title {
		font-size: 32rpx;
		text-align: left;
		margin-bottom: 40rpx;
		font-weight: bold;
	}
	
	.task-item {
		border-radius: $border-radius;
		background: #fff;
		display: flex;
		align-items: center;
		margin-bottom: 80rpx;
		
		&:last-child {
			margin-bottom: 30rpx;
		}
		
		.icon {
			width: 62rpx;
			height: 62rpx;
			background: #F3F3F3;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
		}
		
		.iconfont {
			font-size: 52rpx;
			-webkit-background-clip: text !important;
			-webkit-text-fill-color: transparent;
			background: linear-gradient(135deg, #FE7849 0%, #FF1959 100%);
		}
		
		.iconshangpin {
			font-size: 48rpx;
		}
		
		.wrap {
			flex: 1;
			padding-left: 26rpx;
			
			.title {
				line-height: 1;
				font-size: 28rpx;
				font-weight: bold;
				margin-bottom: 0;
			}
			
			.desc {
				line-height: 1;
				margin-top: 10rpx;
			}
		}
		
		.btn {
			height: 60rpx;
			line-height: 60rpx;
			border-radius: 60rpx;
			text-align: center;
			width: 140rpx;
			color: #fff;
			font-size: 26rpx;
			font-weight: 600;
			background: linear-gradient(135deg, #FE7849 0%, #FF1959 100%);
		}
	}
}