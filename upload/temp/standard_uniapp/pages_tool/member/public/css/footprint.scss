.empty {
	margin-top: 100rpx;
}

.lineheight-clear {
}

.head-wrap {
	width: 100vw;
	height: 90rpx;
	line-height: 90rpx;
	background: #fff;
	box-sizing: border-box;
	padding: 0 30rpx;
	text-align: right;
}

.goods-list.single-column {
	margin: 0 $margin-both;
	
	.checkbox-wrap {
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		
		.iconfont {
			font-size: 40rpx;
			color: #ccc;
		}
	}
	
	.datetime {
		line-height: 1.5;
		font-size: $font-size-base;
	}
	
	.goods-item {
		padding: 26rpx;
		background: #fff;
		border-radius: $border-radius;
		display: flex;
		position: relative;
		margin: $margin-updown 0;
		
		&.first-child {
			margin-top: 0;
		}
		
		
		.goods-img {
			width: 200rpx;
			height: 200rpx;
			overflow: hidden;
			border-radius: $border-radius;
			margin-right: 20rpx;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.goods-tag{
			color: #fff;
			line-height: 1;
			padding: 8rpx 12rpx;
			position: absolute;
			border-top-left-radius: $border-radius;
			border-bottom-right-radius: $border-radius;
			top: 26rpx;
			left: 26rpx;
			font-size: $font-size-goods-tag;
		}
		
		.goods-tag-img {
			position: absolute;
			border-top-left-radius: $border-radius;
			width: 80rpx;
			height: 80rpx;
			top: 26rpx;
			left: 26rpx;
			z-index: 5;
			overflow: hidden;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.info-wrap {
			flex: 1;
			display: flex;
			flex-direction: column;
			width: calc(100% - 220rpx);
		}
		&.manage{
			.info-wrap {
				width: calc(100% - 280rpx);
			}
		}
		
		.name-wrap {
			flex: 1;
		}
		
		.goods-name {
			font-size: $font-size-base;
			line-height: 1.3;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			height: 68rpx;
		}
		
		.introduction {
			line-height: 1;
			margin-top: 10rpx;
		}
		
		.discount-price {
			display: inline-block;
			font-weight: bold;
			line-height: 1;
			margin-top: 16rpx;
			color: var(--price-color);
			.unit {
				margin-right: 6rpx;
			}
		}
		
		.pro-info {
			display: flex;
			margin-top: auto;
			
			.delete-price {
				text-decoration:line-through;
				flex: 1;
				
				.unit {
					margin-right: 6rpx;
				}
			}
			
			& > view {
				line-height: 1;
				
				&:nth-child(2) {
					text-align: right;
				}
			}
		}
		
		.member-price-tag {
			display: inline-block;
			width: 60rpx;
			line-height: 1;
			margin-left: 6rpx;
			
			image {
				width: 100%;
				max-height: 30rpx;
			}
		}
	}
	
}

.bottom-wrap {
	position: fixed;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	bottom: var(--window-bottom);
	overflow: hidden;
	display: flex;
	bottom: 0;
	z-index: 9;
	
	.all-election {
		flex: 1;
		height: 100rpx;
		position: relative;
		display: inline-block;
	
		& > .iconfont {
			font-size: 40rpx;
			position: absolute;
			top: 50%;
			left: 30rpx;
			transform: translateY(-50%);
		}
		& > .icon-yuan_checkbox {
			color: $color-disabled;
		}
	
		& > text {
			margin-left: 56rpx;
			line-height: 100rpx;
			padding-left: 30rpx;
		}
	}
	
	.action-btn {
		flex: 1;
		width: 180rpx;
		height: 100rpx;
		line-height: 100rpx;
		border-radius: 0;
		margin: 0;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		
		button {
			width: 180rpx;
			height: 70rpx;
			line-height: 70rpx;
		}
	}
}