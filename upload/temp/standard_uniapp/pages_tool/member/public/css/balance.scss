.custom-navbar {
	width: 100vw;
	padding-bottom: 20rpx;
	position: fixed;
	left: 0;
	top: 0;
	z-index: 100;
	background: unset;
	
	// #ifdef MP-WEIXIN
	background-size: 100% 380rpx;
	// #endif
	
	.navbar-wrap {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
	}
	
	.navbar-title {
		color: #fff;
		font-size: 32rpx;
		font-weight: 600;
	}
	
	.back {
		position: absolute;
		color: #fff;
		left: 30rpx;
		font-size: 40rpx;
	}
}

.custom-navbar-block {
	padding-bottom: 20rpx;
}

.head-wrap {
	width: 100vw;
	background-size: 100%;
	padding: 60rpx 68rpx 140rpx 68rpx; 
	box-sizing: border-box;
	border-radius: 0 0 100% 100%/0 0 70rpx 70rpx;
	overflow: hidden;
	
	// #ifdef MP-WEIXIN
	padding-top: 160rpx;
	// #endif
	
	.title {
		text-align: left;
		line-height: 1;
		color: #F6F6F6;
	}
	
	.balance {
		color: var(--btn-text-color);
		text-align: left;
		line-height: 1;
		margin-bottom: 20rpx;
		font-size: 64rpx;
	}
	
	.flex-box {
		display: flex;
		margin-top: 56rpx;
		
		.flex-item {
			flex: 1;
			
			.num {
				font-size: 34rpx;
				margin-bottom: 20rpx;
				color: #fff;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
			
			view {
				text-align: left;
				color: #F6F6F6;
				line-height: 1;
			}
		}
	}
}

.menu-wrap {
	border-radius: 20rpx;
	margin: 0 24rpx;
	padding: 0 30rpx;
	background: #fff;
	transform: translateY(-90rpx);
	
	.menu-item {
		display: flex;
		align-items: center;
		padding: 12rpx 0;
		
		.icon {
			height: 80rpx;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			color: #fff;
			margin-right: 20rpx;
			
			.iconfont {
				font-size: 46rpx;
				-webkit-background-clip: text !important;
				-webkit-text-fill-color: transparent;
				background: linear-gradient(135deg, #FE7849 0%, #FF1959 100%);
			}
		}
		
		.title {
			font-size: 28rpx;
			color: #333333;
			flex: 1;
		}
		
		.iconright {
			font-size: 28rpx;
		}
	}
}

.action {
	position: fixed;
	width: 100vw;
	left: 0;
	bottom: 0;
	padding-bottom: 100rpx;
	
	view {
		width: calc(100vw - 64rpx);
		height: 80rpx;
		line-height: 80rpx;
		border-radius: 80rpx;
		margin: 0 auto 30rpx auto;
		text-align: center;
		color: #fff;
		font-size: 32rpx;
	}
	.recharge-withdraw {
		background: #FF4646;
	}
	
	.withdraw {
		border: 4rpx solid #FF4646;
		box-sizing: border-box;
		line-height: 72rpx;
		color: #FF4646;
	}
}