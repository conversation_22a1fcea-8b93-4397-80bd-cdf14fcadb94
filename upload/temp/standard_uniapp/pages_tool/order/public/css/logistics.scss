@mixin flex-row-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

@mixin wrap {
	padding: 30rpx;
	border-radius: $border-radius;
	background: #fff;
	position: relative;
	width: calc(100% - 60rpx);
	box-sizing: border-box;
}
.swiper-item {
	padding-top: 94rpx;
	height: 100%;
	.container {
		height: calc(100vh - 124rpx);
		overflow-y: scroll;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-bottom: 30rpx;
		&.safearea {
			padding: 68rpx;
		}
	}
	padding-bottom: 30rpx;
	padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
	padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
}
.order-nav {
	width: 100vw;
	// height: 60rpx;
	flex-direction: row;
	/* #ifndef APP-PLUS */
	white-space: nowrap;
	/* #endif */
	background: #fff;
	display: flex;
	position: fixed;
	left: 0;
	z-index: 998;

	.uni-tab-item {
		display: inline-block;
		padding: 30rpx 24rpx 0;
		// height: 60rpx;
		// line-height: 60rpx;
	}

	.uni-tab-item-title {
		color: #555;
		font-size: $font-size-toolbar;
		display: block;
		line-height: 1;
		padding: 0 10rpx 30rpx;
		flex-wrap: nowrap;
		/* #ifndef APP-PLUS */
		white-space: nowrap;
		/* #endif */
		text-align: center;
	}

	.uni-tab-item-title-active {
		display: block;
		border-bottom: 2rpx solid #ffffff;
		padding: 0 10rpx 30rpx;
	}

	::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
	}
}

.goods-wrap {
	@include wrap;
	margin-top: 20rpx;
	padding: 30rpx;
	.goods {
		display: flex;
		position: relative;
		margin-bottom: 20rpx;

		&:last-of-type {
			margin-bottom: 0;
		}

		.goods-img {
			width: 180rpx;
			height: 180rpx;
			margin-right: 20rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.goods-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			position: relative;
			max-width: calc(100% - 140rpx);

			.goods-name {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				line-height: 1.5;
				font-size: $font-size-base;
				font-weight: 500;
			}

			.goods-sub-section {
				width: 100%;
				line-height: 1.3;
				display: flex;

				.goods-price {
					font-weight: 700;
					font-size: $font-size-activity-tag;
				}

				.unit {
					font-weight: normal;
					font-size: $font-size-tag;
					margin-right: 2rpx;
				}

				view {
					flex: 1;
					line-height: 1.3;
					&:last-of-type {
						text-align: left;

						.iconfont {
							line-height: 1;
							font-size: $font-size-tag;
						}
					}
				}
			}
		}
	}
}

.express-company-wrap {
	@include wrap;
	margin-top: 20rpx;
	.company-logo {
		width: 120rpx;
		height: 120rpx;
		margin-right: 20rpx;
		float: left;
		image {
			width: 100%;
			height: 100%;
		}
	}

	.info {
		flex: 1;

		.company {
			line-height: 1.5;
			margin-top: 16rpx;
		}

		.no {
			margin-top: 10rpx;
			line-height: 1.5;
		}

		.icon-fuzhi {
			font-size: $font-size-base;
			line-height: 1;
			margin-left: 6rpx;
		}
	}
}

.track-wrap {
	@include wrap;
	margin-top: 20rpx;

	.track-item {
		position: relative;
		flex-wrap: wrap;
		overflow: visible;
		display: flex;
		&:after {
			content: '';
			position: absolute;
			z-index: 1;
			pointer-events: none;
			background-color: #e5e5e5;
			width: 2rpx;
			height: 150%;
			top: 56rpx;
			left: 20rpx;
			bottom: -40rpx;
		}

		.dot {
			margin: 34rpx 20rpx 0 10rpx;
			width: 20rpx;
			height: 20rpx;
			border-radius: 10rpx;
			background-color: #ccc;
			z-index: 9;
		}

		.msg {
			padding: 20rpx 0;
			flex: 1;

			.text {
				line-height: 1.5;
				font-size: $font-size-base;
			}

			.time {
				color: $color-tip;
				font-size: $font-size-activity-tag;
				line-height: 1.3;
				margin-top: 10rpx;
			}
		}

		&:last-of-type:after {
			content: unset;
		}
	}
}
