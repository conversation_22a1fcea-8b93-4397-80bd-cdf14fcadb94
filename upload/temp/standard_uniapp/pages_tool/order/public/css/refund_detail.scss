.detail-container {
	width: 100vw;
	height: 100vh;
}
.container {
	transition: all 0.3s;

	.hide {
		transform: translateX(-100%);
	}
}

.status-wrap {
	padding: 20rpx;
	background: #fff;
	border-top: 1px solid #f5f5f5;
	margin: 20rpx;
	border-radius: $border-radius;

	.status-name {
		display: block;
		font-size: $font-size-toolbar;
		line-height: 70rpx;
		height: 70rpx;
	}

	.refund-explain {
		border-top: 1px dashed #eee;
		padding-top: 20rpx;
	}
}

.refund-address-wrap {
	margin: 20rpx;
	background: #fff;
	padding: 20rpx;
	border-radius: $border-radius;

	.copy {
		font-size: $font-size-activity-tag;
		display: inline-block;
		color: #666;
		background: #fff;
		line-height: 1;
		padding: 6rpx 10rpx;
		margin-left: 10rpx;
		border-radius: 4rpx;
		border: 1px solid #ddd;
	}
}

.history-wrap {
	margin: 20rpx;
	background: #fff;
	padding: 20rpx;
	display: flex;
	position: relative;
	border-radius: $border-radius;

	view {
		flex: 1;
	}

	.icon-right {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		color: #ddd;
		right: 20rpx;
	}
}

.refund-info {
	margin: 20rpx;
	background: #fff;
	border-radius: $border-radius;

	.header {
		height: 90rpx;
		line-height: 90rpx;
		padding: 0 20rpx;
	}

	.body {
		padding-bottom: 20rpx;

		.goods-wrap {
			display: flex;
			position: relative;
			padding: 20rpx;
			background: #f5f5f5;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 180rpx;
				height: 180rpx;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				max-width: calc(100% - 200rpx);

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: $font-size-base;
				}

				.goods-sub-section {
					padding-top: 20rpx;
					width: 100%;
					line-height: 1.3;
					display: flex;

					.refund-price {
						font-size: $font-size-base;
					}

					.unit {
						font-weight: normal;
						font-size: $font-size-tag;
						margin-right: 2rpx;
					}
				}
			}
		}

		.info {
			margin-top: 20rpx;
			
			.cell {
				height: 50rpx;
				line-height: 50rpx;
				padding: 0 30rpx;
				font-size: $font-size-tag;
				color: $color-tip;
			}
			
			&.refund-images{
				margin-top: 0;
				.cell{
					height: auto;
					display: flex;
					align-items: flex-start;
					.cell-title{
						font-size: $font-size-tag;
						color: $color-tip;
					}
					.images{
						flex: 1;
						display: flex;
						align-items: center;
						flex-wrap: wrap;
						image{
							width: 130rpx;
							height: 130rpx;
							margin-right: 20rpx;
							margin-bottom: 20rpx;
						}
					}
				}
			}
			
		}
	}
}

.action {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;
	line-height: 100rpx;

	&.bottom-safe-area {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
	.order-box-btn{
		margin-right: $margin-both;
		margin-left: 0;
	}

	.action-btn {
		height: 70rpx;
		line-height: 70rpx;
		color: #fff;
		padding: 0 40rpx;
		display: inline-block;
		text-align: center;
		margin: 16rpx 20rpx 16rpx 0;
		border-radius: $border-radius;

		&.white {
			height: 68rpx;
			line-height: 68rpx;
			color: #333;
			border: 0.5px solid #999;
			background: #fff;
		}
	}
}

.form-wrap {
	background: #fff;

	.item {
		margin: 0 20rpx;
		display: flex;
		border-bottom: 1px solid #eee;

		&:last-child {
			border-bottom: none;
		}

		.label {
			width: 140rpx;
			line-height: 90rpx;
		}

		.cont {
			flex: 1;
			line-height: 90rpx;

			.input,
			.input-placeholder {
				height: 90rpx;
				line-height: 90rpx;
				font-size: $font-size-base;
			}

			.textarea {
				width: 100%;
				padding: 26rpx 0;
				line-height: 1.3;
				font-size: $font-size-base;
			}
		}
	}
}

.sub-btn {
	margin-top: 20rpx;
}

.record-wrap {
	.cont {
		width: 100%;
		background-color: #fff;
		padding: 30rpx;
		box-sizing: border-box;
		margin-top: 20rpx;
	
		.head {
			display: flex;
			flex-direction: column;
			color: $color-title;
			.time {
				color: $color-tip;
				font-size: $font-size-tag;
				float: right;
			}
		}
		.body {
			padding-top: 20rpx;
			.refund-action{
				line-height: 1;
				color: $color-title;
			}
			.desc {
				margin-top: 10rpx;
				color: $color-tip;
				font-size: $font-size-tag;
			}
		}
	}
	
	.empty-box{
		height: 168rpx;
	}
}

.history-bottom {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;
	display: flex;

	&.bottom-safe-area {		
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	view {
		flex: 1;
		text-align: center;
		line-height: 100rpx;

		&:first-child {
			border-right: 1px solid #eee;
		}
		.iconfont {
			font-weight: bold;
			margin-right: 10rpx;
			font-size: $font-size-base;
			line-height: 1;
		}
	}
	button {
		width: 50%;
		height: 100%;
		// position: absolute;
		border: none;
		z-index: 1;
		padding: 0;
		margin: 0;
		background: none;
		display: flex;
		justify-content: center;
		align-items: center;
		&::after {
			border: none !important;
		}
		.iconfont{
			margin-right: 10rpx;
		}
		
	}
}
