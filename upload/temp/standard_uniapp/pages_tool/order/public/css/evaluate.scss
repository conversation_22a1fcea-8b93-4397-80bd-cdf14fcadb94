.page {
	padding-bottom: 100rpx;
}

.eval-good {
	width: 100%;
	padding: 0 30rpx;
	box-sizing: border-box;
	background: #ffffff;

	.good-box {
		width: 100%;
		height: 100%;
		padding: 30rpx 0;
		border-bottom: 2rpx solid #f5f5f5;
		box-sizing: border-box;
		display: flex;
		justify-content: space-between;

		.good_pic {
			width: 180rpx;
			height: 180rpx;
			margin-right: 20rpx;
			box-sizing: border-box;
		}

		.good_info {
			width: calc(100% - 200rpx);
			height: 100%;
			line-height: 1.3;
			box-sizing: border-box;
		}
	}
}

.eval-text {
	width: 100%;
	padding: 0 $margin-both;
	box-sizing: border-box;
	padding-bottom: $padding;
	margin-top: $margin-updown;

	.text-box {
		width: 100%;
		height: 100%;
		border-radius: $border-radius;
		background: #ffffff;
		padding-bottom: $padding;
		box-sizing: border-box;
		position: relative;

		textarea {
			width: 100%;
			height: 190rpx;
			padding: $padding;
			box-sizing: border-box;
			font-size: $font-size-tag;
		}
	}

	.maxSize {
		position: absolute;
		right: 20rpx;
		top: 160rpx;
		color: #999;
		font-size: $font-size-tag;
	}

	.other-info {
		width: 100%;
		padding: 0 $padding;
		box-sizing: border-box;
		display: flex;
		flex-wrap: wrap;
		margin-top: $margin-updown;
	}

	.other-info-box {
		width: 145rpx;
		height: 145rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-right: 30rpx;
		margin-bottom: 30rpx;
		position: relative;
		
		image {
			width: 100%;
			border-radius: $border-radius;
		}
		.iconfont {
			font-size: 60rpx;
			color: #898989;
			line-height: 1;
		}
		text {
			line-height: 1;
		}
		.imgDel {
			width: 40rpx;
			height: 40rpx;
			position: absolute;
			right: -20rpx;
			top: -20rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			.iconfont {
				font-size: $font-size-toolbar;
			}
		}
	}
	.other-info-box.active {
		border: 1rpx dashed #898989;
	}
	.other-info-box.active:active {
		background: rgba($color: #cccccc, $alpha: 0.6);
	}
	// .other-info-box:nth-child(4n) {
	// 	margin-right: 0;
	// }
}

.eval-star {
	width: 100%;
	background: #ffffff;
	padding: 10rpx 30rpx;
	box-sizing: border-box;

	.star-box {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;

		.star-title {
			height: 60rpx;
			position: relative;
			padding-right: $padding;
			box-sizing: border-box;
			line-height: 60rpx;
			font-size: $font-size-base;
			font-weight: bold;
		}

		.grade-li {
			width: 30%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.icon-haoping1 {
			font-size: $font-size-base;
			margin-right: 10rpx;
		}

		.icon-haoping {
			font-size: $font-size-base;
			color: #999;
			margin-right: 10rpx;
		}

		.icon-zhongchaping {
			font-size: $font-size-base;
			margin-right: 10rpx;
			color: #ccc;
		}
	}
}

.eval-bottom {
	position: fixed;
	z-index: 5;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	bottom: var(--window-bottom);
	overflow: hidden;
	display: flex;
	justify-content: space-between;
	&.safe-area {
		padding-bottom: 68rpx !important;
	}

	.all-election {
		height: 100rpx;
		position: relative;
		padding-left: 20rpx;
		display: inline-block;
		width: 30%;

		& > .iconfont {
			font-size: 45rpx;
			position: absolute;
			top: 50%;
			left: 24rpx;
			transform: translateY(-50%);
		}

		& > text {
			margin-left: 56rpx;
			line-height: 100rpx;
		}
	}

	.action-btn {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		border-radius: 0;
		margin: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		
		button {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
		}
	}
	.action-btn.disabled:after {
		content: '';
		border: none;
	}
}