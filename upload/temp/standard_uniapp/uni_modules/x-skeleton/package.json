{"id": "x-skeleton", "displayName": "skeleton骨架屏（可任意配置-易用-灵活-动画）", "version": "1.0.4", "description": "x-skeleton骨架屏可随意配置内容，扩展性强，简单易用，内含常用的骨架类型", "keywords": ["skeleton", "骨架屏", "加载效果", "vue", "微信小程序"], "repository": "", "engines": {}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "u"}, "App": {"app-vue": "u", "app-nvue": "u"}, "H5-mobile": {"Safari": "u", "Android Browser": "u", "微信浏览器(Android)": "u", "QQ浏览器(Android)": "u"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "u"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}