// 营销活动
.goods-promotion {
	background: var(--promotion-color);
	height: 150rpx;
	.price-info {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		.icon-box {
			margin-right: 20rpx;
			.iconfont {
				font-size: 60rpx;
				color: #ffffff;
			}
		}
		.price-box {
			display: flex;
			align-items: flex-start;
			flex-direction: column;
			height: 100%;
			justify-content: center;
			.seckill-text {
				font-size: 36rpx;
				color: #fff;
				line-height: 1;
			}
			.sale-num {
				margin-top: 15rpx;
				font-size: 28rpx;
			}
		}
	}

	.countdown {
		width: 220rpx;
		background: var(--promotion-aux-color);
		.txt {
			color: #ffffff !important;
			font-size: 28rpx;
		}
		.clockrun {
			margin-top: 16rpx !important;
		}
		&:after {
			position: absolute;
			content: '';
			top: calc(50% - 15rpx);
			z-index: 5;
			left: -15rpx;
			width: 0;
			height: 0;
			border-style: solid;
			border-width: 15rpx 15rpx 15rpx 0;
			border-color: transparent var(--promotion-aux-color) transparent transparent;
		}
	}
}

.rule-wrap {
	.content {
		padding: 10rpx 20rpx 20rpx;
	}
}
