.page {
	width: 100%;
	min-height: 100vh;
	background: var(--seckill-promotion-color);
}
.ns-adv {
	margin: 0;
	border-radius: $border-radius;
	overflow: hidden;
	line-height: 1;
	image {
		width: 100%;
	}
}
.time-box {
	color: #fff;
	text-align: center;
	font-size: $font-size-tag;
	margin-bottom: 20rpx;
}
.time-wrap {
	display: flex;
	padding: 10rpx 0;
	line-height: 1;
	border-radius: $border-radius;
	margin: 10rpx 30rpx 20rpx;

	&.fixed {
		position: fixed;
		z-index: 10;
		top: 0;
		width: 100vw;
		box-sizing: border-box;
	}

	& > image {
		width: 66rpx;
		margin-right: 20rpx;
	}

	.scroll-wrap {
		flex: 1;
		max-width: 100%;
		white-space: nowrap;

		.time-item {
			padding: 14rpx 0;
			text-align: center;
			display: inline-block;
			color: #ffffff;
			background: rgba(0, 0, 0, 0.25);
			margin-right: 20rpx;
			width: calc((100% - 60rpx) / 4);
			border-radius: $border-radius;

			.start-time {
				font-weight: bold;
				line-height: 1;
			}

			.em {
				line-height: 1;
				padding: 6rpx 30rpx;
				border-radius: 24rpx;
				margin-top: 6rpx;
				font-weight: bold;
			}

			.em.active {
				color: $color-title !important;
			}

			> view {
				color: #ffffff;
			}
			&.active {
				color: $color-title;
				border-radius: $border-radius;
				background-color: #fff;
				> view {
					color: $color-title;
				}
			}
		}
	}
}

.lineheight-clear {
	line-height: 1 !important;
}
// 商品列表单列样式
.goods-list.single-column {
	.goods-item {
		padding: 26rpx;
		background: #fff;
		margin: $margin-updown $margin-both;
		border-radius: $border-radius;
		display: flex;
		position: relative;

		.goods-img {
			width: 200rpx;
			height: 200rpx;
			overflow: hidden;
			border-radius: $border-radius;
			margin-right: 20rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.goods-tag {
			color: #fff;
			line-height: 1;
			padding: 8rpx 12rpx;
			position: absolute;
			border-top-left-radius: $border-radius;
			border-bottom-right-radius: $border-radius;
			top: 26rpx;
			left: 26rpx;
			font-size: $font-size-goods-tag;
		}

		.info-wrap {
			flex: 1;
			width: 0;
			display: flex;
			flex-direction: column;
		}

		.name-wrap {
			flex: 1;
		}

		.goods-name {
			font-size: $font-size-base;
			line-height: 1.3;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			height: 68rpx;
		}

		.progress-wrap {
			display: flex;
			margin-top: 10rpx;

			progress {
				flex: 1;
			}

			.txt {
				margin: 0 100rpx 0 20rpx;
			}
		}

		.discount-price {
			display: inline-block;
			font-weight: bold;
			line-height: 1;
			margin-top: 16rpx;
			color: var(--price-color);
			.unit {
				margin-right: 6rpx;
			}

			.txt {
				font-weight: normal;
			}
		}

		.pro-info {
			position: relative;
			margin-top: 16rpx;

			.delete-price {
				text-decoration: line-through;
				flex: 1;
				display: flex;
				align-items: flex-end;

				.unit {
					margin-right: 6rpx;
				}

				.txt {
					text-decoration: none;
				}
			}

			& > view {
				line-height: 1;

				&:nth-child(2) {
					position: absolute;
					right: 0;
					bottom: 0;
				}
			}

			.buy-btn {
				width: 160rpx;
				height: 90rpx;
				border-radius: 10rpx;

				.text {
					color: #fff;
					text-align: center;
					line-height: 1;
					padding-top: 20rpx;
				}

				.progress {
					margin-top: 6rpx;
					padding: 0 20rpx;
					display: flex;

					progress {
						flex: 1;
					}

					.num {
						margin-left: 10rpx;
						color: #fff;
						line-height: 1;
					}
				}
			}
		}

		.member-price-tag {
			display: inline-block;
			width: 60rpx;
			line-height: 1;
			margin-left: 6rpx;

			image {
				width: 100%;
			}
		}
	}

	.item {
		display: flex;
		margin: 0 30rpx 20rpx;
		padding: 0 20rpx;
		background-color: #ffffff;
		border-radius: $border-radius;

		&:last-child {
			margin-bottom: 0;
		}

		.img-wrap {
			width: 200rpx;
			height: 200rpx;

			image {
				width: 200rpx;
				border-radius: $border-radius;
			}
		}

		.content {
			display: flex;
			justify-content: space-between;
			flex-direction: column;
			flex: 1;
			margin: 10rpx 0 10rpx 20rpx;

			.goods-name {
				font-weight: bold;
				&.multi-hidden {
					line-height: 1.3;
				}
			}

			.progress {
				display: flex;
				align-items: center;

				.bg {
					width: 240rpx;
					height: 20rpx;
					border-radius: 20rpx;
					margin-right: 20rpx;
					background-color: #fcecd7;

					.curr {
						height: 20rpx;
						border-radius: 20rpx;
						background-color: #fdbe6c;
					}
				}

				.num {
					font-size: $font-size-tag;
					color: $color-tip;
				}
			}

			.bottom-wrap {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.price-wrap {
					overflow: hidden;
					display: flex;
					align-items: baseline;
					flex-wrap: wrap;
					width: 260rpx;
					font-size: $font-size-tag;

					.discount-price {
						font-weight: bold;
						margin-right: 10rpx;
						line-height: 1;
					}

					.unit {
						font-size: $font-size-tag;
						color: var(--price-color);
					}

					.price {
						font-size: $font-size-toolbar;
						color: var(--price-color);
					}
				}

				.original-price {
					margin-top: 4rpx;
					font-size: $font-size-tag;
					text-decoration: line-through;
					line-height: 1;
					color: $color-tip;
				}

				button {
					height: 50rpx;
					line-height: 50rpx;
					background-color: $base-color;
					color: #fff;
					margin: 0;
					padding: 0 16rpx;
					font-size: $font-size-tag;
					font-weight: bold;
				}
			}
		}
	}
	.item {
		overflow: hidden;
		position: relative;
		padding: 16rpx;
		.img-wrap {
			position: relative;
			overflow: hidden;
		}
		.bottom-wrap .price-font {
			color: #999;
			font-size: $font-size-tag;
		}
		.content .progress {
			.num {
				color: #ff3d3d;
				margin-left: 10rpx;
			}
			.bg {
				background-color: #ffd5d5;
				.curr {
					position: relative;
					background-color: #ff0400 !important;
				}
			}
		}
		.progress-bar {
			position: absolute;
			right: -24rpx;
			width: 30rpx;
			height: 30rpx;
			top: 50%;
			transform: translateY(-50%);
		}
		.price-wrap {
			flex-direction: column-reverse;
		}
	}
}
