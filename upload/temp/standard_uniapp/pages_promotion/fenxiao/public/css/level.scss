.level-top {
	width: 100%;
	height: 40rpx;
	position: relative;
	
	image {
		width: 100%;
		height: 260rpx;
		position: absolute;
	}
}

.level-swiper {
	width: 100vw;
	height: 270rpx;
	
	.level-item {
		width: calc(100% - 60rpx);
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 26rpx;
		
		.level-wrap {
			width: 100%;
			height: 90%;
			background: #fff;
			border-radius: 16rpx;
			transition: all .5s;
			display: flex;
			flex-direction: column;
			padding: 20rpx 30rpx;
			box-sizing: border-box;
			position: relative;
		}
		
		.not-unlocked {
			position: absolute;
			width: 50rpx;
			height: 50rpx;
			background: #4B4B4B;
			display: flex;
			align-items: center;
			justify-content: center;
			right: -1rpx;
			top: -1rpx;
			border-top-right-radius: 16rpx;
			border-bottom-left-radius: 16rpx;
			
			.iconfont {
				color: #D3DEE6;
			}
		}
		
		&.curr {
			margin: 0;
			width: 100%;
			
			.level-wrap {
				height: 100%;
			}
		}
		
		.member-info {
			display: flex;
			align-items: center;
			.head-img {
				width: 90rpx;
				height: 90rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				overflow: hidden;
				
				image {
					width: 100%;
					height: 100%;
				}
			}
			
			.nickname {
				color: #fff;
				margin: 0 20rpx;
				font-size: 28rpx;
			}
			
			.level-name {
				line-height: 1;
				border: 2rpx solid #fff;
				padding: 4rpx 10rpx;
				border-radius: 6rpx;
				font-size: 26rpx;
				color: #f5f5f5;
			}
		}
		
		.level-rate {
			display: flex;
			margin-top: 30rpx;
			
			.rate-item {
				text-align: left;
				flex: 1;
			}
			.title {
				color: #f5f5f5;
				font-size: 24rpx;
			}
			.rate {
				margin-top: 10rpx;
				color: #fff;
				font-size: 38rpx;
				line-height: 1;
				
				.percentage {
					font-size: 28rpx;
					margin-left: 4rpx;
				}
			}
		}
	}
	
	swiper-item {
		&:nth-child(1) .level-wrap {
			background: linear-gradient(to right, #9BA7B1, #D3DEE6);
		}
		&:nth-child(2) .level-wrap {
			background: linear-gradient(to right, #F57E2A, #FAD494);
		}
		&:nth-child(3) .level-wrap {
			background: linear-gradient(to right, #F85151, #FF9999);
		}
		&:nth-child(4) .level-wrap {
			background: linear-gradient(to right, #78B8B4, #AFE6E2);
		}
		&:nth-child(5) .level-wrap {
			background: linear-gradient(to right, #4DA1E1, #58CBF0);
		}
		&:nth-child(6) .level-wrap {
			background: linear-gradient(to right, #81C636, #D1F677);
		}
		&:nth-child(7) .level-wrap {
			background: linear-gradient(to right, #6D7279, #A5AAB0);
		}
		&:nth-child(8) .level-wrap {
			background: linear-gradient(to right, #866DDB, #D49BFE);
		}
		&:nth-child(9) .level-wrap {
			background: linear-gradient(to right, #f1c74e, #f7dc81);
		}
		&:nth-child(10) .level-wrap {
			background: linear-gradient(to right, #418CCF, #9CC6F1);
		}
	}
}

.level-condition {
	margin: 30rpx 26rpx;
	background: #fff;
	padding: 30rpx;
	border-radius: 10rpx;
	
	.condition-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		
		.title {
			font-size: 32rpx;
			font-weight: bolder;
			position: relative;
			padding-left: 20rpx;
			line-height: 1;
			
			&:before {
				content: " ";
				position: absolute;
				left: 0;
				top: 0;
				width: 6rpx;
				height: 100%;
				background-color: $base-color;
				border-radius: 4rpx;
			}
		}
	}
	
	.rate {
		.complete {
			color: #E7B667;
			font-size: 26rpx!important;
			font-weight: normal!important;
		}
		.num {
			color: #bbb;
			font-size: 26rpx!important;
			font-weight: normal!important;
		}
	}
	
	.task-item {
		padding: 20rpx 30rpx;
		background: #F8F8F8;
		border-radius: 10rpx;
		margin-top: 30rpx;
		
		.flex-box {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
		
		.status{
			color: #999;
			
			&.complete {
				color: #000;
			}
		}
		
		.title {
			font-weight: bold;
			
			.iconfont {
				font-size: 28rpx;
				color: #B7B7B7;
				margin-left: 10rpx;
			}
		}
		
		.desc {
			color: #999;
			font-size: 24rpx;
		}
		
		.progress {
			margin: 16rpx 0;
		}
		
		.complete {
			font-size: 24rpx!important;
		}
		.num {
			font-size: 24rpx!important;
		}
	}
}

.popup {
	width: 100vw;
	background: #fff;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;

	.popup-header {
		display: flex;
		border-bottom: 2rpx solid $color-line;
		position: relative;
		padding: 40rpx;

		.tit {
			flex: 1;
			font-size: $font-size-toolbar;
			line-height: 1;
			text-align: center;
		}
		.iconfont {
			line-height: 1;
			position: absolute;
			right: 30rpx;
			top: 50%;
			transform: translate(0, -50%);
			color: $color-tip;
			font-size: $font-size-toolbar;
		}
	}

	.popup-body {
		height: calc(100% - 250rpx);
		padding: 30rpx;
		&.store-popup {
			height: calc(100% - 120rpx);
		}
		&.safe-area {
			height: calc(100% - 270rpx);
		}
		&.store-popup.safe-area {
			height: calc(100% - 140rpx);
		}
	}
}