.fenxiao-index {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.task-popup {
	.head-wrap{
		font-size: 16px;
		line-height: 51px;
		height: 51px;
		display: block;
		text-align: center;
		margin-top: 30rpx;
	}
	.body-wrap{
		max-height: 50vh;
		overflow-y: scroll;
	}
	.equity-content{
		width: 400rpx;
		height: 200rpx;
		position: relative;
		margin: 0 auto;
		// padding: 20rpx 0 20rpx 0rpx;
	}
	.subordinate-consumption{
		// margin-left: 130rpx;
		font-weight: 900;
	}
	.incomplete{
		font-weight: normal;
		color: rgb(153,153,153);
		margin-left: 120rpx;
		font-size: 24rpx;
	}
	.icon-wenhao{
		margin-left: 10rpx;
	}
	.to-complete-box{
		width: 100rpx;
		height: 40rpx;
		border-radius: 60rpx;
		background: rgb(233,51,35);
		position: absolute;
		left: 100%;
		top: 40px;
		line-height: 40rpx;
		text-align: center;
	}
	.to-complete-box{
		padding: 5rpx;
		color: #fff;
	}
	.circle{
		width: 355rpx;
		height: 12rpx;
		margin: 10rpx 0rpx; 
	}
	.zero{
		color:rgb(198,152,53);
		// margin-left: 130rpx;
	}
	.icon-close {
		position: absolute;
		float: right;
		right: 22px;
		font-size: 16px;
	}
}
.fenxiao-index-header {
	width: 100%;
	position: relative;
	height: 320rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	
	&.not-level {
		height: 200rpx;
	}
	
	.member {
		width: 100%;
		height: 156rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 44rpx 32rpx 0;
		box-sizing: border-box;
		position: relative;
		
		.promote-rules {
			position: absolute;
			display: flex;
			align-items: center;
			color: #fff;
			right: 40rpx;
			font-size: 24rpx;
			.iconfont{
				margin-right: 10rpx;
			}
		}
		
		.member-pic {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			border: 4rpx solid #fff;
			position: relative;

			image {
				width: 100%;
				height: 100%;
				border-radius: 50%;
			}
			.level-name {
				height: 32rpx;
				background: linear-gradient(180deg, #FCFCFD 0%, #C8D0DD 100%);
				border-radius: 32rpx;
				line-height: 32rpx;
				padding: 0 10rpx;
				font-size: 18rpx;
				font-weight: 500;
				color: #666666;
				position: absolute;
				bottom: -4rpx;
				left: 50%;
				z-index: 5;
				white-space: nowrap;
				transform: translateX(-50%);
			}
		}

		.member-info {
			flex: 1;
			width: 0;
			margin-left: 32rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			
			view {
				color: #fff;
			}
			
			.member-info-box {
				display: flex;
				align-items: center;
				line-height: 1;
			}
			.name {
				font-size: 32rpx;
				font-weight: 600;
				color: #FFFFFF;
			}

			.recommend {
				font-size: $font-size-tag;
				margin-top: 20rpx;
				line-height: 1;
				margin-bottom: 10rpx;
				display: flex;
				align-items: center;
			}
			
			.copy {
				width: 68rpx;
				height: 30rpx;
				line-height: 30rpx;
				background: linear-gradient(172deg, rgba(255,255,255,0.6) 0%, rgba(255,255,255,0.8) 100%);
				border-radius: 60rpx;
				text-align: center;
				display: inline-block;
				font-size: 18rpx;
				font-weight: 600;
				color: #202230;
				margin-left: 20rpx
			}
		}

		.member-tixian {
			width: 120rpx;
			height: 50rpx;
			border: 2rpx solid #ffffff;
			border-radius: $border-radius;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: $font-size-tag;
			color: #ffffff;
		}
		.code {
			width: 50rpx;
			height: 50rpx;
		}
	}
	
	.fenxiao-level-wrap {
		width: calc(100% - 48rpx);
		height: 128rpx;
		background: linear-gradient(90deg, #FDE5C2 0%, #FDC172 100%);
		border-radius: 20rpx 20rpx 0px 0px;
		padding: 36rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		
		.level-icon {
			width: 70rpx;
			height: 70rpx;
		}
		
		.level-wrap {
			flex: 1;
			width: 0;
			padding: 0 30rpx;
			margin-left: 30rpx;
			position: relative;
			
			view {
				color: #945100;
				line-height: 1;
			}
			
			.title {
				font-weight: 600;
				font-size: 30rpx;
			}
			
			.desc {
				font-size: 24rpx;
				margin-top: 10rpx;
			}
			
			&::before {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				width: 2rpx;
				height: 100%;
				background: linear-gradient(90deg, #FDE5C2 0%, #FDC172 100%);
			}
		}
		
		.btn {
			width: 118rpx;
			height: 54rpx;
			line-height: 54rpx;
			background: #945100;
			border-radius: 54rpx;
			font-size: 24rpx;
			color: #fff;
			font-weight: 500;
			text-align: center;
		}
	}
}

.fenxiao_index_money {
	color: #fff;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;

	.xian {
		height: 40rpx;
		border: 2rpx solid rgba(255, 255, 255, 0.5);
	}

	.index-money-item {
		padding: 40rpx 0;
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		.item_money {
			color: #fff;
			font-size: 36rpx;
			line-height: 1;
		}
		.item_tit {
			padding-top: 10rpx;
			font-size: $font-size-tag;
			color: #fff;
		}
	}
}

.fenxiao-index-allmoney {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	margin: 20rpx 24rpx;
	border-radius: 16rpx;
	background-color: #ffffff;
	width: calc(100% - 48rpx);
	padding: 32rpx;
	box-sizing: border-box;

	.allmoney-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		view {
			line-height: 1;
			color: #888888;
			font-size: 24rpx;
		}
		
		.iconright {
			line-height: 1;
			font-size: 28rpx;
			margin-left: 4rpx;
		}
	}
	
	.total-commission {
		color: #E80200;
		margin-top: 20rpx;
		font-size: 38rpx;
		font-weight: bold;
		line-height: 1;
	}
	
	.allmoney-bottom {
		display: flex;
		margin-top: 50rpx;
	
		.allmoney-all-wrap {
			flex: 1;
			
			.title {
				font-size: 24rpx;
				color: #888888;
			}
			
			.money {
				font-size: 38rpx;
			}
		}
	}
	
	.withdraw-btn {
		height: 92rpx;
		line-height: 92rpx;
		border-radius: 92rpx;
		background: linear-gradient(90deg, #FFE2AC 0%, #FDC174 100%);
		text-align: center;
		font-weight: 800;
		color: #985400;
		font-size: 30rpx;
		margin: 50rpx 30rpx 0 30rpx;
	}
}

.uni-popup__wrapper{
	width: 450rpx;
	height: 400rpx;
	background: rgba(0, 0, 0, 0.4);
}

.fenxiao-team {
	display: flex;
	width: 100%;
	
	.fenxiao-index-other {
		margin: 0 24rpx 20rpx 24rpx;
		border-radius: 16rpx;
		background-color: #ffffff;
		padding: 30rpx 0;
		flex: 1;
		
		&:last-child {
			margin-left: 0;
		}
		
		.all-money-item {
			margin: 0 30rpx;
			display: flex;
			font-size: $font-size-tag;
			align-items: center;
			.img-wrap {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 70rpx;
				height: 70rpx;
				image {
					width: 100%;
					height: 100%;
				}
			}
			.all-money-tit-wrap {
				flex: 1;
				margin-left: 24rpx;
				display: flex;
				flex-direction: column;
				height: 70rpx;
				
				.all-money-tit {
					line-height: 1;
					color: $color-title;
					font-size: $font-size-base;
					flex: 1;
				}
				.all-money-num {
					color: $color-tip;
					font-size: 24rpx;
					line-height: 1;
				}
			}
		}
	}
}

.fenxiao-menu-list {
	margin: 0 24rpx 20rpx 24rpx;
	border-radius: 16rpx;
	background-color: #ffffff;
	width: calc(100% - 48rpx);
	padding: 10rpx 30rpx;
	box-sizing: border-box;
	display: flex;
	flex-wrap: wrap;
	
	.menu-item {
		width: 50%;
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		
		.icon-wrap {
			width: 60rpx;
			display: flex;
			align-content: center;
			justify-content: center;
			margin-right: 20rpx;
			
			image {
				width: 100%;
			}
		}
		
		.info {
			flex: 1;
			
			.title {
				font-size: 28rpx;
				line-height: 1;
			}
			
			.desc {
				font-size: 24rpx;
				margin-top: 12rpx;
				color: #aaa;
				line-height: 1;
			}
		}
	}
}

.empty {
	width: 100%;
	height: 400rpx;
	margin-top: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	image {
		width: 300rpx;
		margin-bottom: 50rpx;
	}
	text {
		font-size: $font-size-tag;
		font-weight: 600;
	}
	view {
		width: 300rpx;
		height: 70rpx;
		border-radius: $border-radius;
		text-align: center;
		line-height: 70rpx;
		color: #ffffff;
		margin-top: 30rpx;
	}
}

.popup {
	width: 100vw;
	background: #fff;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;

	.popup-header {
		display: flex;
		border-bottom: 2rpx solid $color-line;
		position: relative;
		padding: 40rpx;

		.tit {
			flex: 1;
			font-size: $font-size-toolbar;
			line-height: 1;
			text-align: center;
		}
		.iconfont {
			line-height: 1;
			position: absolute;
			right: 30rpx;
			top: 50%;
			transform: translate(0, -50%);
			color: $color-tip;
			font-size: $font-size-toolbar;
		}
	}

	.popup-body {
		height: calc(100% - 250rpx);
		padding: 30rpx;
		&.store-popup {
			height: calc(100% - 120rpx);
		}
		&.safe-area {
			height: calc(100% - 270rpx);
		}
		&.store-popup.safe-area {
			height: calc(100% - 140rpx);
		}
	}
}