.content {
	text-align: center;
	height: 100%;
	.head {
		position: relative;
		font-weight: bold;
		padding: 32rpx 0 24rpx;
		font-size: $font-size-toolbar;
		.iconfont {
			position: absolute;
			right: 20rpx;
		}
	}

	.container {
		view,
		text,
		image {
			box-sizing: border-box;
		}
		.date-list-wrap {
			display: flex;
			align-items: center;
			border-bottom: 2rpx solid #e6e6e6;
			scroll-view {
				width: 80%;
				white-space: nowrap;
				height: 100rpx;
				line-height: 100rpx;
				background-color: #fff;
				position: relative;
				.flex-box {
					display: inline-block;
					width: 25%;

					&.active {
						.date-box {
							border: none;
							.days {
								font-weight: bold;
								color: #818181;
							}
							.date {
								font-weight: bold;
								color: #818181;
							}
						}
					}
					.date-box {
						color: #909399;
						text {
							font-size: $font-size-tag;
						}
					}
				}
			}
			.appointed-day {
				flex: 1;
				border-left: 2rpx solid #e6e6e6;
				.day-box,
				.iconfont {
					font-size: $font-size-tag;
					color: #909399;
				}
				.iconfont {
					margin-left: 4rpx;
				}
			}
		}
		.time-box {
			padding: 0 12rpx;
			display: flex;
			flex-wrap: wrap;
			overflow: scroll;
			background-color: #fff;
			height: auto;
			.item {
				width: 25%;
				padding: 0 8rpx;
				margin-top: 30rpx;
				&-box {
					width: 100%;
					height: 140rpx;
					padding: 0 40rpx;
					background: #fff;
					color: #333;
					border: 2rpx solid #eeeeee;
					font-size: $font-size-base;
					border-radius: 10rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					&.disable {
						background: #f1f3f6 !important;
						color: #999 !important;
					}
					&.active {
						background: $base-color;
						color:#fff;
						// background: #0094D7;
						border: 2rpx solid $base-color;
						font-weight: bold;
					}
					.all {
						font-size: $font-size-tag;
						padding-top: 10rpx;
					}
					//  自定义样式
					&.diy{
						height: 60rpx;
						border-radius: 40rpx;
						.all{
							display: none;
						}
					}
				}
			}
		}
	}

	.bottom {
		display: flex;
		flex-direction: row;
		position: fixed;
		align-items: center;
		bottom: 0;
		top: auto;
		left: 0;
		width: 100%;
		background-color: #fff;
		box-shadow: 0 -2rpx 20rpx #bcbcbc;

		.show-time {
			width: 66%;
			height: 100rpx;
			line-height: 100rpx;
			font-size: $font-size-base;
			text-align: left;
			margin-left: 40rpx;
		}

		.submit-btn {
			width: 25%;
			height: 70rpx;
			line-height: 70rpx;
			font-size: $font-size-base;
		}
	}
}

.yuyue-date-desc{
	padding-top: 2rpx;
	padding-bottom: 4rpx;
	font-size: $font-size-tag;
	color: $color-sub;
	text-align: center;
}
