.ns-adv {
	margin: $margin-updown $margin-both;
	border-radius: $border-radius;
	overflow: hidden;
	line-height: 1;
	
	image {
		width: 100%;
	}
}

.pinfan-step{
	font-size: 30rpx;
	padding: 15px;
	background: #fff;
	margin: 10px 15px;
	.pinfan-title{
		display: flex;
		justify-content: center;
		align-items: center;
		image{
			width: 106rpx;
			height: 8rpx !important;
		}
		view{
			margin: 0 14rpx;
		}
	}
	.step{
		display: flex;
		align-items: center;
		justify-content: space-around;
		margin-top: 40rpx;
		view{
			width:100rpx;
			text-align: center;
			image{
				width: 48rpx;
				height: 48rpx;
			}
			view{
				font-size: $font-size-tag;
			}
		}
		image{
			width: 40rpx;
			height: 10rpx;
		}
	}
}

.lineheight-clear {
	line-height: 1!important;
}
// 商品列表单列样式
.goods-list.single-column {
	
	.goods-item {
		padding: 26rpx;
		background: #fff;
		margin: $margin-updown $margin-both;
		border-radius: $border-radius;
		position: relative;
		.step-status{
			background-color: #FFF5ED;
			padding: 18rpx 26rpx;
			border-radius: $border-radius;
			line-height: 1;
			margin-top: 30rpx;
			
		}
		.list-item{
			display: flex;
		}
		.goods-img {
			width: 200rpx;
			height: 200rpx;
			overflow: hidden;
			border-radius: $border-radius;
			margin-right: 20rpx;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.goods-tag{
			color: #fff;
			line-height: 1;
			padding: 8rpx 12rpx;
			position: absolute;
			border-top-left-radius: $border-radius;
			border-bottom-right-radius: $border-radius;
			top: 26rpx;
			left: 26rpx;
			font-size: $font-size-goods-tag;
		}
		
		.info-wrap {
			flex: 1;
			display: flex;
			flex-direction: column;
		}
		
		.name-wrap {
			flex: 1;
			/* .box {
				margin: 20rpx 0;
				width: 240rpx;
				height: 20rpx;
				border-radius: 10rpx;
				background-color: #FCECD7;
				.con {
					height: 20rpx;
					border-radius: 10rpx;
					background-color: #FDBE6C;
				}
			} */
		}
		
		.goods-name {
			line-height: 1.3;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			height: 68rpx;
			text{
				border-radius: 2px;
				margin-right: 10rpx;
				padding: 4rpx 12rpx;
			}
		}
		
		/* .introduction {
			margin-top: 10rpx;
			display: flex;
			.pin-intro{
				// background-color: #FFF0F0;
				border-radius: 4rpx;
				line-height: 1;
				margin-right: 10rpx;
				padding: 4rpx 12rpx;
			}
			.tuan-intro{
				border: 1px solid;
				border-radius: 4rpx;
				line-height: 1;
				padding: 4rpx 12rpx;
			}
		} */
		.pintuan-info {
			.pinfan-num{
				padding:2rpx 12rpx;
				border-radius: 4rpx;
				font-size:: $font-size-activity-tag;
			}
			.pinfan-box{
				border-radius: 4rpx;
				font-size:: $font-size-activity-tag;
				padding:0 12rpx;
				border: 2rpx solid;
				margin-left: 14rpx;
			}
		}
		.discount-price {
			display: inline-block;
			font-weight: bold;
			line-height: 1;
			margin-top: 16rpx;
			color: var(--price-color);
			.unit {
				margin-right: 6rpx;
			}
			
			.txt {
				font-weight: normal;
			}
		}
		.pro-info {
			position: relative;
			margin-top: 16rpx;
			
			.delete-price {
				line-height: 1;
				flex: 1;
				display: flex;
				align-items: flex-end;
				
				.unit {
					margin-right: 6rpx;
				}
				
				.txt {
					text-decoration:none;
				}
			}
			.detail-btn {
				line-height: 1;
				position: absolute;
				right: 0;
				bottom: 0;
			}
		}
		.member-price-tag {
			display: inline-block;
			width: 60rpx;
			line-height: 1;
			margin-left: 6rpx;
			
			image {
				width: 100%;
			}
		}
	}
	
}