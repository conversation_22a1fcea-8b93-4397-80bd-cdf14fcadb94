.page {
	width: 100%;
	min-height: 100vh;
	background: var(--bargain-promotion-color);
}
.adv-wrap {
	margin: $margin-updown $margin-both;
	width: auto;
}

.lineheight-clear {
	line-height: 1 !important;
}
// 商品列表单列样式
.goods-list.single-column {
	.goods-item {
		padding: 26rpx 26rpx 20rpx;
		background: #fff;
		margin: $margin-updown $margin-both;
		border-radius: 24rpx;
		display: flex;
		position: relative;
		flex-direction: column;
		.goods-item-content {
			display: flex;
		}

		.goods-item-bottom {
			display: flex;
			justify-content: space-between;
			margin-top: 10rpx;
			line-height: 1;
			.item-bottom-left {
				display: flex;
				align-items: baseline;
				margin-top: 10rpx;
				.delete-pirce {
					text-decoration: line-through;
					color: $color-sub;
					margin-left: 20rpx;
				}
				.txt {
					margin-left: 10rpx;
					font-weight: 26rpx;
				}
				.unit,
				.price {
					color: var(--bargain-promotion-color) !important;
				}
			}
			.item-bottom-right {
				display: flex;
				align-items: center;
				font-weight: bold;
				button {
					color: #fff;
					background-color: var(--bargain-promotion-color);
				}
			}
		}

		.goods-img {
			width: 200rpx;
			height: 200rpx;
			overflow: hidden;
			border-radius: $border-radius;
			margin-right: 20rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.goods-tag {
			color: #fff;
			line-height: 1;
			padding: 8rpx 12rpx;
			position: absolute;
			border-top-left-radius: $border-radius;
			border-bottom-right-radius: $border-radius;
			top: 26rpx;
			left: 26rpx;
			font-size: $font-size-goods-tag;
		}

		.info-wrap {
			flex: 1;
			display: flex;
			flex-direction: column;
			width: calc(100% - 220rpx);
			.info-sub-title {
				color: $color-tip;
				font-size: $font-size-tag;
				margin-top: 6rpx;
			}
		}
		.info-bottom {
			display: flex;
			justify-content: space-between;
			margin-top: 10rpx;
			align-items: center;
			.sale-box {
				color: var(--bargain-promotion-color);
			}
			.price-box {
				display: flex;
				line-height: 1;
				.discount-price {
					display: flex;
					font-size: 26rpx;
					line-height: 1;
					margin-top: 4rpx;
				}
				.delete-price {
					display: flex;
					font-size: 26rpx;
					line-height: 1;
					margin-top: 4rpx;
				}
				.price {
					line-height: 1.2;
					color: var(--bargain-promotion-color);
				}
			}
			.pro-info {
				line-height: 1;
				display: flex;
				align-items: center;
				.button-border {
					border: 2rpx solid var(--bargain-promotion-color);
					color: var(--bargain-promotion-color);
					font-size: 24rpx;
					padding: 4rpx 6rpx;
					line-height: 1;
					border-radius: 4rpx;
					background-color: var(--bargain-promotion-color-shallow);
					position: relative;
					border-top-right-radius: 0;
				}
				.button-border::before {
					content: '';
					display: block;
					position: absolute;
					top: -10rpx;
					right: -2rpx;
					border-left: 10rpx solid transparent;
					border-right: 0 solid transparent;
					border-bottom: 10rpx solid var(--bargain-promotion-color);
				}
				.button-border::after {
					content: '';
					display: block;
					position: absolute;
					top: -6rpx;
					right: 0rpx;
					border-left: 10rpx solid transparent;
					border-right: 0 solid transparent;

					border-bottom: 10rpx solid var(--bargain-promotion-color-shallow);
				}
			}
		}

		.name-wrap {
			flex: 1;
		}

		.goods-name {
			font-size: $font-size-base;
			line-height: 1.4;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			font-weight: bold;
			word-wrap: break-word;
			height: 80rpx;
		}

		.progress-wrap {
			display: flex;
			position: relative;
			width: calc(100% - 32rpx);
			margin: 10rpx auto;
			progress {
				flex: 1;
			}
			.progress-point {
				background: var(--bargain-promotion-color);
				width: 24rpx;
				height: 24rpx;
				border-radius: 50%;
				position: absolute;
				right: -12rpx;
				top: -4rpx;
				z-index: 1;
			}
			.progress-select {
				background-color: var(--bargain-promotion-color);
				color: var(--bargain-promotion-aux-color);
				width: 24rpx;
				height: 24rpx;
				border-radius: 50%;
				position: absolute;
				left: 0;
				top: -4rpx;
				font-size: 24rpx;
				z-index: 11;

				&.icon {
					width: 34rpx;
					height: 34rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					top: -10rpx;
				}
				.iconfont {
					background-color: unset;
					color: #fff;
					font-size: 20rpx;
				}
			}

			.txt {
				margin: 0 100rpx 0 20rpx;
			}
		}

		.member-price-tag {
			display: inline-block;
			width: 60rpx;
			line-height: 1;
			margin-left: 6rpx;

			image {
				width: 100%;
			}
		}
	}
}
