.conteiner  {
	width: 100vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
	
	.point-scroll-view {
		flex: 1;
		height: 0;
	}
}

.point-navbar {
	width: 100vw;
	padding-bottom: 20rpx;
	display: flex;
	align-items: center;
	position: fixed;
	left: 0;
	top: 0;
	z-index: 100;
	background-image: linear-gradient(360deg, #F8F8F8 0%, #E74A32 100%);
	background-size: 100% 400rpx;
	background-position-y: top;
	background-repeat: no-repeat;
	
	.nav-wrap {
		height: 100%;
		display: flex;
		padding: 0 24rpx;
		box-sizing: border-box;
	}
	
	.back {
		background: rgba(255, 255, 255, .4);
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		
		.iconfont {
			color: #222222;
			font-size: 36rpx;
			font-weight: bold;
		}
	}
	
	.search {
		flex: 1;
		background: #fff;
		margin: 0 15rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		box-sizing: border-box;
		padding: 20rpx;
		
		.tips {
			color: #4A4A4A;
			font-size: 24rpx;
			margin-left: 20rpx;
		}
	}
	
	.sign {
		image {
			width: 100%;
			height: 100%;
		}
	}
}

.point-navbar-block {
	padding-bottom: 20rpx;
}

.point-wrap {
	background-image: linear-gradient(360deg, #F8F8F8 0%, #E74A32 100%);
	background-size: 100% 380rpx;
	background-repeat: no-repeat;
	// #ifndef MP-WEIXIN
	padding-top: 20rpx
	// #endif
}

/* 说明弹框 */
.tips-layer {
	background: #fff;
	z-index: 999;
	height: 40%;
	width: 100%;
	
	.head {
		position: relative;
	}
	
	.title {
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		font-size: $font-size-toolbar;
		font-weight: 700;
	}
	
	text {
		position: absolute;
		top: 8rpx;
		right: 22px;
		font-size: $font-size-toolbar;
		font-weight: 500;
	}
	
	.body {
		width: 100%;
		height: calc(100% - 80rpx);
		overflow-y: scroll;
		
		.detail {
			padding: 20rpx;
			
			.font-size-base {
				margin-bottom: 10rpx;
			}
		}
		
	}
}

.lineheight-clear {
	line-height: 1!important;
}

// 商品列表双列样式
.goods-list.double-column {
	display: flex;
	flex-wrap: wrap;
	margin-top: 20rpx;
	
	.goods-item {
		flex: 1;
		position: relative;
		background-color: #fff;
		flex-basis: 48%;
		max-width: calc((100% - 24rpx) / 2);
		margin-right: 24rpx;
		margin-bottom: 24rpx;
		border-radius: 18rpx;
			
		&:nth-child(2n) {
			margin-right: 0;
		}
		
		.goods-img {
			position: relative;
			overflow: hidden;
			padding-top: 100%;
			border-top-left-radius: 18rpx;
			border-top-right-radius: 18rpx;
			
			image {
				width: 100%;
				position: absolute;
				top: 50%;
				left: 0;
				transform: translateY(-50%);
			}
		}
		
		.goods-tag{
			color: #fff;
			line-height: 1;
			padding: 8rpx 16rpx;
			position: absolute;
			border-bottom-right-radius: $border-radius;
			top: 0;
			left: 0;
			font-size: $font-size-goods-tag;
		}
		
		.goods-tag-img {
			position: absolute;
			border-top-left-radius: $border-radius;
			width: 80rpx;
			height: 80rpx;
			top: 0;
			left: 0;
			z-index: 5;
			overflow: hidden;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.info-wrap {
			padding: 0 20rpx 24rpx 20rpx;
		}
		
		.goods-name {
			line-height: 1.3;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			margin-top: 20rpx;
			font-size: 26rpx;
			color: #333;
			font-weight: 600;
		}
		
		.discount-price {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			font-weight: bold;
			line-height: 1;
			margin-top: 16rpx;
			color: var(--price-color);
			overflow: hidden;
			flex: 1;
			width: 0;
			margin-right: 20rpx;
			view{
				line-height: 1;
				color: var(--price-color);
			}
			.unit {
				margin-right: 6rpx;
			}
			.point{
				font-size: 32rpx;
			}
		}
		
		.pro-info {
			display: flex;
			margin-top: 16rpx;
			justify-content: flex-start;
			& > view {
				line-height: 1;
				display: flex;
				align-items: center;
				
				button {
					padding: 0 16rpx;
					line-height: 2;
				}
				&:nth-child(2) {
					&:before{
						content: ' ';
						width: 2rpx;
						background-color: #D8D8D8;
						height: 20rpx;
						margin: 0 16rpx;
					}
				}
			}
		}
		
		.member-price-tag {
			display: inline-block;
			width: 60rpx;
			line-height: 1;
			margin-left: 6rpx;
			
			image {
				width: 100%;
			}
		}
		
		.lineheight-clear {
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-wrap: wrap;
			
			.btn {
				width: 96rpx;
				height: 50rpx;
				background: #FF6C24;
				border-radius: 50rpx;
				text-align: center;
				color: #fff;
				font-size: 26rpx;
				margin-top: 20rpx;
			}
		}
	}
}

.head-wrap {
	width: 100vw;
	line-height: 1;
	position: relative;
	height: 270rpx;
	
	& > image {
		width: 100%;
	}
	
	.wrap {
		width: 100%;
		height: 100%;
		position: absolute;
		z-index: 5;
		top: 0;
		left: 0;
	}
	
	.member-wrap {
		height: 190rpx;
		padding: 50rpx 30rpx 30rpx 30rpx;
		display: flex;
		align-items: center;
		box-sizing: border-box;
		
		.headimg {
			width: 100rpx;
			height: 100rpx;
			background: #fff;
			border: 2px solid #fff;
			border-radius: 50%;
			overflow: hidden;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.point {
			margin-left: 30rpx;
			color: var(--btn-text-color);
			font-size: 36rpx;
		}
		
		.point-name {
			font-size: $font-size-tag;
			color: var(--btn-text-color);
			margin-left: 4rpx;
			margin-top: 5rpx;
			
		}
		
		.rule {
			flex: 1;
			text-align: right;
			color: var(--btn-text-color);
		}
		
		.icon-wenhao {
			font-size: 24rpx;
			color: var(--btn-text-color);
			margin-right: 6rpx;
		}
		
	}
	
	.action-wrap {
		// margin: 0 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 80rpx;
		background-color: rgba(255, 255, 255, .1);
		
		view {
			line-height: 1;
			text-align: center;
			width: calc((100vw - 1rpx) / 2);
			color: var(--btn-text-color);
			
			text {
				font-size: $font-size-tag;
				margin-left: 8rpx;
			}
			
			// &:first-child {
			// 	margin-right: 30rpx;
			// }
			
			&.split {
				width: 1rpx;
				height: 50rpx;
				background-color: rgba(238, 238, 238, .3);
				flex-shrink: 0;
			}
			
			image {
				width: 100%;
			}
		}
	}
	
	.no-login {
		display: flex;
		align-items: center;
		justify-content: center;
		text-align: center;
		
		text {
			color: #fff;
		}
		
		.login-btn {
			display: inline-block;
			height: 70rpx;
			line-height: 70rpx;
			width: 200rpx;
			border: 1px solid #fff;
			border-radius: $border-radius;
			margin-bottom: 20rpx;
		}
	}
}

.ns-adv {
	margin: 0;
	border-radius: 0;
	overflow: hidden;
	line-height: 1;
	
	image {
		width: 100%;
		border-radius: 0!important;
	}
}
.body-wrap {
	margin-top: 20rpx;
	
	&.no-login{
		margin-top: 20rpx;
	}
	
	.point-exchange-wrap {
		padding: 0 24rpx;
		margin-top: 30rpx;
	}
	
	.type-wrap {
		display: flex;
		align-items: center;
		
		.type-name {
			font-size: 30rpx;
			color: $color-title;
			line-height: 1;
		}
		
		>view {
			width: 2rpx;
			height: 23rpx;
			background-color: $color-tip;
			margin: 0 20rpx;
		}
		
		.type-sub {
			font-size: $font-size-tag;
			color: $color-tip;
			line-height: 1;
		}
	}
	
	.list-wrap {
		width: 100%;
		
		.list-wrap-scroll {
			width: 100%;
			flex-direction: row;
			// white-space: nowrap;
			line-height: 1;
		}
		
		.list-wrap-item {
			display: inline-block;
			width: 330rpx;
			overflow: hidden;
			margin-right: 30rpx;
			margin-top: 20rpx;
			position: relative;
			
			&.coupon-list-wrap-item {
				// height: 170rpx;
			}
			
			&.hongbao-list-wrap-item {
				height: 141rpx;
			}
			
			&:nth-child(2n+2){
				margin-right: 0;
			}
			
			.img-box {
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				
				image {
					width: 100%;
					height: 100%;
				}
			}
			
			.content {
				position: relative;
				z-index: 9;
				
				//优惠券样式
				.coupon{
					// background-color: #FFEAEA;
					background-size: 100% 100%;
					background-repeat: no-repeat;
					border-radius: $border-radius;
					display: flex;
					padding:$margin-updown 0;
					.coupon_right{
						position:relative;
						width:156rpx;
						max-width: 156rpx;
						
						.coupon_btn{
							margin: 10rpx auto 0;
							width:80rpx;
							height:40rpx;
							line-height: 40rpx;
							font-size: $font-size-tag;
							text-align: center;
							border-radius: 10rpx;
							border-width: 1px;
							border-style: solid;
							background: #FF3D3D;
							color: #fff;
						}
						.coupon_num {
							margin-top:10rpx;
							text-align: center;
							color: #FF3D3D;
						}
						
						&::after{
							position :absolute;
							top:50%;
							margin-left: 0;
							content:"";
							width:0;
							height:96%;
							border-left: 2px dashed #FD463E;
							transform: translateY(-50%);
						}
					}
					.coupon_left{
						flex:1;
						width: 0;
						text-align: left;
						padding:0 $padding;
						display: flex;
						align-items: center;
						.price{
							margin-top: 0 !important;
							padding:0;
							font-weight: 600;
							flex: 1;
							width: 0;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
							color: #FD463E;
							font-size: 56rpx;
							text{
								font-size: $font-size-tag;
								color: #FD463E;
							}
						}
						.coupon-info {
							flex:1;
							width: 0;
							display: flex;
							flex-direction: column;
							align-items: flex-start;
							justify-content: space-between;
							height: 80%;
						}
						.coupon_condition{
							line-height: 1;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							font-size: 26rpx;
							font-weight: bold;
							color: #FD463E;
						}
						.coupon_type {
							color: #FD463E;
							font-size: 24rpx;
						}
					}
				}
				
				.hongbao {
					.coupon_left {
						.price, .coupon_condition {
							color: #FFFFFF;
						}
					}
					
					.coupon_right {
						.coupon_num {
							color: #FFFFFF;
						}
						
						.coupon_btn {
							color: #fff;
							border-color: #fff;
						}
						&::after{
							position :absolute;
							top:0;
							margin-left: 0;
							content:"";
							width:0;
							height:100%;
							border-left:0 ;
							opacity: 0.2;
						}
					}
				}
				
				.coupon-price-wrap {
					width: 100%;
					height: 105rpx;
					display: flex;
					justify-content: space-between;
					
					.coupon-price {
						font-size: 48rpx;
						margin-top: 10rpx;
						margin-left: 20rpx;
						
						text {
							font-size: $font-size-tag;
						}
					}
				}
				
				.coupon-point {
					.coupon-point-num {
						width: 160rpx;
						height: 44rpx;
						position: relative;
						
						image {
							width: 100%;
							height: 100%;
							position: absolute;
						}
						
						text {
							position: relative;
							z-index: 9;
							color: #FFFFFF;
							font-size: 24rpx;
							display: inline-block;
							width: 100%;
							line-height: 44rpx;
							text-align: center;
							vertical-align: top;
						}
					}
					
					.coupon-conditions {
						font-size: $font-size-activity-tag;
						color: $color-tip;
						line-height: 1;
						margin-top: $font-size-tag;
					}
				}
				
				.coupon-name {
					font-size: $font-size-tag;
					color: $color-title;
					margin-top: 23rpx;
					line-height: 1;
					padding: 0 22rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
				
				// 红包
				&.hongbao-content {
					background-color: #FFFFFF;
					border-radius: 20rpx;
					padding-bottom: 30rpx;
				}
				
				.hongbao-img {
					height: 330rpx;
					
					image {
						width: 100%;
						height: 100%;
					}
				}
				
				.price {
					font-size: $font-size-base;
					color: $color-title;
					line-height: 1;
					padding-left: 26rpx;
					margin-top: 20rpx;
				}
				
				.point {
					font-size: $font-size-toolbar;
					padding-left: 26rpx;
					margin-top: 17rpx;
					line-height: 1;
					text {
						font-size: $font-size-tag;
					}
				}
				
				.stock {
					font-size: $font-size-activity-tag;
					color: $color-tip;
					line-height: 1;
					padding-left: 26rpx;
					margin-top: 20rpx;
				}
			}
		}
	}
}

.exchange-coupon {
	.list-wrap {
		width: 100%;
		overflow-x: scroll;
		
		.list-wrap-scroll {
			flex-direction: column;
			display: flex;
			max-height: 388rpx;
			flex-wrap: wrap;
			
			&.single-row {
				max-height: 192rpx;
			}
		}
		
		.list-wrap-item {
			width: 424rpx;
			margin-right: 16rpx;
			margin-top: 18rpx;
		}
	}
}

.type-wrap-box {
	display: flex; 
	justify-content: space-between;
	
	.more {
		color: #ff5251;
		cursor: pointer;
	}
}
.card-category-title{
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	color: #222;
	padding: 0;
	font-weight: bold;
	
	.before-line,.after-line{
		width: 30rpx;
		height: 4rpx;
		margin: 0 10rpx;
		background-color: #222;
	}
	
}
.head-box{
	display: flex;
	flex-direction: column;
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 24rpx;
	box-sizing: border-box;
	width: calc(100% - 48rpx);
	margin: 0 24rpx 20rpx 24rpx;
	position: relative;
	
	.account-content{
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.left{
			display: flex;
			align-items: center;
			image{
				width: 88rpx;
				height: 88rpx;
			}
			view{
				font-size: 32rpx;
				font-weight: bold;
				margin-left: 25rpx;
			}
		}
		.right{
			display: flex;
			align-items: baseline;
			.point{
				font-weight: bold!important;
				font-size: 38rpx;
				color: #FF002D ;
			}
			.text{
				font-size: 28rpx;
				font-weight: bold;
				margin: 0 10rpx;
			}
		}
	}
	.remark{
		display: flex;
		align-items: center;
		margin-top: 30rpx;
		padding: 25rpx 0 0;
		border-top: 2rpx solid #F2F2F2;
		
		.label{
			border-radius: 4rpx;
			border: 2rpx solid #FE172E;
			color: #FE172E;
			padding: 2rpx 8rpx;
			font-size: 22rpx;
			line-height: 1;
			font-weight: bold;
		}
		.text{
			font-size: 24rpx;
			font-weight: bold;
			margin-left: 30rpx;
			line-height: 1;
		}
	}
}

.menu-wrap {
	background: #fff;
	border-radius: 24rpx;
	margin: 20rpx 24rpx;
	padding: 40rpx 0 30rpx 0; 
	
	.menu-list {
		display: flex;
		
		.menu-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			position: relative;
			
			.menu-tag {
				position: absolute;
				z-index: 2;
				right: 0;
				width: 58rpx;
				height: 26rpx;
			}
			
			.menu-img {
				width: 86rpx;
				height: 86rpx;
			}
			
			.title {
				margin-top: 16rpx;
				font-size: 26rpx;
				color: #222;
			}
		}
	}
}

.poster-wrap {
	margin: 20rpx 24rpx;
	display: flex;
	
	.poster-item {
		flex: 1;
		border-radius: 16rpx;
		overflow: hidden;
		line-height: 1;
		
		image {
			width: 100%;
			height: auto;
			will-change: transform;
			line-height: 1;
		}
		
		&:last-child {
			margin-left: 20rpx;
		}
	}
}

.recharge-list-wrap {
	margin: 20rpx 24rpx;
	display: flex;
	
	.item-wrap {
		overflow: hidden;
		background: linear-gradient(321deg, #F4402B 0%, #FD7C40 100%);
		border-radius: 16rpx;
		position: relative;
		padding-top: 40rpx;
		width: calc((100% - 60rpx) / 4);
		margin-right: 20rpx;
		text-align: center;
		
		&:nth-child(4) {
			margin-right: 0;
		}
		
		&:before {
			content: " ";
			display: block;
			position: absolute;
			left: 50%;
			top: 0;
			transform: translate(-50%, -50%);
			background: #f8f8f8;
			width: 30rpx;
			height: 30rpx;
			border-radius: 50%;
		}
		
		.recharge {
			font-size: 26rpx;
			color: #FFF20C;
			white-space: nowrap;
			padding: 0 10rpx;
			overflow: hidden;
		}
		.point {
			font-size: 20rpx;
			color: #fff;
			white-space: nowrap;
			padding: 0 10rpx;
			overflow: hidden;
		}
		.btn {
			margin-top: 30rpx;
			line-height: 60rpx;
			font-size: 24rpx;
			font-weight: 600;
			color: #FFFFFF;
			border-top: 2rpx dashed #fff;
		}
	}
}

.exchange-hongbao {
	.coupon_left {
		.price text {
			color: #fff!important;
		}
	}
	.coupon_right {
		width: 120rpx!important;
		max-width: 120rpx;
	}
}