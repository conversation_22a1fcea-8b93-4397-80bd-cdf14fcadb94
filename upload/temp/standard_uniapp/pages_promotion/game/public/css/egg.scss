.container {
	width: 100vw;
	height: 100vh;
	background-image: linear-gradient(#f3623f, #f74222);
}

.head-wrap {
	position: relative;

	image {
		width: 100%;
		margin: 0 auto;
		display: block;
	}

	.rule-mark {
		position: absolute;
		right: 0;
		top: 60rpx;
		background-color: #ffe854;
		color: #ff7908;
		height: 60rpx;
		line-height: 60rpx;
		padding: 0 30rpx;
		border-top-left-radius: 60rpx;
		border-bottom-left-radius: 60rpx;
	}
}

.status-wrap {
	transform: translateY(-340rpx);
	text-align: center;

	.mark {
		display: inline-block;
		line-height: 1;
		padding: 16rpx 60rpx;
		background-color: #d17e1f;
		color: #fff;
		border-radius: 60rpx;
	}

	.num {
		color: #ffe821;
		font-size: 32rpx;
		margin: 0 4rpx;
		display: inline-block;
	}
}

.eggs-wrap {
	display: flex;
	width: 90%;
	margin: 0 auto;
	transform: translateY(-300rpx);

	.box {
		flex: 1;
		text-align: center;

		image {
			width: 90%;
		}
	}
}

.action-text {
	display: flex;
	transform: translateY(-300rpx);
	margin: 60rpx 50rpx 0 50rpx;

	& > view {
		flex: 1;
		line-height: 1;
	}

	.point {
		color: #fee331;
	}

	.record {
		color: #fff;
		text-align: right;
	}
}

.record-wrap {
	transform: translateY(-300rpx);
	margin: 80rpx 50rpx 30rpx 50rpx;
	border-radius: 10rpx;
	background-color: #ff6e43;
	padding: 12rpx;
	box-shadow: 0 0.45em 0 rgba(217, 42, 0, 1);
	position: relative;
	height: 430rpx;

	.body-shade {
		width: calc(100% - 24rpx);
		height: 60rpx;
		top: 12rpx;
		left: 12rpx;
		background-color: #da2b00;
		position: absolute;
		z-index: 2;
		border-radius: 8rpx;
	}

	.head {
		border: 6rpx solid #ff6e43;
		box-shadow: inset 0 0 10rpx 0 #d92a00, 0 0.45em 0 rgba(217, 42, 0, 1);
		background-color: #da2b00;
		position: absolute;
		z-index: 5;
		text-align: center;
		width: 300rpx;
		height: 70rpx;
		line-height: 70rpx;
		border-radius: 70rpx;
		top: 0;
		left: 50%;
		transform: translate(-50%, -50%);
		color: #fff;
		font-weight: 600;
		letter-spacing: 4rpx;
	}

	.body {
		background-color: #da2b00;
		border-radius: 8rpx;
		height: 340rpx;
		box-shadow: inset 0 0 10rpx 0 #d92a00;
		padding: 60rpx 30rpx 30rpx 30rpx;
		overflow: hidden;

		.wrap {
			& > view {
				display: flex;
			}

			&.animate {
				transition: all 1s ease-in-out;
				transform: translateY(-60rpx);
			}
		}

		.tit {
			line-height: 60rpx;
			width: 220rpx;
			color: #fff;
		}

		.txt {
			line-height: 60rpx;
			flex: 1;
			color: #fee331;
		}
	}
}

.transition-popup {
	.wrap {
		position: relative;
		padding: 0 100rpx;

		.eggs {
			width: 380rpx;
		}

		.hammer {
			position: absolute;
			width: 80rpx;
			top: -60rpx;
			right: 60rpx;
			transform-origin: bottom right;
			transform: rotate(-45deg);
			animation: chuizi 1.1s ease 3;
		}
	}
}

.result-wrap {
	position: relative;

	.bg-img {
		width: 80vw;
	}

	.content-wrap {
		position: absolute;
		z-index: 5;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		text-align: center;

		.look {
			margin: 70rpx auto 0 auto;
			display: block;
			width: 120rpx;
		}

		.msg {
			margin: 10rpx auto 0 auto;
			padding: 0 20rpx;
			color: #da2b00;
			width: 70%;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.btn {
			width: 50%;
			height: 70rpx;
			line-height: 70rpx;
			border-radius: 70rpx;
			text-align: center;
			color: #da2b00;
			background: linear-gradient(#ffe1ac, #fec965);
			margin: 146rpx auto 0 auto;
		}

		.icon-round-close {
			position: absolute;
			color: #fff;
			font-size: 70rpx;
			top: -120rpx;
			right: 0rpx;
		}
	}

	.yes {
		.look {
			width: 80rpx;
			height: 80rpx;
			margin-top: 60rpx;
		}

		.btn {
			margin-top: 160rpx;
		}
	}
}

.rule-wrap {
	border-radius: 10rpx;
	background-color: #ffd697;
	width: 80vw;
	padding: 12rpx;
	box-sizing: border-box;

	.content-wrap {
		background-color: #fff2dd;
		width: 100%;
		border-radius: 8rpx;
		position: relative;

		.rule-head {
			width: 100%;
			position: absolute;
			transform: translateY(-50%);
			left: 0;
			top: 0;
		}

		.rule {
			max-height: 880rpx;
			overflow: hidden;
			padding: 80rpx 30rpx 0 30rpx;
			box-sizing: border-box;

			.tit {
				font-weight: 600;
				color: #da2b00;
				margin-top: 10rpx;
			}
			.text {
				font-size: $font-size-sub;
				color: #da2b00;
			}
		}

		.icon-round-close {
			color: #fff;
			text-align: center;
			position: absolute;
			bottom: -150rpx;
			left: 50%;
			transform: translateX(-50%);
			font-size: 70rpx;
		}
	}
}

@keyframes chuizi {
	0% {
		-moz-transform: rotate(-45);
		-ms-transform: rotate(-45);
		-webkit-transform: rotate(-45);
		transform: rotate(-45deg);
	}
	30% {
		-moz-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	100% {
		-moz-transform: rotate(-45);
		-ms-transform: rotate(-45);
		-webkit-transform: rotate(-45);
		transform: rotate(-45);
	}
}
