.container {
	width: 100vw;
	height: 100vh;
	background-image: linear-gradient(#f3623f, #f74222);
}

.head-wrap {
	position: relative;

	image {
		width: 90%;
		margin: 0 auto;
		display: block;
	}

	.rule-mark {
		position: absolute;
		right: 0;
		top: 60rpx;
		background-color: #ffe854;
		color: #ff7908;
		height: 60rpx;
		line-height: 60rpx;
		padding: 0 30rpx;
		border-top-left-radius: 60rpx;
		border-bottom-left-radius: 60rpx;
	}
}

.turntable-wrap {
	margin: 30rpx;
	padding: 70rpx 50rpx 70rpx 70rpx;
	background-size: 100% 100%;
	border-radius: 16rpx;
	transform: translateY(-200rpx);

	.wrap {
		width: 100%;
		position: relative;
		overflow: hidden;
		height: 536rpx;
	}

	.star-box {
		position: absolute;
		width: calc(33.33333333% - 20rpx);
		height: 100rpx;
		background: linear-gradient(#ffe1ac, #fec965);
		top: 182rpx;
		left: 33.33333333%;
		height: 162rpx;
		border-radius: 8rpx;
		box-shadow: 0 0.25em 0 rgba(225, 157, 31, 1);
		text-align: center;

		.text {
			font-size: 34rpx;
			margin: 30rpx 0 0 0;
			line-height: 60rpx;
			font-weight: 600;
			color: #ff3301;
			text-align: center;

			&.no-tips {
				margin-top: 50rpx;
			}
		}

		.tips {
			font-size: 24rpx;
			display: inline-block;
			margin: 0 auto;
			background: #f0a71d;
			line-height: 1;
			color: #ff3301;
			padding: 6rpx 12rpx;
			border-radius: 24rpx;
		}

		&.disabled {
			box-shadow: 0 0.25em 0 #999;
			background: linear-gradient(#ccc, #aaa);

			.text {
				color: #888;
			}

			.tips {
				color: #777;
				background: #aaa;
			}
		}
	}

	.status-box {
		position: absolute;
		width: calc(33.33333333% - 20rpx);
		background: #ed9580;
		top: 182rpx;
		left: 33.33333333%;
		height: 168rpx;
		border-radius: 8rpx;
		text-align: center;
		padding: 0 20rpx;
		box-sizing: border-box;

		view {
			color: #fff;
			font-size: 32rpx;
			letter-spacing: 4rpx;
			margin-top: 30rpx;
		}
	}

	.award-wrap {
		width: 33.33333333%;
		padding-right: 20rpx;
		position: absolute;
		box-sizing: border-box;

		.box {
			width: 100%;
			background: #fff;
			border-radius: 8rpx;
			padding: 20rpx 0;
			box-shadow: inset 0 0 48rpx 0 #ffd6ab, 0 0.25em 0 rgba(248, 166, 131, 0.96);

			&.on {
				background: linear-gradient(#ffe1ac, #fec965);
				box-shadow: 0 0.25em 0 rgba(225, 157, 31, 1);

				.award-text {
					color: #ff3301;
				}
			}

			.award-img {
				width: 70rpx;
				height: 70rpx;
				margin: 0 auto;
				display: flex;
				align-items: center;

				image {
					width: 70rpx;
					height: auto;
				}
			}

			.award-text {
				text-align: center;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				margin-top: 10rpx;
				font-size: 24rpx;
				padding: 0 20rpx;
				color: #666;
			}
		}

		&:nth-child(1) {
			top: 0;
		}
		&:nth-child(2) {
			top: 0;
			left: 33.33333333%;
		}
		&:nth-child(3) {
			top: 0;
			left: 66.66666666%;
		}
		&:nth-child(4) {
			top: 182rpx;
			left: 66.66666666%;
		}
		&:nth-child(5) {
			top: 364rpx;
			left: 66.66666666%;
		}
		&:nth-child(6) {
			top: 364rpx;
			left: 33.33333333%;
		}
		&:nth-child(7) {
			top: 364rpx;
			left: 0;
		}
		&:nth-child(8) {
			top: 182rpx;
			left: 0;
		}
	}
}

.action-text {
	margin: 0 50rpx;
	display: flex;
	transform: translateY(-200rpx);

	& > view {
		flex: 1;
		line-height: 1;
	}

	.point {
		color: #fee331;
	}

	.record {
		color: #fff;
		text-align: right;
	}
}

.record-wrap {
	transform: translateY(-200rpx);
	margin: 80rpx 50rpx 30rpx 50rpx;
	border-radius: 10rpx;
	background-color: #ff6e43;
	padding: 12rpx;
	box-shadow: 0 0.45em 0 rgba(217, 42, 0, 1);
	position: relative;
	height: 430rpx;

	.body-shade {
		width: calc(100% - 24rpx);
		height: 60rpx;
		top: 12rpx;
		left: 12rpx;
		background-color: #da2b00;
		position: absolute;
		z-index: 2;
		border-radius: 8rpx;
	}

	.head {
		border: 6rpx solid #ff6e43;
		box-shadow: inset 0 0 10rpx 0 #d92a00, 0 0.45em 0 rgba(217, 42, 0, 1);
		background-color: #da2b00;
		position: absolute;
		z-index: 5;
		text-align: center;
		width: 300rpx;
		height: 70rpx;
		line-height: 70rpx;
		border-radius: 70rpx;
		top: 0;
		left: 50%;
		transform: translate(-50%, -50%);
		color: #fff;
		font-weight: 600;
		letter-spacing: 4rpx;
	}

	.body {
		background-color: #da2b00;
		border-radius: 8rpx;
		height: 340rpx;
		box-shadow: inset 0 0 10rpx 0 #d92a00;
		padding: 60rpx 30rpx 30rpx 30rpx;
		overflow: hidden;

		.wrap {
			& > view {
				display: flex;
			}

			&.animate {
				transition: all 1s ease-in-out;
				transform: translateY(-60rpx);
			}
		}

		.tit {
			line-height: 60rpx;
			width: 220rpx;
			color: #fff;
		}

		.txt {
			line-height: 60rpx;
			flex: 1;
			color: #fee331;
		}
	}
}

.result-wrap {
	position: relative;

	.bg-img {
		width: 80vw;
	}

	.content-wrap {
		position: absolute;
		z-index: 5;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		text-align: center;

		.look {
			margin: 70rpx auto 0 auto;
			display: block;
			width: 120rpx;
		}

		.msg {
			margin: 10rpx auto 0 auto;
			padding: 0 20rpx;
			color: #da2b00;
			width: 70%;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.btn {
			width: 50%;
			height: 70rpx;
			line-height: 70rpx;
			border-radius: $border-radius;
			text-align: center;
			color: #da2b00;
			background: linear-gradient(#ffe1ac, #fec965);
			margin: 146rpx auto 0 auto;
		}

		.icon-round-close {
			position: absolute;
			color: #fff;
			font-size: 70rpx;
			top: -120rpx;
			right: 0rpx;
		}
	}

	.yes {
		.look {
			width: 80rpx;
			height: 80rpx;
			margin-top: 60rpx;
		}

		.btn {
			margin-top: 214rpx;
		}
	}
}

.rule-wrap {
	border-radius: 10rpx;
	background-color: #ffd697;
	width: 80vw;
	padding: 12rpx;
	box-sizing: border-box;

	.content-wrap {
		background-color: #fff2dd;
		width: 100%;
		border-radius: 8rpx;
		position: relative;

		.rule-head {
			width: 100%;
			position: absolute;
			transform: translateY(-50%);
			left: 0;
			top: 0;
		}

		.rule {
			max-height: 880rpx;
			overflow: hidden;
			padding: 80rpx 30rpx 0 30rpx;
			box-sizing: border-box;

			.tit {
				font-weight: 600;
				color: #da2b00;
				margin-top: 10rpx;
			}
			.text {
				font-size: $font-size-sub;
				color: #da2b00;
			}
		}

		.icon-round-close {
			color: #fff;
			text-align: center;
			position: absolute;
			bottom: -150rpx;
			left: 50%;
			transform: translateX(-50%);
			font-size: 70rpx;
		}
	}
}
