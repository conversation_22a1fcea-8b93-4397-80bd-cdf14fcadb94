.ns-adv {
	margin: 0;
	border-radius: $border-radius;
	overflow: hidden;
	line-height: 1;
	
	image {
		width: 100%;
	}
}
.page{
	width: 100%;
	min-height: 100vh;
	background: var(--pintuan-promotion-color);
}

.lineheight-clear {
	line-height: 1!important;
}
// 商品列表单列样式
.goods-list.single-column {
	
	.goods-item {
		padding: 26rpx;
		background: #fff;
		margin: $margin-updown $margin-both;
		border-radius: 26rpx;
		display: flex;
		position: relative;
		
		.goods-img {
			width: 200rpx;
			height: 200rpx;
			overflow: hidden;
			border-radius: $border-radius;
			margin-right: 20rpx;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.goods-tag{
			color: #fff;
			line-height: 1;
			padding: 8rpx 12rpx;
			position: absolute;
			border-top-left-radius: $border-radius;
			border-bottom-right-radius: $border-radius;
			top: 26rpx;
			left: 26rpx;
			font-size: $font-size-goods-tag;
		}
		
		.info-wrap {
			flex: 1;
			display: flex;
			flex-direction: column;
			width: calc(100% - 220rpx);
		}
		
		.name-wrap {
			flex: 1;
			
			.name-label{
				border: 0;
				border-radius: 8rpx;
				margin-right: 8rpx;
				padding: 0px 8px;
				background: linear-gradient(to left, var(--pintuan-promotion-color), var(--pintuan-promotion-aux-color));
				color: #fff;
				font-size: 24rpx;
			}
		}
		
		.goods-name {
			font-size: $font-size-base;
			line-height: 1.3;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			height: 68rpx;
			width: 100%;
			word-wrap: break-word;
		}
		
		.pintuan-info {
			text {
				line-height: 1;
				margin-right: 20rpx;
				border: 1px solid #fff;
				font-size: 24rpx;
				color: var(--pintuan-color);
			}
		}
		
		.discount-price {
			display: inline-block;
			font-weight: bold;
			line-height: 1;
			margin-top: 16rpx;
			color: var(--price-color);
			.unit {
				margin-right: 6rpx;
			}
			
			.txt {
				font-weight: normal;
			}
		}
		
		.pro-info {
			position: relative;
			margin-top: 16rpx;
			
			.delete-price {
				flex: 1;
				display: flex;
				align-items: flex-end;
				text-decoration: line-through;
				.unit {
					margin-right: 6rpx;
				}
				
				.txt {
					text-decoration:none;
				}
			}
			
			& > view {
				line-height: 1;
				button{
					color: #fff;
					background-color: var(--pintuan-promotion-color);
                    line-height: 60rpx;
                    height: 60rpx;
                    padding: 0 30rpx;
				}
				&:nth-child(2) {
					position: absolute;
					right: 0;
					bottom: 0;
					font-weight: bold;
				}
			}
		}
		
		.member-price-tag {
			display: inline-block;
			width: 60rpx;
			line-height: 1;
			margin-left: 6rpx;
			
			image {
				width: 100%;
			}
		}
	}
	
}