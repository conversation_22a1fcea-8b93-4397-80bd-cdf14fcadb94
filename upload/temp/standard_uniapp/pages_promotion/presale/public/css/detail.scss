.goods-promotion {
	.price-info {
		.img-wrap {
			width: 120rpx;
		}

		.sale-num {
			color: #fff;
			font-size: $font-size-tag;
		}
	}
}

.goods-presale-info {
	.deposit,
	.presale-price {
		vertical-align: bottom;
	}

	.presale-price {
		border: 1px solid var(--goods-price);
		padding: 0 4rpx;
		border-radius: 6rpx;
		margin-left: 10rpx;
		color: var(--goods-price);
	}
}

.presale-rule {
	padding: 20rpx 0;

	.tit {
		font-weight: 600;
	}

	.process {
		display: flex;
		align-items: center;
		justify-content: center;
		// padding-top: 20rpx;
		.process-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			.number {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 40rpx;
				height: 40rpx;
				border-radius: 50%;
				font-size: $font-size-tag;
			}
			.text {
				margin-top: 20rpx;
			}
		}

		.space {
			margin: 0 20rpx;
			font-size: 40rpx;
		}
	}
}

.follow-and-share {
	top: 20rpx;
}
// 营销活动
.goods-promotion {
	background: var(--promotion-color);
	height: 75px;
	.price-info {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		.icon-box {
			margin-right: 20rpx;
			.iconfont {
				font-size: 60rpx;
				color: #ffffff;
			}
		}
		.price-box {
			display: flex;
			align-items: flex-start;
			flex-direction: column;
			height: 100%;
			justify-content: center;
			.promotion-text {
				font-size: 36rpx;
				color: #fff;
				line-height: 1;
			}
			.sale-num {
				display: flex;
				align-items: center;
				margin-top: 18rpx;
				view {
					color: #ffffff;
					line-height: 1;
				}
			}
		}
	}
}
.countdown {
	width: 220rpx;
	background: var(--promotion-aux-color);
	.txt {
		color: #ffffff !important;
		font-size: 28rpx !important;
	}
	.clockrun {
		margin-top: 16rpx !important;
	}
	&:after {
		position: absolute;
		content: '';
		top: calc(50% - 15rpx);
		z-index: 5;
		left: -15rpx;
		width: 0;
		height: 0;
		border-style: solid;
		border-width: 15rpx 15rpx 15rpx 0;
		border-color: transparent var(--promotion-aux-color) transparent transparent;
	}
}

.rule-wrap {
	.content {
		padding: 10rpx 20rpx 20rpx;
	}
}
