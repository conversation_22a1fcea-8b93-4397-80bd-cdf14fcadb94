.ns-adv {
	margin: $margin-updown $margin-both;
	border-radius: $border-radius;
	overflow: hidden;
	line-height: 1;
	
	image {
		width: 100%;
	}
}

.lineheight-clear {
	line-height: 1!important;
}
// 商品列表单列样式
.goods-list.single-column {
	
	.goods-item {
		padding: 26rpx;
		background: #fff;
		margin: $margin-updown $margin-both;
		border-radius: $border-radius;
		display: flex;
		position: relative;
		
		.goods-img {
			width: 200rpx;
			height: 200rpx;
			overflow: hidden;
			border-radius: $border-radius;
			margin-right: 20rpx;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.goods-tag{
			color: #fff;
			line-height: 1;
			padding: 8rpx 12rpx;
			position: absolute;
			border-top-left-radius: $border-radius;
			border-bottom-right-radius: $border-radius;
			top: 26rpx;
			left: 26rpx;
			font-size: $font-size-goods-tag;
		}
		
		.info-wrap {
			flex: 1;
			display: flex;
			flex-direction: column;
		}
		
		.name-wrap {
			flex: 1;
		}
		
		.goods-name {
			font-size: $font-size-base;
			line-height: 1.3;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			height: 68rpx;
		}
		
		.presale-info {
			text {
				line-height: 1;
				font-size: $font-size-tag;
			}
		}
		
		.discount-price {
			display: inline-block;
			font-weight: bold;
			line-height: 1;
			margin-top: 16rpx;
			color: var(--price-color);
			.unit {
				margin-right: 6rpx;
			}
			
			.txt {
				font-weight: normal;
			}
		}
		
		.pro-info {
			position: relative;
			margin-top: 16rpx;
			
			.delete-price {
				flex: 1;
				display: flex;
				align-items: flex-end;
				
				.unit {
					margin-right: 6rpx;
				}
				
				.txt {
					text-decoration:none;
				}
			}
			
			& > view {
				line-height: 1;
				
				&:nth-child(2) {
					position: absolute;
					right: 0;
					bottom: 0;
				}
			}
		}
		
		.member-price-tag {
			display: inline-block;
			width: 60rpx;
			line-height: 1;
			margin-left: 6rpx;
			
			image {
				width: 100%;
			}
		}
	}
	
}