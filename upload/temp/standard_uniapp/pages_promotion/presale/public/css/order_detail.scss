@mixin flex-row-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

@mixin wrap {
	margin: $margin-updown $margin-both;
	padding: 20rpx 30rpx;
	border-radius: $border-radius;
	background: #fff;
	position: relative;
}
text,
view {
	font-size: $font-size-tag;
}
.align-right {
	text-align: right;
}

.color-text-white {
	color: #fff;
}

.detail-container {
	height: 100vh;

	.height-box {
		display: block;
		padding-bottom: 100rpx;
	}

	&.safe-area {
		.height-box {
			display: block;
			padding-bottom: 168rpx;
		}
	}
}

.status-wrap {
	background-size: 100% 100%;
	padding: 40rpx 0;
	height: 180rpx;

	image {
		width: 104rpx;
		height: 86rpx;
		margin-right: 20rpx;
		margin-top: 20rpx;
	}

	.order-status-left {
		display: flex;
		margin: 0 40rpx;
	}

	& > view {
		text-align: center;
		color: #fff;
	}

	.desc {
		margin-left: 20rpx;
	}

	.price {
		font-weight: 600;
	}

	.action-group {
		text-align: center;
		padding-top: 20rpx;

		.action-btn {
			line-height: 1;
			padding: 16rpx 50rpx;
			display: inline-block;
			border-radius: 32rpx;
			background: #fff;
			box-shadow: 0 0 14rpx rgba(158, 158, 158, 0.6);
		}
	}
}

.site-wrap {
	@include wrap;
	margin-top: -69rpx;

	.site-header {
		display: flex;
		align-items: center;

		.icon-dianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: $font-size-base;
		}
	}

	.site-body {
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 160rpx;
				height: 160rpx;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: $border-radius;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: $font-size-base;
				}

				.goods-sub-section {
					width: 100%;
					line-height: 1.3;
					display: flex;
					margin-top: 20rpx;

					.goods-price {
						font-weight: 700;
						font-size: $font-size-activity-tag;
						color: var(--price-color);
					}

					.unit {
						font-size: $font-size-tag;
						margin-right: 2rpx;
						font-weight: 700;
					}

					view {
						flex: 1;
						line-height: 1.3;
						&:last-of-type {
							text-align: right;

							.iconfont {
								line-height: 1;
								font-size: $font-size-tag;
							}
						}
					}
				}

				.goods-action {
					text-align: right;
					padding-top: 20rpx;

					navigator {
						display: inline-block;
					}

					.order-box-btn {
						height: 48rpx !important;
						line-height: 48rpx !important;
						font-size: $font-size-tag !important;
						display: inline-block;
						background: #fff;
						border: 0.5px solid #999;
						margin-left: 10rpx;
						box-sizing: content-box;
					}
				}
			}
		}
	}
}

.order-cell {
	display: flex;
	margin: 20rpx 0;
	align-items: center;
	background: #fff;
	line-height: 40rpx;

	.tit {
		text-align: left;
	}

	.box {
		flex: 1;
		padding: 0 20rpx;
		line-height: inherit;

		.textarea {
			height: 40rpx;
		}
	}

	.iconfont {
		color: #bbb;
		font-size: $font-size-base;
	}

	.order-pay {
		padding: 0;

		text {
			display: inline-block;
			margin-left: 6rpx;
		}
	}
}

.order-summary {
	@include wrap;

	.order-cell {
		&:first-child {
			margin-top: 0;
		}

		.tit {
			font-size: $font-size-base;
			width: 196rpx;
		}

		.box {
			display: flex;
			align-items: center;

			text {
				font-size: $font-size-base;
			}
		}

		.copy {
			font-size: $font-size-activity-tag;
			display: inline-block;
			background: #f7f7f7;
			line-height: 1;
			padding: 6rpx 10rpx;
			margin-left: 10rpx;
			border-radius: 18rpx;
			border: 2rpx solid #d2d2d2;
		}
	}

	.hr {
		// width: calc(100% - 190rpx);
		width: 100%;
		height: 2rpx;
		background: #f7f7f7;
		margin-bottom: 20rpx;
	}
}

.order-money {
	@include wrap;

	.order-cell {
		.tit {
			font-size: $font-size-base;
		}

		.box {
			font-weight: 600;
			padding: 0;

			text {
				font-size: $font-size-base;
				font-weight: bold;
			}

			.operator {
				font-size: $font-size-tag;
				margin-right: 6rpx;
			}
		}
	}
}

.kefu {
	@include wrap;
	margin: 30rpx 0 10rpx;
	border-top: 2rpx solid #f7f7f7;
	padding-bottom: 0;
	padding-top: 30rpx;
	& > view {
		@include flex-row-center;

		.iconfont {
			font-weight: bold;
			margin-right: 10rpx;
			font-size: $font-size-base;
			line-height: 1;
		}
	}

	button {
		width: 100%;
		// position: absolute;
		border: none;
		z-index: 1;
		padding: 0;
		margin: 0;
		background: none;
		height: 50rpx;
		line-height: 50rpx;
		display: flex;
		justify-content: center;
		&::after {
			border: none !important;
		}
		.iconfont {
			margin-right: 10rpx;
		}
	}
}

.order-action {
	text-align: right;

	.order-box-btn {
		margin-right: $margin-both;
		margin-left: 0;
		font-size: $font-size-tag;
		height: 60rpx;
		line-height: 60rpx;
		box-sizing: content-box;
		min-width: 60rpx;
		text-align: center;

		&.color-base-bg {
			color: #fff;
		}

		&:last-child {
			margin-right: 0;
		}
		
		&.disabled{
			background: #eee;
			border-color: #e5e5e5;
			color: #999;
		}
	}
}
.status-name {
	view {
		font-size: $font-size-toolbar;
		color: #fff;
		line-height: 1;
		text-align: left;
	}
	
	.name {
		margin-top: 40rpx;
	}
	
	.desc {
		font-size: $font-size-tag;
		margin: 20rpx 0 0 0;
	}
}

.head-nav {
	width: 100%;
	height: var(--status-bar-height);
}

.head-nav.active {
	padding-top: 40rpx;
}

.head-return {
	height: 90rpx;
	line-height: 90rpx;
	color: #fff;
	font-weight: 600;
	font-size: $font-size-toolbar;
	position: relative;
	text-align: center;
	text {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		left: 20rpx;
		display: inline-block;
		margin-right: 10rpx;
		font-size: $font-size-toolbar;
	}
}

.store-detail view {
	font-size: $font-size-activity-tag;
}

.store-wrap {
	@include wrap;
	margin-top: -76rpx;

	.store-info {
		display: flex;
		align-items: center;
		padding-left: 50rpx;
		position: relative;

		.icon {
			left: 0;
			position: absolute;
			top: 4rpx;

			.iconfont {
				line-height: 50rpx;
				font-size: $font-size-base;
			}
			.icon-mendian {
				font-size: $font-size-toolbar;
			}
		}

		.store-name {
			display: flex;

			.name {
				flex: 1;
			}
		}

		.store-info-detail {
			flex: 1;
			.store-detail view {
				font-size: $font-size-goods-tag + 2rpx;
			}
			& > view:first-of-type {
				font-size: $font-size-tag + 2rpx;
			}
		}
		.cell-more {
			margin-left: 50rpx;
		}
	}
}
.pick-block {
	&.first-pick-block {
		border-top: 2rpx solid #f1f1f1;
	}
	display: flex;
	align-items: center;
	margin-top: 20rpx;
	padding-top: 20rpx;
	input,
	.last-child {
		flex: 1;
		text-align: right;
		font-size: $font-size-tag;
	}
}
.sku {
	display: flex;
	line-height: 1;
	margin-top: 10rpx;
	margin-bottom: 10rpx;
}
.goods-spec {
	color: #838383;
	font-size: $font-size-goods-tag;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	flex: 1;
}

.presale-process {
	.order-cell {
		margin: 10rpx 0;
	}
}