@mixin wrap {
	margin: $margin-both $margin-updown;
	padding: $padding;
	border-radius: $border-radius;
	background: #fff;
	position: relative;
}
view,
input,
text,
textarea {
	font-size: $font-size-base + 2rpx;
}
.popup-header {
	.icon-close {
		font-size: $font-size-toolbar;
	}
}

.inline {
	display: inline !important;
}

.order-container {
	padding-bottom: 160rpx;

	&.safe-area {
		padding-bottom: 188rpx;
	}
}

.address-wrap {
	@include wrap;
	min-height: 100rpx;
	display: flex;
	align-items: center;
	margin-top: -76rpx;
	padding: 24rpx;

	.icon {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		position: absolute;
		top: 40rpx;
		margin-right: 20rpx;
		// background-color: $base-color;
		image {
			width: 60rpx;
			height: 60rpx;
		}
		.iconfont {
			line-height: 1;
			color: #fff;
			font-size: $font-size-toolbar;
		}
		.icon-mendian {
			font-size: $font-size-toolbar;
		}
	}

	.address-info {
		padding-left: 100rpx;
		padding-right: 40rpx;
		flex: 1;

		.info {
			display: flex;

			text {
				flex: 1;

				&:last-of-type {
					text-align: right;
					color: #999;
				}
			}
		}

		.detail {
			margin-top: 8rpx;
			line-height: 1.3;
		}
	}

	.cell-more {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 10rpx;

		.iconfont {
			color: #999;
			font-size: $font-size-toolbar;
		}
	}
}

.mobile-wrap {
	@include wrap;
	min-height: 100rpx;
	display: flex;
	align-items: center;
	margin-top: -76rpx;
	padding: 24rpx;
	.icon {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		margin-right: 20rpx;
		// background-color: $base-color;
		image {
			width: 60rpx;
			height: 60rpx;
		}
		.iconfont {
			line-height: 1;
			color: #fff;
		}
		.icon-mendian {
			font-size: $font-size-toolbar;
		}
	}
	.mobile-info {
		padding-left: 100rpx;
	}
	.form-group {
		.form-item {
			display: flex;
			line-height: 50rpx;

			.text {
				display: inline-block;
				line-height: 50rpx;
				padding-right: 10rpx;
			}

			.placeholder {
				line-height: 50rpx;
			}

			.input {
				flex: 1;
				height: 50rpx;
				line-height: 50rpx;
				color: #838383;
			}
		}
	}
}

.order-cell {
	display: flex;
	margin: 20rpx 0;
	align-items: center;
	background: #fff;
	line-height: 40rpx;
	position: relative;

	.tit {
		text-align: left;
	}

	.box {
		flex: 1;
		padding: 0 10rpx;
		line-height: inherit;
		text-align: right;

		.textarea {
			height: 40rpx;
			font-size: $font-size-base !important;
		}

		input {
			font-size: $font-size-base !important;
		}
	}

	.iconfont {
		color: #bbb;
		font-size: $font-size-toolbar;
	}

	.order-pay {
		padding: 0;

		text {
			display: inline-block;
			margin-left: 6rpx;
		}
	}
}
.site-wrap {
	@include wrap;

	.site-header {
		display: flex;
		align-items: center;

		.icon-dianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: $font-size-toolbar;
		}
	}

	.site-body {
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;
			&:last-of-type {
				margin-bottom: 0;
				padding-bottom:  $padding;
				border-bottom: 2rpx solid #f1f1f1;
			}

			.goods-img {
				width: 124rpx;
				height: 124rpx;
				padding: 20rpx 0 0 0;
				margin-right: 20rpx;
				border-radius: $border-radius;
				overflow: hidden;
				image {
					width: 100%;
					height: 100%;
					border-radius: $border-radius;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				padding: 20rpx 0 0 0;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
				}

				.goods-sub-section {
					width: 100%;
					line-height: 1.3;
					display: flex;
					align-items: center;
					padding: 0 10rpx;
					box-sizing: border-box;
					.icon-close {
						font-size: $font-size-base;
					}

					.goods-price {
						font-weight: 700;
						font-size: $font-size-base;
					}

					.unit {
						font-weight: normal;
						font-size: $font-size-base;
						margin-right: 2rpx;
					}

					view {
						flex: 1;
						line-height: 1.3;
						font-weight: 500;
						&:last-of-type {
							text-align: right;

							.iconfont {
								line-height: 1;
							}
						}
					}
				}
			}
		}
	}

	.site-footer {
		.order-cell {
			.tit {
				width: 180rpx;
				text-align: left;
			}

			.box {
				input {
					font-size: $font-size-base !important;
				}
				&.text-overflow {
					text {
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						display: block;
						width: 100%;
					}
				}
			}

			&:last-of-type {
				margin-bottom: 0;
			}
		}
	}
}

.order-checkout {
	@include wrap;
	.order-cell {
		.icon-yuan_checkbox,
		.icon-yuan_checked {
			font-size: $font-size-toolbar;
			position: absolute;
			top: 50%;
			right: 0;
			transform: translateY(-50%);
			color: #aaa;
		}
	}
}

.order-money {
	@include wrap;

	.order-cell {
		.box {
			font-weight: 600;
			padding: 0;

			.operator {
				font-size: $font-size-base;
				margin-right: 6rpx;
			}
			input {
				font-size: $font-size-base !important;
			}
		}
	}
}

.order-submit {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	// box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;
	display: flex;
	align-items: center;

	&.bottom-safe-area {
		// padding-bottom: 68rpx !important;
		padding-bottom: 0;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.order-settlement-info {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
	}

	.submit-btn {
		height: 80rpx;
		margin: 0 20rpx 0 34rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		button {
			line-height: 54rpx;
			width: 156rpx;
			height: 54rpx;
			text-align: center;
			padding: 0;
		}
	}
}

.popup {
	width: 100vw;
	background: #fff;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;

	.popup-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 2rpx solid rgba(241, 241, 241, 1);
		padding: 40rpx;
		& > view {
			line-height: 1;
		}

		.tit {
			flex: 1;
			font-size: $font-size-toolbar;
		}
		.align-right {
			display: flex;
			align-items: center;
		}

		.vice-tit {
			margin-right: 20rpx;
		}
	}

	.popup-body {
		height: calc(100% - 250rpx);
		&.store-popup {
			height: calc(100% - 120rpx);
		}
		&.safe-area {
			height: calc(100% - 270rpx);
		}
		&.store-popup.safe-area {
			height: calc(100% - 140rpx);
		}
	}

	.popup-footer {
		height: 120rpx;

		.confirm-btn {
			height: 72rpx;
			line-height: 72rpx;
			color: #fff;
			text-align: center;
			margin: 20rpx 40rpx;
			border-radius: $border-radius;
		}

		&.bottom-safe-area {
			padding-bottom: 68rpx !important;
		}
	}
}

.invoice-popup {
	height: 83vh;
	padding: 18rpx 0;
	box-sizing: border-box;
	position: relative;

	.invoice-close {
		position: absolute;
		line-height: 1;
		top: 48rpx;
		right: 48rpx;
		font-size: $font-size-toolbar;
		z-index: 9;
	}
	.popup-body {
		.invoice-cell {
			padding: 30rpx 0;
			border-top: 1px solid #f5f5f5;
			margin: 0 48rpx;

			&:first-of-type {
				border-top: none;
			}

			.tit {
				font-size: $font-size-toolbar;
			}

			.option-grpup {
				padding-top: 20rpx;

				.option-item {
					height: 54rpx;
					line-height: 54rpx;
					display: inline-block;
					font-size: $font-size-tag;
					padding: 0 36rpx;
					background: #eee;
					border: 1px solid #eee;
					border-radius: 50px;
					margin: 0 20rpx 20rpx 0;

					&:nth-of-type(1),
					&:nth-of-type(2),
					&:nth-of-type(3) {
						margin-bottom: 0;
					}

					&.active {
						// background: $base-color;
						color: #fff;
					}

					&.disabled {
						color: #aaa;
					}
				}
			}

			.invoice-form-group {
				input {
					background: rgba(241, 241, 241, 1);
					border-radius: 10rpx;
					height: 66rpx;
					margin-top: 22rpx;
					padding: 0 32rpx;
				}
			}
		}
		.invoice-tops {
			font-size: $font-size-tag;
			margin: 0 48rpx;
		}
	}
}

.coupon-popup {
	height: 65vh;

	.popup-body {
		background: #f5f5f5;
	}

	.coupon-item {
		@include wrap;

		& > .iconfont {
			font-size: $font-size-toolbar;
			position: absolute;
			top: 50%;
			right: 20rpx;
			transform: translateY(-50%);
		}
		& > .icon-yuan_checkbox {
			color: $color-tip;
		}

		.circular {
			position: absolute;
			top: 50%;
			left: 0;
			transform: translate(-50%, -50%);
			background: #f5f5f5;
			width: 30rpx;
			height: 30rpx;
			border-radius: 50%;
			z-index: 5;
		}

		.coupon-info {
			padding-right: 60rpx;
			height: 140rpx;
			display: flex;
			width: 100%;

			.coupon-money {
				width: 160rpx;
				height: 140rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 20rpx;

				text {
					font-size: 50rpx;
				}
			}

			.info {
				flex: 1;
				max-width: calc(100% - 240rpx);

				view {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.ns-text-color-gray {
					line-height: 1.5;
				}
			}
		}
	}
}

.promotion-popup {
	height: 65vh;

	.order-cell {
		margin: 20rpx 30rpx;

		.promotion-mark {
			padding: 4rpx 10rpx;
			line-height: 1;
			border-radius: 32rpx;
			color: #fff;
			font-size: $font-size-base;
			margin-right: 10rpx;
		}
	}
}

.delivery-popup {
	height: 80vh;
	// padding: 48rpx;
	box-sizing: border-box;

	.delivery-cell {
		padding: 30rpx 0;
		box-sizing: border-box;
		.tit {
			font-size: $font-size-toolbar;
		}

		.option-grpup {
			.option-item {
				display: inline-block;
				line-height: 1;
				font-size: $font-size-toolbar;
				padding: 16rpx 40rpx;
				border: 1px solid #eee;
				border-radius: 32rpx;
				margin: 0 20rpx 20rpx 0;

				&:nth-of-type(1),
				&:nth-of-type(2),
				&:nth-of-type(3) {
					margin-bottom: 0;
				}

				&.active {
					// background: opacify($base-color-rgba, 0.01);
				}

				&.disabled {
					color: #aaa;
				}
			}
		}
	}

	.delivery-cont {
		height: 100%;
		overflow-y: scroll;

		.pickup-point {
			padding: 20rpx 0;
			box-sizing: border-box;
			border-top: 1px solid #f5f5f5;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 0 48rpx;

			.color-active {
				view,
				text {
					// color: $base-color;
				}
			}
			.delivery-detail {
				width: 90%;
			}
			.name {
				display: flex;
				text {
					font-size: $font-size-toolbar;
				}
			}

			.icon {
				flex: 1;
				text-align: right;

				.iconfont {
					line-height: 1;
					font-size: $font-size-toolbar;
				}
			}

			&:first-of-type {
				padding-top: 0;
				border-top: none;
			}

			.info {
				line-height: 1.2;
				view {
					font-size: $font-size-tag;
				}
			}
		}
	}
}

.pay-password {
	width: 80vw;
	background: #fff;
	box-sizing: border-box;
	border-radius: 10rpx;
	overflow: hidden;
	padding: 60rpx 40rpx;
	transform: translateY(-200rpx);

	.title {
		font-size: $font-size-toolbar;
		text-align: center;
	}

	.tips {
		font-size: $font-size-base;
		color: #999;
		text-align: center;
	}

	.btn {
		width: 60%;
		margin: 0 auto;
		margin-top: 30rpx;
		height: 70rpx;
		line-height: 70rpx;
		border-radius: $border-radius;
		color: #fff;
		text-align: center;
		border: 1px solid #ffffff;

		&.white {
			margin-top: 20rpx;
			background-color: #fff !important;
		}
	}

	.password-wrap {
		padding-top: 20rpx;
		width: 90%;
		margin: 0 auto;

		.forget-password {
			margin-top: 20rpx;
			display: inline-block;
		}
	}
}

.head-nav {
	width: 100%;
	height: var(--status-bar-height);
}

.head-nav.active {
	padding-top: 40rpx;
}

.head-return {
	height: 90rpx;
	line-height: 90rpx;
	color: #fff;
	font-weight: 600;
	font-size: $font-size-toolbar;
	width: 100%;
	text-align: center;
	position: relative;
	text {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		left: $margin-both;
		display: inline-block;
		margin-right: 10rpx;
		font-size: $font-size-toolbar;
	}
}
.payment-top {
	width: 100%;
	height: 180rpx;
	overflow: hidden;
}

/* 2020/6/1 新增样式 */
.big-tit {
	font-size: $font-size-toolbar;
}
.balance-switch {
	transform: scale(0.8);
}
.margin-top {
	margin-top: -76rpx;
}
.store-wrap {
	background-color: #fff;
	margin: -140rpx 20rpx 20rpx;
	border-radius: $border-radius;
	.delivery-box {
		padding: 20rpx;
		border-radius: $border-radius;
	}
	.store-info {
		display: flex;
		align-items: center;
		.icon {
			width: 60rpx;
			height: 60rpx;
			border-radius: 50%;
			position: relative;
			margin-right: 26rpx;
			align-self: flex-start;
			margin-top: 12rpx;
			&.image-icon {
				background-color: unset;
				image {
					width: 100%;
					height: 100%;
				}
			}
			.iconfont {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				font-size: 32rpx;
				color: #fff;
			}
		}
		.store-info-detail {
			flex: 1;
			.store-detail view {
				font-size: $font-size-tag + 2rpx;
			}
		}
		.cell-more {
			margin-left: 50rpx;
		}
	}
	.pick-block {
		display: flex;
		align-items: center;
		margin-top: 20rpx;
		padding-top: 20rpx;
		border-top: 2rpx solid #f1f1f1;
		>view{
			flex: 1;
		}
		.time-picker{
			display: flex;
			align-items: center;
			justify-content: flex-end;
			view{
				text-align: center;
				color: $color-tip;
			}
		}
	}
}
// tabs
@mixin triangle {
	content: '';
	position: absolute;
	top: 1rpx;
	width: 0;
	height: 0;
	border-bottom: 70rpx solid #fff;
	z-index: 2;
}
@mixin triangle-left {
	@include triangle;
	left: calc(100% - 1rpx);
	border-right: 16px solid transparent;
}
@mixin triangle-right {
	@include triangle;
	right: calc(100% - 1rpx);
	border-left: 16px solid transparent;
}
.tabs {
	border-radius: 23rpx;
	background: rgba(255, 255, 255, 0.3);
	display: flex;
	align-items: center;
	height: 70rpx;
	position: relative;
	margin-bottom: $margin-updown;
	& > view {
		color: #fff;
		text-align: center;
		font-size: $font-size-tag;
		flex: 1;
		height: 100%;
		line-height: 70rpx;
		opacity: 0.7;
		position: relative;
		&.active {
			background-color: #fff !important;
			opacity: 1;
			transform: scaleY(1.2);
			z-index: 2;
			transform-origin: 0 100%;
			-webkit-transform-origin: 0 100%;
			border-top-left-radius: $border-radius;
			border-top-right-radius: $border-radius;
			// 字体放大后缩小
			.content {
				color: #333;
				transform: scaleY(0.83) translate3d(-50%, -50%, 0);
			}
		}
		.content {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate3d(-50%, -50%, 0);
			color: #fff;
		}

		&:not(:first-of-type):not(:last-of-type).active {
			&:before {
				@include triangle-left;
			}
			&:after {
				@include triangle-right;
			}
		}

		&:first-of-type.active {
			&:before {
				@include triangle-left;
			}
		}

		&:last-of-type.active {
			&:after {
				@include triangle-right;
			}
		}
	}
}
.text-right{
	text-align: right;
}
.sku {
	display: flex;
	line-height: 1;
	margin-top: 10rpx;
	margin-bottom: 10rpx;
}
.goods-spec{
	color: #838383;
	font-size: $font-size-tag;
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap;
	flex:1
}
.address-empty {
		line-height: 100rpx;
		text-align: center;
  }