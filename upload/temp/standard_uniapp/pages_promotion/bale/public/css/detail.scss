.container {
	width: 100vw;
	height: 100vh;
}

.activity-head {
	padding: 0 40rpx 40rpx;
	padding-top: 40rpx;
	height: 160rpx;

	.activity-text {
		color: #fff;
		line-height: 1;
	}

	.time {
		color: #fff;
		font-size: 24rpx;
		margin-top: 6rpx;
	}
	.no-start {
		color: #fff;
		font-size: 30rpx;
		margin-top: 16rpx;
	}
}

.goods-wrap {
	background-color: #fff;
	margin: -80rpx 20rpx 40rpx;
	border-radius: 8rpx;

	.goods-item {
		padding: 30rpx 0;
		display: flex;
		margin: 0 30rpx;
		border-bottom: 1px solid #f8f8f8;

		&:last-child {
			border-bottom: 0;
		}

		.goods-image {
			width: 200rpx;
			height: 200rpx;
			overflow: hidden;
			margin-right: 20rpx;
			border-radius: $border-radius;

			image {
				width: inherit;
			}
		}

		.goods-info {
			flex: 1;
			position: relative;

			.name {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				font-size: $font-size-base;
				line-height: 36rpx;
				font-weight: 600;
			}
			.spec-name {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				font-size: $font-size-base;
			}

			.introduction {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
				font-size: $font-size-base;
				color: #999;
				line-height: 28rpx;
				margin-top: 10rpx;
				height: 28rpx;
			}
		}

		.goods-bottom {
			display: flex;
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;

			& > view {
				flex: 1;
			}

			.price {
				line-height: 1;
				display: flex;
				align-items: baseline;
				font-weight: bold;

				.unit {
					font-size: 24rpx;
				}
			}

			.num {
				line-height: 1;

				view {
					line-height: 1;
				}

				.num-wrap {
					text-align: right;
				}

				.goods-num {
					width: 60rpx;
					padding: 0 10rpx;
					display: inline-block;
					text-align: center;
					transform: translateY(-4rpx);
				}

				.icon-jianshao {
					font-size: 40rpx;
					color: #ccc;
				}
				.icon-add-fill {
					font-size: 40rpx;
				}

				.select {
					color: #fff;
					font-size: 24rpx;
					padding: 0 20rpx;
					height: 40rpx;
					line-height: 40rpx;
					border-radius: 36rpx;
					display: inline-block;
				}
			}
		}
	}
}
.footer-wrap-fill{
	width: 100vw;
	height: 120rpx;
}
.footer-wrap {
	position: fixed;
	width: 100vw;
	bottom: 0;
	padding: 20rpx;
	background: #fff;
	box-shadow: 0 -2px 10px 0 rgba(125, 126, 128, 0.16);
	display: flex;
	box-sizing: border-box;
	z-index: 10;

	& > view {
		flex: 1;
	}

	.left {
		display: flex;

		.cart-wrap {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			background-color: #ddd;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			margin-right: 30rpx;

			.iconfont {
				font-size: 40rpx;
				color: #999;
			}

			.num {
				position: absolute;
				right: 0;
				top: 0;
				font-size: 20rpx;
				transform: translateX(16rpx);
				padding: 4rpx 10rpx;
				line-height: 1;
				color: #fff;
				border-radius: 20rpx;
			}
		}

		.data {
			flex: 1;

			.price {
				line-height: 1;
				display: flex;
				align-items: baseline;
				font-weight: 600;
				font-size: $font-size-toolbar;
				margin-top: 10rpx;

				.unit {
					font-size: 24rpx;
				}
			}

			.desc {
				margin-top: 10rpx;
				font-size: $font-size-base;
				color: #999;
				line-height: 1;
			}
		}
	}

	.right {
		text-align: right;
		display: flex;
		align-items: center;
		justify-content: flex-end;

		.sub-btn {
			height: 70rpx;
			line-height: 70rpx;
			color: #fff;
			text-align: center;
			width: 200rpx;
			border-radius: 50rpx;
			font-size: 28rpx;

			&.disabled {
				background-color: #ccc;
				color: #eee;
			}
		}
	}
}

.cart-shade {
	position: fixed;
	width: 100vw;
	height: 100vh;
	z-index: 4;
	left: 0;
	top: 0;
}

.cart-popup {
	position: fixed;
	width: 100vw;
	height: 0;
	background-color: #fff;
	bottom: 120rpx;
	box-shadow: 0 -2px 10px 0 rgba(125, 126, 128, 0.16);
	box-sizing: content-box;
	z-index: 5;
	transition: all 0.3s;

	&.show {
		height: 45vh;
	}

	.header {
		width: 100%;
		line-height: 1;
		display: flex;
		height: 80rpx;
		background-color: #eee;
		align-items: center;

		& > view {
			flex: 1;
			padding: 0 30rpx;
			box-sizing: border-box;
			font-size: 24rpx;
		}

		.right {
			text-align: right;

			.iconfont {
				font-size: 24rpx;
				margin-right: 6rpx;
			}
		}
	}

	.cart-goods-wrap {
		width: 100%;
		height: calc(100% - 80rpx);
		overflow-y: scroll;

		.goods-item {
			padding: 20rpx 0;
			margin: 0 30rpx;
			border-bottom: 1px solid #eee;
			display: flex;
			align-items: center;

			&:last-child {
				border-bottom: none;
			}

			.info {
				flex: 1;

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					font-size: 28rpx;
					line-height: 36rpx;
					font-weight: 600;
				}

				.sku-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 1;
					overflow: hidden;
					font-size: $font-size-base;
					line-height: 36rpx;
					color: #999;
				}
			}

			.price {
				font-weight: bold;
				padding: 0 40rpx;

				.unit {
					font-size: 24rpx;
				}
			}

			.num {
				.goods-num {
					width: 60rpx;
					padding: 0 10rpx;
					display: inline-block;
					text-align: center;
					transform: translateY(-4rpx);
				}

				.icon-jianshao {
					font-size: 40rpx;
					color: #ccc;
				}
				.icon-add-fill {
					font-size: 40rpx;
				}
			}
		}
	}
}
